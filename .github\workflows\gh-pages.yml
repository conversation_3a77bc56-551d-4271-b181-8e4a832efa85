name: gh-pages

on:
  push:
    branches: ['**']

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [16.x]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    steps:
      - uses: n1hility/cancel-previous-runs@v2
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
      - uses: actions/checkout@v2
        with:
          persist-credentials: false
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v1
        with:
          node-version: ${{ matrix.node-version }}
          repo-token: ${{ secrets.GITHUB_TOKEN }}
      - name: build gh-pages
        run: |
          npm i --legacy-peer-deps
          npm run build --workspaces
          sh deploy-gh-pages.sh
      - name: Deploy 🚀
        uses: JamesIves/github-pages-deploy-action@3.7.1
        with:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          BRANCH: gh-pages # The branch the action should deploy to.
          FOLDER: gh-pages # The folder the action should deploy.
          CLEAN: true # Automatically remove deleted files from the deploy branch
          SINGLE_COMMIT: true
      - name: Generate coverage report
        env:
          NODE_OPTIONS: '--max-old-space-size=8192'
        run: |
          npm run coverage
      - uses: codecov/codecov-action@v2
        with:
          files: ./coverage/clover.xml
          name: codecov-umbrella # optional
          fail_ci_if_error: false
          verbose: true # optional (default = false)
