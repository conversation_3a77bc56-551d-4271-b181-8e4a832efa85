name: PR test

on:
  pull_request:
    branches: ['**']

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [16.x]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    steps:
      - uses: actions/checkout@v2
        with:
          persist-credentials: false
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v1
        with:
          node-version: ${{ matrix.node-version }}
          repo-token: ${{ secrets.GITHUB_TOKEN }}
      - name: test
        run: |
          npm i --legacy-peer-deps
          npm run build --workspaces
          npm test --workspaces
          sh deploy-gh-pages.sh
