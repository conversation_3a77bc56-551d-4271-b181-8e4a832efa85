# Files
.DS_Store
.ruby-version
test.sass
npm-debug.log
yarn.lock

# Folders
.idea/
.sass-cache
gh_pages
_site
node_modules
/dist
/lib
/sdk
/gh-pages
.vscode/
/output
/toolkit/amis-renderer
/toolkit/output
/coverage
/schema.json
/npm
/tempPkg
/mock/cfc/cfc.zip
.rollup.cache

dataseeddesigndocui
tsconfig.tsbuildinfo
lerna-debug.log
.rollup.cache
revision.json
**/revision.json
~$*
*.docx
/mock/aliyunfc/aliyunfc.zip
/test-demo
/verify-demo
# /playground
**/docs.json
docs/zh-CN/extractKnowledge.js
docs/zh-CN/imaKnowledge
.cursorrules

# Cursor AI 临时测试文件
cursor-tests/
