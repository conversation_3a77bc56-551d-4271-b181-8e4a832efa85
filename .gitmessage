# <type>(<scope>): <subject>
#
# <body>
#
# <footer>
#
# type 字段包含:
#   feat：新功能（feature）
#   fix：修复bug
#   docs：文档（documentation）
#   style：格式（不影响代码运行的变动）
#   refactor：重构（即不是新增功能，也不是修改bug的代码变动）
#   test：增加测试
#   chore：构建过程或辅助工具的变动
#
# scope: 用于说明 commit 影响的范围，比如数据层、控制层、视图层等等
#
# subject: commit 目的的简短描述，不超过50个字符
#
# body: 对本次 commit 的详细描述，可以分成多行
#
# footer: 一些备注，通常是 BREAKING CHANGE 或修复的 bug 的链接

feat(REQ-30977):
