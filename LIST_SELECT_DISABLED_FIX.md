# list-select 组件 disabled 状态下 tooltip 问题修复

## 问题描述

在 `list-select` 组件中，当选项处于 disabled 状态时，由于 CSS 设置了 `pointer-events: none`，导致所有鼠标事件都被阻止，包括 hover 事件。这使得内部的 `typography` 组件无法显示 tooltip 效果。

## 问题分析

### 原有实现

```scss
.#{$ns}ListControl {
  &-item {
    &.is-disabled {
      pointer-events: none;  // 问题所在：阻止了所有鼠标事件
      opacity: var(--ListControl-item-onDisabled-opacity);
      // ... 其他样式
    }
  }
}
```

### 问题影响

1. **tooltip 无法显示**：`pointer-events: none` 阻止了 hover 事件
2. **用户体验差**：无法通过 tooltip 查看被截断的长文本
3. **功能不一致**：其他组件的 disabled 状态通常保留 hover 效果

## 解决方案

### 1. 修改 CSS 样式

**文件：`packages/amis-ui/scss/components/form/_list.scss`**

```scss
&.is-disabled {
  // 移除 pointer-events: none，改为在 JavaScript 中处理点击事件
  // pointer-events: none;
  cursor: not-allowed;  // 添加禁用光标样式
  opacity: var(--ListControl-item-onDisabled-opacity);
  // ... 保留其他样式
}
```

### 2. 修改 JavaScript 逻辑

**文件：`packages/amis/src/renderers/Form/ListSelect.tsx`**

#### 修改 handleClick 方法

```typescript
handleClick(option: Option, e: React.MouseEvent<HTMLElement>) {
  if (e.target && (e.target as HTMLElement).closest('a,button')) {
    return;
  }

  // 检查选项或组件是否被禁用
  if (option.disabled || this.props.disabled) {
    e.preventDefault();
    e.stopPropagation();
    return;
  }

  const {onToggle} = this.props;
  onToggle(option);
}
```

#### 修改 handleDBClick 方法

```typescript
handleDBClick(option: Option, e: React.MouseEvent<HTMLElement>) {
  // 检查选项或组件是否被禁用
  if (option.disabled || this.props.disabled) {
    e.preventDefault();
    e.stopPropagation();
    return;
  }

  this.props.onToggle(option, false, true);
  this.props.onAction(null, {
    type: 'submit'
  });
}
```

## 实现特点

### 1. 保留 hover 效果

- 移除 `pointer-events: none`，允许 hover 事件触发
- typography 组件的 tooltip 可以正常显示

### 2. 阻止点击行为

- 在事件处理函数中检查 disabled 状态
- 使用 `preventDefault()` 和 `stopPropagation()` 阻止事件传播

### 3. 视觉反馈

- 添加 `cursor: not-allowed` 显示禁用状态
- 保留原有的透明度和颜色样式

### 4. 兼容性

- 支持单个选项禁用（`option.disabled`）
- 支持整个组件禁用（`this.props.disabled`）
- 保持与现有 API 的完全兼容

## 测试场景

1. **正常状态**：可点击，有 tooltip
2. **整体禁用**：不可点击，但有 tooltip
3. **部分禁用**：混合状态，禁用项不可点击但有 tooltip

## 预期效果

✅ **disabled 选项不可点击**：点击无反应，不会触发选择  
✅ **显示禁用光标**：鼠标悬停时显示 `not-allowed` 光标  
✅ **tooltip 正常显示**：typography 组件的 tooltip 功能正常  
✅ **hover 事件正常**：不被 CSS 阻止，可以触发相关效果  

这个修复方案在保持原有禁用功能的同时，恢复了 hover 相关的交互效果，提升了用户体验。
