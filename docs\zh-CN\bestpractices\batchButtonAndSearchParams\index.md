---
title: CRUD 批量操作与搜索条件联动
description: 毛甜甜
type: 0
group: ⚙ 最佳实践
menuName: CRUD 批量操作与搜索条件联动
icon:
order: 8
---

<div><font color=#978f8f size=1>贡献者：毛甜甜</font> <font color=#978f8f size=1>贡献时间: 2024/11/20</font></div>

## 功能描述

批量操作数据源来源于当前搜索条件搜索的所有数据，不需要像常规批量操作那样手动勾选数据。当搜索区有过滤条件时，批量操作按钮实时切换为可操作状态（无需点击查询按钮），无过滤条件，批量操作按钮实时切换为禁用状态



## 实际场景

1. 场景链接：[法诉系统/调解业务管理/调解案件池](http://moka.dmz.sit.caijj.net/themisui/mediation-business-manage/mediation-case-pool)
2. 复现步骤：
   - 进入页面，批量操作按钮是禁用状态，输入搜索条件，批量操作按钮实时切换到可操作状态。

![页面展示](/dataseeddesigndocui/public/assets/batchButtonAndSearchParams/1.png "页面展示")
![页面展示](/dataseeddesigndocui/public/assets/batchButtonAndSearchParams/2.png "页面展示")

## 实践代码

```js
// 自定义过滤器，将查询参数中的分页参数从搜索条件中排除，然后判断其他搜索条件是否有值(true:为空，false:不为空)
registerFilter('isQueryObjectEmpty', (formValues) => {
  const { page, perPage, ...fields } = formValues;

  return Object.values(fields).every((value) => {
    if (Array.isArray(value)) {
      return value.length === 0;
    }
    return !value;
  });
});

{
  "type": "page",
  "id": "rootPage",
  "data": {
    "isNoSearch": true, // 是否存在搜索内容，用于控制批量按钮的禁用状态。
    "searchData": {} // 搜索内容数据，用于批量操作API，请求数据。
  },
  "body": {
    "type": "crud",
    ... // 其他配置
    "filter": {
      "onEvent": {
        /**
         * 默认情况下，筛选数据只有在点击查询按钮之后，才能在 crud 中获取到。因此要实时获取到筛选的数据。
         * 需要监听 crud filter 的 change事件，将 filter 中数据存到 page 中.
         */
        "change": {
          "actions": [
            {
              "actionType": "setValue",
              "componentId": "rootPage",
              "args": {
                "value": {
                  "isNoSearch": "${event.data|isQueryObjectEmpty}",
                  "searchData": "${event.data}"
                }
              }
            }
          ]
        }
      },
    },
    "headerToolbar": [
      {
        "type": "button",
        "level": "primary",
        "label": "手工分案", // 批量操作按钮
        "disabledOn": "${isNoSearch}", // 根据是否存在搜索条件，控制禁用状态
        "onEvent": {
          "click": {
            "actions": [
              {
                "actionType": "ajax",
                "args": {
                  "api": {
                    "url": "/api/mock2/form/saveForm",
                    "method": "post",
                    "data": "${searchData}", // 使用 filter 中的数据发起请求
                     "adaptor": (payload) => {
                      return {
                        ...payload,
                        status: payload.status === 200 ? 0 : payload.status,
                        msg: '',
                        data: {
                          ...payload.data,
                          canOpr: payload.status === 0 // 是否可操作标示
                        } 
                      }
                     }
                  }
                }
              },
              {
                "expression": "${canOpr}", // 由 批量操作 API 返回结果，判断是否弹窗。
                "actionType": "dialog",
                "dialog": {
                  "title": "手工分案",
                  "body": "确认手工分案吗？"
                }
              }
            ]
          }
        }
      }
    ],
  }
}
```

```schema
// 自定义过滤器，将查询参数中的分页参数从搜索条件中排除，然后判断其他搜索条件是否有值(true:为空，false:不为空)
registerFilter('isQueryObjectEmpty', (formValues) => {
  const { page, perPage, ...fields } = formValues;

  return Object.values(fields).every((value) => {
    if (Array.isArray(value)) {
      return value.length === 0;
    }
    return !value;
  });
});

return {
  "type": "page",
  "id": "rootPage",
  "data": {
    "isNoSearch": true,
    "searchData": {}
  },
  "body": {
    "type": "crud",
    "name": "crud",
    "syncLocation": false,
    "api": "/api/mock2/crud/table4",
    "canAccessSuperData": false,
    "columnsTogglable": false,
    "filter": {
      "title": "",
      "onEvent": {
        "change": {
          "actions": [
            {
              "actionType": "setValue",
              "componentId": "rootPage",
              "args": {
                "value": {
                  "isNoSearch": "${event.data|isQueryObjectEmpty}",
                  "searchData": "${event.data}"
                }
              }
            }
          ]
        }
      },
      "body": [
        {
          "type": "group",
          "mode": "horizontal",
          "body": [
            {
              "type": "input-text",
              "name": "browser",
              "label": "Browser",
              "placeholder": "请输入",
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "engine",
              "label": "Engine",
              "placeholder": "请输入",
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "platform",
              "label": "Platform",
              "placeholder": "请输入",
              "columnRatio": 4
            }
          ]
        }
      ],
      "actions": [
        {
          "type": "reset",
          "label": "重 置"
        },
        {
          "type": "submit",
          "level": "primary",
          "label": "查 询"
        }
      ]
    },
    "headerToolbar": [
      {
        "type": "button",
        "level": "primary",
        "label": "手工分案",
        "disabledOn": "${isNoSearch}",
        "onEvent": {
          "click": {
            "actions": [
              {
                "actionType": "ajax",
                "args": {
                  "api": {
                    "url": "/api/mock2/form/saveForm",
                    "method": "post",
                    "data": "${searchData}",
                    "adaptor": "return { ...payload, status: payload.status === 200 ? 0 : payload.status, msg: '', data: {...payload.data, canOpr: payload.status === 0} }"
                  }
                }
              },
              {
                "expression": "${canOpr}",
                "actionType": "dialog",
                "dialog": {
                  "title": "手工分案",
                  "body": "确认手工分案吗？"
                }
              }
            ]
          }
        }
      }
    ],
    "columns": [
      {
        "name": "id",
        "label": "ID"
      },
      {
        "name": "engine",
        "label": "Rendering engine"
      },
      {
        "name": "browser",
        "label": "Browser"
      },
      {
        "name": "platform",
        "label": "Platform(s)"
      },
      {
        "name": "version",
        "label": "Engine version"
      },
      {
        "name": "grade",
        "label": "CSS grade"
      }
    ]
  }
}
```

## 代码分析

1. 默认情况下，筛选数据只有在点击查询按钮之后，才能在 crud 中获取到。因此要实时获取到筛选的数据，需要监听 crud filter 的 change事件。
2. 监听 filter 的 onChange 事件，在事件触发时，将所有筛选数据设置到 page 中。并判断除分页参数以外的其他搜索条件是否有值，切换 批量按钮的禁用状态。
3. 【手工分案】按钮设置 disabledOn: "${isNoSearch}"。
4. 【手工分案】按钮点击时，先使用搜索条件请求相关业务接口，接口请求成功，弹出二次确认弹窗。

参考文档

1. [Form 表单](/dataseeddesigndocui/#/amis/zh-CN/components/form/index)
2. [CRUD](/dataseeddesigndocui/#/amis/zh-CN/components/crud)
3. [事件动作](/dataseeddesigndocui/#/amis/zh-CN/course/concepts/event-action)
