---
title: Crud嵌套子表格批量选中
description: 庞金明
type: 0
group: ⚙ 最佳实践
menuName: 提交表单，表单项包含$符号
icon:
order: 9
standardMode: true
---

<div><font color=#978f8f size=1>贡献者：庞金明</font> <font color=#978f8f size=1>贡献时间: 2024/10/30</font></div>

## 功能描述

`Curd` 组件获取到所有嵌套的 `subTable` 子表格的选中数据。

## 实践场景

`Crud` 组件有批量选择功能只能拿到当前表格选中的数据。当我们开启子 `subTable` 子表格的批量选择功能，想在上层 `Crud` 获取到所有子表格的选中数据的场景下。

## 实践代码

### 核心代码

```js

// 存储所有子表格选中的数据
let selectedMap = {}

// 将当前 subTable 选中数据，设置到上层中
registerFilter('getSelectedIds', (data) => {
  const { _subTableId, selectedItems } = data
  selectedMap[_subTableId] = selectedItems
  const ids = Object.values(selectedMap).flat().map(({id}) => id)
  return ids
})

// 用于给 crud 的 subTable 设置默认选中
registerFilter('getSelectedItems', () => {
  const items = Object.values(selectedMap).flat()
  return items
})

// 移除所有选项数据
registerFilter('clearSelectedIds', () => {
  selectedMap = {}
  return []
})

// 关键JSON 配置
{
  type: "crud",
  ... // 顶层Crud 其他配置
  headerToolbar: [{
    "type": "button",
    "label": "批量删除",
    "actionType": "dialog",
    // 拿到上层全量的 selectedIds
    "disabledOn": "${!selectedIds.length}",
    "dialog": {
      "title": "批量删除数据：",
      "body": "是否批量删除 ${selectedIds|json}"
    }
  }],
  // 设置 subTable关键配置
  subTable: {
    type: "crud",
    ...// 子表格其他配置
    defaultSelectedItems: "${_|getSelectedItems}", // 设置默认选中值
    onEvent: {
      // 选项变化时同步到顶层
      selectedChange: {
        actions: [
          {
            actionType: "setValue",
            componentId: "pageId",
            args: { value: { selectedIds: "${event.data|getSelectedIds}" } },
          },
        ],
      },
    },
  },
  onEvent: {
    // 当清除选项时，清空选中数据
    clearSelection: {
      actions: [
        {
          actionType: "setValue",
          componentId: "pageId",
          args: { value: { selectedIds: "${event.data|clearSelectedIds}" } },
        },
      ],
    },
  },
}
```

Crud 子表格批量选中功能。

```schema
// 存储所有子表格选中的数据
let selectedMap = {}

// 将当前 subTable 选中数据，设置到上层中
registerFilter('getSelectedIds', (data) => {
  const { _subTableId, selectedItems } = data
  selectedMap[_subTableId] = selectedItems
  const ids = Object.values(selectedMap).flat().map(({id}) => id)
  return ids
})

// 用于给 crud 的 subTable 设置默认选中
registerFilter('getSelectedItems', () => {
  const items = Object.values(selectedMap).flat()
  return items
})

// 移除所有选项数据
registerFilter('clearSelectedIds', () => {
  selectedMap = {}
  return []
})

return {
  type: "page",
  id: "pageId",
  data: { selectedIds: [] },
  body: [
    {
      type: "crud",
      api: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
      syncLocation: false,
      defaultParams: { perPage: 2 },
      headerToolbar: [{
        "type": "button",
        "label": "批量删除",
        "actionType": "dialog",
        "disabledOn": "${!selectedIds.length}",
        "dialog": {
          "title": "批量删除数据：",
          "body": "是否批量删除 ${selectedIds|json}"
        }
      }],
      subTable: {
        type: "crud",
        defaultSelectedItems: "${_|getSelectedItems}",
        api: {
          url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?pid=${id}&perPage=2",
          tdata: { pid: "${id}" },
           adaptor: (payload, res, req) => {
            payload.data.rows = payload.data.rows.map((item) => ({
              ...item,
              id: `${req.tdata.pid}-${item.id}`
            }))
            return payload
          }
        },
        syncLocation: false,
        pickerMode: true,
        multiple: true,
        columns: [
          { name: "id", label: "ID" },
          { name: "engine", label: "Rendering engine" },
          { name: "browser", label: "Browser" },
          { name: "platform", label: "Platform(s)" },
        ],
        onEvent: {
          selectedChange: {
            actions: [
              {
                actionType: "setValue",
                componentId: "pageId",
                args: { value: { selectedIds: "${event.data|getSelectedIds}" } },
              },
            ],
          },
        },
      },
      columns: [
        { name: "id", label: "ID" },
        { name: "engine", label: "Rendering engine" },
        { name: "browser", label: "Browser" },
      ],
      onEvent: {
        clearSelection: {
          actions: [
            {
              actionType: "setValue",
              componentId: "pageId",
              args: { value: { selectedIds: "${event.data|clearSelectedIds}" } },
            },
          ],
        },
      },
    },
  ],
}
```

## 代码分析

1. `subTable` 根据选项变化将选中值同步给顶层。
2. 上层 `Crud` 拿到所有选中数据，用于 `Api` 提交，也把当前选中数据，同步给下层每一个 `subTable`。
