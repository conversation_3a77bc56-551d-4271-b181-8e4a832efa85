---
title: 数据域中取不到最新数据的问题汇总
description: 常鹏元
type: 0
group: ⚙ 最佳实践
menuName: 数据域中取不到最新数据的问题汇总
icon:
order: 9
standardMode: true
---

<div><font color=#978f8f size=1>贡献者：常鹏元</font> <font color=#978f8f size=1>贡献时间: 2024/11/13</font></div>

## 背景

在我们日常使用amis开发业务需求的时候经常会遇见获取数据域上的数据获取不到的问题，为了解决这一类问题，组件库的组件提供了trackExpression、syncFields（combo组件）等属性用来解决同步数据域的问题，但是大家对于这些属性的作用以及使用方式是很模糊的，这里针对于这一类问题，每个属性该用哪种属性来解决，以及使用上需要注意的问题进行总结。

[数据域解释](/dataseeddesigndocui/#/amis/zh-CN/course/concepts/datascope-and-datachain)

### 问题分类

整理下来，大家在数据域中获取不到数据的问题原因主要分为以下几种：

1. 数据域未同步下来，下层组件确实获取不到数据域中的数据
2. 数据已经从上层同步了下来，但是由于命中了一些amis组件的优化策略，导致组件没有更新，导致看起来像是没有获取到最新数据

遇见这一类问题的时候需要先看一下问题属于哪一种场景，然后根据场景选择对应的解决方案。

针对于这一类问题，我们提供了以下解决方案：

1. 数据未同步下来的场景，可以使用trackExpression属性或者combo组件的syncFields属性来同步数据
2. 数据已经同步下来，但是组件没有更新的场景，可以使用crud的updateAllRows或者是表单组件的strictMode: false来使组件重新进行更新

考虑到大家可能对两种场景区分的不是那么清楚，这里梳理了一些优先考虑的思路：

1. 遇见表单里获取不到最新的值的场景，优先考虑是不是组件没有更新，优先考虑strictMode: false，如果无法解决再考虑trackExpression
2. 遇见combo组件的场景，优先考虑combo组件的syncFields和strictMode: false
3. 遇见表格的场景很大概率是由于表格中的优化策略导致单元格没有更新，优先考虑updateAllRows
4. 又一个特例需要注意就是input-table，这个组件即是表单，又是表格，所以想要重新更新单元格的话updateAllRows和strictMode: false需要同时配置

#### 属性解释

下面介绍一下上面说的这些属性：

|属性|作用范围|作用|
|--|--|--|
|[trackExpression](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/datascope-and-datachain?anchor=更新数据链)|这个属性只能配置在[有数据域的组件](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/datascope-and-datachain?anchor=具备数据域的组件)上|trackExpression的作用是监听上层数据域中的数据变化，如果trackExpression中监听的数据发生变化，就会将上层数据同步到当前组件的数据域中，从而实现数据同步。|
|[syncFields](/dataseeddesigndocui/#/amis/zh-CN/components/form/combo?anchor=同步更新内部表单项)|这个属性只能配置在combo组件上|syncFields 是combo中用来同步上层数据中的配置属性，需要配合strictMode: false使用|
|[strictMode](/dataseeddesigndocui/#/amis/zh-CN/components/form/formitem?anchor=及时获取其他表单项的值)|这个属性只能配置在表单[FormItem](/dataseeddesigndocui/#/amis/zh-CN/components/form/formitem)组件上|当配置了strictMode: false，之后，数据域中的内容变化后，组件会重新渲染|
|[updateAllRows](/dataseeddesigndocui/#/amis/zh-CN/components/crud?anchor=属性表)|这个属性只能配置在表格组件上(input-table、crud、table)|updateAllRows的作用是数据变化之后，让所有的行都进行重新渲染|

## 场景示例

### 场景1，crud 的 filter中依赖上层的数据

下面示例中，filter中的select 和 外层的form中的select都依赖了page层的一个数据，外层的form中select是可以获取到数据的，但是filter中的select获取不到数据，因为filter中的select没有同步到当前组件的数据域中，所以需要使用trackExpression来同步数据。

将注释中的 trackExpression 注释放开，即可实现数据同步


```
page
  ├─ form
  └─ crud
       └─ filter(form) 这里取不到page的数据
```

```schema
{
  "type": "page",
  "initApi": {
    "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/options/autoComplete3?term=$term&waitSeconds=1",
    "responseData": {
      "options": "${items|pick:label~lab,value~val}",
    }
  },
  "body": [
    {
      "type": "form",
      "body": [
        {
          "type": "select",
          "name": "keywords1",
          "label": "关键字",
          "clearable": true,
          "source": "${options}",
          "columnRatio": 4
        },
      ]
    },
    {
      "type": "crud",
      "name": "crud",
      "syncLocation": false,
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4",
      "canAccessSuperData": false,
      "filter": {
        "debug": true,
        "title": "",
        // "trackExpression": "${options}",
        "body": [
          {
            "type": "select",
            "name": "keywords",
            "label": "关键字",
            "clearable": true,
            "source": "${options}",
            "columnRatio": 4
          },
        ],
        "actions": [
          {
            "type": "reset",
            "label": "重 置"
          },
          {
            "type": "submit",
            "level": "primary",
            "label": "查 询"
          }
        ]
      },
      "columns": [
        {
          "name": "id",
          "label": "ID"
        }
      ]
    }
  ]
}
```

### 场景2，crud单元格中依赖了上层数据中的数据

下面示例中，crud的测试时间列中使用了page层的数据，但是却没有渲染出任何内容，这个场景是由于crud中存在一些优化策略，导致数据虽然更新了但是crud中的单元格没有重渲染，这个时候需要使用updateAllRows来重新渲染crud中的单元格。

将注释中的 updateAllRows 注释放开，即可实现数据同步，此外有一个细节点就是这个情况不是必现的，只有在page层数据返回晚于表单数据的场景下会出现，同理在外层数据更新时也会出现这一类问题。

```
page
  └─ crud
       └─ col 这里取不到page的数据
```

```schema
{
  "type": "page",
  "id": "job-list-page",
  "initApi": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/page/initData?waitSeconds=2",
  "body": [
    {
      "type": "crud",
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
      "syncLocation": false,
      // "updateAllRows": true,
      "columns": [
        {
          "name": "date",
          "label": "测试时间"
        },
        {
          "type": "operation",
          "label": "操作",
          "buttons": [
            {
              "type": "button",
              "level": "link",
              "label": "编辑",
              "actionType": "dialog",
              "dialog": {
                "size": "lg",
                "title": "编辑作业",
                "showCloseButton": false,
                "id": "add-dialog",
                "showErrorMsg": false,
                "body": [
                  {
                    "type": "form",
                    "id": "add-form",
                    "body": [
                      {
                        "type": "group",
                        "body": [
                          {
                            "type": "tpl",
                            "tpl": "当前时间：${date}"
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            }
          ]
        }
      ]
    }
  ]
}
```

### 场景3，combo中使用上层数据域的数据

下面示例中，combo中select组件中使用了page层的数据，但是下拉框中却没有获取到数据，这个原因是因为上层数据域变化之后combo是不会同步的，需要使用syncFields来同步数据，syncFields一般和strictMode: false配合使用

将注释中的 syncFields 注释放开，即可实现数据同步

```
page
  └─ form
       └─ combo
            └─ select 这里取不到page的数据
```

```schema
{
  type: 'page',
  data: {
    options: []
  },
  "initApi": {
    "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/options/autoComplete3?term=$term&waitSeconds=2",
    "responseData": {
      "options": "${items|pick:label~lab,value~val}",
    }
  },
  body: [
    {
      type: 'form',
      data: {
        combo: [{}]
      },
      body: [
        {
          type: 'combo',
          name: 'combo',
          multiple: true,
          // syncFields: ['options'],
          // strictMode: false,
          items: [
            {
              type: 'select',
              label: '关键字',
              name: 'a',
              source: '${options}'
            }
          ]
        }
      ]
    }
  ]
}
```

### 场景4，editor操作按钮获取不到最新数据

下面示例中，editor操作按钮获取不到最新数据，这是因为表单组件没有重新渲染导致的，将strictMode: false添加上即可解决问题

```
page
  └─ form
       └─ editor
            └─ toolbar 这里取不到最新的editor数据
```

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "body": [
      {
        "type": "editor",
        "name": "editor",
        "language": "json",
        // "strictMode": false,
        "toolbar": [
          {
            "type": "button",
            "level": "link",
            "label": "格式化",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "actionType": "toast",
                    "args": {
                      "msg": "${editor}"
                    }
                  }
                ]
              }
            }
          }
        ],
        "label": "Groovy编辑器"
      }
    ]
  }
}
```

### 场景5，inputTable操作列获取不到最新数据

这个场景一样是获取不到最新值的问题，点击解析按钮的弹窗中获取不到最新的数据，但需要注意inputTable不但是表单而且还是表格，所以需要将strictMode: false 和 updateAllRows: true 都添加上即可解决问题

combo组件中也会遇见此类问题，但是只需要配置strictMode: false即可解决

```
page
  └─ form
       └─ input-table
            └─ col 
                └─ button
                      └─ dialog 这里获取不到最新的input-table数据
```

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "data": {
      "table": [
        {
          "a": "a1",
          "b": "b1",
          "c": true
        },
        {
          "a": "a2",
          "b": "b2",
          "c": true
        },
        {
          "a": "a3",
          "b": "b3",
          "c": true
        }
      ]
    },
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "body": [
      {
        "type": "input-table",
        "name": "table",
        "label": "Table",
        "needConfirm": false,
        // "strictMode": false,
        // "updateAllRows": true,
        "columns": [
          {
            "label": "A",
            "name": "a",
            "type": "input-text"
          },
          {
            "label": "B",
            "name": "b",
            "type": "select",
            "options": [
              "b1",
              "b2",
              "b3"
            ]
          },
          {
            "label": "C",
            "name": "c",
            "type": "checkbox"
          },
          {
            "type": "operation",
            "label": "操作",
            "quickEdit": false,
            "ignoreSchemaCheck": false,
            "buttons": [
              {
                "type": "button",
                "label": "解析",
                "actionType": "dialog",
                "dialog": {
                  "title": "解析",
                  "name": "json",
                  "body": {
                    "type": "form",
                    "title": false,
                    "trackExpression":"${table}",
                    "data": {
                      "tb": "${table}"
                    },
                    "body": [
                      {
                        "type": "input-table",
                        "name": "tb",
                        "label": "Table",
                        "needConfirm": false,
                        "columns": [
                          {
                            "label": "A",
                            "name": "a",
                            "type": "input-text"
                          },
                          {
                            "label": "B",
                            "name": "b",
                            "type": "select",
                            "options": [
                              "b1",
                              "b2",
                              "b3"
                            ]
                          },
                          {
                            "label": "C",
                            "name": "c",
                            "type": "checkbox"
                          }
                        ]
                      }
                    ]
                  }
                }
              }
            ]
          }
        ]
      }
    ]
  }
}
```
