---
title: 表单未保存拦截
description: 陈文豪
type: 0
group: ⚙ 最佳实践
menuName: 表单未保存拦截
icon:
order: 22
---

<div><font color=#978f8f size=1>贡献者：陈文豪</font> <font color=#978f8f size=1>贡献时间: 2024/10/09</font></div>

## 功能描述

在使用`form`表单组件时，如果用户操作过表单数据，但是还未点击提交按钮，此时如果用户关闭页面或者跳转页面，系统会弹出提示框，提示用户表单数据未保存。

## 实际场景

1. 场景描述：从列表页点击”新增“按钮跳转到新增页面，新增页面是一个`form`表单页，如果用户在表单页面进行了操作，但是没有点击提交按钮，此时用户点击关闭页面或者跳转页面，系统会弹出提示框，提示用户表单数据未保存。

2. 场景链接：[催收平台/费用管理/供应商管理](http://moka.dmz.sit.caijj.net/collectionui/#/supplierManage)

3. 复现步骤：
    - 点击上述链接
    - 点击“新增”按钮
    - 点击“返回”按钮，如果表单有操作过，需要弹出提示框

![拦截提示图片](/dataseeddesigndocui/public/assets/formModifiedPrompt/1.png)

## 实践代码
要实现表单未保存拦截功能，需要配置2个地方：
1. `form`组件配置`"promptPageLeave": true`，表示开启表单未保存拦截功能。
2. `env`的`blockRouting`方法，用于拦截用户离开当前页面的操作。


### 第一步：promptPageLeave 属性配置
`form`组件配置`"promptPageLeave": true`。
```json
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "showLabelColon": true,
    "promptPageLeave": true, // form 还没保存，即将离开页面前是否弹框确认。
    "promptPageLeaveMessage": "表单操作过还未保存，确定要离开？？？", // 弹框提示语
    "body": [
      {
        "type": "input-text",
        "name": "name",
        "label": "姓名"
      },
      {
        "name": "email",
        "type": "input-email",
        "label": "邮箱"
      }
    ]
  }
}
```

### 第二步：env 配置
使用createENV时，只需要传入`umiVersion`、`blockUrlList`和`history`参数即可。`@dataseed/amis-utils`版本升级到`1.67.0`以上。

```js
import { history } from 'umi';
import { createENV } from '@dataseed/amis-utils';

const env = createENV({
  history, // history路由操作对象
  umiVersion: '3', // umi版本，默认3
  blockUrlList: ['/form/add'], // 需要拦截的页面列表，支持动态路由。目前只支持hash路由
})
```

示例：

```schema
{
  "type": "page",
  "body": [
    {
      "label": "跳转",
      "type": "button",
      "level": "info",
      "actionType": "link",
      "link": "/amis/zh-CN/components/page"
    },
    {
      "type": "form",
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
      "showLabelColon": true,
      "promptPageLeave": true, // form 还没保存，即将离开页面前是否弹框确认。
      "promptPageLeaveMessage": "表单操作过还未保存，确定要离开？？？", // 弹框提示语
      "body": [
        {
          "type": "input-text",
          "name": "name",
          "label": "姓名"
        },
        {
          "name": "email",
          "type": "input-email",
          "label": "邮箱"
        }
      ]
    }
  ]
}
```

<font color=red>**注意事项：**</font>  
1. umi3.x 版本，用户进入表单页面后，如果刷新页面，浏览器后退拦截操作会失效。
2. umi4.x 版本，页面配置拦截之后，用户刷新页面或关闭浏览器页面时，即使没有操作过表单，也会触发浏览器系统拦截弹窗。
3. 通过按钮跳转页面时，请使用传给 createENV 时的`history`对象。


<!-- **2、未使用createENV时：**
未使用`createENV`时，需要自己实现`env`的`blockRouting`方法。`blockRouting`方法内部的实现和`umi`版本有关，下面分别给出`umi3.x`和`umi4.x`的实现方式。

**umi3.x 版本**
```js
import { history } from 'umi';
import { match } from 'path-to-regexp';

const env = {
  blockRouting: (fn) => {
    // fn 是一个回调函数，执行后，如果表单操作过，返回提示语；如果没操作过返回 undefined

    const BLOCK_URLS = ['/form/add'];
    // 如果当前页面地址在 BLOCK_URLS 里面，则拦截
    const needPrompt = BLOCK_URLS.some((url) => {
      const m = match(url);
      return m(window.location.hash.slice(1));
    });
    if (!needPrompt) {
      return;
    }

    /**
     * 利用history.block回调，在离开当前页面时，根据是否触发拦截条件询问用户是否离开。
     * 即fn返回字符串时，用来作为拦截提示；如果返回false，则不拦截
     */
    const unblock = history.block((location, action) => {
      return fn()
    });

    return () => {
      unblock?.();
    }
  },
}
```

**umi4.x 版本**
```js
import { history } from 'umi';
import { match } from 'path-to-regexp';

const env = {
  blockRouting: (fn) => {
    // fn 是一个回调函数，执行后，如果表单操作过，返回提示语；如果没操作过返回 undefined

    const BLOCK_URLS = ['/form/add'];
    // 如果当前页面地址在 BLOCK_URLS 里面，则拦截
    const needPrompt = BLOCK_URLS.some((url) => {
      const m = match(url);
      return m(window.location.hash.slice(1));
    });
    if (!needPrompt) {
      return;
    }

    const unblock = history.block((tx) => {
      const url = tx.location.pathname; // 即将跳到的页面地址

      if (fn() !== undefined) {
        const tip = fn(); // 获取拦截提示语
        const confirmed = window.confirm(tip);
        if (!confirmed) {
          // 点击取消，则不跳转
          return;
        }
      }
      unblock();
      // NOTE: 通过 setTimeout() 确保在解除拦截后再重试导航，避免立即调用可能导致的不一致行为。
      setTimeout(() => tx.retry(), 300); // 延时调用 tx.retry() 进行跳转
    });

    return () => {
      unblock?.();
    }
  },
}
``` -->

## 代码分析

- `env`的`blockRouting`配置是`amis`本身提供的一种能力，可以获取到表单是否操作过，然后自己实现拦截逻辑。
- `history`是`umi`提供的路由管理工具，`history.block`方法可以拦截用户离开当前页面的操作。


参考文档：

1. [umijs](https://umijs.org/docs/api/api#history)
2. [history](https://github.com/remix-run/history)
