---
title: 关于最佳实践
description:
type: 0
group: ⚙ 最佳实践
menuName: 关于最佳实践
icon:
order: 99
---

用于快速实现特定业务场景的步骤和方法

## 基本概念

大家都知道**程序 = 算法 + 数据结构**，**算法**指的是解决特定问题的步骤和方法，**数据结构**指的是数据与数据之间的逻辑关系；目前大家对组件库的**数据结构**（组件用法，数据域等）使用已经驾轻就熟了，但是对于组件库的**算法**（处理特定业务场景的步骤和方法）还没有精炼出来，每个人在实现相同的业务场景在可能需要花费大量重复时间，同时实现出来的方法可能也不一样，而最佳实践就是为了帮助开发者快速实现特定业务场景，希望大家努力共建，共同实现我们的最优**算法**～

## 贡献排行

<table>
    <tr>
        <th>贡献者</th>
        <th>最佳实践</th>
        <th>贡献数量</th>
    </tr>
    <tr>
        <td rowspan="5">常鹏元</td>
        <td><a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/schemaApi">根据数据渲染动态 Schema</a></td>
        <td rowspan="5">5</td>
    </tr>
    <tr>
        <td>
            <a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/logDemo">轮训日志场景</a>
        </td>
    </tr>
    <tr>
        <td>
            <a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/inputTableDynamicColumns">InputTable 动态列</a>
        </td>
    </tr>
     <tr>
        <td>
            <a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/practiceCacheApi">接口数据缓存</a>
        </td>
    </tr>
     <tr>
        <td>
            <a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/dataDomain">数据域中取不到最新数据的问题汇总</a>
        </td>
    </tr>
    <tr>
        <td rowspan="4">庞金明</td>
        <td><a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/practice6">Dialog 弹窗列表选择</a></td>
        <td rowspan="4">4</td>
    </tr>
    <tr>
        <td><a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/practice9">Formula 反显表单项的计算值</a></td>
    </tr>
    <tr>
        <td><a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/multiInitApi">页面展示依赖多个 Api 数据</a></td>
    </tr>
    <tr>
        <td><a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/crudSubTableSelection">Crud 嵌套子表格批量选中</a></td>
    </tr>
    <tr>
        <td rowspan="4">卢帅兵</td>
        <td><a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/practiceEditorFormat">Editor自定义按钮JSON格式化</a></td>
        <td rowspan="4">4</td>
    </tr>
    <tr>
        <td><a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/practice17">虚拟滚动一行展示多个</a></td>
    </tr>
    <tr>
        <td><a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/practice18">定时刷新API</a></td>
    </tr>
    <tr>
        <td style="text-decoration: line-through;"><a>轮询调用接口（替换为轮训日志场景）</a></td>
    </tr>
    <tr>
        <td rowspan="3">刘梅</td>
        <td><a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/practice11">Combo 与其他表单项联动</a></td>
        <td rowspan="3">3</td>
    </tr>
    <tr>
        <td><a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/practice12">事件对应的响应动作防抖</a></td>
    </tr> 
    <tr>
        <td><a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/practice16">InputTable 搜索列表项</a></td>
    </tr>
    <tr>
        <td rowspan="2">陈兴</td>
        <td><a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/practice7">相互排除的下拉列表</a></td>
        <td rowspan="2">2</td>
    </tr>
        <tr>
        <td><a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/practice8">批量操作按钮依赖条件禁用</a></td>
    </tr>
    <tr>
        <td rowspan="2">田鹏飞</td>
        <td><a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/practice1">Select 值发生变化时弹窗提醒</a></td>
        <td rowspan="2">2</td>
    </tr>
    <tr>
        <td><a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/practice2">CRUD 操作列按钮点击根据接口返回结果显示不同的弹窗</a></td>
    </tr>
    <tr>
      <td>顾芸铭</td>
        <td><a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/practice14">自定义组件触发及监听事件</a></td>
       <td>1</td>
     </tr>
      <tr>
       <td rowspan="2">曾三湘</td>
        <td><a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/practice10">自定义组件如何定义及触发动作</a></td>
        <td rowspan="2">2</td>
     </tr>
    <tr>
        <td><a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/practiceSpecialSub">提交表单，表单项包含$符号</a></td>
    </tr>
     <tr>
      <td rowspan="3">毛甜甜</td>
        <td><a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/practice15">InputTable 多列数据通过接口联动自动填充</a></td>
        <td rowspan="3">3</td>
    </tr>
     <tr>
        <td><a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/practiceSelectDefaultValue">CRUD 服务端设置 filter 默认值</a></td>
    </tr>
    <tr>
        <td><a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/batchButtonAndSearchParams">CRUD 批量操作与搜索条件联动</a></td>
    </tr>
    <tr>
        <td rowspan="1">王梓仲</td>
        <td><a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/practiceIframe">Amis 与 IFrame之间通信</a></td>
        <td>1</td>
    </tr>
    <tr>
        <td rowspan="1">陈文豪</td>
        <td><a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/formModifiedPrompt">表单未保存拦截</a></td>
        <td>1</td>
    </tr>
    <tr>
        <td rowspan="1">吴姚芳</td>
        <td style="text-decoration: line-through;"><a>InputTable 多列数据联动（合并至 Formula 反显表单项的计算值）</a></td>
        <td>1</td>
    </tr>
    <tr>
        <td rowspan="2">郝亚雷</td>
        <td style="text-decoration: line-through;">
            <a>使用过滤器，更改 Select 组件的枚举值(合并至相互排除的下拉列表)</a>
        </td>
        <td rowspan="2">2</td>
    </tr>
    <tr>
        <td><a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/stepAndCrud">动态渲染step组件并联动crud</a></td>
    </tr>
     <tr>
        <td rowspan="1">张广森</td>
        <td >
            <a href="/dataseeddesigndocui/#/amis/zh-CN/bestpractices/practiceTableIntervalStatus">表格单行轮询任务状态</a>
        </td>
        <td>1</td>
    </tr>
</table>
