---
title: InputTable 动态列
description: 常鹏元
type: 0
group: ⚙ 最佳实践
menuName: InputTable 动态列
icon:
order: 15
---

<div><font color=#978f8f size=1>贡献者：常鹏元</font> <font color=#978f8f size=1>贡献时间: 2024/08/27</font></div>

## 功能描述

一些业务场景中需要动态生成`input-table`动态列，可以通过`service`的`schemaApi`实现

## 实践代码

**注意：使用此写法注意将amis的env使用`amis-utils`中暴露的`createEnv`来创建，否则不会生效。并且amis的版本需要在1.59.0以上**

须先检查自己umi项目中`layouts`文件中下发到页面组件的env对象是否是使用`amis-utils`中暴露的`createEnv`来创建的，如果不是，则需要修改。

核心代码

```js
{
  "type": "service",
  "schemaApi": {
    "url": "/", // 通过url配置参数使得数据域上的columns发生变化时会自动计算schema
    "trackExpression": "${columns}", // 监听columns的变化，变化时重新计算schema
    "dataProvider": (tdata) => { // 计算渲染的schema
      return {
        type: 'input-table',
        name: 'table',
        columns: tdata
      }
    }, // 设置了 dataProvider 之后不会发送请求，1.59.0以上版本
    "tdata": "${columns}" // 将columns作为tdata传入，在adaptor中通过tdata获取columns
  }
}
```

在`dataProvider`中计算schema和在`adaptor`中计算效果是一样的，下面示例的写法和上面是等价的。

```schema
{
  "type": "page",
  "id": "page",
  "data": {
    "table": [
      {
        "a": "a1",
        "b": "b1",
        "c": {
          "c1": "123",
          "c2": "222"
        }
      }
    ],
    "columns": [
      {
        "name": "a",
        "label": "A"
      },
      {
        "name": "b",
        "label": "B"
      },
      {
        "type": "combo",
        "name": "c",
        "multiLine": true,
        "multiple": false,
        "label": "C",
        "required": true,
        "items": [
          {
            "type": "input-text",
            "name": "c1",
            "required": true
          },
          {
            "type": "input-text",
            "name": "c2",
            "required": true
          }
        ]
      }
    ]
  },
  "body": [
    {
      "type": "button",
      "label": "更新columns",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "setValue",
              "componentId": "page",
              "args": {
                "value": {
                  "columns": [
                    {
                      "name": "a",
                      "label": "A"
                    },
                    {
                      "name": "b",
                      "label": "B"
                    },
                  ]
                }
              }
            }
          ]
        }
      }
    },
    {
      "type": "form",
      "body": [
        {
          "type": "service",
          "schemaApi": {
            "url": "/",
            "trackExpression": "${columns}",
            "dataProvider": true,
            "tdata": "${columns}",
            "adaptor": "return { type: 'input-table', name: 'table', columns: payload }"
          }
        }
      ]
    }
  ]
}
```

## 代码分析

- **动态渲染** 可以利用`service`组件的`schemaApi`属性来实现，最后一列的单元格可以渲染一个`service`组件，配置`schemaApi`，在`schemaApi`中设置`dataProvider`属性，配置了此属性后不会发送请求（这个特性是在`createEnv`中实现的，故依赖`createEnv`），通过`tdata`将数据进行传递，此时在`adaptor`中的`payload`就是我们传如的`tdata`，可以根据传入的数据拼接出需要渲染的`schema`然后返回。

参考文档

1. [service 动态渲染页面](/dataseeddesigndocui/#/amis/zh-CN/components/service?anchor=动态渲染页面)
