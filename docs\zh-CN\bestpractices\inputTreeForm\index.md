---
title: InputTree 切换 Form 合并数据提交
description: 吴廷国
type: 0
group: ⚙ 最佳实践
menuName: InputTree 切换 Form 合并数据提交
icon:
order: 9
---

<div><font color=#978f8f size=1>贡献者：吴廷国</font> <font color=#978f8f size=1>贡献时间: 2024/11/22</font></div>

## 功能描述

`input-tree` 每一个节点对应一个 Form 表单，每一个 Form 都能编辑信息。对于大多数情况来说，都是每个Form都是独立提交。在某些情况下，需要将每一个 Form 编辑的数据合并一次提交给服务端统一处理。

## 实践场景

1. 场景链接：[APP一站式/元数据管理/组件管理/埋点编辑定义页](http://moka.dmz.sit.caijj.net/clientsuiteui/#/componentMgr/pointEdit?componentCodes=fundTab)
2. 操作流程：点击切换树节点,编辑当前树节点form，最后再点击保存。

![](/dataseeddesigndocui/public/assets/inputTreeForm/2.png)

## 实现流程图

点击左侧树节点，切换右侧表单。右侧表单根据当前选中树节点信息初始化数据。编辑表单后，点击“提交”按钮，将所有表单数据合并一起提交。实现流程图如下：

![](/dataseeddesigndocui/public/assets/inputTreeForm/1.png)

## 核心代码

```js

// 进行数据的存储和维护，和判断是否对全局数据的更新。
registerFilter(
  'formatEventsList',
  (
    inputTableData = [], // 当前inputTable的数据
    curCode, // 当前inputTree的树节点code
    allDataList = [], // 顶层维护的数据
  ) => {
    // 先将当前组件code的源数据数组找出来
    const filterCurIdArr = allDataList.filter(
      (item) => item.layerCode === curCode,
    );
    // 因为输入框的值是动态生成的，所以给inputTableData的每一项加上当前的唯一ID值, 通过设置ID将当前组件code找的数据找出来，再进行数据过滤
    const newData = inputTableData.map((item) => {
      return {
        ...item,
        layerCode: curCode,
      };
    });
    // 点击切换tab时，会触发表单的change事件，初始化时inputTableData为空数组,当把Table数据删除完后inputTableData也为空数组,所以要通过新老数据与进行对比，如果相等，说明是inputTable的第一次初始化，直接返回，不对顶层数据进行更新
    const isEqual =
      newData.length === filterCurIdArr.length &&
      newData.every(
        (item, index) =>
          item.id === filterCurIdArr[index].id &&
          JSON.stringify(item) === JSON.stringify(filterCurIdArr[index]),
      );
    // 点击切换tab时，会触发表单的change事件，这里通过设置唯一值layerCode了判断当前是否进行了树结构的切换，如果只是切换，就不对顶层数据进行更新
    const isTabChange = newData.findIndex((item) => item.layerCode === curCode);
    // 当前数据没有变更，且数据有值，说明是inputTable的初始化，直接返回，不对顶层数据进行更新
    if (isEqual || (isTabChange === -1 && newData.length)) {
      return allDataList;
    }
    // 过滤掉allDataList中的当前table项数据
    const cmptTrackEventsList = allDataList.filter(
      (item) => item.layerCode !== curCode,
    );
    // 将当前数据更新到最上层
    return [...newData, ...cmptTrackEventsList];
  },
);

// 过滤出数据中的form需要展示的数据
registerFilter('filterFormEvents', (curCode, treeData, allDataList) => {
  return {
    componentName: treeData?.detailValue?.componentName,
    table: allDataList.filter((item) => item.layerCode === curCode),
  };
});

核心配置:
 {
  type: 'page',
  id: 'pageId',
  body: [

    {
      type: 'left-right-container',
      left: {
        type: 'form',
        body:{
              type: 'input-tree',
              source: '${options}',
              onEvent: {
                change: {
                  actions: [
                    {
                   // 记录当前树节点的code值，通过当前code值对全局数据进行筛选或编辑
                      actionType: 'setValue', 
                      componentId: 'pageId',
                      args: {
                        value: {
                          curCode: '${event.data.value.codeValue}',
                        },
                      },
                    },
                    {
                      // 过滤出数据中的form需要展示的数据将数据传递到需要对应form表单中
                      actionType: 'setValue',
                      componentId: 'myForm',
                      args: {
                        value:
                          '${event.data.tree2.codeValue|filterFormEvents:${event.data.tree2}:${event.data.allDataList}}',
                      },
                    },
                  ],
                },
              },
            },
      },
      right: [
        {
          type: 'form',
          id: 'myForm',
          onEvent: {
            change: {
              actions: [
                {
                  // 当表单值发生变化时，根据当前组件code将当前数据跟新到最上层,跟新维护的全局数据
                  actionType: 'setValue', 
                  componentId: 'pageId',
                  args: {
                    value: {
                      allDataList:
                        '${event.data.table|formatEventsList:${event.data.curCode}:${event.data.allDataList}}',
                    },
                  },
                },
              ],
            },
          },
        },
      ],
    },
  ],
}

```

操作步骤: 点击切换input-tree树节点,编辑当前树节点form，最后点击保存全局提交。

```schema

// 进行数据的存储和维护，和判断是否对全局数据的更新。
registerFilter(
  'formatEventsList',
  (
    inputTableData = [], // 当前inputTable的数据
    curCode, // 当前inputTree的树节点code
    allDataList = [], // 顶层维护的数据
  ) => {
    // 先将当前组件code的源数据数组找出来
    const filterCurIdArr = allDataList.filter(
      (item) => item.layerCode === curCode,
    );
    // 因为输入框的值是动态生成的，所以给inputTableData的每一项加上当前的唯一ID值, 通过设置ID将当前组件code找的数据找出来，再进行数据过滤
    const newData = inputTableData.map((item) => {
      return {
        ...item,
        layerCode: curCode,
      };
    });
    // 点击切换tab时，会触发表单的change事件，初始化时inputTableData为空数组,当把Table数据删除完后inputTableData也为空数组,所以要通过新老数据与进行对比，如果相等，说明是inputTable的第一次初始化，直接返回，不对顶层数据进行更新
    const isEqual =
      newData.length === filterCurIdArr.length &&
      newData.every(
        (item, index) =>
          item.id === filterCurIdArr[index].id &&
          JSON.stringify(item) === JSON.stringify(filterCurIdArr[index]),
      );
    // 点击切换tab时，会触发表单的change事件，这里通过设置唯一值layerCode了判断当前是否进行了树结构的切换，如果只是切换，就不对顶层数据进行更新
    const isTabChange = newData.findIndex((item) => item.layerCode === curCode);
    // 当前数据没有变更，且数据有值，说明是inputTable的初始化，直接返回，不对顶层数据进行更新
    if (isEqual || (isTabChange === -1 && newData.length)) {
      return allDataList;
    }
    // 过滤掉allDataList中的当前table项数据
    const cmptTrackEventsList = allDataList.filter(
      (item) => item.layerCode !== curCode,
    );
    // 将当前数据跟新到最上层
    return [...newData, ...cmptTrackEventsList];
  },
);

// 过滤出数据中的form需要展示的数据
registerFilter('filterFormEvents', (curCode, treeData, allDataList) => {
  return {
    componentName: treeData?.detailValue?.componentName,
    table: allDataList.filter((item) => item.layerCode === curCode),
  };
});
return {
  type: 'page',
  id: 'pageId',
  data: {
    allDataList: [], // 维护的全局数据
    options: [
      {
        label: '资方切换tab',
        value: 'fundTab',
        detailValue: {
          componentName: '资方切换tab-fundTab',
        },
        codeValue: 'fundTab',
        children: [
          {
            label: '资方列表子组件',
            value: 'fund#List_0',
            detailValue: {
              componentName: '资方列表子组件-fund#List',
            },
            codeValue: 'fund#List_0',
            children: [
              {
                label: '优惠券列表项',
                value: 'fund#List_0_fund#List#Item_0',
                detailValue: {
                  componentName: '优惠券列表项',
                },
                codeValue: 'fund#List#Item',
                children: [],
                icon: 'file',
              },
            ],
            icon: 'file',
          },
        ],
      },
    ],
    behaviorList: [
      {
        label: '曝光',
        value: 'show',
      },
      {
        label: '点击',
        value: 'click',
      },
    ],
  },
  initApi: {
    method: 'get',
    url: 'https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/page/initData',
    adaptor(payload, response) {
      const resData = payload?.data;
      return {
        data: {
          allDataList: [
            {
              layerCode: 'fundTab',
              behavior: 'show',
            },
            {
              layerCode: 'fundTab',
              behavior: 'show',
            },
            {
              layerCode: 'fund#List_0',
              behavior: 'click',
            },
            {
              layerCode: 'fund#List#Item',
              behavior: 'click',
            },
          ],
        },
      };
    },
  },
  body: [
    {
      type: 'static-json',
      source: '${allDataList}',
      label: '表单提交数据 allDataList',
    },
    {
      type: 'title',
      iconConfig: true,
      title: '页面大标题名称大标题名称',
      subTitle: '我是小标题',
      actions: [
        {
          type: 'button',
          level: 'primary',
          label: '保存',
          onEvent: {
            click: {
              actions: [
                {
                  actionType: 'ajax',
                  args: {
                    api: {
                      method: 'POST',
                      url: 'https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2',
                      data: {
                        allDataList: '${allDataList}', // 拿到allDataList全局保存提交
                      },
                    },
                  },
                },
              ],
            },
          },
        },
      ],
    },
    {
      type: 'left-right-container',
      autoFillHeight: true,
      defaultWidth: 400,
      left: {
        type: 'form',
        actions: [],
        body: {
          type: 'flex',
          direction: 'column',
          gap: true,
          items: [
            {
              type: 'input-tree',
              name: 'tree2',
              id: 'tree2',
              label: false,
              multiple: false,
              searchable: true,
              autoCheckChildren: false,
              joinValues: false,
              autoFillHeight: true,
              source: '${options}',
              onEvent: {
                change: {
                  actions: [
                    {
                      actionType: 'setValue', // 记录当前树节点的code值，通过当前code值对全局数据进行筛选或编辑
                      componentId: 'pageId',
                      args: {
                        value: {
                          curCode: '${event.data.value.codeValue}',
                        },
                      },
                    },
                    {
                      actionType: 'setValue', // 过滤出数据中的form需要展示的数据将数据传递到需要对应form表单中
                      componentId: 'myForm',
                      args: {
                        value:
                          '${event.data.tree2.codeValue|filterFormEvents:${event.data.tree2}:${event.data.allDataList}}',
                      },
                    },
                  ],
                },
              },
            },
          ],
        },
      },
      right: [
        {
          type: 'form',
          id: 'myForm',
          labelWidth: 60,
          actions: [],
          body: [
            {
              type: 'group-container',
              activeKey: ['1'],
              items: [
                {
                  key: '1',
                  header: {
                    title: '第一步，基础信息',
                    subTitle: '这是一段提示',
                  },
                  body: [
                    {
                      type: 'group',
                      body: [
                        {
                          type: 'input-text',
                          name: 'componentName',
                          label: '名称',
                          static: true,
                        },
                      ],
                    },
                  ],
                },
                {
                  key: '2',
                  header: {
                    title: '第二步，复杂信息',
                  },
                  body: {
                    type: 'input-table',
                    name: 'table',
                    removable: true,
                    editable: true,
                    addable: true,
                    needConfirm: false,
                    label: false,
                    id: 'form-table-container',
                    columns: [
                      {
                        name: 'behavior',
                        label: '动作',
                        quickEdit: {
                          type: 'select',
                          source: '${behaviorList}',
                        },
                      },
                    ],
                  },
                },
              ],
            },
          ],
          onEvent: {
            change: {
              actions: [
                {
                  actionType: 'setValue',  // 当表单值发生变化时，根据当前组件code将当前数据跟新到最上层,跟新维护的全局数据
                  componentId: 'pageId',
                  args: {
                    value: {
                      allDataList:
                        '${event.data.table|formatEventsList:${event.data.curCode}:${event.data.allDataList}}',
                    },
                  },
                },
              ],
            },
          },
        },
      ],
    },
  ],
}
```

## 代码分析

1. 通过自定义过滤器`formatEventsList` 来对表单数据进行数据的存储和维护，和判断是否对全局数据的更新。
2. 通过自定义过滤器`filterFormEvents` 来对表单数据进行数据状态的过滤，实现对数据状态的展示和更新。
