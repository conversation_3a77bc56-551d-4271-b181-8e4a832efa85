---
title: 轮训日志场景
description: 常鹏元
type: 0
group: ⚙ 最佳实践
menuName: 轮训日志场景
icon:
order: 9
---

<div><font color=#978f8f size=1>贡献者：常鹏元</font> <font color=#978f8f size=1>贡献时间: 2024/08/08</font></div>

## 功能描述

一些业务场景中会有需要轮训日志的场景，比如：运行日志等功能，需要轮训将最新的日志展示在页面上

## 实际场景

1. 场景链接：[大数据一站式/数据研发平台/离线任务同步](http://moka.dmz.sit.caijj.net/analytoolui/#/data-task)

2. 复现步骤：
  - 点击列表操作列试运行

![试运行日志](/dataseeddesigndocui/public/assets/logDemo/1.png)

## 实践代码

可以使用`log`组件的轮训模式，其中`amis`版本需要在`1.58.0`以上

```js
{
  "type": "log",
  "height": 300,
  "interval": 1000, // 设置 interval 就会开启轮训，会按照设置的时间去轮训接口
  "stopAutoRefreshWhen": "${keywords > 20}", // 配置停止条件，这里当接口返回的keyworks大于20的时候，停止轮训
  "highlightConfig": { // 日志中有部分关键字需要高亮的场景可以配置高亮关键字，目前支持 success、error、warning、info 四种高亮，支持正则匹配
    "success": [
      "Page"
    ]
  },
  "source": {
    "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/page/initData?keywords=${keywords|default:0}",
    "method": "get",
    "responseData": {
      "content": "${`${api.query.keywords} ${title}`}", // log组件默认读取接口返回的content字段，也可以通过 contentField 配置读取其他字段
      "keywords": "${+api.query.keywords + 1}"
    }
  }
}
```

```schema: scope="body"
{
  "type": "log",
  "height": 300,
  "interval": 1000,
  "stopAutoRefreshWhen": "${keywords > 20}",
  "highlightConfig": {
    "success": [
      "Page"
    ]
  },
  "operation": ["restart"],
  "source": {
    "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/page/initData?keywords=${keywords|default:0}",
    "method": "get",
    "responseData": {
      "content": "${`${api.query.keywords} ${title}`}",
      "keywords": "${+api.query.keywords + 1}"
    }
  }
}
```

## 代码分析

1. 设置 `interval` 组件开启轮训模式
2. 设置 `stopAutoRefreshWhen` 配置停止条件
3. 设置 `highlightConfig` 配置高亮关键字（根据需求，看是否需要）

参考文档

1. [日志组件](/dataseeddesigndocui/#/amis/zh-CN/components/log?anchor=轮训模式)
