---
title: 页面展示依赖多个 Api 数据
description: 庞金明
type: 0
group: ⚙ 最佳实践
menuName: 页面展示依赖多个 Api 数据
icon:
order: 13
standardMode: true
---

<div><font color=#978f8f size=1>贡献者：庞金明</font> <font color=#978f8f size=1>贡献时间: 2024/08/19</font></div>

## 功能描述

某些页面展示需要依赖多个基础数据源。比如，CRUD列表页，查询条件的 状态枚举值在一个接口中，列展示的 公司ID枚举值放在另一个接口。

> 一般情况下，新需求可以和服务端沟通，将配置类的数据都整合到一个接口中。此场景是某些历史页面改造时，或者服务端已存在接口复用时，可能遇到这种情况。

## 实际场景

1. 场景链接：[账务平台/执行运营/合作方清结算/预付款核销管理](http://moka.dmz.sit.caijj.net/accountingui/#/advance-payment-clear-off)

2. 复现步骤：
    - 点击链接即可

![预付款核销管理](/dataseeddesigndocui/public/assets/multiInitApi/1.png "列表页面")

## 实践代码

使用 `page.initApi` 属性，与 `service` 嵌套， 结合 `api.sendOn`，`api.adaptor` 属性，可实现多个接口串行请求组装。

```js
// 简化后的核心代码部分，完整代码参考DEMO
{
  type: 'page',
  loadingConfig: { // 不显示 loading
    "show": false
  },
  initApi: {
    url: '/baseConfig', // 基础配置API，返回枚举值信息
    adaptor: (payload) => {
      return {
        data: {
          // 组装 baseConfig （key值可以任意自定义）
          baseConfig: {
            ...payload.data,
            userMap: {}, // 映射用户信息
            statusOptions: [] // 映射状态信息
          }
        }
      }
    }
  },
  body: {
    type: 'service',
    loadingConfig: { // 不显示 loading
      "show": false
    },
    api: {
      url: '/companyConfig', // 公司配置API，返回公司枚举值信息
      adaptor: (payload) => {
        return {
          data: {
            // 组装 companyConfig （key值可以任意自定义）
            companyConfig: {
              ...payload.data,
              companyMap: {} // 映射公司信息
            }
          }
        }
      }
    },
    body: {
      type: 'crud',
      api: {
        url: '/list',
        // sendOn 依赖 基础数据API接口返回之后，才发起请求
        sendOn: '${!!baseConfig && !!companyConfig}',
        // 需要配置 trackExpression 之后，sendOn 才会生效。（原因见“代码分析”部分）
        trackExpression: "${!!baseConfig && !!companyConfig}",
        // 将基础数据使用 tdata 透传到 adaptor 中
        tdata: {
          baseConfig: '${baseConfig}',
          companyConfig: '${companyConfig}'
        },
        adaptor: (payload, source, api) => {
          // 获取基础数据，对CRUD 查询接口进行组装
          const { baseConfig,  companyConfig } = api.tdata
          return {
            data: {
              ...payload.data,
              rows: payload.data.rows?.map((item) => {
                ...item,
                // 将枚举值映射到 对应字段上
                user: baseConfig.userMap[item.xxx],
                company: companyConfig.companyMap[item.xxx]
              })
            }
          }
        }
      },
      columns: [{
        {
          name: "user", // 直接使用映射后的字段名
          label: "用户名称"
        },
        {
          name: "company", // 直接使用映射后的字段名
          label: "公司名称",
        },
      }]
    }
  }
}
```

```schema
{
  "type": "page",
  "title": "CRUD列表页依赖多个API",
  "loadingConfig": {
      "show": false
  },
  "initApi": {
    "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getTreeOptions",
    "adaptor":"return { data: { baseConfig: { userMap: { \'Win 95+\': \'张三\', \'Win XP\': \'李四\' }, statusOptions: [{ label: \'通过\', value: \'默认\', color: \'success\' }, { label: \'拒绝\', value: \'危险\', color: \'error\', }] } } }"
  },
  "body": {
    "type": "service",
    "loadingConfig": {
      "show": false
    },
    "api": {
      "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getOptions",
      "adaptor": "return { data: { companyConfig: { companyMap: { \'A\': \'百度\', \'X\': \'谷歌\' } } } }"
    },
    "body": {
      "type": "crud",
      "autoGenerateFilter": {
        "showBtnToolbar": false,
      },
      "api": {
        "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
        "data": {
          "page": "${page}",
          "perPage": "${perPage}",
          "badgeText": "${badgeText}"
        },
        "sendOn": "${!!baseConfig && !!companyConfig}",
        "trackExpression": "${!!baseConfig && !!companyConfig}",
        "tdata": {
          "baseConfig": "${baseConfig}",
          "companyConfig": "${companyConfig}"
        },
        "adaptor": "payload.data.rows = payload.data.rows.map((item) => ({ ...item, user: api.tdata.baseConfig.userMap[item.platform], company: api.tdata.companyConfig.companyMap[item.grade]  })); return payload;"
      },
      "columns": [
        {
          "name": "id",
          "label": "ID"
        },
        {
          "name": "user",
          "label": "用户名称"
        },
        {
          "name": "company",
          "label": "公司名称",
        },
        {
          "name": "badgeText",
          "label": "状态",
          "type": "mapping",
          "source": "${baseConfig.statusOptions}",
          "itemSchema": {
              "type": "tag",
              "label": "${label}",
              "color": "${color}"
          },
          "searchable": {
              "type": "select",
              "clearable": true,
              "source": "${baseConfig.statusOptions}"
          }
        }
      ]
    }
  }
}
```

## 代码分析

当存在依赖多个API接口时，可使用 `page.initApi` 配合 `service` 嵌套来实现。默认情况下，多个 `service` 嵌套，都是并行请求。需要进行串行请求时，可结合 `api.sendOn` 与 `api.adaptor` 组装返回结果与配置接口发起的条件的方式来实现。

**注意：**

`api.sendOn` 在api依赖表达式时下才会生效，仅配置 `sendOn` 时，只有在首次生效。api配置表达式有以下2种方式：

- 方式1: `api.trackExpression: "${xxxx}"` 配置表达式
- 方式2: `api.url: "/xxx?a=${xxx}"` url中配置表达式

原因：不配置 `sendOn` 时，api依赖表达式值变化，则会重新发起请求。但配置了 `sendOn` 时， `api依赖表达式值变化` + `api.sendOn值为true` 同时满足，api才会重新发起请求。

参考文档

1. [api.sendOn 配置请求条件](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api?anchor=配置请求条件)
2. [api.trackExpression 跟踪数据自动刷新](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api?anchor=跟踪数据自动刷新)
3. [接口联动](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/linkage?anchor=接口联动)
4. [service组件](/dataseeddesigndocui/#/amis/zh-CN/components/service)
5. [page组件](/dataseeddesigndocui/#/amis/zh-CN/components/page?anchor=页面初始化请求)
