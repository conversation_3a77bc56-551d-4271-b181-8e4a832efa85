---
title: 自定义组件如何定义及触发动作
description: 曾三湘
type: 0
group: ⚙ 最佳实践
menuName: 自定义组件如何定义及触发动作
icon:
order: 6
standardMode: true
---

<div><font color=#978f8f size=1>贡献者：曾三湘</font> <font color=#978f8f size=1>贡献时间: 2024/07/24</font></div>

## 功能描述

在某些复杂场景，`DATASEED DESIGN`内置组件本身不能满足时，我们需要自定义一些组件。
因此就会涉及到内置组件和自定义组件之间的交互，比如内置组件触发自定义组件的动作。
(注意：**自定义的组件只能是react类组件，函数组件暂时不支持** )

## 实际场景
1. 场景描述：`dialog`弹窗点击确认，调用接口成功之后，需要触发自定义组件的动作

2. 场景链接：[大数据\_新/数据目录/数据发现/详情（数据探查 tab/发起数据探查）](http://moka.dmz.sit.caijj.net/analytoolui/#/fetch-detail/home?assetType=DATASET&engineType=DATAPHIN&metaCode=meta_sit.dim_dataphin_node)

3. 复现步骤：
   - 点击上述链接
   - 点击发起数据探查，弹出数据探查弹窗
   - 选择字段
   - 点击弹窗确定按钮

![点击发起数据探查](/dataseeddesigndocui/public/assets/practice10/1.jpg)

![点击弹窗确定按钮](/dataseeddesigndocui/public/assets/practice10/2.jpg)

## 实践代码
1. 注册自定义组件以及实现

```js
import { Renderer, ScopedContext } from '@dataseed/amis'; //导入ScopedContext
import React from 'react';

// 注册自定义组件
@Renderer({
  type: 'distribution-tab',
  autoVar: true,
})
export default class MyRenderer extends React.Component {
  static contextType = ScopedContext;

  constructor(props, scoped) {
    super(props);

    // 注册scoped
    scoped.registerComponent(this);
  }

  componentWillUnmount() {
    // 页面销毁的时候注销掉
    const scoped = this.context;
    scoped.unRegisterComponent(this);
  }

  //在自定义组件中，doAction方法（固定方法名，不可改）
  doAction(action, data) {
    // actionType：reload 动作
    const { actionType } = action;
    // 在doAction方法里面根据action中的actionType，触发相应动作的方法
    if (actionType === 'reload') {
      // 调用方法
      this.refreshDistribution();
    }
  }

  refreshDistribution = () => {
    // 做reload动作执行后的逻辑
  };

  render() {
    return (
      <div>
         自定义组件代码
      </div>
    );
  }
}
```

2. Schema里使用自定义组件

```js
{
  type: 'page',
  title: '标题',
  body: [
    {
      // 自定义组件的type，对应第一点Renderer里的type
      type: 'distribution-tab', 
      id: 'distribution-tab-id',
    },
    {
      label: '点击', 
      type: 'button',
      actionType: 'dialog', 
      dialog: {
        showCloseButton: false,
        title: '弹框标题',
        api: {
          // ...
        },
        body: {
          type: 'form',
          body: [
            {
              type: 'input-text',
              label: '名字',
              name: 'username',
            },
          ],
          onEvent: {
            submitSucc: {
              //弹窗成功之后调用自定义组件的动作
              actions: [
                {
                  // 提交成功之后触发自定义组件中doAction定义的reload方法
                  actionType: 'reload',
                  // componentId对应自定义组件的id
                  componentId: 'distribution-tab-id',
                },
              ],
            },
          },
        },
      },
    },
  ],
};
```
## 代码分析

1. 内置组件
    - Schema里onEvent事件里触发自定义组件的动作
2. 自定义组件
    - doAction方法监测内置组件触发的动作，根据action里的reload方法，做reload动作执行后的逻辑

