---
title: Combo 与其他表单项联动
description: 刘梅
type: 0
group: ⚙ 最佳实践
menuName: Combo 与其他表单项联动
icon:
order: 8
---

<div><font color=#978f8f size=1>贡献者：刘梅</font> <font color=#978f8f size=1>贡献时间: 2024/07/29</font></div>

## 功能描述

当 combo 组件内某个表单项发生变化时，需要更新与 combo 同级的其他表单项的数据源

## 实际场景

1. 场景链接：[产品运营/协议管理/新协议模版/场景规则管理](http://moka.dmz.sit.caijj.net/esignui/#/contractSceneRuleManage)
2. 复现步骤：
   - 点击上述链接，打开页面
   - 点击查询区域的新增规则按钮，打开弹框
   - 选择协议类型为资方类，选择场景节点，新增一条匹配规则
   - 规则 key 选择资方，此时更新规则 value，规则 value 变化时需要调用接口，更新协议模版的数据源

![选择"空跑结束"数据](/dataseeddesigndocui/public/assets/practice11/1.png "选择'空跑结束'数据")
![再选择一条"已迁移"数据](/dataseeddesigndocui/public/assets/practice11/2.png "再选择一条'已迁移'数据")

## 实践代码

#### 方案一

```js
{
  // 默认为了提高性能，给表单项下发的数据不是最新，只有自己的值变化才是最新的
  // 需要及时的获取其他表单项的值，来更新下拉列表数据源，需要给表单项配置 strictMode 为 false
  "type": "select",
  "name": "template",
  "label": "协议模版",
  "strictMode": false,
  "source": {
    "method": "get",
    "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getOptions?bankValues=${sceneRuleList[0].value}"
  }
}
```


```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "data": {
      "sceneNodeList": [
        {
          "template": "",
          "sceneRuleList": [
            {
              "key": "资方"
            },
            {
              "key": "合作模式"
            }
          ]
        }
      ]
    },
    "body": {
      "type": "combo",
      "name": "sceneNodeList",
      "id": "sceneNodeList",
      "label": "场景节点",
      "subFormMode": "horizontal",
      "subFormHorizontal": {
        "labelWidth": 70
      },
      "multiLine": true,
      "multiple": true,
      "addable": true,
      "items": [
        {
          "type": "combo",
          "name": "sceneRuleList",
          "label": "场景规则",
          "id": "combo",
          "multiLine": true,
          "multiple": true,
          "addable": true,
          "items": [
            {
              "type": "group",
              "body": [
                {
                  "name": "key",
                  "label": "",
                  "type": "input-text"
                },
                {
                  "name": "expression",
                  "label": "",
                  "type": "select",
                  "options": [
                    "=",
                    "!="
                  ]
                },
                {
                  "name": "value",
                  "label": "",
                  "type": "select",
                  "multiple": true,
                  "options": [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                    "6"
                  ]
                }
              ]
            }
          ]
        },
        {
          "type": "select",
          "name": "template",
          "label": "协议模版",
          "strictMode": false,
          "source": {
            "method": "get",
            "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getOptions?bankValues=${sceneRuleList[0].value}"
          }
        }
      ]
    }
  }
}
```

#### 方案二

```js
{
  // 在需要联动其他表单项的组件里监听change事件，触发目标组件的reload动作
  "onEvent": {
    "change": {
      "actions": [
        {
          "actionType": "reload",
          "componentId": "template"
        }
      ]
    }
  }
}
```

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "data": {
      "sceneNodeList": [
        {
          "template": "",
          "sceneRuleList": [
            {
              "key": "资方"
            },
            {
              "key": "合作模式"
            }
          ]
        }
      ]
    },
    "body": {
      "type": "combo",
      "name": "sceneNodeList",
      "id": "sceneNodeList",
      "label": "场景节点",
      "subFormMode": "horizontal",
      "subFormHorizontal": {
        "labelWidth": 70
      },
      "multiLine": true,
      "multiple": true,
      "addable": true,
      "items": [
        {
          "type": "combo",
          "name": "sceneRuleList",
          "label": "场景规则",
          "id": "combo",
          "multiLine": true,
          "multiple": true,
          "addable": true,
          "items": [
            {
              "type": "group",
              "body": [
                {
                  "name": "key",
                  "label": "",
                  "type": "input-text"
                },
                {
                  "name": "expression",
                  "label": "",
                  "type": "select",
                  "options": [
                    "=",
                    "!="
                  ]
                },
                {
                  "name": "value",
                  "label": "",
                  "type": "select",
                  "multiple": true,
                  "options": [
                    "1",
                    "2",
                    "3",
                    "4",
                    "5",
                    "6"
                  ],
                  "onEvent": {
                    "change": {
                      "actions": [
                        {
                          "actionType": "reload",
                          "componentId": "template"
                        }
                      ]
                    }
                  }
                }
              ]
            }
          ]
        },
        {
          "type": "select",
          "name": "template",
          "id": "template",
          "label": "协议模版",
          "source": {
            "method": "get",
            "data": {
              "aa": "${sceneRuleList[0].value}"
            },
            "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getOptions"
          }
        }
      ]
    }
  }
}
```

## 代码分析

- 参考文档

1. [strictMode](/dataseeddesigndocui/#/amis/zh-CN/components/form/formitem?anchor=及时获取其他表单项的值)

