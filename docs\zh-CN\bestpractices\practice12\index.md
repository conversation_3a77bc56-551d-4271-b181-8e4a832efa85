---
title: 事件对应的响应动作防抖
description: 刘梅
type: 0
group: ⚙ 最佳实践
menuName: 事件对应的响应动作防抖
icon:
order: 8
---

<div><font color=#978f8f size=1>贡献者：刘梅</font> <font color=#978f8f size=1>贡献时间: 2024/08/15</font></div>

## 功能描述

点击某个按钮设置 inputTable 的数据，并且需要监听 inputTable 的 change 事件去触发某个交互时，会发现 change 事件被触发了多次，可以通过配置 debounce 解决该问题

## 实际场景

1. 复现步骤：
   - 点击赋值按钮，通过 setValue 更新了 input-table 的数据
   - 此时 input-table 的 change 事件，被触发了多次

![选择"空跑结束"数据](/dataseeddesigndocui/public/assets/practice12/1.png "选择'空跑结束'数据")

## 实践代码

```js
{
  // input-table 的 change 事件，传入debounce属性，配置wait为1000毫秒
  "type": "input-table",
  ...,
  "onEvent": {
    "change": {
      "debounce": {
        "wait": 1000
       },
      "actions": [
        {
          "actionType": "toast",
          "args": {
            "msg": "34514351"
          }
        }
      ]
    }
  }
}
```


```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "body": [
      {
        "type": "button",
        "label": "赋值",
        "onEvent": {
          "click": {
            "actions": [
              {
                "componentId": "setValue-input-table",
                "groupType": "component",
                "actionType": "setValue",
                "args": {
                  "value": [
                    {
                      "a": "a-setValue1",
                      "b": "b-setValue1"
                    },
                    {
                      "a": "a-setValue2",
                      "b": "b-setValue2"
                    }
                  ]
                }
              }
            ]
          }
        }
      },
      {
        "type": "input-table",
        "label": "表格表单",
        "id": "setValue-input-table",
        "name": "table",
        "columns": [
          {
            "name": "a",
            "label": "A"
          },
          {
            "name": "b",
            "label": "B"
          }
        ],
        "addable": true,
        "footerAddBtn": {
          "label": "新增",
          "icon": "fa fa-plus",
          "hidden": true
        },
        "strictMode": true,
        "minLength": 0,
        "needConfirm": false,
        "showTableAddBtn": false,
        "onEvent": {
          "change": {
            "debounce": {
              "wait": 1000
            },
            "actions": [
              {
                "actionType": "toast",
                "args": {
                  "msg": "34514351"
                }
              }
            ]
          }
        }
      }
    ],
    "data": {
      "table": [
        {
          "id": 1,
          "a": "a1",
          "b": "b1"
        },
        {
          "id": 2,
          "a": "a2",
          "b": "b2"
        },
        {
          "id": 3,
          "a": "a3",
          "b": "b3"
        },
        {
          "id": 4,
          "a": "a4",
          "b": "b4"
        },
        {
          "id": 5,
          "a": "a5",
          "b": "b5"
        }
      ]
    }
  }
}
```

## 代码分析

- 参考文档

1. [事件防抖](/dataseeddesigndocui/#/amis/zh-CN/course/concepts/event-action?anchor=事件防抖)
