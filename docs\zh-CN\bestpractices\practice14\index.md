---
title: 自定义组件如何触发及监听事件
description: 顾芸铭
type: 0
group: ⚙ 最佳实践
menuName: 自定义组件如何触发及监听事件
icon:
order: 6
---

<div><font color=#978f8f size=1>贡献者：顾芸铭</font> <font color=#978f8f size=1>贡献时间: 2024/08/20</font></div>

## 功能描述

在某些复杂场景，`DATASEED DESIGN`内置组件本身不能满足时，我们需要自定义一些组件，因此就会涉及到内置组件和自定义组件之间的交互,比如自定义组件如何定义事件。(注意：**自定义的组件如果用react实现只能是类组件，函数组件暂时不支持**)

## 实际场景

1. 场景描述：在一个使用`DATASEED DESIGN`的页面中，分为左边和右边两个部分，左边是用`inputTree`来实现，右边则是定义了一个 G6 画布的 react 类组件作为自定义组件， `inputTree`和画布都有一个功能就是点击节点跳出一个抽屉`drawer`展示详情， `inputTree`同作为内置组件，可以很方便的调出`drawer`，那么画布该如何调出`drawer`来展示详情呢？

2. 场景链接：[账务平台/策略运营/额度平台/额度模版管理](http://moka.dmz.sit.caijj.net/creditpayui/#/amount-tree-admin)

3. 复现步骤：
    - 点击上述链接
    - 点击左边树的节点上最右边的详情按钮，弹出抽屉展示详情
    - 关闭抽屉
    - 点击右边画布上某一个节点，弹出抽屉展示详情

![点击发起数据探查](/dataseeddesigndocui/public/assets/practice14/1.png)

![点击发起数据探查](/dataseeddesigndocui/public/assets/practice14/2.png)

![点击发起数据探查](/dataseeddesigndocui/public/assets/practice14/3.png)

![点击发起数据探查](/dataseeddesigndocui/public/assets/practice14/4.png)



## 实践代码

1. 注册自定义组件

```js
import React, { Component } from 'react';
import { createObject } from '@dataseed/amis';
import { Renderer } from '@dataseed/amis';

// 注册自定义组件
@Renderer({
  type: 'g6-view',
})
 
class Demo extends Component {
  constructor(props) {
    super(props);
  }
  async onClick=()=>{
    const {data, dispatchEvent} = this.props;

    const rendererEvent = await dispatchEvent(
      'nodeClick', //调用这个唯一的事件名
      createObject(data, {
          //需要传到数据域中的参数，以便在调用该组件的页面中使用
          text:'test'
      }),
    );

    if (rendererEvent?.prevented) {
      return;
    }
  }
  render() {
    return <div style={{ width: 180, height: 100 }} onClick={this.onClick}>点击</div>;
  }
}
export default Demo;
```

2. 使用自定义组件

```js
{
  type: 'page',
  body: [
  {
    name: 'nodeView',
    type: 'g6-view', //上面自定义组件使用Renderer方法定义的名字
      onEvent: {
        nodeClick: { //自定义一个唯一的一个事件名
          actions: [
            {
              actionType: 'drawer',
              drawer: {
                type: 'drawer',
                title: '详情',
                body: {
                  type: "tpl",
                  tpl: "Hello ${text}"
                }
                actions: [],
              },
            },
          ],
        },
      },
    }
  ]
}
```

## 代码分析

- 自定义一个唯一的事件名
- 通过`DATASEED DESIGN`中的`dispatchEvent`方法调用这个事件
- 在`Schema`中，通过`onEvent`属性，监听这个事件，并做相应的处理

