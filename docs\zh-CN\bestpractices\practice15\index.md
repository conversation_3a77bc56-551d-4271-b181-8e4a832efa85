---
title: InputTable 多列数据通过接口联动自动填充
description: 毛甜甜
type: 0
group: ⚙ 最佳实践
menuName: InputTable 多列数据通过接口联动自动填充
icon:
order: 8
---

<div><font color=#978f8f size=1>贡献者：毛甜甜</font> <font color=#978f8f size=1>贡献时间: 2024/08/22</font></div>

## 功能描述

在input-table中某一列的值更新后，根据更新后的值调用接口获取数据，将数据填充到后面的几列中。如：inputTable第二列更新后，需请求接口获取详情信息，并将详情信息填充到后面的几列中。



## 实际场景

1. 场景链接：[催收平台/作业运营/绩效管理/绩效详单](http://moka.dmz.sit.caijj.net/collectionui/#/performanceDetails)
2. 复现步骤：
   - 打开新增/编辑弹窗，修改方案选择中某一行方案的值，则会调用接口，获取方案详情信息，将数据填充到方案备注、正负值类型、当月最新绩效日期三列中。

![新增弹窗](/dataseeddesigndocui/public/assets/practice15/1.png "新增弹窗")


## 实践代码
```js
{
  "type": "input-table",
  ...
  "columns": [
    {
      "label": "浏览器",
      "name": "browser",
      "quickEdit": {
        "type": "input-text",
        "autoFill": {
          // showSuggestion值为false，表示开启自动填充模式
          "showSuggestion": false,
          "api": {
            "url": "/api/mock2/form/autoUpdate?browser=${browser}",
            // 若接口返回的数据结构不符合预期，可以通过配置 responseData 来修改
            "responseData": {
              "platform": "${platform}",
            }
          }
        }
      }
    },
    {
      "label": "版本",
      "name": "version",
      "quickEdit": false
    },
    {
      "label": "平台",
      "name": "platform",
      "quickEdit": false
    }
  ]
}
```

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "body": {
      "type": "input-table",
      "name": "autoFill",
      "needConfirm": false,
      "addable": true,
      "showTableAddBtn": false,
      "columns": [
        {
          "label": "浏览器",
          "name": "browser",
          "quickEdit": {
            "type": "input-text",
            "name": "browser",
            "autoFill": {
              "showSuggestion": false,
              "api": "/api/mock2/form/autoUpdate?browser=${browser}"
            }
          }
        },
        {
          "label": "版本",
          "name": "version",
          "quickEdit": false
        },
        {
          "label": "平台",
          "name": "platform",
          "quickEdit": false
        }
      ]
    }
  }
}
```

## 代码分析
1. 通过 `autoFill.api`, 配置自动填充数据源的接口地址，更多使用方式见[API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api). 
2. 配置 `autoFill.showSuggestion: false`，自动填充模式

参考文档

1. [FormItem 自动填充](/dataseeddesigndocui/#/amis/zh-CN/components/form/formitem)
