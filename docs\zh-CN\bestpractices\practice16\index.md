---
title: InputTable 搜索列表项
description: 刘梅
type: 0
group: ⚙ 最佳实践
menuName: InputTable 搜索列表项
icon:
order: 8
---

<div><font color=#978f8f size=1>贡献者：刘梅</font> <font color=#978f8f size=1>贡献时间: 2024/08/22</font></div>

## 功能描述

 input-table 的列表项希望可以通过表单项搜索列表数据

## 实际场景

1. [需求链接](http://wiki.caijj.net/pages/viewpage.action?pageId=313960618)
2. 交互设计：
   - 搜索框输入值，点击搜索icon后，搜索下方列表
   - 下方列表操作数据时，无需清空搜索框，可通过手动点击搜索icon再次触发列表搜索
   - 操作列无需展示添加icon，通过表格下方新增按钮触发新增

![选择"空跑结束"数据](/dataseeddesigndocui/public/assets/practice16/1.png "选择'空跑结束'数据")

## 实践代码

代码实现
```js 
// 引入支持input-table搜索的辅助函数
import { getFilterInputTableSchema } from 'amis-utils';

// 使用示例
{
  type: 'page',
  body: {
    type: 'form',
    debug: true,
    id: 'myForm',
    data: {
      // form数据域需要向下透传全量数据originTableData（这里字段名需要和辅助函数传入的name字段保持一致），在初始情况下，如果未返回唯一值字段，前端需要对数据做一层处理，添加唯一值字段
      originTableData: [
        {
          eventType: 'a',
          sourceName: 'dwa.dwa_risk_wewewewe_rwewrwer1',
          text1: 't1',
          text2: 't2',
          id: 'a',
        },
        ...
      ],
    },
    body: [
      // 使用支持input-table搜索的辅助函数
      getFilterInputTableSchema({
        // 透传的全量数据字段名，必传字段
        name: 'originTableData',
        // 当前input-table的id，必传字段
        id: 'myTable',
        label: '触发事件',
        // 当前input-table所在form的id
        formId: 'myForm',
        // 全量数据里的唯一值字段名
        primaryField: 'id',
        // 搜索框的值匹配的列字段名
        searchField: "eventType",
        topToolbar: {
          // 表格上方左侧按钮区域
          leftItems: [
            {
              type: 'button',
              label: '功能按钮',
              level: 'primary',
              rightIcon: 'remark',
              rightIconTooltip: {
                tooltipPlacement: 'top',
                content: '提示文案',
                tooltipTheme: 'dark',
              },
              className: ' ml-2',
            },
          ]
        },
        perPage: 5,
        required: true,
        addable: true,
        editable: true,
        removable: true,
        showFooterAddBtn: false,
        // 底部自定义部分
        footerToolbar: {
          bottomText: '总件数：<strong>${filterTableData.length}件</strong>',
          // 自定义底部新增按钮
          buttons: [{
            type: 'button',
            level: 'primary',
            size: 'sm',
            label: '新增',
            icon: 'fa fa-plus',
            className: ' mr-2 pm-button-mt',
            // 点击触发input-table的新增动作
            actionType: 'add',
            // 这里需配置input-table的name
            target: 'originTableData'
          }]
        },
        columns: [
          {
            label: '事件类型',
            name: 'eventType',
          },
          {
            label: '准实时数据源表',
            name: 'sourceName',
            width: '20vw',
            required: true,
            quickEdit: {
              type: 'select',
              placeholder: '请选择数据表',
              required: true,
              popOverContainerSelector: 'body',
              validations: {
                isRequired: true,
              },
              validationErrors: {
                isRequired: '请选择数据表',
              },
              options: [
                {
                  label: 'dwa.dwa_risk_wewewewe_rwewrwer1',
                  value: 'dwa.dwa_risk_wewewewe_rwewrwer1',
                },
                {
                  label: 'dwa.dwa_risk_wewewewe_rwewrwer2',
                  value: 'dwa.dwa_risk_wewewewe_rwewrwer2',
                },
                {
                  label: 'dwa.dwa_risk_wewewewe_rwewrwer3',
                  value: 'dwa.dwa_risk_wewewewe_rwewrwer3',
                },
                {
                  label: 'dwa.dwa_risk_wewewewe_rwewrwer4',
                  value: 'dwa.dwa_risk_wewewewe_rwewrwer4',
                },
              ],
            },
          },
          {
            label: '过滤条件表达式',
            required: true,
            quickEdit: {
              type: 'input-text',
              required: true,
              placeholder: "请输入表达式，例如EVENT.param1='test'",
            },
            name: 'text1',
          },
        ],
      })
    ]
  },
}

```

## 代码分析

- form数据域需向下透传全量数据，其中每条数据需有唯一值字段
- 使用辅助函数getFilterInputTableSchema，其中name、id、formId、primaryField、searchField字段为必传字段
- 如果需要在表格底部自定义新增按钮，在footerToolbar.buttons中传入的schema需传属性actionType为add，并且target属性值为当前input-table的name值


参考文档

1. 使用示例可参考 [页面示例-编辑-分组表单-第五步](/dataseeddesigndocui/#/amis/examples/form/group)
2. 辅助函数 [getFilterInputTableSchema](/dataseeddesigndocui/#/amis/zh-CN/derivedfuncs/inputTable)
