---
title: 虚拟滚动一行展示多个
description: 卢帅兵
type: 0
group: ⚙ 最佳实践
menuName: 虚拟滚动一行展示多个
icon:
order: 8
---

<div><font color=#978f8f size=1>贡献者：卢帅兵</font> <font color=#978f8f size=1>贡献时间: 2024/12/02</font></div>

## 功能描述

当页面存在大量重复组件渲染（如：Each组件遍历出大量相同的组件），导致页面滚动时卡顿，这时就可以通过`VirtualList`组件来对页面数据进行虚拟渲染，可以有效的提高页面的流畅性，如果你想要一行展示多条数据，可以参考下面示例。

## 实际场景

1. 场景链接：[监控平台-告警配置-告警看板-看板预览](http://moka.dmz.sit.caijj.net/alertui/#/panelView/ab96680faa2d4afcacd382973a238580)
2. 操作流程：直接进入页面即可。

![](/dataseeddesigndocui/public/assets/practice17/1.png)

## 实践代码

核心代码

```json
{
  "type": "virtual-list",
  // 全量数据 / 每行展示个数，通过CEIL向上取整，以免漏掉数据
  "itemCount": "${CEIL(charts.length / 2, 0)}",
  // 每个元素的高度
  "itemSize": 300, 
  "body": {
    // 使用hbox组件将每行数据均匀展示
    "type": "hbox",
    "columns": [
      {
        "type": "container",
        "body": {
          "type": "chart",
          "api": {
            ...api配置
            "data": {
              // 第一列数据取值公式：<全量数据>[当前下标 * 每行要展示的条数]
              "id": "${charts[__virtualIndex * 2 ].id}"
            }
          },
        }
      },
      {
        "type": "container",
        "body": {
          "type": "chart",
          "api": {
            ...api配置
            "data": {
              /** 
               * 非第一列数据取值公式：
               * <全量数据>[当前下标 * 每行要展示的条数 + 1（超过两个后依次累加：如第三个，+ 2）]
              */
              "id": "${charts[__virtualIndex * 2 + 1].id}"
            }
          },
          "visibleOn": "${charts[__virtualIndex * 2 + 1]}",
        }
      }
    ]
  }
}
```

代码实现

```schema
{
  "type": "page",
  "initApi": {
    "method": "get",
    "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4",
    "data": {
      "page": 1,
      "perPage": 10
    }
  },
  "body": [
    {
      "type": "virtual-list",
      "itemCount": "${CEIL(rows.length / 2, 0)}",
      "itemSize": 300,
      "body": {
        "type": "hbox",
        "columns": [
          {
            "type": "container",
            "body": {
              "type": "chart",
              "api": {
                "method": "get",
                "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/chart/chartData",
                "data": {
                  "id": "${rows[__virtualIndex * 2 ].id}"
                }
              },
              "config": {
                "title": {
                  "text": "${rows[__virtualIndex * 2 ].browser}"
                },
                "xAxis": {
                  "type": "category",
                  "data": [
                    "Mon",
                    "Tue",
                    "Wed",
                    "Thu",
                    "Fri",
                    "Sat"
                  ]
                },
                "yAxis": {
                  "type": "value"
                },
                "series": [
                  {
                    "data": "${line}",
                    "type": "line"
                  }
                ]
              }
            }
          },
          {
            "type": "container",
            "body": {
              "type": "chart",
              "api": {
                "method": "get",
                "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/chart/chartData",
                "data": {
                  "id": "${rows[__virtualIndex * 2 + 1].id}"
                }
              },
              "visibleOn": "${rows[__virtualIndex * 2 + 1]}",
              "config": {
                "title": {
                  "text": "${rows[__virtualIndex * 2 + 1].browser}"
                },
                "xAxis": {
                  "type": "category",
                  "data": [
                    "Mon",
                    "Tue",
                    "Wed",
                    "Thu",
                    "Fri",
                    "Sat"
                  ]
                },
                "yAxis": {
                  "type": "value"
                },
                "series": [
                  {
                    "data": "${line}",
                    "type": "line"
                  }
                ]
              }
            }
          }
        ]
      }
    }
  ]
}

```

## 代码分析

- `数据总量 / 每行展示条数`通过`CEIL`方法向上取整，以免漏掉数据。
- 通过`Hbox`组件包裹住要展示的多个组件，该组件可以将每个组件占比均分。
- 每个组件可以通过`virtual-list`抛出的`__virtualIndex`获取到当前下标。
  - 第一列数据公式：`<全量数据>[当前下标 * 每行要展示的条数]`。
  - 非第一列数据公式：`<全量数据>[当前下标 * 每行要展示的条数 + 1（超过两个后依次累加：如第三个，+ 2）]`。
  - 例如：每行展示两条数据，第一列取值`arr[__virtualIndex * 2]`，第二列取值`arr[__virtualIndex * 2 + 1]`。
- 非第一列数据要配置`visibleOn`，防止`数据总量 / 每行展示条数`没有被`每行展示条数`整除掉，非第一列数据无数据显示问题。

参考文档

1. [虚拟列表组件](/dataseeddesigndocui/#/amis/zh-CN/components/virtual-list)
