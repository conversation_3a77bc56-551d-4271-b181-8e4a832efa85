---
title: 定时刷新API
description: 卢帅兵
type: 0
group: ⚙ 最佳实践
menuName: 定时刷新API
icon:
order: 8
---

<div><font color=#978f8f size=1>贡献者：卢帅兵</font> <font color=#978f8f size=1>贡献时间: 2024/12/03</font></div>

## 功能描述

在项目开发中，有些页面用于数据观看，用户希望可以对页面数据进行定时刷新，而用户可以通过某个配置来设置接口的刷新时间。

## 实际场景

1. 场景链接：[监控平台-告警配置-告警看板-看板预览](http://moka.dmz.sit.caijj.net/alertui/#/panelView/ab96680faa2d4afcacd382973a238580)
2. 操作流程：进入页面后，点击右上角【OFF】，选择自动刷新时间。

![](/dataseeddesigndocui/public/assets/practice17/1.png)

## 实践代码

核心代码

```json
{
   "type": "service",
    "api": {
      "method": "get",
      "url": "/api/amis-mock/mock2/number/random?waitSeconds=1",
      /**
      * 监听刷新时间字段变化，
      * 如果不监听的话，time值在发生变化的时候intervalExpr属性监听不到刷新时间字段值的变化。
      */
      "trackExpression": "${time}"
    },
    // 自动刷新时间
    "intervalExpr": "${time}",
    // 自动刷新停止条件，根据需求需要配置
    "stopAutoRefreshWhen": "${time === 'off'}",
    "body": [
      ...展示内容
    ]
}
```

代码实现

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "id": "my_form",
    "debug": true,
    "body": [
      {
        "type": "select",
        "label": "刷新时间",
        "name": "time",
        "value": "off",
        "options": [
          {
            "label": "off",
            "value": "off"
          },
          {
            "label": "3s",
            "value": 3000
          },
          {
            "label": "5s",
            "value": 5000
          },
          {
            "label": "10s",
            "value": 10000
          }
        ]
      },
      {
        "type": "service",
        "id": "my_service",
        "api": {
          "method": "get",
          "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/number/random?waitSeconds=1",
          "trackExpression": "${time}"
        },
        "intervalExpr": "${time}",
        "stopAutoRefreshWhen": "${time === 'off'}",
        "body": {
          "type": "tpl",
          "tpl": "动态数据展示：${random}"
        }
      }
    ]
  }
}
```

## 代码分析

- 通过form中某个字段来控制`Service`组件自动/停止刷新。
- `Service`组件通过配置`intervalExpr `属性来设置api的自动刷新时间，该属性可以读取数据域中的字段。
- 通过API的`trackExpression`属性监听刷新时间字段变化，如果不监听的话，time值在发生变化的时候intervalExpr属性监听不到刷新时间字段值的变化。
