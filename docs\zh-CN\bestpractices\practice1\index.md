---
title: Select 值发生变化时, 弹窗提醒
description: 田鹏飞
type: 0
group: ⚙ 最佳实践
menuName: Select 改变 弹窗提醒
icon:
order: 1
---

<div><font color=#978f8f size=1>贡献者：田鹏飞</font> <font color=#978f8f size=1>贡献时间: 2024/05/24</font></div>

## 功能描述

select组件与其他组件有联动， 当select组件值发生变化时，弹窗提醒用户，设置新值，会导致其它组件的值发生变更，用户点击取消，select恢复为之前的值，用户点击确认，select则使用新值。

## 实际场景

1. 场景链接：[模型一站式/模型服务管理/新建模型离线服务](http://moka.dmz.sit.caijj.net/modelportalui/#/offline-model-service/add?modelAssetKey=KNATIVE_DZ_TRADE_MOB1_202306_V1&modelServiceName=%E5%9B%9E%E5%BD%92%E4%B8%80%E4%B8%AA%E6%AD%A3%E7%BB%8F%E7%9A%84DI&domainCode=&type=add&modelServiceCode=HUIGUOYIGEZHENGJINGDEDI&modelServiceKey=MS324087)

2. 复现步骤：
   - 点击上述链接
   - 选择模型资产版本进行切换
   - 出现的二次确认弹框分别进行取消和确认操作

![新建模型离线服务](/dataseeddesigndocui/public/assets/practice1/1.png "新建模型离线服务")
![二次确认弹框](/dataseeddesigndocui/public/assets/practice1/2.png "二次确认弹框")

## 实践代码

```js
onEvent: {
  // 监听选择框值变化时的事件处理
  change: {
    actions: [
      // 执行二次提醒弹框动作
      {
        actionType: 'dialog',
        // 阻止select值改变默认行为，由dialog组件确认按钮控制
        preventDefault: true,
        dialog: {
          showCloseButton: false,
          title: '切换选择资产版本',
          actions: [
            // 取消不需要做任何处理
            {
              type: 'button',
              actionType: 'cancel',
              label: '取消',
            },
            // 确认设置select表单项的值
            {
              type: 'button',
              actionType: 'confirm',
              label: '确定',
              primary: true,
              onEvent: {
                click: {
                  actions: [
                    {
                      actionType: 'setValue',
                      componentId: 'offlineModelServiceAddForm',
                      args: {
                        value: {
                          modelAssetVersion: '${value}',
                        },
                      },
                    },
                  ],
                },
              },
            },
          ],
          body: '切换选择资产版本将会清除现有的“第三步”的配置，是否切换?',
        },
      },
    ],
  },
},

```

```schema: scope="body"
{
  type: 'form',
  id: 'offlineModelServiceAddForm',
  debug: true,
  body: [
    {
      type: 'select',
      name: 'modelAssetVersion',
      label: '模型资产版本',
      required: true,
      labelField: 'modelServiceCode',
      valueField: 'modelAssetKey',
      placeholder: '请选择模型资产版本',
      options: [
        {
          key: 1,
          modelAssetKey:
            'version_001',
          modelServiceCode: '版本1',
        },
        {
          key: 2,
          modelAssetKey: 'version_002',
          modelServiceCode: '版本2',
        },
      ],
      onEvent: {
        // 当选择框值变化时的事件处理
        change: {
          // 监听点击事件
          actions: [
            // 执行的动作列表
            {
              actionType: 'dialog',
              preventDefault: true,
              // 选择的版本和当前版本不一致时，弹出提醒弹窗
              dialog: {
                showCloseButton: false,
                title: '切换选择资产版本',
                actions: [
                  {
                    type: 'button',
                    actionType: 'cancel',
                    label: '取消',
                  },
                  {
                    type: 'button',
                    actionType: 'confirm',
                    label: '确定',
                    primary: true,
                    onEvent: {
                      click: {
                        actions: [
                          {
                            actionType: 'setValue',
                            componentId: 'offlineModelServiceAddForm',
                            args: {
                              value: {
                                modelAssetVersion: '${value}',
                              },
                            },
                          },
                        ],
                      },
                    },
                  },
                ],
                body: '切换选择资产版本将会清除现有的“第三步”的配置，是否切换?',
              },
            },
          ],
        },
      },
    },
  ],
}
```

## 代码分析

1. 监听选择框`change`事件
2. 利用`preventDefault`阻止select值改变默认行为
3. 点击取消，不改变`select`值
4. 点击确定，利用`form`的`setValue`动作改变`select`表单项的值

参考文档

1. [阻止事件默认行为](/dataseeddesigndocui/#/amis/zh-CN/course/concepts/event-action?anchor=阻止事件默认行为)







