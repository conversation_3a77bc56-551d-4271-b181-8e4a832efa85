---
title: CRUD操作列按钮点击根据接口返回结果显示不同的弹窗
description: 田鹏飞
type: 0
group: ⚙ 最佳实践
menuName: 根据接口展示不同的弹框
icon:
order: 2
standardMode: true
---

<div><font color=#978f8f size=1>贡献者：田鹏飞</font> <font color=#978f8f size=1>贡献时间: 2024/05/24</font></div>

## 功能描述

在CRUD组件中，点击操作列按钮，根据接口返回到内容，展示不同的弹出框

## 实际场景

1. 场景链接：[模型一站式/模型服务管理](http://moka.dmz.sit.caijj.net/modelportalui/#/service-management/detail?modelServiceCode=MOXINFWSHISHI&modelServiceKey=MS324112&modelAssetKey=KNATIVE_DZ_TRADE_MOB1_202306_V1&activeKey=1)

2. 复现步骤：
   - 点击正式运行按钮（触发接口）
   - 点击没有版本的模型展示上线二次确认弹出框(如下图一)
   - 点击有版本的模型展示对比弹出框(如下图二)

![没有版本展示](/dataseeddesigndocui/public/assets/practice2/1.png "没有版本展示")
![有版本展示](/dataseeddesigndocui/public/assets/practice2/2.png "有版本展示")

## 实践代码

```js
{
  "type": "button",
  "level": "link",
  "label": "正式运行",
  // 给单个按钮开启局部loading
  "selfLoading": true,
  "onEvent": {
    // 监听按钮的点击事件
    "click": {
      "actions": [
        // 发起ajax请求
        {
          "actionType": "ajax",
          "args": {
            "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/number/random?waitSeconds=1"
          }
        },
        {
          "actionType": "dialog",
          // 通过expression根据接口返回值做不通的弹框展示逻辑
          "expression": "${event.data.responseResult.responseData.random > 6}",
          "dialog": {
            "type": "dialog",
            "title": "弹框标题1",
            "body": [
              {
                "type": "form",
                "body": [
                  {
                    "type": "tpl",
                    "tpl": "random返回结果<span class='text-success'>大于6</span>",
                  }
                ]
              }
            ]
          }
        },
        {
          "actionType": "dialog",
          // 通过expression根据接口返回值做不通的弹框展示逻辑
          "expression": "${event.data.responseResult.responseData.random <= 6}",
          "dialog": {
            "type": "dialog",
            "title": "弹框标题2",
            "body": [
              {
                "type": "form",
                "body": [
                  {
                    "type": "tpl",
                    "tpl": "random返回结果<span class='text-danger'>小于等于6</span>",
                  }
                ]
              }
            ]
          }
        }
      ]
    }
  }
}

```

```schema: scope="body"
{
  "type": "page",
  "id": "pageId",
  "data": {
    "bestPracticesList": [
      {
        "key": 1,
        "modelAssetKey": "PIKASHISHI001",
        "modelServiceCode": "皮卡勿动实时模型001"
      }
    ]
  },
  "body": [
    {
      "type": "crud",
      "source": "${bestPracticesList}",
      "columns": [
        {
          "name": "modelAssetKey",
          "label": "模型资产编码"
        },
        {
          "name": "modelServiceCode",
          "label": "模型服务编码"
        },
        {
          "type": "operation",
          "label": "操作",
          "width": 200,
          "buttons": [
            {
              "type": "button",
              "level": "link",
              "label": "正式运行",
              "selfLoading": true,
              "onEvent": {
                "click": {
                  "actions": [
                    {
                      "actionType": "ajax",
                      "args": {
                        "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/number/random?waitSeconds=1"
                      }
                    },
                    {
                      "actionType": "dialog",
                      "expression": "${event.data.responseResult.responseData.random > 6}",
                      "dialog": {
                        "type": "dialog",
                        "title": "弹框标题1",
                        "body": [
                          {
                            "type": "form",
                            "body": [
                              {
                                "type": "tpl",
                                "tpl": "random返回结果<span class='text-success'>大于6</span>",
                              }
                            ]
                          }
                        ]
                      }
                    },
                    {
                      "actionType": "dialog",
                      "expression": "${event.data.responseResult.responseData.random <= 6}",
                      "dialog": {
                        "type": "dialog",
                        "title": "弹框标题2",
                        "body": [
                          {
                            "type": "form",
                            "body": [
                              {
                                "type": "tpl",
                                "tpl": "random返回结果<span class='text-danger'>小于等于6</span>",
                              }
                            ]
                          }
                        ]
                      }
                    }
                  ]
                }
              }
            }
          ]
        }
      ]
    }
  ]
}
```


## 代码分析

1. 监听操作列正式运行按钮的`click`事件
2. 打开 `button` 组件的`selfLoading`防止用户重复点击
3. 发送请求 根据接口返回字段配合`expression`展示不同的弹出框
4. 在事件中写两个弹出框，只有`expression`为ture的事件才会执行，从而达到根据结果展示不同的弹出框

参考文档
1. [引用 http 请求返回的数据](/dataseeddesigndocui/#/amis/zh-CN/course/concepts/event-action?anchor=引用http请求返回的数据)
2. [expression 属性](/dataseeddesigndocui/#/amis/zh-CN/course/concepts/event-action?anchor=属性表)
3. [局部loading](/dataseeddesigndocui/#/amis/zh-CN/components/action?anchor=局部loading)






