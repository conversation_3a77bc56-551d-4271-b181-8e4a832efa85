---
title: 使用过滤器，更改select组件的枚举值
description: 郝亚雷
type: 0
group: ⚙ 最佳实践
menuName: select组件自定义过滤器
icon:
order: 3
---

<div><font color=#978f8f size=1>贡献者：郝亚雷</font> <font color=#978f8f size=1>贡献时间: 2024/05/27</font></div>

## 功能描述

有多个相同枚举的select组件时， 当其中一个select组件值发生变化时，其他的select组件枚举过滤掉选过的枚举。

## 实际场景

1. 场景链接：[产品运营/贷超超市/商户管理/新增](http://moka.dmz.sit.caijj.net/credittoolsui/#/merchantManagement/add?isFromType=add)


2. 复现步骤：
   - 点击上述链接
   - 产品配置选择新增，然后继续新增结算方式。
   - 出现2个新增方式时，第一个选择枚举后，第二个会过滤掉选中过的枚举
![先选择一个select组件枚举](/dataseeddesigndocui/public/assets/practice3/1.png )
![再选第二个select组件](/dataseeddesigndocui/public/assets/practice3/2.png )

## 实践代码
```js
import { registerFilter, render as renderAmis } from '@dataseed/amis';

// 注册自定义过滤器方法
  registerFilter('settleType_filter', (settleTypeEnums, settleInfo) => {
  /**
   * settleTypeEnums:为全部枚举，settleInfo：为已选择的值
   * 通过筛选过滤出未被选择的枚举数组
   * */
  const filteredList = settleTypeEnums.filter(
    (item) => !settleInfo.some((obj) => obj.settleType === item),
  );
  return filteredList;
});


```
```schema: scope="body"
 {
  type: 'form',
  debug: true,
  data: {
     settleEnum: ['CPA', 'CPS', 'CPC'],
   },
  body: [
    {
      type: 'combo',
      name: 'settleInfo',
      label: false,
      multiple: true,
      strictMode: false,
      maxLength: 2,
      items: [
        {
          label: '结算方式',
          type: 'select',
          name: 'settleType',
          clearable: true,
           source: '${settleTypeEnums | settleType_filter:${settleInfo}}',
        },
      ],
    },
  ],
},
```




## 代码分析
  settleType_filter 为注册自定义过滤器名称,
  然后使用registerFilter方法写自己的过滤逻辑:<br/>
   registerFilter(
   'settleType_filter',
   (settleTypeEnums, settleInfo) => {return filteredList});
   <br/>其中settleTypeEnums为需要过滤的数据源，settleInfo为已经选择的数据

  带入需要处理的数据源<br/>
  "source": "${settleTypeEnums | settleType_filter:${settleInfo}}" 

  过滤器详解见：[数据映射](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/data-mapping)








