---
title: input-table 多列数据联动
description: 吴姚芳
type: 0
group: ⚙ 最佳实践
menuName: xxxxxx
icon:
order: 4
standardMode: true
---

<div><font color=#978f8f size=1>贡献者：吴姚芳</font> <font color=#978f8f size=1>贡献时间: 2024/06/18</font></div>

## 功能描述

input-table 中列与列之间存在计算关系，例如 A列【计提金额】 与 B列【调整金额】计算得到 C列【结算金额】的数据。

## 实际场景

1. 场景链接：[触达平台/账单管理/AI 账单/账单详情](http://moka.dmz.sit.caijj.net/messagecenterui/#/aiBillingDetail?id=280&viewMode=check&status=SETTLED)

2. 复现步骤：

  在账单明细中调整表格中调整金额输入框数值或者切换下拉框中+/-符号
![示例图片](/dataseeddesigndocui/public/assets/practice4/1.png )
## 实践代码

```js
import {registerFilter, render as renderAmis} from '@dataseed/amis';

registerFilter('calculatedAmount', (tableData, dataType) => {
  let newSettlementAmt = 0;
  const newData = tableData?.map(itm => {
    /** 根据最新的计提金额和调整金额计算得出结算金额 */
    const _curAmt =
      itm.adjustAmtFlag === 1
        ? itm.provisionAmt + itm.adjustAmt // 相加 ⚠️：这里是示例，真实场景中需要用对应函数包裹否则有精度问题
        : itm.provisionAmt - itm.adjustAmt; // 相减 ⚠️：同上;
    // itm.settlementAmt = _curAmt;
    /** 总结行中的计算金额（为表格中每行的结算金额之和） */
    newSettlementAmt = newSettlementAmt + _curAmt // 求和 ⚠️：同上;
    return {
      ...itm,
      settlementAmt: _curAmt /** 修改结算金额为最新的结算金额 */,
    };
  });
  /** 根据dataType 返回所需要的数据 */
  if (dataType === 'settlementAmt') {
    /** dataType === 'settlementAmt' 返回总结行计算金额 */
    return newSettlementAmt;
  } else {
    /** 返回更新后的表格数据 */
    return newData;
  }
});
```

```schema: scope="body"
{
    "type": "form",
    "id": 'bill-form-id',
    data: {
      billDetails: [
        {
          accountCode: 'AI_CALL_RJOH_ACCOUNT_957',
          accountName: '测试供应商122_AI人机耦合_促首借',
          adjustAmt: 5,
          adjustReason: '调整金额',
          channelType: 'RJOH',
          channelTypeName: 'AI-人机耦合',
          costDepartment: 'QZC',
          costDepartmentName: '轻资产',
          costType: 'AI&IVR·QZC.RJOH',
          costTypeName: 'AI及IVR·轻资产.人机耦合',
          createdAt: *************,
          id: 320,
          metricsOneName: 'delta授信户数',
          metricsOneValue: 647,
          priceOneName: 'delta动支金额',
          priceOneValue: 6,
          provisionAmt: 3882,
          quantityOneName: 'delta授信户数',
          quantityOneValue: 647,
          settlementAmt: 3887,
          updatedAt: *************,
          updatedName: '吴姚芳',
        },
        {
          accountCode: 'AI_CALL_RJOH_ACCOUNT_945',
          accountName: '测试供应商勿动_AI人机耦合_促申完-低转化睡眠户',
          adjustAmt: 7,
          adjustReason: '调整',
          channelType: 'RJOH',
          channelTypeName: 'AI-人机耦合',
          costDepartment: 'QZC',
          costDepartmentName: '轻资产',
          costType: 'AI&IVR·QZC.RJOH',
          costTypeName: 'AI及IVR·轻资产.人机耦合',
          createdAt: *************,
          id: 321,
          metricsOneName: 'delta授信户数',
          metricsOneValue: 165,
          priceOneName: 'delta授信户数',
          priceOneValue: 1,
          provisionAmt: 165,
          quantityOneName: 'delta授信户数',
          quantityOneValue: 165,
          settlementAmt: 172,
          updatedAt: *************,
          updatedName: '吴姚芳',
        },
      ],
      provisionAmt: 4047,
      settlementAmt: 4059,
    },
    "showLabelColon": true,
    
    "body": [
      {
        "type": "input-table",
        id: 'bill-table-id',
        label: false,
        name: 'billDetails',
        onEvent: {
          change: {
            actions: [
              {
                componentId: 'bill-form-id',
                actionType: 'setValue',
                args: {
                  value: {
                    settlementAmt:
                      '${event.data.value|calculatedAmount:settlementAmt}',
                    billDetails:
                      '${event.data.value|calculatedAmount:billDetails}',
                  },
                },
              },
            ],
          },
        },
        columns: [
          
          {
            name: 'accountName',
            label: '账号名称',
          },
          {
            name: 'channelTypeName',
            label: '业务类型',
          },
          {
            name: 'costTypeName',
            label: '费用类型',
          },
          {
            name: 'provisionAmt',
            label: '计提金额（元）',
            type: 'tpl',
            tpl: '¥${provisionAmt}',
          },
          {
            name: 'input-group',
            width: 160,
            label: '调整金额（元）', // 编辑模式
            type: 'input-group',
            className: 'ajust-input-model',
            body: [
              {
                type: 'select',
                name: 'adjustAmtFlag',
                label: false,
                required: true,
                className: 'm-0',
                options: [
                    {
                      label: '+',
                      value: 1,
                    },
                    {
                      label: '-',
                      value: 0,
                    },
                ],
                value: 1,
                style: {
                  width: 60,
                  margin: 0,
                },
              },
              {
                type: 'input-number',
                label: false,
                precision: 2,
                name: 'adjustAmt',
                min: 0,
                validations: {
                  adjustmentValueValide: true,
                },
                validateOnchange: true,
              },
            ],
          },
          {
            name: 'settlementAmt',
            label: '结算金额（元）',
            type: 'tpl',
            tpl: '¥${settlementAmt}',
          },
       
        ],
        affixRow: [
        [
          {
            type: 'text',
            text: '计提金额',
          },
          {
            type: 'tpl',
            tpl: '¥${provisionAmt}',
          },
        ],
        [
          {
            type: 'text',
            text: '结算金额',
          },
          {
            type: 'tpl',
            tpl: '¥${settlementAmt}',
          },
        ],
      ],
      }
    ]
  }


```


## 代码分析

calculatedAmount 为自定义的过滤器名称
registerFilter('calculatedAmount', (tableData, dataType) =>{})
自定义过滤器可作用在不同地方，根据dataType 字段可导出不同结果，满足自己的业务场景。

