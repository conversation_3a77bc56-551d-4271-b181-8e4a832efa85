---
title: 轮询调用接口
description: 卢帅兵
type: 0
group: ⚙ 最佳实践
menuName: 轮询调用接口
icon:
order: 5
---

<div><font color=#978f8f size=1>贡献者：卢帅兵</font> <font color=#978f8f size=1>贡献时间: 2024/07/01</font></div>

## 功能描述

点击操作按钮弹出弹窗，满足一定条件时轮询调用接口，根据接口返回的参数条件停止轮询调用。

## 实际场景

1. 场景链接：[特征一站式/数据回溯/新增任务](http://moka.dmz.sit.caijj.net/featurestoreui/#/dataSetTaskManagement/createTask)

2. 复现步骤：
   - 输入能够使用的 SQL
   - 点击解析并运行按钮（触发解析接口），接口成功后弹出弹窗，根据接口返回字段展示不同内容，解析失败后展示提示内容(如下图一)，解析成功后轮询调用日志接口(如下图二)
   - 解析成功后发起日志轮询接口
   - 轮询结束后根据最后一次接口返回结果展示不同的内容，运行失败(如下图三)，运行成功(如下图四)

![解析失败](/dataseeddesigndocui/public/assets/practice5/1.jpg '解析失败')
![解析成功并轮询调用日志](/dataseeddesigndocui/public/assets/practice5/2.jpg '解析成功并轮询调用日志')
![运行日志失败](/dataseeddesigndocui/public/assets/practice5/3.jpg '运行日志失败')
![运行日志成功](/dataseeddesigndocui/public/assets/practice5/4.jpg '运行日志成功')

## 实践代码

```js
// 核心代码代码部分，完整代码参考DEMO
{
  "type": "service",
  "visibleOn": "${resultStatus !== 'RES_ERROR'}",
  "api": {
    "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/number/random?waitSeconds=1",
    // 传入接口成功返回后所需要的字段
    "tdata": {
      "logText": "${logText}",
      "runLog": "${runLog}"
    },
    "adaptor": (payload, res, api) => {
      // 获取包裹文本的dom元素
      const element = document.getElementsByClassName('runLogBox')[0];
      // 定时滚动到滚动条最底部。滚动条位置 = 文本包裹元素最新的滚动高度
      setTimeout(() => { element.scrollTop = element.scrollHeight;},300);
      return {
      ...payload,
      data: {
          ...payload.data,
          // 旧文本数据 拼接 新文本数据
          runLog: (api.tdata.runLog || '') + api.tdata.logText,
        }
      }
    },
  },
  // 关闭等待接口加载时的 loading 动画
  "silentPolling": true,
  // 轮询停止条件：运行成功 || 运行失败
  "stopAutoRefreshWhen": "this.random === 6 || this.random === 10",
  // 轮询时间间隔
  "interval": 2000,
  "body": [
    // 文本展示
    {
      "type": "code",
      "editorTheme": "vs-dark",
      "className": "block mtk1 runLogBox h-72 white-space-pre",
      "maxHeight": 752,
      "name": "runLog"
    }
  ],
}
```

```schema: scope="body"
{
  "type": "page",
  "id": "pageId",
  "body": [
    {
      "type": "form",
      "mode": "horizontal",
      "wrapWithPanel": false,
      "labelWidth": 50,
      "data": {
        "sql": "select name1,age1,ds from ads_app_blacklst_sit.ceshi_table_name where id=1",
        "logText": "2024-06-27 16:54:33 Taskrun created, will be dispatched to agent\r\n2024-06-27 16:54:33 Taskrun id: tr_5951329061599772672_20240626_5951329095959511040\r\n2024-06-27 16:54:33 Task id: t_5951329061599772672_20240627_5951329061599772673\r\n2024-06-27 16:54:33 Tenant id: 300002005\r\n2024-06-27 16:54:33 Schedule type: AD_HOC\r\n2024-06-27 16:54:33 0th runtime \r\n2024-06-27 16:54:33 Taskrun status changed from INIT to READY \r\n2024-06-27 16:54:34 Generating ExecutableTaskrun for the 1st time \r\n",
      },
      "body": [
        {
          "type": "group",
          "direction": "vertical",
          "label": "SQL",
          "body": [
            {
              "type": "editor",
              "name": "sql",
              "language": "sql",
              "label": false,
              "editorTheme": "vs-dark"
            },
            {
              "type": "button",
              "label": "解析并运行",
              "level": "primary",
              "disabledOn": "${!sql}",
              "actionType": "ajax",
              "api": "/api/mock2/log/log1?sql=$sql",
              "feedback": {
                "title": "运行日志",
                "actions": [
                  {
                    "type": "button",
                    "label": "关闭",
                    "actionType": "cancel",
                    "disabledOn": "${resultStatus ==='RES_SUCCESS'}"
                  },
                ],
                "showErrorMsg": false,
                "showLoading": false,
                "size": "md",
                "showCloseButton": false,
                "id": "dialogId",
                "body": [
                  {
                    "type": "mapping",
                    "name": "resultStatus",
                    "map": {
                      "RES_ERROR": "解析SQL<span class='text-danger'>失败</span>",
                      "RES_SUCCESS": "解析SQL<span class='text-success'>成功</span>，试运行SQL中，预计需要几分钟时间，请耐心等待.......",
                      "RUN_SUCCESS": "解析SQL<span class='text-success'>成功</span>，运行SQL<span class='text-success'>成功</span>",
                      "RUN_ERROR": "解析SQL<span class='text-success'>成功</span>，运行SQL<span class='text-danger'>失败</span>"
                    }
                  },
                  {
                    "type": "service",
                    "visibleOn": "${resultStatus !== 'RES_ERROR'}",
                    "api": {
                      "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/number/random?waitSeconds=1",
                      "tdata": {
                        "logText": "${logText}",
                        "runLog": "${runLog}",
                      },
                      "adaptor": "const element = document.getElementsByClassName('runLogBox')[0]; setTimeout(() => { element.scrollTop = element.scrollHeight; }, 300); return { ...payload, data: {...payload.data, runLog: (api.tdata.runLog || '') + api.tdata.logText } }"
                    },
                    "silentPolling": true,
                    "stopAutoRefreshWhen": "this.random === 6 || this.random === 10",
                    "interval": 2000,
                    "body": [
                      {
                        "type": "code",
                        "editorTheme": "vs-dark",
                        "className": "block mtk1 runLogBox h-72 white-space-pre",
                        "maxHeight": 752,
                        "name": "runLog"
                      }
                    ],
                    "onEvent": {
                      "fetchInited": {
                        "actions": [
                          {
                            "actionType": "setValue",
                            "componentId": "dialogId",
                            "expression": "${event.data.random === 6}",
                            "args": {
                              "value": {
                                "resultStatus": "RUN_SUCCESS"
                              }
                            },
                          },
                          {
                            "actionType": "setValue",
                            "componentId": "dialogId",
                            "expression": "${event.data.random === 10}",
                            "args": {
                              "value": {
                                "resultStatus": "RUN_ERROR"
                              }
                            },
                          }
                        ]
                      }
                    },
                  },
                ]
              }
            }
          ]
        }
      ]
    }
  ]
}
```

## 代码分析

1. 点击【解析并运行】，发起解析 sql 接口，接口请求成功后走 feedback 属性弹出弹窗
2. 根据 sql 解析接口返回的状态字段展示不同的内容，sql 解析成功后展示相应的提示语且发起轮询；sql 解析失败后展示相应提示语，不发起轮询
3. 配置轮询 api 时要将老的日志数据传入 api 的 tdata 属性，再通过 api 的 adaptor 处理返回的数据，在 adaptor 第三个参数 api 中取到 tdata 将老的日志数据拼接上新的日志数据，且设置一个 setTimeout 将日志展示框自动滚动到底部
4. 通过轮询接口返回的结果进行停止轮询，满足条件后停止轮询，不满足的继续执行轮询，直到满足停止条件。
5. 根据轮询最后一次请求返回的结果，展示成功/失败的提示语

注意项：

- 在复制示例代码时要将 adaptor 改为 function 使用

参考文档

1. [service interval 属性](/dataseeddesigndocui/#/amis/zh-CN/components/service?anchor=属性表)
2. [service silentPolling 属性](/dataseeddesigndocui/#/amis/zh-CN/components/service?anchor=属性表)
3. [service stopAutoRefreshWhen 属性](/dataseeddesigndocui/#/amis/zh-CN/components/service?anchor=属性表)
