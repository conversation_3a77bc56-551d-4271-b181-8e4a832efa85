---
title: Dialog 弹窗列表选择
description: 庞金明
type: 0
group: ⚙ 最佳实践
menuName: Dialog 弹窗列表选择
icon:
order: 6
---

<div><font color=#978f8f size=1>贡献者：庞金明、郝亚雷</font> <font color=#978f8f size=1>贡献时间: 2024/07/02</font></div>

## 功能描述

业务方在选择某些主体的时候，需要通过弹窗列表进行搜索查询，然后将所需要的数据进行点击选择，最后把列表中选择的数据关联到所需要的Form表单域中。

组件库中，[Picker组件](/dataseeddesigndocui/#/amis/zh-CN/components/form/picker) 的 `"embed": true` 内嵌模式，可以很方便的实现该功能。

## 实际场景

### 场景一

1. 场景链接：[账务平台/执行运营/合作方清结算/预付款管理/创建预付款单](http://moka.dmz.sit.caijj.net/accountingui/#/advance-payment-manage/payment-add?payCategory=COMMON_PAY_ITEM&payItemType=ADVANCE_PAYMENT&pageStatus=add)

2. 复现步骤：
   - 点击上述链接
   - 点击选择供应商，弹出列表选择框
   - 点击并选择列表行
   - 点击弹窗确定按钮

![点击选择供应商](/dataseeddesigndocui/public/assets/practice6/1.png)

![点击弹窗确定按钮](/dataseeddesigndocui/public/assets/practice6/2.png) 

### 场景二

1. 场景链接：[获客一站式/投放管理/账户授权管理/新账户组管理](http://moka.dmz.sit.caijj.net/tdpplusui/#/accountGroup)

2. 复现步骤：
   - 点击上述链接
   - 点击列表操作-账户分组-添加账号

![添加账号](/dataseeddesigndocui/public/assets/practice6/3.png "选择'添加账号'数据")

![批量添加](/dataseeddesigndocui/public/assets/practice6/4.png "多选数据")

## 实践代码

### 场景一

将弹窗列中选择的数据，同步到表单中。

```js
// 核心代码代码部分，完整代码参考DEMO

// 1. 在弹窗中使用 Picker 组件的 embed 内嵌模式
"type": "picker",
"embed": true,
"name": "selectedPayeeInfo",
"label": false,
"valueField": "id", // API 返回列表数据的唯一标示字段
"joinValues": false, // 选择数据不进行处理（选择项是 object 数据）
"multiple": false, // 单选
"size": "lg",
"source": { // 列表接口请求API
  "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample"
},
"pickerSchema": {
  "mode": "table", // table 选择模式
  ... // 可使用 CRUD 的配置
}


// 2. Dialog 的确定按钮
{
  "label": "确认",
  "type": "action",
  "level": "primary",
  // 确认后关闭 弹窗
  "close": true,
  // 当未选择时，确认按钮 无法点击
  "disabledOn": "${!selectedPayeeInfo}",
  "onEvent": {
    "click": {
      "actions": [
        {
          // 点击确认按钮时，将数据同步到 外层 FORM 表单域中
          "actionType": "setValue",
          "componentId": "detailForm",
          "args": {
            "value": {
              // 对数据进行转换
              "payeeInfo": {
                "supplierName": "${selectedPayeeInfo.engine}",
                "usccCode": "${selectedPayeeInfo.browser}",
                "accountName": "${selectedPayeeInfo.browser}"
              }
            }
          }
        }
      ]
    }
  }
}

```

```schema
{
  type: 'page',
  body: {
    "type": "form",
    "debug": true,
    "id": "detailForm",
    "body": [
      {
        "type": "group",
        "body": [
          {
            "type": "action",
            "label": "选择供应商账户",
            "level": "primary",
            "actionType": "dialog",
            "dialog": {
              "title": "选择供应商账户",
              "size": "lg",
              "showCloseButton": false,
              "actions": [{
                "label": "取消",
                "type": "action",
                "actionType": "close"
              }, {
                "label": "确认",
                "type": "action",
                "level": "primary",
                "close": true,
                "disabledOn": "${!selectedPayeeInfo}",
                "onEvent": {
                  "click": {
                    "actions": [
                      {
                        // 点击确认按钮时，将数据同步到 外层 FORM 表单域中
                        "actionType": "setValue",
                        "componentId": "detailForm",
                        "args": {
                          "value": {
                            // 对数据进行转换
                            "payeeInfo": {
                              "supplierName": "${selectedPayeeInfo.engine}",
                              "usccCode": "${selectedPayeeInfo.browser}",
                              "accountName": "${selectedPayeeInfo.browser}"
                            }
                          }
                        }
                      }
                    ]
                  }
                }
              }],
              "body": {
                "type": 'form',
                "body": [
                  {
                    "type": "picker",
                    "name": "selectedPayeeInfo",
                    "joinValues": false,
                    "valueField": "id",
                    "label": false,
                    "embed": true,
                    "size": "lg",
                    "multiple": false,
                    "source": {
                      "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample"
                    },
                    "pickerSchema": {
                      "mode": "table",
                      "columnsTogglable": false,
                      "autoGenerateFilter": {
                        "showBtnToolbar": false,
                        "defaultExpanded": false
                      },
                      "columns": [
                        {
                          "name": "engine",
                          "label": "供应商名称",
                          "searchable": {
                            "type": "input-text",
                            "clearable": true,
                            "placeholder": "请输入"
                          }
                        },
                        {
                          "name": "browser",
                          "label": "纳税人识别号",
                          "searchable": {
                            "type": "input-text",
                            "clearable": true,
                            "placeholder": "请输入"
                          }
                        },
                        {
                          "name": "platform",
                          "label": "账户名称"
                        }
                      ]
                    }
                  }
                ]
              }
            }
          }
        ]
      },
      {
        "type": "group",
        "body": [
          {
            "type": "input-text",
            "name": "payeeInfo.supplierName",
            "label": "供应商名称",
            "placeholder": "请输入",
            "disabled": true,
          },
          {
            "type": "input-text",
            "name": "payeeInfo.usccCode",
            "label": "纳税人识别号",
            "placeholder": "请输入",
            "disabled": true,
          },
          {
            "type": "input-text",
            "name": "payeeInfo.accountName",
            "label": "账户名称",
            "placeholder": "请输入",
            "disabled": true,
          }
        ]
      }
    ]
  }
}
```

### 场景二

对弹窗列中选择的数据，进行二次确认提示，并请求API接口。

```js
// 关键代码

// 1. 弹窗中使用 Picker 内嵌模式
{
  "type": "picker",
  "embed": true,
  "name": "selectedItems",
  "label": false,
  "joinValues": false,
  "valueField": "id",
  "multiple": true, // 开启多选
  "size": "lg",
  "source": { // 列表接口
    "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
  },
  "pickerSchema": {
    "labelTpl": "${engine}", // 已选择的项，展示label (展示在表格上方)
    ... // 其他 CRUD 配置
  }
}

// 2. 提示弹窗中的确定按钮配置
"type": "button",
"primary": true,
"label": "确认",
"onEvent": {
  "click": {
    "actions": [
      {
        "actionType": "ajax",
        "args": {
          "api": {
            "url": "https://3xsw4ap8wah59.cfc-execute.bj.baidubce.com/api/amis-mock/mock2/form/saveForm",
            // 可获取到 选择的数据项 selectedItems（picker配置的name）
            "data": {
              "ids": "${selectedItems|pick:id|join}"
            }
          },
        }
      },
    ]
  }
}
```

```schema
{
  "type": "page",
  "className": "p-4",
  "body": [
    {
      "type": "button",
      "className": "pm-button-ml",
      "label": "批量添加账号",
      "actionType": "dialog",
      "dialog": {
        "id": "listDialog",
        "size": "lg",
        "title": "添加账号",
        "body": {
          "type": "form",
          "body": [{
            "type": "picker",
            "name": "selectedItems",
            "joinValues": false,
            "valueField": "id",
            "label": false,
            "embed": true,
            "size": "lg",
            "multiple": true,
            "source": {
              "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?waitSeconds=1&keyword=$keyword",
            },
            "pickerSchema": {
              "columnsTogglable": false,
              "labelTpl": "${engine}",
              "headerToolbar": [
                {
                  "type": "tpl",
                  "tpl": "未分组账号列表"
                },
                {
                  "type": "search-box",
                  "name": "keyword",
                  "align": "right",
                  "clearable": true,
                  "placeholder": "请输入"
                }
              ],
              "columns": [
                {
                  "name": "id",
                  "label": "ID"
                },
                {
                  "name": "engine",
                  "label": "Rendering engine"
                },
                {
                  "name": "browser",
                  "label": "Browser"
                },
                {
                  "name": "platform",
                  "label": "Platform(s)"
                },
                {
                  "name": "engine",
                  "label": "Engine"
                },
                {
                  "name": "version",
                  "label": "Engine Version"
                },
                {
                  "name": "grade",
                  "label": "CSS grade"
                }
              ],
            }
          }]
        },
        "actions": [
          {
            "type": "button",
            "actionType": "cancel",
            "label": "取消"
          },
          {
            "type": "button",
            "primary": true,
            "label": "确认",
            "actionType": "dialog",
            "className": "pm-button-ml",
            "disabledOn": "${!GET(selectedItems, 'length')}",
            "dialog": {
              "title": "提示",
              "id": "tipDialog",
              "showCloseButton": false,
              "actions": [{
                "type": "button",
                "actionType": "cancel",
                "label": "取消"
              }, {
                "type": "button",
                "primary": true,
                "label": "确认",
                "onEvent": {
                  "click": {
                    "actions": [
                      {
                        "actionType": "ajax",
                        "args": {
                          "api": {
                            "url": "https://3xsw4ap8wah59.cfc-execute.bj.baidubce.com/api/amis-mock/mock2/form/saveForm",
                            "data": {
                              "seletedItems": "${selectedItems|pick:id|join}"
                            }
                          },
                        }
                      },
                      {
                        "actionType": "closeDialog",
                        "componentId": "tipDialog",
                      },
                      {
                        "actionType": "closeDialog",
                        "componentId": "listDialog",
                      }
                    ]
                  }
                }
              }],
              "body": [
                {
                  "type": "tpl",
                  "tpl": "确定将以下【${selectedItems.length}】个账号添加关联吗："
                },
                {
                  "type": "each",
                  "name": "selectedItems",
                  "items": [
                    {
                      "type": "link",
                      "body": "${engine}"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${(index + 1) < selectedItems.length}",
                      "tpl": "、"
                    }
                  ]
                }
              ]
            }
          }
        ]
      }
    }
  ]
}
```

## 代码分析

- 在弹窗列表选择场景中，可以使用 `Picker` 组件的 内嵌模式，这样既可以使用 CRUD 列表组件的查询/展示等功能，也可以很方便的实现选择功能。
- 由于 dialog 会把 body 中的 form 数据自动同步到 dialog 层。这样可以在 dialog 层方便的使用已经选择的数据，比如 在 actions 自定义按钮，将数据同步给其他组件。
