---
title: 相互排除的下拉列表
description: 介绍如何在表单中实现两个或多个下拉列表之间的选项互斥功能，确保用户不会从两个列表中同时选择相同的选项。
type: 0
group: ⚙ 最佳实践
menuName: 相互排除的下拉列表
icon:
order: 7
---

<div><font color=#978f8f size=1>贡献者：陈兴</font> <font color=#978f8f size=1>贡献时间: 2024/07/15</font></div>

## 功能描述

在业务场景中，经常需要确保某些字段的选择是互斥的，即用户不能从两个或多个下拉列表中同时选择相同的选项。例如，在选择供应商时，如果某个供应商已被一个下拉项选择，则该供应商应在其他下拉项选项中自动排除。

## 实际场景

1. 场景链接：[特征一站式/样本/样本编辑](http://moka.dmz.sit.caijj.net/featurestoreui/#/sampleManager/edit?version=1&sampleCode=spl.incd.usr.pikahuigui1721131540)

2. 复现步骤：
   - 点击上述链接
   - 查看 `样本时间字段` 下拉框，此时它包含有 `ageyb2` 的选项。
   - 点击另一个 `样本主题字段` 下拉框，选择 `ageyb2` 。
   - 再次查看 `样本时间字段` 下拉框，此时 `ageyb2` 的选项已被去除。

![未选择“样本主题字段”时的“样本时间字段”下拉项](/dataseeddesigndocui/public/assets/practice7/1.jpg '未选择“样本主题字段”时的“样本时间字段”下拉项')
![选择“样本主题字段”后的“样本时间字段”下拉项](/dataseeddesigndocui/public/assets/practice7/2.jpg '选择“样本主题字段”后的“样本时间字段”下拉项')

## 实践代码

### 不同 name 的选择框实现

```js
// 关键代码
{
  "label": "选项",
  "type": "select",
  "clearable": true,
  "name": "select1",
  "multiple": true,
  "menuTpl": "<div>${label} 值：${value}, 当前是否选中: ${checked}</div>",
  // 通过在 `source` 属性中使用 `filter`，结合 `notIn` 内置过滤器，实现选项的互斥
  "source": "${sourceOptions|filter:value:notIn:${select2}}"
},
{
  "label": "选项",
  "type": "select",
  "clearable": true,
  "name": "select2",
  "multiple": true,
  "menuTpl": "<div>${label} 值：${value}, 当前是否选中: ${checked}</div>",
  "source": "${sourceOptions|filter:value:notIn:${select1}}"
}
```

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "data": {
    "sourceOptions": [
      {
        "label": "A",
        "value": "a"
      },
      {
        "label": "B",
        "value": "b"
      },
      {
        "label": "C",
        "value": "c"
      }
    ]
  },
  "body": [
    {
      "label": "选项",
      "type": "select",
      "clearable": true,
      "name": "select1",
      "multiple": true,
      "menuTpl": "<div>${label} 值：${value}, 当前是否选中: ${checked}</div>",
      "source": "${sourceOptions|filter:value:notIn:${select2}}"
    },
    {
      "label": "选项",
      "type": "select",
      "clearable": true,
      "name": "select2",
      "multiple": true,
      "menuTpl": "<div>${label} 值：${value}, 当前是否选中: ${checked}</div>",
      "source": "${sourceOptions|filter:value:notIn:${select1}}"
    }
  ]
}
```

### 相同 name 的选择框实现

在 combo 或 inputTable 内中，可能需要实现不同行但相同name的select选项的互斥，实现选项互斥的逻辑类似，有两种方案可供选择。

#### 方案一

我们可以尝试通过系统内置的过滤器实现。

```json
/** combo-内置过滤器实现-关键代码 */
{
  "type": "combo",
  "name": "comboList",
  "label": false,
  "multiple": true,
  // 同步获取其他表单项字段
  "strictMode": false,
  "items": [
    {
      "label": "结算方式",
      "type": "select",
      "name": "settleType",
      "clearable": true,
      "multiple": true,
       /**
        * 1.对sourceOptions进行处理，通过ARRAYFILTER方法排除当前项已选的值，再通过ARRAYMAP方法整理出其他已选的值的集合
        * 2.通过在 `source` 属性中使用 `filter`，结合 `notIn` 内置过滤器排除所有已选项，实现选项的互斥
        * 3.通过join可同时兼容单选和多选
        */
      "source": "${sourceOptions|filter:value:notIn:${ARRAYMAP(ARRAYFILTER(comboList,item=>item.settleType!==settleType),item=>item.settleType) | join}}"
    }
  ]
}
```

```schema: scope="body"
{
  "type": "form",
  "title": "combo-内置过滤器实现",
  "debug": true,
  "data": {
    "sourceOptions": [
      {
        "label": "A",
        "value": "A"
      },
      {
        "label": "B",
        "value": "B"
      },
      {
        "label": "C",
        "value": "C"
      },
      {
        "label": "D",
        "value": "D"
      }
    ]
  },
  "body": [
    {
      "type": "combo",
      "name": "comboList",
      "label": false,
      "multiple": true,
      "strictMode": false,
      "items": [
        {
          "label": "结算方式",
          "type": "select",
          "name": "settleType",
          "clearable": true,
          "multiple": true,
          "source": "${sourceOptions|filter:value:notIn:${ARRAYMAP(ARRAYFILTER(comboList,item=>item.settleType!==settleType),item=>item.settleType) | join}}"
        }
      ]
    }
  ]
}
```

```json
/** inputTable-内置过滤器实现-关键代码*/
{
  "type": "input-table",
  "name": "tableList",
  // 同步获取其他表单项字段
  "strictMode": false,
  // 全量更新行数据
  "updateAllRows": true,
  "columns": [
    {
      "name": "a",
      "label": "A"
    },
    {
      "label": "结算方式",
      "type": "select",
      "name": "settleType",
      "clearable": true,
      "multiple": true,
      /**
        * 1.对sourceOptions进行处理，通过ARRAYFILTER方法排除当前项已选的值，再通过ARRAYMAP方法整理出其他已选的值的集合
        * 2.通过在 `source` 属性中使用 `filter`，结合 `notIn` 内置过滤器排除所有已选项，实现选项的互斥
        * 3.通过join可同时兼容单选和多选
        */
      "source": "${sourceOptions|filter:value:notIn:${ARRAYMAP(ARRAYFILTER(tableList,item=>item.settleType!==settleType),item=>item.settleType) | join}}"
    }
  ]
}
```

```schema: scope="body"
{
  "type": "form",
  "title": "inputTable-内置过滤器实现",
  "debug": "true",
  "id": "tableForm",
  "data": {
    "sourceOptions": [
      {
        "label": "A",
        "value": "A"
      },
      {
        "label": "B",
        "value": "B"
      },
      {
        "label": "C",
        "value": "C"
      },
      {
        "label": "D",
        "value": "D"
      }
    ],
    "tableList": [
      {
        "a": "a1",
        "settleType": ""
      },
      {
        "a": "a1",
        "settleType": ""
      }
    ]
  },
  "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
  "body": [
    {
      "type": "input-table",
      "name": "tableList",
      "strictMode": false,
      "updateAllRows": true,
      "columns": [
        {
          "name": "a",
          "label": "A"
        },
        {
          "label": "结算方式",
          "type": "select",
          "name": "settleType",
          "clearable": true,
          "multiple": true,
          "source": "${sourceOptions|filter:value:notIn:${ARRAYMAP(ARRAYFILTER(tableList,item=>item.settleType!==settleType),item=>item.settleType) | join}}"
        }
      ]
    }
  ]
}
```

#### 方案二

也可以自定义一个过滤器用自定义逻辑来实现，比如 `optionsFilter`，传入全部选项数据源、绑定的数据列表、当前选项值、当前选项 name 、拼接符等关键参数，返回过滤后的选项。注意，一定要先引入 `registerFilter`。

```js
// 自定义过滤器-关键代码

import {registerFilter} from '@dataseed/amis';

/**
 * 自定义过滤器，用于实现选项的互斥
 * @param {Array} sourceOptions 全部选项数据源，必填
 * @param {Array} listData 绑定的数据列表，必填
 * @param {String} itemValue 当前选项值，必填
 * @param {String} itemName 当前选项name，必填
 * @param {String} delimiter 拼接符，可选，默认为逗号
 * @returns {Array} 过滤后的选项
 */
registerFilter(
  'optionsFilter',
  (sourceOptions, listData, itemValue, itemName, delimiter = ',') => {
    const [selectedValues, itemValues] = [new Set(), new Set()];

    // 提取 绑定数据列表 中的选中值
    listData?.forEach((item) => {
      const selectValue = item[itemName];
      if (selectValue) {
        const values = selectValue.split(delimiter); // 通过split兼容多选，这里以string为例，其他类型可自行处理
        values.forEach((value) => selectedValues.add(value));
      }
    });

    // 提取自身选中的值
    if (itemValue) {
      const values = itemValue.split(delimiter); // 通过split兼容多选
      values.forEach((value) => itemValues.add(value));
    }

    // 进行过滤并返回新的数组，过滤掉自身没选中且绑定数据列表中已选中的值
    return sourceOptions.filter((item) => itemValues.has(item.value) || !selectedValues.has(item.value));
  },
);
```

```js
/** combo-自定义过滤器实现-关键代码*/
{
  "type": "combo",
  "name": "comboList",
  "label": false,
  "multiple": true,
  // 同步获取其他表单项字段
  "strictMode": false,
  "items": [
    {
      "label": "结算方式",
      "type": "select",
      "name": "settleType",
      "clearable": true,
      "multiple": true,
      // 使用自定义过滤器并传参
      "source": "${sourceOptions| optionsFilter:${comboList}:${settleType}:'settleType'}"
    }
  ]
}
```

```schema: scope="body"
{
  "type": "form",
  "title": "combo-自定义过滤器实现",
  "debug": true,
  "data": {
    "sourceOptions": [
      {
        "label": "A",
        "value": "A"
      },
      {
        "label": "B",
        "value": "B"
      },
      {
        "label": "C",
        "value": "C"
      },
      {
        "label": "D",
        "value": "D"
      }
    ]
  },
  "body": [
    {
      "type": "combo",
      "name": "comboList",
      "label": false,
      "multiple": true,
      "strictMode": false,
      "items": [
        {
          "label": "结算方式",
          "type": "select",
          "name": "settleType",
          "clearable": true,
          "multiple": true,
          "source": "${sourceOptions| optionsFilter:${comboList}:${settleType}:'settleType'}"
        }
      ]
    }
  ]
}
```

```js
/** inputTable-自定义过滤器实现-关键代码*/
{
  "type": "input-table",
  "name": "tableList",
  // 同步获取其他表单项字段
  "strictMode": false,
  // 全量更新行数据
  "updateAllRows": true,
  "columns": [
    {
      "name": "a",
      "label": "A"
    },
    {
      "label": "结算方式",
      "type": "select",
      "name": "settleType",
      "clearable": true,
      "multiple": true,
      "source": "${sourceOptions| optionsFilter:${tableList}:${settleType}:'settleType'}"
    }
  ]
}
```

```schema: scope="body"
{
  "type": "form",
  "title": "inputTable-自定义过滤器实现",
  "debug": "true",
  "id": "tableForm",
  "data": {
    "sourceOptions": [
      {
        "label": "A",
        "value": "A"
      },
      {
        "label": "B",
        "value": "B"
      },
      {
        "label": "C",
        "value": "C"
      },
      {
        "label": "D",
        "value": "D"
      }
    ],
    "tableList": [
      {
        "a": "a1",
        "settleType": ""
      },
      {
        "a": "a1",
        "settleType": ""
      }
    ]
  },
  "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
  "body": [
    {
      "type": "input-table",
      "name": "tableList",
      "strictMode": false,
      "updateAllRows": true,
      "columns": [
        {
          "name": "a",
          "label": "A"
        },
        {
          "label": "结算方式",
          "type": "select",
          "name": "settleType",
          "clearable": true,
          "multiple": true,
          "source": "${sourceOptions| optionsFilter:${tableList}:${settleType}:'settleType'}"
        }
      ]
    }
  ]
}
```

## 代码分析

- **数据源共享**: 多个下拉列表共享同一个数据源 `sourceOptions` 。
- **互斥实现**: 
  - 通过在 `source` 属性中使用 `filter`+`notIn` 内置过滤器或者自定义过滤器，实现选项的互斥。即，当一个选项被选中后，它会从另一个下拉列表的可选选项中自动排除。
  - 也使用 `registerFilter` 注册过滤器实现自定义逻辑。

参考文档

1. [数据映射 过滤器 filter](/dataseeddesigndocui/#/amis/zh-CN/course/concepts/data-mapping?anchor=filter)
2. [数据映射 自定义过滤器](/dataseeddesigndocui/#/amis/zh-CN/course/concepts/data-mapping?anchor=自定义过滤器)
3. [表达式 ARRAYMAP 及 ARRAYFILTER](/dataseeddesigndocui/#/amis/zh-CN/course/concepts/expression?anchor=ARRAYMAP)
