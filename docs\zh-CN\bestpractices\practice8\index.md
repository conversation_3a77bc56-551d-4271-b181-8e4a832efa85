---
title: 批量操作按钮依赖条件禁用
description: 陈兴
type: 0
group: ⚙ 最佳实践
menuName: 批量操作按钮依赖条件禁用
icon:
order: 8
standardMode: true
---

<div><font color=#978f8f size=1>贡献者：陈兴</font> <font color=#978f8f size=1>贡献时间: 2024/07/15</font></div>

## 功能描述

在数据列表中，批量操作按钮应根据选中行数据的特定条件（例如纳税人识别号一致、账户名称一致）动态启用或禁用。只有当所有选中数据符合特定条件时，相应的批量操作按钮才被启用。

## 实际场景

1. 场景链接：[特征一站式/平台管理/魔方迁移](http://moka.dmz.sit.caijj.net/featurestoreui/#/magicMigrate?activeTab=tab1)
2. 复现步骤：
   - 选择两条迁移状态为"空跑结束"的数据，“开启空跑”的批量操作按钮将启用。
   - 再增加选中一条迁移状态为"已迁移"的数据，“开启空跑”的批量操作按钮将禁用。

![选择"空跑结束"数据](/dataseeddesigndocui/public/assets/practice8/1.png "选择'空跑结束'数据")
![再选择一条"已迁移"数据](/dataseeddesigndocui/public/assets/practice8/2.png "再选择一条'已迁移'数据")


## 实践代码
```js
// 关键代码
"bulkActions": [
  {
    "type": "button",
    "label": "纳税人识别号 都为A 展示",
    // 通过ARRAYSOME数组表达式对选中数据进行过滤,选择启用或者禁用按钮
    "disabledOn": "${ARRAYSOME(selectedItems,item => item.grade !== 'A')}"
  },
  {
    "type": "button",
    "label": "账户名称 都为 Win 95+ 展示",
    "disabledOn": "${ARRAYSOME(selectedItems,item => item.platform !== 'Win 95+')}"
  },
]
```

```schema
{
  "type": "page",
  "body": {
    "type": "crud",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
    "multiple": true,
    "columnsTogglable": false,
    "bulkActions": [
      {
        "type": "button",
        "label": "纳税人识别号 都为A 展示",
        "disabledOn": "${ARRAYSOME(selectedItems,item => item.grade !== 'A')}"
      },
      {
        "type": "button",
        "label": "账户名称 都为 Win 95+ 展示",
        "disabledOn": "${ARRAYSOME(selectedItems,item => item.platform !== 'Win 95+')}"
      },
    ],
    "columns": [
      {
        "name": "engine",
        "label": "供应商名称",
        "searchable": {
          "type": "input-text"
        }
      },
      {
        "name": "grade",
        "label": "纳税人识别号",
        "searchable": {
          "type": "input-text"
        }
      },
      {
        "name": "platform",
        "label": "账户名称"
      }
    ]
  }
}
```

## 代码分析
- **动态禁用**: 通过disabledOn属性，实现了按钮的动态禁用逻辑。
- **互斥实现**: 通过ARRAYSOME数组表达式对选中数据进行过滤，只有当所有选中数据都满足特定条件时，才会启用相应的批量操作按钮。

参考文档

1. [表达式 数组 ARRAYSOME](/dataseeddesigndocui/#/amis/zh-CN/course/concepts/expression?anchor=arraysome)
