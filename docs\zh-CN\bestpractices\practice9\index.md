---
title: 使用 Formula 公式反显其他表单项的计算值
description: 庞金明
type: 0
group: ⚙ 最佳实践
menuName: 使用 Formula 公式反显其他表单项的计算值
icon:
order: 9
standardMode: true
---

<div><font color=#978f8f size=1>贡献者：庞金明</font> <font color=#978f8f size=1>贡献时间: 2024/07/28</font></div>

## 功能描述

很多情况下，在表单中会有某个表单项的值，是基于其他表单项的输入值计算过后的值。 比如，“总计”输入框，需要反显 `InputTable` 所有行的合计信息。

## 实际场景

### 场景一：
1. 场景链接：[账务平台/执行运营/合作方清结算/预付款管理/创建预付款单](http://moka.dmz.sit.caijj.net/accountingui/#/advance-payment-manage/payment-add?payCategory=COMMON_PAY_ITEM&payItemType=ADVANCE_PAYMENT&pageStatus=add)

2. 复现步骤：
   - 点击上述链接
   - 滚动至 费用信息 分栏
   - 添加 费用信息，输入“预付款金额”
   - 基本信息 分栏中的 “预付金额”，会相应更改

![添加费用信息](/dataseeddesigndocui/public/assets/practice9/1.png)

![反显值](/dataseeddesigndocui/public/assets/practice9/2.png)

#### 实践代码

使用 `Formula` 公式组件反显表单计算值。

优势：
1. 在表单中基于某些表单项的值，计算新的值。当任何依赖的值变动，可以自动计算，配合自定义 `filter` 可实现各种计算功能。
2. 支持 `“condition”` 配置生效条件，使用灵活。
3. `Formula` 组件，和 `Hidden` 组件一样，都不会展示在界面上，因此可以用于存储计算的中间值。

```js
{
  // formula 与 hidden 一致，不会被展示
  "type": "formula",
  // 当 name 值，在 form 中 已存在 name 相同的表单项，就会把值设置该表单项上
  // 当 name 值，在 form 中 不存在 name 相同的表单项，就会在 form 添加一个该 name 的值的数据
  "name": "sumAmount", // 合计值
  // 配置表达式，用于赋值给 sumAmount
  "formula": '${items|pick:amount|sum|round:2}'
}, {
  "type": "formula",
  "name": "averageAmount", // 平均值
  "initSet": false,  // 初始化时不计算。根据 sumAmount，items 变更时计算
  // 配置表达式，用于赋值给 averageAmount
  "formula": '${sumAmount/(items.length || 1)|round:2}'
}
```

```schema
{
  "type": "page",
  "data": {
    "items": [
      {
        "item": "工资",
        "amount": "1000",
        "adjustAmt":-3,
        "settlementAmt": 997
      }
    ]
  },
  "body":[
  {
    "title": "使用formula组件",
    "type": "form",
    "debug": true,
    "mode": "horizontal",
    "body": [
      {
        "type": "input-text",
        "label": "合计金额(元)",
        "name": "sumAmount",
        "disabled": true,
        "placeholder": "请输入"
      },
      {
        "type": "formula",
        "name": "sumAmount",
        "formula": "${items|pick:amount|sum|round:2}"
      },
      {
        "type": "input-text",
        "label": "平均金额(元)",
        "name": "averageAmount",
        "disabled": true,
        "placeholder": "请输入"
      },
      {
        "type": "formula",
        "name": "averageAmount",
        "initSet": false,
        "formula": "${sumAmount/(items.length || 1)|round:2}"
      },
      {
        "type": "input-table",
        "name": "items",
        "label": "费用信息",
        "strictMode": false,
        "updateAllRows":true,
        "addable": true,
        "editable": true,
        "removable": true,
        "columns": [
          {
            "name": "item",
            "label": "费项",
            "value": "工资",
            "quickEdit": {
              "type": "select",
              "options": [
                "工资",
                "福利"
              ]
            }
          },
          {
            "name": "amount",
            "label": "金额(元)",
            "value": "100",
            "quickEdit": {
              "type": "input-number",
              "precision": 2
            }
          },
        ]
      }
    ]
 
  
  }
  ]

}
```
### 场景二：
1. 场景链接：[触达平台/账单管理/AI 账单/账单详情](http://moka.dmz.sit.caijj.net/messagecenterui/#/aiBillingDetail?id=280&viewMode=check&status=SETTLED)

2. 复现步骤：
在账单明细中调整表格中调整金额输入框数值或者切换下拉框中+/-符号

![示例图片](/dataseeddesigndocui/public/assets/practice9/3.png )

#### 实践代码
```js

  // 配置表达式，用于赋值给 settlementAmt
   {
      "name": "settlementAmt",
      "label": "结算金额（元）",
      "type": "input-text",
      "static": true
    },
  {
    // 当 name 值，在 form 中 已存在 name 相同的表单项，就会把值设置该表单项上
    "name": "settlementAmt",
    "type": "formula",
    "formula": '${adjustAmtFlag ? (provisionAmt + adjustAmt):(provisionAmt - adjustAmt)}',
   }

```
 

```schema
{
  "type": "page",
  "body":[
  {
    "type": "form",
     "title": "联动form",
    "id": 'bill-form-id',
    data: {
      billDetails: [
        {
          accountName: 'AI人机耦合_促首借',
          adjustAmt: 5,
          provisionAmt: 3882,
           settlementAmt: 3887,
          channelType: 'RJOH',
          channelTypeName: 'AI-人机耦合',
          costTypeName: '轻资产',
          adjustAmtFlag:1,
        },
        {
          accountName: 'AI人机耦合_促申完-低转化睡眠户',
          adjustAmt: 7,
          provisionAmt: 165,
          settlementAmt: 172,
          channelTypeName: 'AI-人机耦合',
          costTypeName: 'AI及IVR·轻资产.人机耦合',
          adjustAmtFlag:1
        },
      ],
      provisionAmt: 4047,
      settlementAmt: 4059,
    },
    "showLabelColon": true,
    
    "body": [
      {
        "type": "input-table",
        id: 'bill-table-id',
        label: false,
        strictMode: false,
        name: 'billDetails',
        columns: [
          
          {
            name: 'accountName',
            label: '账号名称',
          },
          {
            name: 'channelTypeName',
            label: '业务类型',
          },
          {
            name: 'costTypeName',
            label: '费用类型',
          },
          {
            name: 'provisionAmt',
            label: '计提金额（元）',
            type: 'tpl',
            tpl: '¥${provisionAmt}',
          },
          {
            name: 'input-group',
            width: 160,
            label: '调整金额（元）', // 编辑模式
            type: 'input-group',
            className: 'ajust-input-model',
            body: [
              {
                type: 'select',
                name: 'adjustAmtFlag',
                label: false,
                required: true,
                className: 'm-0',
                options: [
                    {
                      label: '+',
                      value: 1,
                    },
                    {
                      label: '-',
                      value: 0,
                    },
                ],
                style: {
                  width: 60,
                  margin: 0,
                },
              },
              {
                type: 'input-number',
                label: false,
                precision: 2,
                name: 'adjustAmt',
                min: 0,
              },
            ],
          },
          {
            "name": "settlementAmt",
            "label": "结算金额（元）",
            "type": "input-text",
            "static": true
          },
          {
          "name": "settlementAmt",
          "type": "formula",
          "formula": '${adjustAmtFlag ? (provisionAmt + adjustAmt):(provisionAmt - adjustAmt)}',
          // 如果您业务中的计算逻辑比较复杂 可以结合自定义filter 的方式得到您需要的计算结果
         }
        ],
        affixRow: [
        [
          {
            type: 'text',
            text: '计提金额',
          },
          {
            type: 'tpl',
            tpl: '¥${provisionAmt}',
          },
        ],
        [
          {
            type: 'text',
            text: '结算金额',
          },
          {
            type: 'tpl',
            tpl: '¥${billDetails|pick:settlementAmt|sum|round:2}',
          },
        ],
      ],
      }
    ]
  }
  ]

}
```


## 代码分析

在 `Form` 的 `static` 模式时，需要展示一些计算值的时候，一般都会考虑使用 `tpl` + `filter` 来实现。

在 `Form` 的编辑态的时，需要用到某些输入项的计算值的场景下。可以考虑使用 `Formula` + `filter` 来给表单数据域设置一个计算值，可以将该值设置到其他表单项上，也可以配合 `sendOn`,`visibleOn` 等其他任何可以用表达式的地方来作联动。

参考文档
- [Formula 公式](/dataseeddesigndocui/#/amis/zh-CN/components/form/formula)
- [Hidden 隐藏字段](/dataseeddesigndocui/#/amis/zh-CN/components/form/hidden)
- [数据映射](/dataseeddesigndocui/#/amis/zh-CN/course/concepts/data-mapping)
