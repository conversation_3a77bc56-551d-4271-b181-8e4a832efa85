---
title: 接口数据缓存
description: 常鹏元
type: 0
group: ⚙ 最佳实践
menuName: 接口数据缓存
icon:
order: 22
---

<div><font color=#978f8f size=1>贡献者：常鹏元</font> <font color=#978f8f size=1>贡献时间: 2024/09/11</font></div>

## 功能描述

在平时开发中，我们经常会遇见同一个接口的数据在多个地方使用的问题，在我们写的过程中我们就需要在多个地方去调用同一个接口，但是我们又不希望接口发送很多次，这时候我们可以使用amis的接口缓存功能。

## 实际场景

多个地方需要使用同一份接口数据的时候，比如多个下拉框都需要用到公司所有人员列表数据，当前业务很多人会在page层发送请求获取数据，下发到下层组件中，但是如果组件中嵌套数据域层级过深可能会导致数据在传递过程中丢失，这时候我们可以使用接口缓存功能。

## 实践代码

核心代码

```json
{
  "type": "form",
  "body": [
    {
      "type": "select",
      "name": "a",
      "label": "a",
      "source": {
        "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getOptions",
        "method": "get",
        "cache": 5000 // 设置5秒缓存事件，5秒内接口不会重新发送请求
      }
    },
    {
      "type": "select",
      "name": "b",
      "label": "b",
      "source": {
        "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getOptions",
        "method": "get",
        "cache": 5000 // 设置5秒缓存事件，5秒内接口不会重新发送请求
      }
    }
  ]
}
```

通过缓存接口数据，后续其他地方中发送相同的接口的时候就会使用缓存数据，而不是重新发送请求。查看network面板，可以看到接口请求只发送了一次。这种方式可以避免一些数据域传值问题。

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "body": [
      {
        "type": "select",
        "name": "a",
        "label": "a",
        "source": {
          "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getOptions",
          "method": "get",
          "cache": 5000
        }
      },
      {
        "type": "select",
        "name": "b",
        "label": "b",
        "source": {
          "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getOptions",
          "method": "get",
          "cache": 5000
        }
      }
    ]
  }
}
```

其他场景，弹窗中和外部表单使用了同一份接口数据，也可以使用接口缓存。

```schema
{
  "type": "page",
  "body": [
    {
      "type": "button",
      "label": "dialog",
      "actionType": "dialog",
      "dialog": {
        "title": "弹窗",
        "body": {
          "type": "form",
          "body": [
            {
              "type": "select",
              "name": "b",
              "label": "b",
              "source": {
                "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getOptions?waitSecond=1",
                "method": "get",
                "cache": 60000
              }
            }
          ]
        }
      }
    },
    {
      "type": "form",
      "body": [
        {
          "type": "select",
          "name": "a",
          "label": "a",
          "source": {
            "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getOptions?waitSecond=1",
            "method": "get",
            "cache": 60000
          }
        }
      ]
    }
  ]
}
```


## 代码分析

- 接口缓存：可以利用API的cache属性来设置接口的缓存时间，当接口发送请求时，如果缓存时间内有缓存数据，则直接使用缓存数据，而不会重新发送请求，从而达到减少请求次数的效果。

参考文档

1. [接口缓存](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api?anchor=配置接口缓存)
