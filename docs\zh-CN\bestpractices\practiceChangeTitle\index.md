---
title: Title 页面标题动态修改
description: 张广森
type: 0
group: ⚙ 最佳实践
menuName: Title 页面标题动态修改
icon:
order: 8
---

<div><font color=#978f8f size=1>贡献者：张广森</font> <font color=#978f8f size=1>贡献时间: 2024/12/5</font></div>

## 功能描述

根据实际业务需求自定义数据的标题，例如“任务名称修改”或“编辑任务名称”。

## 实际场景

1. 场景链接：[大数据一站式/数据目录/全链路血缘/报告管理](http://moka.dmz.sit.caijj.net/analytoolui/#/report-manage)
2. 复现步骤：
   - 点击链接打开页面
   - 点击列表操作栏详情按钮进入详情页
   - 点击标题 icon 标题修改数据名称
 ![大数据一站式/数据目录/全链路血缘/报告管理](https://static02.sit.yxmarketing01.com/materialcenter/d53119ac-6cbf-4729-97ad-74eb5af4629d.png)
## 实践代码

代码实现

```js
{
  type:'page',
  id:"pageId",
 initApi: {
    url: "api/amis-mock/mock2/crud/table4",
    adaptor:(_,res,config)=>{
      return {
        // 详情页初始化接口返回任务名称信息
        data:{
         ...res.data
        }
      }
    }
  },
  body:[

      {
      "type": "title",
      "title": "${title}",
      "assistContent": [
        {
          "type": "icon",
          "icon": "pencil",
          "onEvent": {
            "click": {
              "actions": [{
                "actionType": "dialog",
                "dialog": {
                  "title": "任务名称修改",
                  "body": {
                    "type": "form",
                    ...,
                    // 监听表单提交事件，提交成功后,刷新page重新获取新数据
                    "onEvent": {
                      "submitSucc": {
                        "actions": [
                          {
                            "actionType": 'reload',
                            "componentId": "pageId",
                          },
                        ]
                      }
                    },
                  }
                },

              }]
            }
          }

        }
      ]

    }
    ...schema
  ]
}
```

```schema

{
  type: 'page',
  id:"pageId",
  initApi: {
    url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4",
    // 详情接口默认会返回名称信息，tdata为了模拟真实效果，实际场景中可去除
    tdata:{
      title: "${title|default:'任务名称'}"
    },
    adaptor:(_,res,config)=>{
      return {
        // 详情页初始话接口返回任务名称信息
        data:{
          title: config.tdata.title,
          "text1": "aaaa",
          "text2": 18,
          "text3": "7年级",
        }
      }
    }
  },
  "body": [
    {
      "type": "title",
      "iconConfig": {
        "type": "icon",
        "icon": "chevron-left"
      },
      "title": "${title}",
      "subtitle": "我是小标题",
      "assistContent": [
        {
          "type": "icon",
          "icon": "pencil",
          "className": "icon text-info mr-2 ",
          "onEvent": {
            "click": {
              "actions": [{
                "actionType": "dialog",
                "dialog": {
                  "title": "任务名称修改",
                  "body": {
                    "type": "form",
                      "api": {
                        "url":"https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
                      "method":"post",
                    },
                    "body": [{
                      "type": "input-text",
                      "label": "名称",
                      "name": "title"
                    }],
                    // 监听表单提交事件，提交成功后,刷新page重新获取新数据
                    "onEvent": {
                      "submitSucc": {
                        "actions": [
                          {
                            "actionType": 'reload',
                            "componentId": "pageId",
                            // data为了模拟真实效果，实际场景中直接刷新page页面无需书写data
                            "data": {
                              "title": "${event.data.title}"
                            }
                          },
                        ]
                      }
                    },
                  }
                },

              }]
            }
          }

        }
      ]
    },
    {
      "type": "form",
      "static":true,
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
      "body": {
        "type": "group-container",
        "items": [
          {
            "header": {
              "title": "第一步，基础信息"
            },
            "body": [
              {
                "type": "group",
                "body": [
                  {
                    "type": "input-text",
                    "name": "text1",
                    "label": "姓名",

                  },
                  {
                    "type": "input-text",
                    "name": "text2",
                    "label": "年龄"
                  },
                  {
                    "type": "input-text",
                    "name": "text3",
                    "label": "班级",
                    "required": true
                  }
                ]
              },


            ]
          },

        ]
      },
     
    }
  ]
}

```

## 代码分析

1. 监听图标 click 事件打开更改名称弹框
2. 弹框表单数据提交成功后，刷新 page 页面，获取新的数据
