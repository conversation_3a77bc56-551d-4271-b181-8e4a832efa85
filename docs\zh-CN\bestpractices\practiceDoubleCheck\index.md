---
title: Form表单提交服务端二次确认
description: 贾皓
type: 0
group: ⚙ 最佳实践
menuName: Form表单提交服务端二次确认
icon:
order: 8
---

<div><font color=#978f8f size=1>贡献者：贾皓</font> <font color=#978f8f size=1>贡献时间: 2024/12/16</font></div>

## 功能描述

在业务场景中，在进行Form表单提交之前往往会经过服务端的一道预校验，调用预校验接口，如果服务端认为有必要强提醒用户注意此次提交的风险，会在预校验接口返回时让前端弹出二次确认弹框，需要用户手动确认再进行最终的表单提交。

## 实际场景

1. 需求链接：[产品运营/协议管理/协议模版](http://moka.dmz.sit.caijj.net/esignui/#/contractTemplateList?_shMenuId=PFMK920EC4A6E2334906800DBC3B88ED588E)
2. 复现步骤：
   - 点击上述链接，打开页面。
   - 选择一个已有空跑、灰度的协议模版。
   - 提交空跑、灰度申请按钮
   - 弹出二次确认弹框
   

![](/dataseeddesigndocui/public/assets/practiceDoubleCheck/image.png)

## 实践代码

```js
{
  "type": "button",
  "label": "方案1",
  "actionType": "dialog",
  "dialog": {
    "type": "dialog",
    "id": "submitModal",
    "body": [
      {
        "type": "form",
        // 因为form提交的是预校验API而非正式的提交API，提交成功后不需要立即关闭弹窗
        "closeDialogOnSubmit": false, // 禁止表单提交后自动关闭弹框行为
        "api": "/api/amis-mock/mock2/number/random?waitSeconds=1",
        ...,
        "onEvent": {
          // 此处监听预校验接口的 submitSucc，根据 API返回值判断是否需要二次确认
          "submitSucc": { 
            "actions": [
              {
                "actionType": "dialog",
                // 根据预校验API返回数据，判断是否显示二次确认弹窗
                "expression": "${event.data.result.data.random <= 6}", 
                "stopPropagation": true, // 不继续执行后续的动作
                "dialog": {
                  "title": "提示",
                  "body": [
                    ...
                  ],
                  "actions": [
                    ...,
                    {
                      // 二次确认弹窗，确认按钮，调用正式提交API
                      "label": "确认",
                      "actionType": "ajax",
                      "type": "button",
                      "close": "submitModal", // 关闭 本层与父层 Dialog
                      "api": "/api/amis-mock/mock2/number/random?waitSeconds=1"
                    }
                  ]
                }
              },
              {
                // 预校验接口判断无需二次确认，直接调用正式提交 API
                "actionType": "ajax",
                "args": {
                  "api": "/api/amis-mock/mock2/number/random?waitSeconds=1"
                }
              },
              {
                // 由于设置了 closeDialogOnSubmit：false，提交后不会关闭弹窗。 所以需要主动关闭
                "actionType": "close",
                "componentId": "submitModal",
              }
            ]
          }
        }
      }
    ]
  }
}
```

操作步骤: 
1. 点击按钮，弹出框表单，输入内容后点击提交。（提交后，根据1-10的随机返回值模拟服务端返回是否需要二次确认，小于等于6为需要二次确认。）
3. 当弹出二次弹窗时，点击“确定”按钮，调用正式提交API。


```schema
{
  "type": "page",
  "body": [
    {
      "type": "button",
      "label": "表单弹框",
      "actionType": "dialog",
      "dialog": {
        "type": "dialog",
        "id": "submitModal",
        "body": [
          {
            "type": "form",
            "closeDialogOnSubmit": false,
            "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/number/random?waitSeconds=1",
            "body": [
              {
                "type": "textarea",
                "maxLength": 50,
                "name": "extra",
                "label": "上线备注",
                "required": true,
                "placeholder": "请输入"
              }
            ],
            "onEvent": {
              "submitSucc": {
                "actions": [
                  {
                    "actionType": "dialog",
                    "expression": "${event.data.result.data.random <= 6}",
                    "stopPropagation": true,
                    "dialog": {
                      "title": "提示",
                      "body": [
                        {
                          "type": "icon",
                          "className": "icon pm-icon-mr",
                          "icon": "alert-warning"
                        },
                        "请确认"
                      ],
                      "actions": [
                        {
                          "label": "取消",
                          "actionType": "cancel",
                          "type": "button"
                        },
                        {
                          "label": "确认",
                          "actionType": "ajax",
                          "primary": true,
                          "type": "button",
                          "close": "submitModal",
                          "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/number/random?waitSeconds=1"
                        }
                      ]
                    }
                  },
                  {
                    "actionType": "ajax",
                    "args": {
                      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/number/random?waitSeconds=1"
                    }
                  },
                  {
                    "actionType": "close",
                    "componentId": "sumbitModal",
                  }
                ]
              }
            }
          }
        ]
      }
    },
  ]
}
```
## 代码分析

1. 根据API接口返回判断是否需要弹出弹框，最佳方案是 Button 的 actionType:ajax 的 feedback 配置。可以根据返回值判断是否需要弹框。但是在本场景中，当预校验通过时，需要直接调用正式提交API，feedback 配置无法处理。 

2. 外层Dialog 点击确定按钮调用预校验API，并且不关闭当前 Dialog。在 SubmitSucc 事件中，根据预校验API返回判断是否需要二次确认弹窗。
- 需要2次弹窗时，打开弹窗
  - 点击确定按钮，调用正式提交API，并关闭当前 Dialog，及外层 Dialog
  - 点击取消按钮，关闭当前 dialog
- 不需要2次弹窗时，直接调用正式提交API，并关闭 Dialog


参考文档

1. [feedback相关](/dataseeddesigndocui/#/amis/zh-CN/components/dialog?page=1)
2. [Form submitsucc相关](/dataseeddesigndocui/#/amis/zh-CN/components/For m/index)
