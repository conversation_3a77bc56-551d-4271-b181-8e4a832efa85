---
title: Editor自定义按钮JSON格式化
description: 卢帅兵
type: 0
group: ⚙ 最佳实践
menuName: Editor自定义按钮JSON格式化
icon:
order: 22
---

<div><font color=#978f8f size=1>贡献者：卢帅兵</font> <font color=#978f8f size=1>贡献时间: 2024/10/31</font></div>

## 功能描述

在使用`editor`组件自定义工具按钮时，对 json 进行格式化。

## 实际场景

用户输入 json 比较混乱时（如：用户没有换行/从其他地方粘贴过来一串 json），用户想要更好的观看json结构，这个时候用户就可以通过格式化按钮来对混乱的 json 进行格式化。

1. 场景链接：[网关配置-参数模版](http://moka.dmz.sit.caijj.net/registryui/#/paramTemplateDetail/20/create/ces?templateName=ces)
2. 操作步骤：
      - 点击【JSON导入】按钮
      - 出现弹窗后输入json
      - 输入完成后点击【格式化】
      
![点击JSON导入按钮](/dataseeddesigndocui/public/assets/practiceEditorFormat/step1.png "点击JSON导入按钮")
![输入JSON，点击格式化](/dataseeddesigndocui/public/assets/practiceEditorFormat/step2.png "输入JSON，点击格式化")

## 实践代码

核心代码

```json
"onEvent": {
  "click": {
    "actions": [
      // 校验editor字段
      {
        "actionType": "validate",
        "componentId": "form-validate-some-filed",
        "args": {
          "validateFields": [
            "editor"
          ]
        }
      },
      {
        "actionType": "setValue",
        "componentName": "editor",
        "args": {
          // 通过 toJson过滤器 将json字符串转成 对象/数组，再通过 json过滤器 将 对象/数组 转换成json字符串且缩进两个空格。
          "value": "${editor|toJson|json:2}",
        },
        // 字段校验通过 && 存在值 时执行
        "expression": "${event.data.validateResult.responseData && editor}"
      }
    ]
  }
}
```
【注】在使用自定义按钮时想要拿到`editor`组件最新的值，需要给`editor`组件加一个`strictMode: false`，如果不加的话按钮拿值会有问题。

```schema
{
  "type": "page",
  "body": [
    {
      "type": "form",
      "id": "form-validate-some-filed",
      "debug": true,
      "body": [
        {
          "type": "editor",
          "name": "editor",
          "label": false,
          "language": "json",
          "strictMode": false,
          "validations": {
            "isJson": true
          },
          "toolbar": [
            {
              "type": "button",
              "level": "link",
              "label": "格式化",
              "onEvent": {
                "click": {
                  "actions": [
                    {
                      "actionType": "validate",
                      "componentId": "form-validate-some-filed",
                      "args": {
                        "validateFields": [
                          "editor"
                        ]
                      }
                    },
                    {
                      "actionType": "setValue",
                      "componentName": "editor",
                      "args": {
                        "value": "${editor|toJson|json:2}"
                      },
                      "expression": "${event.data.validateResult.responseData && editor}"
                    }
                  ]
                }
              }
            }
          ]
        }
      ]
    }
  ]
}
```

## 代码分析

- 1.点击【格式化】按钮后校验`editor`组件输入的内容是否符合json格式是否通过。
- 2.校验通过后，通过`toJson`过滤器对 json字符串 转换成 对象/数组，再通过`json`过滤器将 对象/数组 转换成 json字符串，通过`json`过滤器第一个参数可以配置缩进空格数，使其更加美观。

参考文档

1. [数据映射](/dataseeddesigndocui/#/amis/zh-CN/course/concepts/data-mapping?anchor=json)
