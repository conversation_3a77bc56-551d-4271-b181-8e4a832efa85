---
title: Form联动Service动态展示Combo
description: 曾三湘
type: 0
group: ⚙ 最佳实践
menuName: Form联动Service动态展示Combo
icon:
order: 9
---

<div><font color=#978f8f size=1>贡献者：曾三湘</font> <font color=#978f8f size=1>贡献时间: 2024/12/09</font></div>

## 功能描述

在 Form 表单中， 填写一些必填数据，先动态请求服务端获取一部分基础数据，再根据这些基础数据进行编辑，合并计算出最终需要的数据。

## 实际场景

1. 场景链接：[获客平台（主营）/渠道管理/渠道配置/渠道监测](http://moka.dmz.sit.caijj.net/tdpplusui/#/monitoring/link)
2. 复现步骤：
   - 点击上述链接，打开页面。
   - 点击新增链接

![SQL](/dataseeddesigndocui/public/assets/practiceFormSetValue/step1.jpg 'sql')

## 实践代码

核心代码
```json 
// “渠道”下拉框联动选中“推广类型”
{
  "type": "select",
  "name": "mediaCode",
  "source": "${allChannelList}",
  "autoFill": {
    "advType": // 渠道改变的时候，自动填充推广类型
      "${ARRAYFIND(allChannelList, item => item.value === value).ucdTypeSupportList[0]}",
  },
  "label": "渠道",
},
{
  "type": "radios",
  "name": "advType",
  "required": true,
  "label": "推广类型",
  "options": [
    { "label": "下载", "value": "0" },
    { "label": "H5", "value": "1" },
  ],
}
```

```json 
// Service 根据 “渠道”、“推广类型” 表单，动态拉取数据
{
  "type": "service",
  "api": {
    "sendOn": "${advType && mediaCode}", // 当推广类型advType 和 渠道mediaCode都有值的时候，才触发请求
    "trackExpression": "${advType},${mediaCode}", // 监听推广类型advType 和 渠道mediaCode值改变的时候，自动触发请求
    "data": {
      "mediaCode": "${mediaCode}",
      "advType": "${advType}",
    },
  },
}
```

```json
// “链接标记参数” 变更后，生成监测链接列表combo
{
  "type": "formula",
  "name": "allUrlList",
  "formula":
    "${linkrlParameters|resetAllUrlList:${typeList}:${mediaCode}}", 
}
```

```json
// 生成的监测链接列表combo，label是动态的
{
  "type": "combo",
  "label": false,
  "name": "allUrlList",
  "multiple": true,
  "addable": false,
  "removable": false,
  "items": [
    {
      "type": "group",
      "label": {
        "type": "tpl",
        "tpl": "${label}",
      },
      "required": true,
      "direction": "vertical",
      "body": [
        {
          "type": "textarea",
          "className": "leading-6",
          "disabled": true,
          "name": "url",
          "label": false,
          "required": true,
        },
      ],
    },
  ],
}
```

操作步骤： 选择渠道类型，切换推广类型，修改或新增链接标记参数。

```schema
 // 组装url参数
const getParamStr = (linkrlParameters) => {
  if (!linkrlParameters) return '';
  return linkrlParameters
    .map(
      ({ mediaParamName = '', mediaParamMacro = '' }, index) =>
        `${index > 0 ? '&' : ''}${mediaParamName}=${mediaParamMacro}`,
    )
    .join('');
};

// 组装url
const getLinks = (mediaCode, typeList, urlParamStr) => {
  const allUrlList =
    typeList.map((item) => {
      const { label, value, staticUrlPart } = item;
      const baseParamStr = urlParamStr ? `?${urlParamStr}` : '';
      return {
        label,
        url: `/${mediaCode}/${value}${baseParamStr}`,
      };
    }) || [];

  return allUrlList;
};

// 当内容变化时，重新生成链接列表
const resetAllUrlList = (linkrlParameters, typeList, mediaCode) => {
  const urlParamStr = getParamStr(linkrlParameters);
  return getLinks(mediaCode, typeList, urlParamStr);
};

registerFilter('resetAllUrlList', resetAllUrlList);

return {
  "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "initApi": {
      "url": "/api/mock2/crud/table4",
      "method": "get",
      "adaptor": (payload, res) => {
        const { code, message } = res;
        const allChannelList = [   // MOCK的数据
          {
            "value": "TOUTIAO",
            "label": "今日头条",
            "ucdTypeSupportList": ["1"],
          },
          {
            "label": "OPPO",
            "value": "OPPO",
            "ucdTypeSupportList": ["0"],
          },
        ];
        return {
          "status": 0,
          "data": {
            allChannelList,
            "mediaCode": allChannelList?.[0].value, // 默认选中第一个渠道
          },
        };
      },
    },
    "body": [
      {
        "type": "select",
        "name": "mediaCode",
        "required": true,
        "placeholder": "请选择渠道",
        "source": "${allChannelList}",
        "autoFill": {
          "advType":
            "${ARRAYFIND(allChannelList, item => item.value === value).ucdTypeSupportList[0]}",
        },
        "label": "渠道",
      },
      {
        "type": "radios",
        "name": "advType",
        "required": true,
        "label": "推广类型",
        "options": [
          { "label": "下载", "value": "0" },
          { "label": "H5", "value": "1" },
        ],
      },
      {
        "type": "service",
        "api": {
          "url": "/api/mock2/crud/table4",
          "method": "get",
          "sendOn": "${advType && mediaCode}",
          "trackExpression": "${advType},${mediaCode}",
          "data": {
            "mediaCode": "${mediaCode}",
            "advType": "${advType}",
          },
          "adaptor": (_, res, req) => {
            // MOCK的数据
            const data = {
              "typeList": [
                { "label": "曝光监测链接", "value": "exposure" },
                { "label": "点击监测链接", "value": "click" },
              ],
              "paramList": [
                {
                  "mediaParamMacro": "__PROMOTION_ID__",
                  "mediaParamName": "promotion_id",
                },
                {
                  "mediaParamMacro": "__PROMOTION_NAME__",
                  "mediaParamName": "promotion_name",
                },
              ],
            };
            const { paramList:linkrlParameters, typeList } = data;
            return {
              typeList,
              linkrlParameters
            };
          },
        },
        "body": [
          {
            "type": "tabs",
            "tabs": [
              {
                "title": "链接标记参数",
                "tab": {
                  "type": "combo",
                  "name": "linkrlParameters",
                  "multiple": true,
                  "label": false,
                  "value": [],
                  "alwaysShowAddBtn": true,
                  "strictMode": false,
                  "items": [
                    {
                      "name": "mediaParamName",
                      "type": "input-text",
                      "required": true,
                    },
                    {
                      "name": "mediaParamMacro",
                      "placeholder": "请输入",
                      "type": "input-text",
                      "required": true,
                    },
                  ],
                },
              },
            ],
          },
          {
            "type": "formula",
            "name": "allUrlList",
            "formula":
              "${linkrlParameters|resetAllUrlList:${typeList}:${mediaCode}}",
          },
          {
            "type": "combo",
            "label": false,
            "name": "allUrlList",
            "multiple": true,
            "addable": false,
            "removable": false,
            "items": [
              {
                "type": "group",
                "label": {
                  "type": "tpl",
                  "tpl": "${label}",
                },
                "required": true,
                "direction": "vertical",
                "body": [
                  {
                    "type": "textarea",
                    "className": "leading-6",
                    "disabled": true,
                    "name": "url",
                    "label": false,
                    "required": true,
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
}
```

## 代码分析

1. 多表单项联动，渠道联动推广类型用了`autoFill`
2. 推广类型和渠道联动调用service，用了`sendOn`和`trackExpression`, 生成默认的链接标记参数
3. 用了`formula`基于数据, 最后生成了监测链接列表combo
