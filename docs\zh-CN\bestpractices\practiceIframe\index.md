---
title: Amis 与 IFrame 之间通信
description: 王梓仲
type: 0
group: ⚙ 最佳实践
menuName: Amis 与 IFrame 之间通信
icon:
order: 8
---

<div><font color=#978f8f size=1>贡献者：王梓仲</font> <font color=#978f8f size=1>贡献时间: 2024/08/20</font></div>

## 功能描述

当使用 iframe 嵌入外部链接，会遇到两者之间相互通信的场景：
- 需要从 amis 向 iframe 发送信息时，可以使用 service 组件包裹 iframe 组件，在 service 中接收需要传递给 iframe 的值
- 需要从 iframe 链接向 react/amis 代码发送消息时，可以通过 onEvent 监听捕获事件。

## 实际场景

1. 场景链接：[H5配置](http://moka.dmz.sit.caijj.net/customerplatformui/#/shortLink/workShop)
2. 复现步骤：
   - 点击上述链接，打开页面。
   - 输入必填项链接名称，选择链接类型和业务类型。
   - 在代码编辑器中输入JSON。
    - 点击预览，iframe 中会收到发送过来的JSON
   - 合法 json 示例
   ```js
    {
      "respList": [
        {
          "templateType": "TextTemplateConfiguration",
          "configCode": "anti_fraud_name",
          "templateMetadata": {
            "isRequired": true,
            "regex": "",
            "code": "name",
            "description": "姓名",
            "placeholder": "请输入姓名原因（必填）",
            "type": "input",
            "tooltips": ""
          }
        },
        {
          "templateType": "SelectTemplateConfiguration",
          "configCode": "pretendTarget",
          "templateMetadata": {
            "isRequired": true,
            "code": "pretendTarget",
            "options": [
              {
                "optionCode": "HUAN_BEI",
                "associationType": "",
                "associationConfigCode": "",
                "option": "还呗"
              },
              {
                "optionCode": "FUNDER",
                "associationType": "",
                "associationConfigCode": "",
                "option": "资方"
              },
              {
                "optionCode": "MERCHANT",
                "associationType": "",
                "associationConfigCode": "",
                "option": "商家"
              },
              {
                "optionCode": "OTHER",
                "associationType": "TextTemplateConfiguration",
                "generalMetadata": {
                  "isRequired": true,
                  "regex": "",
                  "code": "pretendTargetRemark",
                  "description": "诈骗对象备注",
                  "placeholder": "诈骗分子实施诈骗过程中冒用什么身份或者角色跟您沟通的？请填写对应的身份和角色",
                  "title": "",
                  "type": "textArea",
                  "tooltips": ""
                },
                "associationConfigCode": "anti_fraud_Fraudster",
                "option": "其他"
              }
            ],
            "description": "诈骗对象",
            "placeholder": "请输出诈骗对象（必填）",
            "title": "",
            "tooltips": ""
          }
        },
        {
          "templateType": "TextTemplateConfiguration",
          "configCode": "anti_fraud_details",
          "templateMetadata": {
            "isRequired": false,
            "regex": "",
            "code": "remark",
            "description": "诈骗详情",
            "placeholder": "请您详细描述被诈骗过程，表述越详细越有助于工作人员跟进账户处理问题。",
            "title": "",
            "type": "textArea",
            "tooltips": ""
          }
        }
      ],
      "successScript": "已收到您的反馈，工作人员将于3个工作日内联系您，请您保持电话畅通，感谢您的支持。",
      "title": "诈骗信息登记"
    }
   ```
  

![输入合法的JSON](/dataseeddesigndocui/public/assets/practiceIframe/step1.png "输入合法的JSON")

![点击预览按钮]( /dataseeddesigndocui/public/assets/practiceIframe/step2.png "点击预览按钮")




## 实践代码

#### amis向iFrame通信

###### amis代码
```js
{
  type:"page",
  body:[
    {
      type:"button",
      label:"点击我向iFrame发送信息",
      //该示例使用按钮触发setValue事件，与iFrame通信
      onEvent: {
        click: {
          //触发点击事件
          actions: [
            {
              actionType: 'setValue',//为按钮设置点击事件，使用setValue的方式重置service中的值
              componentId: 'iFrameService',//通过id绑定setValue的目标组件
              args: {
                //args为传递的参数
                value: {
                  //value为传递的参数的值，这里表示iframe将接受到一个对象，即{message:"This is a message from amis to iFrame."}
                  message:"This is a message from amis to iFrame."
                },
              },
            },
          ],
        },
      },
    },
    {
    /*
      这里使用service包裹iframe是为了避免发送数据链上不必要的数据。
      通过setValue service组件，保证每次都向iframe其需要数据，避免冗余。
    */
    type:"service",
    id:"iFrameService",//设置service的id，用于接收setValue事件
    data:{
      message:"This is an initial message.",
    },//service数据域中定义data
    body:{
      type:"iframe",
      src:""//填入需要嵌入的外部链接
    }
  }
  ]
}
```

###### iFrame嵌入链接内部代码

```js
{
  window.addEventListener("message",(e)=>{
    //e.data.data即可获取到传入的信息
  })
}
```
#### iFrame向amis通信

###### amis代码
```js
{
  type:"page",
  body:{
    type:"iframe",
    src:"",//你的iFrame链接
    onEvent:{
      myMessage:{//myMessage即在iFrame嵌入外部链接中，函数postMessage中定义的type:"amis:xxx"中xxx，为自定义事件
        actions:[//监听到事件后的处理函数，这里使用弹窗作为示例
          {
            actionType:"dialog",
            dialog:{
              title:"iFrame",
              body:"${iFrameData}"//iFrameData为通过postMessage向amis发送的信息
            }
          }
        ]
      }
    }
  }
}
```

###### iFrame嵌入链接内部代码

场景一：iframe 中嵌入链接为非 amis 应用
```js
//场景一：iframe中嵌入链接为非amis应用，此时在需要发送消息的部分添加window.parent.postMessage即可
window.parent.postMessage({
    //第一个参数必须遵守格式：{type:"amis:xxx",data:{xxx}}，其中amis:xxx 定义了amis中监听的消息名称，格式必须遵守`amis:xxx`的格式
    type:"amis:myMessage",
    data:{
        iFrameData:"i'm message from iFrame"
    }
},"*")
```

场景二：iframe 中嵌入链接为 amis 应用

其中`postMessage`动作是`1.63.0`支持，并且需要依赖`amis-utils`中的`createEnv`。

```js
//场景二：iframe中嵌入链接为amis应用，此时需要通过自定义事件使用window.parent.postMessage来进行消息传递
{
    type:"button",
    label:"click me to send data",
    //使用onEvent，设置actionType为custom，通过自定义事件调用window.parent.postMessage
    onEvent:{
      click:{
        actions:[
        {
          actionType:"custom",
          script:()=>{
              window.parent.postMessage({
                  //这里数据格式必须严格遵守type:"amis:xxx"，xxx为接收消息的amis应用中设置在onEvent中监听事件名称
                  type:"amis:myMessage",
                  data:{
                      iFrameData:"i'm message from iFrame"
                  }
              },"*")
          }
        }
        ]
      }
    }
}

// 1.63.0版本后，amis支持了postMessage，使用postMessage代替自定义动作
{
    "type":"button",
    "label":"click me to send data",
    "onEvent":{
      "click":{
        "actions":[
          {
            "actionType":"postMessage",
            "args": {
              "type":"amis:myMessage",
              "data":{
                  "iFrameData":"i'm message from iFrame"
              }
            }
          }
        ]
      }
    }
}
```


## 代码分析

#### amis向iFrame通信
1. 在 react 中与 iframe 通信需要借助 postMessage 发送消息、addEventListener 捕获消息；amis 封装了 postMessage 这一发送信息的实现：amis 的 iframe 组件会**将最近一层父数据域中数据发送到 iframe 中**，且其数据格式严格遵守`{type:"xxx",data:{obj1:{},obj2:{}....}}`的格式。
2. type 属性 amis 已经定义好，不需要自己去实现。`type:"amis:init"`代表初始化，`type:"amis:update"`代表数据更新。
3. data属性即传递过来的数据。
4. 因此，我们只需要在嵌入的外部链接的应用中添加 addEventListener 事件监听器，并通过`e.data.data`获取到传递的所有信息。

#### iFrame向amis通信
1. iFrame向 amis 通信则需要借助 postMessage 方法实现。
2. 当 iFrame 嵌入链接为非 amis 应用时：
  - 在需要传递信息的地方添加`window.parent.postMessage`
  - 设置消息格式`data:{type:"amis:xxx",data:{...}}`，其中 type 设置的`amis:xxx`中的xxx是amis应用监听的事件
  - 在 amis 的 iframe 中使用 onEvent 以及在 type 中定义的 amis 类型来捕获接收的消息。
3. 若嵌入外部链接为 amis 代码，并且消息传递希望通过点击事件传递，可以通过自定义事件（actionType:"custom"）实现消息传递：
  - 设置`actionType:"custom"`，为 iFrame 中外部 amis 应用的代码设置自定义事件，并通过`window.parent.postMessage`实现 postMessage 方法
  - postMessage 的内容与另一个场景类似，消息格式`data:{type:"amis:xxx",data:{...}}`
  - 在接收消息的 amis 代码中，通过 onEvent 以及在 type 中定义的 amis 事件类型来捕获接收的消息。

参考文档

1. [iFrame](/dataseeddesigndocui/#/amis/zh-CN/components/iframe?anchor=基本使用)

