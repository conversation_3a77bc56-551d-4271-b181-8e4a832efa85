---
title: Service组件schemaApi动态生成tabs
description: 张广森
type: 0
group: ⚙ 最佳实践
menuName: Service组件schemaApi动态生成tabs
icon:
order: 8
---

<div><font color=#978f8f size=1>贡献者：张广森</font> <font color=#978f8f size=1>贡献时间: 2024/12/5</font></div>

## 功能描述

在开发中，我们通常需要根据接口返回的数据动态生成 tab 标签，并展示不同的内容。

## 实际场景

1. 场景链接： [大数据一站式/数据目录/全链路血缘/报告管理](http://moka.dmz.sit.caijj.net/analytoolui/#/report-manage)
2. 复现步骤： 
   - 点击链接打开页面
   - 点击列表操作栏详情按钮进入详情页
3. 页面交互介绍
   - 接口返回依赖对象信息【以 card 形式展示】，每个 card 对应一个 tab标签
   - 总览 tab 只显示依赖对象 card 信息，点击 card 内【查看详情】按钮，可切换到对应的 tab 标签显示该依赖对象对应列表信息
  !['点击列表详情按钮进入此页面'](https://static02.sit.yxmarketing01.com/materialcenter/d53119ac-6cbf-4729-97ad-74eb5af4629d.png '点击列表详情按钮进入此页面')

## 实践代码

代码实现

```js
{
  type:'page',
  id:"pageTabs",
  initApi:"/api/amis-mock/mock2/sample",
  body:[
    ...,
    {
      type:'service',
      // 利用schemaApi动态生成tabs组件
      schemaApi:{
        "url": '/',
        // dataProvider为true，不会发起请求，如果动态schema信息通过接口返回，则不需要设置dataProvider为true
        "dataProvider": true,
        "tdata": "${items}",
         // 保障初始化接口返回数据后再执行，动态渲染逻辑
        "sendOn":"${items}",
        "trackExpression":"${items}",
        "adaptor":(res)=>{
          return {
             "type": "tabs",
            "activeKey": "${tabKeyIndex|toInt}",
            "tabs": [
              // 调用getTab方法，传入类型和数据,渲染不同tab标签内容
              getTab('ALL'),
              ...res.map(item => getTab(item.value, item))
            ],
          }
        }
      },
    }
  ]
}
// 定义getTabs方法，通过接收类型和数据，返回对应tab标签内容
const getTabs = (type, items) => {
  // 业务场景中第一个tabs为总览数据，展示形式为卡片，type为ALL时，返回总览卡片数据
  if (type === "ALL") {
    return {
      "title": "总览",
      "tab": [{
        "type": "cards",
        // items接口返回卡片信息
        "source": "$items",
        ...,
        "body": [
          {
            ...schema
                       {
            "type": "button",
            "level": "link",
            "className": "underline",
            "label": "查看详情",
            "onEvent": {
              "click": {
                "actions": [
                  // 监听查看详情按钮点击事件，更新page数据域内key值，激活对应的tabs组件
                  {
                    "actionType": "setValue",
                    "componentId": "pageTabs",
                    "args": {
                      "value": {
                        // tabs激活默认根据索引，因此取card的索引作为激活tab的key值，card不包含总览卡片，因此索引需加1
                        "tabKeyIndex": "${index+1}"
                      }
                    }
                  }
                ]
              }
            }
          }
         }
      ]
    }]
  }
}
if (type === 'table') {
  return {
    "title": items.title,
    "tab": [{
      "type": "crud",
      ...schema,
    }]
  }
 }
}
```

```schema
// 根据不同的tabKey值，返回对应的tab标签内容  
 const getTab = (tabKey, items) => {
    // 业务场景中第一个tabs为总览数据，展示形式为卡片
    if (tabKey === 'ALL') {
      return {
        "title": "总览",
        "tab": [{
          "type": "cards",
          "source": "$items",
          "card": {
            "header": {
              "bgColor": "blue",
              "title": {
                "type": "flex",
                "justify": "center",
                "items": [
                  {
                    "type": "tpl",
                    "tpl": "${title}",
                    "className": "pm-text-info"
                  }
                ]
              }
            },
            "body": [
              {
                "type": "flex",
                "gap": true,
                "justify": "center",
                "items": [
                  {
                    "name": "num",
                    "type": "tpl",
                    "tpl": "${num|number}",
                    "className": "pm-text-info text-3xl"
                  },
                  {
                    "type": "button",
                    "level": "link",
                    "className": "underline",
                    "label": "查看详情",
                    "onEvent": {
                      "click": {
                        "actions": [
                          {
                            "actionType": "setValue",
                            "componentId": "pageTabs",
                            "args": {
                              "value": {
                                "tabKeyIndex": "${index+1}"
                              }
                            }
                          }
                        ]
                      }
                    }
                  }
                ]
              }
            ]
          }
        }]
      }
    }
    // 业务场景中除第一个tabs外其余tabs为数据源类型，展示形式为表格 【如果依赖类型为表，则展示为表格。如果依赖类型为字段则展示为嵌套表格】
    if (items.metaCode === 'table') {
      return {
        "title": items.title,
        "tab": [{
          "type": "crud",
          "api": {
            "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
            "method": "get",
            "data": {
              "&": "$$",
              // tabKey当前tabs对应的依赖对象值
              "metaCode": items.metaCode,
              // 当前tabs依赖对象类型
              "metaValue": tabKey
            }
          },
          "syncLocation": false,
          "columns": [
            {
              "name": "id",
              "label": "ID"
            },
            {
              "name": "engine",
              "label": "Rendering engine"
            },
            {
              "name": "browser",
              "label": "Browser"
            },
            {
              "name": "platform",
              "label": "Platform(s)"
            },
            {
              "name": "version",
              "label": "Engine version"
            },
            {
              "name": "grade",
              "label": "CSS grade"
            },
            {
              "type": "operation",
              "label": "操作",
              "width": 80,
              "buttons": [
                {
                  "label": "详情",
                  "type": "button",
                  "level": "link",
                },
                {
                  "label": "删除",
                  "type": "button",
                  "level": "link",
                  "disabledOn": "this.grade === 'A'"
                }
              ]
            }
          ]
        }]
      }
    }
    if (items.metaCode === 'field') {
      return {
        "title": items.title,
        "tab": [
          {
            "type": "crud",
            "api": {
              "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
              "method": 'get',
              "data": {
                "&": "$$",
                // tabKey当前tabs对应的依赖对象值
                "metaCode": items.metaCode,
                // 当前tabs依赖对象类型
                "metaValue": tabKey
              }
            },
            "syncLocation": false,
            "id": "tableId",
            "showExpansionColumn": false,
            "columns": [
              {
                "name": "id",
                "label": "ID"
              },
              {
                "name": "engine",
                "label": "Rendering engine"
              },
              {
                "name": "browser",
                "label": "Browser"
              },
              {
                "name": "platform",
                "label": "Platform(s)"
              },
              {
                "name": "version",
                "label": "Engine version"
              },
              {
                "name": "grade",
                "label": "CSS grade"
              },
              {
                "type": "operation",
                "label": "操作",
                "width": 80,
                "buttons": [
                  {
                    "type": "button",
                    "label": "${_amisExpanded ? '收起' : '展开'}",
                    "level": "link",
                    "onEvent": {
                      "click": {
                        "actions": [
                          {
                            "actionType": "toggleExpanded",
                            "componentId": "tableId",
                            "args": {
                              "condition": "${id === currentId}",
                              "currentId": "${id}"
                            }
                          }
                        ]
                      }
                    }
                  }
                ]
              }
            ],
            "subTable": {
              "type": "crud",
              "footerToolbar": [
                {
                  "type": "pagination",
                  "maxButtons": 5,
                  "layout": "total,pager,perPage"
                }
              ],
              "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
              "columns": [
                {
                  "name": "engine",
                  "label": "Engine"
                },
                {
                  "name": "grade",
                  "label": "Grade"
                },
                {
                  "name": "_rowSubTableId",
                  "label": "行子表格id"
                }
              ],
            }
          }
        ]
      }
    }
  }
return {
    "type": 'page',
    "id": "pageTabs",
    "initApi": {
      "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
      method: "get",
      adaptor: (res) => {
        return {
          data: {
            "items": [
              {
                "num": 25,
                "title": "数仓表",
                "value": "TABLE",
                // 接口返回依赖类型【表、字段】
                metaCode: "table"
              },
              {
                "num": 15,
                "title": "数仓字段",
                "value": "INACTIVE",
                metaCode: "field"
              },
            ],
          }
        }
      }
    },
    "body": [{
      "type": 'service',
      "schemaApi": {
        "url": '/',
        "dataProvider": true,
        "tdata": "${items}",
        // 保障初始化接口返回数据后再执行，动态渲染逻辑
        "sendOn": "${items}",
        "trackExpression": "${items}",
        "adaptor": (res) => {
          return {
            "type": "tabs",
            "activeKey": "${tabKeyIndex|toInt}",
            "tabs": [
              getTab('ALL'),
              ...res.map(item => getTab(item.value, item))
            ],
          }
        }
      }
    }]
  }

```

## 代码分析

1. 通过 schemaApi 动态生成 tabs 组件
2. getTab 函数根据传入的参数动态生成 tabs 组件的配置项
3. 点击卡片【查看详情】按钮，根据卡片的索引，激活对应的tab标签
