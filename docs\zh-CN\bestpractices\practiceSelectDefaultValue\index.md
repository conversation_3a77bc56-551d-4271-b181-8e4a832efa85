---
title: CRUD 服务端设置 filter 默认值
description: 毛甜甜
type: 0
group: ⚙ 最佳实践
menuName: CRUD 服务端设置 filter 默认值
icon:
order: 8
---

<div><font color=#978f8f size=1>贡献者：毛甜甜</font> <font color=#978f8f size=1>贡献时间: 2024/11/20</font></div>

## 功能描述

Crud 搜索条件需要从服务端获取（如Select类型、Input类型）设置默认值，然后再进行搜索。并在点击重置按钮后，搜索条件重置为服务端返回的默认值，而不是置空。

## 实际场景

1. 场景链接：[催收平台/机器人平台/交互短信策略管理/交互短信策略模版](http://moka.dmz.sit.caijj.net/airobotui/#/interactiveSMSstrategy)、[催收平台/机器人平台/交互短信任务管理/短信记录](http://moka.dmz.sit.caijj.net/airobotui/#/interactiveSMSrecording)
2. 复现步骤：
   - 访问页面，搜索区的业务组、发送/接收时间会自动填充默认值，并且列表接口第一次请求带上搜索项默认值。

![页面展示](/dataseeddesigndocui/public/assets/practiceSelectDefaultValue/1.png "页面展示")


## 实践代码

核心实践代码。

```js
{
  "type": "page",
  "initApi": {
    "method": "get",
    "url": "/api/mock2/form/getOptions",
    "tdata": {
      "platform": "${platform}" // 此处取的url上的参数值
    },
    "adaptor": (payload, res, req) => {
      /**
       * 维护一个 defaultParams 对象，存放默认的筛选数据
       */
      const defaultParams = {
        'engine': 'Gecko,Trident',
        // 如果 url 中存在 platform 参数，则优先使用 url 中的参数，否则使用默认参数
        'platform': req.tdata.platform || 'Win 98+ / OSX.2+',
      }
      return {
        'data': { 
          defaultParams,
          // 将 defaultParams 的所有默认属性，映射到 filter 数据域中，用作 formItem 默认值填充
          ...defaultParams,
          // api 返回的一系列选项数据
          'platformOptions': [
            {label:'Win 95+', value:'Win 95+'},
            {label:'Win XP', value:'Win XP'},
            {label:'Win 98+ / OSX.2+', value:'Win 98+ / OSX.2+'}
          ]
        }
      }
    }
  },
  "body": {
    "type": "crud",
    "filter": {
      "body": [
        {
          "type": "group",
          "mode": "horizontal",
          "body": [
            {
              "type": "select",
              "name": "engine", // 由 api 返回的 engine 属性设置默认值
              "label": "engine",
              "multiple": true,
              "placeholder": "选择engine",
            },
            {
              "type": "select",
              "name": "platform", // 由 api 返回的 platform 属性设置默认值
              "label": "平台",
              "multiple": true,
              "placeholder": "选择平台",
              "labelField": "label",
              "valueField": "value",
              // 由 api 返回的 platformOptions 选项配置
              "source": "${platformOptions}"
            }
          ]
        }
      ],
      "actions": [
        {
          "type": "button",
          "label": "重 置",
          "onEvent": {
            "click": {
              "actions": [
              {
                /**
                 * 点击重置操作时，将 在 api请求返回的 defaultParams 默认参数，
                 * 重新设置到 filter 中，会自动 反显到filter 中
                 */
                "actionType": "setValue",
                "componentId": "crudFilter",
                "args": {
                  "value": "${defaultParams}"
                }
              },
              {
                /**
                 * 执行提交动作，使用设置好的服务端返回默认数据，查询 crud。
                 */
                "actionType": "submit",
                "componentId": "crudFilter",
              }]
            }
          }
        },
      ]
    },
  }
}
```

```schema
{
  "type": "page",
  "initApi": {
    "method": "get",
    "url": "/api/mock2/form/getOptions",
    "tdata": {
      "platform": "${platform}"
    },
    "adaptor": (payload, res, req) => {
      const defaultParams = {
        'engine': 'Gecko,Trident',
        'platform': req.tdata.platform || 'Win 98+ / OSX.2+',
      }
      return {
        'data': { 
          defaultParams,
          ...defaultParams,
          'platformOptions': [
            {label:'Win 95+', value:'Win 95+'},
            {label:'Win XP', value:'Win XP'},
            {label:'Win 98+ / OSX.2+', value:'Win 98+ / OSX.2+'}
          ]
        }
      }
    }
  },
  "body": [
    {
      "type": "crud",
      "id": "myCrud",
      "api": "/api/mock2/crud/table4",
      "syncLocation": false,
      "filter": {
        "title": "",
        "id": "crudFilter",
        "body": [
          {
            "type": "group",
            "mode": "horizontal",
            "body": [
              {
                "type": "input-text",
                "name": "engine",
                "label": "engine",
                "placeholder": "请输入",
                "clearable": true,
              },
              {
                "type": "select",
                "name": "platform",
                "label": "平台",
                "multiple": true,
                "placeholder": "选择平台",
                "clearable": true,
                "source": "${platformOptions}"
              }
            ]
          }
        ],
        "actions": [
          {
            "type": "button",
            "label": "重 置",
            "onEvent": {
              "click": {
                "actions": [
                {
                  "actionType": "setValue",
                  "componentId": "crudFilter",
                  "args": {
                    value: "${defaultParams}"
                  }
                },
                {
                  "actionType": "submit",
                  "componentId": "crudFilter",
                }]
              }
            }
          },
          {
            "type": "submit",
            "level": "primary",
            "label": "查 询"
          }
        ]
      },
      "columns": [
        {
          "name": "engine",
          "label": "Rendering engine"
        },
        {
          "name": "browser",
          "label": "Browser"
        },
        {
          "name": "platform",
          "label": "Platform(s)"
        },
        {
          "name": "version",
          "label": "Engine version"
        },
        {
          "name": "grade",
          "label": "CSS grade"
        }
      ]
    }
  ]
}
```

## 代码分析

1. 在 page initApi 的 adaptor 中，维护一个 defaultParams 对象， 用于存储服务的设置的默认数据，并将该对象每一个属性设置到数据域中，用于将反填到 formItem 中。
2. 在 curd 的 “重置” 按钮中，先将 defaultParams 对象，设置到 filter 中，再执行 filter 的 submit 事件，进行查询 crud。
3. 注意：CRUD columns.searchable 自动生成查询条件方式不适用（原因：点击重置按钮，会把搜索栏的默认值清空），需通过配置 filter 实现。

参考文档

1. [CRUD 查询条件表单](/dataseeddesigndocui/#/amis/zh-CN/components/crud)
1. [Options 默认值/自动选中 value](/dataseeddesigndocui/#/amis/zh-CN/components/form/options)
