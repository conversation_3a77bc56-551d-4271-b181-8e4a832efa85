---
title: 提交表单，表单项包含$符号
description: 曾三湘
type: 0
group: ⚙ 最佳实践
menuName: 提交表单，表单项包含$符号
icon:
order: 9
standardMode: true
---

<div><font color=#978f8f size=1>贡献者：曾三湘</font> <font color=#978f8f size=1>贡献时间: 2024/09/09</font></div>

## 功能描述

form表单用动作提交，表单项内容包含$符号时，会把$或者${}解析成变量

## 实际场景

1. 场景链接：[数据研发平台/实时计算平台/作业开发/开发任务](http://moka.dmz.sit.caijj.net/hydraui/#/develop-job?id=92)
2. 复现步骤：
   - 点击上述链接，打开页面。
   - 输入必填项作业SQL
   - 在Editor编辑器中输入SQL且SQL内容里包含$符号。
    - 点击保存并提交

![SQL]( /dataseeddesigndocui/public/assets/practiceSpecialSub/step1.jpg "sql")


#### args的表单项包含$会被解析
```js
 {
  "type": "page",
  "body": {
    "type": "form",
    "id": 'detail-form',
    "body": [
      {
        "type": "input-text",
        "name": "editor",
        "label": "编辑器",
      }
    ],
    "actions": [
      {
        type: 'button',
        level: 'primary',
        label: '校验',
        onEvent: {
          click: {
            actions: [
              {
                actionType: 'validate', // 效验表单
                componentId: 'detail-form',
              },
              {
                actionType: 'ajax',
                expression: '${event.data.validateResult.responseData}', // validateResult无值说明校验通过
                args: {
                  api: {
                    "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/form/saveForm", 
                    method: 'post',
                    data: {
                      editor: '${editor}', // editor里面包含${xx}会被解析掉
                    },
                    adaptor: (_, res) => {
                      const { data: { status, msg } } = res;
                      return {
                        status,
                        msg,
                      };
                    },
                  },
                },
              },
            ],
          },
        },
      },
    ]
  }
}


```

## 实践代码

###### 注册过滤器
```js
// 处理sql当中带$的问题
const formatSQL = (sql: any) => {
  if (sql) {
    let regex = /\$/g; // 正则表达式中的$需要转义
    let newSql = sql.replace(regex, '\\$'); // 替换为\$
    return newSql;
  }
  return '';
};
registerFilter('formatSQL', formatSQL);
```

###### amis代码
```js
{
  "type": "page",
  "body": {
    "type": "form",
    "id": 'detail-form',
    "api": {
      "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/saveForm", // 与保存并提交接口地址不一样
      method: 'post',
      data: {
        editor: '${editor}', // 如果是form提交到api的，包含$的内容不会被解析
      },
      adaptor: (_, res) => {
        const { data: { status, msg } } = res;
        return {
          status,
          msg,
        };
      },
    },
    "body": [
      {
        "type": "editor",
        "name": "editor",
        "label": "编辑器",
      },
    ],
    "actions": [
      {
        type: 'submit', 
        level: 'primary',
        label: '保存',
      },
      {
        type: 'button',
        level: 'primary',
        label: '保存并提交',
        onEvent: {
          click: {
            actions: [
              {
                actionType: 'validate', // 先校验表单
                componentId: 'detail-form',
              },
              {
                actionType: 'ajax',
                expression: '${event.data.validateResult.responseData}', // 表单校验通过，执行ajax
                args: {
                  api: {
                    "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
                    method: 'post',
                    data: {
                      editor: '${editor|formatSQL}',
                    },
                    adaptor: (_, res) => {
                      const { data: { status, msg } } = res;
                      return {
                        status,
                        msg,
                      };
                    },
                  },
                },
              },
            ],
          },
        },
      },
    ]
  }
}
```
## 代码分析

1. 表单提交，用动作触发时，args里的data会把$自动解析成变量
2. ajax提交的时候，`editor: '${editor|formatSQL}'`通过过滤器来将输入的内容，通过正则把$改成\\$，避免字段被解析




