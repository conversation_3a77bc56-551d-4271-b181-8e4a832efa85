---
title: 表格单行轮询获取任务状态
description: 张广森
type: 0
group: ⚙ 最佳实践
menuName: 表格轮询获取任务状态
icon:
order: 9
standardMode: true
---

<div><font color=#978f8f size=1>贡献者：张广森</font> <font color=#978f8f size=1>贡献时间: 2024/11/14</font></div>

## 功能描述

某些场景中 crud每一行数据需要单独轮询接口获取当前行的状态。（轮询整个表格过重时，可使用此方案）
## 实际场景
1. 场景链接：[大数据一站式/指标平台/指标要素定义/度量](http://moka.dmz.sit.caijj.net/metricsamisui/#/standard-atom)  
2. 复现步骤
   - 切换到过滤度量tab标签页
   - 选择已上线的过滤度量，点击编辑按钮
   - 修改过滤条件，点击保存并提交按钮，弹出抽屉，点击抽屉试跑按钮 
    
 ![大数据一站式/指标平台/指标要素定义/度量](https://static02.sit.yxmarketing01.com/materialcenter/b5ff8e32-60aa-4709-9169-f42ec624803b.jpg)
## 实践代码
核心代码
```js
 {
  type: "page",
  id: "internal-page",
  body: [
   {
    type:"crud",
      name: "table",
      id: "table",
      primaryField:"id",// 列表接口返回数据唯一字段id
      api: 'crud/table',//crud表格数据接口
      columns: [
        {
          label: "状态",
          type: "service",// 状态列使用service，利用service发起状态的轮询
          idExpr: "statusService${id}",//为service设置动态id,
          // 点击运行按钮后发送请求
          api: {
            url: 'number/random',//发起轮询接口
            sendOn: "${startApiPolling}", // startApiPolling为true时，发送请求
            trackExpression: "${startApiPolling}",
            adaptor: (_, res) => {
              const { data } = res
              return {
                status: 0,
                data: {
                  // 轮询停止条件【模拟实际场景中，轮询接口返回最终状态后的停止条件】
                  stopApiPolling: data.data.random>6

                }
              }
            }
          },
          interval: 2000,
          // 当满足轮询停止条件时（最终状态返回时）停止轮询
          stopAutoRefreshWhen: '${stopApiPolling}',
          body: [
            {
              type: 'mapping',
              name: 'status',
            },
          ],
          onEvent: {
            fetchInited: {
              actions: [
                // 监听service组件的fetchInited事件等待轮询结束后【最终状态返回后】刷新列表获取最新的状态信息
                {
                  actionType: "reload",
                  expression: "${event.data.stopApiPolling}",
                  componentId: "table",
                },
               
              ]
            }
          }
        },
        {
          type: "operation",
          label: "操作",
          buttons: [
            {
              type: 'button',
              label: "运行",
              primary: true,
              onEvent: {
                click: {
                  actions: [
                   //点击运行按钮，根据service动态id 更改service数据域内startApiPolling状态，让service发起请求
                     {
                      actionType: "setValue",
                      componentId: "statusService${id}",
                      args: {
                        value: {
                          startApiPolling: true,
                        }
                      }
                    },
                  ]
                }
              }
            },
          ]
        },

      ]
    },
  ],

}
```
由于mock接口数据限制，无法完全模拟出真实场景，试运行点击后刷新列表，此时列表并未返回真实的状态，因此每行数据初次轮询结束后，再次点击运行按钮并不会发起轮询。

```schema
 {
  type: "page",
  id: "internal-page",
  body: [
  {
    type:"crud",
      name: "table",
      id: "table",
      primaryField:"id",// 列表接口返回数据唯一字段id
      api: {
        url: "/api/mock2/crud/table?page=1&perPage=2",
        method: "get",
        adaptor: (_, res, config) => {
          return {
            data: {
              count: res.data.data.count,
              rows: res.data.data.rows.map((item) => ({
                ...item,
                status:0
              }))

            }
          }
        }
      },
      columns: [
        {
          "name": "id",
          "label": "ID"
        },

        {
          "name": "browser",
          "label": "browser"
        },
        {
          "name": "engine",
          "label": "engine"
        },
        {
          label: "状态",
          type: "service",
          idExpr: "statusService${id}",
          data: {
            status: "${status}",
          },
          // 点击运行按钮后发送请求
          api: {
            url: 'https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/number/random?waitSeconds=1',//接口返回随机数字（模拟轮询）
            sendOn: "${startApiPolling}",
            trackExpression: "${startApiPolling}",
            adaptor: (_, res) => {
              const { data } = res
              return {
                status: 0,
                data: {
                  stopApiPolling: data.data.random>6
                }
              }
            }

          },
          interval: 2000,
          // 当接口返回数字大于6时（即轮询结束，最终状态返回时）停止轮询，（模拟实际场景中，轮询接口返回状态后的停止条件）
          stopAutoRefreshWhen: '${stopApiPolling}',
          body: [
            {
              type: 'mapping',
              name: 'status',
              map: {
                1: {
                  type: 'tag',
                  label: '运行成功',
                  displayMode: 'bordered',
                  color: 'success',
                },
                2: {
                  type: 'tag',
                  label: '运行中',
                  displayMode: 'bordered',
                  color: 'active',
                },
                '*': {
                  type: 'tag',
                  label: '初始化',
                  displayMode: 'bordered',
                  color: 'inactive',
                },
              },
            },
          ],
          onEvent: {
            fetchInited: {
              actions: [
                // 等待轮询结束后【最终状态返回后】刷新列表获取最新的状态信息
                {
                  actionType: "reload",
                  expression: "${event.data.stopApiPolling}",
                  componentId: "table",
                },
                // 模拟轮询结束后（结果返回后）更新状态，实际需求中列表接口会返回该状态
                {
                  actionType: "setValue",
                  expression: "${event.data.stopApiPolling}",
                  componentId: "statusService${id}",
                  args: {
                    value: {
                      status: 1,
                    }
                  }
                },
              ]
            }
          }
        },
        {
          type: "operation",
          label: "操作",
          buttons: [
            {
              type: 'button',
              label: "运行",
              primary: true,
              onEvent: {
                click: {
                  actions: [

                    // 运行中接口返回，点击后请求接口，状态改为运行中
                    {
                      actionType: "setValue",
                      componentId: "statusService${id}",
                      args: {
                        value: {
                          status: 2,
                          startApiPolling: true,
                        }
                      }
                    },

                  ]
                }
              }
            },
          ]
        },

      ]
    },

  ],

}
```
## 代码分析

1. 点击试运行按钮时，更新当前行service数据域内的sendApi状态，让service发起轮询请求
2. 监听service组件的fetchInited事件，满足轮询请求停止条件时即【最终状态返回后】刷新列表
3. 注意：分页或者重新搜索后，列数据重新加载，会丢失之前的轮询轮询状态，导致轮询终止




