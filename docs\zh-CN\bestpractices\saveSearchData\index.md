---
title: Crud保存搜索条件
description: 刘梅
type: 0
group: ⚙ 最佳实践
menuName: Crud保存搜索条件
icon:
order: 9
---

<div><font color=#978f8f size=1>贡献者：刘梅</font> <font color=#978f8f size=1>贡献时间: 2024/12/31</font></div>

## 功能描述

在 Crud 中， 期望将常用搜索条件保存起来，下次进入页面时，可通过点击快捷按钮，迅速填充表单值进行搜索。

## 实际场景

1. 场景链接：[电销平台/业务管理/案件池/个人案件池](http://moka.dmz.sit.caijj.net/telmarketui/#/personCasePools)
2. 复现步骤：
   - 点击上述链接，打开页面。
   - 输入搜索条件，点击“保存”按钮。
   
![SQL](/dataseeddesigndocui/public/assets/saveSearchData/step1.png 'sql')

## 实践代码

核心代码
```json 
// crud的topToolbar区域展示之前已存储的快捷搜索按钮列表
topToolbar: [
      {
        type: "service",
        id: "quick-search-service-id",
        schemaApi: {
          method: 'get',
          url: "http://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getOptions?waitSeconds=1",
          adaptor: (payload) => {
            return {
              status: 0,
              msg: "",
              data: {
                // 最多只能显示2个快捷搜索按钮，超出2个，点击保存时，必须替换原有按钮
                isSurpassMaxCount: payload.length >= 2,
                type: "container",
                body: [
                  ...getQuickSearchSchema(payload),
                ]
              },
            };
          },
        },
        onEvent: {
          // 监听schemaApi 远程请求返回的ui内容，将是否超过搜索条件最大值isSurpassMaxCount字段更新到page数据域中
          fetchSchemaInited: {
            actions: [{
              actionType: "setValue",
              componentId: "page-id",
              args: {
                value: {
                  isSurpassMaxCount: "${event.data.isSurpassMaxCount}",
                }
              }
            }]
          }
        }
      },
    ]

// 快捷搜索按钮列表数据转换为schema的函数
function getQuickSearchSchema(list = []) {
  const result = list.map((item) => {
    const { quickSearchName: name, quickSearchRemark: remark, id, ...restData } = item;
    // 需要将crud筛选区域的表单项值都设置为空，然后用当前存储的字段值覆盖，在点击按钮后，用当前存储的字段值回填筛选区域
    const curButtonData = { ...EMPTY_SEARCH, ...restData };

    return ({
      type: "button",
      label: name,
      levelExpr: `\${activeKey === ${id}  ? 'primary' : 'default'}`,
      tooltipPlacement: 'top',
      tooltip: remark,
      onEvent: {
        click: {
          actions: [
            // 更新快捷搜索按钮高亮样式activeKey
            {
              actionType: 'setValue',
              componentId: 'page-id',
              args: {
                value: {
                  activeKey: item.id,
                },
              }
            },
            // 回填筛选区域表单值
            {
              actionType: 'setValue',
              componentId: 'filter-form-id',
              args: {
                value: curButtonData,
              },
            },
            // 触发crud的查询，调用接口更新列表
            {
              actionType: 'submit',
              componentId: 'filter-form-id',
            },
          ],
        },
      },
    });
  })

  return result;
}
```

```json 
// crud的actions自定义，添加保存按钮，在筛选区域录入了表单项值后，可点击保存按钮打开弹框，录入当前搜索条件的名称描述等字段，存储当前搜索条件
 actions: [
        {
          type: 'button',
          label: '保存',
          tooltip: '保存为快捷搜索',
          // 如果表单项未录入值，则不允许保存
          disabledOn: '${&|isQueryObjectEmpty}',
          disabledTip: '当前搜索内容为空，无法保存',
          actionType: 'dialog',
          dialog: {
            title: '快捷搜索保存',
            body: [
              {
                type: 'form',
                labelWidth: 90,
                debug: true,
                data: {
                  '&': '$$',
                },
                api: {
                  method: 'post',
                  data: {
                    '&': '$$',
                    },
                  url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getOptions?waitSeconds=1",
                },
                body: [
                  ...,
                  {
                    type: 'select',
                    name: 'deleteId',
                    label: '替换',
                    // 最多只能显示2个快捷搜索按钮，超出2个，点击保存时，必须替换原有按钮
                    requiredOn: '${isSurpassMaxCount}',
                    labelField: 'quickSearchName',
                    valueField: 'id',
                    clearable: true,
                    source: "http://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getOptions?waitSeconds=1",
                    placeholder: '请选择替换的快捷搜索按钮',
                  }

                ],
                onEvent: {
                  // 提交成功后，更新搜索按钮列表
                  submitSucc: {
                    actions: [
                      {
                        actionType: 'reload',
                        componentId: 'quick-search-service-id',
                      },
                    ],
                  },
                },
              },
            ],
          },
        },
        {
          type: 'button',
          label: '重置',
          onEvent: {
            click: {
              actions: [
                // 需清空表单项
                {
                  actionType: 'reset',
                  componentId: 'filter-form-id',
                },
                // 更新列表数据
                {
                  actionType: 'submit',
                  componentId: 'filter-form-id',
                },
                // 清空快捷搜索按钮选中项
                {
                  actionType: 'setValue',
                  componentId: 'page-id',
                  args: {
                    value: {
                      activeKey: "",
                    },
                  },
                },
              ],
            },
          },
        },
        {
          type: 'submit',
          level: 'primary',
          label: '查 询',
          onEvent: {
            click: {
              actions: [
                // 清空快捷搜索按钮选中项
                {
                  actionType: 'setValue',
                  componentId: 'page-id',
                  args: {
                    value: {
                      activeKey: "",
                    },
                  },
                },
              ],
            },
          },
        },
      ]
```

操作步骤： 输入搜索值，点击保存打开弹框，录入信息后提交。最多只能显示2个快捷搜索按钮，超出2个，点击保存时，必须替换原有按钮

```schema
const EMPTY_SEARCH = {
  engine: undefined,
  browser: undefined,
  platform: undefined,
  version: undefined,
  grade: undefined,
}

// 快捷搜索按钮列表schema
function getQuickSearchSchema(list = []) {
  const result = list.map((item) => {
    const { quickSearchName: name, quickSearchRemark: remark, id, ...restData } = item;
    const curButtonData = { ...EMPTY_SEARCH, ...restData };

    return ({
      type: "button",
      label: name,
      levelExpr: `\${activeKey === ${id}  ? 'primary' : 'default'}`,
      tooltipPlacement: 'top',
      tooltip: remark,
      onEvent: {
        click: {
          actions: [
            {
              actionType: 'setValue',
              componentId: 'page-id',
              args: {
                value: {
                  activeKey: item.id,
                },
              }
            },
            {
              actionType: 'setValue',
              componentId: 'filter-form-id',
              args: {
                value: curButtonData,
              },
            },
            {
              actionType: 'submit',
              componentId: 'filter-form-id',
            },
          ],
        },
      },
    });
  })

  return result;
}

// 判断当前查询表单是否有某个字段有值
registerFilter('isQueryObjectEmpty', (formValues = {}) => {
  const { pageNo, pageSize, ...fields } = formValues;

  return Object.values(fields).every((value) => {
    if (Array.isArray(value)) {
      return value.length === 0;
    }
    if (value === false || value === 0) {
      return true
    }
    return !value;
  });
});

return {
  type: 'page',
  id: 'page-id',
  debug: true,
  body: {
    type: 'crud',
    pageField: 'pageNo',
    perPageField: 'pageSize',
    id: 'crud-id',
    columnsTogglable: false,
    api: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4",
    topToolbar: [
      {
        type: "service",
        id: "quick-search-service-id",
        schemaApi: {
          method: 'get',
          url: "http://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getOptions?waitSeconds=1",
          adaptor: () => {
            // mock数据
            const quickSearchData = JSON.parse(sessionStorage.getItem('quickSearchData') || "[]")

            return {
              status: 0,
              msg: "",
              data: {
                // 最多只能显示2个快捷搜索按钮，超出2个，点击保存时，必须替换原有按钮
                isSurpassMaxCount: quickSearchData.length >= 2,
                type: "container",
                body: [
                  ...getQuickSearchSchema(quickSearchData),
                ]
              },
            };
          },
        },
        onEvent: {
          fetchSchemaInited: {
            actions: [{
              actionType: "setValue",
              componentId: "page-id",
              args: {
                value: {
                  isSurpassMaxCount: "${event.data.isSurpassMaxCount}",
                }
              }
            }]
          }
        }
      },
    ],
    "columns": [
      {
        "name": "engine",
        "label": "Rendering engine"
      },
      {
        "name": "browser",
        "label": "Browser"
      },
      {
        "name": "platform",
        "label": "Platform(s)"
      },
      {
        "name": "version",
        "label": "Engine version"
      },
      {
        "name": "grade",
        "label": "CSS grade"
      }
    ],
    filter: {
      title: '',
      id: 'filter-form-id',
      debug: true,
      // 监听数据变化，否则dialog内拿不到最新数据
      trackExpression: "${isSurpassMaxCount}",
      body: [
        {
          type: "group",
          mode: "horizontal",
          body: [{
            type: 'input-text',
            label: 'Rendering engine',
            name: 'engine',
            clearable: true,
            placeholder: '请输入',
            columnRatio: 4
          },
            {
              type: 'input-text',
              label: 'Browser',
              name: 'browser',
              clearable: true,
              placeholder: '请输入',
              columnRatio: 4
            },
            {
              type: 'input-text',
              label: 'Platform(s)',
              name: 'platform',
              clearable: true,
              placeholder: '请输入',
              columnRatio: 4
            },
            {
              type: 'input-text',
              label: 'Engine version',
              name: 'version',
              clearable: true,
              placeholder: '请输入',
              columnRatio: 4
            },
              {
                type: 'input-text',
                label: 'CSS grade',
                name: 'grade',
                clearable: true,
                placeholder: '请输入',
                columnRatio: 4
              }]
        }
      ],
      actions: [
        {
          type: 'button',
          label: '保存',
          tooltipPlacement: 'top',
          tooltip: '保存为快捷搜索',
          // 如果表单项未录入值，则不允许保存
          disabledOn: '${&|isQueryObjectEmpty}',
          disabledTip: '当前搜索内容为空，无法保存',
          actionType: 'dialog',
          dialog: {
            title: '快捷搜索保存',
            showCloseButton: false,
            showErrorMsg: false,
            showLoading: false,
            body: [
              {
                type: 'form',
                labelWidth: 90,
                debug: true,
                data: {
                  '&': '$$',
                },
                api: {
                  method: 'post',
                  url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getOptions?waitSeconds=1",
                  adaptor: (payload, api) => {
                    // mock数据
                    let originData = JSON.parse(sessionStorage.getItem("quickSearchData") || "[]")
                    const current = JSON.parse(api.config.data)
                    if (current.deleteId) {
                      originData = originData.filter(item => item.id !== current.deleteId)
                      delete current.deleteId
                    }

                    const quickSearchData = [...originData, { ...current, id: Date.now() }]
                    sessionStorage.setItem('quickSearchData', JSON.stringify(quickSearchData))

                    return {
                      status: 0,
                      data: {}
                    };
                  },
                },
                body: [
                  {
                    type: 'input-text',
                    name: 'quickSearchName',
                    required: true,
                    label: '快捷搜索名称',
                    maxLength: 8,
                    placeholder: '请输入快捷搜索按钮',
                  },
                  {
                    type: 'input-text',
                    name: 'quickSearchRemark',
                    required: true,
                    label: '备注',
                    maxLength: 30,
                    placeholder: '请输入备注',
                  },
                  {
                    type: 'select',
                    name: 'deleteId',
                    label: '替换',
                    requiredOn: '${isSurpassMaxCount}',
                    labelField: 'quickSearchName',
                    valueField: 'id',
                    clearable: true,
                    source: {
                      method: 'get',
                      url: "http://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getOptions?waitSeconds=1",
                      adaptor: () => {
                        // mock数据
                        const quickSearchData = JSON.parse(sessionStorage.getItem('quickSearchData') || "[]")
                        console.log('quickSearchData', quickSearchData)
                        return {
                          status: 0,
                          msg: "",
                          data: {
                            options: quickSearchData
                          },
                        };
                      },
                    },
                    placeholder: '请选择替换的快捷搜索按钮',
                  }

                ],
                onEvent: {
                  // 更新快捷搜索按钮列表
                  submitSucc: {
                    actions: [
                      {
                        actionType: 'reload',
                        componentId: 'quick-search-service-id',
                      },
                    ],
                  },
                },
              },
            ],
          },
        },
        {
          type: 'button',
          label: '重置',
          onEvent: {
            click: {
              actions: [
                // 需清空表单项
                {
                  actionType: 'reset',
                  componentId: 'filter-form-id',
                },
                // 更新列表数据
                {
                  actionType: 'submit',
                  componentId: 'filter-form-id',
                },
                // 清空快捷搜索按钮选中项
                {
                  actionType: 'setValue',
                  componentId: 'page-id',
                  args: {
                    value: {
                      activeKey: "",
                    },
                  },
                },
              ],
            },
          },
        },
        {
          type: 'submit',
          level: 'primary',
          label: '查 询',
          onEvent: {
            click: {
              actions: [
                // 清空快捷搜索按钮选中项
                {
                  actionType: 'setValue',
                  componentId: 'page-id',
                  args: {
                    value: {
                      activeKey: "",
                    },
                  },
                },
              ],
            },
          },
        },
      ],
    }
  }
}
```

## 代码分析

1. page层使用 activeKey 变量，与 levelExpr 配置，来高亮具体选中某一个按钮。
2. crud 的筛选区的“保存”按钮通过`$|isQueryObjectEmpty` 来控制禁用。并在 form传入 data：{&:$$} 将搜索项的值给保存弹框里的表单设置初始化值。
3. 需要为filter配置`trackExpression: "${isSurpassMaxCount}"` ，当搜索按钮的值发生变化时更新筛选区域，否则dialog内拿不到最新数据。
