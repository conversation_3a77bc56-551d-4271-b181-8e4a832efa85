---
title: 根据数据渲染动态 Schema
description: 常鹏元
type: 0
group: ⚙ 最佳实践
menuName: 根据数据渲染动态 Schema
icon:
order: 9
---

<div><font color=#978f8f size=1>贡献者：常鹏元</font> <font color=#978f8f size=1>贡献时间: 2024/08/05</font></div>

## 功能描述

在业务中，根据一些后端数据，动态渲染一些schema是很常见的需求，但是并不是所有的组件都支持动态schema，比如列表最后一列操作列的按钮需要根据服务端返回的数据动态渲染。

## 实际场景

!["根据后端数据渲染动态按钮"](/dataseeddesigndocui/public/assets/schemaApi/1.png)

列表中每一行最后一列操作列的按钮需要根据服务端返回的数据动态渲染，每一行都不一样，且点击后跳转地址都不一致，此时操作列的按钮需要根据数据进行渲染。

## 实践代码

**注意：使用此写法注意将amis的env使用`amis-utils`中暴露的`createEnv`来创建，否则不会生效。并且amis的版本需要在1.59.0以上**

须先检查自己umi项目中`layouts`文件中下发到页面组件的env对象是否是使用`amis-utils`中暴露的`createEnv`来创建的，如果不是，则需要修改。

```js
import {createEnv} from 'amis-utils';
import $http from '@lattebank/webadmin-http';

const env = createEnv({ axiosInstance: $http }); // 创建env

... // 省略代码
  <RouteComponent
    ... // 省略代码
    env={env} // 下发env
  />
... // 省略代码
```

核心代码

```js
{
  "type": "service",
  "label": "操作",
  "schemaApi": {
    "url": "/", // url需要随便配置一个值，不然不会走进组件逻辑
    "dataProvider": true, // 设置了 dataProvider 之后不会发送请求，1.59.0以上版本
    "tdata": "${actions}", // 通过tdata传递数据
    "adaptor": (payload) => {
      // 在adaptor中的payload就是我们传入的tdata，在此处可以根据数据去计算出一个需要渲染的schema并返回，最终返回的这个schema会被渲染到页面上
      return { 
        type: 'operation', 
        buttons: payload.map(item => ({
          type: 'button', 
          label: `按钮 ${item}`, 
          level: 'link', 
          actionType: 'dialog', 
          dialog: { 
            title: '操作', 
            body: { 
              type: 'tpl', 
              tpl: `内容${item}` 
            } 
          }
        })) 
      }
    }
  }
}
```

由于没有合适的线上接口，这里随便使用了一个接口，在`adaptor`中设置了接口的返回值，列表每一行都返回了一个`actions`数组，后面的操作按钮需要根据这个数组去动态渲染，由于站点示例上没法写函数所以`adaptor`中写的是字符串，意思和上面核心代码中的`adaptor`的逻辑是完全一致的。

```schema
{
  "type": "page",
  "body": [
    {
      "type": "crud",
      "api": {
        "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4",
        "adaptor": "return { ...payload, data: { ...payload.data, rows: payload.data.rows.map(item => ({ ...item, actions: [1, 2, 3, 4] })) }  }"
      },
      "columns": [
        {
          "name": "browser",
          "label": "Browser"
        },
        {
          "type": "service",
          "label": "操作",
          "schemaApi": {
            "url": "/",
            "dataProvider": true,
            "tdata": "${actions}",
            "adaptor": "return { type: 'operation', buttons: payload.map(item => ({ type: 'button', label: `按钮 ${item}`, level: 'link', actionType: 'dialog', dialog: { title: '操作', body: { type: 'tpl', tpl: `内容${item}` } }})) }"
          }
        }
      ]
    }
  ]
}
```

## 代码分析

- **动态渲染** 可以利用`service`组件的`schemaApi`属性来实现，最后一列的单元格可以渲染一个`service`组件，配置`schemaApi`，在`schemaApi`中设置`dataProvider`属性，配置了此属性后不会发送请求（这个特性是在`createEnv`中实现的，故依赖`createEnv`），通过`tdata`将数据进行传递，此时在`adaptor`中的`payload`就是我们传如的`tdata`，可以根据传入的数据拼接出需要渲染的`schema`然后返回。

*`dataProvider`也支持设置为一个函数，函数的入参就是`tdata`，返回值就是`adaptor`中的`payload`*

参考文档

1. [service 动态渲染页面](/dataseeddesigndocui/#/amis/zh-CN/components/service?anchor=动态渲染页面)
