---
title: 动态渲染step组件并联动crud
description: 郝亚雷
type: 0
group: ⚙ 最佳实践
menuName: step组件联动crud
icon:
order: 19
---

<div><font color=#978f8f size=1>贡献者：郝亚雷</font> <font color=#978f8f size=1>贡献时间: 2024/12/05</font></div>

## 功能描述

在业务中，通过接口返回动态数据组装步骤step组件，每个步骤相互切换时显隐对应的crud。

## 实际场景
1. 场景链接：[获客平台（主营）/RTA服务/建模管理/建模项目管理/建模项目执行计划详情](http://moka.dmz.sit.caijj.net/tdrtaui/#/modelingManage/projectManage)
2. 复现步骤：
   - 点击上述链接
   - 点击操作列-详情
   - 点选择执行计划tab-点击操作列详情

!["根据后端数据动态渲染左侧步骤组件"](/dataseeddesigndocui/public/assets/stepAndCrud/stepAndCrud.png)

左侧step组件是动态的，可以有很多步骤， step组件每个节点点击后切换对应的crud组件，每一个crud对应的列数据不一样。
## 核心代码

**注意：使用此写法amis的版本需要在1.72.0以上**

```js
   {
        type: 'steps',
        mode: 'vertical', // 步骤垂直展示
        name: 'stepsIndex', //  用于绑定当前步骤
        status: { //  左侧的步骤状态 有失败、未开始、已完成 ，通过每一个步骤的value进行赋值
          first: 'error',// 当前所处步骤的状态 当一个步骤的value设置为 first，则当前步骤为失败状态，
          second: 'wait',// 当前所处步骤的状态 当一个步骤的value设置为 second，则当前步骤为未开始状态，
          last: 'finish',// 当前所处步骤的状态 当一个步骤的value设置为 last， 则当前步骤为已完成状态，
        },
        clickable: true,// 是否允许点击切换步骤
        source: '${stepRespList}', //  steps组件的动态数据，此处可以通过接口返回的数据组装想要的scheme
        onEvent: {
          click: { // 监听点击事件
            actions: [
              {
                actionType: 'setValue',
                componentId: 'page',
                args: { // 对page数据域赋值currentStepsCode，用于对应的crud展示 同时改变stepsIndex 更新步骤状态
                  value:
                    '${{ currentStepsCode: stepRespList[event.data.index].stepCode ,stepsIndex: event.data.index}}',
                },
              },
            ],
          },
        },
      },
```

实际实现代码中会先从接口获取所有步骤的状态如stepRespList，  然后通过stepRespList数组的数据组成steps组件，同时通过点击事件更新page数据域的currentStepsCode，用于对应的crud展示。

```js
{
        type: 'crud',
        className: 'flex-grow', // 和steps组件并排展示样式
        columnsTogglable: false,
        visibleOn: '${currentStepsCode === "FIRST"}', // 根据数据域中的currentStepsCode变化显隐crud
        api: 'https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?waitSeconds=1',
        footerToolbar: [
          {
            type: 'pagination',
            maxButtons: 5,
            layout: 'total,pager,perPage',
          },
        ],
        columns: [
          {
            name: 'id',
            label: 'ID',
          },
          {
            name: 'engine',
            label: 'Rendering engine',
          },
          {
            name: 'browser',
            label: 'Browser',
          },
        ],
      },
```


```schema: scope="body"
{
  type: 'page',
  id: 'page',
  data: {
    currentStepsCode: 'FORTH',
    stepsIndex: 3,
    stepValue: 3,
    stepRespList: [
      {
        title: '设备加工',
        description: {
          type: 'typography',
          text: '设备加工的描述设备加工的描述设备加工的描述',
          ellipsis: {
            rows: 2,
          },
        },
        stepCode: 'FIRST',
        value: 'last',
      },
      {
        title: '入模加工',
        description: {
          type: 'typography',
          text: '入模加工的描述设备加工的描述设备加工的描述',
          ellipsis: {
            rows: 2,
          },
        },
        stepCode: 'SECOND',
        value: 'last',
      },
      {
        title: '模型融合',
        description: {
          type: 'typography',
          text: '模型融合的描述设备加工的描述设备加工的描述',
          ellipsis: {
            rows: 2,
          },
        },
        stepCode: 'THIRD',
        value: 'last',
      },
      {
        title: '模型分合并',
        description: {
          type: 'typography',
          text: '模型融合的描述设备加工的描述设备加工的描述',
          ellipsis: {
            rows: 2,
          },
        },
        stepCode: 'FORTH',
        value: 'first',
      },
    ],
  },
  body: {
    type: 'flex',
    justify: 'start',
    gap: true,
    alignItems: 'start',
    items: [
      {
        type: 'steps',
        mode: 'vertical',
        name: 'stepsIndex',
        status: {
          first: 'error',
          second: 'wait',
          last: 'finish',
        },
        clickable: true,
        source: '${stepRespList}',
        onEvent: {
          click: {
            actions: [
              {
                actionType: 'setValue',
                componentId: 'page',
                args: {
                  value:
                    '${{ currentStepsCode: stepRespList[event.data.index].stepCode ,stepsIndex: event.data.index}}',
                },
              },
            ],
          },
        },
      },
      {
        type: 'crud',
        className: 'flex-grow',
        columnsTogglable: false,
        visibleOn: '${currentStepsCode === "FIRST"}',
        api: 'https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?waitSeconds=1',
        footerToolbar: [
          {
            type: 'pagination',
            maxButtons: 5,
            layout: 'total,pager,perPage',
          },
        ],
        columns: [
          {
            name: 'id',
            label: 'ID',
          },
          {
            name: 'engine',
            label: 'Rendering engine',
          },
          {
            name: 'browser',
            label: 'Browser',
          },
          {
            type: 'operation',
            label: '操作',
            width: 80,
            buttons: [
              {
                label: '删除',
                type: 'button',
                level: 'link',
              },
            ],
          },
        ],
      },
      {
        type: 'crud',
        className: 'flex-grow',
        columnsTogglable: false,
        visibleOn: '${currentStepsCode === "SECOND"}',
        api: 'https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?waitSeconds=1',
        footerToolbar: [
          {
            type: 'pagination',
            maxButtons: 5,
            layout: 'total,pager,perPage',
          },
        ],
        columns: [
          {
            name: 'id',
            label: 'ID',
          },
          {
            name: 'engine',
            label: 'Engine',
          },
          {
            name: 'version',
            label: 'Engine Version',
          },
          {
            type: 'operation',
            label: '操作',
            width: 80,
            buttons: [
              {
                label: '审核',
                type: 'button',
                level: 'link',
              },
            ],
          },
        ],
      },
      {
        type: 'crud',
        className: 'flex-grow',
        columnsTogglable: false,
        visibleOn: '${currentStepsCode === "THIRD"}',
        api: 'https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?waitSeconds=1',
        footerToolbar: [
          {
            type: 'pagination',
            maxButtons: 5,
            layout: 'total,pager,perPage',
          },
        ],
        columns: [
          {
            name: 'id',
            label: 'ID',
          },
          {
            name: 'engine',
            label: 'Rendering engine',
          },
          {
            name: 'browser',
            label: 'Browser',
          },
          {
            name: 'platform',
            label: 'Platform(s)',
          },
          {
            name: 'engine',
            label: 'Engine',
          },
        ],
      },
      {
        type: 'crud',
        className: 'flex-grow',
        columnsTogglable: false,
        visibleOn: '${currentStepsCode === "FORTH"}',
        api: 'https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?waitSeconds=1',
        footerToolbar: [
          {
            type: 'pagination',
            maxButtons: 5,
            layout: 'total,pager,perPage',
          },
        ],
        columns: [
          {
            name: 'id',
            label: 'ID',
          },
          {
            name: 'engine',
            label: 'Rendering engine',
          },
          {
            name: 'browser',
            label: 'Browser',
          },
          {
            type: 'operation',
            label: '操作',
            width: 80,
            buttons: [
              {
                label: '上线',
                type: 'button',
                level: 'link',
              },
            ],
          },
        ],
      },
    ],
  },
}
```

## 代码分析

- **动态渲染**  通过接口返回的数据组装成steps组件，然后通过点击事件更新page数据域的currentStepsCode，用于显隐对应的crud，同时更新stepsIndex，用于更新steps组件。
- **注意事项**  为了避免crud直接数据的相互干扰，这里一定要每个步骤显示对应的crud，所以每个步骤都单独定义crud组件，通过visibleOn属性控制是否显示。


参考文档

1. [Steps 步骤条](/dataseeddesigndocui/#/amis/zh-CN/components/steps)
