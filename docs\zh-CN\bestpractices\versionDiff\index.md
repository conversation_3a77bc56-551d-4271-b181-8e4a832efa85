---
title: 版本对比
description: 郭启缘
type: 0
group: ⚙ 最佳实践
menuName: 版本对比
icon:
order: 9
standardMode: true,
---

<div><font color=#978f8f size=1>贡献者：郭启缘</font> <font color=#978f8f size=1>贡献时间: 2024/12/17</font></div>

## 功能描述

版本对比功能，凸显对比值的前后差异，需要转换后端数据、根据场景展示不同的样式，是一个比较繁琐的场景

## 实际场景
1. 场景链接：[获客平台（主营）/RTA服务/广点通/RTA策略/详情/版本比对](http://moka.dmz.sit.caijj.net/tdrtaui/#/common-RTA/version-diff/GDT?type=add&strategyId=3317&strategyCode=RTA20241203112608714&strategyName=123)

2. 复现步骤：
    - 点击上述链接
    - 选择基准版本、对比版本
    - 点击查询按钮

![拦截提示图片](https://static02.sit.yxmarketing01.com/tdmaterial/8cd6f746c5c6479e8d8dce659744867d.png)


## 实践代码

### 第一步：转换后端数据

```js
// 原始数据
[{
  字段: {
    前值: '',
    后值: ''
  }
}]
// eg
[{
  branchName: {
    source: '策略1',
    target: '策略2'
  }
}]
// 转换为 =>
[{
  branchName: {
    source: '策略1',
    target: '策略2',
    status: 'edit'
  }
}]

// 数据转换函数
const transformData = (list) => {
  return list.map(data => {
    const res = {}
    Object.keys(data).forEach(key => {
      const { source, target } = data[key]
      if (source === target) {
        // 前后值相等，status 为 same
        res[key] = {
          source,
          target: '',
          status: 'same'
        }
      } else if (!source && target) {
        // 前值为假，后值为真，status 为 add
        res[key] = {
          source: '',
          target,
          status: 'add'
        }
      } else if (source && !target) {
        // 前值为真，后值为假，status 为 delete
        res[key] = {
          source: '',
          // source 赋值给 target，方便后续展示
          target: source,
          status: 'delete'
        }
      } else if (source !== target) {
        // 前后值不相等，status 为 edit
        res[key] = {
          source,
          target,
          status: 'edit'
        }
      }
      // 如果有子节点，子节点也做相同转换
      if (data.children) {
        res.children = transformData(data.children)
      }
    })
    return res
  })
}
```

### 第二步：使用自定义列渲染

```js
/** 
 * 生成版本对比表格的统一列配置
 * status 值 add|edit|delete|same
 */
const getUpdateTextColumn = (config) => {
  const getFieldProp = (prop) => `${config.name}.${prop}`;
  return {
    type: 'container',
    body: [{
      type: 'tpl',
      // 前后值没有变化时
      visibleOn: `\${${getFieldProp('status')} === 'same'}`,
      // 展示前值
      tpl: `\${${getFieldProp('source')} || "-"}`,
      // 当前值 不存在时，展示为灰色
      className: {
        'pm-text-muted': `\${!${getFieldProp('source')}}`,
      }
    },
    {
      type: 'tpl',
      // 编辑时展示 原值 + 箭头。isSingle为true是单条数据对比，无需该节点
      visibleOn: `\${${getFieldProp('status')} === 'edit' && ${!config.isSingle}}`,
      // 展示前值
      tpl: `\${${getFieldProp('source')}} &nbsp;&#8594;&nbsp;`,
    },
    {
      type: 'tpl',
      // 前后值发生变化
      visibleOn: `\${${getFieldProp('status')} !== 'same'}`,
      // 展示后值
      tpl: `\$${getFieldProp('target')}`,
      className: {
        // 添加
        'pm-text-success': `\${${getFieldProp('status')} === 'add'}`,
        // 编辑
        'pm-text-warning': `\${${getFieldProp('status')} === 'edit'}`,
        // 删除
        'pm-text-danger line-through': `\${${getFieldProp('status')} === 'delete'}`,
      }
    }]
  }
};
```


### 示例

```schema
const getUpdateTextColumn = (config) => {
  const getFieldProp = (prop) => `${config.name}.${prop}`;
  return {
    type: 'container',
    body: [{
      type: 'tpl',
      // 前后值没有变化时
      visibleOn: `\${${getFieldProp('status')} === 'same'}`,
      // 展示前值
      tpl: `\${${getFieldProp('source')} || "-"}`,
      // 当前值 不存在时，展示为灰色
      className: {
        'pm-text-muted': `\${!${getFieldProp('source')}}`,
      }
    },
    {
      type: 'tpl',
      // 编辑时展示 原值 + 箭头
      visibleOn: `\${${getFieldProp('status')} === 'edit' && ${!config.isSingle}}`,
      // 展示前值
      tpl: `\${${getFieldProp('source')}} &nbsp;&#8594;&nbsp;`,
    },
    {
      type: 'tpl',
      // 前后值发生变化
      visibleOn: `\${${getFieldProp('status')} !== 'same'}`,
      // 展示后值
      tpl: `\$${getFieldProp('target')}`,
      className: {
        // 添加
        'pm-text-success': `\${${getFieldProp('status')} === 'add'}`,
        // 编辑
        'pm-text-warning': `\${${getFieldProp('status')} === 'edit'}`,
        // 删除
        'pm-text-danger line-through': `\${${getFieldProp('status')} === 'delete'}`,
      }
    }]
  }
};


const transformData = (list) => {
  return list.map(data => {
    const res = {}
    Object.keys(data).forEach(key => {
      const { source, target } = data[key]
      if (source === target) {
        // 前后值相等，status 为 same
        res[key] = {
          source,
          target: '',
          status: 'same'
        }
      } else if (!source && target) {
        // 前值为假，后值为真，status 为 add
        res[key] = {
          source: '',
          target,
          status: 'add'
        }
      } else if (source && !target) {
        // 前值为真，后值为假，status 为 delete
        res[key] = {
          source: '',
          // source 赋值给 target，方便后续展示
          target: source,
          status: 'delete'
        }
      } else if (source !== target) {
        // 前后值不相等，status 为 edit
        res[key] = {
          source,
          target,
          status: 'edit'
        }
      }
      // 如果有子节点，子节点也做相同转换
      if (data.children) {
        res.children = transformData(data.children)
      }
    })
    return res
  })
}

return {
  "type": "page",
  "data": {
    "versionList": [
      {
        "label": "V1",
        "value": "1"
      },
      {
        "label": "V2",
        "value": "2"
      },
      {
        "label": "V3",
        "value": "3"
      }
    ],
  },
  "body": [
    {
      type: "title",
      title: "版本对比",
      iconConfig: true,
    },
    {
      type: 'form',
      queryForm: true,
      id: 'searchId',
      data: {
        changed: true,
      },
      target: 'diffGroupId',
      actions: [
        {
          type: 'reset',
          label: '重 置',
          onEvent: {
            click: {
              actions: [
                {
                  actionType: 'setValue',
                  componentId: 'diffFormId',
                  args: {
                    value: {
                      basicInfo: [],
                      contentInfo: [],
                    },
                  },
                },
              ],
            },
          },
        },
        {
          type: 'submit',
          label: '查 询',
          level: 'primary',
          disabledOn: '${OR(!sourceVersion, !targetVersion)}',
          disabledTip: {
            type: 'tooltip-wrapper',
            content: '基准版本、对比版本，必须同时选择才支持查询',
            tooltipTheme: 'dark',
          },
        },
      ],
      body: [
        {
          type: 'group',
          body: [
            {
              type: 'select',
              name: 'sourceVersion',
              label: '基准版本',
              columnRatio: 4,
              source: '${versionList}',
            },
            {
              type: 'select',
              name: 'targetVersion',
              label: '对比版本',
              columnRatio: 4,
              source: '${versionList}',
            },
          ],
        },
      ],
    },
    {
      type: 'service',
      id: 'diffGroupId',
      api: {
        method: 'post',
        url: 'https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4',
        data: {
          sourceVersion: '${sourceVersion}',
          targetVersion: '${targetVersion}',
        },
        adaptor: (payload, res) => {
          const info = [
            {
              fields: {
                source: '版本号',
                target: '版本号',
              },
              basicVersion: {
                source: 'V1',
                target: 'V1',
              },
              diffVersion: {
                source: 'V1',
                target: 'V2',
              }
            },
            {
              fields: {
                source: '版本描述',
                target: '版本描述',
              },
              basicVersion: {
                source: '描述',
                target: '描述',
              },
              diffVersion: {
                source: '',
                target: '描述',
              }
            },
            {
              fields: {
                source: '版本状态',
                target: '版本状态',
              },
              basicVersion: {
                source: '',
                target: '',
              },
              diffVersion: {
                source: '',
                target: '',
              }
            },
            {
              fields: {
                source: '更新人',
                target: '更新人',
              },
              basicVersion: {
                source: '王五',
                target: '王五',
              },
              diffVersion: {
                source: '王五',
                target: '张三',
              }
            },
            {
              fields: {
                source: '通知人',
                target: '通知人',
              },
              basicVersion: {
                source: '李四',
                target: '李四',
              },
              diffVersion: {
                source: '李四',
                target: '',
              }
            }
          ];

          const content = [
            {
              branchName: {
                source: '',
                target: '策略1',
                status: 'add'
              },
              branchId: {
                source: '',
                target: 'fdasfdsa',
                status: 'add'
              },
              bucketType: {
                source: '',
                target: '类型1',
                status: 'add'
              },
              bucketId: {
                source: '',
                target: 'fsdafdsa',
                status: 'add'
              },
              children: [
                {
                  strategyCode: {
                    source: '',
                    target: 'aaaa',
                    status: 'add'
                  },
                  stratgyOutput: {
                    source: '',
                    target: '输出1',
                    status: 'add'
                  },
                  strategyBid: {
                    source: '',
                    target: '100',
                    status: 'add'
                  },
                  condition: {
                    source: '',
                    target: '次数 > 1',
                    status: 'add'
                  }
                },
                {
                  strategyCode: {
                    source: '',
                    target: 'bbbb',
                    status: 'add'
                  },
                  stratgyOutput: {
                    source: '',
                    target: '输出2',
                    status: 'add'
                  },
                  strategyBid: {
                    source: '',
                    target: '200',
                    status: 'add'
                  },
                  condition: {
                    source: '',
                    target: '次数 > 2',
                    status: 'add'
                  }
                },
              ]
            },
            {
              branchName: {
                source: '策略2',
                target: '',
                status: 'delete'
              },
              branchId: {
                source: 'fdasfdsa',
                target: '',
                status: 'delete'
              },
              bucketType: {
                source: '类型2',
                target: '',
                status: 'delete'
              },
              bucketId: {
                source: 'fsdafdsa',
                target: '',
                status: 'delete'
              },
              children: [
                {
                  strategyCode: {
                    source: 'aaaa',
                    target: '',
                    status: 'delete'
                  },
                  stratgyOutput: {
                    source: '输出1',
                    target: '',
                    status: 'delete'
                  },
                  strategyBid: {
                    source: '100',
                    target: '',
                    status: 'delete'
                  },
                  condition: {
                    source: '次数 > 1',
                    target: '',
                    status: 'delete'
                  }
                },
                {
                  strategyCode: {
                    source: 'bbbb',
                    target: '',
                    status: 'delete'
                  },
                  stratgyOutput: {
                    source: '输出2',
                    target: '',
                    status: 'delete'
                  },
                  strategyBid: {
                    source: '200',
                    target: '',
                    status: 'delete'
                  },
                  condition: {
                    source: '次数 > 2',
                    target: '',
                    status: 'delete'
                  }
                }
              ]
            },
            {
              branchName: {
                source: '策略3',
                target: '策略4',
                status: 'edit'
              },
              branchId: {
                source: 'fdasfdsa',
                target: 'fdasfdsa',
                status: 'same'
              },
              bucketType: {
                source: '类型3',
                target: '',
                status: 'delete'
              },
              bucketId: {
                source: '',
                target: 'fsdafdsa',
                status: 'add'
              },
              children: [
                {
                  strategyCode: {
                    source: 'aaaa',
                    target: 'cccc',
                    status: 'edit'
                  },
                  stratgyOutput: {
                    source: '输出1',
                    target: '',
                    status: 'delete'
                  },
                  strategyBid: {
                    source: '',
                    target: '100',
                    status: 'add'
                  },
                  condition: {
                    source: '次数 > 1',
                    target: '次数 > 1',
                    status: 'same'
                  }
                },
                {
                  strategyCode: {
                    source: 'bbbb',
                    target: 'cccc',
                    status: 'edit'
                  },
                  stratgyOutput: {
                    source: '输出2',
                    target: '',
                    status: 'delete'
                  },
                  strategyBid: {
                    source: '',
                    target: '200',
                    status: 'add'
                  },
                  condition: {
                    source: '次数 > 2',
                    target: '次数 > 2',
                    status: 'same'
                  }
                },
              ]
            },
            {
              branchName: {
                source: '策略5',
                target: '策略5',
                status: 'same'
              },
              branchId: {
                source: 'fdasfdsa',
                target: 'fdasfdsa',
                status: 'same'
              },
              bucketType: {
                source: '类型5',
                target: '类型5',
                status: 'same'
              },
              bucketId: {
                source: '',
                target: '',
                status: 'same'
              },
              children: [
                {
                  strategyCode: {
                    source: 'aaaa',
                    target: 'aaaa',
                    status: 'same'
                  },
                  stratgyOutput: {
                    source: '输出1',
                    target: '输出1',
                    status: 'same'
                  },
                  strategyBid: {
                    source: '100',
                    target: '100',
                    status: 'same'
                  },
                  condition: {
                    source: '次数 > 1',
                    target: '次数 > 1',
                    status: 'same'
                  }
                },
                {
                  strategyCode: {
                    source: 'bbbb',
                    target: 'bbbb',
                    status: 'same'
                  },
                  stratgyOutput: {
                    source: '输出2',
                    target: '输出2',
                    status: 'same'
                  },
                  strategyBid: {
                    source: '200',
                    target: '200',
                    status: 'same'
                  },
                  condition: {
                    source: '',
                    target: '',
                    status: 'same'
                  }
                }
              ]
            }
          ];
          return {
            basicInfo: transformData(info),
            contentInfo: transformData(content)
          };
        },
      },
      body: {
        type: 'group-container',
        collapsible: false,
        items: [
          {
            header: {
              title: '基本信息对比',
            },
            body: [
              {
                type: 'table',
                source: '${basicInfo}',
                columns: [
                  {
                    name: 'fields',
                    label: '字段名',
                    width: '20%',
                    type: 'tpl',
                    tpl: '${fields.source || "-"}',
                    className: {
                      'pm-text-muted': '${!fields.source}',
                    }
                  },
                  {
                    name: 'basicVersion',
                    label: '基准版本',
                    width: '40%',
                    type: 'tpl',
                    tpl: '${basicVersion.source || "-"}',
                    className: {
                      'pm-text-muted': '${!basicVersion.source}',
                    }
                  },
                  {
                    name: 'diffVersion',
                    label: '对比版本',
                    width: '40%',
                    type: 'group',
                    direction: 'vertical',
                    ...getUpdateTextColumn({ name: 'diffVersion', isSingle: true })
                  },
                ],
              },
            ],
          },
          {
            header: {
              title: '内容比对',
            },
            body: [
              {
                type: 'flex',
                justify: 'flex-start',
                alignItems: 'center',
                className: 'mb-4',
                gap: true,
                items: [
                  {
                    type: 'tag',
                    label: '新增',
                    displayMode: 'bordered',
                    color: 'success',
                  },
                  {
                    type: 'tag',
                    label: '修改',
                    displayMode: 'bordered',
                    color: 'running',
                  },
                  {
                    type: 'tag',
                    label: '删除',
                    className: 'line-through',
                    displayMode: 'bordered',
                    color: 'error',
                  },
                ],
              },
              {
                type: 'table',
                source: '${contentInfo}',
                id: 'contentDiffId',
                columns: [
                  {
                    name: 'branchName',
                    label: '分支名称',
                    ...getUpdateTextColumn({ name: 'branchName' })
                  },
                  {
                    name: 'branchId',
                    label: '分支ID',
                    ...getUpdateTextColumn({ name: 'branchId' })
                  },
                  {
                    name: 'bucketType',
                    label: '实验分桶类型',
                    ...getUpdateTextColumn({ name: 'bucketType' })
                  },
                  {
                    name: 'bucketId',
                    label: '实验分桶ID',
                    ...getUpdateTextColumn({ name: 'bucketId' })
                  }
                ],
                subTable: {
                  type: 'table',
                  source: '${children}',
                  columns: [
                    {
                      name: 'strategyCode',
                      label: '策略编码',
                      ...getUpdateTextColumn({ name: 'strategyCode' })
                    },
                    {
                      name: 'stratgyOutput',
                      label: '输出e结果',
                      ...getUpdateTextColumn({ name: 'stratgyOutput' })
                    },
                    {
                      name: 'strategyBid',
                      label: '出价',
                      ...getUpdateTextColumn({ name: 'strategyBid' })
                    },
                    {
                      name: 'condition',
                      label: '条件',
                      ...getUpdateTextColumn({ name: 'condition' })
                    },
                  ],
                },
              },
            ],
          },
        ],
      },
    }
  ]
}
```

## 代码分析

1. form 表单做搜索区域。查询按钮做限制，如果用户不选择必备的字段则置灰
2. 版本对比有两个 table，套一层 service（获取数据） + group-container（分组）
3. 使用 transformData 函数转换数据为统一格式，再用 getUpdateTextColumn 函数生成自定义列，展示版本对比的样式
