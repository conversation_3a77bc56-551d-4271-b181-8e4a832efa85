---
title: Wizard 单步Form 间联动
description: 吴姚芳
type: 0
group: ⚙ 最佳实践
menuName: Wizard 单步Form 间联动
icon:
standardMode: true
order: 24
---

<div><font color=#978f8f size=1>贡献者：吴姚芳</font> <font color=#978f8f size=1>贡献时间: 2024/12/05</font></div>

## 功能描述

`wizard`向导组件中的每一步其实就是一个类`form`组件， `form`组件支持的属性配置在单步中都可以设置。比如分步校验，分步接口调用以及单独的事件监听。

## 实际场景1
1. 场景链接：[创建工具API](https://moka.dmz.sit.caijj.net/aiassistantui/#/personalPage/toolsInfo?toolkit_id=2&tool_base_id=1)
2. 操作步骤：
      - 点击基本信息中的【保存并继续】按钮会进行当前步骤表单的校验和接口调用；
      - 点击配置API参数中的【保存并继续】按钮会进行当前步骤表单的校验和接口调用；
      - 点击调试与校验中的【完成】按钮会进行当前步骤表单的校验和接口调用；
  
![页面展示](/dataseeddesigndocui/public/assets/wizardStepsValidate/1.png "页面展示")    


### 实践代码
向导中的每一步都需要校验，校验成功进行接口请求,成功之后跳转至下一步。


```js
{
  ....
  type: "wizard", 
  steps: [
    {
      // 向导组件单步中的配置项
      ...,
       "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4",
      //  动作配置
       "actions": [
        // 点击取消，返回上一步
         {
           "type": "button",
           "label": "取消",
           "onEvent": {
             "click": {
               "actions": [
                 {
                   "actionType": "goBack"
                 }
               ]
             }
           }
         },
        //  点击保存并继续，进行表单校验，校验成功进行接口请求，请求成功之后进入下一步
         {
           "type": "button",
           "label": "保存并继续",
           "level": "primary",
           "onEvent": {
             "click": {
               "actions": [
                 {
                   "actionType": "step-submit",
                   "componentId": "form-wizard-id"
                 }
               ]
             }
           }
         }
       ],
    },
    ...,
     {
      // 第三步配置
      ...,
       "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4",
        "actions": [
          // 点击上一步，返回上一步
            {
              "type": "button",
              "label": "上一步",
              "onEvent": {
                "click": {
                  "actions": [
                    {
                      "actionType": "prev",
                      "componentId": "form-wizard-id"
                    }
                  ]
                }
              }
            },
            //  点击完成，进行当前步骤的校验，校验成功进行接口请求
            {
              "type": "button",
              "label": "完成",
              "level": "primary",
              "onEvent": {
                "click": {
                  "actions": [
                    {
                      "actionType": "step-submit",
                      "componentId": "form-wizard-id"
                    }
                  ]
                }
              }
            }
          ],
    }
  ]
}
```



```schema
{
  "type": "page",
  "body": [
    {
      "type": "title",
      "title": "页面大标题名称大标题名称",
      "subtitle": "我是小标题",
      "iconConfig": {
        "icon": "chevron-left"
      }
    },
    {
      "type": "wrapper",
      "size": 0,
      "bgColor": "white",
      "body": {
        "type": "wizard",
        "id": "form-wizard-id",
        "steps": [
          {
            "title": "1.调试与校验",
            "mode": "horizontal",
            "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4",
            "actions": [
              {
                "type": "button",
                "label": "取消",
                "onEvent": {
                  "click": {
                    "actions": [
                      {
                        "actionType": "goBack"
                      }
                    ]
                  }
                }
              },
              {
                "type": "button",
                "label": "保存并继续",
                "level": "primary",
                "onEvent": {
                  "click": {
                    "actions": [
                      {
                        "actionType": "step-submit",
                        "componentId": "form-wizard-id"
                      }
                    ]
                  }
                }
              }
            ],
            "body": [
              {
                "type": "group",
                "body": [
                  {
                    "type": "input-text",
                    "name": "department",
                    "label": "归属部门"
                  },
                  {
                    "type": "input-text",
                    "name": "platform",
                    "label": "Platform",
                    "placeholder": "请输入"
                  },
                  {
                    "type": "input-text",
                    "name": "css",
                    "label": "CSS",
                    "required": true,
                    "placeholder": "请输入"
                  }
                ]
              }
            ]
          },
          {
            "title": "2.配置API参数",
            "mode": "horizontal",
            "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4",
            "body": [
              {
                "type": "group",
                "body": [
                  {
                    "type": "input-text",
                    "name": "department22",
                    "label": "Browser"
                  },
                  {
                    "type": "input-text",
                    "name": "platform22",
                    "label": "标签",
                    "placeholder": "请输入"
                  },
                  {
                    "type": "input-text",
                    "name": "css22",
                    "label": "部门",
                    "required": true,
                    "placeholder": "请输入"
                  }
                ]
              }
            ],
            "actions": [
              {
                "type": "button",
                "label": "上一步",
                "onEvent": {
                  "click": {
                    "actions": [
                      {
                        "actionType": "prev",
                        "componentId": "form-wizard-id"
                      }
                    ]
                  }
                }
              },
              {
                "type": "button",
                "label": "保存并继续",
                "level": "primary",
                "onEvent": {
                  "click": {
                    "actions": [
                      {
                        "actionType": "step-submit",
                        "componentId": "form-wizard-id"
                      }
                    ]
                  }
                }
              }
            ]
          },
          {
            "title": "3.调试与校验",
            "mode": "horizontal",
            "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4",
            "actions": [
              {
                "type": "button",
                "label": "上一步",
                "onEvent": {
                  "click": {
                    "actions": [
                      {
                        "actionType": "prev",
                        "componentId": "form-wizard-id"
                      }
                    ]
                  }
                }
              },
              {
                "type": "button",
                "label": "完成",
                "level": "primary",
                "onEvent": {
                  "click": {
                    "actions": [
                      {
                        "actionType": "step-submit",
                        "componentId": "form-wizard-id"
                      }
                    ]
                  }
                }
              }
            ],
            "body": [
              {
                "type": "group",
                "body": [
                  {
                    "type": "input-text",
                    "name": "department33",
                    "label": "方案名称"
                  },
                  {
                    "type": "input-text",
                    "name": "platform33",
                    "label": "规则名称",
                    "placeholder": "请输入"
                  },
                  {
                    "type": "input-text",
                    "name": "css33",
                    "label": "邮箱",
                    "required": true,
                    "placeholder": "请输入"
                  }
                ]
              }
            ]
          }
        ]
      }
    }
  ]
}
```

### 代码分析

 
 1.配置每步自己的actions，可以自定义按钮的动作事件；点击触发向导组件的step-submit动作进行表单的校验和请求的发送。
 

## 实际场景2
1. 场景链接：[大数据一站式/数据目录/全链路血缘/血缘分析](http://moka.dmz.sit.caijj.net/analytoolui/#/blood-line-analysis?_shMenuId=tenant_menu_P0280_blood_analysis_m3mxxgji)
2. 操作步骤：
     - 在步骤一中输入配置项，点击【下一步】；
     - 在步骤二中输入配置项，再点击【上一步】返回步骤一;
     - 当步骤一中输入项发生改变，第二步中的配置恢复默认值；
  
![页面展示](/dataseeddesigndocui/public/assets/wizardStepsValidate/2.png "页面展示")  

### 实践代码

```js
  {
  ...
  "body": [
      ...,
      "body": {
        "type": "wizard",
        "id": "form-id",
        "steps": [
          {
            "title": "第一步",
            "mode": "horizontal",
            "body": [...],
            // 当前配置项变化时，第二步中的配置恢复初始默认值
            "onEvent": {
              "change": {
                "actions": [
                  {
                    "actionType": "setValue",
                    "componentId": "form-id",
                    "args": {
                      "value": {
                        "name": "默认值",
                        "sex": ""
                      }
                    }
                  }
                ]
              }
            }
          },
          {
            "title": "第二步",
            "mode": "horizontal",
            "body": [
              {
                "type": "group",
                "body": [
                  {
                    "type": "input-text",
                    "name": "name",
                    "label": "姓名"
                  },
                  {
                    "type": "input-text",
                    "name": "sex",
                    "label": "性别",
                    "placeholder": "请输入"
                  },
                ]
              },
            ],
          },
          {
            "title": "第三步",
            "mode": "horizontal",
            "body": []
          }
        ]
      },
    }
  ]
}

```


```schema
{
  "type": "page",
  "body": [
    {
      "type": "title",
      "title": "页面大标题名称大标题名称",
      "subtitle": "我是小标题",
      "iconConfig": {
        "icon": "chevron-left"
      }
    },
    {
      "type": "wrapper",
      "size": 0,
      "bgColor": "white",
      "body": {
        "type": "wizard",
        "id": "form-id",
        "steps": [
          {
            "title": "第一步",
            "mode": "horizontal",
            "body": [
              {
                "type": "group",
                "body": [
                  {
                    "type": "input-text",
                    "name": "department2",
                    "label": "归属部门"
                  },
                  {
                    "type": "input-text",
                    "name": "platform2",
                    "label": "Platform",
                    "placeholder": "请输入"
                  },
                  {
                    "type": "input-text",
                    "name": "css",
                    "label": "CSS",
                    "required": true,
                    "placeholder": "请输入"
                  }
                ]
              },
            ],
            "onEvent": {
              "change": {
                "actions": [
                  {
                    "actionType": "setValue",
                    "componentId": "form-id",
                    "args": {
                      "value": {
                        "name": "默认值",
                        "sex": ""
                      }
                    }
                  }
                ]
              }
            }
          },
          {
            "title": "第二步",
            "mode": "horizontal",
            "body": [
              {
                "type": "group",
                "body": [
                  {
                    "type": "input-text",
                    "name": "name",
                    "label": "姓名"
                  },
                  {
                    "type": "input-text",
                    "name": "sex",
                    "label": "性别",
                    "placeholder": "请输入"
                  },
                ]
              },
            ],
          },
          {
            "title": "第三步",
            "mode": "horizontal",
            "body": [
              {
                "type": "group",
                "body": [
                  {
                    "type": "input-text",
                    "name": "department3",
                    "label": "数据来源"
                  },
                  {
                    "type": "input-text",
                    "name": "platform3",
                    "label": "邮箱",
                    "placeholder": "请输入"
                  },
                ]
              }
            ]
          }
        ]
      },
    }
  ]
}
```

### 代码分析
1. 当步骤一中的表单项有修改时，会触发`change`事件来设置步骤二中的表单项姓名和性别的值；

参考文档
1. [向导组件](/dataseeddesigndocui/#/amis/zh-CN/components/wizard)
