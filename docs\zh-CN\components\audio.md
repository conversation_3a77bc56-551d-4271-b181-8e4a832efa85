---
title: Audio 音频
description:
type: 0
group: ⚙ 组件
menuName: Audio 音频
icon:
order: 28
---

## 基本使用

```schema: scope="body"
{
    "type": "audio",
    "src": "https://amis.bj.bcebos.com/amis/2019-7/1562137295708/chicane-poppiholla-original-radio-edit%20(1).mp3"
}
```

## 通过接口获取

由于没有线上获取音频的接口所以没有在线示例，使用可参考如下代码，接口必须返回二进制文件流，目前不支持超大文件分片的场景

```js
{
    "type": "audio",
    "source": {
        "url": "/commercialopr/messagecenterconf/messagecore/aicall/query/audio/200", // 配置接口地址
        "method": "get",
        "responseType": "blob", // 返回类型必须配置为 blob
    }
}
```

## 属性表

| 属性名    | 类型      | 默认值                                           | 说明                                    |
| --------- | --------- | ------------------------------------------------ | --------------------------------------- |
| type      | `string`  | `"audio"`                                        | 指定为 audio 渲染器                     |
| className | `string`  |                                                  | 外层 Dom 的类名                         |
| inline    | `boolean` | true                                             | 是否是内联模式                          |
| src       | `string`  |                                                  | 音频地址                                |
| loop      | `boolean` | false                                            | 是否循环播放                            |
| autoPlay  | `boolean` | false                                            | 是否自动播放                            |
| rates     | `array`   | `[]`                                             | 可配置音频播放倍速如：`[1.0, 1.5, 2.0]` |
| controls  | `array`   | `['rates', 'play', 'time', 'process', 'volume']` | 内部模块定制化                          |
| source    | `Api`     |                                                  | 支持接口获取音频二进制文件，优先级比src高。`1.64.0`支持       |
