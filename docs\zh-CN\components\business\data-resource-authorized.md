---
title: DataResourceAuthorized 数据资源权限授权
description:
type: 0
group: ⚙ 组件
menuName: DataResourceAuthorized
icon:
order: 39
standardMode: true
---

DataResourceAuthorized 组件是和身份管理平台对接的组件，用于申请权限与授权，1.62.0版本支持。

由于组件内部会调用身份管理平台线上接口，故站点出现接口异常属于正常现象，此组件还未在线上环境测试，使用前请联系组件库人员。

## 基本用法

组件模式分为授权和申请两种，通过 `modalType` 属性来控制

```schema
{
  "type": "page",
  "data": {
    "dataResourceTypeList": [
      {
        "dataResourceTypeCode": "drt_product",
        "dataResourceTypeName": "数字身份开发测试权限",
        "dataResourceOuterCode": "P0068",
        "dataResourceOuterName": "数字身份开发测试产品",
        "dataResourceOperatingList": [
          {
            "code": "dot_owner",
            "name": "拥有者",
            "key": 0
          },
          {
            "code": "dot_readwrite",
            "name": "读写",
            "key": 1
          },
          {
            "code": "dot_readonly",
            "name": "只读",
            "key": 2
          }
        ]
      }
    ],
    "customOrgList": [
      {
        "name": "自定义",
        "corporationIdOrOrgId": "dsafdsa-dsfdsa"
      }
    ],
    "customUserList": [
      {
        "name": "自定义",
        "userId": "dsafdsa-dsfdsa"
      }
    ]
  },
  "body": [
    {
      "type": "button-toolbar",
      "buttons": [{
        "type": "button",
        "label": "授权",
        "actionType": "dialog",
        "showCloseButton": false,
        "dialog": {
          "title": "授权管理",
          "size": "md",
          "actions": [],
          "body": [
            {
              "type": "data-resource-authorized",
              "id": "authorized",
              "modalType": "AUTHORIZED",
              "dataResourceTypeList": "${dataResourceTypeList}"
            }
          ]
        }
      },
      {
        "type": "button",
        "label": "申请权限",
        "actionType": "dialog",
        "dialog": {
          "title": "申请权限",
          "size": "md",
          "body": [
            {
              "type": "data-resource-authorized",
              "modalType": "APPLIED",
              "dataResourceTypeList": "${dataResourceTypeList}"
            }
          ]
        }
      }]
    }
  ]
}
```

组件内部表单字段说明

|字段|说明|
| -- | -- |
|dataResourceTypeCode|资源类型code|
|dataResourceOuterCode|资源名称code|
|dataResourceOuterName|资源名称|
|resourceOperatingMetaCode|操作类型|
|subjectType|授权类型|
|userValue|授权给账号|
|orgValue|授权给组织|
|expiredTimeType|授权到期日类型|
|expiredTimeValue|固定日期的授权到期日期|
|description|备注|

故需要回填数据的时候可以将上述字段添加默认值

```schema
{
  "type": "page",
  "data": {
    "dataResourceTypeList": [
      {
        "dataResourceTypeCode": "drt_product",
        "dataResourceTypeName": "数字身份开发测试权限",
        "dataResourceOuterCode": "P0068",
        "dataResourceOuterName": "数字身份开发测试产品",
        "dataResourceOperatingList": [
          {
            "code": "dot_owner",
            "name": "拥有者",
            "key": 0
          },
          {
            "code": "dot_readwrite",
            "name": "读写",
            "key": 1
          },
          {
            "code": "dot_readonly",
            "name": "只读",
            "key": 2
          }
        ]
      }
    ]
  },
  "body": [
    {
      "type": "button",
      "label": "申请权限",
      "actionType": "dialog",
      "dialog": {
        "title": "申请权限",
        "size": "md",
        "showCloseButton": false,
        "data": {
          "dataResourceTypeCode": "drt_product",
          "dataResourceOuterName": "数字身份开发测试产品",
          "resourceOperatingMetaCode": "dot_owner",
          "expiredTimeType": "fixedDate",
          "dataResourceTypeList": "${dataResourceTypeList}"
        },
        "body": [
          {
            "type": "data-resource-authorized",
            "id": "applied",
            "modalType": "APPLIED",
            "dataResourceTypeList": "${dataResourceTypeList}"
          }
        ]
      }
    }
  ]
}
```

## 属性表

|属性名|类型|默认值|说明|
| --- | - | --- | -- |
|modalType|`"AUTHORIZED"\|"APPLIED"`|`"APPLIED"`| 弹窗类型：`AUTHORIZED`：授权弹窗，`APPLIED`：申请权限弹窗 |
|dataResourceTypeList|`DataResourceType[] \| expression`| | 资源类型下拉选项，支持表达式 |
|sensitive| `"对外公开" \| "对内公开" \| "敏感" \| "机密" \| "需信安审批"` | `"对内公开"` | 数据敏感等级，视图无关，但会将此字段提交给服务端    <br/> 1. 对内公开、对外公开：发起人、数据权限Owner审批 <br/> 2. 敏感、机密：发起人、发起人上级、数据权限Owner审批 <br/> 3. 需信安审批：发起人、发起人上级、信安、数据权限Owner审批 |
|customDataResouceType| `{ label: string, disabled: boolean }` | | 自定义资源类型的label，与禁用状态 |
|customDataResouceName| `{ label: string }` | | 自定义资源名称的label |
|customSubjectType| `{ disabled: boolean }` | | 授权类型的禁用状态 |
|customOrgValue| `{ disabled: boolean }	` | | 自定义 组织列表数据，以及组织输入项禁用状态，默认会从`/idaas/v2/users`接口获取数据 |
|customUserValue| `{ disabled: boolean }` | | 自定义 用户账号列表，以及授权给账号 选项禁用状态， 默认会从`/idaas/orgs/tree`接口获取数据 |
|customExpiredTimeType| `{ disabled: boolean }` | | 授权到期日 选项禁用状态 |
|customExpiredTimeValue| `{ disabled: boolean }` | | 授权到期日输入框禁用状态 |
|customTableHeader| `{ dataResouceTypeLabel: string, operationTypeLabel: string }` | | 授权管理，自定义表头: dataResouceTypeLabel - 资源类型列的表头，operationTypeLabel - 授权操作列的表头 |
|appliedApi| `Api` | `/idaasopr/data-authorization/opt/apply` | 申请权限接口 |
|authorizationListApi| `Api` | `/idaasopr/data-authorization/opt/query` | 授权列表接口 |
|userApi| `Api` | `/idaas/v2/users` | 获取用户列表接口 |
|orgApi| `Api` | `/idaas/orgs/tree` | 获取组织列表 |
|deleteAuthApi| `Api` | `/idaasopr/data-authorization/:dataAuthorizationId` | 移除权限接口 |
|addAuthApi| `Api` | `/idaasopr/data-authorization` | 授权接口 |
|authDetailApi| `Api` | `/idaasopr/data-resource-type/:dataResourceTypeCode` | 选择资源类型详情接口，用户获取资源类型对应的操作类型列表 |
|updateAuthApi| `Api` | `/idaasopr/data-authorization/:dataAuthorizationId` | 修改权限接口，用于修改授权到期日 |

DataResourceType 类型说明

|字段|类型|说明|
| -- | -- | -- |
|dataResourceTypeCode| `string` | 资源类型code |
|dataResourceTypeName| `string` | 资源类型名称 |
|dataResourceOuterCode| `string` | 资源code |
|dataResourceOuterName| `string` | 资源名称 |
|dataResourceOperatingList| `Operating[]` | 操作类型列表 |

Operating 类型说明

|字段|类型|说明|
| -- | -- | -- |
|code| `string` | 操作类型code |
|name| `string` | 操作类型名称 |


## /idaas/orgs/tree和/idaas/v2/users接口404，怎么处理？

`/idaas/orgs/tree`和`/idaas/v2/users`接口无需opr转发，可参考如下配置代理：

```javascript
 proxy: {
    '/idaas': {
        target: 'http://moka.dmz.dev.caijj.net',
        changeOrigin: true,
    }
 },
```