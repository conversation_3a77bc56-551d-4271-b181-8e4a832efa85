---
title: DepartmentSelect 组件
description:
type: 0
group: ⚙ 组件
menuName: DepartmentSelect 组件
icon:
---

基于 TreeSelect 进行封装，用于使用归属部门的场景

## 基本用法

```schema
{
  type: "page",
  body: [
    {
        "type":"form",
        "api":"/api/mock2/form/saveForm",
        "debug":true,
        "body":[
            {
                "type":"department-select",
                "name":"department-select",
                "id":"department-select",
                "data":{
                    "ds":true
                },
                "source":{
                    "url": '/api/mock2/business/department-select',
                    "data":{
                        "cptName":"department-select"
                    }
                },
            }
        ]
    }
  ]
}
```

## 属性表

DepartMentSelect 包含 TreeSelect 组件的所有属性，下面仅显示调整的属性，其他属性用法 参考 [TreeSelect 属性表](/dataseeddesigndocui/#/amis/zh-CN/components/form/treeselect#属性表)

| 属性名      | 类型        | 默认                                                                         | 说明                                                                                                                                                       |
| ----------- | ----------- | ---------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------- |
| source      | `ApiObject` | {<br/>&nbsp;&nbsp;url: "/infraopr/orgs",<br/>&nbsp;&nbsp;method: "get"<br/>} | `url`、`method` 已被内置，mock 场景下可覆盖，其他属性不受影响。<br/>url 默认是生产环境的 api，如果配置以`/api/`开头，会自动识别成 mock api，接受用户配置。 |
| multiple    | `boolean`   | `true`                                                                       | 设置默认值，可被覆盖                                                                                                                                       |
| searchable  | `boolean`   | `true`                                                                       | 设置默认值，可被覆盖                                                                                                                                       |
| maxTagCount | `numer`     | `2`                                                                          | 设置默认值，可被覆盖                                                                                                                                       |
