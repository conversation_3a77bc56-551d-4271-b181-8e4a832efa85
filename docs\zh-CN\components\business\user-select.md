---
title: UserSelect 组件
description:
type: 0
group: ⚙ 组件
menuName: UserSelect 组件
icon:
standardMode: true
---

基于 Select 组件进行封装，用于使用用户选择的场景

## 基本用法

```schema
{
  type: "page",
  body: [
    {
      "type":"form",
      "api":"/api/mock2/form/saveForm",
      "debug":true,
      "body":[
          {
              "type":"user-select",
              "name":"user-select",
              "id":"user-select",
              "label": "选择用户",
              "data":{
                  "us":true
              },
              "source":{
                  "url": '/api/mock2/business/user-select',
                  "data":{
                      "cptName":"user-select"
                  }
              },
          }
      ]
    }
  ]
}
```

## 属性表

UserSelect 包含 Select 组件的所有属性，下面仅显示调整的属性，其他属性用法 参考 [Select 属性表](/dataseeddesigndocui/#/amis/zh-CN/components/form/select#属性表)

| 属性名      | 类型        | 默认值                                                                            | 说明                                                                                                                                                       |
| ----------- | ----------- | --------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------- |
| source      | `ApiObject` | {<br/>&nbsp;&nbsp;url: "/infraopr/employees",<br/>&nbsp;&nbsp;method: "get"<br/>} | `url`、`method` 已被内置，mock 场景下可覆盖，其他属性不受影响。<br/>url 默认是生产环境的 api，如果配置以`/api/`开头，会自动识别成 mock api，接受用户配置。 |
| multiple    | `boolean`   | `true`                                                                            | 设置默认值，可被覆盖                                                                                                                                       |
| searchable  | `boolean`   | `true`                                                                            | 设置默认值，可被覆盖                                                                                                                                       |
| maxTagCount | `numer`     | `2`                                                                               | 设置默认值，可被覆盖                                                                                                                                       |
