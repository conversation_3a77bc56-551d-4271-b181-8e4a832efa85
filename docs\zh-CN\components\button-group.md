---
title: ButtonGroup 按钮组
description:
type: 0
group: ⚙ 组件
menuName: ButtonGroup
icon:
order: 30
---

用于将配置的多个按钮集合为一个整体，按钮之间无间距，按钮schema可自定义配置actionType、url等属性。

相关组件区分：
1. [Button-Group-Select 按钮点选](/dataseeddesigndocui/#/amis/zh-CN/components/form/button-group-select)：按钮集合当 select 点选用，按钮之间无间距，支持表单项属性及功能。通常作为ForItem使用，或InputTree等组件带筛选按钮的场景。

2. [Button-Toolbar 按钮工具栏](/dataseeddesigndocui/#/amis/zh-CN/components/form/button-toolbar)：多个按钮排列组合展示，按钮直接存在默认间距8px。通常用于Crud、GroupContainer等组件带全局操作按钮的场景。

## 场景推荐
### 基本使用

```schema
{
  "type": "page",
  "body": {
    "type": "button-group",
    "buttons": [
      {
        "type": "button",
        "label": "Button",
        "actionType": "dialog",
        "dialog": {
          "confirmMode": false,
          "title": "提示",
          "body": "对，你刚点击了！"
        }
      },
      {
        "type": "button",
        "actionType": "url",
        "url": "https://www.baidu.com",
        "blank": true,
        "label": "百度一下"
      },
      {
        "type": "button",
        "label": "普通按钮"
      }
    ]
  }
}
```

## 组件用法
### 垂直模式

默认水平展示，配置`"vertical": true`，实现垂直模式

```schema: scope="body"
[
  {
    "type": "button-group",
    "vertical": true,
    "buttons": [
      {
        "type": "button",
        "label": "按钮1"
      },
      {
        "type": "button",
        "label": "按钮2"
      },
      {
        "type": "button",
        "label": "按钮3"
      }
    ]
  }
]
```

### 平铺模式

默认行内展示，配置 `"tiled": true` 实现平铺模式，占满一行

```schema: scope="body"
[
  {
    "type": "button-group",
    "tiled": true,
    "buttons": [
      {
        "type": "button",
        "label": "按钮1"
      },
      {
        "type": "button",
        "label": "按钮2"
      },
      {
        "type": "button",
        "label": "按钮3"
      }
    ]
  }
]
```

### 按钮主题样式

配置 `btnLevel` 统一设置按钮主题样式，注意 `buttons ` 或 `options` 中的`level`属性优先级高于`btnLevel`。配置 `btnActiveLevel` 为按钮设置激活态时的主题样式。

```schema: scope="body"
[
  {
    "type": "button-group",
    "btnLevel": "light",
    "btnActiveLevel": "warning",
    "buttons": [
      {
        "type": "button",
        "label": "按钮1"
      },
      {
        "type": "button",
        "label": "按钮2"
      },
      {
        "type": "button",
        "label": "按钮3",
        "level": "primary"
      }
    ]
  }
]
```

### 属性表

| 属性名         | 类型                                                                                                                | 默认值           | 说明                       |
| -------------- | ------------------------------------------------------------------------------------------------------------------- | ---------------- | -------------------------- |
| type           | `string`                                                                                                            | `"button-group"` | 指定为 button-group 渲染器 |
| vertical       | `boolean`                                                                                                           | `false`          | 是否使用垂直模式           |
| tiled          | `boolean`                                                                                                           | `false`          | 是否使用平铺模式           |
| btnLevel       | `'link' \| 'primary' \| 'secondary' \| 'info'\|'success' \| 'warning' \| 'danger' \| 'light'\| 'dark' \| 'default'` | `"default"`      | 按钮样式                   |
| btnActiveLevel | `'link' \| 'primary' \| 'secondary' \| 'info'\|'success' \| 'warning' \| 'danger' \| 'light'\| 'dark' \| 'default'` | `"primary"`      | 选中按钮样式               |
| buttons        | `Array<Action>`                                                                                                     |                  | [按钮](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/action)           |
| className      | `string`                                                                                                            |                  | 外层 Dom 的类名            |
