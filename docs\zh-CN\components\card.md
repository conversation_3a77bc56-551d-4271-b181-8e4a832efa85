---
title: Card 卡片
description:
type: 0
group: ⚙ 组件
menuName: Card 卡片
icon:
order: 31
---

## 场景推荐

### 基本使用

```schema
{
  "type": "page",
  "body": {
    "type": "card",
    "header": {
      "title": "标题",
      "subTitle": "副标题",
      "description": "这是一段描述",
      "avatar": "data:image/svg+xml,%3C%3Fxml version='1.0' standalone='no'%3F%3E%3C!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3E%3Csvg t='1631083237695' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='2420' xmlns:xlink='http://www.w3.org/1999/xlink' width='1024' height='1024'%3E%3Cdefs%3E%3Cstyle type='text/css'%3E%3C/style%3E%3C/defs%3E%3Cpath d='M959.872 128c0.032 0.032 0.096 0.064 0.128 0.128v767.776c-0.032 0.032-0.064 0.096-0.128 0.128H64.096c-0.032-0.032-0.096-0.064-0.128-0.128V128.128c0.032-0.032 0.064-0.096 0.128-0.128h895.776zM960 64H64C28.8 64 0 92.8 0 128v768c0 35.2 28.8 64 64 64h896c35.2 0 64-28.8 64-64V128c0-35.2-28.8-64-64-64z' p-id='2421' fill='%23bfbfbf'%3E%3C/path%3E%3Cpath d='M832 288c0 53.024-42.976 96-96 96s-96-42.976-96-96 42.976-96 96-96 96 42.976 96 96zM896 832H128V704l224-384 256 320h64l224-192z' p-id='2422' fill='%23bfbfbf'%3E%3C/path%3E%3C/svg%3E"
    },
    "toolbar": [
      {
        "type": "button",
        "label": false,
        "icon": "fa fa-star-o",
        "linkWithoutPadding": true,
        "level": "link"
      }
    ],
    "body": [
      {
        "type": "tpl",
        "tpl": "Trident",
        "inline": false,
        "label": "Engine"
      },
      {
        "name": "static",
        "type": "static",
        "label": "Browser",
        "value": "Internet Explorer 5.0"
      }
    ]
  }
}
```

### 看板模式

```schema
{
  "type": "page",
  "data": {
    "title": "数仓表",
    "items": [
      {
        "num": 25
      },
      {
        "num": 30
      }
    ]
  },
  "body": {
    "type": "card",
    "mode": "board",
    "header": {
      "bgColor": "blue",
      "title": "${title}",
      "titleClassName": "pm-text-info"
    },
    "bodyClassName": "pm-text-info",
    "body": [
      {
        "type": "each",
        "name": "items",
        "mode": "horizontal",
        "alignItems": "center",
        "justify": "center",
        "gap": true,
        "items": [
          {
            "name": "num",
            "type": "tpl",
            "tpl": "${num|number}",
            "className": "text-3xl"
          },
          {
            "type": "tpl",
            "tpl": "|",
            "visibleOn": "${index < items.length - 1}"
          }
        ]
      }
    ]
  }
}
```

### 卡片+tabs组合

```schema
{
  "type": "page",
  "id": "page-header",
  "data": {
    "text1": "营销中心",
    "text2": 2,
    "text3": 1593327764,
    "text4": "负责人",
    "text5": "text5",
    "text6": 1593327764,
    "text7": "创建人",
    "text8": "text8",
    "text9": "text9",
    "itemLists": [
      {
        "title": "客户账",
        "isLike": false
      }
    ]
  },
  "body": [
    {
      "type": "title",
      "title": "页面大标题名称大标题名称Demo",
      "subTitle": "这是小标题",
      "iconConfig": true,
      "actions": [
        {
          "type": "button",
          "label": false,
          "icon": "fa fa-star-o",
          "level": "link",
          "linkWithoutPadding": true,
          "visibleOn": "${collection}",
          "onEvent": {
            "click": {
              "actions": [
                {
                  "actionType": "setValue",
                  "componentId": "page-header",
                  "args": {
                    "value": {
                      "collection": false
                    }
                  }
                }
              ]
            }
          },
        },
        {
          "type": "button",
          "label": false,
          "icon": "fa fa-star",
          "linkWithoutPadding": true,
          "level": "link",
          "visibleOn": "${!collection}",
          "onEvent": {
            "click": {
              "actions": [
                {
                  "actionType": "setValue",
                  "componentId": "page-header",
                  "args": {
                    "value": {
                      "collection": true
                    }
                  }
                }
              ]
            }
          }
        }
      ]
    },
    {
      "type": "wrapper",
      "bgColor": "white",
      "body": [
        {
          "type": "card",
          "header": {
            "title": "FLINK_TABLE_NAME",
            "subTitle": "副标题",
            "description": "这是一段描述",
            "avatar": "https://suda.cdn.bcebos.com/images/amis/ai-fake-face.jpg"
          }
        },
        {
          "type": "tabs",
          "tabs": [
            {
              "title": "基础表单1",
              "icon": "fa fa-home",
              "body": {
                "type": "form",
                "title": "",
                "mode": "horizontal",
                "actions": [],
                "initApi": "",
                "wrapWithPanel": false,
                "labelWidth": 60,
                "body": [
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "static",
                        "name": "text1",
                        "label": "归属部门",
                        "columnRatio": 4
                      },
                      {
                        "type": "static",
                        "name": "text2",
                        "label": "文本2",
                        "columnRatio": 4
                      },
                      {
                        "type": "static-date",
                        "name": "text3",
                        "label": "文本3",
                        "columnRatio": 4
                      }
                    ]
                  },
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "static",
                        "name": "text4",
                        "label": "负责人",
                        "columnRatio": 4
                      },
                      {
                        "type": "static",
                        "name": "text5",
                        "label": "文本5",
                        "columnRatio": 4
                      },
                      {
                        "type": "static-datetime",
                        "name": "text6",
                        "label": "文本6",
                        "columnRatio": 4
                      }
                    ]
                  },
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "static",
                        "name": "text7",
                        "label": "营销中心",
                        "columnRatio": 4
                      },
                      {
                        "type": "static",
                        "name": "text8",
                        "label": "文本8",
                        "columnRatio": 4
                      },
                      {
                        "type": "static",
                        "name": "text9",
                        "label": "文本9",
                        "columnRatio": 4
                      }
                    ]
                  }
                ]
              }
            }
          ]
        }
      ]
    }
  ]
}
```

### 卡片比例配置

```schema
{
  "type": "page",
  "data": {
    "title": "复合指标",
    "subTitle": "多个基础指标或派生指标的四则运算，如：人均交易额=交易金额/交易用户数",
    "avatar": "../../../examples/static/card-demo3.png"
  },
  "body": [
    {
      "type": "card",
      "header": {
        "title": "${title}",
        "subTitle": "${subTitle}"
      },
      "media": {
        "imageRatio": "4:3",
        "type": "image",
        "url": "${avatar}",
        "position": "left"
      },
      "itemAction": {
        "type": "button",
        "actionType": "url",
        "url": "/dataseeddesigndocui/#/amis/zh-CN/components/card",
        "blank": true
      }
    }
  ]
}
```

## 组件用法

### 方形和圆角型

可以通过配置`avatarShape`配置来设置头像的形状，默认是圆形的，设置为`rounded`可以设置为圆角型，设置为`square`可以设置为方形。

```schema
{
  "type": "page",
  "body": {
    "type": "flex",
    "gap": true,
    "items": [
      {
        "type": "card",
        "header": {
          "title": "这是标题",
          "subTitle": "副标题",
          "description": "描述",
          "avatar": "https://suda.cdn.bcebos.com/images/amis/ai-fake-face.jpg"
        }
      },
      {
        "type": "card",
        "header": {
          "title": "这是标题",
          "subTitle": "副标题",
          "description": "描述",
          "avatarShape": "rounded",
          "avatar": "https://suda.cdn.bcebos.com/images/amis/ai-fake-face.jpg"
        }
      },
      {
        "type": "card",
        "header": {
          "title": "这是标题",
          "subTitle": "副标题",
          "description": "描述",
          "avatarShape": "square",
          "avatar": "https://suda.cdn.bcebos.com/images/amis/ai-fake-face.jpg"
        }
      }
    ]
  }
}
```

### 打开链接

通过 `href` 属性可以设置点击卡片打开外部链接

```schema
{
  "type": "page",
  "body": {
    "type": "card",
    "href": "https://github.com/baidu/amis",
    "header": {
      "title": "标题",
      "subTitle": "副标题",
      "description": "这是一段描述",
      "avatarClassName": "pull-left thumb-md avatar b-3x m-r",
      "avatar": "data:image/svg+xml,%3C%3Fxml version='1.0' standalone='no'%3F%3E%3C!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3E%3Csvg t='1631083237695' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='2420' xmlns:xlink='http://www.w3.org/1999/xlink' width='1024' height='1024'%3E%3Cdefs%3E%3Cstyle type='text/css'%3E%3C/style%3E%3C/defs%3E%3Cpath d='M959.872 128c0.032 0.032 0.096 0.064 0.128 0.128v767.776c-0.032 0.032-0.064 0.096-0.128 0.128H64.096c-0.032-0.032-0.096-0.064-0.128-0.128V128.128c0.032-0.032 0.064-0.096 0.128-0.128h895.776zM960 64H64C28.8 64 0 92.8 0 128v768c0 35.2 28.8 64 64 64h896c35.2 0 64-28.8 64-64V128c0-35.2-28.8-64-64-64z' p-id='2421' fill='%23bfbfbf'%3E%3C/path%3E%3Cpath d='M832 288c0 53.024-42.976 96-96 96s-96-42.976-96-96 42.976-96 96-96 96 42.976 96 96zM896 832H128V704l224-384 256 320h64l224-192z' p-id='2422' fill='%23bfbfbf'%3E%3C/path%3E%3C/svg%3E"
    },
    "useCardLabel": false,
    "body": [
      {
        "type": "static",
        "tpl": "Trident",
        "inline": false,
        "label": "Engine",
        "mode": "horizontal"
      },
      {
        "name": "static",
        "type": "static",
        "label": "Browser",
        "value": "Internet Explorer 5.0",
        "mode": "horizontal"
      }
    ]
  }
}
```

### 设置头像文本

如果没有 avatar，还可以通过 `avatarText` 设置头像文本

```schema
{
  "type": "page",
  "body": {
    "type": "card",
    "href": "https://github.com/baidu/amis",
    "header": {
      "title": "标题",
      "subTitle": "副标题",
      "description": "这是一段描述",
      "avatarText": "AMIS"
    },
    
  }
}
```

可以设置文本背景色，它会根据数据分配一个颜色，主要配合 `cards` 使用

```schema
{
  "type": "page",
  "data": {
    "items": [
      {
        "engine": "Trident",
        "browser": "Internet Explorer 4.0"
      },
      {
        "engine": "Chrome",
        "browser": "Chrome 44"
      },
      {
        "engine": "Gecko",
        "browser": "Firefox 1.0"
      },
      {
        "engine": "Presto",
        "browser": "Opera 10"
      },
      {
        "engine": "Webkie",
        "browser": "Safari 12"
      }
    ]
  },
  "body": {
    "type": "cards",
    "source": "$items",
    columnsCount:2,
    "card": {
      "header": {
        "title": "标题",
        "subTitle": "副标题",
        "description": "这是一段描述",
        "avatarText": "${engine|substring:0:2|upperCase}",
        "avatarTextBackground": [
          "#FFB900",
          "#D83B01",
          "#B50E0E",
          "#E81123",
          "#B4009E",
          "#5C2D91",
          "#0078D7",
          "#00B4FF",
          "#008272"
        ]
      }
    }
  }
}
```

### 点击卡片的行为

通过设置 `itemAction` 可以设置整个卡片的点击行为

```schema
{
  "type": "page",
  "body": {
    "type": "card",
    "className": "hover:shadow",
    "itemAction": {
      "type": "button",
      "actionType": "dialog",
      "dialog": {
        "title": "详情",
        "body": "当前描述"
      }
    },
    "header": {
      "title": "标题",
      "subTitle": "副标题",
      "description": "这是一段描述",
      "avatarClassName": "pull-left thumb-md avatar b-3x m-r",
      "avatar": "data:image/svg+xml,%3C%3Fxml version='1.0' standalone='no'%3F%3E%3C!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3E%3Csvg t='1631083237695' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='2420' xmlns:xlink='http://www.w3.org/1999/xlink' width='1024' height='1024'%3E%3Cdefs%3E%3Cstyle type='text/css'%3E%3C/style%3E%3C/defs%3E%3Cpath d='M959.872 128c0.032 0.032 0.096 0.064 0.128 0.128v767.776c-0.032 0.032-0.064 0.096-0.128 0.128H64.096c-0.032-0.032-0.096-0.064-0.128-0.128V128.128c0.032-0.032 0.064-0.096 0.128-0.128h895.776zM960 64H64C28.8 64 0 92.8 0 128v768c0 35.2 28.8 64 64 64h896c35.2 0 64-28.8 64-64V128c0-35.2-28.8-64-64-64z' p-id='2421' fill='%23bfbfbf'%3E%3C/path%3E%3Cpath d='M832 288c0 53.024-42.976 96-96 96s-96-42.976-96-96 42.976-96 96-96 96 42.976 96 96zM896 832H128V704l224-384 256 320h64l224-192z' p-id='2422' fill='%23bfbfbf'%3E%3C/path%3E%3C/svg%3E"
    },
    "body": "这里是内容"
  }
}
```

注意它和前面的 `href` 配置冲突，如果设置了 `href` 这个将不会生效

<!-- ### 设置多媒体卡片

通过设置 `media` 可以设置为多媒体卡片, 通过 `mediaPosition` 可以设置多媒体位置

```schema
{
  "type": "page",
  "body": {
    "type": "card",
    "header": {
      "title": "标题"
    },
    "media": {
      "type": "image",
      "imageRatio": "4:3",
      "url": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
      "position": "left"
    },
    "body": "这里是内容",
    "secondary": "次要说明",
    "actions": [
      {
        "type": "button",
        "label": "操作",
        "actionType": "dialog",
        "dialog": {
          "title": "操作",
          "body": "你正在编辑该卡片"
        }
      },
      {
        "type": "button",
        "label": "操作",
        "actionType": "dialog",
        "dialog": {
          "title": "操作",
          "body": "你正在编辑该卡片"
        }
      },
      {
        "type": "dropdown-button",
        "level": "link",
        "icon": "fa fa-ellipsis-h",
        "hideCaret": true,
        "buttons": [
          {
            "type": "button",
            "label": "编辑",
            "actionType": "dialog",
            "dialog": {
              "title": "编辑",
              "body": "你正在编辑该卡片"
            }
          },
          {
            "type": "button",
            "label": "删除",
            "actionType": "dialog",
            "dialog": {
              "title": "提示",
              "body": "你删掉了该卡片"
            }
          }
        ]
      }
    ],
    "toolbar": [
      {
        "type": "tpl",
        "tpl": "标签",
      }
    ]
  }
}
``` -->

<!-- ### 设置标签卡片

```schema
{
  "type": "page",
  "body": {
    "type": "card",
    "header": {
      "title": "标题"
    },
    "body": "这里是内容这里是内容这里是内容这里是内容这里是内容这里是内容这里是内容这里是内容这里是内容这里是内容这里是内容这里是内容这里是内容",
    "secondary": "次要说明",
    "actions": [
      {
        "type": "button",
        "label": "操作",
        "actionType": "dialog",
        "className": "mr-4",
        "dialog": {
          "title": "操作",
          "body": "你正在编辑该卡片"
        }
      },
      {
        "type": "button",
        "label": "操作",
        "actionType": "dialog",
        "className": "mr-2.5",
        "dialog": {
          "title": "操作",
          "body": "你正在编辑该卡片"
        }
      },
      {
        "type": "dropdown-button",
        "level": "link",
        "icon": "fa fa-ellipsis-h",
        "className": "pr-1 flex",
        "hideCaret": true,
        "buttons": [
          {
            "type": "button",
            "label": "编辑",
            "actionType": "dialog",
            "dialog": {
              "title": "编辑",
              "body": "你正在编辑该卡片"
            }
          },
          {
            "type": "button",
            "label": "删除",
            "actionType": "dialog",
            "dialog": {
              "title": "提示",
              "body": "你删掉了该卡片"
            }
          }
        ]
      }
    ],
    "toolbar": [
      {
        "type": "tpl",
        "tpl": "标签",
        "className": "label label-warning"
      }
    ]
  }
}
``` -->


<!-- ### 配置工具栏

```schema
{
  "type": "page",
  "body": {
    "type": "card",
    "header": {
      "title": "标题",
      "subTitle": "副标题",
      "description": "这是一段描述",
      "avatarClassName": "pull-left thumb-md avatar b-3x m-r",
      "avatar": "data:image/svg+xml,%3C%3Fxml version='1.0' standalone='no'%3F%3E%3C!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3E%3Csvg t='1631083237695' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='2420' xmlns:xlink='http://www.w3.org/1999/xlink' width='1024' height='1024'%3E%3Cdefs%3E%3Cstyle type='text/css'%3E%3C/style%3E%3C/defs%3E%3Cpath d='M959.872 128c0.032 0.032 0.096 0.064 0.128 0.128v767.776c-0.032 0.032-0.064 0.096-0.128 0.128H64.096c-0.032-0.032-0.096-0.064-0.128-0.128V128.128c0.032-0.032 0.064-0.096 0.128-0.128h895.776zM960 64H64C28.8 64 0 92.8 0 128v768c0 35.2 28.8 64 64 64h896c35.2 0 64-28.8 64-64V128c0-35.2-28.8-64-64-64z' p-id='2421' fill='%23bfbfbf'%3E%3C/path%3E%3Cpath d='M832 288c0 53.024-42.976 96-96 96s-96-42.976-96-96 42.976-96 96-96 96 42.976 96 96zM896 832H128V704l224-384 256 320h64l224-192z' p-id='2422' fill='%23bfbfbf'%3E%3C/path%3E%3C/svg%3E"
    },
    "body": "这里是内容",
    "toolbar": [
      {
        "type": "button",
        "icon": "fa fa-eye",
        "actionType": "dialog",
        "dialog": {
          "title": "查看",
          "body": {
            "type": "form",
            "body": [
              {
                "type": "static",
                "name": "engine",
                "label": "Engine"
              },
              {
                "type": "divider"
              },
              {
                "type": "static",
                "name": "browser",
                "label": "Browser"
              },
              {
                "type": "divider"
              },
              {
                "type": "static",
                "name": "platform",
                "label": "Platform(s)"
              },
              {
                "type": "divider"
              },
              {
                "type": "static",
                "name": "version",
                "label": "Engine version"
              },
              {
                "type": "divider"
              },
              {
                "type": "static",
                "name": "grade",
                "label": "CSS grade"
              },
              {
                "type": "divider"
              },
              {
                "type": "html",
                "html": "<p>添加其他 <span>Html 片段</span> 需要支持变量替换（todo）.</p>"
              }
            ]
          }
        }
      },
      {
        "type": "dropdown-button",
        "level": "link",
        "icon": "fa fa-ellipsis-h",
        "hideCaret": true,
        "buttons": [
          {
            "type": "button",
            "label": "编辑",
            "actionType": "dialog",
            "dialog": {
              "title": "编辑",
              "body": "你正在编辑该卡片"
            }
          },
          {
            "type": "button",
            "label": "删除",
            "actionType": "dialog",
            "dialog": {
              "title": "提示",
              "body": "你删掉了该卡片"
            }
          }
        ]
      }
    ]
  }
}
``` -->

### 属性表

| 属性名                        | 类型                                 | 默认值                              | 说明                                              | 版本 |
| ----------------------------- | ------------------------------------ | ----------------------------------- | ------------------------------------------------- | --- |
| type                          | `string`                             | `"card"`                            | 指定为 Card 渲染器                                |
| className                     | `string`                             |                                     | 外层 Dom 的类名                                   |
| href                          | [模板](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/template) |                                     | 外部链接                                          |
| header                        | `Object`                             |                                     | Card 头部内容设置                                 |
| header.bgColor | `string`      |                                      |                                     | Card 头部背景颜色。 支持： `blue`  
| headerClassName                       | `string`                             |                                     | Card 头部类名                                 |
| header.title                  | [模板](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/template) \| [SchemaNode](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode) |                                     | 标题                                              | `1.43.0` 支持配置 [SchemaNode](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode)  |
| header.titleClassName         | `string`                             |                                     | 标题类名                                          |
| header.subTitle               | [模板](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/template) \| [SchemaNode](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode)  |                                     | 副标题                                            | `1.43.0` 支持配置 [SchemaNode](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode) |
| header.subTitleClassName      | `string`                             |                                     | 副标题类名                                        |
| header.subTitlePlaceholder    | `string`                             |                                     | 副标题占位                                        |
| header.description            | [模板](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/template) |                                     | 描述                                              |
| header.descriptionClassName   | `string`                             |                                     | 描述类名                                          |
| header.descriptionPlaceholder | `string`                             |                                     | 描述占位                                          |
| header.avatar                 | [模板](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/template) |                                     | 图片                                              |
|  header.avatarShape                 | `'circle' \| 'square' \| 'rounded'` |                'circle'                     | 形状，有三种 'circle' （圆形）、'square'（正方形）、'rounded'（圆角）                                            |
| header.avatarClassName        | `string`                             | `"pull-left thumb avatar b-3x m-r"` | 图片包括层类名                                    |
| header.imageClassName         | `string`                             |                                     | 图片类名                                          |
| header.avatarText             | [模板](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/template) |                                     | 如果不配置图片，则会在图片处显示该文本            |
| header.avatarTextBackground   | `Array`                              |                                     | 设置文本背景色，它会根据数据分配一个颜色          |
| header.avatarTextClassName    | `string`                             |                                     | 图片文本类名                                      |
| header.highlight              | `boolean`                            | `false`                             | 是否显示激活样式                                  |
| header.highlightClassName     | `string`                             |                                     | 激活样式类名                                      |
| header.href                   | [模板](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/template) |                                     | 点击卡片跳转的链接地址                            |
| header.blank                  | `boolean`                            | `true`                              | 是否新窗口打开                                    |
| body                          | `Array`                              |                                     | 内容容器，主要用来放置非表单项组件                |
| bodyClassName                 | `string`                             |                                     | 内容区域类名                                      |
| actions                       | Array<[Action](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/action)>            |                                     | 配置按钮集合                                      |
| actionsCount                  | `number`                             | `4`                                 | 按钮集合每行个数                                  |
| itemAction                    | [Action](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/action)                   |                                     | 点击卡片的行为                                    |
| media                         | `Object`                             |                                     | Card 多媒体部内容设置                             |
| media.type                    | `'image'\|'video'`                   |                                     | 多媒体类型                                        |
| media.url                     | `string`                             |                                     | 图片/视频链接                                     |
| media.position                | `'left'\|'right'\|'top'\|'bottom'`   | `'left'`                            | 多媒体位置                                        |
| media.className               | `string`                             | `"w-44 h-28"`                       | 多媒体类名                                        |
| media.isLive                  | `boolean`                            | `false`                             | 视频是否为直播                                    |
| media.autoPlay                | `boolean`                            | `false`                             | 视频是否自动播放                                  |
| media.poster                  | `string`                             | `false`                             | 视频封面                                          |
| media.imageRatio                  | 仅支持`4:3`、`3:4`、`1:1`                            | `1:1`                             | 图片比例                                          |
| secondary                     | [模板](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/template) |                                     | 次要说明                                          |
| toolbar                       | Array<[Action](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/action)>            |                                     | 工具栏按钮                                        |
| dragging                      | `boolean`                            | `false`                             | 是否显示拖拽图标                                  |
| selectable                    | `boolean`                            | `false`                             | 卡片是否可选                                      |
| checkable                     | `boolean`                            | `true`                              | 卡片选择按钮是否禁用                              |
| selected                      | `boolean`                            | `false`                             | 卡片选择按钮是否选中                              |
| hideCheckToggler              | `boolean`                            | `false`                             | 卡片选择按钮是否隐藏                              |
| multiple                      | `boolean`                            | `false`                             | 卡片是否为多选                                    |
| useCardLabel                  | `boolean`                            | `true`                              | 卡片内容区的表单项 label 是否使用 Card 内部的样式 |
| isOption                  | `boolean`                            |                               | 是否作为选择项	 | `1.43.0` |
| metaClassName                  | `string`                            |                               | meta区域类名，是tile、subTitle、description的父级容器	 | `1.47.0` |
