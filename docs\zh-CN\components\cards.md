---
title: Cards 卡片组
description:
type: 0
group: ⚙ 组件
menuName: Cards 卡片组
icon:
order: 32
---

卡片展示，不支持配置初始化接口初始化数据域，所以需要搭配类似像`Service`这样的，具有配置接口初始化数据域功能的组件，或者手动进行数据域初始化，然后通过`source`属性，获取数据链中的数据，完成数据展示。

## 场景推荐
### 基础卡片列表

```schema
{
  "type": "page",
  "data": {
    "items": [
      {
        "engine": "Trident",
        "browser": "Internet Explorer 4.0",
        "platform": "Win 95+",
        "version": "4",
        "grade": "X"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 5.0",
        "platform": "Win 95+",
        "version": "5",
        "grade": "C"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 5.5",
        "platform": "Win 95+",
        "version": "5.5",
        "grade": "A"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 6",
        "platform": "Win 98+",
        "version": "6",
        "grade": "A"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 7",
        "platform": "Win XP SP2+",
        "version": "7",
        "grade": "A"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 8",
        "platform": "Win XP SP2+",
        "version": "7",
        "grade": "A"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 7",
        "platform": "Win XP SP2+",
        "version": "7",
        "grade": "A"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 7",
        "platform": "Win XP SP2+",
        "version": "7",
        "grade": "A"
      }
    ]
  },
  "body": {
    "type": "cards",
    "source": "$items",
    "columnsCount": 2,
    "card": {
        "header": {
          "title": "标题",
          "subTitle": "副标题",
          "description": "这是一段描述",
          "avatar": "data:image/svg+xml,%3C%3Fxml version='1.0' standalone='no'%3F%3E%3C!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3E%3Csvg t='1631083237695' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='2420' xmlns:xlink='http://www.w3.org/1999/xlink' width='1024' height='1024'%3E%3Cdefs%3E%3Cstyle type='text/css'%3E%3C/style%3E%3C/defs%3E%3Cpath d='M959.872 128c0.032 0.032 0.096 0.064 0.128 0.128v767.776c-0.032 0.032-0.064 0.096-0.128 0.128H64.096c-0.032-0.032-0.096-0.064-0.128-0.128V128.128c0.032-0.032 0.064-0.096 0.128-0.128h895.776zM960 64H64C28.8 64 0 92.8 0 128v768c0 35.2 28.8 64 64 64h896c35.2 0 64-28.8 64-64V128c0-35.2-28.8-64-64-64z' p-id='2421' fill='%23bfbfbf'%3E%3C/path%3E%3Cpath d='M832 288c0 53.024-42.976 96-96 96s-96-42.976-96-96 42.976-96 96-96 96 42.976 96 96zM896 832H128V704l224-384 256 320h64l224-192z' p-id='2422' fill='%23bfbfbf'%3E%3C/path%3E%3C/svg%3E"
        },
      "toolbar": [
        {
          "type": "button",
          "label": false,
          "icon": "fa fa-star",
          "visibleOn": "${index<=2}",
          "linkWithoutPadding": true,
          "level": "link",
        },
       {
        "type": "button",
        "label": false,
        "icon": "fa fa-star-o",
        "visibleOn": "${index>2}",
        "linkWithoutPadding": true,
        "level": "link",
      }
      ],
      "columnsCount": 1,
      "body": [
        {
          "label": "Engine",
          "name": "engine"
        },
        {
          "label": "Browser",
          "name": "browser"
        },
        {
          "name": "version",
          "label": "Version"
        }
      ]
    }
  }
}

```

<!-- ### 一行一个

```schema
{
  "type": "page",
  "data": {
    "items": [
      {
        "engine": "Trident",
        "browser": "Internet Explorer 4.0",
        "platform": "Win 95+",
        "version": "4",
        "grade": "X"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 5.0",
        "platform": "Win 95+",
        "version": "5",
        "grade": "C"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 5.5",
        "platform": "Win 95+",
        "version": "5.5",
        "grade": "A"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 6",
        "platform": "Win 98+",
        "version": "6",
        "grade": "A"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 7",
        "platform": "Win XP SP2+",
        "version": "7",
        "grade": "A"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 8",
        "platform": "Win XP SP2+",
        "version": "7",
        "grade": "A"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 7",
        "platform": "Win XP SP2+",
        "version": "7",
        "grade": "A"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 7",
        "platform": "Win XP SP2+",
        "version": "7",
        "grade": "A"
      }
    ]
  },
  "body": {
    "type": "cards",
    "source": "$items",
    "columnsCount": 1, 
    "card": {
      "header": {
        "title": "标题",
        "subTitle": "副标题",
        "description": "这是一段描述",
        "avatar": "data:image/svg+xml,%3C%3Fxml version='1.0' standalone='no'%3F%3E%3C!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3E%3Csvg t='1631083237695' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='2420' xmlns:xlink='http://www.w3.org/1999/xlink' width='1024' height='1024'%3E%3Cdefs%3E%3Cstyle type='text/css'%3E%3C/style%3E%3C/defs%3E%3Cpath d='M959.872 128c0.032 0.032 0.096 0.064 0.128 0.128v767.776c-0.032 0.032-0.064 0.096-0.128 0.128H64.096c-0.032-0.032-0.096-0.064-0.128-0.128V128.128c0.032-0.032 0.064-0.096 0.128-0.128h895.776zM960 64H64C28.8 64 0 92.8 0 128v768c0 35.2 28.8 64 64 64h896c35.2 0 64-28.8 64-64V128c0-35.2-28.8-64-64-64z' p-id='2421' fill='%23bfbfbf'%3E%3C/path%3E%3Cpath d='M832 288c0 53.024-42.976 96-96 96s-96-42.976-96-96 42.976-96 96-96 96 42.976 96 96zM896 832H128V704l224-384 256 320h64l224-192z' p-id='2422' fill='%23bfbfbf'%3E%3C/path%3E%3C/svg%3E"
      },
      "toolbar": [
        {
          "type": "button",
          "label": false,
          "icon": "fa fa-star",
          "visibleOn": "${index<=2}",
          "linkWithoutPadding": true,
          "level": "link"
        },
       {
        "type": "button",
        "label": false,
        "icon": "fa fa-star-o",
        "visibleOn": "${index>2}",
        "linkWithoutPadding": true,
        "level": "link"
      }
      ],
      "body": [
        {
          "label": "Engine",
          "name": "engine"
        },
        {
          "label": "Browser",
          "name": "browser"
        },
        {
          "name": "version",
          "label": "Version"
        }
      ],
      "actions": [
        {
          "label": "按钮1",
          "type": "button",
          "level": "link",
          "actionType": "dialog",
          "dialog": {
            "title": "弹框1",
            "body": "这是个简单的弹框。"
          }
        },
        {
          "label": "按钮2",
          "type": "button",
          "level": "link",
          "actionType": "dialog",
          "dialog": {
            "title": "弹框2",
            "body": "这是个简单的弹框。"
          }
        },
        {
          "name": "show-more",
          "type": "show-more",
          "label": "操作",
          "align": "right",
          "icon": "fa fa-ellipsis-h",
          "visibleDivider": false,
          "collapseOnExceed": 1,
          "buttons": [
            {
              "label": "按钮3",
              "type": "button",
              "level": "link",
              "actionType": "dialog",
              "dialog": {
                "title": "弹框3",
                "body": "这是个简单的弹框。"
              }
            },
            {
              "label": "按钮4",
              "type": "button",
              "level": "link",
              "actionType": "dialog",
              "dialog": {
                "title": "弹框4",
                "body": "这是个简单的弹框。"
              }
            },
            {
              "label": "按钮5",
              "type": "button",
              "level": "link",
              "actionType": "dialog",
              "dialog": {
                "title": "弹框5",
                "body": "这是个简单的弹框。"
              }
            }
          ]
        }
      ],
    }
  }
}

``` -->

<!-- ### 标题水平展示

当标题区域无头像等内容，期望水平展示时，可通过配置`title`属性为`Title容器`来实现，支持配置`Title容器`的`title、subTitle、assistContent、actions`属性

> **注意：**如果配置`title`属性为`Title容器`，则标题的操作按钮区域仅支持配置`Title容器`的`actions`实现

```schema
{
  "type": "page",
  "data": {
    "items": [
      {
        "engine": "Trident",
        "browser": "Internet Explorer 4.0",
        "platform": "Win 95+",
        "version": "4",
        "grade": "X"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 5.0",
        "platform": "Win 95+",
        "version": "5",
        "grade": "C"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 5.5",
        "platform": "Win 95+",
        "version": "5.5",
        "grade": "A"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 6",
        "platform": "Win 98+",
        "version": "6",
        "grade": "A"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 7",
        "platform": "Win XP SP2+",
        "version": "7",
        "grade": "A"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 8",
        "platform": "Win XP SP2+",
        "version": "7",
        "grade": "A"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 7",
        "platform": "Win XP SP2+",
        "version": "7",
        "grade": "A"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 7",
        "platform": "Win XP SP2+",
        "version": "7",
        "grade": "A"
      }
    ]
  },
  "body": {
    "type": "cards",
    "source": "$items",
    "columnsCount": 1,
    "card": {
      "header": {
        "title": {
          "type": "title",
          "title": "标题",
          "subTitle": "这是一个小标题",
          "assistContent": [
            {
              "type": "remark",
              "content": "这是一段提醒"
            }
          ],
          "actions": [
            {
              "type": "button",
              "linkWithoutPadding": true,
              "level": "link",
              "label": "",
              "icon": "fa fa-star-o",
              "vendor": "",
              "visibleOn": "${id > 2}"
            },
            {
              "type": "button",
              "linkWithoutPadding": true,
              "level": "link",
              "label": "",
              "icon": "fa fa-star",
              "vendor": "",
              "visibleOn": "${id <= 2}"
            },
            {
              "label": "详情",
              "type": "button",
              "level": "link",
              "linkWithoutPadding": true,
              "actionType": "dialog",
              "dialog": {
                "title": "弹框1",
                "body": "这是个简单的弹框。"
              },
            },
            {
              "label": "注销",
              "type": "button",
              "level": "link",
              "linkWithoutPadding": true,
              "actionType": "dialog",
              "dialog": {
                "title": "弹框2",
                "body": "这是个简单的弹框。"
              }
            }
          ],
        },
      },
      "body": [
        {
          "label": "Engine",
          "name": "engine"
        },
        {
          "label": "Browser",
          "name": "browser"
        },
        {
          "name": "version",
          "label": "Version"
        }
      ],
      "actions": [
        {
          "label": "按钮1",
          "type": "button",
          "level": "link",
          "actionType": "dialog",
          "dialog": {
            "title": "弹框1",
            "body": "这是个简单的弹框。"
          }
        },
        {
          "label": "按钮2",
          "type": "button",
          "level": "link",
          "actionType": "dialog",
          "dialog": {
            "title": "弹框2",
            "body": "这是个简单的弹框。"
          }
        },
        {
          "name": "show-more",
          "type": "show-more",
          "label": "操作",
          "align": "right",
          "icon": "fa fa-ellipsis-h",
          "visibleDivider": false,
          "collapseOnExceed": 1,
          "buttons": [
            {
              "label": "按钮3",
              "type": "button",
              "level": "link",
              "actionType": "dialog",
              "dialog": {
                "title": "弹框3",
                "body": "这是个简单的弹框。"
              }
            },
            {
              "label": "按钮4",
              "type": "button",
              "level": "link",
              "actionType": "dialog",
              "dialog": {
                "title": "弹框4",
                "body": "这是个简单的弹框。"
              }
            },
            {
              "label": "按钮5",
              "type": "button",
              "level": "link",
              "actionType": "dialog",
              "dialog": {
                "title": "弹框5",
                "body": "这是个简单的弹框。"
              }
            }
          ]
        }
      ],
    }
  }
}

``` -->

### 看板列表

需要展示多组数值类数据时，可使用卡片的看板模式。支持对标题区域设置背景色，标题区域和内容区域设置文字颜色。

```schema
{
  "type": "page",
  "data": {
    "items": [
      {
        "num": 25,
        "title": "数仓表"
      },
      {
        "num": 30,
        "title": "特征"
      },
      {
        "num": 15,
        "title": "指标"
      },
      {
        "num": 8,
        "title": "报表"
      },
      {
        "num": 12,
        "title": "客群"
      },
      {
        "num": 5,
        "title": "模型"
      },
      {
        "num": 25,
        "title": "经营计划"
      },
      {
        "num": 23,
        "title": "其他"
      }
    ]
  },
  "body": {
    "type": "cards",
    "source": "$items",
    "card": {
      "mode": "board",
      "header": {
        "bgColor": "blue",
        "title": "${title}",
        "titleClassName": "pm-text-info",
      },
      "bodyClassName": "pm-text-info",
      "body": [
        {
          "type": "flex",
          "gap": true,
          "justify": "center",
          "items": [
            {
              "name": "num",
              "type": "tpl",
              "tpl": "${num|number}",
              "className": "text-3xl"
            },
            {
              "type": "button",
              "level": "link",
              "className": "underline",
              "label": "查看详情"
            }
          ]
        }
      ]
    }
  }
}
```

如果内容区域需要展示多组数值，并且根据数据业务含义展示不同字体颜色，可参考该场景。

```schema
{
  "type": "page",
  "data": {
    "items": [
      {
        "title": "埋点定义数",
        "color": "pm-text-success",
        "nums": [
          {
            "num": 25
          },
          {
            "num": 30
          }
        ]
      },
      {
        "title": "不活跃埋点数",
        "color": "pm-text-danger",
        "nums": [
          {
            "num": 25
          },
          {
            "num": 30
          }
        ]
      },
      {
        "title": "今天下发接口请求次数",
        "color": "pm-text-success",
        "nums": [
          {
            "num": 25
          },
          {
            "num": 30
          }
        ]
      },
      {
        "title": "近5分钟下发接口请求异常数",
        "color": "pm-text-danger",
        "nums": [
          {
            "num": 25
          },
          {
            "num": 30
          }
        ]
      },
      {
        "title": "今天上报埋点数",
        "color": "pm-text-success",
        "nums": [
          {
            "num": 25
          },
          {
            "num": 30
          }
        ]
      },
      {
        "title": "近5分钟服务端校验埋点不通过数",
        "color": "pm-text-danger",
        "nums": [
          {
            "num": 25
          },
          {
            "num": 30
          }
        ]
      }
    ]
  },
  "body": {
    "type": "cards",
    "source": "$items",
    "card": {
      "mode": "board",
      "header": {
        "title": {
          "type": "typography",
          "text": "${title}"
        },
        "titleClassName": "pm-text-secondary"
      },
      "body": [
        {
          "type": "each",
          "name": "nums",
          "mode": "horizontal",
          "alignItems": "center",
          "justify": "center",
          "gap": true,
          "items": [
            {
              "name": "num",
              "type": "tpl",
              "tpl": "${num|number}",
              "classNameExpr": "${color} text-3xl"
            },
            {
              "type": "tpl",
              "tpl": "|",
              "visibleOn": "${index < nums.length - 1}"
            }
          ]
        }
      ]
    }
  }
}
```

### 底部区域展示描述信息

```schema
{
  "type": "page",
  "data": {
    "items": [
      {
        "title": "标题",
        "subTitle": "副标题",
        "description": "这是一段描述",
        "starCount": 9
      },
      {
        "title": "超长标题超长标题超长标题超长标题",
        "subTitle": "超长副标题超长副标题超长副标题超长副标题",
        "description": "这是一段描述",
        "starCount": 9
      },
      {
        "title": "标题",
        "subTitle": "副标题",
        "description": "这是一段描述",
        "starCount": 9
      },
      {
        "title": "标题",
        "subTitle": "副标题",
        "description": "这是一段描述",
        "starCount": 9
      }
    ]
  },
  "body": {
    "type": "cards",
    "source": "$items",
    "columnsCount": 4,
    "card": {
      "header": {
        "title": {
          "type": "typography",
          "text": "${title}"
        },
        "subTitle": {
          "type": "typography",
          "text": "${subTitle}"
        },
        "description": "${description}",
        "avatarShape": "rounded",
        "avatarText": "AMIS"
      },
      "toolbar": [
        {
          "type": "flex",
          "gap": true,
          "items": [
            {
              "type": "button",
              "icon": "star-regular",
              "level": "link"
            }
          ]
        }
      ],
      "actions": [
        {
          "type": "wrapper",
          "body": [
            {
              "type": "flex",
              "gap": true,
              "alignItems": "center",
              "items": [
                {
                  "type": "icon",
                  "icon": "star-regular"
                },
                {
                  "type": "tpl",
                  "tpl": "${starCount}"
                }
              ]
            }
          ]
        }
      ]
    }
  }
}
```

## 组件用法
### 基本用法

这里我们使用手动初始数据域的方式，即配置`data`属性，进行数据域的初始化。

```schema
{
  "type": "page",
  "data": {
    "items": [
      {
        "engine": "Trident",
        "browser": "Internet Explorer 4.0",
        "platform": "Win 95+",
        "version": "4",
        "grade": "X"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 5.0",
        "platform": "Win 95+",
        "version": "5",
        "grade": "C"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 5.5",
        "platform": "Win 95+",
        "version": "5.5",
        "grade": "A"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 6",
        "platform": "Win 98+",
        "version": "6",
        "grade": "A"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 7",
        "platform": "Win XP SP2+",
        "version": "7",
        "grade": "A"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 8",
        "platform": "Win XP SP2+",
        "version": "7",
        "grade": "A"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 7",
        "platform": "Win XP SP2+",
        "version": "7",
        "grade": "A"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 7",
        "platform": "Win XP SP2+",
        "version": "7",
        "grade": "A"
      }
    ]
  },
  "body": {
    "type": "cards",
    "source": "$items",
    "columnsCount": 2, 
    "card": {
      "header": {
        "title": "标题",
        "subTitle": "副标题",
        "description": "这是一段描述",
        "avatar": "data:image/svg+xml,%3C%3Fxml version='1.0' standalone='no'%3F%3E%3C!DOCTYPE svg PUBLIC '-//W3C//DTD SVG 1.1//EN' 'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3E%3Csvg t='1631083237695' class='icon' viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='2420' xmlns:xlink='http://www.w3.org/1999/xlink' width='1024' height='1024'%3E%3Cdefs%3E%3Cstyle type='text/css'%3E%3C/style%3E%3C/defs%3E%3Cpath d='M959.872 128c0.032 0.032 0.096 0.064 0.128 0.128v767.776c-0.032 0.032-0.064 0.096-0.128 0.128H64.096c-0.032-0.032-0.096-0.064-0.128-0.128V128.128c0.032-0.032 0.064-0.096 0.128-0.128h895.776zM960 64H64C28.8 64 0 92.8 0 128v768c0 35.2 28.8 64 64 64h896c35.2 0 64-28.8 64-64V128c0-35.2-28.8-64-64-64z' p-id='2421' fill='%23bfbfbf'%3E%3C/path%3E%3Cpath d='M832 288c0 53.024-42.976 96-96 96s-96-42.976-96-96 42.976-96 96-96 96 42.976 96 96zM896 832H128V704l224-384 256 320h64l224-192z' p-id='2422' fill='%23bfbfbf'%3E%3C/path%3E%3C/svg%3E"
      },
      "toolbar": [
        {
          "type": "button",
          "label": false,
          "icon": "fa fa-star",
          "visibleOn": "${index<=2}",
          "linkWithoutPadding": true,
          "level": "link"
        },
       {
        "type": "button",
        "label": false,
        "icon": "fa fa-star-o",
        "visibleOn": "${index>2}",
        "linkWithoutPadding": true,
        "level": "link"
      }
      ],
      "body": [
        {
          "label": "Engine",
          "name": "engine"
        },
        {
          "label": "Browser",
          "name": "browser"
        },
        {
          "name": "version",
          "label": "Version"
        }
      ]
    }
  }
}

```

<!-- 或者你也可以使用 CRUD 的 card 模式 -->

<!-- ### 当 cards 在 crud 中且配置了批量操作按钮时可点选

如果配置了 `checkOnItemClick`，当用户点击卡片时就能选中这个卡片，而不是只能点击勾选框才能选中

```schema
{
  "type": "page",
  "data": {
    "items": [
      {
        "id": 1,
        "engine": "Trident",
        "browser": "Internet Explorer 4.0",
        "platform": "Win 95+",
        "version": "4",
        "grade": "X"
      },
      {
        "id": 2,
        "engine": "Trident",
        "browser": "Internet Explorer 5.0",
        "platform": "Win 95+",
        "version": "5",
        "grade": "C"
      },
      {
        "id": 3,
        "engine": "Trident",
        "browser": "Internet Explorer 5.5",
        "platform": "Win 95+",
        "version": "5.5",
        "grade": "A"
      },
      {
        "id": 4,
        "engine": "Trident",
        "browser": "Internet Explorer 6",
        "platform": "Win 98+",
        "version": "6",
        "grade": "A"
      }
    ]
  },
  "body": {
    "type": "crud",
    "mode": "cards",
    "checkOnItemClick": true,
    "source": "$items",
    "columnsCount": 4,
    "bulkActions": [
      {
        "type": "button",
        "label": "查看选中",
        "actionType": "dialog",
        "dialog": {
          "body": "${items|json}"
        }
      }
    ],
    "card": {
      "headerClassName": "border-b-0",
      "body": [
        {
          "label": "Engine",
          "name": "engine"
        },
        {
          "label": "Browser",
          "name": "browser"
        },
        {
          "name": "version",
          "label": "Version"
        }
      ],
      "actions": [
        {
          "type": "button",
          "level": "link",
          "icon": "fa fa-eye",
          "actionType": "dialog",
          "dialog": {
            "title": "查看详情",
            "body": {
              "type": "form",
              "body": [
                {
                  "label": "Engine",
                  "name": "engine",
                  "type": "static"
                },
                {
                  "name": "browser",
                  "label": "Browser",
                  "type": "static"
                },
                {
                  "name": "version",
                  "label": "Version",
                  "type": "static"
                }
              ]
            }
          }
        }
      ]
    }
  }
}
``` -->

<!-- ## 选择模式

设置`"selectable": true`, 卡片组开启多选模式

```schema
{
  "type": "page",
  "data": {
    "items": [
      {
        "engine": "Trident",
        "browser": "Internet Explorer 4.0",
        "platform": "Win 95+",
        "version": "4",
        "grade": "X"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 5.0",
        "platform": "Win 95+",
        "version": "5",
        "grade": "C"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 5.5",
        "platform": "Win 95+",
        "version": "5.5",
        "grade": "A"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 6",
        "platform": "Win 98+",
        "version": "6",
        "grade": "A"
      }
    ]
  },
  "body": {
    "type": "cards",
    "selectable": true,
    "source": "$items",
    "card": {
      "body": [
        {
          "label": "Engine",
          "name": "engine"
        },
        {
          "label": "Browser",
          "name": "browser"
        },
        {
          "name": "version",
          "label": "Version"
        }
      ],
      "actions": [
        {
          "type": "button",
          "level": "link",
          "icon": "fa fa-eye",
          "actionType": "dialog",
          "dialog": {
            "title": "查看详情",
            "body": {
              "type": "form",
              "body": [
                {
                  "label": "Engine",
                  "name": "engine",
                  "type": "static"
                },

                {
                  "name": "browser",
                  "label": "Browser",
                  "type": "static"
                },
                {
                  "name": "version",
                  "label": "Version",
                  "type": "static"
                }
              ]
            }
          }
        }
      ]
    }
  }
}
```

卡片组默认支持多选，设置`"multiple": false`开启单选模式

```schema
{
  "type": "page",
  "data": {
    "items": [
      {
        "engine": "Trident",
        "browser": "Internet Explorer 4.0",
        "platform": "Win 95+",
        "version": "4",
        "grade": "X"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 5.0",
        "platform": "Win 95+",
        "version": "5",
        "grade": "C"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 5.5",
        "platform": "Win 95+",
        "version": "5.5",
        "grade": "A"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 6",
        "platform": "Win 98+",
        "version": "6",
        "grade": "A"
      }
    ]
  },
  "body": {
    "type": "cards",
    "selectable": true,
    "multiple": false,
    "source": "$items",
    "card": {
      "body": [
        {
          "label": "Engine",
          "name": "engine"
        },
        {
          "label": "Browser",
          "name": "browser"
        },
        {
          "name": "version",
          "label": "Version"
        }
      ],
      "actions": [
        {
          "type": "button",
          "level": "link",
          "icon": "fa fa-eye",
          "actionType": "dialog",
          "dialog": {
            "title": "查看详情",
            "body": {
              "type": "form",
              "body": [
                {
                  "label": "Engine",
                  "name": "engine",
                  "type": "static"
                },

                {
                  "name": "browser",
                  "label": "Browser",
                  "type": "static"
                },
                {
                  "name": "version",
                  "label": "Version",
                  "type": "static"
                }
              ]
            }
          }
        }
      ]
    }
  }
}
``` -->

### 属性表

| 属性名           | 类型                                                                     | 默认值              | 说明                                                                    | 版本    |
| ---------------- | ------------------------------------------------------------------------ | ------------------- | ----------------------------------------------------------------------- | ------- |
| type             | `string`                                                                 |                     | `"cards"` 指定为卡片组。                                                |
| title            | [模板](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/template)         |                     | 标题                                                                    |
| source           | [数据映射](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/data-mapping) | `${items}`          | 数据源, 获取当前数据域中的变量                                          |
| placeholder      | [模板](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/template)         | ‘暂无数据’          | 当没数据的时候的文字提示                                                |
| className        | `string`                                                                 |                     | 外层 CSS 类名                                                           |
| headerClassName  | `string`                                                                 | `amis-grid-header`  | 顶部外层 CSS 类名                                                       |
| footerClassName  | `string`                                                                 | `amis-grid-footer`  | 底部外层 CSS 类名                                                       |
| itemClassName    | `string`                                                                 | `col-sm-4 col-md-3` | 卡片 CSS 类名                                                           |
| card             | [Card](/dataseeddesigndocui/#/amis/zh-CN/components/card)                |                     | 配置卡片信息                                                            |
| selectable       | `boolean`                                                                | `false`             | 卡片组是否可选                                                          |
| multiple         | `boolean`                                                                | `true`              | 卡片组是否为多选                                                        |
| checkOnItemClick | `boolean`                                                                |                     | 点选卡片内容是否选中卡片                                                |
| columnsCount     | `number`                                                                 |                     | 视口可排列的的卡片列数                                                  |
| wrapLayout       | `boolean`                                                                | `true`              | 卡片排列是否换行，该属性在瀑布流模式（masonryLayout 属性为 true）时失效 | `1.4.0` |
