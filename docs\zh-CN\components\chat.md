---
title: ChatRoom 聊天室
description:
type: 0
group: ⚙ 组件
menuName: ChatRoom 聊天室
icon:
order: 34

---

聊天室组件，用来渲染聊天室功能。1.71.0 版本开始支持。

## 基本用法

```js
{
  "type": "chat-room",
  "source": [ // 消息源数据
    {
      "sender": true, // true 表示自己，false 表示机器人
      "content": "你好，请问有什么可以帮到你？", // 消息内容
      "avatar": "User", // 头像字段
    },
    {
      "sender": false,
      "content": "我是一个机器人，你可以向我提问任何问题。",
      "avatar": "AI"
    }
  ],
  "sendApi": { // 点击发送按钮时，调用的接口
    "url": "/api/amis-mock/mock2/saveForm",
    "data": {
      "text": "${inputValue}" // 可以通过inputValue获取到输入框的值
    },
    "adaptor": () => {
      return {
        "sender": false,
        "content": "你好，请问有什么可以帮到你？"
      }
    }
  }
}
```

```schema
{
  "type": "page",
  "data": {
    "userId": "userId"
  },
  "body": {
    "type": "chat-room",
    "source": [
      {
        "sender": true,
        "content": "你好，请问有什么可以帮到你？"
      },
      {
        "sender": false,
        "content": "我是一个机器人，你可以向我提问任何问题。"
      }
    ],
    "sendApi": {
      "url": "/api/amis-mock/mock2/saveForm",
      "data": {
        "text": "${inputValue}",
        "userId": "${userId}"
      },
      "adaptor": () => {
        return {
          "sender": false,
          "content": "你好，请问有什么可以帮到你？"
        }
      }
    }
  }
}
```

## 自定义头像

可以通过数据源中的 `avatar` 字段来设置用户和聊天机器人的头像

```schema
{
  "type": "page",
  "id": "chatPage2",
  "data": {
    "messageList": [
      {
        "avatar": "User",
        "sender": true,
        "content": "你好，请问有什么可以帮到你？"
      },
      {
        "avatar": "AI",
        "sender": false,
        "content": "我是一个AI机器人，你可以向我提问任何问题。"
      },
      {
        "avatar": "Bot",
        "sender": false,
        "content": "我是一个Bot机器人，你可以向我提问任何问题。"
      }
    ]
  },
  "body": {
    "type": "chat-room",
    "name": "messageList"
  }
}
```

也可以渲染图片头像

```schema
{
  "type": "page",
  "id": "chatPage2",
  "data": {
    "messageList": [
      {
        "avatar": "https://suda.cdn.bcebos.com/images/amis/ai-fake-face.jpg",
        "sender": true,
        "content": "你好，请问有什么可以帮到你？"
      },
      {
        "avatar": "https://suda.cdn.bcebos.com/images/amis/ai-fake-face.jpg",
        "sender": false,
        "content": "我是一个机器人，你可以向我提问任何问题。"
      }
    ]
  },
  "body": {
    "type": "chat-room",
    "name": "messageList"
  }
}
```

## 设置发送时间

可以通过 `prefixSchema` 和 `suffixSchema` 来设置消息气泡的前缀和后缀所展示的字段，可以设置`timeField`，来让发送的消息中放入时间字段。

```schema
{
  "type": "page",
  "id": "chatPage3",
  "data": {
    "messageList": [
      {
        "sender": true,
        "content": "你好，请问有什么可以帮到你？",
        "date": "2023-07-01 12:00:00"
      },
      {
        "sender": false,
        "content": "我是一个机器人，你可以向我提问任何问题。",
        "date": "2023-07-01 12:01:00"
      }
    ]
  },
  "body": {
    "type": "chat-room",
    "name": "messageList",
    "timeField": "date",
    "prefixSchema": "${date}"
  }
}
```

## 自定义消息气泡内容

可以通过 `messageSchema` 来自定义消息气泡的内容，支持使用表达式来动态渲染消息气泡的内容。

```schema
{
  "type": "page",
  "id": "chatPage4",
  "data": {
    "messageList": [
      {
        "sender": true,
        "content": "你好，请问有什么可以帮到你？"
      },
      {
        "sender": false,
        "type": "image",
        "content": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80"
      }
    ]
  },
  "body": {
    "type": "chat-room",
    "name": "messageList",
    "messageSchema": [
      {
        "type": "tpl",
        "visibleOn": "${type !== 'image'}",
        "tpl": "${content}"
      },
      {
        "type": "image",
        "visibleOn": "${type === 'image'}",
        "src": "${content}",
        "imageMode": "original"
      }
    ]
  }
}
```

## 预览模式

```schema
{
  "type": "page",
  "data": {
    "messageList": [
      {
        "sender": true,
        "content": "你好，请问有什么可以帮到你？",
        "date": "2023-07-01 12:00:00"
      },
      {
        "sender": false,
        "content": "我是一个机器人，你可以向我提问任何问题。",
        "date": "2023-07-01 12:01:00"
      },
      {
        "sender": true,
        "content": "你好，请问有什么可以帮到你？",
        "date": "2023-07-01 12:00:00"
      },
      {
        "sender": false,
        "content": "我是一个机器人，你可以向我提问任何问题。",
        "date": "2023-07-01 12:01:00"
      },
      {
        "sender": true,
        "content": "你好，请问有什么可以帮到你？",
        "date": "2023-07-01 12:00:00"
      },
      {
        "sender": false,
        "content": "我是一个机器人，你可以向我提问任何问题。",
        "date": "2023-07-01 12:01:00"
      },
      {
        "sender": true,
        "content": "你好，请问有什么可以帮到你？",
        "date": "2023-07-01 12:00:00"
      },
      {
        "sender": false,
        "content": "我是一个机器人，你可以向我提问任何问题。",
        "date": "2023-07-01 12:01:00"
      }
    ]
  },
  "body": [
    {
      "type": "chat-room",
      "name": "messageList",
      "timeField": "date",
      "prefixSchema": "${date}",
      "previewMode": true
    }
  ]
}
```

## 属性表

|属性名|类型|默认值|说明|版本|
|---|---|---|---|---|
|source|`Array\|expression`|-|数据源，可以是数组，也可以是表达式，不支持配置api|
|senderField| `string` | `sender` | 发送者字段，为true表示发送者 |
|bodyHeight| `number` | `300` | 聊天框高度|
|contentField| `string` | `content` | 消息气泡内容字段名|
|prefixSchema| `string` | - | 消息气泡前缀字段名|
|suffixSchema| `string` | - | 消息气泡后缀字段名|
|messageSchema| `Schema` | - | 自定义消息气泡内容 |
|sendOnEnter| `boolean` | `true` | 是否回车发送消息|
|sendApi| `Api` | - | 发送消息的 API，返回值会被放进消息数组中，返回数据需要符合消息数据格式 |
|timeField|`string` | - | 消息时间字段名，发送消息时，会自动添加该字段，如要让消息时间显示在消息气泡上，需和`prefixSchema` 和 `suffixSchema` 配合使用 |
|previewMode|`boolean`| `false` | 预览模式 |
|title|`Schema`| - | 聊天室标题区域 |
|senderMessageBackground|`string`| `#dbeafe` | 发送消息背景色 |
|otherMessageBackground|`string`| `#dbeafe` | 接收消息背景色 |
|className| `string` |    | 外层 Dom 的类名 | 1.87.2 |
|avatarConfig| `Object` |  | 头像配置 |  |
|avatarConfig.showOneWord  | `boolean`  | `false` | 是否展示单个字符    |  1.81.2    |
|avatarConfig.show    | `boolean`  | `true` | 是否展示头像        |  1.87.2    |

## 事件表

|事件名|参数|说明|
|----|----|----|
|send| `value` 发送的消息 | 发送消息时触发 |
|messageChange| `messageList` 消息列表 | 消息列表变化时触发 |

## 动作表

|动作名|参数|说明|
|----|----|----|
|send| `inputValue` 发送的消息 | 发送消息 |
|scrollIntoView| `index` 消息索引<br />`expression` 表达式，优先级不如`index`，不需要写`$`直接写逻辑公式即可 | 滚动到指定消息 |

```schema
{
  "type": "page",
  "data": {
    "messageList": [
      {
        "sender": true,
        "content": "你好，请问有什么可以帮到你？",
        "date": "2023-07-01 12:00:00"
      },
      {
        "sender": false,
        "content": "我是一个机器人，你可以向我提问任何问题。",
        "date": "2023-07-01 12:01:01"
      },
      {
        "sender": true,
        "content": "你好，请问有什么可以帮到你？",
        "date": "2023-07-01 12:00:02"
      },
      {
        "sender": false,
        "content": "我是一个机器人，你可以向我提问任何问题。",
        "date": "2023-07-01 12:01:03"
      },
      {
        "sender": true,
        "content": "你好，请问有什么可以帮到你？",
        "date": "2023-07-01 12:00:04"
      },
      {
        "sender": false,
        "content": "我是一个机器人，你可以向我提问任何问题。",
        "date": "2023-07-01 12:01:05",
      },
      {
        "sender": true,
        "content": "你好，请问有什么可以帮到你？",
        "date": "2023-07-01 12:00:06"
      },
      {
        "sender": false,
        "content": "我是一个机器人，你可以向我提问任何问题。",
        "date": "2023-07-01 12:01:07"
      }
    ]
  },
  "body": [
    {
      "type": "button",
      "label": "scroll 到第6条消息",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "scrollIntoView",
              "componentId": "chatRoom1",
              "args": {
                "index": 6
              }
            }
          ]
        }
      }
    },
    {
      "type": "button",
      "label": "scroll 到 2023-07-01 12:00:06",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "scrollIntoView",
              "componentId": "chatRoom1",
              "args": {
                "expression": "date === '2023-07-01 12:00:06'"
              }
            }
          ]
        }
      }
    },
    {
      "type": "chat-room",
      "id": "chatRoom1",
      "name": "messageList",
      "timeField": "date",
      "prefixSchema": "${date}",
      "previewMode": true
    }
  ]
}
```
