---
title: Code 代码高亮
description:
type: 0
group: ⚙ 组件
menuName: Code
icon:
order: 38
standardMode: true
---

使用代码高亮的方式来显示一段代码。

## 基本用法

```schema: scope="body"
{
  "type": "code",
  "language": "html",
  "value": "<div>html</div>"
}
```

## 语言设置

默认语言是 html，可以通过 language 指定以下语言：

`bat`、 `c`、 `coffeescript`、 `cpp`、 `csharp`、 `css`、 `dockerfile`、 `fsharp`、 `go`、 `handlebars`、 `html`、 `ini`、 `java`、 `javascript`、 `json`、 `less`、 `lua`、 `markdown`、 `msdax`、 `objective-c`、 `php`、 `plaintext`、 `postiats`、 `powershell`、 `pug`、 `python`、 `r`、 `razor`、 `ruby`、 `sb`、 `scss`、`shell`、 `sol`、 `sql`、 `swift`、 `typescript`、 `vb`、 `xml`、 `yaml`

```schema
{
  "body": {
    "type": "code",
    "language": "javascript",
    "value": "(function () {\n  let amis = amisRequire('amis/embed');\n  let amisJSON = {\n    type: 'page',\n    title: '表单页面',\n    body: {\n      type: 'form',\n      mode: 'horizontal',\n      api: '/saveForm',\n      body: [\n        {\n          label: 'Name',\n          type: 'input-text',\n          name: 'name'\n        },\n        {\n          label: 'Email',\n          type: 'input-email',\n          name: 'email'\n        }\n      ]\n    }\n  };\n  let amisScoped = amis.embed('#root', amisJSON);\n})();"
  }
}
```

language 支持从上下文获取数据

```schema
{
  "type": "page",
  "data": {
    "lang": "javascript"
  },
  "body": {
    "type": "code",
    "language": "${lang}",
    "value": "function amis() {\n  console.log('amis');\n}"
  }
}
```

## 动态数据

可以使用 name 来从上下文来获取数据，比如

```schema
{
  "type": "page",
  "data": {
    "sourcecode": "<div>html</div>"
  },
  "body": {
    "type": "code",
    "language": "html",
    "name": "sourcecode"
  }
}
```

因此它还能放在表单、crud 中实现代码的展现。

## 主题及 tab 大小

通过 `editorTheme` 设置主题，`tagSize` 设置 tab 宽度

```schema: scope="body"
{
  "type": "code",
  "language": "javascript",
  "tagSize": 4,
  "value": "function amis() {\n\tconsole.log('amis');\n}"
}

```

## 超出换行

通过 `wordWrap` 设置是否折行，默认是折行

```schema: scope="body"
{
  "type": "code",
  "language": "typescript",
  "tagSize": 4,
  "wordWrap": false,
  "value": "function amis() {\n\tconsole.log('amis')\tconsole.log('amis')\tconsole.log('amis')\tconsole.log('amis')\tconsole.log('amis')\tconsole.log('amis');\n}"
}
```

## 自定义语言高亮

还可以通过 `customLang` 参数来自定义高亮。

`customLang` 中主要是 `tokens` 设置，这里是语言词法配置，它有 4 个配置项：

- `name`：词法名称
- `regex`：词法的正则匹配，注意因为是在字符串中，这里正则中如果遇到 `\` 需要写成 `\\`
- `regexFlags`: 可选，正则的标志参数
- `color`：颜色
- `fontStyle`: 可选，字体样式，比如 `bold` 代表加粗

## 格式化代码片段

通过 `formatter` 配置开启代码格式化功能。目前支持的语言：`html`、`css`、`javascript`、`sql`、`json`。

通过 `formatterOptions` 额外的格式化配置项, **目前仅有 `sql` 支持（其他语言即使配置了也不会生效的）** [具体参考 sql-formatter](https://github.com/sql-formatter-org/sql-formatter#configuration-options)

> 需要注意的是因不同语言所使用的格式化程序不一致，因此配置也会不一致。比如配置 "formatterOptions": {"keywordCase": "lower"}
> 仅会在`sql`语言下生效，其他语言不仅不会生效，还有可能报错。

```schema: scope="body"
[
  {
      "type":"panel",
      "title":"code--html",
      "body":{
          "type":"code",
          "language":"html",
          "formatter":true,
          "value": "<html><head><title>标题</title></head><body><span>内容区域</span></body></html>",
      }
  },
  {
      "type":"panel",
      "title":"code--js",
      "body":{
          "type":"code",
          "language":"javascript",
          "formatter":true,
          "value":"function test(a,b){console.log(a,b);console.log(a,b);console.log(a,b);console.log(a,b);console.log(a,b);console.log(a,b);}"
      }
  },
  {
      "type":"panel",
      "title":"code--css",
      "body":{
          "type":"code",
          "language":"css",
          "formatter":true,
          "value": '.demo{"font-family":sans-serif;"font-size":16;"font-weight":bold;}',
      }
  },
  {
      "type":"panel",
      "title":"code--json",
      "body":{
          "type":"code",
          "language":"json",
          "formatter":true,
          "value": '{"a":1, "b":2, "c": {"c1": 3}}',
      }
  },
  {
      "type":"panel",
      "title":"code--sql",
      "body":{
          "type":"code",
          "language":"sql",
          "formatter":true,
          "formatterOptions": {
            keywordCase: "lower",
          },
          "value": "SELECT ProductID, OrderQty, SUM(LineTotal) AS Total FROM Sales.SalesOrderDetail WHERE UnitPrice < 5 GROUP BY ProductID, OrderQty ORDER BY ProductID, OrderQty  OPTION (HASH GROUP, FAST 10);"
      }
  }
]
```

## 主题代码片段

通过 `editorTheme` 配置主题模式。

> 需要注意的是代码片段中不包含回车换行符，会使用code展示代码片段；如果包含有回车换行符，会使用pre展示代码片段，保持原有格式

> 可通过wrapperComponent属性强制指定使用的标签是code还是pre

```schema: scope="body"
[
  {
      "type":"panel",
      "title":"code--css",
      "body":{
          "type":"code",
          "language":"css",
          "editorTheme":"vs-dark",
          "formatter":true,
          "value": '.demo{"font-family":sans-serif;"font-size":16;"font-weight":bold;}',
      }
  },
  {
      "type":"panel",
      "title":"code--css",
      "body":{
          "type":"code",
          "language":"css",
          "editorTheme":"vs-dark",
          "wrapperComponent": "pre",
          "formatter":true,
          "value": '.demo{"font-family":sans-serif;"font-size":16;"font-weight":bold;}',
      }
  },
  {
      "type":"panel",
      "title":"code--json",
      "body":{
          "type":"code",
          "language":"json",
          "editorTheme":"vs-dark",
          "wrapperComponent": "pre",
          "formatter":true,
          "value": '{"a":1, "b":2, "c": {"c1": 3}}',
      }
  },
]
```

## 最大高度代码片段

通过 `maxHeight` 配置组件的最大高度。

```schema: scope="body"
[
  {
      "type":"panel",
      "title":"code--sql",
      "body":{
          "type":"code",
          "language":"sql",
          "editorTheme":"vs-dark",
          "wrapperComponent": "pre",
          "maxHeight": 200,
          "formatter":true,
          "formatterOptions": {
            keywordCase: "lower",
          },
          "value": "SELECT ProductID, OrderQty, SUM(LineTotal) AS Total FROM Sales.SalesOrderDetail WHERE UnitPrice < 5 GROUP BY ProductID, OrderQty ORDER BY ProductID, OrderQty  OPTION (HASH GROUP, FAST 10);"
      }
  }
]
```

## 属性表

| 属性名           | 类型      | 默认值  | 说明                                                                       | 版本   |
| ---------------- | --------- | ------- | -------------------------------------------------------------------------- | ------ |
| className        | `string`  |         | 外层 CSS 类名                                                              |
| value            | `string`  |         | 显示的颜色值                                                               |
| name             | `string`  |         | 在其他组件中，时，用作变量映射                                             |
| language         | `string`  |         | 所使用的高亮语言，默认是 plaintext                                         |
| tabSize          | `number`  | 4       | 默认 tab 大小                                                              |
| editorTheme      | `string`  | 'vs'    | 主题，还有 'vs-dark'                                                       |
| wordWrap         | `string`  | `true`  | 是否折行                                                                   |
| formatter        | `boolean` | `false` | 是否对代码进行格式化                                                       | 1.16.0 |
| formatterOptions | `object`  |         | 当激活格式化功能后，对格式化程序进行额外的配置, 与当前要格式化的语言强相关 | 1.16.0 |
| wrapperComponent | `"pre" \| "code"`  |         | 指定code组件容器是`pre`还是`code` |  |
| maxHeight | `number`  |         | pre容器最大高度，code是內联元素，设置无效 | 1.50.0 |
