---
title: Dialog 对话框
description:
type: 0
group: ⚙ 组件
menuName: Dialog 对话框
icon:
order: 41
standardMode: true
---

<font color=red>**注意事项：**</font>
1. 当在关闭弹窗不想销毁弹窗内容，即配置 `destroyOnClose: flase` 时，不要在 `dialog` 上添加 `data`，这种场景可以将数据放在 `dialog` 内部的 `form` 或者 `service` 中。

Dialog 弹框 主要由 [Action](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/action) 触发，主要展示一个对话框以供用户操作。

## 场景推荐

### 基本用法

1. 基础弹框：内容包含表单静态展示，一行1列；
2. 中号弹框：内容包含表单静态展示，一行2列；
3. 大号弹框：内容包含表单静态展示，一行3列；

```schema
{
  "type": "page",
  "body": {
    "type": "flex",
    "justify": "start",
    "gap": true,
    "items": [
      {
        "type": "button",
        "label": "标准",
        "actionType": "dialog",
        "dialog": {
          "showErrorMsg": false,
          "title": {
            "type": "title",
            "title": "标准",
            "noPadding": true,
            "assistContent": [
              {
                "type": "remark",
                "content": "这是一段提示"
              }
            ]
          },
          "showCloseButton": false,
          "actions": [
            {
              "type": "button",
              "actionType": "cancel",
              "label": "取消",
            },
            {
              "type": "button",
              "actionType": "confirm",
              "label": "确认",
            },
            {
              "type": "button",
              "actionType": "confirm",
              "primary": true,
              "label": "确认并上线"
            }
          ],
          "body": [
            {
              "type": "alert",
              "title": "提示类标题",
              "body": "提示类文案",
              "level": "info",
              "showIcon": true
            },
            {
              "type": "form",
              "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm?waitSeconds=2",
              "body": [
                {
                  "type": "input-text",
                  "name": "platform",
                  "placeholder": "请输入PlatForm(s)",
                  "label": "PlatForm(s)"
                },
                {
                  "type": "input-text",
                  "name": "cssGrade",
                  "label": "CSS grade",
                  "required": true,
                  "placeholder": "请输入CSS grade"
                },
                {
                  "type": "input-text",
                  "name": "brower",
                  "placeholder": "请输入Brower",
                  "label": "Brower"
                },
                {
                  "type": "input-text",
                  "name": "version",
                  "label": "Version",
                  "required": true,
                  "placeholder": "请输入Version"
                }
              ]
            }
          ]
        }
      },
      {
        "type": "button",
        "label": "中号",
        "actionType": "dialog",
        "dialog": {
          "title": "中号",
          "size": "lg",
          "body": {
            "type": "form",
            "title": "",
            "static": true,
            "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
            "actions": [],
            "body": [
              {
                "type": "group",
                "body": [
                  {
                    "type": "select",
                    "name": "department",
                    "label": "归属部门"
                  },
                  {
                    "type": "input-text",
                    "name": "platform",
                    "label": "Platform",
                    "placeholder": "请输入"
                  }
                ]
              },
              {
                "type": "group",
                "body": [
                  {
                    "type": "input-text",
                    "name": "css",
                    "label": "CSS",
                    "required": true,
                    "placeholder": "请输入"
                  },
                  {
                    "type": "input-text",
                    "name": "browser",
                    "label": "Browser",
                    "placeholder": "请输入"
                  }
                ]
              },
              {
                "type": "group",
                "body": [
                  {
                    "type": "select",
                    "name": "selected",
                    "label": "用户选择",
                    "placeholder": "请选择",
                    "options": [
                      {
                        "label": "a",
                        "value": "a"
                      },
                      {
                        "label": "b",
                        "value": "b"
                      }
                    ]
                  },
                  {
                    "type": "input-text",
                    "name": "browser2",
                    "label": "Browser",
                    "placeholder": "请输入"
                  }
                ]
              },
              {
                "type": "group",
                "body": [
                  {
                    "type": "textarea",
                    "name": "remark",
                    "label": "备注",
                    "showCounter": true,
                    "maxLength": 30,
                    "placeholder": "请输入",
                    "trimContents": true
                  }
                ]
              }
            ]
          }
        }
      },
      {
        "type": "button",
        "label": "大号",
        "actionType": "dialog",
        "dialog": {
          "title": "大号",
          "size": "xl",
          "body": {
            "type": "form",
            "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
            "body": [
              {
                "type": "input-file",
                "name": "file",
                "accept": "*",
                "label": false,
                "receiver": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/upload/file",
                "drag": true
              },
              {
                "type": "group",
                "body": [
                  {
                    "type": "input-text",
                    "name": "text1",
                    "label": "姓名"
                  },
                  {
                    "type": "input-text",
                    "name": "text2",
                    "label": "年龄"
                  },
                  {
                    "type": "input-text",
                    "name": "text3",
                    "label": "班级",
                    "required": true
                  }
                ]
              },
              {
                "type": "group",
                "body": [
                  {
                    "type": "input-text",
                    "name": "text4",
                    "label": "邮箱"
                  },
                  {
                    "type": "input-text",
                    "name": "text5",
                    "label": "电话"
                  },
                  {
                    "type": "input-text",
                    "name": "text6",
                    "label": "地址"
                  }
                ]
              },
              {
                "type": "group",
                "body": [
                  {
                    "type": "textarea",
                    "name": "textarea",
                    "label": "姓名",
                    "maxLength": 30,
                    "placehold": "请输入"
                  }
                ]
              },
              {
                "type": "group",
                "body": [
                  {
                    "type": "input-rich-text",
                    "name": "second5",
                    "label": "其它"
                  }
                ]
              }
            ]
          }
        }
      }
    ]
  }
}
```

### 提示类

```schema
{
  "type": "page",
  "type": "page",
  "data": {
    "arr": [
      {
        "name": "FP-2234523434"
      },
      {
        "name": "JC-123452345t"
      },
      {
        "name": "JP-1234562345r"
      },
      {
        "name": "JP-123456234ss5r"
      }
    ]
  },
  "body": {
    "type": "flex",
    "justify": "start",
    "gap": true,
    "items": [
      {
        "type": "button",
        "label": "提示+关联信息",
        "actionType": "dialog",
        "dialog": {
          "title": "节点下线",
          "showCloseButton": false,
          "body": [
            {
              "type": "tpl",
              "tpl": "节点【XXXX】被如下在线状态策略关联，请先解除关联："
            },
            {
              "type": "each",
              "name": "arr",
              "items": [
                {
                  "type": "link",
                  "href": "${name}",
                  "body": "${name}"
                },
                {
                  "type": "button",
                  "level": "link",
                  "label": "（点击toast提示）",
                  "linkWithoutPadding": true,
                  "onEvent": {
                    "click": { // 监听点击事件
                      "actions": [
                        {
                          "actionType": "toast",
                          "args": {
                            "msgType": "info",
                            "msg": "派发点击事件"
                          }
                        }
                      ]
                    }
                  }
                },
                {
                  "type": "tpl",
                  "visibleOn": "${(index + 1) < arr.length}",
                  "tpl": "、"
                }
              ]
            }
          ]
        }
      },
      {
        "type": "button",
        "label": "二次确认（带详情）",
        "actionType": "dialog",
        "dialog": {
          "showErrorMsg": false,
          "title": "二次确认（带详情）",
          "showCloseButton": false,
          "body": {
            "type": "flex",
            "gap": true,
            "direction": "column",
            "items": [
              {
                "type": "tpl",
                "tpl": "确定删除这条信息吗？"
              },
              {
                "type": "alert",
                "body": {
                  "type": "form",
                  "mode": "horizontal",
                  "withoutItemMarginBottom": true,
                  "data": {
                    "platform": "技术中心 / App及前台研发部",
                    "cssGrade": "张三",
                    "brower": "张三"
                  },
                  "static": true,
                  "labelWidth": 60,
                  "wrapWithPanel": false,
                  "body": [
                    {
                      "type": "static",
                      "name": "platform",
                      "label": "归属部门"
                    },
                    {
                      "type": "static",
                      "name": "cssGrade",
                      "label": "负责人"
                    },
                    {
                      "type": "static",
                      "name": "brower",
                      "label": "创建人"
                    }
                  ]
                },
                "level": "warning"
              }
            ]
          }
        }
      },
      {
        "type": "button",
        "label": "关键词突出",
        "actionType": "dialog",
        "dialog": {
          "showErrorMsg": false,
          "title": "关键词突出",
          "showCloseButton": false,
          "body": [
            {
              "type": "flex",
              "gap": true,
              "alignItems": "baseline",
              "items": [
                {
                  "type": "icon",
                  "icon": "alert-warning"
                },
                {
                  "type": "html",
                  "html": "回滚后会将<b>【版本5】</b>更新为【正式运行】状态，是否继续操作？"
                }
              ]
            }
          ]
        }
      }
    ]
  }
}
```

### 批量解析

批量解析弹框：内容包含提示组件+表单项+操作按钮+Crud数据展示

```schema
{
  "type": "page",
  "body": {
    "type": "button",
    "label": "批量解析",
    "actionType": "dialog",
    "dialog": {
      "showErrorMsg": false,
      "title": "批量解析",
      "showCloseButton": false,
      "size": "md",
      "data": {
        "dataEnum": {
          "INIT": "未上线",
          "ONLINE": "已上线",
          "OFFLINE": "已下线",
          "ONLINE_STOP": "已上线（停更）",
          "DATA_INIT": "数据初始化中"
        },
        "colorEnum": {
          "INIT": "active",
          "ONLINE": "success",
          "OFFLINE": "inactive",
          "ONLINE_STOP": "success",
          "DATA_INIT": "active"
        },
        "changeList": [
          {
            "canGoOffline": false,
            "featureCode": "test",
            "featureName": "time2",
            "featureStatus": "DATA_INIT",
            "groupCode": "fg.off.usr.ads_app_blacklst_sit_spl_incd_usr_user_level11_v1_df",
            "reason": "特征不是“已上线”/“已上线（停更）”状态",
            "serviceKey": "off_usr_level11_time2"
          }
        ],
        "offCount": 0,
        "onlineCount": 1
      },
      "body": {
        "type": "form",
        "mode": "vertical",
        "body": {
          "type": "flex",
          "standardMode": true,
          "gap": true,
          "direction": "column",
          "alignItems": "stretch",
          "items": [
            {
              "type": "alert",
              "body": "单击【确定】仅提交可下线的特征<br>2、系统将发起工单，工单审核通过后，特征KEY不可用",
              "level": "warning",
              "showIcon": true
            },
            {
              "type": "textarea",
              "name": "featureKeyList",
              "required": true,
              "label": "特征Key",
              "placeholder": "请输入，最多1000条数据，换行符分割"
            },
            {
              "type": "button",
              "label": "解析特征",
              "level": "primary"
            },
            {
              "type": "crud",
              "id": "offlineCrudId",
              "strictMode": true,
              "name": "changeList",
              "source": "${changeList}",
              "columnsTogglable": false,
              "headerToolbar": [
                {
                  "type": "tpl",
                  "tpl": "可下线：${offCount}个，不可下线：${onlineCount}个"
                }
              ],
              "columns": [
                {
                  "label": "特征KEY",
                  "name": "featureCode"
                },
                {
                  "name": "featureName",
                  "label": "特征名称"
                },
                {
                  "label": "上线状态",
                  "type": "flex",
                  "alignItems": "flex-start",
                  "justify": "flex-start",
                  "items": [
                    {
                      "type": "tag",
                      "label": "${dataEnum[featureStatus]}",
                      "color": "${colorEnum[featureStatus]}",
                      "displayMode": "bordered"
                    }
                  ]
                },
                {
                  "type": "static-date",
                  "label": "上线时间",
                  "name": "onlineTime",
                  "format": "YYYY-MM-DD HH:mm:ss"
                },
                {
                  "name": "canGoOffline",
                  "label": "是否可下线",
                  "headSearchable": {
                    "type": "select",
                    "name": "canGoOffline",
                    "label": "是否可下线",
                    "options": [
                      {
                        "label": "是",
                        "value": "YES"
                      },
                      {
                        "label": "否",
                        "value": "NO"
                      }
                    ]
                  }
                },
                {
                  "name": "reason",
                  "label": "不可下线原因"
                }
              ]
            }
          ]
        }
      }
    }
  }
}
```


### 分组表单

```schema
{
  "type": "page",
  "body": {
    "type": "flex",
    "justify": "start",
    "gap": true,
    "items": [
      {
        "type": "button",
        "label": "分组表单",
        "actionType": "dialog",
        "dialog": {
          "title": "分组表单",
          "size": "lg",
          "body": {
            "type": "form",
            "labelWidth": 60,
            "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
            "body": {
              "type": "group-container",
              "activeKey": [
                "1"
              ],
              "collapsible": false,
              "items": [
                {
                  "header": {
                    "title": "第一步，基础信息",
                    "assistContent": [
                      {
                        "type": "remark",
                        "content": "这是一段提示"
                      }
                    ],
                    "actions": [
                      {
                        "type": "button",
                        "label": "实验列表",
                        "level": "link",
                        "linkWithoutPadding": true
                      }
                    ]
                  },
                  "body": [
                    {
                      "type": "group",
                      "body": [
                        {
                          "type": "input-text",
                          "name": "text1",
                          "label": "姓名"
                        },
                        {
                          "type": "input-text",
                          "name": "text2",
                          "label": "年龄"
                        },
                        {
                          "type": "input-text",
                          "name": "text3",
                          "label": "班级",
                          "required": true
                        }
                      ]
                    },
                    {
                      "type": "group",
                      "body": [
                        {
                          "type": "input-text",
                          "name": "text4",
                          "description": "调整数量大小查看效果吧！",
                          "label": "邮箱"
                        },
                        {
                          "type": "input-text",
                          "name": "text5",
                          "label": "电话"
                        },
                        {
                          "type": "input-text",
                          "name": "text6",
                          "label": "地址",
                          "columnRatio": 4
                        }
                      ]
                    },
                    {
                      "type": "group",
                      "body": [
                        {
                          "type": "select",
                          "name": "text8",
                          "label": "类型",
                          "columnRatio": 4,
                          "options": [
                            {
                              "label": "曹操",
                              "value": "caocao"
                            },
                            {
                              "label": "刘备",
                              "value": "liubei"
                            }
                          ]
                        },
                      ]
                    }
                  ]
                },
                {
                  "header": {
                    "title": "第二步，复杂信息"
                  },
                  "body": [
                    {
                      "type": "group",
                      "body": [
                        {
                          "type": "input-text",
                          "name": "second1",
                          "label": "邮箱"
                        },
                        {
                          "type": "input-text",
                          "name": "second2",
                          "label": "电话"
                        },
                        {
                          "type": "input-text",
                          "name": "second3",
                          "label": "地址",
                          "columnRatio": 4
                        }
                      ]
                    },
                    {
                      "type": "group",
                      "body": [
                        {
                          "type": "textarea",
                          "name": "textarea",
                          "label": "姓名",
                          "placeholder": "请输入"
                        }
                      ]
                    },
                    {
                      "type": "group",
                      "body": [
                        {
                          "type": "input-rich-text",
                          "name": "second5",
                          "label": "其它"
                        }
                      ]
                    }
                  ]
                }
              ],
              "actions": [
                {
                  "type": "button",
                  "label": "取消"
                },
                {
                  "type": "submit",
                  "level": "primary",
                  "label": "保存"
                }
              ]
            }
          }
        }
      },
      {
        "type": "button",
        "label": "可折叠分组",
        "actionType": "dialog",
        "dialog": {
          "title": "可折叠分组",
          "size": "lg",
          "body": {
            "type": "form",
            "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
            "body": {
              "type": "group-container",
              "activeKey": [
                "1"
              ],
              "collapsible": true,
              "items": [
                {
                  "header": {
                    "title": "第一步，转换文本",
                    "subTitle": "这是小标题"
                  },
                  "key": "1",
                  "body": [
                    {
                      "type": "group",
                      "body": [
                        {
                          "type": "textarea",
                          "name": "textarea",
                          "labelWidth": 160,
                          "label": "请输入要转换的文本",
                          "labelRemark": "提示信息",
                          "placeholder": "请输入"
                        }
                      ]
                    },
                    {
                      "type": "button",
                      "label": "转换",
                      "level": "primary"
                    }
                  ]
                },
                {
                  "header": {
                    "title": "第二步，转换结果"
                  },
                  "body": [
                    {
                      "type": "group-container",
                      "isSmallGroup": true,
                      "items": [
                        {
                          "header": {
                            "title": "驱动指标配置"
                          },
                          "body": [
                            {
                              "type": "group",
                              "body": [
                                {
                                  "type": "static-tags",
                                  "label": "已选特征",
                                  "mode": "inline",
                                  "items": [
                                    {
                                      "type": "tag",
                                      "label": "这是一个很长长长长长长长长长长长长长的标签",
                                      "color": "success",
                                      "displayMode": "bordered"
                                    },
                                    {
                                      "type": "tag",
                                      "label": "这是一个很长长长长长长长长长长长长长的标签",
                                      "color": "success",
                                      "displayMode": "bordered"
                                    },
                                    {
                                      "type": "tag",
                                      "label": "这是一个很长长长长长长长长长长长长长的标签",
                                      "color": "success",
                                      "displayMode": "bordered"
                                    },
                                    {
                                      "type": "tag",
                                      "label": "这是一个很长长长长长长长长长长长长长的标签",
                                      "color": "success",
                                      "displayMode": "bordered"
                                    }
                                  ]
                                }
                              ]
                            },
                            {
                              "type": "table",
                              "name": "table",
                              "selectable": true,
                              "multiple": true,
                              "items": [
                                {
                                  "a": "a1",
                                  "b": "a2",
                                  "c": "a3",
                                  "d": "a4"
                                },
                                {
                                  "a": "a1",
                                  "b": "a2",
                                  "c": "a3",
                                  "d": "a4"
                                },
                                {
                                  "a": "a1",
                                  "b": "a2",
                                  "c": "a3",
                                  "d": "a4"
                                }
                              ],
                              "columns": [
                                {
                                  "name": "a",
                                  "label": "指标名称"
                                },
                                {
                                  "name": "b",
                                  "label": "列表2"
                                },
                                {
                                  "name": "c",
                                  "label": "列表3"
                                },
                                {
                                  "name": "d",
                                  "label": "列表4"
                                }
                              ]
                            }
                          ]
                        },
                        {
                          "header": {
                            "title": "关注指标配置"
                          },
                          "body": [
                            {
                              "type": "group",
                              "body": [
                                {
                                  "type": "static-tags",
                                  "label": "已选特征",
                                  "mode": "inline",
                                  "items": [
                                    {
                                      "type": "tag",
                                      "label": "这是一个很长长长长长长长长长长长长长的标签",
                                      "color": "success",
                                      "displayMode": "bordered"
                                    },
                                    {
                                      "type": "tag",
                                      "label": "这是一个很长长长长长长长长长长长长长的标签",
                                      "color": "success",
                                      "displayMode": "bordered"
                                    },
                                    {
                                      "type": "tag",
                                      "label": "这是一个很长长长长长长长长长长长长长的标签",
                                      "color": "success",
                                      "displayMode": "bordered"
                                    },
                                    {
                                      "type": "tag",
                                      "label": "这是一个很长长长长长长长长长长长长长的标签",
                                      "color": "success",
                                      "displayMode": "bordered"
                                    }
                                  ]
                                }
                              ]
                            },
                            {
                              "type": "table",
                              "name": "table",
                              "selectable": true,
                              "multiple": true,
                              "items": [
                                {
                                  "a": "a1",
                                  "b": "a2",
                                  "c": "a3",
                                  "d": "a4"
                                },
                                {
                                  "a": "a1",
                                  "b": "a2",
                                  "c": "a3",
                                  "d": "a4"
                                },
                                {
                                  "a": "a1",
                                  "b": "a2",
                                  "c": "a3",
                                  "d": "a4"
                                }
                              ],
                              "columns": [
                                {
                                  "name": "a",
                                  "label": "指标名称"
                                },
                                {
                                  "name": "b",
                                  "label": "列表2"
                                },
                                {
                                  "name": "c",
                                  "label": "列表3"
                                },
                                {
                                  "name": "d",
                                  "label": "列表4"
                                }
                              ]
                            }
                          ]
                        },
                        {
                          "header": {
                            "title": "匹配无结果"
                          },
                          "body": [
                            {
                              "type": "group",
                              "body": [
                                {
                                  "type": "static-tags",
                                  "label": "关键字",
                                  "mode": "inline",
                                  "items": [
                                    {
                                      "type": "tag",
                                      "label": "这是一个很长长长长长长长长长长长长长的标签",
                                      "color": "error",
                                      "displayMode": "bordered"
                                    },
                                    {
                                      "type": "tag",
                                      "label": "这是一个很长长长长长长长长长长长长长的标签",
                                      "color": "error",
                                      "displayMode": "bordered"
                                    },
                                    {
                                      "type": "tag",
                                      "label": "这是一个很长长长长长长长长长长长长长的标签",
                                      "color": "error",
                                      "displayMode": "bordered"
                                    },
                                    {
                                      "type": "tag",
                                      "label": "这是一个很长长长长长长长长长长长长长的标签",
                                      "color": "error",
                                      "displayMode": "bordered"
                                    }
                                  ]
                                }
                              ]
                            }
                          ]
                        }
                      ]
                    }
                  ]
                },
                {
                  "header": {
                    "title": "第三步，已选特征"
                  },
                  "body": [
                    {
                      "type": "group",
                      "body": [
                        {
                          "type": "static-tags",
                          "label": "已选特征4个",
                          "mode": "inline",
                          "items": [
                            {
                              "type": "tag",
                              "label": "这是一个很长长长长长长长长长长长长长的标签",
                              "color": "success",
                              "displayMode": "bordered",
                              "closable": true
                            },
                            {
                              "type": "tag",
                              "label": "这是一个很长长长长长长长长长长长长长的标签",
                              "color": "success",
                              "displayMode": "bordered",
                              "closable": true
                            },
                            {
                              "type": "tag",
                              "label": "这是一个很长长长长长长长长长长长长长的标签",
                              "color": "success",
                              "displayMode": "bordered",
                              "closable": true
                            },
                            {
                              "type": "tag",
                              "label": "这是一个很长长长长长长长长长长长长长的标签",
                              "color": "success",
                              "displayMode": "bordered",
                              "closable": true
                            }
                          ]
                        }
                      ]
                    }
                  ]
                }
              ],
              "actions": [
                {
                  "type": "button",
                  "label": "取消"
                },
                {
                  "type": "submit",
                  "level": "primary",
                  "label": "保存"
                }
              ]
            }
          }
        }
      },
      {
        "type": "button",
        "label": "Tabs模式带分组",
        "actionType": "dialog",
        "dialog": {
          "title": "Tabs模式带分组",
          "size": "lg",
          "body": {
            "type": "tabs",
            "tabs": [
              {
                "title": "可折叠",
                "body": {
                  "type": "form",
                  "static": true,
                  "labelWidth": 60,
                  "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
                  "body": {
                    "type": "group-container",
                    "activeKey": [
                      "1"
                    ],
                    "collapsible": true,
                    "items": [
                      {
                        "header": {
                          "title": "第一步，基础信息"
                        },
                        "body": [
                          {
                            "type": "group",
                            "body": [
                              {
                                "type": "input-text",
                                "name": "text1",
                                "label": "姓名"
                              },
                              {
                                "type": "input-text",
                                "name": "text2",
                                "label": "年龄"
                              },
                              {
                                "type": "input-text",
                                "name": "text3",
                                "label": "班级",
                                "required": true
                              }
                            ]
                          },
                          {
                            "type": "group",
                            "body": [
                              {
                                "type": "input-text",
                                "name": "text4",
                                "label": "邮箱"
                              },
                              {
                                "type": "input-text",
                                "name": "text5",
                                "label": "电话"
                              },
                              {
                                "type": "input-text",
                                "name": "text6",
                                "label": "地址",
                                "columnRatio": 4
                              }
                            ]
                          },
                          {
                            "type": "group",
                            "body": [
                              {
                                "type": "input-text",
                                "name": "text7",
                                "label": "其它",
                                "columnRatio": 4
                              }
                            ]
                          }
                        ]
                      },
                      {
                        "header": {
                          "title": "第二步，复杂信息",
                        },
                        "body": [
                          {
                            "type": "group",
                            "body": [
                              {
                                "type": "input-text",
                                "name": "second1",
                                "label": "邮箱"
                              },
                              {
                                "type": "input-text",
                                "name": "second2",
                                "label": "电话"
                              },
                              {
                                "type": "input-text",
                                "name": "second3",
                                "label": "地址",
                                "columnRatio": 4
                              }
                            ]
                          },
                          {
                            "type": "group",
                            "body": [
                              {
                                "type": "textarea",
                                "name": "textarea",
                                "label": "姓名",
                                "placeholder": "请输入"
                              }
                            ]
                          },
                          {
                            "type": "group",
                            "body": [
                              {
                                "type": "input-rich-text",
                                "name": "second5",
                                "label": "其它"
                              }
                            ]
                          }
                        ]
                      },
                      {
                        "header": {
                          "title": "第三步，策略信息"
                        },
                        "body": [
                          {
                            "type": "group",
                            "body": [
                              {
                                "type": "tabs",
                                "addable": true,
                                "closable": true,
                                "editable": true,
                                "addBtnText": " ",
                                "tabPosition": "center",
                                "tabsMode": "strong",
                                "defaultTabForAdd": {
                                  "title": "策略分支x",
                                  "body": [
                                    {
                                      "type": "group",
                                      "body": [
                                        {
                                          "type": "input-text",
                                          "name": "third1",
                                          "label": "sjksajkd"
                                        },
                                        {
                                          "type": "input-text",
                                          "name": "third2",
                                          "label": "sjksajkd"
                                        },
                                        {
                                          "type": "input-text",
                                          "name": "third3",
                                          "label": "sjksajkd"
                                        }
                                      ]
                                    },
                                    {
                                      "type": "group",
                                      "body": [
                                        {
                                          "type": "input-text",
                                          "name": "third4",
                                          "label": "sjksajkd"
                                        },
                                        {
                                          "type": "input-text",
                                          "name": "third5",
                                          "label": "sjksajkd"
                                        },
                                        {
                                          "type": "input-text",
                                          "name": "third6",
                                          "label": "sjksajkd"
                                        }
                                      ]
                                    }
                                  ]
                                },
                                "tabs": [
                                  {
                                    "title": "策略分支1",
                                    "tab": [
                                      {
                                        "type": "group",
                                        "body": [
                                          {
                                            "type": "input-text",
                                            "name": "third1",
                                            "label": "sjksajkd"
                                          },
                                          {
                                            "type": "input-text",
                                            "name": "third2",
                                            "label": "sjksajkd"
                                          },
                                          {
                                            "type": "input-text",
                                            "name": "third3",
                                            "label": "sjksajkd"
                                          }
                                        ]
                                      },
                                      {
                                        "type": "group",
                                        "body": [
                                          {
                                            "type": "input-text",
                                            "name": "third5",
                                            "label": "sjksajkd"
                                          },
                                          {
                                            "type": "select",
                                            "name": "third4",
                                            "label": "sjksajkd",
                                            "options": [
                                              {
                                                "label": "a",
                                                "value": "a"
                                              },
                                              {
                                                "label": "b",
                                                "value": "b"
                                              }
                                            ]
                                          },
                                          {
                                            "type": "input-text",
                                            "name": "third6",
                                            "label": "sjksajkd"
                                          }
                                        ]
                                      }
                                    ]
                                  },
                                  {
                                    "title": "策略分支2",
                                    "tab": [
                                      {
                                        "type": "group",
                                        "body": [
                                          {
                                            "type": "input-text",
                                            "name": "third7",
                                            "label": "sjksajkd"
                                          },
                                          {
                                            "type": "input-text",
                                            "name": "third8",
                                            "label": "sjksajkd"
                                          },
                                          {
                                            "type": "input-text",
                                            "name": "third9",
                                            "label": "sjksajkd"
                                          }
                                        ]
                                      },
                                      {
                                        "type": "group",
                                        "body": [
                                          {
                                            "type": "input-text",
                                            "name": "third10",
                                            "label": "sjksajkd"
                                          },
                                          {
                                            "type": "select",
                                            "name": "third4",
                                            "label": "sjksajkd",
                                            "options": [
                                              {
                                                "label": "a",
                                                "value": "a"
                                              },
                                              {
                                                "label": "b",
                                                "value": "b"
                                              }
                                            ]
                                          },
                                          {
                                            "type": "input-text",
                                            "name": "third12",
                                            "label": "sjksajkd"
                                          }
                                        ]
                                      }
                                    ]
                                  },
                                  {
                                    "title": "策略分支3",
                                    "tab": [
                                      {
                                        "type": "group",
                                        "body": [
                                          {
                                            "type": "input-text",
                                            "name": "third13",
                                            "label": "sjksajkd"
                                          },
                                          {
                                            "type": "input-text",
                                            "name": "third14",
                                            "label": "sjksajkd"
                                          },
                                          {
                                            "type": "input-text",
                                            "name": "third15",
                                            "label": "sjksajkd"
                                          }
                                        ]
                                      },
                                      {
                                        "type": "group",
                                        "body": [
                                          {
                                            "type": "input-text",
                                            "name": "third16",
                                            "label": "sjksajkd"
                                          },
                                          {
                                            "type": "select",
                                            "name": "third4",
                                            "label": "sjksajkd",
                                            "options": [
                                              {
                                                "label": "a",
                                                "value": "a"
                                              },
                                              {
                                                "label": "b",
                                                "value": "b"
                                              }
                                            ]
                                          },
                                          {
                                            "type": "input-text",
                                            "name": "third18",
                                            "label": "sjksajkd"
                                          }
                                        ]
                                      }
                                    ]
                                  }
                                ]
                              },
                            ]
                          }
                        ]
                      }
                    ],
                    "actions": [
                      {
                        "type": "button",
                        "label": "取消"
                      },
                      {
                        "type": "submit",
                        "level": "primary",
                        "label": "保存"
                      }
                    ]
                  }
                }
              },
              {
                "title": "基础模式",
                "body": {
                  "type": "form",
                  "title": "",
                  "mode": "normal",
                  "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
                  "actions": [],
                  "body": {
                    "type": "group-container",
                    "items": [
                      {
                        "header": {
                          "title": "获取衍生特征值时，需传入以下参数，请赋值"
                        },
                        "body": [
                          {
                            "type": "input-table",
                            "name": "table",
                            "label": "特征参数",
                            "perPage": 1,
                            "columnRatio": 12,
                            "columns": [
                              {
                                "name": "a",
                                "label": "A"
                              },
                              {
                                "name": "b",
                                "label": "B"
                              },
                              {
                                "name": "c",
                                "label": "C",
                                "type": "input-text",
                                "placeholder": "请输入数字",
                              }
                            ]
                          },
                          {
                            "type": "button",
                            "level": "primary",
                            "columnRatio": 12,
                            "label": "测试"
                          }
                        ]
                      },
                      {
                        "header": {
                          "title": "测试结果"
                        },
                        "body": {
                          "type": "flex",
                          "direction": "column",
                          "standardMode": true,
                          "gap": true,
                          "items": [
                            {
                              "type": "flex",
                              "justify": "start",
                              "items": [
                                {
                                  "type": "tpl",
                                  "tpl": "调用成功！",
                                  "className": "text-success"
                                },
                                {
                                  "type": "tpl",
                                  "tpl": "结果如下："
                                }
                              ]
                            },
                            {
                              "type": "table",
                              "source": "$table",
                              "columns": [
                                {
                                  "name": "a",
                                  "label": "A"
                                },
                                {
                                  "name": "b",
                                  "label": "B"
                                },
                                {
                                  "name": "c",
                                  "label": "C"
                                }
                              ]
                            },
                            {
                              "type": "tpl",
                              "tpl": "接口响应："
                            },
                            {
                              "type": "panel",
                              "body": {
                                "type": "wrapper",
                                "body": {
                                  "type": "code",
                                  "language": "json",
                                  "formatter": true,
                                  "value": "{\"a\":1, \"b\":2, \"c\": {\"c1\": 3}}"
                                }
                              }
                            }
                          ]
                        }
                      }
                    ]
                  }
                }
              }
            ]
          }
        }
      }
    ]
  }
}
```

### 表格

```schema
{
  "type": "page",
  "body": {
    "type": "button",
    "label": "表格",
    "actionType": "dialog",
    "dialog": {
      "title": "表格",
      "size": "lg",
      "body": {
        "type": "form",
        "labelWidth": 60,
        "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
        "body": {
          "type": "crud",
          "keepItemSelectionOnPageChange": true,
          "multiple": true,
          "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?waitSeconds=1",
          "autoGenerateFilter": false,
          "headerToolbar": [
            "bulkActions",
            {
              "type": "button-group-select",
              "name": "button-group-select",
              "align": "right",
              "options": [
                {
                  "value": "all",
                  "label": "查询全部"
                },
                {
                  "value": "forme",
                  "label": "待我审核"
                },
                {
                  "value": "reject",
                  "label": "已拒绝"
                }
              ],
              "onEvent": {
                "change": {
                  "actions": [
                    {
                      "actionType": "query",
                      "componentId": "custom-crud-id",
                      "args": {
                        "queryParams": {
                          "button-group-select": "${button-group-select}"
                        }
                      }
                    }
                  ]
                }
              }
            }
          ],
          "bulkActions": [
            {
              "label": "批量删除",
              "actionType": "ajax",
              "api": "delete:https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample/${ids|raw}",
              "confirmText": "确定要批量删除?"
            },
            {
              "label": "批量修改",
              "actionType": "dialog",
              "dialog": {
                "title": "批量编辑",
                "showCloseButton": false,
                "body": {
                  "type": "form",
                  "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample/bulkUpdate2",
                  "body": [
                    {
                      "type": "hidden",
                      "name": "ids"
                    },
                    {
                      "type": "input-text",
                      "name": "engine",
                      "label": "Engine"
                    }
                  ]
                }
              }
            }
          ],
          "columns": [
            {
              "name": "id",
              "label": "ID"
            },
            {
              "name": "engine",
              "label": "Rendering engine"
            },
            {
              "name": "browser",
              "label": "Browser"
            },
            {
              "name": "platform",
              "label": "Platform(s)"
            },
            {
              "name": "engine",
              "label": "Engine"
            },
            {
              "name": "version",
              "label": "Engine Version"
            },
            {
              "name": "grade",
              "label": "CSS grade"
            }
          ]
        }
      }
    }
  }
}
```


### 分步向导

```schema
{
  "type": "page",
  "body": {
    "type": "button",
    "label": "分步向导",
    "actionType": "dialog",
    "dialog": {
      "title": "分步向导",
      "showCloseButton": false,
      "componentId": "dialog-id",
      "actions": [],
      "body": {
        "type": "form",
        "wrapWithPanel": false,
        "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm?waitSeconds=2",
        "body": {
          "type": "wizard",
          "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
          "steps": [
            {
              "title": "第一步",
              "mode": "horizontal",
              "body": [
                {
                  "type": "input-text",
                  "name": "platform",
                  "placeholder": "请输入PlatForm(s)",
                  "label": "PlatForm(s)"
                },
                {
                  "type": "input-text",
                  "name": "cssGrade",
                  "label": "CSS grade",
                  "required": true,
                  "placeholder": "请输入CSS grade"
                },
                {
                  "type": "input-text",
                  "name": "brower",
                  "placeholder": "请输入Brower",
                  "label": "Brower"
                },
                {
                  "type": "input-text",
                  "name": "version",
                  "label": "Version",
                  "required": true,
                  "placeholder": "请输入Version"
                }
              ],
              "actions": [
                {
                  "type": "button",
                  "label": "取消",
                  "actionType": "cancel",
                  "componentId": "dialog-id"
                },
                {
                  "type": "button",
                  "label": "下一步",
                  "level": "primary",
                  "actionType": "next"
                }
              ]
            },
            {
              "title": "第二步",
              "mode": "horizontal",
              "body": [
                {
                  "type": "input-text",
                  "name": "platform2",
                  "placeholder": "请输入PlatForm(s)",
                  "label": "PlatForm(s)"
                },
                {
                  "type": "input-text",
                  "name": "cssGrade2",
                  "label": "CSS grade",
                  "required": true,
                  "placeholder": "请输入CSS grade"
                },
                {
                  "type": "input-text",
                  "name": "brower2",
                  "placeholder": "请输入Brower",
                  "label": "Brower"
                },
                {
                  "type": "input-text",
                  "name": "version2",
                  "label": "Version",
                  "required": true,
                  "placeholder": "请输入Version"
                }
              ],
              "actions": [
                {
                  "type": "button",
                  "label": "取消",
                  "actionType": "cancel",
                  "componentId": "dialog-id"
                },
                {
                  "type": "button",
                  "label": "上一步",
                  "actionType": "prev"
                },
                {
                  "type": "button",
                  "label": "下一步",
                  "level": "primary",
                  "actionType": "next"
                }
              ]
            },
            {
              "title": "第三步",
              "mode": "horizontal",
              "body": [
                {
                  "type": "input-text",
                  "name": "platform3",
                  "placeholder": "请输入PlatForm(s)",
                  "label": "PlatForm(s)"
                },
                {
                  "type": "input-text",
                  "name": "cssGrade3",
                  "label": "CSS grade",
                  "required": true,
                  "placeholder": "请输入CSS grade"
                },
                {
                  "type": "input-text",
                  "name": "brower3",
                  "placeholder": "请输入Brower",
                  "label": "Brower"
                },
                {
                  "type": "input-text",
                  "name": "version3",
                  "label": "Version",
                  "required": true,
                  "placeholder": "请输入Version"
                }
              ],
              "actions": [
                {
                  "type": "button",
                  "label": "取消",
                  "actionType": "cancel",
                  "componentId": "dialog-id"
                },
                {
                  "type": "button",
                  "label": "上一步",
                  "actionType": "prev"
                },
                {
                  "type": "button",
                  "label": "下一步",
                  "level": "primary",
                  "actionType": "next"
                }
              ]
            }
          ]
        }
      }
    }
  }
}

```

### 图片集

```schema
{
  "type": "page",
  "data": {
    "imageList": [
      "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
      "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
      "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
      "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg@s_0,w_216,l_1,f_jpg,q_80",
      "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_216,l_1,f_jpg,q_80",
      "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
      "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
      "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
      "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg@s_0,w_216,l_1,f_jpg,q_80",
      "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_216,l_1,f_jpg,q_80",
      "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
      "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
      "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
      "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg@s_0,w_216,l_1,f_jpg,q_80",
      "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_216,l_1,f_jpg,q_80",
    ]
  },
  "body": {
    "type": "button",
    "label": "图片集",
    "actionType": "dialog",
    "dialog": {
      "title": "图片集",
      "size": "lg",
      "actions": [],
      "body": {
        "type": "images",
        "source": "${imageList}"
      }
    }
  }
}
```

### 穿梭弹窗

```schema
{
  "type": "page",
  "body": {
    "type": "button",
    "label": "穿梭弹窗",
    "actionType": "dialog",
    "dialog": {
      "title": "穿梭弹窗",
      "size": "lg",
      "body": {
        "type": "form",
        "mode": "normal",
        "api": "/api/mock2/form/saveForm",
        "body": [
          {
            "label": "组合穿梭器",
            "type": "tabs-transfer",
            "name": "a",
            "sortable": true,
            "selectMode": "tree",
            "options": [
              {
                "label": "成员",
                "selectMode": "tree",
                "searchable": true,
                "children": [
                  {
                    "label": "法师",
                    "children": [
                      {
                        "label": "诸葛亮",
                        "value": "zhugeliang"
                      }
                    ]
                  },
                  {
                    "label": "战士",
                    "children": [
                      {
                        "label": "曹操",
                        "value": "caocao"
                      },
                      {
                        "label": "钟无艳",
                        "value": "zhongwuyan"
                      }
                    ]
                  },
                  {
                    "label": "打野",
                    "children": [
                      {
                        "label": "李白",
                        "value": "libai"
                      },
                      {
                        "label": "韩信",
                        "value": "hanxin"
                      },
                      {
                        "label": "云中君",
                        "value": "yunzhongjun"
                      }
                    ]
                  }
                ]
              },
              {
                "label": "用户",
                "selectMode": "chained",
                "children": [
                  {
                    "label": "法师",
                    "children": [
                      {
                        "label": "诸葛亮",
                        "value": "zhugeliang2"
                      }
                    ]
                  },
                  {
                    "label": "战士",
                    "children": [
                      {
                        "label": "曹操",
                        "value": "caocao2"
                      },
                      {
                        "label": "钟无艳",
                        "value": "zhongwuyan2"
                      }
                    ]
                  },
                  {
                    "label": "打野",
                    "children": [
                      {
                        "label": "李白",
                        "value": "libai2"
                      },
                      {
                        "label": "韩信",
                        "value": "hanxin2"
                      },
                      {
                        "label": "云中君",
                        "value": "yunzhongjun2"
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    }
  }
}
```

### 执行日志

```schema
{
  "type": "page",
  "body": {
    "type": "button",
    "label": "执行日志",
    "actionType": "dialog",
    "dialog": {
      "title": "执行日志",
      "size": "lg",
      "body": [
        {
          "type": "flex",
          "standardMode": true,
          "gap": true,
          "direction": "column",
          "items": [
            {
              "type": "alert",
              "title": "提示类标题",
              "body": "提示类文案",
              "level": "info",
              "showIcon": true,
            },
            {
              "type": "code",
              "language": "typescript",
              "tagSize": 4,
              "wordWrap": false,
              "value": `2024-12-12 dsafdsafdsafdsdsa7fdsa9f78ds6a78fds7a8f90ds7afdsfdsafds
2024-12-12 afy8dsa7fds8a90f7ds9a7fdskjlahfjkdoshafjkdhsakfhdsakhfds
2024-12-12 khfjkdshakflhdskfhdsjhfjdksafds
2024-12-12 dsfds89af78dsaf6dsa9fdsaf8dsa7f89ds7af0dsa890f7dsa
2024-12-12 dsafds789fdsa89fdsf9ds0a7f89dsafjkdsahfdsafdsaidsaf
2024-12-12 dsfdsaf97d8s9a7f98dsaf89ds7a89f7ds89af`,
              "editorTheme": "vs-dark"
            }
          ]
        }
      ]
    }
  }
}
```

### 文件上传

```schema
{
  "type": "page",
  "body": {
    "type": "button",
    "label": "文件上传",
    "actionType": "dialog",
    "dialog": {
      "showErrorMsg": false,
      "title": "文件上传",
      "showCloseButton": false,
      "body": {
        "type": "form",
        "api": "/api/mock2/form/saveForm?waitSeconds=2",
        "data": {
          "fileCount": 50
        },
        "body": [
          {
            "type": "group",
            "body": [
              {
                "type": "radios",
                "name": "radios",
                "required": true,
                "label": "选择填充方式",
                "value": "file",
                "options": [
                  {
                    "label": "",
                    "value": "file",
                    "extraLabel": {
                      "type": "wrapper",
                      "size": "none",
                      "body": [
                        {
                          "type": "tpl",
                          "tpl": "导入文件"
                        },
                        {
                          "type": "remark",
                          "content": "请上传符合规范的文件类型"
                        }
                      ]
                    }
                  }
                ]
              }
            ]
          },
          {
            "type": "group",
            "body": [
              {
                "type": "flex",
                "gap": true,
                "items": [
                  {
                    "type": "input-file",
                    "name": "file",
                    "label": "上传文件",
                    "hideUploadButton": true,
                    "required": true,
                    "level": "primary",
                    "receiver": "/api/upload/file"
                  },
                  {
                    "type": "button",
                    "level": "link",
                    "linkWithoutPadding": true,
                    "label": "下载模版"
                  }
                ]
              }
            ]
          }
        ]
      }
    }
  }
}
```

### 左右布局

```schema
{
  "type": "page",
  "body": {
    "type": "button",
    "label": "左右布局",
    "actionType": "dialog",
    "dialog": {
      "title": "左右布局",
      "size": "xl",
      "body": {
        "type": "form",
        "body": {
          "type": "left-right-container",
          "leftWidth": 200,
          "draggable": false,
          "left": {
            "type": "input-tree",
            "label": false,
            "searchable": true,
            "autoFillHeight": true,
            "name": "tree2",
            "multiple": false,
            "autoCheckChildren": false,
            "options": [
              {
                "label": "A",
                "value": "a"
              },
              {
                "label": "B",
                "value": "b",
                "children": [
                  {
                    "label": "B-1",
                    "value": "b-1"
                  },
                  {
                    "label": "B-2",
                    "value": "b-2"
                  },
                  {
                    "label": "B-3",
                    "value": "b-3"
                  }
                ]
              },
              {
                "label": "B",
                "value": "b",
                "children": [
                  {
                    "label": "B-1",
                    "value": "b-1"
                  },
                  {
                    "label": "B-2",
                    "value": "b-2"
                  },
                  {
                    "label": "B-3",
                    "value": "b-3"
                  }
                ]
              },
              {
                "label": "B",
                "value": "b",
                "children": [
                  {
                    "label": "B-1",
                    "value": "b-1"
                  },
                  {
                    "label": "B-2",
                    "value": "b-2"
                  },
                  {
                    "label": "B-3",
                    "value": "b-3"
                  }
                ]
              },
              {
                "label": "B",
                "value": "b",
                "children": [
                  {
                    "label": "B-1",
                    "value": "b-1"
                  },
                  {
                    "label": "B-2",
                    "value": "b-2"
                  },
                  {
                    "label": "B-3",
                    "value": "b-3"
                  }
                ]
              },
              {
                "label": "B",
                "value": "b",
                "children": [
                  {
                    "label": "B-1",
                    "value": "b-1"
                  },
                  {
                    "label": "B-2",
                    "value": "b-2"
                  },
                  {
                    "label": "B-3",
                    "value": "b-3"
                  }
                ]
              },
              {
                "label": "B",
                "value": "b",
                "children": [
                  {
                    "label": "B-1",
                    "value": "b-1"
                  },
                  {
                    "label": "B-2",
                    "value": "b-2"
                  },
                  {
                    "label": "B-3",
                    "value": "b-3"
                  }
                ]
              },
              {
                "label": "B",
                "value": "b",
                "children": [
                  {
                    "label": "B-1",
                    "value": "b-1"
                  },
                  {
                    "label": "B-2",
                    "value": "b-2"
                  },
                  {
                    "label": "B-3",
                    "value": "b-3"
                  }
                ]
              },
              {
                "label": "B",
                "value": "b",
                "children": [
                  {
                    "label": "B-1",
                    "value": "b-1"
                  },
                  {
                    "label": "B-2",
                    "value": "b-2"
                  },
                  {
                    "label": "B-3",
                    "value": "b-3"
                  }
                ]
              },
              {
                "label": "B",
                "value": "b",
                "children": [
                  {
                    "label": "B-1",
                    "value": "b-1"
                  },
                  {
                    "label": "B-2",
                    "value": "b-2"
                  },
                  {
                    "label": "B-3",
                    "value": "b-3"
                  }
                ]
              },
              {
                "label": "C",
                "value": "c"
              }
            ],
            "onEvent": {
              "change": {
                "actions": [
                  {
                    "actionType": "query",
                    "componentId": "right-crud",
                    "args": {
                      "queryParams": {
                        "tree2": "${event.data.value}"
                      }
                    }
                  }
                ]
              }
            }
          },
          "right": {
            "type": "crud",
            "syncLocation": false,
            "columnsTogglable": false,
            "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
            "id": "right-crud",
            "name": "right-crud",
            "autoGenerateFilter": {
              "showBtnToolbar": false,
              "defaultExpanded": false
            },
            "unsetQueryParams": [
              "tree2"
            ],
            "columns": [
              {
                "name": "id",
                "label": "ID",
                "searchable": {
                  "type": "input-text",
                  "name": "id",
                  "label": "主键",
                  "placeholder": "输入id"
                }
              },
              {
                "name": "id",
                "label": "序号"
              },
              {
                "name": "engine",
                "label": "Rendering engine",
                "searchable": {
                  "type": "input-text",
                  "name": "engine",
                  "label": "Rendering engine",
                  "placeholder": "输入Rendering engine"
                }
              },
              {
                "name": "browser",
                "label": "Browser",
                "searchable": {
                  "type": "input-text",
                  "name": "browser",
                  "label": "Browser",
                  "placeholder": "输入Browser"
                }
              },
              {
                "name": "platform",
                "label": "Platform(s)"
              },
              {
                "name": "version",
                "label": "Engine version"
              },
              {
                "name": "grade",
                "label": "CSS grade"
              },
              {
                "type": "operation",
                "label": "操作 ",
                "buttons": [
                  {
                    "label": "详情",
                    "type": "button",
                    "level": "link",
                    "actionType": "dialog",
                    "dialog": {
                      "title": "详情",
                      "showCloseButton": false,
                      "body": "这是个简单的弹框。"
                    }
                  },
                  {
                    "label": "删除",
                    "type": "button",
                    "actionType": "ajax",
                    "level": "link",
                    "disabled": true,
                    "confirmText": "确认要删除吗？",
                    "api": {
                      "method": "delete",
                      "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
                    }
                  },
                  {
                    "label": "编辑",
                    "type": "button",
                    "level": "link",
                    "actionType": "dialog",
                    "dialog": {
                      "title": "编辑",
                      "showCloseButton": false,
                      "body": "这是个简单的弹框。"
                    }
                  },
                  {
                    "label": "空跑",
                    "type": "button",
                    "level": "link",
                    "actionType": "dialog",
                    "dialog": {
                      "title": "空跑",
                      "showCloseButton": false,
                      "body": "这是个简单的弹框。"
                    }
                  }
                ]
              }
            ]
          }
        }
      }
    }
  }
}
```

### 卡片列表

```schema
{
  "type": "page",
  "data": {
    "items2": [
      {
        "title": "复合指标",
        "subTitle": "多个基础指标或派生指标的四则运算，如：人均交易额=交易金额/交易用户数",
        "avatar": "../../../examples/static/card-demo3.png"
      },
      {
        "title": "基础指标",
        "subTitle": "基于业务度量字段进行聚合，如：交易金额",
        "avatar": "../../../examples/static/card-demo3.png"
      },
      {
        "title": "派生指标",
        "subTitle": "基于基础指标、时间限定、业务限定和衍生方式定义的指标",
        "avatar": "../../../examples/static/card-demo3.png"
      }
    ],
    "itemLists": [
      {
        "title": "资方额度"
      },
      {
        "title": "对客额度上限"
      },
      {
        "title": "主标题"
      },
      {
        "title": "主标题"
      },
      {
        "title": "主标题"
      },
      {
        "title": "主标题423424撒大大的大萨达撒"
      }
    ]
  },
  "body": {
    "type": "flex",
    "justify": "start",
    "gap": true,
    "items": [
      {
        "type": "button",
        "label": "卡片列表",
        "actionType": "dialog",
        "dialog": {
          "title": "卡片列表",
          "actions": [],
          "size": "lg",
          "body": {
            "type": "cards",
            "columnsCount": 3,
            "source": "${items2}",
            "card": {
              "header": {
                "title": "${title}",
                "subTitle": "${subTitle}",
                "avatar": "${avatar}"
              },
              "itemAction": {
                "type": "button",
                "actionType": "url",
                "url": "/dataseeddesigndocui/#/amis/zh-CN/components/card",
                "blank": true
              }
            }
          }
        }
      },
      {
        "type": "button",
        "label": "弹窗跳转带收藏功能",
        "actionType": "dialog",
        "dialog": {
          "title": "弹窗跳转带收藏功能",
          "actions": [],
          "size": "lg",
          "body": {
            "type": "group-container",
            "items": [
              {
                "type": "panel",
                "body": [
                  {
                    "type": "flex",
                    "justify": "flex-start",
                    "alignItems": "flex-start",
                    "items": [
                      {
                        "type": "tpl",
                        "tpl": "我收藏的："
                      },
                      {
                        "type": "link",
                        "href": "http://moka.dmz.dev.caijj.net/dataseeddesigndocui/#/amis/zh-CN/components/card",
                        "body": "资方额度"
                      }
                    ]
                  }
                ]
              },
              {
                "type": "panel",
                "header": {
                  "title": "商务条件调整"
                },
                "body": [
                  {
                    "type": "cards",
                    "columnsCount": 5,
                    "source": "${itemLists}",
                    "card": {
                      "header": {
                        "title": "${title}"
                      },
                      "toolbar": [
                        {
                          "type": "button",
                          "icon": "fa fa-star-o",
                          "actionType": "dialog",
                          "level": "link",
                          "linkWithoutPadding": true,
                          "dialog": {
                            "title": "操作",
                            "body": "你正在编辑该卡片"
                          }
                        }
                      ],
                      "itemAction": {
                        "type": "button",
                        "actionType": "url",
                        "url": "/dataseeddesigndocui/#/amis/zh-CN/components/card",
                        "blank": true
                      }
                    }
                  }
                ]
              },
              {
                "type": "panel",
                "header": {
                  "title": "产品要素调整"
                },
                "body": [
                  {
                    "type": "cards",
                    "columnsCount": 5,
                    "source": "${itemLists}",
                    "card": {
                      "header": {
                        "title": "${title}"
                      },
                      "toolbar": [
                        {
                          "type": "button",
                          "icon": "fa fa-star-o",
                          "actionType": "dialog",
                          "level": "link",
                          "linkWithoutPadding": true,
                          "dialog": {
                            "title": "操作",
                            "body": "你正在编辑该卡片"
                          }
                        }
                      ],
                      "itemAction": {
                        "type": "button",
                        "actionType": "url",
                        "url": "/dataseeddesigndocui/#/amis/zh-CN/components/card",
                        "blank": true
                      }
                    }
                  }
                ]
              }
            ]
          }
        }
      }
    ]
  }
}
```

## 组件用法

### 基本用法

```schema
{
  "type": "page",
  "body": [
    {
      "type": "button-toolbar",
      "buttons": [
        {
          "label": "提示弹框",
          "type": "button",
          "actionType": "dialog",
          "dialog": {
            "title": "弹框标题",
            "body": "这是一个弹框",
            "actions": []
          }
        },
        {
          "label": "点击弹框",
          "type": "button",
          "actionType": "dialog",
          "dialog": {
            "showCloseButton": false,
            "title": "弹框标题",
            "body": {
              "type": "form",
              "body": [
                {
                  "type": "input-text",
                  "label": "名字",
                  "name": "username"
                }
              ]
            }
          }
        }
      ]
    }
  ]
}
```

### 配置尺寸

```schema: scope="body"
{
    "type": "button-toolbar",
    "buttons": [
        {
            "type": "button",
            "label": "较小的弹框",
            "actionType": "dialog",
            "dialog": {
                "showCloseButton": false,
                "size": "sm",
                "title": "提示",
                "body": "这是个简单的弹框"
            }
        },
        {
            "type": "button",
            "label": "标准弹框",
            "actionType": "dialog",
            "dialog": {
                "showCloseButton": false,
                "title": "提示",
                "body": "这是个简单的弹框"
            }
        },
        {
            "type": "button",
            "label": "较大的弹框",
            "actionType": "dialog",
            "dialog": {
                "showCloseButton": false,
                "size": "lg",
                "title": "提示",
                "body": "这是个简单的弹框"
            }
        },
        {
            "type": "button",
            "label": "很大的弹框",
            "actionType": "dialog",
            "dialog": {
                "showCloseButton": false,
                "size": "xl",
                "title": "提示",
                "body": "这是个简单的弹框"
            }
        },
        {
            "type": "button",
            "label": "占满屏幕的弹框",
            "actionType": "dialog",
            "dialog": {
                "showCloseButton": false,
                "size": "full",
                "title": "全屏弹框",
                "body": "弹框尽可能占满，内容部分滚动。"
            }
        }
    ]
}
```

### 弹框与数据映射

默认弹框内由于数据链的存在，会自动映射父级同名变量，例如下例：

```schema: scope="body"
{
  "type": "crud",
  "api": "/api/mock2/sample",
  "draggable": true,
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "engine",
      "label": "Rendering engine"
    },
    {
      "name": "browser",
      "label": "Browser"
    },
    {
      "name": "platform",
      "label": "Platform(s)"
    },
    {
      "name": "version",
      "label": "Engine version"
    },
    {
      "name": "grade",
      "label": "CSS grade"
    },
    {
      "type": "button",
      "label": "一个弹框",
      "actionType": "dialog",
      "dialog": {
        "showCloseButton": false,
        "title": "一个弹框",
        "body": [
          {
            "type": "form",
            "api": "/api/mock2/sample/$id",
            "body": [
              {
                "type": "input-text",
                "name": "engine",
                "label": "Engine"
              }
            ]
          }
        ]
      }
    }
  ]
}
```

上例弹框中的表单项 `Engine` 会自动映射到父级数据中的 `engine` 变量，如果想调整当前特性，如你想调整父级映射变量的字段，可以利用[数据映射](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/data-mapping)，例如：

```schema
{
  "type": "page",
  "body": {
    "type": "crud",
    "api": "/api/mock2/sample",
    "draggable": true,
    "syncLocation": false,
    "filter": {
      "body": [
        {
          "type": "select",
          "label": "状态",
          "name": "status",
          "options": [
            {
              "label": "运行中",
              "value": "running"
            },
            {
              "label": "创建中",
              "value": "creating"
            }
          ],
          "checkAll": true,
          "multiple": true,
          "joinValues": true,
          "defaultCheckAll": true,
          "checkAllLabel": "全选",
          "valuesNoWrap": false
        }
      ]
    },
    "headerToolbar": [
      {
        "label": "新增",
        "type": "button",
        "actionType": "dialog",
        "level": "primary",
        "dialog": {
          "type": "dialog",
          "title": "新增",
          "closeOnEsc": false,
          "closeOnOutside": false,
          "showCloseButton": false,
          "data": {
            "&": "\$\$",
            "status": "__undefined"
          },
          "body": [
            {
              "type": "form",
              "api": "/api/mock2/form/saveForm",
              "debug": true,
              "body": [
                {
                  "type": "select",
                  "name": "status",
                  "label": "状态",
                  "options": [
                    {
                      "label": "运行中",
                      "value": "running"
                    },
                    {
                      "label": "创建中",
                      "value": "creating"
                    }
                  ],
                  "disabled": false,
                  "selectFirst": false
                }
              ]
            }
          ]
        }
      }
    ],
    "columns": [
      {
        "name": "id",
        "label": "ID"
      },
      {
        "name": "engine",
        "label": "Rendering engine"
      },
      {
        "name": "browser",
        "label": "Browser"
      },
      {
        "name": "platform",
        "label": "Platform(s)"
      },
      {
        "name": "version",
        "label": "Engine version"
      },
      {
        "name": "grade",
        "label": "CSS grade"
      },
      {
        "type": "button",
        "label": "一个弹框",
        "actionType": "dialog",
        "dialog": {
          "data": {
            "engine2": "${engine}"
          },
          "showCloseButton": false,
          "title": "一个弹框",
          "body": [
            {
              "type": "form",
              "api": "/api/mock2/sample/$id",
              "body": [
                {
                  "type": "input-text",
                  "name": "engine2",
                  "label": "Engine"
                }
              ]
            }
          ]
        }
      }
    ]
  }
}
```

上例给 `dialog` 中配置 `data` 属性，可以将上层的 `engine` 变量映射为 `engine2`。请注意点击“新增”按钮后 dialog 内 form 的数据域会直接继承 CRUD 所在的数据域，如果 CRUD 过滤器中查询字段和 dialog 表单中的字段相同时，会错误的将外部数据映射到表单数据域内，需要配置数据映射将相关字段绑定的数据删除`{"&": "$$", "status": "__undefined"}`

### 多级弹框

```schema: scope="body"
{
    "type": "button",
    "label": "多级弹框",
    "actionType": "dialog",
    "dialog": {
        "showCloseButton": false,
        "title": "提示",
        "body": "这是个简单的弹框",
        "actions": [
            {
                "type": "button",
                "actionType": "cancel",
                "label": "取消"
            },
            {
                "type": "button",
                "actionType": "confirm",
                "label": "确认"
            },
            {
                "type": "button",
                "actionType": "dialog",
                "label": "再弹一个",
                "primary": true,
                "dialog": {
                    "title": "弹框中的弹框",
                    "body": "如果你想，可以无限弹下去",
                    "actions": [
                        {
                            "type": "button",
                            "actionType": "dialog",
                            "label": "来吧",
                            "level": "info",
                            "dialog": {
                                "title": "弹框中的弹框",
                                "body": "如果你想，可以无限弹下去",
                                "actions": [
                                    {
                                        "type": "button",
                                        "actionType": "confirm",
                                        "label": "不弹了",
                                        "primary": true
                                    }
                                ]
                            }
                        }
                    ]
                }
            }
        ]
    }
}
```

### 行为后关闭弹框

在弹框中配置行为按钮，可以在按钮上配置`"close": true`，在行为完成后，关闭当前弹框。

```schema: scope="body"
{
    "type": "button",
    "label": "弹个框",
    "actionType": "dialog",
    "dialog": {
        "showCloseButton": false,
        "title": "弹框",
        "body": [
          {
            "type": "button",
            "label": "默认的 ajax 请求",
            "actionType": "ajax",
            "api": "/api/mock2/form/saveForm?waitSeconds=1"
          },
          {
            "type": "button",
            "label": "ajax 请求成功后关闭弹框",
            "actionType": "ajax",
            "api": "/api/mock2/form/saveForm?waitSeconds=1",
            "close": true
          }
        ]
    }
}
```

### 配置弹窗的按钮

可以通过设置 `actions` 来控制弹窗中的按钮。

```schema: scope="body"
{
    "type": "button-toolbar",
    "buttons": [
        {
            "type": "button",
            "label": "无按钮",
            "actionType": "dialog",
            "dialog": {
                "title": "提示",
                "actions": [],
                "body": "无按钮的弹框"
            }
        },
        {
            "type": "button",
            "label": "底部有两个按钮",
            "actionType": "dialog",
            "dialog": {
                "showCloseButton": false,
                "title": "提示",
                "actions": [{
                  "type": "button",
                  "actionType": "cancel",
                  "label": "取消"
                }, {
                  "type": "button",
                  "actionType": "confirm",
                  "label": "保存",
                  "primary": true
                }],
                "body": "底部有两个按钮的弹框"
            }
        }
    ]
}
```

### 弹框中配置表单

### 基本使用

```schema: scope="body"
{
    "type": "button",
    "label": "弹个表单",
    "actionType": "dialog",
    "dialog": {
        "showCloseButton": false,
        "title": "在弹框中的表单",
        "body": {
            "type": "form",
            "api": "/api/mock2/form/saveForm?waitSeconds=2",
            "body": [
                {
                    "type": "input-text",
                    "name": "username",
                    "required": true,
                    "placeholder": "请输入用户名",
                    "label": "用户名"
                },
                {
                    "type": "input-password",
                    "name": "password",
                    "label": "密码",
                    "required": true,
                    "placeholder": "请输入密码"
                },
                {
                    "type": "checkbox",
                    "name": "rememberMe",
                    "label": "记住登录"
                }
            ]
        }
    }
}
```

### 提交表单 或 ajax 请求

弹框中通过配置`form`或`ajax`行为按钮，可以实现`form`提交和`ajax`请求操作。

```schema: scope="body"
{
    "type": "button",
    "label": "弹个表单",
    "actionType": "dialog",
    "dialog": {
        "showCloseButton": false,
        "title": "在弹框中的表单",
        "actions": [
            {
                "label": "取消",
                "actionType": "cancel",
                "type": "button"
            },
            {
                "label": "提交表单",
                "actionType": "submit",
                "primary": true,
                "type": "button"
            },
            {
                "label": "ajax请求",
                "actionType": "ajax",
                "primary": true,
                "type": "button",
                "api": "/api/mock2/form/saveForm?waitSeconds=2"
            }
        ],
        "body": {
            "type": "form",
            "api": "/api/mock2/form/saveForm?waitSeconds=2",
            "body": [
                {
                    "type": "input-text",
                    "name": "text",
                    "required": true,
                    "label": "用户名",
                    "placeholder": "请输入用户名"
                },
                {
                    "type": "input-password",
                    "name": "password",
                    "label": "密码",
                    "placeholder": "请输入密码",
                    "required": true
                },
                {
                    "type": "checkbox",
                    "name": "rememberMe",
                    "label": "记住登录"
                }
            ]
        }
    }
}
```

### 提交表单 或 ajax 请求后不关闭弹框

默认情况下，当弹框中配置了 form 并进行了**提交表单操作（confirm）**或进行了**`ajax`请求**，请求成功后，会自动关闭当前弹框，你可以通过手动设置`"close": false` 来禁止该默认特性。

```schema: scope="body"
{
    "type": "button",
    "label": "弹个表单",
    "actionType": "dialog",
    "dialog": {
        "title": "在弹框中的表单",
        "actions": [
            {
                "label": "提交表单后不关闭",
                "actionType": "submit",
                "close": false,
                "primary": true,
                "type": "button"
            },
            {
                "label": "ajax请求后不关闭",
                "actionType": "ajax",
                "primary": true,
                "type": "button",
                "api": "/api/mock2/form/saveForm?waitSeconds=2"
            }
        ],
        "body": {
            "type": "form",
            "api": "/api/mock2/form/saveForm?waitSeconds=2",
            "body": [
                {
                    "type": "input-text",
                    "name": "text",
                    "required": true,
                    "label": "用户名",
                    "placeholder": "请输入用户名"
                },
                {
                    "type": "input-password",
                    "name": "password",
                    "label": "密码",
                    "required": true,
                    "placeholder": "请输入密码"
                },
                {
                    "type": "checkbox",
                    "name": "rememberMe",
                    "label": "记住登录"
                }
            ]
        }
    }
}
```

### feedback 反馈弹框

feedback 反馈弹框是指，在 ajax 请求后，可以显示一个弹框，进行反馈操作

#### feedback 基本使用

```schema: scope="body"
{
    "type": "button",
    "label": "Feedback",
    "actionType": "ajax",
    "api": "/api/mock2/form/initData?waitSeconds=2",
    "tooltip": "点击我后会发送一个请求，请求回来后，弹出一个框。",
    "feedback": {
        "title": "操作成功",
        "body": "xxx 已操作成功"
    }
}
```

#### 弹框中配置 feedback

##### 关闭 feedback 弹框时，同时关闭上层弹框

当你在弹框中配置了 feedback，操作之后，你希望关闭当前 feedback 弹框同时，关闭上层的弹框，具体有两种方式

###### 1. 不请求接口，直接关闭

`feedback`的`actions`中配置 `"actionType": "close"` 的按钮

```schema: scope="body"
{
  "type": "button",
  "label": "弹一个框",
  "actionType": "dialog",
  "dialog": {
    "title": "提示",
     "body": {
            "type": "form",
            "api": "/api/mock2/form/saveForm?waitSeconds=2",
            "body": [
                {
                    "type": "input-text",
                    "name": "text",
                    "required": true,
                    "label": "用户名",
                    "placeholder": "请输入用户名"
                },
                {
                    "type": "input-password",
                    "name": "password",
                    "label": "密码",
                    "required": true,
                    "placeholder": "请输入密码"
                },
                {
                    "type": "checkbox",
                    "name": "rememberMe",
                    "label": "记住登录"
                }
            ]
        },
    "actions": [
      {
        "type": "button",
        "label": "提交表单 Feedback",
        "actionType": "confirm",
        "feedback": {
          "body": "feedback弹框中，不请求接口了，直接点击按钮关闭",
          "actions": [
            {
              "type": "button",
              "actionType": "close",
              "label": "关闭"
            }
          ]
        }
      },
      {
        "type": "button",
        "label": "ajax请求 Feedback",
        "actionType": "ajax",
        "close": true,
        "api": "/api/mock2/form/initData?waitSeconds=1",
        "feedback": {
          "body": "feedback弹框中，不请求接口了，直接点击按钮关闭",
          "actions": [
            {
              "type": "button",
              "actionType": "close",
              "label": "关闭"
            }
          ]
        }
      }
    ]
  }
}
```

###### 2. 请求接口，请求成功后，关闭所有弹框

需要在 feedback 的 `body` 中添加 Form 组件，并配置`"actionType": "confirm"`，

```schema: scope="body"
{
  "type": "button",
  "label": "弹一个框",
  "actionType": "dialog",
  "dialog": {
    "body": {
        "type": "form",
        "api": "/api/mock2/form/saveForm?waitSeconds=2",
        "body": [
            {
                "type": "input-text",
                "name": "text",
                "required": true,
                "label": "用户名",
                "placeholder": "请输入用户名"
            },
            {
                "type": "input-password",
                "name": "password",
                "label": "密码",
                "required": true,
                "placeholder": "请输入密码"
            },
            {
                "type": "checkbox",
                "name": "rememberMe",
                "label": "记住登录"
            }
        ]
    },
    "actions": [
      {
        "type": "button",
        "label": "confirm Feedback",
        "actionType": "confirm",
        "feedback": {
          "body": {
            "type": "form",
            "api": "/api/mock2/form/saveForm?waitSeconds=1",
            "body": [
              {
                "type": "tpl",
                "tpl": "点击确认，请求接口，接口请求成功后，关闭弹框"
              }
            ]
          },
          "actions": [
            {
              "type": "button",
              "actionType": "confirm",
              "label": "确认",
              "primary": true
            }
          ]
        }
      },
      {
        "type": "button",
        "label": "ajax Feedback",
        "actionType": "ajax",
        "close": true,
        "api": "/api/mock2/form/saveForm?waitSeconds=1",
        "feedback": {
          "body": {
            "type": "form",
            "api": "/api/mock2/form/saveForm?waitSeconds=1",
            "body": [
              {
                "type": "tpl",
                "tpl": "点击确认，请求接口，接口请求成功后，关闭弹框"
              }
            ]
          },
          "actions": [
            {
              "type": "button",
              "actionType": "confirm",
              "label": "确认",
              "primary": true
            }
          ]
        }
      }
    ]
  }
}
```

> 注意上面的例子：如果你的触发`feedback`的按钮`actionType`为`ajax`时，为需要额外在按钮上配置`"close": true`

##### 取消 feedback 弹框时，不同时关闭上层弹框

改场景只适用于**不请求接口关闭弹框**的场景，需要在 feedback 层添加`"skipRestOnCancel": true`

```schema: scope="body"
{
  "type": "button",
  "label": "弹一个框",
  "actionType": "dialog",
  "dialog": {
    "title": "提示",
    "body": {
      "type": "form",
      "api": "/api/mock2/form/initData?waitSeconds=1",
      "body": [
        {
          "type": "tpl",
          "tpl": "这是一个简单的弹框"
        }
      ]
    },
    "actions": [
      {
        "type": "button",
        "actionType": "confirm",
        "label": "Feedback",
        "feedback": {
          "body": "这是一个feedback弹框",
          "skipRestOnCancel": true,
          "actions": [
            {
              "type": "button",
              "actionType": "close",
              "label": "关闭"
            }
          ]
        }
      }
    ]
  }
}
```

##### 确认 feedback 弹框时，不同时关闭上层弹框

如果想让 feedback 弹框确认后，让之前触发这个 feedback 的按钮中断，那么需要配置 `skipRestOnConfirm`，这就意味着之前触发这个 feedback 的按钮必须重新提交一次。

#### 根据条件显示 feedback

可以根据条件弹出，例如下面这个例子，只有当接口返回的时间戳可以整除 2 时才显示弹框。

```schema: scope="body"
{
    "type": "button",
    "label": "条件feedback",
    "actionType": "ajax",
    "api": "/api/mock2/form/initData?waitSeconds=1",
    "feedback": {
        "visibleOn": "!(this.date % 2)",
        "title": "操作成功",
        "body": "当前时间戳: <code>${date}</code>"
    }
}
```

### 配置 Esc 键和点击外部关闭弹框

可以通过配置 `closeOnEsc` 和 `closeOnOutside` 支持用 esc 键和点击其它区域关闭弹框。

```schema: scope="body"
{
    "label": "点击弹框",
    "type": "button",
    "actionType": "dialog",
    "dialog": {
      "closeOnEsc": true,
      "closeOnOutside": true,
      "title": "弹框标题",
      "body": [
        {
        "type": "input-text",
        "label": "更复杂的标签提示",
        "labelRemark": {
          "trigger": ["click"],
          "type": "remark",
          "title": "提示",
          "content": "<pre>first \nsecond\n${text1}</pre>"
        },
        "name": "text3"
      },
        {
          "label": "点击弹框2",
          "type": "button",
          "actionType": "dialog",
          "dialog": {
            "closeOnEsc": true,
            "closeOnOutside": true,
            "title": "弹框标题2",
            "body": "内容"
          }
        }
      ]
    }
}
```

### 是否展示关闭按钮

配置`"showCloseButton": false`，可以隐藏关闭按钮

```schema: scope="body"
{
    "type": "button",
    "label": "无关闭按钮",
    "actionType": "dialog",
    "dialog": {
      "title": "弹框标题",
      "body": "这是个简单的弹框",
      "showCloseButton": false
    }
}
```

`showCloseButton` 支持配置表达式

```schema: scope="body"
[{
    "label": "点击弹框",
    "type": "button",
    "actionType": "dialog",
    "dialog": {
      "data": {
        "show": true
      },
      "showCloseButton": "${!show}",
      "title": "弹框标题",
      "body": {
        "type": "form",
        "body": [{
          "type": "input-text",
          "label": "名字",
          "name": "username"
        }],
        "actions": [
          {
            "label": "取消",
            "actionType": "cancel",
            "type": "button",
            "visibleOn": "${show}"
          },
          {
            "label": "确定",
            "actionType": "submit",
            "primary": true,
            "type": "button",
            "visibleOn": "${show}"
          }
        ]
      }
    }
}]
```

### 配置 打开指定的弹窗

> 关闭当前弹窗并打开下一个弹窗，onEvent 只需要配置 1 个 action：actionType = "cancel"（下一个弹窗是配置在 onEvent 同一个层级）

> 关闭当前弹窗并打开上一个弹窗，onEvent 需要配置 2 个 action：actionType = "cancel" 和 actionType = "open"

```schema
{
  "type": "page",
  "data": {
    "d0": "0",
    "destroyOnClose": false
  },
  "body": [
    {
      "name": "button",
      "type": "button",
      "label": "点击弹框",
      "actionType": "dialog",
      "dialog": {
        "id": "dialog0",
        "name": "dialog0",
        "size": "xs",
        "title": "当前是第一个弹窗",
        "destroyOnClose": "${destroyOnClose}",
        "actions": [
          {
            "type": "button",
            "actionType": "confirm",
            "label": "确认",
            "primary": true
          },
          {
            "type": "button",
            "actionType": "dialog",
            "label": "触发第二个弹窗",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "actionType": "cancel",
                    "componentId": "dialog0",
                  }
                ]
              }
            },
            "dialog": {
              "id": "dialog1",
              "name": "dialog1",
              "destroyOnClose": false,
              "title": "当前是第二个弹窗",
              "body": "如果你想，可以无限弹下去",
              "actions": [
                {
                  "type": "button",
                  "actionType": "dialog",
                  "label": "触发第三个弹窗",
                  "level": "info",
                  "onEvent": {
                    "click": {
                      "actions": [
                        {
                          "actionType": "cancel",
                          "componentId": "dialog1",
                        }
                      ]
                    }
                  },
                  "dialog": {
                    "id": "dialog2",
                    "name": "dialog2",
                    "title": "当前是第三个弹窗",
                    "body": "如果你想，可以无限弹下去",
                    "destroyOnClose": false,
                    "actions": [
                      {
                        "type": "button",
                        "label": "回到第一个",
                        "level": "info",
                        "onEvent": {
                          "click": {
                            "actions": [
                              {
                                "actionType": "cancel",
                                "componentId": "dialog2",
                              },
                              {
                                "actionType": "open",
                                "componentId": "dialog0",
                                "args": {
                                  "source": "dialog2",
                                  "location": "dialog0",
                                  "label": "从第三个回到第一个"
                                }
                              }
                            ]
                          }
                        }
                      },
                      {
                        "type": "button",
                        "label": "回到第二个",
                        "level": "info",
                        "onEvent": {
                          "click": {
                            "actions": [
                              {
                                "actionType": "cancel",
                                "componentId": "dialog2",
                              },
                              {
                                "actionType": "open",
                                "componentId": "dialog1",
                              }
                            ]
                          }
                        }
                      }
                    ]
                  }
                },
                {
                  "type": "button",
                  "label": "回到第一个",
                  "level": "info",
                  "onEvent": {
                    "click": {
                      "actions": [
                        {
                          "actionType": "cancel",
                          "componentId": "dialog1",
                        },
                        {
                          "actionType": "open",
                          "componentId": "dialog0",
                        }
                      ]
                    }
                  }
                }
              ]
            }
          }
        ],
        "body": {
          "type": "form",
          debug: true,
          "api": "/api/mock2/form/saveForm",
          "body": [
            {
              "name": "text-1",
              "type": "input-text",
              "label": "text1",
              "onEvent": {
                "change": {
                  "actions": [
                    {
                      "actionType": "setValue",
                      "componentId": "dialog0",
                      "args": {
                        "value": {
                          "text1": "${text-1}"
                        }
                      }
                    }
                  ]
                }
              }
            },
            {
              "name": "text-2",
              "type": "input-text",
              "label": "text2",
              "onEvent": {
                "change": {
                  "actions": [
                    {
                      "actionType": "setValue",
                      "componentId": "dialog0",
                      "args": {
                        "value": {
                          "text2": "${text-2}"
                        }
                      }
                    }
                  ]
                }
              }
            }
          ]
        }
      }
    }
  ]
}
```

### 属性表

| 属性名          | 类型                                                                              | 默认值             | 说明                                                                                                                         | 版本                    |
| --------------- | --------------------------------------------------------------------------------- | ------------------ | ---------------------------------------------------------------------------------------------------------------------------- | ----------------------- |
| type            | `string`                                                                          |                    | `"dialog"` 指定为 Dialog 渲染器                                                                                              |
| title           | [SchemaNode](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode)             |                    | 弹出层标题                                                                                                                   |
| body            | [SchemaNode](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode)             |                    | 往 Dialog 内容区加内容                                                                                                       |
| size            | `string`                                                                          |                    | 指定 dialog 大小，支持: `xs`、`sm`、`md`、`lg`、`xl`、`full`                                                                 |
| bodyClassName   | `string`                                                                          | `modal-body`       | Dialog body 区域的样式类名                                                                                                   |
| closeOnEsc      | `boolean`                                                                         | `false`            | 是否支持按 `Esc` 关闭 Dialog                                                                                                 |
| showCloseButton | `boolean` \| [表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression) | `true`             | 是否显示右上角的关闭按钮, 支持布尔值和表达式                                                                                 | `1.15.0` 版本支持表达式 |
| showErrorMsg    | `boolean`                                                                         | `true`             | 是否在弹框左下角显示报错信息                                                                                                 |
| showLoading     | `boolean`                                                                         | `true`             | 是否在弹框左下角显示 loading 动画                                                                                            |
| disabled        | `boolean`                                                                         | `false`            | 如果设置此属性，则该 Dialog 只读没有提交操作。                                                                               |
| actions         | Array<[Action](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/action)>           | 【确认】和【取消】 | 如果想不显示底部按钮，可以配置：`[]`                                                                                         |
| data            | `object`                                                                          |                    | 支持[数据映射](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/data-mapping)，如果不设定将默认将触发按钮的上下文中继承数据。 |
| destroyOnClose  | `boolean` or `SchemaTpl`                                                          | `true`             | 设置在弹窗关闭后是否需要卸载弹窗组件，默认是卸载。支持模版变量，当 `destroyOnClose` 设置为 `false` 时，请不要在 `dialog` 上设置 `data`                                                              |
| closeDialogBusying  | `boolean`                                                           | `false`             | 设置关闭Dialog的监听行为时的busying状态记录功能，为了提高性能                                                               |`1.37.0`|
| downPassLoading  | `boolean`                                                           | `true`             | 设置Dialog的`loading`状态是否赋值到`disabled`                                                         |`1.46.0`|
| popOverContainerSelector | `string`                                                                          |                    | 设置弹窗的父级容器，默认为 body，使用querySelector获取                                                                                       | `1.61.0` |

### 事件表

当前组件会对外派发以下事件，可以通过`onEvent`来监听这些事件，并通过`actions`来配置执行的动作，在`actions`中可以通过`${事件参数名}`来获取事件产生的数据，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action)。

> `[name]`为当前数据域中的字段名，例如：当前数据域为 {username: 'amis'}，则可以通过${username}获取对应的值。

| 事件名称 | 事件参数                                                                 | 说明               |
| -------- | ------------------------------------------------------------------------ | ------------------ |
| confirm  | `event.data: object` 弹窗数据<br/>`[name]: any` 当前数据域中指定字段的值 | 点击确认提交时触发 |
| cancel   | `event.data: object` 弹窗数据<br/>`[name]: any` 当前数据域中指定字段的值 | 点击取消时触发     |

### 动作表

当前组件对外暴露以下特性动作，其他组件可以通过指定`actionType: 动作名称`、`componentId: 该组件id`来触发这些动作，动作配置可以通过`args: {动作配置项名称: xxx}`来配置具体的参数，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action#触发其他组件的动作)。

| 动作名称 | 动作配置                   | 说明                                          |
| -------- | -------------------------- | --------------------------------------------- |
| confirm  | -                          | 确认（提交）                                  |
| cancel   | -                          | 取消（关闭）                                  |
| open     | -                          | 打开指定弹窗（前提条件 关闭的弹窗不能被卸载，即 `destroyOnClose` 为 `false`） |
| setValue | `value: object` 更新的数据 | 更新数据                                      |
