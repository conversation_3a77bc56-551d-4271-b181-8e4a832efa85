---
title: DragAndDrop 拖拽
description:
type: 0
group: ⚙ 组件
menuName: DragAndDrop 拖拽
icon:
order: 99
---

## 基本用法

**1.66.0版本支持**

拖拽组件包含两个组件：`DragNode`和`DropContainer`。
其中，`DragNode`用于拖拽，`DropContainer`用于放置。
如下面的例子，将`DragNode`中的内容拖拽到`DropContainer`中，可以在`DropContainer`的事件中接收到拖拽的数据，`DragNode`可以通过`dragData`属性来设置需要被接收的数据。

拖拽组件只提供拖拽功能和事件，本身并不会操作数据和改变视图，具体的拖拽修改数据的行为以及对视图的修改需要自行在事件中处理。

```schema
{
  "type": "page",
  "id": "page",
  "data": {
    "text": "接收拖放的内容"
  },
  "body": [
    {
      "type": "drop-container",
      "className": "bg-blue-100 mb-2",
      "body": "${text|json}",
      "onEvent": {
        "drop": {
          "actions": [
            {
              "actionType": "setValue",
              "componentId": "page",
              "args": {
                "value": {
                  "text": "${event.data.dragData}"
                }
              }
            }
          ]
        }
      }
    },
    {
      "type": "drag-node",
      "dragData": {
        "id": 1
      },
      "body": "可以拖拽的内容"
    }
  ]
}
```

也可以设置两个拖拽区域内容互相拖拽

```schema
{
  "type": "page",
  "id": "page2",
  "data": {
    "arr": [
      {
        "id": 1,
        "text": "标签1"
      },
      {
        "id": 2,
        "text": "标签2"
      },
      {
        "id": 3,
        "text": "标签3"
      }
    ],
    "arr1": []
  },
  "body": [
    {
      "type": "drop-container",
      "className": "p-2 border-solid border-gray-200 border rounded",
      "onEvent": {
        "drop": {
          "actions": [
            {
              "actionType": "setValue",
              "componentId": "page2",
              "args": {
                "value": {
                  "arr": "${IF(ARRAYFIND(${arr}, item => item.id === ${event.data.dragData.id}), ${arr}, CONCAT(${arr}, ${event.data.dragData}))}",
                  "arr1": "${ARRAYFILTER(${arr1}, item => item.id !== ${event.data.dragData.id})}"
                }
              }
            }
          ]
        }
      },
      "body": [
        "容器1",
        {
          "type": "each",
          "name": "arr",
          "items": {
            "type": "drag-node",
            "className": "mr-2",
            "dragData": {
              "id": "${id}",
              "text": "${text}"
            },
            "body": {
              "type": "tag",
              "label": "${text}"
            }
          }
        }
      ]
    },
    {
      "type": "drop-container",
      "className": "mt-2 p-2 border-solid border-gray-200 border rounded",
      "onEvent": {
        "drop": {
          "actions": [
            {
              "actionType": "setValue",
              "componentId": "page2",
              "args": {
                "value": {
                  "arr1": "${IF(ARRAYFIND(${arr1}, item => item.id === ${event.data.dragData.id}), ${arr1}, CONCAT(${arr1}, ${event.data.dragData}))}",
                  "arr": "${ARRAYFILTER(${arr}, item => item.id !== ${event.data.dragData.id})}"
                }
              }
            }
          ]
        }
      },
      "body": [
        "容器2",
        {
          "type": "each",
          "name": "arr1",
          "items": {
            "type": "drag-node",
            "className": "mr-2",
            "dragData": {
              "id": "${id}",
              "text": "${text}"
            },
            "body": {
              "type": "tag",
              "label": "${text}"
            }
          }
        }
      ]
    }
  ]
}
```

## 拖拽类型

当存在多个拖拽节点以及拖放区域时可以通过`dragType`和`dropType`来控制哪些节点能够拖放到哪些区域，如下面的例子，拖拽节点`A`和`B`只能拖拽到`容器1`中，节点`C`和`D`只能拖拽到`容器2`中。

```schema
{
  "type": "page",
  "id": "page1",
  "data": {
    "container1": [],
    "container2": []
  },
  "body": [
    {
      "type": "drag-node",
      "className": "mr-2",
      "dragType": "container1",
      "dragData": {
        "text": "A"
      },
      "body": {
        "type": "tag",
        "label": "节点A"
      },
    },
    {
      "type": "drag-node",
      "className": "mr-2",
      "dragType": "container1",
      "dragData": {
        "text": "B"
      },
      "body": {
        "type": "tag",
        "label": "节点B"
      },
    },
    {
      "type": "drag-node",
      "className": "mr-2",
      "dragType": "container2",
      "dragData": {
        "text": "C"
      },
      "body": {
        "type": "tag",
        "label": "节点C"
      },
    },
    {
      "type": "drag-node",
      "dragType": "container2",
      "dragData": {
        "text": "D"
      },
      "body": {
        "type": "tag",
        "label": "节点D"
      },
    },
    {
      "type": "drop-container",
      "dropType": "container1",
      "className": "bg-blue-100 mt-2 mb-2 p-2",
      "onEvent": {
        "drop": {
          "actions": [
            {
              "actionType": "setValue",
              "componentId": "page1",
              "args": {
                "value": {
                  "container1": "${CONCAT(container1, event.data.dragData)}"
                }
              }
            }
          ]
        },
        "dropFail": {
          "actions": [
            {
              "actionType": "toast",
              "args": {
                "msg": "不能拖放到这里"
              }
            }
          ]
        }
      },
      "body": [
        "容器1",
        {
          "type": "each",
          "name": "container1",
          "items": {
            "type": "tag",
            "label": "${text}"
          }
        }
      ]
    },
    {
      "type": "drop-container",
      "dropType": "container2",
      "className": "bg-blue-100 mb-2 p-2",
      "onEvent": {
        "drop": {
          "actions": [
            {
              "actionType": "setValue",
              "componentId": "page1",
              "args": {
                "value": {
                  "container2": "${CONCAT(container2, event.data.dragData)}"
                }
              }
            }
          ]
        },
        "dropFail": {
          "actions": [
            {
              "actionType": "toast",
              "args": {
                "msg": "不能拖放到这里"
              }
            }
          ]
        }
      },
      "body": [
        "容器2",
        {
          "type": "each",
          "name": "container2",
          "items": {
            "type": "tag",
            "label": "${text}"
          }
        }
      ]
    }
  ]
}
```

也可以通过`canDropClassName` 和 `cannotDropClassName` 来设置一些拖拽过程中的样式，比如节点不可拖拽到容器上时让容器呈现红色背景

```schema
{
  "type": "page",
  "id": "page3",
  "data": {
    "container1": [],
    "container2": []
  },
  "body": [
    {
      "type": "drag-node",
      "className": "mr-2",
      "dragType": "container1",
      "dragData": {
        "text": "A"
      },
      "body": {
        "type": "tag",
        "label": "节点A"
      },
    },
    {
      "type": "drag-node",
      "className": "mr-2",
      "dragType": "container1",
      "dragData": {
        "text": "B"
      },
      "body": {
        "type": "tag",
        "label": "节点B"
      },
    },
    {
      "type": "drag-node",
      "className": "mr-2",
      "dragType": "container2",
      "dragData": {
        "text": "C"
      },
      "body": {
        "type": "tag",
        "label": "节点C"
      },
    },
    {
      "type": "drag-node",
      "dragType": "container2",
      "dragData": {
        "text": "D"
      },
      "body": {
        "type": "tag",
        "label": "节点D"
      },
    },
    {
      "type": "drop-container",
      "dropType": "container1",
      "className": "border-solid border-gray-200 border rounded mt-2 mb-2 p-2",
      "canDropClassName": "bg-success",
      "cannotDropClassName": "bg-danger",
      "onEvent": {
        "drop": {
          "actions": [
            {
              "actionType": "setValue",
              "componentId": "page3",
              "args": {
                "value": {
                  "container1": "${CONCAT(container1, event.data.dragData)}"
                }
              }
            }
          ]
        },
        "dropFail": {
          "actions": [
            {
              "actionType": "toast",
              "args": {
                "msg": "不能拖放到这里"
              }
            }
          ]
        }
      },
      "body": [
        "容器1",
        {
          "type": "each",
          "name": "container1",
          "items": {
            "type": "tag",
            "label": "${text}"
          }
        }
      ]
    },
    {
      "type": "drop-container",
      "dropType": "container2",
      "className": "border-solid border-gray-200 border rounded mb-2 p-2",
      "canDropClassName": "bg-success",
      "cannotDropClassName": "bg-danger",
      "onEvent": {
        "drop": {
          "actions": [
            {
              "actionType": "setValue",
              "componentId": "page3",
              "args": {
                "value": {
                  "container2": "${CONCAT(container2, event.data.dragData)}"
                }
              }
            }
          ]
        },
        "dropFail": {
          "actions": [
            {
              "actionType": "toast",
              "args": {
                "msg": "不能拖放到这里"
              }
            }
          ]
        }
      },
      "body": [
        "容器2",
        {
          "type": "each",
          "name": "container2",
          "items": {
            "type": "tag",
            "label": "${text}"
          }
        }
      ]
    }
  ]
}
```

## 属性表

### DragNode

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| dragType | `string \| string[]` | `"default"` | 拖拽元素的类型，表示可以被哪些类型的容器接收，结合`DropContainer`使用 |
| dragData | `any` | - | 拖拽元素的数据，结合`DropContainer`使用 |
| disabled | `boolean` | `false` | 是否禁用拖拽 |
| isDragingClassName | `string` | - | 拖拽时的类名 |
| isDragingStyle | `Style` | - | 拖拽时的样式 |

### DropContainer

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| dropType | `string \| string[]` | - | 能被拖放元素的类型，表示允许被拖拽进来的节点类型，结合`DropNode`使用 |
| disabled | `boolean` | `false` | 是否禁用拖放 |
| canDropStyle | `Style` | - | 能够拖放时的样式 |
| canDropClassName | `string` | - | 能够拖放时的类名 |
| cannotDropStyle | `Style` | - | 不能拖放时的样式 |
| cannotDropClassName | `string` | - | 不能拖放时的类名 |

## 事件表

### DragNode

| 事件名 | 事件参数 | 说明 |
| --- | --- | --- |
| dragStart | `dragData: any`，拖拽中的数据 | 拖拽开始 |
| dragEnd | `dragData: any`，拖拽中的数据 | 拖拽结束 |

### DropContainer

| 事件名 | 事件参数 | 说明 |
| --- | --- | --- |
| drop | `dragData: any`，拖拽中的数据 | 拖放成功 |
| dropFail | `dragData: any`，拖拽中的数据 | 拖放失败 |
| dragEnter | `dragData: any`，拖拽中的数据 | 拖拽进入 |
| dragLeave | `dragData: any`，拖拽中的数据 | 拖拽离开 |
