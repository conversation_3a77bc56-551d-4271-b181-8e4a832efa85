---
title: Drawer 抽屉
description:
type: 0
group: ⚙ 组件
menuName: Drawer 抽屉
icon:
order: 43
standardMode: true
---
屏幕边缘滑出的浮层面板。

## 场景推荐

### 抽屉+分步向导

抽屉中的内容为分布向导组件所呈现的内容。

```schema
{
  "type": "page",
  "body": {
    "type": "button",
    "label": "抽屉+分步向导",
    "actionType": "drawer",
    "drawer": {
      "showCloseButton": false,
      "title": "抽屉标题",
      "actions": [],
      "body": {
        "type": "wizard",
        "initApi": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm?waitSeconds=2",
        "delimiter": "arrow",
        "steps": [
          {
            "title": "第一步",
            "mode": "horizontal",
            "labelWidth": 40,
            "body": [
              {
                "name": "website",
                "label": "网址",
                "type": "input-url"
              },
              {
                "name": "email",
                "label": "邮箱",
                "type": "input-email"
              }
            ],
            "actions": [
              {
                "type": "button",
                "label": "取消",
                "actionType": "cancel"
              },
              {
                "type": "button",
                "label": "下一步",
                "actionType": "next"
              },
              {
                "type": "button",
                "label": "提交",
                "actionType": "submit",
                "level": "primary"
              }
            ]
          },
          {
            "title": "第二步",
            "mode": "horizontal",
            "labelWidth": 40,
            "body": [
              {
                "name": "email2",
                "label": "邮箱",
                "type": "input-email",
                "required": true
              }
            ],
            "actions": [
              {
                "type": "button",
                "label": "上一步",
                "actionType": "prev"
              },
              {
                "type": "button",
                "label": "下一步",
                "actionType": "next"
              },
              {
                "type": "button",
                "label": "提交",
                "actionType": "submit",
                "level": "primary"
              }
            ]
          },
          {
            "title": "第三步",
            "mode": "horizontal",
            "labelWidth": 40,
            "body": [
              "这是最后一步了"
            ],
            "actions": [
              {
                "type": "button",
                "label": "上一步",
                "actionType": "prev"
              },
              {
                "type": "button",
                "label": "提交",
                "actionType": "submit",
                "level": "primary"
              }
            ]
          }
        ]
      }
    }
  }
}
```

### 抽屉+tab 模式

抽屉中的内容为tab组件所呈现的内容。

```schema
{
  "type": "page",
  "data": {
    "text1": "营销中心",
    "text2": 2,
    "text3": 1593327764,
    "text4": "负责人",
    "text5": "text5",
    "text6": 1593327764,
    "text7": "创建人",
    "text8": "text8",
    "text9": "text9"
  },
  "body": [
    {
      "type": "button",
      "label": "Tab模式",
      "actionType": "drawer",
      "drawer": {
        "position": "right",
        "size": "lg",
        "title": {
          "type": "title",
          "title": "详情",
          "subTitle": "这是小标题",
          "noPadding": true,
        },
        "body": {
          "type": "tabs",
          "tabs": [
            {
              "title": "JSON组件",
              "icon": "fa fa-home",
              "body": [
                {
                  "type": "flex",
                  "direction": "column",
                  "gap": true,
                  "items": [
                    {
                      "type": "button",
                      "label": "复制",
                      "level": "primary"
                    },
                    {
                      "type": "json",
                      "value": {
                        "table": [
                          {
                            "a": "a1",
                            "b": "b1",
                            "id": 1,
                            "children": [
                              {
                                "a": "a1-child1",
                                "b": "b1-child1",
                                "id": "1-1"
                              },
                              {
                                "a": "a1-child2",
                                "b": "b1-child2",
                                "id": "1-2"
                              }
                            ]
                          },
                          {
                            "a": "a2",
                            "b": "b2",
                            "id": 2,
                            "children": [
                              {
                                "a": "a1-child1",
                                "b": "b1-child1",
                                "id": "2-1"
                              },
                              {
                                "a": "a1-child2",
                                "b": "b1-child2",
                                "id": "2-2"
                              }
                            ]
                          },
                          {
                            "a": "a3",
                            "b": "b3",
                            "id": 3,
                            "children": [
                              {
                                "a": "a1-child1",
                                "b": "b1-child1",
                                "id": "3-1"
                              },
                              {
                                "a": "a1-child2",
                                "b": "b1-child2",
                                "id": "3-2"
                              }
                            ]
                          }
                        ]
                      }
                    }
                  ]
                }
              ]
            },
            {
              "title": "列表",
              "icon": "fab fa-apple",
              "body": {
                "type": "crud",
                "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
                "syncLocation": false,
                "columns": [
                  {
                    "name": "id",
                    "label": "ID"
                  },
                  {
                    "name": "engine",
                    "label": "Rendering engine"
                  },
                  {
                    "name": "browser",
                    "label": "Browser"
                  },
                  {
                    "name": "platform",
                    "label": "Platform(s)"
                  },
                  {
                    "name": "version",
                    "label": "Engine version"
                  },
                  {
                    "name": "grade",
                    "label": "CSS grade"
                  },
                  {
                    "type": "operation",
                    "label": "操作",
                    "width": 80,
                    "buttons": [
                      {
                        "label": "详情",
                        "type": "button",
                        "level": "link",
                        "actionType": "dialog",
                        "dialog": {
                          "title": "查看详情",
                          "body": {
                            "type": "form",
                            "body": [
                              {
                                "type": "input-text",
                                "name": "engine",
                                "label": "Engine"
                              },
                              {
                                "type": "input-text",
                                "name": "browser",
                                "label": "Browser"
                              },
                              {
                                "type": "input-text",
                                "name": "platform",
                                "label": "platform"
                              },
                              {
                                "type": "input-text",
                                "name": "version",
                                "label": "version"
                              },
                              {
                                "type": "control",
                                "label": "grade",
                                "body": {
                                  "type": "tag",
                                  "label": "${grade}",
                                  "displayMode": "normal",
                                  "color": "active"
                                }
                              }
                            ]
                          }
                        }
                      },
                      {
                        "label": "删除",
                        "type": "button",
                        "level": "link",
                        "disabledOn": "this.grade === 'A'"
                      }
                    ]
                  }
                ]
              }
            },
            {
              "title": "基础表单",
              "icon": "fas fa-bug",
              "body": {
                "type": "form",
                "title": "",
                "mode": "horizontal",
                "actions": [],
                "labelWidth": 60,
                "wrapWithPanel": false,
                "body": [
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "static",
                        "name": "text1",
                        "label": "归属部门",
                        "columnRatio": 4
                      },
                      {
                        "type": "static-date",
                        "name": "text3",
                        "label": "文本3",
                        "columnRatio": 4
                      },
                      {
                        "type": "static-date",
                        "name": "text3",
                        "label": "文本4",
                        "columnRatio": 4
                      }
                    ]
                  },
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "static",
                        "name": "text4",
                        "label": "负责人",
                        "columnRatio": 4
                      },
                      {
                        "type": "static",
                        "name": "text5",
                        "label": "文本5",
                        "columnRatio": 4
                      },
                      {
                        "type": "static-datetime",
                        "name": "text6",
                        "label": "文本6",
                        "columnRatio": 4
                      }
                    ]
                  },
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "static",
                        "name": "text7",
                        "label": "营销中心",
                        "columnRatio": 4
                      },
                      {
                        "type": "static",
                        "name": "text8",
                        "label": "文本8",
                        "columnRatio": 4
                      },
                      {
                        "type": "static",
                        "name": "text9",
                        "label": "文本9",
                        "columnRatio": 4
                      }
                    ]
                  }
                ]
              }
            }
          ]
        },
        "actions": []
      }
    }
  ]
}
```

## 组件用法
### 基本用法

```schema: scope="body"
{
    "label": "弹出",
    "type": "button",
    "actionType": "drawer",
    "drawer": {
      "showCloseButton": false,
      "title": "抽屉标题",
      "body": {
            "label": "第二层",
            "type": "button",
            "actionType": "drawer",
            "drawer": {
                "title": "抽屉标题",
                "body": {
                    "label": "第三层",
                    "type": "button",
                    "actionType": "drawer",
                    "drawer": {
                        "title": "抽屉标题",
                        "body": "这是第三层抽屉"
                    }
                }
            }
      }
    }
}
```

### 抽屉尺寸

```schema
{
    "type": "page",
    "body": {
        "type": "button-toolbar",
        "buttons": [
            {
                "type": "button",
                "label": "极小框",
                "actionType": "drawer",
                "drawer": {
                    "position": "right",
                    "size": "xs",
                    "title": "提示",
                    "body": "这是个简单的弹框",
                    "actions": []
                }
            },
            {
                "type": "button",
                "label": "小框",
                "actionType": "drawer",
                "drawer": {
                    "position": "right",
                    "size": "sm",
                    "title": "提示",
                    "body": "这是个简单的弹框",
                    "actions": []
                }
            },
            {
                "type": "button",
                "label": "中框",
                "actionType": "drawer",
                "drawer": {
                    "position": "right",
                    "size": "md",
                    "title": "提示",
                    "body": "这是个简单的弹框",
                    "actions": []
                }
            },
            {
                "type": "button",
                "label": "大框",
                "actionType": "drawer",
                "drawer": {
                    "position": "right",
                    "size": "lg",
                    "title": "提示",
                    "body": "这是个简单的弹框",
                    "actions": []
                }
            },
            {
                "type": "button",
                "label": "超大框",
                "actionType": "drawer",
                "drawer": {
                    "size": "xl",
                    "position": "right",
                    "title": "提示",
                    "body": "这是个简单的弹框",
                    "actions": []
                }
            },
        ]
    }
}
```

### 自定义抽屉尺寸

值如果是数字类型单位默认使用`px`, 如果是字符串类型可以使用自定义 css 宽度变量，如：`%`、`vw`、`px`等

```schema: scope="body"
{
    "type": "button-toolbar",
    "buttons": [
        {
            "type": "button",
            "label": "自定义宽度",
            "actionType": "drawer",
            "drawer": {
                "position": "right",
                "width": 300,
                "title": "提示",
                "body": "这是个自定义300px宽度的弹框"
            }
        },
        {
            "type": "button",
            "label": "自定义高度",
            "actionType": "drawer",
            "drawer": {
                "position": "bottom",
                "height": 300,
                "title": "提示",
                "body": "这是个自定义300px高度的弹框"
            }
        },
    ]
}
```

### 指定弹出方向

```schema: scope="body"
{
    "type": "button-toolbar",
    "buttons": [
        {
            "type": "button",
            "label": "左侧弹出",
            "actionType": "drawer",
            "drawer": {
                "position": "left",
                "title": "提示",
                "body": "这是个简单的弹框"
            }
        },
        {
            "type": "button",
            "label": "右侧弹出",
            "actionType": "drawer",
            "drawer": {
                "position": "right",
                "title": "提示",
                "body": "这是个简单的弹框"
            }
        },
        {
            "type": "button",
            "label": "顶部弹出",
            "actionType": "drawer",
            "drawer": {
                "position": "top",
                "title": "提示",
                "body": "这是个简单的弹框"
            }
        },
        {
            "type": "button",
            "label": "底部弹出",
            "actionType": "drawer",
            "drawer": {
                "position": "bottom",
                "title": "提示",
                "body": "这是个简单的弹框"
            }
        }
    ]
}
```

### 可拖拽抽屉大小

配置`"resizable": true`，可以拖拽调整`drawer`大小

```schema: scope="body"
{
    "type": "button",
    "label": "可拖拽调整大小",
    "actionType": "drawer",
    "drawer": {
        "position": "right",
        "resizable": true,
        "title": "提示",
        "body": "这是个简单的弹框"
    }
}
```

### 是否展示关闭按钮

配置`"showCloseButton": false`，可以隐藏关闭按钮

```schema: scope="body"
{
    "type": "button",
    "label": "无关闭按钮",
    "actionType": "drawer",
    "drawer": {
        "position": "right",
        "title": "提示",
        "body": "这是个简单的弹框",
        "showCloseButton": false
    }
}
```

`showCloseButton` 支持配置表达式

```schema: scope="body"
[{
    "label": "点击弹框",
    "type": "button",
    "actionType": "drawer",
    "drawer": {
      "data": {
        "show": true
      },
      "showCloseButton": "${!show}",
      "title": "弹框标题",
      "body": {
        "type": "form",
        "body": [{
          "type": "input-text",
          "label": "名字",
          "name": "username"
        }],
        "actions": [
          {
            "label": "取消",
            "actionType": "cancel",
            "type": "button",
            "visibleOn": "${show}"
          },
          {
            "label": "确定",
            "actionType": "submit",
            "primary": true,
            "type": "button",
            "visibleOn": "${show}"
          }
        ]
      }
    }
}]
```

### 不显示蒙层

```schema: scope="body"
{
    "type": "button",
    "label": "不显示蒙层",
    "actionType": "drawer",
    "drawer": {
        "position": "right",
        "overlay": false,
        "title": "提示",
        "body": "这是个简单的弹框"
    }
}
```

### 点击抽屉外自动关闭

配置`"closeOnOutside":true`

#### 显示蒙层

```schema: scope="body"
{
    "type": "button",
    "label": "点击抽屉外自动关闭",
    "actionType": "drawer",
    "drawer": {
        "position": "right",
        "closeOnOutside": true,
        "title": "提示",
        "body": "这是个简单的弹框"
    }
}
```

#### 不显示蒙层

```schema: scope="body"
{
    "type": "button",
    "label": "点击抽屉外自动关闭",
    "actionType": "drawer",
    "drawer": {
        "position": "right",
        "overlay": false,
        "closeOnOutside": true,
        "title": "提示",
        "body": "这是个简单的弹框"
    }
}
```

### 属性表

| 属性名          | 类型                                                                              | 默认值             | 说明                                                                                                                          | 版本                    |
| --------------- | --------------------------------------------------------------------------------- | ------------------ | ----------------------------------------------------------------------------------------------------------------------------- | ----------------------- |
| type            | `string`                                                                          |                    | `"drawer"` 指定为 Drawer 渲染器                                                                                               |
| title           | [SchemaNode](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode)             |                    | 弹出层标题                                                                                                                    |
| body            | [SchemaNode](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode)             |                    | 往 Drawer 内容区加内容                                                                                                        |
| size            | `string`                                                                          |                    | 指定 Drawer 大小，支持: `xs`、`sm`、`md`、`lg`、`xl`                                                                          |
| position        | `string`                                                                          |                    | 指定 Drawer 方向，支持: `left`、`right`、`top`、`bottom`                                                                      |
| className       | `string`                                                                          | ``                 | Drawer 最外层容器的样式类名                                                                                                   |
| headerClassName | `string`                                                                          |                    | Drawer 头部 区域的样式类名                                                                                                    |
| bodyClassName   | `string`                                                                          | `modal-body`       | Drawer body 区域的样式类名                                                                                                    |
| footerClassName | `string`                                                                          |                    | Drawer 页脚 区域的样式类名                                                                                                    |
| showCloseButton | `boolean` \| [表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression) | `true`             | 是否显示右上角的关闭按钮, 支持布尔值和表达式                                                                                  | `1.15.0` 版本支持表达式 |
| closeOnEsc      | `boolean`                                                                         | `false`            | 是否支持按 `Esc` 关闭 Drawer                                                                                                  |
| closeOnOutside  | `boolean`                                                                         | `false`            | 点击内容区外是否关闭 Drawer                                                                                                   |
| overlay         | `boolean`                                                                         | `true`             | 是否显示蒙层                                                                                                                  |
| resizable       | `boolean`                                                                         | `false`            | 是否可通过拖拽改变 Drawer 大小                                                                                                |
| width           | `string \| number`                                                                | `500px`            | 容器的宽度，在 position 为 `left` 或 `right` 时生效                                                                           |
| height          | `string \| number`                                                                | `500px`            | 容器的高度，在 position 为 `top` 或 `bottom` 时生效                                                                           |
| actions         | Array<[Action](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/action)>           | 【确认】和【取消】 | 可以不设置，默认只有两个按钮。                                                                                                |
| data            | `object`                                                                          |                    | 支持 [数据映射](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/data-mapping)，如果不设定将默认将触发按钮的上下文中继承数据。 |
| popOverContainerSelector | `string`                                                                          |                    | 设置弹窗的父级容器，默认为 body，使用querySelector获取                                                                                       | `1.61.0` |

### 事件表

当前组件会对外派发以下事件，可以通过`onEvent`来监听这些事件，并通过`actions`来配置执行的动作，在`actions`中可以通过`${事件参数名}`来获取事件产生的数据，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action)。

| 事件名称 | 事件参数                                                                 | 说明               |
| -------- | ------------------------------------------------------------------------ | ------------------ |
| confirm  | `event.data: object` 抽屉数据<br/>`[name]: any` 当前数据域中指定字段的值 | 点击确认提交时触发 |
| cancel   | `event.data: object` 抽屉数据<br/>`[name]: any` 当前数据域中指定字段的值 | 点击取消时触发     |

### 动作表

当前组件对外暴露以下特性动作，其他组件可以通过指定`actionType: 动作名称`、`componentId: 该组件id`来触发这些动作，动作配置可以通过`args: {动作配置项名称: xxx}`来配置具体的参数，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action#触发其他组件的动作)。

| 动作名称 | 动作配置                   | 说明         |
| -------- | -------------------------- | ------------ |
| confirm  | -                          | 确认（提交） |
| cancel   | -                          | 取消（关闭） |
| setValue | `value: object` 更新的数据 | 更新数据     |
