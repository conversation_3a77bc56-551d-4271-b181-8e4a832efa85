---
title: Each 循环渲染器
description:
type: 0
group: ⚙ 组件
menuName: Each 循环渲染器
icon:
order: 45
standardMode: true
---

## 场景推荐

### 基本用法

```schema
{
  "type": "page",
  "data": {
    "arr": [
      {
        "name": "FP-2234523434"
      },
      {
        "name": "JC-123452345t"
      },
      {
        "name": "JP-1234562345r"
      },
      {
        "name": "JP-123456234ss5r"
      }
    ]
  },
  "body": {
    "type": "form",
    "body": [
      {
        "type": "tpl",
        "tpl": "节点【XXXX】被如下在线状态策略关联，请先解除关联："
      },
      {
        "type": "each",
        "name": "arr",
        "items": [
          {
            "type": "link",
            "href": "${name}",
            "body": "${name}"
          },
          {
            "type": "tpl",
            "visibleOn": "${(index + 1) < arr.length}",
            "tpl": "、"
          }
        ]
      }
    ]
  }
}
```

<!-- ### select自定义menuTpl

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "body": [
      {
        "type": "select",
        "name": "owner",
        "label": "创建人",
        "placeholder": "请选择",
        "labelField": "userName",
        "valueField": "userId",
        "columnRatio": 4,
        "itemHeight": 64,
        "menuTpl": {
          "type": "tooltip-wrapper",
          "inline": true,
          "content": {
            "type": "flex",
            "direction": "column",
            "alignItems": "start",
            "items": [
              {
                "type": "tpl",
                "tpl": "${userName}"
              },
              {
                "type": "each",
                "name": "orgs",
                "items": [
                  {
                    "type": "tpl",
                    "tpl": "<div>${orgName}</div>",
                    "className": "text-xs text-secondary "
                  }
                ]
              }
            ]
          },
          "body": {
            "type": "flex",
            "direction": "column",
            "alignItems": "start",
            "items": [
              {
                "type": "tpl",
                "tpl": "${userName}"
              },
              {
                "type": "each",
                "name": "orgs",
                "items": [
                  {
                    "type": "tpl",
                    "tpl": "${orgName}"
                  }
                ]
              }
            ]
          }
        },
        "options": [
          {
            "userName": "张三",
            "userId": "a",
            "orgs": [
              {
                "orgName": "数禾/技术中心/信贷中台/信贷前置组/研发部",
                "orgId": "1"
              },
              {
                "orgName": "数禾/技术中心/信贷中台/信贷前置组/研发部",
                "orgId": "2"
              },
              {
                "orgName": "数禾/技术中心/信贷中台/信贷前置组/研发部",
                "orgId": "3"
              }
            ],
            "date": "入职日期: 2022年9月1日"
          },
          {
            "userName": "李四",
            "userId": "b",
            "orgs": [
              {
                "orgName": "数禾/技术中心/信贷中台/信贷前置组/研发部",
                "orgId": "1"
              },
              {
                "orgName": "数禾/技术中心/信贷中台/信贷前置组/研发部",
                "orgId": "2"
              },
              {
                "orgName": "数禾/技术中心/信贷中台/信贷前置组/研发部",
                "orgId": "3"
              }
            ],
            "date": "入职日期: 2022年9月2日"
          },
          {
            "userName": "王五",
            "userId": "c",
            "orgs": [
              {
                "orgName": "数禾/技术中心/信贷中台/信贷前置组/研发部",
                "orgId": "1"
              },
              {
                "orgName": "数禾/技术中心/信贷中台/信贷前置组/研发部",
                "orgId": "2"
              },
              {
                "orgName": "数禾/技术中心/信贷中台/信贷前置组/研发部",
                "orgId": "3"
              }
            ],
            "date": "入职日期: 2022年9月3日"
          }
        ]
      }
    ]
  }
}
``` -->

### 动态渲染多组静态表单

根据数据动态展示多组表单的静态模式，适用于父容器宽度较窄的情况，采用form的紧凑模式，缩小表单项之间垂直间距

```schema
{
  "type": "page",
  "data": {
    "batchFormData": [
      {
        "department": "测试测试测试测试测试测试测试测试测试测试测试测试",
        "platform": "测试测试测试测试",
        "css": "测试测试测试测试"
      },
      {
        "department": "测试测试测试测试测试测试测试测试测试测试测试测试",
        "platform": "测试测试测试测试",
        "css": "测试测试测试测试"
      },
      {
        "department": "测试测试测试测试测试测试测试测试测试测试测试测试",
        "platform": "测试测试测试测试",
        "css": "测试测试测试测试"
      }
    ]
  },
  "body": {
    "labelAlign": "left",
    "static": true,
    "type": "form",
    "title": "",
    "id": "form1",
    "api": {
      "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
      "method": "post"
    },
    "actions": [],
    "body": {
      "type": "each",
      "name": "batchFormData",
      "items": [
        {
          "type": "group",
          "withoutMarginBottom": true,
          "body": [
            {
              "type": "input-text",
              "name": "department",
              "label": "归属部门归属部门"
            }
          ]
        },
        {
          "type": "group",
          "withoutMarginBottom": true,
          "body": [
            {
              "type": "input-text",
              "name": "platform",
              "label": "Platform",
              "placeholder": "请输入"
            }
          ]
        },
        {
          "type": "group",
          "withoutMarginBottom": true,
          "body": [
            {
              "type": "input-text",
              "name": "css",
              "label": "CSS",
              "placeholder": "请输入"
            }
          ]
        },
        {
          "type": "divider",
          "visibleOn": "${index < batchFormData.length - 1}"
        }
      ]
    },
  }
}
```

## 组件用法
### 基本用法

```schema: scope="page"
{
  "type": "page",
  "data": {
    "arr": ["A", "B", "C"]
  },
  "body": {
        "type": "each",
        "name": "arr",
        "items": {
            "type": "tpl",
            "tpl": "<span class='label label-default m-l-sm'><%= data.item %></span> "
        }
    }
}
```

### 如果是对象数组

如果数组中的数据是对象，可以直接使用 data.xxx 来获取，另外能用 data.index 来获取数组索引，但如果对象本身也有名字为 index 的字段就会覆盖到，获取不到索引了。

```schema:height="160" scope="page"
{
  "type": "page",
  "data": {
    "arr": [{"name": "a"}, {"name": "b"}, {"name": "c"}]
  },
  "body": {
        "type": "each",
        "name": "arr",
        "items": {
            "type": "tpl",
            "tpl": "<span class='label label-default m-l-sm'><%= data.name %>:<%= data.index %></span> "
        }
    }
}
```

### 用作 Field 时

当用在 Table 的列配置 Column、List 的内容、Card 卡片的内容和表单的 Static-XXX 中时，可以设置`name`属性，映射同名变量，然后用可以通过 `item` 变量获取单项值

### Table 中的列类型

```schema: scope="body"
{
    "type": "table",
    "data": {
        "items": [
            {
                "id": "1",
                "each": ["A1", "B1", "C1"]
            },
            {
                "id": "2",
                "each": ["A2", "B2", "C2"]
            },
            {
                "id": "3",
                "each": []
            }
        ]
    },
    "columns": [
        {
            "name": "id",
            "label": "Id"
        },

        {
            "name": "each",
            "label": "循环",
            "type": "each",
            "placeholder": "暂无内容",
            "items": {
                "type": "tpl",
                "tpl": "<span class='label label-info m-l-sm'><%= this.item %></span>"
            }
        }
    ]
}
```

List 的内容、Card 卡片的内容配置同上

### Form 中静态展示

```schema: scope="body"
{
    "type": "form",
    "data": {
        "each": ["A", "B", "C"]
    },
    "body": [
        {
            "type": "each",
            "label": "静态展示each",
            "name": "each",
            "items": {
                "type": "tpl",
                "tpl": "<span class='label label-info m-l-sm'><%= this.item %></span>"
            }
        }
    ]
}
```

### 使用数据映射

```schema: scope="page"
{
  "type": "page",
  "data": {
    "arr": ["A", "B", "C"]
  },
  "body": {
        "type": "each",
        "source": "${arr}",
        "items": {
            "type": "tpl",
            "tpl": "<span class='label label-default m-l-sm'><%= data.item %></span> "
        }
    }
}
```

`name` 的优先级会比 `source` 更高

### 属性表

| 属性名      | 类型     | 默认值   | 说明                                                                 |
| ----------- | -------- | -------- | -------------------------------------------------------------------- |
| type        | `string` | `"each"` | 指定为 Each 组件                                                     |
| value       | `array`  | `[]`     | 用于循环的值                                                         |
| name        | `string` |          | 获取数据域中变量                                                     |
| source      | `string` |          | 获取数据域中变量， 支持 [数据映射](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/data-mapping) |
| items       | `object` |          | 使用`value`中的数据，循环输出渲染器。                                |
| placeholder | `string` |          | 当 `value` 值不存在或为空数组时的占位文本                            |
