---
title: Empty 空状态
description:
type: 0
group: ⚙ 组件
menuName: Empty 空状态
icon:
order: 60
standardMode: true
---

## 基本用法

```schema
{
    "type": "page",
    "className": "flex items-center",
    "body": {
        "type": "empty"
    }
}
```

## 自定义文案


```schema
{
    "type": "page",
    "className": "flex items-center",
    "body": {
        "type": "empty",
        "description": "自定义空状态描述"
    }
}
```

## 属性表

| 属性名    | 类型     | 默认值     | 说明                                | 版本 |
| --------- | -------- | ---------- | ----------------------------------- | --- |
| type      | `string` |   `"empty"`         | 指定为 空状态 渲染器     | `1.40.0` |
| className | `string` |            | 外层 Dom 的类名                     | `1.40.0` |
| icon | `string` | `"table-empty"` | 空状态图标 | `1.40.0` |
| description | [模板](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/template) | `"暂无数据"` | 空状态描述文案 | `1.40.0` |
