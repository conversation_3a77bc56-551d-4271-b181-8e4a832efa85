---
title: Flex 布局
description:
type: 0
group: ⚙ 组件
menuName: Flex 布局
icon:
order: 47
---

Flex 布局是基于 CSS Flex 实现的布局效果，它比 Grid 和 HBox 对子节点位置的可控性更强，比用 CSS 类的方式更易用，并且默认使用水平垂直居中的对齐。
## 场景推荐

### 配置间距
当多个组件需要组合排列展示时，可通过`flex`布局，支持水平和垂直两种布局方式。

```schema: scope="body"
{
"type": "form",
    "api": "/api/mock2/saveForm?waitSeconds=2",
    "data": {
      "platform": "text1",
      "css": "text2",
      "bankList": [
        "4017"
      ]
    },
    "labelWidth": 70,
    "body":[{
      "type": "flex",
      "gap": true,
      "items": [
        {
          "type": "tag",
          "label": "GET",
          "color": "success"
        },
        {
          "type": "static",
          "label": false,
          "value": "我是描述文字"
        }
      ]
    }
    ]
  }

```

### 自适应场景

```schema: scope="body"
 { "type": "form",
    "api": "/api/mock2/saveForm?waitSeconds=2",
    "data": {
      "platform": "text1",
      "css": "text2",
      "bankList": [
        "4017"
      ]
    },
    "body": [
      {
        "type": "group",
        "body": [
          {
            "type": "flex",
            "gap": true,
            "items": [
              {
                "type": "select",
                "name": "eventSource",
                "label": "事件平台",
                "options": [
                  {
                    "label": "业务系统",
                    "value": "SYSTEM"
                  },
                  {
                    "label": "特征系统",
                    "value": "FEATURE"
                  },
                  {
                    "label": "北斗系统",
                    "value": "EFUEL"
                  },
                  {
                    "label": "埋点系统",
                    "value": "STATS"
                  }
                ],
                "className": "flex-grow"
              },
              {
                "type": "button",
                "label": "新建",
                "level": "link",
                "linkWithoutPadding": true
              },
              {
                "type": "button",
                "label": "详情",
                "level": "link",
                "linkWithoutPadding": true
              }
            ]
          }
        ]
      },
      {
        "type": "group",
        "body": [
          {
            "type": "flex",
            "gap": true,
            "items": [
              {
                "type": "select",
                "name": "eventSource",
                "label": "系统平台",
                "options": [
                  {
                    "label": "业务系统",
                    "value": "SYSTEM"
                  },
                  {
                    "label": "特征系统",
                    "value": "FEATURE"
                  },
                  {
                    "label": "北斗系统",
                    "value": "EFUEL"
                  },
                  {
                    "label": "埋点系统",
                    "value": "STATS"
                  }
                ],
                "className": " flex-grow"
              },
              {
                "type": "button",
                "label": "新建",
                "level": "link",
                "linkWithoutPadding": true
              },
              {
                "type": "button",
                "label": "详情",
                "level": "link",
                "linkWithoutPadding": true
              }
            ]
          },
          {
            "type": "input-text",
            "name": "css",
            "label": "CSS",
            "required": true,
            "placeholder": "请输入"
          }
        ]
      },
      {
        "type": "group",
        "body": [
          {
            "type": "flex",
            "gap": true,
            "items": [
              {
                "type": "select",
                "name": "eventSource",
                "label": "账务系统",
                "options": [
                  {
                    "label": "业务系统",
                    "value": "SYSTEM"
                  },
                  {
                    "label": "特征系统",
                    "value": "FEATURE"
                  },
                  {
                    "label": "北斗系统",
                    "value": "EFUEL"
                  },
                  {
                    "label": "埋点系统",
                    "value": "STATS"
                  }
                ],
                "className": " flex-grow"
              },
              {
                "type": "button",
                "label": "新建",
                "level": "link",
                "linkWithoutPadding": true
              },
              {
                "type": "button",
                "label": "详情",
                "level": "link",
                "linkWithoutPadding": true
              }
            ]
          },
          {
            "type": "input-text",
            "name": "css",
            "label": "CSS",
            "required": true,
            "placeholder": "请输入"
          },
          {
            "type": "input-tag",
            "name": "tag",
            "label": "标签",
            "placeholder": "请选择标签",
            "options": [
              "Aaron Rodgers",
              "Tom Brady",
              "Charlse Woodson",
              "Aaron Jones"
            ]
          }
        ]
      }
    ]
  }
```

<!-- ### 两列均分

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/saveForm?waitSeconds=2",
    "data": {
      "platform": "text1",
      "css": "text2",
      "bankList": [
        "4017"
      ]
    },
    "body": [
      {
        "type": "flex",
        "justify": "flex-start",
        "items": [
          {
            "type": "tabs",
            "className": "flex-1",
            "tabs": [
              {
                "title": "控制台",
                "tab": [
                  {
                    "type": "editor",
                    "name": "editor",
                    "label": false,
                    "required": true,
                    "editorTheme": "vs-dark",
                    "placeholder": "请输入"
                  }
                ]
              },
              {
                "title": "测试参数",
                "tab": [
                  {
                    "type": "editor",
                    "name": "editor",
                    "label": false,
                    "required": true,
                    "editorTheme": "vs-dark",
                    "placeholder": "请输入"
                  }
                ],
              },
              {
                "title": "代码输出",
                "tab": [
                  {
                    "type": "editor",
                    "name": "editor",
                    "label": false,
                    "required": true,
                    "editorTheme": "vs-dark",
                    "placeholder": "请输入"
                  }
                ]
              }
            ]
          },
          {
            "type": "tabs",
            "className": "flex-1",
            "tabs": [
              {
                "title": "工具输出结果",
                "tab": [
                  {
                    "type": "editor",
                    "name": "editor",
                    "label": false,
                    "required": true,
                    "editorTheme": "vs-dark",
                    "placeholder": "请输入"
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
}
``` -->

## 组件用法

### 基本用法

配置`gap`属性为`true`, 可实现组件之间的间距，默认水平方向间距为`8px`,垂直方向为`16px`，暂不支持修改间距值

```schema
{
  "type": "page",
  "body":{
    "type":"wrapper",
    "bgColor":"white",
      "body": {
        "type": "flex",
        "gap": true,
        "items": [
          {
            "type": "tag",
            "label": "test",
            "color": "success"
          },
          {
            "type": "tag",
            "label": "test",
            "color": "error"
          },
          {
            "type": "tag",
            "label": "test",
            "color": "warning"
          },
        ]
      }
  }
}
```

其中 `items` 里的每一个都可以是其他 amis 类型。

### 子节点水平分布

> 严格来说并不一定是水平，因为还能通过 direction 修改方向，不过这里为了简化就这么称呼了

可以通过 justify 控制水平分布方式，默认是 `center` 居中，其他几种的示例如下：

```schema
{
  "type": "page",
  "body": [
    {
      "type": "wrapper",
      "bgColor": "white",
      "body": [
        {
          "type": "tpl",
          "tpl": "center"
        },
        {
          "type": "flex",
          "justify": "center",
          "gap": true,
          "items": [
            {
              "type": "tag",
              "label": "test",
              "color": "success"
            },
            {
              "type": "tag",
              "label": "test",
              "color": "error"
            },
            {
              "type": "tag",
              "label": "test",
              "color": "warning"
            }
          ]
        },
        {
          "type": "divider"
        },
        {
          "type": "tpl",
          "tpl": "flex-start"
        },
        {
          "type": "flex",
          "gap": true,
          "justify": "flex-start",
          "items": [
            {
              "type": "tag",
              "label": "test",
              "color": "success"
            },
            {
              "type": "tag",
              "label": "test",
              "color": "error"
            },
            {
              "type": "tag",
              "label": "test",
              "color": "warning"
            }
          ]
        },
        {
          "type": "divider"
        },
        {
          "type": "tpl",
          "tpl": "flex-end"
        },
        {
          "type": "flex",
          "justify": "flex-end",
          "gap": true,
          "items": [
            {
              "type": "tag",
              "label": "test",
              "color": "success"
            },
            {
              "type": "tag",
              "label": "test",
              "color": "error"
            },
            {
              "type": "tag",
              "label": "test",
              "color": "warning"
            }
          ]
        },
        {
          "type": "divider"
        },
        {
          "type": "tpl",
          "tpl": "space-around"
        },
        {
          "type": "flex",
          "justify": "space-around",
          "items": [
            {
              "type": "tag",
              "label": "test",
              "color": "success"
            },
            {
              "type": "tag",
              "label": "test",
              "color": "error"
            },
            {
              "type": "tag",
              "label": "test",
              "color": "warning"
            }
          ]
        },
        {
          "type": "divider"
        },
        {
          "type": "tpl",
          "tpl": "space-between"
        },
        {
          "type": "flex",
          "justify": "space-between",
          "items": [
            {
              "type": "tag",
              "label": "test",
              "color": "success"
            },
            {
              "type": "tag",
              "label": "test",
              "color": "error"
            },
            {
              "type": "tag",
              "label": "test",
              "color": "warning"
            }
          ]
        },
        {
          "type": "divider"
        },
        {
          "type": "tpl",
          "tpl": "space-evenly"
        },
        {
          "type": "divider"
        },
        {
          "type": "flex",
          "justify": "space-evenly",
          "items": [
            {
              "type": "tag",
              "label": "test",
              "color": "success"
            },
            {
              "type": "tag",
              "label": "test",
              "color": "error"
            },
            {
              "type": "tag",
              "label": "test",
              "color": "warning"
            }
          ]
        }
      ]
    }
  ]
}
```

### 垂直方向位置

可以通过设置 `alignItems` 改变在子节点在垂直方向的位置，默认是 `center` 居中，其他几个常见设置请参考：

```schema
{
  "type": "page",
  "body": {
    "type": "page",
    "body": [
      {
        "type": "tpl",
        "tpl": "center",
      },
      {
        "type": "flex",
        "justify":"center",
        "alignItems": "center",
        "gap": true,
        "style": {
          "height": 60,
          "backgroundColor": "#fff"
        },
        "items": [
          {
            "type": "tag",
            "label": "test",
            "color": "success"
          },
          {
            "type": "tag",
            "label": "test",
            "color": "error"
          },
          {
            "type": "tag",
            "label": "test",
            "color": "active"
          }
        ]
      },
      {
        "type": "divider"
      },
      {
        "type": "tpl",
        "tpl": "flex-start",
      },
      {
        "type": "flex",
        "justify":"center",
        "alignItems": "flex-start",
        "gap": true,
        "style": {
          "height": 60,
          "backgroundColor": "#fff"
        },
        "items": [
          {
            "type": "tag",
            "label": "test",
            "color": "success"
          },
          {
            "type": "tag",
            "label": "test",
            "color": "error"
          },
          {
            "type": "tag",
            "label": "test",
            "color": "active"
          }
        ]
      },
      {
        "type": "divider"
      },
       {
        "type": "tpl",
        "tpl": "flex-end",
      },
      {
        "type": "flex",
        "justify":"center",
        "alignItems": "flex-end",
        "gap": true,
        "style": {
          "height": 60,
          "backgroundColor": "#fff"
        },
        "items": [
          {
            "type": "tag",
            "label": "test",
            "color": "success"
          },
          {
            "type": "tag",
            "label": "test",
            "color": "error"
          },
          {
            "type": "tag",
            "label": "test",
            "color": "active"
          }
        ]
      }
    ]
  }
}
```

### 布局方向

默认是行的方式，可以通过 "direction": "column" 改成列的方式。

```schema
{
    "type": "page",
    "body": [
       {
         "type":"wrapper",
         "bgColor":"white",
         "body":[
          {
            "type": "tpl",
            "tpl": "direction: row",
          },
          {
            "type": "flex",
            "gap": true,
            "justify": "center",
            "items": [
              {
                "type": "tag",
                "label": "test",
                "color": "success"
              },
              {
                "type": "tag",
                "label": "test",
                "color": "error"
              },
              {
                "type": "tag",
                "label": "test",
                "color": "active"
              }
            ]
          },
          {
            "type":"divider"
          },
          {
            "type": "tpl",
            "tpl": "direction: column",
          },
          {
            "type": "flex",
            "direction": "column",
            "justify": "center",
            "alignItems":"center",
            "gap": true,
            "items": [
              {
                "type": "tag",
                "label": "test",
                "color": "success"
              },
              {
                "type": "tag",
                "label": "test",
                "color": "error"
              },
              {
                "type": "tag",
                "label": "test",
                "color": "active"
              }
            ]
          }]
       },
       
    ]
}
```

### 移动端支持

有时候希望在移动端有不同展现，比如将 `direction` 改成 `column`：

```schema
 {
  "type": "page",
  "body": {
    "type": "wrapper",
    "bgColor": "white",
    "body":{
    "type": "flex",
    "gap": true,
    "justify": "center",
    "mobile": {
      "direction": "column"
    },
    "items": [
      {
        "type": "tag",
        "label": "test",
        "color": "success"
      },
      {
        "type": "tag",
        "label": "test",
        "color": "error"
      },
      {
        "type": "tag",
        "label": "test",
        "color": "active"
      }
    ]
  }
  }
}
```

其他关于移动端定制的细节请参考[这里](/dataseeddesigndocui/#/amis/zh-CN/docs/extend/mobile)。

### 属性表

| 属性名     | 类型                                   | 默认值 | 说明                                                                                                |
| ---------- | -------------------------------------- | ------ | --------------------------------------------------------------------------------------------------- |
| type       | `string`                               | `flex` | 指定为 Flex 渲染器                                                                                  |
| className  | `string`                               |        | css 类名                                                                                            |
| justify    | `string`                               |        | "start", "flex-start", "center", "end", "flex-end", "space-around", "space-between", "space-evenly" |
| alignItems | `string`                               |        | "stretch", "start", "flex-start", "flex-end", "end", "center", "baseline"                           |
| style      | `object`                               |        | 自定义样式                                                                                          |
| items[]    | [SchemaNode](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode) |        |
| direction   | `string` |        | "column"，设置内部元素垂直布局 |        |
| gap   | `boolean` |        | 如果为true,水平间距为8px和垂直间距16px

