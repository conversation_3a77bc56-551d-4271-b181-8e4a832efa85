---
title: Between 表单项区间
description:
type: 0
group: null
menuName: Between
icon:
order: 24
---

用于录入一个区间范围的表单项，比如录入利率范围：5% ~ 10%。

## 场景推荐
### 带提示信息    
```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "mode": "horizontal",
  "body": [
    { "type":"between",
      "label": "放款范围",
      "separatorStr": "~",
      "labelRemark": {
        "type": "remark",
        "content": "这是一个提示"
      },
      "description": "放款范围规则为左闭右开",
      "items": [
        {
          "type": "input-group",
          "suffix": "元",
          "body": [
            {
              "type": "input-text",
              "name": "groupfix",
              "placeholder": "请输入",
              "addonAfterStr": "元",
            }
          ]
        },
        {
          "type": "input-group",
          "suffix": "元",
          "body": [
            {
              "type": "input-text",
              "name": "groupfix2",
              "placeholder": "请输入",
              "addonAfterStr": "元",
            }
          ]
        }  
      ]
    }
  ]
}
```
- 落地案例    
[资金平台-运营工单-借款金额区间调整](https://moka.sit.caijj.net/fundui/#/loanAmountIntervalAdjust?workOrderType=PRODUCT_ELEMENT&subWorkOrderType=LOAN_AMOUNT_INTERVAL)   
  ![资金平台-运营工单-借款金额区间调整](https://static02.sit.yxmarketing01.com/materialcenter/378e7d51-80d8-454c-a057-98e81773fbfc.png)

## 组件用法
### 基本用法

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "mode": "horizontal",
  "body": [
    {
      "type":"between",
      "label": "放款范围",
      "items": [
        {
          "type": "input-text",
          "name": "test1"
        },
        {
          "type": "input-text",
          "name": "test2"
        },
      ]
    }
  ]
}
```

### 区间符号可自定义

通过配置`separatorStr`属性，可以自定义区间符号。

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "mode": "horizontal",
  "body": [
   {
      "type":"between",
      "label": "放款范围",
      "separatorStr": "~",
      "items": [
        {
          "type": "input-text",
          "name": "test1"
        },
        {
          "type": "input-text",
          "name": "test2"
        },
      ]
    }
  ]
}
```

### 带后缀
当区间有后缀，通过配置`suffix`属性实现。

```schema: scope="body"
{
  "type": "form",
  "mode": "horizontal",
  "body": [
    {
      "type":"between",
      "label": "放款范围",
      "separatorStr": "~",
      "items": [
        {
          "type": "input-group",
          "suffix": "元",
          "body": [
            {
              "type": "input-text",
              "name": "groupfix1",
              "placeholder": "请输入"
            },
          ]
        },
        {
          "type": "input-group",
          "suffix": "元",
          "body": [
            {
              "type": "input-text",
              "name": "groupfix2",
              "placeholder": "请输入"
            },
          ]
        },
      ]
    }
  ]
}
```

### 静态展示

```schema: scope="body"
{
  "type": "form",
  "mode": "horizontal",
  "data": {
    "groupfix1": 10,
    "groupfix2": 20,
  },
  "body": [
    {
      "type": "group",
      "body": [
      {
          "type":"between",
          "static": true,
          "columnRatio": 6,
          "label": "放款范围",
          "separatorStr": "~",
          "items": [
            {
              "type": "input-group",
              "suffix": "元",
              "body": [
                {
                  "type": "input-text",
                  "name": "groupfix1",
                  "placeholder": "请输入"
                },
              ]
            },
            {
              "type": "input-group",
              "suffix": "元",
              "body": [
                {
                  "type": "input-text",
                  "name": "groupfix2",
                  "placeholder": "请输入"
                },
              ]
            },
          ]
        }
      ]
    }
    
  ]
}
```

### 属性表

| 属性名    | 类型                        | 默认值         | 说明       | 版本 |                                                                
| --------- | --------------------------- | -------------- | --------------- | ----------------------------------------------------------- |
| type | `string`                    |    'between'            | 指定为 between渲染器  
| className | `string`                    |                | CSS 类名                                                                   |
| label     | `string`                    |                | group 的标签                                                               |
| items      | Array<[表单项](/dataseeddesigndocui/#/amis/zh-CN/components/form/formitem)> |                | 表单项集合                                                                 |
| separatorStr      | `string`                    |                | 区间符号 |

