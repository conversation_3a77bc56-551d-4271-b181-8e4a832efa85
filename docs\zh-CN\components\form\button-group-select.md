---
title: Button-Group-Select 按钮点选
description:
type: 0
group: null
menuName: Button-Group-Select
icon:
order: 6
---

按钮集合当 select 点选用，按钮之间无间距，支持表单项属性及功能。通常作为ForItem使用，或InputTree等组件带筛选按钮的场景。

相关组件区分：
1. [ButtonGroup 按钮组](/dataseeddesigndocui/#/amis/zh-CN/components/button-group)：用于将配置的多个按钮集合为一个整体，按钮之间无间距，按钮schema可自定义配置actionType、url等属性。
2. [Button-Toolbar 按钮工具栏](/dataseeddesigndocui/#/amis/zh-CN/components/form/button-toolbar)：多个按钮排列组合展示，按钮直接存在默认间距8px。通常用于Crud、GroupContainer等组件带全局操作按钮的场景。

## 场景推荐

### 表单项使用

用于表单项中需要按钮集合点选的场景

```schema
{
  "type": "page",
  "data": {
    "type": "a"
  },
  "body": {
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "button-group-select",
        "label": "选项",
        "name": "type",
        "options": [
          {
            "label": "Option A",
            "value": "a"
          },
          {
            "label": "Option B",
            "value": "b"
          },
          {
            "label": "Option C",
            "value": "c"
          }
        ]
      }
    ]
  }
}
```

### Crud筛选按钮组

```schema
{
  "type": "page",
  "id": "switch-mode-crud",
  "data": {
    "mode": "cards",
    "button-group-select": "all"
  },
  "body": {
    "type": "crud",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/users",
    "id": "custom-crud-id",
    "filter": {
      "title": "",
      "body": [
        {
          "type": "group",
          "mode": "horizontal",
          "body": [
            {
              "type": "input-text",
              "name": "keywords",
              "label": "关键字",
              "clearable": true,
              "placeholder": "通过关键字搜索",
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "engine",
              "label": "Engine",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "platform",
              "label": "Platform",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "keywords1",
              "label": "关键字1",
              "clearable": true,
              "placeholder": "通过关键字搜索",
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "engine1",
              "label": "Engine1",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "platform1",
              "label": "Platform1",
              "clearable": true,
              "columnRatio": 4
            }
          ]
        }
      ],
      "actions": [
        {
          "type": "reset",
          "label": "重 置"
        },
        {
          "type": "submit",
          "level": "primary",
          "label": "查 询"
        }
      ]
    },
    "headerFilter": {
      "body": [
        {
          "type": "flex",
          "gap": true,
          "items": [
            {
              "label": false,
              "type": "button-group-select",
              "name": "button-group-select",
              "options": [
                {
                  "value": "all",
                  "label": "查询全部"
                },
                {
                  "value": "forme",
                  "label": "待我审核"
                },
                {
                  "value": "reject",
                  "label": "已拒绝"
                }
              ],
              "onEvent": {
                "change": {
                  "actions": [
                    {
                      "actionType": "query",
                      "componentId": "custom-crud-id",
                      "args": {
                        "queryParams": {
                          "button-group-select": "${button-group-select}"
                        }
                      }
                    }
                  ]
                }
              }
            },
            {
              "type": "flex",
              "items": [
                {
                  "type": "tpl",
                  "tpl": "关键字："
                },
                {
                  "type": "search-box",
                  "name": "keywords",
                  "placeholder": "请输入"
                }
              ]
            },
          ]
        }
      ]
    },
    "mode": "${mode}",
    "columnsCount": 5,
    "card": {
      "header": {
        "title": "主标题"
      },
      "body": [
        {
          "type": "flex",
          "direction": "column",
          "gap": true,
          "items": [
            {
              "type": "flex",
              "justify": "space-between",
              "items": [
                {
                  "type": "tpl",
                  "tpl": "主文案"
                },
                {
                  "type": "tag",
                  "label": "分析中",
                  "displayMode": "normal",
                  "color": "#4096ff"
                }
              ]
            },
            {
              "type": "typography",
              "text": "这是一段很长的文案介绍",
            }
          ]
        }
      ]
    }
  }
}
```

## 组件用法
### 基本用法

按钮集合当 select 点选用。

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "debug": true,
  "body": [
    {
      "type": "button-group-select",
      "label": "选项",
      "name": "type",
      "options": [
        {
          "label": "Option A",
          "value": "a"
        },
        {
          "label": "Option B",
          "value": "b"
        },
        {
          "label": "Option C",
          "value": "c"
        }
      ]
    }
  ]
}
```

### 垂直模式

配置`"vertical": true`，实现垂直模式

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "button-group-select",
      "label": "选项",
      "name": "type",
      "vertical": true,
      "options": [
        {
          "label": "Option A",
          "value": "a"
        },
        {
          "label": "Option B",
          "value": "b"
        },
        {
          "label": "Option C",
          "value": "c"
        }
      ]
    }
  ]
}
```

### 平铺模式

配置 `"tiled": true` 实现平铺模式

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "button-group-select",
      "label": "选项",
      "name": "type",
      "tiled": true,
      "options": [
        {
          "label": "Option A",
          "value": "a"
        },
        {
          "label": "Option B",
          "value": "b"
        },
        {
          "label": "Option C",
          "value": "c"
        }
      ]
    }
  ]
}
```

### 按钮主题样式

配置 `btnLevel` 统一设置按钮主题样式，注意 `buttons ` 或 `options` 中的`level`属性优先级高于`btnLevel`。配置 `btnActiveLevel` 为按钮设置激活态时的主题样式。

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "debug": true,
  "body": [
    {
      "type": "button-group-select",
      "label": "选项",
      "name": "type",
      "btnLevel": "light",
      "btnActiveLevel": "warning",
      "options": [
        {
          "label": "Option A",
          "value": "a"
        },
        {
          "label": "Option B",
          "value": "b"
        },
        {
          "label": "Option C",
          "value": "c",
          "level": "primary"
        }
      ]
    }
  ]
}
```

### 支持角标

按钮可支持角标，在 options 中配置

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "debug": true,
  "body": [
    {
      "type": "button-group-select",
      "label": "选项",
      "name": "type",
      "options": [
        {
          "label": "Option A",
          "value": "a"
        },
        {
          "label": "Option B",
          "value": "b",
          "badge": {
            "mode": "text",
            "text": 15
          }
        },
        {
          "label": "Option C",
          "value": "c",
          "badge": {
            "mode": "ribbon",
            "text": "HOT"
      }
        }
      ]
    }
  ]
}
```

### 属性表

当做选择器表单项使用时，除了支持 [普通表单项属性表](/dataseeddesigndocui/#/amis/zh-CN/components/form/formitem#%E5%B1%9E%E6%80%A7%E8%A1%A8) 中的配置以外，还支持下面一些配置

| 属性名         | 类型                                                                                                                | 默认值                  | 说明                                                                                        | 版本    |
| -------------- | ------------------------------------------------------------------------------------------------------------------- | ----------------------- | ------------------------------------------------------------------------------------------- | ------- |
| type           | `string`                                                                                                            | `"button-group-select"` | 指定为 button-group-select 渲染器                                                           |
| vertical       | `boolean`                                                                                                           | `false`                 | 是否使用垂直模式                                                                            |
| tiled          | `boolean`                                                                                                           | `false`                 | 是否使用平铺模式                                                                            |
| btnLevel       | `'link' \| 'primary' \| 'secondary' \| 'info'\|'success' \| 'warning' \| 'danger' \| 'light'\| 'dark' \| 'default'` | `"default"`             | 按钮样式                                                                                    |
| btnActiveLevel | `'link' \| 'primary' \| 'secondary' \| 'info'\|'success' \| 'warning' \| 'danger' \| 'light'\| 'dark' \| 'default'` | `"primary"`             | 选中按钮样式                                                                                |
| options        | `Array<object>`或`Array<string>`                                                                                    |                         | [选项组](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E9%9D%99%E6%80%81%E9%80%89%E9%A1%B9%E7%BB%84-options)                   |
| option.badge   | `object`                                                                                                            |                         | [角标](/dataseeddesigndocui/#/amis/zh-CN/components/badge#属性表)                                                                     |  |
| source         | `string`或 [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api)                                                                           |                         | [动态选项组](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E5%8A%A8%E6%80%81%E9%80%89%E9%A1%B9%E7%BB%84-source)                |
| multiple       | `boolean`                                                                                                           | `false`                 | [多选](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E5%A4%9A%E9%80%89-multiple)                                               |
| labelField     | `boolean`                                                                                                           | `"label"`               | [选项标签字段](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E9%80%89%E9%A1%B9%E6%A0%87%E7%AD%BE%E5%AD%97%E6%AE%B5-labelfield) |
| valueField     | `boolean`                                                                                                           | `"value"`               | [选项值字段](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E9%80%89%E9%A1%B9%E5%80%BC%E5%AD%97%E6%AE%B5-valuefield)            |
| joinValues     | `boolean`                                                                                                           | `true`                  | [拼接值](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E6%8B%BC%E6%8E%A5%E5%80%BC-joinvalues)                                  |
| extractValue   | `boolean`                                                                                                           | `false`                 | [提取值](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E6%8F%90%E5%8F%96%E5%A4%9A%E9%80%89%E5%80%BC-extractvalue)              |
| autoFill       | `object`                                                                                                            |                         | [自动填充](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E8%87%AA%E5%8A%A8%E5%A1%AB%E5%85%85-autofill)                         |
| visibleDivider   | boolean                        | `false`      | 两两之间显示`｜`分割线，仅 `vertical` 为 false 时生效                                       | `1.24.0` |

### 事件表

当前组件会对外派发以下事件，可以通过`onEvent`来监听这些事件，并通过`actions`来配置执行的动作，在`actions`中可以通过`${事件参数名}`来获取事件产生的数据（`< 2.3.2 及以下版本 为 ${event.data.[事件参数名]}`），详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action)。

> `[name]`表示当前组件绑定的名称，即`name`属性，如果没有配置`name`属性，则通过`value`取值。

| 事件名称 | 事件参数                  | 说明             |
| -------- | ------------------------- | ---------------- |
| change   | `[name]: string` 组件的值 | 选中值变化时触发 |

### 动作表

当前组件对外暴露以下特性动作，其他组件可以通过指定`actionType: 动作名称`、`componentId: 该组件id`来触发这些动作，动作配置可以通过`args: {动作配置项名称: xxx}`来配置具体的参数，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action#触发其他组件的动作)。

| 动作名称 | 动作配置                 | 说明                                                    |
| -------- | ------------------------ | ------------------------------------------------------- |
| clear    | -                        | 清空                                                    |
| reset    | -                        | 将值重置为`resetValue`，若没有配置`resetValue`，则清空  |
| reload   | -                        | 重新加载，调用 `source`，刷新数据域数据刷新（重新加载） |
| setValue | `value: string` 更新的值 | 更新数据                                                |
