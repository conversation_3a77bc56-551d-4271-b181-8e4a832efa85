---
title: Button-Toolbar 按钮工具栏
description:
type: 0
group: null
menuName: Button-Toolbar
icon:
order: 5
---

多个按钮排列组合展示，按钮直接存在默认间距8px。通常用于Crud、GroupContainer等组件带全局操作按钮的场景。

相关组件区分：
1. [Button-Group-Select 按钮点选](/dataseeddesigndocui/#/amis/zh-CN/components/form/button-group-select)：按钮集合当 select 点选用，按钮之间无间距，支持表单项属性及功能。通常作为ForItem使用，或InputTree等组件带筛选按钮的场景。

3. [ButtonGroup 按钮组](/dataseeddesigndocui/#/amis/zh-CN/components/button-group)：用于将配置的多个按钮集合为一个整体，按钮之间无间距，按钮schema可自定义配置actionType、url等属性。

## 场景推荐
### 列表全局操作+单个搜索

```schema
{
  "type": "page",
  "data": {
    "button-group-select": "all"
  },
  "body": {
    "type": "crud",
    "api": "/api/mock2/crud/table4",
    "columnsTogglable": true,
    "headerToolbar": [
      {
        "type": "button-toolbar",
        "maxCount": 2,
        "buttons": [{
          "type": "button",
          "label": "主按钮",
          "actionType": "url",
          "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
          "level": "primary",
          "blank": false
        },
        {
          "type": "button",
          "label": "次按钮1",
          "actionType": "url",
          "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index"
        },
        {
          "type": "button",
          "label": "次按钮2",
          "disabled": true
        },
        {
          "type": "button",
          "label": "次按钮3"
        },
        {
          "type": "button",
          "label": "次按钮4"
        }]
      },
      {
        "type": "search-box",
        "name": "keywords",
        "size": "md",
        "align": "right",
        "placeholder": "请输入"
      },
      {
        "type": "tpl",
        "align": "right",
        "tpl": "关键字:"
      }
    ],
    "columns": [
      {
        "name": "id",
        "label": "ID",
      },
      {
        "name": "id",
        "label": "序号"
      },
      {
        "name": "engine",
        "label": "Rendering engine"
      },
      {
        "name": "browser",
        "label": "Browser"
      },
      {
        "name": "platform",
        "label": "Platform(s)"
      },
      {
        "name": "version",
        "label": "Engine version"
      },
      {
        "name": "grade",
        "label": "CSS grade"
      },
      {
        "type": "operation",
        "label": "操作",
        "buttons": [
          {
            "label": "详情",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "详情",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "删除",
            "type": "button",
            "actionType": "ajax",
            "level": "link",
            "disabled": true,
            "confirmText": "确认要删除吗？",
            "api": {
              "method": "delete",
              "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
            }
          },
          {
            "label": "编辑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "编辑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "空跑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "空跑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          }
        ]
      }
    ]
  }
}

```

### 分组容器带多个全局操作按钮

```schema
{
  "type": "page",
  "data": {
    "text1": "aaaa",
    "text2": 18,
    "text3": "7年级",
    "text4": "<EMAIL>",
    "text5": "12345678",
    "text6": "上海市浦东新区",
    "text7": "测试",
    "text8": "text8",
    "text9": "text9",
    "second1": "<EMAIL>",
    "second2": "12345678",
    "second3": "上海市浦东新区",
    "second4": "上海市浦东新区"
  },
  "body": {
    "type": "tabs",
    "tabs": [
      {
        "title": "Tabs标题",
        "tab": {
          "type": "form",
          "actions": [],
          "static": true,
          "body": [
            {
              "type": "flex",
              "gap": true,
              "direction": "column",
              "items": [
                {
                  "type": "button-toolbar",
                  "buttons": [
                    {
                      "type": "button",
                      "label": "主按钮",
                      "level": "primary"
                    },
                    {
                      "type": "button",
                      "label": "次按钮"
                    }
                  ]
                },
                {
                  "type": "group-container",
                  "collapsible": false,
                  "items": [
                    {
                      "type": "panel",
                      "header": {
                        "title": "第一步，基础信息"
                      },
                      "body": [
                        {
                          "type": "group",
                          "body": [
                            {
                              "type": "input-text",
                              "name": "text1",
                              "label": "姓名"
                            },
                            {
                              "type": "input-text",
                              "name": "text2",
                              "label": "年龄"
                            }
                          ]
                        },
                        {
                          "type": "group",
                          "body": [
                            {
                              "type": "input-text",
                              "name": "text4",
                              "label": "邮箱"
                            },
                            {
                              "type": "input-text",
                              "name": "text5",
                              "label": "电话"
                            }
                          ]
                        },
                        {
                          "type": "group",
                          "body": [
                            {
                              "type": "input-text",
                              "name": "text7",
                              "label": "其它",
                              "columnRatio": 6
                            }
                          ]
                        }
                      ]
                    },
                    {
                      "type": "panel",
                      "header": {
                        "title": "第二步，复杂信息"
                      },
                      "body": [
                        {
                          "type": "group",
                          "body": [
                            {
                              "type": "input-text",
                              "name": "second1",
                              "label": "邮箱"
                            },
                            {
                              "type": "input-text",
                              "name": "second2",
                              "label": "电话"
                            }
                          ]
                        },
                        {
                          "type": "group",
                          "body": [
                            {
                              "type": "input-text",
                              "name": "textarea",
                              "label": "姓名",
                              "maxLength": 30,
                              "showCounter": true,
                              "placeholder": "请输入"
                            }
                          ]
                        },
                        {
                          "type": "group",
                          "body": [
                            {
                              "type": "input-text",
                              "name": "second5",
                              "label": "其它"
                            }
                          ]
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      }
    ]
  }
}
```

## 组件用法
### 基本用法

多个按钮横向排列

```schema
{
  "type": "page",
  "body": {
    "type": "button-toolbar",
    "buttons": [
      {
        "type": "button",
        "label": "主按钮",
        "level": "primary",
        "actionType": "dialog",
        "dialog": {
          "confirmMode": false,
          "title": "提示",
          "body": "对，你刚点击了！"
        }
      },
      {
        "type": "button",
        "actionType": "url",
        "url": "https://www.baidu.com",
        "blank": true,
        "label": "跳转"
      }
    ]
  }
}
```

### 表单项使用

在表单项中使用时，支持配置`label`属性

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "body": [
      {
        "type": "button-toolbar",
        "label": "按钮组",
        "buttons": [
          {
            "type": "button",
            "label": "按钮",
            "actionType": "dialog",
            "dialog": {
              "title": "提示",
              "body": "对，你刚点击了！"
            }
          },
          {
            "type": "submit",
            "label": "提交"
          },
          {
            "type": "reset",
            "label": "重置"
          }
        ]
      },
      {
        "type": "input-text",
        "name": "test",
        "label": "Text"
      }
    ]
  }
}
```

### 超出指定个数

超出指定个数时，显示 “更多” 下拉列表菜单。默认是超出4个按钮展示 “更多” 按钮。设置 `maxCount` 配置，可以调整能够显示按钮的个数，

```schema: scope="body"
{
  "type": "button-toolbar",
  "maxCount": 2,
  "buttons": [
    {
      "type": "button",
      "label": "主按钮",
      "level": "primary",
      "actionType": "dialog",
      "dialog": {
        "confirmMode": false,
        "title": "提示",
        "body": "对，你刚点击了！"
      }
    },
    {
      "type": "button",
      "actionType": "url",
      "url": "https://www.baidu.com",
      "blank": true,
      "label": "跳转"
    },
    {
      "type": "button",
      "label": "按钮1"
    },
    {
      "type": "button",
      "label": "按钮2"
    },
    {
      "type": "button",
      "label": "按钮3"
    }
  ]
}
```

### 按钮标签

当按钮有 `badge` 标签时，会默自动整间认调距。

```schema: scope="body"
{
  "type": "button-toolbar",
  "buttons": [
    {
        "type": "button",
        "label": "主按钮",
        "actionType": "url",
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
        "level": "primary",
        "blank": false,
        "badge": {
          "mode": "text",
          "text": 15,
        },
      },
      {
        "type": "button",
        "label": "次按钮1",
        "actionType": "url",
        "gapSize":"sm",
        "badge": {
          "mode": "text",
          "text": 5,
        },
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index"
      },
      {
        "type": "button",
        "label": "次按钮2",
        "actionType": "url",
        "gapSize":"sm",
        "badge": {
          "mode": "text",
          "text": 10905,
        },
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index"
      },

      {
        "type": "button",
        "label": "次按钮3",
        "actionType": "url",
        "gapSize":"md",
        "badge": {
          "mode": "text",
          "text": "notice",
        },
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index"
      },
      {
        "type": "button",
        "label": "次按钮4",
        "disabled": true
      },
      {
        "type": "button",
        "label": "次按钮5"
      },
      {
        "type": "button",
        "label": "次按钮6"
      }
  ]
}
```

### 属性表

| 属性名  | 类型                        | 默认值             | 说明                      |
| ------- | --------------------------- | ------------------ | ------------------------- |
| type    | `string`                    | `"button-toolbar"` | 指定为 ButtonToolbar 组件 |
| maxCount    | `number`                    | `4` | 超出指定按钮数量的按钮，展示 “更多” 下拉按钮 |
| className      | `string`               |                  | 外层 Dom 的类名            |
| buttons        | `Array<Action>`        |            | [按钮](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/action)           |
