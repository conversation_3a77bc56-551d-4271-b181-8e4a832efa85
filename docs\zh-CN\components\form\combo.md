---
title: Combo 组合
description:
type: 0
group: null
menuName: Combo
icon:
order: 12
---

用于将多个表单项组合到一起，实现深层结构的数据编辑。

比如想提交 `user.name` 这样的数据结构，有两种方法：一种是将表单项的 name 设置为`user.name`，另一种就是使用 combo。

## 场景推荐
### 基本模式

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "mode": "horizontal",
    "api": "/api/mock2/form/saveForm",
    "data": {
      "combo2": [
        {
          "text": "1",
          "select": "a"
        },
        {
          "text": "2",
          "select": "b"
        }
      ]
    },
    "body": [
      {
        "type": "combo",
        "name": "combo2",
        "label": "Combo 多选展示",
        "multiple": true,
        "items": [
          {
            "name": "text",
            "label": "文本",
            "type": "input-text"
          },
          {
            "name": "select",
            "label": "选项",
            "type": "select",
            "options": [
              "a",
              "b",
              "c"
            ]
          }
        ]
      }
    ]
  }
}
```

### 多对多

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "mode": "horizontal",
    "labelWidth": 80,
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
    "data": {
      "combos": [
        {
          "combo1": [
            {
              "text": "333",
              "select": "a"
            },
            {
              "text": "333",
              "select": "a"
            },
            {
              "text": "333",
              "select": "a"
            }
          ],
          "text": "1"
        },
        {
          "combo1": [
            {
              "text": "333sss",
              "select": "a"
            }
          ],
          "text": "1"
        },
        {
          "combo1": [
            {
              "text": "333",
              "select": "a"
            }
          ],
          "text": "1"
        }
      ]
    },
    "body": [
      {
        "type": "group",
        "body": [
          {
            "type": "combo",
            "name": "combos",
            "label": "产品配置",
            "multiple": true,
            "multiLine": true,
            "copyable": true,
            "extraActions": {
              "type": "icon",
              "icon": "fa-exchange",
              "onEvent": {
                "click": {
                  "actions": [
                    {
                      "actionType": "dialog",
                      "showCloseButton": false,
                      "dialog": {
                        "title": "触发标题",
                        "body": "触发了点击"
                      }
                    }
                  ]
                }
              }
            },
            "items": [
              {
                "type": "fieldSet",
                "title": "配置${index + 1}",
                "collapsable": true,
                "body": [
                  {
                    "type": "group",
                    "label": "产品名称",
                    "body": [
                      {
                        "name": "text",
                        "label": false,
                        "type": "input-text"
                      }
                    ]
                  },
                  {
                    "type": "combo",
                    "name": "combo1",
                    "label": false,
                    "multiple": true,
                    "draggable": true,
                    "items": [
                      {
                        "name": "text",
                        "label": "结算方式",
                        "type": "input-text"
                      },
                      {
                        "name": "select",
                        "label": "结算单价",
                        "type": "select",
                        "options": [
                          "a",
                          "b",
                          "c"
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    ],
    "actions": [
      {
        "type": "button",
        "label": "取消"
      },
      {
        "type": "button",
        "label": "提交",
        "level": "primary"
      }
    ]
  }
}
```

#### 落地案例
[产品运营-协议管理-新协议模版-编辑](http://moka.dmz.sit.caijj.net/esignui/#/contractSceneRuleManage)

![产品运营-协议管理-新协议模版-编辑](https://static02.sit.yxmarketing01.com/materialcenter/cd731aa0-64a7-4f50-a9cc-707441d9d7cf.png)

### tabs模式

默认成员是一个一个排列的，如果数据比较多有点让人眼花缭乱。所以 Combo 支持了 tabs 的排列方式，`tabsMode：true` 用来开启此模式。Combo的Tabs模式，目前样式根据规范内置处理为`strong`，暂不支持配置。

> **注意：**这是新引入的功能，目前还不支持拖拽组合使用。且此模式只有多选时才能生效。
>

```schema
{
    "type": "page",
  "body": {
    "title": "",
    "type": "form",
    "mode": "horizontal",
    "autoFocus": false,
    "body": [
      {
        "type": "combo",
        "name": "combo101",
        "label": "组合多条多行",
        "multiple": true,
        "multiLine": true,
        "value": [
          {}
        ],
        "tabsMode": true,
        "subFormMode": "horizontal",
        "subFormHorizontal": {
          "labelWidth": 40
        },
        "maxLength": 3,
        "items": [
          {
            "name": "a",
            "label": "文本",
            "type": "input-text",
            "placeholder": "文本",
            "value": "",
            "size": "full"
          },
          {
            "name": "b",
            "label": "选项",
            "type": "select",
            "options": [
              "a",
              "b",
              "c"
            ],
            "size": "full"
          }
        ]
      }
    ],
    "submitText": null,
    "actions": []
  }
}
```

## 组件用法

### 基本使用

配置`items`属性，组合多个表单项

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "combo",
      "name": "user",
      "label": "用户",
      "items": [
        {
          "name": "text",
          "label": "名字",
          "type": "input-text"
        },
        {
          "name": "gender",
          "label": "性别",
          "type": "select",
          "options": ["男", "女"]
        }
      ]
    }
  ]
}
```

### 多行展示模式

默认，combo 内表单项是横着展示一排，如果想换行展示，可以配置`"multiLine": true`

```schema
{
  "type": "page",
  "body": {
    "type": "page",
    "body": {
      "type": "form",
      "mode": "horizontal",
      "api": "/api/mock2/form/saveForm",
      "body": [
        {
          "type": "combo",
          "name": "combo1",
          "label": "Combo 单行展示",
          "items": [
            {
              "name": "text",
              "label": "文本",
              "type": "input-text"
            },
            {
              "name": "select",
              "label": "选项",
              "type": "select",
              "options": [
                "a",
                "b",
                "c"
              ]
            }
          ]
        },
        {
          "type": "divider"
        },
        {
          "type": "combo",
          "name": "combo2",
          "label": "Combo 多行展示",
          "multiLine": true,
          "items": [
            {
              "name": "text",
              "label": "文本",
              "type": "input-text"
            },
            {
              "name": "select",
              "label": "选项",
              "type": "select",
              "options": [
                "a",
                "b",
                "c"
              ]
            }
          ]
        }
      ]
    }
  }
}
```

### 多选模式

默认，combo 为单选模式，可以配置`"multiple": true`实现多选模式。

这时提交的将会是对象数组。

```schema: scope="body"
{
  "type": "form",
  "mode": "horizontal",
  "api": "/api/mock2/form/saveForm",
  "debug": true,
  "body": [
    {
      "type": "combo",
      "name": "combo1",
      "label": "Combo 单选展示",
      "items": [
        {
          "name": "text",
          "label": "文本",
          "type": "input-text"
        },
        {
          "name": "select",
          "label": "选项",
          "type": "select",
          "options": ["a", "b", "c"]
        }
      ]
    },
    {
        "type": "divider"
    },
    {
      "type": "combo",
      "name": "combo2",
      "label": "Combo 多选展示",
      "multiple": true,
      "items": [
        {
          "name": "text",
          "label": "文本",
          "type": "input-text"
        },
        {
          "name": "select",
          "label": "选项",
          "type": "select",
          "options": ["a", "b", "c"]
        }
      ]
    }
  ]
}
```

### 限制个数

多选模式下，可以配置`minLength`和`maxLength`配置该 Combo 可添加的条数

```schema: scope="body"
{
  "type": "form",
  "mode": "horizontal",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "combo",
      "name": "combo1",
      "label": "最少添加1条",
      "multiple": true,
      "minLength": 1,
      "items": [
        {
          "name": "text",
          "label": "文本",
          "type": "input-text"
        },
        {
          "name": "select",
          "label": "选项",
          "type": "select",
          "options": ["a", "b", "c"]
        }
      ]
    },
    {
      "type": "combo",
      "name": "combo2",
      "label": "最多添加3条",
      "multiple": true,
      "maxLength": 3,
      "items": [
        {
          "name": "text",
          "label": "文本",
          "type": "input-text"
        },
        {
          "name": "select",
          "label": "选项",
          "type": "select",
          "options": ["a", "b", "c"]
        }
      ]
    }
  ]
}
```

也可以使用变量配置`minLength`和`maxLength`

```schema: scope="body"
{
  "type": "form",
  "mode": "horizontal",
  "api": "/api/mock2/form/saveForm",
  "data": {
    "minLength": 2,
    "maxLength": 4
  },
  "body": [
    {
      "type": "combo",
      "name": "combo1",
      "label": "最少添加2条, 最多添加4条",
      "mode": "normal",
      "multiple": true,
      "minLength": "${minLength}",
      "maxLength": "${maxLength}",
      "items": [
        {
          "name": "text",
          "label": "文本",
          "type": "input-text"
        },
        {
          "name": "select",
          "label": "选项",
          "type": "select",
          "options": ["a", "b", "c"]
        }
      ]
    }
  ]
}
```

### 值格式

观察下例中表单数据域值的变化，可以发现：

- 单选模式时，**数据格式为对象**；
- 多选模式时，**数据格式为数组，数组成员是对象**

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "mode": "horizontal",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "combo",
      "name": "combo1",
      "label": "Combo 单选展示",
      "items": [
        {
          "name": "text",
          "label": "文本",
          "type": "input-text"
        },
        {
          "name": "select",
          "label": "选项",
          "type": "select",
          "options": ["a", "b", "c"]
        }
      ]
    },
    {
        "type": "divider"
    },
    {
      "type": "combo",
      "name": "combo2",
      "label": "Combo 多选展示",
      "multiple": true,
      "items": [
        {
          "name": "text",
          "label": "文本",
          "type": "input-text"
        },
        {
          "name": "select",
          "label": "选项",
          "type": "select",
          "options": ["a", "b", "c"]
        }
      ]
    }
  ]
}
```

### 打平值

默认多选模式下，数据格式是对象数组的形式，当你配置的组合中只有一个表单项时，可以配置`"flat": true`，将值进行打平处理。

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "mode": "horizontal",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
        "type": "combo",
        "name": "combo1",
        "label": "默认模式",
        "multiple": true,
        "items": [
            {
                "name": "text",
                "type": "input-text"
            }
        ]
    },
    {
        "type": "divider"
    },
    {
        "type": "combo",
        "name": "combo2",
        "label": "打平模式",
        "multiple": true,
        "flat": true,
        "items": [
            {
                "name": "text",
                "type": "input-text"
            }
        ]
    }
  ]
}
```

查看上例表单数据域，可以看到打平后数据格式如下：

```json
{
  "combo2": ["aaa", "bbb"]
}
```

### 增加层级

combo 还有一个作用是增加层级，比如返回的数据是一个深层对象

```json
{
  "a": {
    "b": "data"
  }
}
```

如果要用文本框显示，name 必须是 `a.b`，但使用 combo 创建层级后，name 就可以只是 `b`：

```json
{
  "name": "a",
  "type": "combo",
  "label": "",
  "noBorder": true,
  "multiLine": true,
  "items": [
    {
      "type": "input-text",
      "name": "b"
    }
  ]
}
```

这样就能结合 [definitions](/dataseeddesigndocui/#/amis/zh-CN/docs/types/definitions#树形结构) 实现无限层级结构。

### 唯一验证

可以在配置的`body`项上，配置`"unique": true`，指定当前表单项不可重复

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "mode": "horizontal",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
        "type": "combo",
        "name": "combo666",
        "label": "唯一",
        "multiple": true,
        "items": [
            {
                "name": "text",
                "type": "input-text",
                "placeholder": "文本",
                "unique": true
            },
            {
                "name": "select",
                "type": "select",
                "options": [
                    "a",
                    "b",
                    "c"
                ],
                "unique": true
            }
        ]
    }
  ]
}
```

上例中，`text`和`select`都配置了`"unique": true`，新增多条 combo，在任意两个`text`输入框的值相同时，提交时都会报错`"当前值不唯一"`，而`select`选择框也不可选择重复的选项

> 暂不支持 `Nested-Select` 组件

### 拖拽排序

多选模式下，可以配置`"draggable": true`实现拖拽调整排序

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "mode": "horizontal",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
        "type": "combo",
        "name": "combo",
        "label": "拖拽排序",
        "multiple": true,
        "value": [
            {
                "text": "1",
                "select": "a"
            },
            {
                "text": "2",
                "select": "b"
            }
        ],
        "draggable": true,
        "items": [
            {
                "name": "text",
                "type": "input-text"
            },
            {
                "name": "select",
                "type": "select",
                "options": [
                    "a",
                    "b",
                    "c"
                ]
            }
        ]
    }
  ]
}
```


### tabs模式标题与图标配置

`tabsLabelTpl` 用来生成标题文本模板，默认为：`成员 ${index|plus}`

`tabsIconTpl` 用来生成标题前图标，支持表达式

`tabsIconPosition` 图标位置，可选 `left`、`right`（默认 `left`），支持表达式

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "mode": "horizontal",
    "body": [
      {
        "type": "combo",
        "name": "combo101",
        "label": "配置标题图标",
        "multiple": true,
        "tabsMode": true,
        "tabsLabelTpl": "成员${index+1}",
        "tabsIconTpl": "${index === 0 ? 'fa fa-user' : 'fa fa-cog'}",
        "value": [{}],
        "items": [
          {
            "name": "a",
            "type": "input-text",
            "label": "文本"
          }
        ]
      }
    ]
  }
}
```

注意：此模式只有多选时才能生效。

- `tabsMode` boolean 用来开启此模式
- `tabsLabelTpl` 用来生成标题文本模板，默认为：`成员 ${index|plus}`
- `tabsIconTpl` 用来生成标题前图标，支持表达式
- `tabsIconPosition` 图标位置，可选 `left`、`right`（默认 `left`），支持表达式

### Tabs 模式 mountOnEnter

> `1.11.0` 版本支持
默认是按需加载，切换tab才渲染。

```schema
{
   "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "mode": "horizontal",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "data": {
      "combo101": [
        {}
      ]
    },
    "body": {
      "type": "combo",
      "name": "combo101",
      "label": "组合多条多行",
      "multiple": true,
      "tabsMode": true,
      "mountOnEnter": false,
      "subFormMode": "horizontal",
      "subFormHorizontal": {
        "labelWidth": 40
      },
      "items": [
        {
          "name": "a",
          "label": "文本",
          "type": "input-text",
          "placeholder": "文本",
          "value": "",
          "size": "full",
          "required": true
        },
        {
          "name": "b",
          "label": "选项",
          "type": "select",
          "options": [
            "a",
            "b",
            "c"
          ],
          "size": "full"
        }
      ]
    }
  }
}
```

### Tabs 模式表达式配置

> `1.87.4` 版本支持

`mountOnEnter` 和 `unmountOnExit` 支持表达式配置，可以为不同的选项卡单独配置是否按需加载和销毁。表达式上下文包含：
- `index`: 当前选项卡索引（从0开始）
- `__index`: 当前选项卡索引（从0开始）
- `__items`: 所有选项卡数据数组
- 当前选项卡的数据

```schema
{
   "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "mode": "horizontal",
    "api": "/api/mock2/form/saveForm",
    "data": {
      "combo": [
        {"name": "Tab 1", "content": "内容1"},
        {"name": "Tab 2", "content": "内容2"},
        {"name": "Tab 3", "content": "内容3"}
      ]
    },
    "body": {
      "type": "combo",
      "name": "combo",
      "label": "表达式配置示例",
      "multiple": true,
      "tabsMode": true,
      "mountOnEnter": "${index !== 1}",
      "unmountOnExit": "${index === 0 || index === 2}",
      "subFormMode": "horizontal",
      "subFormHorizontal": {
        "labelWidth": 60
      },
      "items": [
        {
          "name": "name",
          "label": "名称",
          "type": "input-text",
          "placeholder": "请输入名称"
        },
        {
          "name": "content",
          "label": "内容",
          "type": "textarea",
          "placeholder": "请输入内容"
        }
      ]
    }
  }
}
```

上例中：
- `mountOnEnter: "${index !== 1}"` 表示除了第2个选项卡，其他选项卡都会立即渲染
- `unmountOnExit: "${index === 0 || index === 2}"` 表示第1个和第3个选项卡在隐藏时会被销毁

- `mountOnEnter` boolean 类型，只有在点击卡片的时候才会渲染，在内容较多的时候可以提升性能。默认值 true.
- `unmountOnExit` 切换 tab 时，自动销毁掉隐藏的 tab

### Tabs 模式新增弹窗

配置`addFormDialog`属性，点击新增会先弹窗，然后将弹窗表单中的数据添加到 Combo 中。

```schema
{
    "type": "page",
  "body": {
    "title": "",
    "type": "form",
    "debug": true,
    "mode": "horizontal",
    "autoFocus": false,
    "body": [
      {
        "type": "combo",
        "name": "combo101",
        "label": "组合多条多行",
        "multiple": true,
        "multiLine": true,
        "value": [
          {}
        ],
        "maxLength": 3,
        "tabsMode": true,
        "subFormMode": "horizontal",
        "subFormHorizontal": {
          "labelWidth": 40
        },
        "items": [
          {
            "name": "a",
            "label": "文本",
            "type": "input-text",
            "placeholder": "文本",
            "value": "",
            "size": "full"
          },
          {
            "name": "b",
            "label": "选项",
            "type": "select",
            "options": [
              "a",
              "b",
              "c"
            ],
            "size": "full"
          }
        ],
        "addFormDialog": {
          "title": "新增",
          "body": [
            {
              "name": "a",
              "label": "文本",
              "type": "input-text"
            }
          ]
        }
      }
    ]
  }
}
```

### Tabs 模式activeKey
Tabs模式支持设置`activeKey`，默认为第一个tab，如果设置`activeKey`，则激活`activeKey`对应的tab。

> 初始化组件时 `defaultKey` 优先级高于 `activeKey`，但 `defaultKey` 仅作用于组件初始化时，不会响应上下文数据变化。

```schema
{
    "type": "page",
  "data": {
    "defaultKey": 1,
  },
  "body": {
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "mode": "horizontal",
    "body": [
      {
        "type": "radios",
        "name": "key",
        "label": "激活的选项卡",
        "options": [
          {
            "label": "Tab 1",
            "value": 0
          },
          {
            "label": "Tab 2",
            "value": 1
          },
          {
            "label": "Tab 3",
            "value": 2
          }
        ]
      },
      {
        "type": "combo",
        "name": "combo101",
        "label": "Tabs模式",
        "multiple": true,
        "multiLine": true,
        "strictMode": false,
        "value": [
          {},
          {},
          {}
        ],
        "tabsMode": true,
        "subFormMode": "horizontal",
        "subFormHorizontal": {
          "labelWidth": 40
        },
        "activeKey": "${key|toInt}",
        "defaultKey": "${defaultKey|toInt}",
        "items": [
          {
            "name": "a",
            "label": "文本",
            "type": "input-text",
            "placeholder": "文本",
            "required": true
          },
          {
            "name": "b",
            "label": "选项",
            "type": "select",
            "options": [
              "a",
              "b",
              "c"
            ],
            "size": "full"
          }
        ]
      }
    ]
  }
}
```

### 获取父级数据

默认情况下，Combo 内表达项无法获取父级数据域的数据，如下，我们添加 Combo 表单项时，尽管 Combo 内的文本框的`name`与父级数据域中的`super_text`变量同名，但是没有自动映射值。

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "mode": "horizontal",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
        "type": "input-text",
        "label": "父级文本框",
        "name": "super_text",
        "value": "123"
    },
    {
        "type": "combo",
        "name": "combo1",
        "label": "不可获取父级数据",
        "multiple": true,
        "items": [
            {
                "name": "super_text",
                "type": "input-text"
            }
        ]
    }
  ]
}
```

可以配置`"canAccessSuperData": true`开启此特性，如下，配置了该配置项后，添加 Combo 的`text`表单项会自动映射父级数据域的同名变量

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "mode": "horizontal",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
        "type": "input-text",
        "label": "父级文本框",
        "name": "super_text",
        "value": "123"
    },
    {
        "type": "combo",
        "name": "combo2",
        "label": "可获取父级数据",
        "multiple": true,
        "canAccessSuperData": true,
        "items": [
            {
                "name": "super_text",
                "type": "input-text"
            }
        ]
    }
  ]
}
```

### 同步更新内部表单项

配置`canAccessSuperData`可以获取父级数据域值，但是为了效率，在父级数据域变化的时候，默认 combo 内部是不会进行同步的

如下，添加一组 combo，然后可以看到默认会映射父级变量值`123`，但是当你在更改父级数据域`super_text`文本框值后，combo 内部文本框并没有同步更新

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "mode": "horizontal",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
        "type": "input-text",
        "label": "父级文本框",
        "name": "super_text",
        "value": "123"
    },
    {
        "type": "combo",
        "name": "combo2",
        "label": "可获取父级数据",
        "multiple": true,
        "canAccessSuperData": true,
        "items": [
            {
                "name": "super_text",
                "type": "input-text"
            }
        ]
    }
  ]
}
```

如果想实现内部同步更新，需要如下配置：

- 配置`"strictMode": false`
- 配置`syncFields`字符串数组，数组项是需要同步的变量名

以上面为例，我们在 combo 上配置`"strictMode": false`和`"syncFields": ["super_text"]`，即可实现同步

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "mode": "horizontal",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
        "type": "input-text",
        "label": "父级文本框",
        "name": "super_text",
        "value": "123"
    },
    {
        "type": "combo",
        "name": "combo2",
        "label": "可获取父级数据",
        "multiple": true,
        "canAccessSuperData": true,
        "strictMode": false,
        "syncFields": ["super_text"],
        "items": [
            {
                "name": "super_text",
                "type": "input-text"
            }
        ]
    }
  ]
}
```

### 设置序号

默认 Combo 数据域中，每一项会有一个隐藏变量`index`，可以利用 Tpl 组件，显示当前项序号

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "mode": "horizontal",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
        "type": "combo",
        "name": "combo",
        "label": "显示序号",
        "multiple": true,
        "items": [
            {
                "type": "tpl",
                "tpl": "<%= this.index + 1%>",
                "className": "p-t-xs",
                "mode": "inline"
            },
            {
                "name": "text",
                "type": "input-text"
            }
        ]
    }
  ]
}
```

### 子表单强制校验

> 默认情况下，当 formItem 值改变时会触发校验，通过后就不会再触发。

可以通过配置 `subFormForceValidate` 属性实现强制校验整个表单的所有 formItem

适用场景：子表单中一个 formItem 变更 想触发其他的 formItem 进行联动校验的场景


### 操控子表单

> `1.89.1` 版本支持

Combo 渲染内部子表单时，会自动为子表单生成 `name` 属性，方便通过 `componentName` 精准定位。生成规则如下：

1. **单条模式**（`multiple: false`）
   ```text
   ${comboName}-form-0
   ```
2. **多条模式**（`multiple: true`）
   ```text
   ${comboName}-form-${index}
   ```
   其中 `index` 为当前行序号（从 0 开始）。因此在事件动作中，可以写成 `${'comboName-form-' + index}` 来 **动态** 指向当前行的子表单。

下面的示例在每个子表单添加一个"校验"按钮，点击后只校验当前行字段 `a`，并通过 `toast` 弹出校验结果。

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "mode": "horizontal",
    "body": [
      {
        "type": "combo",
        "name": "combo101",
        "label": "组合多条多行",
        "multiple": true,
        "multiLine": true,
        "tabsMode": true,
        "value": [{}],
        "items": [
          {
            "name": "a",
            "label": "文本",
            "type": "input-text",
            "required": true
          },
          {
            "name": "b",
            "label": "选项",
            "type": "select",
            "options": ["a", "b", "c"],
            "required": true
          },
          {
            "type": "flex",
            "justify": "flex-end",
            "items": [{
              "type": "button",
              "label": "校验",
              "onEvent": {
                "click": {
                  "actions": [
                    {
                      "actionType": "validate",
                      "componentName": "${'combo101-form-' + index}",
                      "outputVar": "validateRes",
                      "args": {
                        "validateFields": ["a"]
                      }
                    },
                    {
                      "actionType": "toast",
                      "args": {
                        "msg": "${validateRes|json}"
                      }
                    }
                  ]
                }
              }
            }],
          }
        ]
      }
    ]
  }
}
```

更多讨论可参考 playground 中的示例文件 `issue1265.js`。

### 复制

配置`items`属性，组合多个表单项

```schema
{
    "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "combo",
        "name": "user",
        "label": "用户",
        "multiple": true,
        "multiLine": true,
        "copyable": true,
        "value": [
          {}
        ],
        "items": [
          {
            "name": "text",
            "label": "名字",
            "type": "input-text"
          },
          {
            "name": "gender",
            "label": "性别",
            "type": "select",
            "options": [
              "男",
              "女"
            ]
          }
        ]
      }
    ]
  }
}
```

### 行新增按钮

combo 可以通过设置`"itemAddable": true`开启行新增按钮，点击后在当前行下面新增一条。  

配置`itemAddVisibleOn`表达式时，可通过`__items`获取combo全部数据。


```schema
{
    "type": "page",
  "body": {
    "type": "form",
    "mode": "horizontal",
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "combo",
        "name": "combo1",
        "label": "Combo 多行展示",
        "multiLine": true,
        "multiple": true,
        "itemAddable": true,
        "itemAddVisibleOn": "${select === 'a' || index === 0 || __items.length - 1 === index}",
        "value": [
          {}
        ],
        "items": [
          {
            "name": "text",
            "label": "文本",
            "type": "input-text"
          },
          {
            "name": "select",
            "label": "选项",
            "type": "select",
            "options": [
              "a",
              "b",
              "c"
            ]
          }
        ]
      }
    ]
  }
}
```

### 属性表

当做选择器表单项使用时，除了支持 [普通表单项属性表](/dataseeddesigndocui/#/amis/zh-CN/components/form/formitem#%E5%B1%9E%E6%80%A7%E8%A1%A8) 中的配置以外，还支持下面一些配置

| 属性名                           | 类型                                                                        | 默认值                                   | 说明                                                                                                                                                             | 版本 |
| -------------------------------- | --------------------------------------------------------------------------- | ---------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------- | ---- |
| formClassName                    | `string`                                                                    |                                          | 单组表单项的类名                                                                                                                                                 |      |
| items                            | Array<[表单项](/dataseeddesigndocui/#/amis/zh-CN/components/form/formitem)> |                                          | 组合展示的表单项                                                                                                                                                 |      |
| items[x].columnClassName         | `string`                                                                    |                                          | 列的类名，可以用它配置列宽度。默认平均分配。                                                                                                                     |      |
| items[x].unique                  | `boolean`                                                                   |                                          | 设置当前列值是否唯一，即不允许重复选择。                                                                                                                         |      |
| noBorder                         | `boolean`                                                                   | `false`                                  | 单组表单项是否显示边框                                                                                                                                           |      |
| scaffold                         | `object`                                                                    | `{}`                                     | 单组表单项初始值                                                                                                                                                 |      |
| multiple                         | `boolean`                                                                   | `false`                                  | 是否多选                                                                                                                                                         |      |
| multiLine                        | `boolean`                                                                   | `false`                                  | 默认是横着展示一排，设置以后竖着展示                                                                                                                             |      |
| minLength                        | `number`                                                                    |                                          | 最少添加的条数，支持变量                                                                                                                                         |      |
| maxLength                        | `number`                                                                    |                                          | 最多添加的条数，支持变量                                                                                                                                         |      |
| flat                             | `boolean`                                                                   | `false`                                  | 是否将结果扁平化(去掉 name),只有当 items 的 length 为 1 且 multiple 为 true 的时候才有效。                                                                       |      |
| joinValues                       | `boolean`                                                                   | `true`                                   | 默认为 `true` 当扁平化开启的时候，是否用分隔符的形式发送给后端，否则采用 array 的方式。                                                                          |      |
| delimiter                        | `string`                                                                    | `false`                                  | 当扁平化开启并且 joinValues 为 true 时，用什么分隔符。                                                                                                           |      |
| addable                          | `boolean`                                                                   | `false`                                  | 是否可新增                                                                                                                                                       |      |
| removable                        | `boolean`                                                                   |                                   | 是否可删除                                                                                                                                                       |      |
| deleteApi                        | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api)                     |                                          | 如果配置了，则删除前会发送一个 api，请求成功才完成删除                                                                                                           |      |
| deleteConfirmText                | `string`                                                                    | `"确认要删除？"`                         | 当配置 `deleteApi` 才生效！删除时用来做用户确认                                                                                                                  |      |
| copyable                        | `boolean`                                                                   | `false`                                  | 是否支持复制。multiple为true时有效。                                                                                                                                                       |  `1.53.0`    |
| copyBtn                        | [Button](/dataseeddesigndocui/#/amis/zh-CN/components/button) or`string`    | 自定义复制按钮                           | 只有当`copyable`为 `true` 时有效; 如果为`string`则为按钮的文本；如果为`Button`则根据配置渲染复制按钮。                                                          |  `1.53.0`    |
| copyIcon                        | `string`    |                            |         复制按钮icon                                                          |  `1.53.0`    |
| itemAddable                        | `boolean`                                                                   | `false`                                  | 行新增按钮。multiple为true时有效。    |  `1.53.1`    |
| itemAddBtn         | [Button](/dataseeddesigndocui/#/amis/zh-CN/components/button) or`string`    | 自定义行新增按钮    | 只有当`itemAddable`为 `true` 时有效; 如果为`string`则为按钮的文本；如果为`Button`则根据配置渲染行新增按钮。    |  `1.53.1`    |
| itemAddIcon                        | `string`    |                            |         行新增按钮icon                         |  `1.53.1`    |
| itemAddVisibleOn                   | [表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression)    |                            |  行新增按钮是否展示，`itemAddable: true`时生效         |   `1.53.1`   |
| draggable                        | `boolean`                                                                   | `false`                                  | 是否可以拖动排序, 需要注意的是当启用拖动排序的时候，会多一个\$id 字段                                                                                            |      |
| draggableTip                     | `string`                                                                    |                                          | 可拖拽的提示文字                                                                                                                                                 |      |
| subFormMode                      | `string`                                                                    | `"horizontal"`                               | 可选`normal`、`horizontal`、`inline`                                                                                                                             |      |
| subFormHorizontal                | `Object`                                                                    | `{"left":2, "right":10, justify: false,labelWidth: number}` | 当 subFormMode 为 `horizontal` 时有用，用来控制 label 的展示占比                                                                                                 |      |
| subFormForceValidate             | `boolean`                                                                   | `false`                                  | 是否开启子表单强制校验. `注意：开启此项后每次触发校验都会重新校验当前子表单的所有 formItem`(支持版本：1.9.0+)                                                    |      |
| placeholder                      | `string`                                                                    | ``                                       | 没有成员时显示。                                                                                                                                                 |      |
| canAccessSuperData               | `boolean`                                                                   | `false`                                  | 指定是否可以自动获取上层的数据并映射到表单项上                                                                                                                   |      |
| conditions                       | `object`                                                                    |                                          | 数组的形式包含所有条件的渲染类型，单个数组内的`test` 为判断条件，数组内的`items`为符合该条件后渲染的`schema`                                                     |      |
| typeSwitchable                   | `boolean`                                                                   | `false`                                  | 是否可切换条件，配合`conditions`使用                                                                                                                             |      |
| strictMode                       | `boolean`                                                                   | `true`                                   | 默认为严格模式，设置为 false 时，当其他表单项更新是，里面的表单项也可以及时获取，否则不会。                                                                      |      |
| syncFields                       | `Array<string>`                                                             | `[]`                                     | 配置同步字段。只有`strictMode`为`false` 时有效。如果 Combo 层级比较深，底层的获取外层的数据可能不同步。但是给 combo 配置这个属性就能同步下来。输入格式：`["os"]` |      |
| nullable                         | `boolean`                                                                   | `false`                                  | 允许为空，如果子表单项里面配置验证器，且又是单条模式。可以允许用户选择清空（不填）。                                                                             |      |
| itemClassName                    | `string`                                                                    |                                          | 单组 CSS 类                                                                                                                                                      |      |
| itemsWrapperClassName            | `string`                                                                    |                                          | 组合区域 CSS 类                                                                                                                                                  |      |
| deleteBtn                        | [Button](/dataseeddesigndocui/#/amis/zh-CN/components/button) or`string`    | 自定义删除按钮                           | 只有当`removable`不为 `false` 时有效; 如果为`string`则为按钮的文本；如果为`Button`则根据配置渲染删除按钮。                                                          |      |
| deleteIcon                        | `string`    |                            | 删除按钮icon                                                          |      |
| itemRemovableOn                   | [表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression)    |                            |  是否可删除，`removable`不为 `false` 时有效。                                                          |      |
| itemCopyableOn                   | [表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression)    |                            |  是否可复制，`copyable`不为 `false` 时有效。                                                          |   `1.84.3`   |
| addBtn                           | [Button](/dataseeddesigndocui/#/amis/zh-CN/components/button)               | 自定义新增按钮                           | 可新增自定义配置渲染新增按钮，在`tabsMode: true`下不生效。                                                                                                       |      |
| addButtonClassName               | `string`                                                                    |                                          | 新增按钮 CSS 类名                                                                                                                                                |      |
| addButtonText                    | `string`                                                                    | `"新增"`                                 | 新增按钮文字                                                                                                                                                     |      |
| messages                         | `object`                                                                    |                                          | 覆盖消息提示                                                                                                                                                     |      |
| messages.validateFailed          | `string`                                                                    |                                          | 验证错误提示                                                                                                                                                     |      |
| alwaysShowAddBtn | `boolean` | `false` | 始终展示新增按钮，`addable`为`true`时生效 |  `1.49.0`  |
| extraActions            | SchemaNode \| SchemaNode[]   |         |  支持自定义额外的操作   | `1.53.0`|
| addFormDialog             | [Form](/dataseeddesigndocui/#/amis/zh-CN/components/form/index) |          | Tabs模式时，点新增的弹窗表单配置，同 [Form](/dataseeddesigndocui/#/amis/zh-CN/components/form/index)                         |  `1.53.1`  |
| defaultKey            | `number`               |                  | Tabs模式时，组件初始化时激活的选项卡，索引值，支持使用表达。              | `1.61.0` |
| activeKey             | `number`               |                  | Tabs模式时，激活的选项卡，索引值，支持使用表达式，可响应上下文数据变化。    | `1.61.0` |
| reverseMode | `boolean`  |   `false`      | 倒序增加模式 | `1.84.3` |
| disableDropTop | `number` | `0` | 前序数量不可拖拽                                                                                    | `1.84.3` |
| disableDropBottom | `number` | `0` | 后序数量不可拖拽                                                                                    | `1.84.3` |
| tabsMode | `boolean` | `false` | 是否采用 tabs 展示模式。仅在`multiple`为`true`时生效 | |
| tabsStyle | `string` | | Tabs 的展示模式，可选：`''`、`'line'`、`'card'`、`'radio'`、`'strong'` | |
| tabsLabelTpl | `string` | `"成员 ${index\|plus}"` | 选项卡标题的生成模板 | |
| tabsIconTpl | `string` |  | 选项卡图标的生成模板，支持表达式。 | 1.89.1 |
| tabsIconPosition | `string` | `'left'` | 选项卡图标位置，可选`left`、`right`，支持表达式。 | 1.89.1 |
| mountOnEnter | `boolean` \| `string` | `true` | 选项卡首次激活时才渲染。仅在`tabsMode`为`true`时生效。支持表达式，表达式上下文包含：`index`（选项卡索引）、`__index`（选项卡索引）、`__items`（所有选项卡数据）、当前选项卡数据 | `1.11.0+`（表达式：`1.87.4+`） |
| unmountOnExit | `boolean` \| `string` | `false` | 选项卡隐藏时销毁节点。仅在`tabsMode`为`true`时生效。支持表达式，表达式上下文包含：`index`（选项卡索引）、`__index`（选项卡索引）、`__items`（所有选项卡数据）、当前选项卡数据 | `1.11.0+`（表达式：`1.87.4+`） |

### 事件表

当前组件会对外派发以下事件，可以通过`onEvent`来监听这些事件，并通过`actions`来配置执行的动作，在`actions`中可以通过`${事件参数名}`来获取事件产生的数据，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action)。

> `[name]`表示当前组件绑定的名称，即`name`属性，如果没有配置`name`属性，则通过`value`取值。

| 事件名称   | 事件参数                                                                                         | 说明                                         | 版本 |
| ---------- | ------------------------------------------------------------------------------------------------ | -------------------------------------------- | ---- |
| add        | `[name]: object \| object[]` 组件的值                                                            | 添加组合项时触发|      |
| copy        | `value: object` 组件的值<br />`copyValue: object` 复制项值<br />`copyIndex: number` 复制项的索引                                                            | 复制组合项时触发|   1.87.2   |
| addSuccess        | `[name]: object \| object[]` 组件的值                                                            | 添加组合项成功时触发                             |   1.38.1   |
| delete     | `key: number` 移除项的索引<br />`item: object` 移除项<br />`[name]: object \| object[]` 组件的值 | 删除组合项时触发                             |      |
| tabsChange | `key: number` 选项卡索引<br />`item: object` 激活项<br />`[name]: object \| object[]` 组件的值   | 当设置 tabsMode 为 true 时，切换选项卡时触发 |      |

### 动作表

当前组件对外暴露以下特性动作，其他组件可以通过指定`actionType: 动作名称`、`componentId: 该组件id`来触发这些动作，动作配置可以通过`args: {动作配置项名称: xxx}`来配置具体的参数，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action#触发其他组件的动作)。

| 动作名称 | 动作配置                                                                          | 说明                                                                                              | 版本 |
| -------- | --------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------- | ---- |
| addItem  | `item: object` 新增项的值；`index: number` 新增项的索引；                                        | 只有开启`multiple`模式才能使用, `multiple`模式下，给新增项添加默认值                              |      |
| clear    | -                                                                                 | 清空                                                                                              |      |
| reset    | -                                                                                 | 将值重置为`resetValue`，若没有配置`resetValue`，则清空                                            |
| setValue | `value: object \| Array<object>` 更新的值<br/>`index?: number` 指定更新的数据索引 | 更新数据，对象数组针对开启`multiple`模式, `multiple`模式下可以通过指定`index`来更新指定索引的数据 |      |
| changeActiveKey   | `activeKey: number` 指定激活选项卡索引                                                        | 激活指定的选项卡 |  `1.61.0`    |

### 动作示例

#### 复制数值

此示例主要用来演示如何通过已有数据快速填充 combo 某条数据。点击 copy 按钮会弹出一个 crud 列表，点击对应行上的复制按钮，将选中数据填充到外层的 combo.

注意事项：

1. 需要给 combo 设置个 id 属性，用来给事件动作指定目标用。
2. 弹窗按钮配置了数据映射 `{comboIndex: "${index}"}` 因为 crud 的行数据上也有 index 变量，派送动作时获取 index 变量是 crud 所在行的序号。所以弹出弹窗的时候，先把 combo 的序号赋值给 comboIndex
3. crud 操作栏里面添加了个按钮，close: true 设置是让动作完成后关闭弹窗。
4. 按钮里面添加了 onEvent 配置，click 时做 `setValue` 动作，并设置参数 index 为 '${comboIndex}' 值为 `${&}`。其中 `${&}` 是特殊语法，用来取整个上下数据。

```schema: scope="body"
{
  "type": "form",
  "mode": "horizontal",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "combo",
      "name": "combo",
      "id": "thecombo",
      "multiple": true,
      "value": [
        {
          "engine": ""
        }
      ],
      "items": [
        {
          "name": "engine",
          "type": "input-text"
        },
        {
          "label": "Copy",
          "type": "button",
          "actionType": "dialog",
          "size": "md",
          "dialog": {
            "title": "历史记录",
            "actions": [],
            "data": {
              "comboIndex": "${index}"
            },
            "body": [
              {
                "type": "crud",
                "api": "/api/mock2/sample",
                "columns": [
                  {
                    "label": "Engine",
                    "name": "engine"
                  },
                  {
                    "type": "operation",
                    "label": "操作",
                    "buttons": [
                      {
                        "label": "复制",
                        "type": "button",
                        "close": true,
                        "onEvent": {
                          "click": {
                            "actions": [
                              {
                                "componentId": "thecombo",
                                "actionType": "setValue",
                                "args": {
                                  "index": "${comboIndex}",
                                  "value": "${&}"
                                }
                              }
                            ]
                          }
                        }
                      }
                    ]
                  }
                ]
              }
            ]
          }
        }
      ]
    }
  ]
}
```

#### 行记录内表单项联动

在 combo 中行记录内表单项联动需要指定`componentName`为需要联动的表单项名称，以下示例中，当选择指定行内第一个下拉框的值时，将对应的修改所在行内第二个下拉框的值。

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "data": {
    "combo": [
      {
        "select_1": "A",
        "select_2": "c"
      },
      {
        "select_1": "A",
        "select_2": "d"
      }
    ]
  },
  "mode": "horizontal",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "combo",
      "label": "组合输入",
      "name": "combo",
      "multiple": true,
      "addable": true,
      "removable": true,
      "removableMode": "icon",
      "addBtn": {
        "label": "新增",
        "icon": "fa fa-plus",
        "level": "primary",
        "size": "sm",
        "onEvent": {
          "click": {
            "weight": 0,
            "actions": [
            ]
          }
        }
      },
      "items": [
        {
          "type": "select",
          "label": "选项${index}",
          "name": "select_1",
          "options": [
            {
              "label": "选项A",
              "value": "A"
            },
            {
              "label": "选项B",
              "value": "B"
            }
          ],
          "multiple": false,
          "onEvent": {
            "change": {
              "actions": [
                {
                  "componentName": "select_2",
                  "args": {
                    "value": "${IF(event.data.value==='A','c','d')}"
                  },
                  "actionType": "setValue"
                }
              ]
            }
          }
        },
        {
          "type": "select",
          "name": "select_2",
          "placeholder": "选项",
          "options": [
            {
              "label": "C",
              "value": "c"
            },
            {
              "label": "D",
              "value": "d"
            }
          ]
        }
      ]
    }
  ]
}
```

#### 嵌套结构中行记录内表单项联动

这里所说的是列表结构数据的嵌套。下面的示例中，combo 内包含一个表格编辑框，即 combo 数据是一个列表结构，它的记录中嵌套了另一个列表结构（input-table）。想要实现 input-table 内行记录【修改】操作只更新所在行记录中的表单项。通过`componentName`来指定所需更新的字段名，它将帮你定位到当前操作行。

```schema
{
  "type": "page",
  "body": {
  "type": "form",
  "debug": true,
  "data": {
    "combo": [
      {
        "table": [{
          "name": "amis",
          "age": "18"
        }]
      },
      {
        "table": [{
          "name": "boss",
          "age": "10"
        }]
      }
    ]
  },
  "mode": "horizontal",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "combo",
      "name": "combo",
      "id": "comboId",
      "label": false,
      "strictMode": false,
      "multiple": true,
      "addBtn": {
        "type": "button",
        "label": "增加",
        "level": "default",
        "block": true
      },
      "items": [
        {
          "type": "input-table",
          "name": "table",
          "strictMode": false,
          "label": false,
          "needConfirm": false,
          "addable": true,
          "removable": true,
          "columns": [
            {
              "label": "姓名",
              "name": "name",
              "quickEdit": false
            },
            {
              "label": "年龄",
              "name": "age"
            },
            {
              "type": "input-group",
              "label": "操作",
              "quickEdit": false,
              "buttons": [
                {
                  "type": "button",
                  "level": "link",
                  "onEvent": {
                    "click": {
                      "actions": [
                        {
                          "dialog": {
                            "closeOnEsc": false,
                            "body": [
                              {
                                "onEvent": {
                                  "validateSucc": {
                                    "weight": 0,
                                    "actions": [
                                      {
                                        "actionType": "closeDialog"
                                      },
                                      {
                                        "args": {
                                          "index": "${index}",
                                          "value": {
                                            "name": "$name",
                                            "age": "$age"
                                          }
                                        },
                                        "actionType": "setValue",
                                        "componentName": "table"
                                      }
                                    ]
                                  }
                                },
                                "body": [
                                  {
                                    "label": "姓名",
                                    "name": "name",
                                    "type": "input-text",
                                    "required": true
                                  },
                                  {
                                    "label": "年龄",
                                    "name": "age",
                                    "type": "input-text",
                                    "required": true
                                  }
                                ],
                                "type": "form",
                                "title": "表单"
                              }
                            ],
                            "type": "dialog",
                            "title": "行记录",
                            "showLoading": true,
                            "withDefaultData": true,
                            "dataMapSwitch": true,
                            "size": "lg",
                            "showErrorMsg": true,
                            "showCloseButton": true,
                          },
                          "actionType": "dialog"
                        }
                      ]
                    }
                  },
                  "label": "修改"
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}

}

```

#### Tabs模式激活指定选项卡
通过`changeActiveKey`动作，可以激活指定选项卡，`activeKey`索引从1开始。

```schema
{
    "type": "page",
  "body": [
    {
      "type": "button",
      "label": "激活Tab",
      "className": "mb-4",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "changeActiveKey",
              "componentId": "activeComboId",
              "args": {
                "activeKey": 3
              }
            }
          ]
        }
      }
    },
    {
      "type": "form",
      "api": "/api/mock2/form/saveForm",
      "mode": "horizontal",
      "labelWidth": 60,
      "body": [
        {
          "type": "combo",
          "name": "combo101",
          "id": "activeComboId",
          "label": "Tabs模式",
          "multiple": true,
          "multiLine": true,
          "strictMode": false,
          "value": [
            {},
            {},
            {}
          ],
          "tabsMode": true,
          "subFormMode": "horizontal",
          "subFormHorizontal": {
            "labelWidth": 40
          },
          "items": [
            {
              "name": "a",
              "label": "文本",
              "type": "input-text",
              "placeholder": "文本",
              "required": true
            },
            {
              "name": "b",
              "label": "选项",
              "type": "select",
              "options": [
                "a",
                "b",
                "c"
              ],
              "size": "full"
            }
          ]
        }
      ]
    }
  ]
}
```
