---
title: 组合条件
description:
type: 0
group: null
menuName: 组合条件
icon:
standardMode: true
---

## 基本用法

用于设置复杂组合条件，支持添加条件，添加分组，设置组合方式，拖拽排序等功能。

```schema: scope="body"
{
    "type": "form",
    "debug": true,
    "body": [
        {
          "type": "condition-builder",
          "label": "条件组件",
          "name": "conditions",
          "description": "适合让用户自己拼查询条件，然后后端根据数据生成 query where",
          "searchable": true,
          "fields": [
            {
              "label": "文本",
              "type": "text",
              "name": "text"
            },
            {
              "label": "数字",
              "type": "number",
              "name": "number"
            },
            {
              "label": "布尔",
              "type": "boolean",
              "name": "boolean"
            },
            {
              "label": "选项",
              "type": "select",
              "name": "select",
              "options": [
                {
                  "label": "A",
                  "value": "a"
                },
                {
                  "label": "B",
                  "value": "b"
                },
                {
                  "label": "C",
                  "value": "c"
                },
                {
                  "label": "D",
                  "value": "d"
                },
                {
                  "label": "E",
                  "value": "e"
                }
              ]
            },
            {
              "label": "动态选项",
              "type": "select",
              "name": "select2",
              "source": "/api/mock2/form/getOptions?waitSeconds=1"
            },
            {
              "label": "日期",
              "children": [
                {
                  "label": "日期",
                  "type": "date",
                  "name": "date"
                },
                {
                  "label": "时间",
                  "type": "time",
                  "name": "time"
                },
                {
                  "label": "日期时间",
                  "type": "datetime",
                  "name": "datetime"
                }
              ]
            }
          ]
        }
    ]
}
```

## toolbarMode基本用法

用于设置复杂组合条件，支持添加条件，添加分组，设置组合方式，拖拽排序等功能。  

> 注意：竖版模式不支持自定义兄弟关系，如果需要，请使用`"verticalLineStyle": true`竖线风格。

```schema: scope="body"
{
    "type": "form",
    "debug": true,
    "body": [
        {
          "type": "condition-builder",
          "toolbarMode": "vertical",
          "label": "条件组件",
          "name": "conditions",
          "description": "适合让用户自己拼查询条件，然后后端根据数据生成 query where",
          "searchable": true,
          "fields": [
            {
              "label": "文本",
              "type": "text",
              "name": "text"
            },
            {
              "label": "数字",
              "type": "number",
              "name": "number"
            },
            {
              "label": "布尔",
              "type": "boolean",
              "name": "boolean"
            },
            {
              "label": "选项",
              "type": "select",
              "name": "select",
              "options": [
                {
                  "label": "A",
                  "value": "a"
                },
                {
                  "label": "B",
                  "value": "b"
                },
                {
                  "label": "C",
                  "value": "c"
                },
                {
                  "label": "D",
                  "value": "d"
                },
                {
                  "label": "E",
                  "value": "e"
                }
              ]
            },
            {
              "label": "动态选项",
              "type": "select",
              "name": "select2",
              "source": "/api/mock2/form/getOptions?waitSeconds=1"
            },
            {
              "label": "日期",
              "children": [
                {
                  "label": "日期",
                  "type": "date",
                  "name": "date"
                },
                {
                  "label": "时间",
                  "type": "time",
                  "name": "time"
                },
                {
                  "label": "日期时间",
                  "type": "datetime",
                  "name": "datetime"
                }
              ]
            }
          ]
        }
    ]
}
```


## 竖线风格

`"verticalLineStyle": true`横版模式下设置竖线风格。和`vertical`竖版模式不同的是，竖线风格下，支持自定义兄弟节点关系，`vertical`竖版模式下，兄弟节点关系由父级统一设置，不支持自定义。

```schema: scope="body"
{
    "type": "form",
    "debug": true,
    "body": [
        {
          "type": "condition-builder",
          "label": "条件组件",
          "name": "conditions",
          "verticalLineStyle": true,
          "description": "适合让用户自己拼查询条件，然后后端根据数据生成 query where",
          "searchable": true,
          "fields": [
            {
              "label": "文本",
              "type": "text",
              "name": "text"
            },
            {
              "label": "数字",
              "type": "number",
              "name": "number"
            },
            {
              "label": "布尔",
              "type": "boolean",
              "name": "boolean"
            },
            {
              "label": "选项",
              "type": "select",
              "name": "select",
              "options": [
                {
                  "label": "A",
                  "value": "a"
                },
                {
                  "label": "B",
                  "value": "b"
                },
                {
                  "label": "C",
                  "value": "c"
                },
                {
                  "label": "D",
                  "value": "d"
                },
                {
                  "label": "E",
                  "value": "e"
                }
              ]
            },
            {
              "label": "动态选项",
              "type": "select",
              "name": "select2",
              "source": "/api/mock2/form/getOptions?waitSeconds=1"
            },
            {
              "label": "日期",
              "children": [
                {
                  "label": "日期",
                  "type": "date",
                  "name": "date"
                },
                {
                  "label": "时间",
                  "type": "time",
                  "name": "time"
                },
                {
                  "label": "日期时间",
                  "type": "datetime",
                  "name": "datetime"
                }
              ]
            }
          ]
        }
    ]
}
```

## 拖拽控制

当`draggable`为`false`时，关闭拖拽

```schema: scope="body"
{
    "type": "form",
    "debug": true,
    "body": [
        {
          "type": "condition-builder",
          "label": "条件组件",
          "name": "conditions",
          "description": "适合让用户自己拼查询条件，然后后端根据数据生成 query where",
          "searchable": true,
          "draggable": false,
          "fields": [
            {
              "label": "文本",
              "type": "text",
              "name": "text"
            },
            {
              "label": "数字",
              "type": "number",
              "name": "number"
            },
            {
              "label": "布尔",
              "type": "boolean",
              "name": "boolean"
            },
            {
              "label": "选项",
              "type": "select",
              "name": "select",
              "options": [
                {
                  "label": "A",
                  "value": "a"
                },
                {
                  "label": "B",
                  "value": "b"
                },
                {
                  "label": "C",
                  "value": "c"
                },
                {
                  "label": "D",
                  "value": "d"
                },
                {
                  "label": "E",
                  "value": "e"
                }
              ]
            },
            {
              "label": "动态选项",
              "type": "select",
              "name": "select2",
              "source": "/api/mock2/form/getOptions?waitSeconds=1"
            },
            {
              "label": "日期",
              "children": [
                {
                  "label": "日期",
                  "type": "date",
                  "name": "date"
                },
                {
                  "label": "时间",
                  "type": "time",
                  "name": "time"
                },
                {
                  "label": "日期时间",
                  "type": "datetime",
                  "name": "datetime"
                }
              ]
            }
          ]
        }
    ]
}
```

## 自定义配置用法

- 通过`conditionItemBody`配置条件表达式参数的自定义配置项
- 通过`maxLevel`设置一个条件组合里最大条件节点数量，支持模板变量
- 通过`minLevel`设置一个条件组合里最小条件节点数量，支持模板变量
- 通过`rootCondiOptions`设置根条件连接符
- 通过`leafCondiOptions`设置非根条件连接符

```schema: scope="body"
{
    "type": "form",
    "debug": true,
    "body": [
        {
          "type": "input-text",
          "name": "maxLevelData",
          "label": "最大节点"
        },
        {
          "type": "input-text",
          "name": "minLevelData",
          "label": "最小节点"
        },
        {
          "type": "condition-builder",
          "label": "条件组件",
          "name": "conditions",
          "description": "适合让用户自己拼查询条件，然后后端根据数据生成 query where",
          "searchable": true,
          "conditionItemBody": [
            {
              "type": "input-text",
              "name": "field1",
              "label": "字段1"
            },
            {
              "name": "field2",
              "type": "input-text",
              "label": "字段2"
            }
          ],
          "maxLevel": '${maxLevelData}',
          "minLevel": '${minLevelData}',
          "rootCondiOptions": [
            {
              "label": "条件1",
              "value": "test1"
            },
            {
              "label": "条件2",
              "value": "test2"
            }
          ],
          "leafCondiOptions": [
            {
              "label": "条件3",
              "value": "test3"
            },
            {
              "label": "条件4",
              "value": "test4"
            }
          ]
        }
    ]
}
```

可以通过自定义实现较为复杂的逻辑联动。

示例：根据字段1值变化，调用接口api，返回 options 为字段3的下拉框数据。


```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "body": [
    {
      "type": "condition-builder",
      "label": "条件组件",
      "name": "conditions",
      "description": "适合让用户自己拼查询条件，然后后端根据数据生成 query where",
      "searchable": true,
      "conditionItemBody": [
        {
          "type": "service",
          "style": {
            "display": "flex"
          },
          "api": "/api/mock2/form/deferOptions?label=${field1}",
          "data": {
            "options": [],
          },
          "body": [
            {
              "type": "input-text",
              "name": "field1",
              "label": "字段1"
            },
            {
              "name": "field2",
              "type": "input-text",
              "label": "字段2"
            },
            {
              "name": "field3",
              "type": "select",
              "label": "字段3",
              "source": "${options}"
            }
          ]
        }
      ],
      "rootCondiOptions": [
        {
          "label": "条件1",
          "value": "test1"
        },
        {
          "label": "条件2",
          "value": "test2"
        }
      ],
      "leafCondiOptions": [
        {
          "label": "条件3",
          "value": "test3"
        },
        {
          "label": "条件4",
          "value": "test4"
        }
      ]
    }
  ]
}
```


## 值格式说明

```ts
type ValueGroup = {
  conjunction: 'and' | 'or';
  children: Array<ValueGroup | ValueItem>;
};
type ValueItem = {
  // 左侧字段，这块有预留类型，不过目前基本上只是字段。
  left: {
    type: 'field';
    field: string;
  };

  // 还有更多类型，暂不细说
  op: 'equals' | 'not_equal' | 'less' | 'less_or_equal';

  // 根据字段类型和 op 的不同，值格式会不一样。
  // 如果 op 是范围，right 就是个数组 [开始值，结束值]，否则就是值。
  right: any;
};

type Value = ValueGroup;
```

## 自定义字段选项

字段选项为这个组件主要配置部分，通过 `conditionItemBody` 字段来配置，有哪些字段，并且字段的类型是什么，支持哪些比较操作符。

`conditionItemBody` 为数组类型，每个成员表示一个可选字段，支持多个层，配置示例

```json
"conditionItemBody": [
  {
    "type": "input-text",
    "name": "field1",
    "label": "字段1"
  },
  {
    "name": "field2",
    "type": "input-text",
    "label": "字段2"
  }
]
```

## 字段选项

字段选项为这个组件主要配置部分，通过 `fields` 字段来配置，有哪些字段，并且字段的类型是什么，支持哪些比较操作符。

`fields` 为数组类型，每个成员表示一个可选字段，支持多个层，配置示例

```json
"fields": [
  {
    "label": "字段1"
    // 字段1
  },
  {
    "label": "字段2"
    // 字段2
  },
  {
    "label": "字段分组",
    "children": [
      {
        "label": "字段3"
      },
      {
        "label": "字段4"
      }
    ]
  }
]
```

## 支持的字段类型

这里面能用的字段类型和表单项中的字段类型不一样，还没支持那么多，基本上只有一些基础的类型，其他复杂类型还需后续扩充，目前基本上支持以下这些类型。

### 文本

- `type` 字段配置中配置成 `"text"`
- `label` 字段名称。
- `placeholder` 占位符
- `operators` 默认为 `[ 'equal', 'not_equal', 'is_empty', 'is_not_empty', 'like', 'not_like', 'starts_with', 'ends_with' ]` 如果不要那么多，可以配置覆盖。
- `defaultOp` 默认为 `"equal"`

```schema: scope="body"
{
    "type": "form",
    "debug": true,
    "body": [
        {
          "type": "condition-builder",
          "label": "条件组件",
          "name": "conditions",
          "description": "适合让用户自己拼查询条件，然后后端根据数据生成 query where",
          "fields": [
            {
              "label": "A",
              "type": "text",
              "name": "a"
            }
          ]
        }
    ]
}
```

### 数字

- `type` 字段配置中配置成 `"number"`
- `label` 字段名称。
- `placeholder` 占位符
- `operators` 默认为 `[ 'equal', 'not_equal', 'less', 'less_or_equal', 'greater', 'greater_or_equal', 'between', 'not_between', 'is_empty', 'is_not_empty' ]` 如果不要那么多，可以配置覆盖。
- `defaultOp` 默认为 `"equal"`
- `minimum` 最小值
- `maximum` 最大值
- `step` 步长

```schema: scope="body"
{
    "type": "form",
    "debug": true,
    "body": [
        {
          "type": "condition-builder",
          "label": "条件组件",
          "name": "conditions",
          "description": "适合让用户自己拼查询条件，然后后端根据数据生成 query where",
          "fields": [
            {
              "label": "A",
              "type": "number",
              "name": "a",
              "minimum": 1,
              "maximum": 10,
              "step": 1
            }
          ]
        }
    ]
}
```

### 日期

- `type` 字段配置中配置成 `"date"`
- `label` 字段名称。
- `placeholder` 占位符
- `operators` 默认为 `[ 'equal', 'not_equal', 'less', 'less_or_equal', 'greater', 'greater_or_equal', 'between', 'not_between', 'is_empty', 'is_not_empty' ]` 如果不要那么多，可以配置覆盖。
- `defaultOp` 默认为 `"equal"`
- `defaultValue` 默认值
- `format` 默认 `"YYYY-MM-DD"` 值格式
- `inputFormat` 默认 `"YYYY-MM-DD"` 显示的日期格式。

```schema: scope="body"
{
    "type": "form",
    "debug": true,
    "body": [
        {
          "type": "condition-builder",
          "label": "条件组件",
          "name": "conditions",
          "description": "适合让用户自己拼查询条件，然后后端根据数据生成 query where",
          "fields": [
            {
              "label": "A",
              "type": "date",
              "name": "a"
            }
          ]
        }
    ]
}
```

### 日期时间

- `type` 字段配置中配置成 `"datetime"`
- `label` 字段名称。
- `placeholder` 占位符
- `operators` 默认为 `[ 'equal', 'not_equal', 'less', 'less_or_equal', 'greater', 'greater_or_equal', 'between', 'not_between', 'is_empty', 'is_not_empty' ]` 如果不要那么多，可以配置覆盖。
- `defaultOp` 默认为 `"equal"`
- `defaultValue` 默认值
- `format` 默认 `""` 值格式
- `inputFormat` 默认 `"YYYY-MM-DD HH:mm"` 显示的日期格式。
- `timeFormat` 默认 `"HH:mm"` 时间格式，决定输入框有哪些。

```schema: scope="body"
{
    "type": "form",
    "debug": true,
    "body": [
        {
          "type": "condition-builder",
          "label": "条件组件",
          "name": "conditions",
          "description": "适合让用户自己拼查询条件，然后后端根据数据生成 query where",
          "fields": [
            {
              "label": "A",
              "type": "datetime",
              "name": "a"
            }
          ]
        }
    ]
}
```

### 时间

- `type` 字段配置中配置成 `"time"`
- `label` 字段名称。
- `placeholder` 占位符
- `operators` 默认为 `[ 'equal', 'not_equal', 'less', 'less_or_equal', 'greater', 'greater_or_equal', 'between', 'not_between', 'is_empty', 'is_not_empty' ]` 如果不要那么多，可以配置覆盖。
- `defaultOp` 默认为 `"equal"`
- `defaultValue` 默认值
- `format` 默认 `"HH:mm"` 值格式
- `inputFormat` 默认 `"HH:mm"` 显示的日期格式。

```schema: scope="body"
{
    "type": "form",
    "debug": true,
    "body": [
        {
          "type": "condition-builder",
          "label": "条件组件",
          "name": "conditions",
          "description": "适合让用户自己拼查询条件，然后后端根据数据生成 query where",
          "fields": [
            {
              "label": "A",
              "type": "time",
              "name": "a"
            }
          ]
        }
    ]
}
```

### 下拉选择

- `type` 字段配置中配置成 `"select"`
- `label` 字段名称。
- `placeholder` 占位符
- `operators` 默认为 `[ 'select_equals', 'select_not_equals', 'select_any_in', 'select_not_any_in' ]` 如果不要那么多，可以配置覆盖。
- `defaultOp`
- `options` 选项列表，`Array<{label: string, value: any}>`
- `source` 动态选项，请配置 api。
- `searchable` 是否可以搜索
- `autoComplete` 自动提示补全，每次输入新内容后，将调用接口，根据接口返回更新选项。

```schema: scope="body"
{
    "type": "form",
    "debug": true,
    "body": [
        {
          "type": "condition-builder",
          "label": "条件组件",
          "name": "conditions",
          "description": "适合让用户自己拼查询条件，然后后端根据数据生成 query where",
          "fields": [
            {
              "label": "A",
              "type": "select",
              "placeholder": "这个是下拉框",
              "name": "a",
              "source": "/api/mock2/form/getOptions?waitSeconds=1",
              "searchable": true
            }
          ]
        }
    ]
}
```

配置`autoComplete`属性后，每次输入新内容后会自动调用接口加载新的选项，用数据映射，获取变量 term，为当前输入的关键字。

```schema: scope="body"
{
    "type": "form",
    "debug": true,
    "body": [
        {
          "type": "condition-builder",
          "label": "条件组件",
          "name": "conditions",
          "description": "适合让用户自己拼查询条件，然后后端根据数据生成 query where",
          "fields": [
            {
              "label": "选项自动补全",
              "type": "select",
              "name": "select",
              "searchable": true,
              "autoComplete": "https://3xsw4ap8wah59.cfc-execute.bj.baidubce.com/api/amis-mock/mock2/options/autoComplete?term=$term",
            }
          ]
        }
    ]
}
```

### 自定义

- `type` 字段配置中配置成 `"custom"`
- `label` 字段名称
- `placeholder` 占位符
- `operators` 默认为空，需配置自定义判断条件，支持字符串或 key-value 格式
- `value` 字段配置右边值需要渲染的组件，支持 amis 输入类组件或自定义输入类组件

```schema: scope="body"
{
    "type": "form",
    "debug": true,
    "body": [
        {
          "type": "condition-builder",
          "label": "条件组件",
          "name": "conditions",
          "description": "适合让用户自己拼查询条件，然后后端根据数据生成 query where",
          "fields": [
            {
              "label": "自定义",
              "type": "custom",
              "name": "a",
              "value": {
                "type": "input-color"
              },
              "operators": [
                "equal",
                {
                  "label": "等于（自定义）",
                  "value": "custom_equal"
                }
              ]
            }
          ]
        }
    ]
}
```

其中`operators`通过配置 values 还支持右边多个组件的渲染，`right`值格式为对象，`key`为组件的`name`

```schema: scope="body"
{
    "type": "form",
    "debug": true,
    "body": [
        {
          "type": "condition-builder",
          "label": "条件组件",
          "name": "conditions",
          "description": "适合让用户自己拼查询条件，然后后端根据数据生成 query where",
          "fields": [
            {
              "label": "自定义",
              "type": "custom",
              "name": "a",
              "value": {
                "type": "input-color"
              },
              "operators": [
                {
                  "label": "等于（自定义）",
                  "value": "custom_equal"
                },
                {
                  "label": "属于",
                  "value": "belong",
                  "values": [
                    {
                      "type": "input-text",
                      "name": "color1"
                    },
                    {
                      "type": "tpl",
                      "tpl": "~"
                    },
                    {
                      "type": "input-text",
                      "name": "color2"
                    }
                  ]
                }
              ]
            }
          ]
        }
    ]
}
```

## 字段选项远程拉取

- 方式 1 配置 `source` 接口返回的数据对象 `data` 中存在 fields 变量即可。
- 方式 2 关联上下文变量如 `source: "${xxxxField}"`

```schema: scope="body"
{
    "type": "form",
    "body": [
      {
        "type": "condition-builder",
        "label": "条件组件",
        "name": "conditions",
        "description": "适合让用户自己拼查询条件，然后后端根据数据生成 query where",
        "source": "/api/condition-fields?a=${a}&waitSeconds=2"
      }
    ]
}
```

## 字段选项类型

通过 selectMode 配置组合条件左侧选项类型，可配置项为`list`、`tree`，默认为`list`。两者数据格式相同，只是下拉框展示方式不同，当存在多层 children 嵌套时，建议使用`tree`。

selectMode 为`list`时

```schema: scope="body"
{
    "type": "form",
    "debug": true,
    "body": [
        {
          "type": "condition-builder",
          "label": "条件组件",
          "name": "conditions",
          "description": "适合让用户自己拼查询条件，然后后端根据数据生成 query where",
          "fields": [
            {
              "label": "文本",
              "type": "text",
              "name": "text"
            },
            {
              "label": "数字",
              "type": "number",
              "name": "number"
            },
            {
              "label": "布尔",
              "type": "boolean",
              "name": "boolean"
            },
            {
              "label": "选项",
              "type": "select",
              "name": "select",
              "options": [
                {
                  "label": "A",
                  "value": "a"
                },
                {
                  "label": "B",
                  "value": "b"
                },
                {
                  "label": "C",
                  "value": "c"
                },
                {
                  "label": "D",
                  "value": "d"
                },
                {
                  "label": "E",
                  "value": "e"
                }
              ]
            }
          ]
        }
    ]
}
```

selectMode 为`tree`时

```schema: scope="body"
{
    "type": "form",
    "debug": true,
    "body": [
        {
          "type": "condition-builder",
          "label": "条件组件",
          "name": "conditions",
          "selectMode": "tree",
          "description": "适合让用户自己拼查询条件，然后后端根据数据生成 query where",
          "fields": [
            {
              "label": "文本",
              "type": "text",
              "name": "text"
            },
            {
              "label": "数字",
              "type": "number",
              "name": "number"
            },
            {
              "label": "布尔",
              "type": "boolean",
              "name": "boolean"
            },
            {
              "label": "树形结构",
              "type": "tree",
              "name": "tree",
              children: [
                {
                  "label": "Folder A",
                  "type": "tree",
                  "name": "Folder_A",
                  "type": "number",
                  "value": 1,
                  "children": [
                    {
                      "label": "file A",
                      "value": 2,
                      "name": "file_A",
                      "type": "number",
                    },
                    {
                      "label": "Folder B",
                      "value": 3,
                      "name": "Folder_B",
                      "type": "number",
                      "children": [
                        {
                          "label": "file b1",
                          "value": 3.1,
                          "name": "file_b1",
                          "type": "number"
                        },
                        {
                          "label": "file b2",
                          "value": 3.2,
                          "name": "file_b2",
                          "type": "number"
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
    ]
}
```

## 简易模式

通过 builderMode 配置为简易模式，在这个模式下将不开启树形分组功能，输出结果只有一层，方便后端实现简单的 SQL 生成。

```schema: scope="body"
{
    "type": "form",
    "debug": true,
    "body": [
        {
          "type": "condition-builder",
          "label": "条件组件",
          "builderMode": "simple",
          "name": "conditions",
          "description": "适合让用户自己拼查询条件，然后后端根据数据生成 query where",
          "fields": [
            {
              "label": "文本",
              "type": "text",
              "name": "text"
            },
            {
              "label": "数字",
              "type": "number",
              "name": "number"
            },
            {
              "label": "布尔",
              "type": "boolean",
              "name": "boolean"
            },
            {
              "label": "选项",
              "type": "select",
              "name": "select",
              "options": [
                {
                  "label": "A",
                  "value": "a"
                },
                {
                  "label": "B",
                  "value": "b"
                },
                {
                  "label": "C",
                  "value": "c"
                },
                {
                  "label": "D",
                  "value": "d"
                },
                {
                  "label": "E",
                  "value": "e"
                }
              ]
            },
            {
              "label": "动态选项",
              "type": "select",
              "name": "select2",
              "source": "/api/mock2/form/getOptions?waitSeconds=1"
            },
            {
              "label": "日期",
              "children": [
                {
                  "label": "日期",
                  "type": "date",
                  "name": "date"
                },
                {
                  "label": "时间",
                  "type": "time",
                  "name": "time"
                },
                {
                  "label": "日期时间",
                  "type": "datetime",
                  "name": "datetime"
                }
              ]
            }
          ]
        }
    ]
}
```

在这个模式下还可以通过 `showANDOR` 来显示顶部的条件类型切换

```schema: scope="body"
{
    "type": "form",
    "debug": true,
    "body": [
        {
          "type": "condition-builder",
          "label": "条件组件",
          "builderMode": "simple",
          "showANDOR": true,
          "name": "conditions",
          "description": "适合让用户自己拼查询条件，然后后端根据数据生成 query where",
          "fields": [
            {
              "label": "文本",
              "type": "text",
              "name": "text"
            },
            {
              "label": "数字",
              "type": "number",
              "name": "number"
            },
            {
              "label": "布尔",
              "type": "boolean",
              "name": "boolean"
            },
            {
              "label": "选项",
              "type": "select",
              "name": "select",
              "options": [
                {
                  "label": "A",
                  "value": "a"
                },
                {
                  "label": "B",
                  "value": "b"
                },
                {
                  "label": "C",
                  "value": "c"
                },
                {
                  "label": "D",
                  "value": "d"
                },
                {
                  "label": "E",
                  "value": "e"
                }
              ]
            },
            {
              "label": "动态选项",
              "type": "select",
              "name": "select2",
              "source": "/api/mock2/form/getOptions?waitSeconds=1"
            },
            {
              "label": "日期",
              "children": [
                {
                  "label": "日期",
                  "type": "date",
                  "name": "date"
                },
                {
                  "label": "时间",
                  "type": "time",
                  "name": "time"
                },
                {
                  "label": "日期时间",
                  "type": "datetime",
                  "name": "datetime"
                }
              ]
            }
          ]
        }
    ]
}
```

## 非内嵌模式

当表单区域较窄时，可以使用非内嵌模式，弹窗设置具体信息

```schema: scope="body"
{
    "type": "form",
    "debug": true,
    "body": [
        {
          "type": "condition-builder",
          "label": "条件组件",
          "embed": false,
          "title": "条件组合设置",
          "builderMode": "simple",
          "name": "conditions",
          "description": "适合让用户自己拼查询条件，然后后端根据数据生成 query where",
          "fields": [
            {
              "label": "文本",
              "type": "text",
              "name": "text"
            },
            {
              "label": "数字",
              "type": "number",
              "name": "number"
            },
            {
              "label": "布尔",
              "type": "boolean",
              "name": "boolean"
            },
            {
              "label": "选项",
              "type": "select",
              "name": "select",
              "options": [
                {
                  "label": "A",
                  "value": "a"
                },
                {
                  "label": "B",
                  "value": "b"
                },
                {
                  "label": "C",
                  "value": "c"
                },
                {
                  "label": "D",
                  "value": "d"
                },
                {
                  "label": "E",
                  "value": "e"
                }
              ]
            },
            {
              "label": "动态选项",
              "type": "select",
              "name": "select2",
              "source": "/api/mock2/form/getOptions?waitSeconds=1"
            },
            {
              "label": "日期",
              "children": [
                {
                  "label": "日期",
                  "type": "date",
                  "name": "date"
                },
                {
                  "label": "时间",
                  "type": "time",
                  "name": "time"
                },
                {
                  "label": "日期时间",
                  "type": "datetime",
                  "name": "datetime"
                }
              ]
            }
          ]
        }
    ]
}
```

## 动态控制按钮

可以通过`removeable`、`copyable`、`addable`、`itemAddable`、`itemRemoveable`、`itemCopyable` 来统一控制操作按钮是否展示，如果需要特殊的逻辑来控制则可以使用 `removeableOn`、`copyableOn`、`addableOn`、`itemAddableOn`、`itemRemoveableOn`、`itemCopyableOn`来编写表达式更加灵活的控制操作按钮的显隐，表达式中可以通过`currLevel`来获取层级，通过`index`来获取在当前层级的索引

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "body": [
      {
        "type": "condition-builder",
        "label": "条件组件",
        "name": "conditions",
        "description": "适合让用户自己拼查询条件，然后后端根据数据生成 query where",
        "searchable": true,
        "addableOn": "currLevel < 3",
        "itemRemoveableOn": "index !== 0",
        "itemCopyableOn": "data.field1 === 'copy'",
        "conditionItemBody": [
          {
            "type": "input-text",
            "name": "field1",
            "label": "字段1"
          },
          {
            "name": "field2",
            "type": "input-text",
            "label": "字段2"
          },
          {
            "type": "input-text",
            "visibleOn": "currLevel === 2",
            "label": "命中条件"
          }
        ],
        "rootCondiOptions": [
          {
            "label": "条件1",
            "value": "test1"
          },
          {
            "label": "条件2",
            "value": "test2"
          }
        ],
        "leafCondiOptions": [
          {
            "label": "条件3",
            "value": "test3"
          },
          {
            "label": "条件4",
            "value": "test4"
          }
        ]
      }
    ]
  }
}
```

## 属性表

| 属性名            | 类型                | 默认值                                                      | 说明                           | 版本  |
| ----------------- | ------------------- | ----------------------------------------------------------- | ------------------------------ | ----- |
| className         | `string`            |                                                             | 外层 dom 类名                  |       |
| fieldClassName    | `string`            |                                                             | 输入字段的类名                 |       |
| source            | `string`            |                                                             | 通过远程拉取配置项             |       |
| embed             | `boolean`           | true                                                        | 内嵌展示                       |       |
| title             | `string`            |                                                             | 弹窗配置的顶部标题             |       |
| fields            |                     |                                                             | 字段配置                       |       |
| showANDOR         | `boolean`           |                                                             | 用于 simple 模式下显示切换按钮 |       |
| showNot           | `boolean`           |                                                             | 是否显示「非」按钮             |       |
| searchable        | `boolean`           |                                                             | 字段是否可搜索                 |       |
| draggable      | `'boolean'`、      | `'true'` | 是否可拖拽                   | 1.12.0
| selectMode        | `'list'`、`'tree'`  | `'list'`                                                    | 组合条件左侧选项类型           |       |
| rootCondiOptions  | `object[]`          | `[{label: '且', value: 'and'}, {label: '或', value: 'or'}]` | 根条件连接符                   | 1.3.0 |
| leafCondiOptions  | `object[]`          | `[{label: '且', value: 'and'}, {label: '或', value: 'or'}]` | 非根条件连接符                 | 1.3.0 |
| minLevel          | `'number'`、`'tpl'` |                                                             | 一个条件组合里最大条件节点数量 | 1.5.0 |
| maxLevel          | `'number'`、`'tpl'` |                                                             | 一个条件组合里最小条件节点数量 | 1.5.0 |
| conditionItemBody | `Array<Schema>`     |                                                             | 条件表达式自定义配置项         | 1.5.0 |
| toolbarMode | `vertical \| horizontal`     |    `horizontal`                                                         | 调整逻辑选择工具栏的位置         | 1.25.0 |
| removeable | `boolean` | `true` | 控制「删除组」按钮是否展示 | `1.56.0` |
| removeableOn | `expression` |  | 控制「删除组」按钮是否展示 | `1.56.0` |
| copyable | `boolean` | `true` | 控制「复制组」按钮是否展示 | `1.56.0` |
| copyableOn | `expression` |  | 控制「复制组」按钮是否展示 | `1.56.0` |
| addable | `boolean` | `true` | 控制「添加条件组」按钮是否展示 | `1.56.0` |
| addableOn | `expression` |  | 控制「添加条件组」按钮是否展示 | `1.56.0` |
| itemAddable | `boolean` | `true` | 控制「添加条件」按钮是否展示 | `1.56.0` |
| itemAddableOn | `expression` |  | 控制「添加条件」按钮是否展示 | `1.56.0` |
| itemRemoveable | `boolean` | `true` | 控制每个条件后的删除icon是否展示 | `1.56.0` |
| itemRemoveableOn | `expression` |  | 控制每个条件后的删除icon是否展示 | `1.56.0` |
| itemCopyable | `boolean` | `true` | 控制每个条件后的复制icon是否展示 | `1.56.0` |
| itemCopyableOn | `expression` |  | 控制每个条件后的复制icon是否展示 | `1.56.0` |
| verticalLineStyle | `boolean` | `true` | 是否以`vertical`垂直样式风格展示条件组。仅横版模式下生效，可自定义兄弟关系。 | `1.83.2` |

## 事件表

当前组件会对外派发以下事件，可以通过`onEvent`来监听这些事件，并通过`actions`来配置执行的动作，在`actions`中可以通过`${事件参数名}`来获取事件产生的数据，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action)。

| 事件名称          | 事件参数                                                                                                                 | 说明                   | 版本   |
| ----------------- | ------------------------------------------------------------------------------------------------------------------------ | ---------------------- | ------ |
| add               | `addedItem: object` 新增的条件或条件组<br />`value: object` 修改后的值                                                   | 新增条件或条件组时触发 | 1.12.0 |
| conjunctionChange | `conjunction: object` 修改后的条件链接符<br />`changedItem: object` 被修改的条件或条件组<br />`value: object` 修改后的值 | 修改条件连接符时触发   | 1.12.0 |

## 动作表

当前组件对外暴露以下特性动作，其他组件可以通过指定`actionType: 动作名称`、`componentId: 该组件id`来触发这些动作，动作配置可以通过`args: {动作配置项名称: xxx}`来配置具体的参数，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action#触发其他组件的动作)。

| 动作名称 | 动作配置                 | 说明     | 版本 |
| -------- | ------------------------ | -------- | ---- |
| setValue | `value: object` 更新的值 | 更新数据 |      |
