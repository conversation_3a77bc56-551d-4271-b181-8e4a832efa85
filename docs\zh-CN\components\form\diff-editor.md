---
title: DiffEditor 对比编辑器
description:
type: 0
group: null
menuName: DiffEditor 对比编辑器
icon:
order: 17
---

> 注意该组件需要依赖第三方库：monaco-editor

> 想要对比的内容生效需要保证 js 文件加载成功，针对不同语法 目前支持五种文件的加载，都挂在 staticui 应用下。

> [editor.worker.js](http://moka.dmz.prod.caijj.net/staticui/monaco-editor/vs/editor/editor.worker.js)、[json.worker.js](http://moka.dmz.prod.caijj.net/staticui/monaco-editor/vs/language/json/json.worker.js)、[css.worker.js](http://moka.dmz.prod.caijj.net/staticui/monaco-editor/vs/language/css/css.worker.js)、[html.worker.js](http://moka.dmz.prod.caijj.net/staticui/monaco-editor/vs/language/html/html.worker.js)、[ts.worker.js](http://moka.dmz.prod.caijj.net/staticui/monaco-editor/vs/language/typescript/ts.worker.js)

## 场景推荐
### 版本对比

```schema
{
  "type": "page",
  "data": {
    "value1": "hello word",
    "value2": "hello friend",
    "baselineVersion": "v2",
    "diffVersion": "v1"
  },
  "body": {
    "type": "form",
    "title": "",
    "actions": [],
    "static": true,
    "labelWidth": 60,
    "body": [
      {
        "type": "group",
        "body": [
          {
            "type": "select",
            "label": "对比版本",
            "name": "baselineVersion",
            "options": [
              {
                "label": "版本1 (李冰清，2022-11-08 11:27:38)",
                "value": "v1"
              },
              {
                "label": "版本2 (申铭，2024-04-28 05:00:06)",
                "value": "v2"
              }
            ]
          },
          {
            "type": "select",
            "label": "基准版本",
            "name": "diffVersion",
            "options": [
              {
                "label": "版本1 (李冰清，2022-11-08 11:27:38)",
                "value": "v1"
              },
              {
                "label": "版本2 (申铭，2024-04-28 05:00:06)",
                "value": "V2"
              }
            ]
          }
        ]
      },
      {
        "type": "diff-editor",
        "name": "value2",
        "label": false,
        "disabled": true,
        "diffValue": "${value1}"
      }
    ]
  }
}
```

## 组件用法
### 基本使用

```schema: scope="body"
{
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "body": [
        {
            "type": "diff-editor",
            "name": "diff",
            "label": "Diff-Editor",
            "diffValue": "hello world",
            "value": "hello"
        }
    ]
}
```

### 禁用编辑器

左侧编辑器始终不可编辑，右侧编辑器可以通过设置`disabled`或`disabledOn`，控制是否禁用

```schema: scope="body"
{
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "body": [
        {
            "type": "diff-editor",
            "name": "diff",
            "label": "Diff-Editor",
            "diffValue": "hello world",
            "value": "hello",
            "disabledOn": "this.isDisabled"
        },
        {
            "type": "switch",
            "name": "isDisabled",
            "label": "是否禁用"
        }
    ]
}
```

### diff 数据域中的两个变量

如下例，左侧编辑器中的值，通过`"diffValue": "${value1}"`获取，右侧编辑器的值，通过设置`"name": "value2"`，自动映射数据域中`value2`的值

```schema: scope="body"
{
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "data": {
        "value1": "hello world",
        "value2": "hello wrold"
    },
    "body": [
        {
            "type": "diff-editor",
            "name": "value2",
            "label": "Diff-Editor",
            "diffValue": "${value1}"
        }
    ]
}
```

### 属性表

除了支持 [普通表单项属性表](/dataseeddesigndocui/#/amis/zh-CN/components/form/formitem#%E5%B1%9E%E6%80%A7%E8%A1%A8) 中的配置以外，还支持下面一些配置

| 属性名    | 类型                                                    | 默认值       | 说明                                                                                                                                        |
| --------- | ------------------------------------------------------- | ------------ | ------------------------------------------------------------------------------------------------------------------------------------------- |
| language  | `string`                                                | `javascript` | 编辑器高亮的语言，可选 [支持的语言](/dataseeddesigndocui/#/amis/zh-CN/components/form/editor#%E6%94%AF%E6%8C%81%E7%9A%84%E8%AF%AD%E8%A8%80) |
| diffValue | [Tpl](/dataseeddesigndocui/#/amis/zh-CN/components/tpl) |              | 左侧值                                                                                                                                      |

### 事件表

当前组件会对外派发以下事件，可以通过`onEvent`来监听这些事件，并通过`actions`来配置执行的动作，在`actions`中可以通过`${事件参数名}`来获取事件产生的数据，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action)。

> `[name]`表示当前组件绑定的名称，即`name`属性，如果没有配置`name`属性，则通过`value`取值。

| 事件名称 | 事件参数                  | 说明                     |
| -------- | ------------------------- | ------------------------ |
| change   | `[name]: string` 组件的值 | 代码变化时触发           |
| focus    | `[name]: string` 组件的值 | 右侧输入框获取焦点时触发 |
| blur     | `[name]: string` 组件的值 | 右侧输入框失去焦点时触发 |

### 动作表

当前组件对外暴露以下特性动作，其他组件可以通过指定`actionType: 动作名称`、`componentId: 该组件id`来触发这些动作，动作配置可以通过`args: {动作配置项名称: xxx}`来配置具体的参数，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action#触发其他组件的动作)。

| 动作名称 | 动作配置                                 | 说明                                                   |
| -------- | ---------------------------------------- | ------------------------------------------------------ |
| clear    | -                                        | 清空                                                   |
| reset    | -                                        | 将值重置为`resetValue`，若没有配置`resetValue`，则清空 |
| focus    | -                                        | 获取焦点，焦点落在右侧编辑面板                         |
| setValue | `value: string` 更新的右侧编辑面板中的值 | 更新数据                                               |
