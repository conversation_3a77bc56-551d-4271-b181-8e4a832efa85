---
title: DimensionTable 多维表
description:
type: 0
group: null
menuName: DimensionTable 多维表
icon:
order: 61
standardMode: true
---

## 基本用法

> 此组件还在实验阶段，使用前请先确认你要的功能满足了需求

可编辑的二维表格组件，v1.81.0 支持

表格中分为4个区域，分别是：

`title`第1行第1列标题区域；
`columnHeader` 列头区域；
`rowHeader` 行头区域；
`cell` 数据格区域；

每一个区域中需要配置 `body` 用于表格内静态展示，`editDialog` 用于配置编辑框表单项，点击弹窗“确定”按钮，将会将表单数据，存到对应的单元格中。当前格子 `body` 可以获取对应数据进行渲染。（editDialog.body 支持配置 type:form）

默认情况下，表格模式是 `tableMode:"basic"` 一维表格， 行/列 头均只支持一行。`tableMode` 还可以设置为，`standard`标准模式、`merge`合并模式， 不同模式下具有不同交互行为，详情可参照下方文档描述。

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "debug": true,
    "body": [
      {
        "type": "dimension-table",
        "name": "table",
        "label": "多维表",
        "title": {
          "body": {
            "type": "input-text",
            "name": "title",
            "label": false,
            "static": true,
          },
          "editDialog": {
            "body": [
              {
                "type": "input-text",
                "name": "title",
                "label": "标题"
              }
            ]
          }
        },
        "columnHeader": {
          "body": {
            "type": "input-text",
            "name": "column",
            "label": false,
            "static": true
          },
          "editDialog": {
            "body": [
              {
                "type": "input-text",
                "name": "column",
                "label": "列标题"
              }
            ]
          }
        },
        "rowHeader": {
          "body": {
            "type": "input-text",
            "name": "row",
            "label": false,
            "static": true
          },
          "editDialog": {
            "body": [
              {
                "type": "input-text",
                "name": "row",
                "label": "行标题"
              }
            ]
          }
        },
        "cell": {
          "body": {
            "type": "input-text",
            "name": "cell",
            "label": false,
            "static": true
          },
          "editDialog": {
            "body": [
              {
                "type": "input-text",
                "name": "cell",
                "label": "表格内容"
              }
            ]
          }
        }
      }
    ]
  }
}
```

## 操作工具栏控制

默认情况下，不同区域的单元格均支持 添加、删除、编辑、复制 操作。可以使用 `addable`、`removable`、`copyable`、`editable` 做对应的控制。如果设置在表单项最外层，增控制所有区域。如果仅设置在指定区域，则只控制对应区域。

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "debug": true,
    "body": [
      {
        "type": "dimension-table",
        "name": "table",
        "label": "二维表",
        "copyable": true, // 允许复制
        "editable": true, // 允许编辑
        "removable": true, // 允许删除
        "addable": false, // 不允许插入
        "title": {
          "editable": false, // 不允许编辑
          "body": {
            "type": "tpl",
            "tpl": "表格标题"
          }
        },
        "columnHeader": {
          "body": {
            "type": "input-text",
            "name": "column",
            "label": false,
            "static": true
          },
          "editDialog": {
            "body": [
              {
                "type": "input-text",
                "name": "column",
                "label": "列标题"
              }
            ]
          }
        },
        "rowHeader": {
          "body": {
            "type": "input-text",
            "name": "row",
            "label": false,
            "static": true
          },
          "editDialog": {
            "body": [
              {
                "type": "input-text",
                "name": "row",
                "label": "行标题"
              }
            ]
          }
        },
        "cell": {
          "body": {
            "type": "input-text",
            "name": "cell",
            "label": false,
            "static": true
          },
          "editDialog": {
            "body": [
              {
                "type": "input-text",
                "name": "cell",
                "label": "表格内容"
              }
            ]
          }
        }
      }
    ]
  }
}
```

## 添加校验规则

单元格输入的内容，通过“编辑”按钮，弹出的表单框填写，如果需要校验，则可通过编辑弹窗进行校验。如果是通过“复制”操作增加的数据，可在对应区域配置 `rules` 配置进行校验。当有表格有任何数据变动均会触发校验。

注意：`rules` 仅校验表格每一个格子内静态展示时的数据，不会校验“编辑”弹窗内的表单项的校验。配置格式如下：

```js
[{
  /**
   * 必填：表达式，返回 false 或者 '',表示校验成功。
   * 如果有复杂的校验规则时，可使用自定义表达式，返回有值字符串作为错误信息。 
   */
  rule: '${xxx}',
  message: 'xx', // 非必填： rule 表达式返回的 true 时，对应的错误文案。（如果rule表达式返回有值字符串，则以表达式为准）
}]
```

当 `rule` 使用自定义filter 来校验单元格时。在自定义filter可以获取到当前格子的保存的数据，也能通过 `__td` 获取到单元格的状态数据。

格子间联动校验示例：

```ts

// 校验列头 所有格子的和不超过10
registerFilter('validateColumnHeader', (cellData) => {
  const {
    column, // 单元格数据（由 editDialog form表单配置）
    __td, // 具体结构，参考下方文档
    table, // 多维表全量数据(formItem的name)，具体结构参考后边文档，“数据格式”部分
  } = cellData 

  const { columnHeader = [] } = table

  const allColumnSum = columnHeader.reduce((sum, item) => {
    sum += item.data.column || 0
    return sum
  }, 0)

  const errMsg = allColumnSum > 10
    ? '列头和超过10'
    : ''

  return errMsg
})

// 校验 JSON配置
{
  columnHeader: {
    ...//其他配置
    rules: [{
      rule: '${&|validateColumnHeader}'
    }]
  }
}

// __td 元数据
{
  id: string // 格子唯一id
  parentId: string // 格子父级id
  isTitle: boolean // 标题区域
  isRowHeader: boolean // 行头区域
  isColumnHeader: boolean // 列头区域
  isCell: boolean // 数据格 区域
  rowspan: number // 行合并
  colspan: number // 列合并
  rowIndex: number // 行索引
  columnIndex: number // 列索引
  // ...其他字段
}

```

```schema
registerFilter('validateColumnHeader', (cellData) => {
  const {
    column,
    __td,
    table, // 多维表全量数据(formItem的name)，具体结构参考后边文档，“数据格式”部分
  } = cellData

  const { columnHeader = [] } = table

  const allColumnSum = columnHeader.reduce((sum, item) => {
    sum += item.data.column || 0
    return sum
  }, 0)

  const errMsg = allColumnSum > 10
    ? '列头数量超过10'
    : ''

  return errMsg
})

return {
  "type": 'page',
  "body": {
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "debug": true,
    "body": [
      {
        "type": "dimension-table",
        "name": "table",
        "label": "二维表",
        "title": {
          "body": {
            "type": "input-text",
            "name": "title",
            "label": false,
            "static": true,
          },
          "editDialog": {
            "body": [{
              "type": "input-text",
              "name": "title",
              "label": "标题"
            }]
          },
          "rules": [{
            "rule": '${!title}',
            "message": '请输入标题'
          }]
        },
        "columnHeader": {
          "body": {
            "type": "input-number",
            "name": "column",
            "label": false,
            "static": true,
          },
          "editDialog": {
            "body": [{
              "type": "input-number",
              "name": "column",
              "label": "列标题"
            }]
          },
          "rules": [{
            "rule": '${!column}',
            "message": '请输入列标题'
          },
          {
            "rule": '${&|validateColumnHeader}',
          }],
        },
        "rowHeader": {
          "rules": [{
            "rule": '${!row}',
            "message": '请输入行标题'
          }],
          "body": {
            "type": "input-text",
            "name": "row",
            "label": false,
            "static": true,
          },
          "editDialog": {
            "body": [{
              "type": "input-text",
              "name": "row",
              "label": "行标题"
            }]
          }
        },
        "cell": {
          "rules": [{
            "rule": '${!cell}',
            "message": '请输入单元格内容'
          }],
          "body": {
            "type": "input-text",
            "name": "cell",
            "label": false,
            "static": true,
          },
          "editDialog": {
            "body": [{
              "type": "input-text",
              "name": "cell",
              "label": "单元格内容"
            }]
          }
        }
      }
    ]
  }
}

```

## 标准模式

默认模式下，表头只能支持一维。当表头需要多维时，可配置 `tableMode:"standard"` 开启标准模式。该模式下，表头区域可扩展为多维。

标准模式交互逻辑：

1. title区域仅有编辑操作。
2. 列头区域：可以插入新的行，只能添加 “同级列”，（基于上一行的格子，分叉新增列），纵向形成树状结构。如果要完全新增一列，需要在第1行的列头，增加同级列。
3. 行头区域：可以插入新的列，只能添加 “同级行”，（基于左侧一列的格子，分叉新增行），横向形成树状结构。如果要完全新增一行，需要在第1列的行头，增加同级行。
4. 数据格区域：增加新行时，相当于最后1列行头，增加“同级行”。增加新列时，相当于最后1行列头，增加“同级列”。

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "debug": true,
    "body": [
      {
        "type": "dimension-table",
        "name": "table",
        "label": "多维表",
        "tableMode": "standard",
        "title": {
          "body": {
            "type": "input-text",
            "name": "title",
            "label": false,
            "static": true
          },
          "editDialog": {
            "body": [
              {
                "type": "input-text",
                "name": "title",
                "label": "标题"
              }
            ]
          }
        },
        "columnHeader": {
          "body": {
            "type": "input-text",
            "name": "column",
            "label": false,
            "static": true
          },
          "editDialog": {
            "body": [
              {
                "type": "input-text",
                "name": "column",
                "label": "列标题"
              }
            ]
          }
        },
        "rowHeader": {
          "body": {
            "type": "input-text",
            "name": "row",
            "label": false,
            "static": true
          },
          "editDialog": {
            "body": [
              {
                "type": "input-text",
                "name": "row",
                "label": "行标题"
              }
            ]
          }
        },
        "cell": {
          "body": {
            "type": "input-text",
            "name": "cell",
            "label": false,
            "static": true
          },
          "editDialog": {
            "body": [
              {
                "type": "input-text",
                "name": "cell",
                "label": "单元格内容"
              }
            ]
          }
        }
      }
    ]
  }
}
```

**已有数据数据反显**，将编辑的数据直接存储，再次设置之前编辑的数据，会正常反显。

```schema
{
  "type": "page",
  "body": {
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "debug": true,
  "body": [
    {
      "type": "dimension-table",
      "name": "table",
      "label": "多维表",
      "tableMode": "standard",
      "title": {
        "body": {
          "type": "input-text",
          "name": "title",
          "label": false,
          "static": true
        },
        "editDialog": {
          "body": [
            {
              "type": "input-text",
              "name": "title",
              "label": "标题"
            }
          ]
        }
      },
      "columnHeader": {
        "body": {
          "type": "input-text",
          "name": "column",
          "label": false,
          "static": true
        },
        "editDialog": {
          "body": [
            {
              "type": "input-text",
              "name": "column",
              "label": "列标题"
            }
          ]
        }
      },
      "rowHeader": {
        "body": {
          "type": "input-text",
          "name": "row",
          "label": false,
          "static": true
        },
        "editDialog": {
          "body": [
            {
              "type": "input-text",
              "name": "row",
              "label": "行标题"
            }
          ]
        }
      },
      "cell": {
        "body": {
          "type": "input-text",
          "name": "cell",
          "label": false,
          "static": true
        },
        "editDialog": {
          "body": [
            {
              "type": "input-text",
              "name": "cell",
              "label": "单元格内容"
            }
          ]
        }
      }
    }
  ],
  "initApi": {
    "url": "/api/amis-mock/mock2/page/initData",
    "adaptor": () => {
      const data = {
        "table": {
          "title": {
            "data": {
              "title": "标准模式表格"
            },
            "props": {
              "id": "3b8406c4-07c6-4000-a5f4-4acde2d30000",
              "isTitle": true,
              "colspan": 2,
              "rowspan": 2
            }
          },
          "rowHeader": [
            {
              "data": {
                "row": "行1"
              },
              "props": {
                "id": "dbd28ada-dfd9-4000-afd6-c7715a134000",
                "colspan": 1,
                "rowspan": 2,
                "isRowHeader": true,
                "rowId": "ba1412af-a82b-4000-aca9-2f86fc033000"
              },
              "children": [
                {
                  "data": {
                    "row": "行11"
                  },
                  "props": {
                    "id": "d16446ca-c0a2-4000-a4b6-8ea6e81b9000",
                    "colspan": 1,
                    "rowspan": 1,
                    "isRowHeader": true,
                    "parentId": "dbd28ada-dfd9-4000-afd6-c7715a134000",
                    "rowId": "ba1412af-a82b-4000-aca9-2f86fc033000"
                  }
                },
                {
                  "data": {
                    "row": "行12"
                  },
                  "props": {
                    "id": "5983f27b-9823-4000-a0fb-4c2ed32e7000",
                    "colspan": 1,
                    "rowspan": 1,
                    "isRowHeader": true,
                    "parentId": "dbd28ada-dfd9-4000-afd6-c7715a134000",
                    "rowId": "8327ce3f-8b57-4000-aed7-a52619f14000"
                  }
                }
              ]
            },
            {
              "data": {
                "row": "行2"
              },
              "props": {
                "id": "16d97374-654b-4000-a031-a28d989e8000",
                "colspan": 1,
                "rowspan": 2,
                "isRowHeader": true,
                "rowId": "fccf3d27-d4c9-4000-a024-bdc28cef6000"
              },
              "children": [
                {
                  "data": {
                    "row": "行21"
                  },
                  "props": {
                    "id": "9317270b-fffb-4000-a0c4-9667c4b4a000",
                    "colspan": 1,
                    "rowspan": 1,
                    "isRowHeader": true,
                    "parentId": "16d97374-654b-4000-a031-a28d989e8000",
                    "rowId": "fccf3d27-d4c9-4000-a024-bdc28cef6000"
                  }
                },
                {
                  "data": {
                    "row": "行22"
                  },
                  "props": {
                    "id": "ca6f62a9-f53d-4000-a573-a4820449a000",
                    "colspan": 1,
                    "rowspan": 1,
                    "isRowHeader": true,
                    "parentId": "16d97374-654b-4000-a031-a28d989e8000",
                    "rowId": "9b81d328-0c8e-4000-a4d7-caa04d5aa000"
                  }
                }
              ]
            }
          ],
          "columnHeader": [
            {
              "data": {
                "column": "列1"
              },
              "props": {
                "id": "a992aeb4-5728-4000-a828-200a4af3f000",
                "colspan": 2,
                "rowspan": 1,
                "isColumnHeader": true,
                "rowId": "2106cd0b-fe5c-4000-a881-0403ccb14000"
              },
              "children": [
                {
                  "data": {
                    "column": "列11",
                  },
                  "props": {
                    "id": "5e0abdbf-60e7-4000-af04-57d24de2c000",
                    "colspan": 1,
                    "rowspan": 1,
                    "isColumnHeader": true,
                    "parentId": "a992aeb4-5728-4000-a828-200a4af3f000",
                    "rowId": "2c78d1bb-52e2-4000-a3f5-461bf82d1000"
                  }
                },
                {
                  "data": {
                    "column": "列12"
                  },
                  "props": {
                    "id": "ebcd53ff-02da-4000-a38f-770a0f038000",
                    "colspan": 1,
                    "rowspan": 1,
                    "isColumnHeader": true,
                    "parentId": "a992aeb4-5728-4000-a828-200a4af3f000",
                    "rowId": "2c78d1bb-52e2-4000-a3f5-461bf82d1000"
                  }
                }
              ]
            },
            {
              "data": {
                "column": "列2"
              },
              "props": {
                "id": "9db1fb53-c9c3-4000-a863-e1b0fda16000",
                "colspan": 1,
                "rowspan": 1,
                "isColumnHeader": true,
                "rowId": "2106cd0b-fe5c-4000-a881-0403ccb14000"
              },
              "children": [
                {
                  "data": {
                    "column": "列21"
                  },
                  "props": {
                    "id": "ee457257-2e14-4000-ad96-cfb7600ef000",
                    "colspan": 1,
                    "rowspan": 1,
                    "isColumnHeader": true,
                    "parentId": "9db1fb53-c9c3-4000-a863-e1b0fda16000",
                    "rowId": "2c78d1bb-52e2-4000-a3f5-461bf82d1000"
                  }
                }
              ]
            }
          ],
          "cell": [
            [
              {
                "data": {
                  "cell": "格子[0,0]"
                },
                "props": {
                  "id": "340331ea-1b76-4000-a433-327e68039000",
                  "colspan": 1,
                  "rowspan": 1,
                  "isCell": true
                }
              },
              {
                "data": {
                  "cell": "格子[0,1]"
                },
                "props": {
                  "id": "99ff80cb-d62a-4000-a057-fb9a3da6e000",
                  "colspan": 1,
                  "rowspan": 1,
                  "isCell": true
                }
              },
              {
                "data": {
                  "cell": "格子[0,2]"
                },
                "props": {
                  "id": "b2c72c62-29a0-4000-a278-ea9a52cd9000",
                  "colspan": 1,
                  "rowspan": 1,
                  "isCell": true
                }
              }
            ],
            [
              {
                "data": {
                  "cell": "格子[1,0]"
                },
                "props": {
                  "id": "0e3aa55c-15ed-4000-a07f-30f4edd5a000",
                  "colspan": 1,
                  "rowspan": 1,
                  "isCell": true
                }
              },
              {
                "data": {
                  "cell": "格子[1,1]"
                },
                "props": {
                  "id": "f07fbf6a-16ee-4000-a2ab-a57429621000",
                  "isCell": true,
                  "colspan": 1,
                  "rowspan": 1
                }
              },
              {
                "data": {
                  "cell": "格子[1,2]"
                },
                "props": {
                  "id": "f4975da8-5116-4000-ac87-5c14d1b0d000",
                  "colspan": 1,
                  "rowspan": 1,
                  "isCell": true
                }
              }
            ],
            [
              {
                "data": {
                  "cell": "格子[2,0]"
                },
                "props": {
                  "id": "e88df40d-902d-4000-a5d5-2cda4fd8c000",
                  "colspan": 1,
                  "rowspan": 1,
                  "isCell": true
                }
              },
              {
                "data": {
                  "cell": "格子[2,1]"
                },
                "props": {
                  "id": "50b6f6c6-cabc-4000-a17d-61621ef3d000",
                  "colspan": 1,
                  "rowspan": 1,
                  "isCell": true
                }
              },
              {
                "data": {
                  "cell": "格子[2,2]"
                },
                "props": {
                  "id": "dddf1e8b-9ea6-4000-a957-33901e532000",
                  "colspan": 1,
                  "rowspan": 1,
                  "isCell": true
                }
              }
            ],
            [
              {
                "data": {
                  "cell": "格子[3,0]"
                },
                "props": {
                  "id": "42c4999f-380f-4000-ac0d-6936399fd000",
                  "colspan": 1,
                  "rowspan": 1,
                  "isCell": true
                }
              },
              {
                "data": {
                  "cell": "格子[3,1]"
                },
                "props": {
                  "id": "51b7ce3d-565f-4000-a22c-002dd001c000",
                  "colspan": 1,
                  "rowspan": 1,
                  "isCell": true
                }
              },
              {
                "data": {
                  "cell": "格子[3,2]"
                },
                "props": {
                  "id": "5836b3f9-7d99-4000-abbb-1075b5017000",
                  "colspan": 1,
                  "rowspan": 1,
                  "isCell": true
                }
              }
            ]
          ]
        }
      };

      return {
        data,
      };
    },
  },
}

}
```

## 合并模式

当需要 自由添加行/列 标题， 框选合并/拆分单元格功能时，可配置 `tableMode:"merge"` 开启该模式。当为合并模式时，支持 配置 `mergeable` 控制是否开启对应区域的合并功能。

合并单元格交互逻辑：

1. title区域，仅有编辑操作。
2. 按住鼠标左键，拖动可框选单元格，选中的单元格将高亮展示。存在选中的单元格时，在表格内任意地方点击会取消选中。
3. 只有相同区域的单元格才能合并。合并后，数据是第1个格子数据。
4. 在同一区域，多次选择的格子，会合并高亮选中。选中的格子，只有“合并单元格”操作。
5. 只有经过合并了的单元格，才可以拆分。拆分后，仅第1个格子有数据，其余格子为空数据。

注意：该模式的数据与基础模式数据不一致，一旦选定后不能切换 tableMode，否则会无法正常反显。

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "debug": true,
    "body": [
      {
        "type": "dimension-table",
        "name": "table",
        "tableMode": "merge",
        "label": "多维表",
        "title": {
          "body": {
            "type": "input-text",
            "name": "title",
            "label": false,
            "static": true
          },
          "editDialog": {
            "body": [
              {
                "type": "input-text",
                "name": "title",
                "label": "标题"
              }
            ]
          },
          "rules": [
            {
              "rule": "${!title}",
              "message": "请输入标题"
            }
          ]
        },
        "columnHeader": {
          "rules": [
            {
              "rule": "${!column}",
              "message": "请输入列标题"
            }
          ],
          "body": {
            "type": "input-text",
            "name": "column",
            "label": false,
            "static": true
          },
          "editDialog": {
            "body": [
              {
                "type": "input-text",
                "name": "column",
                "label": "列标题"
              }
            ]
          }
        },
        "rowHeader": {
          "rules": [
            {
              "rule": "${!row}",
              "message": "请输入行标题"
            }
          ],
          "body": {
            "type": "input-text",
            "name": "row",
            "label": false,
            "static": true
          },
          "editDialog": {
            "body": [
              {
                "type": "input-text",
                "name": "row",
                "label": "行标题"
              }
            ]
          }
        },
        "cell": {
          "rules": [
            {
              "rule": "${!cell}",
              "message": "请输入单元格内容"
            }
          ],
          "body": {
            "type": "input-text",
            "name": "cell",
            "label": false,
            "static": true
          },
          "editDialog": {
            "body": [
              {
                "type": "input-text",
                "name": "cell",
                "label": "单元格内容"
              }
            ]
          }
        }
      }
    ],
    "initApi": {
      "url": "/api/amis-mock/mock2/page/initData",
      "adaptor": () => {
      const data = {
        table: [
            {
              "id": "93911989-376e-4000-a42b-009f8f5e0000",
              "tds": [
                {
                  "id": "17ab8240-2301-4000-af32-027965b2c000",
                  "isTitle": true,
                  "colspan": 2,
                  "rowspan": 2,
                  "data": {
                    "title": "标题"
                  }
                },
                {
                  "id": "b9558043-9898-4000-a62a-75809bd94000",
                  "isColumnHeader": true,
                  "colspan": 2,
                  "rowspan": 1,
                  "data": {
                    "column": "列1"
                  }
                },
                {
                  "id": "69e7e79d-9f03-4000-acf2-f677a7a02000",
                  "colspan": 2,
                  "rowspan": 1,
                  "data": {
                    "column": "列2"
                  },
                  "isColumnHeader": true
                }
              ]
            },
            {
              "id": "056139c5-b38d-4000-ad67-788359b29000",
              "tds": [
                {
                  "id": "503209ac-3792-4000-a84c-3af5e2478000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "column": "列11"
                  },
                  "isColumnHeader": true
                },
                {
                  "id": "c6c45cdd-7477-4000-ae87-7dc01e093000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "column": "列12"
                  },
                  "isColumnHeader": true
                },
                {
                  "id": "ba707d9e-caad-4000-a9fa-307ef1d1f000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "column": "列21"
                  },
                  "isColumnHeader": true
                },
                {
                  "id": "dfc81823-d2af-4000-abb4-96429996e000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "column": "列22"
                  },
                  "isColumnHeader": true
                }
              ]
            },
            {
              "id": "42dc6c1b-477c-4000-aac3-a09c6bc6d000",
              "tds": [
                {
                  "id": "bb711406-dd09-4000-ad0c-ca797cbc2000",
                  "isRowHeader": true,
                  "colspan": 1,
                  "rowspan": 2,
                  "data": {
                    "row": "行1"
                  }
                },
                {
                  "id": "ce644483-9f9c-4000-a0ae-4566e271c000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "row": "行11"
                  },
                  "isRowHeader": true
                },
                {
                  "id": "5b1be93a-cca8-4000-af42-d0b2abc1d000",
                  "isCell": true,
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,0]"
                  }
                },
                {
                  "id": "cde2e00d-1289-4000-a7fd-01a9045b4000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,1]"
                  },
                  "isCell": true
                },
                {
                  "id": "483564d7-7e71-4000-a710-d7edbfd76000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,1]"
                  },
                  "isCell": true
                },
                {
                  "id": "fdb82633-89b4-4000-a8a9-149d4b8fa000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,1]"
                  },
                  "isCell": true
                }
              ]
            },
            {
              "id": "98615567-ebe2-4000-a062-75f240454000",
              "tds": [
                {
                  "id": "62a147b8-d632-4000-a8e4-b2b5d772e000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "row": "行12"
                  },
                  "isRowHeader": true
                },
                {
                  "id": "0a11eb4e-3e9c-4000-a61a-b84c732e5000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,0]"
                  },
                  "isCell": true
                },
                {
                  "id": "03a63e9f-5a97-4000-a990-3c1859302000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,1]"
                  },
                  "isCell": true
                },
                {
                  "id": "cb3ea548-3643-4000-ac29-ba600f7dc000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,1]"
                  },
                  "isCell": true
                },
                {
                  "id": "68faf63b-d7fc-4000-a158-50e21784e000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,1]"
                  },
                  "isCell": true
                }
              ]
            },
            {
              "id": "7dec8e8c-e719-4000-aedb-cb7ed7d74000",
              "tds": [
                {
                  "id": "3a5979b2-dc9b-4000-ae47-49158b532000",
                  "colspan": 1,
                  "rowspan": 2,
                  "data": {
                    "row": "行2"
                  },
                  "isRowHeader": true
                },
                {
                  "id": "9a911f1d-1e4b-4000-a522-72c3e8fd0000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "row": "行21"
                  },
                  "isRowHeader": true
                },
                {
                  "id": "a5eb53d4-b033-4000-a539-c2ebe1b76000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,0]"
                  },
                  "isCell": true
                },
                {
                  "id": "e1caae82-30df-4000-af5c-f5975fd77000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,1]"
                  },
                  "isCell": true
                },
                {
                  "id": "3ced73e4-f69f-4000-a27d-2cc39946c000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,1]"
                  },
                  "isCell": true
                },
                {
                  "id": "a994edce-092b-4000-a7fb-f37824bf5000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,1]"
                  },
                  "isCell": true
                }
              ]
            },
            {
              "id": "468a4b68-37e1-4000-a512-62f4f6b47000",
              "tds": [
                {
                  "id": "acbafac3-5724-4000-a4e9-49a8c0e9e000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "row": "行22"
                  },
                  "isRowHeader": true
                },
                {
                  "id": "9269b874-74be-4000-a217-48692fe5a000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,0]"
                  },
                  "isCell": true
                },
                {
                  "id": "15d3ee4b-5a0c-4000-a666-85c344f00000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,1]"
                  },
                  "isCell": true
                },
                {
                  "id": "fbba3570-1efe-4000-ab4b-7f0d2f32d000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,1]"
                  },
                  "isCell": true
                },
                {
                  "id": "e532686b-8694-4000-a7c0-36b3728da000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,1]"
                  },
                  "isCell": true
                }
              ]
            },
            {
              "id": "247d41f2-c912-4000-a789-1746d55b1000",
              "tds": [
                {
                  "id": "132377c1-aeb1-4000-af68-6cb784dcc000",
                  "colspan": 1,
                  "rowspan": 3,
                  "data": {
                    "row": "行3"
                  },
                  "isRowHeader": true
                },
                {
                  "id": "1b7d3b2e-6ccc-4000-a61d-dfa411946000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "row": "行31"
                  },
                  "isRowHeader": true
                },
                {
                  "id": "66445e56-4938-4000-a22b-b584e4588000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,0]"
                  },
                  "isCell": true
                },
                {
                  "id": "032fd12e-80e1-4000-a9dc-f091b1d97000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,1]"
                  },
                  "isCell": true
                },
                {
                  "id": "ffa9c48a-b570-4000-a0e8-fccd3bd7a000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,1]"
                  },
                  "isCell": true
                },
                {
                  "id": "df796000-706f-4000-a433-1f9745f94000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,1]"
                  },
                  "isCell": true
                }
              ]
            },
            {
              "id": "2a0cdc92-e602-4000-a364-899793603000",
              "tds": [
                {
                  "id": "afd9a4dc-5a12-4000-a2f0-c0d21a52b000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "row": "行32"
                  },
                  "isRowHeader": true
                },
                {
                  "id": "e80b1a4a-4431-4000-a8d2-fae452ca7000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,0]"
                  },
                  "isCell": true
                },
                {
                  "id": "a3ccf566-2a3b-4000-ac74-cd4ed5ded000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,1]"
                  },
                  "isCell": true
                },
                {
                  "id": "6b5e0e69-cc33-4000-a001-158e573a4000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,1]"
                  },
                  "isCell": true
                },
                {
                  "id": "2d5b0a5d-41f2-4000-a945-bc25707b2000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,1]"
                  },
                  "isCell": true
                }
              ]
            },
            {
              "id": "ea1423ae-0177-4000-ad9c-60913fa76000",
              "tds": [
                {
                  "id": "584f306e-f6b0-4000-a4e9-daa1cea17000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "row": "行33"
                  },
                  "isRowHeader": true
                },
                {
                  "id": "d4ae9930-10ab-4000-a55c-d46a37efd000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,0]"
                  },
                  "isCell": true
                },
                {
                  "id": "41ba02c0-90be-4000-a410-1641ed351000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,1]"
                  },
                  "isCell": true
                },
                {
                  "id": "4a97653e-7a36-4000-ae08-7ae11ecd9000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,1]"
                  },
                  "isCell": true
                },
                {
                  "id": "3d6ef1b3-e338-4000-a454-1dae65be8000",
                  "colspan": 1,
                  "rowspan": 1,
                  "data": {
                    "cell": "格子[0,1]"
                  },
                  "isCell": true
                }
              ]
            }
          ]
        }
      return {
        data
        }
      },
    }
  }
}
```

## 数据格式

在 表格模式 `tableMode` 为 `basic`基础模式，或`standard`标准模式时，表格数据为以下特定的对象格式保存。

```js
{
  // 第1行第1列表格标题区域
  title: {
    data: {}, // 具体单元格内存储数据。由 editDialog 表单弹窗，保存的数据
    props: {} // 其他表格属性，仅用于表格展示 (具体结构可参考“校验”部分 __td 格式 )
  },
  // 列标题区域（树状结构数据）
  columnHeader: [{
    data: {}, // 具体单元格内存储数据。由 editDialog 表单弹窗，保存的数据
    props: {} // 其他表格属性，仅用于表格展示
    children: [{ // 存在多维时
      data: {}, 
      props: {}
    }]
  }],
  // 行标题区域（树状结构数据）
  rowHeader: [{
    data: {}, // 具体单元格内存储数据。由 editDialog 表单弹窗，保存的数据
    props: {} // 其他表格属性，仅用于表格展示
    children: [{ // 存在多维时
      data: {}, 
      props: {}
    }]
  }],
  // 表格区域(2维数组)
  cell: [
    [{
      data: {}, // 具体单元格内存储数据。由 editDialog 表单弹窗，保存的数据
      props: {} // 其他表格属性，仅用于表格展示
    }]
  ]
}
```

在`tableMode`为 `merge` 合并模式时，数据为以下格式保存。

```js
[
  [{
    id: 'xx', // 行id
    tds: [{
      data: {}, // 具体单元格内存储数据。由 editDialog 表单弹窗，保存的数据
      id: 'xx', // 表格id
      ...// 其他表格属性，仅用于表格展示
    }]
  }]
]
```

在使用该组件时，需要注意不同表格模式后的数据兼容性问题。也需要与服务端商议，按照指定数据格式存储，如果数据不一致，可在 api 的 adaptor 中进行转换。

## Table 表格属性表

| 属性名 | 类型                 | 默认值 | 说明             |
| ------ | -------------------- | ------ | ---------------- |
| tableMode | `"basic"\|"standard"\|"merge"` |  "basic"  |  表格模式，默认是为 "basic"模式，不支持多维表头，与框选合并功能 |
| trackExpression | `表达式` | | 表达式值改变，则刷新表格。 （当单元格内静态展示渲染，依赖了上层数据域数据时需要配置，否则单元格渲染无法获取最新数据）|
| title | `object` |   | 左上角标题区域，参考 TableArea 属性表 |
| rowHeader | `object` |        | 行标题区域，参考 TableArea 属性表 |
| columnHeader | `object` |        | 列标题区域，参考 TableArea 属性表 |
| cell | `object` |        | 单元格区域，参考 TableArea 属性表 |
| addable | `boolean` |  true      | 可插入行/列  |
| editable | `boolean` |  true    | 可编辑单元格 |
| removable | `boolean` |  true     | 可删除行/列  |
| copyable | `boolean` |   true     | 可复制行/列；可复制/粘贴单元格(仅相同区域可复制粘贴)  |
| mergeable | `boolean` |   true     | 可合并/拆分单元格，仅在 tableMode:"merge" 情况下可用 |
| emptyHolder | `string` |  "暂无数据"  | 静态模式是，空数据情况下，提示文案 |
| hoverMode | `"row"\|"cross"` |  "row"  | 鼠标悬浮在单元格时样式，默认整行高亮。`1.83.2`版本支持"cross"十字交叉模式。 |

## TableArea 表格区域属性表

| 属性名 | 类型                 | 默认值 | 说明             |
| ------ | -------------------- | ------ | ---------------- |
| body | `Schema` |  | 单元格静态展示内容，当依赖了上层数据域变量时。需要在table中配置 `trackExpression` 表达式。  |
| editDialog | `Schema` |    | 编辑弹窗展示内容，参考dialog配置。  |
| rules | `Array<{rule: string, message?: string}>` |        | 静态展示校验规则。"rule" 校验表达式，"message" 提示文案。  |
| style | `object` |   | 单元格的样式 |
| addable | `boolean` |  true  | 可插入行/列  |
| editable | `boolean` | true  | 可编辑单元格。（title区域，仅支持 `editable`） |
| removable | `boolean` | true  | 可删除行/列  |
| copyable | `boolean` | true | 可复制行/列；可复制/粘贴单元格(仅相同区域可复制粘贴)  |
| mergeable | `boolean` | true  | 可合并/拆分单元格，仅在 tableMode:"merge" 情况下可用 |
