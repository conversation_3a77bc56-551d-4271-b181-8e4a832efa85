---
title: DsDatePicker
description:
type: 0
group: ⚙ 组件
menuName: DsDatePicker 日期选择
icon:
order: 27
standardMode: true
---

用来选择日期

## 基本使用

```schema: scope="body"
{
  "type": "form",
  "body": {
    "type": "ds-date-picker",
    "label": "选择日期"
  }
}
```

## 格式化

格式化(日，月，年，日期加时间，仅时间)

```
年/月/日/星期几/天T时:分:秒
"YYYY/MM/DD/星期d/DDTHH:mm:ss"
```

更多： https://day.js.org/docs/zh-CN/display/format

```schema: scope="body"
{
  "type": "form",
  "title": "格式化(日，月，年，日期加时间，仅时间)",
  "body": [
    {
      "type": "ds-date-picker",
      "label": "默认",
    },
    {
      "type": "ds-date-picker",
      "label": "YYYY-MM-DD",
      "format": "YYYY-MM-DD"
    },
    {
      "type": "ds-date-picker",
      "label": "MM-DD",
      "format": "MM-DD"
    },
    {
      "type": "ds-date-picker",
      "label": "YYYY-MM",
      "format": "YYYY-MM",
      "picker": "month"
    },
    {
      "type": "ds-date-picker",
      "label": "YYYY",
      "format": "YYYY",
      "picker": "year"
    },
  ]
}
```

## 日期+时间

日期选择组件也可以显示时间，或仅仅显示时间

```schema: scope="body"
{
  "type": "form",
  "body": [
    {
      "type": "ds-date-picker",
      "label": "显示时间默认",
      "showTime": true,
    },
    {
      "type": "ds-date-picker",
      "label": "日期+时间指定格式",
      "format": "YYYY-MM-DD HH:mm",
      "showTime": true,
    },
    {
      "type": "ds-date-picker",
      "label": "日期+时间指定格式优化",
      "format": "YYYY-MM-DD HH:mm",
      "showTime": {
        "format": "HH:mm",
      },
    },
    {
      "type": "ds-date-picker",
      "label": "仅显示时间",
      "showTime": true,
      "picker": "time",
    },
    {
      "type": "ds-date-picker",
      "label": "显示日期+时间复杂配置",
      "format": "YYYY-MM-DD HH:mm",
      "showTime": {
        "format": "HH:mm",
      },
      "disabledHourRanges": [[0, 7], [12, 13], [18, 24]],
      "disabledMinuteRanges": [[0, 5], [10, 20]],
      "disabledSecondRanges": [[0, 5], [20, 30]],
    },
    {
      "type": "ds-date-picker",
      "label": "仅显示时间复杂配置",
      "showTime": {
        "format": "HH:mm",
      },
      "picker": "time",
      "disabledHourRanges": [[0, 7], [12, 13], [18, 24]],
      "disabledMinuteRanges": [[0, 5], [10, 20]],
      "disabledSecondRanges": [[0, 5], [20, 30]],
    },
    {
      "type": "ds-date-picker",
      "label": "步进",
      "showTime": {
        "hourStep": 4,
        minuteStep: 15,
        secondStep: 10,
      },
      "picker": "time",
    },
  ]
}
```

## 默认值

使用 value 值设置默认值

```schema: scope="body"
{
  "type": "form",
  "body": [
    {
      "type": "ds-date-picker",
      "value": "2023-04-17",
    },
  ]
}
```

## static

```schema: scope="body"
{
  "type": "form",
  "body": [
    {
      "type": "ds-date-picker",
      "label": "value: 2023-05-09",
      "static": true,
      "value": "2023-05-09",
      "format": "YYYY-MM-DD",
    }
  ]
}
```

## 限制日期可选范围

固定可选范围 ["2023-04-18", "2023-04-26"]

所有日期限制条件（`minDate`、`maxDate`、`disabledDateRanges`）都会同时生效，任何一个条件禁用的日期都不可选。

```schema: scope="body"
{
  "type": "form",
  "body": [
    {
      "type": "ds-date-picker",
      "label": "minDate",
      minDate: "now",
    },
    {
      "type": "ds-date-picker",
      "label": "maxDate",
      maxDate: "now",
    },
    {
      "type": "ds-date-picker",
      "label": "2023-04-18 ~ 2023-04-26",
      minDate: "2023-04-18",
      maxDate: "2023-04-26"
    },
    {
      "type": "ds-date-picker",
      "label": "2023-04-18 ~ 2023-04-18",
      minDate: "2023-04-18",
      maxDate: "2023-04-18"
    },
    {
      "type": "ds-date-picker",
      "label": "minDate: 2023-04-18",
      minDate: "2023-04-18",
    },
    {
      "type": "ds-date-picker",
      "label": "多个禁用选择区间 -3days~+3days, +7days~+10days",
      disabledDateRanges: [
        ["-3days", "+3days"],
        ["+7days", "+10days"],
      ]
    },
    {
      "type": "ds-date-picker",
      "label": "多个禁用选择区间 2023-04-05~2023-04-07, 2023-04-10~2023-04-13...",
      disabledDateRanges: [
        ["", "2023-04-03"],
        ["2023-04-05", "2023-04-07"],
        ["2023-04-10", "2023-04-13"],
        ["2023-04-17", "2023-04-19"],
        ["+3days", ""]
      ]
    },
    {
      "type": "ds-date-picker",
      "label": "组合限制",
      minDate: "2025-05-01",
      maxDate: "2025-05-31",
      disabledDateRanges: [
        ["2025-05-03", "2025-05-05"],
        ["2025-05-10", "2025-05-12"]
      ]
    },
  ]
}
```

## 不可选择日期和时间

可参照 [ant design日期组件禁用范围](https://ant.design/components/date-picker-cn#components-date-picker-demo-disabled-date)

```
const range = (start, end) => {
  const result = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }
  return result;
};

{
  "type": "page",
  "body": [
    {
      "type": "ds-date-picker",
      "label": "日期",
      "showTime": true,
      disabledDate: (current) => current && current < dayjs().endOf("day"),
      disabledTime: () => ({
        disabledHours: () => range(0, 24).splice(4, 20),
        disabledMinutes: () => range(30, 60),
        disabledSeconds: () => [55, 56],
      })
    }
  ]
}
```

```
const range = (start, end) => {
  const result = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }
  return result;
};

{
  "type": "page",
  "body": [
    {
      "type": "ds-date-range-picker",
      "label": "日期范围",
      "showTime": true,
      disabledDate: (current) => current && current < dayjs().endOf("day"),
      disabledTime: (_, type) => {
        if (type === "start") {
          return {
            disabledHours: () => range(0, 60).splice(4, 20),
            disabledMinutes: () => range(30, 60),
            disabledSeconds: () => [55, 56],
          };
        }
        return {
          disabledHours: () => range(0, 60).splice(20, 4),
          disabledMinutes: () => range(0, 31),
          disabledSeconds: () => [55, 56],
        };
      }
    }
  ]
}
```

## 预设快捷选择日期

### 内置预设字符串

配置 `presets` 属性，可以开启快捷预设，支持给定的字符串预设。如 `"presets": [ "yesterday", "today", "tomorrow"]`

内置列表

- now 现在
- today 今天
- yesterday 昨天
- thisweek 本周一
- thismonth 本月初
- prevmonth 上个月初
- prevquarter 上个季度初
- thisquarter 本季度初
- tomorrow 明天
- endofthisweek 本周日
- endofthismonth 本月底
- endoflastmonth 下个月底

```schema: scope="body"
{
  "type": "form",
  "title": "预设快捷选择日期",
  "body": [
    {
      "type": "ds-date-picker",
      presets: ["yesterday", "today", "tomorrow"],
    },
  ]
}
```

### 自定义快捷预设

`presets` 配置，除了支持上述给定的相对时间字符串，也可以设置自定义的预设值。 (自定义预设与给定的字符串预设，可以同时设置)

自定义预设项目的格式：

```json
{
  "label": "10天后", // 必填，字符串，快捷按钮展示文案
  "key": "10DayAfter", // 必填，字符串，当前快捷按钮唯一key
  "date": "${DATEMODIFY(NOW(), 10, 'day')}", // 必填，字符串|dayjs，可使用表达式DATEMODIFY,NOW 计算时间。也可直接使用 dayjs对象，比如： dayjs().add(10, 'day')
}
```

```schema: scope="body"
{
  type: 'form',
  debug: true,
  body: [{
    "type": "ds-date-picker",
    name: "date3",
    label: "时间范围",
    presetsIncludeToday: true,
    presets: [
      'today',
      'tomorrow',
      {
        key: '5DaysAfter',
        label: '5天后',
        date: "${DATEMODIFY(NOW(), 5, 'day')}",
      },
      {
        key: '10DaysAfter',
        label: '10天后',
        date: "${DATEMODIFY(NOW(), 10, 'day')}",
      },
    ],
  },
  ]
}
```

## 相对时间

如 "-3days", "-30days", "-1hours"

单位列表：

- days
- weeks
- months
- quarters
- years
- hours
- minutes
- sceonds

```schema: scope="body"
{
  "type": "form",
  "label": "使用相对时间",
  "body": [
    {
      "type": "ds-date-picker",
      "label": "可选日期范围 [-3days, +7days]",
      minDate: "-3days",
      maxDate: "+7days",
    },
    {
      "type": "ds-date-picker",
      "label": "可选日期范围 [-30days, now]",
      minDate: "-30days",
      maxDate: "now",
    },
    {
      "type": "ds-date-picker",
      "label": "可选日期范围 [now, +7days]",
      minDate: "now",
      maxDate: "+7days",
    },
  ]
}
```

## 重置表单

```schema: scope="body"
{
  "type": "form",
  "body": {
    "type": "form",
    "body": [
      {
        "type": "input-text",
        "name": "name",
        "label": "姓名"
      },
      {
        "type": "input-email",
        "name": "email",
        "label": "邮箱"
      },
      {
        "label": "选项",
        "type": "select",
        "name": "select",
        "menuTpl": "<div>${label} 值：${value}, 当前是否选中: ${checked}</div>",
        "options": [
          {
            "label":"A",
            "value":"a"
          },
          {
            "label":"B",
            "value":"b"
          },
          {
            "label":"C",
            "value":"c"
          }
        ]
      },
      {
        "type": "ds-date-picker",
        "name": "date1",
        "label": "日期"
      },
      {
        "type": "ds-date-range-picker",
        "name": "date2",
        "label": "日期"
      }
    ],
    "actions": [
      {
        "type": "reset",
        "label": "重置"
      },
      {
        "type": "submit",
        "label": "保存"
      }
    ]
  }
}
```

## 属性表

暂不支持内嵌模式

| 参数               | 说明                                                          | 类型                                               | 默认值             |  版本   |
| ------------------ | ------------------------------------------------------------- | -------------------------------------------------- | ------------------ | --- |
| allowClear         | 是否显示清除按钮                                              | boolean                                            | true               |
| autoFocus          | 自动获取焦点                                                  | boolean                                            | false              |
| bordered           | 是否有边框                                                    | boolean                                            | true               |
| className          | 选择器 className                                              | string                                             | -                  |
| disabled           | 禁用                                                          | boolean                                            | false              |
| minDate            | 可选择的最小日期                                              | string                                             | -                  |
| maxDate            | 可选择的最大日期                                              | string                                             | -                  |
| disabledDateRanges | 禁用日期范围，二维数组，支持多个禁用选择区间                  | `[string, string][]`                               | -                  |
| format             | 设置日期格式, 根据不同的 picker, 默认格式不同                 | formatType                                         |                    |
| inputReadOnly      | 设置输入框为只读（避免在移动设备上打开虚拟键盘）              | boolean                                            | false              |
| open               | 控制弹层是否展开                                              | boolean                                            | -                  |
| picker             | 设置选择器类型                                                | `date` \| `month` \| `year` | `date`             |
| placeholder        | 输入框提示文字                                                | string \| \[string, string]                        | -                  |
| placement          | 选择框弹出的位置                                              | `bottomLeft` `bottomRight` `topLeft` `topRight`    | bottomLeft         |
| presets            | 预设时间范围快捷选择                                          | ["yestoday","today", "tomorrow"]                   | -                  |
| size               | 输入框大小，`large` 高度为 40px，`small` 为 24px，默认是 32px | `large` \| `middle` \| `small`                     | -                  |
| status             | 设置校验状态                                                  | "error" \| "warning"                               | -                  |
| showNow            | 当设定了 `showTime` 的时候，面板是否显示“此刻”按钮            | boolean                                            | -                  |
| showTime           | 增加时间选择功能                                              | boolean \| Object                                  | TimePicker Options |
| showToday          | 是否展示“今天”按钮                                            | boolean                                            | true               |
| value              | 日期                                                          |                                                    | -                  |
| disabledTime              | 不可选择的时间                                                   |      `function(date: dayjs, partial: start \| end)`                                              | -                  | `1.10.0` |
| disabledDate              | 不可选择的日期, 当`minDate`、`maxDate`和`disabledDateRanges` 无法满足需求时，可通过这个自定义实现                                                          |      `(currentDate: dayjs) => boolean`                                              | -                  | `1.10.0` |

## 事件表

当前组件会对外派发以下事件，可以通过 onEvent 来监听这些事件，并通过 actions 来配置执行的动作，在 actions 中可以通过${事件参数名}来获取事件产生的数据,，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action)。

| 事件名称    | 事件参数                 | 说明                     |
| ----------- | ------------------------ | ------------------------ |
| focus       | [name]: string 组件的值  | 输入框获取焦点时触发     |
| blur        | [name]: string 组件的值  | 输入框失去焦点时触发     |
| change      | [name]: string 组件的值  | 时间值变化时触发         |
| ok          | [name]: string 组件的值  | 点击确定按钮时触发       |
| panelChange | [name]: string 组件的值  | 日历面板切换时触发       |
| openChange  | boolean 当前日历打开状态 | 弹出日历和关闭日历时触发 |

示例

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  data: {
    date: "2023-05-09 15:30:20",
  },
  "body": [
    {
      "type": "ds-date-picker",
      name: "date",
      "label": "事件",
      "showTime": true,
      "onEvent": {
        "focus": { // 监听事件
          "actions": [ // 执行的动作列表
            {
              "actionType": "toast", // 执行toast提示动作
              "args": { // 动作参数
                "msgType": "info",
                "msg": "派发 focus 事件"
              }
            }
          ],
        },
        "blur": {
          "actions": [
            {
              "actionType": "toast",
              "args": {
                "msgType": "info",
                "msg": "派发 blur 事件"
              }
            }
          ],
        },
        "change": {
          "actions": [
            {
              "actionType": "toast",
              "args": {
                "msgType": "info",
                "msg": "派发 change 事件, 当前值 ${event.data.value}",
              }
            }
          ],
        },
      },
    },
  ]
}
```

## 动作表

| 动作名称 | 动作配置                   | 说明                                                 |
| -------- | -------------------------- | ---------------------------------------------------- |
| clear    | -                          | 清空                                                 |
| reset    | -                          | 将值重置为 resetValue，若没有配置 resetValue，则清空 |
| setValue | value: string 更新的时间值 | 更新数据，依赖格式 format                            |

示例

```schema: scope="body"
{
  "type": "form",
  "body": [
    {
      "type": "button",
      "label": "clear",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "clear",
              "componentId": "actionDatePicker",
              "args": {
                "value": null,
              }
            }
          ]
        }
      }
    },
    {
      "type": "button",
      "label": "reset",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "reset",
              "componentId": "actionDatePicker",
              "args": {
                "value": "",
              }
            }
          ]
        }
      }
    },
    {
      "type": "button",
      "label": "setValue",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "setValue",
              "componentId": "actionDatePicker",
              "args": {
                "value": "+1days"
              },
            }
          ]
        }
      }
    },
    {
      "type": "ds-date-picker",
      id: "actionDatePicker",
      "label": "动作",
      "value": "now",
    }
  ]
}
```
