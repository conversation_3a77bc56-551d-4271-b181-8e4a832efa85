---
title: DsDateRangePicker
description:
type: 0
group: ⚙ 组件
menuName: DsDateRangePicker 日期范围选择
icon:
order: 27
---

用来选择日期范围

## 基本使用

```schema: scope="body"
{
  "type": "form",
  "body": {
    "type": "ds-date-range-picker",
    "name": "date",
    "label": "选择日期范围"
  }
}
```

## 选择日期和时间

```schema: scope="body"
{
  "type": "form",
  "data": {
    "date": [
      "2023-08-21 10:00:00",
      "2023-09-03 18:40:32"
    ]
  },
  "body": {
    "type": "ds-date-range-picker",
    "name": "date",
    "label": "选择日期范围",
    "showTime": true,
    "minDate": "now"
  }
}
```

## 格式化

格式化(日，周，月，季度，年，日期加时间，仅时间)

```schema: scope="body"
{
  "type": "form",
  "body": [
    {
      "type": "ds-date-range-picker",
      "format": "YYYY/MM/DD",
    },

    {
      "type": "ds-date-range-picker",
      "format": "MM/DD",
    },
    {
      "type": "ds-date-range-picker",
      "format": "YYYY/MM",
      picker: "month",
    },
    {
      "type": "ds-date-range-picker",
      "format": "YYYY",
      picker: "year",
    },
  ],
}
```

## 默认值

```schema: scope="body"
{
  "type": "form",
  "label": "设置默认值",
  "body": [
    {
      "type": "ds-date-range-picker",
      "value": ["2023/04/17", "2023/04/18"],
      "format": "YYYY/MM/DD"
    },
  ]
}
```

## static

```schema: scope="body"
{
  "type": "form",
  "body": [
    {
      "type": "ds-date-range-picker",
      "label": "value: ['2023/04/17', '2023/04/18']",
      "value": ["2023/04/17", "2023/04/18"],
      "static": true,
      "format": "YYYY/MM/DD"
    },
    {
      "type": "ds-date-range-picker",
      "label": "value: ['today', '+7days']",
      "value": ["today", "+7days"],
      "static": true,
    },
  ]
}
```

## 限制日期可选范围

固定可选范围 ["2023-04-17", "2023-04-26"]

配置 disabledDateRanges 后 minDate 与 maxDate 配置将无效。

```schema: scope="body"
{
  "type": "form",
  "body": [
    {
      "type": "ds-date-range-picker",
      "label": "minDate",
      minDate: "+3days",
    },
    {
      "type": "ds-date-range-picker",
      "label": "maxDate",
      maxDate: "+3days",
    },
    {
      "type": "ds-date-range-picker",
      minDate: "2023-04-18",
      maxDate: "2023-04-26",
    },
    {
      "type": "ds-date-range-picker",
      minDate: "2023-04-18",
      maxDate: "2023-04-26",
      disabledRange: true,
    },
    {
      "type": "ds-date-range-picker",
      minDate: "2023-05",
      maxDate: "2023-10",
      picker: "month",
    },
    {
      "type": "ds-date-range-picker",
      minDate: "2022",
      maxDate: "2028",
      picker: "year",
    },
  ]
}
```

## 预设快捷选择日期区间

### 内置预设字符串

配置 `presets` 属性，可以开启快捷预设，支持给定的字符串预设。如 ["today", "7daysago", "30daysago", "90daysago"]。

默认情况下，"近X天" 是不包括 "今天" 的，可配置 `presetsIncludeToday: true`，会包含 “今天”。

内置区间列表：

- today 今天
- yesterday 昨天
- tomorrow 明天
- 1daysago 最近 1 天
- 3daysago 最近 3 天
- 7daysago 最近 7 天
- 30daysago 最近 30 天
- 90daysago 最近 90 天
- prevweek 上周
- thisweek 本周
- thismonth 本月
- thisquarter 本季度
- prevmonth 上个月
- prevquarter 上个季度
- thisyear 今年
- prevyear 去年

```schema: scope="body"
{
  "type": "form",
  "body": [
    {
      "label": "不包含今天",
      "type": "ds-date-range-picker",
      name: "date1",
      presets: ["today", "7daysago", "30daysago", "90daysago"],
    },
    {
      "label": "包含今天",
      "type": "ds-date-range-picker",
      name: "date2",
      presetsIncludeToday: true,
      presets: ["today", "7daysago", "30daysago", "90daysago"],
    },
  ]
}
```

### 自定义快捷预设

`presets` 配置，除了支持上述给定的相对时间字符串，也可以设置自定义的预设值。 (自定义预设与给定的字符串预设，可以同时设置)

自定义预设项目的格式：

```json
{
  "label": "10天后", // 必填，字符串，快捷按钮展示文案
  "key": "10DayAfter", // 必填，字符串，当前快捷按钮唯一key
  "startDate": "${DATEMODIFY(NOW(), 10, 'day')}", // 必填，字符串｜dayjs，可使用表达式 DATEMODIFY,NOW 计算时间。也可直接使用 dayjs对象，比如： dayjs().add(10, 'day')
  "endDate": "${NOW()}" // 必填，字符串|dayjs， 可使用表达式 DATEMODIFY,NOW 计算时间。也可直接使用 dayjs对象，比如： dayjs().add(10, 'day')
}
```

```schema: scope="body"
{
  type: 'form',
  debug: true,
  body: [{
    "type": "ds-date-range-picker",
    name: "date3",
    label: "时间范围",
    showTime: true,
    presetsIncludeToday: true,
    presets: [
      {
        key: '1HourAgo',
        label: '近1小时',
        startDate: "${DATEMODIFY(NOW(), -1, 'hour')}",
        endDate: "${NOW()}"
      },
      {
        key: '2HourAgo',
        label: '近2小时',
        startDate: "${DATEMODIFY(NOW(), -2, 'hour')}",
        endDate: "${NOW()}"
      },
      {
        key: '4HourAgo',
        label: '近4小时',
        startDate: "${DATEMODIFY(NOW(), -4, 'hour')}",
        endDate: "${NOW()}"
      },
      'today',
      'yesterday',
    ],
  },
  ]
}
```

## 使用预设设置日期值

配置 `presetValue`  属性，可以设置指定的预设值。该属性必须，配合 `presets` 一起使用。

此外，由 `presets` 设置的值（点击日期选择弹出层，左侧的快捷设置的按钮时）， 在 `change` 事件中，可获取到 `event.data.presetValue` 值，可以将该值存到 form 中。

```schema: scope="body"
{
  type: 'form',
  id: 'myForm',
  debug: true,
  data: {
    presetDate3: 'today'
  },
  body: [{
    "type": "ds-date-range-picker",
    name: "date3",
    label: "时间范围",
    showTime: true,
    presetValue: '${presetDate3}',
    presetsIncludeToday: true,
    presets: [
      "today",
      {
        key: '5DaysAgo',
        label: '最近5天',
        startDate: "${DATEMODIFY(NOW(), -5, 'day')}",
        endDate: "${NOW()}"
      },
      "7daysago",
      "30daysago"
    ],
    onEvent: {
      change: {
        actions: [{
          actionType: 'setValue',
          componentId: 'myForm',
          args: {
            value: {
              presetDate3: '${event.data.presetValue}'
            }
          }
        }]
      }
    }
  },
  {
    type: 'button',
    label: "设置：近7天",
    onEvent: {
      click: {
        actions: [{
          actionType: 'setValue',
          componentId: 'myForm',
          args: {
            value: {
              presetDate3: '7daysago'
            }
          }
        }]
      }
    }
  }
  ]
}
```

## 相对时间

如 "-3days", "-30days", "-1hours"

单位列表：

- days
- months
- quarters
- years
- hours
- minutes
- sceonds

```schema: scope="body"
{
  "type": "form",
  "label": "使用相对时间",
  "body": [
    {
      "type": "ds-date-range-picker",
      "label": "可选日期范围 [-3days, +7days]",
      minDate: "-3days",
      maxDate: "+7days",
    },
    {
      "type": "ds-date-range-picker",
      "label": "可选日期范围 [-30days, now]",
      minDate: "-30days",
      maxDate: "now",
    },
    {
      "type": "ds-date-range-picker",
      "label": "可选日期范围 [now, +7days]",
      minDate: "now",
      maxDate: "+7days",
    },
  ]
}
```

## 属性表

| 参数               | 说明                                                          | 类型                                               | 默认值     |      备注               |
| ------------------ | ------------------------------------------------------------- | -------------------------------------------------- | ---------- | ------------------- |
| allowClear         | 是否显示清除按钮                                              | boolean                                            | true       |
| autoFocus          | 自动获取焦点                                                  | boolean                                            | false      |
| bordered           | 是否有边框                                                    | boolean                                            | true       |
| className          | 选择器 className                                              | string                                             | -          |
| disabled           | 禁用                                                          | boolean                                            | false      |
| minDate            | 可选择的最小日期                                              | string                                             | -          |
| maxDate            | 可选择的最大日期                                              | string                                             | -          |
| disabledDateRanges | 优先级高，二维数组                                            | `[string, string][]`                               | -          |
| format             | 设置日期格式, 根据不同的 picker, 默认格式不同                 | formatType                                         |            |
| inputReadOnly      | 设置输入框为只读（避免在移动设备上打开虚拟键盘）              | boolean                                            | false      |
| open               | 控制弹层是否展开                                              | boolean                                            | -          |
| picker             | 设置选择器类型                                                | `date` \| `month`  \| `year` | `date`     |
| placeholder        | 输入框提示文字                                                | string \| \[string, string]                        | -          |
| placement          | 选择框弹出的位置                                              | `bottomLeft` `bottomRight` `topLeft` `topRight`    | bottomLeft |
| presets            | 预设时间范围快捷选择                                          | ["yestoday","today", "tomorrow"]                   | -          |
| presetsIncludeToday| 预设时间范围 “近XX天”，日期范围是否包含今天   | -                   | -          |  版本1.67.0  |
| size               | 输入框大小，`large` 高度为 40px，`small` 为 24px，默认是 32px | `large` \| `middle` \| `small`                     | -          |
| status             | 设置校验状态                                                  | "error" \| "warning"                               | -          |
| showNow            | 当设定了 `showTime` 的时候，面板是否显示“此刻”按钮            | boolean                                            | -          |
| showTime           | 增加时间选择功能                                              | boolean                                            | object     | 参考 AntdTimePicker |
| showToday          | 是否展示“今天”按钮                                            | boolean                                            | true       |
| value              | 日期                                                          |                                                    | -          |
| presetValue | 预设值 | 'today' \| '7daysago' ... | undefined | 可以在 change 事件的 `event.data.presetValue` 取到  |
## 事件表

当前组件会对外派发以下事件，可以通过 onEvent 来监听这些事件，并通过 actions 来配置执行的动作，在 actions 中可以通过${event.data.value}来获取事件产生的数据,，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action)。

| 事件名称       | 事件参数(event.data 的类型)                 | 说明                     |
| -------------- | ------------------------ | ------------------------ |
| focus          | `{value?: [string, string]}`  | 输入框获取焦点时触发     |
| blur           | `{value?: [string, string]}`  | 输入框失去焦点时触发     |
| change         | `{value?: [string, string], presetValue?: string}`  | 时间值变化时触发         |
| ok             | `{value?: [string, string]}`  | 点击确定按钮时触发       |
| panelChange    | `{value?: [string, string]}`  | 日历面板切换时触发       |
| calendarChange | `{value?: [string, string]}` | 待选日期发生变化时触发   |
| openChange     | boolean 当前日历打开状态 | 弹出日历和关闭日历时触发 |

## 动作表

| 动作名称 | 动作配置                   | 说明                                                 |
| -------- | -------------------------- | ---------------------------------------------------- |
| clear    | -                          | 清空                                                 |
| reset    | -                          | 将值重置为 resetValue，若没有配置 resetValue，则清空 |
| setValue | value: [string, string] 更新的时间值 | 更新数据，依赖格式 format                            |

示例

```schema: scope="body"
{
  "type": "form",
  "body": [
    {
      "type": "button",
      "label": "clear",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "clear",
              "componentId": "actionDateRangePicker",
              "args": {
                "value": null,
              }
            }
          ]
        }
      }
    },
    {
      "type": "button",
      "label": "reset",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "reset",
              "componentId": "actionDateRangePicker",
              "args": {
                "value": "",
              }
            }
          ]
        }
      }
    },
    {
      "type": "button",
      "label": "setValue",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "setValue",
              "componentId": "actionDateRangePicker",
              "args": {
                "value": ["+1days", "+7days"],
              },
            }
          ]
        }
      }
    },
    {
      "type": "ds-date-range-picker",
      "id": "actionDateRangePicker",
      "label": "动作",
      "value": ["now", "+1days"],
    }
  ]
}
```
