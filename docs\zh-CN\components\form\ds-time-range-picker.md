---
title: DsTimeRangePicker
description:
type: 0
group: ⚙ 组件
menuName: DsTimeRangePicker 时间选择
icon:
order: 27
standardMode: true
---

用来选择日期范围

## 基本使用

```schema: scope="body"
{
  "type": "form",
  "body": {
    "type": "ds-time-range-picker",
    "label": "选择时间范围"
  }
}
```

## 格式化

格式： `HH:mm:ss`

```schema: scope="body"
{
  "type": "form",
  "title": "格式化(时、分、秒)",
  "body": [
    {
      "type": "ds-time-range-picker",
      "label": "HH:mm:ss",
      "format": ""
    },
    {
      "type": "ds-time-range-picker",
      "label": "HH:mm",
      "format": "HH:mm"
    },
    {
      "type": "ds-time-range-picker",
      "label": "mm:ss",
      "format": "mm:ss",
    },
  ]
}
```

## 默认值

使用 value 值设置默认值

```schema: scope="body"
{
  "type": "form",
  "title": "设置默认值",
  "body": [
    {
      "type": "ds-time-range-picker",
      "label": "默认值 ['12:08:09', '15:09:10']",
      "value": ["12:08:09", "15:09:10"]
    },
    {
      "type": "ds-time-range-picker",
      "label": "默认值 ['12:08', '13:09']",
      "value": ["12:08", "13:09"],
      "format": "HH:mm"
    }
  ]
}
```

## static

```schema: scope="body"
{
  "type": "form",
  "body": [
    {
      "type": "ds-time-range-picker",
      "label": "value: ['12:08:09', '15:09:10']",
      "static": true,
      "value": ["12:08:09", "15:09:10"],
    },
    {
      "type": "ds-time-range-picker",
      "label": "value: ['12:08', '15:09']",
      "static": true,
      "value": ["12:08", "15:09"],
      "format": "HH:mm",
    },
  ]
}
```

## 限制时间可选区间

可使用以下属性控制，disabledXXX 与 enabledXXX 配置共存时，enabledXXX 配置将无效。

- "disabledHourRanges": [[0, 7], [12, 13], [18, 24]],
- "disabledMinuteRanges": [[0, 5], [10, 20]],
- "disabledSecondRanges": [[0, 5], [20, 30]],
- enabledHourRanges: [[8, 18]],
- enabledMinuteRanges: [[10, 20]],
- enabledSecondRanges: [[15, 20]],

```schema: scope="body"
{
  "type": "form",
  "title": "限制时间可选范围",
  "body": [
    {
      "type": "ds-time-range-picker",
      "label": "不可选区间",
      "disabledHourRanges": [[0, 7], [12, 13], [18, 24]],
      "disabledMinuteRanges": [[0, 5], [10, 20]],
      "disabledSecondRanges": [[0, 5], [20, 30]],
    },
    {
      "type": "ds-time-range-picker",
      "label": "不可选区间，隐藏不可选项",
      "hideDisabledOptions": true,
      "disabledHourRanges": [[0, 7], [12, 13], [18, 24]],
      "disabledMinuteRanges": [[0, 5], [10, 20]],
      "disabledSecondRanges": [[0, 5], [20, 30]],
    },
    {
      "type": "ds-time-range-picker",
      "label": "可选区间",
      "hideDisabledOptions": true,
      enabledHourRanges: [[8, 18]],
      enabledMinuteRanges: [[10, 20]],
      enabledSecondRanges: [[15, 20]],
    },
    {
      "type": "ds-time-range-picker",
      "label": "配置并存, enabledXXX 无效",
      "hideDisabledOptions": true,
      enabledHourRanges: [[8, 18]],
      enabledMinuteRanges: [[10, 20]],
      enabledSecondRanges: [[15, 20]],
      "disabledHourRanges": [[0, 7], [12, 13], [18, 24]],
      "disabledMinuteRanges": [[0, 5], [10, 20]],
      "disabledSecondRanges": [[0, 5], [20, 30]],
    },
  ]
}
```

## 步进

```schema: scope="body"
{
  "type": "form",
  "title": "步进",
  "body": [
    {
      "type": "ds-time-range-picker",
      "label": "步进",
      "hourStep": 4,
      minuteStep: 15,
      secondStep: 10,
    },
  ]
}
```

## 12小时制

```schema: scope="body"
{
  "type": "form",
  "label": "步进",
  "body": [
    {
      "type": "ds-time-range-picker",
      "label": "12 小时制",
      "use12Hours": true
    },
  ]
}
```

## 属性表

| 参数 | 说明  | 类型 | 默认值  |
| ------------------ | ----------------- | ------- | --------- |
| allowClear         | 是否显示清除按钮      | boolean | true |
| autoFocus          | 自动获取焦点          | boolean    | false                |
| bordered           | 是否有边框            | boolean    | true                 |
| className          | 选择器 className      | string     | - |
| disabled           | 禁用                  | boolean    | false                |
| hourStep           | 小时选项间隔           | number | 1 |
| minuteStep         | 分钟选项间隔           | number | 1 |
| secondStep         | 秒选项间隔             | number | 1 |
| disabledHourRanges | 不可选时间区间 | `[number,number][]` | -
| disabledMinuteRanges | 不可选时间区间 | `[number,number][]` | -
| disabledSecondRanges | 不可选时间区间 | `[number,number][]` | -
| enabledHourRanges    | 可选时间区间   | `[number,number][]` | -
| enabledMinuteRanges  | 可选时间区间   | `[number,number][]` | -
| enabledSecondRanges  | 可选时间区间   | `[number,number][]` | -
| format             | 设置日期格式, 根据不同的 picker, 默认格式不同                 | formatType |   |
| inputReadOnly      | 设置输入框为只读（避免在移动设备上打开虚拟键盘）              | boolean    | false                |
| open               | 控制弹层是否展开      | boolean    | - |
| placeholder        | 输入框提示文字        | string \| \[string, string]     | - |
| placement          | 选择框弹出的位置      | `bottomLeft` `bottomRight` `topLeft` `topRight`    | bottomLeft           |
| size               | 输入框大小，`large` 高度为 40px，`small` 为 24px，默认是 32px | `large` \| `middle` \| `small`  | - |
| status             | 设置校验状态          | "error" \| "warning"            | - |
| showNow            | 当设定了 `showTime` 的时候，面板是否显示“此刻”按钮            | boolean    | - |
| value              | 日期                  |            | - |
| use12Hours         | 使用 12 小时制，为 true 时 format 默认为 h:mm:ss a | boolean | false |

## 事件表

| 事件名称 | 事件参数                   | 说明 |
| ------- | ------------------------ | ------- |
| change  | [name]: string 组件的值    | 时间值变化时触发   |
| focus   | [name]: string 组件的值    | 输入框获取焦点时触发   |
| blur    | [name]: string 组件的值    | 输入框失去焦点时触发   |
| openChange | boolean                | 当前选择框打开状态   |

示例

```schema: scope="body"
{
  "type": "form",
  "body": [
    {
      "type": "ds-time-range-picker",
      name: "time",
      "label": "事件",
      "onEvent": {
        "focus": { // 监听事件
          "actions": [ // 执行的动作列表
            {
              "actionType": "toast", // 执行toast提示动作
              "args": { // 动作参数
                "msgType": "info",
                "msg": "派发 focus 事件"
              }
            }
          ],
        },
        "blur": {
          "actions": [
            {
              "actionType": "toast",
              "args": {
                "msgType": "info",
                "msg": "派发 blur 事件"
              }
            }
          ],
        },
        "change": {
          "actions": [
            {
              "actionType": "toast",
              "args": {
                "msgType": "info",
                "msg": "派发 change 事件, 当前值 ${event.data.value}",
              }
            }
          ],
        },
      },
    },
  ]
}
```

## 动作表

| 动作名称 | 动作配置  | 说明 | 默认值  |
| ------- | ----------------- | ------- | --------- |
| clear   | -       | 清空   |
| reset   | -       | 将值重置为resetValue，若没有配置resetValue，则清空   |
| setValue| value: string 更新的时间值 | 更新数据，依赖格式format   |

示例

```schema: scope="body"
{
  "type": "form",
  "body": [
    {
      "type": "button",
      "label": "clear",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "clear",
              "componentId": "actionTimeRangePicker",
              "args": {
                "value": null,
              }
            }
          ]
        }
      }
    },
    {
      "type": "button",
      "label": "reset",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "reset",
              "componentId": "actionTimeRangePicker",
              "args": {
                "value": "",
              }
            }
          ]
        }
      }
    },
    {
      "type": "button",
      "label": "setValue",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "setValue",
              "componentId": "actionTimeRangePicker",
              "args": {
                "value": ["12:08:09", "15:09:10"]
              },
            }
          ]
        }
      }
    },
    {
      "type": "ds-time-range-picker",
      "id": "actionTimeRangePicker",
      "label": "动作",
      "value": ["10:01:02", "11:02:03"],
    }
  ]
}
```

