---
title: Form 表单
description:
type: 0
group: ⚙ 组件
menuName: Form 表单
icon:
order: 24
---

表单是 amis 中核心组件之一，主要作用是提交或者展示表单数据。

## 模块划分

![Form容器模块](https://static02.sit.yxmarketing01.com/materialcenter/3b244d04-d597-4912-87c2-10474a876f63.png)

1. 表单项内容区域（必有）
   内部包含了这种类型的表单项，一行能放置几个可以根据实际情况来定。

2. 底部辅助按钮区域（非必有）
   对于表单需要做保存操作，或者搭配页面做全局操作。

## 场景推荐

### 基本模式

```schema
{
  "type": "page",
  "data": {
    "tags": ["执行中", "审核中", "运行中"],
    "table": [
      {
        "a": "a1",
        "b": "b1",
        "c": {
          "name": "c1",
          "id": "C1"
        }
      },
      {
        "a": "a2",
        "b": "b2",
        "c": {
          "name": "c2",
          "id": "C2"
        }
      },
      {
        "a": "a3",
        "b": "b3",
        "c": {
          "name": "c3",
          "id": "C3"
        }
      }
    ],
    "prefix": "近",
    "unit": "day",
    "editor": "function() {\n  console.log(`hello world`)\n}"
  },
  "body": {
    "type": "form",
    "api": "/api/mock2/saveForm?waitSeconds=2",
    "data": {
      "isEditPage": true,
      "input-sub-form": [
        {
          "title": "123",
          "b": "456"
        }
      ]
    },
    "body": [
      {
        "type": "alert",
        "body": "这是一段提示类文案",
        "level": "info",
        "showIcon": true
      },

      {
        "type": "group",
        "body": [
          {
            "type": "select",
            "name": "department",
            "label": "归属部门",
            "description": "所属部门"
          },
          {
            "type": "input-text",
            "name": "platform",
            "label": "Platform",
            "placeholder": "请输入",
            "showCounter": true,
            "maxLength": 20,
            "required": true
          },
          {
            "type": "nested-select",
            "name": "nestedSelect",
            "label": "级联选择器",
            "options": [
              {
                "label": "A",
                "value": "a"
              },
              {
                "label": "B",
                "value": "b",
                "children": [
                  {
                    "label": "B-1",
                    "value": "b-1"
                  },
                  {
                    "label": "B-2",
                    "value": "b-2"
                  },
                  {
                    "label": "B-3",
                    "value": "b-3"
                  }
                ]
              },
              {
                "label": "C",
                "value": "c"
              }
            ],
            "columnRatio": 4
          }
        ]
      },
      {
        "type": "group",
        "body": [
          {
            "type": "input-tag",
            "name": "tag",
            "label": "标签",
            "placeholder": "请选择标签",
            "options": [
              "Aaron Rodgers",
              "Tom Brady",
              "Charlse Woodson",
              "Aaron Jones"
            ]
          },
          {
            "type": "ds-date-range-picker",
            "name": "date",
            "label": "选择日期范围"
          },
          {
            "type": "input-group",
            "label": "账户开立日期",
            "columnRatio": 4,
            "name": "rule1",
            "body": [
              {
                "type": "select",
                "name": "unita1",
                "options": [
                  {
                    "label": "账户开立日期距报告日期",
                    "value": "day"
                  },
                  {
                    "label": "月",
                    "value": "month"
                  },
                  {
                    "label": "年",
                    "value": "year"
                  }
                ],
                "inputClassName": " flex-grow "
              },
              {
                "type": "select",
                "name": "unit1",
                "multiple": true,
                "options": [
                  {
                    "label": "3天",
                    "value": "day"
                  },
                  {
                    "label": "3月",
                    "value": "month"
                  },
                  {
                    "label": "3年",
                    "value": "year"
                  },
                  {
                    "label": "30天",
                    "value": "days"
                  },
                  {
                    "label": "30月",
                    "value": "months"
                  },
                  {
                    "label": "30年",
                    "value": "years"
                  },
                  {
                    "label": "300天",
                    "value": "dayss"
                  },
                  {
                    "label": "300月",
                    "value": "monthss"
                  },
                  {
                    "label": "300年",
                    "value": "yearss"
                  }
                ],
                "maxTagCount": 4,
                "checkAll": true,
                "inputClassName": " flex-grow "
              }
            ]
          }
        ]
      },
      {
        "type": "group",
        "body": [
          {
            "type": "input-group",
            "label": "数据保留规则",
            "prefix": "${prefix}",
            "prefixName": "customPrefix",
            "body": [
              {
                "type": "input-text",
                "label": false,
                "name": "days",
                "placeholder": "请输入"
              },
              {
                "type": "select",
                "name": "unit",
                "options": [
                  {
                    "label": "天",
                    "value": "day"
                  },
                  {
                    "label": "月",
                    "value": "month"
                  },
                  {
                    "label": "年",
                    "value": "year"
                  }
                ]
              }
            ]
          },
          {
            "type": "group",
            "label": "事件来源",
            "body": [
              {
                "type": "flex",
                "gap": true,
                "items": [
                  {
                    "type": "select",
                    "name": "eventSource",
                    "label": false,
                    "options": [
                      {
                        "label": "业务系统",
                        "value": "SYSTEM"
                      },
                      {
                        "label": "特征系统",
                        "value": "FEATURE"
                      },
                      {
                        "label": "北斗系统",
                        "value": "EFUEL"
                      },
                      {
                        "label": "埋点系统",
                        "value": "STATS"
                      }
                    ],
                    "className": " flex-grow"
                  },
                  {
                    "type": "button",
                    "label": "新建",
                    "level": "link",
                    "linkWithoutPadding": true
                  },
                  {
                    "type": "button",
                    "label": "详情",
                    "level": "link",
                    "linkWithoutPadding": true
                  }
                ]
              }
            ]
          },
          {
            "name": "radios",
            "type": "radios",
            "label": "执行时间",
            "columnRatio": 4,
            "options": [
              {
                "value": "b",
                "label": "",
                "extraLabel": {
                  "type": "tooltip-wrapper",
                  "content": "0:00 ～ 12:00",
                  "body": [
                    {
                      "type": "flex",
                      "gap": true,
                      "items": [
                        {
                          "type": "tpl",
                          "tpl": "上午"
                        },
                        {
                          "type": "icon",
                          "icon": "far fa-question-circle"
                        }
                      ]
                    }
                  ]
                }
              },
              {
                "label": "",
                "value": "c",
                "extraLabel": {
                  "type": "tooltip-wrapper",
                  "content": "12:00 ～ 24:00",
                  "body": [
                    {
                      "type": "flex",
                      "gap": true,
                      "items": [
                        {
                          "type": "tpl",
                          "tpl": "下午"
                        },
                        {
                          "type": "icon",
                          "icon": "far fa-question-circle"
                        }
                      ]
                    }
                  ]
                }
              }
            ]
          }
        ]
      },
      {
        "type": "group",
        "body": [
          {
            "name": "checkboxes",
            "type": "checkboxes",
            "label": "适用场景",
            "joinValues": false,
            "columnRatio": 4,
            "options": [
              {
                "label": "H5",
                "value": "a"
              },
              {
                "label": "APP",
                "value": "b"
              },
              {
                "label": "微信小程序",
                "value": "c"
              }
            ]
          },
          {
            "name": "applicable-rules",
            "type": "input-text",
            "label": "适用规则",
            "placeholder": "请选择或输入后回车",
            "columnRatio": 4,
            "options": [
              {
                "label": "张三",
                "value": "张三"
              },
              {
                "label": "李四",
                "value": "李四"
              },
              {
                "label": "王五",
                "value": "王五"
              },
              {
                "label": "小六",
                "value": "小六"
              }
            ]
          },
          {
            "type": "input-formula",
            "name": "formula",
            "label": "公式",
            "evalMode": true,
            "value": "SUM(1 , 2)",
            "variables": [
              {
                "label": "表单字段",
                "children": [
                  {
                    "label": "文章名",
                    "value": "name",
                    "tag": "文本"
                  },
                  {
                    "label": "作者",
                    "value": "author",
                    "tag": "文本"
                  },
                  {
                    "label": "售价",
                    "value": "price",
                    "tag": "数字"
                  },
                  {
                    "label": "出版时间",
                    "value": "time",
                    "tag": "时间"
                  },
                  {
                    "label": "版本号",
                    "value": "version",
                    "tag": "数字"
                  },
                  {
                    "label": "出版社",
                    "value": "publisher",
                    "tag": "文本"
                  }
                ]
              },
              {
                "label": "流程字段",
                "children": [
                  {
                    "label": "联系电话",
                    "value": "telphone"
                  },
                  {
                    "label": "地址",
                    "value": "addr"
                  }
                ]
              },
              {
                "label": "长文本测试分类长文本测试分类长文本测试分类长文本测试分类",
                "children": [
                  {
                    "label": "这是一段测试长文本这是一段测试长文本这是一段测试长文本",
                    "value": "longtext",
                    "tag": "文本"
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        "type": "group",
        "body": [
          {
            "type": "group",
            "label": "方案配置",
            "gap": "xs",
            "body": [
              {
                "type": "select",
                "name": "unita3",
                "label": false,
                "value": "day",
                "options": [
                  {
                    "label": "小时",
                    "value": "day"
                  },
                  {
                    "label": "分钟",
                    "value": "month"
                  }
                ],
                "columnRatio": 4
              },
              {
                "type": "select",
                "name": "unit4",
                "label": false,
                "value": "day",
                "options": [
                  {
                    "label": "整点",
                    "value": "day"
                  },
                  {
                    "label": "自定义",
                    "value": "month"
                  }
                ],
                "columnRatio": 4,
                "visibleOn": "${unita3 === 'day'}"
              },
              {
                "type": "ds-time-range-picker",
                "name": "time2",
                "label": false,
                "columnRatio": 4,
                "visibleOn": "${unit4 === 'day'}"
              },
              {
                "type": "input-text",
                "name": "time3",
                "placeholder": "请输入",
                "label": false,
                "columnRatio": 4,
                "visibleOn": "${unit4 === 'month'}"
              }
            ]
          }
        ]
      },
      {
        "type": "group",
        "body": [
          {
            "type": "between",
            "label": "放款范围",
            "separatorStr": "~",
            "labelRemark": {
              "type": "remark",
              "content": "这是一个提示"
            },
            "description": "放款范围规则为左闭右开",
            "items": [
              {
                "type": "input-group",
                "suffix": "元",
                "body": [
                  {
                    "type": "input-text",
                    "name": "groupfix",
                    "placeholder": "请输入",
                    "addonAfterStr": "元"
                  }
                ]
              },
              {
                "type": "input-group",
                "suffix": "元",
                "body": [
                  {
                    "type": "input-text",
                    "name": "groupfix2",
                    "placeholder": "请输入",
                    "addonAfterStr": "元"
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        "type": "group",
        "body": [
          {
            "required": true,
            "type": "group",
            "gap": "xs",
            "label": "配置多项必填",
            "body": [
              {
                "required": true,
                "type": "input-text",
                "name": "text1",
                "label": false,
                "placeholder": "请输入"
              },
              {
                "required": true,
                "type": "input-text",
                "name": "text2",
                "label": false,
                "placeholder": "请输入"
              },
              {
                "required": true,
                "label": false,
                "type": "select",
                "name": "select",
                "options": [
                  {
                    "label": "A",
                    "value": "a"
                  },
                  {
                    "label": "B",
                    "value": "b"
                  },
                  {
                    "label": "C",
                    "value": "c"
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        "type": "group",
        "body": [
          {
            "type": "input-sub-form",
            "name": "input-sub-form",
            "label": "子表单配置",
            "multiple": true,
            "columnRatio": 12,
            "btnLabel": "设置${title}",
            "addableOn": "${isEditPage}",
            "removableOn": "${isEditPage}",
            "form": {
              "title": "配置子表单",
              "showCloseButton": false,
              "body": [
                {
                  "name": "title",
                  "label": "标题",
                  "required": true,
                  "type": "input-text"
                },
                {
                  "name": "b",
                  "label": "其他",
                  "type": "input-text"
                }
              ]
            }
          }
        ]
      },
      {
        "type": "group",
        "body": [
          {
            "type": "input-array",
            "name": "array",
            "label": "邮箱集合",
            "value": [],
            "inline": true,
            "items": {
              "type": "input-email"
            },
            "columnRatio": 8
          }
        ]
      },
      {
        "type": "group",
        "body": [
          {
            "name": "serviceTime1",
            "type": "radios",
            "label": "业务时间",
            "columnRatio": 4,
            "selectFirst": true,
            "options": [
              {
                "label": "连续时间",
                "value": "continuum"
              },
              {
                "label": "自定义",
                "value": "custom"
              }
            ]
          }
        ]
      },
      {
        "type": "group",
        "body": [
          {
            "type": "static-tags",
            "label": "流程中",
            "source": "${tags}",
            "items": [
              {
                "type": "tag",
                "label": "${item}",
                "displayMode": "bordered",
                "color": "active"
              }
            ]
          }
        ]
      },
      {
        "type": "group",
        "body": [
          {
            "type": "ds-date-range-picker",
            "name": "continuum1",
            "label": "连续时间",
            "columnRatio": 12,
            "visibleOn": "${serviceTime1 === `continuum`}"
          },
          {
            "type": "textarea",
            "name": "text1",
            "label": "文本",
            "columnRatio": 12,
            "showCounter": true,
            "maxLength": 30,
            "placeholder": "请输入",
            "trimContents": true,
            "visibleOn": "${serviceTime1 === `custom`}"
          }
        ]
      },
      {
        "type": "group",
        "body": [
          {
            "type": "button-group-select",
            "label": "所在地区",
            "name": "type",
            "options": [
              {
                "label": "杭州",
                "value": "Hangzhou"
              },
              {
                "label": "上海",
                "value": "Shanghai"
              },
              {
                "label": "北京",
                "value": "Beijing"
              },
              {
                "label": "成都",
                "value": "Chengdu"
              }
            ]
          }
        ]
      },
      {
        "type": "group",
        "direction": "vertical",
        "label": "处理时间",
        "body": [
          {
            "name": "timeRadios",
            "type": "radios",
            "label": false,
            "selectFirst": true,
            "options": [
              {
                "label": "连续时间",
                "value": "continuum"
              },
              {
                "label": "自定义",
                "value": "custom"
              }
            ]
          },
          {
            "label": false,
            "type": "ds-date-range-picker",
            "name": "continuum2",
            "visibleOn": "${timeRadios === `continuum`}"
          },
          {
            "label": false,
            "type": "textarea",
            "name": "text2",
            "showCounter": true,
            "maxLength": 30,
            "placeholder": "请输入",
            "trimContents": true,
            "visibleOn": "${timeRadios === `custom`}"
          }
        ]
      },
      {
        "type": "group",
        "body": [
          {
            "type": "input-file",
            "name": "file",
            "label": "素材上传",
            "multiple": true,
            "maxSize": 10485760,
            "description": "支持上传格式PNG、JPG、PDF",
            "accept": ".jpg,.png,.pdf",
            "listType": "preview",
            "receiver": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/upload/file"
          }
        ]
      },
      {
        "type": "group",
        "body": [
          {
            "type": "input-image",
            "name": "image",
            "label": "上传图片",
            "multiple": true,
            "receiver": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/upload/file"
          }
        ]
      },
      {
        "type": "group",
        "body": [
          {
            "type": "json-schema-editor",
            "name": "jsonSchema",
            "label": "字段类型"
          }
        ]
      },
      {
        "type": "combo",
        "name": "comboAndDialog",
        "label": "组合多条多行",
        "multiple": true,
        "multiLine": true,
        "value": [{}],
        "tabsMode": true,
        "tabsStyle": "card",
        "maxLength": 3,
        "showErrorMsg": false,
        "items": [
          {
            "name": "a",
            "label": "文本",
            "type": "input-text",
            "placeholder": "文本",
            "value": "",
            "size": "full",
            "required": true
          },
          {
            "name": "b",
            "label": "选项",
            "type": "select",
            "options": ["a", "b", "c"],
            "size": "full"
          }
        ],
        "addFormDialog": {
          "title": "新增",
          "body": [
            {
              "name": "a",
              "label": "文本",
              "type": "input-text"
            }
          ]
        }
      },
      {
        "type": "group",
        "label": "高级配置",
        "body": [
          {
            "type": "fieldSet",
            "titleAlign": "right",
            "title": "展开",
            "collapsable": true,
            "collapsed": true,
            "size": "base",
            "collapseTitle": "收起",
            "body": [
              {
                "type": "alert",
                "title": "提示类标题",
                "body": "提示类文案",
                "level": "info",
                "showIcon": true
              },
              {
                "type": "group",
                "body": [
                  {
                    "type": "group",
                    "name": "editor",
                    "label": "Flink任务配置",
                    "required": true,
                    "direction": "vertical",
                    "body": [
                      {
                        "type": "button",
                        "label": "Flink任务配置说明",
                        "level": "link",
                        "linkWithoutPadding": true
                      },
                      {
                        "type": "editor",
                        "name": "editor",
                        "label": false,
                        "required": true,
                        "placeholder": "请输入",
                        "toolbar": [
                          {
                            "type": "button",
                            "level": "link",
                            "label": "导出",
                            "actionType": "dialog",
                            "dialog": {
                              "title": "系统提示",
                              "body": "导出成功"
                            }
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      },
      {
        "type": "group",
        "body": [
          {
            "type": "input-rich-text",
            "name": "tip",
            "label": "底部提示",
            "placeholder": "请输入"
          }
        ]
      },
      {
        "type": "group",
        "name": "second5",
        "label": "特征",
        "direction": "vertical",
        "body": [
          {
            "type": "alert",
            "title": "警告类标题",
            "body": "警告类文案",
            "level": "warning",
            "showIcon": true
          },
          {
            "type": "editor",
            "name": "editor",
            "label": false,
            "required": true,
            "editorTheme": "vs-dark",
            "placeholder": "请输入",
            "allowFullscreen": false,
            "toolbar": [
              {
                "type": "button",
                "level": "link",
                "label": "格式化",
                "actionType": "dialog",
                "dialog": {
                  "title": "系统提示",
                  "body": "格式化成功"
                }
              },
              {
                "type": "button",
                "level": "link",
                "label": "试跑",
                "actionType": "dialog",
                "dialog": {
                  "title": "系统提示",
                  "body": "试跑成功"
                }
              }
            ]
          }
        ]
      },
      {
        "type": "group",
        "label": "表格项",
        "direction": "vertical",
        "body": [
          {
            "type": "flex",
            "direction": "column",
            "gap": true,
            "items": [
              {
                "type": "button",
                "label": "更新数据",
                "primary": true,
                "actionType":"dialog",
                "dialog": {
                  "title":"标题",
                  "body":"这是内容区域"
                }
              },
              {
                "type": "input-table",
                "name": "table",
                "label": false,
                "addable": true,
                "editable": true,
                "columns": [
                  {
                    "label": "名称",
                    "name": "a",
                    "required": true,
                    "quickEdit": {
                      "type": "select",
                      "clearable": true,
                      "required": true,
                      "options": ["a1", "a2", "a3"]
                    }
                  },
                  {
                    "label": "B",
                    "name": "b",
                    "quickEdit": true
                  },
                  {
                    "label": "C",
                    "name": "c.name",
                    "quickEdit": {
                      "type": "select",
                      "name": "c",
                      "labelField": "name",
                      "valueField": "id",
                      "joinValues": false,
                      "options": [
                        {
                          "name": "c1",
                          "id": "C1"
                        },
                        {
                          "name": "c2",
                          "id": "C2"
                        },
                        {
                          "name": "c3",
                          "id": "C3"
                        }
                      ]
                    }
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
}
```

<!--
### 垂直模式

文字与表单项分行显示。

```schema
{
  "type": "page",
  "body": {
     "type": "form",
    "api": "/api/mock2/saveForm?waitSeconds=2",
    "mode": "vertical",
    "body": [
      {
        "type": "select",
        "name": "department",
        "label": "归属部门"
      },
      {
        "type": "input-text",
        "name": "platform",
        "label": "Platform",
        "placeholder": "请输入",
        "showCounter": true,
        "maxLength": 20
      },
      {
        "type": "input-text",
        "name": "css",
        "label": "CSS",
        "required": true,
        "placeholder": "请输入"
      },
      {
        "type": "checkboxes",
        "name": "bankList",
        "label": "选择银行",
        "columnsCount": 5,
        "columnRatio": 12,
        "labelField": "bankName",
        "valueField": "bankCode",
        "options": [
          {
            "bankCode": "4017",
            "bankName": "广发银行",
            "bankNmCode": "广发银行_4017",
            "cardType": "DEBIT"
          },
          {
            "bankCode": "4001",
            "bankName": "农业银行",
            "bankNmCode": "农业银行_4001",
            "cardType": "DEBIT"
          },
          {
            "bankCode": "4009",
            "bankName": "中国银行",
            "bankNmCode": "中国银行_4009",
            "cardType": "DEBIT"
          },
          {
            "bankCode": "4045",
            "bankName": "北京银行",
            "bankNmCode": "北京银行_4045",
            "cardType": "DEBIT"
          },
          {
            "bankCode": "4004",
            "bankName": "招商银行",
            "bankNmCode": "招商银行_4004",
            "cardType": "DEBIT"
          },
          {
            "bankCode": "4005",
            "bankName": "邮储银行",
            "bankNmCode": "邮储银行_4005",
            "cardType": "DEBIT"
          },
          {
            "bankCode": "4002",
            "bankName": "工商银行",
            "bankNmCode": "工商银行_4002",
            "cardType": "DEBIT"
          },
          {
            "bankCode": "4008",
            "bankName": "平安银行",
            "bankNmCode": "平安银行_4008",
            "cardType": "DEBIT"
          },
          {
            "bankCode": "4006",
            "bankName": "民生银行",
            "bankNmCode": "民生银行_4006",
            "cardType": "DEBIT"
          },
          {
            "bankCode": "4003",
            "bankName": "建设银行",
            "bankNmCode": "建设银行_4003",
            "cardType": "DEBIT"
          },
          {
            "bankCode": "4043",
            "bankName": "上海银行",
            "bankNmCode": "上海银行_4043",
            "cardType": "DEBIT"
          }
        ]
      }
    ]
  })
})
``` -->

### 静态表单带全局操作按钮

```schema
{
  "type": "page",
  "data": {
    "department": "department",
    "platform": "platform",
    "css": "css",
    "browser": "browser",
    "selected": "a",
    "department1": "department1",
    "platform1": "platform1",
    "css1": "css1",
    "browser1": "browser1",
    "selected1": "b",
    "remark": "备注",
    "remark1": "备注"
  },
  "body": [
    {
      "type": "title",
      "iconConfig": true,
      "title": "刘亦菲",
      "assistContent": [
        {
          "type": "flex",
          "gap": true,
          "items": [
            {
              "type": "tags",
              "items": [
                {
                  "type": "tag",
                  "label": "有提额资格",
                  "displayMode": "bordered",
                  "color": "active"
                },
                {
                  "type": "tag",
                  "label": "随借随还",
                  "displayMode": "bordered",
                  "color": "error"
                },
                {
                  "type": "tag",
                  "label": "安全资产",
                  "displayMode": "bordered",
                  "color": "success"
                },
              ]
            },
            {
              "type": "form",
              "mode": "inline",
              "body": [
                {
                  "type": "input-rating",
                  "name": "rating",
                  "count": 5,
                  "value": 3.5,
                  "half": true
                }
              ],
              "actions": []
            }
          ]
        }
      ],
      "actions": [
        {
          "type": "button",
          "label": "刷新",
        },
        {
          "type": "button",
          "level": "primary",
          "label": "下一个",
        },
      ],
    },
    {
      "type": "form",
      "title": "",
      "api": "/api/mock2/saveForm?waitSeconds=2",
      "id": "myForm",
      "actions": [],
      "static": true,
      "body": [
        {
          "type": "flex",
          "justify": "space-between",
          "alignItems": "start",
          "items": [
            {
              "type": "button-toolbar",
              "maxCount": 4,
              "buttons": [
                {
                  "type": "button",
                  "label": "调价调额",
                  "level": "primary"
                },
                {
                  "type": "button",
                  "label": "在线试算"
                }
              ]
            },
            {
              "type": "flex",
              "gap": true,
              "items": [
                {
                  "type": "icon",
                  "icon": "picture",
                  "highLight": true,
                  "size": "md"
                },
                {
                  "type": "icon",
                  "icon": "star-regular",
                  "size": "md"
                }
              ]
            }
          ]
        },
        {
          "type": "group",
          "body": [
            {
              "type": "select",
              "name": "department",
              "label": "归属部门"
            },
            {
              "type": "input-text",
              "name": "platform",
              "label": "Platform",
              "placeholder": "请输入"
            },
            {
              "type": "input-text",
              "name": "css",
              "label": "CSS",
              "required": true,
              "placeholder": "请输入"
            }
          ]
        },
        {
          "type": "group",
          "body": [
            {
              "type": "input-text",
              "name": "browser",
              "label": "Browser",
              "placeholder": "请输入"
            },
            {
              "type": "select",
              "name": "selected",
              "label": "用户选择",
              "placeholder": "请选择",
              "options": [
                {
                  "label": "a",
                  "value": "a"
                },
                {
                  "label": "b",
                  "value": "b"
                }
              ]
            },
            {
              "type": "input-text",
              "name": "browser",
              "label": "Browser",
              "placeholder": "请输入"
            }
          ]
        },
        {
          "type": "group",
          "body": [
            {
              "type": "select",
              "name": "department1",
              "label": "归属部门1"
            },
            {
              "type": "input-text",
              "name": "platform1",
              "label": "Platform1",
              "placeholder": "请输入"
            },
            {
              "type": "input-text",
              "name": "css1",
              "label": "CSS1",
              "required": true,
              "placeholder": "请输入"
            }
          ]
        },
        {
          "type": "group",
          "body": [
            {
              "type": "input-text",
              "name": "browser1",
              "label": "Browser1",
              "placeholder": "请输入"
            },
            {
              "type": "select",
              "name": "selected1",
              "label": "用户选择1",
              "placeholder": "请选择",
              "options": [
                {
                  "label": "a",
                  "value": "a"
                },
                {
                  "label": "b",
                  "value": "b"
                }
              ]
            },
            {
              "type": "input-text",
              "name": "browser1",
              "label": "Browser1",
              "placeholder": "请输入"
            }
          ]
        },
        {
          "type": "group",
          "body": [
            {
              "type": "textarea",
              "name": "remark",
              "label": "备注",
              "showCounter": true,
              "maxLength": 30,
              "placeholder": "请输入",
              "trimContents": true
            }
          ]
        }
      ]
    }
  ]
}
```

### 底部带分割线

```schema
{
  "type": "page",
  "body": {
    "type": "tabs",
    "tabs": [{
      "title": "Tab标题",
      "tab": {
        "type": "form",
        "api": "/api/mock2/saveForm?waitSeconds=2",
        "footerBorder": true,
        "body": [
          {
            "type": "checkboxes",
            "name": "bankList",
            "label": "选择银行",
            "columnsCount": 5,
            "columnRatio": 12,
            "labelField": "bankName",
            "valueField": "bankCode",
            "options": [
              {
                "bankCode": "4017",
                "bankName": "广发银行",
                "bankNmCode": "广发银行_4017",
                "cardType": "DEBIT"
              },
              {
                "bankCode": "4001",
                "bankName": "农业银行",
                "bankNmCode": "农业银行_4001",
                "cardType": "DEBIT"
              },
              {
                "bankCode": "4009",
                "bankName": "中国银行",
                "bankNmCode": "中国银行_4009",
                "cardType": "DEBIT"
              },
              {
                "bankCode": "4045",
                "bankName": "北京银行",
                "bankNmCode": "北京银行_4045",
                "cardType": "DEBIT"
              },
              {
                "bankCode": "4004",
                "bankName": "招商银行",
                "bankNmCode": "招商银行_4004",
                "cardType": "DEBIT"
              },
              {
                "bankCode": "4005",
                "bankName": "邮储银行",
                "bankNmCode": "邮储银行_4005",
                "cardType": "DEBIT"
              },
            ]
          },
          {
            "type": "group",
            "body": [
              {
                "type": "checkboxes",
                "name": "bankList",
                "label": "选择银行",
                "columnsCount": 5,
                "columnRatio": 12,
                "labelField": "bankName",
                "valueField": "bankCode",
                "options": [
                  {
                    "bankCode": "4017",
                    "bankName": "广发银行",
                    "bankNmCode": "广发银行_4017",
                    "cardType": "DEBIT"
                  },
                  {
                    "bankCode": "4001",
                    "bankName": "农业银行",
                    "bankNmCode": "农业银行_4001",
                    "cardType": "DEBIT"
                  },
                  {
                    "bankCode": "4009",
                    "bankName": "中国银行",
                    "bankNmCode": "中国银行_4009",
                    "cardType": "DEBIT"
                  },
                  {
                    "bankCode": "4045",
                    "bankName": "北京银行",
                    "bankNmCode": "北京银行_4045",
                    "cardType": "DEBIT"
                  },
                  {
                    "bankCode": "4004",
                    "bankName": "招商银行",
                    "bankNmCode": "招商银行_4004",
                    "cardType": "DEBIT"
                  },
                  {
                    "bankCode": "4005",
                    "bankName": "邮储银行",
                    "bankNmCode": "邮储银行_4005",
                    "cardType": "DEBIT"
                  },
                  {
                    "bankCode": "4002",
                    "bankName": "工商银行",
                    "bankNmCode": "工商银行_4002",
                    "cardType": "DEBIT"
                  },
                  {
                    "bankCode": "4008",
                    "bankName": "平安银行",
                    "bankNmCode": "平安银行_4008",
                    "cardType": "DEBIT"
                  },
                  {
                    "bankCode": "4006",
                    "bankName": "民生银行",
                    "bankNmCode": "民生银行_4006",
                    "cardType": "DEBIT"
                  },
                  {
                    "bankCode": "4003",
                    "bankName": "建设银行",
                    "bankNmCode": "建设银行_4003",
                    "cardType": "DEBIT"
                  },
                  {
                    "bankCode": "4043",
                    "bankName": "上海银行",
                    "bankNmCode": "上海银行_4043",
                    "cardType": "DEBIT"
                  }
                ]
              }
            ]
          }
        ]
      }
    }]
  }
}
```

### 表单项带操作按钮

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/saveForm?waitSeconds=2",
    "data": {
      "platform": "text1",
      "css": "text2",
      "bankList": [
        "4017"
      ]
    },
    "labelWidth": 70,
    "body": [
      {
        "type": "group",
        "body": [
          {
            "type": "flex",
            "gap": true,
            "items": [
              {
                "type": "select",
                "name": "eventSource",
                "label": "账务系统",
                "options": [
                  {
                    "label": "业务系统",
                    "value": "SYSTEM"
                  },
                  {
                    "label": "特征系统",
                    "value": "FEATURE"
                  },
                  {
                    "label": "北斗系统",
                    "value": "EFUEL"
                  },
                  {
                    "label": "埋点系统",
                    "value": "STATS"
                  }
                ],
                "className": " flex-grow"
              },
              {
                "type": "button",
                "label": "新建",
                "level": "link",
                "linkWithoutPadding": true
              },
              {
                "type": "button",
                "label": "详情",
                "level": "link",
                "linkWithoutPadding": true
              }
            ]
          },
          {
            "type": "input-text",
            "name": "platform",
            "label": "Platform",
            "placeholder": "请输入",
            "showCounter": true,
            "maxLength": 20
          },
          {
            "type": "input-text",
            "name": "css",
            "label": "CSS",
            "required": true,
            "placeholder": "请输入"
          },
        ]
      },
      {
        "type": "group",
        "body": [
          {
            "type": "checkboxes",
            "name": "bankList",
            "label": "选择银行",
            "columnsCount": 5,
            "columnRatio": 12,
            "labelField": "bankName",
            "valueField": "bankCode",
            "options": [
              {
                "bankCode": "4017",
                "bankName": "广发银行",
                "bankNmCode": "广发银行_4017",
                "cardType": "DEBIT"
              },
              {
                "bankCode": "4001",
                "bankName": "农业银行",
                "bankNmCode": "农业银行_4001",
                "cardType": "DEBIT"
              },
              {
                "bankCode": "4009",
                "bankName": "中国银行",
                "bankNmCode": "中国银行_4009",
                "cardType": "DEBIT"
              },
              {
                "bankCode": "4045",
                "bankName": "北京银行",
                "bankNmCode": "北京银行_4045",
                "cardType": "DEBIT"
              },
              {
                "bankCode": "4004",
                "bankName": "招商银行",
                "bankNmCode": "招商银行_4004",
                "cardType": "DEBIT"
              },
              {
                "bankCode": "4005",
                "bankName": "邮储银行",
                "bankNmCode": "邮储银行_4005",
                "cardType": "DEBIT"
              },
              {
                "bankCode": "4002",
                "bankName": "工商银行",
                "bankNmCode": "工商银行_4002",
                "cardType": "DEBIT"
              },
              {
                "bankCode": "4008",
                "bankName": "平安银行",
                "bankNmCode": "平安银行_4008",
                "cardType": "DEBIT"
              },
              {
                "bankCode": "4006",
                "bankName": "民生银行",
                "bankNmCode": "民生银行_4006",
                "cardType": "DEBIT"
              },
              {
                "bankCode": "4003",
                "bankName": "建设银行",
                "bankNmCode": "建设银行_4003",
                "cardType": "DEBIT"
              },
              {
                "bankCode": "4043",
                "bankName": "上海银行",
                "bankNmCode": "上海银行_4043",
                "cardType": "DEBIT"
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### 详情模式

```schema
{
  "type": "page",
  "body": [
    {
      "type": "title",
      "iconConfig": true,
      "title": "页面大标题名称大标题名称",
      "subTitle": "我是小标题",
      "assistContent": [
        {
          "type": "tag",
          "label": "普通标签",
          "color": "processing"
        }
      ],
      "actions": [
        {
          "type": "button",
          "label": "取消",
          "onEvent": {
            "click": {
              "actions": [
                {
                  "actionType": "toast",
                  "args": {
                    "msgType": "info",
                    "msg": "响应取消操作"
                  }
                }
              ]
            }
          }
        },
        {
          "type": "button",
          "level": "primary",
          "label": "保存",
          "onEvent": {
            "click": {
              "actions": [
                {
                  "componentId": "myForm",
                  "actionType": "submit"
                }
              ]
            }
          }
        }
      ]
    },
    {
      "type": "form",
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
      "static": true,
      "labelWidth": 60,
      "data": {
        "platform": "text1",
        "css": "text2",
        "bankList": [
          "4017"
        ]
      },
      "body": [
        {
          "type": "alert",
          "body": "提示类文案",
          "level": "info",
          "showIcon": true
        },
        {
          "type": "group",
          "body": [
            {
              "type": "select",
              "name": "department",
              "label": "归属部门",
              "options": [
                "技术部",
                "总经理"
              ]
            },
            {
              "type": "input-text",
              "name": "platform",
              "label": "Platform",
              "placeholder": "请输入",
              "showCounter": true,
              "maxLength": 20
            },
            {
              "type": "input-text",
              "name": "css",
              "label": "CSS",
              "required": true,
              "placeholder": "请输入"
            },
          ]
        },
        {
          "type": "group",
          "body": [
            {
              "type": "checkboxes",
              "name": "bankList",
              "label": "选择银行",
              "columnsCount": 5,
              "columnRatio": 12,
              "labelField": "bankName",
              "valueField": "bankCode",
              "options": [
                {
                  "bankCode": "4017",
                  "bankName": "广发银行",
                  "bankNmCode": "广发银行_4017",
                  "cardType": "DEBIT"
                },
                {
                  "bankCode": "4001",
                  "bankName": "农业银行",
                  "bankNmCode": "农业银行_4001",
                  "cardType": "DEBIT"
                },
                {
                  "bankCode": "4009",
                  "bankName": "中国银行",
                  "bankNmCode": "中国银行_4009",
                  "cardType": "DEBIT"
                },
                {
                  "bankCode": "4045",
                  "bankName": "北京银行",
                  "bankNmCode": "北京银行_4045",
                  "cardType": "DEBIT"
                },
                {
                  "bankCode": "4004",
                  "bankName": "招商银行",
                  "bankNmCode": "招商银行_4004",
                  "cardType": "DEBIT"
                },
                {
                  "bankCode": "4005",
                  "bankName": "邮储银行",
                  "bankNmCode": "邮储银行_4005",
                  "cardType": "DEBIT"
                },
                {
                  "bankCode": "4002",
                  "bankName": "工商银行",
                  "bankNmCode": "工商银行_4002",
                  "cardType": "DEBIT"
                },
                {
                  "bankCode": "4008",
                  "bankName": "平安银行",
                  "bankNmCode": "平安银行_4008",
                  "cardType": "DEBIT"
                },
                {
                  "bankCode": "4006",
                  "bankName": "民生银行",
                  "bankNmCode": "民生银行_4006",
                  "cardType": "DEBIT"
                },
                {
                  "bankCode": "4003",
                  "bankName": "建设银行",
                  "bankNmCode": "建设银行_4003",
                  "cardType": "DEBIT"
                },
                {
                  "bankCode": "4043",
                  "bankName": "上海银行",
                  "bankNmCode": "上海银行_4043",
                  "cardType": "DEBIT"
                }
              ]
            }
          ]
        },
        {
          "type": "tabs",
          "tabs": [
            {
              "title": "带全局操作按钮的列表",
              "tab": [
                {
                  "type": "crud",
                  "syncLocation": false,
                  "columnsTogglable": false,
                  "footerToolbar": [
                    {
                      "type": "pagination",
                      "layout": "total,pager,perPage,go"
                    }
                  ],
                  "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4",
                  "columns": [
                    {
                      "name": "id",
                      "label": "ID"
                    },
                    {
                      "name": "engine",
                      "label": "Rendering engine"
                    },
                    {
                      "name": "browser",
                      "label": "Browser"
                    },
                    {
                      "name": "platform",
                      "label": "Platform(s)"
                    },
                    {
                      "name": "version",
                      "label": "Engine version"
                    },
                    {
                      "name": "grade",
                      "label": "CSS grade"
                    },
                    {
                      "type": "operation",
                      "label": "操作",
                      "buttons": [
                        {
                          "label": "详情",
                          "type": "button",
                          "level": "link",
                          "actionType": "dialog",
                          "dialog": {
                            "title": "详情",
                            "showCloseButton": false,
                            "body": "这是个简单的弹框。"
                          }
                        },
                        {
                          "label": "删除",
                          "type": "button",
                          "actionType": "ajax",
                          "level": "link",
                          "disabled": true,
                          "confirmText": "确认要删除吗？",
                          "api": {
                            "method": "delete",
                            "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
                          }
                        },
                        {
                          "label": "编辑",
                          "type": "button",
                          "level": "link",
                          "actionType": "dialog",
                          "dialog": {
                            "title": "编辑",
                            "showCloseButton": false,
                            "body": "这是个简单的弹框。"
                          }
                        },
                        {
                          "label": "空跑",
                          "type": "button",
                          "level": "link",
                          "actionType": "dialog",
                          "dialog": {
                            "title": "空跑",
                            "showCloseButton": false,
                            "body": "这是个简单的弹框。"
                          }
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

### 详情模式带快速编辑或操作按钮

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/saveForm?waitSeconds=2",
    "static": true,
    "id":"static_form_id",
    "actions": [],
    "data": {
      "platform": "text1",
      "css": "text2",
      "bankList": ["4017"],
      "staticText":"100"
    },
    "body": [
      {
        "type": "group",
        "body": [
          {
            "type": "select",
            "name": "department",
            "label": "归属部门"
          },
          {
            "type": "static",
            "name": "platform",
            "label": "Platform",
            "placeholder": "请输入",
            "showCounter": true,
            "maxLength": 20,
            "quickEdit": {
              "type": "input-text",
              "saveImmediately": true
            }
          },
          {
            "type": "input-text",
            "name": "css",
            "label": "CSS",
            "required": true,
            "placeholder": "请输入"
          },
        ]
      },
       {
        "type": "group",
        "body": [
          {
            "type": "flex",
            "gap": true,
            "items": [
              {
                "type": "input-text",
                "name": "staticText",
                "label": "付款金额",
              },
              {
                "type": "button",
                "icon": "arrow-rotate",
                "level": "link",
                "linkWithoutPadding": true,
                "onEvent": {
                  "click": {
                    "actions": [
                      {
                        "actionType": "setValue",
                        "componentId": "static_form_id",
                        "args": {
                          "value": {
                            "staticText": "2000元"
                          }
                        }
                      }
                    ]
                  }
                }
              }
            ]
          },
          {
            "type": "input-text",
            "name": "platform",
            "label": "Platform",
            "placeholder": "请输入",
            "showCounter": true,
            "maxLength": 20
          },
          {
            "type": "input-text",
            "name": "css",
            "label": "CSS",
            "required": true,
            "placeholder": "请输入"
          },
        ]
      },
      {
        "type": "group",
        "body": [
          {
            "type": "checkboxes",
            "name": "bankList",
            "label": "选择银行",
            "columnsCount": 5,
            "columnRatio": 12,
            "labelField": "bankName",
            "valueField": "bankCode",
            "options": [
              {
                "bankCode": "4017",
                "bankName": "广发银行",
                "bankNmCode": "广发银行_4017",
                "cardType": "DEBIT"
              },
              {
                "bankCode": "4001",
                "bankName": "农业银行",
                "bankNmCode": "农业银行_4001",
                "cardType": "DEBIT"
              },
              {
                "bankCode": "4009",
                "bankName": "中国银行",
                "bankNmCode": "中国银行_4009",
                "cardType": "DEBIT"
              },
              {
                "bankCode": "4045",
                "bankName": "北京银行",
                "bankNmCode": "北京银行_4045",
                "cardType": "DEBIT"
              },
              {
                "bankCode": "4004",
                "bankName": "招商银行",
                "bankNmCode": "招商银行_4004",
                "cardType": "DEBIT"
              },
              {
                "bankCode": "4005",
                "bankName": "邮储银行",
                "bankNmCode": "邮储银行_4005",
                "cardType": "DEBIT"
              },
              {
                "bankCode": "4002",
                "bankName": "工商银行",
                "bankNmCode": "工商银行_4002",
                "cardType": "DEBIT"
              },
              {
                "bankCode": "4008",
                "bankName": "平安银行",
                "bankNmCode": "平安银行_4008",
                "cardType": "DEBIT"
              },
              {
                "bankCode": "4006",
                "bankName": "民生银行",
                "bankNmCode": "民生银行_4006",
                "cardType": "DEBIT"
              },
              {
                "bankCode": "4003",
                "bankName": "建设银行",
                "bankNmCode": "建设银行_4003",
                "cardType": "DEBIT"
              },
              {
                "bankCode": "4043",
                "bankName": "上海银行",
                "bankNmCode": "上海银行_4043",
                "cardType": "DEBIT"
              }
            ]
          }
        ]
      }
    ]
  }
}
```
- 落地案例
 [特征一站式-样本-样本详情](http://moka.dmz.sit.caijj.net/featurestoreui/#/sampleManager/detail/spl.inc.usr.fsdfsadfsad?activeTab=tab1)
  ![特征一站式-样本-样本详情](https://static02.sit.yxmarketing01.com/materialcenter/c4eaa255-cfde-427b-b554-006856f340c8.jpg)

### 紧凑模式

```schema
{
  "type": "page",
  "data": {
    "text1": "营销中心",
    "text2": 2,
    "text3": **********,
    "text4": "负责人",
    "text5": "text5",
    "text6": **********,
    "text7": "创建人",
    "text8": "text8",
    "department": "营销中心",
    "platform": 2,
    "css": **********,
    "browser": "负责人",
    "selected": "text5",
    "browser2": **********,
    "remark": "创建人",
    "tip": "text8",
    "text9": "text9",
  },
  "body": [
    {
      "type": "form",
      "static": true,
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
      "labelWidth": 60,
      "body": [
        {
          "type": "group",
          "withoutMarginBottom": true,
          "body": [
            {
              "type": "select",
              "name": "department",
              "label": "归属部门",
            },
            {
              "type": "input-text",
              "name": "platform",
              "label": "Platform",
              "placeholder": "请输入",
            },
            {
              "type": "input-text",
              "name": "css",
              "label": "CSS",
              "placeholder": "请输入",
            },
          ]
        },
        {
          "type": "group",
          "withoutMarginBottom": true,
          "body": [
            {
              "type": "input-text",
              "name": "browser",
              "label": "Browser",
              "placeholder": "请输入",
            },
            {
              "type": "select",
              "name": "selected",
              "label": "用户选择",
              "placeholder": "请选择",
              "options": [
                {
                  "label": "a",
                  "value": "a"
                },
                {
                  "label": "b",
                  "value": "b"
                }
              ]
            },
            {
              "type": "input-text",
              "name": "browser2",
              "label": "Browser",
              "placeholder": "请输入",
            },
          ]
        },
        {
          "type": "group",
          "withoutMarginBottom": true,
          "body": [
            {
              "type": "textarea",
              "name": "remark",
              "label": "备注",
              "showCounter": true,
              "maxLength": 30,
              "placeholder": "请输入",
              "trimContents": true
            },
          ]
        },
      ]
    }
  ]
}
```

### 查询表单

配置 `queryForm:true` 展示为搜索表单样式，底部会默认增加 查询、重置 按钮。如果需要自定义按钮时，可配置 `form.actions` 覆盖。联动功能需要搭配 `form.id`,`form.target` 配置一起使用。

```schema
{
  "type": "page",
  "data": {
    "downloads": [
      {
        "name": "测试环境堡垒机操作手册测试环境堡垒机操作手册测试环境堡垒机操作手册测试环境堡垒机操作手册测试环境堡垒机操作手册测试环境堡垒机操作手册测试环境堡垒机操作手册测试环境堡垒机操作手册测试环境堡垒机操作手册.pdf",
        "link": "https://www.baidu.com"
      },
      {
        "name": "跳板机操作手册.pdf",
        "link": "https://www.baidu.com"
      },
      {
        "name": "运维日常问题排查手册.pdf",
        "link": "https://www.baidu.com"
      }
    ]
  },
  "body": [
    {
      "type": "form",
      "queryForm": true,
      "target": "targetFormId",
      "id": "sourceFormId",
      "labelWidth": 65,
      "body": [
        {
          "type": "group",
          "body": [
            {
              "type": "input-text",
              "name": "keywords",
              "label": "关键字",
              "clearable": true,
              "placeholder": "通过关键字搜索",
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "engine",
              "label": "Engine",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "platform",
              "label": "Platform",
              "clearable": true,
              "columnRatio": 4
            }
          ]
        },
        {
          "type": "group",
          "body": [
            {
              "type": "input-text",
              "name": "keywords1",
              "label": "关键字1",
              "clearable": true,
              "placeholder": "通过关键字搜索",
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "engine1",
              "label": "Engine1",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "platform1",
              "label": "Platform1",
              "clearable": true,
              "columnRatio": 4
            }
          ]
        }
      ],
    },
    {
      "type": "form",
      "id": "targetFormId",
      "actions": [],
      "initApi": {
          "method": "get",
          "url": "/api/mock2/form/initForm",
          "data": {
              "test1": "${keywords}",
              "test2": "${engine}",
          },
      },
      "body": {
        "type": "group-container",
        "collapsible": false,
        "items": [{
          "header": {
            "title": "第一步，基础信息",
            "subTitle": "这是小标题"
          },
          "body": [
            {
              "type": "group",
              "body": [
                {
                  "name": "test1",
                  "type": "static",
                  "label": "静态展示"
                },
                {
                  "type": "static",
                  "name": "test3",
                  "label": "年龄",
                },
                {
                  "type": "static",
                  "name": "test3",
                  "label": "班级"
                }
              ]
            },
            {
              "type": "group",
              body: [
                {
                  "type": "static",
                  "name": "test1",
                  "label": "邮箱"
                },
                {
                  "type": "static",
                  "name": "test3",
                  "label": "电话"
                },
                {
                  "type": "static",
                  "name": "test3",
                  "label": "地址",
                  "columnRatio": 4
                }
              ]
            },
            {
              "type": "group",
              body: [
                {
                    "type": "static",
                    "name": "test1",
                    "label": "其它",
                    "columnRatio": 4,
                }
              ]
            }
          ]
        }, {
          "header": {
            "title": "第二步，复杂信息",
          },
          "body": [
            {
              "type": "group",
              body: [
                {
                  "type": "static",
                  "name": "test1",
                  "label": "邮箱",
                },
                {
                  "type": "static",
                  "name": "test3",
                  "label": "电话",
                },
                {
                  "type": "static",
                  "name": "test3",
                  "label": "地址",
                  "columnRatio": 4,
                }
              ]
            },
            {
              "type": "group",
              body: [
                {
                  "type": "static",
                  "name": "test1",
                  "label": "地址",
                  "columnRatio": 4,
                }
              ]
            }
          ]
        }]
      }
    },
  ]
}
```

### 查询表单结合左右布局

```schema
{
  "type": "page",
  "id": "pageId",
  "hasData": false,
  "data": {
    "text1": "营销中心",
    "text2": 2,
    "text3": **********,
    "text4": "负责人",
    "text5": "13137080987",
    "text6": **********,
    "text7": "创建人",
    "text8": "text8",
    "department": "营销中心",
    "platform": 2,
    "css": **********,
    "browser": "负责人",
    "selected": "text5",
    "browser2": **********,
    "remark": "创建人",
    "tip": "text8",
    "text9": "text9"
  },
  "body": [
    {
      "type": "form",
      "queryForm": true,
      "id": "crudFilterId",
      "body": [
        {
          "type": "button-toolbar",
          "buttons": [
            {
              "type": "button",
              "level": "primary",
              "label": "主功能按钮"
            }
          ]
        },
        {
          "type": "group",
          "mode": "horizontal",
          "body": [
            {
              "type": "input-text",
              "name": "keywords",
              "label": "关键字",
              "clearable": true,
              "placeholder": "通过关键字搜索",
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "engine",
              "label": "Engine",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "platform",
              "label": "Platform",
              "clearable": true,
              "columnRatio": 4
            }
          ]
        },
        {
          "type": "group",
          "mode": "horizontal",
          "body": [
            {
              "type": "input-text",
              "name": "keywords1",
              "label": "关键字1",
              "clearable": true,
              "placeholder": "通过关键字搜索",
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "engine1",
              "label": "Engine1",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "platform1",
              "label": "Platform1",
              "clearable": true,
              "columnRatio": 4
            }
          ]
        }
      ]
    },
    {
      "type": "left-right-container",
      "defaultWidth": 250,
      "className": "h-screen",
      "left": {
        "type": "form",
        "actions": [],
        "wrapWithPanel": false,
        "body": [
          {
            "type": "input-tree",
            "label": false,
            "name": "tree2",
            "multiple": false,
            "searchable": false,
            "autoCheckChildren": false,
            "options": [
              {
                "label": "A1",
                "value": "a1",
                "children": [
                  {
                    "label": "A1-1",
                    "value": "a1-1"
                  },
                  {
                    "label": "A1-2",
                    "value": "a1-2"
                  },
                  {
                    "label": "A1-3",
                    "value": "a1-3"
                  }
                ]
              },
              {
                "label": "B1",
                "value": "b1",
                "children": [
                  {
                    "label": "B1-1",
                    "value": "b1-1"
                  },
                  {
                    "label": "B1-2",
                    "value": "b1-2"
                  },
                  {
                    "label": "B1-3",
                    "value": "b1-3"
                  }
                ]
              },
              {
                "label": "C1",
                "value": "c1"
              },
              {
                "label": "A2",
                "value": "a2",
                "children": [
                  {
                    "label": "A2-1",
                    "value": "a2-1"
                  },
                  {
                    "label": "A2-2",
                    "value": "a2-2"
                  },
                  {
                    "label": "A2-3",
                    "value": "a2-3"
                  }
                ]
              },
              {
                "label": "B2",
                "value": "b2",
                "children": [
                  {
                    "label": "B2-1",
                    "value": "b2-1"
                  },
                  {
                    "label": "B2-2",
                    "value": "b2-2"
                  },
                  {
                    "label": "B2-3",
                    "value": "b2-3"
                  }
                ]
              },
              {
                "label": "C2",
                "value": "c2"
              },
              {
                "label": "A3",
                "value": "a3",
                "children": [
                  {
                    "label": "A3-1",
                    "value": "a3-1"
                  },
                  {
                    "label": "A3-2",
                    "value": "a3-2"
                  },
                  {
                    "label": "A3-3",
                    "value": "a3-3"
                  }
                ]
              },
              {
                "label": "B3",
                "value": "b3",
                "children": [
                  {
                    "label": "B3-1",
                    "value": "b3-1"
                  },
                  {
                    "label": "B3-2",
                    "value": "b3-2"
                  },
                  {
                    "label": "B3-3",
                    "value": "b3-3"
                  }
                ]
              },
              {
                "label": "C3",
                "value": "c3"
              }
            ],
            "onEvent": {
              "change": {
                "actions": [
                  {
                    "actionType": "setValue",
                    "componentId": "pageId",
                    "args": {
                      "value": {
                        "hasData": true
                      }
                    }
                  }
                ]
              }
            }
          }
        ]
      },
      "right": {
        "type": "form",
        "id": "form-id",
        "initApi": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/page/initData?tree=${tree2}",
        "actions": [],
        "static": true,
        "body": {
          "type": "group-container",
          "items": [
            {
              "header": {
                "title": "第一步，基础信息"
              },
              "body": [
                {
                  "type": "group",
                  "body": [
                    {
                      "type": "input-text",
                      "name": "text1",
                      "label": "姓名"
                    },
                    {
                      "type": "input-text",
                      "name": "text2",
                      "label": "年龄"
                    }
                  ]
                },
                {
                  "type": "group",
                  "body": [
                    {
                      "type": "input-text",
                      "name": "text4",
                      "label": "邮箱"
                    },
                    {
                      "type": "input-text",
                      "name": "text5",
                      "label": "电话"
                    }
                  ]
                },
                {
                  "type": "group",
                  "body": [
                    {
                      "type": "input-text",
                      "name": "text7",
                      "label": "其它",
                      "columnRatio": 6
                    }
                  ]
                }
              ]
            },
            {
              "header": {
                "title": "第二步，复杂信息"
              },
              "body": [
                {
                  "type": "group",
                  "body": [
                    {
                      "type": "input-text",
                      "name": "second1",
                      "label": "邮箱"
                    },
                    {
                      "type": "input-text",
                      "name": "second2",
                      "label": "电话"
                    }
                  ]
                },
                {
                  "type": "group",
                  "body": [
                    {
                      "type": "input-text",
                      "name": "textarea",
                      "label": "姓名",
                      "maxLength": 30,
                      "showCounter": true,
                      "placehold": "请输入"
                    }
                  ]
                },
                {
                  "type": "group",
                  "body": [
                    {
                      "type": "input-text",
                      "name": "second5",
                      "label": "其它"
                    }
                  ]
                }
              ]
            }
          ]
        }
      }
    }
  ]
}
```
### 表单项带高级参数
```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/saveForm?waitSeconds=2",
    "data": {
      "isEditPage": true,
      "input-sub-form": [
        {
          "title": "123",
          "b": "456"
        }
      ]
    },
    "body": [
      {
        "type": "group",
        "body": [
          {
            "type": "select",
            "name": "department",
            "label": "归属部门",
            "description": "所属部门",
          },
          {
            "type": "input-text",
            "name": "platform",
            "label": "Platform",
            "placeholder": "请输入",
            "showCounter": true,
            "maxLength": 20,
            "required": true,
          },
          {
            "type": "ds-date-range-picker",
            "name": "date",
            "label": "选择日期范围"
          },

        ]
      },
       {
          "type": "fieldSet",
          "title": "高级参数",
          "collapsable": true,
          "collapsed": true,
          size: 'base',
          "body": [
            {
              "name": "text1",
              "type": "input-text",
              "label": "文本1"
            },

            {
              "name": "text2",
              "type": "input-text",
              "label": "文本2"
            }
          ]
        },
    ]
  }
}
```
## 组件用法
### body 支持变量和表达式

- `body` 支持变量和表达式，详情见属性表。

```schema
{
  "type": "page",
  "id": "myPage",
  "data": {
    "isTT": true,
    "tt": [
      {
        "type": "input-text",
        "name": "name1",
        "label": "姓名1",
        "required": true,
        "validations": {
          "isNumeric": true,
          "minimum": 10
        }
      },
      {
        "name": "email1",
        "type": "input-email",
        "label": "邮箱1",
        "required": true
      }
    ],
    "tt0": [
      {
        "type": "input-text",
        "name": "name",
        "label": "姓名",
        "required": true,
        "validations": {
          "isNumeric": true,
          "minimum": 10
        }
      }
    ]
  },
  "body": {
    "type": "flex",
    "direction": "column",
    "gap": true,
    "items": [
      {
        "type": "button-toolbar",
        "buttons": [
          {
            "type": "button",
            "label": "清空表单值并更新表单项",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "componentId": "myForm",
                    "actionType": "clear"
                  },
                  {
                    "componentId": "myPage",
                    "actionType": "setValue",
                    "args": {
                      "value": {
                        "isTT": true
                      }
                    }
                  }
                ]
              }
            }
          },
          {
            "type": "button",
            "label": "清空表单值并更新表单项2",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "componentId": "myForm",
                    "actionType": "setValue",
                    "args": {
                      "value": {
                        "name": ""
                      }
                    }
                  },
                  {
                    "componentId": "myPage",
                    "actionType": "setValue",
                    "args": {
                      "value": {
                        "isTT": false
                      }
                    }
                  }
                ]
              }
            }
          }
        ]
      },
      {
        "type": "form",
        "id": "myForm",
        "debug": true,
        "api": "/api/mock2/form/saveForm",
        "showLabelColon": true,
        "body": "${isTT ? tt : tt0}"
      }
    ]
  }
}
```

### 自定义校验提示信息

- 若不配置 `messages`，默认校验失败
- `showValidedMsg` 是否提示校验信息，默认为 false
- `validateFailed` 校验失败的提示信息

如下我们配置姓名和邮箱表单项，并可以填写数据并提交给接口`/api/mock2/form/saveForm`。

```schema: scope="body"
{
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "messages": {
      "validateFailed": "不好意思，校验失败",
      "showValidedMsg": true
    },
    "body": [
      {
        "type": "input-text",
        "name": "name",
        "label": "姓名",
        "required": true
      },
      {
        "name": "email",
        "type": "input-email",
        "label": "邮箱",
        "required": true
      }
    ]
}
```

### 自动滚动到第一个校验错误的表单项

如下我们配置姓名和邮箱等表单项，校验失败默认能自动滚动到第一个校验失败的表单项。

```schema: scope="body"
{
    "type": "form",
    "debug": true,
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "input-text",
        "name": "name",
        "label": "姓名",
        "required": true
      },
      {
        "name": "email",
        "type": "input-email",
        "label": "邮箱",
        "required": true
      },
      {
        "type": "input-text",
        "name": "age",
        "label": "年龄",
        "required": true
      },
      {
        "name": "sex",
        "type": "input-email",
        "label": "性别",
        "required": true
      },
      {
        "type": "input-text",
        "name": "country",
        "label": "国籍",
        "required": true
      },
      {
        "name": "address",
        "type": "input-email",
        "label": "住址",
        "required": true
      },
      {
        "type": "input-text",
        "name": "work",
        "label": "工作",
        "required": true
      },
      {
        "name": "marriage",
        "type": "input-email",
        "label": "婚姻",
        "required": true
      }
    ]
}
```

### 自定义 label 宽度

水平模式下 `labelWidth` 可以设置标签文本的自定义宽度，默认单位为`px`。该属性的优先级：表单项 > 表单。

```schema: scope="body"
{
  "type": "form",
  "mode": "horizontal",
  "labelWidth": 120,
  "body": [
    {
      "type": "input-email",
      "name": "email",
      "label": "邮箱",
      "labelWidth": 200,
      "required": true
    },
    {
      "type": "input-password",
      "name": "password",
      "label": "密码",
      "required": true
    },
    {
      "type": "input-text",
      "name": "address",
      "label": "地址",
      "mode": "inline"
    },
    {
      "type": "input-text",
      "name": "mailCode",
      "label": "邮编",
      "mode": "inline",
      "labelWidth": 80
    },
    {
      "type": "radios",
      "name": "mailCode",
      "label": "性别",
      "mode": "row",
      "options": [
        {
          "label": "Male",
          "value": "male"
        },
        {
          "label": "Female",
          "value": "female"
        }
      ]
    }
  ]
}
```

<!-- ### 实现一行展示多个表单项

有两种方法，一个是通过 `columnCount` 来控制表单显示几列

```schema: scope="body"
 {
    "type": "form",
    "columnCount": 2,
    "body": [
      {
        "type": "input-email",
        "name": "email",
        "label": "邮箱",
        "required": true
      },
      {
        "type": "input-password",
        "name": "password",
        "label": "密码"
      }
    ]
  }
``` -->

另一个方法是使用 group，它能实现每行显示不同列数，以及不同列的宽度分配情况，可以实现更灵活的控制

```schema: scope="body"
 [
    {
      "type": "form",
      "body": [
        {
          "type": "group",
          "body": [
            {
              "type": "input-text",
              "name": "text1",
              "label": "文本1"
            },
            {
              "type": "input-text",
              "name": "text2",
              "label": "文本2"
            }
          ]
        },
        {
          "type": "divider"
        },
        {
          "type": "group",
          "body": [
            {
              "type": "input-text",
              "name": "text3",
              "label": "文本3",
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "text4",
              "label": "文本4",
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "text5",
              "label": "文本5"
            }
          ]
        }
      ]
    },
    {
      "type": "divider"
    },
    {
      "type": "form",
      "mode": "horizontal",
      "body": [
        {
          "type": "group",
          "body": [
            {
              "type": "input-email",
              "name": "email2",
              "label": "邮箱"
            },
            {
              "type": "input-password",
              "name": "password2",
              "label": "密码"
            }
          ]
        }
      ]
    }
  ]
```

### 底部按钮栏

#### 隐藏默认提交按钮

Form 默认会在底部渲染一个提交按钮，用于执行表单的提交行为。你可以通过两种方式去掉这个默认的提交按钮：

1. 配置：`"submitText": ""`
2. 配置：`"actions": []`

```schema: scope="body"
[
    {
      "type": "form",
      "submitText": "",
      "body": [
        {
          "type": "input-text",
          "name": "email",
          "label": "邮箱"
        }
      ]
    },
    {
      "type": "form",
      "actions": [],
      "body": [
        {
          "type": "input-text",
          "name": "email",
          "label": "邮箱"
        }
      ]
    }
  ]
```

#### 配置若干自定义按钮

同样，你可以通过 actions 属性，配置任意你想要的行为按钮。

```schema: scope="body"
{
    "type": "form",
    "body": [
      {
        "type": "input-text",
        "name": "email",
        "label": "邮箱"
      },
      {
        "type": "input-password",
        "name": "password",
        "label": "密码"
      }
    ],
    "actions": [
      {
        "type": "submit",
        "label": "登录"
      },
      {
        "type": "action",
        "actionType": "dialog",
        "label": "登录须知",
        "dialog": {
          "title": "登录须知",
          "body": "登录须知"
        }
      },
      {
        "type": "button",
        "label": "百度一下",
        "level": "primary",
        "url": "https://www.baidu.com/"
      }
    ]
  }
```

请记住，如果想触发表单提交行为，请配置`"actionType": "submit"`或`"type": "submit"`按钮

### 去掉表单边框

通过配置`"wrapWithPanel": false`，可以去掉默认表单边框（包括标题，按钮栏以及边距样式等）。

```schema: scope="body"
{
    "type": "form",
    "wrapWithPanel": false,
    "body": [
      {
        "type": "input-text",
        "name": "email",
        "label": "邮箱"
      },
      {
        "type": "input-password",
        "name": "password",
        "label": "密码"
      }
    ]
  }
```

**注意！配置该属性后，`title`和`actions`属性将失效并无法渲染，请在表单内自行配置。**

#### 固定底部栏

如果表单项较多导致表单过长，而不方便操作底部的按钮栏，可以配置`"affixFooter": true`属性，将底部按钮栏固定在浏览器底部

### 表单静态展示

在一些场景，表单提交后需要将填写的内容静态展示

#### 设置初始状态

通过配置`static: true`将整个表单设置为静态展示，单个表单项也支持此配置

```schema: scope="body"
{
  "type": "form",
  "mode": "horizontal",
  "labelWidth": 150,
  "id": "allFormStatic",
  "static": true,
  "body": [
    {
      "type": "input-text",
      "name": "var1",
      "label": "输入框",
      "value": "text"
    },
    {
      "type": "input-color",
      "name": "var2",
      "label": "颜色选择",
      "value": "#F0F"
    },
    {
      "type": "switch",
      "name": "switch",
      "label": "开关",
      "option": "开关说明",
      "value": true
    }
  ],
  "actions": []
}
```

#### 切换输入态和展示态

也支持使用动作切换表单的 输入态和展示态（静态），也可以使用动作对单个表单项进行状态切换
可以在[示例页](/dataseeddesigndocui/#/amis/examples/details/formAndTab)查看表单项的静态展示方式

```schema: scope="body"
{
  "type": "form",
  "mode": "horizontal",
  "labelWidth": 150,
  "id": "allFormSwitch",
  "data": {
    "isStatic": false
  },
  "body": [
    {
      "type": "input-text",
      "name": "var1",
      "label": "输入框",
      "value": "text"
    },
    {
      "type": "input-color",
      "name": "var2",
      "label": "颜色选择",
      "value": "#F0F"
    },
    {
      "type": "switch",
      "name": "switch",
      "label": "开关",
      "option": "开关说明",
      "value": true
    },
    {
      "type": "button-toolbar",
      "name": "button-toolbar",
      "buttons": [
        {
          "type": "button",
          "label": "提交",
          "level": "primary",
          "visibleOn": "${!isStatic}",
          "onEvent": {
            "click": {
              "actions": [
                {
                  "actionType": "setValue",
                  "componentId": "allFormSwitch",
                  "args": {
                    "value": {
                      "isStatic": true
                    }
                  }
                },
                {
                  "actionType": "static",
                  "componentId": "allFormSwitch"
                }
              ]
            }
          }
        },
        {
          "type": "button",
          "label": "编辑",
          "level": "primary",
          "visibleOn": "${isStatic}",
          "onEvent": {
            "click": {
              "actions": [
                {
                  "actionType": "setValue",
                  "componentId": "allFormSwitch",
                  "args": {
                    "value": {
                      "isStatic": false
                    }
                  }
                },
                {
                  "actionType": "nonstatic",
                  "componentId": "allFormSwitch"
                }
              ]
            }
          }
        }
      ]
    },
  ],
  "actions": []
}
```

### 表单项数据初始化

表单可以通过配置`initApi`，实现表单初始化时请求接口，用于展示数据或初始化表单项。

```schema: scope="body"
{
    "type": "form",
    "initApi": "/api/mock2/form/initData",
    "body": [
      {
        "type": "input-text",
        "name": "name",
        "label": "姓名"
      },
      {
        "type": "input-text",
        "name": "email",
        "label": "邮箱"
      }
    ]
}
```

比如以上这个例子接口返回为

```
{
    status: 0,
    msg: "",
    data: {
        name: "Amis Renderer",
        author: "fex",
        date: 1615978757,
        info: ""
    }
}
```

第一个表单项的 name 配置为 `name`，所以这个表单初始化完毕后展示 `Amis Renderer`。

> 表单项的 value 是不支持表达式，所以不要尝试用 `value: "${xxx}"` 来关联数据。

#### 轮询初始化请求

Form 支持轮询初始化接口，步骤如下：

1. 配置`initApi`
2. 配置 `interval`：单位为毫秒，最小 `1000`

```schema: scope="body"
{
    "type": "form",
    "initApi": "/api/mock2/page/initData",
    "interval": 3000,
    "body": [
      {
        "type": "input-text",
        "name": "date",
        "label": "时间戳"
      }
    ]
  }
```

如果希望在满足某个条件的情况下停止轮询，配置`stopAutoRefreshWhen`表达式。

```schema: scope="body"
{
    "type": "form",
    "initApi": "/api/mock2/page/initData",
    "interval": 3000,
    "stopAutoRefreshWhen": "this.date % 5",
    "body": [
      {
        "type": "input-text",
        "name": "date",
        "label": "时间戳"
      }
    ]
  }
```

#### 静态初始化数据域

我们也可以手动设置 form 的数据域来初始化多个表单项值

```schema: scope="body"
{
    "type": "form",
    "data": {
      "name": "rick",
      "email": "<EMAIL>"
    },
    "body": [
      {
        "type": "input-text",
        "name": "name",
        "label": "姓名"
      },
      {
        "type": "input-email",
        "name": "email",
        "label": "邮箱"
      }
    ]
  }
```

注意这里的 `data` 会进行数据映射，如果想不映射，需要进行转义，比如下面的例子

```schema: scope="body"
{
    "type": "form",
    "data": {
      "name": "\\${rick}",
      "email": "<EMAIL>"
    },
    "body": [
      {
        "type": "input-text",
        "name": "name",
        "label": "姓名"
      },
      {
        "type": "input-email",
        "name": "email",
        "label": "邮箱"
      }
    ]
  }
```

#### 数据格式一致性问题

当表单来初始化表单项值时，需要保持数据格式的一致性。

如果表单初始化的值与表单项配置的数据格式不符合，而且用户没有再次操作该表单项，而直接提交表单，那么会将当前默认值原封不动的提交给后端，也许会导致不一致性的问题，我们看一个例子：

```schema: scope="body"
{
    "type": "form",
    "data": {
        "select": ["a", "c"]
    },
    "body": [
        {
            "label": "选项",
            "type": "select",
            "name": "select",
            "multiple": true,
            "options": [
                {
                    "label":"A",
                    "value":"a"
                },
                {
                    "label":"B",
                    "value":"b"
                },
                {
                    "label":"C",
                    "value":"c"
                }
            ]
        }
    ]
}
```

上例中， `select` 我们配置了`"multiple": true`，预期中，我们希望选中 `A` 和 `C` 项时，表单项的数据格式为：`"a,c"`，但是我们表单数据域中，`select`默认值为`"value": ["a", "c"]`，并不符合我们当前表单项的数据格式配置，这样会导致两个问题：

1. 有可能不会默认选中 `A` 和 `C` 选项；
2. 当不操作该表单项，直接提交时，预期是：`"a,c"`，但提交给后端的数据为：`["a", "c"]`，导致了不一致性的问题。

> 通过 `initApi` 配置默认值同理，不再赘述

因此一定确保默认值与选择器表单项数据格式配置相匹配。

### 表单提交

配置`api`属性，当表单执行提交行为时，会默认将当前表单数据域中的数据使用`post`方式发送给所配置`api`

```schema: scope="body"
{
    "type": "form",
    "api": "/api/saveForm",
    "body": [
      {
        "type": "input-text",
        "name": "name",
        "label": "姓名"
      },
      {
        "type": "input-email",
        "name": "email",
        "label": "邮箱"
      }
    ]
  }
```

点击提交按钮，会看到发送表单请求，请求数据体为：

```json
{
  "name": "xxx",
  "email": "<EMAIL>"
}
```

发送请求默认为 `POST` 方式，会将所有表单项整理成一个对象发送过过去。除此之外你可以主动获取以下信息。

- `diff` 只会包含 `diff` 结果
- `prinstine` 原始数据
  如:

```json
{
  "api": {
    "method": "post",
    "url": "/api/xxx/save",
    "data": {
      "modified": "$$",
      "diff": "${diff}",
      "origin": "${prinstine}"
    }
  }
}
```

> 如果 返回了 `data` 对象，且是对象，会把结果 merge 到表单数据里面。

当你需要配置特定的请求方式，请求体，`header`时，使用对象类型 api 配置，并使用 数据映射 进行数据配置。

下面示例我们更改了请求方法为`PUT`，并在原提交数据的基础上添加一个字段`"_from"`。更多用法查看 [API 文档](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api) 和 [数据映射](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/data-mapping)文档

```schema: scope="body"
{
    "type": "form",
    "initApi": {
      "method": "put",
      "url": "/api/mock2/page/initData",
      "data": {
        "&": "$$$$",
        "_from": "browser"
      }
    },
    "body": [
      {
        "type": "input-text",
        "name": "name",
        "label": "姓名"
      },
      {
        "type": "input-email",
        "name": "email",
        "label": "邮箱"
      }
    ]
  }
```

触发表单提交行为有下面几种方式：

1. 默认的`提交按钮`
2. 为行为按钮配置`"actionType": "submit"`
3. 配置`"type": "submit"`的按钮

#### 轮询提交请求

通过设置`asyncApi`，当表单提交发送保存接口后，还会继续轮询请求该接口，默认间隔为`3秒`，直到返回 `finished` 属性为 `true` 才 结束。

```schema: scope="body"
{
    "type": "form",
    "initApi": "/api/mock2/page/initData",
    "asyncApi": "/api/mock2/page/initData",
    "body": [
      {
        "type": "input-text",
        "name": "name",
        "label": "姓名"
      },
      {
        "type": "input-email",
        "name": "email",
        "label": "邮箱"
      }
    ]
  }
```

如果决定结束轮询的标识字段名不是 `finished`，请设置`finishedField`属性，比如：`"finishedField": "is_success"`

### 表单校验

一般可以通过在[表单项格式校验](/dataseeddesigndocui/#/amis/zh-CN/components/form/formitem#%E6%A0%BC%E5%BC%8F%E6%A0%A1%E9%AA%8C)中，配置校验规则完成校验，但是有时候，我们需要组合多个表单项实现一些校验，这时可以通过配置 `rules` 来实现组合校验。

例如下例，我们想校验 `a` 和 `b` 表单项不可以同时有值，否则就报错，则可以进行如下配置：

```schema: scope="body"
{
  "type": "form",
  "api": "/api/form/saveForm",
  "rules": [
    {
      "rule": "!(data.a && data.b)",
      "message": "a 和 b 不能同时有值"
    }
  ],
  "body": [
    {
      "type": "input-text",
      "name": "a",
      "label": "A"
    },
    {
      "type": "input-text",
      "name": "b",
      "label": "B"
    }
  ]
}
```

> `rule` 编写使用 [表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression)

#### 组合校验高亮表单项

默认组合校验的错误信息显示在表单的底部，如果希望可以定位到表单项自己，则可以通过配置 `name` 来高亮错误。

```schema: scope="body"
{
  "type": "form",
  "api": "/api/form/saveForm",
  "rules": [
    {
      "rule": "!(data.a && data.b)",
      "message": "a 和 b 不能同时有值",
      "name": ["a", "b"]
    }
  ],
  "body": [
    {
      "type": "input-text",
      "name": "a",
      "label": "A"
    },
    {
      "type": "input-text",
      "name": "b",
      "label": "B"
    }
  ]
}
```

### 重置表单

配置`"type": "reset"`或者`"actionType": "reset"`的按钮，可以实现点击重置表单项值。

```schema: scope="body"
{
    "type": "form",
    "body": [
      {
        "type": "input-text",
        "name": "name",
        "label": "姓名"
      },
      {
        "type": "input-email",
        "name": "email",
        "label": "邮箱"
      }
    ],
    "actions": [
        {
            "type": "reset",
            "label": "重置"
        },
        {
            "type": "submit",
            "label": "保存"
        }
    ]
  }
```

> **请注意：**这里的重置是将表单数据域重置到**初始状态**，**而不是清空**，如果你配置了初始化接口，那么重置操作是会**将表单项重置至初始化表单项值**

### 表单数据域调试

配置`debug:true`可以查看当前表单的数据域数据详情，方便数据映射、表达式等功能调试，如下，你可以修改表单项查看数据域变化。`debugConfig`可以额外配置 debug 区域的相关配置

```schema: scope="body"
{
    "type": "form",
    "debug": true,
    "debugConfig": {
      "enableClipboard": true,
      "displayDataTypes": true
    },
    "body": [
      {
        "type": "input-text",
        "name": "name",
        "label": "姓名"
      },
      {
        "type": "input-email",
        "name": "email",
        "label": "邮箱"
      }
    ]
  }
```

> 该配置不会展示完整的数据链，只会展示当前表单的数据域

### 禁用数据链

默认表单是可以获取到完整数据链中的数据的，但是该默认行为不适用于所有场景，例如：

在 CRUD 的列表项中配置弹框，弹框中有一个表单，则该表单项中所有的同`name`表单项都会根据上层`crud`的行数据进行初始化，如果你是实现编辑的功能那并没有是什么问题，但是如果你是新建功能，那么这将不符合你的预期，你可以手动设置`"canAccessSuperData": false`来关闭该行为

### 提交后行为

表单提交成功后，可以执行一些行为。

#### 重置表单

如果想提交表单成功后，重置当前表单至初始状态，可以配置`"resetAfterSubmit": true`。

```schema: scope="body"
{
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "resetAfterSubmit": true,
    "body": [
      {
        "type": "input-text",
        "name": "name",
        "label": "姓名"
      },
      {
        "name": "email",
        "type": "input-email",
        "label": "邮箱"
      }
    ]
  }
```

编辑表单项，点击提交，成功后会发现表单项的值会重置到初始状态，即空

> 注意，如果表单项有默认值，则会将该表单项的值重置至该默认值。

#### 跳转页面

配置`redirect`属性，可以指定表单提交成功后要跳转至的页面

```schema: scope="body"
{
    "type": "form",
    "initApi": "/api/mock2/page/initData",
    "redirect": "/user/list",
    "body": [
      {
        "type": "input-text",
        "name": "name",
        "label": "姓名"
      },
      {
        "type": "input-email",
        "name": "email",
        "label": "邮箱"
      }
    ]
  }
```

#### 刷新目标组件

配置`reload`属性为其他组件`name`值，可以在表单提交成功之后，刷新指定组件。

```schema: scope="body"
[
    {
      "type": "form",
      "initApi": "/api/mock2/page/initData",
      "reload": "my_service",
      "body": [
        {
          "type": "input-text",
          "name": "name",
          "label": "姓名"
        },
        {
          "type": "input-email",
          "name": "email",
          "label": "邮箱"
        }
      ]
    },
    {
      "type": "service",
      "name": "my_service",
      "api": "/api/mock2/page/initData",
      "body": "service初识数据"
    }
  ]
```

上例中`form`提交成功后，会触发`name`为`my_service`的`Service`组件重新请求初始化接口

上面示例是一种[组件间联动](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/linkage#%E7%BB%84%E4%BB%B6%E9%97%B4%E8%81%94%E5%8A%A8)

#### 显示提交的返回结果

默认情况下表单提交返回结果会写入当前表单的数据域，如果要显示在当前表单，可以直接使用 `static` 类型，比如下面的例子

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "input-text",
      "name": "name",
      "label": "姓名"
    },
    {
      "type": "static",
      "name": "id",
      "visibleOn": "typeof data.id !== 'undefined'",
      "label": "返回 ID"
    }
  ]
}
```

#### 将提交返回内容发送到其它组件

还可以将返回结果发送到其它组件，首先设置另一个表单的 `name`，然后通过 `reload` 配置参数来提交

```schema: scope="body"
[
  {
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "reload": "otherForm?id=${id}",
    "body": [
      {
        "type": "input-text",
        "name": "name",
        "label": "姓名"
      }
    ]
  },
  {
    "type": "form",
    "name": "otherForm",
    "actions": [],
    "body": [
      {
        "type": "static",
        "name": "id",
        "label": "返回 ID"
      }
    ]
  }
]
```

#### 将数据域发送给目标组件

配置`target`属性为目标组件`name`值，可以在触发提交行为后，将当前表单的数据域发送给目标组件。

```schema: scope="body"
[
    {
      "type": "form",
      "target": "detailForm",
      "body": [
        {
          "type": "input-text",
          "placeholder": "关键字",
          "name": "keywords"
        }
      ]
    },
    {
      "type": "form",
      "name": "detailForm",
      "initApi": "/api/mock2/page/initData?keywords=${keywords}",
      "body": [
        {
          "label": "名称",
          "type": "static",
          "name": "name"
        },
        {
          "label": "作者",
          "type": "static",
          "name": "author"
        },
        {
          "label": "关键字",
          "type": "static",
          "name": "keywords"
        },
        {
          "label": "请求时间",
          "type": "static-datetime",
          "format": "YYYY-MM-DD HH:mm:ss",
          "name": "date"
        }
      ]
    }
  ]
```

第一个表单在提交时，会将它的表单数据域数据发送给`detailForm`表单，触发`detailForm`的初始化接口联动，重新请求接口更新数据域，并更新关键字表单项。

上面示例组合使用了 [组件间联动](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/linkage#%E7%BB%84%E4%BB%B6%E9%97%B4%E8%81%94%E5%8A%A8) 和 [接口联动](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/linkage#%E6%8E%A5%E5%8F%A3%E8%81%94%E5%8A%A8)

### 持久化保存表单项数据

表单默认在重置之后（切换页面、弹框中表单关闭表单），会自动清空掉表单中的所有数据，如果你想持久化保留当前表单项的数据而不清空它，那么通过 Form 配置 `persistData: "xxx"`，指定一个 `key` ，来实现数据持久化保存

> 注意，如果使用在 CRUD 列表中的编辑框内的 Form 中，可以利用数据映射语法，`persistData: "xxx:${id}"`，来为 form 指定一个唯一的 key

如果想提交成功后，清空该缓存，则配置`"clearPersistDataAfterSubmit": true`

#### 限制只存储某些 key

如果只想存储部分 key，可以配置 `"persistDataKeys": ["key1", "key2"]`，这样就只有 name 为 key1 和 key2 的表单项数据会持久化

### 禁用回车提交

表单默认情况下回车就会提交，如果想阻止这个行为，可以加上 `preventEnterSubmit` 配置项。

```schema: scope="body"
{
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "preventEnterSubmit": true,
    "body": [
      {
        "type": "input-text",
        "name": "name",
        "label": "姓名"
      },
      {
        "name": "email",
        "type": "input-email",
        "label": "邮箱"
      }
    ]
}
```

### validate 校验指定的表单项

validate 默认会校验整个表单，若想只校验部分表单项，需要配置 `args.validateFields`。
校验结果默认缓存在 `${event.data.validateResult}`，`validateResult.responseData: true` 表示校验成功，`undefined` 表示检验失败。
可以通过添加 `outputVar` 配置来修改缓存的变量 `validateResult`。

```schema
{
  "type": "page",
  "body": {
    "type": "flex",
    "direction": "column",
    "gap": true,
    "items": [
      {
        "type": "button",
        "label": "validate动作",
        "onEvent": {
          "click": {
            "actions": [
              {
                "actionType": "validate",
                "componentId": "form-validate-some-filed",
                "args": {
                  "validateFields": [
                    "name",
                    "age"
                  ]
                }
              },
              {
                "actionType": "setValue",
                "componentName": "validateResult",
                "args": {
                  "value": "${event.data.validateResult}"
                }
              },
              {
                "actionType": "toast",
                "args": {
                  "msg":"${event.data.validateResult.responseData ? \'校验成功\' : \'校验失败\'}"
                }
              }
            ]
          }
        }
      },
      {
        "type": "static",
        "label": "校验结果",
        "mode": "horizontal",
        "name": "validateResult",
        "tpl": "${validateResult|json}"
      },
      {
        "type": "form",
        "id": "form-validate-some-filed",
        "api": "/api/mock2/form/saveForm",
        "debug": true,
        "body": [
          {
            "type": "input-text",
            "name": "name",
            "label": "姓名",
            "required": true
          },
          {
            "name": "age",
            "type": "input-text",
            "label": "年龄",
            "required": true,
            "validations": {
              "isNumeric": true
            }
          },
          {
            "name": "email",
            "type": "input-email",
            "label": "邮箱",
            "required": true
          }
        ]
      }
    ]
  }
}

```

### 属性表

| 属性名                      | 类型                                                                                                                                                  | 默认值                                     | 说明                                                                                                                                                                                                                                                                                                                                                         | 版本     |
| --------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | -------- |
| type                        | `string`                                                                                                                                              |                                            | `"form"` 指定为 Form 渲染器                                                                                                                                                                                                                                                                                                                                  |
| name                        | `string`                                                                                                                                              |                                            | 设置一个名字后，方便其他组件与其通信                                                                                                                                                                                                                                                                                                                         |
| mode                        | `string`                                                                                                                                              | `normal`                                   | 表单展示方式，可以是：`normal` 或者 `horizontal`， `normal`文案与表单项是垂直放置的                                                                                                                                                                                                                                                                                                           |
| horizontal                  | `Object`                                                                                                                                              | `{"left":2, "right":10, "justify": false}` | 当 mode 为 `horizontal` 时有用，用来控制 label 的展示占比                                                                                                                                                                                                                                                                                                    |
| labelWidth                  | `number \| string`                                                                                                                                    |                                            | 表单项标签自定义宽度                                                                                                                                                                                                                                                                                                                                         |
| submitText                  | `String`                                                                                                                                              | `"提交"`                                   | 默认的提交按钮名称，如果设置成空，则可以把默认按钮去掉。                                                                                                                                                                                                                                                                                                     |
| className                   | `string`                                                                                                                                              |                                            | 外层 Dom 的类名                                                                                                                                                                                                                                                                                                                                              |
| body                        | Array<[表单项](/dataseeddesigndocui/#/amis/zh-CN/components/form/formitem) or [SchemaNode](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode) > |                                            | Form 表单项集合，从 1.19.0 版本支持变量和表达式                                                                                                                                                                                                                                                                                                              | `1.19.0` |
| actions                     | Array<[行为按钮](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/action)>                                                                             |                                            | Form 提交按钮，成员为 Action                                                                                                                                                                                                                                                                                                                                 |
| wrapWithPanel               | `boolean`                                                                                                                                             | `true`                                     | 是否让 Form 用 panel 包起来，设置为 false 后，actions 将无效。                                                                                                                                                                                                                                                                                               |
| panelClassName              | `string`                                                                                                                                              |                                            | 外层 panel 的类名                                                                                                                                                                                                                                                                                                                                            |
| api                         | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api)                                                                                               |                                            | Form 用来保存数据的 api。                                                                                                                                                                                                                                                                                                                                    |
| initApi                     | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api)                                                                                               |                                            | Form 用来获取初始数据的 api。                                                                                                                                                                                                                                                                                                                                |
| rules                       | Array<{rule:string;message:string;name?: string[]}>                                                                                                   |                                            | 表单组合校验规则                                                                                                                                                                                                                                                                                                                                             |
| interval                    | `number`                                                                                                                                              | `3000`                                     | 刷新时间(最低 3000)                                                                                                                                                                                                                                                                                                                                          |
| silentPolling               | `boolean`                                                                                                                                             | `false`                                    | 配置刷新时是否显示加载动画                                                                                                                                                                                                                                                                                                                                   |
| stopAutoRefreshWhen         | `string`                                                                                                                                              | `""`                                       | 通过[表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression) 来配置停止刷新的条件                                                                                                                                                                                                                                                                |
| initAsyncApi                | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api)                                                                                               |                                            | Form 用来获取初始数据的 api,与 initApi 不同的是，会一直轮询请求该接口，直到返回 finished 属性为 true 才 结束。                                                                                                                                                                                                                                               |
| initFetch                   | `boolean`                                                                                                                                             | `true`                                     | 设置了 initApi 或者 initAsyncApi 后，默认会开始就发请求，设置为 false 后就不会起始就请求接口                                                                                                                                                                                                                                                                 |
| initFetchOn                 | `string`                                                                                                                                              |                                            | 用表达式来配置                                                                                                                                                                                                                                                                                                                                               |
| initFinishedField           | `string`                                                                                                                                              | `finished`                                 | 设置了 initAsyncApi 后，默认会从返回数据的 data.finished 来判断是否完成，也可以设置成其他的 xxx，就会从 data.xxx 中获取                                                                                                                                                                                                                                      |
| initCheckInterval           | `number`                                                                                                                                              | `3000`                                     | 设置了 initAsyncApi 以后，默认拉取的时间间隔                                                                                                                                                                                                                                                                                                                 |
| asyncApi                    | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api)                                                                                               |                                            | 设置此属性后，表单提交发送保存接口后，还会继续轮询请求该接口，直到返回 `finished` 属性为 `true` 才 结束。                                                                                                                                                                                                                                                    |
| checkInterval               | `number`                                                                                                                                              | 3000                                       | 轮询请求的时间间隔，默认为 3 秒。设置 `asyncApi` 才有效                                                                                                                                                                                                                                                                                                      |
| finishedField               | `string`                                                                                                                                              | `"finished"`                               | 如果决定结束的字段名不是 `finished` 请设置此属性，比如 `is_success`                                                                                                                                                                                                                                                                                          |
| submitOnChange              | `boolean`                                                                                                                                             | `false`                                    | 表单修改即提交                                                                                                                                                                                                                                                                                                                                               |
| submitOnInit                | `boolean`                                                                                                                                             | `false`                                    | 初始就提交一次                                                                                                                                                                                                                                                                                                                                               |
| resetAfterSubmit            | `boolean`                                                                                                                                             | `false`                                    | 提交后是否重置表单                                                                                                                                                                                                                                                                                                                                           |
| primaryField                | `string`                                                                                                                                              | `"id"`                                     | 设置主键 id, 当设置后，检测表单是否完成时（asyncApi），只会携带此数据。                                                                                                                                                                                                                                                                                      |
| target                      | `string`                                                                                                                                              |                                            | 默认表单提交自己会通过发送 api 保存数据，但是也可以设定另外一个 form 的 name 值，或者另外一个 `CRUD` 模型的 name 值。 如果 target 目标是一个 `Form` ，则目标 `Form` 会重新触发 `initApi`，api 可以拿到当前 form 数据。如果目标是一个 `CRUD` 模型，则目标模型会重新触发搜索，参数为当前 Form 数据。当目标是 `window` 时，会把当前表单的数据附带到页面地址上。 |
| redirect                    | `string`                                                                                                                                              |                                            | 设置此属性后，Form 保存成功后，自动跳转到指定页面。支持相对地址，和绝对地址（相对于组内的）。                                                                                                                                                                                                                                                                |
| reload                      | `string`                                                                                                                                              |                                            | 操作完后刷新目标对象。请填写目标组件设置的 name 值，如果填写为 `window` 则让当前页面整体刷新。                                                                                                                                                                                                                                                               |
| autoFocus                   | `boolean`                                                                                                                                             | `false`                                    | 是否自动聚焦。                                                                                                                                                                                                                                                                                                                                               |
| canAccessSuperData          | `boolean`                                                                                                                                             | `true`                                     | 指定是否可以自动获取上层的数据并映射到表单项上                                                                                                                                                                                                                                                                                                               |
| persistData                 | `string`                                                                                                                                              | `""`                                       | 指定一个唯一的 key，来配置当前表单是否开启本地缓存                                                                                                                                                                                                                                                                                                           |
| persistDataKeys             | `string[]`                                                                                                                                            | `""`                                       | 指指定只有哪些 key 缓存                                                                                                                                                                                                                                                                                                                                      |
| clearPersistDataAfterSubmit | `boolean`                                                                                                                                             | `true`                                     | 指定表单提交成功后是否清除本地缓存                                                                                                                                                                                                                                                                                                                           |
| preventEnterSubmit          | `boolean`                                                                                                                                             | `false`                                    | 禁用回车提交表单                                                                                                                                                                                                                                                                                                                                             |
| trimValues                  | `boolean`                                                                                                                                             | `false`                                    | trim 当前表单项的每一个值                                                                                                                                                                                                                                                                                                                                    |
| promptPageLeave             | `boolean`                                                                                                                                             | `false`                                    | form 还没保存，即将离开页面前是否弹框确认。                                                                                                                                                                                                                                                                                                                  |
| columnCount                 | `number`                                                                                                                                              | 0                                          | 表单项显示为几列                                                                                                                                                                                                                                                                                                                                             |
| inheritData                 | `boolean`                                                                                                                                             | `true`                                     | 默认表单是采用数据链的形式创建个自己的数据域，表单提交的时候只会发送自己这个数据域的数据，如果希望共用上层数据域可以设置这个属性为 false，这样上层数据域的数据不需要在表单中用隐藏域或者显式映射才能发送了。                                                                                                                                                 |
| disabled                    | `boolean`                                                                                                                                             |                                            | 整个表单是否是禁用状态                                                                                                                                                                                                                                                                                                                                       |
| disabledOn                  | [表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression)                                                                                  |                                            | 整个表单是否禁用的条件                                                                                                                                                                                                                                                                                                                                       |
| static                      | `boolean`                                                                                                                                             |                                            | 整个表单静态方式展示，详情请查看[示例页](/dataseeddesigndocui/#/amis/examples/details/formAndTab)                                                                                                                                                                                                                                                            |
| staticOn                    | [表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression)                                                                                  |                                            | 整个表单是否静态展示的条件                                                                                                                                                                                                                                                                                                                                   |
| staticClassName             | `string`                                                                                                                                              |                                            | 表单静态展示时使用的类名                                                                                                                                                                                                                                                                                                                                     |
| closeDialogOnSubmit         | `boolean`                                                                                                                                             |                                            | 提交的时候是否关闭弹窗。当 form 里面有且只有一个弹窗的时候，本身提交会触发弹窗关闭，此属性可以关闭此行为                                                                                                                                                                                                                                                     |
| showLabelColon              | `boolean`                                                                                                                                             | true                                       | 控制是否在 label 后面自动补充冒号 `1.10.0+ 版本之后默认为true，之前的版本默认为false`                                                                                                                                                                                                                                                                        |
| validateAllField            | `boolean`                                                                                                                                             | false                                      | 是否校验未渲染出来的表单数据                                                                                                                                                                                                                                                                                                                                 |
| messages                    | `Object`                                                                                                                                              |                                            | 消息提示覆写。                                                                                                                                                                                                                                                                                          |
| message.validateFailed      | `string`                                                                                                                                              | ""                                         | 表单校验失败时的信息                                                                                                                                                                                                                                                                                                                                         |
| message.showValidedMsg      | `boolean`                                                                                                                                             | `false`                                    | 是否提示校验信息                                                                                                                                                                                                                                                                                                                                             |
| messages.fetchSuccess       | `string`                                                                  |                                            | 获取成功时提示                                                                                                                                                                                                                                                                                                                                               |
| messages.fetchFailed        | `string`                                                                  |                                            | 获取失败时提示                                                                                                                                                                                                                                                                                                                                               |
| messages.saveSuccess        | `string`                                                                  |                                            | 保存成功时提示                                                                                                                                                                                                                                                                                                                                               |
| messages.saveFailed         | `string`                                                                  |                                            | 保存失败时提示                                                                                                                                                                                                                                                                                                                                               |
| autoFillHeight            | `boolean`                                                                                                                                             | false                                      | 高度是否占满父元素，通常配合 [LeftRightContainer 容器](/dataseeddesigndocui/#/amis/zh-CN/components/left-right-container) 一起使用
| updatePristineAfterStoreDataReInit | `boolean` | - | 上层数据域存储数据初始化后，是否设置为表单的默认值 |  |

### 事件表

当前组件会对外派发以下事件，可以通过`onEvent`来监听这些事件，并通过`actions`来配置执行的动作，在`actions`中可以通过`${事件参数名}`来获取事件产生的数据，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action)。

| 事件名称              | 事件参数                                                     | 说明                                                                                                                                      |
| --------------------- | ------------------------------------------------------------ | ----------------------------------------------------------------------------------------------------------------------------------------- |
| inited                | `event.data: object` initApi 远程请求返回的初始化数据        | 配置 initApi 时，initApi 接口请求完成时触发；没配时，Form 组件实例被创建并插入 DOM 中时触发                                               |
| change                | `event.data: object` 当前表单数据                            | 表单值变化时触发                                                                                                                          |
| formItemValidateSucc  | `event.data: object` 当前表单数据                            | 表单项校验成功时触发                                                                                                                      |
| formItemValidateError | `event.data: object` 当前表单数据                            | 表单项校验失败时触发 <br/>支持使用`event.data.errorFields`获取表单内所有错误的 formItem                                                   |
| validateSucc          | `event.data: object` 当前表单数据                            | 表单校验成功时触发                                                                                                                        |
| validateError         | `event.data: object` 当前表单数据                            | 表单校验失败时触发 <br/>支持使用`event.data.errorFields`获取表单内所有错误的 formItem                                                     |
| submit                | `event.data: object` 当前表单数据                            | 点击提交按钮或者触发表单提交动作的时候触发，配置了该事件后将不会触发表单提交时的校验、提交到 api 或者 target 等行为，所有行为需要自己配置 |
| submitSucc            | `event.data.result: object` api 远程请求成功后返回的结果数据 | 提交成功时触发                                                                                                                            |
| submitFail            | `event.data.error: object` api 远程请求失败后返回的错误信息  | 提交失败时触发                                                                                                                            |

### 动作表

当前组件对外暴露以下特性动作，其他组件可以通过指定`actionType: 动作名称`、`componentId: 该组件id`来触发这些动作，动作配置可以通过`args: {动作配置项名称: xxx}`来配置具体的参数，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action#触发其他组件的动作)。

| 动作名称  | 动作配置                                                                                                | 说明                                           | 版本                                                                   |
| --------- | ------------------------------------------------------------------------------------------------------- | ---------------------------------------------- | ---------------------------------------------------------------------- |
| submit    | `outputVar` 设置提交结果输出字段，默认为`submitResult`                                                  | 提交表单。 提交结果可通过 `submitResult` 获取  | 1.59.0 支持配置 `outputVar`                                            |
| reset     | -                                                                                                       | 重置表单                                       |
| clear     | -                                                                                                       | 清空表单                                       |
| validate  | `args.validateFields`可校验指定的表单项; <br/> `outputVar` 设置校验结果输出字段，默认为`validateResult` | 校验表单。校验结果可通过 `validateResult` 获取 | 1.14.0 支持配置 `args.validateFields`<br/> 1.59.0 支持配置 `outputVar` |
| clearError  | - | 清除表单校验产生的错误状态 | 1.86.4 |
| reload    | -                                                                                                       | 刷新（重新加载）                               |
| setValue  | `value: object` 更新的表单数据                                                                          | 更新数据，对数据进行 merge                     |
| static    | -                                                                                                       | 表单切换为静态展示                             |
| nonstatic | -                                                                                                       | 表单切换为普通输入态                           |


#### clearError

> 1.86.4 及以上版本
> 清除表单校验产生的错误状态

```schema
{
  "type": "page",
  "title": "清除表单错误",
  "body": [
    {
      "type": "form",
      "title": "表单",
      "id": "form",
      "body": [
        {
          "type": "input-text",
          "label": "必填项",
          "name": "required",
          "required": true
        }
      ]
    },
    {
      "type": "button",
      "label": "清除错误",
      "level": "primary",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "clearError",
              "componentId": "form"
            }
          ]
        }
      }
    }
  ]
}
```
