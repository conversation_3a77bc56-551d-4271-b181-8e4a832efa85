---
title: InputFile 文件上传
description:
type: 0
group: null
menuName: InputFile
icon:
order: 21
---
用来负责文件上传，文件上传成功后会返回文件地址，这个文件地址会作为这个表单项的值，整个表单提交的时候，其实提交的是文件地址，文件上传已经在这个控件中完成了。


## 场景推荐

### 基本使用

```schema: scope='body'
 {
  "type": "form",
  "debug": true,
  "mode": "horizontal",
  "body": [
     {
        "type": "input-file",
        "name": "file",
        "label": "素材上传",
        "multiple": true,
        "accept": ".jpg,.png,.pdf",
        "listType": "preview",
        "receiver": "/api/upload/file",
     }
  ]
}

```


### 文件上传按钮跟随下载模版

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/form/saveForm?waitSeconds=2",
    "data": {
      "fileCount": 50
    },
    "body": [
      {
        "type": "group",
        "body": [
          {
            "type": "radios",
            "name": "radios",
            "required": true,
            "label": "选择填充方式",
            "value": "file",
            "options": [
              {
                "label": "",
                "value": "file",
                "extraLabel": {
                  "type": "wrapper",
                  "size": "none",
                  "body": [
                    {
                      "type": "tpl",
                      "tpl": "导入文件"
                    },
                    {
                      "type": "remark",
                      "content": "请上传符合规范的文件类型"
                    }
                  ]
                }
              }
            ]
          }
        ]
      },
      {
        "type": "group",
        "body": [
          {
            "type": "flex",
            "gap": true,
            "items": [
              {
                "type": "input-file",
                "name": "file",
                "label": "上传文件",
                "hideUploadButton": true,
                "required": true,
                "level": "primary",
                "receiver": "/api/upload/file",
                "extraActions": {
                  "type": "button",
                  "level": "link",
                  "label": "下载模版"
                }
              }
            ]
          }
        ]
      },
      {
        "type": "group",
        "label": "数量",
        "direction": "vertical",
        "body": [
          {
            "type": "static-text",
            "name": "fileCount",
            "label": false
          },
          {
            "type": "editor",
            "name": "editor",
            "label": false,
          }
        ]
      }
    ]
  }
}
```

### 拖拽上传

将文件拖入指定区域，完成上传，同样支持点击上传。

```schema: scope='body'
 {
  "type": "form",
  "debug": true,
  "mode": "horizontal",
  "body": [
     {
        "type": "input-file",
            "name": "file",
            "accept": "*",
            "label": false,
            "receiver": "/api/upload/file",
            "drag": true
     }
  ]
}

```


### 自定义渲染

可以通过`fileSchema`来自定义上传后的文件列表展示内容，不支持预览模式，schema中可以配置`downloadFile` 或者 `removeFile` 来实现文件的下载和删除，比如下面示例，点击`download`按钮就会下载文件，点击`del`按钮会删除文件

```schema: scope="body"
{
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "body": [
        {
            "type": "input-file",
            "id": "file",
            "name": "file",
            "label": "File",
            "accept": "*",
            "receiver": "/api/upload/file",
            "fileSchema": [
            "${name}",
            {
              "type": "button",
              "label": "download",
              "level": "link",
              "downloadFile": true,
            },
            {
              "type": "button",
              "label": "del",
              "level": "link",
              "removeFile": true,
            }
          ]
        }
    ]
}
```
### 静态展示
```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "debug": true,
    "data": {
      "downloads": [
        {
          "name": "测试环境堡垒机操作手册.pdf",
          "link": "https://www.baidu.com"
        },
        {
          "name": "跳板机操作手册.pdf",
          "link": "https://www.baidu.com"
        },
        {
          "name": "运维日常问题排查手册.pdf",
          "link": "https://www.baidu.com"
        }
      ]
    },
    "body": [
      {
        "type": "input-file",
        "name": "downloads",
        "label": "文件预览",
        "static": true,
        "multiple": true,
        "joinValues": false,
        "valueField": "link"
      }
    ]
  }
}
```

## 组件用法
<!-- ### 基本用法

用来负责文件上传，文件上传成功后会返回文件地址，这个文件地址会作为这个表单项的值，整个表单提交的时候，其实提交的是文件地址，文件上传已经在这个控件中完成了。

> 如果希望文件内容伴随表单一起提交，可以配置 `asBlob` 或者 `asBase64`。

```schema: scope="body"
{
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "body": [
        {
            "type": "input-file",
            "name": "file",
            "label": "File",
            "accept": "*",
            "receiver": "/api/upload/file"
        }
    ]
}
``` -->

#### 接口说明

这是单文件上传模式，通过配置 `receiver` 来接管文件上传。

接口发送方式是 POST, 数据体为 form-data 格式。对应的文件字段名为 `file`。这个可以通过 `fileField` 来配置。要求返回的数据格式如下。

```json
{
  "status": 0,
  "msg": "",
  "data": {
    "value": "xxxx"
  }
}
```

- value：必须返回该字段用作回显，一般是文件资源地址

> 注意这只是单文件上传部分，如果允许上传的文件比较大，建议用分块上传，请阅读下面的分块上传部分。


#### 配置带描述信息
> 如果希望文件内容伴随表单一起提交，可以配置 `asBlob` 或者 `asBase64`。

```schema: scope='body'
 {
  "type": "form",
  "debug": true,
  "mode": "horizontal",
  "body": [
     {
        "type": "input-file",
        "name": "file",
        "label": "素材上传",
        "multiple": true,
        "maxSize": 10485760,
        "description": "支持上传格式PNG、JPG、PDF",
        "accept": ".jpg,.png,.pdf",
        "listType": "preview",
        "receiver": "/api/upload/file",
     }
  ]
}

```

#### 上传按钮为primary
通过配置"level":"primary" 实现

```schema: scope='body'
{
  "type": "form",
  "api": "/api/mock2/form/saveForm?waitSeconds=2",
  "data":{
    "fileCount": 50
  },
  "body": [
     {
      "type":"group",
      "body": [
         {
        "type": "flex",
        "gap": true,
        "items": [
          {
            "type": "input-file",
            "name": "file",
            "label": "上传文件",
            "hideUploadButton": true,
            "required": true,
            "level": "primary",
            "receiver": "/api/upload/file",
          },
          {
            "type": "button",
            "level": "link",
            "linkWithoutPadding": true,
            "label": "下载模版",
          }
        ]
      }
      ]
     },
     {
       "type":"group",
       "body":[
         {
          "type": "input-text",
          "static": true,
          "name": "fileCount",
          "label": "上传总数"
          }
       ]
     }
    ]
}
```
### 限制文件类型

可以配置`accept`来限制可选择的文件类型，格式是文件后缀名`.xxx`

```schema: scope="body"
{
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "body": [
        {
            "type": "input-file",
            "name": "file",
            "label": "限制只能上传csv文件",
            "accept": ".csv",
            "receiver": "/api/upload/file"
        }
    ]
}
```

想要限制多个类型，则用逗号分隔，例如：`.csv,.md`

### 限制文件大小

可以配置`maxSize`来限制文件大小，配置了`description`时，提示语会默认放在`description`后

```schema: scope="body"
{
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "body": [
        {
            "type": "input-file",
            "name": "file",
            "label": "不能上传超过 1M 的文件",
            "maxSize": 1048576,
            "receiver": "/api/upload/file"
        }
    ]
}
```

### 手动上传

如果不希望 File 组件上传，可以配置 `asBlob` 或者 `asBase64`，采用这种方式后，组件不再自己上传了，而是直接把文件数据作为表单项的值，文件内容会在 Form 表单提交的接口里面一起带上。

```schema: scope="body"
{
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "debug": true,
    "body": [
        {
            "type": "input-file",
            "name": "file",
            "label": "File",
            "accept": "*",
            "asBlob": true
        }
    ]
}
```

上例中，选择任意文件，然后观察数据域变化；点击提交，amis 自动会调整接口数据格式为`FormData`

### 分块上传

如果文件过大，则可能需要使用分块上传，默认大于 5M（chunkSize 配置决定） 的文件，同时配置了`startChunkApi`/`chunkApi`/`finishChunkApi`是会自动开启，可以通过 `useChunk` 配置成 false 关闭。

分块上传需要配置三个接口来完成分别是:

- `startChunkApi` 用来做分块前的准备工作
- `chunkApi` 用来接收每个分块上传
- `finishChunkApi` 用来收尾分块上传

还可以通过 `concurrency` 控制并行数量，默认是 3

### startChunkApi

用来做分块前的准备工作，一个文件只会调用一次。如果出错了，后续的分块上传就会中断。

发送说明：默认是 post，发送的数据中会包含 `filename` 字段，记录文件名，默认的数据体格式为 json。可以额外配置参数，请参考 API 的配置说明。

要求返回的数据中必须包含：

- `uploadId` 这次上传的唯一 ID。
- `key` 有点类似 `uploadId`，可有可无，爱速搭中用来记录后端文件存储路径。

其他属性返回目前是没有任何作用的。

如：

```
{
  "status": 0,
  "msg": "",
  "data": {
    "key": "images/JSSDK_page-xxxx.zip",
    "uploadId": "036f64cd5dd95750d4bcb33556b629c6"
  }
}
```

#### chunkApi

用来接收每个分块上传，大文件会根据 chunkSize 分割成多块，然后每块上传都会调用这个接口。

发送说明：默认为 post，发送体格式为 form-data。包含以下信息：

- `uploadId` startChunkApi 返回的
- `key` startChunkApi 返回的
- `partNumber` 分块序号，从 1 开始。
- `partSize` 分块大小
- `file` 文件体

要求返回的数据中必须包含:

- `eTag` 通常为文件的内容戳。

如：

```
{
  "status": 0,
  "msg": "",
  "data": {
    "eTag": "016bd9b68ddd5cd7318875da3ea28207"
  }
}
```

#### finishChunkApi

等所有分块上传完后，将上传文件收集到的 `eTag` 信息合并一起，再次请求后端完成文件上传。

发送说明：默认为 post，数据体默认为 json，包含以下信息

- `filename` 文件名
- `key` startChunkApi 返回的
- `uploadId` startChunkApi 返回的
- `partList` 数组，每个成员为 `{partNumber: xxx, eTag: "xxxxx"}` 即分块编号和分块 `eTag` 信息。

数据返回，类似单文件上传一样，必须有 `value` 属性，可选返回 `url` 用来决定文件的下载地址。如：

```
{
  "status": 0,
  "msg": "",
  "data": {
    "value": "https://xxxx.cdn.bcebos.com/images/JSSDK_page-xxxxx.zip"
  }
}
```

### 自动填充

上传成功后，可以通过配置 `autoFill` 将上传接口返回的值填充到某个表单项中（在非表单下暂不支持）：

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "input-file",
      "name": "file",
      "label": "File",
      "accept": "*",
      "receiver": "/api/upload/file",
      "autoFill": {
        "myUrl": "${url}"
      }
    },
    {
      "type": "input-text",
      "name": "myUrl",
      "label": "url"
    }
  ]
}
```

上例中，file 组件上传后，接口返回格式例如如下：

```json
{
  "status": 0,
  "msg": "",
  "data": {
    "value": "xxxxxxx",
    "filename": "xxxx.csv",
    "url": "http://xxxx.xxx.xxx"
  }
}
```

然后 file 上配置：

```json
"autoFill": {
    "myUrl": "${url}"
}
```

这样上传成功后，会把接口中的 `url` 变量，赋值给 `myUrl` 变量

**多选模式**

当表单项为多选模式时，不能再直接取选项中的值了，而是通过 `items` 变量来取，通过它可以获取当前选中的选项集合。

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "input-file",
      "name": "file",
      "label": "File",
      "multiple": true,
      "receiver": "/api/upload/file",
      "autoFill": {
        "myUrl": "${items|pick:url}",
        "lastUrl": "${items|last|pick:url}"
      }
    }
  ]
}
```


### 直传 OSS 模式

目前方案，oss 上传不支持分块分片。

本地调试需要添加 mockapi

```js
// 举例
proxy: {
  "/midwareopr": {
    target: "http://moka.dmz.sit.caijj.net/",
    changeOrigin: true,
  },
},
```

非应用中测试，需要配置添加 `headers: { 'X-TOKEN': 'xxx' }`

可以通过 btnClassName 控制上传按钮仅显示为文本样式

```schema: scope="body"
{
  type: 'form',
  api: '/api/mock2/form/saveForm',
  body: [
    {
      type: 'input-file',
      name: 'file',
      label: 'File',
      accept: '*',
      multiple: true,
      uploadMode: 'oss',
      btnClassName: 'uploadText',
      receiver: {
        // url: '', // 'publicNext' | 'privateNext' | 'privateOverride'
        // headers: {
        //    'X-TOKEN': XToken,
        // },
        data: {
          menu: 'vipshipmgrui/v/#/tying',
          prefix: '',
        },
      },
    }
  ]
}
```

### 预览模式

通过配置`listType`为`preview`开启预览模式，目前仅支持图片和 pdf 类型文件的预览。

```schema: scope="body"
{
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "debug": true,
    "body": [
        {
            "type": "input-file",
            "name": "file",
            "label": "File",
            "accept": "*",
            "multiple": true,
            "joinValues": false,
            "listType": "preview",
            "receiver": "/api/upload/file"
        }
    ]
}
```

### 表单项的值为对象类型

当需要传递给服务端的表单项的值不是一个简单的字符串而是一个对象类型时，可以通过配置 `extractValue: true` ,`joinValues: false`来提取对象类型的值。这种模式下，组件会智能处理初始数据和上传返回的数据，确保数据结构的一致性。

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "data": {
    "file": [{
      "a": 11,
      "b": 22,
      "fileName": "1.txt"
    }]
  },
  "body": [
    {
      "type": "input-file",
      "name": "file",
      "label": "素材上传",
      "multiple": true,
      "nameField": "fileName",
      "extractValue": true,
      "joinValues": false,
      "receiver": {
        "url": "/api/upload/file",
        "adaptor": "return {status: 0, data: {value: {a: Math.floor(Math.random()*100), b: Math.floor(Math.random()*100), fileName: '上传文件.txt'}}}"
      }
    }
  ]
}
```

在上面的示例中：

- **初始数据**：`{a: 11, b: 22, fileName: "1.txt"}` - 对象格式的业务数据
- **上传返回**：`{value: {a: 1, b: 2, fileName: "2.txt"}}` - 接口返回的对象数据
- **最终输出**：`[{a: 11, b: 22, fileName: "1.txt"}, {a: 1, b: 2, fileName: "2.txt"}]` - 统一的对象数组

**配置说明**：

- `extractValue: true` - 提取值模式，支持对象类型
- `nameField: "fileName"` - 指定用于显示文件名的字段
- `joinValues: false` - 多选时返回数组而非字符串

**适用场景**：

- 文件上传需要携带额外的元数据（如文件分类、标签等）
- 初始数据为复杂对象结构
- 需要在表单提交时保持完整的业务数据结构

### 下载

O 端请求库`@lattebank/webadmin-http`内置了 download 方法，downloadUrl 属性配置 API，将 method 设置为 download 即可

`@lattebank/webadmin-http`需升级至 0.2.9 以上，之前版本和 amis 配套使用会 toast 提示错误，升级即可解决错误问题

```js
// 举例
downloadUrl: {
  method: "download",
  url: "http://xxxx.xxx.xxx",
}
```

### 属性表

除了支持 [普通表单项属性表](/dataseeddesigndocui/#/amis/zh-CN/components/form/formitem#%E5%B1%9E%E6%80%A7%E8%A1%A8) 中的配置以外，还支持下面一些配置

| 属性名           | 类型                                                    | 默认值                                                                                                     | 说明                                                                                                                                 |
| ---------------- | ------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------ |
| receiver         | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api) |                                                                                                            | 上传文件接口，当开启阿里云 OSS 直传时，需要配置此项为 ApiOptions 格式，更多要求参看下文                                              |
| uploadMode       | `string`                                                | -                                                                                                          | `1.3.0` 开始支持，直传阿里云 oss 需要配置 此项为 'oss'                                                                               |
| accept           | `string`                                                | `text/plain`                                                                                               | 默认只支持纯文本，要支持其他类型，请配置此属性为文件后缀`.xxx`                                                                       |
| asBase64         | `boolean`                                               | `false`                                                                                                    | 将文件以`base64`的形式，赋值给当前组件                                                                                               |
| asBlob           | `boolean`                                               | `false`                                                                                                    | 将文件以二进制的形式，赋值给当前组件                                                                                                 |
| maxSize          | `number`                                                |                                                                                                            | 默认没有限制，当设置后，文件大小大于此值将不允许上传。单位为`B`                                                                      |
| description      | `string`                                                |                                                                                                            | 上传文案描述，如同时设置了`maxSize`，限制大小的文案会展示在上传文案后方                                                              |
| maxLength        | `number`                                                |                                                                                                            | 默认没有限制，当设置后，一次只允许上传指定数量文件。                                                                                 |
| multiple         | `boolean`                                               | `false`                                                                                                    | 是否多选。                                                                                                                           |
| drag             | `boolean`                                               | `false`                                                                                                    | 是否为拖拽上传                                                                                                                       |
| joinValues       | `boolean`                                               | `true`                                                                                                     | [拼接值](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E6%8B%BC%E6%8E%A5%E5%80%BC-joinvalues)                           |
| extractValue     | `boolean`                                               | `false`                                                                                                    | [提取值](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E6%8F%90%E5%8F%96%E5%A4%9A%E9%80%89%E5%80%BC-extractvalue)，支持对象类型值的提取       |
| delimiter        | `string`                                                | `,`                                                                                                        | [拼接符](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E6%8B%BC%E6%8E%A5%E7%AC%A6-delimiter)                            |
| autoUpload       | `boolean`                                               | `true`                                                                                                     | 是否选择完就自动开始上传                                                                                                             |
| listType         | `list` \| `preview`                                     | `list`                                                                                                     | 上传列表的内建样式，`preview`模式仅支持图片和 pdf 格式文件。`1.46.0`版本支持。                                                       |
| hideUploadButton | `boolean`                                               | `false`                                                                                                    | 隐藏上传按钮                                                                                                                         |
| stateTextMap     | object                                                  | `{ init: '', pending: '等待上传', uploading: '上传中', error: '上传出错', uploaded: '已上传', ready: '' }` | 上传状态文案                                                                                                                         |
| fileField        | `string`                                                | `file`                                                                                                     | 如果你不想自己存储，则可以忽略此属性。                                                                                               |
| nameField        | `string`                                                | `name`                                                                                                     | 接口返回哪个字段用来标识文件名                                                                                                       |
| valueField       | `string`                                                | `value`                                                                                                    | 文件的值用那个字段来标识。                                                                                                           |
| urlField         | `string`                                                | `url`                                                                                                      | 文件下载地址的字段名。                                                                                                               |
| btnLabel         | `string`                                                |                                                                                                            | 上传按钮的文字                                                                                                                       |
| downloadUrl      | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api) |                                                                                                            | 默认显示文件路径的时候会支持直接下载，可以支持加前缀如：`http://xx.dom/filename=` ，如果不希望这样，可以把当前配置项设置为 `false`。 |
| useChunk         | `boolean`或`"auto"`                                     | `"auto"`                                                                                                   | amis 所在服务器，限制了文件上传大小不得超出 10M，所以 amis 在用户选择大文件的时候，自动会改成分块上传模式。                          |
| chunkSize        | `number`                                                | `5 * 1024 * 1024`                                                                                          | 分块大小                                                                                                                             |
| startChunkApi    | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api) |                                                                                                            | startChunkApi                                                                                                                        |
| chunkApi         | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api) |                                                                                                            | chunkApi                                                                                                                             |
| finishChunkApi   | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api) |                                                                                                            | finishChunkApi                                                                                                                       |
| concurrency      | `number`                                                |                                                                                                            | 分块上传时并行个数                                                                                                                   |
| documentation    | `string`                                                |                                                                                                            | 文档内容                                                                                                                             |
| documentLink     | `string`                                                |                                                                                                            | 文档链接                                                                                                                             |
| initAutoFill     | `boolean`                                               | `true`                                                                                                     | 初表单反显时是否执行                                                                                                                 |
| fileSchema       | `schema`                                                |                                                                                                            | 自定义上传文件模版渲染 `1.59.1` 版本支持                                                                                             |
|  level       | `string`                                                |                                                                                                            | 上传按钮颜色配置                                                                                          |

#### 直传 OSS 模式 receiver 属性表

url 有 3 个可选值，对应后端三个接口，意义如下

- publicNext 默认，共有桶，随机命名
- privateNext 私有桶，随机命名
- privateOverride 私有桶，同名覆盖

data 中，其他属性配置

| 属性名 | 类型   | 默认值 | 说明                       |
| ------ | ------ | ------ | -------------------------- |
| menu   | string | -      | 必填项，当前菜单           |
| prefix | string | -      | 可选项，上传资源的前缀路径 |

示例

```js
{
  type: 'input-file',
  name: 'file',
  label: 'File',
  accept: '*',
  uploadMode: 'oss', // 开启直传 OSS
  receiver: {
    // url: '', // 可选值 'publicNext' | 'privateNext' | 'privateOverride'
    data: {
      menu: 'vipshipmgrui/v/#/tying', // 必填项
      prefix: '', // 可选项，上传资源的前缀路径
    },
  },
}
```

### 事件表

当前组件会对外派发以下事件，可以通过`onEvent`来监听这些事件，并通过`actions`来配置执行的动作，在`actions`中可以通过`${事件参数名}`来获取事件产生的数据，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action)。

> `[name]`表示当前组件绑定的名称，即`name`属性，如果没有配置`name`属性，则通过`file`取值。

| 事件名称 | 事件参数                                                                                                                                    | 说明                                     |
| -------- | ------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------- |
| change   | `[name]: FileValue` \| `Array<FileValue>` 组件的值                                                                                          | 上传文件值变化时触发(上传失败同样会触发) |
| remove   | `item: FileValue` 被移除的文件<br/>`[name]: FileValue` \| `Array<FileValue>` 组件的值                                                       | 移除文件时触发                           |
| success  | `item: FileValue` 上传的文件<br/>`result: any` 远程上传请求成功后接口返回的结果数据<br/>`[name]: FileValue` \| `Array<FileValue>` 组件的值  | 上传成功时触发                           |
| fail     | `item: FileValue` 上传的文件 <br /> `error: object` 远程上传请求失败后返回的错误信息<br/>`[name]: FileValue` \| `Array<FileValue>` 组件的值 | 上传文件失败时触发                       |

#### FileValue 属性表

| 属性名 | 类型     | 说明                                               |
| ------ | -------- | -------------------------------------------------- |
| name   | `string` | 文件名称                                           |
| value  | `string` | 上传成功后返回的 url                               |
| state  | `string` | 文件当前状态,值可为 `pending` `uploaded` `invalid` |
| error  | `string` | 错误信息                                           |

### 动作表

当前组件对外暴露以下特性动作，其他组件可以通过指定`actionType: 动作名称`、`componentId: 该组件id`来触发这些动作，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action#触发其他组件的动作)。

| 动作名称     | 动作配置                       | 说明                           |
| ------------ | ------------------------------ | ------------------------------ |
| clear        | -                              | 清空                           |
| removeFile   | `fileIndex: number` 文件的索引 | 移除某个文件 `1.59.1` 版本支持 |
| downloadFile | `fileIndex: number` 文件的索引 | 下载某个文件 `1.59.1` 版本支持 |
