---
title: InputFormula 公式编辑器
description:
type: 0
group: null
menuName: InputFormula
icon:
order: 21
standardMode: true
---

## 基本用法

用来输入公式。还是 beta 版本，整体待优化。 

可配置 `enableSourceMode:true` 开启源码编辑模式切换,`enableRunPanel:true` 开启运行模式切换，1.60.0 及以上版本。

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "body": [
    {
      "type": "input-formula",
      "name": "formula",
      "label": "公式",
      "evalMode": true,
      "value": "SUM(1 , user.id)",
      "variables": [
        {
          "label": "表单字段",
          "children": [
            {
              "label": "文章名",
              "value": "name",
              "tag": "文本"
            },
            {
              "label": "作者",
              "value": "author",
              "tag": "文本"
            },
            {
              "label": "售价",
              "value": "price",
              "tag": "数字"
            },
            {
              "label": "出版时间",
              "value": "time",
              "tag": "时间"
            },
            {
              "label": "版本号",
              "value": "version",
              "tag": "数字"
            },
            {
              "label": "出版社",
              "value": "publisher",
              "tag": "文本"
            }
          ]
        },
        {
          "label": "流程字段",
          "children": [
            {
              "label": "联系电话",
              "value": "telphone"
            },
            {
              "label": "地址",
              "value": "addr"
            }
          ]
        },
        {
          "label": "人员对象",
          "value": "user",
          "children": [
            {
              "label": "ID",
              "value": "user.id"
            },
            {
              "label": "用户名",
              "value": "user.name"
            },
          ]
        },
        {
          "label": "长文本测试分类长文本测试分类长文本测试分类长文本测试分类",
          "children": [
            {
              "label": "这是一段测试长文本这是一段测试长文本这是一段测试长文本",
              "value": "longtext",
              "tag": "文本"
            }
          ]
        }
      ],
    }
  ]
}
```


配置 `variables` 属性设置变量列表数据，可使用表达式`${xxx}`获取数据域数据，也可配置具体的数据。数据格式为：
```json
[
  {
    "label": "分组标题", // 分组标题，不需分组时可不传顶层label
    "children": [
      "label": "名称", // 必须，变量展示
      "value": "name", // 必须，变量实际字段名
      "tag": "string", // 变量标签(展示变量列表选项 右端)
      "description": "描述信息", // 变量描述(会展示在tooltip中，不存在时tooltip展示label)
    ]
  }
]
```

配置`functions` 属性设置公式列表数据，可使用表达式`${xxx}`获取数据域数据，也可配置具体的数据。不设置时默认为内置函数列表。数据格式为：
```json
[
  {
    "groupName": "逻辑函数",
    "items": [
      {
        "name": "IF",
        "description": "示例：IF(A, B, C)\\n\\n如果满足条件A，则返回B，否则返回C，支持多层嵌套IF函数。\\n\\n也可以用表达式如：A ? B : C",
        "example": "IF(condition, consequent, alternate)",
        "params": [
          {
            "type": "expression",
            "name": "condition",
            "description": "条件表达式."
          },
          {
            "type": "any",
            "name": "consequent",
            "description": "条件判断通过的返回结果"
          },
          {
            "type": "any",
            "name": "alternate",
            "description": "条件判断不通过的返回结果"
          }
        ],
        "returns": {
          "type": "any",
          "description": "根据条件返回不同的结果"
        }
      }
    ]
  }
]
```

## 展示模式
<!-- 
设置`"inputMode": "button"`可以切换编辑器的展示模式为按钮模式。

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "body": [
    {
      "type": "input-formula",
      "name": "formula",
      "label": "公式",
      "variableMode": "tree",
      "evalMode": true,
      "value": "SUM(1 , 2)",
      "inputMode": "button",
      "variables": [
        {
          "label": "表单字段",
          "children": [
            {
              "label": "文章名",
              "value": "name",
              "tag": "文本"
            },
            {
              "label": "作者",
              "value": "author",
              "tag": "文本"
            },
            {
              "label": "售价",
              "value": "price",
              "tag": "数字"
            },
            {
              "label": "出版时间",
              "value": "time",
              "tag": "时间"
            },
            {
              "label": "版本号",
              "value": "version",
              "tag": "数字"
            },
            {
              "label": "出版社",
              "value": "publisher",
              "tag": "文本"
            }
          ]
        },
        {
          "label": "流程字段",
          "children": [
            {
              "label": "联系电话",
              "value": "telphone"
            },
            {
              "label": "地址",
              "value": "addr"
            }
          ]
        }
      ],
    }
  ]
}
``` -->

设置`"inputMode": "input-group"`可以切换编辑器的展示模式为输入框组合模式。

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "body": [
    {
      "type": "input-formula",
      "name": "formula",
      "label": "公式",
      "variableMode": "tree",
      "evalMode": true,
      "value": "SUM(1 , 2)",
      "inputMode": "input-group",
      "variables": [
        {
          "label": "表单字段",
          "children": [
            {
              "label": "文章名",
              "value": "name",
              "tag": "文本"
            },
            {
              "label": "作者",
              "value": "author",
              "tag": "文本"
            },
            {
              "label": "售价",
              "value": "price",
              "tag": "数字"
            },
            {
              "label": "出版时间",
              "value": "time",
              "tag": "时间"
            },
            {
              "label": "版本号",
              "value": "version",
              "tag": "数字"
            },
            {
              "label": "出版社",
              "value": "publisher",
              "tag": "文本"
            }
          ]
        },
        {
          "label": "流程字段",
          "children": [
            {
              "label": "联系电话",
              "value": "telphone"
            },
            {
              "label": "地址",
              "value": "addr"
            }
          ]
        }
      ],
    }
  ]
}
```

## 变量展示

设置不同`variableMode`字段切换变量展示模式，树形结构：

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "body": [
    {
      "type": "input-formula",
      "name": "formula",
      "label": "公式",
      "variableMode": "tree",
      "evalMode": true,
      "variables": [
        {
          "label": "表单字段",
          "children": [
            {
              "label": "文章名",
              "value": "name",
              "tag": "文本"
            },
            {
              "label": "作者",
              "value": "author",
              "tag": "文本"
            },
            {
              "label": "售价",
              "value": "price",
              "tag": "数字"
            },
            {
              "label": "出版时间",
              "value": "time",
              "tag": "时间"
            },
            {
              "label": "版本号",
              "value": "version",
              "tag": "数字"
            },
            {
              "label": "出版社",
              "value": "publisher",
              "tag": "文本"
            }
          ]
        },
        {
          "label": "流程字段",
          "children": [
            {
              "label": "联系电话",
              "value": "telphone"
            },
            {
              "label": "地址",
              "value": "addr"
            }
          ]
        }
      ],
    }
  ]
}
```

Tab 结构：

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "body": [
    {
      "type": "input-formula",
      "name": "formula",
      "label": "公式",
      "variableMode": "tabs",
      "evalMode": false,
      "variables": [
        {
          "label": "表单字段",
          "children": [
            {
              "label": "文章名",
              "value": "name",
              "tag": "文本"
            },
            {
              "label": "作者",
              "value": "author",
              "tag": "文本"
            },
            {
              "label": "售价",
              "value": "price",
              "tag": "数字"
            },
            {
              "label": "出版时间",
              "value": "time",
              "tag": "时间"
            },
            {
              "label": "版本号",
              "value": "version",
              "tag": "数字"
            },
            {
              "label": "出版社",
              "value": "publisher",
              "tag": "文本"
            }
          ]
        },
        {
          "label": "流程字段",
          "children": [
            {
              "label": "联系电话",
              "value": "telphone"
            },
            {
              "label": "地址",
              "value": "addr"
            }
          ]
        }
      ],
    }
  ]
}
```


配置 `variableMenuTpl` 变量选项使用自定义展示，1.60.0 及以上版本。

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "body": [
    {
      "type": "input-formula",
      "name": "formula",
      "label": "公式",
      "variableMenuTpl": "${!children? value + \": \" + label: label}",
      "variables": [
        {
          "label": "表单字段",
          "children": [
            {
              "label": "文章名",
              "value": "name",
            },
            {
              "label": "作者",
              "value": "author",
            },
            {
              "label": "售价",
              "value": "price",
            },
            {
              "label": "出版时间",
              "value": "time",
            },
            {
              "label": "版本号",
              "value": "version",
            },
            {
              "label": "出版社",
              "value": "publisher",
            }
          ]
        },
        {
          "label": "流程字段",
          "children": [
            {
              "label": "联系电话",
              "value": "telphone"
            },
            {
              "label": "地址",
              "value": "addr"
            }
          ]
        }
      ],
    }
  ]
}
```

配置 `variableExtraActions` 变量选项，添加自定义操作项，1.60.0 及以上版本。

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "body": [
    {
      "type": "input-formula",
      "name": "formula",
      "label": "公式",
      "variableExtraActions": [
        {
          "type": "action",
          "icon": "fa fa-external-link",
          "level": "link",
          "actionType": "dialog",
          "dialog": {
            "title": "详情",
            "actions": [],
            "data": {
              "item": {
                "label": "${label}",
                "value": "${value}"
              }
            },
            "body": {
              "type": "static-json",
              "name": "item"
            }
          }
        }
      ],
      "variables": [
        {
          "label": "表单字段",
          "children": [
            {
              "label": "文章名",
              "value": "name",
              "tag": "文本"
            },
            {
              "label": "作者",
              "value": "author",
              "tag": "文本"
            },
            {
              "label": "售价",
              "value": "price",
              "tag": "数字"
            },
            {
              "label": "出版时间",
              "value": "time",
              "tag": "时间"
            },
            {
              "label": "版本号",
              "value": "version",
              "tag": "数字"
            },
            {
              "label": "出版社",
              "value": "publisher",
              "tag": "文本"
            }
          ]
        },
        {
          "label": "流程字段",
          "children": [
            {
              "label": "联系电话",
              "value": "telphone"
            },
            {
              "label": "地址",
              "value": "addr"
            }
          ]
        }
      ],
    }
  ]
}
```

## 模板模式

当配置 `evalMode` 为 false 时则为模板模式，意思是说默认不当做表达式，只有 `${`和`}`包裹的部分才是表达式。

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "body": [
    {
      "type": "input-formula",
      "name": "formula",
      "label": "公式",
      "evalMode": false,
      "value": "my name is \\${name}",
      "variables": [
        {
          "label": "表单字段",
          "children": [
            {
              "label": "文章名",
              "value": "name",
              "tag": "文本"
            },
            {
              "label": "作者",
              "value": "author",
              "tag": "文本"
            },
            {
              "label": "售价",
              "value": "price",
              "tag": "数字"
            },
            {
              "label": "出版时间",
              "value": "time",
              "tag": "时间"
            },
            {
              "label": "版本号",
              "value": "version",
              "tag": "数字"
            },
            {
              "label": "出版社",
              "value": "publisher",
              "tag": "文本"
            }
          ]
        },
        {
          "label": "流程字段",
          "children": [
            {
              "label": "联系电话",
              "value": "telphone"
            },
            {
              "label": "地址",
              "value": "addr"
            }
          ]
        }
      ],
    }
  ]
}
```

## 混合模式

混合模式的意思是支持输入文本和输入公式两种格式的值，当输入公式时值会自动用 `${` 和 `}` 包裹，如果不是这种格式则认为是输入普通的字符串。通过 `mixedMode` 为 true 启用这种模式

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "body": [
    {
      "type": "input-formula",
      "name": "value",
      "label": "混合模式",
      "mixedMode": true,
      "value": "\\${SUM(1, 2)}",
      "variables": [
        {
          "label": "表单字段",
          "children": [
            {
              "label": "文章名",
              "value": "name",
              "tag": "文本"
            },
            {
              "label": "作者",
              "value": "author",
              "tag": "文本"
            },
            {
              "label": "售价",
              "value": "price",
              "tag": "数字"
            },
            {
              "label": "出版时间",
              "value": "time",
              "tag": "时间"
            },
            {
              "label": "版本号",
              "value": "version",
              "tag": "数字"
            },
            {
              "label": "出版社",
              "value": "publisher",
              "tag": "文本"
            }
          ]
        },
        {
          "label": "流程字段",
          "children": [
            {
              "label": "联系电话",
              "value": "telphone"
            },
            {
              "label": "地址",
              "value": "addr"
            }
          ]
        }
      ],
      "onEvent": {
        "change": {
          "actions": [
            {
              "actionType": "toast",
              "args": {
                "msg": "${event.data.value}"
              }
            }
          ]
        }
      }
    }
  ]
}
```

## 属性表

| 属性名            | 类型                                                                                       | 默认值         | 说明                                                                           | 版本 |
| ----------------- | ------------------------------------------------------------------------------------------ | -------------- | ------------------------------------------------------------------------------ | --------- |
| title             | `string`                                                                                   | `'公式编辑器'` | 弹框标题                                                                       |
| header            | `string`                                                                                   | -              | 编辑器 header 标题，如果不设置，默认使用表单项`label`字段                      |
| evalMode          | `boolean`                                                                                  | `true`         | 表达式模式 或者 模板模式，模板模式则需要将表达式写在 `${` 和 `}` 中间。        |
| variableMode      | `string`                                                                                   | `list`         | 可配置成 `tabs` 或者 `tree` 默认为列表，支持分组。                             |
| inputMode         | `'input-button' \| 'input-group'`                                              | `'input-button'`              | 控件的展示模式                                                                 |
| variables         | `'string' \| 'Array'`                         | -           | 数组 或 数据映射
| functions         | `'string' \| 'Array'`                          | -              | 数组 或 数据映射。默认就是 amis-formula 里面定义的函数。 |
| allowInput        | `boolean`                                                                                  | -              | 输入框是否可输入                                                               |
| singleLine        | `boolean`                                                                                  | `true`              | 输入框是否单行展示（仅编辑状态支持）    |    `1.64.0`    |
| borderMode        | `'full' \| 'half' \| 'none'`                                                               | -              | 输入框边框模式                                                                 |
| enableSourceMode   | `boolean`                                                                   | -              | 编辑器展示 源码模式开关                                                                 | `1.60.0`  |
| enableRunPanel    | `boolean`                                                                   | -              | 编编辑器展示 运行开关                                                                 | `1.60.0`  |
| variableMenuTpl      |  [模板](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/template) \| [Schema](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode#schema-配置)                                                               | -              | 变量面板选项渲染模版                                                                 | `1.60.0`  |
| variableExtraActions | `SchemaNode[]`                            | -              | 变量面板选项额外操作项版                                                                 | `1.60.0`  |
| placeholder       | `string`                                                                                   | `'暂无数据'`   | 输入框占位符                                                                   |
| className         | `string`                                                                                   | -              | 控件外层 CSS 样式类名                                                          |
| variableClassName | `string`                                                                                   | -              | 变量面板 CSS 样式类名                                                          |
| functionClassName | `string`                                                                                   | -              | 函数面板 CSS 样式类名                                                          |

## 事件表

| 事件名称 | 事件参数 | 说明 | 版本 |
| --- | --- | --- | --- |
| change | `value: string` | 值改变时触发 | 1.82.0 |
