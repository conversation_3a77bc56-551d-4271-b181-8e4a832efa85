---
title: Input-Group 输入框组合
description:
type: 0
group: null
menuName: Input-Group
icon:
order: 28
standardMode: true
---

**输入框组合选择器** 可用于输入框与其他组件进行组合。 <br>  inputGroup与Group最直接的区别就是，组合中间是否有间隙，如果有推荐用Group。

## 场景推荐

### 表单基本用法
多个表单项的组合以及带前后缀的情况可以参考该示例

```schema
{
  "type": "page",
  "data": {
    "unita1": "day",
    "unit1": [
      "day",
      "month",
      "year",
      "days",
      "months",
      "years",
      "dayss",
      "monthss",
      "yearss"
    ]
  },
  "body": {
    // "static": true,
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "mode": "horizontal",
    "body": [
      {
        "type": "input-group",
        "label": "账户开立日期",
        "name": "rule1",
        "flex": "column",
        "body": [
          {
            "type": "select",
            "name": "unita1",
            "inputClassName": "flex-grow",
            "options": [
              {
                "label": "账户开立日期距报告日期账户开立日期账户开立日期",
                "value": "day"
              },
              {
                "label": "月",
                "value": "month"
              },
              {
                "label": "年",
                "value": "year"
              }
            ],
          },
          {
            "type": "select",
            "name": "unit1",
            "multiple": true,
            "inputClassName": "flex-grow",
            "options": [
              {
                "label": "3天",
                "value": "day"
              },
              {
                "label": "3月",
                "value": "month"
              },
              {
                "label": "3年",
                "value": "year"
              },
              {
                "label": "30天",
                "value": "days"
              },
              {
                "label": "30月",
                "value": "months"
              },
              {
                "label": "30年",
                "value": "years"
              },
              {
                "label": "300天",
                "value": "dayss"
              },
              {
                "label": "300月",
                "value": "monthss"
              },
              {
                "label": "300年",
                "value": "yearss"
              }
            ],
            "maxTagCount": 4,
            "checkAll": true
          }
        ]
      },
      {
        "type": "input-group",
        "label": "数据保留规则",
        "prefix": "近",
        "prefixName": "customPrefix",
        "body": [
          {
            "type": "input-text",
            "label": false,
            "name": "days",
            "placeholder": "请输入",
          },
          {
            "type": "select",
            "name": "unit",
            "options": [
              {
                "label": "天",
                "value": "day"
              },
              {
                "label": "月",
                "value": "month"
              },
              {
                "label": "年",
                "value": "year"
              }
            ],
            "value": "day"
          },
        ],
      },
      {
        "type": "input-group",
        "label": "金额",
        "suffix": "元",
        "body": [
          {
            "type": "input-text",
            "name": "groupfix",
            "placeholder": "请输入"
          },
        ]
      },
    ],
  },
};
```

## 组件用法
### 基本用法

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "input-group",
      "name": "input-group",
      "label": "input 组合",
      "body": [
        {
          "type": "input-text",
          "placeholder": "搜索作业ID/名称",
          "inputClassName": "b-r-none p-r-none",
          "name": "input-group"
        },
        {
          "type": "submit",
          "label": "搜索",
          "level": "primary"
        }
      ]
    },
    {
      "type": "input-group",
      "label": "各种组合",
      "body": [
        {
          "type": "select",
          "name": "memoryUnits",
          "options": [
            {
              "label": "Gi",
              "value": "Gi"
            },
            {
              "label": "Mi",
              "value": "Mi"
            },
            {
              "label": "Ki",
              "value": "Ki"
            }
          ],
          "value": "Gi"
        },
        {
          "type": "input-text",
          "name": "memory"
        },
        {
          "type": "select",
          "name": "memoryUnits2",
          "options": [
            {
              "label": "Gi",
              "value": "Gi"
            },
            {
              "label": "Mi",
              "value": "Mi"
            },
            {
              "label": "Ki",
              "value": "Ki"
            }
          ],
          "value": "Gi"
        },
        {
          "type": "button",
          "label": "Go"
        }
      ]
    }
  ]
}

```

### 前后缀
input-group 支持配置`prefix`和`suffix`配置前后缀：

```schema: scope="body"
 {
      "type": "form",
      "data": {
        "prefix": "近"
      },
      "title": "编辑表单",
      "mode": "horizontal",
      "debug": true,
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
      "body": [
        {
          "type": "input-group",
          "label": "inputGroup",
          "prefix": "${prefix}",
          "suffix": "元",
          "body": [
            {
              "type": "input-text",
              "name": "groupfix"
            }
          ]
        },
      ]
    }

```

配置`prefixName`和`suffixName`即可将前后缀以表单项的提交给后端接口，不配置则不提交：

```schema: scope="body"
{
  "type": "form",
  "title": "编辑表单",
  "mode": "horizontal",
  "labelWidth": 200,
  "debug": true,
  data: {
    "groupfix": "1231231232",
  },
  "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
  "body": [
    {
      "type": "input-group",
      "label": "inputGroup prefix & suffix",
      "prefix": "近",
      "prefixName": "prefix",
      "suffix": "元",
      "suffixName": "suffix",
      "body": [
        {
          "type": "input-text",
          "name": "groupfix"
        }
      ]
    },
  ]
}
```

### 校验

input-group 配置校验方法较为特殊，需要配置下面步骤：

1. input-group 上配置任意`name`值 (必填, 否则表单内存在多个输入组合时无法定位)
2. input-group 的 body 内配置的表单项上配置校验规则
3. 如果 input-group 的子元素配置了`label`, 则会在校验失败时作为标识符展示, 否则仅使用索引值作为标识符
4. 单个子元素多条校验信息会使用`; `分隔
5. 可以使用`"errorMode": "full" | "partial"`设置错误提示风格, `full`整体飘红, `partial`仅错误元素飘红, 默认为`"full"`

> 细粒度错误提示

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "input-group",
      "name": "input-group",
      "label": "输入组合校验",
      "body": [
        {
          "type": "input-text",
          "placeholder": "请输入长度不超过6的数字类型",
          "name": "group-input1",
          "label": "子元素一",
          "validations": {
            "isNumeric": true,
            "maxLength": 6
          },
        },
        {
          "type": "input-text",
          "placeholder": "请输入长度不少于5的文本",
          "name": "group-input2",
          "required": true,
          "validations": {
            "minLength": 5
          }
        }
      ]
    }
  ]
}
```

### 自定义错误信息的索引格式

> 不配置errorMsgIndex的显示效果 和 配置 errorMsgIndex = true 的效果一样，均显示索引, 默认格式是：`"({index})"`

> 配置 errorMsgIndex = false 将不显示索引

> 配置 errorMsgIndex 成字符串的时候，必须包含占位符，否则无法显示。占位符是 `"{index}"`, 注意和变量不同。

> 若配置了上述没有罗列的情况 均不显示索引

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "api": "/amis/api/mock2/form/saveForm",
  "body": [
    {
      "type": "input-group",
      "name": "input-group",
      "label": "输入组合校验",
      "validationConfig": {
        "errorMode": "partial",
        "errorMsgIndex": "第{index}个输入框",
      },
      "body": [
        {
          "type": "input-text",
          "placeholder": "请输入长度不超过3的数字类型",
          "name": "group-input1",
          "label": "group-input1",
          "validations": {
            "isNumeric": true,
            "maxLength": 3
          }
        },
        {
          "type": "input-text",
          "placeholder": "请输入长度不少于5的文本",
          "name": "group-input2",
          "label": "group-input2",
          "required": true,
          "validations": {
            "minLength": 5
          }
        },
        {
          "type": "input-text",
          "placeholder": "请输入长度不少于8的文本",
          "name": "group-input3",
          "required": true,
          "validations": {
            "minLength": 8
          }
        }
      ]
    }
  ]
}
```

### 属性表

| 属性名           | 类型                                      | 默认值   | 说明                                                  | 版本    |
| ---------------- | ----------------------------------------- | -------- | ----------------------------------------------------- | ------- |
| className        | `string`                                  |          | CSS 类
名                                              |         |
| prefix        | `string`                                  |          |前缀                                              |   `1.57.0`      |
| prefixName        | `string`                                  |          |前缀随表单提交时表单项name                                              |   `1.60.0`      |
| suffix        | `string`                                  |          | 后缀                                              |    `1.57.0`        |
| suffixName        | `string`                                  |          |后缀随表单提交时表单项name                                              |   `1.60.0`      |
| body             | Array<[表单项](/dataseeddesigndocui/#/amis/zh-CN/components/form/formitem)>               |          | 表单项集合                                            |         |
| validationConfig | `Record<any, any>` | -        | 校验相关配置, 具体配置属性如下                        |
| validationConfig.errorMode       | `"full" \| "partial"`                     | `"full"` | 错误提示风格, `full`整体飘红, `partial`仅错误元素飘红 |
| validationConfig.delimiter       | `string`                                  | `"; "`   | 单个子元素多条校验信息的分隔符                        |
| validationConfig.errorMsgIndex       | `boolean \| string`                                  | `true`   | 配置错误信息的索引格式。默认显示格式 `"(1)"`                        | `1.14.0` |
