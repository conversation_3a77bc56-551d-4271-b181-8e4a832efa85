---
title: InputPassword 密码输入框
description:
type: 0
group: null
menuName: InputPassword
icon:
order: 35
---

## 基本用法

```schema: scope="body"
{
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "body": [
        {
            "type": "input-password",
            "name": "password",
            "label": "密码"
        }
    ]
}
```

## 配置密码显/隐藏

`revealPassword`属性可以设置是否展示密码显/隐按钮。不配置时，在编辑模式下默认为`true`，在 `static` 静态展示模式下，默认值为 `false`。

如果需要在某些条件下动态展示“显隐密码按钮”，可配置 `revealPasswordOn:${xx}` 使用表达式来控制。

```schema: scope="body"
{
    "type": "form",
    "mode": "horizontal",
    "api": "/api/mock2/form/saveForm",
    "data": {
        "password": "mypassword",
        "revealPassword": false,
    },
    "body": [
        {
            "type": "input-password",
            "name": "password",
            "label": "密码",
        },
        {
            "type": "input-password",
            "name": "password",
            "label": "密码",
            "static": true,
        },
        {
            "type": "divider",
        },
        {
            "name": "revealPassword",
            "type": "switch",
            "label": "显隐密码按钮",
            "onText": "显示",
            "offText": "隐藏" 
        },
        {
            "type": "input-password",
            "name": "password",
            "label": "密码",
            "revealPasswordOn": '${revealPassword}'
        },
        {
            "type": "input-password",
            "name": "password",
            "label": "密码",
            "static": true,
            "revealPasswordOn": '${revealPassword}'
        }
    ]
}
```

## 属性表

请参考[输入框](/dataseeddesigndocui/#/amis/zh-CN/components/form/input-text)

| 属性名         | 类型      | 默认值 | 说明                  |
| -------------- | --------- | ------ | --------------------- |
| revealPassword | `boolean` | `true` | 是否展示密码显/隐按钮 |

## 事件表

请参考[输入框](/dataseeddesigndocui/#/amis/zh-CN/components/form/input-text)

## 动作表

请参考[输入框](/dataseeddesigndocui/#/amis/zh-CN/components/form/input-text)
