---
title: InputRichText 富文本编辑器
description:
type: 0
group: null
menuName: InputRichText
icon:
order: 47
---

目前富文本编辑器基于 [tinymce](https://github.com/tinymce/tinymce)。

## 基本用法

```schema: scope="body"
{
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "body": [
        {
            "type": "input-rich-text",
            "name": "rich",
            "label": "Rich Text"
        }
    ]
}
```

## 图片上传

通过设置 `receiver` 来支持文件上传，图片将会放在 `file` 字段中

> 可通过 fileField 字段修改

它的返回值类似如下：

```json
{
  "link": "https://xxx.png"
}
```

也可以是

```json
{
  "status": 0,
  "data": {
    "link": "https://xxx.png"
  }
}
```

下面是个示例，但不会真正上传，每次都返回同一张图片

```schema: scope="body"
{
    "type": "form",
    "body": [
        {
            "type": "input-rich-text",
            "receiver": "/api/mock2/sample/mirror?json={%22link%22:%22/amis/static/logo_c812f54.png%22}",
            "name": "rich",
            "label": "Rich Text"
        }
    ]
}
```

## 关闭上传图片

可以通过`uploadImage`来控制是否需要上出图片，不上传的话默认会将图片转换为`base64`

```schema: scope="body"
{
    "type": "form",
    "body": [
        {
            "type": "input-rich-text",
            "receiver": "/api/mock2/sample/mirror?json={%22link%22:%22/amis/static/logo_c812f54.png%22}",
            "name": "rich",
            "uploadImage": false,
            "label": "Rich Text"
        }
    ]
}
```

## 图片直传 OSS 模式

```schema: scope="body"
{
    "type": "form",
    "body": [
        {
            "type": "input-rich-text",
            "name": "rich",
            "uploadMode": "oss",
            "label": "Rich Text",
            "receiver": {
              "url": '', // 'publicNext' | 'privateNext' | 'privateOverride'
              "data": {
                "menu": 'vipshipmgrui/v/#/tying',
                "prefix": '',
              },
            },
        }
    ]
}
```

## tinymce 自定义配置

可以设置 options 属性来自定义编辑器的展现，详细配置项请参考[官方文档](https://www.tiny.cloud/docs/tinymce/6/basic-setup/)。

> amis 所使用版本为 tinymce 6

注意在下面的编辑器里修改 JSON 配置后不会实时生效。

```schema: scope="body"
{
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "body": [
        {
            "type": "input-rich-text",
            "name": "rich",
            "options": {
                "menubar": false,
                "height": 200,
                "plugins": [
                    "advlist", "autolink", "link", "image", "lists", "charmap", "preview", "anchor", "pagebreak", "searchreplace", "wordcount", "visualblocks", "visualchars", "code", "fullscreen", "insertdatetime", "media", "nonbreaking", "table", "emoticons", "template", "help"
                ],
                "toolbar": "undo redo | formatselect | bold italic backcolor  | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help"
            }
        }
    ]
}
```

## 关于 tinymce 粘贴 word 的问题

因为 amis 中使用的是开源版本 tinymce，没有商业版本功能，导致比如从 Word 中粘贴表格会看不到边框，解决方法是自己

```json
{
  "type": "input-rich-text",
  "name": "rich",
  "options": {
    "content_css": "/xxx.css"
  }
}
```

比如下面的示例

```css
.mce-item-table th {
  font-weight: bold;
}
.mce-item-table th,
.mce-item-table td {
  padding: 6px 13px;
  border: 1px solid #ddd;
}
.mce-item-table tr {
  border-top: 1px solid #ccc;
}
```

但最终页面渲染的时候，这个 class 没有了，得改成 table

```css
table th {
  font-weight: bold;
}
table th,
table td {
  padding: 6px 13px;
  border: 1px solid #ddd;
}
table tr {
  border-top: 1px solid #ccc;
}
```

## 预览模式 

```schema: scope="body"
{
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "body": [
        {
            "type": "input-rich-text",
            "name": "rich",
            "label": false,
            "value": "<h1>欢迎来到示例 HTML 页面</h1><p>这是一个包含多种 HTML 元素的示例页面，用于演示 HTML 的各种功能。</p><h2>标题和段落</h2><p>这是第一个段落。<span style=\"background-color: rgb(241, 196, 15);\">段落通常用于显示文本内容</span>。</p>",
            "previewMode": true
        }
    ]
}
```

也可以设置放大器

```schema: scope="body"
{
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "body": [
        {
            "type": "input-rich-text",
            "name": "rich",
            "label": false,
            "value": "<h1>欢迎来到示例 HTML 页面</h1><p>这是一个包含多种 HTML 元素的示例页面，用于演示 HTML 的各种功能。</p><h2>标题和段落</h2><p>这是第一个段落。<span style=\"background-color: rgb(241, 196, 15);\">段落通常用于显示文本内容</span>。</p>",
            "previewMode": true,
            "previewTrigger": {
              "level": "link"
            }
        }
    ]
}
```

## 属性表

当做选择器表单项使用时，除了支持 [普通表单项属性表](/dataseeddesigndocui/#/amis/zh-CN/components/form/formitem#%E5%B1%9E%E6%80%A7%E8%A1%A8) 中的配置以外，还支持下面一些配置

| 属性名        | 类型                           | 默认值 | 说明  | 版本                                                                                                                                                  |
| ------------- | ------------------------------ | ------ | ------------------------------------------------------------------------------------------------------------------------------------------------------- | -------- |
| saveAsUbb     | `boolean`                      |        | 是否保存为 ubb 格式                                                                                                                                     |
| receiver      | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api) |        | 默认的图片保存API，当开启阿里云 OSS 直传时，需要配置此项为 ApiOptions 格式，更多要求参看下文                                                                                                                                      |
| videoReceiver | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api) |        | 默认的视频保存 API                                                                                                                                      |
| fileField     | string                         |        | 上传文件时的字段名                                                                                                                                      |
| size          | `string`                       |        | 框的大小，可设置为 `md` 或者 `lg`                                                                                                                       |
| options       | `object`                       |        | 需要参考 [tinymce](https://www.tiny.cloud/docs/configure/integration-and-setup/) 的文档，如配置显示的按钮，可以通过 options 设置 [toolbar](https://www.tiny.cloud/docs/demo/custom-toolbar-button/) 字符串 |
| uploadMode     | string                         |   -     | 直传阿里云 oss 时配置此项为 'oss'                                                                                                                                     | 1.14.0 |
| uploadImage     | `boolean`                         |   `true`    | 是否上传图片, 配置false会将图片转换为base64格式                                                                                                                                     | 1.63.0 |
| previewMode    | `boolean` | `false` | 是否开启预览模式 | 1.66.0 |
| previewTrigger    | `ButtonSchema` | `-` | 放大触发器 | 1.66.0 |

### 直传 OSS 模式 receiver 属性

receiver 详细属性请参照 [@dataseed/cdn-cloud](http://gitlab.caijj.net/yanfaerbu/qianduan/utils/-/tree/master/packages/cdn-cloud) 使用方法

## 事件表

> `[name]`表示当前组件绑定的名称，即`name`属性，如果没有配置`name`属性，则通过`value`取值。

| 事件名称 | 事件参数 | 说明 | 版本 |
| -------- | -------- | ---- | ---- |
| change | `[name]: string` | 值变化时触发 | 1.63.0 |
| focus | `[name]: string` | 获得焦点时触发 | 1.63.0 |
| blur | `[name]: string` | 失去焦点时触发 | 1.63.0 |
