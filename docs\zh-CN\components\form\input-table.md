---
title: InputTable 表格
description:
type: 0
group: null
menuName: InputTable 表格
icon:
order: 54
---

用于数组类型的数据增删改操作

<font color=red>**注意事项：**</font>
1. input-table 的column配置的type必须是表单组件（纯展示的column除外），对应文档“数据输入”中的组件。如果一个单元格中需要展示多个表单组件，可以将column的type设置为input-group。

## 场景推荐

### 基本使用

通常对数组类型的数据做新增、修改、删除等操作时，使用该场景

```schema
{
  "type": "page",
  "data": {
    "table": [
      {
        "a": "a1",
        "b": "b1",
        "c": {
          "c1": "123",
          "c2": "222"
        }
      }
    ],
    "title": "获取衍生特征值时，需传入以下参数，请赋值:"
  },
  "body": {
    "title": "",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
    "actions": [],
    "body": [
      {
        "type": "input-table",
        "name": "table",
        "label": "特征参数",
        "addable": true,
        "removable": true,
        "editable": true,
        "columns": [
          {
            "name": "a",
            "label": "A"
          },
          {
            "name": "b",
            "label": "B"
          },
          {
            "type": "input-group",
            "label": "C",
            "body": [
              {
                "type": "combo",
                "name": "c",
                "multiLine": true,
                "multiple": false,
                "label": false,
                "required": true,
                "items": [
                  {
                    "type": "input-text",
                    "name": "c1",
                    "required": true,
                    "label": false
                  },
                  {
                    "type": "input-text",
                    "name": "c2",
                    "required": true,
                    "label": false
                  }
                ]
              }
            ]
          }
        ]
      }
    ],
    "type": "form"
  }
}
```
- 落地案例  
 [大数据一站式-实时计算平台-元数据管理](http://moka.dmz.sit.caijj.net/hydraui/#/create-json?type=add)  
 ![大数据一站式-实时计算平台-元数据管理](https://static02.sit.yxmarketing01.com/materialcenter/8cb613c7-f155-45fc-8ecb-986f937a75ca.jpg)
### 带全局操作按钮和搜索项

针对表格需要添加全局操作按钮可放在表格左上方位置，过滤列表数据搜索框放在表格右上方，点击搜索icon触发下方表格数据更新，同时维护一份全量数据

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "id": "myForm",
    "data": {
      "originTableData": [
        {
          "eventType": "a",
          "sourceName": "dwa.dwa_risk_wewewewe_rwewrwer1",
          "text1": "t1",
          "text2": "t2",
          "id": "a"
        }
      ]
    },
    "body": {
      "label": "触发事件",
      "type": "group",
      "body": [
        {
          "type": "flex",
          "gap": true,
          "direction": "column",
          "items": [
            {
              "type": "flex",
              "justify": "space-between",
              "items": [
                {
                  "type": "button",
                  "label": "功能按钮",
                  "level": "primary",
                  "rightIcon": "remark",
                  "rightIconTooltip": {
                    "tooltipPlacement": "top",
                    "content": "提示文案",
                    "tooltipTheme": "dark"
                  },
                },
                {
                  "type": "search-box",
                  "name": "keywords",
                  "onEvent": {
                    "search": {
                      "actions": [
                        // 此处需要将input-table的页码重置为1，否则在非第一页，搜索过滤后，再点击新增，会新增多条空白行
                        {
                          "componentId": "myTable",
                          "actionType": "changePage",
                          "args": {
                            "page": 1
                          }
                        },
                        // 需要将search-box的值更新到form数据域里，供下方formula过滤数据时使用
                        {
                          "actionType": "setValue",
                          "componentId": "myForm",
                          "args": {
                            "value": {
                              "keywords": "${event.data.keywords}"
                            }
                          }
                        },
                        {
                          // 如果不加这个动作，使用formula的autoSet：true的话，在下面这种场景下会有问题：
                          // keywords输入值搜索后，在input-table里新增一行数据，并且输入的值不符合当前keywords过滤条件，这个时候再点击搜索icon，input-table展示数据不正确
                          // formula组件在站点文档中未暴露动作，但在实际代码中，实现了doAction，不管传入任何actionType，都会调用自身的onChange重新计算值
                          "actionType": "change",
                          "componentId": "filterTableDataFormula"
                        }
                      ]
                    }
                  }
                }
              ]
            },
            {
              "type": "input-table",
              "id": "myTable",
              "perPage": 5,
              "name": "filterTableData",
              "required": true,
              "editable": true,
              "removable": true,
              "needConfirm": false,
              "columns": [
                {
                  "label": "事件类型",
                  "name": "eventType"
                },
                {
                  "label": "准实时数据源表",
                  "name": "sourceName",
                  "width": "20vw",
                  "required": true,
                  "quickEdit": {
                    "type": "select",
                    "placeholder": "请选择数据表",
                    "required": true,
                    "popOverContainerSelector": "body",
                    "validations": {
                      "isRequired": true
                    },
                    "validationErrors": {
                      "isRequired": "请选择数据表"
                    },
                    "options": [
                      {
                        "label": "dwa.dwa_risk_wewewewe_rwewrwer1",
                        "value": "dwa.dwa_risk_wewewewe_rwewrwer1"
                      },
                      {
                        "label": "dwa.dwa_risk_wewewewe_rwewrwer2",
                        "value": "dwa.dwa_risk_wewewewe_rwewrwer2"
                      },
                      {
                        "label": "dwa.dwa_risk_wewewewe_rwewrwer3",
                        "value": "dwa.dwa_risk_wewewewe_rwewrwer3"
                      },
                      {
                        "label": "dwa.dwa_risk_wewewewe_rwewrwer4",
                        "value": "dwa.dwa_risk_wewewewe_rwewrwer4"
                      }
                    ]
                  }
                },
                {
                  "label": "过滤条件表达式",
                  "required": true,
                  "quickEdit": {
                    "type": "input-text",
                    "required": true,
                    "placeholder": "请输入表达式，例如EVENT.param1='test'"
                  },
                  "name": "text1"
                }
              ],
              "label": false,
              "footerToolbarClassName": "flex-1 pr-4",
              "showFooterAddBtn": false,
              "extraFooterConfig": {
                "type": "flex",
                "direction": "column",
                "alignItems": "start",
                "items": [
                  {
                    "type": "tpl",
                    "tpl": "总件数：<strong>${originTableData.length}件</strong>"
                  },
                  {
                    "type": "flex",
                    "items": [
                      {
                        "type": "button",
                        "level": "primary",
                        "size": "sm",
                        "label": "新增",
                        "icon": "fa fa-plus",
                        "className": " mr-2 pm-button-mt",
                        "onEvent": {
                          "click": {
                            "actions": [
                              {
                                "componentId": "myTable",
                                "actionType": "addItem",
                                "args": {
                                  "index": "${originTableData.length}",
                                  "item": [
                                    {
                                      "id": "${TIMESTAMP(NOW(), 'x')}"
                                    }
                                  ]
                                }
                              }
                            ]
                          }
                        }
                      }
                    ]
                  }
                ]
              },
              "onEvent": {
                "add": {
                  "actions": [
                    {
                      "componentId": "input-table-id",
                      "actionType": "setValue",
                      "args": {
                        "index": "${event.data.index}",
                        "value": {
                          "id": "${TIMESTAMP(NOW(), 'x')}"
                        }
                      }
                    }
                  ]
                },
                "change": {
                  "debounce": {
                    "wait": 500
                  },
                  "actions": [
                    {
                      "actionType": "setValue",
                      "componentId": "myForm",
                      "args": {
                        "value": {
                          "originTableData":
                        `\${UNIQ(CONCAT(filterTableData, originTableData), 'id')
                          }`
                        }
                      }
                    }
                  ]
                },
                "delete": {
                  "actions": [
                    {
                      "actionType": "setValue",
                      "componentId": "myForm",
                      "args": {
                        "value": {
                          "originTableData":
                        `\${ARRAYFILTER(originTableData, row => row['id'
                            ] !== event.data.item['id'
                            ])
                          }`
                        }
                      }
                    }
                  ]
                }
              }
            },
            {
              "type": "formula",
              "name": "filterTableData",
              "id": "filterTableDataFormula",
              "autoSet": false,
              "formula": '${keywords ? ARRAYFILTER(originTableData, row => row.eventType === keywords) : originTableData}',
            }
          ]
        }
      ]
    }
  }
}
```
- 落地案例  
 [大数据一站式-实时计算平台-元数据管理](http://moka.dmz.sit.caijj.net/hydraui/#/create-json?type=add)  
 ![大数据一站式-实时计算平台-元数据管理](https://static02.sit.yxmarketing01.com/materialcenter/8cb613c7-f155-45fc-8ecb-986f937a75ca.jpg)
## 组件用法
### 显示序号

```schema: scope="body"
{
  "type": "form",
  "data": {
    "table": [
        {
            "a": "a1",
            "b": "b1"
        },
        {
            "a": "a2",
            "b": "b2"
        },
        {
            "a": "a3",
            "b": "b3"
        }
    ]
  },
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type":"input-table",
      "name":"table",
      "label":false,
      "showIndex": true,
      "columns":[
          {
            "name": "a",
            "label": "A"
          },
          {
            "name": "b",
            "label": "B"
          }
      ]
    }
  ]
}
```

### 嵌套子表格

与主表格共用一个表头, value 数据中存在 children，通过`valueField`来指定行唯一 key, 否则嵌套子表格在编辑输入框时会失焦

```schema: scope="body"
{
  "type": "form",
  "mode": "normal",
  "data": {
    "table": [
        {
          "a": "a1",
          "b": "b1",
          "id": 1,
          "children": [
            {
              "a": "a1-child1",
              "b": "b1-child1",
              "id": "1-1"
            },
            {
              "a": "a1-child2",
              "b": "b1-child2",
              "id": "1-2"
            }
          ]
        },
        {
          "a": "a2",
          "b": "b2",
          "id": 2,
          "children": [
            {
              "a": "a1-child1",
              "b": "b1-child1",
              "id": "2-1"
            },
            {
              "a": "a1-child2",
              "b": "b1-child2",
              "id": "2-2"
            }
          ]
        },
        {
          "a": "a3",
          "b": "b3",
          "id": 3,
          "children": [
            {
              "a": "a1-child1",
              "b": "b1-child1",
              "id": "3-1"
            },
            {
              "a": "a1-child2",
              "b": "b1-child2",
              "id": "3-2"
            }
          ]
        }
      ]
  },
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type":"input-table",
      "name":"table",
      "label":false,
      "valueField": "id",
      "columns":[
          {
            "name": "id",
            "label": "序号"
          },
          {
            "name": "a",
            "label": "A",
            "type": "input-text"
          },
          {
            "name": "b",
            "label": "B",
            "type": "input-text"
          }
      ]
    }
  ]
}
```

子表格有独立的表头, 可通过配置`subTable`来实现，value 数据中存在 children，通过`valueField`来指定行唯一 key, 否则嵌套子表格在编辑输入框时会失焦

```schema: scope="body"
{
  "type": "form",
  "mode":"nomal",
  "debug": true,
  "data": {
    "table": [
        {
            "a": "a1",
            "b": "b1",
            "id": "1",
            "children": [
              {
                "a": "a1-child1",
                "b": "b1-child1",
                "id": "1-1"
              },
              {
                "a": "a1-child2",
                "b": "b1-child2",
                "id": "1-2"
              }
            ]
        },
        {
            "a": "a2",
            "b": "b2",
            "id": "2",
            "children": [
              {
                "a": "a2-child1",
                "b": "b2-child1",
                "id": "2-1"
              }
            ]
        },
        {
            "a": "a3",
            "b": "b3",
            "id": "3",
            "children": [
              {
                "a": "a3-child1",
                "b": "b3-child1",
                "id": "3-1"
              },
              {
                "a": "a3-child2",
                "b": "b3-child2",
                "id": "3-2"
              },
              {
                "a": "a3-child3",
                "b": "b3-child3",
                "id": "3-3"
              }
            ]
        }
    ]
  },
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type":"input-table",
      "name":"table",
      "label":false,
      "valueField": "id",
      "expandConfig": {
        "expand": "first"
      },
      "columns":[
          {
            "name": "id",
            "label": "序号"
          },
          {
            "name": "a",
            "label": "A",
            "type": "input-text"
          },
          {
            "name": "b",
            "label": "B",
            "type": "input-text"
          }
      ],
      "subTable": {
        "valueField": "id",
        "type": "input-table",
        "columns": [
          {
            "name": "id",
            "label": "序号"
          },
          {
            "name": "a",
            "label": "A",
            "type": "input-text"
          },
          {
            "name": "b",
            "label": "B",
            "type": "input-text"
          }
        ]
      },
      "onEvent": {
        "change": {
          "actions": [{
            "actionType": "custom",
            "script": "console.log(event.data, 'event')"
          }]
        }
      }
    }
  ]
}
```

### 可新增行

可以配置`addable`和`editable`指定可以新增且编辑行数据

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
    "type":"input-table",
    "name":"table",
    "addable": true,
    "label": false,
    "editable": true,
    "columns":[
        {
          "name": "a",
          "label": "A"
        },
        {
          "name": "b",
          "label": "B"
        }
    ]
  }
  ]
}
```

### 可复制新增行

还能通过 `copyable` 来增加一个复制按钮来复制当前行

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
    "type":"input-table",
    "name":"table",
    "label":false,
    "addable": true,
    "copyable": true,
    "editable": true,
    "value": [
      {
        "a": "a1",
        "b": "b1"
      }
    ],
    "columns":[
        {
          "name": "a",
          "label": "A"
        },
        {
          "name": "b",
          "label": "B"
        }
    ]
  }
  ]
}
```

### 配置按钮为文字

可以通过对应的 `BtnLabel` 及 `BtnIcon` 来改成显示文字而不是图标

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
    "type":"input-table",
    "name":"table",
    "addable": true,
    "addBtnLabel": "添加",
    "addBtnIcon": false,
    "copyable": true,
    "copyBtnLabel": "复制",
    "copyBtnIcon": false,
    "editable": true,
    "editBtnLabel": "编辑",
    "editBtnIcon": false,
    "label":false,
    "value": [
      {
        "a": "a1",
        "b": "b1"
      }
    ],
    "columns":[
        {
          "name": "a",
          "label": "A"
        },
        {
          "name": "b",
          "label": "B"
        }
    ]
  }
  ]
}
```

### 按钮触发新增行

按钮上配置`"actionType": "addItem"`和`target`指定表格`name`，可以实现点击按钮添加一行的效果。

```schema: scope="body"
{
  "type": "form",
  "data": {
    "table": [
      {
        "a": "a1",
        "b": "b1"
      },
      {
        "a": "a2",
        "b": "b2"
      },
      {
        "a": "a3",
        "b": "b3"
      }
    ]
  },
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "input-table",
      "name": "table",
      "label": "Table",
      "columns": [
        {
          "label": "A",
          "name": "a"
        },
        {
          "label": "B",
          "name": "b"
        }
      ]
    },
    {
      "type": "button",
      "label": "Table新增一行",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "addItem",
              "componentName": "table",
              "args": {
                "index": "${table.length}",
                "item": {}
              }
            }
          ]
        }
      }
    }
  ]
}
```

当表格上配置了`addApi`时，会请求该 `api`，并将返回数据添加到目标表格。

另外还可以配置`payload`，直接将数据添加到目标表格。

```schema: scope="body"
{
  "type": "form",
  "data": {
    "table": [
      {
        "a": "a1",
        "b": "b1"
      },
      {
        "a": "a2",
        "b": "b2"
      },
      {
        "a": "a3",
        "b": "b3"
      }
    ]
  },
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "input-table",
      "name": "table",
      "label": "Table",
      "columns": [
        {
          "label": "A",
          "name": "a"
        },
        {
          "label": "B",
          "name": "b"
        }
      ]
    },
    {
      "type": "button",
      "label": "Table新增一行",
      "target": "table",
      "actionType": "add",
      "payload": {
        "a": "a4",
        "b": "b4"
      }
    }
  ]
}
```

### 可编辑内容

每一列的都可以通过 type 来将其改造成可编辑的列，比如下面的例子（建议配合 `"needConfirm": false` 来改成非确认模式）

```schema: scope="body"
{
  "type": "form",
  "data": {
    "table": [
      {
        "a": "a1",
        "b": "b1"
      },
      {
        "a": "a2",
        "b": "b2"
      },
      {
        "a": "a3",
        "b": "b3"
      }
    ]
  },
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "input-table",
      "name": "table",
      "label": "Table",
      "addable": true,
      "needConfirm": false,
      "columns": [
        {
          "label": "A",
          "name": "a",
          "type": "input-text"
        },
        {
          "label": "B",
          "name": "b",
          "type": "select",
          "options": [
            "b1", "b2", "b3"
          ]
        }
      ]
    }
  ]
}
```

除了上面的例子，还可以在列上配置`quickEdit`实现编辑配置，实现展现和编辑分离，更多配置参考 [快速编辑](/dataseeddesigndocui/#/amis/zh-CN/components/crud#%E5%BF%AB%E9%80%9F%E7%BC%96%E8%BE%91)

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "data": {
    "table": [
      {
        "a": "a1",
        "b": "b1",
        "c": {
          "name": "c1",
          "id": "C1"
        }
      },
      {
        "a": "a2",
        "b": "b2",
        "c": {
          "name": "c2",
          "id": "C2"
        }
      },
      {
        "a": "a3",
        "b": "b3",
        "c": {
          "name": "c3",
          "id": "C3"
        }
      }
    ]
  },
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "input-table",
      "name": "table",
      "label": "Table",
      "columns": [
        {
          "label": "A",
          "name": "a",
          "quickEdit": {
            "type": "select",
            "options": ["a1", "a2", "a3"]
          }
        },
        {
          "label": "B",
          "name": "b",
          "quickEdit": true
        },
        {
          "label": "C",
          "name": "c.name",
          "quickEdit": {
            "type": "select",
            "name": "c",
            "labelField": "name",
            "valueField": "id",
            "joinValues": false,
            "options": [
              {
                "name": "c1",
                "id": "C1"
              },
              {
                "name": "c2",
                "id": "C2"
              },
              {
                "name": "c3",
                "id": "C3"
              }
            ]
          }
        },
      ]
    }
  ]
}
```

### 显示分页

可以配置`perPage`属性设置一页显示多少条数据。如果不配置此属性，则不会显示分页器

```schema: scope="body"
{
  "type": "form",
  "data": {
    "table": [
        {
            "a": "a1",
            "b": "b1",
            "id": "1"
        },
        {
            "a": "a2",
            "b": "b2",
            "id": "2"
        },
        {
            "a": "a3",
            "b": "b3",
            "id": "3"
        },
        {
            "a": "a4",
            "b": "b4",
            "id": "4"
        },
        {
            "a": "a5",
            "b": "b5",
            "id": "5"
        },
        {
            "a": "a6",
            "b": "b6",
            "id": "6"
        }
    ],
    "selected": [{
      "a": "a1",
      "b": "b1"
    }],
  },
  "body": [
    {
      "showIndex": true,
      "type":"input-table",
       "label":false,
      "perPage": 5,
      "name":"table",
      "columns":[
          {
            "name": "a",
            "label": "A"
          },
          {
            "name": "b",
            "label": "B"
          }
      ],
      "onEvent": {
        "selectedChange": {
          "actions": [{
            "actionType": "custom",
            "script": "console.log(event.data, 'event')"
          }]
        }
      }
    }
  ]
}

```

可以配置`validateOnPageChange`属性设置切换分页时，是否校验当前页数据

```schema: scope="body"
{
  "type": "form",
  "data": {
    "table": [
        {
            "a": "",
            "b": "b1",
            "id": "1"
        },
        {
            "a": "a2",
            "b": "b2",
            "id": "2"
        },
        {
            "a": "a3",
            "b": "b3",
            "id": "3"
        },
        {
            "a": "a4",
            "b": "b4",
            "id": "4"
        },
        {
            "a": "a5",
            "b": "b5",
            "id": "5"
        },
        {
            "a": "a6",
            "b": "b6",
            "id": "6"
        }
    ],
    "selected": [{
      "a": "a1",
      "b": "b1"
    }],
  },
  "body": [
    {
      "showIndex": true,
      "type":"input-table",
      "perPage": 5,
      "addable": true,
      "needConfirm": false,
      "validateOnPageChange": true,
      "label":false,
      "name":"table",
      "columns":[
          {
            "name": "a",
            "label": "A",
            "type": "input-text",
            "required": true
          },
          {
            "name": "b",
            "label": "B",
            "type": "input-text",
            "required": true
          }
      ],
      "onEvent": {
        "selectedChange": {
          "actions": [{
            "actionType": "custom",
            "script": "console.log(event.data, 'event')"
          }]
        }
      }
    }
  ]
}

```

可以配置`paginationConfig` 属性设置分页容器的属性

```schema: scope="body"
{
  "type": "form",
  "data": {
    "table": [
        {
            "a": "a1",
            "b": "b1",
            "id": "1"
        },
        {
            "a": "a2",
            "b": "b2",
            "id": "2"
        },
        {
            "a": "a3",
            "b": "b3",
            "id": "3"
        },
        {
            "a": "a4",
            "b": "b4",
            "id": "4"
        },
        {
            "a": "a5",
            "b": "b5",
            "id": "5"
        },
        {
            "a": "a6",
            "b": "b6",
            "id": "6"
        }
    ],
    "selected": [{
      "a": "a1",
      "b": "b1"
    }],
  },
  "body": [
    {
      "showIndex": true,
      "type":"input-table",
       "label":false,
      "perPage": 5,
      "alwaysShowPagination": true,
      "paginationConfig":{
       "layout": "total,pager,perPage,go",
       "perPageAvailable": [
          5,
          10,
          15,
          20
        ]
      },
      "name":"table",
      "columns":[
          {
            "name": "a",
            "label": "A"
          },
          {
            "name": "b",
            "label": "B"
          }
      ],
    }
  ]
}

```

### 第一列可选择

> = 1.2.0 版本，可配置`selected`属性回显，搭配分页配置，切换分页会保留选择状态

```schema: scope="body"
{
  "type": "form",
  "data": {
    "table": [
        {
            "a": "a1",
            "b": "b1",
            "id": "1"
        },
        {
            "a": "a2",
            "b": "b2",
            "id": "2"
        },
        {
            "a": "a3",
            "b": "b3",
            "id": "3"
        },
        {
            "a": "a4",
            "b": "b4",
            "id": "4"
        },
        {
            "a": "a5",
            "b": "b5",
            "id": "5"
        },
        {
            "a": "a6",
            "b": "b6",
            "id": "6"
        }
    ],
    "selected": [{
      "a": "a1",
      "b": "b1"
    }],
  },
  "body": [
    {
      "showIndex": true,
      "type":"input-table",
      "name":"table",
      "selectable": true,
       "label":false,
      "multiple": true,
      "perPage": 4,
      "selected": "$selected",
      "valueField": "a",
      "columns":[
          {
            "name": "a",
            "label": "A"
          },
          {
            "name": "b",
            "label": "B"
          }
      ],
      "onEvent": {
        "selectedChange": {
          "actions": [{
            "actionType": "custom",
            "script": "console.log(event.data, 'event')"
          }]
        }
      }
    }
  ]
}
```

### 动态列

参考最佳实践：[input-table动态列](/dataseeddesigndocui/#/amis/zh-CN/bestpractices/inputTableDynamicColumns)


### 可拖拽

配置`"draggable": true`，实现可拖拽调整顺序

```schema: scope="body"
{
  "type": "form",
  "data": {
    "table": [
      {
        "a": "a1",
        "b": "b1"
      },
      {
        "a": "a2",
        "b": "b2"
      },
      {
        "a": "a3",
        "b": "b3"
      }
    ]
  },
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "input-table",
      "name": "table",
      "label": "Table",
      "draggable": true,
      "columns": [
        {
          "label": "A",
          "name": "a"
        },
        {
          "label": "B",
          "name": "b"
        }
      ]
    }
  ]
}
```

### 限制个数

可以配置 `minLength`和`maxLength` 配置 InputTable 可添加的条数限制。

当使用 `minLength,maxLength` 时，需要配合 `updateAllRows` 一起使用。

```schema: scope="body"
{
  "type": "form",
  "data": {
    "table": [
      {
        "a": "a1",
        "b": "b1"
      },
      {
        "a": "a2",
        "b": "b2"
      },
      {
        "a": "a3",
        "b": "b3"
      }
    ]
  },
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "input-table",
      "name": "table",
      "label": "Table",
      "minLength": 1,
      "maxLength": 5,
      "needConfirm": false,
      "addable": true,
      "removable": true,
      "columns": [
        {
          "label": "A",
          "name": "a",
          "quickEdit": false
        },
        {
          "label": "B",
          "name": "b"
        }
      ]
    }
  ]
}
```

也可以使用变量配置`minLength`和`maxLength`

```schema: scope="body"
{
  "type": "form",
  "data": {
    "table": [
      {
        "a": "a1",
        "b": "b1"
      },
      {
        "a": "a2",
        "b": "b2"
      },
      {
        "a": "a3",
        "b": "b3"
      }
    ],
    "minLength": 2,
    "maxLength": 4
  },
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "input-table",
      "name": "table",
      "label": "Table",
      "minLength": "${minLength}",
      "maxLength": "${maxLength}",
      "needConfirm": false,
      "addable": true,
      "removable": true,
      "columns": [
        {
          "label": "A",
          "name": "a",
          "quickEdit": false
        },
        {
          "label": "B",
          "name": "b"
        }
      ]
    }
  ]
}
```

### 非确认模式

配置`"needConfirm": false`，以实现新增**单行数据**时不需要确认即可提交到数据域。

```schema: scope="body"
{
  "type": "form",
  "data": {
    "table": [
      {
        "a": "a1",
        "b": "b1"
      },
      {
        "a": "a2",
        "b": "b2"
      },
      {
        "a": "a3",
        "b": "b3"
      }
    ]
  },
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "input-table",
      "name": "table",
      "label": "Table",
      "needConfirm": false,
      "addable": true,
      "removable": true,
      "columns": [
        {
          "label": "A",
          "name": "a",
          "quickEdit": false
        },
        {
          "label": "B",
          "name": "b"
        }
      ]
    }
  ]
}
```

### 获取父级数据

默认情况下，Table 内表达项无法获取父级数据域的数据，如下，我们添加 Table 表单项时，尽管 Table 内的文本框的`name`与父级数据域中的`super_text`变量同名，但是没有自动映射值。

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "mode": "horizontal",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
        "type": "input-text",
        "label": "父级文本框",
        "name": "super_text",
        "value": "123"
    },
    {
        "type": "input-table",
        "name": "list",
        "label": "不可获取父级数据",
        "addable": true,
        "needConfirm": false,
        "columns": [
            {
                "name": "super_text",
                "type": "text",
                "label": "A"
            }
        ]
    }
  ]
}
```

可以配置`"canAccessSuperData": true` 同时配置 `"strictMode": false` 开启此特性，如下，配置了该配置项后，添加 Table 的`text`表单项会初始会自动映射父级数据域的同名变量。需要注意的是，这里只会初始会映射，一旦修改过就是当前行数据为主了。也就是说，表单项类型的，只会起到初始值的作用。如果为非表单项则会同步更新，比如这个例子的第二列。同时非表单项字段可以用在表单项字段中做联动。

> 注意：非表单项的同步更新需要配置`"updateAllRows": true`才能生效

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "mode": "horizontal",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
        "type": "input-text",
        "label": "父级文本框",
        "name": "super_text",
        "value": "123"
    },
    {
        "type": "switch",
        "label": "父级勾选框",
        "name": "super_switch",
        "value": false
    },
    {
        "type": "input-table",
        "name": "list",
        "label": "可获取父级数据",
        "addable": true,
        "needConfirm": false,
        "canAccessSuperData": true,
        "strictMode": false,
        "updateAllRows": true,
        "value": [{}],
        "columns": [
            {
                "name": "super_text",
                "type": "text",
                "label": "表单项",
                "quickEdit": {
                  "disabledOn": "this.super_switch"
                }
            },

            {
                "name": "super_switch",
                "type": "status",
                "quickEdit": false,
                "label": "非表单项"
            }
        ]
    }
  ]
}
```

### 高亮行

通过 `rowClassNameExpr` 来添加类，比如下面的例子中，如果输入的内容是 `a` 则背景色为绿色`

```schema: scope="body"
{
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "input-table",
        "name": "table",
        "addable": true,
        "editable": true,
         "label":false,
        "rowClassNameExpr": "<%= data.a === 'a' ? 'bg-success' : '' %>",
        "columns": [
          {
            "name": "a",
            "label": "A"
          }
        ]
      }
    ]
  }
```

### 表单项校验

列信息 `columns` 的对应项为表单项时，可以设置表单项的校验规则，来实现对该项的校验，校验配置可以查看 [格式校验](/dataseeddesigndocui/#/amis/zh-CN/components/form/formitem#格式校验)

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "data": {
    "table": [
      {
        "input": 111,
        "select": "s1",
        "text": "text"
      },
      {}
    ]
  },
  "api": "https://3xsw4ap8wah59.cfc-execute.bj.baidubce.com/api/amis-mock/mock2/form/saveForm",
  "body": [
    {
      "type": "input-table",
      "name": "table",
      "label": "Table",
      "columns": [
        {
          "label": "数字输入",
          "name": "input",
          "type": "input-text",
          "placeholder": "请输入数字",
          "required": true,
          "validations": {
            "isNumeric": true
          },
          "validationErrors": {
            "isNumeric": "请输入数字"
          }
        },
        {
          "label": "选项",
          "name": "select",
          "type": "select",
          "required": true,
          "options": [
            "s1",
            "s2",
            "s3"
          ]
        },
        {
          "label": "普通文本",
          "name": "text"
        }
      ]
    }
  ]
}
```

确认模式下，新增、编辑保存时，会针对当前编辑行进行校验，确认模式默认开启

```schema: scope="body"
{
  "type": "form",
  "debug": true,
  "data": {
    "table": [
      {
        "a": "a1",
        "b": "b1",
        "c": {
          "name": "c1",
          "id": "C1"
        }
      },
      {
        "a": "a2",
        "b": "b2",
        "c": {
          "name": "c2",
          "id": "C2"
        }
      },
      {
        "a": "a3",
        "b": "b3",
        "c": {
          "name": "c3",
          "id": "C3"
        }
      }
    ]
  },
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "input-table",
      "name": "table",
      "label": "Table",
      "addable": true,
      "editable": true,
      "columns": [
        {
          "label": "A",
          "name": "a",
          "quickEdit": {
            "type": "select",
            "clearable": true,
            "required": true,
            "options": [
              "a1",
              "a2",
              "a3"
            ]
          }
        },
        {
          "label": "B",
          "name": "b",
          "quickEdit": true
        },
        {
          "label": "C",
          "name": "c.name",
          "quickEdit": {
            "type": "select",
            "name": "c",
            "labelField": "name",
            "valueField": "id",
            "joinValues": false,
            "options": [
              {
                "name": "c1",
                "id": "C1"
              },
              {
                "name": "c2",
                "id": "C2"
              },
              {
                "name": "c3",
                "id": "C3"
              }
            ]
          }
        }
      ]
    }
  ]
}
```

### 自定义配置额外的footer内容

extraFooterConfig 支持自定义配置额外的footer内容, 若底部新增按钮与自定义配置同时存在，底部新增按钮在前

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "data": {
    "table": [
      {
        "a": "a1",
        "b": "b1"
      },
      {
        "a": "a2",
        "b": "b2"
      },
      {
        "a": "a3",
        "b": "b2"
      },
      {
        "a": "a4",
        "b": "b4"
      },
      {
        "a": "a5",
        "b": "b5"
      },
      {
        "a": "a6",
        "b": "b6"
      }
    ]
  },
  "body": [
    {
      "type": "input-table",
      "name": "table",
       "label":false,
      "perPage": 5,
      "addable": true,
      "editable": true,
      "columns": [
        {
          "name": "a",
          "label": "A"
        },
        {
          "name": "b",
          "label": "B"
        }
      ],
      "extraFooterConfig": [
        {
          "type": "button",
          "label": "自定义按钮",
        },
        {
          "type": "button",
          "label": "弹个框",
          "actionType": "dialog",
          "dialog": {
            "title": "弹窗",
            "body": "这是一个简单弹窗"
          }
        }
      ]
    }
  ]
}
```

### 行操作按钮可单独控制是否展示

> 表达式内在使用运算符时注意左右留空格

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "data": {
      "table": [
        {
          "a": "a1",
          "b": "b1"
        },
        {
          "a": "a2",
          "b": "b2"
        },
        {
          "a": "a3",
          "b": "b2"
        },
        {
          "a": "a4",
          "b": "b4"
        },
        {
          "a": "a5",
          "b": "b5"
        }
      ]
    },
    "body": [
      {
        "type": "input-table",
        "name": "table",
         "label":false,
        "perPage": 5,
        "addable": true,
        "editable": true,
        "removable": true,
        "showAddChildrenBtn": true,
        "tableAddBtnVisibleOn": "${a === 'a1'}",
        "tableAddChildrenBtnVisibleOn": "${__depth <= 2}",
        "tableEditBtnVisibleOn": "${index === 1}",
        "tableDeleteBtnVisibleOn": "${index + offset === table.length - 1}",
        "columns": [
          {
            "name": "a",
            "label": "A"
          },
          {
            "name": "b",
            "label": "B"
          }
        ]
      }
    ]
  }
}
```

### 属性表

| 属性名                       | 类型                                                                  | 默认值          | 说明                                                                                                 | 版本    |
| ---------------------------- | --------------------------------------------------------------------- | --------------- | ---------------------------------------------------------------------------------------------------- | ------- |
| type                         | `string`                                                              | `"input-table"` | 指定为 Table 渲染器                                                                                  |
| addable                      | `boolean`                                                             | `false`         | 是否可增加一行                                                                                       |
| editable                     | `boolean`                                                             | `false`         | 是否可编辑                                                                                           |
| removable                    | `boolean`                                                             | `false`         | 是否可删除                                                                                           |
| showTableAddBtn              | `boolean`                                                             | `true`          | 是否显示表格操作栏添加按钮，前提是要开启可新增功能                                                   |
| showFooterAddBtn             | `boolean`                                                             | `true`          | 是否显示表格下方新增按钮，前提是要开启可新增功能                                                       |         |
| addApi                       | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api)               | -               | 新增时提交的 API                                                                                     |
| footerAddBtn                 | [SchemaNode](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode) | -               | 底部新增按钮配置                                                                                     |
| updateApi                    | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api)               | -               | 修改时提交的 API                                                                                     |
| deleteApi                    | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api)               | -               | 删除时提交的 API                                                                                     |
| addBtnLabel                  | `string`                                                              |                 | 增加按钮名称                                                                                         |
| addBtnIcon                   | `string`                                                              | `"plus"`        | 增加按钮图标                                                                                         |
| showAddChildrenBtn              | `boolean`                                                             | `false`          | 是否显示新增子级按钮，前提是要开启可新增功能                                             |`1.14.0` |
| addChildrenBtnIcon              | `string`                                                             |           | 新增子级按钮Icon                                                   |`1.62.0`|
| addChildrenBtnLabel              | `string`                                                             |           | 新增子级按钮名称                                                   |`1.14.0`|
| addAboveBtnLabel              | `string`                                                             |           | 向上添加同级按钮名称                                                   |`1.14.0`|
| addBelowBtnLabel              | `string`                                                             |           | 向下添加同级按钮名称                                                  |`1.14.0`|
| tableAddBtnVisibleOn              | `SchemaExpression`                                                             |           | 表格操作栏添加按钮是否展示[表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression)，`index`为当前页索引，`offset`为页面偏移，`__depth` 为当前行深度(从1开始)量                                                  |`1.52.0`|
| tableAddChildrenBtnVisibleOn              | `SchemaExpression`                                                             |           | 表格操作栏 添加子级 按钮是否展示[表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression)，`index`为当前页索引，`offset`为页面偏移量，`__depth` 为当前行深度(从1开始)                                              |`1.62.0`|
| tableEditBtnVisibleOn              | `SchemaExpression`                                                             |           | 表格操作栏编辑按钮是否展示[表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression)，`index`为当前页索引，`offset`为页面偏移量                                                  |`1.52.0`|
| tableDeleteBtnVisibleOn              | `SchemaExpression`                                                             |           | 表格操作栏删除按钮是否展示[表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression)，`index`为当前页索引，`offset`为页面偏移量                                                  |`1.52.0`|
| tableAddBtnImmediateAdd              | `boolean`                                                             |  `false`         | 点击表格操作栏添加按钮是否直接向下新增一行                                                  |`1.52.0`|
| copyBtnLabel                 | `string`                                                              |                 | 复制按钮文字                                                                                         |
| copyBtnIcon                  | `string`                                                              | `"copy"`        | 复制按钮图标                                                                                         |
| editBtnLabel                 | `string`                                                              | `""`            | 编辑按钮名称                                                                                         |
| editBtnIcon                  | `string`                                                              | `"pencil"`      | 编辑按钮图标                                                                                         |
| deleteBtnLabel               | `string`                                                              | `""`            | 删除按钮名称                                                                                         |
| deleteBtnIcon                | `string`                                                              | `"minus"`       | 删除按钮图标                                                                                         |
| confirmBtnLabel              | `string`                                                              | `""`            | 确认编辑按钮名称                                                                                     |
| confirmBtnIcon               | `string`                                                              | `"check"`       | 确认编辑按钮图标                                                                                     |
| cancelBtnLabel               | `string`                                                              | `""`            | 取消编辑按钮名称                                                                                     |
| cancelBtnIcon                | `string`                                                              | `"times"`       | 取消编辑按钮图标                                                                                     |
| needConfirm                  | `boolean`                                                             | `true`          | 是否需要确认操作，，可用来控控制表格的操作交互                                                       |
| canAccessSuperData           | `boolean`                                                             | `false`         | 是否可以访问父级数据，也就是表单中的同级数据，通常需要跟 strictMode 搭配使用                         |
| strictMode                   | `boolean`                                                             | `true`          | 为了性能，默认其他表单项项值变化不会让当前表格更新，有时候为了同步获取其他表单项字段，需要开启这个。 |
| minLength                    | `number`                                                              | `0`             | 最小行数量                                                                                           |
| maxLength                    | `number`                                                              | `Infinity`      | 最大行数量                                                                                           |
| perPage                      | `number`                                                              | -               | 每页展示几行数据，如果不配置则不会显示分页器                                                         |
| columns                      | `array`                                                               | []              | 列信息                                                                                               | `1.19.0` 版本支持配置变量
| columns[x].quickEdit         | `boolean` 或者 `object`                                               | -               | 配合 editable 为 true 一起使用                                                                       |
| columns[x].quickEditOnUpdate | `boolean` 或者 `object`                                               | -               | 可以用来区分新建模式和更新模式的编辑配置                                                             |
| selectable                   | `boolean`                                                             | `false`         | 支持勾选，使用该功能请配置 `valueField` 数据的唯一  key                                                                                             | `1.2.0` |
| multiple                     | `boolean`                                                             | `false`         | 勾选 icon 是否为多选样式`checkbox`， 默认为`radio`                                                   | `1.2.0` |
| valueField                   | `string`                                                              | `value`         | 表示行数据的唯一 key,                                                                                | `1.2.0` |
| selected                     | `string` 或 `Array`                                                   | []              | 勾选回显，常搭配`valueField`使用，否则行唯一 key 取值 undefined，则无法回显                          | `1.2.0` |
| mountAll                     | `boolean`                                                   | `false`              | 是否渲染表格所有内容，适用于表格嵌套时，折叠的子表格项也需要被校验的场景                          | `1.11.0` |
| validateOnPageChange                     | `boolean`                                                   | `false`              | 切换分页时，是否校验当前页数据                          | `1.14.1` |
| showIndex                     | `boolean`                                                   | `false`              | 是否显示序号                         |  |
| extraFooterConfig                     | [SchemaNode](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode)                                                   | -              | 支持自定义额外的footer配置                         | `1.32.0` |
| subTable                     | [SchemaNode](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode)                                                   | -              | 支持自定义嵌套子内容配置                         |  |
| showExpansionColumn                     | `boolean`                                                   | -              | 支持自表格嵌套时，能隐藏表格默认的展开/收起列置                         | `1.40.0` |
| updateAllRows                     | `boolean`                                                   | false              | true，则全量更新行数据；false，则走TableRow优化流程，只更新数据变更行                       | `1.46.0` |
| alwaysShowPagination           | `boolean`                                                               | `false`                         | 是否总是显示分页                                                                                         | `1.54.0` | 
| reUseRow           |  `match 或 boolean`                                                               |                         | InputTable 组件是否复用每行以提高性能，默认全复用，`match`为匹配复用，`true`为完全复用，`false`为不复用（不推荐）                                                                                       | `1.62.1` | 
| batchNum           |  `number`                                                               |                         | 分批渲染个数，配置后默认开启分批渲染                                                                                     | `1.73.2` | 
| paginationConfig           | `PaginationObject`       | -                      | 分页容器配置属性              |   `1.54.0`   |

### PaginationObject

`PaginationObject` 为 [Pagination](/dataseeddesigndocui/#/amis/zh-CN/components/pagination) 属性配置，但是不需要配置如下属性 `type`、`total`、`lastPage`、`activePage`。

### 事件表

当前组件会对外派发以下事件，可以通过 onEvent 来监听这些事件，并通过 actions 来配置执行的动作，在 actions 中可以通过${事件参数名}或${event.data.[事件参数名]}来获取事件产生的数据，详细查看事件动作。

[name]表示当前组件绑定的名称，即 name 属性，如果没有配置 name 属性，则通过 value 取值。

| 事件名称      | 事件参数                                                                                                                                                  | 说明                                                                 |版本|
| ------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------- |---|
| add           | `[name]: object[]` 列表记录                                                                                                                               | 点击左下角添加按钮 或 某一行右侧操作栏添加按钮时触发                 | `1.8.0` |    
| addConfirm    | `index: number` 新增行记录索引 <br /> `item: object` 新增行记录 <br/> `[name]: object[]`列表记录  |                                                        开启`needConfirm`，点击添加按钮，填入数据后点击“保存”按钮后触发      | `1.8.0` |          
| addSuccess    | `index: number` 新增行记录索引 <br /> `item: object` 新增行记录 <br/> `[name]: object[]`列表记录                                                          | 开启`needConfirm`并且配置`addApi`，点击“保存”后调用接口成功时触发    | `1.8.0` |    
| addFail       | `index: number` 新增行记录索引 <br /> `item: object` 新增行记录 <br/> `[name]: object[]`列表记录<br />`error: object` `addApi`请求失败后返回的错误信息    | 开启`needConfirm`并且配置`addApi`，点击“保存”后调用接口失败时触发    | `1.8.0` |    
| edit          | `index: number` 所在行记录索引 <br /> `item: object` 所在行记录 <br/> `[name]: object[]`列表记录                                                          | 点击某一行右侧操作栏“编辑”按钮时触发                                 | `1.8.0` |
| editConfirm   | `index: number` 所在行记录索引 <br /> `item: object` 所在行记录 <br/> `[name]: object[]`列表记录                                                          | 开启`needConfirm`，点击“编辑”按钮，填入数据后点击“保存”按钮后触发    |  `1.8.0` |
| editSuccess   | `index: number` 所在行记录索引 <br /> `item: object` 所在行记录 <br/> `[name]: object[]`列表记录                                                          | 开启`needConfirm`并且配置`updateApi`，点击“保存”后调用接口成功时触发 |  `1.8.0` |
| editFail      | `index: number` 所在行记录索引 <br /> `item: object` 所在行记录 <br/> `[name]: object[]`列表记录<br />`error: object` `updateApi`请求失败后返回的错误信息 | 开启`needConfirm`并且配置`updateApi`，点击“保存”后调用接口失败时触发 | `1.8.0` |
| delete        | `index: number` 所在行记录索引 <br /> `item: object` 所在行记录 <br/> `[name]: object[]`列表记录                                                          | 点击某一行右侧操作栏“删除”按钮时触发                                 | `1.8.0` |
| deleteSuccess | `index: number` 所在行记录索引 <br /> `item: object` 所在行记录 <br/> `[name]: object[]`列表记录                                                          | 配置了`deleteApi`，调用接口成功时触发                                | `1.8.0` |
| deleteFail    | `index: number` 所在行记录索引 <br /> `item: object` 所在行记录 <br/> `[name]: object[]`列表记录<br />`error: object` `deleteApi`请求失败后返回的错误信息 | 配置了`deleteApi`，调用接口失败时触发                                | `1.8.0` |
| change        | `[name]: object[]` 列表记录                                                                                                                               | 组件数据发生改变时触发                                               | `1.8.0` |

#### add

点击左下角添加按钮 或 某一行右侧操作栏添加按钮时触发。

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "data": {
    "table": [
      {
        "a": "a1",
        "b": "b1"
      }
    ]
  },
  "body": [
    {
      "showIndex": true,
      "type": "input-table",
      "name": "table",
       "label":false,
      "columns": [
        {
          "name": "a",
          "label": "A"
        },
        {
          "name": "b",
          "label": "B"
        }
      ],
      "addable": true,
      "needConfirm": false,
      "onEvent": {
        "add": {
          "actions": [
            {
              "actionType": "toast",
              "args": {
                "msgType": "info",
                "position": "top-right",
                "title": "add事件",
                "msg": "value: ${event.data.value | json}, index: ${event.data.index}"
              }
            }
          ]
        }
      }
    }
  ]
}
```

#### addConfirm

开启`needConfirm`，点击添加按钮，填入数据后点击“保存”按钮后触发。

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "data": {
    "table": [
      {
        "a": "a1",
        "b": "b1"
      }
    ]
  },
  "body": [
    {
      "showIndex": true,
      "type": "input-table",
       "label":false,
      "name": "table",
      "columns": [
        {
          "name": "a",
          "label": "A"
        },
        {
          "name": "b",
          "label": "B"
        }
      ],
      "addable": true,
      "needConfirm": true,
      "onEvent": {
        "addConfirm": {
          "actions": [
            {
              "actionType": "toast",
              "args": {
                "msgType": "info",
                "position": "top-right",
                "title": "addConfirm事件",
                "msg": "value: ${event.data.value | json}, item: ${event.data.item | json}"
              }
            }
          ]
        }
      }
    }
  ]
}
```

#### addSuccess

开启`needConfirm`并且配置`addApi`，点击“保存”后调用接口成功时触发。

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "data": {
    "table": [
      {
        "a": "a1",
        "b": "b1"
      }
    ]
  },
  "body": [
    {
      "showIndex": true,
      "type": "input-table",
       "label":false,
      "name": "table",
      "columns": [
        {
          "name": "a",
          "label": "A"
        },
        {
          "name": "b",
          "label": "B"
        }
      ],
      "addable": true,
      "needConfirm": true,
      "addApi": "/api/mock2/table/addSuccess",
      "onEvent": {
        "addSuccess": {
          "actions": [
            {
              "actionType": "toast",
              "args": {
                "msgType": "info",
                "position": "top-right",
                "title": "addSuccess事件",
                "msg": "value: ${event.data.value | json}, item: ${event.data.item | json}"
              }
            }
          ]
        }
      }
    }
  ]
}
```

#### addFail

开启`needConfirm`并且配置`addApi`，点击“保存”后调用接口失败时触发。

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "data": {
    "table": [
      {
        "a": "a1",
        "b": "b1"
      }
    ]
  },
  "body": [
    {
      "showIndex": true,
      "type": "input-table",
      "name": "table",
       "label":false,
      "columns": [
        {
          "name": "a",
          "label": "A"
        },
        {
          "name": "b",
          "label": "B"
        }
      ],
      "addable": true,
      "needConfirm": true,
      "addApi": "/api/mock2/table/addFail",
      "onEvent": {
        "addFail": {
          "actions": [
            {
              "actionType": "toast",
              "args": {
                "msgType": "info",
                "position": "top-right",
                "title": "addFail事件",
                "msg": "value: ${event.data.value | json}, error: ${event.data.error | json}"
              }
            }
          ]
        }
      }
    }
  ]
}
```

#### edit

点击某一行右侧操作栏“编辑”按钮时触发

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "data": {
    "table": [
      {
        "a": "a1",
        "b": "b1"
      }
    ]
  },
  "body": [
    {
      "showIndex": true,
      "type": "input-table",
       "label":false,
      "name": "table",
      "columns": [
        {
          "name": "a",
          "label": "A"
        },
        {
          "name": "b",
          "label": "B"
        }
      ],
      "needConfirm": true,
      "editable": true,
      "onEvent": {
        "edit": {
          "actions": [
            {
              "actionType": "toast",
              "args": {
                "msgType": "info",
                "position": "top-right",
                "title": "edit事件",
                "msg": "value: ${event.data.value | json}, item: ${event.data.item | json}"
              }
            }
          ]
        }
      }
    }
  ]
}
```

#### editConfirm

开启`needConfirm`，点击“编辑”按钮，填入数据后点击“保存”按钮后触发.

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "data": {
    "table": [
      {
        "a": "a1",
        "b": "b1"
      }
    ]
  },
  "body": [
    {
      "showIndex": true,
      "type": "input-table",
      "name": "table",
       "label":false,
      "columns": [
        {
          "name": "a",
          "label": "A"
        },
        {
          "name": "b",
          "label": "B"
        }
      ],
      "editable": true,
      "onEvent": {
        "editConfirm": {
          "actions": [
            {
              "actionType": "toast",
              "args": {
                "msgType": "info",
                "position": "top-right",
                "title": "editConfirm事件",
                "msg": "value: ${event.data.value | json}, item: ${event.data.item | json}"
              }
            }
          ]
        }
      }
    }
  ]
}
```

#### editSuccess

开启`needConfirm`并且配置`updateApi`，点击“保存”后调用接口成功时触发。

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "data": {
    "table": [
      {
        "id": 1,
        "a": "a1",
        "b": "b1"
      }
    ]
  },
  "body": [
    {
      "showIndex": true,
      "type": "input-table",
      "name": "table",
      "columns": [
        {
          "name": "a",
          "label": "A"
        },
        {
          "name": "b",
          "label": "B"
        }
      ],
      "editable": true,
      "needConfirm": true,
       "label":false,
      "updateApi": "/api/mock2/table/editSuccess",
      "onEvent": {
        "editSuccess": {
          "actions": [
            {
              "actionType": "toast",
              "args": {
                "msgType": "info",
                "position": "top-right",
                "title": "editSuccess事件",
                "msg": "value: ${event.data.value | json}, item: ${event.data.item | json}"
              }
            }
          ]
        }
      }
    }
  ]
}
```

#### editFail

开启`needConfirm`并且配置`updateApi`，点击“保存”后调用接口失败时触发。

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "data": {
    "table": [
      {
        "id": 1,
        "a": "a1",
        "b": "b1"
      }
    ]
  },
  "body": [
    {
      "showIndex": true,
      "type": "input-table",
      "name": "table",
       "label":false,
      "columns": [
        {
          "name": "a",
          "label": "A"
        },
        {
          "name": "b",
          "label": "B"
        }
      ],
      "editable": true,
      "needConfirm": true,
      "updateApi": "/api/mock2/table/editFail",
      "onEvent": {
        "editFail": {
          "actions": [
            {
              "actionType": "toast",
              "args": {
                "msgType": "info",
                "position": "top-right",
                "title": "editFail事件",
                "msg": "value: ${event.data.value | json}, error: ${event.data.error | json}"
              }
            }
          ]
        }
      }
    }
  ]
}
```

#### delete

点击某一行右侧操作栏“删除”按钮时触发。

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "data": {
    "table": [
      {
        "a": "a1",
        "b": "b1"
      }
    ]
  },
  "body": [
    {
      "showIndex": true,
      "type": "input-table",
       "label":false,
      "name": "table",
      "columns": [
        {
          "name": "a",
          "label": "A"
        },
        {
          "name": "b",
          "label": "B"
        }
      ],
      "removable": true,
      "needConfirm": false,
      "onEvent": {
        "delete": {
          "actions": [
            {
              "actionType": "toast",
              "args": {
                "msgType": "info",
                "position": "top-right",
                "title": "delete事件",
                "msg": "item: ${event.data.item | json}, index: ${event.data.index}"
              }
            }
          ]
        }
      }
    }
  ]
}
```

#### deleteSuccess

开启`needConfirm`并且配置`updateApi`，点击“保存”后调用接口成功时触发。

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "data": {
    "table": [
      {
        "id": 1,
        "a": "a1",
        "b": "b1"
      }
    ]
  },
  "body": [
    {
      "showIndex": true,
      "type": "input-table",
       "label":false,
      "name": "table",
      "columns": [
        {
          "name": "a",
          "label": "A"
        },
        {
          "name": "b",
          "label": "B"
        }
      ],
      "removable": true,
      "needConfirm": false,
      "deleteApi": "/api/mock2/table/deleteSuccess",
      "onEvent": {
        "deleteSuccess": {
          "actions": [
            {
              "actionType": "toast",
              "args": {
                "msgType": "info",
                "position": "top-right",
                "title": "deleteSuccess事件",
                "msg": "value: ${event.data.value | json}, item: ${event.data.item | json}"
              }
            }
          ]
        }
      }
    }
  ]
}
```

#### deleteFail

配置了`deleteApi`，调用接口失败时触发。

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "data": {
    "table": [
      {
        "id": 1,
        "a": "a1",
        "b": "b1"
      }
    ]
  },
  "body": [
    {
      "showIndex": true,
      "type": "input-table",
       "label":false,
      "name": "table",
      "columns": [
        {
          "name": "a",
          "label": "A"
        },
        {
          "name": "b",
          "label": "B"
        }
      ],
      "removable": true,
      "needConfirm": false,
      "deleteApi": "/api/mock2/table/deleteFail",
      "onEvent": {
        "deleteFail": {
          "actions": [
            {
              "actionType": "toast",
              "args": {
                "msgType": "info",
                "position": "top-right",
                "title": "deleteFail事件",
                "msg": "value: ${event.data.value | json}, error: ${event.data.error | json}"
              }
            }
          ]
        }
      }
    }
  ]
}
```

#### change

组件数据发生改变时触发。

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "data": {
    "table": [
      {
        "id": 1,
        "a": "a1",
        "b": "b1"
      }
    ]
  },
  "body": [
    {
      "showIndex": true,
      "type": "input-table",
      "name": "table",
      "columns": [
        {
          "name": "a",
          "label": "A"
        },
        {
          "name": "b",
          "label": "B"
        }
      ],
       "label":false,
      "addable": true,
      "onEvent": {
        "change": {
          "actions": [
            {
              "actionType": "toast",
              "args": {
                "msgType": "info",
                "position": "top-right",
                "title": "change事件",
                "msg": "value: ${event.data.value | json}"
              }
            }
          ]
        }
      }
    }
  ]
}
```

### 动作表

当前组件对外暴露以下特性动作，其他组件可以通过指定`actionType: 动作名称`、`componentId: 该组件id`来触发这些动作，动作配置可以通过`args: {动作配置项名称: xxx}`来配置具体的参数，详细请查看[事件动作]。

| 动作名称   | 动作配置                                                                                                                | 说明     | 版本                                                             |
| ---------- | ----------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------- | ---------- |
| addItem    | `item: object\|Array<object>` 添加的数据<br />`index: number` 指定添加的位置，如果未指定则在数据尾端插入 <br />`type: string` 新增同级类型 `above \| below`              | 在已有数据的基础上插入同级数据  | `1.8.0` |                                        |
| addChildrenItem    | `item: object\|Array<object>` 添加的数据<br />`subTableIndex: number` 父级数据的索引<br />`path: string` 层级路径               | 在已有数据的基础上插入子级数据  | `1.14.0` |                                        |
| deleteItem | `condition:` 删除条件[表达式]，用于支持批量删除的场景<br /> `index: number ` 指定删除哪一行数据                         | 删除某一行数据     | `1.8.0` |                                                      |
| setValue   | `value: object \| Array<object>` 替换的值<br /> `index?: number` 可选，替换第几行数据，如果没有指定，则替换全部表格数据 | 替换表格数据         | `1.8.0` |                                                    |
| clear      | -                                                                                                                       | 清空表格数据    | `1.8.0` |                                                         |
| reset      | -                                                                                                                       | 将表格数据重置为`resetValue`，若没有配置`resetValue`，则清空表格数据 | `1.8.0` |    
| changePage      | `page: number` 要跳转到的目标页码                                                                                                                     | 切换表格页码 | `1.14.1` | 
| setSelectedItems      | `items: Array<object> ` 期望勾选的列数据.（items为行数据）   | 自定义设置列勾选项。 依赖 `valueField`，需先定义行唯一key | `1.17.0` |    


#### addItem

```schema: scope="body"
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "data": {
      "table": [
        {
          "id": 1,
          "a": "a1",
          "b": "b1"
        },
        {
          "id": 2,
          "a": "a2",
          "b": "b2"
        },
        {
          "id": 3,
          "a": "a3",
          "b": "b3"
        },
        {
          "id": 4,
          "a": "a4",
          "b": "b4"
        },
        {
          "id": 5,
          "a": "a5",
          "b": "b5"
        }
      ]
    },
    "body": [
      {
        "type": "button-toolbar",
        "label": false,
        "buttons": [
          {
            "type": "button",
            "label": "新增一行（未指定添加位置）",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "componentId": "addItem-input-table",
                    "groupType": "component",
                    "actionType": "addItem",
                    "args": {
                      "item": [
                        {
                          "a": "a-noIndex",
                          "b": "b-noIndex"
                        }
                      ]
                    }
                  }
                ]
              }
            }
          },
          {
            "type": "button",
            "label": "新增一行（指定添加位置）",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "componentId": "addItem-input-table",
                    "groupType": "component",
                    "actionType": "addItem",
                    "args": {
                      "index": 3,
                      "item": [
                        {
                          "a": "a-index",
                          "b": "b-index"
                        }
                      ]
                    }
                  }
                ]
              }
            }
          },
        ]
      },
      {
        "type": "input-table",
        "name": "table",
        "label": "表格表单",
        "columns": [
          {
            "name": "a",
            "label": "A"
          },
          {
            "name": "b",
            "label": "B"
          }
        ],
        "addable": true,
        "footerAddBtn": {
          "label": "新增",
          "icon": "fa fa-plus",
          "hidden": true
        },
        "strictMode": true,
        "id": "addItem-input-table",
        "minLength": 0,
        "needConfirm": false,
        "showTableAddBtn": false
      }
    ]
  }
}
```

#### addChildrenItem

```schema: scope="body"
{
  "type": "page",
  "body": {
    "type": "form",
    "mode": "normal",
    "debug": true,
    "data": {
      "table": [
        {
          "a": "a1",
          "b": "b1",
          "id": "1",
          "children": [
            {
              "a": "a1-child1",
              "b": "b1-child1",
              "id": "1-1"
            },
            {
              "a": "a1-child2",
              "b": "b1-child2",
              "id": "1-2"
            }
          ]
        },
        {
          "a": "a2",
          "b": "b2",
          "id": "2",
          "children": [
            {
              "a": "a2-child1",
              "b": "b2-child1",
              "id": "2-1"
            }
          ]
        },
        {
          "a": "a3",
          "b": "b3",
          "id": "3",
          "children": [
            {
              "a": "a3-child1",
              "b": "b3-child1",
              "id": "3-1"
            },
            {
              "a": "a3-child2",
              "b": "b3-child2",
              "id": "3-2"
            },
            {
              "a": "a3-child3",
              "b": "b3-child3",
              "id": "3-3"
            }
          ]
        }
      ]
    },
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "flex",
        "direction":"column",
        "gap": true,
        "items": [
          {
            "type": "button",
            "label": "新增子级",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "componentId": "addItem-input-table_1-0",
                    "groupType": "component",
                    "actionType": "addChildrenItem",
                    "args": {
                      "item": {
                        "id": "000000",
                        "a": "000000",
                        "b": "000000"
                      },
                      "subTableIndex": 1,
                      "path": "1"
                    }
                  }
                ]
              }
            }
          },
          {
            "type": "input-table",
            "name": "table",
            "valueField": "id",
            "id": "addItem-input-table",
            "expandConfig": {
              "expand": "all",
            },
            "addable": true,
            "showTableAddBtn": true,
            "showAddChildrenBtn": true,
            "mountAll": true,
            "columns": [
              {
                "name": "id",
                "label": "序号"
              },
              {
                "name": "a",
                "label": "A",
                "type": "input-text"
              },
              {
                "name": "b",
                "label": "B",
                "type": "input-text"
              }
            ],
            "onEvent": {
              "change": {
                "actions": [
                  {
                    "actionType": "custom",
                    "script": "console.log(event.data, 'event')"
                  }
                ]
              }
            }
          }
        ]
      }
    ]
  }
}
```

#### deleteItem

```schema: scope="body"
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "button-toolbar",
        "label": false,
        "buttons": [
          {
            "type": "button",
            "label": "删除行（指定行号）",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "componentId": "deleteItem-input-table",
                    "groupType": "component",
                    "actionType": "deleteItem",
                    "args": {
                      "index": "1,2,3"
                    }
                  }
                ]
              }
            }
          },
          {
            "type": "button",
            "label": "删除行（指定条件表达式）",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "componentId": "deleteItem-input-table",
                    "groupType": "component",
                    "actionType": "deleteItem",
                    "args": {
                      "condition": "${a === 'a3' || b === 'b4'}"
                    }
                  }
                ]
              }
            }
          }
        ]
      },
      {
        "type": "input-table",
        "label": "表格表单",
        "id": "deleteItem-input-table",
        "name": "table",
        "columns": [
          {
            "name": "a",
            "label": "A"
          },
          {
            "name": "b",
            "label": "B"
          }
        ],
        "addable": true,
        "footerAddBtn": {
          "label": "新增",
          "icon": "fa fa-plus",
          "hidden": true
        },
        "strictMode": true,
        "minLength": 0,
        "needConfirm": false,
        "showTableAddBtn": false
      }
    ],
    "data": {
      "table": [
        {
          "id": 1,
          "a": "a1",
          "b": "b1"
        },
        {
          "id": 2,
          "a": "a2",
          "b": "b2"
        },
        {
          "id": 3,
          "a": "a3",
          "b": "b3"
        },
        {
          "id": 4,
          "a": "a4",
          "b": "b4"
        },
        {
          "id": 5,
          "a": "a5",
          "b": "b5"
        }
      ]
    }
  }
}
```

如果是嵌套的场景可以传`index: 一级索引.二级索引`的方式去删除，如下例子，想要删除第一行里的第二个行的数据，可以传`index: 0.1`

```schema: scope="body"
{
  "type": "page",
  "body": {
  "type": "form",
  "id": "form",
  "mode":"normal",
  "debug": true,
  "data": {
    "table": [
      {
        "a": "a1",
        "b": "b1",
        "id": "1",
        "children": [
          {
            "a": "a1-child1",
            "b": "b1-child1",
            "id": "1-1"
          },
          {
            "a": "a1-child2",
            "b": "b1-child2",
            "id": "1-2"
          }
        ]
      },
      {
        "a": "a2",
        "b": "b2",
        "id": "2",
        "children": [
          {
            "a": "a2-child1",
            "b": "b2-child1",
            "id": "2-1"
          }
        ]
      },
      {
        "a": "a3",
        "b": "b3",
        "id": "3",
        "children": [
          {
            "a": "a3-child1",
            "b": "b3-child1",
            "id": "3-1"
          },
          {
            "a": "a3-child2",
            "b": "b3-child2",
            "id": "3-2"
          },
          {
            "a": "a3-child3",
            "b": "b3-child3",
            "id": "3-3"
          }
        ]
      }
    ],
  },
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "button-toolbar",
      "label": false,
      "buttons": [{
          "type": "button",
          "label": "删除行（指定行号）",
          "onEvent": {
            "click": {
              "actions": [
                {
                  "componentId": "deleteItem-input-table1",
                  "groupType": "component",
                  "actionType": "deleteItem",
                  "args": {
                    "index": "0.1"
                  }
                }
              ]
            }
          }
        },
        {
          "type": "button",
          "label": "删除行（指定条件表达式）",
          "onEvent": {
            "click": {
              "actions": [
                {
                  "componentId": "deleteItem-input-table1",
                  "groupType": "component",
                  "actionType": "deleteItem",
                  "args": {
                    "condition": "${(a === 'a3' || b === 'b4')}"
                  }
                }
              ]
            }
          }
        }]
    },
    {
      "type": "input-table",
      "name": "table",
      "id": "deleteItem-input-table1",
      "valueField": "id",
      "expandConfig": {
        "expand": "all"
      },
      "columns": [
        {
          "name": "id",
          "label": "序号"
        },
        {
          "name": "a",
          "label": "A",
          "type": "input-text"
        },
        {
          "name": "b",
          "label": "B",
          "type": "input-text"
        }
      ],
      "subTable": {
        "valueField": "id",
        "type": "input-table",
        "columns": [
          {
            "name": "id",
            "label": "序号"
          },
          {
            "name": "a",
            "label": "A",
            "type": "input-text"
          },
          {
            "name": "b",
            "label": "B",
            "type": "input-text"
          }
        ]
      },
      "onEvent": {
        "change": {
          "actions": [
            {
              "actionType": "custom",
              "script": "console.log(event.data, 'event')"
            }
          ]
        }
      }
    }
  ]
}

}
```

#### setValue

```schema: scope="body"
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "button-toolbar",
        "label": false,
        "buttons": [
          {
            "type": "button",
            "label": "赋值",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "componentId": "setValue-input-table",
                    "groupType": "component",
                    "actionType": "setValue",
                    "args": {
                      "value": [
                        {
                          "a": "a-setValue1",
                          "b": "b-setValue1"
                        },
                        {
                          "a": "a-setValue2",
                          "b": "b-setValue2"
                        }
                      ]
                    }
                  }
                ]
              }
            }
          }]
      },
      {
        "type": "input-table",
        "label": "表格表单",
        "id": "setValue-input-table",
        "name": "table",
        "columns": [
          {
            "name": "a",
            "label": "A"
          },
          {
            "name": "b",
            "label": "B"
          }
        ],
        "addable": true,
        "footerAddBtn": {
          "label": "新增",
          "icon": "fa fa-plus",
          "hidden": true
        },
        "strictMode": true,
        "minLength": 0,
        "needConfirm": false,
        "showTableAddBtn": false
      }
    ],
    "data": {
      "table": [
        {
          "id": 1,
          "a": "a1",
          "b": "b1"
        },
        {
          "id": 2,
          "a": "a2",
          "b": "b2"
        },
        {
          "id": 3,
          "a": "a3",
          "b": "b3"
        },
        {
          "id": 4,
          "a": "a4",
          "b": "b4"
        },
        {
          "id": 5,
          "a": "a5",
          "b": "b5"
        }
      ]
    }
  }
}
```

#### clear

```schema: scope="body"
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "button-toolbar",
        "label": false,
        "buttons": [
          {
            "type": "button",
            "label": "清空",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "componentId": "clear-input-table",
                    "groupType": "component",
                    "actionType": "clear"
                  }
                ]
              }
            }
          }]
      },
      {
        "type": "input-table",
        "label": "表格表单",
        "id": "clear-input-table",
        "name": "table",
        "columns": [
          {
            "name": "a",
            "label": "A"
          },
          {
            "name": "b",
            "label": "B"
          }
        ],
        "addable": true,
        "footerAddBtn": {
          "label": "新增",
          "icon": "fa fa-plus",
          "hidden": true
        },
        "strictMode": true,
        "minLength": 0,
        "needConfirm": false,
        "showTableAddBtn": false
      }
    ],
    "data": {
      "table": [
        {
          "id": 1,
          "a": "a1",
          "b": "b1"
        },
        {
          "id": 2,
          "a": "a2",
          "b": "b2"
        },
        {
          "id": 3,
          "a": "a3",
          "b": "b3"
        },
        {
          "id": 4,
          "a": "a4",
          "b": "b4"
        },
        {
          "id": 5,
          "a": "a5",
          "b": "b5"
        }
      ]
    }
  }
}
```

#### reset

```schema: scope="body"
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "button-toolbar",
        "label": false,
        "buttons": [
          {
            "type": "button",
            "label": "重置",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "componentId": "reset-input-table",
                    "groupType": "component",
                    "actionType": "reset"
                  }
                ]
              }
            }
          }]
      },
      {
        "type": "input-table",
        "label": "表格表单",
        "id": "reset-input-table",
        "name": "table",
        "resetValue": [
          {
            "a": "a-resetValue1",
            "b": "b-resetValue1"
          },
          {
            "a": "a-resetValue2",
            "b": "b-resetValue2"
          }
        ],
        "columns": [
          {
            "name": "a",
            "label": "A"
          },
          {
            "name": "b",
            "label": "B"
          }
        ],
        "addable": true,
        "footerAddBtn": {
          "label": "新增",
          "icon": "fa fa-plus",
          "hidden": true
        },
        "strictMode": true,
        "minLength": 0,
        "needConfirm": false,
        "showTableAddBtn": false
      }
    ],
    "data": {
      "table": [
        {
          "id": 1,
          "a": "a1",
          "b": "b1"
        },
        {
          "id": 2,
          "a": "a2",
          "b": "b2"
        },
        {
          "id": 3,
          "a": "a3",
          "b": "b3"
        },
        {
          "id": 4,
          "a": "a4",
          "b": "b4"
        },
        {
          "id": 5,
          "a": "a5",
          "b": "b5"
        }
      ]
    }
  }
}
```

#### changePage

```schema: scope="body"
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "button-toolbar",
        "label": false,
        "buttons": [
          {
            "type": "button",
            "label": "跳转第2页",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "componentId": "change-page-input-table",
                    "actionType": "changePage",
                    "args": {
                      "page": 2
                    }
                  }
                ]
              }
            }
          }
        ]
      },
      {
        "type": "input-table",
        "label": "表格表单",
        "id": "change-page-input-table",
        "name": "table",
        "resetValue": [
          {
            "a": "a-resetValue1",
            "b": "b-resetValue1"
          },
          {
            "a": "a-resetValue2",
            "b": "b-resetValue2"
          }
        ],
        "columns": [
          {
            "name": "a",
            "label": "A"
          },
          {
            "name": "b",
            "label": "B"
          }
        ],
        "addable": true,
        "perPage": 5,
        "strictMode": true,
        "minLength": 0,
        "needConfirm": false,
        "showTableAddBtn": false
      }
    ],
    "data": {
      "table": [
        {
          "id": 1,
          "a": "a1",
          "b": "b1"
        },
        {
          "id": 2,
          "a": "a2",
          "b": "b2"
        },
        {
          "id": 3,
          "a": "a3",
          "b": "b3"
        },
        {
          "id": 4,
          "a": "a4",
          "b": "b4"
        },
        {
          "id": 5,
          "a": "a5",
          "b": "b5"
        },
        {
          "id": 6,
          "a": "a6",
          "b": "b6"
        }
      ]
    }
  }
}
```

#### setSelectedItems

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/amis-mock/mock2/form/saveForm",
    "body": [
      {
        "type": "button-toolbar",
        "label": false,
        "buttons": [{
            "type": "button",
            "label": "自定义列勾选项",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "componentId": "setSelectedItems-input-table",
                    "groupType": "component",
                    "actionType": "setSelectedItems",
                    "args": {
                      "items": [
                        {
                          "a": "a3",
                          "b": "b3"
                        },
                        {
                          "a": "a4",
                          "b": "b4"
                        }
                      ]
                    }
                  }
                ]
              }
            }
          }]
      },
      {
        "type": "input-table",
        "label": "表格表单",
        "id": "setSelectedItems-input-table",
        "name": "table",
        "valueField": "a",
        "selectable": true,
        "multiple": true,
        "columns": [
          {
            "name": "a",
            "label": "A"
          },
          {
            "name": "b",
            "label": "B"
          }
        ],
        "addable": true,
        "footerAddBtn": {
          "label": "新增",
          "icon": "fa fa-plus",
          "hidden": true
        },
        "strictMode": true,
        "minLength": 0,
        "needConfirm": false,
        "showTableAddBtn": false
      }
    ],
    "data": {
      "table": [
        {
          "id": 1,
          "a": "a1",
          "b": "b1"
        },
        {
          "id": 2,
          "a": "a2",
          "b": "b2"
        },
        {
          "id": 3,
          "a": "a3",
          "b": "b3"
        },
        {
          "id": 4,
          "a": "a4",
          "b": "b4"
        },
        {
          "id": 5,
          "a": "a5",
          "b": "b5"
        }
      ]
    }
  }
}
```

## 常见问题

### 单元格中的表单修改后失焦

每一行都是有一个唯一key的，也就是 `valueField` 或者 `primaryField` ，默认为 `id`，如果在修改的过程中这个唯一字段发生了变化就会导致整行组件重新挂载导致失焦，故不要把唯一标识字段作为表单字段。

### 单元格中的表单使用了autoFill翻页后数据会丢失

默认情况下表格的每一行组件都是会复用的，翻页会会导致表格中渲染的数据发生变化，会自动执行预期之外的 `autoFill` 从而导致一些数据被 `autoFill` 修改，看起来像是数据丢失了，如果遇见这种场景可以将 `inputTable` 设置 `reUseRow: false`，这个配置的作用是翻页的时候不复用之前的行组件，同时因为不复用所以性能也会有所下降。

### 确认模式如何回显option的label

单元格回显的是`form`数据域中`InputTable`的数据，要想反显对应的`label`值，需要使用`mapping`组件映射当前`name`对应的`label`。

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "data": {
      "table": [
        {
          "a": "a1",
          "b": "b1"
        },
        {
          "a": "a2",
          "b": "b2"
        },
        {
          "a": "a3",
          "b": "b3"
        }
      ]
    },
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "body": [
      {
        "type": "input-table",
        "name": "table",
        "label": "InputTable",
        "addable": true,
        "editable": true,
        "needConfirm": true,
        "columns": [
          {
            "label": "A",
            "name": "a"
          },
          {
            "label": "B",
            "name": "b",
            "type": "mapping",
            "map": [
              {
                "label": "b1 label",
                "value": "b1"
              },
              {
                "label": "b2 label",
                "value": "b2"
              },
              {
                "label": "b3 label",
                "value": "b3"
              }
            ],
            "quickEdit": {
              "name": "b",
              "type": "select",
              "options": [
                {
                  "label": "b1 label",
                  "value": "b1"
                },
                {
                  "label": "b2 label",
                  "value": "b2"
                },
                {
                  "label": "b3 label",
                  "value": "b3"
                }
              ]
            }
          },
        ]
      }
    ]
  }
}

```
