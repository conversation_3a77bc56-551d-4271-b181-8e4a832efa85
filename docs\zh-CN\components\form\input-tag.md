---
title: InputTag 标签选择器
description:
type: 0
group: null
menuName: InputTag 标签选择器
icon:
order: 55
standardMode: true
---
## 场景推荐
### 基本使用

用户能够自由添加、移除或编辑一组标签化信息的应用场景时推荐使用。需要添加的值可以在下拉框中选择，也支持自定义输入，值会自动过滤重复的标签值，最终为逗号分隔的字符串格式

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "body": [
     {
        "type": "input-tag",
        "name": "browser",
        "label": "标签",
        "placeholder": "请输入",
          "maxTagCount": 5,
          "options": [
            "<PERSON>",
            "<PERSON> Brady",
            "<PERSON><PERSON><PERSON> Woodson",
            "<PERSON>"
          ]
      }
    ]
  }
}

```

- 落地案例
 [特征一站式/平台管理/魔方迁移/平台报告](http://moka.dmz.sit.caijj.net/featurestoreui/#/magicMigrate/airRunReport)

![特征一站式/平台管理/魔方迁移/平台报告](https://static02.sit.yxmarketing01.com/materialcenter/ba865c0e-a286-4e8f-944d-cb34e42d7f73.png)

## 组件用法
<!--
### 基本使用

```schema: scope="body"
{
    "type": "form",
    "body": [
        {
            "type": "input-tag",
            "name": "tag",
            "label": "标签",
            "placeholder": "请选择标签",
            "options": [
                "Aaron Rodgers",
                "Tom Brady",
                "Charlse Woodson",
                "Aaron Jones"
            ]
        }
    ]
}
``` -->
### 限制标签最大展示数量

`maxTagCount`可以限制标签的最大展示数量，超出数量的部分会收纳到 Popover 中，可以通过`overflowTagPopover`配置 Popover 相关的[属性](/dataseeddesigndocui/#/amis/zh-CN/components/tooltip#属性表)，注意该属性仅在多选模式开启后生效。

```schema: scope="body"
{
    "type": "form",
    "body": [
        {
            "type": "input-tag",
            "name": "tag",
            "label": "标签",
            "maxTagCount": 3,
            "overflowTagPopover": {
                "title": "水果"
            },
            "value": "Pineapple,Kiwifruit,Banana,Blueberry,Carambola",
            "options": [
                {"label": "苹果", "value": "Apple"},
                {"label": "香蕉", "value": "Banana"},
                {"label": "黑莓", "value": "Blackberry"},
                {"label": "蓝莓", "value": "Blueberry"},
                {"label": "樱桃", "value": "Cherry"},
                {"label": "杨桃", "value": "Carambola"},
                {"label": "椰子", "value": "Coconut"},
                {"label": "猕猴桃", "value": "Kiwifruit"},
                {"label": "柠檬", "value": "Lemon"},
                {"label": "菠萝", "value": "Pineapple"}
            ]
        }
    ]
}
```

### 批量输入

可以设置`"enableBatchAdd": true`开启批量输入模式，默认的分隔符为`"-"`，可以使用`"separator"`属性自定义分隔符，注意避免和`"delimiter"`属性冲突。

```schema: scope="body"
{
    "type": "form",
    "body": [
        {
            "type": "input-tag",
            "name": "tag",
            "label": "标签",
            "enableBatchAdd": true
        }
    ]
}
```

`1.83.6`版本后，`separator`属性支持配置正则表达式，直接传给底层`split`方法分割。

```schema: scope="body"
{
    "type": "form",
    "body": [
        {
            "type": "input-tag",
            "name": "tag",
            "label": "标签",
            "enableBatchAdd": true,
            "separator": /[,;]/ // 粘帖"a,b;c"试试
        }
    ]
}
```

### 数量&文本长度限制

可以设置`max`限制输入的标签数量，设置`maxTagLength`限制单个标签的最大文本长度。

```schema: scope="body"
{
    "type": "form",
    "body": [
        {
            "type": "input-tag",
            "name": "tag",
            "label": "标签",
            "options": ["abc", "def", "xyz"],
            "enableBatchAdd": true,
            "max": 5,
            "maxTagLength": 3
        }
    ]
}
```

### 支持配置个性化标签

> 2023-04-19 by @lirenjie support

当option配置为对象时，添加 customTag 字段来激活功能，未配置时走默认样式。详细使用说明参考下方属性表

```schema: scope="body"
{
    "type": "form",
    "body": [
        {
          "type":"input-tag",
          "name":"tag",
          "label":"标签",
          "placeholder":"请选择标签",
          "options":[
              {
                  "label":"诸葛亮(zhugeliang)",
                  "value":"zhugeliang",
                  "customTag":{
                      "type":"tag",
                      "label":"诸葛亮",
                      "displayMode":"rounded",
                      "color":"inactive"
                  }
              },
              {
                  "label":"王昭君(wangzhaojun)",
                  "value":"wangzhaojun",
                  "customTag":{
                      "type":"tag",
                      "label":"王昭君",
                      "color":"processing"
                  }
              },
              {
                  "label":"钟馗(zhongkui)",
                  "value":"zhongkui",
                  "customTag":{
                      "type":"tag",
                      "label":"钟馗",
                      "color":"success"
                  }
              },
              {
                  "label":"露娜(luna)",
                  "value":"luna",
                  "customTag":{
                      "type":"tag",
                      "label":"露娜",
                      "closable":true
                  }
              },
              {
                  "label":"钟无艳(zhongwuyan)",
                  "value":"zhongwuyan",
                  "customTag":{
                      "type":"tag",
                      "label":"钟无艳",
                      "displayMode":"normal",
                      "color":"inactive"
                  }
              },
              {
                  "label":"花木兰(huamulan)",
                  "value":"huamulan",
                  "customTag":{
                      "type":"tag",
                      "label":"花木兰",
                      "displayMode":"normal",
                      "color":"active"
                  }
              }
          ]
      }
    ]
}
```

### 属性表

除了支持 [普通表单项属性表](/dataseeddesigndocui/#/amis/zh-CN/components/form/formitem#%E5%B1%9E%E6%80%A7%E8%A1%A8) 中的配置以外，还支持下面一些配置

| 属性名             | 类型                                      | 默认值                                                                             | 说明                                                                                        |
| ------------------ | ----------------------------------------- | ---------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------- |
| options            | `Array<object>`或`Array<string>`          |                                                                                    | [选项组](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E9%9D%99%E6%80%81%E9%80%89%E9%A1%B9%E7%BB%84-options)                   |
| optionsTip         | `Array<object>`或`Array<string>`          | `"最近您使用的标签"`                                                               | 选项提示                                                                                    |
| source             | `string`或 [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api) |                                                                                    | [动态选项组](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E5%8A%A8%E6%80%81%E9%80%89%E9%A1%B9%E7%BB%84-source)                |
| delimiter          | `string`                                  | `false`                                                                            | [拼接符](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E6%8B%BC%E6%8E%A5%E7%AC%A6-delimiter)                                   |
| labelField         | `string`                                  | `"label"`                                                                          | [选项标签字段](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E9%80%89%E9%A1%B9%E6%A0%87%E7%AD%BE%E5%AD%97%E6%AE%B5-labelfield) |
| valueField         | `string`                                  | `"value"`                                                                          | [选项值字段](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E9%80%89%E9%A1%B9%E5%80%BC%E5%AD%97%E6%AE%B5-valuefield)            |
| joinValues         | `boolean`                                 | `true`                                                                             | [拼接值](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E6%8B%BC%E6%8E%A5%E5%80%BC-joinvalues)                                  |
| extractValue       | `boolean`                                 | `false`                                                                            | [提取值](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E6%8F%90%E5%8F%96%E5%A4%9A%E9%80%89%E5%80%BC-extractvalue)              |
| clearable          | `boolean`                                 | `false`                                                                            | 在有值的时候是否显示一个删除图标在右侧。                                                    |
| resetValue         | `string`                                  | `""`                                                                               | 删除后设置此配置项给定的值。                                                                |
| max                | `number`                                  |                                                                                    | 允许添加的标签的最大数量                                                                    |
| maxTagLength       | `number`                                  |                                                                                    | 单个标签的最大文本长度                                                                      |
| maxTagCount        | `number`                                  |                                                                                    | 标签的最大展示数量，超出数量后以收纳浮层的方式展示，仅在多选模式开启后生效                  |
| overflowTagPopover | `TooltipObject`                           | `{"placement": "top", "trigger": "hover", "showArrow": false, "offset": [0, -10]}` | 收纳浮层的配置属性，详细配置参考[Tooltip](/dataseeddesigndocui/#/amis/zh-CN/components/tooltip#属性表)                                |
| enableBatchAdd     | `boolean`                                 | `false`                                                                            | 是否开启批量添加模式                                                                        |
| pasteImmediateAdd  | `boolean`    | `true`           | 粘帖后是否立即添加到表单，默认失焦或回车时添加。`1.74.0`版本支持。    |
| separator          | `string`或`RegExp`                                  | `"-"`                                                                              | 开启批量添加后，输入多个标签的分隔符，支持传入多个符号，默认为"-"。`1.86.3`版本后支持配置正则表达式。                          |
| customTag          | `object`                                  |                                                                               | 开启后生效个性化标签功能，由于复用Tag组件功能且受当前主功能限制，配置这些字段将不会生效：type、label、closable、以及事件表所有字段。配置参考 [Tag 属性表](/dataseeddesigndocui/#/amis/zh-CN/components/tag#属性表)                          |

### 事件表

当前组件会对外派发以下事件，可以通过`onEvent`来监听这些事件，并通过`actions`来配置执行的动作，在`actions`中可以通过`${事件参数名}`来获取事件产生的数据，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action)。

> `[name]`表示当前组件绑定的名称，即`name`属性，如果没有配置`name`属性，则通过`value`取值。

| 事件名称 | 事件参数                                                                                                          | 说明                 |
| -------- | ----------------------------------------------------------------------------------------------------------------- | -------------------- |
| change   | `[name]: string` 组件的值（多个以逗号分割）<br/>`selectedItems: Option[]` 选中的项<br/>`items: Option[]` 所有选项 | 选中值变化时触发     |
| blur     | `[name]: string` 组件的值<br/>`selectedItems: Option[]` 选中的项<br/>`items: Option[]` 所有选项                   | 输入框失去焦点时触发 |
| focus    | `[name]: string` 组件的值<br/>`selectedItems: Option[]` 选中的项<br/>`items: Option[]` 所有选项                   | 输入框获取焦点时触发 |
| clear    | `[name]: string` 组件的值                   | 点击清除按钮时触发 |
| delete    | `[name]: string` 组件的值<br/>`selectedItems: Option[]` 选中的项<br/>`items: Option[]` 所有选项      | 删除单个标签触发 |

#### clear

配置`clearable`为 true，点击清除按钮时触发。

```schema: scope="body"
{
  "type": "form",
  "body": [
    {
      "type": "input-tag",
      "name": "tag",
      "label": "标签",
      "value": "Pineapple,Kiwifruit",
      "options": [
        {
          "label": "苹果",
          "value": "Apple"
        },
        {
          "label": "香蕉",
          "value": "Banana"
        },
        {
          "label": "猕猴桃",
          "value": "Kiwifruit"
        },
        {
          "label": "柠檬",
          "value": "Lemon"
        },
        {
          "label": "菠萝",
          "value": "Pineapple"
        }
      ],
      "clearable": true,
      "onEvent": {
        "clear": {
          "actions": [
            {
              "actionType": "toast",
              "args": {
                "msg": "${event.data.value|json}"
              }
            }
          ]
        }
      }
    }
  ]
}
```

#### delete

删除标签时触发。

```schema: scope="body"
{
  "type": "form",
  "body": [
    {
      "type": "input-tag",
      "name": "tag",
      "label": "标签",
      "value": "Pineapple,Kiwifruit",
      "options": [
        {
          "label": "苹果",
          "value": "Apple"
        },
        {
          "label": "香蕉",
          "value": "Banana"
        },
        {
          "label": "猕猴桃",
          "value": "Kiwifruit"
        },
        {
          "label": "柠檬",
          "value": "Lemon"
        },
        {
          "label": "菠萝",
          "value": "Pineapple"
        }
      ],
      "clearable": true,
      "onEvent": {
        "delete": {
          "actions": [
            {
              "actionType": "toast",
              "args": {
                "msg": "${event.data.value|json}"
              }
            }
          ]
        }
      }
    }
  ]
}
```

### 动作表

当前组件对外暴露以下特性动作，其他组件可以通过指定`actionType: 动作名称`、`componentId: 该组件id`来触发这些动作，动作配置可以通过`args: {动作配置项名称: xxx}`来配置具体的参数，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action#触发其他组件的动作)。

| 动作名称 | 动作配置                 | 说明                                                    |
| -------- | ------------------------ | ------------------------------------------------------- |
| clear    | -                        | 清空                                                    |
| reset    | -                        | 将值重置为`resetValue`，若没有配置`resetValue`，则清空  |
| reload   | -                        | 重新加载，调用 `source`，刷新数据域数据刷新（重新加载） |
| setValue | `value: string` 更新的值 | 更新数据，多个值用`,`分隔                               |
