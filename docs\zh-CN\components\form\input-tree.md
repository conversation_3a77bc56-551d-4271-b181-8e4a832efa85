---
title: InputTree 树形选择框
description:
type: 0
group: null
menuName: InpputTree 树形选择框
icon:
order: 59
standardMode: true
---

当业务数据为树形数据结构需要进行某些操作时，可通过树形选择框实现。

## 场景推荐
### 表单基本使用

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "labelWidth": 40,
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "input-tree",
        "name": "tree",
        "label": "Tree",
        "searchable": true,
        "creatable": true,
        "removable": true,
        "editable": true,
        "options": [
          {
            "label": "Folder A",
            "value": 1,
            "children": [
              {
                "label": "file A",
                "value": 2
              },
              {
                "label": "Folder B",
                "value": 3,
                "children": [
                  {
                    "label": "file b1",
                    "value": 3.1
                  },
                  {
                    "label": "file b2",
                    "value": 3.2
                  }
                ]
              }
            ]
          },
          {
            "label": "file C",
            "value": 4
          },
          {
            "label": "file D",
            "value": 5
          }
        ]
      },
    ]
  }
}
```

### 带全局操作按钮+筛选按钮组

```schema
{
  "type": "page",
  "id": "pageId",
  "bodyClassName": "h-screen",
  "data": {
    "tree2": {
      "label": "数禾测试数据A",
      "value": "shuhe-test-data-a"
    },
    "text1": "shuhe-test-data-a",
    "type": "a",
    "options": [
      {
        "label": "数禾测试数据A",
        "value": "shuhe-test-data-a"
      },
      {
        "label": "数禾测试数据B",
        "value": "shuhe-test-data-b",
        "children": [
          {
            "label": "数禾测试数据B-1",
            "value": "shuhe-test-data-b-1"
          },
          {
            "label": "数禾测试数据B-2",
            "value": "shuhe-test-data-b-2"
          },
          {
            "label": "数禾测试数据B-3",
            "value": "shuhe-test-data-b-3"
          }
        ]
      },
      {
        "label": "数禾测试数据C",
        "value": "c"
      }
    ]
  },
  "body": {
    "type": "left-right-container",
    "autoFillHeight": true,
    "defaultWidth": 400,
    "left": {
      "type": "form",
      "body": {
        "type": "flex",
        "direction": "column",
        "gap": true,
        "items": [
          {
            "type": "flex",
            "justify": "space-between",
            "items": [
              {
                "type": "button",
                "label": "主功能按钮",
                "level": "primary"
              },
              {
                "type": "button-group-select",
                "label": false,
                "name": "type",
                "options": [
                  {
                    "label": "全部",
                    "value": "a"
                  },
                  {
                    "label": "可编辑",
                    "value": "b"
                  }
                ]
              }
            ]
          },
          {
            "type": "input-tree",
            "name": "tree2",
            "id": "tree2",
            "label": false,
            "multiple": false,
            "searchable": true,
            "autoCheckChildren": false,
            "joinValues": false,
            "autoFillHeight": true,
            "creatable": true,
            "removable": true,
            "editable": true,
            "source": "${type === 'a' ? options : []}",
            "onEvent": {
              "change": {
                "actions": [
                  {
                    "actionType": "setValue",
                    "componentId": "myForm",
                    "args": {
                      "value": {
                        "text1": "${event.data.value.value}"
                      }
                    }
                  }
                ]
              }
            }
          }
        ]
      }
    },
    "right": [
      {
        "type": "title",
        "title": "${tree2.label}",
        "subTitle": "${tree2.value}",
        "iconConfig": true
      },
      {
        "type": "form",
        "id": "myForm",
        "labelWidth": 60,
        "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
        "body": [
          {
            "type": "group-container",
            "activeKey": [
              "1"
            ],
            "items": [
              {
                "key": "1",
                "header": {
                  "title": "第一步，基础信息",
                  "subTitle": "这是一段提示",
                  "actions": [
                    {
                      "type": "button",
                      "label": "实验列表",
                      "level": "link"
                    }
                  ]
                },
                "body": [
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "input-text",
                        "name": "text1",
                        "label": "姓名"
                      },
                      {
                        "type": "input-text",
                        "name": "text2",
                        "label": "年龄"
                      },
                      {
                        "type": "input-text",
                        "name": "text3",
                        "label": "班级",
                        "required": true
                      }
                    ]
                  },
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "input-text",
                        "name": "text4",
                        "label": "邮箱"
                      },
                      {
                        "type": "input-text",
                        "name": "text5",
                        "label": "电话"
                      },
                      {
                        "type": "input-text",
                        "name": "text6",
                        "label": "地址",
                        "columnRatio": 4
                      }
                    ]
                  },
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "input-text",
                        "name": "text7",
                        "label": "其它",
                        "columnRatio": 4
                      }
                    ]
                  }
                ]
              },
              {
                "key": "2",
                "header": {
                  "title": "第二步，复杂信息"
                },
                "body": [
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "input-text",
                        "name": "second1",
                        "label": "邮箱"
                      },
                      {
                        "type": "input-text",
                        "name": "second2",
                        "label": "电话"
                      },
                      {
                        "type": "input-text",
                        "name": "second3",
                        "label": "地址",
                        "columnRatio": 4
                      }
                    ]
                  },
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "textarea",
                        "name": "textarea",
                        "label": "姓名",
                        "placeholder": "请输入"
                      }
                    ]
                  },
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "input-rich-text",
                        "name": "second5",
                        "label": "其它"
                      }
                    ]
                  }
                ]
              },
              {
                "key": "3",
                "header": {
                  "title": "第三步，策略信息"
                },
                "collapsed": false,
                "body": [
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "tabs",
                        "addable": true,
                        "closable": true,
                        "editable": true,
                        "addBtnText": " ",
                        "tabPosition": "center",
                        "tabsMode": "strong",
                        "defaultTabForAdd": {
                          "title": "策略分支x",
                          "body": [
                            {
                              "type": "group",
                              "body": [
                                {
                                  "type": "input-text",
                                  "name": "third1",
                                  "label": "sjksajkd"
                                },
                                {
                                  "type": "input-text",
                                  "name": "third2",
                                  "label": "sjksajkd"
                                },
                                {
                                  "type": "input-text",
                                  "name": "third3",
                                  "label": "sjksajkd"
                                }
                              ]
                            },
                            {
                              "type": "group",
                              "body": [
                                {
                                  "type": "input-text",
                                  "name": "third4",
                                  "label": "sjksajkd"
                                },
                                {
                                  "type": "input-text",
                                  "name": "third5",
                                  "label": "sjksajkd"
                                },
                                {
                                  "type": "input-text",
                                  "name": "third6",
                                  "label": "sjksajkd"
                                }
                              ]
                            }
                          ]
                        },
                        "tabs": [
                          {
                            "title": "策略分支1",
                            "tab": [
                              {
                                "type": "group",
                                "body": [
                                  {
                                    "type": "input-text",
                                    "name": "third1",
                                    "label": "sjksajkd"
                                  },
                                  {
                                    "type": "input-text",
                                    "name": "third2",
                                    "label": "sjksajkd"
                                  },
                                  {
                                    "type": "input-text",
                                    "name": "third3",
                                    "label": "sjksajkd"
                                  }
                                ]
                              },
                              {
                                "type": "group",
                                "body": [
                                  {
                                    "type": "input-text",
                                    "name": "third5",
                                    "label": "sjksajkd"
                                  },
                                  {
                                    "type": "select",
                                    "name": "third4",
                                    "label": "sjksajkd",
                                    "options": [
                                      {
                                        "label": "a",
                                        "value": "a"
                                      },
                                      {
                                        "label": "b",
                                        "value": "b"
                                      }
                                    ]
                                  },
                                  {
                                    "type": "input-text",
                                    "name": "third6",
                                    "label": "sjksajkd"
                                  }
                                ]
                              }
                            ]
                          },
                          {
                            "title": "策略分支2",
                            "tab": [
                              {
                                "type": "group",
                                "body": [
                                  {
                                    "type": "input-text",
                                    "name": "third7",
                                    "label": "sjksajkd"
                                  },
                                  {
                                    "type": "input-text",
                                    "name": "third8",
                                    "label": "sjksajkd"
                                  },
                                  {
                                    "type": "input-text",
                                    "name": "third9",
                                    "label": "sjksajkd"
                                  }
                                ]
                              },
                              {
                                "type": "group",
                                "body": [
                                  {
                                    "type": "input-text",
                                    "name": "third10",
                                    "label": "sjksajkd"
                                  },
                                  {
                                    "type": "select",
                                    "name": "third4",
                                    "label": "sjksajkd",
                                    "options": [
                                      {
                                        "label": "a",
                                        "value": "a"
                                      },
                                      {
                                        "label": "b",
                                        "value": "b"
                                      }
                                    ]
                                  },
                                  {
                                    "type": "input-text",
                                    "name": "third12",
                                    "label": "sjksajkd"
                                  }
                                ]
                              }
                            ]
                          },
                          {
                            "title": "策略分支3",
                            "tab": [
                              {
                                "type": "group",
                                "body": [
                                  {
                                    "type": "input-text",
                                    "name": "third13",
                                    "label": "sjksajkd"
                                  },
                                  {
                                    "type": "input-text",
                                    "name": "third14",
                                    "label": "sjksajkd"
                                  },
                                  {
                                    "type": "input-text",
                                    "name": "third15",
                                    "label": "sjksajkd"
                                  }
                                ]
                              },
                              {
                                "type": "group",
                                "body": [
                                  {
                                    "type": "input-text",
                                    "name": "third16",
                                    "label": "sjksajkd"
                                  },
                                  {
                                    "type": "select",
                                    "name": "third4",
                                    "label": "sjksajkd",
                                    "options": [
                                      {
                                        "label": "a",
                                        "value": "a"
                                      },
                                      {
                                        "label": "b",
                                        "value": "b"
                                      }
                                    ]
                                  },
                                  {
                                    "type": "input-text",
                                    "name": "third18",
                                    "label": "sjksajkd"
                                  }
                                ]
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ],
        "actions": [
          {
            "type": "button",
            "label": "取消",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "actionType": "toast",
                    "args": {
                      "msgType": "info",
                      "msg": "响应取消操作"
                    }
                  }
                ]
              }
            }
          },
          {
            "type": "button",
            "level": "primary",
            "label": "保存",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "componentId": "myForm",
                    "actionType": "submit"
                  }
                ]
              }
            }
          }
        ]
      }
    ]
  }
}
```

## 组件用法
### 基本使用

配置的`options`中，可以通过`children`字段进行嵌套展示，实现树形选择器

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "labelWidth": 40,
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "input-tree",
        "name": "tree",
        "label": "Tree",
        "options": [
          {
            "label": "Folder A",
            "value": 1,
            "children": [
              {
                "label": "file A",
                "value": 2
              },
              {
                "label": "Folder B",
                "value": 3,
                "children": [
                  {
                    "label": "file b1",
                    "value": 3.1
                  },
                  {
                    "label": "file b2",
                    "value": 3.2
                  }
                ]
              }
            ]
          },
          {
            "label": "file C",
            "value": 4
          },
          {
            "label": "file D",
            "value": 5
          }
        ]
      }
    ]
  }
}
```

默认点击一个选中节点不会取消选中，可以通过`canCancelSelectedNode: true`使得再次点击的时候可以取消选中

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "labelWidth": 40,
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "input-tree",
        "name": "tree",
        "label": "Tree",
        "canCancelSelectedNode": true,
        "options": [
          {
            "label": "Folder A",
            "value": 1,
            "children": [
              {
                "label": "file A",
                "value": 2
              },
              {
                "label": "Folder B",
                "value": 3,
                "children": [
                  {
                    "label": "file b1",
                    "value": 3.1
                  },
                  {
                    "label": "file b2",
                    "value": 3.2
                  }
                ]
              }
            ]
          },
          {
            "label": "file C",
            "value": 4
          },
          {
            "label": "file D",
            "value": 5
          }
        ]
      }
    ]
  }
}
```

### 选择器样式

配置`"type": "tree-select"`可以实现选择器样式

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "labelWidth": 40,
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "tree-select",
        "name": "tree",
        "label": "Tree",
        "options": [
          {
            "label": "Folder A",
            "value": 1,
            "children": [
              {
                "label": "file A",
                "value": 2
              },
              {
                "label": "file B",
                "value": 3
              }
            ]
          },
          {
            "label": "file C",
            "value": 4
          },
          {
            "label": "file D",
            "value": 5
          }
        ]
      }
    ]
  }
}
```

### 是否显示展开线

通过 `showOutline` 来控制是否显示展开线。

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "labelWidth": 40,
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "input-tree",
        "name": "tree",
        "label": "Tree",
        "showOutline": true,
        "options": [
          {
            "label": "Folder A",
            "value": 1,
            "children": [
              {
                "label": "file A",
                "value": 2
              },
              {
                "label": "Folder B",
                "value": 3,
                "children": [
                  {
                    "label": "file b1",
                    "value": 3.1
                  },
                  {
                    "label": "file b2",
                    "value": 3.2
                  }
                ]
              }
            ]
          },
          {
            "label": "file C",
            "value": 4
          },
          {
            "label": "file D",
            "value": 5
          }
        ]
      }
    ]
  }
}
```

### 选中父节点是否自动选中子节点

`autoCheckChildren`默认为 true，选中父节点会自动选中子节点，可以设置`"autoCheckChildren": false`，不自动选中子节点

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "labelWidth": 130,
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "input-tree",
        "name": "tree1",
        "label": "默认自动选中子节点",
        "multiple": true,
        "options": [
          {
            "label": "A",
            "value": "a"
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "C",
            "value": "c"
          }
        ]
      },
      {
        "type": "divider"
      },
      {
        "type": "input-tree",
        "name": "tree2",
        "label": "不自动选中子节点",
        "multiple": true,
        "autoCheckChildren": false,
        "options": [
          {
            "label": "A",
            "value": "a"
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "C",
            "value": "c"
          }
        ]
      }
    ]
  }
}
```

### 选中父节点自动选中子节点，数据是否包含父子节点的值

`cascade`默认为 false，子节点禁止反选，值不包含子节点值，配置`"cascade": true`，子节点可以反选，值包含父子节点值。

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "input-tree",
        "name": "tree1",
        "label": "默认子节点禁止反选，值不包含子节点值",
        "multiple": true,
        "options": [
          {
            "label": "A",
            "value": "a"
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "C",
            "value": "c"
          }
        ]
      },
      {
        "type": "divider"
      },
      {
        "type": "input-tree",
        "name": "tree2",
        "label": "子节点可以反选，值包含父子节点值",
        "multiple": true,
        "cascade": true,
        "options": [
          {
            "label": "A",
            "value": "a"
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "C",
            "value": "c"
          }
        ]
      }
    ]
  }
}
```

`withChildren`默认为 false，子节点禁止反选，值包含父子节点值，配置`withChildren": true`，子节点禁止反选，值包含父子节点值

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "input-tree",
        "name": "tree1",
        "label": "默认不包含子节点的值",
        "multiple": true,
        "options": [
          {
            "label": "A",
            "value": "a"
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "C",
            "value": "c"
          }
        ]
      },
      {
        "type": "divider"
      },
      {
        "type": "input-tree",
        "name": "tree2",
        "label": "自动带上子节点的值",
        "multiple": true,
        "withChildren": true,
        "options": [
          {
            "label": "A",
            "value": "a"
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "C",
            "value": "c"
          }
        ]
      }
    ]
  }
}
```

也可以设置`onlyChildren`，实现只包含子节点的值

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "input-tree",
        "name": "tree1",
        "label": "默认不包含子节点的值",
        "multiple": true,
        "options": [
          {
            "label": "A",
            "value": "a"
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "C",
            "value": "c"
          }
        ]
      },
      {
        "type": "divider"
      },
      {
        "type": "input-tree",
        "name": "tree2",
        "label": "只包含子节点的值",
        "multiple": true,
        "onlyChildren": true,
        "options": [
          {
            "label": "A",
            "value": "a"
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "C",
            "value": "c"
          }
        ]
      }
    ]
  }
}
```

### 只允许选择叶子节点

在单选时，可通过 `onlyLeaf` 可以配置只允许选择叶子节点

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "labelWidth": 40,
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "input-tree",
        "name": "tree",
        "label": "Tree",
        "onlyLeaf": true,
        "searchable": true,
        "options": [
          {
            "label": "Folder A",
            "value": 1,
            "children": [
              {
                "label": "file A",
                "value": 2
              },
              {
                "label": "file B",
                "value": 3
              }
            ]
          },
          {
            "label": "file C",
            "value": 4
          },
          {
            "label": "file D",
            "value": 5
          },
          {
            "label": "Folder E",
            "value": "61",
            "children": [
              {
                "label": "Folder G",
                "value": "62",
                "children": [
                  {
                    "label": "file H",
                    "value": 6
                  },
                  {
                    "label": "file I",
                    "value": 7
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### 默认展开

默认是展开所有子节点的，如果不想默认展开，则配置`"initiallyOpen": false`

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "input-tree",
        "name": "tree1",
        "label": "默认不自动带上子节点的值",
        "initiallyOpen": false,
        "options": [
          {
            "label": "A",
            "value": "a"
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "C",
            "value": "c"
          }
        ]
      }
    ]
  }
}
```

如果层级较多，也可以配置`unfoldedLevel`指定展开的层级数，默认展开第 1 层

下例中设置`"unfoldedLevel": 2`，表示展开第 2 层

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "input-tree",
        "name": "tree1",
        "label": "默认不自动带上子节点的值",
        "initiallyOpen": false,
        "unfoldedLevel": 2,
        "options": [
          {
            "label": "A",
            "value": "a"
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2",
                "children": [
                  {
                    "label": "B-2-1",
                    "value": "b-2-1"
                  },
                  {
                    "label": "B-2-2",
                    "value": "b-2-2"
                  },
                  {
                    "label": "B-2-3",
                    "value": "b-2-3"
                  }
                ]
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "C",
            "value": "c"
          }
        ]
      }
    ]
  }
}
```

通过inputTree组件的`expand`和`collapse`完成树层级调整

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "id": "myForm",
    "debugger": true,
    "data": {
      "level": 2
    },
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "tpl",
        "tpl": "level:${level}"
      },
      {
        "type": "button",
        "label": "+ level",
        "className": "ml-2",
        "onEvent": {
          "click": {
            "actions": [
              {
                "componentId": "myForm",
                "actionType": "setValue",
                "args": {
                  "value": {
                    "level": "${level + 1}"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "type": "button",
        "label": "- level",
        "className": "ml-2",
        "onEvent": {
          "click": {
            "actions": [
              {
                "componentId": "myForm",
                "actionType": "setValue",
                "args": {
                  "value": {
                    "level": "${level - 1}"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "type": "button",
        "label": "增加层级",
        "className": "ml-2",
        "onEvent": {
          "click": {
            "actions": [
              {
                "actionType": "expand",
                "componentId": "myInputTree",
                "args": {
                  "openLevel": "${level}"
                }
              }
            ]
          }
        }
      },
      {
        "type": "button",
        "label": "减少层级",
        "className": "ml-2",
        "onEvent": {
          "click": {
            "actions": [
              {
                "actionType": "collapse",
                "componentId": "myInputTree"
              }
            ]
          }
        }
      },
      {
        "className": "mt-4",
        "type": "input-tree",
        "name": "tree1",
        "id": "myInputTree",
        "label": "默认不自动带上子节点的值",
        "initiallyOpen": false,
        "unfoldedLevel": 2,
        "options": [
          {
            "label": "A",
            "value": "a"
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2",
                "children": [
                  {
                    "label": "B-2-1",
                    "value": "b-2-1"
                  },
                  {
                    "label": "B-2-2",
                    "value": "b-2-2"
                  },
                  {
                    "label": "B-2-3",
                    "value": "b-2-3"
                  }
                ]
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "C",
            "value": "c"
          }
        ]
      }
    ]
  }
}
```

### 可编辑

配置 `creatable`、`removable` 和 `editable` 可以实现树可编辑。

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "labelWidth": 40,
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "input-tree",
        "name": "tree",
        "label": "Tree",
        "creatable": true,
        "removable": true,
        "editable": true,
        "options": [
          {
            "label": "Folder A",
            "value": 1,
            "children": [
              {
                "label": "file A",
                "value": 2
              },
              {
                "label": "file B",
                "value": 3
              }
            ]
          },
          {
            "label": "file C",
            "value": 4
          },
          {
            "label": "file D",
            "value": 5
          }
        ]
      }
    ]
  }
}
```

### 控制哪些项可编辑

配置 `creatable`、`removable`、`editable`和`additionable` 可以实现树可编辑，同时如果需要关闭部分节点的编辑权限，可以在节点上配置`creatable`、`removable` `editable`和`additionable`。

`rootCreatable` 可以用来关闭顶层是否可以创建。如果想要控制顶层可编辑，请配置 `hideRoot`，用节点来控制。

```schema
{
  "type": "page",
  "body": {
  "type": "form",
  "labelWidth": 40,
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "input-tree",
      "name": "tree",
      "label": "Tree",
      "creatable": true,
      "removable": true,
      "editable": true,
      "extraActions": [
        {
          "type": "tooltip-wrapper",
          "content": "拷贝数据，点击可以查看当前数据的label和value",
          "className": "inline",
          "inline": true,
          "body": [
            {
              "type": "icon",
              "className": "w-4 ml-2",
              "icon": "user",
              "onEvent": {
                "click": {
                  "actions": [
                    {
                      "actionType": "toast",
                      "args": {
                        "msgType": "info",
                        "msg": "label:${event.data.label}--value:${event.data.value}"
                      }
                    }
                  ]
                }
              }
            }
          ]
        },
      ],
      "rootCreatable": false,
      "options": [
        {
          "label": "Folder A",
          "value": 1,
          "creatable": false,
          "removable": false,
          "editable": false,
          "additionable": false,
          "children": [
            {
              "label": "file A",
              "value": 2
            },
            {
              "label": "file B",
              "value": 3
            }
          ]
        },
        {
          "label": "file C",
          "value": 4,
          "removable": false,
          "additionable": false,
        },
        {
          "label": "file D",
          "value": 5,
          "editable": false
        }
      ]
    }
  ]
}
}
```

### 控制添加/编辑的表单

配置 `addControls` 可以控制添加时需要填写哪些信息，同样还有 `editControls` 来配置编辑节点的表单 

当前icon所在节点的层级信息，新增可通过`parent.level`获取，编辑直接取`level`

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "labelWidth": 40,
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "input-tree",
        "name": "tree",
        "label": "Tree",
        "creatable": true,
        "addControls": [
          {
            "label": "节点名称",
            "type": "input-text",
            "required": true,
            "name": "label"
          },
          {
            "label": "节点值",
            "type": "input-text",
            "required": true,
            "name": "value"
          }
        ],
        "options": [
          {
            "label": "Folder A",
            "value": 1,
            "children": [
              {
                "label": "file A",
                "value": 2
              },
              {
                "label": "file B",
                "value": 3
              }
            ]
          },
          {
            "label": "file C",
            "value": 4
          },
          {
            "label": "file D",
            "value": 5
          }
        ]
      }
    ]
  }
}
```

## 指定默认展开的选项

使用`menuTpl`属性，自定义下拉选项的渲染内容。

```schema
{
  "type": "page",
  "body": {
  "type": "form",
  "labelWidth": 40,
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "input-tree",
      "name": "tree",
      "label": "Tree",
      "menuTpl": "<div class='flex justify-between'><span>${label}</span><span class='bg-gray-200 rounded p-1 text-xs text-center w-14'>${tag}</span></div>",
      "iconField": "icon",
      "options": [
        {
          "label": "采购单",
          "value": "order",
          "tag": "数据模型",
          "icon": "fa fa-database",
          "children": [
            {
              "label": "ID",
              "value": "id",
              "tag": "数字",
              "icon": "fa fa-check",
            },
            {
              "label": "采购人",
              "value": "name",
              "tag": "字符串",
              "icon": "fa fa-check",
            },
            {
              "label": "采购时间",
              "value": "time",
              "tag": "日期时间",
              "icon": "fa fa-check",
            }
          ]
        }
      ]
    }
  ]
}

}
```

### 选项搜索

开启`"searchable": true`后，支持搜索当前数据源内的选项。

`searchConfig` 可以配置搜索框属性，具体支持属性如下：

| 属性名 | 类型  | 默认值 |  描述 |
| ----- | ---- | ----- | ----- |
| className    | `string`   |       | 输入框class类     |
| placeholder  | `string`   |       | 输入框占位提示     |
| enhance      | `boolean`  |       | 是否为加强样式     |
| clearable    | `boolean`  |       | 是否可清除        |

注意：`searchConfig.placeholder` 是输入框的占位提示。`placeholder` 是无数据时的占位提示。

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "labelWidth": 40,
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "input-tree",
        "name": "tree",
        "label": "Tree",
        "deferApi": "/api/mock2/form/deferOptions?label=${label}&waitSeconds=2",
        "searchable": true,
        "placeholder": "无数据",
        "searchConfig": {
          "placeholder": "请输入搜索内容"
        },
        "options": [
          {
            "label": "Folder A",
            "value": 1,
            "collapsed": true,
            "children": [
              {
                "label": "file A",
                "value": 2
              },
              {
                "label": "file B",
                "value": 3
              }
            ]
          },
          {
            "label": "这下面是懒加载的",
            "value": 4,
            "defer": true
          },
          {
            "label": "file D",
            "value": 5
          }
        ]
      }
    ]
  }
}
```

### 自定义展开/折叠图标

通过`switcherIcon`自定义展开/折叠图标。

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "labelWidth": 40,
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "input-tree",
        "name": "tree",
        "label": "Tree",
        "switcherIcon": "fas fa-tenge",
        "options": [
          {
            "label": "Folder A",
            "value": 1,
            "children": [
              {
                "label": "file A",
                "value": 2
              },
              {
                "label": "Folder B",
                "value": 3,
                "children": [
                  {
                    "label": "file b1",
                    "value": 3.1
                  },
                  {
                    "label": "file b2",
                    "value": 3.2
                  }
                ]
              }
            ]
          },
          {
            "label": "file C",
            "value": 4
          },
          {
            "label": "file D",
            "value": 5
          }
        ]
      }
    ]
  }
}
```

### 节点不占据整行

通过`blockNode`控制节点是否占据整行。

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "labelWidth": 40,
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "input-tree",
        "name": "tree",
        "label": "Tree",
        "blockNode": false,
        "options": [
          {
            "label": "Folder A",
            "value": 1,
            "children": [
              {
                "label": "file A",
                "value": 2
              },
              {
                "label": "file B",
                "value": 3
              }
            ]
          },
          {
            "label": "file C",
            "value": 4
          }
        ]
      }
    ]
  }
}
```

### 指定默认展开的选项

通过`defaultExpandedValues`指定默认展开的选项，被指定项的祖先选项都会默认展开。

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "labelWidth": 40,
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "input-tree",
        "name": "tree",
        "label": "Tree",
        "initiallyOpen": false,
        "defaultExpandedValues": [
          3
        ],
        "options": [
          {
            "label": "Folder A",
            "value": 1,
            "children": [
              {
                "label": "file A",
                "value": 2
              },
              {
                "label": "Folder B",
                "value": 3,
                "children": [
                  {
                    "label": "file b1",
                    "value": 4,
                  },
                  {
                    "label": "Folder C",
                    "value": 5,
                    "children": [
                      {
                        "label": "file C",
                        "value": 6
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### 可拖拽

通过`draggable`开启拖拽模式，通过`dropOn`控制某次拖拽是否被被允许，通过`dragApi`配置拖拽完成后要发送的请求，详情请看属性表。还有一系列跟拖拽相关的事件，详情看事件表。

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "id": "theForm",
    "labelWidth": 40,
    "api": "/api/mock2/form/saveForm",
    "data": {
      "dragItem": null,
      "enterItem": null,
      "overItem": null,
      "leaveItem": null,
      "options": null
    },
    "debug": true,
    "body": [
      {
        "type": "input-tree",
        "name": "tree",
        "label": "Tree",
        "draggable": true,
        "dropOn": "${dropItem.value !== 3}",
        "options": [
          {
            "label": "Folder A1",
            "value": 1,
            "children": [
              {
                "label": "Folder A2",
                "value": 2,
                "children": [
                  {
                    "label": "file A",
                    "value": 6
                  }
                ]
              },
              {
                "label": "file B",
                "value": 3
              }
            ]
          },
          {
            "label": "file C",
            "value": 4
          },
          {
            "label": "file D",
            "value": 5
          }
        ],
        "onEvent": {
          "dragStart": {
            "actions": [
              {
                "actionType": "setValue",
                "componentId": "theForm",
                "args": {
                  "value": "${{dragItem: event.data.item.label}}"
                }
              }
            ]
          },
          "dragEnd": {
            "actions": [
              {
                "actionType": "setValue",
                "componentId": "theForm",
                "args": {
                  "value": "${{dragItem: null}}"
                }
              }
            ]
          },
          "dragEnter": {
            "actions": [
              {
                "actionType": "setValue",
                "componentId": "theForm",
                "args": {
                  "value": "${{enterItem: event.data.item ? event.data.item.label : null}}"
                }
              }
            ]
          },
          "dragOver": {
            "actions": [
              {
                "actionType": "setValue",
                "componentId": "theForm",
                "args": {
                  "value": "${{overItem: event.data.item ? event.data.item.label : null}}"
                }
              }
            ]
          },
          "dragLeave": {
            "actions": [
              {
                "actionType": "setValue",
                "componentId": "theForm",
                "args": {
                  "value": "${{leaveItem: event.data.item ? event.data.item.label : null}}"
                }
              }
            ]
          },
          "dropSuccess": {
            "actions": [
              {
                "actionType": "setValue",
                "componentId": "theForm",
                "args": {
                  "value": "${{options: event.data.options}}"
                }
              }
            ]
          },
          "dropFail": {
            "actions": [
              {
                "actionType": "toast",
                "args": {
                  "msgType": "error",
                  "msg": "不能把节点放到 ${event.data.dropItem.label} 中"
                }
              }
            ]
          }
        }
      }
    ]
  }
}
```

### 自定义设置搜索条件

通过 `setSearchValue` 动作，可自定义搜索条件，详情看动作表。

```schema
{
  "type": "page",
  "body": {
  "type": "form",
  "id": "theForm",
  "labelWidth": 40,
  "api": "/api/mock2/form/saveForm",
  "data": {
    "dragItem": null,
    "enterItem": null,
    "overItem": null,
    "leaveItem": null,
    "options": null
  },
  "debug": true,
  "body": [
    {
      "type": "button",
      "label": "设置搜索条件",
      "className": "mb-4",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "setSearchValue",
              "componentId": "test",
              "args": {
                "searchValue": "option a",
              }
            }
          ]
        }
      }
    },
    {
      "type": "input-tree",
      "name": "tree",
      "label": "Tree",
      "id": "test",
      "searchable": true,
      "draggable": true,
      "dropOn": "${dropItem.value !== 3}",
      "source": "/api/mock2/form/getOptions?waitSeconds=1",
      "onEvent": {
        "dropSuccess": {
          "actions": [
            {
              "actionType": "setValue",
              "componentId": "theForm",
              "args": {
                "value": "${{options: event.data.options}}"
              }
            },
            {
              "actionType": "setSearchValue",
              "componentId": "test",
              "args": {
                "searchValue": "option b",
              }
            }
          ]
        },
        "dropFail": {
          "actions": [
            {
              "actionType": "toast",
              "args": {
                "msgType": "error",
                "msg": "不能把节点放到 ${event.data.dropItem.label} 中"
              }
            }
          ]
        }
      }
    }
  ]
}

}
```

### 高度自适应

可通过配置`treeContainerClassName`属性，设置 InputTree 组件的高度, 配合 autoFillHeight 使子组件继承 treeContainer 的高度。

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "labelWidth": 40,
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "wrapper",
        "body": {
          "type": "input-tree",
          "name": "tree",
          "label": "Tree",
          "searchable": true,
          "autoFillHeight": true,
          "treeContainerClassName": "h-80 max-h-none",
          "virtualThreshold": 9,
          "source": "/api/mock2/tree/search"
        }
      }
    ]
  }
}
```

### 高亮行

可以通过配置`itemClassNameExpr`来为行添加 CSS 类，支持 [模板](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/template) 语法。同时，可以通过`__folded`、`__checked`、`__item`、`__options`分别获取折叠状态、选中状态、当前行数据、全量数据（`1.70.0`）。

例如下例，`"${value === 2 ? 'bg-success' : ''}"` 表示当行数据的 `value` 变量等于 `2` 时，给当前行添加`bg-success` CSS 类名，即绿色背景色

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "labelWidth": 40,
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "input-tree",
        "name": "tree",
        "label": "Tree",
        "itemClassNameExpr": "${value === 2 ? 'bg-success' : ''}",
        "options": [
          {
            "label": "Folder A",
            "value": 1,
            "children": [
              {
                "label": "file A",
                "value": 2
              },
              {
                "label": "Folder B",
                "value": 3,
                "children": [
                  {
                    "label": "file b1",
                    "value": 3.1
                  },
                  {
                    "label": "file b2",
                    "value": 3.2
                  }
                ]
              }
            ]
          },
          {
            "label": "file C",
            "value": 4
          },
          {
            "label": "file D",
            "value": 5
          }
        ]
      }
    ]
  }
}
```

### 属性表

当做选择器表单项使用时，除了支持 [普通表单项属性表](/dataseeddesigndocui/#/amis/zh-CN/components/form/formitem#%E5%B1%9E%E6%80%A7%E8%A1%A8) 中的配置以外，还支持下面一些配置

| 属性名                 | 类型                                                                                                                                              | 默认值           | 说明                                                                                                                                                                                                                                      | 版本                           |
| ---------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------- | ---------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------ |
| options                | `Array<object>`或`Array<string>`                                                                                                                  |                  | [选项组](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E9%9D%99%E6%80%81%E9%80%89%E9%A1%B9%E7%BB%84-options)                                                                                                                 |
| placeholder                 |`string`                                                                                                                 |                  | 无 `options` 时的占位内容  |
| source                 | `string`或 [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api)                                                                                |                  | [动态选项组](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E5%8A%A8%E6%80%81%E9%80%89%E9%A1%B9%E7%BB%84-source)                                                                                                              |
| autoComplete           | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api)                                                                                           |                  | [自动提示补全](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E8%87%AA%E5%8A%A8%E8%A1%A5%E5%85%A8-autocomplete)                                                                                                               |
| multiple               | `boolean`                                                                                                                                         | `false`          | 是否多选                                                                                                                                                                                                                                  |
| delimiter              | `string`                                                                                                                                          | `false`          | [拼接符](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E6%8B%BC%E6%8E%A5%E7%AC%A6-delimiter)                                                                                                                                 |
| labelField             | `string`                                                                                                                                          | `"label"`        | [选项标签字段](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E9%80%89%E9%A1%B9%E6%A0%87%E7%AD%BE%E5%AD%97%E6%AE%B5-labelfield)                                                                                               |
| valueField             | `string`                                                                                                                                          | `"value"`        | [选项值字段](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E9%80%89%E9%A1%B9%E5%80%BC%E5%AD%97%E6%AE%B5-valuefield)                                                                                                          |
| iconField              | `string`                                                                                                                                          | `"icon"`         | 图标值字段                                                                                                                                                                                                                                |
| joinValues             | `boolean`                                                                                                                                         | `true`           | [拼接值](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E6%8B%BC%E6%8E%A5%E5%80%BC-joinvalues)                                                                                                                                |
| extractValue           | `boolean`                                                                                                                                         | `false`          | [提取值](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E6%8F%90%E5%8F%96%E5%A4%9A%E9%80%89%E5%80%BC-extractvalue)                                                                                                            |
| creatable              | `boolean`                                                                                                                                         | `false`          | [新增选项](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E5%89%8D%E7%AB%AF%E6%96%B0%E5%A2%9E-creatable)                                                                                                                      |
| extraActions            | SchemaNode[]                                                                       |                  | 从`1.24.0`版本支持该属性，支持自定义额外的操作项                                                                        | `1.24.0`|
| addControls            | Array<[表单项](/dataseeddesigndocui/#/amis/zh-CN/components/form/formitem)>                                                                       |                  | [自定义新增表单项](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E8%87%AA%E5%AE%9A%E4%B9%89%E6%96%B0%E5%A2%9E%E8%A1%A8%E5%8D%95%E9%A1%B9-addcontrols)                                                                        |
| addDialog     | `Schema`               |              | [配置新增弹框其它属性](/dataseeddesigndocui/#/amis/zh-CN/components/form/options?anchor=%E9%85%8D%E7%BD%AE%E6%96%B0%E5%A2%9E%E5%BC%B9%E6%A1%86%E5%85%B6%E5%AE%83%E5%B1%9E%E6%80%A7)  |
| addApi                 | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api)                                                                                           |                  | [配置新增选项接口](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E9%85%8D%E7%BD%AE%E6%96%B0%E5%A2%9E%E6%8E%A5%E5%8F%A3-addapi)                                                                                               |
| editable               | `boolean`                                                                                                                                         | `false`          | [编辑选项](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E5%89%8D%E7%AB%AF%E7%BC%96%E8%BE%91-editable)                                                                                                                       |
| editControls           | Array<[表单项](/dataseeddesigndocui/#/amis/zh-CN/components/form/formitem)>                                                                       |                  | [自定义编辑表单项](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E8%87%AA%E5%AE%9A%E4%B9%89%E7%BC%96%E8%BE%91%E8%A1%A8%E5%8D%95%E9%A1%B9-editcontrols)                                                                       |
| editDialog     | `Schema`               |              | [配置编辑弹框其它属性](/dataseeddesigndocui/#/amis/zh-CN/components/form/options?anchor=%E9%85%8D%E7%BD%AE%E7%BC%96%E8%BE%91%E5%BC%B9%E6%A1%86%E5%85%B6%E5%AE%83%E5%B1%9E%E6%80%A7)  |
| editApi                | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api)                                                                                           |                  | [配置编辑选项接口](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E9%85%8D%E7%BD%AE%E7%BC%96%E8%BE%91%E6%8E%A5%E5%8F%A3-editapi)                                                                                              |
| removable              | `boolean`                                                                                                                                         | `false`          | [删除选项](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E5%88%A0%E9%99%A4%E9%80%89%E9%A1%B9)                                                                                                                                |
| deleteApi              | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api)                                                                                           |                  | [配置删除选项接口](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E9%85%8D%E7%BD%AE%E5%88%A0%E9%99%A4%E6%8E%A5%E5%8F%A3-deleteapi)                                                                                            |
| disabled               | `boolean`                                                                                                                                         | `false`          | 是否禁用                                                                                                                                                                                                                                  |                                |
| searchable             | `boolean`                                                                                                                                         | `false`          | 是否可检索                                                                                                                                                                                                                                |                                |
| searchConfig            | `object`                                                                                                                                         |           | 搜索框配置（当searchable:true时，生效）                                                                                                                                                                                                                                |                                |
| hideRoot               | `boolean`                                                                                                                                         | `true`           | 如果想要显示个顶级节点，请设置为 `false`                                                                                                                                                                                                  |
| rootLabel              | `boolean`                                                                                                                                         | `"顶级"`         | 当 `hideRoot` 不为 `false` 时有用，用来设置顶级节点的文字。                                                                                                                                                                               |
| showIcon               | `boolean`                                                                                                                                         | `true`           | 是否显示图标                                                                                                                                                                                                                              |
| showRadio              | `boolean`                                                                                                                                         | `false`          | 是否显示单选按钮，`multiple` 为 `false` 是有效。                                                                                                                                                                                          |
| showOutline            | `boolean`                                                                                                                                         | `false`          | 是否显示树层级展开线                                                                                                                                                                                                                      |
| initiallyOpen          | `boolean`                                                                                                                                         | `true`           | 设置是否默认展开所有层级。                                                                                                                                                                                                                |
| unfoldedLevel          | `number`                                                                                                                                          | `1`              | 设置默认展开的级数，只有`initiallyOpen`不是`true`时生效。  从`1.24.0`版本开始支持取变量                                                                                                                                                                               |`1.24.0`|
| autoCheckChildren      | `boolean`                                                                                                                                         | `true`           | 当选中父节点时级联选择子节点。                                                                                                                                                                                                            |
| cascade                | `boolean`                                                                                                                                         | `false`          | autoCheckChildren 为 true 时生效；默认行为：子节点禁用，值只包含父节点值；设置为 true 时，子节点可反选，值包含父子节点值。                                                                                                                |
| withChildren           | `boolean`                                                                                                                                         | `false`          | cascade 为 false 时生效，选中父节点时，值里面将包含父子节点的值，否则只会保留父节点的值。                                                                                                                                                 |
| onlyChildren           | `boolean`                                                                                                                                         | `false`          | autoCheckChildren 为 true 时生效，不受 cascade 影响；onlyChildren 为 true，ui 行为级联选中子节点，子节点可反选，值只包含子节点的值。                                                                                                      |
| onlyLeaf               | `boolean`                                                                                                                                         | `false`          | 只允许选择叶子节点                                                                                                                                                                                                                        |
| rootCreatable          | `boolean`                                                                                                                                         | `false`          | 是否可以创建顶级节点                                                                                                                                                                                                                      |
| rootCreateTip          | `string`                                                                                                                                          | `"添加一级节点"` | 创建顶级节点的悬浮提示                                                                                                                                                                                                                    |
| minLength              | `number`                                                                                                                                          |                  | 最少选中的节点数                                                                                                                                                                                                                          |
| maxLength              | `number`                                                                                                                                          |                  | 最多选中的节点数                                                                                                                                                                                                                          |
| treeContainerClassName | `string`                                                                                                                                          |                  | tree 最外层容器类名                                                                                                                                                                                                                       |
| enableNodePath         | `boolean`                                                                                                                                         | `false`          | 是否开启节点路径模式                                                                                                                                                                                                                      |
| pathSeparator          | `string`                                                                                                                                          | `/`              | 节点路径的分隔符，`enableNodePath`为`true`时生效                                                                                                                                                                                          |
| highlightTxt           | `string`                                                                                                                                          |                  | 标签中需要高亮的字符，支持变量                                                                                                                                                                                                            |
| itemHeight             | `number`                                                                                                                                          | `32`             | 每个选项的高度，用于虚拟渲染                                                                                                                                                                                                              |
| virtualThreshold       | `number`                                                                                                                                          | `100`            | 在选项数量超过多少时开启虚拟渲染                                                                                                                                                                                                          |
| menuTpl                | `string \| schemaNode`                                                                                                                                          |                  | 选项自定义渲染节点，支持schema。在虚拟滚动场景下如果高度未被撑开，请设置itemHeight                                                                                                                                                                                                                  |                                |
| enableDefaultIcon      | `boolean`                                                                                                                                         | `true`           | 是否为选项添加默认的前缀 Icon，父节点默认为`folder`，叶节点默认为`file`                                                                                                                                                                   |                                |
| switcherIcon           | [模板](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/template) \| [Schema](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode#schema-配置) |                  | 自定义展开/收起图标。如果传入模板，则其将作为`Icon`组件的 [icon 属性值](/dataseeddesigndocui/#/amis/zh-CN/components/icon#属性表)；如果传入`Schema`，请传入代表一个 [Icon 的 Schema](/dataseeddesigndocui/#/amis/zh-CN/components/icon)。 | `TreeSelect` 自 1.2.0 开始支持 |
| defaultExpandedValues  | `Array<any>`                                                                                                                                      |                  | 默认展开的选项的 value 值，其祖先选项也会展开。（`TreeSelect` 不支持）                                                                                                                                                                    |                                |
| draggable              | boolean                                                                                                                                           | false            | 是否开启拖拽功能（`TreeSelect` 不支持）                                                                                                                                                                                                   |
| dragApi                | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api)                                                                                           |                  | 配置调整选项顺序接口，请求参数中包含两个值：`item`（被拖拽选项）和 `position`（树结构调整后 item 最终所在的位置），调整树结构时可以先删除 `item`，再根据 `position` 插入 item。（`TreeSelect` 不支持）                                    |
| dropOn                 | [表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression)                                                                              |                  | 表达式结果为真，则进行调整树结构；否则，放弃调整树结构。表达式中可访问 dragItem 和 dropItem。（`TreeSelect` 不支持）                                                                                                                      |
| autoFillHeight                 | `boolean`                          |                  | tree的高度继承父级treeContainer                                          |  `1.29.0`    |
| showActionsOnDisabled | `boolean` | false | 禁用状态下是否展示操作按钮，仅对自定义按钮（`extraActions`）生效 | `1.56.0` |
| canCancelSelectedNode | `boolean` | false | 单选场景下点击选中的节点是否取消选中 | `1.57.0` |
| itemDisabledOn | `expression` |                  | 控制单个选项是否禁用，支持表达式，表达式中可以获取option上的数据。 | `1.58.0` |
| itemClassNameExpr    | [模板](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/template)           |                           | 通过模板给行添加 CSS 类名。可以通过`__folded`、`__checked`、`__item`、`__options`分别获取折叠状态、选中状态、当前行数据、全量数据（`1.70.0`）。         |     `1.59.0`     |



### 事件表

当前组件会对外派发以下事件，可以通过`onEvent`来监听这些事件，并通过`actions`来配置执行的动作，在`actions`中可以通过`${事件参数名}`来获取事件产生的数据，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action)。

> `[name]`表示当前组件绑定的名称，即`name`属性，如果没有配置`name`属性，则通过`value`取值。

| 事件名称     | 事件参数                                                                                                                                                          | 说明                                                                                                                                   | 版本 |
| ------------ | ----------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------- | ---- |
| change       | `[name]: string` 组件的值                                                                                                                                         | 选中值变化时触发                                                                                                                       |      |
| dragStart    | `item: object` 被拖拽的节点                                                                                                                                       | 开始拖拽节点时在被拖拽节点上触发（`TreeSelect` 不支持）                                                                                |      |
| dragEnd      | `item: object` 被拖拽的节点                                                                                                                                       | 结束或取消拖拽节点时在被拖拽节点上触发（`TreeSelect` 不支持）                                                                          |      |
| dragEnter    | `item: object \| null` 放置节点，如果节点被拖拽到第一层节点，则此值为 `null`                                                                                      | 拖拽内容进入放置节点热区时在放置节点上触发（`TreeSelect` 不支持）                                                                      |      |
| dragOver     | `item: object \| null` 放置节点，如果节点被拖拽到第一层节点，则此值为 `null`                                                                                      | 拖拽内容在放置节点热区内移动时在放置节点上触发（`TreeSelect` 不支持）                                                                  |      |
| dragLeave    | `item: object \| null` 放置节点，如果节点被拖拽到第一层节点，则此值为 `null`                                                                                      | 拖拽内容离开放置节点热区时在放置节点上触发（`TreeSelect` 不支持）                                                                      |      |
| dropSuccess  | `dropItem: object \| null` 放置节点，如果节点被拖拽到第一层节点，则此值为 `null` <br/>`dragItem: object` 被拖拽的节点 <br/>`options: object[]` 被调整后的选项数据 | 移动节点成功时触发（`TreeSelect` 不支持）                                                                                              |      |
| dropFail     | `dropItem: object \| null` 放置节点，如果节点被拖拽到第一层节点，则此值为 `null` <br/>`dragItem: object` 被拖拽的节点 <br/>`reason: string` 失败原因              | 移动节点失败时触发<br/>`reason` 枚举：<br/>1. `"dropOn returns falsy value"`<br/>2. `"request error: xxx"`<br/>（`TreeSelect` 不支持） |      |
| expand       | `item: object`被操作的节点<br/>`expanded: boolean`节点被操作后是否为展开状态                                                                                      | 手动展开/收起节点时触发（`TreeSelect` 不支持）                                                                                         |      |
| rightClick   | `item: object`被右键点击的选项                                                                                                                                    | 右键点击节点时触发（`TreeSelect` 不支持）                                                                                              |      |
| add          | `items: object[]`选项集合<br/>`[name]: object` 新增的节点信息                                                                                                     | 新增节点提交时触发                                                                                                                     |      |
| edit         | `items: object[]`选项集合<br/>`[name]: object` 编辑的节点信息                                                                                                     | 编辑节点提交时触发                                                                                                                     |      |
| delete       | `items: object[]`选项集合<br/>`[name]: object` 删除的节点信息                                                                                                     | 删除节点提交时触发                                                                                                                     |      |
| loadFinished | `[name]: object` deferApi 懒加载远程请求成功后返回的数据                                                                                                          | 懒加载接口远程请求成功时触发                                                                                                           |      |

### 动作表

当前组件对外暴露以下特性动作，其他组件可以通过指定`actionType: 动作名称`、`componentId: 该组件id`来触发这些动作，动作配置可以通过`args: {动作配置项名称: xxx}`来配置具体的参数，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action#触发其他组件的动作)。

| 动作名称 | 动作配置                               | 说明                                                                                    | 版本 |
| -------- | -------------------------------------- | --------------------------------------------------------------------------------------- | ---- |
| expand   | openLevel: `number` <br/> openValues: `Array<number>`                    | `openLevel`: 展开指定层级；`openValues`: 展开指定节点的value值。同时配置时仅`openValues`生效。            |  `1.59.0`版本支持`openValues`    |
| collapse | -                                      | 收起                                                                                    |      |
| clear    | -                                      | 清空                                                                                    |      |
| reset    | -                                      | 将值重置为`resetValue`，若没有配置`resetValue`，则清空                                  |      |
| setValue | `value: string` \| `string[]` 更新的值 | 更新数据，开启`multiple`支持设置多项，开启`joinValues`时，多值用`,`分隔，否则多值用数组 |      |
| setSearchValue | `searchValue: string` 设置的值 | 设置搜索栏数据 |  `1.27.0`    |


### 展开指定节点

`expand`动作配置`openValues`指定展开的选项，被指定项的祖先选项都会展开。

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "labelWidth": 40,
    "body": [
      {
        "type": "button",
        "label": "展开",
        "className": "mb-4",
        "onEvent": {
          "click": {
            "actions": [
              {
                "actionType": "expand",
                "componentId": "expand_input_tree",
                "args": {
                  "openValues": [3],
                }
              }
            ]
          }
        }
      },
      {
        "type": "input-tree",
        "name": "tree",
        "label": "Tree",
        "initiallyOpen": false,
        "options": [
          {
            "label": "Folder A",
            "value": 1,
            "children": [
              {
                "label": "file A",
                "value": 2
              },
              {
                "label": "Folder B",
                "value": 3,
                "children": [
                  {
                    "label": "file b1",
                    "value": 3.1
                  },
                  {
                    "label": "file b2",
                    "value": 3.2,
                    "children": [
                      {
                        "label": "file b2_1",
                        "value": 3.11
                      }
                    ]
                  }
                ]
              }
            ]
          },
          {
            "label": "file C",
            "value": 4,
            "children": [
              {
                "label": "file A",
                "value": 4.1
              }
            ]
          },
          {
            "label": "file D",
            "value": 5
          }
        ],
        "value": 5,
        "id": "expand_input_tree"
      }
    ]
  }

}
```
