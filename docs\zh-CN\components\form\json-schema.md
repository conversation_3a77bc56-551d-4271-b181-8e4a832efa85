---
title: JSONSchema
description:
type: 0
group: null
menuName: JSONSchema
icon:
order: 61
standardMode: true
---

## 基本用法

> 此组件还在实验阶段，很多 json-schema 属性没有对应实现，使用前请先确认你要的功能满足了需求

基于 json-schema 定义生成表单输入项。

```schema: scope="body"
{
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    debug: true,
    "body": [
        {
            "type": "json-schema",
            "name": "value",
            "label": "字段值",
            "schema": {
              type: 'object',
              properties: {
                id: {
                  type: 'number',
                  title: 'ID'
                },
                name: {
                  type: 'string',
                  title: '名称'
                },
                description: {
                  type: 'string',
                  title: '描述'
                }
              }
            }
        }
    ]
}
```

Schema 配置定义

```js
{
  "schema": {
    "type": "object", // 数据类型：支持 object | array | number | integer | string | boolean
    "additionalProperties": false, // 是否可以新增自定义属性
    "propertiesRemovable": false, // properties中配置的属性是否可移除
    "required": [ // 对象中的必填字段
      "id",
      "name"
    ],
    "properties": { // object 数据的属性配置
      "id": { // 属性 name
        "type": "integer", 
        "title": "ID" // 属性 展示 title
        "min": 10,  // 最小值
        "max": 100000, // 最大值
        "step": 1, // 数字输入框右侧分步按钮，点一次的变化值
      },
      "price": {
        "type": "number",
        "title": "价格",
        "min": 10,  // 最小值
        "max": 100, // 最大值
        "step": 10, // 数字输入框右侧分步按钮，点一次的变化值
        "precision": 2, // 保留小数
      },
      "name": {
        "type": "string",
        "title": "名称"
      },
       "status": {
        "type": "boolean",
        "title": "信息状态",
        "default": false, // 默认值
        "trueText": "保密", // true时 文案
        "falseText": "公开",// false时 文案
      },
      "tag": {
        "type": "array",
        "title": "个人标签",
        "items": { // array 数据项配置
          "type": "string",
          "required": true, // 数组项必填（required:true 仅支持非object,array的基础项）
        },
        "minContains": 2, // 最小数据条数
        "maxContains": 5 // 最大数据条数
      }
    }
  }
}
```

## 复杂 case

```schema: scope="body"
{
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    debug: true,
    "body": [
        {
            "type": "json-schema",
            "name": "value",
            "label": "字段值",
            "schema": {
              type: 'object',
              additionalProperties: false,
              required: ['id', 'name'],
              properties: {
                id: {
                  type: 'number',
                  title: 'ID'
                },
                name: {
                  type: 'string',
                  title: '名称'
                },
                description: {
                  type: 'string',
                  title: '描述'
                },
                date: {
                  type: 'object',
                  title: '日期',
                  additionalProperties: false,
                  required: ['year', 'month', 'day'],
                  properties: {
                    year: {
                      type: 'number',
                      title: '年'
                    },
                    month: {
                      type: 'number',
                      title: '月'
                    },
                    day: {
                      type: 'number',
                      title: '日'
                    }
                  }
                },
                tag: {
                  type: 'array',
                  title: '个人标签',
                  items: {
                    type: 'string'
                  },
                  minContains: 2,
                  maxContains: 10
                }
              }
            }
        }
    ]
}
```

## 远程获取 schema

```schema: scope="body"
{
    "type": "form",
    debug: true,
    "body": [
        {
            "type": "json-schema",
            "name": "value",
            "label": "字段值",
            "schema": "/api/mock2/json-schema"
        }
    ]
}
```

## 属性表

| 属性名 | 类型                 | 默认值 | 说明             | 版本             |
| ------ | -------------------- | ------ | ---------------- | ---------------- |
| schema | `object` \| `表达式` \| [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api) |        | 指定 json-schema |       |
| namePlaceholder | `string` |        | 属性名输入框占位提示文本 |    1.63.0   |
| valuePlaceholder | `string` |        | 属性值输入框占位提示文本，可以取到当前项配置的`schema`数据域 |  1.63.0     |
| defaultExpanded | `boolean` |        | 存在嵌套`object \| array` 是否默认展开 |   1.63.0    |
