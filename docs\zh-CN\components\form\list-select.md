---
title: ListSelect 列表
description:
type: 0
group: null
menuName: ListSelect
icon:
order: 29
standardMode: true
---

ListSelect 一般用来实现选择，可以单选也可以多选，和 Radio/Checkboxs 最大的不同是在展现方面支持带图片。

## 基本用法

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "list-select",
      "name": "select",
      "label": "单选",
      "clearable": true,
      "options": [
        {
          "label": "Option A",
          "value": "a"
        },
        {
          "label": "Option B",
          "value": "b"
        }
      ]
    }
  ]
}
```

## 选项带图片

```schema: scope="body"
{
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "list-select",
        "name": "select",
        "label": "图片",
        "options": [
          {
            "label": "OptionA",
            "value": "a",
            "image": "https://suda.cdn.bcebos.com/amis/images/alice-macaw.jpg"
          },
          {
            "label": "OptionB",
            "value": "b",
            "image": "https://suda.cdn.bcebos.com/amis/images/alice-macaw.jpg"
          },
          {
            "label": "OptionC",
            "value": "c",
            "image": "https://suda.cdn.bcebos.com/amis/images/alice-macaw.jpg"
          },
          {
            "label": "OptionD",
            "value": "d",
            "image": "https://suda.cdn.bcebos.com/amis/images/alice-macaw.jpg"
          }
        ]
      }
    ]
}
```

## card模式

通过设置 `itemMode` 为 `card` 开启卡片模式，卡片模式数据上需要配置 `title` 和 `description` 属性。

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "list-select",
        "name": "select",
        "label": "单选",
        "clearable": true,
        "itemMode": "card",
        "options": [
          {
            "title": "Option A",
            "value": "a",
            "description": "this is a text"
          },
          {
            "title": "Option B",
            "value": "b",
            "description": "this is a text"
          }
        ]
      }
    ]
  }
}
```


## 自定义选项模板

通过 `itemSchema` 可以自定义选项的渲染模板。

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "list-select",
        "name": "select",
        "label": "自定义选项模板",
        "itemSchema": {
          "type": "wrapper",
          "body": [
            {
              "type": "tpl",
              "tpl": "<h3>${title}</h3><p>${description}</p>"
            }
          ]
        },
        "options": [
          {
            "label": "Option A",
            "value": "a",
            "title": "Option A",
            "description": "this is a text"
          },
          {
            "label": "Option B",
            "value": "b",
            "title": "Option B",
            "description": "this is a text"
          }
        ]
      }
    ]
  }
}
```

## 属性表

当做选择器表单项使用时，除了支持 [普通表单项属性表](/dataseeddesigndocui/#/amis/zh-CN/components/form/formitem#%E5%B1%9E%E6%80%A7%E8%A1%A8) 中的配置以外，还支持下面一些配置

| 属性名        | 类型                                      | 默认值    | 说明                                                                                        |
| ------------- | ----------------------------------------- | --------- | ------------------------------------------------------------------------------------------- |
| options       | `Array<object>`或`Array<string>`          |           | [选项组](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E9%9D%99%E6%80%81%E9%80%89%E9%A1%B9%E7%BB%84-options)                   |
| source        | `string`或 [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api) |           | [动态选项组](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E5%8A%A8%E6%80%81%E9%80%89%E9%A1%B9%E7%BB%84-source)                |
| multiple      | `boolean`                                 | `false`   | [多选](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E5%A4%9A%E9%80%89-multiple)                                               |
| labelField    | `string`                                  | `"label"` | [选项标签字段](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E9%80%89%E9%A1%B9%E6%A0%87%E7%AD%BE%E5%AD%97%E6%AE%B5-labelfield) |
| valueField    | `string`                                  | `"value"` | [选项值字段](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E9%80%89%E9%A1%B9%E5%80%BC%E5%AD%97%E6%AE%B5-valuefield)            |
| joinValues    | `boolean`                                 | `true`    | [拼接值](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E6%8B%BC%E6%8E%A5%E5%80%BC-joinvalues)                                  |
| extractValue  | `boolean`                                 | `false`   | [提取值](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E6%8F%90%E5%8F%96%E5%A4%9A%E9%80%89%E5%80%BC-extractvalue)              |
| autoFill      | `object`                                  |           | [自动填充](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E8%87%AA%E5%8A%A8%E5%A1%AB%E5%85%85-autofill)                         |
| listClassName | `string`                                  |           | 支持配置 list div 的 css 类名。比如: `flex justify-between`                                 |
| itemSchema | [SchemaNode](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode) | | 自定义选项渲染模板 |
| itemMode | `card \| default` | `default` | 支持配置 list item 的模式。比如: `card`, `1.77.0` 版本支持 |
| sameOptionChange | `boolean` | | 选择相同的选项时是否触发 change 事件。`1.90.6` 版本支持 |

## 事件表

当前组件会对外派发以下事件，可以通过`onEvent`来监听这些事件，并通过`actions`来配置执行的动作，在`actions`中可以通过`${事件参数名}`来获取事件产生的数据，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action)。

> `[name]`表示当前组件绑定的名称，即`name`属性，如果没有配置`name`属性，则通过`value`取值。

| 事件名称 | 事件参数                  | 说明             |
| -------- | ------------------------- | ---------------- |
| change   | `[name]: string` 组件的值 | 选中值变化时触发 |

## 动作表

当前组件对外暴露以下特性动作，其他组件可以通过指定`actionType: 动作名称`、`componentId: 该组件id`来触发这些动作，动作配置可以通过`args: {动作配置项名称: xxx}`来配置具体的参数，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action#触发其他组件的动作)。

| 动作名称 | 动作配置                               | 说明                                                                                    |
| -------- | -------------------------------------- | --------------------------------------------------------------------------------------- |
| clear    | -                                      | 清空                                                                                    |
| reset    | -                                      | 将值重置为`resetValue`，若没有配置`resetValue`，则清空                                  |
| reload   | -                                      | 重新加载，调用 `source`，刷新数据域数据刷新（重新加载）                                 |
| setValue | `value: string` \| `string[]` 更新的值 | 更新数据，开启`multiple`支持设置多项，开启`joinValues`时，多值用`,`分隔，否则多值用数组 |
