---
title: MatrixCheckboxes 矩阵
description:
type: 0
group: null
menuName: MatrixCheckboxes
icon:
order: 30
standardMode: true
---

矩阵类型的输入框。

## 基本用法

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "debug": true,
  "body": [
    {
      "type": "matrix-checkboxes",
      "name": "matrix",
      "label": "Matrix",
      "rowLabel": "行标题说明",
      "columns": [
        {
          "label": "列1"
        },
        {
          "label": "列2"
        }
      ],
      "rows": [
        {
          "label": "行1"
        },
        {
          "label": "行2"
        }
      ]
    }
  ]
}
```

## 单选模式

配置`"multiple": false`可以设置单选，配置`singleSelectMode`可以设置单选模式

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "debug": true,
  "body": [
    {
      "type": "matrix-checkboxes",
      "name": "matrix",
      "label": "Matrix",
      "rowLabel": "行标题说明",
      "multiple": false,
      "columns": [
        {
          "label": "列1"
        },
        {
          "label": "列2"
        }
      ],
      "rows": [
        {
          "label": "行1"
        },
        {
          "label": "行2"
        }
      ]
    }
  ]
}
```

## 动态选项

可以配置 source 渲染动态选项

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
        "name": "matrix",
        "type": "matrix-checkboxes",
        "label": "动态矩阵组件",
        "source": "/api/mock2/options/matrix?waitSeconds=1"
    }
  ]
}
```

以上面为例，source 接口返回格式如下：

```json
{
  "status": 0,
  "msg": "ok",
  "data": {
    "columns": [
      {
        "label": "Col A",
        "col": "a"
      },
      {
        "label": "Col B",
        "col": "b"
      },
      {
        "label": "Col C",
        "col": "c"
      },
      {
        "label": "Col D",
        "col": "d"
      },
      {
        "label": "Col E",
        "col": "e"
      }
    ],
    "rows": [
      {
        "label": "Row 1",
        "rol": 1
      },
      {
        "label": "Row 2",
        "rol": 2
      },
      {
        "label": "Row 3",
        "rol": 3
      },
      {
        "label": "Row 4",
        "rol": 4
      },
      {
        "label": "Row 5",
        "rol": 5
      },
      {
        "label": "Row 6",
        "rol": 6
      }
    ]
  }
}
```

### column 模式

默认为 column 模式，即每列只能单选某个单元格

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "matrix-checkboxes",
      "name": "matrix",
      "label": "Matrix",
      "rowLabel": "行标题说明",
      "multiple": false,
      "columns": [
        {
          "label": "列1"
        },
        {
          "label": "列2"
        }
      ],
      "rows": [
        {
          "label": "行1"
        },
        {
          "label": "行2"
        }
      ]
    }
  ]
}
```

### cell 模式

cell 模式，指全部选项中只能单选某个单元格

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "matrix-checkboxes",
      "name": "matrix",
      "label": "Matrix",
      "rowLabel": "行标题说明",
      "multiple": false,
      "singleSelectMode": "cell",
      "columns": [
        {
          "label": "列1"
        },
        {
          "label": "列2"
        }
      ],
      "rows": [
        {
          "label": "行1"
        },
        {
          "label": "行2"
        }
      ]
    }
  ]
}
```

### row 模式

row 模式，每行只能单选某个单元格

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "matrix-checkboxes",
      "name": "matrix",
      "label": "Matrix",
      "rowLabel": "行标题说明",
      "multiple": false,
      "singleSelectMode": "row",
      "columns": [
        {
          "label": "列1"
        },
        {
          "label": "列2"
        }
      ],
      "rows": [
        {
          "label": "行1"
        },
        {
          "label": "行2"
        }
      ]
    }
  ]
}
```

## 框选选中

如果配置了 `dragSelect` 属性，则可以通过鼠标框选的方式选中多个选择框。

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "debug": true,
    "body": [
      {
        "name": "matrix",
        "type": "matrix-checkboxes",
        "label": "动态矩阵组件",
        "dragSelect": true,
        "source": "/api/mock2/options/matrix?waitSeconds=1"
      }
    ]
  }
}
```

## 开启行全选

如果配置了 `rowCheckAll` 属性，则可以开启行全选功能，每行的第一列会出现一个全选框。

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "debug": true,
    "body": [
      {
        "name": "matrix",
        "type": "matrix-checkboxes",
        "label": "动态矩阵组件",
        "dragSelect": true,
        "rowCheckAll": true,
        "source": "/api/mock2/options/matrix?waitSeconds=1"
      }
    ]
  }
}
```

## 选中样式

矩阵默认选中是勾选单元格里面的 `checkbox`，如果不需要展示 `checkbox` 想要高亮选中的单元格的背景，可以设置 `cellCheckedMode: "highlight"` 即可

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "debug": true,
    "body": [
      {
        "name": "matrix",
        "type": "matrix-checkboxes",
        "label": "动态矩阵组件",
        "dragSelect": true,
        "rowCheckAll": true,
        "cellCheckedMode": "highlight",
        "source": "/api/mock2/options/matrix?waitSeconds=1"
      }
    ]
  }
}
```

## 合并表头

可以在数据中配置 `colSpan` 属性来控制表头合并，被合并的表头不会展示，比如下面例子 `Col 1` 中 `colSpan` 为 2，所以它会合并后面的一列 `Col 2` 的所在列不会在视图中展示

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "debug": true,
    "body": [
      {
        "name": "matrix",
        "type": "matrix-checkboxes",
        "label": "动态矩阵组件",
        "dragSelect": true,
        "rowCheckAll": true,
        "cellCheckedMode": "highlight",
        "columns": [
          {
            "label": "Col 1",
            "name": "1",
            "colSpan": 2
          },
          {
            "label": "Col 2",
            "name": "2"
          },
          {
            "label": "Col 3",
            "name": "3",
            "colSpan": 2
          },
          {
            "label": "Col 4",
            "name": "4"
          }
        ],
        "rows": [
          {
            "label": "Row 1",
            "name": "1"
          },
          {
            "label": "Row 2",
            "name": "2"
          }
        ]
      }
    ]
  }
}
```

也可以更多列的合并

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "debug": true,
    "body": [
      {
        "name": "matrix",
        "type": "matrix-checkboxes",
        "label": "动态矩阵组件",
        "dragSelect": true,
        "rowCheckAll": true,
        "cellCheckedMode": "highlight",
        "columns": [
          {
            "label": "Col 1",
            "name": "1",
            "colSpan": 3
          },
          {
            "label": "Col 2",
            "name": "2"
          },
          {
            "label": "Col 3",
            "name": "3"
          },
          {
            "label": "Col 4",
            "name": "4"
          }
        ],
        "rows": [
          {
            "label": "Row 1",
            "name": "1"
          },
          {
            "label": "Row 2",
            "name": "2"
          }
        ]
      }
    ]
  }
}
```

同样的 `rows` 数据中也支持配行的合并 `rowSpan`，使用方式和 `colSpan` 一样

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "debug": true,
    "body": [
      {
        "name": "matrix",
        "type": "matrix-checkboxes",
        "label": "动态矩阵组件",
        "dragSelect": true,
        "rowCheckAll": true,
        "cellCheckedMode": "highlight",
        "columns": [
          {
            "label": "Col 1",
            "name": "1"
          },
          {
            "label": "Col 2",
            "name": "2"
          },
          {
            "label": "Col 3",
            "name": "3"
          },
          {
            "label": "Col 4",
            "name": "4"
          }
        ],
        "rows": [
          {
            "label": "Row 1",
            "name": "1",
            "rowSpan": 2
          },
          {
            "label": "Row 2",
            "name": "2"
          },
          {
            "label": "Row 3",
            "name": "3",
            "rowSpan": 2
          },
          {
            "label": "Row 4",
            "name": "4"
          }
        ]
      }
    ]
  }
}
```

**注意：columns 只能配置 colSpan，rows 只能配置 rowSpan**

## 属性表

除了支持 [普通表单项属性表](/dataseeddesigndocui/#/amis/zh-CN/components/form/formitem#%E5%B1%9E%E6%80%A7%E8%A1%A8) 中的配置以外，还支持下面一些配置

| 属性名           | 类型                           | 默认值     | 说明                                                                                                                                                        |
| ---------------- | ------------------------------ | ---------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------- |
| columns          | `Array<column>`                |            | 列信息，数组中 `label` 字段是必须给出的                                                                                                                     |
| rows             | `Array<row>`                   |            | 行信息， 数组中 `label` 字段是必须给出的                                                                                                                    |
| rowLabel         | `string`                       |            | 行标题说明                                                                                                                                                  |
| source           | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api) |            | Api 地址，如果选项组不固定，可以通过配置 `source` 动态拉取。                                                                                                |
| multiple         | `boolean`                      | `true`     | 是否多选                                                                                                                                                    |
| singleSelectMode | `string`                       | `"column"` | 设置单选模式，`multiple`为`false`时有效，可设置为`cell`, `row`, `column` 分别为全部选项中只能单选某个单元格、每行只能单选某个单元格，每列只能单选某个单元格 |
| dragSelect | `boolean`                       | `false` | 是否开启鼠标框选选择 `1.79.0` 版本支持 |
| rowCheckAll | `boolean`                       | `false` | 是否开启行全选 `1.79.0` 版本支持 |
| cellCheckedMode | `"checkbox" \| "highlight"` | `"checkbox"` | 单元格选中的样式 `1.79.0` 版本支持 |
| columnConfig | Object | - | 行的一些配置 `1.79.0` 版本支持 |
| columnConfig.sticky | `boolean` | `false` | 固定每行的第一列 `1.79.0` 版本支持 |
| columnConfig.width | `number` | 无 | 框选区域内每一列的宽度 `1.81.0` 版本支持 |
| compactMode | `boolean` | `false` | 是否开启紧凑模式 `1.79.0` 版本支持 |
| clearable | `boolean` | `false` | 是否开启清除按钮 `1.79.0` 版本支持 |

## 事件表

当前组件会对外派发以下事件，可以通过`onEvent`来监听这些事件，并通过`actions`来配置执行的动作，在`actions`中可以通过`${事件参数名}`来获取事件产生的数据，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action)。

> `[name]`表示当前组件绑定的名称，即`name`属性，如果没有配置`name`属性，则通过`value`取值。

| 事件名称 | 事件参数                 | 说明             |
| -------- | ------------------------ | ---------------- |
| change   | `[name]: Array` 组件的值 | 选中值变化时触发 |

## 动作表

当前组件对外暴露以下特性动作，其他组件可以通过指定`actionType: 动作名称`、`componentId: 该组件id`来触发这些动作，动作配置可以通过`args: {动作配置项名称: xxx}`来配置具体的参数，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action#触发其他组件的动作)。

| 动作名称 | 动作配置                | 说明                                                    |
| -------- | ----------------------- | ------------------------------------------------------- |
| clear    | -                       | 清空                                                    |
| reset    | -                       | 将值重置为`resetValue`，若没有配置`resetValue`，则清空  |
| reload   | -                       | 重新加载，调用 `source`，刷新数据域数据刷新（重新加载） |
| setValue | `value: Array` 更新的值 | 更新数据                                                |
