---
title: Select 选择器
description:
type: 0
group: null
menuName: Select 选择器
icon:
order: 48
---

## 场景推荐

### 基本用法

option内容超出后会出现省略号和Tooltip，未超出则不出现

```schema: scope="body"
{
  "type": "form",
  "actions": [],
  "body": [
    {
      "label": "选项",
      "type": "select",
      "name": "select",
      "menuTpl": {
        "type": "typography",
        "text": "${label}"
      },
      "options": [
        {
          "label": "IEInternet Explorer（简称：IE）是微软公司推出的一款网页浏览器。原称Microsoft Internet Explorer（6版本以前）和Windows Internet Explorer（7、8、9、10、11版本）。在IE7以前，中文直译为“网络探路者”，但在IE7以后官方便直接俗称",
          "value": "IE"
        },
        {
          "label": "FIREFOX",
          "value": "FIREFOX"
        },
        {
          "label": "CHROME(Google Chrome是一款由Google公司开发的网页浏览器。 该浏览器基于其他开源软件如WebKit撰写，目标是提升稳定性、速度和安全性，并创造出简单且有效率的使用者界面。)",
          "value": "CHROME"
        },
      ]
    }
  ]
}
```

### 带辅助信息

#### 双行展示

```schema: scope="body"
{
  "type": "form",
  "actions": [],
  "body": [
    {
      "label": "选项",
      "type": "select",
      "itemHeight": 60,
      "labelField": "userName",
      "valueField": "userId",
      "name": "select",
      "multiple": true,
      "menuTpl": {
        "type": "tooltip-wrapper",
        "inline": true,
        "content": {
          "type": "flex",
          "direction": "column",
          "alignItems": "flex-start",
          "items": [
            {
              "type": "tpl",
              "tpl": "${userName}"
            },
            {
              "type": "tpl",
              "tpl": "${org}"
            },
            {
              "type": "tpl",
              "tpl": "${date}"
            }
          ]
        },
        "body": {
          "type": "flex",
          "direction": "column",
          "alignItems": "flex-start",
          "items": [
            {
              "type": "tpl",
              "tpl": "${userName}"
            },
            {
              "type": "tpl",
              "tpl": "${org}"
            }
          ]
        }
      },
      "options": [
        {
          "userName": "张三",
          "userId": "a",
          "org": "数禾/技术中心/信贷中台/信贷前置组/研发部",
          "date": "入职日期: 2022年9月1日"
        },
        {
          "userName": "李四",
          "userId": "b",
          "org": "数禾/运维部",
          "date": "入职日期: 2022年9月2日"
        },
        {
          "userName": "王五",
          "userId": "c",
          "org": "数禾/财务部",
          "date": "入职日期: 2022年9月3日"
        },
        {
          "userName": "张三",
          "userId": "a1",
          "org": "数禾/技术中心/信贷中台/信贷前置组/研发部",
          "date": "入职日期: 2022年9月1日"
        },
        {
          "userName": "李四",
          "userId": "b1",
          "org": "数禾/运维部",
          "date": "入职日期: 2022年9月2日"
        },
        {
          "userName": "王五",
          "userId": "c1",
          "org": "数禾/财务部",
          "date": "入职日期: 2022年9月3日"
        },
        {
          "userName": "张三",
          "userId": "a2",
          "org": "数禾/技术中心/信贷中台/信贷前置组/研发部",
          "date": "入职日期: 2022年9月1日"
        },
        {
          "userName": "李四",
          "userId": "b2",
          "org": "数禾/运维部",
          "date": "入职日期: 2022年9月2日"
        },
        {
          "userName": "王五",
          "userId": "c2",
          "org": "数禾/财务部",
          "date": "入职日期: 2022年9月3日"
        },
        {
          "userName": "张三",
          "userId": "a3",
          "org": "数禾/技术中心/信贷中台/信贷前置组/研发部",
          "date": "入职日期: 2022年9月1日"
        },
        {
          "userName": "李四",
          "userId": "b3",
          "org": "数禾/运维部",
          "date": "入职日期: 2022年9月2日"
        },
        {
          "userName": "王五",
          "userId": "c3",
          "org": "数禾/财务部",
          "date": "入职日期: 2022年9月3日"
        },
        {
          "userName": "张三",
          "userId": "a4",
          "org": "数禾/技术中心/信贷中台/信贷前置组/研发部",
          "date": "入职日期: 2022年9月1日"
        },
        {
          "userName": "李四",
          "userId": "b4",
          "org": "数禾/运维部",
          "date": "入职日期: 2022年9月2日"
        },
        {
          "userName": "王五",
          "userId": "c4",
          "org": "数禾/财务部",
          "date": "入职日期: 2022年9月3日"
        },
        {
          "userName": "张三",
          "userId": "a5",
          "org": "数禾/技术中心/信贷中台/信贷前置组/研发部",
          "date": "入职日期: 2022年9月1日"
        },
        {
          "userName": "李四",
          "userId": "b5",
          "org": "数禾/运维部",
          "date": "入职日期: 2022年9月2日"
        },
        {
          "userName": "王五",
          "userId": "c5",
          "org": "数禾/财务部",
          "date": "入职日期: 2022年9月3日"
        },
        {
          "userName": "张三",
          "userId": "a6",
          "org": "数禾/技术中心/信贷中台/信贷前置组/研发部",
          "date": "入职日期: 2022年9月1日"
        },
        {
          "userName": "李四",
          "userId": "b6",
          "org": "数禾/运维部",
          "date": "入职日期: 2022年9月2日"
        },
        {
          "userName": "王五",
          "userId": "c6",
          "org": "数禾/财务部",
          "date": "入职日期: 2022年9月3日"
        }
      ]
    }
  ]
}
```

#### 动态渲染

```schema: scope="body"
{
  "type": "form",
  "actions": [],
  "body": [
    {
      "label": "选项",
      "type": "select",
      "itemHeight": 60,
      "labelField": "userName",
      "valueField": "userId",
      "name": "select",
      "menuTpl": {
        "type": "tooltip-wrapper",
        "inline": true,
        "content": {
          "type": "flex",
          "direction": "column",
          "alignItems": "flex-start",
          "items": [
            {
              "type": "tpl",
              "tpl": "${userName}"
            },
            {
              "type": "each",
              "name": "orgs",
              "items": [
                {
                  "type": "tpl",
                  "tpl": "<div>${orgName}</div>"
                }
              ]
            }
          ]
        },
        "body": {
          "type": "flex",
          "direction": "column",
          "alignItems": "flex-start",
          "items": [
            {
              "type": "tpl",
              "tpl": "${userName}"
            },
            {
              "type": "each",
              "name": "orgs",
              "items": [
                {
                  "type": "tpl",
                  "tpl": "${orgName}"
                }
              ]
            }
          ]
        }
      },
      "options": [
        {
          "userName": "张三",
          "userId": "a",
          "orgs": [
            {
              "orgName": "数禾/技术中心/信贷中台/信贷前置组/研发部",
              "orgId": "1"
            },
            {
              "orgName": "数禾/技术中心/信贷中台/信贷前置组/研发部",
              "orgId": "2"
            },
            {
              "orgName": "数禾/技术中心/信贷中台/信贷前置组/研发部",
              "orgId": "3"
            }
          ],
          "date": "入职日期: 2022年9月1日"
        },
        {
          "userName": "李四",
          "userId": "b",
          "orgs": [
            {
              "orgName": "数禾/技术中心/信贷中台/信贷前置组/研发部",
              "orgId": "1"
            },
            {
              "orgName": "数禾/技术中心/信贷中台/信贷前置组/研发部",
              "orgId": "2"
            },
            {
              "orgName": "数禾/技术中心/信贷中台/信贷前置组/研发部",
              "orgId": "3"
            }
          ],
          "date": "入职日期: 2022年9月2日"
        },
        {
          "userName": "王五",
          "userId": "c",
          "orgs": [
            {
              "orgName": "数禾/技术中心/信贷中台/信贷前置组/研发部",
              "orgId": "1"
            },
            {
              "orgName": "数禾/技术中心/信贷中台/信贷前置组/研发部",
              "orgId": "2"
            },
            {
              "orgName": "数禾/技术中心/信贷中台/信贷前置组/研发部",
              "orgId": "3"
            }
          ],
          "date": "入职日期: 2022年9月3日"
        }
      ]
    }
  ]
}
```

## 组件用法

### 基本用法

参考 [Options](/dataseeddesigndocui/#/amis/zh-CN/components/form/options)

### 自定义菜单

```schema: scope="body"
{
    "type": "form",
    "body": [
        {
            "label": "选项",
            "type": "select",
            "name": "select",
            "menuTpl": "<div>${label} 值：${value}, 当前是否选中: ${checked}</div>",
            "options": [
                {
                    "label":"A",
                    "value":"a"
                },
                {
                    "label":"B",
                    "value":"b"
                },
                {
                    "label":"C",
                    "value":"c"
                }
            ]
        }
    ]
}
```

#### 分组展示模式

_单选_

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "label": "分组",
      "type": "select",
      "name": "a",
      "selectMode": "group",
      "options": [
        {
          "label": "法师",
          "children": [
            {
              "label": "诸葛亮",
              "value": "zhugeliang"
            }
          ]
        },
        {
          "label": "战士",
          "children": [
            {
              "label": "曹操",
              "value": "caocao"
            },
            {
              "label": "钟无艳",
              "value": "zhongwuyan"
            }
          ]
        },
        {
          "label": "打野",
          "children": [
            {
              "label": "李白",
              "value": "libai"
            },
            {
              "label": "韩信",
              "value": "hanxin"
            },
            {
              "label": "云中君",
              "value": "yunzhongjun"
            }
          ]
        }
      ]
    }
  ]
}
```

_多选_

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "label": "分组",
      "type": "select",
      "name": "a",
      "multiple": true,
      "selectMode": "group",
      "options": [
        {
          "label": "法师",
          "children": [
            {
              "label": "诸葛亮",
              "value": "zhugeliang"
            }
          ]
        },
        {
          "label": "战士",
          "children": [
            {
              "label": "曹操",
              "value": "caocao"
            },
            {
              "label": "钟无艳",
              "value": "zhongwuyan"
            }
          ]
        },
        {
          "label": "打野",
          "children": [
            {
              "label": "李白",
              "value": "libai"
            },
            {
              "label": "韩信",
              "value": "hanxin"
            },
            {
              "label": "云中君",
              "value": "yunzhongjun"
            }
          ]
        }
      ]
    }
  ]
}
```

#### 表格模式

需要额外配置 `columns` 配置，参考 Table 中的说明。

_单选_

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "label": "表格形式",
      "type": "select",
      "name": "a",
      "selectMode": "table",
      "columns": [
        {
          "name": "label",
          "label": "英雄"
        },
        {
          "name": "position",
          "label": "位置"
        }
      ],
      "options": [
        {
          "label": "诸葛亮",
          "value": "zhugeliang",
          "position": "中单"
        },
        {
          "label": "曹操",
          "value": "caocao",
          "position": "上单"
        },
        {
          "label": "钟无艳",
          "value": "zhongwuyan",
          "position": "上单"
        },
        {
          "label": "李白",
          "value": "libai",
          "position": "打野"
        },
        {
          "label": "韩信",
          "value": "hanxin",
          "position": "打野"
        },
        {
          "label": "云中君",
          "value": "yunzhongjun",
          "position": "打野"
        }
      ]
    }
  ]
}
```

_多选_

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "label": "表格形式",
      "type": "select",
      "name": "a",
      "selectMode": "table",
      "multiple": true,
      "columns": [
        {
          "name": "label",
          "label": "英雄"
        },
        {
          "name": "position",
          "label": "位置"
        }
      ],
      "options": [
        {
          "label": "诸葛亮",
          "value": "zhugeliang",
          "position": "中单"
        },
        {
          "label": "曹操",
          "value": "caocao",
          "position": "上单"
        },
        {
          "label": "钟无艳",
          "value": "zhongwuyan",
          "position": "上单"
        },
        {
          "label": "李白",
          "value": "libai",
          "position": "打野"
        },
        {
          "label": "韩信",
          "value": "hanxin",
          "position": "打野"
        },
        {
          "label": "云中君",
          "value": "yunzhongjun",
          "position": "打野"
        }
      ]
    }
  ]
}
```

#### 树形模式

_单选_

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "label": "树型展示",
      "type": "select",
      "name": "a",
      "selectMode": "tree",
      "options": [
        {
          "label": "法师",
          "children": [
            {
              "label": "诸葛亮",
              "value": "zhugeliang"
            }
          ]
        },
        {
          "label": "战士",
          "children": [
            {
              "label": "曹操",
              "value": "caocao"
            },
            {
              "label": "钟无艳",
              "value": "zhongwuyan"
            }
          ]
        },
        {
          "label": "打野",
          "children": [
            {
              "label": "李白",
              "value": "libai"
            },
            {
              "label": "韩信",
              "value": "hanxin"
            },
            {
              "label": "云中君",
              "value": "yunzhongjun"
            }
          ]
        }
      ]
    }
  ]
}
```

_多选_

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "label": "树型展示",
      "type": "select",
      "name": "a",
      "multiple": true,
      "selectMode": "tree",
      "options": [
        {
          "label": "法师",
          "children": [
            {
              "label": "诸葛亮",
              "value": "zhugeliang"
            }
          ]
        },
        {
          "label": "战士",
          "children": [
            {
              "label": "曹操",
              "value": "caocao"
            },
            {
              "label": "钟无艳",
              "value": "zhongwuyan"
            }
          ]
        },
        {
          "label": "打野",
          "children": [
            {
              "label": "李白",
              "value": "libai"
            },
            {
              "label": "韩信",
              "value": "hanxin"
            },
            {
              "label": "云中君",
              "value": "yunzhongjun"
            }
          ]
        }
      ]
    }
  ]
}
```

#### 级联选择

按列来点选。

_单选_

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "label": "级联选择",
      "type": "select",
      "name": "a",
      "selectMode": "chained",
      "options": [
        {
          "label": "法师",
          "children": [
            {
              "label": "诸葛亮",
              "value": "zhugeliang"
            }
          ]
        },
        {
          "label": "战士",
          "children": [
            {
              "label": "曹操",
              "value": "caocao"
            },
            {
              "label": "钟无艳",
              "value": "zhongwuyan"
            }
          ]
        },
        {
          "label": "打野",
          "children": [
            {
              "label": "李白",
              "value": "libai"
            },
            {
              "label": "韩信",
              "value": "hanxin"
            },
            {
              "label": "云中君",
              "value": "yunzhongjun"
            }
          ]
        }
      ]
    }
  ]
}
```

_多选_

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "label": "级联选择",
      "type": "select",
      "name": "a",
      "selectMode": "chained",
      "multiple": true,
      "options": [
        {
          "label": "法师",
          "children": [
            {
              "label": "诸葛亮",
              "value": "zhugeliang"
            }
          ]
        },
        {
          "label": "战士",
          "children": [
            {
              "label": "曹操",
              "value": "caocao"
            },
            {
              "label": "钟无艳",
              "value": "zhongwuyan"
            }
          ]
        },
        {
          "label": "打野",
          "children": [
            {
              "label": "李白",
              "value": "libai"
            },
            {
              "label": "韩信",
              "value": "hanxin"
            },
            {
              "label": "云中君",
              "value": "yunzhongjun"
            }
          ]
        }
      ]
    }
  ]
}
```

#### 支持搜索

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "label": "带搜索",
      "type": "select",
      "name": "a",
      "selectMode": "chained",
      "searchable": true,
      "sortable": true,
      "multiple": true,
      "options": [
        {
          "label": "法师",
          "children": [
            {
              "label": "诸葛亮",
              "value": "zhugeliang"
            }
          ]
        },
        {
          "label": "战士",
          "children": [
            {
              "label": "曹操",
              "value": "caocao"
            },
            {
              "label": "钟无艳",
              "value": "zhongwuyan"
            }
          ]
        },
        {
          "label": "打野",
          "children": [
            {
              "label": "李白",
              "value": "libai"
            },
            {
              "label": "韩信",
              "value": "hanxin"
            },
            {
              "label": "云中君",
              "value": "yunzhongjun"
            }
          ]
        }
      ]
    }
  ]
}
```

#### 延时加载

选型设置 defer: true，结合配置组件层的 `deferApi` 来实现。

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "label": "延时加载",
      "type": "select",
      "name": "a",
      "multiple": true,
      "selectMode": "tree",
      "deferApi": "/api/mock2/form/deferOptions?label=${label}&waitSeconds=2",
      "options": [
        {
          "label": "法师",
          "children": [
            {
              "label": "诸葛亮",
              "value": "zhugeliang"
            }
          ]
        },
        {
          "label": "战士",
          "defer": true
        },
        {
          "label": "打野",
          "children": [
            {
              "label": "李白",
              "value": "libai"
            },
            {
              "label": "韩信",
              "value": "hanxin"
            },
            {
              "label": "云中君",
              "value": "yunzhongjun"
            }
          ]
        }
      ]
    }
  ]
}
```

#### 关联选择模式

分为左右两部分，左边点选后关联出现右边。左右都可以配置展示模式。

_单选_

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "label": "关联选择模式",
      "type": "select",
      "name": "b",
      "sortable": true,
      "searchable": true,
      "deferApi": "/api/mock2/form/deferOptions?label=${label}",
      "selectMode": "associated",
      "leftMode": "tree",
      "leftOptions": [
        {
          "label": "法师",
          "children": [
            {
              "label": "诸葛亮",
              "value": "zhugeliang"
            }
          ]
        },
        {
          "label": "战士",
          "children": [
            {
              "label": "曹操",
              "value": "caocao"
            },
            {
              "label": "钟无艳",
              "value": "zhongwuyan"
            }
          ]
        },
        {
          "label": "打野",
          "children": [
            {
              "label": "李白",
              "value": "libai"
            },
            {
              "label": "韩信",
              "value": "hanxin"
            },
            {
              "label": "云中君",
              "value": "yunzhongjun"
            }
          ]
        }
      ],
      "options": [
        {
          "ref": "zhugeliang",
          "children": [
            {
              "label": "A",
              "value": "a"
            }
          ]
        },
        {
          "ref": "caocao",
          "children": [
            {
              "label": "B",
              "value": "b"
            },

            {
              "label": "C",
              "value": "c"
            }
          ]
        },
        {
          "ref": "zhongwuyan",
          "children": [
            {
              "label": "D",
              "value": "d"
            },

            {
              "label": "E",
              "value": "e"
            }
          ]
        },
        {
          "ref": "libai",
          "defer": true
        },
        {
          "ref": "hanxin",
          "defer": true
        },
        {
          "ref": "yunzhongjun",
          "defer": true
        }
      ]
    }
  ]
}
```

_多选_

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "label": "关联选择模式",
      "type": "select",
      "name": "b",
      "multiple": true,
      "sortable": true,
      "searchable": true,
      "deferApi": "/api/mock2/form/deferOptions?label=${label}",
      "selectMode": "associated",
      "leftMode": "tree",
      "leftOptions": [
        {
          "label": "法师",
          "children": [
            {
              "label": "诸葛亮",
              "value": "zhugeliang"
            }
          ]
        },
        {
          "label": "战士",
          "children": [
            {
              "label": "曹操",
              "value": "caocao"
            },
            {
              "label": "钟无艳",
              "value": "zhongwuyan"
            }
          ]
        },
        {
          "label": "打野",
          "children": [
            {
              "label": "李白",
              "value": "libai"
            },
            {
              "label": "韩信",
              "value": "hanxin"
            },
            {
              "label": "云中君",
              "value": "yunzhongjun"
            }
          ]
        }
      ],
      "options": [
        {
          "ref": "zhugeliang",
          "children": [
            {
              "label": "A",
              "value": "a"
            }
          ]
        },
        {
          "ref": "caocao",
          "children": [
            {
              "label": "B",
              "value": "b"
            },

            {
              "label": "C",
              "value": "c"
            }
          ]
        },
        {
          "ref": "zhongwuyan",
          "children": [
            {
              "label": "D",
              "value": "d"
            },

            {
              "label": "E",
              "value": "e"
            }
          ]
        },
        {
          "ref": "libai",
          "defer": true
        },
        {
          "ref": "hanxin",
          "defer": true
        },
        {
          "ref": "yunzhongjun",
          "defer": true
        }
      ]
    }
  ]
}
```

leftOptions 动态加载，默认 source 接口是返回 options 部分，而 leftOptions 是没有对应的接口可以动态返回了。为了方便，目前如果 source 接口返回的选中中，第一个 option 是以下这种格式则也会把 options[0].leftOptions 当成 leftOptions, options[0].children 当 options。同时 options[0].leftDefaultValue 可以用来配置左侧选项的默认值。

```
{
    status: 0,
    msg: '',
    data: {
        options: [
            {
                leftOptions: [],
                children: [],
                leftDefaultValue: ''
            }
        ]
    }
}
```

#### 人员点选

> 请通过网络面板查看接口请求返回。

实际上就是采用的「关联选择模式」的 select，注意要看那一部分文档，需要知道怎么动态加载 leftOptions。左侧部分和人员都是通过 source 接口返回。需要懒加载的项通过设置 `defer` 为 true 来标记。左右两部分都支持懒加载。
都是通过 deferApi 来实现，后端根据传过来的参数决定是懒加载树，还是栏加载人员。

- 有 dep 值则是懒加载部门树
- 有 ref 值则是懒加载人员

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "label": "人员选择",
      "type": "select",
      "name": "b",
      "multiple": true,
      "sortable": true,
      "searchable": true,
      "selectMode": "associated",
      "leftMode": "tree",
      "source": "/api/mock2/form/departUser",
      "searchApi": '/api/mock2/form/departUserSearch?term=${term}',
      "deferApi": "/api/mock2/form/departUser?ref=${ref}&dep=${value}"
    }
  ]
}
```

### 限制标签最大展示数量

`maxTagCount`可以限制标签的最大展示数量，超出数量的部分会收纳到 Popover 中，可以通过`overflowTagPopover`配置 Popover 相关的[属性](/dataseeddesigndocui/#/amis/zh-CN/components/tooltip#属性表)，注意该属性仅在多选模式开启后生效。

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "label": "水果",
      "type": "select",
      "name": "select",
      "multiple": true,
      "maxTagCount": 3,
      "checkAll": true,
      "defaultCheckAll": true,
      "overflowTagPopover": {
        "title": "水果"
      },
      "options": [
        {"label": "苹果", "value": "Apple"},
        {"label": "香蕉", "value": "Banana"},
        {"label": "黑莓", "value": "Blackberry"},
        {"label": "蓝莓", "value": "Blueberry"},
        {"label": "樱桃", "value": "Cherry"},
        {"label": "杨桃", "value": "Carambola"},
        {"label": "椰子", "value": "Coconut"},
        {"label": "猕猴桃", "value": "Kiwifruit"},
        {"label": "柠檬", "value": "Lemon"},
        {"label": "菠萝", "value": "Pineapple"}
      ]
    }
  ]
}
```

### 最大选中数量

`max`可以限制标签的最大选中数量。注意，该属性仅在多选模式开启后生效，暂时仅支持一般模式，不要和defaultCheckAll、checkAll、selectMode同时存在。

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "label": "水果",
      "type": "select",
      "name": "select",
      "multiple": true,
      "max": 3,
      "options": [
        {"label": "苹果", "value": "Apple"},
        {"label": "香蕉", "value": "Banana"},
        {"label": "黑莓", "value": "Blackberry"},
        {"label": "蓝莓", "value": "Blueberry"},
        {"label": "樱桃", "value": "Cherry"},
        {"label": "杨桃", "value": "Carambola"},
        {"label": "椰子", "value": "Coconut"},
        {"label": "猕猴桃", "value": "Kiwifruit"},
        {"label": "柠檬", "value": "Lemon"},
        {"label": "菠萝", "value": "Pineapple"}
      ]
    }
  ]
}
```

### 自定义下拉区域宽度与对齐方式

使用字符串或数字，使用数字时单位为`px`；支持单位: `%`、`px`、`rem`、`em`、`vw`。

```schema: scope="body"
{
  "type": "page",
  "body": {
    "type": "form",
    "body": [
      {
        "label": "80% 宽度靠右对齐",
        "type": "select",
        "name": "select",
        "menuTpl": "<div>${label} 值：${value}, 当前是否选中: ${checked}</div>",
        "overlay": {
          "width": "80%",
          "align": "right"
        },
        "options": [
          {
            "label": "A",
            "value": "a"
          },
          {
            "label": "B",
            "value": "b"
          },
          {
            "label": "C",
            "value": "c"
          }
        ]
      },
      {
        "label": "300px 宽度中间对齐",
        "type": "select",
        "name": "select",
        "menuTpl": "<div>${label} 值：${value}, 当前是否选中: ${checked}</div>",
        "overlay": {
          "width": 300,
          "align": "center"
        },
        "options": [
          {
            "label": "A",
            "value": "a"
          },
          {
            "label": "B",
            "value": "b"
          },
          {
            "label": "C",
            "value": "c"
          }
        ]
      }
    ]
  }
}
```

使用相对数值，如：`-20px` 相当于 `100% - 20px`；`+10vw` 相当于 `100% + 10vw`。支持如上相同单位。

```schema: scope="body"
{
  "type": "page",
  "body": {
    "type": "form",
    "body": [
      {
        "label": "相对窄 100px 向左对齐",
        "type": "select",
        "name": "select",
        "overlay": {
          "width": "-100px",
          "align": "left"
        },
        "popOverContainerSelector": "body",
        "options": [
          {
            "label": "A",
            "value": "a"
          },
          {
            "label": "B",
            "value": "b"
          },
          {
            "label": "C",
            "value": "c"
          }
        ]
      }
    ]
  }
}
```

### searchApi

**发送**

默认 GET，携带 term 变量，值为搜索框输入的文字，可从上下文中取数据设置进去。

**响应**

格式要求如下：

```json
{
  "status": 0,
  "msg": "",
  "data": {
    "options": [
      {
        "label": "描述",
        "value": "值" // ,
        // "children": [] // 可以嵌套
      },

      {
        "label": "描述2",
        "value": "值2"
      }
    ],

    "value": "值" // 默认值，可以获取列表的同时设置默认值。
  }
}
```

适用于需选择的数据/信息源较多时，用户可直观的知道自己所选择的数据/信息的场景，一般左侧框为数据/信息源，右侧为已选数据/信息，被选中信息同时存在于 2 个框内。

### 多选全选

开启全选后，默认开启`"checkAllBySearch": true`，检索状态下全选内容为当前过滤项。如果设置了`"checkAllBySearch": false`，则无论是否在检索状态下，全选都会选择全部数据源。

> `checkAllBySearch`默认开启

```schema: scope="body"
{
    "type": "form",
    "body": [
        {
            "label": "多选",
            "type": "select",
            "name": "select2",
            "searchable": true,
            "checkAll": true,
            "multiple": true,
            "clearable": true,
            "source": "/api/mock2/form/getOptions"
        }
    ]
}
```

### 列表项远程过滤搜索

本地过滤搜索 仅配置"searchable": true 即可
远程搜索 需要配置 "autoComplete": [SchemaApi] SchemaApi是个类型对象，详情参考 [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api) 模块

> 若需要映射api数据结构, 需要配置responseData [配置返回数据](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api#配置返回数据) 或者编写adaptor（js代码）[配置接收适配器](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api#配置接收适配器)

```schema: scope="body"
{
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "body": [
        {
          "label":"选项",
          "type":"select",
          "name":"select",
          "multiple":true,
          "loading":true,
          "autoComplete":{
              "url":"/api/mock2/options/autoComplete3?term=$term",
              "responseData":{
                  "options":"${items|pick:label~lab,value~val}"
              }
          }
      }
    ]
}
```

### 配置虚拟滚动以及选项高度

> 假设virtualThreshold 设置为20 当options总数大于20时才会开启虚拟滚动。 itemHeight 设置的值 只会在虚拟滚动开启时才会生效

```schema: scope="body"
{
    "type": "form",
    debug: true,
    "body": [
        {
          "label":"选项",
          "type":"select",
          "name":"select",
          "itemHeight":64,
          "virtualThreshold":20,
          "multiple":false,
          "options":[
              {
                  "label":"label0",
                  "value":0,
                  "id":"id0"
              },
              {
                  "label":"label1",
                  "value":1,
                  "id":"id1"
              },
              {
                  "label":"label2",
                  "value":2,
                  "id":"id2"
              },
              {
                  "label":"label3",
                  "value":3,
                  "id":"id3"
              },
              {
                  "label":"label4",
                  "value":4,
                  "id":"id4"
              },
              {
                  "label":"label5",
                  "value":5,
                  "id":"id5"
              },
              {
                  "label":"label6",
                  "value":6,
                  "id":"id6"
              },
              {
                  "label":"label7",
                  "value":7,
                  "id":"id7"
              },
              {
                  "label":"label8",
                  "value":8,
                  "id":"id8"
              },
              {
                  "label":"label9",
                  "value":9,
                  "id":"id9"
              },
              {
                  "label":"label10",
                  "value":10,
                  "id":"id10"
              },
              {
                  "label":"label11",
                  "value":11,
                  "id":"id11"
              },
              {
                  "label":"label12",
                  "value":12,
                  "id":"id12"
              },
              {
                  "label":"label13",
                  "value":13,
                  "id":"id13"
              },
              {
                  "label":"label14",
                  "value":14,
                  "id":"id14"
              },
              {
                  "label":"label15",
                  "value":15,
                  "id":"id15"
              },
              {
                  "label":"label16",
                  "value":16,
                  "id":"id16"
              },
              {
                  "label":"label17",
                  "value":17,
                  "id":"id17"
              },
              {
                  "label":"label18",
                  "value":18,
                  "id":"id18"
              },
              {
                  "label":"label19",
                  "value":19,
                  "id":"id19"
              },
              {
                  "label":"label20",
                  "value":20,
                  "id":"id20"
              }
          ],
      }
    ]
}
```

### 配置鼠标移入时选项显示tooltip

menuTpl 可获取的数据域来源有三块，模版变量解析取值优先级：option > state > data(包含父级数据域)

```json
{ // select组件的 state
  "multiple": false,
  "checkAll": false,
  "checked": false,
  "inputValue": "",
  "searchable": false,
  "index": 49
}
```

```json
{ // 配置的单个option，与下方配置的schemaNode一致
  "label": "label49",
  "value": 49,
  "id": "id49"
}
```

> menuTpl 不仅可以配置 tpl string，还可以配置 shcemaNode。 注意只在 不设置selectMode时才会生效。

```schema: scope="body"
{
    "type": "form",
    debug: true,
    "body": [
        {
          "label":"选项",
          "type":"select",
          "name":"select",
          "selectedTooltipMode": "always",
          "menuTpl":{
              "type":"tooltip-wrapper",
              "content":"${label} - ${value}",
              "body":"<div>${label} 值：${value}, 当前是否选中: ${checked}<div>第二行这里有段描述</div></div>"
          },
          "maxTagCount":5,
          "multiple":false,
          "options":[
              {
                  "label":"label0",
                  "value":0,
                  "id":"id0"
              },
              {
                  "label":"label1",
                  "value":1,
                  "id":"id1"
              },
              {
                  "label":"label2",
                  "value":2,
                  "id":"id2"
              },
              {
                  "label":"label3",
                  "value":3,
                  "id":"id3"
              },
              {
                  "label":"label4",
                  "value":4,
                  "id":"id4"
              },
              {
                  "label":"label5",
                  "value":5,
                  "id":"id5"
              },
              {
                  "label":"label6",
                  "value":6,
                  "id":"id6"
              },
              {
                  "label":"label7",
                  "value":7,
                  "id":"id7"
              },
              {
                  "label":"label8",
                  "value":8,
                  "id":"id8"
              },
              {
                  "label":"label9",
                  "value":9,
                  "id":"id9"
              },
              {
                  "label":"label10",
                  "value":10,
                  "id":"id10"
              },
              {
                  "label":"label11",
                  "value":11,
                  "id":"id11"
              },
              {
                  "label":"label12",
                  "value":12,
                  "id":"id12"
              },
              {
                  "label":"label13",
                  "value":13,
                  "id":"id13"
              },
              {
                  "label":"label14",
                  "value":14,
                  "id":"id14"
              },
              {
                  "label":"label15",
                  "value":15,
                  "id":"id15"
              },
              {
                  "label":"label16",
                  "value":16,
                  "id":"id16"
              },
              {
                  "label":"label17",
                  "value":17,
                  "id":"id17"
              },
              {
                  "label":"label18",
                  "value":18,
                  "id":"id18"
              },
              {
                  "label":"label19",
                  "value":19,
                  "id":"id19"
              },
              {
                  "label":"label20",
                  "value":20,
                  "id":"id20"
              }
          ],
      }
    ]
}
```

### 搜索值自动清空

当 `"autoClearSearchValue": true`时，下拉区域隐藏清空搜索值。

```schema: scope="body"
{
    "type": "form",
    "body": [
        {
            "label": "选项",
            "type": "select",
            "name": "select",
            "searchable": true,
            "autoClearSearchValue": true,
            "options": [
                {
                    "label":"A",
                    "value":"a"
                },
                {
                    "label":"B",
                    "value":"b"
                },
                {
                    "label":"C",
                    "value":"c"
                }
            ]
        }
    ]
}
```

### 自定义tooltip

通过 `selectedTooltip.content` 来实现自定义 `tooltip`，这个功能在 1.83.0 支持

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "actions": [],
    "body": [
      {
        "label": "选项",
        "type": "select",
        "itemHeight": 60,
        "labelField": "userName",
        "valueField": "userId",
        "name": "select",
        "multiple": true,
        "selectedTooltipMode": "always",
        "selectedTooltip": {
          "content": {
            "type": "flex",
            "direction": "column",
            "alignItems": "flex-start",
            "items": [
              {
                "type": "tpl",
                "tpl": "${userName}"
              },
              {
                "type": "tpl",
                "tpl": "${org}"
              },
              {
                "type": "tpl",
                "tpl": "${date}"
              }
            ]
          }
        },
        "menuTpl": {
          "type": "tooltip-wrapper",
          "inline": true,
          "content": {
            "type": "flex",
            "direction": "column",
            "alignItems": "flex-start",
            "items": [
              {
                "type": "tpl",
                "tpl": "${userName}"
              },
              {
                "type": "tpl",
                "tpl": "${org}"
              },
              {
                "type": "tpl",
                "tpl": "${date}"
              }
            ]
          },
          "body": {
            "type": "flex",
            "direction": "column",
            "alignItems": "flex-start",
            "items": [
              {
                "type": "tpl",
                "tpl": "${userName}"
              },
              {
                "type": "tpl",
                "tpl": "${org}"
              }
            ]
          }
        },
        "options": [
          {
            "userName": "张三",
            "userId": "a",
            "org": "数禾/技术中心/信贷中台/信贷前置组/研发部",
            "date": "入职日期: 2022年9月1日"
          },
          {
            "userName": "李四",
            "userId": "b",
            "org": "数禾/运维部",
            "date": "入职日期: 2022年9月2日"
          },
          {
            "userName": "王五",
            "userId": "c",
            "org": "数禾/财务部",
            "date": "入职日期: 2022年9月3日"
          },
          {
            "userName": "张三",
            "userId": "a1",
            "org": "数禾/技术中心/信贷中台/信贷前置组/研发部",
            "date": "入职日期: 2022年9月1日"
          },
          {
            "userName": "李四",
            "userId": "b1",
            "org": "数禾/运维部",
            "date": "入职日期: 2022年9月2日"
          },
          {
            "userName": "王五",
            "userId": "c1",
            "org": "数禾/财务部",
            "date": "入职日期: 2022年9月3日"
          },
          {
            "userName": "张三",
            "userId": "a2",
            "org": "数禾/技术中心/信贷中台/信贷前置组/研发部",
            "date": "入职日期: 2022年9月1日"
          },
          {
            "userName": "李四",
            "userId": "b2",
            "org": "数禾/运维部",
            "date": "入职日期: 2022年9月2日"
          },
          {
            "userName": "王五",
            "userId": "c2",
            "org": "数禾/财务部",
            "date": "入职日期: 2022年9月3日"
          },
          {
            "userName": "张三",
            "userId": "a3",
            "org": "数禾/技术中心/信贷中台/信贷前置组/研发部",
            "date": "入职日期: 2022年9月1日"
          },
          {
            "userName": "李四",
            "userId": "b3",
            "org": "数禾/运维部",
            "date": "入职日期: 2022年9月2日"
          },
          {
            "userName": "王五",
            "userId": "c3",
            "org": "数禾/财务部",
            "date": "入职日期: 2022年9月3日"
          },
          {
            "userName": "张三",
            "userId": "a4",
            "org": "数禾/技术中心/信贷中台/信贷前置组/研发部",
            "date": "入职日期: 2022年9月1日"
          },
          {
            "userName": "李四",
            "userId": "b4",
            "org": "数禾/运维部",
            "date": "入职日期: 2022年9月2日"
          },
          {
            "userName": "王五",
            "userId": "c4",
            "org": "数禾/财务部",
            "date": "入职日期: 2022年9月3日"
          },
          {
            "userName": "张三",
            "userId": "a5",
            "org": "数禾/技术中心/信贷中台/信贷前置组/研发部",
            "date": "入职日期: 2022年9月1日"
          },
          {
            "userName": "李四",
            "userId": "b5",
            "org": "数禾/运维部",
            "date": "入职日期: 2022年9月2日"
          },
          {
            "userName": "王五",
            "userId": "c5",
            "org": "数禾/财务部",
            "date": "入职日期: 2022年9月3日"
          },
          {
            "userName": "张三",
            "userId": "a6",
            "org": "数禾/技术中心/信贷中台/信贷前置组/研发部",
            "date": "入职日期: 2022年9月1日"
          },
          {
            "userName": "李四",
            "userId": "b6",
            "org": "数禾/运维部",
            "date": "入职日期: 2022年9月2日"
          },
          {
            "userName": "王五",
            "userId": "c6",
            "org": "数禾/财务部",
            "date": "入职日期: 2022年9月3日"
          }
        ]
      }
    ]
  }
}
```

### 属性表

除了支持 [普通表单项属性表](/dataseeddesigndocui/#/amis/zh-CN/components/form/formitem#%E5%B1%9E%E6%80%A7%E8%A1%A8) 中的配置以外，还支持下面一些配置

| 属性名                   | 类型                                                                              | 默认值                                                                             | 说明                                                                                                                                                                                                         |
| ------------------------ | --------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| options                  | `Array<object>`或`Array<string>`                                                  |                                                                                    | [选项组](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E9%9D%99%E6%80%81%E9%80%89%E9%A1%B9%E7%BB%84-options)                                                                                                                                    |
| source                   | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api) 或 [数据映射](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/data-mapping) |                                                                                    | [动态选项组](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E5%8A%A8%E6%80%81%E9%80%89%E9%A1%B9%E7%BB%84-source)                                                                                                                                 |
| autoComplete             | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api)                                                    |                                                                                    | [自动提示补全](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E8%87%AA%E5%8A%A8%E8%A1%A5%E5%85%A8-autocomplete)                                                                                                                                  |
| delimiter                | `string`                                                                          | `false`                                                                            | [拼接符](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E6%8B%BC%E6%8E%A5%E7%AC%A6-delimiter)                                                                                                                                                    |
| labelField               | `string`                                                                          | `"label"`                                                                          | [选项标签字段](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E9%80%89%E9%A1%B9%E6%A0%87%E7%AD%BE%E5%AD%97%E6%AE%B5-labelfield)                                                                                                                  |
| valueField               | `string`                                                                          | `"value"`                                                                          | [选项值字段](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E9%80%89%E9%A1%B9%E5%80%BC%E5%AD%97%E6%AE%B5-valuefield)                                                                                                                             |
| joinValues               | `boolean`                                                                         | `true`                                                                             | [拼接值](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E6%8B%BC%E6%8E%A5%E5%80%BC-joinvalues)                                                                                                                                                   |
| extractValue             | `boolean`                                                                         | `false`                                                                            | [提取值](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E6%8F%90%E5%8F%96%E5%A4%9A%E9%80%89%E5%80%BC-extractvalue)                                                                                                                               |
| checkAll                 | `boolean`                                                                         | `false`                                                                            | 是否支持全选                                                                                                                                                                                                 |
| checkAllLabel            | `string`                                                                          | `全选`                                                                             | 全选的文字                                                                                                                                                                                                   |
| checkAllBySearch         | `boolean`                                                                         | `true`                                                                             | 有检索时只全选检索命中的项                                                                                                                                                                                   |
| defaultCheckAll          | `boolean`                                                                         | `false`                                                                            | 默认是否全选                                                                                                                                                                                                 |
| creatable                | `boolean`                                                                         | `false`                                                                            | [新增选项](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E5%89%8D%E7%AB%AF%E6%96%B0%E5%A2%9E-creatable)                                                                                                                                         |
| multiple                 | `boolean`                                                                         | `false`                                                                            | [多选](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#多选-multiple)                                                                                                                                                                              |
| searchable               | `boolean`                                                                         | `false`                                                                            | [检索](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#检索-searchable)                                                                                                                                                                            |
| createBtnLabel           | `string`                                                                          | `"新增选项"`                                                                       | [新增选项](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E6%96%B0%E5%A2%9E%E9%80%89%E9%A1%B9)                                                                                                                                                   |
| addControls              | Array<[表单项](/dataseeddesigndocui/#/amis/zh-CN/components/form/formitem)>                                                       |                                                                                    | [自定义新增表单项](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E8%87%AA%E5%AE%9A%E4%B9%89%E6%96%B0%E5%A2%9E%E8%A1%A8%E5%8D%95%E9%A1%B9-addcontrols)                                                                                           |
| addDialog     | `Schema`               |              | [配置新增弹框其它属性](/dataseeddesigndocui/#/amis/zh-CN/components/form/options?anchor=%E9%85%8D%E7%BD%AE%E6%96%B0%E5%A2%9E%E5%BC%B9%E6%A1%86%E5%85%B6%E5%AE%83%E5%B1%9E%E6%80%A7)  |
| addApi                   | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api)                                                       |                                                                                    | [配置新增选项接口](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E9%85%8D%E7%BD%AE%E6%96%B0%E5%A2%9E%E6%8E%A5%E5%8F%A3-addapi)                                                                                                                  |
| editable                 | `boolean`                                                                         | `false`                                                                            | [编辑选项](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E5%89%8D%E7%AB%AF%E7%BC%96%E8%BE%91-editable)                                                                                                                                          |
| editControls             | Array<[表单项](/dataseeddesigndocui/#/amis/zh-CN/components/form/formitem)>                                                       |                                                                                    | [自定义编辑表单项](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E8%87%AA%E5%AE%9A%E4%B9%89%E7%BC%96%E8%BE%91%E8%A1%A8%E5%8D%95%E9%A1%B9-editcontrols)                                                                                          |
| editDialog     | `Schema`               |              | [配置编辑弹框其它属性](/dataseeddesigndocui/#/amis/zh-CN/components/form/options?anchor=%E9%85%8D%E7%BD%AE%E7%BC%96%E8%BE%91%E5%BC%B9%E6%A1%86%E5%85%B6%E5%AE%83%E5%B1%9E%E6%80%A7)  |
| editApi                  | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api)                                                       |                                                                                    | [配置编辑选项接口](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E9%85%8D%E7%BD%AE%E7%BC%96%E8%BE%91%E6%8E%A5%E5%8F%A3-editapi)                                                                                                                 |
| removable                | `boolean`                                                                         | `false`                                                                            | [删除选项](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E5%88%A0%E9%99%A4%E9%80%89%E9%A1%B9)                                                                                                                                                   |
| deleteApi                | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api)                                                       |                                                                                    | [配置删除选项接口](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E9%85%8D%E7%BD%AE%E5%88%A0%E9%99%A4%E6%8E%A5%E5%8F%A3-deleteapi)                                                                                                               |
| autoFill                 | `object`                                                                          |                                                                                    | [自动填充](/dataseeddesigndocui/#/amis/zh-CN/components/form/options#%E8%87%AA%E5%8A%A8%E5%A1%AB%E5%85%85-autofill)                                                                                                                                          |
| menuTpl                  | `string \| schemaNode`                                                                          |                                                                                    | 支持配置自定义菜单                                                                                                                                                                                           |
| clearable                | `boolean`                                                                         |                                                                                    | 单选模式下是否支持清空                                                                                                                                                                                       |
| hideSelected             | `boolean`                                                                         | `false`                                                                            | 隐藏已选选项                                                                                                                                                                                                 |
|autoClearSearchValue             | `boolean`                                                                         | `false`                                                                            | 下拉区域隐藏时是否清空搜索值，不设置`selectMode`时生效                                                                                                                                                                                                 |
| mobileClassName          | `string`                                                                          |                                                                                    | 移动端浮层类名                                                                                                                                                                                               |
| selectMode               | `string`                                                                          | ``                                                                                 | 可选：`group`、`table`、`tree`、`chained`、`associated`。分别为：列表形式、表格形式、树形选择形式、级联选择形式，关联选择形式（与级联选择的区别在于，级联是无限极，而关联只有一级，关联左边可以是个 tree）。 |
| searchResultMode         | `string`                                                                          |                                                                                    | 如果不设置将采用 `selectMode` 的值，可以单独配置，参考 `selectMode`，决定搜索结果的展示形式。注意：如果SelectMode的值不是associated，searchResultMode的值不能设置为associated。                                                                                                                |
| columns                  | `Array<Object>`                                                                   |                                                                                    | 当展示形式为 `table` 可以用来配置展示哪些列，跟 table 中的 columns 配置相似，只是只有展示功能。                                                                                                              |
| leftOptions              | `Array<Object>`                                                                   |                                                                                    | 当展示形式为 `associated` 时用来配置左边的选项集。                                                                                                                                                           |
| leftMode                 | `string`                                                                          |                                                                                    | 当展示形式为 `associated` 时用来配置左边的选择形式，支持 `list` 或者 `tree`。默认为 `list`。                                                                                                                 |
| rightMode                | `string`                                                                          |                                                                                    | 当展示形式为 `associated` 时用来配置右边的选择形式，可选：`list`、`table`、`tree`、`chained`。                                                                                                               |
| max              | `number`                  |                                                                                    | 标签的最大选中数量，不设置`selectMode`，同时多选模式开启后生效。版本：`1.59.0`   |
| maxTagCount              | `number`                                                                          |                                                                                    | 标签的最大展示数量，超出数量后以收纳浮层的方式展示，仅在多选模式开启后生效                                                                                                                                   |
| overflowTagPopover       | `TooltipObject`                                                                   | `{"placement": "top", "trigger": "hover", "showArrow": false, "offset": [0, -10]}` | 收纳浮层的配置属性，详细配置参考[Tooltip](/dataseeddesigndocui/#/amis/zh-CN/components/tooltip#属性表)                                                                                                                                                 |
| selectedTooltipMode       | `none` \| `always` \| `overflow`                                                 | `none` | 已选值tooltip展示模式，不设置`selectMode`，单选或多选设置`valuesNoWrap=true`时生效。版本：1.46.0 |
| selectedTooltipPlacement       | `top` \| `bottom` \| `left`  \| `right`                                              | `top` | 已选值tooltip展示位置，不设置`selectMode`，单选或多选设置`valuesNoWrap=true`时生效。版本：1.46.0 |
| selectedTooltip       | `TooltipObject`     | `{"placement": "top", "trigger": "hover"}` | 已选值Tooltip的配置属性，表达式可通过`__selectedOption`获取已选对象，仅针对单选情况。详细配置参考[Tooltip](/dataseeddesigndocui/#/amis/zh-CN/components/tooltip#属性表)。版本：1.74.0 , 1.83.0 版本之后支持配置content自定义tooltip的内容                      |
| optionClassName          | `string`                                                                          |                                                                                    | 选项 CSS 类名. 这个类只能控制选项的文本内容，不包括其他元素。比如 多选时的checkbox不受控制。不设置selectMode时生效                                                                                                                                                                                                |
| popOverContainerSelector | `string`                                                                          |                                                                                    | 弹层挂载位置选择器，会通过`querySelector`获取                                                                                                                                                                |
| clearable                | `boolean`                                                                         |                                                                                    | 是否展示清空图标                                                                                                                                                                                             |
| overlay                  | `{ width: string \| number, align: "left" \| "center" \| "right" }`               |                                                                                    | 弹层宽度与对齐方式                                                                                                                                                                          |
| className                  |               |                                                                                    | select组件顶层类，可以控制子类，进行定制化。比如：Select-menu、Select-option、Select-option-content                                                                                                                                                                          |
| virtualThreshold                  | `number`               | `100`                                                                                    | 不设置时 官方默认超过100才开启虚拟滚动功能                                                                                                                                                                          |
| itemHeight                  | `number`             | `32`                                                                                   | 默认是32 只有当虚拟滚动功能开启时 才会生效                                                                                                                                                                          |
| sameOptionChange  | `boolean`    | `true` | 选择相同的选项是否执行change事件，默认执行 |

### 事件表

当前组件会对外派发以下事件，可以通过`onEvent`来监听这些事件，并通过`actions`来配置执行的动作，在`actions`中可以通过`${事件参数名}`来获取事件产生的数据，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action)。

> `[name]`表示当前组件绑定的名称，即`name`属性，如果没有配置`name`属性，则通过`value`取值。

| 事件名称 | 事件参数                                                                                                                                    | 说明                 |
| -------- | ------------------------------------------------------------------------------------------------------------------------------------------- | -------------------- |
| change   | `[name]: string` 组件的值<br/>`selectedItems: Option \| Option[]` 选中的项<br/>`items: Option[]` 选项集合 | 选中值变化时触发     |
| blur     | `[name]: string` 组件的值<br/>`items: Option[]` 选项集合                                                  | 输入框失去焦点时触发 |
| focus    | `[name]: string` 组件的值<br/>`items: Option[]` 选项集合                                                  | 输入框获取焦点时触发 |
| add      | `[name]: Option` 新增的选项<br/>`items: Option[]` 选项集合                                                | 新增选项提交时触发   |
| edit     | `[name]: Option` 编辑的选项<br/>`items: Option[]` 选项集合                                                | 编辑选项提交时触发   |
| delete   | `[name]: Option` 删除的选项<br/>`items: Option[]` 选项集合                                                | 删除选项提交时触发   |

### 动作表

当前组件对外暴露以下特性动作，其他组件可以通过指定`actionType: 动作名称`、`componentId: 该组件id`来触发这些动作，动作配置可以通过`args: {动作配置项名称: xxx}`来配置具体的参数，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action#触发其他组件的动作)。

| 动作名称 | 动作配置                               | 说明                                                                                    |   版本   |
| -------- | -------------------------------------- | --------------------------------------------------------------------------------------- | --------------- |
| clear    | -                                      | 清空                                                                                    |  |
| clearSearchValue    | -                             | 清空过滤搜索的输入值，其效果等同于手动点击清空按钮（即“x”号）   |1.44.0 |
| reset    | -                                      | 将值重置为`resetValue`，若没有配置`resetValue`，则清空                                  |  |
| reload   | -                                      | 重新加载，调用 `source`，刷新数据域数据刷新（重新加载）                                 |  |
| setValue | `value: string` \| `string[]` 更新的值 | 更新数据，开启`multiple`支持设置多项，开启`joinValues`时，多值用`,`分隔，否则多值用数组 |  |
