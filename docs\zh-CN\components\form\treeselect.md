---
title: TreeSelect 树形选择器
description:
type: 0
group: null
menuName: TreeSelect 树形选择器
icon:
order: 60
---

## 基本使用

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "tree-select",
      "name": "tree",
      "label": "Tree",
      "searchable": true,
      "options": [
        {
          "label": "Folder A",
          "value": 1,
          "children": [
            {
              "label": "file A",
              "value": 2
            },
            {
              "label": "file B",
              "value": 3
            }
          ]
        },
        {
          "label": "file C",
          "value": 4
        },
        {
          "label": "file D",
          "value": 5
        },
        {
          "label": "Folder E",
          "value": 6,
          "children": [
            {
              "label": "Folder G",
              "value": 7,
              "children": [
                {
                  "label": "file H",
                  "value": 8
                },
                {
                  "label": "file I",
                  "value": 9
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

## 仅展示选中节点文本信息

设置`hideNodePathLabel: true`，可以隐藏选择框中已选择节点的祖先节点（ancestor）的`labelField`字段值，仅展示当前选中节点的`labelField`字段值。

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "tree-select",
      "name": "tree1",
      "label": "展示已选择节点的祖先节点的文本信息",
      "value": "1,6,7",
      "multiple": true,
      "options": [
        {
          "label": "Folder A",
          "value": 1,
          "children": [
            {
              "label": "file A",
              "value": 2
            },
            {
              "label": "file B",
              "value": 3
            }
          ]
        },
        {
          "label": "file C",
          "value": 4
        },
        {
          "label": "file D",
          "value": 5
        },
        {
          "label": "Folder E",
          "value": 6,
          "children": [
            {
              "label": "Folder G",
              "value": 7,
              "children": [
                {
                  "label": "file H",
                  "value": 8
                },
                {
                  "label": "file I",
                  "value": 9
                }
              ]
            }
          ]
        }
      ]
    },
    {
      "type": "divider"
    },
    {
      "type": "tree-select",
      "name": "tree2",
      "label": "仅展示已选择节点的文本信息",
      "value": "1,6,7",
      "multiple": true,
      "hideNodePathLabel": true,
      "options": [
        {
          "label": "Folder A",
          "value": 1,
          "children": [
            {
              "label": "file A",
              "value": 2
            },
            {
              "label": "file B",
              "value": 3
            }
          ]
        },
        {
          "label": "file C",
          "value": 4
        },
        {
          "label": "file D",
          "value": 5
        },
        {
          "label": "Folder E",
          "value": 6,
          "children": [
            {
              "label": "Folder G",
              "value": 7,
              "children": [
                {
                  "label": "file H",
                  "value": 8
                },
                {
                  "label": "file I",
                  "value": 9
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

## 只允许选择叶子节点

在单选时，可通过 `onlyLeaf` 可以配置只允许选择叶子节点

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "tree-select",
      "name": "tree",
      "label": "Tree",
      "onlyLeaf": true,
      "searchable": true,
      "options": [
        {
          "label": "Folder A",
          "value": 1,
          "children": [
            {
              "label": "file A",
              "value": 2
            },
            {
              "label": "file B",
              "value": 3
            }
          ]
        },
        {
          "label": "file C",
          "value": 4
        },
        {
          "label": "file D",
          "value": 5
        },
        {
          "label": "Folder E",
          "value": 6,
          "children": [
            {
              "label": "Folder G",
              "value": 7,
              "children": [
                {
                  "label": "file H",
                  "value": 8
                },
                {
                  "label": "file I",
                  "value": 9
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

## 如何让某些节点无法点？

只需要对应的节点没有 value 就行，比如下面例子的目录节点都无法点，只能点文件节点

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "tree-select",
      "name": "tree",
      "label": "Tree",
      "searchable": true,
      "options": [
        {
          "label": "Folder A",
          "value": 1,
          "children": [
            {
              "label": "file A",
              "value": 2
            },
            {
              "label": "file B",
              "value": 3
            }
          ]
        },
        {
          "label": "file C",
          "value": 4
        },
        {
          "label": "file D",
          "value": 5
        },
        {
          "label": "Folder E",
          "value": 6,
          "children": [
            {
              "label": "Folder G",
              "value": 7,
              "children": [
                {
                  "label": "file H",
                  "value": 8
                },
                {
                  "label": "file I",
                  "value": 9
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

## 搜索选项

配置`autoComplete`接口可以实现从远程数据搜索目标结果，搜索的关键字段为`term`，注意搜索的逻辑需要在服务端实现。

```schema: scope="body"
{
    "type":"form",
    "api":"/api/mock2/form/saveForm",
    "body":[
        {
            "type":"tree-select",
            "name":"tree",
            "label":"Tree",
            "autoComplete":"/api/mock2/tree/search?term=$term",
            "source":"/api/mock2/tree/search"
        }
    ]
}
```

## 自定义选项渲染

使用`menuTpl`属性，自定义下拉选项的渲染内容。

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "tree-select",
      "name": "tree",
      "standardMode": true,
      "label": "Tree",
      "menuTpl": [
        {
          "type":"flex",
          "justify":"space-between",
          "items":[
            {
              "type":"typography",
              "className":"w-4/5",
              "text":"${label}"
            },
            {
              "type":"tag",
              "label":"${tag}"
            }
          ]
        }
      ],
      "iconField": "icon",
      "searchable": true,
      "options": [
        {
          "label": "采购单",
          "value": "order",
          "tag": "数据模型",
          "icon": "fa fa-database",
          "children": [
            {
              "label": "ID",
              "value": "id",
              "tag": "数字",
              "icon": "fa fa-check",
            },
            {
              "label": "采购人",
              "value": "name",
              "tag": "字符串",
              "icon": "fa fa-check",
            },
            {
              "label": "采购时间",
              "value": "time",
              "tag": "日期时间",
              "icon": "fa fa-check",
            },
            {
              "label": "供应商",
              "value": "vendor",
              "tag": "数据模型(N:1)",
              "icon": "fa fa-database",
              "children": [
                {
                  "label": "供应商ID",
                  "value": "vendor_id",
                  "tag": "数字",
                  "icon": "fa fa-check",
                },
                {
                  "label": "超长的供应商名称供应商名称供应商名称供应商名称供应商超长的供应商名称供应商名称供应商名称供应商名称供应商超长的供应商名称供应商名称供应商名称供应商名称供应商超长的供应商名称供应商名称供应商名称供应商名称供应商超长的供应商名称供应商名称供应商名称供应商名称供应商",
                  "value": "vendor_name",
                  "tag": "字符串",
                  "icon": "fa fa-check",
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

## 限制标签最大展示数量

`maxTagCount`可以限制标签的最大展示数量，超出数量的部分会收纳到 Popover 中，可以通过`overflowTagPopover`配置 Popover 相关的[属性](/dataseeddesigndocui/#/amis/zh-CN/components/tooltip#属性表)，注意该属性仅在多选模式开启后生效。

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "tree-select",
      "name": "tree",
      "label": "Tree",
      "searchable": true,
      "multiple": true,
      "maxTagCount": "2",
      "overflowTagPopover": {
        "title": "其他"
      },
      "options": [
        {
          "label": "Folder A",
          "value": 1,
          "children": [
            {
              "label": "file A",
              "value": 2
            },
            {
              "label": "file B",
              "value": 3
            }
          ]
        },
        {
          "label": "file C",
          "value": 4
        },
        {
          "label": "file D",
          "value": 5
        },
        {
          "label": "Folder E",
          "value": 6,
          "children": [
            {
              "label": "Folder G",
              "value": 7,
              "children": [
                {
                  "label": "file H",
                  "value": 8
                },
                {
                  "label": "file I",
                  "value": 9
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

## 标签文本最大显示长度

通过`maxTagTextLength`控制标签文本最大显示长度。

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "tree-select",
      "name": "tree",
      "label": "Tree",
      "searchable": true,
      "multiple": true,
      "maxTagTextLength": 10,
      "options": [
        {
          "label": "Folder A",
          "value": 1,
          "children": [
            {
              "label": "file A",
              "value": 2
            },
            {
              "label": "file B",
              "value": 3
            }
          ]
        },
        {
          "label": "file C",
          "value": 4
        },
        {
          "label": "file D",
          "value": 5
        },
        {
          "label": "Folder E",
          "value": 6,
          "children": [
            {
              "label": "Folder G",
              "value": 7,
              "children": [
                {
                  "label": "file H",
                  "value": 8
                },
                {
                  "label": "file I",
                  "value": 9
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

## 选项为空时的提示文本

通过`optionsPlaceholder`控制选项为空时的提示文本。

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "body": [
    {
      "type": "tree-select",
      "name": "tree",
      "label": "Tree",
      "optionsPlaceholder": "空",
      "options": []
    }
  ]
}
```

## 定义选中项回填的方式

通过`showCheckedStrategy`定义选中项回填的方式，仅在 `multiple` 为 `true` 时有效。

```schema: scope="body"
{
  "type": "form",
  "api": "/api/mock2/form/saveForm",
  "debug": true,
  "body": [
    {
      "type": "tree-select",
      "name": "tree",
      "label": "Tree",
      "searchable": true,
      "multiple": true,
      "showCheckedStrategy": "all",
      "options": [
        {
          "label": "Folder A",
          "value": 1,
          "children": [
            {
              "label": "file A",
              "value": 2
            },
            {
              "label": "file B",
              "value": 3
            }
          ]
        },
        {
          "label": "file C",
          "value": 4
        },
        {
          "label": "file D",
          "value": 5
        },
        {
          "label": "Folder E",
          "value": 6,
          "children": [
            {
              "label": "Folder G",
              "value": 7,
              "children": [
                {
                  "label": "file H",
                  "value": 8
                },
                {
                  "label": "file I",
                  "value": 9
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

## 属性表

下列属性为`tree-select`独占属性, 更多属性用法，参考[InputTree 树形选择框](/dataseeddesigndocui/#/amis/zh-CN/components/form/input-tree)

| 属性名              | 类型            | 默认值                                                                             | 说明                                                                                                                                                                                                     | 版本  |
| ------------------- | --------------- | ---------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----- |
| hideNodePathLabel   | `boolean`       | `false`                                                                            | 是否隐藏选择框中已选择节点的路径 label 信息                                                                                                                                                              |       |
| onlyLeaf            | `boolean`       | `false`                                                                            | 只允许选择叶子节点                                                                                                                                                                                       |       |
| searchable          | `boolean`       | `false`                                                                            | 是否可检索，仅在 type 为 `tree-select` 的时候生效                                                                                                                                                        |       |
| maxTagCount         | `number`        |                                                                                    | 标签的最大展示数量，超出数量后以收纳浮层的方式展示，仅在多选模式开启后生效                                                                                                                               |
| overflowTagPopover  | `TooltipObject` | `{"placement": "top", "trigger": "hover", "showArrow": false, "offset": [0, -10]}` | 收纳浮层的配置属性，详细配置参考[Tooltip](/dataseeddesigndocui/#/amis/zh-CN/components/tooltip#属性表)                                                                                                                                             |
| maxTagTextLength    | `number`        |                                                                                    | 标签文本最大显示长度                                                                                                                                                                                     | 1.2.0 |
| optionsPlaceholder  | `string`        | `"暂无数据"`                                                                       | 选项为空时的提示文本                                                                                                                                                                                     | 1.2.0 |
| showCheckedStrategy | `string`        | `"parent"`                                                                         | 当 multiple 为 true 时，定义选中项回填的方式，有以下 3 个可选值：<br/>`"child"`：只显示子节点。<br/>`"parent"`：只显示父节点(当父节点下所有子节点都选中时)。<br/>`"all"`：显示所有选中节点(包括父节点)。 | 1.2.0 |
| popOverContainerSelector | `string`   |  | 弹层挂载位置选择器，会通过`querySelector`获取 | 1.59.0 |
| checkAll                 | `boolean`     | `false`    | 是否支持全选     | `1.78.0`     |
| checkAllLabel            | `string`      | `全选`      | 全选的文字    | `1.78.0`     |

## 事件表

当前组件会对外派发以下事件，可以通过`onEvent`来监听这些事件，并通过`actions`来配置执行的动作，在`actions`中可以通过`${事件参数名}`来获取事件产生的数据，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action)。

> `[name]`表示当前组件绑定的名称，即`name`属性，如果没有配置`name`属性，则通过`value`取值。

| 事件名称     | 事件参数                                                      | 说明                         |
| ------------ | ------------------------------------------------------------- | ---------------------------- |
| change       | `[name]: string` 组件的值                                     | 选中值变化时触发             |
| blur         | `[name]: string` 组件的值                                     | 输入框失去焦点时触发         |
| focus        | `[name]: string` 组件的值                                     | 输入框获取焦点时触发         |
| add          | `[name]: object` 新增的节点信息<br/>`items: object[]`选项集合 | 新增节点提交时触发           |
| edit         | `[name]: object` 编辑的节点信息<br/>`items: object[]`选项集合 | 编辑节点提交时触发           |
| delete       | `[name]: object` 删除的节点信息<br/>`items: object[]`选项集合 | 删除节点提交时触发           |
| loadFinished | `[name]: object` deferApi 懒加载远程请求成功后返回的数据      | 懒加载接口远程请求成功时触发 |

## 动作表

当前组件对外暴露以下特性动作，其他组件可以通过指定`actionType: 动作名称`、`componentId: 该组件id`来触发这些动作，动作配置可以通过`args: {动作配置项名称: xxx}`来配置具体的参数，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action#触发其他组件的动作)。

| 动作名称 | 动作配置                               | 说明                                                                                    |
| -------- | -------------------------------------- | --------------------------------------------------------------------------------------- |
| clear    | -                                      | 清空                                                                                    |
| reset    | -                                      | 将值重置为`resetValue`，若没有配置`resetValue`，则清空                                  |
| setValue | `value: string` \| `string[]` 更新的值 | 更新数据，开启`multiple`支持设置多项，开启`joinValues`时，多值用`,`分隔，否则多值用数组 |
