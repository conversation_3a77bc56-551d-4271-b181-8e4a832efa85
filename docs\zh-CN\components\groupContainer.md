---
title: GroupContainer 分组容器
description:
type: 0
group: ⚙ 组件
menuName: GroupContainer 分组容器
icon:
order: 25
---

当页面元素需要区分维度分组展示时，可使用分组容器。

分组容器主要分为两种展示模式：灰底背景和白底背景，使用哪种展示模式取决于所在父级容器，两种模式仅UI视觉效果上有区别。

## 模块划分
![列表展示模块图](https://static02.sit.yxmarketing01.com/materialcenter/f8de6bcf-259d-45e5-bab5-0773153ca3e8.png)


  1. 标题区域（非必有）  
  标题区域规范同[Title容器](/dataseeddesigndocui/#/amis/zh-CN/components/title-container)，需要注意的是，标题整个区域非必有，但如果存在，则必须配置`title`主标题。

  1. 内容区域（必有）   
  内容区域是整个分组容器里必须展示的部分，每个分组的内容区域支持再嵌套一层小分组，不支持多层嵌套。

  1. 嵌套小分组区域（非必有）   
  分组内容中元素需要再继续拆分粒度，可使用嵌套小分组，每个小分组包含标题区域（仅支持展示主标题）和内容展示区域，其中内容展示区域不支持再次嵌套分组。


## 场景推荐 
### 灰底背景分组 
以下情况需使用灰底背景：
1. 当分组所在父级为具有灰色背景色的容器时
   
例如常见的编辑页面或详情页面，所在父级容器为灰底背景色的page，这种场景下需要使用灰底背景来进行分组展示

灰底背景展示跟白底背景展示的区别在于，灰底背景下，每个分组之间使用16px的灰色区域来进行区分，分组内标题区域和内容区域由上下边距为16px的分割线进行区分

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "labelWidth": 60,
    "body": {
      "type": "group-container",
      "activeKey": [
        "1"
      ],
      "collapsible": true,
      "items": [
        {
          "header": {
            "title": "第一步，基础信息",
            "assistContent": [
              {
                "type": "remark",
                "content": "这是一段提示"
              }
            ],
            "actions": [
              {
                "type": "button",
                "label": "实验列表",
                "level": "link"
              }
            ],
          },
          "body": [
            {
              "type": "group",
              "body": [
                {
                  "type": "input-text",
                  "name": "text1",
                  "label": "姓名"
                },
                {
                  "type": "input-text",
                  "name": "text2",
                  "label": "年龄"
                },
                {
                  "type": "input-text",
                  "name": "text3",
                  "label": "班级",
                  "required": true
                },
              ]
            },
            {
              "type": "group",
              "body": [
                {
                  "type": "input-text",
                  "name": "text4",
                  "label": "邮箱"
                },
                {
                  "type": "input-text",
                  "name": "text5",
                  "label": "电话"
                },
                {
                  "type": "input-text",
                  "name": "text6",
                  "label": "地址",
                  "columnRatio": 4
                }
              ]
            },
            {
              "type": "group",
              "body": [
                {
                  "type": "input-text",
                  "name": "text7",
                  "label": "其它",
                  "columnRatio": 4
                }
              ]
            },
          ]
        },
        {
          "header": {
            "title": "第二步，复杂信息"
          },
          "body": [
            {
              "type": "group",
              "body": [
                {
                  "type": "input-text",
                  "name": "second1",
                  "label": "邮箱"
                },
                {
                  "type": "input-text",
                  "name": "second2",
                  "label": "电话"
                },
                {
                  "type": "input-text",
                  "name": "second3",
                  "label": "地址",
                  "columnRatio": 4
                }
              ]
            },
            {
              "type": "group",
              "body": [
                {
                  "type": "textarea",
                  "name": "textarea",
                  "label": "姓名",
                  "placehold": "请输入"
                }
              ]
            },
            {
              "type": "group",
              "body": [
                {
                  "type": "input-rich-text",
                  "name": "second5",
                  "label": "其它"
                }
              ]
            }
          ]
        },
        {
          "header": {
            "title": "第三步，策略信息"
          },
          "body": [
            {
              "type": "group",
              "body": [
                {
                  "type": "tabs",
                  "addable": true,
                  "closable": true,
                  "editable": true,
                  "addBtnText": " ",
                  "tabPosition": "center",
                  "tabsMode": "strong",
                  "defaultTabForAdd": {
                    "title": "策略分支x",
                    "body": [
                      {
                        "type": "group",
                        "body": [
                          {
                            "type": "input-text",
                            "name": "third1",
                            "label": "sjksajkd"
                          },
                          {
                            "type": "input-text",
                            "name": "third2",
                            "label": "sjksajkd"
                          },
                          {
                            "type": "input-text",
                            "name": "third3",
                            "label": "sjksajkd"
                          }
                        ]
                      },
                      {
                        "type": "group",
                        "body": [
                          {
                            "type": "input-text",
                            "name": "third4",
                            "label": "sjksajkd"
                          },
                          {
                            "type": "input-text",
                            "name": "third5",
                            "label": "sjksajkd"
                          },
                          {
                            "type": "input-text",
                            "name": "third6",
                            "label": "sjksajkd"
                          }
                        ]
                      }
                    ]
                  },
                  "tabs": [
                    {
                      "title": "策略分支1",
                      "tab": [
                        {
                          "type": "group",
                          "body": [
                            {
                              "type": "input-text",
                              "name": "third1",
                              "label": "sjksajkd"
                            },
                            {
                              "type": "input-text",
                              "name": "third2",
                              "label": "sjksajkd"
                            },
                            {
                              "type": "input-text",
                              "name": "third3",
                              "label": "sjksajkd"
                            }
                          ]
                        },
                        {
                          "type": "group",
                          "body": [
                            {
                              "type": "input-text",
                              "name": "third5",
                              "label": "sjksajkd"
                            },
                            {
                              "type": "select",
                              "name": "third4",
                              "label": "sjksajkd",
                              "options": [
                                {
                                  "label": "a",
                                  "value": "a"
                                },
                                {
                                  "label": "b",
                                  "value": "b"
                                }
                              ]
                            },
                            {
                              "type": "input-text",
                              "name": "third6",
                              "label": "sjksajkd"
                            }
                          ]
                        }
                      ]
                    },
                    {
                      "title": "策略分支2",
                      "tab": [
                        {
                          "type": "group",
                          "body": [
                            {
                              "type": "input-text",
                              "name": "third7",
                              "label": "sjksajkd"
                            },
                            {
                              "type": "input-text",
                              "name": "third8",
                              "label": "sjksajkd"
                            },
                            {
                              "type": "input-text",
                              "name": "third9",
                              "label": "sjksajkd"
                            }
                          ]
                        },
                        {
                          "type": "group",
                          "body": [
                            {
                              "type": "input-text",
                              "name": "third10",
                              "label": "sjksajkd"
                            },
                            {
                              "type": "select",
                              "name": "third4",
                              "label": "sjksajkd",
                              "options": [
                                {
                                  "label": "a",
                                  "value": "a"
                                },
                                {
                                  "label": "b",
                                  "value": "b"
                                }
                              ]
                            },
                            {
                              "type": "input-text",
                              "name": "third12",
                              "label": "sjksajkd"
                            }
                          ]
                        }
                      ]
                    },
                    {
                      "title": "策略分支3",
                      "tab": [
                        {
                          "type": "group",
                          "body": [
                            {
                              "type": "input-text",
                              "name": "third13",
                              "label": "sjksajkd"
                            },
                            {
                              "type": "input-text",
                              "name": "third14",
                              "label": "sjksajkd"
                            },
                            {
                              "type": "input-text",
                              "name": "third15",
                              "label": "sjksajkd"
                            }
                          ]
                        },
                        {
                          "type": "group",
                          "body": [
                            {
                              "type": "input-text",
                              "name": "third16",
                              "label": "sjksajkd"
                            },
                            {
                              "type": "select",
                              "name": "third4",
                              "label": "sjksajkd",
                              "options": [
                                {
                                  "label": "a",
                                  "value": "a"
                                },
                                {
                                  "label": "b",
                                  "value": "b"
                                }
                              ]
                            },
                            {
                              "type": "input-text",
                              "name": "third18",
                              "label": "sjksajkd"
                            }
                          ]
                        }
                      ]
                    }
                  ]
                },
              ]
            }
          ]
        }
      ],
      "actions": [
        {
          "type": "button",
          "label": "取消"
        },
        {
          "type": "submit",
          "level": "primary",
          "label": "保存"
        }
      ]
    }
  }
}
```

- 落地案例  
[大数据一站式-指标平台-指标要素定义-度量-新建](http://moka.dmz.sit.caijj.net/metricsamisui/#/create-measure?id=0&type=create&status=0&link=dim)

![大数据一站式-指标平台-指标要素定义-度量-新建](https://static02.sit.yxmarketing01.com/materialcenter/94b2dd56-d762-4998-920f-c8b3c5a36776.png)


### 白底背景分组
以下情况需使用白底背景：
1. 当分组所在父级为具有白色背景色的容器时

例如在tabs、wizard、dialog内嵌套分组，这种场景下需要使用白底背景来进行分组展示

白底背景展示跟灰底背景展示的区别在于，白底背景下，整个分组容器区域均为白色背景，每个分组之间使用上下边距为16px的分割线来进行区分，分组内标题区域和内容区域无分割线，仅有16px边距区分

```schema
{
  "type": "page",
  "body": {
    "type": "tabs",
    "tabs": [
      {
        "title": "基础表单",
        "body": {
          "type": "form",
          "labelWidth": 60,
          "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
          "body": {
            "type": "group-container",
            "activeKey": [
              "1"
            ],
            "collapsible": true,
            "items": [
              {
                "header": {
                  "title": "第一步，基础信息",
                  "assistContent": [
                    {
                      "type": "remark",
                      "content": "这是一段提示"
                    }
                  ],
                  "actions": [
                    {
                      "type": "button",
                      "label": "实验列表",
                      "level": "link"
                    }
                  ]
                },
                "body": [
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "input-text",
                        "name": "text1",
                        "label": "姓名"
                      },
                      {
                        "type": "input-text",
                        "name": "text2",
                        "label": "年龄"
                      },
                      {
                        "type": "input-text",
                        "name": "text3",
                        "label": "班级",
                        "required": true
                      }
                    ]
                  },
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "input-text",
                        "name": "text4",
                        "label": "邮箱"
                      },
                      {
                        "type": "input-text",
                        "name": "text5",
                        "label": "电话"
                      },
                      {
                        "type": "input-text",
                        "name": "text6",
                        "label": "地址",
                        "columnRatio": 4
                      }
                    ]
                  },
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "input-text",
                        "name": "text7",
                        "label": "其它",
                        "columnRatio": 4
                      }
                    ]
                  }
                ]
              },
              {
                "header": {
                  "title": "第二步，复杂信息",
                },
                "body": [
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "input-text",
                        "name": "second1",
                        "label": "邮箱"
                      },
                      {
                        "type": "input-text",
                        "name": "second2",
                        "label": "电话"
                      },
                      {
                        "type": "input-text",
                        "name": "second3",
                        "label": "地址",
                        "columnRatio": 4
                      }
                    ]
                  },
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "textarea",
                        "name": "textarea",
                        "label": "姓名",
                        "placeholder": "请输入"
                      }
                    ]
                  },
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "input-rich-text",
                        "name": "second5",
                        "label": "其它"
                      }
                    ]
                  }
                ]
              },
              {
                "header": {
                  "title": "第三步，策略信息"
                },
                "body": [
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "tabs",
                        "addable": true,
                        "closable": true,
                        "editable": true,
                        "addBtnText": " ",
                        "tabPosition": "center",
                        "tabsMode": "strong",
                        "defaultTabForAdd": {
                          "title": "策略分支x",
                          "body": [
                            {
                              "type": "group",
                              "body": [
                                {
                                  "type": "input-text",
                                  "name": "third1",
                                  "label": "sjksajkd"
                                },
                                {
                                  "type": "input-text",
                                  "name": "third2",
                                  "label": "sjksajkd"
                                },
                                {
                                  "type": "input-text",
                                  "name": "third3",
                                  "label": "sjksajkd"
                                }
                              ]
                            },
                            {
                              "type": "group",
                              "body": [
                                {
                                  "type": "input-text",
                                  "name": "third4",
                                  "label": "sjksajkd"
                                },
                                {
                                  "type": "input-text",
                                  "name": "third5",
                                  "label": "sjksajkd"
                                },
                                {
                                  "type": "input-text",
                                  "name": "third6",
                                  "label": "sjksajkd"
                                }
                              ]
                            }
                          ]
                        },
                        "tabs": [
                          {
                            "title": "策略分支1",
                            "tab": [
                              {
                                "type": "group",
                                "body": [
                                  {
                                    "type": "input-text",
                                    "name": "third1",
                                    "label": "sjksajkd"
                                  },
                                  {
                                    "type": "input-text",
                                    "name": "third2",
                                    "label": "sjksajkd"
                                  },
                                  {
                                    "type": "input-text",
                                    "name": "third3",
                                    "label": "sjksajkd"
                                  }
                                ]
                              },
                              {
                                "type": "group",
                                "body": [
                                  {
                                    "type": "input-text",
                                    "name": "third5",
                                    "label": "sjksajkd"
                                  },
                                  {
                                    "type": "select",
                                    "name": "third4",
                                    "label": "sjksajkd",
                                    "options": [
                                      {
                                        "label": "a",
                                        "value": "a"
                                      },
                                      {
                                        "label": "b",
                                        "value": "b"
                                      }
                                    ]
                                  },
                                  {
                                    "type": "input-text",
                                    "name": "third6",
                                    "label": "sjksajkd"
                                  }
                                ]
                              }
                            ]
                          },
                          {
                            "title": "策略分支2",
                            "tab": [
                              {
                                "type": "group",
                                "body": [
                                  {
                                    "type": "input-text",
                                    "name": "third7",
                                    "label": "sjksajkd"
                                  },
                                  {
                                    "type": "input-text",
                                    "name": "third8",
                                    "label": "sjksajkd"
                                  },
                                  {
                                    "type": "input-text",
                                    "name": "third9",
                                    "label": "sjksajkd"
                                  }
                                ]
                              },
                              {
                                "type": "group",
                                "body": [
                                  {
                                    "type": "input-text",
                                    "name": "third10",
                                    "label": "sjksajkd"
                                  },
                                  {
                                    "type": "select",
                                    "name": "third4",
                                    "label": "sjksajkd",
                                    "options": [
                                      {
                                        "label": "a",
                                        "value": "a"
                                      },
                                      {
                                        "label": "b",
                                        "value": "b"
                                      }
                                    ]
                                  },
                                  {
                                    "type": "input-text",
                                    "name": "third12",
                                    "label": "sjksajkd"
                                  }
                                ]
                              }
                            ]
                          },
                          {
                            "title": "策略分支3",
                            "tab": [
                              {
                                "type": "group",
                                "body": [
                                  {
                                    "type": "input-text",
                                    "name": "third13",
                                    "label": "sjksajkd"
                                  },
                                  {
                                    "type": "input-text",
                                    "name": "third14",
                                    "label": "sjksajkd"
                                  },
                                  {
                                    "type": "input-text",
                                    "name": "third15",
                                    "label": "sjksajkd"
                                  }
                                ]
                              },
                              {
                                "type": "group",
                                "body": [
                                  {
                                    "type": "input-text",
                                    "name": "third16",
                                    "label": "sjksajkd"
                                  },
                                  {
                                    "type": "select",
                                    "name": "third4",
                                    "label": "sjksajkd",
                                    "options": [
                                      {
                                        "label": "a",
                                        "value": "a"
                                      },
                                      {
                                        "label": "b",
                                        "value": "b"
                                      }
                                    ]
                                  },
                                  {
                                    "type": "input-text",
                                    "name": "third18",
                                    "label": "sjksajkd"
                                  }
                                ]
                              }
                            ]
                          }
                        ]
                      },
                    ]
                  }
                ]
              }
            ],
            "actions": [
              {
                "type": "button",
                "label": "取消"
              },
              {
                "type": "submit",
                "level": "primary",
                "label": "保存"
              }
            ]
          }
        }
      }
    ]
  }
}
```

- 落地案例  
[大数据一站式-指标平台-指标要素定义-维度-新建](http://moka.dmz.sit.caijj.net/metricsamisui/#/dimension?_shMenuId=menu_P0280_define_dimension)

![大数据一站式-指标平台-指标要素定义-维度-新建](https://static02.sit.yxmarketing01.com/materialcenter/0f1c5c6a-7f76-431f-b96e-7064ca29b5fc.png)


### 无标题分组
当分组不需要展示标题时，可不展示

> **注意：**多个分组时，仅支持连续的无标题分组+连续的带标题分组，例如分组1～分组2都没有标题，分组3有标题，那么分组3后续的所有分组都必须带标题；如果分组1有标题，则后续所有分组都必须带标题
> 

```schema
{
  "type": "page",
  "data": {
    "text1": "aaaa",
    "text2": 18,
    "text3": "7年级",
    "text4": "<EMAIL>",
    "text5": "********",
    "text6": "上海市浦东新区",
    "text7": "测试",
    "text8": "text8",
    "text9": "text9",
    "second1": "<EMAIL>",
    "second2": "********",
    "second3": "上海市浦东新区",
    "second4": "上海市浦东新区"
  },
  "body": {
    "type": "tabs",
    "tabs": [
      {
        "title": "无标题分组",
        "tab": {
          "type": "form",
          "actions": [],
          "body": {
            "type": "group-container",
            "items": [
              {
                "type": "panel",
                "body": [
                  {
                    "type": "group",
                    "body": [
                      {
                        "name": "text1",
                        "type": "static",
                        "label": "静态展示",
                        "quickEdit": {
                          "type": "input-text"
                        }
                      },
                      {
                        "type": "static",
                        "name": "text2",
                        "label": "年龄"
                      },
                      {
                        "type": "static",
                        "name": "text3",
                        "label": "班级",
                        "required": true
                      }
                    ]
                  },
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "static",
                        "name": "text4",
                        "label": "邮箱"
                      },
                      {
                        "type": "static",
                        "name": "text5",
                        "label": "电话"
                      },
                      {
                        "type": "static",
                        "name": "text6",
                        "label": "地址",
                        "columnRatio": 4
                      }
                    ]
                  },
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "static",
                        "name": "text7",
                        "label": "其它",
                        "columnRatio": 4
                      }
                    ]
                  }
                ]
              },
              {
                "type": "panel",
                "body": [
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "static",
                        "name": "second1",
                        "label": "邮箱"
                      },
                      {
                        "type": "static",
                        "name": "second2",
                        "label": "电话"
                      },
                      {
                        "type": "static",
                        "name": "second3",
                        "label": "地址",
                        "columnRatio": 4
                      }
                    ]
                  },
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "static",
                        "name": "second4",
                        "label": "地址",
                        "columnRatio": 4
                      }
                    ]
                  }
                ]
              },
              {
                "type": "panel",
                "header": {
                  "title": "策略信息"
                },
                "body": [
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "static",
                        "name": "second1",
                        "label": "邮箱"
                      },
                      {
                        "type": "static",
                        "name": "second2",
                        "label": "电话"
                      },
                      {
                        "type": "static",
                        "name": "second3",
                        "label": "地址",
                        "columnRatio": 4
                      }
                    ]
                  },
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "static",
                        "name": "second4",
                        "label": "地址",
                        "columnRatio": 4
                      }
                    ]
                  }
                ]
              }
            ]
          }
        }
      }
    ]
  }
}
```

- 落地案例  
[资金平台-运营工单-资方授信额度调整](https://moka.sit.caijj.net/fundui/#/bankLimitAdjust?workOrderType=BUSINESS_CONDITIONS&subWorkOrderType=BANK_LIMIT)

![资金平台-运营工单-资方授信额度调整](https://static02.sit.yxmarketing01.com/materialcenter/27e8a8d0-ee13-438b-a86d-2028ab63d9de.png)


### 分组内嵌套小分组
当分组内元素较多、或需要再次进行分组归纳时，可嵌套小分组展示

> **注意：**仅支持在分组容器内容区域再嵌套一层小分组，不支持多层嵌套

```schema
{
  "type": "page",
  "data": {
    "inputTable": [
      {
        "a": "a1",
        "b": "b1"
      },
      {
        "a": "a2",
        "b": "b2"
      },
      {
        "a": "a3",
        "b": "b3"
      }
    ],
    "operateTable": [
      {
        "a": "a1",
        "b": "a2",
        "c": "a3",
        "d": "a4"
      },
      {
        "a": "a1",
        "b": "a2",
        "c": "a3",
        "d": "a4"
      },
      {
        "a": "a1",
        "b": "a2",
        "c": "a3",
        "d": "a4"
      }
    ]
  },
  "body": [
    {
      "type": "title",
      "iconConfig": {
        "type": "icon",
        "icon": "chevron-left"
      },
      "title": "页面大标题名称大标题名称",
      "subtitle": "我是小标题",
      "assistContent": [
        {
          "type": "tag",
          "label": "普通标签",
          "color": "processing"
        }
      ]
    },
    {
      "type": "form",
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
      "body": {
        "type": "group-container",
        "items": [
          {
            "header": {
              "title": "第一步，基础信息",
              "assistContent": [
                {
                  "type": "remark",
                  "content": "这是一段提示"
                }
              ],
              "actions": [
                {
                  "type": "button",
                  "label": "实验列表",
                  "level": "link"
                }
              ],
            },
            "body": [
              {
                "type": "group",
                "body": [
                  {
                    "type": "input-text",
                    "name": "text1",
                    "label": "姓名"
                  },
                  {
                    "type": "input-text",
                    "name": "text2",
                    "label": "年龄"
                  },
                  {
                    "type": "input-text",
                    "name": "text3",
                    "label": "班级",
                    "required": true
                  }
                ]
              },
              {
                "type": "group",
                "body": [
                  {
                    "type": "input-text",
                    "name": "text4",
                    "label": "邮箱"
                  },
                  {
                    "type": "input-text",
                    "name": "text5",
                    "label": "电话"
                  },
                  {
                    "type": "input-text",
                    "name": "text6",
                    "label": "地址",
                    "columnRatio": 4
                  }
                ]
              },
              {
                "type": "group",
                "body": [
                  {
                    "type": "input-text",
                    "name": "text7",
                    "label": "其它",
                    "columnRatio": 4
                  }
                ]
              }
            ]
          },
          {
            "header": {
              "title": "第二步，复杂信息"
            },
            "body": [
              {
                "type": "flex",
                "gap": true,
                "direction": "column",
                "items": [
                  {
                    "type": "alert",
                    "body": "提示类文案这里是信息提示",
                    "level": "info",
                    "showIcon": true
                  },
                  {
                    "type": "tpl",
                    "tpl": "系统默认设置信水平：100%"
                  },
                ]
              },
              {
                "type": "group-container",
                "isSmallGroup": true,
                "items": [
                  {
                    "header": {
                      "title": "驱动指标配置"
                    },
                    "body": [
                      {
                        "type": "select",
                        "name": "eventSource",
                        "label": "驱动指标",
                        "required": true,
                        "options": [
                          {
                            "label": "a",
                            "value": "a"
                          },
                          {
                            "label": "b",
                            "value": "b"
                          }
                        ],
                        "className": "flex-grow"
                      },
                      {
                        "name": "inputTable",
                        "type": "input-table",
                        "showErrorMsg": false,
                        "columns": [
                          {
                            "label": "A",
                            "name": "a"
                          },
                          {
                            "label": "B",
                            "name": "b"
                          }
                        ]
                      }
                    ]
                  },
                  {
                    "header": {
                      "title": "关注指标配置"
                    },
                    "body": [
                      {
                        "type": "flex",
                        "gap": true,
                        "items": [
                          {
                            "type": "select",
                            "name": "drive",
                            "label": "关注指标",
                            "required": true,
                            "columnRatio": 10,
                            "className": "flex-grow",
                            "options": [
                              {
                                "label": "a",
                                "value": "a"
                              },
                              {
                                "label": "b",
                                "value": "b"
                              }
                            ]
                          },

                          {
                            "type": "button",
                            "label": "新建",
                            "level": "link",
                            "linkWithoutPadding": true
                          },
                          {
                            "type": "button",
                            "label": "详情",
                            "level": "link",
                            "linkWithoutPadding": true
                          }
                        ]
                      },
                      {
                        "name": "operateTable",
                        "type": "input-table",
                        "showErrorMsg": false,
                        "columns": [
                          {
                            "name": "a",
                            "label": "指标名称"
                          },
                          {
                            "name": "b",
                            "label": "列表2"
                          },
                          {
                            "name": "c",
                            "label": "列表3"
                          },
                          {
                            "name": "d",
                            "label": "列表4"
                          },
                          {
                            "type": "operation",
                            "label": "操作",
                            "buttons": [
                              {
                                "label": "",
                                "type": "button",
                                "actionType": "dialog",
                                "icon": "fa fa-plus",
                                "dialog": {
                                  "title": "弹框",
                                  "showCloseButton": false,
                                  "body": "这是个简单的弹框。"
                                }
                              },
                              {
                                "label": "",
                                "type": "button",
                                "actionType": "dialog",
                                "icon": "fa fa-pencil-square-o",
                                "dialog": {
                                  "title": "弹框",
                                  "showCloseButton": false,
                                  "body": "这是个简单的弹框。"
                                }
                              },
                              {
                                "label": "",
                                "type": "button",
                                "actionType": "dialog",
                                "icon": "fa fa-minus",
                                "dialog": {
                                  "title": "弹框",
                                  "showCloseButton": false,
                                  "body": "这是个简单的弹框。"
                                }
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      },
      "actions": [
        {
          "type": "button",
          "label": "取消"
        },
        {
          "type": "submit",
          "level": "primary",
          "label": "保存"
        }
      ]
    }
  ]
}
```

- 落地案例  
[账务平台-业务配置-合作方清结算-财务费项关联业务事项-新建](http://moka.dmz.sit.caijj.net/accountsettlementui/#/feeBusinessEvent?_shMenuId=FSNH68F90A4E347B45AA81B047745D5BB1A5)

![账务平台-业务配置-合作方清结算-财务费项关联业务事项-新建](https://static02.sit.yxmarketing01.com/materialcenter/7561d881-7c29-467d-a585-9be90f2a170a.png)

### 带全局操作按钮

需要针对整个分组区域有操作按钮的场景，可在分组区域的左上方放置操作按钮

```schema
{
  "type": "page",
  "data": {
    "text1": "aaaa",
    "text2": 18,
    "text3": "7年级",
    "text4": "<EMAIL>",
    "text5": "********",
    "text6": "上海市浦东新区",
    "text7": "测试",
    "text8": "text8",
    "text9": "text9",
    "second1": "<EMAIL>",
    "second2": "********",
    "second3": "上海市浦东新区",
    "second4": "上海市浦东新区"
  },
  "body": {
    "type": "tabs",
    "tabs": [
      {
        "title": "tab1",
        "tab": {
          "type": "form",
          "actions": [],
          "static": true,
          "body": {
            "type": "flex",
            "direction": "column",
            "gap": true,
            "items": [
              {
                "type": "button",
                "label": "编辑",
                "actionType": "url",
                "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
                "level": "primary",
                "blank": false
              },
              {
                "type": "group-container",
                "collapsible": false,
                "items": [
                  {
                    "type": "panel",
                    "header": {
                      "title": "第一步，基础信息"
                    },
                    "body": [
                      {
                        "type": "group",
                        "body": [
                          {
                            "type": "input-text",
                            "name": "text1",
                            "label": "姓名"
                          },
                          {
                            "type": "input-text",
                            "name": "text2",
                            "label": "年龄"
                          }
                        ]
                      },
                      {
                        "type": "group",
                        "body": [
                          {
                            "type": "input-text",
                            "name": "text4",
                            "label": "邮箱"
                          },
                          {
                            "type": "input-text",
                            "name": "text5",
                            "label": "电话"
                          }
                        ]
                      },
                      {
                        "type": "group",
                        "body": [
                          {
                            "type": "input-text",
                            "name": "text7",
                            "label": "其它",
                            "columnRatio": 6
                          }
                        ]
                      }
                    ]
                  },
                  {
                    "type": "panel",
                    "header": {
                      "title": "第二步，复杂信息"
                    },
                    "body": [
                      {
                        "type": "group",
                        "body": [
                          {
                            "type": "input-text",
                            "name": "second1",
                            "label": "邮箱"
                          },
                          {
                            "type": "input-text",
                            "name": "second2",
                            "label": "电话"
                          }
                        ]
                      },
                      {
                        "type": "group",
                        "body": [
                          {
                            "type": "input-text",
                            "name": "textarea",
                            "label": "姓名",
                            "maxLength": 30,
                            "showCounter": true,
                            "placeholder": "请输入"
                          }
                        ]
                      },
                      {
                        "type": "group",
                        "body": [
                          {
                            "type": "input-text",
                            "name": "second5",
                            "label": "其它"
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          }
        }
      }
    ]
  }
}
```

### 分组带操作按钮

当第一组的某个操作按钮影响着第二组的展示信息时，可以在分组内容的左下角放置操作按钮

<!-- 当需要针对某个分组内的交互有相关操作按钮时，可在分组的标题区域右侧配置操作按钮 -->

```schema
{
  "type": "page",
  "data": {
    "table": [
      {
        "a": "a1",
        "b": "b1",
        "c": "c1"
      },
      {
        "a": "a2",
        "b": "b2",
        "c": "c2"
      }
    ],
    "title": "获取衍生特征值时，需传入以下参数，请赋值:"
  },
  "body": {
    "type": "form",
    "title": "",
    "mode": "normal",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
    "actions": [],
    "body": {
      "type": "group-container",
      "items": [
        {
          "header": {
            "title": "获取衍生特征值时，需传入以下参数，请赋值"
          },
          "body": [
            {
              "type": "input-table",
              "name": "table",
              "label": "特征参数",
              "perPage": 1,
              "columnRatio": 12,
              "columns": [
                {
                  "name": "a",
                  "label": "A"
                },
                {
                  "name": "b",
                  "label": "B"
                },
                {
                  "name": "c",
                  "label": "C",
                  "type": "input-text",
                  "placeholder": "请输入数字",
                }
              ]
            },
            {
              "type": "button",
              "level": "primary",
              "columnRatio": 12,
              "label": "测试"
            }
          ]
        },
        {
          "header": {
            "title": "测试结果"
          },
          "body": {
            "type": "flex",
            "direction": "column",
            "gap": true,
            "items": [
              {
                "type": "flex",
                "justify": "start",
                "items": [
                  {
                    "type": "tpl",
                    "tpl": "调用成功！",
                    "className": "text-success"
                  },
                  {
                    "type": "tpl",
                    "tpl": "结果如下："
                  }
                ]
              },
              {
                "type": "table",
                "source": "$table",
                "columns": [
                  {
                    "name": "a",
                    "label": "A"
                  },
                  {
                    "name": "b",
                    "label": "B"
                  },
                  {
                    "name": "c",
                    "label": "C"
                  }
                ]
              },
              {
                "type": "tpl",
                "tpl": "接口响应："
              },
              {
                "type": "panel",
                "body": {
                  "type": "code",
                  "language": "json",
                  "formatter": true,
                  "value": "{\"a\":1, \"b\":2, \"c\": {\"c1\": 3}}"
                }
              }
            ]
          }
        }
      ]
    }
  }
}
```

- 落地案例  
  [客经平台-活动管理-回溯空跑任务列表-新建任务按钮](http://moka.dmz.sit.caijj.net/bizui/#/recallList?_shMenuId=RK8T7C043281B3CC420A9F9B30A4E8BF5659)  
   ![客经平台-活动管理-回溯空跑任务列表-新建任务按钮](https://static02.sit.yxmarketing01.com/tdmaterial/09674f9dd208493fa608dd6a6e35d63f.png)

### 版本对比
#### 版本对比结果分模块展示

页面元素区分维度分组展示对比结果

```schema
{
  "type": "page",
  "data": {
    "basicInfo": [
      {
        "changeType": "",
        "key-version1": "12345",
        "key-version2": "12345",
        "keyStatus": "",
        "id-version1": "查询用户列表",
        "id-version2": "查询用户列表",
        "idStatus": "",
        "text1Status": "",
        "text4Status": "EDIT",
        "text5Status": "DELETE",
        "text6Status": "ADD",
        "text1-version1": "在线",
        "text1-version2": "在线",
        "text4-version1": "GET",
        "text4-version2": "POST",
        "text5-version1": "/users",
        "text5-version2": "/users",
        "text6-version1": "",
        "text6-version2": "查询用户列表"
      }
    ],
    "queryList": [
      {
        "id": 1,
        "changeType": "",
        "key-version1": "k1",
        "key-version2": "k1",
        "keyStatus": "",
        "id-version1": "userId",
        "id-version2": "userId",
        "idStatus": "",
        "text1Status": "",
        "text4Status": "",
        "text5Status": "",
        "text1-version1": "string",
        "text1-version2": "string",
        "text4-version1": "是",
        "text4-version2": "是",
        "text5-version1": "用户Id",
        "text5-version2": "用户Id",
        "grade": "ABCD",
        "grade1": "ABCD",
        "grade2": "B",
        "grade3": "C",
        "gradeStatus": "inactive",
        "grade1Status": "inactive",
        "grade2Status": "inactive",
        "grade3Status": "inactive"
      },
      {
        "id": 2,
        "changeType": "增",
        "key-version1": "-",
        "key-version2": "k2",
        "keyStatus": "ADD",
        "id-version1": "",
        "id-version2": "userType",
        "idStatus": "ADD",
        "text1Status": "ADD",
        "text4Status": "ADD",
        "text5Status": "ADD",
        "text1-version1": "",
        "text1-version2": "string",
        "text4-version1": "",
        "text4-version2": "是",
        "text5-version1": "",
        "text5-version2": "用户类型",
        "grade": "A这里有更多撒金凤卡少女反馈加啊是南非进口三反馈加三分快三方面小美女你发怒发怒发怒",
        "grade1": "1232178346816421641326426147126473214143214",
        "grade2": "sadkjhsakdfhskahfkashfdksahfkasdksafkadsfkasf",
        "grade3": "sadkjhsakdfhskahfkashfdksahfkasdksafkadsfkasf",
        "gradeStatus": "success",
        "grade1Status": "success",
        "grade2Status": "success",
        "grade3Status": "success"
      },
      {
        "id": 3,
        "changeType": "改",
        "key-version1": "k3",
        "key-version2": "k3",
        "keyStatus": "",
        "id-version1": "userId",
        "id-version2": "userId",
        "idStatus": "",
        "text1Status": "EDIT",
        "text4Status": "",
        "text5Status": "",
        "text1-version1": "string",
        "text1-version2": "integer",
        "text4-version1": "是",
        "text4-version2": "是",
        "text5-version1": "用户Id",
        "text5-version2": "用户Id",
        "grade": "这里有更多撒金凤卡少女反馈加啊是南非进口三反馈加三分快三方面小美女你发怒发怒发怒",
        "grade1": "1232178346816421641326426147126473214143214",
        "grade2": "sadkjhsakdfhskahfkashfdksahfkasdksafkadsfkasf",
        "grade3": "23914729310",
        "gradeStatus": "error",
        "grade1Status": "error",
        "grade2Status": "success",
        "grade3Status": "success"
      },
      {
        "id": 4,
        "changeType": "删",
        "key-version1": "k4",
        "key-version2": "k4",
        "keyStatus": "DELETE",
        "id-version1": "userId",
        "id-version2": "userId",
        "idStatus": "DELETE",
        "text1Status": "DELETE",
        "text4Status": "DELETE",
        "text5Status": "DELETE",
        "text1-version1": "string",
        "text1-version2": "string",
        "text4-version1": "是",
        "text4-version2": "是",
        "text5-version1": "用户Id",
        "text5-version2": "用户Id",
        "grade": "这里有更多撒金凤卡少女反馈加啊是南非进口三反馈加三分快三方面小美女你发怒发怒发怒",
        "grade1": "1232178346816421641326426147126473214143214",
        "grade2": "sadkjhsakdfhskahfkashfdksahfkasdksafkadsfkasf",
        "gradeStatus": "error",
        "grade1Status": "error",
        "grade2Status": "error",
        "grade3Status": "error"
      }
    ]
  },
  "body": {
    "type": "form",
    "title": "",
    "actions": [],
    "labelWidth": 60,
    "body": {
      "type": "group-container",
      "items": [
        {
          "body": [
            {
              "type": "flex",
              "direction": "column",
              "gap": true,
              "items": [
                {
                  "type": "tags",
                  "items": [
                    {
                      "type": "tag",
                      "label": "新增",
                      "displayMode": "bordered",
                      "color": "add-status"
                    },
                    {
                      "type": "tag",
                      "label": "修改",
                      "displayMode": "bordered",
                      "color": "edit-status"
                    },
                    {
                      "type": "tag",
                      "label": "删除",
                      "displayMode": "bordered",
                      "color": "delete-status"
                    }
                  ]
                },
                {
                  "type": "group",
                  "body": [
                    {
                      "type": "radios",
                      "value": "byKey",
                      "name": "diffType",
                      "label": "对比模式",
                      "columnRatio": 8,
                      "options": [
                        {
                          "label": "按参数Key",
                          "value": "byKey"
                        },
                        {
                          "label": "按参数Name",
                          "value": "byName"
                        }
                      ]
                    }
                  ]
                },
                {
                  "type": "group",
                  "body": [
                    {
                      "type": "select",
                      "label": "基准版本",
                      "name": "baselineVersion",
                      "value": "v1",
                      "columnRatio": 4,
                      "options": [
                        {
                          "label": "V1",
                          "value": "v1"
                        },
                        {
                          "label": "V2",
                          "value": "v2"
                        }
                      ]
                    },
                    {
                      "type": "select",
                      "label": "对比版本",
                      "name": "diffVersion",
                      "value": "v2",
                      "columnRatio": 4,
                      "options": [
                        {
                          "label": "V1",
                          "value": "v1"
                        },
                        {
                          "label": "V2",
                          "value": "v2"
                        }
                      ]
                    }
                  ]
                },
                {
                  "type": "alert",
                  "level": "info",
                  "body": [
                    {
                      "type": "tpl",
                      "tpl": "结论"
                    },
                    {
                      "type": "remark",
                      "content": "破坏性变更：对消费者影响严重；兼容性变更：对消费者影响轻微或无影响"
                    },
                    {
                      "type": "tpl",
                      "tpl": "："
                    },
                    {
                      "type": "tpl",
                      "tpl": "兼容性变更"
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          "header": {
            "title": "基础信息"
          },
          "body": [
            {
              "type": "crud",
              "syncLocation": false,
              "columnsTogglable": false,
              "autoGenerateFilter": {
                "showBtnToolbar": false,
                "defaultExpanded": false
              },
              "footerToolbar": [],
              "source": "$basicInfo",
              "columns": [
                {
                  "name": "key-version1",
                  "label": "API ID",
                  "type": "group",
                  "direction": "vertical",
                  "body": [
                    {
                      "type": "tpl",
                      "visibleOn": "${!keyStatus}",
                      "tpl": "${key-version2}"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${keyStatus === 'ADD'}",
                      "tpl": "<span class='pm-versionDiff-add'>${key-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${keyStatus === 'DELETE'}",
                      "tpl": "<span class='pm-versionDiff-delete'>${key-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${keyStatus === 'EDIT'}",
                      "tpl": "<span class='pm-diff-edit-preValue-text-color'>${key-version1}</span>&nbsp;&#8594;&nbsp;"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${keyStatus === 'EDIT'}",
                      "tpl": "<span class='pm-versionDiff-edit'>${key-version2}</span>"
                    }
                  ]
                },
                {
                  "name": "id-version1",
                  "label": "API名称",
                  "type": "group",
                  "direction": "vertical",
                  "body": [
                    {
                      "type": "tpl",
                      "visibleOn": "${!idStatus}",
                      "tpl": "${id-version2}"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${idStatus === 'ADD'}",
                      "tpl": "<span class='pm-versionDiff-add'>${id-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${idStatus === 'DELETE'}",
                      "tpl": "<span class='pm-versionDiff-delete'>${id-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${idStatus === 'EDIT'}",
                      "tpl": "<span class='pm-diff-edit-preValue-text-color'>${id-version1}</span>&nbsp;&#8594;&nbsp;"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${idStatus === 'EDIT'}",
                      "tpl": "<span class='pm-versionDiff-edit'>${id-version2}</span>"
                    }
                  ]
                },
                {
                  "name": "text1-version2",
                  "label": "API 状态",
                  "type": "group",
                  "direction": "vertical",
                  "body": [
                    {
                      "type": "tpl",
                      "visibleOn": "${!text1Status}",
                      "tpl": "${text1-version2}"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text1Status === 'ADD'}",
                      "tpl": "<span class='pm-versionDiff-add'>${text1-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text1Status === 'DELETE'}",
                      "tpl": "<span class='pm-versionDiff-delete'>${text1-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text1Status === 'EDIT'}",
                      "tpl": "<span class='pm-diff-edit-preValue-text-color'>${text1-version1}</span>&nbsp;&#8594;&nbsp;"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text1Status === 'EDIT'}",
                      "tpl": "<span class='pm-versionDiff-edit'>${text1-version2}</span>"
                    }
                  ]
                },
                {
                  "name": "text4-version1",
                  "label": "Method",
                  "type": "group",
                  "direction": "vertical",
                  "body": [
                    {
                      "type": "tpl",
                      "visibleOn": "${!text4Status}",
                      "tpl": "${text4-version2}"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text4Status === 'ADD'}",
                      "tpl": "<span class='pm-versionDiff-add'>${text4-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text4Status === 'DELETE'}",
                      "tpl": "<span class='pm-versionDiff-delete'>${text4-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text4Status === 'EDIT'}",
                      "tpl": "<span class='pm-diff-edit-preValue-text-color'>${text4-version1}</span>&nbsp;&#8594;&nbsp;"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text4Status === 'EDIT'}",
                      "tpl": "<span class='pm-versionDiff-edit'>${text4-version2}</span>"
                    }
                  ]
                },
                {
                  "name": "text5-version1",
                  "label": "URL",
                  "type": "group",
                  "direction": "vertical",
                  "body": [
                    {
                      "type": "tpl",
                      "visibleOn": "${text5Status !== 'EDIT'}",
                      "tpl": "<span class='${text5Status === 'ADD' ? 'pm-versionDiff-add' : (text5Status === 'DELETE' ? 'pm-versionDiff-delete  pm-versionDiff-delete' : '')}'>${text5-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text5Status === 'EDIT' }",
                      "tpl": "修改前：<span>${text5-version1}</span></br>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text5Status === 'EDIT' }",
                      "tpl": "修改后：<span class='pm-versionDiff-edit'>${text5-version2}</span>"
                    }
                  ]
                },
                {
                  "name": "text6-version1",
                  "label": "描述",
                  "type": "group",
                  "direction": "vertical",
                  "body": [
                    {
                      "type": "tpl",
                      "visibleOn": "${!text6Status}",
                      "tpl": "${text6-version2}"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text6Status === 'ADD'}",
                      "tpl": "<span class='pm-versionDiff-add'>${text6-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text6Status === 'DELETE'}",
                      "tpl": "<span class='pm-versionDiff-delete'>${text6-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text6Status === 'EDIT'}",
                      "tpl": "<span class='pm-diff-edit-preValue-text-color'>${text6-version1}</span>&nbsp;&#8594;&nbsp;"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text6Status === 'EDIT'}",
                      "tpl": "<span class='pm-versionDiff-edit'>${text6-version2}</span>"
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          "header": {
            "title": "请求参数"
          },
          "body": [
            {
              "type": "crud",
              "syncLocation": false,
              "columnsTogglable": false,
              "autoGenerateFilter": {
                "showBtnToolbar": false,
                "defaultExpanded": false
              },
              "footerToolbar": [],
              "title": "Header(请求头参数)",
              "columns": [
                {
                  "name": "key-version1",
                  "label": "参数key",
                  "type": "group",
                  "direction": "vertical",
                  "body": [
                    {
                      "type": "tpl",
                      "visibleOn": "${!keyStatus}",
                      "tpl": "${key-version2}"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${keyStatus === 'ADD'}",
                      "tpl": "<span class='pm-versionDiff-add'>${key-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${keyStatus === 'DELETE'}",
                      "tpl": "<span class='pm-versionDiff-delete'>${key-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${keyStatus === 'EDIT'}",
                      "tpl": "<span class='pm-diff-edit-preValue-text-color'>${key-version1}</span>&nbsp;&#8594;&nbsp;"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${keyStatus === 'EDIT'}",
                      "tpl": "<span class='pm-versionDiff-edit'>${key-version2}</span>"
                    }
                  ]
                },
                {
                  "name": "id-version1",
                  "label": "参数名称",
                  "type": "group",
                  "direction": "vertical",
                  "body": [
                    {
                      "type": "tpl",
                      "visibleOn": "${!idStatus}",
                      "tpl": "${id-version2}"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${idStatus === 'ADD'}",
                      "tpl": "<span class='pm-versionDiff-add'>${id-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${idStatus === 'DELETE'}",
                      "tpl": "<span class='pm-versionDiff-delete'>${id-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${idStatus === 'EDIT'}",
                      "tpl": "<span class='pm-diff-edit-preValue-text-color'>${id-version1}</span>&nbsp;&#8594;&nbsp;"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${idStatus === 'EDIT'}",
                      "tpl": "<span class='pm-versionDiff-edit'>${id-version2}</span>"
                    }
                  ]
                },
                {
                  "name": "text1-version2",
                  "label": "数据类型",
                  "type": "group",
                  "direction": "vertical",
                  "body": [
                    {
                      "type": "tpl",
                      "visibleOn": "${!text1Status}",
                      "tpl": "${text1-version2}"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text1Status === 'ADD'}",
                      "tpl": "<span class='pm-versionDiff-add'>${text1-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text1Status === 'DELETE'}",
                      "tpl": "<span class='pm-versionDiff-delete'>${text1-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text1Status === 'EDIT'}",
                      "tpl": "<span class='pm-diff-edit-preValue-text-color'>${text1-version1}</span>&nbsp;&#8594;&nbsp;"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text1Status === 'EDIT'}",
                      "tpl": "<span class='pm-versionDiff-edit'>${text1-version2}</span>"
                    }
                  ]
                },
                {
                  "name": "text4-version1",
                  "label": "是否必填",
                  "type": "group",
                  "direction": "vertical",
                  "body": [
                    {
                      "type": "tpl",
                      "visibleOn": "${!text4Status}",
                      "tpl": "${text4-version2}"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text4Status === 'ADD'}",
                      "tpl": "<span class='pm-versionDiff-add'>${text4-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text4Status === 'DELETE'}",
                      "tpl": "<span class='pm-versionDiff-delete'>${text4-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text4Status === 'EDIT'}",
                      "tpl": "<span class='pm-diff-edit-preValue-text-color'>${text4-version1}</span>&nbsp;&#8594;&nbsp;"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text4Status === 'EDIT'}",
                      "tpl": "<span class='pm-versionDiff-edit'>${text4-version2}</span>"
                    }
                  ]
                },
                {
                  "name": "text5-version1",
                  "label": "说明",
                  "type": "group",
                  "direction": "vertical",
                  "body": [
                    {
                      "type": "tpl",
                      "visibleOn": "${!text5Status}",
                      "tpl": "${text5-version2}"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text5Status === 'ADD'}",
                      "tpl": "<span class='pm-versionDiff-add'>${text5-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text5Status === 'DELETE'}",
                      "tpl": "<span class='pm-versionDiff-delete'>${text5-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text5Status === 'EDIT'}",
                      "tpl": "<span class='pm-diff-edit-preValue-text-color'>${text5-version1}</span>&nbsp;&#8594;&nbsp;"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text5Status === 'EDIT'}",
                      "tpl": "<span class='pm-versionDiff-edit'>${text5-version2}</span>"
                    }
                  ]
                },
                {
                  "name": "tag-version1",
                  "label": "标签",
                  "type": "tags",
                  "direction": "vertical",
                  "width": "160px",
                  "items": [
                    [
                      {
                        "type": "tooltip-wrapper",
                        "placement": "left",
                        "content": "${grade}",
                        "visibleOn": "this.gradeStatus === 'inactive' && grade",
                        "body": {
                          "type": "tag",
                          "displayMode": "bordered",
                          "label": "${grade}",
                          "color": "${gradeStatus}"
                        }
                      },
                      {
                        "type": "tooltip-wrapper",
                        "placement": "left",
                        "content": "${grade}",
                        "visibleOn": "this.gradeStatus === 'success' && grade",
                        "body": {
                          "type": "tag",
                          "displayMode": "bordered",
                          "label": "${grade}",
                          "color": "${gradeStatus}"
                        }
                      },
                      {
                        "type": "tooltip-wrapper",
                        "placement": "left",
                        "content": "${grade}",
                        "visibleOn": "this.gradeStatus === 'error' && grade",
                        "body": {
                          "type": "tag",
                          "displayMode": "bordered",
                          "label": "${grade}",
                          "style": {
                            "textDecoration": "line-through"
                          },
                          "color": "${gradeStatus}"
                        }
                      }
                    ],
                    [
                      {
                        "type": "tooltip-wrapper",
                        "placement": "left",
                        "content": "${grade1}",
                        "visibleOn": "this.grade1Status === 'inactive' && grade1",
                        "body": {
                          "type": "tag",
                          "displayMode": "bordered",
                          "label": "${grade1}",
                          "color": "${grade1Status}"
                        }
                      },
                      {
                        "type": "tooltip-wrapper",
                        "placement": "left",
                        "content": "${grade1}",
                        "visibleOn": "this.grade1Status === 'success' && grade1",
                        "body": {
                          "type": "tag",
                          "displayMode": "bordered",
                          "label": "${grade1}",
                          "color": "${grade1Status}"
                        }
                      },
                      {
                        "type": "tooltip-wrapper",
                        "placement": "left",
                        "content": "${grade1}",
                        "visibleOn": "this.grade1Status === 'error' && grade1",
                        "body": {
                          "type": "tag",
                          "displayMode": "bordered",
                          "label": "${grade1}",
                          "style": {
                            "textDecoration": "line-through"
                          },
                          "color": "${grade1Status}"
                        }
                      }
                    ],
                    [
                      {
                        "type": "tooltip-wrapper",
                        "placement": "left",
                        "content": "${grade2}",
                        "visibleOn": "this.grade2Status === 'inactive' && grade2",
                        "body": {
                          "type": "tag",
                          "displayMode": "bordered",
                          "label": "${grade2}",
                          "color": "${grade2Status}"
                        }
                      },
                      {
                        "type": "tooltip-wrapper",
                        "placement": "left",
                        "content": "${grade2}",
                        "visibleOn": "this.grade2Status === 'success' && grade2",
                        "body": {
                          "type": "tag",
                          "displayMode": "bordered",
                          "label": "${grade2}",
                          "color": "${grade2Status}"
                        }
                      },
                      {
                        "type": "tooltip-wrapper",
                        "placement": "left",
                        "content": "${grade2}",
                        "visibleOn": "this.grade2Status === 'error' && grade2",
                        "body": {
                          "type": "tag",
                          "displayMode": "bordered",
                          "label": "${grade2}",
                          "style": {
                            "textDecoration": "line-through"
                          },
                          "color": "${grade2Status}"
                        }
                      }
                    ],
                    [
                      {
                        "type": "tooltip-wrapper",
                        "placement": "left",
                        "content": "${grade3}",
                        "visibleOn": "this.grade3Status === 'inactive' && grade3",
                        "body": {
                          "type": "tag",
                          "displayMode": "bordered",
                          "label": "${grade3}",
                          "color": "${grade3Status}"
                        }
                      },
                      {
                        "type": "tooltip-wrapper",
                        "placement": "left",
                        "content": "${grade3}",
                        "visibleOn": "this.grade3Status === 'success' && grade3",
                        "body": {
                          "type": "tag",
                          "displayMode": "bordered",
                          "label": "${grade3}",
                          "color": "${grade3Status}"
                        }
                      },
                      {
                        "type": "tooltip-wrapper",
                        "placement": "left",
                        "content": "${grade3}",
                        "visibleOn": "this.grade3Status === 'error' && grade3",
                        "body": {
                          "type": "tag",
                          "displayMode": "bordered",
                          "label": "${grade3}",
                          "style": {
                            "textDecoration": "line-through"
                          },
                          "color": "${grade3Status}"
                        }
                      }
                    ]
                  ]
                }
              ],
              "source": "${queryList}"
            },
            {
              "type": "crud",
              "syncLocation": false,
              "columnsTogglable": false,
              "autoGenerateFilter": {
                "showBtnToolbar": false,
                "defaultExpanded": false
              },
              "footerToolbar": [],
              "title": "Cookie(缓存参数)",
              "columns": [
                {
                  "name": "key-version1",
                  "label": "参数key",
                  "type": "group",
                  "direction": "vertical",
                  "body": [
                    {
                      "type": "tpl",
                      "visibleOn": "${!keyStatus}",
                      "tpl": "${key-version2}"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${keyStatus === 'ADD'}",
                      "tpl": "<span class='pm-versionDiff-add'>${key-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${keyStatus === 'DELETE'}",
                      "tpl": "<span class='pm-versionDiff-delete'>${key-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${keyStatus === 'EDIT'}",
                      "tpl": "<span class='pm-diff-edit-preValue-text-color'>${key-version1}</span>&nbsp;&#8594;&nbsp;"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${keyStatus === 'EDIT'}",
                      "tpl": "<span class='pm-versionDiff-edit'>${key-version2}</span>"
                    }
                  ]
                },
                {
                  "name": "id-version1",
                  "label": "参数名称",
                  "type": "group",
                  "direction": "vertical",
                  "body": [
                    {
                      "type": "tpl",
                      "visibleOn": "${!idStatus}",
                      "tpl": "${id-version2}"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${idStatus === 'ADD'}",
                      "tpl": "<span class='pm-versionDiff-add'>${id-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${idStatus === 'DELETE'}",
                      "tpl": "<span class='pm-versionDiff-delete'>${id-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${idStatus === 'EDIT'}",
                      "tpl": "<span class='pm-diff-edit-preValue-text-color'>${id-version1}</span>&nbsp;&#8594;&nbsp;"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${idStatus === 'EDIT'}",
                      "tpl": "<span class='pm-versionDiff-edit'>${id-version2}</span>"
                    }
                  ]
                },
                {
                  "name": "text1-version2",
                  "label": "数据类型",
                  "type": "group",
                  "direction": "vertical",
                  "body": [
                    {
                      "type": "tpl",
                      "visibleOn": "${!text1Status}",
                      "tpl": "${text1-version2}"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text1Status === 'ADD'}",
                      "tpl": "<span class='pm-versionDiff-add'>${text1-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text1Status === 'DELETE'}",
                      "tpl": "<span class='pm-versionDiff-delete'>${text1-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text1Status === 'EDIT'}",
                      "tpl": "<span class='pm-diff-edit-preValue-text-color'>${text1-version1}</span>&nbsp;&#8594;&nbsp;"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text1Status === 'EDIT'}",
                      "tpl": "<span class='pm-versionDiff-edit'>${text1-version2}</span>"
                    }
                  ]
                },
                {
                  "name": "text4-version1",
                  "label": "是否必填",
                  "type": "group",
                  "direction": "vertical",
                  "body": [
                    {
                      "type": "tpl",
                      "visibleOn": "${!text4Status}",
                      "tpl": "${text4-version2}"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text4Status === 'ADD'}",
                      "tpl": "<span class='pm-versionDiff-add'>${text4-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text4Status === 'DELETE'}",
                      "tpl": "<span class='pm-versionDiff-delete'>${text4-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text4Status === 'EDIT'}",
                      "tpl": "<span class='pm-diff-edit-preValue-text-color'>${text4-version1}</span>&nbsp;&#8594;&nbsp;"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text4Status === 'EDIT'}",
                      "tpl": "<span class='pm-versionDiff-edit'>${text4-version2}</span>"
                    }
                  ]
                },
                {
                  "name": "text5-version1",
                  "label": "说明",
                  "type": "group",
                  "direction": "vertical",
                  "body": [
                    {
                      "type": "tpl",
                      "visibleOn": "${!text5Status}",
                      "tpl": "${text5-version2}"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text5Status === 'ADD'}",
                      "tpl": "<span class='pm-versionDiff-add'>${text5-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text5Status === 'DELETE'}",
                      "tpl": "<span class='pm-versionDiff-delete'>${text5-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text5Status === 'EDIT'}",
                      "tpl": "<span class='pm-diff-edit-preValue-text-color'>${text5-version1}</span>&nbsp;&#8594;&nbsp;"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text5Status === 'EDIT'}",
                      "tpl": "<span class='pm-versionDiff-edit'>${text5-version2}</span>"
                    }
                  ]
                }
              ],
              "source": "${queryList}"
            }
          ]
        },
        {
          "header": {
            "title": "响应参数"
          },
          "body": [
            {
              "type": "crud",
              "id": "crudId",
              "showExpansionColumn": false,
              "syncLocation": false,
              "columnsTogglable": false,
              "autoGenerateFilter": {
                "showBtnToolbar": false,
                "defaultExpanded": false
              },
              "footerToolbar": [
                {
                  "type": "pagination",
                  "maxButtons": 5,
                  "layout": "total,pager,perPage"
                }
              ],
              "title": "Header(请求头参数)",
              "columns": [
                {
                  "name": "key-version1",
                  "label": "参数key",
                  "type": "group",
                  "direction": "vertical",
                  "body": [
                    {
                      "type": "tpl",
                      "visibleOn": "${!keyStatus}",
                      "tpl": "${key-version2}"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${keyStatus === 'ADD'}",
                      "tpl": "<span class='pm-versionDiff-add'>${key-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${keyStatus === 'DELETE'}",
                      "tpl": "<span class='pm-versionDiff-delete'>${key-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${keyStatus === 'EDIT'}",
                      "tpl": "<span class='pm-diff-edit-preValue-text-color'>${key-version1}</span>&nbsp;&#8594;&nbsp;"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${keyStatus === 'EDIT'}",
                      "tpl": "<span class='pm-versionDiff-edit'>${key-version2}</span>"
                    }
                  ]
                },
                {
                  "name": "id-version1",
                  "label": "参数名称",
                  "type": "group",
                  "direction": "vertical",
                  "body": [
                    {
                      "type": "tpl",
                      "visibleOn": "${!idStatus}",
                      "tpl": "${id-version2}"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${idStatus === 'ADD'}",
                      "tpl": "<span class='pm-versionDiff-add'>${id-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${idStatus === 'DELETE'}",
                      "tpl": "<span class='pm-versionDiff-delete'>${id-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${idStatus === 'EDIT'}",
                      "tpl": "<span class='pm-diff-edit-preValue-text-color'>${id-version1}</span>&nbsp;&#8594;&nbsp;"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${idStatus === 'EDIT'}",
                      "tpl": "<span class='pm-versionDiff-edit'>${id-version2}</span>"
                    }
                  ]
                },
                {
                  "name": "text1-version2",
                  "label": "数据类型",
                  "type": "group",
                  "direction": "vertical",
                  "body": [
                    {
                      "type": "tpl",
                      "visibleOn": "${!text1Status}",
                      "tpl": "${text1-version2}"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text1Status === 'ADD'}",
                      "tpl": "<span class='pm-versionDiff-add'>${text1-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text1Status === 'DELETE'}",
                      "tpl": "<span class='pm-versionDiff-delete'>${text1-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text1Status === 'EDIT'}",
                      "tpl": "<span class='pm-diff-edit-preValue-text-color'>${text1-version1}</span>&nbsp;&#8594;&nbsp;"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text1Status === 'EDIT'}",
                      "tpl": "<span class='pm-versionDiff-edit'>${text1-version2}</span>"
                    }
                  ]
                },
                {
                  "name": "text4-version1",
                  "label": "是否必填",
                  "type": "group",
                  "direction": "vertical",
                  "body": [
                    {
                      "type": "tpl",
                      "visibleOn": "${!text4Status}",
                      "tpl": "${text4-version2}"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text4Status === 'ADD'}",
                      "tpl": "<span class='pm-versionDiff-add'>${text4-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text4Status === 'DELETE'}",
                      "tpl": "<span class='pm-versionDiff-delete'>${text4-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text4Status === 'EDIT'}",
                      "tpl": "<span class='pm-diff-edit-preValue-text-color'>${text4-version1}</span>&nbsp;&#8594;&nbsp;"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text4Status === 'EDIT'}",
                      "tpl": "<span class='pm-versionDiff-edit'>${text4-version2}</span>"
                    }
                  ]
                },
                {
                  "name": "text5-version1",
                  "label": "说明",
                  "type": "group",
                  "direction": "vertical",
                  "body": [
                    {
                      "type": "tpl",
                      "visibleOn": "${!text5Status}",
                      "tpl": "${text5-version2}"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text5Status === 'ADD'}",
                      "tpl": "<span class='pm-versionDiff-add'>${text5-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text5Status === 'DELETE'}",
                      "tpl": "<span class='pm-versionDiff-delete'>${text5-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text5Status === 'EDIT'}",
                      "tpl": "<span class='pm-diff-edit-preValue-text-color'>${text5-version1}</span>&nbsp;&#8594;&nbsp;"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text5Status === 'EDIT'}",
                      "tpl": "<span class='pm-versionDiff-edit'>${text5-version2}</span>"
                    }
                  ]
                },
                {
                  "type": "operation",
                  "label": "操作",
                  "width": 80,
                  "buttons": [
                    {
                      "type": "button",
                      "label": "${_amisExpanded ? '收起' : '展开'}",
                      "level": "link",
                      "onEvent": {
                        "click": {
                          "actions": [
                            {
                              "actionType": "toggleExpanded",
                              "componentId": "crudId",
                              "args": {
                                "condition": "${id === currentId}",
                                "currentId": "${id}"
                              }
                            }
                          ]
                        }
                      }
                    }
                  ]
                }
              ],
              "source": "${queryList}",
              "subTable": {
                "type": "crud",
                "footerToolbar": [
                  {
                    "type": "pagination",
                    "maxButtons": 5,
                    "layout": "total,pager,perPage"
                  }
                ],
                "source": "${queryList}",
                "columns": [
                  {
                    "name": "key-version1",
                    "label": "参数key",
                    "type": "group",
                    "direction": "vertical",
                    "body": [
                      {
                        "type": "tpl",
                        "visibleOn": "${!keyStatus}",
                        "tpl": "${key-version2}"
                      },
                      {
                        "type": "tpl",
                        "visibleOn": "${keyStatus === 'ADD'}",
                        "tpl": "<span class='pm-versionDiff-add'>${key-version2}</span>"
                      },
                      {
                        "type": "tpl",
                        "visibleOn": "${keyStatus === 'DELETE'}",
                        "tpl": "<span class='pm-versionDiff-delete'>${key-version2}</span>"
                      },
                      {
                        "type": "tpl",
                        "visibleOn": "${keyStatus === 'EDIT'}",
                        "tpl": "<span class='pm-diff-edit-preValue-text-color'>${key-version1}</span>&nbsp;&#8594;&nbsp;"
                      },
                      {
                        "type": "tpl",
                        "visibleOn": "${keyStatus === 'EDIT'}",
                        "tpl": "<span class='pm-versionDiff-edit'>${key-version2}</span>"
                      }
                    ]
                  },
                  {
                    "name": "id-version1",
                    "label": "参数名称",
                    "type": "group",
                    "direction": "vertical",
                    "body": [
                      {
                        "type": "tpl",
                        "visibleOn": "${!idStatus}",
                        "tpl": "${id-version2}"
                      },
                      {
                        "type": "tpl",
                        "visibleOn": "${idStatus === 'ADD'}",
                        "tpl": "<span class='pm-versionDiff-add'>${id-version2}</span>"
                      },
                      {
                        "type": "tpl",
                        "visibleOn": "${idStatus === 'DELETE'}",
                        "tpl": "<span class='pm-versionDiff-delete'>${id-version2}</span>"
                      },
                      {
                        "type": "tpl",
                        "visibleOn": "${idStatus === 'EDIT'}",
                        "tpl": "<span class='pm-diff-edit-preValue-text-color'>${id-version1}</span>&nbsp;&#8594;&nbsp;"
                      },
                      {
                        "type": "tpl",
                        "visibleOn": "${idStatus === 'EDIT'}",
                        "tpl": "<span class='pm-versionDiff-edit'>${id-version2}</span>"
                      }
                    ]
                  },
                  {
                    "name": "text1-version2",
                    "label": "数据类型",
                    "type": "group",
                    "direction": "vertical",
                    "body": [
                      {
                        "type": "tpl",
                        "visibleOn": "${!text1Status}",
                        "tpl": "${text1-version2}"
                      },
                      {
                        "type": "tpl",
                        "visibleOn": "${text1Status === 'ADD'}",
                        "tpl": "<span class='pm-versionDiff-add'>${text1-version2}</span>"
                      },
                      {
                        "type": "tpl",
                        "visibleOn": "${text1Status === 'DELETE'}",
                        "tpl": "<span class='pm-versionDiff-delete'>${text1-version2}</span>"
                      },
                      {
                        "type": "tpl",
                        "visibleOn": "${text1Status === 'EDIT'}",
                        "tpl": "<span class='pm-diff-edit-preValue-text-color'>${text1-version1}</span>&nbsp;&#8594;&nbsp;"
                      },
                      {
                        "type": "tpl",
                        "visibleOn": "${text1Status === 'EDIT'}",
                        "tpl": "<span class='pm-versionDiff-edit'>${text1-version2}</span>"
                      }
                    ]
                  },
                  {
                    "name": "text4-version1",
                    "label": "是否必填",
                    "type": "group",
                    "direction": "vertical",
                    "body": [
                      {
                        "type": "tpl",
                        "visibleOn": "${!text4Status}",
                        "tpl": "${text4-version2}"
                      },
                      {
                        "type": "tpl",
                        "visibleOn": "${text4Status === 'ADD'}",
                        "tpl": "<span class='pm-versionDiff-add'>${text4-version2}</span>"
                      },
                      {
                        "type": "tpl",
                        "visibleOn": "${text4Status === 'DELETE'}",
                        "tpl": "<span class='pm-versionDiff-delete'>${text4-version2}</span>"
                      },
                      {
                        "type": "tpl",
                        "visibleOn": "${text4Status === 'EDIT'}",
                        "tpl": "<span class='pm-diff-edit-preValue-text-color'>${text4-version1}</span>&nbsp;&#8594;&nbsp;"
                      },
                      {
                        "type": "tpl",
                        "visibleOn": "${text4Status === 'EDIT'}",
                        "tpl": "<span class='pm-versionDiff-edit'>${text4-version2}</span>"
                      }
                    ]
                  },
                  {
                    "name": "text5-version1",
                    "label": "说明",
                    "type": "group",
                    "direction": "vertical",
                    "body": [
                      {
                        "type": "tpl",
                        "visibleOn": "${!text5Status}",
                        "tpl": "${text5-version2}"
                      },
                      {
                        "type": "tpl",
                        "visibleOn": "${text5Status === 'ADD'}",
                        "tpl": "<span class='pm-versionDiff-add'>${text5-version2}</span>"
                      },
                      {
                        "type": "tpl",
                        "visibleOn": "${text5Status === 'DELETE'}",
                        "tpl": "<span class='pm-versionDiff-delete'>${text5-version2}</span>"
                      },
                      {
                        "type": "tpl",
                        "visibleOn": "${text5Status === 'EDIT'}",
                        "tpl": "<span class='pm-diff-edit-preValue-text-color'>${text5-version1}</span>&nbsp;&#8594;&nbsp;"
                      },
                      {
                        "type": "tpl",
                        "visibleOn": "${text5Status === 'EDIT'}",
                        "tpl": "<span class='pm-versionDiff-edit'>${text5-version2}</span>"
                      }
                    ]
                  }
                ]
              }
            },
            {
              "type": "crud",
              "syncLocation": false,
              "columnsTogglable": false,
              "autoGenerateFilter": {
                "showBtnToolbar": false,
                "defaultExpanded": false
              },
              "footerToolbar": [],
              "title": "Cookie(缓存参数)",
              "columns": [
                {
                  "name": "key-version1",
                  "label": "参数key",
                  "type": "group",
                  "direction": "vertical",
                  "body": [
                    {
                      "type": "tpl",
                      "visibleOn": "${!keyStatus}",
                      "tpl": "${key-version2}"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${keyStatus === 'ADD'}",
                      "tpl": "<span class='pm-versionDiff-add'>${key-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${keyStatus === 'DELETE'}",
                      "tpl": "<span class='pm-versionDiff-delete'>${key-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${keyStatus === 'EDIT'}",
                      "tpl": "<span class='pm-diff-edit-preValue-text-color'>${key-version1}</span>&nbsp;&#8594;&nbsp;"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${keyStatus === 'EDIT'}",
                      "tpl": "<span class='pm-versionDiff-edit'>${key-version2}</span>"
                    }
                  ]
                },
                {
                  "name": "id-version1",
                  "label": "参数名称",
                  "type": "group",
                  "direction": "vertical",
                  "body": [
                    {
                      "type": "tpl",
                      "visibleOn": "${!idStatus}",
                      "tpl": "${id-version2}"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${idStatus === 'ADD'}",
                      "tpl": "<span class='pm-versionDiff-add'>${id-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${idStatus === 'DELETE'}",
                      "tpl": "<span class='pm-versionDiff-delete'>${id-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${idStatus === 'EDIT'}",
                      "tpl": "<span class='pm-diff-edit-preValue-text-color'>${id-version1}</span>&nbsp;&#8594;&nbsp;"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${idStatus === 'EDIT'}",
                      "tpl": "<span class='pm-versionDiff-edit'>${id-version2}</span>"
                    }
                  ]
                },
                {
                  "name": "text1-version2",
                  "label": "数据类型",
                  "type": "group",
                  "direction": "vertical",
                  "body": [
                    {
                      "type": "tpl",
                      "visibleOn": "${!text1Status}",
                      "tpl": "${text1-version2}"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text1Status === 'ADD'}",
                      "tpl": "<span class='pm-versionDiff-add'>${text1-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text1Status === 'DELETE'}",
                      "tpl": "<span class='pm-versionDiff-delete'>${text1-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text1Status === 'EDIT'}",
                      "tpl": "<span class='pm-diff-edit-preValue-text-color'>${text1-version1}</span>&nbsp;&#8594;&nbsp;"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text1Status === 'EDIT'}",
                      "tpl": "<span class='pm-versionDiff-edit'>${text1-version2}</span>"
                    }
                  ]
                },
                {
                  "name": "text4-version1",
                  "label": "是否必填",
                  "type": "group",
                  "direction": "vertical",
                  "body": [
                    {
                      "type": "tpl",
                      "visibleOn": "${!text4Status}",
                      "tpl": "${text4-version2}"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text4Status === 'ADD'}",
                      "tpl": "<span class='pm-versionDiff-add'>${text4-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text4Status === 'DELETE'}",
                      "tpl": "<span class='pm-versionDiff-delete'>${text4-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text4Status === 'EDIT'}",
                      "tpl": "<span class='pm-diff-edit-preValue-text-color'>${text4-version1}</span>&nbsp;&#8594;&nbsp;"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text4Status === 'EDIT'}",
                      "tpl": "<span class='pm-versionDiff-edit'>${text4-version2}</span>"
                    }
                  ]
                },
                {
                  "name": "text5-version1",
                  "label": "说明",
                  "type": "group",
                  "direction": "vertical",
                  "body": [
                    {
                      "type": "tpl",
                      "visibleOn": "${!text5Status}",
                      "tpl": "${text5-version2}"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text5Status === 'ADD'}",
                      "tpl": "<span class='pm-versionDiff-add'>${text5-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text5Status === 'DELETE'}",
                      "tpl": "<span class='pm-versionDiff-delete'>${text5-version2}</span>"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text5Status === 'EDIT'}",
                      "tpl": "<span class='pm-diff-edit-preValue-text-color'>${text5-version1}</span>&nbsp;&#8594;&nbsp;"
                    },
                    {
                      "type": "tpl",
                      "visibleOn": "${text5Status === 'EDIT'}",
                      "tpl": "<span class='pm-versionDiff-edit'>${text5-version2}</span>"
                    }
                  ]
                }
              ],
              "source": "${queryList}"
            }
          ]
        }
      ]
    }
  }
}
```

- 落地案例  
  [获客平台（主营）-RTA服务-今日头条-RTA策略-RTA策略版本对比](http://moka.dmz.sit.caijj.net/tdrtaui/#/common-RTA/version-diff/TOUTIAO?type=add&strategyId=2721&strategyCode=RTA20230925323&strategyName=头条rta策略测试lwj)  
   ![获客平台（主营）-RTA服务-今日头条-RTA策略-RTA策略版本对比](https://static02.sit.yxmarketing01.com/tdmaterial/2963ed6181394408aca295f778f607a9.png)

#### 版本对比前后值同时分模块展示

基准版本和对比版本同时分左右两侧展示，同时页面元素区分维度分组展示

```schema
{
  "type": "page",
  "data": {
    "text1": "营销中心",
    "status": "ADD",
    "text1Status": "DELETE",
    "text2Status": "EDIT",
    "text2": 2,
    "text3": 1593327764,
    "text4": "负责人等哈时间段哈就是打开就撒打撒肯定是大空间圣诞贺卡撒较大",
    "text5": "123423456576",
    "text55": "12342345090",
    "text6": 1593327764,
    "text40": "12342dsadsadsadsajdhasjkdh345090",
    "text7": "创建人",
    "text8": "text8",
    "text9": "text9",
    "queryList1": [
      {
        "changeType": "",
        "key-version1": "k1",
        "key-version2": "k1",
        "keyStatus": "",
        "id-version1": "userId",
        "id-version2": "userId",
        "idStatus": "",
        "text1Status": "",
        "text4Status": "",
        "text5Status": "",
        "text1-version1": "string",
        "text1-version2": "string",
        "text4-version1": "是",
        "text4-version2": "是",
        "text5-version1": "用户Id",
        "text5-version2": "用户Id",
        "grade": "ABCD",
        "grade1": "ABCD",
        "grade2": "B",
        "grade3": "C",
        "gradeStatus": "inactive",
        "grade1Status": "inactive",
        "grade2Status": "inactive",
        "grade3Status": "inactive"
      },
      {
        "changeType": "增",
        "key-version1": "",
        "key-version2": "k2",
        "keyStatus": "ADD",
        "id-version1": "",
        "id-version2": "userType",
        "idStatus": "ADD",
        "text1Status": "ADD",
        "text4Status": "ADD",
        "text5Status": "ADD",
        "text1-version1": "",
        "text1-version2": "string",
        "text4-version1": "",
        "text4-version2": "是",
        "text5-version1": "",
        "text5-version2": "用户类型"
      },
      {
        "changeType": "改",
        "key-version1": "k3大家啊数控刀具撒肯德基",
        "key-version2": "k3",
        "keyStatus": "EDIT",
        "id-version1": "userId大家家",
        "id-version2": "userI爸爸d",
        "idStatus": "EDIT",
        "text1Status": "EDIT",
        "text4Status": "EDIT",
        "text5Status": "EDIT",
        "text1-version1": "stringstringstringstringstringstringstring",
        "text1-version2": "integer带回家收到撒黄金时代stringstringstringstringstringstring",
        "text4-version1": "",
        "text4-version2": "string",
        "text5-version1": "",
        "text5-version2": "用户Id",
        "grade": "这里有更多撒金凤卡少女反馈加啊是南非进口三反馈加三分快三方面小美女你发怒发怒发怒",
        "grade1": "1232178346816421641326426147126473214143214",
        "gradeStatus": "inactive",
        "grade1Status": "inactive"
      },
      {
        "changeType": "删",
        "key-version1": "k4",
        "key-version2": "k4",
        "keyStatus": "DELETE",
        "id-version1": "userId",
        "id-version2": "userId",
        "idStatus": "DELETE",
        "text1Status": "DELETE",
        "text4Status": "DELETE",
        "text5Status": "DELETE",
        "text1-version1": "string",
        "text1-version2": "string",
        "text4-version1": "是",
        "text4-version2": "是",
        "text5-version1": "用户Id",
        "text5-version2": "用户Id",
        "grade": "这里有更多撒金凤卡少女反馈加啊是南非进口三反馈加三分快三方面小美女你发怒发怒发怒",
        "grade1": "1232178346816421641326426147126473214143214",
        "grade2": "sadkjhsakdfhskahfkashfdksahfkasdksafkadsfkasf",
        "gradeStatus": "inactive",
        "grade1Status": "inactive",
        "grade2Status": "inactive"
      }
    ],
    "queryList2": [
      {
        "changeType": "",
        "key-version1": "k1",
        "key-version2": "k1",
        "keyStatus": "",
        "id-version1": "userId",
        "id-version2": "userId",
        "idStatus": "",
        "text1Status": "",
        "text4Status": "",
        "text5Status": "",
        "text1-version1": "string",
        "text1-version2": "string",
        "text4-version1": "是",
        "text4-version2": "是",
        "text5-version1": "用户Id",
        "text5-version2": "用户Id",
        "grade": "ABCD",
        "grade1": "ABCD",
        "grade2": "B",
        "grade3": "C",
        "gradeStatus": "inactive",
        "grade1Status": "inactive",
        "grade2Status": "inactive",
        "grade3Status": "inactive"
      },
      {
        "changeType": "增",
        "key-version1": "",
        "key-version2": "k2",
        "keyStatus": "ADD",
        "id-version1": "",
        "id-version2": "userType",
        "idStatus": "ADD",
        "text1Status": "ADD",
        "text4Status": "ADD",
        "text5Status": "ADD",
        "text1-version1": "",
        "text1-version2": "string",
        "text4-version1": "",
        "text4-version2": "是",
        "text5-version1": "",
        "text5-version2": "用户类型",
        "grade": "A这里有更多撒金凤卡少女反馈加啊是南非进口三反馈加三分快三方面小美女你发怒发怒发怒",
        "grade1": "1232178346816421641326426147126473214143214",
        "grade2": "sadkjhsakdfhskahfkashfdksahfkasdksafkadsfkasf",
        "grade3": "sadkjhsakdfhskahfkashfdksahfkasdksafkadsfkasf",
        "gradeStatus": "success",
        "grade1Status": "success",
        "grade2Status": "success",
        "grade3Status": "success"
      },
      {
        "changeType": "改",
        "key-version1": "k3大家啊数控刀具撒肯德基",
        "key-version2": "k3",
        "keyStatus": "EDIT",
        "id-version1": "userId大家家",
        "id-version2": "userI爸爸d",
        "idStatus": "EDIT",
        "text1Status": "EDIT",
        "text4Status": "EDIT",
        "text5Status": "EDIT",
        "text1-version1": "stringstringstringstringstringstringstring",
        "text1-version2": "integer带回家收到撒黄金时代stringstringstringstringstringstring",
        "text4-version1": "",
        "text4-version2": "是",
        "text5-version1": "",
        "text5-version2": "用户Id",
        "grade": "这里有更多撒金凤卡少女反馈加啊是南非进口三反馈加三分快三方面小美女你发怒发怒发怒",
        "grade1": "1232178346816421641326426147126473214143214",
        "grade2": "dsfdsafdsafjwofewjcxnvfew",
        "grade3": "dsafsdafsafsda",
        "gradeStatus": "error",
        "grade1Status": "error",
        "grade2Status": "success",
        "grade3Status": "success"
      },
      {
        "changeType": "删",
        "key-version1": "k4",
        "key-version2": "k4",
        "keyStatus": "DELETE",
        "id-version1": "userId",
        "id-version2": "userId",
        "idStatus": "DELETE",
        "text1Status": "DELETE",
        "text4Status": "DELETE",
        "text5Status": "DELETE",
        "text1-version1": "string",
        "text1-version2": "string",
        "text4-version1": "是",
        "text4-version2": "是",
        "text5-version1": "用户Id",
        "text5-version2": "用户Id",
        "grade": "这里有更多撒金凤卡少女反馈加啊是南非进口三反馈加三分快三方面小美女你发怒发怒发怒",
        "grade1": "1232178346816421641326426147126473214143214",
        "grade2": "sadkjhsakdfhskahfkashfdksahfkasdksafkadsfkasf",
        "gradeStatus": "error",
        "grade1Status": "error",
        "grade2Status": "error"
      }
    ]
  },
  "body": {
    "type": "wrapper",
    "bgColor": "white",
    "body": {
      "type": "form",
      "title": "",
      "actions": [],
      "static": true,
      "body": {
        "type": "flex",
        "gap": true,
        "direction": "column",
        "items": [
          {
            "type": "flex",
            "gap": true,
            "direction": "column",
            "items": [
              {
                "type": "tags",
                "items": [
                  {
                    "type": "tag",
                    "label": "新增",
                    "displayMode": "bordered",
                    "color": "add-status"
                  },
                  {
                    "type": "tag",
                    "label": "修改",
                    "displayMode": "bordered",
                    "color": "edit-status"
                  },
                  {
                    "type": "tag",
                    "label": "删除",
                    "displayMode": "bordered",
                    "color": "delete-status"
                  }
                ]
              },
              {
                "type": "group",
                "body": [
                  {
                    "type": "select",
                    "label": "基准版本",
                    "name": "baselineVersion",
                    "value": "v1",
                    "labelWidth": 60,
                    "options": [
                      {
                        "label": "V1",
                        "value": "v1"
                      },
                      {
                        "label": "V2",
                        "value": "v2"
                      }
                    ]
                  },
                  {
                    "type": "select",
                    "label": "对比版本",
                    "name": "diffVersion",
                    "value": "v2",
                    "labelWidth": 60,
                    "options": [
                      {
                        "label": "V1",
                        "value": "v1"
                      },
                      {
                        "label": "V2",
                        "value": "v2"
                      }
                    ]
                  }
                ]
              }
            ]
          },
          {
            "type": "grid",
            "gap": "sm",
            "columns": [
              {
                "md": 6,
                "body": [
                  {
                    "type": "panel",
                    "body": [
                      {
                        "type": "group-container",
                        "items": [
                          {
                            "header": {
                              "title": "基础信息"
                            },
                            "body": [
                              {
                                "type": "group",
                                "body": [
                                  {
                                    "type": "tooltip-wrapper",
                                    "content": "${text1}",
                                    "columnRatio": 6,
                                    "body": {
                                      "type": "static-tpl",
                                      "name": "text1",
                                      "label": "姓名",
                                      "tpl": "<div title=${text1} class='pm-ellipsis'>${text1}</div>"
                                    }
                                  },
                                  {
                                    "type": "static-tpl",
                                    "name": "text2",
                                    "label": "年龄",
                                    "columnRatio": 6,
                                    "tpl": "<div title=${text2} class='pm-ellipsis'>${text2}</div>"
                                  }
                                ]
                              },
                              {
                                "type": "group",
                                "body": [
                                  {
                                    "type": "tooltip-wrapper",
                                    "content": "${text40}",
                                    "columnRatio": 6,
                                    "body": {
                                      "type": "static-tpl",
                                      "name": "text40",
                                      "label": "邮箱",
                                      "tpl": "<div title=${text40} class='pm-ellipsis'>${text40}</div>"
                                    }
                                  },
                                  {
                                    "type": "static-tpl",
                                    "name": "text5",
                                    "label": "电话",
                                    "columnRatio": 6,
                                    "tpl": "<div title=${text5} class='pm-ellipsis'>${text5}</div>"
                                  }
                                ]
                              },
                              {
                                "type": "group",
                                "body": [
                                  {
                                    "type": "static-tpl",
                                    "name": "text7",
                                    "label": "其它",
                                    "columnRatio": 6,
                                    "tpl": "<div title=${text7} class='pm-ellipsis'>${text7}</div>"
                                  }
                                ]
                              }
                            ]
                          },
                          {
                            "header": {
                              "title": "入参"
                            },
                            "body": [
                              {
                                "type": "group",
                                "body": [
                                  {
                                    "type": "static-tpl",
                                    "name": "text1",
                                    "label": "资信token"
                                  },
                                  {
                                    "type": "static-tpl",
                                    "name": "text2",
                                    "label": "年龄"
                                  }
                                ]
                              },
                              {
                                "type": "group",
                                "body": [
                                  {
                                    "type": "crud",
                                    "syncLocation": false,
                                    "columnsTogglable": false,
                                    "autoGenerateFilter": {
                                      "showBtnToolbar": false,
                                      "defaultExpanded": false
                                    },
                                    "footerToolbar": [],
                                    "columns": [
                                      {
                                        "name": "key-version1",
                                        "label": "参数key",
                                        "width": 100,
                                        "type": "group",
                                        "direction": "vertical",
                                        "body": [
                                          {
                                            "type": "typography",
                                            "text": "${key-version1}",
                                            "visibleOn": "${key-version1}",
                                            "ellipsis": {
                                              "rows": 1
                                            }
                                          },
                                          {
                                            "type": "tpl",
                                            "tpl": "-",
                                            "visibleOn": "${!key-version1}",
                                            "className": " text-muted "
                                          }
                                        ]
                                      },
                                      {
                                        "name": "id-version1",
                                        "label": "参数名称",
                                        "width": 100,
                                        "type": "group",
                                        "direction": "vertical",
                                        "body": [
                                          {
                                            "type": "typography",
                                            "text": "${id-version1}",
                                            "visibleOn": "${id-version1}",
                                            "ellipsis": {
                                              "rows": 1
                                            }
                                          },
                                          {
                                            "type": "tpl",
                                            "tpl": "-",
                                            "visibleOn": "${!id-version1}",
                                            "className": " text-muted "
                                          }
                                        ]
                                      },
                                      {
                                        "name": "text1-version1",
                                        "label": "数据类型",
                                        "width": 100,
                                        "type": "group",
                                        "direction": "vertical",
                                        "body": [
                                          {
                                            "type": "group",
                                            "mode": "inline",
                                            "body": [
                                              {
                                                "type": "tpl",
                                                "tpl": "更新前"
                                              },
                                              {
                                                "type": "button",
                                                "level": "link",
                                                "label": "详情",
                                                "actionType": "dialog",
                                                "linkWithoutPadding": true,
                                                "style": {
                                                  "height": "22px"
                                                },
                                                "dialog": {
                                                  "title": "详情",
                                                  "showCloseButton": false,
                                                  "body": "这是个简单的弹框。"
                                                }
                                              }
                                            ]
                                          }
                                        ]
                                      },
                                      {
                                        "name": "text4-version1",
                                        "label": "是否必填",
                                        "width": 100,
                                        "type": "group",
                                        "direction": "vertical",
                                        "body": [
                                          {
                                            "type": "typography",
                                            "text": "${text4-version1}",
                                            "visibleOn": "${text4-version1}",
                                            "ellipsis": {
                                              "rows": 1
                                            }
                                          }
                                        ]
                                      },
                                      {
                                        "name": "text5-version1",
                                        "label": "说明",
                                        "width": 100,
                                        "type": "group",
                                        "direction": "vertical",
                                        "body": [
                                          {
                                            "type": "typography",
                                            "text": "${text5-version1}",
                                            "visibleOn": "${text5-version1}",
                                            "ellipsis": {
                                              "rows": 1
                                            }
                                          }
                                        ]
                                      }
                                    ],
                                    "source": "${queryList1}",
                                    "title": "入模参数"
                                  }
                                ]
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  }
                ]
              },
              {
                "md": 6,
                "body": [
                  {
                    "type": "panel",
                    "body": [
                      {
                        "type": "group-container",
                        "items": [
                          {
                            "header": {
                              "title": "基础信息"
                            },
                            "body": [
                              {
                                "type": "group",
                                "body": [
                                  {
                                    "type": "static-tpl",
                                    "name": "text1",
                                    "label": "姓名",
                                    "columnRatio": 6,
                                    "tpl": "<div title='${text1}' class='pm-ellipsis ${text1Status=== 'ADD' ? 'pm-versionDiff-add' : (text1Status === 'EDIT' ? 'pm-versionDiff-edit' : (text1Status === 'DELETE' ? 'pm-versionDiff-delete' : ''))}'>${text1}</div>"
                                  },
                                  {
                                    "type": "static-tpl",
                                    "name": "text2",
                                    "label": "年龄",
                                    "columnRatio": 6,
                                    "tpl": "<div title='${text2}' class='pm-ellipsis ${text2Status=== 'ADD' ? 'pm-versionDiff-add' : (text2Status === 'EDIT' ? 'pm-versionDiff-edit' : (text2Status === 'DELETE' ? 'pm-versionDiff-delete' : ''))}'>${text2}</div>"
                                  }
                                ]
                              },
                              {
                                "type": "group",
                                "body": [
                                  {
                                    "type": "tooltip-wrapper",
                                    "content": "${text4}",
                                    "columnRatio": 6,
                                    "body": {
                                      "type": "static-tpl",
                                      "name": "text4",
                                      "label": "邮箱",
                                      "tpl": "<div title='${text4}' class='pm-ellipsis ${status=== 'ADD' ? 'pm-versionDiff-add' : (status === 'EDIT' ? 'pm-versionDiff-edit' : (status === 'DELETE' ? 'pm-versionDiff-delete' : ''))}'>${text4}</div>"
                                    }
                                  },
                                  {
                                    "type": "static-tpl",
                                    "name": "text5",
                                    "label": "电话",
                                    "tpl": "<div title='${text5}' class='pm-ellipsis ${text5Status=== 'ADD' ? 'pm-versionDiff-add' : (text5Status === 'EDIT' ? 'pm-versionDiff-edit' : (text5Status === 'DELETE' ? 'pm-versionDiff-delete' : ''))}'>${text5}</div>",
                                    "columnRatio": 6
                                  }
                                ]
                              },
                              {
                                "type": "group",
                                "body": [
                                  {
                                    "type": "static-tpl",
                                    "name": "text7",
                                    "label": "其它",
                                    "columnRatio": 6,
                                    "tpl": "<div title='${text7}' class='pm-ellipsis ${text7Status=== 'ADD' ? 'pm-versionDiff-add' : (text7Status === 'EDIT' ? 'pm-versionDiff-edit' : (text7Status === 'DELETE' ? 'pm-versionDiff-delete' : ''))}'>${text7}</div>"
                                  }
                                ]
                              }
                            ]
                          },
                          {
                            "header": {
                              "title": "入参"
                            },
                            "body": [
                              {
                                "type": "group",
                                "body": [
                                  {
                                    "type": "static-tpl",
                                    "name": "text1",
                                    "label": "资信token"
                                  },
                                  {
                                    "type": "static-tpl",
                                    "name": "text2",
                                    "label": "年龄"
                                  }
                                ]
                              },
                              {
                                "type": "group",
                                "body": [
                                  {
                                    "type": "crud",
                                    "syncLocation": false,
                                    "columnsTogglable": false,
                                    "autoGenerateFilter": {
                                      "showBtnToolbar": false,
                                      "defaultExpanded": false
                                    },
                                    "footerToolbar": [],
                                    "columns": [
                                      {
                                        "name": "key-version1",
                                        "label": "参数key",
                                        "type": "group",
                                        "direction": "vertical",
                                        "body": [
                                          {
                                            "type": "typography",
                                            "visibleOn": "${!keyStatus}",
                                            "text": "${key-version2}",
                                            "ellipsis": {
                                              "rows": 1
                                            }
                                          },
                                          {
                                            "type": "typography",
                                            "visibleOn": "${keyStatus === 'ADD'}",
                                            "text": "<span class='pm-versionDiff-add'>${key-version2}</span>",
                                            "ellipsis": {
                                              "rows": 1
                                            }
                                          },
                                          {
                                            "type": "typography",
                                            "visibleOn": "${keyStatus === 'DELETE'}",
                                            "text": "<span class='pm-versionDiff-delete'>${key-version2}</span>",
                                            "ellipsis": {
                                              "rows": 1
                                            }
                                          },
                                          {
                                            "type": "typography",
                                            "visibleOn": "${keyStatus === 'EDIT'}",
                                            "text": "<span title='${key-version2}'><span class='pm-versionDiff-edit'>${key-version2}</span></span>",
                                            "ellipsis": {
                                              "rows": 1
                                            }
                                          }
                                        ],
                                        "width": 100
                                      },
                                      {
                                        "name": "id-version1",
                                        "label": "参数名称",
                                        "type": "group",
                                        "direction": "vertical",
                                        "body": [
                                          {
                                            "type": "typography",
                                            "visibleOn": "${!idStatus}",
                                            "text": "${id-version2}",
                                            "ellipsis": {
                                              "rows": 1
                                            }
                                          },
                                          {
                                            "type": "typography",
                                            "visibleOn": "${idStatus === 'ADD'}",
                                            "text": "<span class='pm-versionDiff-add'>${id-version2}</span>",
                                            "ellipsis": {
                                              "rows": 1
                                            }
                                          },
                                          {
                                            "type": "typography",
                                            "visibleOn": "${idStatus === 'DELETE'}",
                                            "text": "<span class='pm-versionDiff-delete'>${id-version2}</span>",
                                            "ellipsis": {
                                              "rows": 1
                                            }
                                          },
                                          {
                                            "type": "typography",
                                            "visibleOn": "${idStatus === 'EDIT'}",
                                            "text": "<span title='${id-version2}'><span class='pm-versionDiff-edit'>${id-version2}</span></span>",
                                            "ellipsis": {
                                              "rows": 1
                                            }
                                          }
                                        ],
                                        "width": 100
                                      },
                                      {
                                        "name": "text1-version2",
                                        "label": "数据类型",
                                        "type": "group",
                                        "direction": "vertical",
                                        "body": [
                                          {
                                            "type": "group",
                                            "mode": "inline",
                                            "width": 100,
                                            "body": [
                                              {
                                                "type": "tpl",
                                                "tpl": "<div title='${text1-version2}' class='pm-ellipsis ${text1Status=== 'ADD' ? 'pm-versionDiff-add' : (text1Status === 'EDIT' ? 'pm-versionDiff-edit' : (text1Status === 'DELETE' ? 'pm-versionDiff-delete' : ''))}'>更新后</div>"
                                              },
                                              {
                                                "type": "button",
                                                "level": "link",
                                                "label": "详情",
                                                "actionType": "dialog",
                                                "linkWithoutPadding": true,
                                                "style": {
                                                  "height": "22px"
                                                },
                                                "dialog": {
                                                  "title": "详情",
                                                  "showCloseButton": false,
                                                  "body": "这是个简单的弹框。"
                                                }
                                              }
                                            ]
                                          }
                                        ],
                                        "width": 100
                                      },
                                      {
                                        "name": "text4-version1",
                                        "label": "是否必填",
                                        "type": "group",
                                        "direction": "vertical",
                                        "body": [
                                          {
                                            "type": "typography",
                                            "visibleOn": "${!text4Status}",
                                            "text": "${text4-version2}",
                                            "ellipsis": {
                                              "rows": 1
                                            }
                                          },
                                          {
                                            "type": "typography",
                                            "visibleOn": "${text4Status === 'ADD'}",
                                            "text": "<span class='pm-versionDiff-add'>${text4-version2}</span>",
                                            "ellipsis": {
                                              "rows": 1
                                            }
                                          },
                                          {
                                            "type": "typography",
                                            "visibleOn": "${text4Status === 'DELETE'}",
                                            "text": "<span class='pm-versionDiff-delete'>${text4-version2}</span>",
                                            "ellipsis": {
                                              "rows": 1
                                            }
                                          },
                                          {
                                            "type": "typography",
                                            "visibleOn": "${text4Status === 'EDIT'}",
                                            "text": "<span title='${text4-version2}'><span class='pm-versionDiff-edit'>${text4-version2}</span></span>",
                                            "ellipsis": {
                                              "rows": 1
                                            }
                                          }
                                        ],
                                        "width": 100
                                      },
                                      {
                                        "name": "text5-version1",
                                        "label": "说明",
                                        "type": "group",
                                        "direction": "vertical",
                                        "body": [
                                          {
                                            "type": "typography",
                                            "visibleOn": "${!text5Status}",
                                            "text": "${text5-version2}",
                                            "ellipsis": {
                                              "rows": 1
                                            }
                                          },
                                          {
                                            "type": "typography",
                                            "visibleOn": "${text5Status === 'ADD'}",
                                            "text": "<span class='pm-versionDiff-add'>${text5-version2}</span>",
                                            "ellipsis": {
                                              "rows": 1
                                            }
                                          },
                                          {
                                            "type": "typography",
                                            "visibleOn": "${text5Status === 'DELETE'}",
                                            "text": "<span class='pm-versionDiff-delete'>${text5-version2}</span>",
                                            "ellipsis": {
                                              "rows": 1
                                            }
                                          },
                                          {
                                            "type": "typography",
                                            "visibleOn": "${text5Status === 'EDIT'}",
                                            "text": "<span title='${text5-version2}'><span class='pm-versionDiff-edit'>${text5-version2}</span></span>",
                                            "ellipsis": {
                                              "rows": 1
                                            }
                                          }
                                        ],
                                        "width": 100
                                      }
                                    ],
                                    "source": "${queryList2}",
                                    "title": "入模参数"
                                  }
                                ]
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    }
  }
}
```


#### 版本对比-带查询区域

```schema
{
  "type": "page",
  "data": {
    "versionList": [
      {
        "label": "V1",
        "value": 1
      },
      {
        "label": "V2",
        "value": 2
      }
    ],
    "tableData": [
      {
        "fieldName": "API ID",
        "baselineVersion": "12345",
        "diffVersion": "12345"
      },
      {
        "fieldName": "API名称",
        "baselineVersion": "查询用户列表",
        "diffVersion": "查询用户列表"
      },
      {
        "fieldName": "API状态",
        "baselineVersion": "在线",
        "diffVersion": "在线"
      },
      {
        "fieldName": "Method",
        "baselineVersion": "GET",
        "diffVersion": "POST",
        "status": "EDIT"
      },
      {
        "fieldName": "URL",
        "baselineVersion": "/users",
        "diffVersion": "/user-list",
        "status": "EDIT"
      },
      {
        "fieldName": "描述",
        "baselineVersion": "",
        "diffVersion": "查询用户列表",
        "status": "ADD"
      },
      {
        "fieldName": "补充",
        "baselineVersion": "查询用户列表",
        "diffVersion": "查询用户列表",
        "status": "DELETE"
      }
    ],
    "basicInfo": [
      {
        "changeType": "",
        "key-version1": "API id",
        "key-version2": "12345",
        "id-version1": "v1",
        "id-version2": "查询用户列表",
        "idStatus": "",
        "text1Status": "",
        "text4Status": "EDIT",
        "text5Status": "DELETE",
        "text6Status": "ADD",
        "text1-version1": "",
        "text1-version2": "v2",
 
      },
      {
        "changeType": "",
        "key-version1": "API version",
        "key-version2": "12345ee",
        "keyStatus": "eee",
        "id-version1": "v1",
        "id-version2": "查询用户列表",
        "idStatus": "",
        "text1Status": "",
        "text4Status": "EDIT",
        "text5Status": "DELETE",
        "text6Status": "ADD",
        "text1-version1": "在线",
        "text1-version2": "v2",
 
      }
    ],
  },
  "body": [
    {
      "type": "form",
      "queryForm": true,
      "target": "targetFormId",
      "id": "sourceFormId",
      "labelWidth": 65,
      "data":{
        "isShow": true
      },
      "body": [
        {
          "type": "group",
          "body": [
            {
              "type": "input-text",
              "name": "keywords",
              "label": "基准版本",
              "clearable": true,
              "columnRatio": 4,
              "options": "${versionList}"
            },
            {
              "type": "select",
              "name": "engine",
              "label": "对比版本",
              "clearable": true,
              "columnRatio": 4,
              "options": "${versionList}"
            },
            {
              "type": "checkbox",
              "name": "isShow",
              "label": "仅展示变更",
              "columnRatio": 4
            }
          ]
        },
      ],
    },
    {
      "type": "form",
      "id": "targetFormId",
      "actions": [],
      "initApi": {
        "method": "get",
        "url": "/api/mock2/form/initForm",
        "data": {
          "test1": "${keywords}",
          "test2": "${engine}",
        },
      },
      "body": {
        "type": "group-container",
        "collapsible": false,
        "items": [
          {
            "header": {
              "title": "基本信息",
            },
            "body": [
              {
                "type": "crud",
                "syncLocation": false,
                "columnsTogglable": false,
                "autoGenerateFilter": {
                  "showBtnToolbar": false,
                  "defaultExpanded": false
                },
                "footerToolbar": [],
                "source": "$basicInfo",
                "columns": [
                  {
                    "name": "key-version1",
                    "label": "字段名",
                  },
                  {
                    "name": "id-version1",
                    "label": "基准版本",
                  },
                  {
                    "name": "text1-version2",
                    "label": "对比版本",
                  },
                ]
              }
            ]
          },
          {
            "header": {
              "title": "内容比对",
            },
            "body": {
              "type":"flex",
              "gap": true,
              "direction":"column",
              "items":[
                {
                  "type": "tags",
                  "items": [
                    {
                      "type": "tag",
                      "label": "新增",
                      "displayMode": "bordered",
                      "color": "add-status"
                    },
                    {
                      "type": "tag",
                      "label": "修改",
                      "displayMode": "bordered",
                      "color": "edit-status"
                    },
                    {
                      "type": "tag",
                      "label": "删除",
                      "displayMode": "bordered",
                      "color": "delete-status"
                    }
                  ]
                },
                {
                  "type": "crud",
                  "syncLocation": false,
                  "columnsTogglable": false,
                  "autoGenerateFilter": {
                    "showBtnToolbar": false,
                    "defaultExpanded": false
                  },
                  "footerToolbar": [],
                  "columns": [
                    {
                      "name": "fieldName",
                      "label": "字段名"
                    },
                    {
                      "name": "baselineVersion",
                      "label": "基准版本"
                    },
                    {
                      "name": "diffVersion",
                      "label": "对比版本",
                      "type": "tpl",
                      "tpl": "<span class='${status === 'ADD' ? 'pm-versionDiff-add' : (status === 'EDIT' ? 'pm-versionDiff-edit' : (status === 'DELETE' ? 'pm-versionDiff-delete' : ''))}'>${diffVersion}</span>"
                    }
                  ],
                  "source": "${tableData}"
                }
              ]
            }
          }
        ]
      }
    }
  ]
}
```
- 落地案例  
  [大数据一站式-指标平台-指标开发-详情按钮-版本信息tab-版本对比按钮](http://moka.dmz.sit.caijj.net/metricsamisui/#/filter-index?_shMenuId=tenant_menu_P0280_metric_management)  
   ![大数据一站式-指标平台-指标开发-详情按钮-版本信息tab-版本对比按钮](https://static02.sit.yxmarketing01.com/tdmaterial/ffe55e41737643eda997e966b2445204.png)


<!-- ### 分组内多组数据展示

```schema
{
  "type": "page",
  "data": {
    "name": "页面信息",
    "code": "test",
    "items": [
      {
        "engine": "Trident",
        "browser": "Internet Explorer 4.0",
        "platform": "Win 95+",
        "version": "4",
        "grade": "X"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 5.0",
        "platform": "Win 95+",
        "version": "5",
        "grade": "C"
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 5.5",
        "platform": "Win 95+",
        "version": "5.5",
        "grade": "A"
      }
    ]
  },
  "body": {
    "type": "tabs",
    "tabs": [
      {
        "title": "基本信息",
        "body": {
          "type": "form",
          "static": true,
          "labelWidth": 60,
          "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
          "body": {
            "type": "group-container",
            "activeKey": [
              "1"
            ],
            "items": [
              {
                "header": {
                  "title": "页面信息"
                },
                "body": [
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "input-text",
                        "name": "name",
                        "label": "名称"
                      },
                      {
                        "type": "input-text",
                        "name": "code",
                        "label": "编码"
                      }
                    ]
                  },
                  {
                    "type": "group-container",
                    "isSmallGroup": true,
                    "items": [
                      {
                        "header": {
                          "title": "埋点数据"
                        },
                        "body": [
                          {
                            "type": "cards",
                            "source": "$items",
                            "columnsCount": 1,
                            "card": {
                              "body": [
                                {
                                  "label": "Engine",
                                  "name": "engine"
                                },
                                {
                                  "label": "Browser",
                                  "name": "browser"
                                },
                                {
                                  "name": "version",
                                  "label": "Version"
                                }
                              ]
                            }
                          }
                        ]
                      }
                    ]
                  }
                ]
              },
              {
                "header": {
                  "title": "埋点信息"
                },
                "body": [
                  {
                    "type": "cards",
                    "source": "$items",
                    "columnsCount": 3,
                    "card": {
                      "body": [
                        {
                          "label": "Engine",
                          "name": "engine"
                        },
                        {
                          "label": "Browser",
                          "name": "browser"
                        },
                        {
                          "name": "version",
                          "label": "Version"
                        }
                      ]
                    }
                  }
                ]
              }
            ]
          }
        }
      }
    ]
  }
}
``` -->

## 组件用法
### 标题区域

标题区域支持配置，可通过设置对应属性展示对应区域，`title`主标题、`subTitle`副标题、`assistContent`辅助内容、`actions`右侧自定义操作按钮

> **注意：**标题区域如果存在，则必须配置`title`主标题

```schema
{
  "type": "page",
  "body": [
    {
      "type": "form",
      "id": "myForm",
      "api": "/api/mock2/saveForm?waitSeconds=2",
      "body": {
        "type": "group-container",
        "collapsible": false,
        "items": [
          {
            "type": "panel",
            "header": {
              "title": "第一步，基础信息",
              "subTitle": "副标题",
              "assistContent": [
                {
                  "type": "remark",
                  "content": "这是一段提示"
                },
              ],
              "actions": [
                {
                  "type": "button",
                  "label": "实验列表",
                  "level": "link",
                  "linkWithoutPadding": true
                }
              ]
            },
            "body": [
              {
                "type": "group",
                "body": [
                  {
                    "type": "input-text",
                    "name": "text1",
                    "label": "姓名"
                  },
                  {
                    "type": "input-text",
                    "name": "text2",
                    "label": "年龄"
                  },
                  {
                    "type": "input-text",
                    "name": "text3",
                    "label": "班级",
                    "required": true
                  },
                ]
              },
              {
                "type": "group",
                "body": [
                  {
                    "type": "input-text",
                    "name": "text4",
                    "label": "邮箱"
                  },
                  {
                    "type": "input-text",
                    "name": "text5",
                    "label": "电话"
                  },
                  {
                    "type": "input-text",
                    "name": "text6",
                    "label": "地址",
                    "columnRatio": 4
                  }
                ]
              }
            ]
          }
        ]
      },
      "actions": [
        {
          "type": "button",
          "label": "取消"
        },
        {
          "type": "submit",
          "level": "primary",
          "label": "保存"
        }
      ]
    }
  ]
}
```

### 分组可折叠

可配置`collapsible: true`实现可折叠功能，同时配置`activeKey`属性指定展开哪些分组

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/saveForm?waitSeconds=2",
    "body": {
      "type": "group-container",
      "collapsible": true,
      "activeKey": [
        "1"
      ],
      "items": [
        {
          "header": {
            "title": "第一步，基础信息"
          },
          "body": [
            {
              "type": "group",
              "body": [
                {
                  "type": "input-text",
                  "name": "text1",
                  "label": "姓名"
                },
                {
                  "type": "input-text",
                  "name": "text2",
                  "label": "年龄"
                },
                {
                  "type": "input-text",
                  "name": "text3",
                  "label": "班级",
                  "required": true
                }
              ]
            },
            {
              "type": "group",
              "body": [
                {
                  "type": "input-text",
                  "name": "text4",
                  "label": "邮箱"
                },
                {
                  "type": "input-text",
                  "name": "text5",
                  "label": "电话"
                },
                {
                  "type": "input-text",
                  "name": "text6",
                  "label": "地址",
                  "columnRatio": 4
                }
              ]
            },
            {
              "type": "group",
              "body": [
                {
                  "type": "input-text",
                  "name": "text7",
                  "label": "其它",
                  "columnRatio": 4
                }
              ]
            },
          ]
        },
        {
          "header": {
            "title": "第二步，复杂信息"
          },
          "body": [
            {
              "type": "group",
              "body": [
                {
                  "type": "input-text",
                  "name": "second1",
                  "label": "邮箱"
                },
                {
                  "type": "input-text",
                  "name": "second2",
                  "label": "电话"
                },
                {
                  "type": "input-text",
                  "name": "second3",
                  "label": "地址",
                  "columnRatio": 4
                }
              ]
            },
            {
              "type": "group",
              "body": [
                {
                  "type": "textarea",
                  "name": "textarea",
                  "label": "姓名",
                  "placeholder": "请输入"
                }
              ]
            },
            {
              "type": "group",
              "body": [
                {
                  "type": "input-rich-text",
                  "name": "second5",
                  "label": "其它"
                }
              ]
            }
          ]
        }
      ]
    },
    "actions": [
      {
        "type": "button",
        "label": "取消"
      },
      {
        "type": "submit",
        "level": "primary",
        "label": "保存"
      }
    ]
  }
}
```

### 属性表

| 属性名   | 类型                                                                                                                 | 默认值 | 说明                                                                                                                                                                                   | 版本 |
| -------- | -------------------------------------------------------------------------------------------------------------------- | ------ | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ---- |
| collapsible      | boolean                                                              | -      | 是否可折叠                                                                                                      |   |
| isSmallGroup      | boolean                                                              | false     | 是否为小分组                                                                                                      |   |
| activeKey | `Array<string \| number \| never> \| string \| number \| boolean \| `[表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression) | - | 激活面板的 key，需配置 `collapsible: true` 配合使用。<br/>自 1.89.1 起支持设置 `true` 表示全部展开。 |  |
| accordion | `boolean` | `false` | 手风琴模式，需配置`collapsible: true`配合使用  |   |
| expandIcon | `SchemaNode` | - | 自定义切换图标，需配置`collapsible: true`配合使用  |   |
| items | `SchemaNode[]` | - | 内容，具体配置可参考下方`item`属性表 |   |
| autoSwitchWhenValidated | boolean | false | 当表单校验失败时，如果失败项位于可折叠分组内，会自动展开该分组以便用户查看和修改。 |  1.87.3 |


Item 属性表

| 属性名   | 类型                                                                                                                 | 默认值 | 说明                                                                                                                                                                                   |
| -------- | -------------------------------------------------------------------------------------------------------------------- | ------ | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| header      | `Object`                                                              | -      | 标题区域配置，具体配置参考 [Title属性表](/dataseeddesigndocui/#/amis/zh-CN/components/title-container)                                                                                                  |
| body | `SchemaNode` ｜ `SchemaNode[]` | - | 内容 |
