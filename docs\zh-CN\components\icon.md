---
title: Icon 图标
description:
type: 0
group: ⚙ 组件
menuName: Icon
icon:
order: 50
standardMode: true
---

> 在 React 项目中使用 Icon 需要引入 `@fortawesome/fontawesome-free`，然后在代码中 `import '@fortawesome/fontawesome-free/css/all.css'`，还有相关的 webpack 配置，具体请参考 [amis-react-starter](https://github.com/aisuda/amis-react-starter) 里的配置

图标规范：

- 使用 icon 组件时，icon与其他组件的间距为 8px。

## 场景推荐

### 常见图标

1. 图标在实际应用中，在 inputTree 组件和 inputTable、CRUD 等组件中出现的频率高，那结合组件本身的样式，图标一般展示为高亮状态，且鼠标移上去提示图标相关功能。
2. 对于复制图标，常见的是默认色系。

```schema
{
  "type": "page",
  "data": {
    "columnCount": 3,
    "icons": [
      {
        "label": "排序",
        "icon": "exchange"
      },
      {
        "label": "排序置顶",
        "icon": "set-top"
      },
      {
        "label": "排序置底",
        "icon": "set-bottom"
      },
      {
        "label": "上下拖拽",
        "icon": "drag"
      },
      {
        "label": "跨等级移",
        "icon": "exchange-move"
      },
      {
        "label": "详情",
        "icon": "detail"
      },
      {
        "label": "导出",
        "icon": "export"
      },
      {
        "label": "上传",
        "icon": "upload"
      },
      {
        "label": "收藏",
        "icon": "star-regular"
      },
      {
        "label": "已收藏",
        "icon": "star-solid"
      },
      {
        "label": "拖拽把手",
        "icon": "drag"
      },
      {
        "label": "编辑",
        "icon": "pencil"
      },
      {
        "label": "删除",
        "icon": "minus"
      },
      {
        "label": "点赞",
        "icon": "thumbs-up-solid"
      },
      {
        "label": "取消点赞",
        "icon": "thumbs-up"
      },
      {
        "label": "文件夹",
        "icon": "folder"
      },
      {
        "label": "页面",
        "icon": "file-regular"
      },
      {
        "label": "子页面",
        "icon": "file"
      },
      {
        "label": "刷新",
        "icon": "arrow-rotate"
      },
      {
        "label": "复制",
        "icon": "copy"
      },
      {
        "label": "授权",
        "icon": "authorization"
      },
      {
        "label": "图片",
        "icon": "picture"
      },
      {
        "label": "提示",
        "icon": "remark"
      },
      {
        "label": "配置",
        "icon": "configuration"
      },
      {
        "label": "启用",
        "icon": "enabled"
      },
      {
        "label": "禁用",
        "icon": "unenabled"
      },
      {
        "label": "来电",
        "icon": "running-phone"
      },
      {
        "label": "电话",
        "icon": "circle-phone"
      },
      {
        "label": "挂断电话",
        "icon": "circle-phone-down"
      },
      {
        "label": "短信",
        "icon": "circle-message"
      },
      {
        "label": "提示类图标",
        "icon": "alert-info"
      },
      {
        "label": "成功类图标",
        "icon": "alert-success"
      },
      {
        "label": "警告类图标",
        "icon": "alert-warning"
      },
      {
        "label": "错误类图标",
        "icon": "alert-danger"
      }
    ]
  },
  "body": {
    type: 'service',
    schemaApi: {
      "url": "/",
      "dataProvider": true,
      "tdata": {
        icons: "${icons}",
        columnCount: '${columnCount}'
      },
      "adaptor": (payload, source, req) => {
          const { icons, columnCount } = req.tdata
          const rowCount = Math.ceil(icons.length / columnCount)

          const config = {
            type: 'wrapper',
            size: 'none',
            body:  Array(rowCount).fill().map((_, rowIndex) => {
              return {
                "type": "grid",
                "className": "m-t",
                "columns": Array(columnCount).fill().map((_, index) => {
                    const icon = icons[rowIndex * 3 + index]
                    return {
                      "body": [
                        icon ? {
                          "type": "tooltip-wrapper",
                          "content": icon.icon,
                          "body": [{
                            type: 'tpl',
                            tpl: `${icon.label}：`
                          },
                          {
                            "type": "icon",
                            "standardMode": true,
                            "icon": icon.icon
                          }],
                          "inline": true
                        } : {
                          type: 'wrapper',
                          size: 'none',
                          body: ''
                        },
                      ]
                    }
                })
              }
            })
          }

          return config
      }
    }
  }
}
```

#### 落地案例
  [客服平台-凭证收集-图片icon](http://moka.dmz.sit.caijj.net/customerplatformui/#/proof-collect)
  ![客服平台-凭证收集-图片icon](https://static02.sit.yxmarketing01.com/materialcenter/88461e68-be0c-4bdf-8faa-d3bc5f5b8a35.png)

### 配合button使用支持点击事件

```schema
{
  "type": "page",
  "body": [
    {
      "type": "button",
      "body": {
        "type": "icon",
        "icon": "running-phone",
        "size": "lg"
      },
      "actionType": "dialog",
      "dialog": {
        "title": "弹框",
        "body": "这是个简单的弹框。"
      }
    }
  ]
}
```

## 组件用法

### 基本使用

```schema
{
  "type": "page",
  "body": [
    {
      "type": "icon",
      "icon": "picture"
    }
  ]
}
```

### 配置尺寸

图标尺寸支持配置`sm`、`md`、`lg`，默认为 `sm`

```schema
{
  "type": "page",
  "body": {
    "type": "flex",
    "gap": true,
    "items": [
      {
        "type": "icon",
        "icon": "picture"
      },
      {
        "type": "icon",
        "icon": "picture",
        "size": "md"
      },
      {
        "type": "icon",
        "icon": "picture",
        "size": "lg"
      }
    ]
  }
}
```

### 图标高亮

icon 基于字体实现，可配置 `highLight` 来控制它。

```schema
{
  "type": "page",
  "body": [
    {
      "type": "icon",
      "icon": "picture",
      "highLight": true
    }
  ]
}
```

### 属性表

| 属性名    | 类型                                                             | 默认值 | 说明                                                                                                                      |
| --------- | ---------------------------------------------------------------- | ------ | ------------------------------------------------------------------------------------------------------------------------- |
 icon      | [模板](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/template) |        | icon 名称，支持通过 registerIcon 注册的 icon          |
| highLight | `boolean`                                                         |        | 设置 svg 类 icon 的颜色为主题色                                                                                                             |
| className | `string`                                                         |        | 外层 CSS 类名                                                                                                             | 
| size | `sm \| md \| lg`                                                         |     `sm`   | icon大小                                                                                                             |

### 事件表

当前组件会对外派发以下事件，可以通过`onEvent`来监听这些事件，并通过`actions`来配置执行的动作，详细查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action)。

| 事件名称   | 事件参数                               | 说明           |
| ---------- | -------------------------------------- | -------------- |
| click      | `nativeEvent: MouseEvent` 鼠标事件对象 | 点击时触发     |
| mouseenter | `nativeEvent: MouseEvent` 鼠标事件对象 | 鼠标移入时触发 |
| mouseleave | `nativeEvent: MouseEvent` 鼠标事件对象 | 鼠标移出时触发 |
