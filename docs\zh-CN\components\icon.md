---
title: Icon 图标
description:
type: 0
group: ⚙ 组件
menuName: Icon
icon:
order: 50
---

图标规范：

- 使用 icon 组件时，icon与其他组件的间距为 8px。

## 场景推荐

### 常用业务图标

以下是在实际业务场景中使用频率较高的图标推荐：

```schema
{
  "type": "page",
  "data": {
    "icons": [
      {
        "label": "排序",
        "icon": "exchange"
      },
      {
        "label": "排序置顶",
        "icon": "set-top"
      },
      {
        "label": "排序置底",
        "icon": "set-bottom"
      },
      {
        "label": "上下拖拽",
        "icon": "drag"
      },
      {
        "label": "跨等级移",
        "icon": "exchange-move"
      },
      {
        "label": "详情",
        "icon": "detail"
      },
      {
        "label": "导出",
        "icon": "export"
      },
      {
        "label": "上传",
        "icon": "upload"
      },
      {
        "label": "收藏",
        "icon": "star-regular"
      },
      {
        "label": "已收藏",
        "icon": "star-solid"
      },
      {
        "label": "拖拽把手",
        "icon": "drag"
      },
      {
        "label": "编辑",
        "icon": "pencil"
      },
      {
        "label": "删除",
        "icon": "minus"
      },
      {
        "label": "点赞",
        "icon": "thumbs-up-solid"
      },
      {
        "label": "取消点赞",
        "icon": "thumbs-up"
      },
      {
        "label": "文件夹",
        "icon": "folder"
      },
      {
        "label": "页面",
        "icon": "file-regular"
      },
      {
        "label": "子页面",
        "icon": "file"
      },
      {
        "label": "刷新",
        "icon": "arrow-rotate"
      },
      {
        "label": "复制",
        "icon": "copy"
      },
      {
        "label": "授权",
        "icon": "authorization"
      },
      {
        "label": "图片",
        "icon": "picture"
      },
      {
        "label": "提示",
        "icon": "remark"
      },
      {
        "label": "配置",
        "icon": "configuration"
      },
      {
        "label": "启用",
        "icon": "enabled"
      },
      {
        "label": "禁用",
        "icon": "unenabled"
      },
      {
        "label": "来电",
        "icon": "running-phone"
      },
      {
        "label": "电话",
        "icon": "circle-phone"
      },
      {
        "label": "挂断电话",
        "icon": "circle-phone-down"
      },
      {
        "label": "短信",
        "icon": "circle-message"
      },
      {
        "label": "热度",
        "icon": "hot"
      },
      {
        "label": "提示类图标",
        "icon": "alert-info"
      },
      {
        "label": "成功类图标",
        "icon": "alert-success"
      },
      {
        "label": "警告类图标",
        "icon": "alert-warning"
      },
      {
        "label": "错误类图标",
        "icon": "alert-danger"
      },
      {
        "label": "暂停",
        "icon": "pause-circle",
        "className": "pm-text-warning"
      },
      {
        "label": "开始",
        "icon": "play-circle-o",
        "className": "pm-text-success"
      },
      {
        "label": "停止",
        "icon": "stop-circle",
        "className": "pm-text-danger"
      },
      {
        "label": "添加",
        "icon": "plus-circle"
      },
      {
        "label": "展开",
        "icon": "angle-double-down"
      },
      {
        "label": "收起",
        "icon": "angle-double-up"
      },
      {
        "label": "手动操作",
        "icon": "pm-edit"
      }
    ]
  },
  "body": {
    "type": "each",
    "name": "icons",
    "className": "flex flex-wrap justify-start",
    "items": {
      "type": "tooltip-wrapper",
      "content": "${icon}",
      "placement": "top",
      "className": "flex items-center p-2 mr-2",
      "body": [
        {
          "type": "icon",
          "icon": "${icon}",
          "className": "mr-2",
          "size": "md"
        },
        {
          "type": "plain",
          "text": "${label}",
          "className": "text-xs text-muted"
        }
      ]
    }
  }
}
```

### 配合button使用支持点击事件

```schema
{
  "type": "page",
  "body": [
    {
      "type": "button",
      "body": {
        "type": "icon",
        "icon": "running-phone",
        "size": "lg"
      },
      "actionType": "dialog",
      "dialog": {
        "title": "弹框",
        "body": "这是个简单的弹框。"
      }
    }
  ]
}
```

## 组件用法

### 基本使用

```schema
{
  "type": "page",
  "body": [
    {
      "type": "icon",
      "icon": "picture"
    }
  ]
}
```

### 配置尺寸

图标尺寸支持配置`sm`、`md`、`lg`，默认为 `sm`

```schema
{
  "type": "page",
  "body": {
    "type": "flex",
    "gap": true,
    "items": [
      {
        "type": "icon",
        "icon": "picture"
      },
      {
        "type": "icon",
        "icon": "picture",
        "size": "md"
      },
      {
        "type": "icon",
        "icon": "picture",
        "size": "lg"
      }
    ]
  }
}
```

### 图标高亮

icon 基于字体实现，可配置 `highLight` 来控制它，该属性仅支持组件库内置图标及自定义注册的图标。

```schema
{
  "type": "page",
  "body": [
    {
      "type": "icon",
      "icon": "picture",
      "highLight": true
    }
  ]
}
```

### 图标查找规则

amis 按以下优先级查找和显示图标：

1. **内置注册图标** - 通过 `registerIcon` 注册的图标（优先级最高）
2. **图片链接** - 支持 SVG/PNG 等图片 URL
3. **FontAwesome图标** - 默认支持 FontAwesome v4 格式，如 `fa fa-home`
4. **自定义字体图标** - 如 iconfont 等

**使用建议：**
- 优先使用内置图标，性能更好且风格统一
- 需要特殊图标时，可以注册自定义图标或使用图片链接
- 避免图标名称与内置图标冲突

> **查找机制说明**：当您配置 `{"type": "icon", "icon": "home"}` 时，系统会按优先级查找：
> 1. 首先检查是否有名为"home"的内置图标或注册图标
> 2. 如果没有，则使用默认的 `vendor="fa"` 查找FontAwesome图标
> 3. 因此即使不指定vendor，也能正常显示FontAwesome图标（如果没有同名内置图标）
> 
> **其他组件中的使用**：在Tree、Button等组件中间接使用Icon时，没有vendor默认值，需要使用完整的图标类名（如 `"fa fa-home"`）。

### 内置图标

@dataseed/amis 内置了 **100+ 个常用图标**，可直接使用，无需额外引入。这些图标经过精心设计，覆盖了常见的业务场景。

> **使用提示**：内置图标具有最高优先级，当图标名称与 FontAwesome 或其他字体图标冲突时，会优先使用内置图标。

#### 基础操作类

```schema
{
  "type": "page",
  "data": {
    "icons": [
      {"label": "添加", "icon": "plus"},
      {"label": "添加(别名)", "icon": "add"},
      {"label": "减少", "icon": "minus"},
      {"label": "删除", "icon": "remove"},
      {"label": "确认", "icon": "check"},
      {"label": "关闭", "icon": "close"},
      {"label": "关闭(小)", "icon": "close-small"},
      {"label": "关闭(透明)", "icon": "close-transparent"},
      {"label": "编辑", "icon": "pencil"},
      {"label": "新编辑", "icon": "new-edit"},
      {"label": "查看", "icon": "view"},
      {"label": "搜索", "icon": "search"},
      {"label": "复制", "icon": "copy"},
      {"label": "重试", "icon": "retry"},
      {"label": "刷新", "icon": "refresh"},
      {"label": "重载", "icon": "reload"},
      {"label": "撤销", "icon": "undo"},
      {"label": "重做", "icon": "redo"},
      {"label": "返回", "icon": "back"},
      {"label": "移动", "icon": "move"}
    ]
  },
  "body": {
    "type": "each",
    "name": "icons",
    "className": "flex flex-wrap justify-start",
    "items": {
      "type": "tooltip-wrapper",
      "content": "${icon}",
      "placement": "top",
      "className": "flex items-center p-2 mr-2",
      "body": [
        {
          "type": "icon",
          "icon": "${icon}",
          "className": "mr-2",
          "size": "md"
        },
        {
          "type": "plain",
          "text": "${label}",
          "className": "text-xs text-muted"
        }
      ]
    }
  }
}
```

#### 文件管理类

```schema
{
  "type": "page",
  "data": {
    "icons": [
      {"label": "文件", "icon": "file"},
      {"label": "文件(常规)", "icon": "file-regular"},
      {"label": "文件夹", "icon": "folder"},
      {"label": "上传", "icon": "upload"},
      {"label": "云上传", "icon": "cloud-upload"},
      {"label": "下载", "icon": "download"},
      {"label": "下载2", "icon": "download2"},
      {"label": "图片", "icon": "image"},
      {"label": "图片(常规)", "icon": "picture"},
      {"label": "垃圾桶", "icon": "trash"},
      {"label": "导出", "icon": "export"},
      {"label": "详情", "icon": "detail"}
    ]
  },
  "body": {
    "type": "each",
    "name": "icons",
    "className": "flex flex-wrap justify-start",
    "items": {
      "type": "tooltip-wrapper",
      "content": "${icon}",
      "placement": "top",
      "className": "flex items-center p-2 mr-2",
      "body": [
        {
          "type": "icon",
          "icon": "${icon}",
          "className": "mr-2",
          "size": "md"
        },
        {
          "type": "plain",
          "text": "${label}",
          "className": "text-xs text-muted"
        }
      ]
    }
  }
}
```

#### 状态提示类

```schema
{
  "type": "page",
  "data": {
    "icons": [
      {"label": "成功", "icon": "success"},
      {"label": "失败", "icon": "fail"},
      {"label": "警告", "icon": "warning"},
      {"label": "警告标记", "icon": "warning-mark"},
      {"label": "信息", "icon": "info"},
      {"label": "信息圆圈", "icon": "info-circle"},
      {"label": "提示成功", "icon": "alert-success"},
      {"label": "提示信息", "icon": "alert-info"},
      {"label": "提示警告", "icon": "alert-warning"},
      {"label": "提示危险", "icon": "alert-danger"},
      {"label": "提示失败", "icon": "alert-fail"},
      {"label": "问号", "icon": "question"},
      {"label": "问号标记", "icon": "question-mark"},
      {"label": "启用", "icon": "enable"},
      {"label": "禁用", "icon": "disabled"},
      {"label": "启用v2", "icon": "enabled"},
      {"label": "禁用v2", "icon": "unenabled"}
    ]
  },
  "body": {
    "type": "each",
    "name": "icons",
    "className": "flex flex-wrap justify-start",
    "items": {
      "type": "tooltip-wrapper",
      "content": "${icon}",
      "placement": "top",
      "className": "flex items-center p-2 mr-2",
      "body": [
        {
          "type": "icon",
          "icon": "${icon}",
          "className": "mr-2",
          "size": "md"
        },
        {
          "type": "plain",
          "text": "${label}",
          "className": "text-xs text-muted"
        }
      ]
    }
  }
}
```

#### 导航控制类

```schema
{
  "type": "page",
  "data": {
    "icons": [
      {"label": "左箭头", "icon": "left-arrow"},
      {"label": "右箭头", "icon": "right-arrow"},
      {"label": "上一个", "icon": "prev"},
      {"label": "下一个", "icon": "next"},
      {"label": "右箭头粗", "icon": "right-arrow-bold"},
      {"label": "下箭头粗", "icon": "down-arrow-bold"},
      {"label": "向下", "icon": "down"},
      {"label": "右双箭头", "icon": "right-double-arrow"},
      {"label": "插入符", "icon": "caret"},
      {"label": "展开", "icon": "expand-alt"},
      {"label": "收缩", "icon": "compress-alt"},
      {"label": "树展开", "icon": "tree-down"},
      {"label": "排序交换", "icon": "exchange"},
      {"label": "移动交换", "icon": "exchange-move"},
      {"label": "置顶", "icon": "set-top"},
      {"label": "置底", "icon": "set-bottom"},
      {"label": "拖拽", "icon": "drag"},
      {"label": "拖拽条", "icon": "drag-bar"}
    ]
  },
  "body": {
    "type": "each",
    "name": "icons",
    "className": "flex flex-wrap justify-start",
    "items": {
      "type": "tooltip-wrapper",
      "content": "${icon}",
      "placement": "top",
      "className": "flex items-center p-2 mr-2",
      "body": [
        {
          "type": "icon",
          "icon": "${icon}",
          "className": "mr-2",
          "size": "md"
        },
        {
          "type": "plain",
          "text": "${label}",
          "className": "text-xs text-muted"
        }
      ]
    }
  }
}
```

#### 媒体控制类

```schema
{
  "type": "page",
  "data": {
    "icons": [
      {"label": "播放", "icon": "play"},
      {"label": "暂停", "icon": "pause"},
      {"label": "音量", "icon": "volume"},
      {"label": "静音", "icon": "mute"},
      {"label": "确认", "icon": "enter"},
      {"label": "放大", "icon": "zoom-in"},
      {"label": "缩小", "icon": "zoom-out"},
      {"label": "窗口还原", "icon": "window-restore"},
      {"label": "左旋转", "icon": "rotate-left"},
      {"label": "右旋转", "icon": "rotate-right"},
      {"label": "原始尺寸", "icon": "scale-origin"},
      {"label": "箭头旋转", "icon": "arrow-rotate"}
    ]
  },
  "body": {
    "type": "each",
    "name": "icons",
    "className": "flex flex-wrap justify-start",
    "items": {
      "type": "tooltip-wrapper",
      "content": "${icon}",
      "placement": "top",
      "className": "flex items-center p-2 mr-2",
      "body": [
        {
          "type": "icon",
          "icon": "${icon}",
          "className": "mr-2",
          "size": "md"
        },
        {
          "type": "plain",
          "text": "${label}",
          "className": "text-xs text-muted"
        }
      ]
    }
  }
}
```

#### 业务功能类

```schema
{
  "type": "page",
  "data": {
    "icons": [
      {"label": "日历", "icon": "calendar"},
      {"label": "时钟", "icon": "clock"},
      {"label": "日期", "icon": "date"},
      {"label": "主页", "icon": "home"},
      {"label": "位置", "icon": "location"},
      {"label": "计划", "icon": "schedule"},
      {"label": "设置", "icon": "setting"},
      {"label": "配置", "icon": "configuration"},
      {"label": "额外配置", "icon": "extra-configuration"},
      {"label": "过滤", "icon": "filter"},
      {"label": "列过滤", "icon": "column-filter"},
      {"label": "列", "icon": "columns"},
      {"label": "函数", "icon": "function"},
      {"label": "菜单", "icon": "menu"},
      {"label": "星星", "icon": "star"},
      {"label": "星星常规", "icon": "star-regular"},
      {"label": "星星实心", "icon": "star-solid"},
      {"label": "点赞", "icon": "thumbs-up"},
      {"label": "点赞实心", "icon": "thumbs-up-solid"},
      {"label": "排序默认", "icon": "sort-default"},
      {"label": "排序升序", "icon": "sort-asc"},
      {"label": "排序降序", "icon": "sort-desc"},
      {"label": "授权", "icon": "authorization"},
      {"label": "工单", "icon": "work-order"},
      {"label": "备注", "icon": "remark"},
      {"label": "热门", "icon": "hot"},
      {"label": "pm编辑", "icon": "pm-edit"}
    ]
  },
  "body": {
    "type": "each",
    "name": "icons",
    "className": "flex flex-wrap justify-start",
    "items": {
      "type": "tooltip-wrapper",
      "content": "${icon}",
      "placement": "top",
      "className": "flex items-center p-2 mr-2",
      "body": [
        {
          "type": "icon",
          "icon": "${icon}",
          "className": "mr-2",
          "size": "md"
        },
        {
          "type": "plain",
          "text": "${label}",
          "className": "text-xs text-muted"
        }
      ]
    }
  }
}
```

#### 通讯相关类

```schema
{
  "type": "page",
  "data": {
    "icons": [
      {"label": "电话", "icon": "circle-phone"},
      {"label": "挂断电话", "icon": "circle-phone-down"},
      {"label": "短信", "icon": "circle-message"},
      {"label": "来电", "icon": "running-phone"}
    ]
  },
  "body": {
    "type": "each",
    "name": "icons",
    "className": "flex flex-wrap justify-start",
    "items": {
      "type": "tooltip-wrapper",
      "content": "${icon}",
      "placement": "top",
      "className": "flex items-center p-2 mr-2",
      "body": [
        {
          "type": "icon",
          "icon": "${icon}",
          "className": "mr-2",
          "size": "md"
        },
        {
          "type": "plain",
          "text": "${label}",
          "className": "text-xs text-muted"
        }
      ]
    }
  }
}
```

### 自定义注册图标

@dataseed/amis 的 npm 包里面暴露了 `registerIcon` 方法，通过它可以注册自定义图标，配合 `icon` 组件即可使用。

> **注意**：registerIcon 注册的图标一定是React组件，否则使用时会报错。

下面代码示例中演示`umijs`项目中如何自定义注册图标，`umijs`自动将`svg`文件转换为React组件，所以可以直接使用`ReactComponent`。

**代码示例：**

```ts
import {registerIcon} from '@dataseed/amis';
import AlertDangerUrl { ReactComponent as SvgAlertDangerIcon } from '../../icons/alert-danger.svg'; // `umijs`自动将`svg`文件转换为React组件

registerIcon('custom-alert-danger', SvgAlertDangerIcon);
```

**使用方法：**
```json
{
  "type": "page",
  "body": [
    {
      "type": "icon",
      "icon": "custom-alert-danger"
    }
  ]
}
```

### FontAwesome 图标

FontAwesome 是世界最流行的图标库，amis 默认支持 FontAwesome v4 格式。

> **React 项目配置**：在 React 项目中使用 FontAwesome 图标需要引入 `@fortawesome/fontawesome-free`，然后在代码中 `import '@fortawesome/fontawesome-free/css/all.css'`，还有相关的 webpack 配置，具体请参考 [amis-react-starter](https://github.com/aisuda/amis-react-starter) 里的配置。如果只使用内置图标，则无需此配置。

#### 使用示例

```schema
{
  "type": "page",
  "body": {
    "type": "flex",
    "gap": true,
    "items": [
      {
        "type": "icon",
        "icon": "home"
      },
      {
        "type": "icon", 
        "icon": "user"
      },
    ]
  }
}
```

### 自定义字体图标

支持使用 iconfont 等自定义字体图标库。

#### iconfont 使用方法

**步骤1：引入iconfont样式**
```html
<link rel="stylesheet" href="//at.alicdn.com/t/font_xxx.css">
```

**步骤2：配置图标**

***方法一：使用完整类名***
```json
{
  "type": "icon",
  "icon": "iconfont icon-master"
}
```

***方法二：使用 vendor 参数***
```json
{
  "type": "icon",
  "icon": "icon-jiedian",
  "vendor": "iconfont"
}
```

#### 示例

```schema
{
  "type": "page",
  "body": {
    "type": "flex",
    "gap": true,
    "items": [
      {
        "type": "icon",
        "icon": "iconfont icon-master",
        "vendor": ""
      },
      {
        "type": "icon",
        "icon": "icon-jiedian", 
        "vendor": "iconfont"
      },
    ]
  }
}
```

> **提示**：使用自定义字体图标时，设置 `vendor=""` 可以直接使用完整的CSS类名。

### 图片图标

支持使用图片链接作为图标。

#### URL 图标

```schema
{
  "type": "page",
  "body": {
    "type": "icon",
    "icon": "https://suda.cdn.bcebos.com/images%2F2021-01%2Fdiamond.svg"
  }
}
```

#### SVG 字符串

```schema
{
  "type": "page", 
  "body": {
    "type": "icon",
    "icon": "<svg viewBox=\"0 0 1024 1024\"><path d=\"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z\" fill=\"#1890ff\"/></svg>"
  }
}
```

> **注意**：使用图片图标时，`highLight` 属性不生效。

## 属性表

| 属性名    | 类型                                                             | 默认值 | 说明                                                                                                                      |
| --------- | ---------------------------------------------------------------- | ------ | ------------------------------------------------------------------------------------------------------------------------- |
| icon      | [模板](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/template) |        | icon 名称，支持通过 registerIcon 注册的 icon、FontAwesome 图标、图片链接等          |
| vendor    | `'fa' \| 'iconfont' \| string`                                  | `'fa'`   | 字体图标厂商，fa 表示 FontAwesome，iconfont 表示阿里巴巴矢量图标库                                        |
| highLight | `boolean`                                                        |        | 设置 svg 类 icon 的颜色为主题色                                                                                                             |
| className | `string`                                                         |        | 外层 CSS 类名                                                                                                             | 
| size      | `'sm' \| 'md' \| 'lg'`                                          | `'sm'` | icon大小                                                                                                             |

## 事件表

当前组件会对外派发以下事件，可以通过`onEvent`来监听这些事件，并通过`actions`来配置执行的动作，详细查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action)。

| 事件名称   | 事件参数                               | 说明           |
| ---------- | -------------------------------------- | -------------- |
| click      | `nativeEvent: MouseEvent` 鼠标事件对象 | 点击时触发     |
| mouseenter | `nativeEvent: MouseEvent` 鼠标事件对象 | 鼠标移入时触发 |
| mouseleave | `nativeEvent: MouseEvent` 鼠标事件对象 | 鼠标移出时触发 |

## 常见问题

### 图标不显示怎么办？

**排查步骤：**

1. **检查图标名称是否正确**
   ```json
   // ✅ 正确
   {"type": "icon", "icon": "home"}
   
   // ❌ 错误：拼写错误
   {"type": "icon", "icon": "hone"}
   ```

2. **检查图标类型和配置**
   ```json
   // 内置图标
   {"type": "icon", "icon": "home"}
   
   // FontAwesome图标
   {"type": "icon", "icon": "home"}
   
   // 自定义字体图标
   {"type": "icon", "icon": "icon-home", "vendor": "iconfont"}
   ```

3. **检查CSS样式是否正确引入**
   - FontAwesome：确保引入了对应的CSS文件
   - iconfont：确保引入了自定义的字体文件

### 为什么某些图标显示不是我期望的？

这通常是因为图标查找优先级导致的：

```json
// 如果你配置了这个
{"type": "icon", "icon": "home"}

// 但显示的不是FontAwesome的home图标
// 这是因为内置图标优先级更高，使用的是内置的home图标

// 解决方案：明确指定FontAwesome
{"type": "icon", "icon": "fa fa-home"}
```
