---
title: LeftRightContainer 容器
description:
type: 0
group: ⚙ 组件
menuName: LeftRightContainer
icon:
order: 83
---

当有内容展示需要分为左右两部分时，可使用 LeftRightContainer 容器。

LeftRightContainer 容器的分隔线与右侧部分，中间有16px透明间隔。分隔线可拖动来调整布局占用比例。容器自身没有高度，默认是占满父容器高度，若父容器没有高度则按照自身高度。

## 模块划分

![LeftRightContainer展示模块图](https://static02.sit.yxmarketing01.com/materialcenter/9e4a6996-98b3-418d-adbb-7443e59f74b3.png)

1. 左侧展示区域（必有）  

左侧展示区域，有白色背景与16px内边距。可放入任何组件，但一般放入能与右侧联动的内容，比如：树组件目录，或者查询表单等。

1. 右侧展示区域（必有）

右侧展示区域，透明背景无内边距。可放入任何组件，用于页面内容展示，比如：CRUD列表、表单、分组等。

## 场景推荐

### 左侧Tree，右侧分组

一般在页面左侧是 `Tree` 组件，点选后关联到右侧展示根据所选内容刷新。右侧可以是各种展示组件。

注意：由于 `LeftRightContainer` 的滚动需要依赖父容器高度。文档中 `Page` 组件没有设置高度，因此配置了 `"bodyClassName": "h-screen"`（下同），在业务中可能不需要该配置。如果 `LeftRightContainer` 在`Form`中使用，需要为`Form`设置`autoFillHeight: true`，该属性表示`Form`高度占满父元素

```schema
{
  "type": "page",
  "bodyClassName": "h-screen",
  "body": {
    "type": "form",
    "autoFillHeight": true,
    "body": {
      "type": "left-right-container",
      "defaultWidth": 250,
      "left": {
        "type": "input-tree",
        "label": false,
        "searchable": true,
        "editable": true,
        "autoFillHeight": true,
        "name": "tree2",
        "multiple": false,
        "autoCheckChildren": false,
        "options": [
          {
            "label": "A",
            "value": "a"
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "C",
            "value": "c"
          }
        ],
        "onEvent": {
          "change": {
            "actions": [
              {
                "actionType": "query",
                "componentId": "right-crud",
                "args": {
                  "queryParams": {
                    "tree2": "${event.data.value}"
                  }
                }
              }
            ]
          }
        },
      },
      "right": {
        "type": "form",
        "static": true,
        "actions": [],
        "body": {
          "type": "group-container",
          "collapsible": true,
          "activeKey": [
            "1"
          ],
          "items": [
            {
              "key": "1",
              "header": {
                "title": "第一步，基础信息",
                "subTitle": "这是小标题"
              },
              "body": [
                {
                  "type": "group",
                  "body": [
                    {
                      "type": "input-text",
                      "name": "text1",
                      "label": "姓名"
                    },
                    {
                      "type": "input-text",
                      "name": "text2",
                      "label": "年龄"
                    },
                    {
                      "type": "input-text",
                      "name": "text3",
                      "label": "班级",
                      "required": true
                    }
                  ]
                },
                {
                  "type": "group",
                  "body": [
                    {
                      "type": "input-text",
                      "name": "text4",
                      "label": "邮箱"
                    },
                    {
                      "type": "input-text",
                      "name": "text5",
                      "label": "电话"
                    },
                    {
                      "type": "input-text",
                      "name": "text6",
                      "label": "地址"
                    }
                  ]
                },
                {
                  "type": "group",
                  "body": [
                    {
                      "type": "input-text",
                      "name": "text7",
                      "label": "其它",
                      "columnRatio": 4
                    }
                  ]
                }
              ]
            },
            {
              "key": "2",
              "header": {
                "title": "第一步，基础信息"
              },
              "body": [
                {
                  "type": "group",
                  "body": [
                    {
                      "type": "input-text",
                      "name": "second1",
                      "label": "邮箱"
                    },
                    {
                      "type": "input-text",
                      "name": "second2",
                      "label": "电话"
                    },
                    {
                      "type": "input-text",
                      "name": "second3",
                      "label": "地址",
                      "columnRatio": 4
                    }
                  ]
                },
                {
                  "type": "group",
                  "body": [
                    {
                      "type": "input-text",
                      "name": "second3",
                      "label": "姓名",
                      "placeholder": "请输入"
                    }
                  ]
                }
              ]
            },
            {
              "key": "3",
              "header": {
                "title": "第三步，其他信息"
              },
              "body": [
                {
                  "type": "group",
                  "body": [
                    {
                      "type": "input-text",
                      "name": "second1",
                      "label": "邮箱"
                    },
                    {
                      "type": "input-text",
                      "name": "second2",
                      "label": "电话"
                    },
                    {
                      "type": "input-text",
                      "name": "second3",
                      "label": "地址",
                      "columnRatio": 4
                    }
                  ]
                },
                {
                  "type": "group",
                  "body": [
                    {
                      "type": "input-text",
                      "name": "second3",
                      "label": "姓名",
                      "placeholder": "请输入"
                    }
                  ]
                }
              ]
            }
          ]
        }
      }
    }
  }
}
```

- 落地案例  
  [信息化一站式-权限管理-角色管理](http://moka.dmz.sit.caijj.net/idaasui/#/outer/authManagement?_shMenuId=tenant_menu_P0174_informatization_role_manager_m2vhqduh)  
   ![信息化一站式-权限管理-角色管理](https://static02.sit.yxmarketing01.com/tdmaterial/e2b4d80a6c8b4f08b54325f203e312d5.png)

### 左侧表单，右侧列表展示

一般在页面左侧是 `Form` 表单，关联到右侧展示根据所填写内容进行联动刷新。右侧可以是各种展示组件。

```schema
{
  "type": "page",
  "id": "pageId",
  "bodyClassName": "h-screen",
  "data": {
    "tree2": {
      "label": "数禾测试数据A",
      "value": "shuhe-test-data-a"
    },
    "text1": "shuhe-test-data-a"
  },
  "body": {
    "type": "left-right-container",
    "leftTitle": "过滤器",
    "left": [{
      "type": "form",
      "labelWidth": 60,
      "body": [
        {
          "type": "group-container",
          "collapsible": true,
          "activeKey": ["0", "1"],
          "items": [
            {
              "type": "panel",
              "header": {
                "title": "基础信息"
              },
              "body": [
                {
                  "type": "input-text",
                  "name": "text-search1",
                  "label": "姓名"
                },
                {
                  "type": "input-text",
                  "name": "text-search2",
                  "label": "年龄"
                }
              ]
            },
            {
              "type": "panel",
              "header": {
                "title": "复杂信息"
              },
              "body": [
                {
                  "type": "input-text",
                  "name": "text-search3",
                  "label": "邮箱"
                },
                {
                  "type": "input-text",
                  "name": "text-search4",
                  "label": "电话"
                }
              ]
            }
          ]
        }
      ],
      "actions": [
        {
          "type": "button",
          "label": "重置"
        },
        {
          "type": "button",
          "level": "primary",
          "label": "查询"
        }
      ]
    }],
    "right": {
      "type": "crud",
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
      "columns": [
        {
          "name": "id",
          "label": "ID",
          "searchable": {
            "type": "input-text",
            "placeholder": "请输入"
          }
        },
        {
          "name": "engine",
          "label": "Rendering engine",
          "searchable": {
            "type": "input-text",
            "placeholder": "请输入"
          }
        },
        {
          "name": "browser",
          "label": "Browser",
          "searchable": {
            "type": "input-text",
            "placeholder": "请输入"
          }
        },
        {
          "name": "platform",
          "label": "Platform(s)"
        },
        {
          "name": "version",
          "label": "Engine version"
        },
        {
          "name": "grade",
          "label": "CSS grade"
        },
        {
          "type": "operation",
          "label": "操作",
          "width": 80,
          "buttons": [
            {
              "label": "详情",
              "type": "button",
              "level": "link",
              "actionType": "dialog",
              "dialog": {
                "title": "查看详情",
                "body": {
                  "type": "form",
                  "body": [
                    {
                      "type": "input-text",
                      "name": "engine",
                      "label": "Engine"
                    },
                    {
                      "type": "input-text",
                      "name": "browser",
                      "label": "Browser"
                    },
                    {
                      "type": "input-text",
                      "name": "platform",
                      "label": "platform"
                    },
                    {
                      "type": "input-text",
                      "name": "version",
                      "label": "version"
                    },
                    {
                      "type": "control",
                      "label": "grade",
                      "body": {
                        "type": "tag",
                        "label": "${grade}",
                        "displayMode": "normal",
                        "color": "active"
                      }
                    }
                  ]
                }
              }
            },
            {
              "label": "删除",
              "type": "button",
              "level": "link",
              "disabledOn": "this.grade === 'A'"
            }
          ]
        }
      ]
    }
  }
}

```

- 落地案例  
  [大数据_新-数据目录-数据发现](https://moka.dmz.sit.caijj.net/analytoolui/#/data-found-search)  
   ![大数据_新-数据目录-数据发现](https://static02.sit.yxmarketing01.com/tdmaterial/df6e5fb4d00f476e8b2eeff08ee78ab2.png)

<!-- ### 右侧带标题联动左侧tree

```schema
{
  "type": "page",
  "id": "page",
  "data": {
    "treePathList": [
      {
        "title": "shuhe-test-data-b"
      },
      {
        "title": "shuhe-test-data-b-2"
      }
    ],
    "treeSelect": "shuhe-test-data-b-2",
    "treeSelectOrigin": "shuhe-test-data-b-2",
    "options": [
      {
        "label": "数禾测试数据A",
        "value": "shuhe-test-data-a"
      },
      {
        "label": "数禾测试数据B",
        "value": "shuhe-test-data-b",
        "children": [
          {
            "label": "数禾测试数据B-1",
            "value": "shuhe-test-data-b-1",
            "children": [
              {
                "label": "数禾测试数据B-1-1",
                "value": "shuhe-test-data-b-1-1"
              },
              {
                "label": "数禾测试数据B-2",
                "value": "shuhe-test-data-b-2-1"
              },
              {
                "label": "数禾测试数据B-3",
                "value": "shuhe-test-data-b-3-1"
              }
            ]
          },
          {
            "label": "数禾测试数据B-2",
            "value": "shuhe-test-data-b-2"
          },
          {
            "label": "数禾测试数据B-3",
            "value": "shuhe-test-data-b-3"
          }
        ]
      },
      {
        "label": "数禾测试数据C",
        "value": "c"
      }
    ]
  },
  "body": [
    {
      "type": "title",
      "title": "页面大标题名称大标题名称Demo",
      "iconConfig": true
    },
    {
      "type": "left-right-container",
      "autoFillHeight": true,
      "defaultWidth": 400,
      "labelWidth": 60,
      "left": {
        "type": "form",
        "id": "myForm",
        "debug": true,
        "actions": [],
        "body": {
          "type": "input-tree",
          "name": "treeSelectOrigin",
          "id": "tree2",
          "label": false,
          "multiple": false,
          "searchable": true,
          "autoCheckChildren": false,
          "joinValues": true,
          "enableNodePath": true,
          "autoFillHeight": true,
          "initiallyOpen": false,
          "source": "${options}",
          "onEvent": {
            "change": {
              "actions": [
                {
                  "actionType": "setValue",
                  "componentId": "page",
                  "args": {
                    "value": {
                      "treePathList": "${ARRAYMAP(${SPLIT(treeSelectOrigin, '/')},item => {title: item})}",
                      "treeSelect": "${SPLIT(treeSelectOrigin, '/')[SPLIT(treeSelectOrigin, '/').length -1]}"
                    }
                  }
                }
              ]
            }
          }
        }
      },
      "right": [
        {
          "type": "flex",
          "direction": "column",
          "gap": true,
          "items": [
            {
              "type": "wrapper",
              "bgColor": "white",
              "body": {
                "type": "flex",
                "gap": true,
                "alignItems": "center",
                "className": "flex-wrap",
                "items": [
                  {
                    "type": "button",
                    "label": "👏 欢迎来到,",
                    "level": "text"
                  },
                  {
                    "type": "each",
                    "name": "treePathList",
                    "wrapperComponent": false,
                    "visibleOn": "${treePathList && treePathList.length > 0}",
                    "items": [
                      {
                        "type": "button",
                        "label": "${title}",
                        "level": "text",
                        "visibleOn": "${treeSelect !== title}",
                        "onEvent": {
                          "click": {
                            "actions": [
                              {
                                "actionType": "setValue",
                                "componentId": "page",
                                "args": {
                                  "value": {
                                    "treeSelect": "${title}"
                                  }
                                }
                              },
                              {
                                "actionType": "setValue",
                                "componentId": "myForm",
                                "args": {
                                  "value": {
                                    "treeSelectOrigin": "${title}"
                                  }
                                }
                              }
                            ]
                          }
                        }
                      },
                      {
                        "type": "button",
                        "label": "${title}",
                        "level": "link",
                        "visibleOn": "${treeSelect === title}"
                      },
                      {
                        "type": "button",
                        "label": "  > ",
                        "level": "text",
                        "visibleOn": "${(index + 1) < treePathList.length}"
                      }
                    ]
                  },
                  {
                    "type": "button",
                    "label": "指标资产门户",
                    "level": "text"
                  },
                  {
                    "type": "button",
                    "label": "清空",
                    "level": "link",
                    "visibleOn": "${treePathList && treePathList.length > 0}",
                    "onEvent": {
                      "click": {
                        "actions": [
                          {
                            "actionType": "setValue",
                            "componentId": "page",
                            "args": {
                              "value": {
                                "treeSelect": "",
                                "treePathList": []
                              }
                            }
                          },
                          {
                            "actionType": "setValue",
                            "componentId": "myForm",
                            "args": {
                              "value": {
                                "treeSelectOrigin": ""
                              }
                            }
                          },
                          {
                            "actionType": "collapse",
                            "componentId": "tree2"
                          }
                        ]
                      }
                    }

                  }
                ]
              }
            },
            {
              "type": "crud",
              "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4?treeSelect=${treeSelect}",
              "topToolbar": [
                {
                  "type": "button",
                  "label": "主按钮",
                  "actionType": "url",
                  "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
                  "level": "primary",
                  "blank": false
                },
                {
                  "type": "button",
                  "label": "次按钮1",
                  "actionType": "url",
                  "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
                },
                {
                  "type": "button",
                  "label": "次按钮2",
                  "actionType": "url",
                  "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
                },
                {
                  "type": "button",
                  "label": "次按钮3",
                  "actionType": "url",
                  "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
                },
                {
                  "type": "button",
                  "label": "次按钮4",
                  "disabled": true
                },
                {
                  "type": "button",
                  "label": "次按钮5"
                },
                {
                  "type": "button",
                  "label": "次按钮6"
                }
              ],
              "filter": {
                "title": "",
                "body": [
                  {
                    "type": "group",
                    "mode": "horizontal",
                    "body": [
                      {
                        "type": "input-text",
                        "name": "keywords",
                        "label": "关键字",
                        "clearable": true,
                        "placeholder": "通过关键字搜索",
                        "columnRatio": 4
                      },
                      {
                        "type": "input-text",
                        "name": "engine",
                        "label": "Engine",
                        "clearable": true,
                        "columnRatio": 4
                      },
                      {
                        "type": "input-text",
                        "name": "platform",
                        "label": "Platform",
                        "clearable": true,
                        "columnRatio": 4
                      },
                      {
                        "type": "input-text",
                        "name": "keywords1",
                        "label": "关键字1",
                        "clearable": true,
                        "placeholder": "通过关键字搜索",
                        "columnRatio": 4
                      },
                      {
                        "type": "input-text",
                        "name": "engine1",
                        "label": "Engine1",
                        "clearable": true,
                        "columnRatio": 4
                      },
                      {
                        "type": "input-text",
                        "name": "platform1",
                        "label": "Platform1",
                        "clearable": true,
                        "columnRatio": 4
                      }
                    ]
                  }
                ],
                "actions": [
                  {
                    "type": "reset",
                    "label": "重 置"
                  },
                  {
                    "type": "submit",
                    "level": "primary",
                    "label": "查 询"
                  }
                ]
              },
              "columns": [
                {
                  "name": "id",
                  "label": "ID"
                },
                {
                  "name": "engine",
                  "label": "Rendering engine",
                  "headSearchable": {
                    "type": "input-text",
                    "name": "engine3",
                    "label": "Rendering enginer"
                  }
                },
                {
                  "name": "browser",
                  "label": "Browser",
                  "headSearchable": {
                    "type": "input-text",
                    "name": "browser3",
                    "label": "Browser"
                  }
                },
                {
                  "name": "platform",
                  "label": "Platform(s)"
                },
                {
                  "name": "version",
                  "label": "Engine version"
                },
                {
                  "name": "grade",
                  "label": "CSS grade"
                },
                {
                  "type": "operation",
                  "label": "操作",
                  "buttons": [
                    {
                      "type": "button",
                      "level": "link",
                      "label": "详情",
                      "actionType": "dialog",
                      "dialog": {
                        "title": "详情",
                        "showCloseButton": false,
                        "body": "这是个简单的弹框。"
                      }
                    },
                    {
                      "label": "删除",
                      "type": "button",
                      "actionType": "ajax",
                      "level": "link",
                      "disabled": true,
                      "confirmText": "确认要删除吗？",
                      "api": {
                        "method": "delete",
                        "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
                      }
                    },
                    {
                      "label": "编辑",
                      "type": "button",
                      "level": "link",
                      "actionType": "dialog",
                      "dialog": {
                        "title": "编辑",
                        "showCloseButton": false,
                        "body": "这是个简单的弹框。"
                      }
                    },
                    {
                      "label": "空跑",
                      "type": "button",
                      "level": "link",
                      "actionType": "dialog",
                      "dialog": {
                        "title": "空跑",
                        "showCloseButton": false,
                        "body": "这是个简单的弹框。"
                      }
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
``` -->

### 左侧tabs+tree，右侧tabs+crud带全局操作按钮

```schema
{
  "type": "page",
  "bodyClassName": "h-screen",
  "body": {
    "type": "left-right-container",
    "defaultWidth": 250,
    "left": {
      "type": "tabs",
      "tabs": [
        {
          "title": "用户旅程",
          "tab": {
            "type": "form",
            "actions": [],
            "wrapWithPanel": false,
            "body": [
              {
                "type": "input-tree",
                "label": false,
                "searchable": true,
                "editable": true,
                "autoFillHeight": true,
                "name": "tree2",
                "multiple": false,
                "autoCheckChildren": false,
                "options": [
                  {
                    "label": "A",
                    "value": "a"
                  },
                  {
                    "label": "B",
                    "value": "b",
                    "children": [
                      {
                        "label": "B-1",
                        "value": "b-1"
                      },
                      {
                        "label": "B-2",
                        "value": "b-2"
                      },
                      {
                        "label": "B-3",
                        "value": "b-3"
                      }
                    ]
                  },
                  {
                    "label": "B",
                    "value": "b",
                    "children": [
                      {
                        "label": "B-1",
                        "value": "b-1"
                      },
                      {
                        "label": "B-2",
                        "value": "b-2"
                      },
                      {
                        "label": "B-3",
                        "value": "b-3"
                      }
                    ]
                  },
                  {
                    "label": "B",
                    "value": "b",
                    "children": [
                      {
                        "label": "B-1",
                        "value": "b-1"
                      },
                      {
                        "label": "B-2",
                        "value": "b-2"
                      },
                      {
                        "label": "B-3",
                        "value": "b-3"
                      }
                    ]
                  },
                  {
                    "label": "B",
                    "value": "b",
                    "children": [
                      {
                        "label": "B-1",
                        "value": "b-1"
                      },
                      {
                        "label": "B-2",
                        "value": "b-2"
                      },
                      {
                        "label": "B-3",
                        "value": "b-3"
                      }
                    ]
                  },
                  {
                    "label": "B",
                    "value": "b",
                    "children": [
                      {
                        "label": "B-1",
                        "value": "b-1"
                      },
                      {
                        "label": "B-2",
                        "value": "b-2"
                      },
                      {
                        "label": "B-3",
                        "value": "b-3"
                      }
                    ]
                  },
                  {
                    "label": "B",
                    "value": "b",
                    "children": [
                      {
                        "label": "B-1",
                        "value": "b-1"
                      },
                      {
                        "label": "B-2",
                        "value": "b-2"
                      },
                      {
                        "label": "B-3",
                        "value": "b-3"
                      }
                    ]
                  },
                  {
                    "label": "B",
                    "value": "b",
                    "children": [
                      {
                        "label": "B-1",
                        "value": "b-1"
                      },
                      {
                        "label": "B-2",
                        "value": "b-2"
                      },
                      {
                        "label": "B-3",
                        "value": "b-3"
                      }
                    ]
                  },
                  {
                    "label": "B",
                    "value": "b",
                    "children": [
                      {
                        "label": "B-1",
                        "value": "b-1"
                      },
                      {
                        "label": "B-2",
                        "value": "b-2"
                      },
                      {
                        "label": "B-3",
                        "value": "b-3"
                      }
                    ]
                  },
                  {
                    "label": "B",
                    "value": "b",
                    "children": [
                      {
                        "label": "B-1",
                        "value": "b-1"
                      },
                      {
                        "label": "B-2",
                        "value": "b-2"
                      },
                      {
                        "label": "B-3",
                        "value": "b-3"
                      }
                    ]
                  },
                  {
                    "label": "C",
                    "value": "c"
                  }
                ]
              }
            ]
          }
        },
        {
          "title": "企业视角",
          "tab": {
            "type": "form",
            "actions": [],
            "wrapWithPanel": false,
            "body": [
              {
                "type": "input-tree",
                "label": false,
                "searchable": true,
                "editable": true,
                "autoFillHeight": true,
                "name": "tree2",
                "multiple": false,
                "autoCheckChildren": false,
                "options": [
                  {
                    "label": "A",
                    "value": "a"
                  },
                  {
                    "label": "B",
                    "value": "b",
                    "children": [
                      {
                        "label": "B-1",
                        "value": "b-1"
                      },
                      {
                        "label": "B-2",
                        "value": "b-2"
                      },
                      {
                        "label": "B-3",
                        "value": "b-3"
                      }
                    ]
                  },
                  {
                    "label": "B",
                    "value": "b",
                    "children": [
                      {
                        "label": "B-1",
                        "value": "b-1"
                      },
                      {
                        "label": "B-2",
                        "value": "b-2"
                      },
                      {
                        "label": "B-3",
                        "value": "b-3"
                      }
                    ]
                  },
                  {
                    "label": "B",
                    "value": "b",
                    "children": [
                      {
                        "label": "B-1",
                        "value": "b-1"
                      },
                      {
                        "label": "B-2",
                        "value": "b-2"
                      },
                      {
                        "label": "B-3",
                        "value": "b-3"
                      }
                    ]
                  },
                  {
                    "label": "B",
                    "value": "b",
                    "children": [
                      {
                        "label": "B-1",
                        "value": "b-1"
                      },
                      {
                        "label": "B-2",
                        "value": "b-2"
                      },
                      {
                        "label": "B-3",
                        "value": "b-3"
                      }
                    ]
                  },
                  {
                    "label": "B",
                    "value": "b",
                    "children": [
                      {
                        "label": "B-1",
                        "value": "b-1"
                      },
                      {
                        "label": "B-2",
                        "value": "b-2"
                      },
                      {
                        "label": "B-3",
                        "value": "b-3"
                      }
                    ]
                  },
                  {
                    "label": "B",
                    "value": "b",
                    "children": [
                      {
                        "label": "B-1",
                        "value": "b-1"
                      },
                      {
                        "label": "B-2",
                        "value": "b-2"
                      },
                      {
                        "label": "B-3",
                        "value": "b-3"
                      }
                    ]
                  },
                  {
                    "label": "B",
                    "value": "b",
                    "children": [
                      {
                        "label": "B-1",
                        "value": "b-1"
                      },
                      {
                        "label": "B-2",
                        "value": "b-2"
                      },
                      {
                        "label": "B-3",
                        "value": "b-3"
                      }
                    ]
                  },
                  {
                    "label": "B",
                    "value": "b",
                    "children": [
                      {
                        "label": "B-1",
                        "value": "b-1"
                      },
                      {
                        "label": "B-2",
                        "value": "b-2"
                      },
                      {
                        "label": "B-3",
                        "value": "b-3"
                      }
                    ]
                  },
                  {
                    "label": "B",
                    "value": "b",
                    "children": [
                      {
                        "label": "B-1",
                        "value": "b-1"
                      },
                      {
                        "label": "B-2",
                        "value": "b-2"
                      },
                      {
                        "label": "B-3",
                        "value": "b-3"
                      }
                    ]
                  },
                  {
                    "label": "C",
                    "value": "c"
                  }
                ]
              }
            ]
          }
        }
      ]
    },
    "right": {
      "type": "tabs",
      "tabs": [
        {
          "title": "过滤性指标",
          "tab": {
            "type": "crud",
            "syncLocation": false,
            "columnsTogglable": false,
            "footerToolbar": [
              {
                "type": "tpl",
                "tpl": "共${count}条"
              },
              {
                "type": "pagination",
                "maxButtons": 3,
                "layout": "pager,perPage,go"
              }
            ],
            "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4",
            "filter": {
              "title": "",
              "body": [
                {
                  "type": "group",
                  "mode": "horizontal",
                  "body": [
                    {
                      "type": "input-text",
                      "name": "keywords",
                      "label": "关键字",
                      "clearable": true,
                      "placeholder": "通过关键字搜索",
                      "columnRatio": 4
                    },
                    {
                      "type": "input-text",
                      "name": "engine",
                      "label": "Engine",
                      "clearable": true,
                      "columnRatio": 4
                    },
                    {
                      "type": "input-text",
                      "name": "platform1",
                      "label": "Platform1",
                      "clearable": true,
                      "columnRatio": 4
                    }
                  ]
                }
              ],
              "actions": [
                {
                  "type": "reset",
                  "label": "重 置"
                },
                {
                  "type": "submit",
                  "level": "primary",
                  "label": "查 询"
                }
              ]
            },
            "topToolbar": {
              "type": "button",
              "label": "新增",
              "level": "primary"
            },
            "columns": [
              {
                "name": "id",
                "label": "ID"
              },
              {
                "name": "engine",
                "label": "Rendering engine"
              },
              {
                "name": "browser",
                "label": "Browser"
              },
              {
                "name": "platform",
                "label": "Platform(s)"
              },
              {
                "name": "version",
                "label": "Engine version"
              },
              {
                "name": "grade",
                "label": "CSS grade"
              },
              {
                "type": "operation",
                "label": "操作",
                "buttons": [
                  {
                    "label": "详情",
                    "type": "button",
                    "level": "link",
                    "actionType": "dialog",
                    "dialog": {
                      "title": "详情",
                      "showCloseButton": false,
                      "body": "这是个简单的弹框。"
                    }
                  },
                  {
                    "label": "删除",
                    "type": "button",
                    "actionType": "ajax",
                    "level": "link",
                    "disabled": true,
                    "confirmText": "确认要删除吗？",
                    "api": {
                      "method": "delete",
                      "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
                    }
                  },
                  {
                    "label": "编辑",
                    "type": "button",
                    "level": "link",
                    "actionType": "dialog",
                    "dialog": {
                      "title": "编辑",
                      "showCloseButton": false,
                      "body": "这是个简单的弹框。"
                    }
                  },
                  {
                    "label": "空跑",
                    "type": "button",
                    "level": "link",
                    "actionType": "dialog",
                    "dialog": {
                      "title": "空跑",
                      "showCloseButton": false,
                      "body": "这是个简单的弹框。"
                    }
                  }
                ]
              }
            ]
          }
        },
        {
          "title": "衍生指标",
          "tab": {
            "type": "crud",
            "syncLocation": false,
            "columnsTogglable": false,
            "footerToolbar": [
              {
                "type": "tpl",
                "tpl": "共${count}条"
              },
              {
                "type": "pagination",
                "maxButtons": 3,
                "layout": "pager,perPage,go"
              }
            ],
            "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4",
            "filter": {
              "title": "",
              "body": [
                {
                  "type": "group",
                  "mode": "horizontal",
                  "body": [
                    {
                      "type": "input-text",
                      "name": "keywords",
                      "label": "关键字",
                      "clearable": true,
                      "placeholder": "通过关键字搜索",
                      "columnRatio": 4
                    },
                    {
                      "type": "input-text",
                      "name": "engine",
                      "label": "Engine",
                      "clearable": true,
                      "columnRatio": 4
                    },
                    {
                      "type": "input-text",
                      "name": "platform1",
                      "label": "Platform1",
                      "clearable": true,
                      "columnRatio": 4
                    }
                  ]
                }
              ],
              "actions": [
                {
                  "type": "reset",
                  "label": "重 置"
                },
                {
                  "type": "submit",
                  "level": "primary",
                  "label": "查 询"
                }
              ]
            },
            "topToolbar": {
              "type": "button",
              "label": "新增",
              "level": "primary"
            },
            "columns": [
              {
                "name": "id",
                "label": "ID"
              },
              {
                "name": "engine",
                "label": "Rendering engine"
              },
              {
                "name": "browser",
                "label": "Browser"
              },
              {
                "name": "platform",
                "label": "Platform(s)"
              },
              {
                "name": "version",
                "label": "Engine version"
              },
              {
                "name": "grade",
                "label": "CSS grade"
              },
              {
                "type": "operation",
                "label": "操作",
                "buttons": [
                  {
                    "label": "详情",
                    "type": "button",
                    "level": "link",
                    "actionType": "dialog",
                    "dialog": {
                      "title": "详情",
                      "showCloseButton": false,
                      "body": "这是个简单的弹框。"
                    }
                  },
                  {
                    "label": "删除",
                    "type": "button",
                    "actionType": "ajax",
                    "level": "link",
                    "disabled": true,
                    "confirmText": "确认要删除吗？",
                    "api": {
                      "method": "delete",
                      "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
                    }
                  },
                  {
                    "label": "编辑",
                    "type": "button",
                    "level": "link",
                    "actionType": "dialog",
                    "dialog": {
                      "title": "编辑",
                      "showCloseButton": false,
                      "body": "这是个简单的弹框。"
                    }
                  },
                  {
                    "label": "空跑",
                    "type": "button",
                    "level": "link",
                    "actionType": "dialog",
                    "dialog": {
                      "title": "空跑",
                      "showCloseButton": false,
                      "body": "这是个简单的弹框。"
                    }
                  }
                ]
              }
            ]
          }
        }
      ]
    }
  }
}

```

- 落地案例  
  [大数据一站式-指标平台-指标要素定义-度量](https://moka.dmz.sit.caijj.net/metricsamisui/#/standard-atom?_shMenuId=tenant_menu_P0280_atom_metric)  
   ![大数据一站式-指标平台-指标要素定义-度量](https://static02.sit.yxmarketing01.com/tdmaterial/cb4adf170968435888392a328dca53a5.png)

### 固定宽度

```schema
{
  "type": "page",
  "bodyClassName": "h-screen",
  "body": {
    "type": "left-right-container",
    "leftWidth": 200,
    "draggable": false,
    "left": {
    "type": "form",
    "actions": [],
    "wrapWithPanel": false,
    "className": "h-full",
    "body": [{
      "type": "input-tree",
      "label": false,
      "searchable": true,
      "autoFillHeight": true,
      "name": "tree2",
      "multiple": false,
      "autoCheckChildren": false,
      "options": [
        {
          "label": "A",
          "value": "a"
        },
        {
          "label": "B",
          "value": "b",
          "children": [
            {
              "label": "B-1",
              "value": "b-1"
            },
            {
              "label": "B-2",
              "value": "b-2"
            },
            {
              "label": "B-3",
              "value": "b-3"
            }
          ]
        },
        {
          "label": "B",
          "value": "b",
          "children": [
            {
              "label": "B-1",
              "value": "b-1"
            },
            {
              "label": "B-2",
              "value": "b-2"
            },
            {
              "label": "B-3",
              "value": "b-3"
            }
          ]
        },
        {
          "label": "B",
          "value": "b",
          "children": [
            {
              "label": "B-1",
              "value": "b-1"
            },
            {
              "label": "B-2",
              "value": "b-2"
            },
            {
              "label": "B-3",
              "value": "b-3"
            }
          ]
        },
        {
          "label": "B",
          "value": "b",
          "children": [
            {
              "label": "B-1",
              "value": "b-1"
            },
            {
              "label": "B-2",
              "value": "b-2"
            },
            {
              "label": "B-3",
              "value": "b-3"
            }
          ]
        },
        {
          "label": "B",
          "value": "b",
          "children": [
            {
              "label": "B-1",
              "value": "b-1"
            },
            {
              "label": "B-2",
              "value": "b-2"
            },
            {
              "label": "B-3",
              "value": "b-3"
            }
          ]
        },
        {
          "label": "B",
          "value": "b",
          "children": [
            {
              "label": "B-1",
              "value": "b-1"
            },
            {
              "label": "B-2",
              "value": "b-2"
            },
            {
              "label": "B-3",
              "value": "b-3"
            }
          ]
        },
        {
          "label": "B",
          "value": "b",
          "children": [
            {
              "label": "B-1",
              "value": "b-1"
            },
            {
              "label": "B-2",
              "value": "b-2"
            },
            {
              "label": "B-3",
              "value": "b-3"
            }
          ]
        },
        {
          "label": "B",
          "value": "b",
          "children": [
            {
              "label": "B-1",
              "value": "b-1"
            },
            {
              "label": "B-2",
              "value": "b-2"
            },
            {
              "label": "B-3",
              "value": "b-3"
            }
          ]
        },
        {
          "label": "B",
          "value": "b",
          "children": [
            {
              "label": "B-1",
              "value": "b-1"
            },
            {
              "label": "B-2",
              "value": "b-2"
            },
            {
              "label": "B-3",
              "value": "b-3"
            }
          ]
        },
        {
          "label": "C",
          "value": "c"
        }
      ],
      "onEvent": {
        "change": {
          "actions": [
            {
              "actionType": "query",
              "componentId": "right-crud",
              "args": {
                "queryParams": {
                  "tree2": "${event.data.value}"
                }
              }
            }
          ]
        }
      },
    }]
    },
    "right": {
      "type": "crud",
      "syncLocation": false,
      "columnsTogglable": false,
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
      "id": "right-crud",
      "name": "right-crud",
      "autoGenerateFilter": {
        "showBtnToolbar": false,
        "defaultExpanded": false
      },
      "unsetQueryParams": ["tree2"],
      "columns": [
        {
          "name": "id",
          "label": "ID",
          "searchable": {
            "type": "input-text",
            "name": "id",
            "label": "主键",
            "placeholder": "输入id"
          }
        },
        {
          "name": "id",
          "label": "序号"
        },
        {
          "name": "engine",
          "label": "Rendering engine",
          "searchable": {
            "type": "input-text",
            "name": "engine",
            "label": "Rendering engine",
            "placeholder": "输入Rendering engine"
          }
        },
        {
          "name": "browser",
          "label": "Browser",
          "searchable": {
            "type": "input-text",
            "name": "browser",
            "label": "Browser",
            "placeholder": "输入Browser"
          }
        },
        {
          "name": "platform",
          "label": "Platform(s)"
        },
        {
          "name": "version",
          "label": "Engine version"
        },
        {
          "name": "grade",
          "label": "CSS grade"
        },
        {
          "type": "operation",
          "label": "操作 ",
          "buttons": [
            {
              "label": "详情",
              "type": "button",
              "level": "link",
              "actionType": "dialog",
              "dialog": {
                "title": "详情",
                "showCloseButton": false,
                "body": "这是个简单的弹框。"
              }
            },
            {
              "label": "删除",
              "type": "button",
              "actionType": "ajax",
              "level": "link",
              "disabled": true,
              "confirmText": "确认要删除吗？",
              "api": {
                "method": "delete",
                "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
              }
            },
            {
              "label": "编辑",
              "type": "button",
              "level": "link",
              "actionType": "dialog",
              "dialog": {
                "title": "编辑",
                "showCloseButton": false,
                "body": "这是个简单的弹框。"
              }
            },
            {
              "label": "空跑",
              "type": "button",
              "level": "link",
              "actionType": "dialog",
              "dialog": {
                "title": "空跑",
                "showCloseButton": false,
                "body": "这是个简单的弹框。"
              }
            }
          ]
        }
      ]
    }
  }
}

```

### 左右容器带标题

```schema
{
  "type": "page",
  "id": "pageId",
  "bodyClassName": "h-screen",
  "data": {
    "tree2": {
      "label": "数禾测试数据A",
      "value": "shuhe-test-data-a"
    },
    "text1": "shuhe-test-data-a"
  },
  "body": {
    "type": "left-right-container",
    "leftTitle": "左侧标题",
    "rightTitle": "右侧标题",
    "left": [
      {
      "type": "form",
      "labelWidth": 60,
      "body": [
        {
          "type": "group-container",
          "collapsible": true,
          "activeKey": ["0", "1"],
          "items": [
            {
              "type": "panel",
              "header": {
                "title": "基础信息"
              },
              "body": [
                {
                  "type": "input-text",
                  "name": "text-search1",
                  "label": "姓名"
                },
                {
                  "type": "input-text",
                  "name": "text-search2",
                  "label": "年龄"
                }
              ]
            },
            {
              "type": "panel",
              "header": {
                "title": "复杂信息"
              },
              "body": [
                {
                  "type": "input-text",
                  "name": "text-search3",
                  "label": "邮箱"
                },
                {
                  "type": "input-text",
                  "name": "text-search4",
                  "label": "电话"
                }
              ]
            }
          ]
        }
      ],
      "actions": [
        {
          "type": "button",
          "label": "重置"
        },
        {
          "type": "button",
          "level": "primary",
          "label": "查询"
        }
      ]
    }
    ],
    "right": [
      {
        "type": "crud",
        "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
        "columns": [
          {
            "name": "id",
            "label": "ID",
            "searchable": {
              "type": "input-text",
              "placeholder": "请输入"
            }
          },
          {
            "name": "engine",
            "label": "Rendering engine",
            "searchable": {
              "type": "input-text",
              "placeholder": "请输入"
            }
          },
          {
            "name": "browser",
            "label": "Browser",
            "searchable": {
              "type": "input-text",
              "placeholder": "请输入"
            }
          },
          {
            "name": "platform",
            "label": "Platform(s)"
          },
          {
            "name": "version",
            "label": "Engine version"
          },
          {
            "name": "grade",
            "label": "CSS grade"
          },
          {
            "type": "operation",
            "label": "操作",
            "width": 80,
            "buttons": [
              {
                "label": "详情",
                "type": "button",
                "level": "link",
                "actionType": "dialog",
                "dialog": {
                  "title": "查看详情",
                  "body": {
                    "type": "form",
                    "body": [
                      {
                        "type": "input-text",
                        "name": "engine",
                        "label": "Engine"
                      },
                      {
                        "type": "input-text",
                        "name": "browser",
                        "label": "Browser"
                      },
                      {
                        "type": "input-text",
                        "name": "platform",
                        "label": "platform"
                      },
                      {
                        "type": "input-text",
                        "name": "version",
                        "label": "version"
                      },
                      {
                        "type": "control",
                        "label": "grade",
                        "body": {
                          "type": "tag",
                          "label": "${grade}",
                          "displayMode": "normal",
                          "color": "active"
                        }
                      }
                    ]
                  }
                }
              },
              {
                "label": "删除",
                "type": "button",
                "level": "link",
                "disabledOn": "this.grade === 'A'"
              }
            ]
          }
        ]
      }
    ] 
  }
}
```

- 落地案例  
  [APP一站式-APP页面管理平台-埋点管理-埋点定义管理](http://moka.dmz.sit.caijj.net/clientsuiteui/#/trackEventManage/trackEventDetail?eventId=29798577-587c-4c6d-a245-ab2f3a8153a7)  
   ![APP一站式-APP页面管理平台-埋点管理-埋点定义管理](https://static02.sit.yxmarketing01.com/tdmaterial/28000c9a6cdb4d8c8396c92666db7e2e.png)

## 组件用法

### 左侧区域可拖拽

默认左侧可拖拽调整宽度，配置 `defaultWidth` 可设置左侧默认宽度，并可配合 `minWidth`,`maxWidth` 对左侧可拖拽宽度进行限制。（宽度设置，支持数字或者百分比）

```schema
{
  "type": "page",
  "body": {
    "type": "left-right-container",
    "defaultWidth": 300,
    "minWidth": 200,
    "maxWidth": 500,
    "left": {
      "type": "container",
      "body": "左侧区域"
    },
    "right": {
      "type": "container",
      "body": "右边区域"
    }
  }
}
```

### 左侧区域可折叠

> 1.86.1 及以上版本支持 `leftProps` 配置

通过设置`leftProps`的`collapsable`属性为`true`，左侧区域可折叠。**给按钮配置`closeResizable`和`openResizable`分别控制左侧区域折叠和展开。**

```schema
{
  "type": "page",
  "body": {
    "type": "left-right-container",
    "defaultWidth": 300,
    "minWidth": 100,
    "maxWidth": 600,
    "draggable": true,
    "leftProps": {
      "collapsable": true,
      "bodyClassName": "h-full overflow-y-auto"
    },
    "left": [
      {
        "type": "container",
        "className": "overflow-hidden", // 避免折叠后内容显示
        "body": [
          {
            "type": "title",
            "title": "我是标题",
            "className": "border-solid border-0 border-b border-gray-100",
            "actions": [
              {
                "type": "button",
                "body": {
                  "type": "icon",
                  "icon": "angle-double-left"
                },
                "closeResizable": true // 控制左侧区域折叠
              }
            ]
          },
          {
            "type": "form",
            "wrapWithPanel": false,
            "body": [
              {
                "type": "input-tree",
                "name": "tree",
                "searchable": false,
                "label": false,
                "options": [
                  {
                    "label": "Folder A",
                    "value": 1,
                    "children": [
                      {
                        "label": "file A",
                        "value": 2
                      },
                      {
                        "label": "Folder B",
                        "value": 3,
                        "children": [
                          {
                            "label": "file b1",
                            "value": 3.1
                          },
                          {
                            "label": "file b2",
                            "value": 3.2
                          }
                        ]
                      }
                    ]
                  },
                  {
                    "label": "file C",
                    "value": 4
                  },
                  {
                    "label": "file D",
                    "value": 5
                  }
                ]
              }
            ],
            "actions": []
          }
        ]
      },
      {
        "type": "button",
        "body": {
          "type": "icon",
          "icon": "angle-double-right"
        },
        "style": {
          "position": "absolute",
          "top": "0px",
          "right": "-15px"
        },
        "openResizable": true // 控制左侧区域展开
      }
    ],
    "right": {
      "type": "container",
      "body": "右边区域"
    }
  }
}
```

### 设置固定宽度

配置 `draggable:false` 关闭拖拽功能，则可固定宽度。固定窗口时，可配置 `leftWidth` 设置左侧宽度，或者配置 `rightWidth` 设置右侧宽度。（宽度设置，支持数字或者百分比）

```schema
{
  "type": "page",
  "body": {
    "type": "left-right-container",
    "draggable": false,
    "left": {
      "type": "container",
      "body": "左侧区域"
    },
    "right": {
      "type": "container",
      "body": "右边区域"
    }
  }
}
```

### 属性表

| 属性名    | 类型     | 默认值                  | 说明          | 版本 |
| --------- | -------- | ----------------------- | ------------- | ----- |
| className | `string` |                         | 容器外层 CSS 类名 |  |
| left      | `SchemaNode` |             | 左侧内容区域 |  |
| right     | `SchemaNode` |             | 右边侧内容区域 |  |
| draggable | `boolean`    | `true`      | 左侧区域，是否可拖拽设置宽度 |  |
| defaultWidth | `number \| string` |    | 左侧默认宽度，可配合 minWidth,maxWidth 对左侧区域拖动限制     | |
| minWidth     |  `number \| string` | `50px`  | 左侧区域可拖拽的最小宽度      | |
| maxWidth     | `number \| string` | `500px`  | 左侧区域可拖拽的最大宽度     | |
| leftWidth    | `number \| string` |          |  左侧宽度，仅在 `draggable:false`生效      | |
| rightWidth   | `number \| string` |          |  右侧宽度，仅在 `draggable:false`生效      | |
| leftTitle   | `string` |          |  左侧标题      | |
| rightTitle   | `string` |          |  右侧标题，白底场景会有下边框      | |
