---
title: Legend 图例
description:
type: 0
group: ⚙ 组件
menuName: Legend
icon:
order: 55
# standardMode: true
---

支持版本 1.77 版本

## 基本用法

```schema
{
  "type": "page",
  "body": [
    {
      "type": "legend",
      "options": [
        {
          "label": "legend1",
          "color": "#ff0000"
        },
        {
          "label": "legend2",
          "color": "#00ff00"
        },
        {
          "label": "legend3",
          "color": "#0000ff"
        }
      ]
    }
  ]
}
```

数据结构

```ts
interface LegendItem {
  color?: string;
  label?: string;
  tagMode?: 'tag' | 'line';
}
```

## 垂直布局

通过 `layout` 设置为 `vertical` 来让图例垂直布局。

```schema
{
  "type": "page",
  "body": [
    {
      "type": "legend",
      "layout": "vertical",
      "options": [
        {
          "label": "legend1",
          "color": "#ff0000"
        },
        {
          "label": "legend2",
          "color": "#00ff00"
        },
        {
          "label": "legend3",
          "color": "#0000ff"
        }
      ]
    }
  ]
}
```

## 获取数据域上数据

通过 `source` 属性可以获取数据域上数据。

```schema
{
  "type": "page",
  "data": {
    "legends": [
      {
        "label": "legend1",
        "color": "#ff0000"
      },
      {
        "label": "legend2",
        "color": "#00ff00"
      },
      {
        "label": "legend3",
        "color": "#0000ff"
      }
    ]
  },
  "body": [
    {
      "type": "legend",
      "source": "${legends}" 
    }
  ]
}
```
## label的颜色跟随标签

通过设置 `labelColorFollower: true` 可以让标签的颜色跟随标签。

```schema
{
  "type": "page",
  "body": [
    {
      "type": "legend",
      "labelColorFollower": true,
      "options": [
        {
          "label": "legend1",
          "color": "#ff0000"
        },
        {
          "label": "legend2",
          "color": "#00ff00"
        },
        {
          "label": "legend3",
          "color": "#0000ff"
        }
      ]
    }
  ]
}
```

## 标签模式

通过将 `tagMode` 修改为 `line` 可以将图例标签改为线模式。

```schema
{
  "type": "page",
  "body": [
    {
      "type": "legend",
      "tagMode": "line",
      "options": [
        {
          "label": "legend1",
          "color": "#ff0000"
        },
        {
          "label": "legend2",
          "color": "#00ff00"
        },
        {
          "label": "legend3",
          "color": "#0000ff"
        }
      ]
    }
  ]
}
```

数据上也可以配置 `tagMode`，优先级会比组件层的高

```schema
{
  "type": "page",
  "body": [
    {
      "type": "legend",
      "options": [
        {
          "label": "legend1",
          "color": "#ff0000"
        },
        {
          "label": "legend2",
          "color": "#00ff00"
        },
        {
          "label": "legend3",
          "color": "#0000ff",
          "tagMode": "line",
        }
      ]
    }
  ]
}
```

## 属性表

| 属性名 | 类型 | 默认值 | 说明 |
| ------ | ---- | ------ | ---- |
| options | array |   -    | 图例配置 |
| source | string |   -    | 获取数据域上数据 |
| layout | `horizontal \| vertical` |  `horizontal`  | 布局，默认为水平 |
| labelColorFollower | boolean |  false  | 标签颜色跟随标签 |
| tagMode | `line \| tag` |  `tag`  | 标签模式，默认为标签模式 |
| justifyContent | `flex-start \| flex-end \| center` |  `flex-start`  | 对齐方式，默认为左对齐，仅水平布局下有效 | 
| maxHeight | number |  -  | 最大高度，超出后会分页模式，仅垂直布局生效 |
