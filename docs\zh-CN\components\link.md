---
title: Link 链接
description:
type: 0
group: ⚙ 组件
menuName: Link
icon:
order: 55
---

## 基本用法

```schema
{
    "type": "page",
    "body": {
        "type": "link",
        "href": "https://www.baidu.com",
        "body": "百度一下，你就知道"
    }
}
```

## 新标签页打开

```schema
{
    "type": "page",
    "body": {
        "type": "link",
        "href": "https://www.baidu.com",
        "body": "百度一下，你就知道",
        "blank": true
    }
}
```

## 禁用超链接

```schema
{
    "type": "page",
    "body": {
        "type": "link",
        "href": "https://www.baidu.com",
        "body": "百度一下，你就知道",
        "blank": true,
        "disabled": true
    }
}
```

## 添加图标

```schema
{
    "type": "page",
    "body": {
        "type": "link",
        "href": "https://www.baidu.com",
        "body": "百度一下，你就知道",
        "blank": true,
        "icon": "fa fa-search"
    }
}
```

## 属性表

| 属性名     | 类型                                                                           | 默认值 | 说明                                                                                 |
| ---------- |------------------------------------------------------------------------------| ------ | ------------------------------------------------------------------------------------ |
| type       | `string` 或 [表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression) |        | 如果在 Table、Card 和 List 中，为`"link"`；在 Form 中用作静态展示，为`"static-link"` |
| body       | `string` 或 [表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression)                                                                  |        | 标签内文本                                                                           |
| href       | `string`                                                                     |        | 链接地址                                                                             |
| blank      | `boolean`                                                                    |        | 是否在新标签页打开                                                                   |
| htmlTarget | `string`                                                                     |        | a 标签的 target，优先于 blank 属性                                                   |
| title      | `string`                                                                     |        | a 标签的 title                                                                       |
| disabled   | `boolean`                                                                    |        | 禁用超链接                                                                           |
| icon       | `string`                                                                     |        | 超链接图标，以加强显示                                                               |
| rightIcon  | `string`                                                                     |        | 右侧图标                                                                             |
| emptyText  | `string`                                                                     |    -   | 当body、href和`"static-link"`状态下的value都为空时展示的占位内容(1.43.0及以后版本)                        |                                                                             |
