---
title: CRUD 增删改查
description:
type: 0
group: ⚙ 组件
menuName: 列表展示
icon:
order: 25
---

<!-- 合适的数据展示方式可以帮助用户快速地定位和浏览数据，以及更高效得协同工作。在设计时有以下几点需要注意：
 - 依据信息的重要等级、操作频率和关联程度来编排展示的顺序。
 - 注意极端情况下的引导，如数据信息过长，内容为空的初始化状态等。 -->

CRUD，即增删改查组件，主要用来展现数据列表，并支持各类【增】【删】【改】【查】等操作。

## 模块划分

![列表展示模块图](https://static02.sit.yxmarketing01.com/materialcenter/65341b15-e648-495f-a761-b8ef1707cf84.png)

1. 数据筛选区域（非必有）  
   包含了全局操作按钮（比如新增、导入等主流程操作类按钮，超出 5 个即纳入更多下拉中），对于搜索项个数超出 6 个，会出现在高级搜索中做展开收起展示。

2. 辅助操作区域（非必有）  
   对于批量操作、单个搜索、拖拽排序、模式切换、对于表格模式中的表头列搜索、列展示筛选等辅助操作区域，并支持多功能组合出现。

3. 数据展示区域（必有）  
   展示方式支持多样化，常推荐表格模式和卡片模式。

4. 底部区域（非必有）  
   底部区域包含总结行和分页，左右分布。  
   对于大量数据，分批展示更利于用户快速定位和浏览目标数据。

## 场景推荐

### Table 表格模式

Table 模式支持 [Table](/dataseeddesigndocui/#/amis/zh-CN/components/table) 中的所有功能。  
这个模式下会默认关闭固定表头功能，如果需要可以使用 `"affixHeader": true` 打开。

```schema
{
  "type": "page",
  "data": {
    "button-group-select": "all",
    "dataEnum": {
      "init": "初始化",
      "register": "已注册",
      "off": "已注销",
      "running": "试运行中"
    },
    "colorEnum": {
      "init": "active",
      "register": "success",
      "off": "inactive",
      "running": "running"
    },
    "tagInfo": {
      "string": "dfadsafdsafdsadsafddsafsa",
      "chinese": "代发价法搜丰富的撒旦法发撒发的",
      "number": "23143214321423143214321432143",
      "mixture": "dsaf这里13"
    },
  },
  "body": {
    "debug": true,
    "type": "crud",
    "api": {
      "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4",
      "adaptor": "return { \n count: payload.data.count \n data: payload.data.rows.map(item => { let currentStatus = item.status \n delete item.status \n return { ...item,\n currentStatus \n} \n}) \n}"
    },
    "footerToolbar": [
      {
        "type": "tpl",
        "tpl": "统计金额：¥200 元、统计数量：<strong>100</strong> 件",
        "toolbarClassName": "flex-1"
      },
      {
        "type": "pagination",
        "maxButtons": 2,
        "layout": "total,pager,perPage"
      }
    ],
    "topToolbar": [
      {
        "type": "button",
        "label": "主按钮",
        "actionType": "url",
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
        "level": "primary",
        "blank": false
      },
      {
        "type": "button",
        "label": "次按钮1",
        "actionType": "url",
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
      },
      {
        "type": "button",
        "label": "次按钮2",
        "actionType": "url",
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
      },
      {
        "type": "button",
        "label": "次按钮3",
        "actionType": "url",
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
      },
      {
        "type": "button",
        "label": "次按钮4",
        "disabled": true
      },
      {
        "type": "button",
        "label": "次按钮5"
      },
      {
        "type": "button",
        "label": "次按钮6"
      }
    ],
    "headerFilter": {
      "body": [
        {
          "type": "button-group-select",
          "name": "button-group-select",
          "align": "right",
          "options": [
            {
              "value": "all",
              "label": "查询全部"
            },
            {
              "value": "forme",
              "label": "待我审核"
            },
            {
              "value": "reject",
              "label": "已拒绝"
            }
          ],
          "onEvent": {
            "change": {
              "actions": [
                {
                  "actionType": "query",
                  "componentId": "custom-crud-id",
                  "args": {
                    "queryParams": {
                      "button-group-select": "${button-group-select}"
                    }
                  }
                }
              ]
            }
          }
        }
      ]
    },
    "bulkActions": [
      {
        "label": "批量删除",
        "actionType": "ajax",
        "api": "delete:https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample/${ids|raw}",
        "confirmText": "确定要批量删除?"
      },
      {
        "label": "批量修改",
        "actionType": "dialog",
        "dialog": {
          "title": "批量编辑",
          "showCloseButton": false,
          "body": {
            "type": "form",
            "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample/bulkUpdate2",
            "body": [
              {
                "type": "hidden",
                "name": "ids"
              },
              {
                "type": "input-text",
                "name": "engine",
                "label": "Engine"
              }
            ]
          }
        }
      }
    ],
    "columns": [
      {
        "name": "id",
        "label": "ID",
        "searchable": {
          "type": "input-text",
          "placeholder": "请输入"
        }
      },
      {
        "name": "browser",
        "label": "名称",
        "searchable": {
          "type": "input-text",
          "placeholder": "请输入"
        },
        "remark": "这是一段提示",
        "type": "flex",
        "gap": true,
        "items": [
          {
            "type": "tpl",
            "tpl": "${browser}"
          },
          {
            "type": "tooltip-wrapper",
            "content": "点击复制",
            "body": {
              "type": "icon",
              "icon": "copy",
              "onEvent": {
                "click": {
                  "actions": [
                    {
                      "actionType": "copy",
                      "args": {
                        "content": "${browser}"
                      }
                    }
                  ]
                }
              }
            }
          }
        ]
      },
      {
        "name": "version",
        "label": "版本",
        "headSearchable": true,
        "searchable": {
          "type": "input-text",
          "placeholder": "请输入"
        },
        "type": "container",
        "body": [
          {
            "type": "flex",
            "gap": true,
            "items": [
              {
                "type": "tpl",
                "tpl": "${version}"
              },
              {
                "type": "remark",
                "content": "这是一段提醒",
                "visibleOn": "${version}"
              },
            ]
          }
        ]
      },
      {
        "name": "grade",
        "label": "更新时间",
        "format": "YYYY-MM-DD HH:mm:ss",
        "valueFormat": "x",
        "type": "date",
        "headSearchable": true
      },
      {
        "type": "wrapper",
        "size": "none",
        "label": "状态",
        "body": [
          {
            "type": "tooltip-wrapper",
            "isPopOver": true,
            "tooltipTheme": "light",
            "trigger": "click",
            "title": "详情",
            "placement": "right",
            "size": "sm",
            "body": {
              "type": "tag",
              "displayMode": "bordered",
              "visibleOn": "${currentStatus || status}",
              "label": "${dataEnum[currentStatus  || status]}",
              "color": "${colorEnum[currentStatus  || status]}",
              "description": {
                "type": "link",
                "href": "https://www.baidu.com",
                "body": "注册审批中",
                "blank": true,
                "visibleOn": "${currentStatus ===`init`  || status ===`init`}"
              }
            },
            "content": {
              "type": "form",
              "mode": "horizontal",
              "static": true,
              "actions": [],
              "title": "",
              "body": [
                {
                  "type": "group-container",
                  "collapsible": false,
                  "items": [
                    {
                      "header": {
                        "title": "第一步，基础信息"
                      },
                      "body": [
                        {
                          "name": "text1",
                          "type": "static",
                          "label": "静态展示",
                          "quickEdit": {
                            "type": "input-text"
                          }
                        },
                        {
                          "type": "static",
                          "name": "text2",
                          "label": "年龄"
                        }
                      ]
                    },
                    {
                      "header": {
                        "title": "第二步，复杂信息"
                      },
                      "body": [
                        {
                          "type": "static",
                          "name": "second1",
                          "label": "邮箱"
                        },
                        {
                          "type": "static",
                          "name": "second2",
                          "label": "电话"
                        }
                      ]
                    },
                    {
                      "header": {
                        "title": "第二步，复杂信息"
                      },
                      "title": {
                        "type": "tpl",
                        "tpl": "第三步，策略信息",
                      },
                      "body": [
                        {
                          "type": "static",
                          "name": "second1",
                          "label": "邮箱"
                        },
                        {
                          "type": "static",
                          "name": "second2",
                          "label": "电话"
                        }
                      ]
                    }
                  ]
                }
              ]
            },
          },
        ]
      },
      {
        "label": "标签",
        "type": "tags",
        "width": "5%",
        "direction": "vertical",
        "items": [
          {
            "type": "tooltip-wrapper",
            "placement": "right",
            "content": "${SPLIT(tagInfo[string])}",
            "body": {
              "type": "tag",
              "displayMode": "bordered",
              "label": "${tagInfo[string]}",
              "color": "${colorEnum[init]}"
            },
          },
          {
            "type": "tooltip-wrapper",
            "placement": "right",
            "content": "${SPLIT(tagInfo[chinese])}",
            "body": {
              "type": "tag",
              "displayMode": "bordered",
              "label": "${tagInfo[chinese]}",
              "color": "${colorEnum[init]}"
            },
          },
          {
            "type": "tooltip-wrapper",
            "placement": "right",
            "content": "${SPLIT(tagInfo[number])}",
            "body": {
              "type": "tag",
              "displayMode": "bordered",
              "label": "${tagInfo[number]}",
              "color": "${colorEnum[init]}"
            },
          },
          {
            "type": "tooltip-wrapper",
            "placement": "right",
            "content": "${SPLIT(tagInfo[mixture])}",
            "body": {
              "type": "tag",
              "displayMode": "bordered",
              "label": "${tagInfo[mixture]}",
              "color": "${colorEnum[init]}"
            },
          }
        ]
      },
      {
        "type": "tooltip-wrapper",
        "tooltipTheme": "light",
        "label": "进度",
        "placement": "bottom",
        "content": {
          "type": "steps",
          "mode": "vertical",
          "value": 2,
          "steps": [
            {
              "title": "查分加工完成",
              "value": "finish"
            },
            {
              "title": "查分加工完成",
              "value": "finish"
            },
            {
              "title": "溶合加工完成",
              "value": "finish"
            },
            {
              "title": "数据融合完成",
            }
          ]
        },
        "inline": true,
        "body": [
          {
            "type": "progress",
            "mode": "circle",
            "label": "项目进度",
            "value": 66,
            "valueTpl": "2/3",
            "strokeWidth": 5
          },
        ]
      },
      {
        "name": "textContent",
        "label": "文案内容",
        "type": "typography",
        "width": 150,
        "ellipsis": {
          "rows": 2
        }
      },
       {
      "label": "超链接",
      "ellipsis": {
        "rows": 2,
        "tooltip": "${textContent}"
      },
      "type": "typography",
      "text": {
        "type": "link",
        "href": "https://www.baidu.com/",
        "body": "${textContent}"
      }
    },
      {
        "name": "differenceRate",
        "label": "差异值",
        "type": "container",
        "sortable": true,
        "body": {
          "type": "tpl",
          "tpl": "<span style='color: ${differenceRate > 69 ? 'red' : 'black'}'>${differenceRate}</span>"
        },
        "searchable": {
          "type": "between",
          "label": "差异值",
          "separatorStr": "~",
          "labelRemark": {
            "type": "remark",
            "content": "这是一个提示"
          },
          "items": [
            {
              "type": "input-text",
              "name": "groupfix",
              "placeholder": "请输入"
            },
            {
              "type": "input-text",
              "name": "groupfix2",
              "placeholder": "请输入"
            }
          ]
        }
      },
      {
        "type": "operation",
        "label": "操作",
        "width": 80,
        "buttons": [
          {
            "label": "详情",
            "type": "button",
            "level": "link",
            "rightIcon": "remark",
            "rightIconTooltip": "查看数据详情",
            "rightIconClassName": "pm-text-muted",
            "actionType": "dialog",
            "dialog": {
              "title": "查看详情",
              "body": {
                "type": "form",
                "body": [
                  {
                    "type": "input-text",
                    "name": "engine",
                    "label": "Engine"
                  },
                  {
                    "type": "input-text",
                    "name": "browser",
                    "label": "Browser"
                  },
                  {
                    "type": "input-text",
                    "name": "platform",
                    "label": "platform"
                  },
                  {
                    "type": "input-text",
                    "name": "version",
                    "label": "version"
                  },
                  {
                    "type": "control",
                    "label": "grade",
                    "body": {
                      "type": "tag",
                      "label": "${grade}",
                      "displayMode": "normal",
                      "color": "active"
                    }
                  }
                ]
              }
            }
          },
          {
            "label": "删除",
            "type": "button",
            "level": "link",
            "disabledOn": "this.grade === 'A'"
          }
        ]
      }
    ]
  }
}
```

- 落地案例  
  [获客平台（主营）-RTA服务-建模管理-建模项目管理-执行计划tab](http://moka.dmz.sit.caijj.net/tdrtaui/#/modelingManage/projectManage/projectDetail?projectUuid=c88fb44e-80db-40c0-9d4e-0552ff7dd011&projectId=3&tabIndex=tab1)  
   ![获客平台（主营）-RTA服务-建模管理-建模项目管理-执行计划tab](https://static02.sit.yxmarketing01.com/tdmaterial/2eec84bbd8e14e7483968543864d00d6.png)

### Cards 卡片模式

Cards 模式支持 [Cards](/dataseeddesigndocui/#/amis/zh-CN/components/cards) 中的所有功能。默认情况下，一行一个 Card。

```schema
{
  "type": "page",
  "body": {
    "filter": {
      "body": [
        {
          "type": "group",
          "mode": "horizontal",
          "body": [
            {
              "type": "input-text",
              "name": "keywords",
              "label": "关键字",
              "clearable": true,
              "placeholder": "通过关键字搜索",
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "engine",
              "label": "Engine",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "platform",
              "label": "Platform",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "keywords1",
              "label": "关键字1",
              "clearable": true,
              "placeholder": "通过关键字搜索",
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "engine1",
              "label": "Engine1",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "platform1",
              "label": "Platform1",
              "clearable": true,
              "columnRatio": 4
            }
          ]
        }
      ],
      "actions": [
        {
          "type": "reset",
          "label": "重 置"
        },
        {
          "type": "submit",
          "level": "primary",
          "label": "查 询"
        }
      ]
    },
    "type": "crud",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/users",
    "syncLocation": false,
    "mode": "cards",
    "defaultParams": {
      "perPage": 6
    },
    "switchPerPage": false,
    "placeholder": "没有用户信息",
    "card": {
      "title": {
        "type": "title",
        "title": "${realName}",
        "subTitle": "这是一个小标题",
        "assistContent": [
          {
            "type": "tag",
            "label": "普通标签",
            "color": "processing",
          }
        ],
        "actions": [
          {
            "type": "button",
            "linkWithoutPadding": true,
            "level": "link",
            "label": "",
            "icon": "fa fa-star-o",
            "vendor": "",
            "visibleOn": "${id > 2}"
          },
          {
            "type": "button",
            "linkWithoutPadding": true,
            "level": "link",
            "label": "",
            "icon": "fa fa-star",
            "vendor": "",
            "visibleOn": "${id <= 2}"
          },
          {
            "label": "详情",
            "type": "button",
            "level": "link",
            "linkWithoutPadding": true,
            "actionType": "dialog",
            "dialog": {
              "title": "弹框1",
              "body": "这是个简单的弹框。"
            },
          },
          {
            "label": "注销",
            "type": "button",
            "level": "link",
            "linkWithoutPadding": true,
            "actionType": "dialog",
            "dialog": {
              "title": "弹框2",
              "body": "这是个简单的弹框。"
            }
          }
        ]
      },
      "body": {
        "mode": "horizontal",
        "title": "",
        "type": "form",
        "id": "targetFormId",
        "labelWidth": 40,
        "actions": [],
        "body": [
          {
            "type": "group",
            "withoutMarginBottom": true,
            "body": [
              {
                "name": "realName",
                "type": "static",
                "label": "姓名"
              },
              {
                "type": "static",
                "name": "name",
                "label": "name"
              },
              {
                "type": "static",
                "name": "email",
                "label": "邮箱"
              }
            ]
          },
          {
            "type": "group",
            "mode": "horizontal",
            "withoutMarginBottom": true,
            "body": [
              {
                "type": "static",
                "name": "id",
                "label": "地址",
                "columnRatio": 12
              }
            ]
          },
          {
            "type": "group",
            "mode": "horizontal",
            "withoutMarginBottom": true,
            "body": [
              {
                "type": "static",
                "name": "email",
                "label": "其它",
                "columnRatio": 12
              }
            ]
          }
        ]
      }
    }
  }
}
```

一行多个 Cards

```schema
{
  "type": "page",
  "body": {
    "type": "crud",
    "mode": "cards",
    "syncLocation": false,
    "columnsTogglable": false,
    "headerToolbar": [
     {
      "type": "search-box",
      "name": "name",
      "size": "lg",
      "placeholder": "请输入智能体名称查询",
    }
   ],
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/users",
    "columnsCount": 3,
    "card": {
      "header": {
        "title": {
          "type": "typography",
          "text": "${realName}"
        },
        "subTitle": {
          "type": "typography",
          "text": "${email}"
        },
        "description": {
          "type": "typography",
          "text": "${email}"
        },
        "avatarShape": "rounded",
        "avatar": "${avatar}"
      },
      "toolbar": [
        {
          "type": "flex",
          "gap": true,
          "items": [
            {
              "type": "button",
              "label": false,
              "level": "link",
              "icon": "fa fa-star",
              "visibleOn": "${index<=2}"
            },
            {
              "type": "button",
              "label": false,
              "level": "link",
              "icon": "fa fa-star-o",
              "visibleOn": "${index>2}"
            }
          ]
        }
      ],
      "actions": [
        {
          "type": "wrapper",
          "body": [
            {
              "type": "flex",
              "gap": true,
              "alignItems": "center",
              "items": [
                {
                  "type": "icon",
                  "icon": "star-regular"
                },
                {
                  "type": "tpl",
                  "tpl": "2"
                }
              ]
            }
          ]
        }
      ]
    }
  }
}
```

- 落地案例  
  [产研一站式平台-智能平台-智能体广场](http://moka.dmz.sit.caijj.net/aiassistantui/#/agent/square?_shMenuId=tenant_menu_P0304_agent_square_m54rizzb)  
   ![产研一站式平台-智能平台-智能体广场](https://static02.sit.yxmarketing01.com/materialcenter/6d19c9d7-c0e2-4787-bb14-410408c5d2f6.png)


### 嵌套子表格

```schema
{
  "type": "page",
  "body": {
    "type": "crud",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
    "syncLocation": false,
    "id": "tableId",
    "showExpansionColumn": false,
    "columns": [
      {
        "name": "id",
        "label": "ID"
      },
      {
        "name": "engine",
        "label": "Rendering engine"
      },
      {
        "name": "browser",
        "label": "Browser"
      },
      {
        "name": "platform",
        "label": "Platform(s)"
      },
      {
        "name": "version",
        "label": "Engine version"
      },
      {
        "name": "grade",
        "label": "CSS grade"
      },
      {
        "type": "operation",
        "label": "操作",
        "width": 80,
        "buttons": [
          {
            "type": "button",
            "label": "${_amisExpanded ? '收起' : '展开'}",
            "level": "link",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "actionType": "toggleExpanded",
                    "componentId": "tableId",
                    "args": {
                      "condition": "${id === currentId}",
                      "currentId": "${id}"
                    }
                  }
                ]
              }
            }
          }
        ]
      }
    ],
    "subTable": {
      "type": "crud",
      "footerToolbar": [
        {
          "type": "pagination",
          "maxButtons": 5,
          "layout": "total,pager,perPage"
        }
      ],
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
      "columns": [
        {
          "name": "engine",
          "label": "Engine"
        },
        {
          "name": "grade",
          "label": "Grade"
        },
        {
          "name": "_rowSubTableId",
          "label": "行子表格id"
        }
      ],
    }
  }
}
```

- 落地案例  
  [获客猎鹰平台-RTA服务-建模管理-待查任务管理](http://moka.dmz.sit.caijj.net/tdrtaui/#/modelingManage/processList?_shMenuId=menu_P0039_daicha)  
   ![获客猎鹰平台-RTA服务-建模管理-待查任务管理](https://static02.sit.yxmarketing01.com/tdmaterial/140734c6fcaa44e8bd233c54962945e0.png)


### 表格区带tab 标签
```schema
{
  "type": "page",
  "body": {
    "type": "crud",
    "syncLocation": false,
    "columnsTogglable": false,
    "mode": "tabs",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table5",
    "tabs": [
      {
        "title": "列表 1",
        "tab": {
          "type": "crud",
          "syncLocation": false,
          "columnsTogglable": false,
          "footerToolbar": [
            {
              "type": "pagination",
              "layout": "total,pager,perPage,go"
            }
          ],
          "source": "${list2}",
          "columns": [
            {
              "name": "id",
              "label": "ID"
            },
            {
              "name": "engine",
              "label": "Rendering engine"
            },
            {
              "name": "browser",
              "label": "Browser"
            },
            {
              "name": "platform",
              "label": "Platform(s)"
            },
            {
              "name": "version",
              "label": "Engine version"
            },
            {
              "name": "grade",
              "label": "CSS grade"
            },
            {
              "type": "operation",
              "label": "操作",
              "buttons": [
                {
                  "label": "详情",
                  "type": "button",
                  "level": "link",
                  "actionType": "dialog",
                  "dialog": {
                    "title": "详情",
                    "showCloseButton": false,
                    "body": "这是个简单的弹框。"
                  }
                },
                {
                  "label": "删除",
                  "type": "button",
                  "actionType": "ajax",
                  "level": "link",
                  "disabled": true,
                  "confirmText": "确认要删除吗？",
                  "api": {
                    "method": "delete",
                    "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
                  }
                },
                {
                  "label": "编辑",
                  "type": "button",
                  "level": "link",
                  "actionType": "dialog",
                  "dialog": {
                    "title": "弹框1",
                    "showCloseButton": false,
                    "body": "这是个简单的弹框。"
                  }
                },
                {
                  "label": "空跑",
                  "type": "button",
                  "level": "link",
                  "actionType": "dialog",
                  "dialog": {
                    "title": "弹框2",
                    "showCloseButton": false,
                    "body": "这是个简单的弹框。"
                  }
                }
              ]
            }
          ]
        }
      },
      {
        "title": "列表 2",
        "tab": {
          "type": "crud",
          "syncLocation": false,
          "columnsTogglable": false,
          "footerToolbar": [
            {
              "type": "pagination",
              "layout": "total,pager,perPage,go"
            }
          ],
          "source": "${list1}",
          "columns": [
            {
              "name": "id",
              "label": "ID"
            },
            {
              "name": "engine",
              "label": "Rendering engine"
            },
            {
              "name": "browser",
              "label": "Browser"
            },
            {
              "name": "platform",
              "label": "Platform(s)"
            },
            {
              "name": "version",
              "label": "Engine version"
            },
            {
              "name": "grade",
              "label": "CSS grade"
            },
            {
              "type": "operation",
              "label": "操作",
              "buttons": [
                {
                  "label": "详情",
                  "type": "button",
                  "level": "link",
                  "actionType": "dialog",
                  "dialog": {
                    "title": "详情",
                    "showCloseButton": false,
                    "body": "这是个简单的弹框。"
                  }
                },
                {
                  "label": "删除",
                  "type": "button",
                  "actionType": "ajax",
                  "level": "link",
                  "disabled": true,
                  "confirmText": "确认要删除吗？",
                  "api": {
                    "method": "delete",
                    "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
                  }
                },
                {
                  "label": "编辑",
                  "type": "button",
                  "level": "link",
                  "actionType": "dialog",
                  "dialog": {
                    "title": "弹框1",
                    "showCloseButton": false,
                    "body": "这是个简单的弹框。"
                  }
                },
                {
                  "label": "空跑",
                  "type": "button",
                  "level": "link",
                  "actionType": "dialog",
                  "dialog": {
                    "title": "弹框2",
                    "showCloseButton": false,
                    "body": "这是个简单的弹框。"
                  }
                }
              ]
            }
          ]
        }
      },
      {
        "title": "列表 3",
        "tab": {
          "type": "crud",
          "syncLocation": false,
          "columnsTogglable": false,
          "footerToolbar": [
            {
              "type": "pagination",
              "layout": "total,pager,perPage,go"
            }
          ],
          "source": "${list3}",
          "columns": [
            {
              "name": "id",
              "label": "ID"
            },
            {
              "name": "engine",
              "label": "Rendering engine"
            },
            {
              "name": "browser",
              "label": "Browser"
            },
            {
              "name": "platform",
              "label": "Platform(s)"
            },
            {
              "name": "version",
              "label": "Engine version"
            },
            {
              "name": "grade",
              "label": "CSS grade"
            },
            {
              "type": "operation",
              "label": "操作",
              "buttons": [
                {
                  "label": "详情",
                  "type": "button",
                  "level": "link",
                  "actionType": "dialog",
                  "dialog": {
                    "title": "详情",
                    "showCloseButton": false,
                    "body": "这是个简单的弹框。"
                  }
                },
                {
                  "label": "删除",
                  "type": "button",
                  "actionType": "ajax",
                  "level": "link",
                  "disabled": true,
                  "confirmText": "确认要删除吗？",
                  "api": {
                    "method": "delete",
                    "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
                  }
                },
                {
                  "label": "编辑",
                  "type": "button",
                  "level": "link",
                  "actionType": "dialog",
                  "dialog": {
                    "title": "弹框1",
                    "showCloseButton": false,
                    "body": "这是个简单的弹框。"
                  }
                },
                {
                  "label": "空跑",
                  "type": "button",
                  "level": "link",
                  "actionType": "dialog",
                  "dialog": {
                    "title": "弹框2",
                    "showCloseButton": false,
                    "body": "这是个简单的弹框。"
                  }
                }
              ]
            }
          ]
        }
      }
    ],
    "filter": {
      "title": "",
      "body": [
        {
          "type": "group",
          "mode": "horizontal",
          "body": [
            {
              "type": "input-text",
              "name": "keywords",
              "label": "关键字",
              "clearable": true,
              "placeholder": "通过关键字搜索",
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "engine",
              "label": "Engine",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "platform",
              "label": "Platform",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "keywords1",
              "label": "关键字1",
              "clearable": true,
              "placeholder": "通过关键字搜索",
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "engine1",
              "label": "Engine1",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "platform1",
              "label": "Platform1",
              "clearable": true,
              "columnRatio": 4
            }
          ]
        }
      ],
      "actions": [
        {
          "type": "reset",
          "label": "重 置"
        },
        {
          "type": "submit",
          "level": "primary",
          "label": "查 询"
        }
      ]
    }
  }
}
```

### 版本对比

期望对比数据区分维度分组展示时，可参考[分组容器-数据多维度分组版本对比](/dataseeddesigndocui/#/amis/zh-CN/components/group-container)

#### 前后值对比

期望以具体字段为维度，看到两个版本的前后值对比，可以参考该场景

```schema
{
  "type": "page",
  "data": {
    "tableData": [
      {
        "fieldName": "API ID",
        "baselineVersion": "12345",
        "diffVersion": "12345"
      },
      {
        "fieldName": "API名称",
        "baselineVersion": "查询用户列表",
        "diffVersion": "查询用户列表"
      },
      {
        "fieldName": "API状态",
        "baselineVersion": "在线",
        "diffVersion": "在线"
      },
      {
        "fieldName": "Method",
        "baselineVersion": "GET",
        "diffVersion": "POST",
        "status": "EDIT"
      },
      {
        "fieldName": "URL",
        "baselineVersion": "/users",
        "diffVersion": "/user-list",
        "status": "EDIT"
      },
      {
        "fieldName": "描述",
        "baselineVersion": "",
        "diffVersion": "查询用户列表",
        "status": "ADD"
      },
      {
        "fieldName": "补充",
        "baselineVersion": "查询用户列表",
        "diffVersion": "查询用户列表",
        "status": "DELETE"
      }
    ]
  },
  "body": {
    "type": "wrapper",
    "bgColor": "white",
    "body": {
      "type": "flex",
      "direction": "column",
      "gap": true,
      "items": [
        {
          "type": "tags",
          "items": [
            {
              "type": "tag",
              "label": "新增",
              "displayMode": "bordered",
              "color": "add-status"
            },
            {
              "type": "tag",
              "label": "修改",
              "displayMode": "bordered",
              "color": "edit-status"
            },
            {
              "type": "tag",
              "label": "删除",
              "displayMode": "bordered",
              "color": "delete-status"
            }
          ]
        },
        {
          "type": "form",
          "title": "",
          "actions": [],
          "mode": "horizontal",
          "wrapWithPanel": false,
          "labelWidth": 80,
          "body": [
            {
              "type": "between",
              "label": "基准版本",
              "labelRemark": {
                "type": "remark",
                "content": "这是一个提示"
              },
              "items": [
                {
                  "type": "select",
                  "label": "基准版本",
                  "name": "baselineVersion",
                  "value": "v1",
                  "options": [
                    {
                      "label": "V1",
                      "value": "v1"
                    },
                    {
                      "label": "V2",
                      "value": "v2"
                    }
                  ]
                },
                {
                  "type": "select",
                  "label": "对比版本",
                  "name": "diffVersion",
                  "value": "v2",
                  "options": [
                    {
                      "label": "V1",
                      "value": "v1"
                    },
                    {
                      "label": "V2",
                      "value": "v2"
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          "type": "crud",
          "syncLocation": false,
          "columnsTogglable": false,
          "autoGenerateFilter": {
            "showBtnToolbar": false,
            "defaultExpanded": false
          },
          "footerToolbar": [],
          "columns": [
            {
              "name": "fieldName",
              "label": "字段名"
            },
            {
              "name": "baselineVersion",
              "label": "基准版本"
            },
            {
              "name": "diffVersion",
              "label": "对比版本",
              "type": "tpl",
              "tpl": "<span class='${status === 'ADD' ? 'pm-versionDiff-add' : (status === 'EDIT' ? 'pm-versionDiff-edit' : (status === 'DELETE' ? 'pm-versionDiff-delete' : ''))}'>${diffVersion}</span>"
            }
          ],
          "source": "${tableData}"
        }
      ]
    }
  }
}
```



#### 多条数据固定字段对比

如果需要对比多条数据，并且固定某几个字段的前后值对比，可以参考该场景

```schema
{
  "type": "page",
  "body": {
    "type": "crud",
    "syncLocation": false,
    "columnsTogglable": false,
    "autoGenerateFilter": {
      "showBtnToolbar": false,
      "defaultExpanded": false
    },
    "footerToolbar": [
      {
        "type": "pagination",
        "layout": "total,pager,perPage,go"
      }
    ],
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table7",
    "columns": [
      {
        "name": "engine",
        "label": "Engine",
        "align": "left"
      },
      {
        "name": "grade",
        "label": "Grade",
        "align": "left"
      },
      {
        "name": "versionBefore",
        "label": "修改前",
        "groupName": "版本",
        "align": "left"
      },
      {
        "name": "versionAfter",
        "label": "修改后",
        "groupName": "版本",
        "type": "group",
        "direction": "vertical",
        "align": "left",
        "body": [
          {
            "type": "tpl",
            "tpl": "<span class='${versionAfterStatus === 'ADD' ? 'pm-versionDiff-add' : (versionAfterStatus === 'EDIT' ? 'pm-versionDiff-edit' : (versionAfterStatus === 'DELETE' ? 'pm-versionDiff-delete' : ''))}'>${versionAfter}</span>"
          }
        ]
      },
      {
        "name": "browserBefore",
        "label": "修改前",
        "groupName": "浏览器",
        "align": "left"
      },
      {
        "name": "browserAfter",
        "label": "修改后",
        "groupName": "浏览器",
        "align": "left",
        "type": "group",
        "direction": "vertical",
        "body": [
          {
            "type": "tpl",
            "tpl": "<span class='${browserAfterStatus === 'ADD' ? 'pm-versionDiff-add' : (browserAfterStatus === 'EDIT' ? 'pm-versionDiff-edit' : (browserAfterStatus === 'DELETE' ? 'pm-versionDiff-delete' : ''))}'>${browserAfter}</span>"
          }
        ]
      }
    ]
  }
}
```

- 落地案例  
  [客户中心_新-客户信息管理-客户信息模板](http://moka.dmz.sit.caijj.net/customercenterui/#/customer-templates?_shMenuId=tenant_menu_P0291_7689789787909989867766_lw7jf0x6)  
   ![客户中心_新-客户信息管理-客户信息模板](https://static02.sit.yxmarketing01.com/tdmaterial/c493d25d2ae84c47972bd24f5791aa8a.png)

#### 多条数据对比

如果需要对比多条数据，期望看到整条数据的变更状态是新增、删除还是修改，或者不确定会修改哪些字段，可以参考该场景

```schema
{
  "type": "page",
  "data": {
    "queryList": [
      {
        "id": 1,
        "changeType": "",
        "key-version1": "k1",
        "key-version2": "k1",
        "keyStatus": "",
        "id-version1": "userId",
        "id-version2": "userId",
        "idStatus": "",
        "text1Status": "",
        "text4Status": "",
        "text5Status": "",
        "text1-version1": "string",
        "text1-version2": "string",
        "text4-version1": "是",
        "text4-version2": "是",
        "text5-version1": "用户Id",
        "text5-version2": "用户Id",
        "grade": "ABCD",
        "grade1": "ABCD",
        "grade2": "B",
        "grade3": "C",
        "gradeStatus": "inactive",
        "grade1Status": "inactive",
        "grade2Status": "inactive",
        "grade3Status": "inactive"
      },
      {
        "id": 2,
        "changeType": "增",
        "key-version1": "-",
        "key-version2": "k2",
        "keyStatus": "ADD",
        "id-version1": "",
        "id-version2": "userType",
        "idStatus": "ADD",
        "text1Status": "ADD",
        "text4Status": "ADD",
        "text5Status": "ADD",
        "text1-version1": "",
        "text1-version2": "string",
        "text4-version1": "",
        "text4-version2": "是",
        "text5-version1": "",
        "text5-version2": "用户类型",
        "grade": "A这里有更多撒金凤卡少女反馈加啊是南非进口三反馈加三分快三方面小美女你发怒发怒发怒",
        "grade1": "1232178346816421641326426147126473214143214",
        "grade2": "sadkjhsakdfhskahfkashfdksahfkasdksafkadsfkasf",
        "grade3": "sadkjhsakdfhskahfkashfdksahfkasdksafkadsfkasf",
        "gradeStatus": "success",
        "grade1Status": "success",
        "grade2Status": "success",
        "grade3Status": "success"
      },
      {
        "id": 3,
        "changeType": "改",
        "key-version1": "k3",
        "key-version2": "k3",
        "keyStatus": "",
        "id-version1": "userId",
        "id-version2": "userId",
        "idStatus": "",
        "text1Status": "EDIT",
        "text4Status": "",
        "text5Status": "",
        "text1-version1": "string",
        "text1-version2": "integer",
        "text4-version1": "是",
        "text4-version2": "是",
        "text5-version1": "用户Id",
        "text5-version2": "用户Id",
        "grade": "这里有更多撒金凤卡少女反馈加啊是南非进口三反馈加三分快三方面小美女你发怒发怒发怒",
        "grade1": "1232178346816421641326426147126473214143214",
        "grade2": "sadkjhsakdfhskahfkashfdksahfkasdksafkadsfkasf",
        "grade3": "23914729310",
        "gradeStatus": "error",
        "grade1Status": "error",
        "grade2Status": "success",
        "grade3Status": "success"
      },
      {
        "id": 4,
        "changeType": "删",
        "key-version1": "k4",
        "key-version2": "k4",
        "keyStatus": "DELETE",
        "id-version1": "userId",
        "id-version2": "userId",
        "idStatus": "DELETE",
        "text1Status": "DELETE",
        "text4Status": "DELETE",
        "text5Status": "DELETE",
        "text1-version1": "string",
        "text1-version2": "string",
        "text4-version1": "是",
        "text4-version2": "是",
        "text5-version1": "用户Id",
        "text5-version2": "用户Id",
        "grade": "这里有更多撒金凤卡少女反馈加啊是南非进口三反馈加三分快三方面小美女你发怒发怒发怒",
        "grade1": "1232178346816421641326426147126473214143214",
        "grade2": "sadkjhsakdfhskahfkashfdksahfkasdksafkadsfkasf",
        "gradeStatus": "error",
        "grade1Status": "error",
        "grade2Status": "error",
        "grade3Status": "error"
      }
    ]
  },
  "body": {
    "type": "wrapper",
    "bgColor": "white",
    "body": {
      "type": "flex",
      "direction": "column",
      "gap": true,
      "items": [
        {
          "type": "tags",
          "items": [
            {
              "type": "tag",
              "label": "新增",
              "displayMode": "bordered",
              "color": "add-status"
            },
            {
              "type": "tag",
              "label": "修改",
              "displayMode": "bordered",
              "color": "edit-status"
            },
            {
              "type": "tag",
              "label": "删除",
              "displayMode": "bordered",
              "color": "delete-status"
            }
          ]
        },
        {
          "type": "form",
          "title": "",
          "actions": [],
          "mode": "horizontal",
          "wrapWithPanel": false,
          "labelWidth": 80,
          "body": [
            {
              "type": "between",
              "label": "基准版本",
              "labelRemark": {
                "type": "remark",
                "content": "这是一个提示"
              },
              "items": [
                {
                  "type": "select",
                  "label": "基准版本",
                  "name": "baselineVersion",
                  "value": "v1",
                  "options": [
                    {
                      "label": "V1",
                      "value": "v1"
                    },
                    {
                      "label": "V2",
                      "value": "v2"
                    }
                  ]
                },
                {
                  "type": "select",
                  "label": "对比版本",
                  "name": "diffVersion",
                  "value": "v2",
                  "options": [
                    {
                      "label": "V1",
                      "value": "v1"
                    },
                    {
                      "label": "V2",
                      "value": "v2"
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          "type": "crud",
          "id": "crudId",
          "showExpansionColumn": false,
          "syncLocation": false,
          "columnsTogglable": false,
          "autoGenerateFilter": {
            "showBtnToolbar": false,
            "defaultExpanded": false
          },
          "columns": [
            {
              "name": "key-version1",
              "label": "参数key",
              "type": "group",
              "direction": "vertical",
              "body": [
                {
                  "type": "tpl",
                  "visibleOn": "${!keyStatus}",
                  "tpl": "${key-version2}"
                },
                {
                  "type": "tpl",
                  "visibleOn": "${keyStatus === 'ADD'}",
                  "tpl": "<span class='pm-versionDiff-add'>${key-version2}</span>"
                },
                {
                  "type": "tpl",
                  "visibleOn": "${keyStatus === 'DELETE'}",
                  "tpl": "<span class='pm-versionDiff-delete'>${key-version2}</span>"
                },
                {
                  "type": "tpl",
                  "visibleOn": "${keyStatus === 'EDIT'}",
                  "tpl": "<span class='pm-diff-edit-preValue-text-color'>${key-version1}</span>&nbsp;&#8594;&nbsp;"
                },
                {
                  "type": "tpl",
                  "visibleOn": "${keyStatus === 'EDIT'}",
                  "tpl": "<span class='pm-versionDiff-edit'>${key-version2}</span>"
                }
              ]
            },
            {
              "name": "id-version1",
              "label": "参数名称",
              "type": "group",
              "direction": "vertical",
              "body": [
                {
                  "type": "tpl",
                  "visibleOn": "${!idStatus}",
                  "tpl": "${id-version2}"
                },
                {
                  "type": "tpl",
                  "visibleOn": "${idStatus === 'ADD'}",
                  "tpl": "<span class='pm-versionDiff-add'>${id-version2}</span>"
                },
                {
                  "type": "tpl",
                  "visibleOn": "${idStatus === 'DELETE'}",
                  "tpl": "<span class='pm-versionDiff-delete'>${id-version2}</span>"
                },
                {
                  "type": "tpl",
                  "visibleOn": "${idStatus === 'EDIT'}",
                  "tpl": "<span class='pm-diff-edit-preValue-text-color'>${id-version1}</span>&nbsp;&#8594;&nbsp;"
                },
                {
                  "type": "tpl",
                  "visibleOn": "${idStatus === 'EDIT'}",
                  "tpl": "<span class='pm-versionDiff-edit'>${id-version2}</span>"
                }
              ]
            },
            {
              "name": "text1-version2",
              "label": "数据类型",
              "type": "group",
              "direction": "vertical",
              "body": [
                {
                  "type": "tpl",
                  "visibleOn": "${!text1Status}",
                  "tpl": "${text1-version2}"
                },
                {
                  "type": "tpl",
                  "visibleOn": "${text1Status === 'ADD'}",
                  "tpl": "<span class='pm-versionDiff-add'>${text1-version2}</span>"
                },
                {
                  "type": "tpl",
                  "visibleOn": "${text1Status === 'DELETE'}",
                  "tpl": "<span class='pm-versionDiff-delete'>${text1-version2}</span>"
                },
                {
                  "type": "tpl",
                  "visibleOn": "${text1Status === 'EDIT'}",
                  "tpl": "<span class='pm-diff-edit-preValue-text-color'>${text1-version1}</span>&nbsp;&#8594;&nbsp;"
                },
                {
                  "type": "tpl",
                  "visibleOn": "${text1Status === 'EDIT'}",
                  "tpl": "<span class='pm-versionDiff-edit'>${text1-version2}</span>"
                }
              ]
            },
            {
              "name": "text4-version1",
              "label": "是否必填",
              "type": "group",
              "direction": "vertical",
              "body": [
                {
                  "type": "tpl",
                  "visibleOn": "${!text4Status}",
                  "tpl": "${text4-version2}"
                },
                {
                  "type": "tpl",
                  "visibleOn": "${text4Status === 'ADD'}",
                  "tpl": "<span class='pm-versionDiff-add'>${text4-version2}</span>"
                },
                {
                  "type": "tpl",
                  "visibleOn": "${text4Status === 'DELETE'}",
                  "tpl": "<span class='pm-versionDiff-delete'>${text4-version2}</span>"
                },
                {
                  "type": "tpl",
                  "visibleOn": "${text4Status === 'EDIT'}",
                  "tpl": "<span class='pm-diff-edit-preValue-text-color'>${text4-version1}</span>&nbsp;&#8594;&nbsp;"
                },
                {
                  "type": "tpl",
                  "visibleOn": "${text4Status === 'EDIT'}",
                  "tpl": "<span class='pm-versionDiff-edit'>${text4-version2}</span>"
                }
              ]
            },
            {
              "name": "text5-version1",
              "label": "说明",
              "type": "group",
              "direction": "vertical",
              "body": [
                {
                  "type": "tpl",
                  "visibleOn": "${!text5Status}",
                  "tpl": "${text5-version2}"
                },
                {
                  "type": "tpl",
                  "visibleOn": "${text5Status === 'ADD'}",
                  "tpl": "<span class='pm-versionDiff-add'>${text5-version2}</span>"
                },
                {
                  "type": "tpl",
                  "visibleOn": "${text5Status === 'DELETE'}",
                  "tpl": "<span class='pm-versionDiff-delete'>${text5-version2}</span>"
                },
                {
                  "type": "tpl",
                  "visibleOn": "${text5Status === 'EDIT'}",
                  "tpl": "<span class='pm-diff-edit-preValue-text-color'>${text5-version1}</span>&nbsp;&#8594;&nbsp;"
                },
                {
                  "type": "tpl",
                  "visibleOn": "${text5Status === 'EDIT'}",
                  "tpl": "<span class='pm-versionDiff-edit'>${text5-version2}</span>"
                }
              ]
            },
            {
              "type": "operation",
              "label": "操作",
              "width": 80,
              "buttons": [
                {
                  "type": "button",
                  "label": "${_amisExpanded ? '收起' : '展开'}",
                  "level": "link",
                  "onEvent": {
                    "click": {
                      "actions": [
                        {
                          "actionType": "toggleExpanded",
                          "componentId": "crudId",
                          "args": {
                            "condition": "${id === currentId}",
                            "currentId": "${id}"
                          }
                        }
                      ]
                    }
                  }
                }
              ]
            }
          ],
          "source": "${queryList}",
          "subTable": {
            "type": "crud",
            "footerToolbar": [
              {
                "type": "pagination",
                "maxButtons": 5,
                "layout": "total,pager,perPage"
              }
            ],
            "source": "${queryList}",
            "columns": [
              {
                "name": "key-version1",
                "label": "参数key",
                "type": "group",
                "direction": "vertical",
                "body": [
                  {
                    "type": "tpl",
                    "visibleOn": "${!keyStatus}",
                    "tpl": "${key-version2}"
                  },
                  {
                    "type": "tpl",
                    "visibleOn": "${keyStatus === 'ADD'}",
                    "tpl": "<span class='pm-versionDiff-add'>${key-version2}</span>"
                  },
                  {
                    "type": "tpl",
                    "visibleOn": "${keyStatus === 'DELETE'}",
                    "tpl": "<span class='pm-versionDiff-delete'>${key-version2}</span>"
                  },
                  {
                    "type": "tpl",
                    "visibleOn": "${keyStatus === 'EDIT'}",
                    "tpl": "<span class='pm-diff-edit-preValue-text-color'>${key-version1}</span>&nbsp;&#8594;&nbsp;"
                  },
                  {
                    "type": "tpl",
                    "visibleOn": "${keyStatus === 'EDIT'}",
                    "tpl": "<span class='pm-versionDiff-edit'>${key-version2}</span>"
                  }
                ]
              },
              {
                "name": "id-version1",
                "label": "参数名称",
                "type": "group",
                "direction": "vertical",
                "body": [
                  {
                    "type": "tpl",
                    "visibleOn": "${!idStatus}",
                    "tpl": "${id-version2}"
                  },
                  {
                    "type": "tpl",
                    "visibleOn": "${idStatus === 'ADD'}",
                    "tpl": "<span class='pm-versionDiff-add'>${id-version2}</span>"
                  },
                  {
                    "type": "tpl",
                    "visibleOn": "${idStatus === 'DELETE'}",
                    "tpl": "<span class='pm-versionDiff-delete'>${id-version2}</span>"
                  },
                  {
                    "type": "tpl",
                    "visibleOn": "${idStatus === 'EDIT'}",
                    "tpl": "<span class='pm-diff-edit-preValue-text-color'>${id-version1}</span>&nbsp;&#8594;&nbsp;"
                  },
                  {
                    "type": "tpl",
                    "visibleOn": "${idStatus === 'EDIT'}",
                    "tpl": "<span class='pm-versionDiff-edit'>${id-version2}</span>"
                  }
                ]
              },
              {
                "name": "text1-version2",
                "label": "数据类型",
                "type": "group",
                "direction": "vertical",
                "body": [
                  {
                    "type": "tpl",
                    "visibleOn": "${!text1Status}",
                    "tpl": "${text1-version2}"
                  },
                  {
                    "type": "tpl",
                    "visibleOn": "${text1Status === 'ADD'}",
                    "tpl": "<span class='pm-versionDiff-add'>${text1-version2}</span>"
                  },
                  {
                    "type": "tpl",
                    "visibleOn": "${text1Status === 'DELETE'}",
                    "tpl": "<span class='pm-versionDiff-delete'>${text1-version2}</span>"
                  },
                  {
                    "type": "tpl",
                    "visibleOn": "${text1Status === 'EDIT'}",
                    "tpl": "<span class='pm-diff-edit-preValue-text-color'>${text1-version1}</span>&nbsp;&#8594;&nbsp;"
                  },
                  {
                    "type": "tpl",
                    "visibleOn": "${text1Status === 'EDIT'}",
                    "tpl": "<span class='pm-versionDiff-edit'>${text1-version2}</span>"
                  }
                ]
              },
              {
                "name": "text4-version1",
                "label": "是否必填",
                "type": "group",
                "direction": "vertical",
                "body": [
                  {
                    "type": "tpl",
                    "visibleOn": "${!text4Status}",
                    "tpl": "${text4-version2}"
                  },
                  {
                    "type": "tpl",
                    "visibleOn": "${text4Status === 'ADD'}",
                    "tpl": "<span class='pm-versionDiff-add'>${text4-version2}</span>"
                  },
                  {
                    "type": "tpl",
                    "visibleOn": "${text4Status === 'DELETE'}",
                    "tpl": "<span class='pm-versionDiff-delete'>${text4-version2}</span>"
                  },
                  {
                    "type": "tpl",
                    "visibleOn": "${text4Status === 'EDIT'}",
                    "tpl": "<span class='pm-diff-edit-preValue-text-color'>${text4-version1}</span>&nbsp;&#8594;&nbsp;"
                  },
                  {
                    "type": "tpl",
                    "visibleOn": "${text4Status === 'EDIT'}",
                    "tpl": "<span class='pm-versionDiff-edit'>${text4-version2}</span>"
                  }
                ]
              },
              {
                "name": "text5-version1",
                "label": "说明",
                "type": "group",
                "direction": "vertical",
                "body": [
                  {
                    "type": "tpl",
                    "visibleOn": "${!text5Status}",
                    "tpl": "${text5-version2}"
                  },
                  {
                    "type": "tpl",
                    "visibleOn": "${text5Status === 'ADD'}",
                    "tpl": "<span class='pm-versionDiff-add'>${text5-version2}</span>"
                  },
                  {
                    "type": "tpl",
                    "visibleOn": "${text5Status === 'DELETE'}",
                    "tpl": "<span class='pm-versionDiff-delete'>${text5-version2}</span>"
                  },
                  {
                    "type": "tpl",
                    "visibleOn": "${text5Status === 'EDIT'}",
                    "tpl": "<span class='pm-diff-edit-preValue-text-color'>${text5-version1}</span>&nbsp;&#8594;&nbsp;"
                  },
                  {
                    "type": "tpl",
                    "visibleOn": "${text5Status === 'EDIT'}",
                    "tpl": "<span class='pm-versionDiff-edit'>${text5-version2}</span>"
                  }
                ]
              }
            ]
          }
        }
      ]
    }
  }
}
```

- 落地案例  
  [获客平台（主营）-RTA服务-今日头条-RTA策略-RTA策略版本对比](http://moka.dmz.sit.caijj.net/tdrtaui/#/common-RTA/version-diff/TOUTIAO?type=add&strategyId=2721&strategyCode=RTA20230925323&strategyName=头条rta策略测试lwj)  
   ![获客平台（主营）-RTA服务-今日头条-RTA策略-RTA策略版本对比](https://static02.sit.yxmarketing01.com/tdmaterial/2963ed6181394408aca295f778f607a9.png)

### 模式切换
#### Crud与图表视图切换

```schema
{
  "type": "page",
  "id": "switch-mode-crud-1",
  "data": {
    "pageMode": "crud"
  },
  "body": {
    "type": "wrapper",
    "bgColor": "white",
    "body": {
      "type": "flex",
      "direction": "column",
      "gap": true,
      "items": [
        {
          "type": "flex",
          "justify": "end",
          "alignItems": "center",
          "items": [
            {
              "type": "button-group-select",
              "name": "pageMode",
              "align": "right",
              "visibleDivider": true,
              "btnLevel": "text",
              "btnActiveLevel": "link",
              "options": [
                {
                  "label": "列表模式",
                  "value": "crud"
                },
                {
                  "label": "图表模式",
                  "value": "chart"
                }
              ],
              "onEvent": {
                "change": {
                  "actions": [
                    {
                      "actionType": "setValue",
                      "componentId": "switch-mode-crud-1",
                      "args": {
                        "value": {
                          "pageMode": "${event.data.value}"
                        }
                      }
                    }
                  ]
                }
              }
            }
          ]
        },
        {
          "type": "wrapper",
          "size": 0,
          "body": [
            {
              "type": "crud",
              "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/users",
              "id": "custom-crud-id",
              "visibleOn": "${pageMode === 'crud'}",
              "filter": {
                "title": "",
                "body": [
                  {
                    "type": "group",
                    "mode": "horizontal",
                    "body": [
                      {
                        "type": "input-text",
                        "name": "keywords",
                        "label": "关键字",
                        "clearable": true,
                        "placeholder": "通过关键字搜索",
                        "columnRatio": 4
                      },
                      {
                        "type": "input-text",
                        "name": "engine",
                        "label": "Engine",
                        "clearable": true,
                        "columnRatio": 4
                      },
                      {
                        "type": "input-text",
                        "name": "platform",
                        "label": "Platform",
                        "clearable": true,
                        "columnRatio": 4
                      },
                      {
                        "type": "input-text",
                        "name": "keywords1",
                        "label": "关键字1",
                        "clearable": true,
                        "placeholder": "通过关键字搜索",
                        "columnRatio": 4
                      },
                      {
                        "type": "input-text",
                        "name": "engine1",
                        "label": "Engine1",
                        "clearable": true,
                        "columnRatio": 4
                      },
                      {
                        "type": "input-text",
                        "name": "platform1",
                        "label": "Platform1",
                        "clearable": true,
                        "columnRatio": 4
                      }
                    ]
                  }
                ],
                "actions": [
                  {
                    "type": "reset",
                    "label": "重 置"
                  },
                  {
                    "type": "submit",
                    "level": "primary",
                    "label": "查 询"
                  }
                ]
              },
              "headerToolbar": [
                {
                  "type": "button",
                  "primary": true,
                  "label": "下载"
                }
              ],
              "columns": [
                {
                  "name": "name",
                  "label": "name"
                },
                {
                  "name": "email",
                  "label": "email"
                },
                {
                  "name": "realName",
                  "label": "realName"
                },
                {
                  "name": "isOwner",
                  "label": "isOwner"
                }
              ]
            },
            {
              "type": "grid",
              "visibleOn": "${pageMode === 'chart'}",
              "gap": "sm",
              "columns": [
                {
                  "type": "chart",
                  "name": "chart1",
                  "initFetch": true,
                  "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/chart/chart"
                },
                {
                  "type": "chart",
                  "name": "chart2",
                  "initFetch": true,
                  "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/chart/chart2"
                }
              ]
            }
          ]
        }
      ]
    }
  }
}
```

#### table与cards模式切换

```schema
{
  "type": "page",
  "id": "switch-mode-crud",
  "data": {
    "mode": "cards",
    "button-group-select": "all"
  },
  "body": {
    "type": "crud",
    "syncLocation": false,
    "columnsTogglable": false,
    "footerToolbar": [
      {
        "type": "pagination",
        "layout": "total,pager,perPage,go"
      }
    ],
    "headerFilter": {
      "body": {
        "type": "flex",
        "justify": "space-between",
        "items": [
          {
            "type": "flex",
            "gap": true,
            "items": [
              {
                "type": "button-group-select",
                "name": "button-group-select",
                "options": [
                  {
                    "value": "all",
                    "label": "查询全部"
                  },
                  {
                    "value": "forme",
                    "label": "待我审核"
                  },
                  {
                    "value": "reject",
                    "label": "已拒绝"
                  }
                ],
                "onEvent": {
                  "change": {
                    "actions": [
                      {
                        "actionType": "query",
                        "componentId": "custom-crud-id",
                        "args": {
                          "queryParams": {
                            "button-group-select": "${button-group-select}"
                          }
                        }
                      }
                    ]
                  }
                }
              },
              {
                "type": "tpl",
                "tpl": "关键字:"
              },
              {
                "type": "search-box",
                "name": "keywords",
                "placeholder": "请输入"
              },
            ]
          },
          {
            "type": "button-group-select",
            "name": "mode",
            "align": "right",
            "visibleDivider": true,
            "btnLevel": "text",
            "btnActiveLevel": "link",
            "options": [
              {
                "label": "列表模式",
                "value": "table"
              },
              {
                "label": "全图模式",
                "value": "cards"
              }
            ],
            "onEvent": {
              "change": {
                "actions": [
                  {
                    "actionType": "setValue",
                    "componentId": "switch-mode-crud",
                    "args": {
                      "value": {
                        "mode": "${event.data.value}"
                      }
                    }
                  }
                ]
              }
            }
          },
        ]
      }
    },
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/users",
    "id": "custom-crud-id",
    "filter": {
      "title": "",
      "body": [
        {
          "type": "group",
          "mode": "horizontal",
          "body": [
            {
              "type": "input-text",
              "name": "keywords",
              "label": "关键字",
              "clearable": true,
              "placeholder": "通过关键字搜索",
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "engine",
              "label": "Engine",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "platform",
              "label": "Platform",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "keywords1",
              "label": "关键字1",
              "clearable": true,
              "placeholder": "通过关键字搜索",
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "engine1",
              "label": "Engine1",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "platform1",
              "label": "Platform1",
              "clearable": true,
              "columnRatio": 4
            }
          ]
        }
      ],
      "actions": [
        {
          "type": "reset",
          "label": "重 置"
        },
        {
          "type": "submit",
          "level": "primary",
          "label": "查 询"
        }
      ]
    },
    "mode": "${mode}",
    "columnsCount": 5,
    "card": {
      "header": {
        "bgColor": "blue",
        "title": "主标题"
      },
      "body": [
        {
          "type": "flex",
          "direction": "column",
          "gap": true,
          "items": [
            {
              "type": "flex",
              "justify": "space-between",
              "items": [
                {
                  "type": "tpl",
                  "tpl": "主文案主文案"
                },
                {
                  "type": "tag",
                  "label": "分析中",
                  "displayMode": "normal",
                  "color": "#4096ff"
                }
              ]
            },
            {
              "type": "tpl",
              "tpl": "这是一段很长的文案介绍"
            }
          ]
        }
      ]
    },
    "columns": [
      {
        "name": "name",
        "label": "name"
      },
      {
        "name": "email",
        "label": "email"
      },
      {
        "name": "realName",
        "label": "realName"
      },
      {
        "name": "isOwner",
        "label": "isOwner"
      }
    ]
  }
}
```

- 落地案例  
  [产研一站式平台-智能平台-Agent-Agent列表-全部](http://moka.dmz.sit.caijj.net/aiassistantui/#/agent-list?_shMenuId=tenant_menu_P0304_agents_list_lzc9r70o)  
   ![产研一站式平台-智能平台-Agent-Agent列表-全部](https://static02.sit.yxmarketing01.com/tdmaterial/69058af005504f73bc29fab2f75a7501.png)

### 单元格展示卡片信息

```schema
{
  "type": "page",
  "data": {
    "button-group-select": "all"
  },
  "body": {
    "type": "crud",
    "api": "/api/mock2/crud/table4",
    "columns": [
      {
        "name": "id",
        "label": "ID",
        "type": "card",
        "header": {
          "title": "这是标题",
          "subTitle": "副标题",
          "description": "描述",
          "avatarShape": "rounded",
          "avatar": "https://suda.cdn.bcebos.com/images/amis/ai-fake-face.jpg"
        }
      },
      {
        "name": "engine",
        "label": "Rendering engine"
      },
      {
        "name": "browser",
        "label": "Browser"
      },
      {
        "name": "platform",
        "label": "Platform(s)"
      },
      {
        "name": "version",
        "label": "Engine version"
      },
      {
        "name": "grade",
        "label": "CSS grade"
      },
      {
        "type": "operation",
        "label": "操作",
        "buttons": [
          {
            "label": "详情",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "详情",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "删除",
            "type": "button",
            "actionType": "ajax",
            "level": "link",
            "disabled": true,
            "confirmText": "确认要删除吗？",
            "api": {
              "method": "delete",
              "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
            }
          },
          {
            "label": "编辑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "编辑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "空跑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "空跑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          }
        ]
      }
    ]
  }
}
```

### 辅助操作区域多场景

对于批量操作、单个搜索、拖拽排序、模式切换、对于表格模式中的表头列搜索、列展示筛选等辅助操作区域。以下以表格模式为例:

#### 表头列搜索

适用于查询字段少，不希望占用属于筛选区域的大量空间，可推荐该交互方式

```schema
{
  "type": "page",
 
  "body": {
    "type": "crud",
    "api": "/api/mock2/crud/table4",
    "topToolbar": [
      {
        "type": "button",
        "label": "主按钮",
        "actionType": "url",
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
        "level": "primary",
        "blank": false
      },
      {
        "type": "button",
        "label": "次按钮1",
        "actionType": "url",
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
      },
      {
        "type": "button",
        "label": "次按钮2",
        "actionType": "url",
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
      },
      {
        "type": "button",
        "label": "次按钮3",
        "actionType": "url",
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
      },
      {
          "type": "button",
          "label": "次按钮4",
          "disabled": true
        },
        {
          "type": "button",
          "label": "次按钮5"
        },
        {
          "type": "button",
          "label": "次按钮6"
        }
    ],
    "filter": {
      "title": "",
      "body": [
        {
          "type": "group",
          "mode": "horizontal",
          "body": [
            {
              "type": "input-text",
              "name": "keywords",
              "label": "关键字",
              "clearable": true,
              "placeholder": "通过关键字搜索",
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "engine",
              "label": "Engine",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "platform",
              "label": "Platform",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "keywords1",
              "label": "关键字1",
              "clearable": true,
              "placeholder": "通过关键字搜索",
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "engine1",
              "label": "Engine1",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "platform1",
              "label": "Platform1",
              "clearable": true,
              "columnRatio": 4
            }
          ]
        }
      ],
      "actions": [
        {
          "type": "reset",
          "label": "重 置"
        },
        {
          "type": "submit",
          "level": "primary",
          "label": "查 询"
        }
      ]
    },
    "columns": [
      {
        "name": "id",
        "label": "ID"
      },
      {
        "name": "engine",
        "label": "Rendering engine",
        "headSearchable": {
          "type": "input-text",
          "name": "engine3",
          "label": "Rendering enginer"
        }
      },
      {
        "name": "browser",
        "label": "Browser",
        "headSearchable": {
          "type": "input-text",
          "name": "browser3",
          "label": "Browser"
        }
      },
      {
        "name": "platform",
        "label": "Platform(s)"
      },
      {
        "name": "version",
        "label": "Engine version"
      },
      {
        "name": "grade",
        "label": "CSS grade"
      },
      {
        "type": "operation",
        "label": "操作",
        "buttons": [
          {
            "type": "button",
            "level": "link",
            "label": "详情",
            "actionType": "dialog",
            "dialog": {
              "title": "详情",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "删除",
            "type": "button",
            "actionType": "ajax",
            "level": "link",
            "disabled": true,
            "confirmText": "确认要删除吗？",
            "api": {
              "method": "delete",
              "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
            }
          },
          {
            "label": "编辑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "编辑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "空跑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "空跑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          }
        ]
      }
    ]
  }
}
```

#### 分段器搜索

```schema
{
  "type": "page",
  "data": {
    "button-group-select": "all"
  },
  "body": {
    "type": "crud",
    "api": "/api/mock2/crud/table4",
    "topToolbar": [
      {
        "type": "button",
        "label": "主按钮",
        "actionType": "url",
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
        "level": "primary",
        "blank": false
      },
      {
        "type": "button",
        "label": "次按钮1",
        "actionType": "url",
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
      },
      {
        "type": "button",
        "label": "次按钮2",
        "actionType": "url",
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
      },
      {
        "type": "button",
        "label": "次按钮3",
        "actionType": "url",
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
      },
      {
        "type": "button",
        "label": "次按钮4",
        "disabled": true
      },
      {
        "type": "button",
        "label": "次按钮5"
      },
      {
        "type": "button",
        "label": "次按钮6"
      }
    ],
    "headerFilter": {
      "body": [
        {
          "type": "button-group-select",
          "name": "button-group-select",
          "options": [
            {
              "value": "all",
              "label": "查询全部"
            },
            {
              "value": "forme",
              "label": "待我审核"
            },
            {
              "value": "reject",
              "label": "已拒绝"
            }
          ]
        }
      ]
    },
    "columns": [
      {
        "name": "id",
        "label": "ID",
        "searchable": {
          "type": "input-text",
          "name": "id",
          "label": "ID",
          "placeholder": "输入ID"
        }
      },
      {
        "name": "engine",
        "label": "Rendering engine",
        "searchable": {
          "type": "input-text",
          "name": "engine",
          "label": "engine",
          "placeholder": "输入Rendering engine"
        }
      },
      {
        "name": "browser",
        "label": "Browser",
        "searchable": {
          "type": "input-text",
          "name": "browser",
          "label": "Browser",
          "placeholder": "输入Browser"
        }
      },
      {
        "name": "platform",
        "label": "Platform(s)",
        "searchable": {
          "type": "input-text",
          "name": "platform",
          "label": "Platform(s)",
          "placeholder": "输入Platform(s)"
        }
      },
      {
        "name": "version",
        "label": "Engine version",
        "searchable": {
          "type": "input-text",
          "name": "version",
          "label": "Engine version",
          "placeholder": "输入Engine version"
        }
      },
      {
        "name": "grade",
        "label": "CSS grade",
        "searchable": {
          "type": "input-text",
          "name": "grade",
          "label": "CSS grade",
          "placeholder": "输入CSS grade"
        }
      },
      {
        "type": "operation",
        "label": "操作",
        "buttons": [
          {
            "label": "详情",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "详情",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "删除",
            "type": "button",
            "actionType": "ajax",
            "level": "link",
            "disabled": true,
            "confirmText": "确认要删除吗？",
            "api": {
              "method": "delete",
              "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
            }
          },
          {
            "label": "编辑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "编辑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "空跑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "空跑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          }
        ]
      }
    ]
  }
}
```

- 落地案例  
  [大数据一站式-数据目录-项目空间管理](http://moka.dmz.sit.caijj.net/analytoolui/#/dpProjectSpace?_shMenuId=tenant_menu_lysdwnjm)  
   ![大数据一站式-数据目录-项目空间管理](https://static02.sit.yxmarketing01.com/materialcenter/32434a95-3afd-4c25-a9ee-6ad0923e5a91.png)

#### 仅标题

```schema
{
  "type": "page",
  "data": {
    "button-group-select": "all"
  },
 
  "body": {
    "type": "crud",
    "api": "/api/mock2/crud/table4",
    "title":"这里是table标题",
    "columns": [
      {
        "name": "id",
        "label": "ID"
      },
      {
        "name": "id",
        "label": "序号"
      },
      {
        "name": "engine",
        "label": "Rendering engine"
      },
      {
        "name": "browser",
        "label": "Browser"
      },
      {
        "name": "platform",
        "label": "Platform(s)"
      },
      {
        "name": "version",
        "label": "Engine version"
      },
      {
        "name": "grade",
        "label": "CSS grade"
      },
      {
        "type": "operation",
        "label": "操作",
        "buttons": [
          {
            "label": "详情",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "详情",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "删除",
            "type": "button",
            "actionType": "ajax",
            "level": "link",
            "disabled": true,
            "confirmText": "确认要删除吗？",
            "api": {
              "method": "delete",
              "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
            }
          },
          {
            "label": "编辑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "编辑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "空跑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "空跑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          }
        ]
      }
    ]
  }
}
```

#### 单个搜索

```schema
{
  "type": "page",
  "data": {
    "button-group-select": "all"
  },
  "body": {
    "type": "crud",
    "api": "/api/mock2/crud/table4",
    "headerToolbar": [
      {
        "type": "tpl",
        "tpl": "关键字:"
      },
      {
        "type": "search-box",
        "name": "keywords",
        "size": "lg",
        "placeholder": "请输入"
      },
    ],
    "columns": [
      {
        "name": "id",
        "label": "ID"
      },
      {
        "name": "id",
        "label": "序号"
      },
      {
        "name": "engine",
        "label": "Rendering engine"
      },
      {
        "name": "browser",
        "label": "Browser"
      },
      {
        "name": "platform",
        "label": "Platform(s)"
      },
      {
        "name": "version",
        "label": "Engine version"
      },
      {
        "name": "grade",
        "label": "CSS grade"
      },
      {
        "type": "operation",
        "label": "操作",
        "buttons": [
          {
            "label": "详情",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "详情",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "删除",
            "type": "button",
            "actionType": "ajax",
            "level": "link",
            "disabled": true,
            "confirmText": "确认要删除吗？",
            "api": {
              "method": "delete",
              "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
            }
          },
          {
            "label": "编辑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "编辑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "空跑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "空跑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          }
        ]
      }
    ]
  }
}
```



#### 全局操作按钮+批量操作
通常带查询区域的crud，需要有全局维度的操作按钮时，比如像新增按钮，可以通过配置topToolbar，在crud查询区域顶部添加全局操作按钮。   
针对表格需要批量选中进行某种操作时，可通过配置bulkActions，在表格的内容区域配置批量操作按钮、批量删除。

```schema
{
  "type": "page",
  "body": {
    "type": "crud",
    "api": "/api/mock2/crud/table4",
     "topToolbar": [
      {
        "type": "button",
        "label": "新增",
        "actionType": "url",
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
        "level": "primary",
        "blank": false,
      } ,
      {
        "type": "button",
        "label": "次按钮",
        "actionType": "url",
        "gapSize": "sm",
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index"
      },
      {
        "type": "button",
        "label": "次按钮2",
        "actionType": "url",
        "gapSize": "md",
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index"
      }
      ],
    "bulkActions": [
      {
        "label": "批量删除",
        "actionType": "ajax",
        "api": "delete:/api/mock2/sample/${ids|raw}",

        "confirmText": "确定要批量删除?"
      },
      {
        "label": "批量修改",
        "actionType": "dialog",
        "dialog": {
          "title": "批量编辑",
          "showCloseButton": false,
          "body": {
            "type": "form",
            "api": "/api/mock2/sample/bulkUpdate2",
            "body": [
              {
                "type": "hidden",
                "name": "ids"
              },
              {
                "type": "input-text",
                "name": "engine",
                "label": "Engine"
              }
            ]
          }
        }
      }
    ],
    "columns": [
      {
        "name": "id",
        "label": "ID",
        "searchable": {
          "type": "input-text",
          "name": "id",
          "label": "ID",
          "placeholder": "输入ID"
        }
      },
      {
        "name": "engine",
        "label": "Rendering engine",
        "searchable": {
          "type": "input-text",
          "name": "engine",
          "label": "engine",
          "placeholder": "输入Rendering engine"
        }
      },
      {
        "name": "browser",
        "label": "Browser",
        "searchable": {
          "type": "input-text",
          "name": "browser",
          "label": "Browser",
          "placeholder": "输入Browser"
        }
      },
      {
        "name": "platform",
        "label": "Platform(s)",
        "searchable": {
          "type": "input-text",
          "name": "platform",
          "label": "Platform(s)",
          "placeholder": "输入Platform(s)"
        }
      },
      {
        "name": "version",
        "label": "Engine version",
        "searchable": {
          "type": "input-text",
          "name": "version",
          "label": "Engine version",
          "placeholder": "输入Engine version"
        }
      },
      {
        "name": "grade",
        "label": "CSS grade",
        "searchable": {
          "type": "input-text",
          "name": "grade",
          "label": "CSS grade",
          "placeholder": "输入CSS grade"
        }
      },
      {
        "type": "operation",
        "label": "操作",
        "buttons": [
          {
            "type": "button",
            "level": "link",
            "label": "详情",
            "actionType": "dialog",
            "dialog": {
              "title": "详情",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "删除",
            "type": "button",
            "actionType": "ajax",
            "level": "link",
            "disabled": true,
            "confirmText": "确认要删除吗？",
            "api": {
              "method": "delete",
              "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
            }
          },
          {
            "label": "编辑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "编辑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "空跑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "空跑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          }
        ]
      }
    ]
  }
}
```


#### 标题+单个搜索

```schema
{
  "type": "page",
  "data": {
    "button-group-select": "all"
  },
 
  "body": {
    "type": "crud",
    "api": "/api/mock2/crud/table4",
    "headerToolbar": [
      {
        "type": "tpl",
        "tpl": "这里是table标题"
      },
      {
        "type": "search-box",
        "name": "keywords",
        "size": "lg",
        "align": "right",
        "placeholder": "请输入"
      },
      {
        "type": "tpl",
        "align": "right",
        "tpl": "关键字:"
      }
    ],
    "columns": [
      {
        "name": "id",
        "label": "ID"
      },
      {
        "name": "id",
        "label": "序号"
      },
      {
        "name": "engine",
        "label": "Rendering engine"
      },
      {
        "name": "browser",
        "label": "Browser"
      },
      {
        "name": "platform",
        "label": "Platform(s)"
      },
      {
        "name": "version",
        "label": "Engine version"
      },
      {
        "name": "grade",
        "label": "CSS grade"
      },
      {
        "type": "operation",
        "label": "操作",
        "buttons": [
          {
            "label": "详情",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "详情",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "删除",
            "type": "button",
            "actionType": "ajax",
            "level": "link",
            "disabled": true,
            "confirmText": "确认要删除吗？",
            "api": {
              "method": "delete",
              "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
            }
          },
          {
            "label": "编辑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "编辑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "空跑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "空跑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          }
        ]
      }
    ]
  }
}
```

#### 批量操作+分段器搜索

```schema
{
  "type": "page",
  "data": {
    "button-group-select": "all"
  },
  "body": {
    "type": "crud",
    "api": "/api/mock2/crud/table4",
    "id": "custom-crud-id",
    "topToolbar": [
      {
        "type": "button",
        "label": "主按钮",
        "actionType": "url",
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
        "level": "primary",
        "blank": false,
        "badge": {
          "mode": "text",
          "text": 15,
        },
      },
      {
        "type": "button",
        "label": "次按钮1",
        "actionType": "url",
        "gapSize":"sm",
        "badge": {
          "mode": "text",
          "text": 5,
        },
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index"
      },
      {
        "type": "button",
        "label": "次按钮2",
        "actionType": "url",
        "gapSize":"sm",
        "badge": {
          "mode": "text",
          "text": 10905,
        },
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index"
      },

      {
        "type": "button",
        "label": "次按钮3",
        "actionType": "url",
        "gapSize":"md",
        "badge": {
          "mode": "text",
          "text": "notice",
        },
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index"
      },
      {
        "type": "button",
        "label": "次按钮4",
        "disabled": true
      },
      {
        "type": "button",
        "label": "次按钮5"
      },
      {
        "type": "button",
        "label": "次按钮6"
      }
      ],
      "headerFilter": {
        "body": [
          {
            "type": "button-group-select",
            "name": "button-group-select",
            "align": "right",
            "options": [
              {
                "value": "all",
                "label": "查询全部"
              },
              {
                "value": "forme",
                "label": "待我审核"
              },
              {
                "value": "reject",
                "label": "已拒绝"
              }
            ],
            "onEvent": {
              "change": {
                "actions": [
                  {
                    "actionType": "query",
                    "componentId": "custom-crud-id",
                    "args": {
                      "queryParams": {
                        "button-group-select":"${button-group-select}"
                      }
                    }
                  }
                ]
              }
            }
          }
        ]
      },
      "bulkActions": [
        {
          "label": "批量删除",
          "actionType": "ajax",
          "api": "delete:/api/mock2/sample/${ids|raw}",
          "confirmText": "确定要批量删除?"
        },
        {
          "label": "批量修改",
          "actionType": "dialog",
          "dialog": {
            "title": "批量编辑",
            "showCloseButton": false,
            "body": {
              "type": "form",
              "api": "/api/mock2/sample/bulkUpdate2",
              "body": [
                {
                  "type": "hidden",
                  "name": "ids"
                },
                {
                  "type": "input-text",
                  "name": "engine",
                  "label": "Engine"
                }
              ]
            }
          }
        }
      ],
      "columns": [
        {
          "name": "id",
          "label": "ID",
          "searchable": {
            "type": "input-text",
            "name": "id",
            "label": "主键",
            "placeholder": "输入id"
          }
        },
        {
          "name": "id",
          "label": "序号",
          "searchable": {
            "type": "input-text",
            "name": "id",
            "label": "序号",
            "placeholder": "输入序号"
          }
        },
        {
          "name": "engine",
          "label": "Rendering engine",
          "searchable": {
            "type": "input-text",
            "name": "engine",
            "label": "Rendering engine",
            "placeholder": "输入Rendering engine"
          }
        },
        {
          "name": "browser",
          "label": "Browser",
          "searchable": {
            "type": "input-text",
            "name": "browser",
            "label": "Browser",
            "placeholder": "输入Browser"
          }
        },
        {
          "name": "platform",
          "label": "Platform(s)",
          "searchable": {
            "type": "input-text",
            "name": "platform",
            "label": "Platform(s)",
            "placeholder": "输入Platform(s)"
          }
        },
        {
          "name": "version",
          "label": "Engine version",
          "searchable": {
            "type": "input-text",
            "name": "version",
            "label": "Engine version",
            "placeholder": "输入Engine version"
          }
        },
        {
          "name": "grade",
          "label": "CSS grade",
          "searchable": {
            "type": "input-text",
            "name": "grade",
            "label": "CSS grade",
            "placeholder": "输入CSS grade"
          }
        },
        {
          "type": "operation",
          "label": "操作",
          "buttons": [
            {
              "label": "详情",
              "type": "button",
              "level": "link",
              "actionType": "dialog",
              "dialog": {
                "title": "详情",
                "showCloseButton": false,
                "body": "这是个简单的弹框。"
              }
            },
            {
              "label": "删除",
              "type": "button",
              "actionType": "ajax",
              "level": "link",
              "disabled": true,
              "confirmText": "确认要删除吗？",
              "api": {
                "method": "delete",
                "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
              }
            },
            {
              "label": "编辑",
              "type": "button",
              "level": "link",
              "actionType": "dialog",
              "dialog": {
                "title": "编辑",
                "showCloseButton": false,
                "body": "这是个简单的弹框。"
              }
            },
            {
              "label": "空跑",
              "type": "button",
              "level": "link",
              "actionType": "dialog",
              "dialog": {
                "title": "空跑",
                "showCloseButton": false,
                "body": "这是个简单的弹框。"
              }
            }
          ]
        }
      ]
    }
}
```

#### 批量操作+表头列搜索

```schema
{
  "type": "page",
 
  "body": {
    "type": "crud",
    "api": "/api/mock2/crud/table4",
    "bulkActions": [
      {
        "label": "批量删除",
        "actionType": "ajax",
        "api": "delete:/api/mock2/sample/${ids|raw}",
        "confirmText": "确定要批量删除?"
      }
    ],
    "filter": {
      "title": "",
      "body": [
        {
          "type": "group",
          "mode": "horizontal",
          "body": [
            {
              "type": "input-text",
              "name": "keywords",
              "label": "关键字",
              "clearable": true,
              "placeholder": "通过关键字搜索",
              "columnRatio": 4,
            },
            {
              "type": "input-text",
              "name": "engine",
              "label": "Engine",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "platform",
              "label": "Platform",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "keywords1",
              "label": "关键字1",
              "clearable": true,
              "placeholder": "通过关键字搜索",
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "engine1",
              "label": "Engine1",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "platform1",
              "label": "Platform1",
              "clearable": true,
              "columnRatio": 4
            }
          ]
        }
      ],
      "actions": [
        {
          "type": "reset",
          "label": "重 置"
        },
        {
          "type": "submit",
          "level": "primary",
          "label": "查 询"
        }
      ]
    },
    "columns": [
      {
        "name": "id",
        "label": "ID"
      },
      {
        "name": "engine",
        "label": "Rendering engine",
        "headSearchable": {
          "type": "input-text",
          "name": "engine3",
          "label": "Rendering engine"
        }
      },
      {
        "name": "browser",
        "label": "Browser",
      },
      {
        "name": "platform",
        "label": "Platform(s)",
        "headSearchable": {
          "type": "input-text",
          "name": "platform3",
          "label": "Platform(s)"
        }
      },
      {
        "name": "version",
        "label": "Engine version"
      },
      {
        "name": "grade",
        "label": "CSS grade"
      },
      {
        "type": "operation",
        "label": "操作",
        "buttons": [
          {
            "label": "详情",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "详情",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "删除",
            "type": "button",
            "actionType": "ajax",
            "level": "link",
            "disabled": true,
            "confirmText": "确认要删除吗？",
            "api": {
              "method": "delete",
              "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings",
            }
          }
        ]
      }
    ]
  }
}
```

#### 列设置+列排序

```schema
{
  "type": "page",
 
  "body": {
    "type": "crud",
    "api": "/api/mock2/crud/table4",
    "columnsTogglable": true,
    "headerToolbar": [
      {
        "type": "columns-toggler",
        "draggable": true,
        "overlay": true,
      }
    ],
    "columns": [
      {
        "name": "id",
        "label": "ID",
        "searchable": {
          "type": "input-text",
          "name": "id",
          "label": "ID",
          "placeholder": "输入ID"
        }
      },
      {
        "name": "engine",
        "label": "Rendering engine",
        "sortable": true,
        "searchable": {
          "type": "input-text",
          "name": "engine",
          "label": "engine",
          "placeholder": "输入Rendering engine"
        }
      },
      {
        "name": "browser",
        "label": "Browser",
        "sortable": true,
        "searchable": {
          "type": "input-text",
          "name": "browser",
          "label": "Browser",
          "placeholder": "输入Browser"
        }
      },
      {
        "name": "platform",
        "label": "Platform(s)",
        "sortable": true,
        "searchable": {
          "type": "input-text",
          "name": "platform",
          "label": "Platform(s)",
          "placeholder": "输入Platform(s)"
        }
      },
      {
        "name": "version",
        "label": "Engine version",
        "searchable": {
          "type": "input-text",
          "name": "version",
          "label": "Engine version",
          "placeholder": "输入Engine version"
        }
      },
      {
        "name": "grade",
        "label": "CSS grade",
        "searchable": {
          "type": "input-text",
          "name": "grade",
          "label": "CSS grade",
          "placeholder": "输入CSS grade"
        }
      },
      {
        "type": "operation",
        "label": "操作",
        "buttons": [
          {
            "type": "button",
            "level": "link",
            "label": "详情",
            "actionType": "dialog",
            "dialog": {
              "title": "详情",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "删除",
            "type": "button",
            "actionType": "ajax",
            "level": "link",
            "disabled": true,
            "confirmText": "确认要删除吗？",
            "api": {
              "method": "delete",
              "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
            }
          },
          {
            "label": "编辑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "编辑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "空跑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "空跑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          }
        ]
      }
    ]
  }
}
```

#### 列设置+表头列搜索

```schema
{
  "type": "page",
  "data": {
    "button-group-select": "all"
  },
 
  "body": {
    "type": "crud",
    "api": "/api/mock2/crud/table4",
    "columnsTogglable": true,
    "isInlineHeader": true,
    "columns": [
      {
        "name": "id",
        "label": "ID",
        "searchable": {
          "type": "input-text",
          "name": "id",
          "label": "主键",
          "placeholder": "输入id"
        }
      },
      {
        "name": "id",
        "label": "序号",
        "searchable": {
          "type": "input-text",
          "name": "id",
          "label": "序号",
          "placeholder": "输入序号"
        }
      },
      {
        "name": "engine",
        "label": "Rendering engine",
        "headSearchable": {
          "type": "input-text",
          "name": "engine3",
          "label": "Rendering engine",
          "placeholder": "输入Rendering engine"
        }
      },
      {
        "name": "browser",
        "label": "Browser",
        "searchable": {
          "type": "input-text",
          "name": "browser",
          "label": "Browser",
          "placeholder": "输入Browser"
        }
      },
      {
        "name": "platform",
        "label": "Platform(s)",
        "headSearchable": {
          "type": "input-text",
          "name": "platform3",
          "label": "Platform(s)",
          "placeholder": "输入Platform(s)"
        }
      },
      {
        "name": "version",
        "label": "Engine version",
        "searchable": {
          "type": "input-text",
          "name": "version",
          "label": "Engine version",
          "placeholder": "输入Engine version"
        }
      },
      {
        "name": "grade",
        "label": "CSS grade",
        "searchable": {
          "type": "input-text",
          "name": "grade",
          "label": "CSS grade",
          "placeholder": "输入CSS grade"
        }
      },
      {
        "type": "operation",
        "label": "操作",
        "buttons": [
          {
            "label": "详情",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "详情",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "删除",
            "type": "button",
            "actionType": "ajax",
            "level": "link",
            "disabled": true,
            "confirmText": "确认要删除吗？",
            "api": {
              "method": "delete",
              "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
            }
          },
          {
            "label": "编辑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "编辑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "空跑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "空跑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          }
        ]
      }
    ]
  }
}
```

#### 操作按钮+单个搜索

```schema
{
  "type": "page",
  "data": {
    "button-group-select": "all"
  },
 
  "body": {
    "type": "crud",
    "api": "/api/mock2/crud/table4",
    "columnsTogglable": true,
    "headerToolbar": [
      {
        "type": "button-toolbar",
        "maxCount": 2,
        "buttons": [{
          "type": "button",
          "label": "主按钮",
          "actionType": "url",
          "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
          "level": "primary",
          "blank": false
        },
        {
          "type": "button",
          "label": "次按钮1",
          "actionType": "url",
          "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index"
        },
        {
          "type": "button",
          "label": "次按钮2",
          "disabled": true
        },
        {
          "type": "button",
          "label": "次按钮3"
        },
        {
          "type": "button",
          "label": "次按钮4"
        }]
      },
      {
        "type": "search-box",
        "name": "keywords",
        "size": "md",
        "align": "right",
        "placeholder": "请输入"
      },
      {
        "type": "tpl",
        "align": "right",
        "tpl": "关键字:"
      }
    ],
    "columns": [
      {
        "name": "id",
        "label": "ID",
      },
      {
        "name": "id",
        "label": "序号"
      },
      {
        "name": "engine",
        "label": "Rendering engine"
      },
      {
        "name": "browser",
        "label": "Browser"
      },
      {
        "name": "platform",
        "label": "Platform(s)"
      },
      {
        "name": "version",
        "label": "Engine version"
      },
      {
        "name": "grade",
        "label": "CSS grade"
      },
      {
        "type": "operation",
        "label": "操作",
        "buttons": [
          {
            "label": "详情",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "详情",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "删除",
            "type": "button",
            "actionType": "ajax",
            "level": "link",
            "disabled": true,
            "confirmText": "确认要删除吗？",
            "api": {
              "method": "delete",
              "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
            }
          },
          {
            "label": "编辑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "编辑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "空跑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "空跑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          }
        ]
      }
    ]
  }
}
```

#### 列设置+批量操作+表头列搜索

```schema
{
  "type": "page",
  "data": {
    "button-group-select": "all"
  },
 
  "body":{
    "type": "crud",
    "api": "/api/mock2/crud/table4",
    "columnsTogglable": true,
    "bulkActions": [
      {
        "label": "批量删除",
        "actionType": "ajax",
        "api": "delete:/api/mock2/sample/${ids|raw}",
        "confirmText": "确定要批量删除?"
      },
      {
        "label": "批量修改",
        "actionType": "dialog",
        "dialog": {
          "title": "批量编辑",
          "showCloseButton": false,
          "body": {
            "type": "form",
            "api": "/api/mock2/sample/bulkUpdate2",
            "body": [
              {
                "type": "hidden",
                "name": "ids"
              },
              {
                "type": "input-text",
                "name": "engine",
                "label": "Engine"
              }
            ]
          }
        }
      }
    ],
    "columns": [
      {
        "name": "id",
        "label": "ID",
        "searchable": {
          "type": "input-text",
          "name": "id",
          "label": "主键",
          "placeholder": "输入id"
        }
      },
      {
        "name": "id",
        "label": "序号",
        "searchable": {
          "type": "input-text",
          "name": "id",
          "label": "序号",
          "placeholder": "输入序号"
        }
      },
      {
        "name": "engine",
        "label": "Rendering engine",
        "searchable": {
          "type": "input-text",
          "name": "engine",
          "label": "Rendering engine",
          "placeholder": "输入Rendering engine"
        }
      },
      {
        "name": "browser",
        "label": "Browser",
        "headSearchable": {
          "type": "input-text",
          "name": "browser3",
          "label": "Browser",
          "placeholder": "输入Browser"
        }
      },
      {
        "name": "platform",
        "label": "Platform(s)",
        "searchable": {
          "type": "input-text",
          "name": "platform",
          "label": "Platform(s)",
          "placeholder": "输入Platform(s)"
        }
      },
      {
        "name": "version",
        "label": "Engine version",
        "searchable": {
          "type": "input-text",
          "name": "version",
          "label": "Engine version",
          "placeholder": "输入Engine version"
        }
      },
      {
        "name": "grade",
        "label": "CSS grade",
        "searchable": {
          "type": "input-text",
          "name": "grade",
          "label": "CSS grade",
          "placeholder": "输入CSS grade"
        }
      },
      {
        "type": "operation",
        "label": "操作",
        "buttons": [
          {
            "label": "详情",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "详情",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "删除",
            "type": "button",
            "actionType": "ajax",
            "level": "link",
            "disabled": true,
            "confirmText": "确认要删除吗？",
            "api": {
              "method": "delete",
              "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
            }
          },
          {
            "label": "编辑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "编辑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "空跑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "空跑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          }
        ]
      }
    ]
  }
}
```

#### 列设置+全局/批量操作+单个搜索

全局操作按钮/批量操作按钮（按钮最多可放置 3 个，超出展示在更多下拉中）

```schema
{
  "type": "page",
  "data": {
    "button-group-select": "all"
  },
 
  "body": {
    "type": "crud",
    "api": "/api/mock2/crud/table4",
    "columnsTogglable": true,
    "headerToolbar": [
      {
        "type": "button-toolbar",
        "maxCount": 2,
        "buttons": [{
          "type": "button",
          "label": "主按钮",
          "actionType": "url",
          "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
          "level": "primary",
          "blank": false
        },
        {
          "type": "button",
          "label": "次按钮1",
          "actionType": "url",
          "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index"
        },
        {
          "type": "button",
          "label": "次按钮2",
          "disabled": true
        },
        {
          "type": "button",
          "label": "次按钮3"
        },
        {
          "type": "button",
          "label": "次按钮4"
        }]
      },
      {
        "type": "search-box",
        "name": "keywords",
        "size": "md",
        "align": "right",
        "placeholder": "请输入"
      },
      {
        "type": "tpl",
        "align": "right",
        "tpl": "关键字:"
      }
    ],
    "columns": [
      {
        "name": "id",
        "label": "ID",
      },
      {
        "name": "id",
        "label": "序号"
      },
      {
        "name": "engine",
        "label": "Rendering engine"
      },
      {
        "name": "browser",
        "label": "Browser"
      },
      {
        "name": "platform",
        "label": "Platform(s)"
      },
      {
        "name": "version",
        "label": "Engine version"
      },
      {
        "name": "grade",
        "label": "CSS grade"
      },
      {
        "type": "operation",
        "label": "操作",
        "buttons": [
          {
            "label": "详情",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "详情",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "删除",
            "type": "button",
            "actionType": "ajax",
            "level": "link",
            "disabled": true,
            "confirmText": "确认要删除吗？",
            "api": {
              "method": "delete",
              "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
            }
          },
          {
            "label": "编辑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "编辑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "空跑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "空跑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          }
        ]
      }
    ]
  }
}
```

#### 操作按钮+批量操作+单个搜索

```schema
{
  "type": "page",
  "data": {
    "button-group-select": "all"
  },
  "body": {
    "type": "crud",
    "api": "/api/mock2/crud/table4",
    "headerToolbar": [
      {
        "type": "button",
        "label": "导入Excel",
        "level": "primary"
      },
      "bulkActions",
      {
        "type": "search-box",
        "size": "sm",
        "name": "keywords",
        "align": "right",
        "placeholder": "请输入"
      },
      {
        "type": "tpl",
        "align": "right",
        "tpl": "关键字:"
      },
    ],
    "bulkActions": [
      {
        "label": "批量删除",
        "actionType": "ajax",
        "api": "delete:/api/mock2/sample/${ids|raw}",
        "confirmText": "确定要批量删除?"
      },
      {
        "label": "批量修改",
        "actionType": "dialog",
        "dialog": {
          "title": "批量编辑",
          "showCloseButton": false,
          "body": {
            "type": "form",
            "api": "/api/mock2/sample/bulkUpdate2",
            "body": [
              {
                "type": "hidden",
                "name": "ids"
              },
              {
                "type": "input-text",
                "name": "engine",
                "label": "Engine"
              }
            ]
          }
        }
      }
    ],
    "columns": [
      {
        "name": "id",
        "label": "ID"
      },
      {
        "name": "id",
        "label": "序号"
      },
      {
        "name": "engine",
        "label": "Rendering engine"
      },
      {
        "name": "browser",
        "label": "Browser"
      },
      {
        "name": "platform",
        "label": "Platform(s)"
      },
      {
        "name": "version",
        "label": "Engine version"
      },
      {
        "name": "grade",
        "label": "CSS grade"
      },
      {
        "type": "operation",
        "label": "操作",
        "buttons": [
          {
            "label": "详情",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "详情",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "删除",
            "type": "button",
            "actionType": "ajax",
            "level": "link",
            "disabled": true,
            "confirmText": "确认要删除吗？",
            "api": {
              "method": "delete",
              "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
            }
          },
          {
            "label": "编辑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "编辑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "空跑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "空跑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          }
        ]
      }
    ]
  }
}
```

- 落地案例  
  [获客一站式-投放管理-账户分组页](http://moka.dmz.sit.caijj.net/tdpplusui/#/accountGroup/subGroup?groupKey=2e0b441f-ea44-4158-8231-e1e1bee1e08f&groupName=ogb%E6%B5%8B%E8%AF%95&scene=REPORT)  
   ![获客一站式-投放管理-账户分组页](https://static02.sit.yxmarketing01.com/materialcenter/303c6fe9-1693-4bfc-aeb5-94cadd439a2c.png)


### 紧凑模式

配置 `"compactMode": true` 开启紧凑模式。对于报表一类的数据展示，需要一屏看到更多的数据，便于做业务分析，常推荐紧凑模式，其他场景暂不推荐。

```schema:scope="none"
{
  "type": "page",
  "body": {
    "type": "crud",
    "compactMode": true,
    "api": "/api/mock2/crud/table4",
    "columns": [
      {
        "name": "id",
        "label": "ID",
        "searchable": {
          "type": "input-text",
          "name": "id",
          "label": "主键",
          "placeholder": "输入id"
        }
      },
      {
        "name": "id",
        "label": "序号",
        "searchable": {
          "type": "input-text",
          "name": "id",
          "label": "序号",
          "placeholder": "输入序号"
        }
      },
      {
        "name": "engine",
        "label": "Rendering engine",
        "searchable": {
          "type": "input-text",
          "name": "engine",
          "label": "Rendering engine",
          "placeholder": "输入Rendering engine"
        }
      },
      {
        "name": "browser",
        "label": "Browser",
        "searchable": {
          "type": "input-text",
          "name": "browser",
          "label": "Browser",
          "placeholder": "输入Browser"
        }
      },
      {
        "name": "platform",
        "label": "Platform(s)",
        "searchable": {
          "type": "input-text",
          "name": "platform",
          "label": "Platform(s)",
          "placeholder": "输入Platform(s)"
        }
      },
      {
        "name": "version",
        "label": "Engine version",
        "searchable": {
          "type": "input-text",
          "name": "version",
          "label": "Engine version",
          "placeholder": "输入Engine version"
        }
      },
      {
        "name": "grade",
        "label": "CSS grade",
        "searchable": {
          "type": "input-text",
          "name": "grade",
          "label": "CSS grade",
          "placeholder": "输入CSS grade"
        }
      }
    ]
  }
}
```

### Table 列中数字右对齐

对于报表一类的数据展示或是金额一类的数据，需要上下行数据进行比较做业务分析，常推荐数字类列右对齐，其他场景暂不推荐。

```schema
{
  "type": "page",
  "body": {
    "type": "crud",
    "api": "/api/mock2/crud/table4",
    "columns": [
      {
        "name": "id",
        "label": "id",
        "searchable": {
          "type": "input-text",
          "name": "id",
          "label": "ID",
          "placeholder": "输入ID"
        }
      },
      {
        "name": "browser",
        "label": "名称",
        "searchable": {
          "type": "input-text",
          "name": "browser",
          "label": "名称",
          "placeholder": "输入名称"
        },
      },
      {
        "name": "version",
        "label": "version",
        "searchable": {
          "type": "input-text",
          "name": "version",
          "label": "version",
          "placeholder": "输入version"
        },
      },
      {
        "label": "完件数(T&G)",
        "name": "finishNum",
        "searchable": {
          "type": "input-text",
          "name": "finishNum",
          "label": "完件数(T&G)",
          "placeholder": "输入完件数(T&G)"
        },
      },
      {
        "name": "creditNum",
        "label": "授信数(T&G)",
        "searchable": {
          "type": "input-text",
          "name": "creditNum",
          "label": "授信数(T&G)",
          "placeholder": "输入授信数(T&G)"
        },
      },
      {
        "label": "借款金额(元)",
        "name": "realMoney",
        "align": "right"
      },
      {
        "label": "还款金额(元)",
        "name": "realMoney",
        "align": "right",
      },
    ]
  }
}
```


### 不知总条数

不知总条数，无法知晓数据 total 是多少

```schema
{
  "type": "page",
  "data": {
    "dataEnum": {
      "init": "初始化",
      "register": "已注册",
      "off": "已注销"
    },
    "colorEnum": {
      "init": "active",
      "register": "success",
      "off": "inactive"
    },
    "OPTIONS": [
      {
        "label": "初始化",
        "value": "init"
      }, {
        "label": "已注册",
        "value": "register"
      }, {
        "label": "已注销",
        "value": "off"
      }
    ]

  },
  "body": {
    "type": "crud",
    "api": "/api/mock2/crud/table6",
    "footerToolbar":[
      {
        "type": "pagination",
        "layout": "pager"
      }
    ],
    "columns": [
      {
        "name": "id",
        "label": "ID",
        "searchable": {
          "type": "input-text",
          "name": "id",
          "label": "ID",
          "placeholder": "输入ID"
        }
      },
      {
        "name": "engine",
        "label": "Rendering engine",
        "type": "tpl",
        "tpl": "${engine|truncate:5}",
        "popOver": "${engine}",
        "searchable": {
          "type": "input-text",
          "name": "engine",
          "label": "engine",
          "placeholder": "输入Rendering engine"
        }
      },
      {
        "name": "browser",
        "label": "名称",
        "searchable": {
          "type": "input-text",
          "name": "browser",
          "label": "名称",
          "placeholder": "输入名称"
        },
      },
      {
        "name": "version",
        "label": "Engine version",
        "searchable": {
          "type": "input-text",
          "name": "version",
          "label": "Engine version",
          "placeholder": "输入Engine version"
        }
      },
      {
        "name": "grade",
        "label": "更新时间",
        "format": "YYYY-MM-DD HH:mm:ss",
        "valueFormat": "x",
        "type": "date",
        "searchable": {
          "type": "ds-date-range-picker",
          "label": "更新时间",
        }
      },
      {
        "type": "wrapper",
        "size": "none",
        "label": "状态",
        "width": "15%",
        "body": [
          {
            "type": "tag",
            "displayMode": "bordered",
            "label": "${dataEnum[status]}",
            "color": "${colorEnum[status]}",
            "description": {
              "type": "link",
              "visibleOn": "${status===`init`}",
              "href": "https://www.baidu.com",
              "body": "注册审批中",
              "blank": true
            }
          }
        ],
        "searchable": {
          "type": "select",
          "source": "${OPTIONS}",
          "placeholder": "请选择状态",
          "clearable": true,
          "name": "status"
        },
      },
      {
        "type": "operation",
        "label": "操作",
        "buttons": [
          {
            "type": "button",
            "level": "link",
            "label": "详情",
            "actionType": "dialog",
            "dialog": {
              "title": "详情",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "删除",
            "type": "button",
            "actionType": "ajax",
            "level": "link",
            "disabled": true,
            "confirmText": "确认要删除吗？",
            "api": {
              "method": "delete",
              "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
            }
          },
          {
            "label": "编辑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "编辑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "空跑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "空跑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          }
        ]
      }
    ]
  }
}
```

## 组件用法

### 基本用法

最基本的用法是配置 **数据源接口(api)** 以及 **展示列(columns)**  
注意 CRUD 所需的数据必须放 items 中，因此如果只是想显示表格类型的数据没有分页，请使用 [Table](/dataseeddesigndocui/#/amis/zh-CN/components/table)。

```schema: scope="body"
{
  "type": "crud",
  "api": "/api/mock2/sample",
  "syncLocation": false,
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "engine",
      "label": "Rendering engine"
    },
    {
      "name": "browser",
      "label": "Browser"
    },
    {
      "name": "platform",
      "label": "Platform(s)"
    },
    {
      "name": "version",
      "label": "Engine version"
    },
    {
      "name": "grade",
      "label": "CSS grade"
    },
    {
      "type": "operation",
      "label": "操作",
      "width": 80,
      "buttons": [
        {
          "label": "详情",
          "type": "button",
          "level": "link",
          "actionType": "dialog",
          "dialog": {
            "title": "查看详情",
            "body": {
              "type": "form",
              "body": [
                {
                  "type": "input-text",
                  "name": "engine",
                  "label": "Engine"
                },
                {
                  "type": "input-text",
                  "name": "browser",
                  "label": "Browser"
                },
                {
                  "type": "input-text",
                  "name": "platform",
                  "label": "platform"
                },
                {
                  "type": "input-text",
                  "name": "version",
                  "label": "version"
                },
                {
                  "type": "control",
                  "label": "grade",
                  "body": {
                    "type": "tag",
                    "label": "${grade}",
                    "displayMode": "normal",
                    "color": "active"
                  }
                }
              ]
            }
          }
        },
        {
          "label": "删除",
          "type": "button",
          "level": "link",
          "disabledOn": "this.grade === 'A'"
        }
      ]
    }
  ]
}
```

### 数据源接口数据结构要求

- `items`或`rows`：用于返回数据源数据，格式是数组
- `total`或`count`: 用于返回数据库中一共有多少条数据，用于生成分页

```json
{
  "status": 0,
  "msg": "",
  "data": {
    "items": [
      {
        // 每一行的数据
        "id": 1,
        "xxx": "xxxx"
      }
    ],

    "total": 200 // 注意！！！这里不是当前请求返回的 items 的长度，而是数据库中一共有多少条数据，用于生成分页组件
    // 如果你不想要分页，把这个不返回就可以了。
  }
}
```

如果想要通过接口控制当前所处在第几页，可以返回字段 `page`（或自定义字段 `pageField` 的值）。

```json
{
  "status": 0,
  "msg": "",
  "data": {
    "items": [
      {
        // 每一行的数据
        "id": 1,
        "xxx": "xxxx"
      }
    ],

    "total": 200,
    "page": 20
  }
}
```

如果无法知道数据总数，只能知道是否有下一页，请返回如下格式，amis 会简单生成一个简单版本的分页控件。

```json
{
  "status": 0,
  "msg": "",
  "data": {
    "items": [
      {
        // 每个成员的数据。
        "id": 1,
        "xxx": "xxxx"
      }
    ],

    "hasNext": true // 是否有下一页。
  }
}
```

如果不需要分页，或者配置了 `loadDataOnce` 则可以忽略掉 `total` 和 `hasNext` 参数。

> 如果 api 地址中有变量，比如 `/api/mock2/sample/${id}`，amis 就不会自动加上分页参数，需要自己加上，改成 `/api/mock2/sample/${id}?page=${page}&perPage=${perPage}`

### 分页参数

默认的分页参数是 `page` 和 `perPage`，page 代表页数，比如第一页，perPage 代表每页显示几行。

如果要其它格式，比如转成 `limit` 和 `offset`，可以使用公式来转换，比如

`/api/mock2/sample?limit=${perPage}&offset=${(page - 1) * perPage}`

### 功能（增、删、改、查）

1. 增：新增功能其实还是依靠其它位置放个弹框表单完成，弹框完事了会自动让页面里面的 CRUD 刷新。
2. 删：主要有三种实现：单条操作、批量操作或者直接添加一个操作栏，在里面放个类型为 ajax 类型的按钮即可。在这个按钮里面能获得对应的行数据，而且完成后也会自动刷新这个 CRUD 列表。
3. 改：改和删其实是差不多的，唯一的区别在于，配置不同的 api，按钮类型改成弹框。  
   弹框里面可用数据自动就是点击的那一行的行数据，如果列表没有返回，可以在 form 里面再配置个 initApi 初始化数据，如果行数据里面有倒是不需要再拉取了。表单项的 name 跟数据 key 对应上便自动回显了。默认发送给表单的保存接口只会包含配置了的表单项，如果不够，请在 api 上配置数据映射，或者直接添加 hidden 类型的表单项（即隐藏域 input[type=hidden]）。
4. 查：就不单独介绍了，这个文档绝大部分都是关于查的。

```schema: scope="body"
{
  "type": "crud",
  "api": "/api/mock2/sample",
  "syncLocation": false,
  "autoGenerateFilter": {
    "defaultExpanded": true,
    "showBtnToolbar": false
  },
  "topToolbar": [
    {
      "label": "新增",
      "type": "button",
      "actionType": "dialog",
      "level": "primary",
      "dialog": {
        "title": "新增表单",
        "showCloseButton": false,
        "body": {
          "type": "form",
          "api": "post:/api/mock2/sample",
          "body": [
            {
              "type": "input-text",
              "name": "engine",
              "label": "Engine"
            },
            {
              "type": "input-text",
              "name": "browser",
              "label": "Browser"
            }
          ]
        }
      }
    }
  ],
  "columns": [
    {
      "name": "id",
      "label": "ID",
      "searchable": {
        "type": "input-text",
        "name": "id",
        "label": "主键",
        "placeholder": "输入id"
      }
    },
    {
      "name": "engine",
      "label": "Rendering engine",
      "searchable": true
    },
    {
      "name": "browser",
      "label": "Browser",
      "searchable": {
        "type": "select",
        "name": "browser",
        "label": "浏览器",
        "placeholder": "选择浏览器",
        "options": [
          {
            "label": "Internet Explorer ",
            "value": "ie"
          },
          {
            "label": "AOL browser",
            "value": "aol"
          },
          {
            "label": "Firefox",
            "value": "firefox"
          }
        ]
      }
    },
    {
      "name": "platform",
      "label": "Platform(s)",
      "headSearchable": true
    },
    {
      "name": "version",
      "label": "Engine version",
      "searchable": {
        "type": "input-number",
        "name": "version",
        "label": "版本号",
        "placeholder": "输入版本号",
        "mode": "horizontal"
      }
    },
    {
      "name": "grade",
      "label": "CSS grade",
      "headSearchable": {
        "type": "input-text",
        "name": "grade",
        "label": "CSS grade",
        "mode": "horizontal"
      }
    },
    {
      "type": "operation",
      "label": "操作",
      "buttons": [
        {
          "label": "删除",
          "type": "button",
          "actionType": "ajax",
          "level": "link",
          "confirmText": "确认要删除？",
          "api": "delete:/api/mock2/sample/${id}"
        },
        {
          "label": "修改",
          "type": "button",
          "level": "link",
          "actionType": "dialog",
          "dialog": {
            "title": "修改表单",
            "body": {
              "type": "form",
              "initApi": "/api/mock2/sample/${id}",
              "api": "post:/api/mock2/sample/${id}",
              "showCloseButton": false,
              "body": [
                {
                  "type": "input-text",
                  "name": "engine",
                  "label": "Engine"
                },
                {
                  "type": "input-text",
                  "name": "browser",
                  "label": "Browser"
                }
              ]
            }
          }
        }
      ]
    }
  ]
}
```

### 查询条件表单

大部分表格展示有对数据进行检索的需求，CRUD 自身支持通过配置`filter`，实现查询条件过滤表单。`filter` 配置实际上同 [Form](/dataseeddesigndocui/#/amis/zh-CN/components/form/index) 组件，因此支持绝大部分`form`的功能。

在条件搜索区的 `Engine` 输入框中输入任意值查询会发现结果中 `ID` 为 1 - 3 的 `Rendering engine` 列因为返回值中没有对应字段值，被错误填入了与 `filter` 中相同 `name` 的字段值，这是因为表格 Cell 通过[数据链](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/datascope-and-datachain)获取到了上层数据域 `filter` 中相同字段的数据值。这种情况可以在 CRUD `columns` 对应列配置`"canAccessSuperData": false`禁止访问父级数据域（比如: `Platform`列）。

在表头出现搜索，可配置 `headSearchable` 实现，且搜索后，在表头上方可回显搜索条件。

```schema: scope="body"
{
  "type": "crud",
  "name": "crud",
  "syncLocation": false,
  "api": "/api/mock2/crud/table4",
  "topToolbar": [
    {
      "type": "button",
      "label": "新建",
      "actionType": "url",
      "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
      "level": "primary",
      "blank": false
    }
  ],
  "canAccessSuperData": false,
  "updateAllRows": true,
  "filter": {
    "debug": true,
    "title": "",
    "body": [
      {
        "type": "group",
        "mode": "horizontal",
        "body": [
          {
            "type": "input-text",
            "name": "keywords",
            "label": "关键字",
            "clearable": true,
            "placeholder": "通过关键字搜索",
            "columnRatio": 4
          },
          {
            "type": "input-text",
            "name": "engine",
            "label": "Engine",
            "clearable": true,
            "columnRatio": 4
          },
          {
            "type": "input-text",
            "name": "platform",
            "label": "Platform",
            "clearable": true,
            "columnRatio": 4
          },
          {
            "type": "input-text",
            "name": "keywords1",
            "label": "关键字1",
            "clearable": true,
            "placeholder": "通过关键字搜索",
            "columnRatio": 4
          },
          {
            "type": "input-text",
            "name": "engine1",
            "label": "Engine1",
            "clearable": true,
            "columnRatio": 4
          },
          {
            "type": "input-text",
            "name": "platform1",
            "label": "Platform1",
            "clearable": true,
            "columnRatio": 4
          },
          {
            "type": "input-text",
            "name": "keywords2",
            "label": "关键字2",
            "clearable": true,
            "placeholder": "通过关键字搜索",
            "columnRatio": 4
          },
          {
            "type": "input-text",
            "name": "engine2",
            "label": "Engine2",
            "clearable": true,
            "columnRatio": 4
          },
          {
            "type": "input-text",
            "name": "platform2",
            "label": "Platform2",
            "clearable": true,
            "columnRatio": 4
          }
        ]
      }
    ],
      actions: [
          {
              "type": "reset",
              "label": "重 置"
          },
          {
              "type": "submit",
              "level": "primary",
              "label": "查 询"
          }
      ]
  },
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "engine",
      "label": "Rendering engine",
      "headSearchable": true
    },
    {
      "name": "browser",
      "label": "Browser",
      "headSearchable": {
        "type": "input-text",
        "name": "browser",
        "label": "Browser"
      }
    },
    {
      "name": "platform",
      "label": "Platform(s)"
    },
    {
      "name": "version",
      "label": "Engine version"
    },
    {
      "name": "grade",
      "label": "CSS grade"
    }
  ]
}
```

**请注意**：在默认没有自定义配置 api 数据映射时，提交查询条件表单，会自动将表单中的表单项值，发送给`crud`所配置的接口，然后通过后端接口，实现对数据的过滤操作，前端默认是不会进行任何的数据过滤操作

如果想前端实现过滤功能，请看前端一次性加载部分。

#### 自动生成查询区域

通过设置`"autoGenerateFilter": true`开启查询区域，会根据列元素的 `searchable` 属性值，自动生成查询条件表单，只有 `searchable` 属性值为合法的组件 Schema 时才会生成查询条件。注意这个属性和 `filter` 冲突，开启 `filter` 后 `autoGenerateFilter` 将会失效。

在表头出现搜索，可配置 `headSearchable` 实现，且搜索后，在表头上方可回显搜索条件。

**autoGenerateFilter 属性表**
| 属性名 | 类型 | 默认值 | 说明 |
| --------------- | --------- | ------- | ------------------------------------------------------------------------------------------------------- |
| ~~columnsNum~~ | `number` | | 虽然 amis 官方支持调整显示数量，由于组件库规范要求，已支持大于 2 行进行自动折叠收起显示, 不支持自行配置 |
| showBtnToolbar | `boolean` | `true` | 是否显示设置查询字段 |
| defaultExpanded | `boolean` | `false` | 设置默认是否全部展开 |

```schema: scope="body"
{
  "type": "crud",
  "api": "/api/mock2/sample",
  "syncLocation": false,
  "autoGenerateFilter": {
    "defaultExpanded": true,
    "showBtnToolbar": false
  },
  "columns": [
    {
      "name": "id",
      "label": "ID",
      "searchable": {
        "type": "input-text",
        "name": "id",
        "label": "主键",
        "placeholder": "输入id"
      }
    },
    {
      "name": "engine",
      "label": "Rendering engine",
      "searchable": true
    },
    {
      "name": "browser",
      "label": "Browser",
      "searchable": {
        "type": "select",
        "name": "browser",
        "label": "浏览器",
        "placeholder": "选择浏览器",
        "options": [
          {
            "label": "Internet Explorer ",
            "value": "ie"
          },
          {
            "label": "AOL browser",
            "value": "aol"
          },
          {
            "label": "Firefox",
            "value": "firefox"
          }
        ]
      }
    },
    {
      "name": "platform",
      "label": "Platform(s)",
      "headSearchable": true
    },
    {
      "name": "version",
      "label": "Engine version",
      "searchable": {
        "type": "input-number",
        "name": "version",
        "label": "版本号",
        "placeholder": "输入版本号",
        "mode": "horizontal"
      }
    },
    {
      "name": "grade",
      "label": "CSS grade",
      "headSearchable": {
        "type": "input-text",
        "name": "grade",
        "label": "CSS grade",
        "mode": "horizontal"
      }
    }
  ]
}
```

#### filter 支持高级搜索

`1.29.0` 版本，可通过配置 `filterFormAdvanceSearchAble: true` 支持 filter 模式的高级搜索。`filterFormAdvanceSearchAble` 默认值为 false。高级搜索功能要求 filter.body 中的配置，必须仅配置表单项，不能有类似使用 group 嵌套的情况出现。

```schema: scope="body"
{
  "type": "crud",
  "api": "/api/mock2/sample",
  "syncLocation": false,
  "filterFormAdvanceSearchAble": true,
  "filter": {
    "debug": false,
    "title": "",
    "body": [
      {
        "type": "input-text",
        "name": "keywords",
        "label": "关键字",
        "clearable": true,
        "placeholder": "通过关键字搜索",
        "columnRatio": 4
      },
      {
        "type": "input-text",
        "name": "engine",
        "label": "Engine",
        "clearable": true,
        "columnRatio": 4
      },
      {
        "type": "input-text",
        "name": "platform",
        "label": "Platform",
        "clearable": true,
        "columnRatio": 4
      },
      {
        "type": "input-text",
        "name": "keywords1",
        "label": "关键字1",
        "clearable": true,
        "placeholder": "通过关键字搜索",
        "columnRatio": 4
      },
      {
        "type": "input-text",
        "name": "engine1",
        "label": "Engine1",
        "clearable": true,
        "columnRatio": 4
      },
      {
        "type": "input-text",
        "name": "platform1",
        "label": "Platform1",
        "clearable": true,
        "columnRatio": 4
      },
      {
        "type": "input-text",
        "name": "keywords2",
        "label": "关键字2",
        "clearable": true,
        "placeholder": "通过关键字搜索",
        "columnRatio": 4
      },
      {
        "type": "input-text",
        "name": "engine2",
        "label": "Engine2",
        "clearable": true,
        "columnRatio": 4
      },
      {
        "type": "input-text",
        "name": "platform2",
        "label": "Platform2",
        "clearable": true,
        "columnRatio": 4
      }
    ],
    actions: [
      {
        "type": "reset",
        "label": "重 置"
      },
      {
        "type": "submit",
        "level": "primary",
        "label": "查 询"
      }
    ]
  },
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "engine",
      "label": "Rendering engine"
    },
    {
      "name": "browser",
      "label": "Browser"
    },
    {
      "name": "platform",
      "label": "Platform(s)"
    },
    {
      "name": "version",
      "label": "Engine version"
    },
    {
      "name": "grade",
      "label": "CSS grade"
    },
    {
      "type": "operation",
      "label": "操作",
      "width": 80,
      "buttons": [
        {
          "label": "详情",
          "type": "button",
          "level": "link",
          "actionType": "dialog",
          "dialog": {
            "title": "查看详情",
            "body": {
              "type": "form",
              "body": [
                {
                  "type": "input-text",
                  "name": "engine",
                  "label": "Engine"
                },
                {
                  "type": "input-text",
                  "name": "browser",
                  "label": "Browser"
                },
                {
                  "type": "input-text",
                  "name": "platform",
                  "label": "platform"
                },
                {
                  "type": "input-text",
                  "name": "version",
                  "label": "version"
                },
                {
                  "type": "control",
                  "label": "grade",
                  "body": {
                    "type": "tag",
                    "label": "${grade}",
                    "displayMode": "normal",
                    "color": "active"
                  }
                }
              ]
            }
          }
        },
        {
          "label": "删除",
          "type": "button",
          "level": "link",
          "disabledOn": "this.grade === 'A'"
        }
      ]
    }
  ]
}
```

### 配置默认请求参数

可以配置`defaultParams`，来指定拉取接口时的默认参数：

```schema: scope="body"
{
  "type": "crud",
  "syncLocation": false,
  "api": "/api/mock2/sample",
  "defaultParams": {
    "perPage": 50
  },
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "engine",
      "label": "Rendering engine"
    },
    {
      "name": "browser",
      "label": "Browser"
    },
    {
      "name": "platform",
      "label": "Platform(s)"
    },
    {
      "name": "version",
      "label": "Engine version"
    },
    {
      "name": "grade",
      "label": "CSS grade"
    }
  ]
}
```

例如上例中，配置`{ perPage: 50 }`，指定分页的默认每页数据条数为 50 条。

### 数据源接口轮询

可以配置`interval`来实现数据接口轮询功能，最低为`1000`毫秒：

```schema: scope="body"
{
  "type": "crud",
  "syncLocation": false,
  "api": "/api/mock2/sample",
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "engine",
      "label": "Rendering engine"
    },
    {
      "name": "browser",
      "label": "Browser"
    },
    {
      "name": "platform",
      "label": "Platform(s)"
    },
    {
      "name": "version",
      "label": "Engine version"
    },
    {
      "name": "grade",
      "label": "CSS grade"
    }
  ]
}
```

配置`stopAutoRefreshWhen`表达式，来实现满足条件，停止轮询

### 列配置

除了支持 [Table 中的列配置](/dataseeddesigndocui/#/amis/zh-CN/components/table#%E5%88%97%E9%85%8D%E7%BD%AE) 以外，crud 还支持下面这些配置，帮助更好的操作数据

#### 排序检索

可以在列上配置`"sortable": true`，该列表头右侧会渲染一个可点击的排序图标，可以切换`正序`和`倒序`。

```schema: scope="body"
{
  "type": "crud",
  "syncLocation": false,
  "api": "/api/mock2/sample",
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "engine",
      "label": "Rendering engine",
      "sortable": true
    }
  ]
}
```

amis 只负责生成排序组件，并将排序参数传递给接口，而不会在前端对数据进行排序处理。参数格式如下：

```json
{
  "orderBy": "engine", // 这里为所配置列的 name
  "orderDir": "asc" // asc 为升序，desc 为降序
}
```

你可以通过[数据映射](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/data-mapping)，在`api`中获取这些参数。

#### 快速搜索

可以在列上配置`"headSearchable": true`，该列表头右侧会渲染一个可点击的搜索图标，点击可以输入关键字进行该列的搜索：

```schema: scope="body"
{
  "type": "crud",
  "syncLocation": false,
  "api": "/api/mock2/sample",
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "engine",
      "label": "Rendering engine",
      "headSearchable": true
    }
  ]
}
```

amis 只负责生成搜索组件，并将搜索参数传递给接口，而不会在前端对数据进行搜索处理。参数格式如下：

```json
{
  "engine": "xxx" // 这里的key是列的 name，value是输入的关键字
}
```

你可以通过[数据映射](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/data-mapping)，在`api`中获取这些参数。

<!-- #### 快速过滤

可以在列上配置`filterable`属性，该列表头右侧会渲染一个可点击的过滤图标，点击显示下拉框，选中进行过滤：

```schema: scope="body"
{
  "type": "crud",
  "syncLocation": false,
  "api": "/api/mock2/sample",
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "grade",
      "label": "CSS grade",
      "filterable": {
        "options": [
          "A",
          "B",
          "C",
          "D",
          "X"
        ]
      }
    },
    {
      "name": "version",
      "label": "Version",
      "filterable": {
        "options": [
          {"label": "0", "value": 0},
          {"label": "1", "value": 1}
        ]
      }
    }
  ]
}
```

amis 只负责生成下拉选择器组件，并将搜索参数传递给接口，而不会在前端对数据进行搜索处理。参数格式如下：

```json
{
  "grade": "xxx" // 这里的key是列的 name，value是选中项的value值
}
```

你可以通过[数据映射](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/data-mapping)，在`api`中获取这些参数。 -->

#### 快速编辑

可以通过给列配置：`"quickEdit":true`和`quickSaveApi` 可以实现表格内快速编辑并批量保存的功能。

如下`Rendering engine`列的每一行中，会生成可编辑图标，点击后会显示弹框，用于编辑该列的值，

```schema: scope="body"
{
  "type": "crud",
  "syncLocation": false,
  "api": "/api/mock2/sample",
  "quickSaveApi": "/api/mock2/sample/bulkUpdate",
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "engine",
      "label": "Rendering engine",
      "quickEdit":true
    }
  ]
}
```

##### 指定编辑表单项类型

`quickEdit`也可以配置对象形式，可以指定编辑表单项的类型，例如`"type": "select"`：

```schema: scope="body"
{
  "type": "crud",
  "syncLocation": false,
  "api": "/api/mock2/sample",
  "quickSaveApi": "/api/mock2/sample/bulkUpdate",
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "grade",
      "label": "CSS grade",
      "quickEdit": {
        "type": "select",
        "options": [
          "A",
          "B",
          "C",
          "D",
          "X"
        ]
      }
    }
  ]
}
```

##### 快速编辑多个表单项

```schema: scope="body"
{
  "type": "crud",
  "syncLocation": false,
  "api": "/api/mock2/sample",
  "quickSaveApi": "/api/mock2/sample/bulkUpdate",
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "grade",
      "label": "CSS grade",
      "quickEdit": {
        "body": [
          {
            "type": "select",
            "name": "grade",
            "label": "grade",
            "options": [
              "A",
              "B",
              "C",
              "D",
              "X"
            ]
          },
          {
            "label": "id",
            "type": "input-text",
            "name": "id"
          }
        ]
      }
    }
  ]
}
```

<!-- #### 内联模式

配置`quickEdit`的`mode`为`inline`。可以直接将编辑表单项渲染至表格内，可以直接操作编辑。

```schema: scope="body"
{
  "type": "crud",
  "syncLocation": false,
  "api": "/api/mock2/sample",
  "quickSaveApi": "/api/mock2/sample/bulkUpdate",
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "grade",
      "label": "CSS grade",
      "quickEdit": {
        "mode": "inline",
        "type": "select",
        "size": "xs",
        "options": [
          "A",
          "B",
          "C",
          "D",
          "X"
        ]
      }
    },
    {
      "name": "switch",
      "label": "switch",
      "quickEdit": {
        "mode": "inline",
        "type": "switch",
        "onText": "开启",
        "offText": "关闭"
      }
    }
  ]
}
``` -->

##### 即时保存

如果想编辑完表单项之后，不想点击顶部确认按钮来进行保存，而是即时保存当前标记的数据，则需要配置 `quickEdit` 中的 `"saveImmediately": true`，然后配置接口`quickSaveItemApi`，可以直接将编辑表单项渲染至表格内操作。

```schema: scope="body"
{
  "type": "crud",
  "syncLocation": false,
  "api": "/api/mock2/sample",
  "quickSaveItemApi": "/api/mock2/sample/$id",
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "grade",
      "label": "CSS grade",
      "quickEdit": {
        "mode": "inline",
        "type": "select",
        "size": "xs",
        "options": [
          "A",
          "B",
          "C",
          "D",
          "X"
        ],
        "saveImmediately": true
      }
    },
    {
      "name": "switch",
      "label": "switch",
      "quickEdit": {
        "mode": "inline",
        "type": "switch",
        "onText": "开启",
        "offText": "关闭",
        "saveImmediately": true
      }
    }
  ]
}
```

你也可以在`saveImmediately`中配置 api，实现即时保存，如果要配合 api 的返回结果刷新其他接口，可以结合`quickEditConfig`的`reload`一起使用

```schema: scope="body"
{
  "type": "crud",
  "syncLocation": false,
  "api": "/api/mock2/sample",
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "grade",
      "label": "CSS grade",
      "quickEdit": {
        "mode": "inline",
        "type": "select",
        "size": "xs",
        "options": [
          "A",
          "B",
          "C",
          "D",
          "X"
        ],
        "saveImmediately": {
          "api": "/api/mock2/sample/$id"
        }
      }
    },
    {
      "name": "grade",
      "label": "CSS grade",
      "quickEdit": {
        "mode": "inline",
        "type": "switch",
        "onText": "开启",
        "offText": "关闭",
        "saveImmediately": true
      }
    }
  ]
}
```

##### 配置快速编辑启动条件

通过 `quickEditEnabledOn` 配置表达式来实现，如下，只有 id 小于 5 的数据可以编辑 engine。

```schema: scope="body"
{
  "type": "crud",
  "syncLocation": false,
  "api": "/api/mock2/sample",
  "quickSaveApi": "/api/mock2/sample/bulkUpdate",
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "engine",
      "label": "Rendering engine",
      "quickEdit":true,
      "quickEditEnabledOn": "${id < 5}"
    }
  ]
}
```

### 顶部和底部工具栏

crud 组件支持通过配置`headerToolbar`和`footerToolbar`属性，实现在表格顶部和底部渲染组件

```schema: scope="body"
{
  "type": "crud",
  "syncLocation": false,
  "api": "/api/mock2/sample",
  "headerToolbar": [
    {
      "type": "tpl",
      "tpl": "一共有${count}条数据"
    }
  ],
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "engine",
      "label": "Rendering engine"
    },
    {
      "name": "browser",
      "label": "Browser"
    },
    {
      "name": "platform",
      "label": "Platform(s)"
    },
    {
      "name": "version",
      "label": "Engine version"
    },
    {
      "name": "grade",
      "label": "CSS grade"
    }
  ]
}
```

上例中我们在顶部渲染了一段模板，通过`${count}`取到数据域中，CRUD 返回的`count`变量值；然后我们在底部渲染了一个按钮。

从上面一些例子中你可能已经发现，当我们不配置该属性时，crud 默认会在顶部和底部渲染一些组件，实际上，`headerToolbar`和`footerToolbar`默认会有下面这些配置：

```json
{
  "headerToolbar": ["bulkActions", "pagination"],
  "footerToolbar": ["statistics", "pagination"]
}
```

- 在顶部工具栏中：渲染批量操作按钮（如果在 crud 中，配置了 bulkActions 的话）和 分页组件
- 在底部工具栏中：渲染数据统计组件 和 分页组件

> 如果你不希望在顶部或者底部渲染默认组件，你可以设置`headerToolbar`和`footerToolbar`为空数组`[]`

这些组件还能设置 `align` 来控制位置，有 `left` 和 `right` 两种，比如

```json
{
  "headerToolbar": [
    {
      "type": "bulkActions",
      "align": "right"
    }
  ]
}
```

#### 分页

如果你的数据并不是很大，而且后端不方便做分页和条件过滤操作，那么通过配置`loadDataOnce`实现前端一次性加载并支持分页和条件过滤操作。

<div class="p-4 text-base text-gray-800 rounded-lg bg-gray-50" role="alert">
  <span class="font-medium text-gray-800 block">温馨提示</span>
  <span class="block">开启<code>loadDataOnce</code>后，搜索和过滤将交给组件处理，默认对所有字段采用模糊匹配（比如：<code>mi</code>将会匹配<code>amis</code>）。如果首次加载数据时设置了预设条件，导致接口返回的数据集合未按照此规则过滤，则可能导致切换页码后分页错误。此时有2种方案处理：</span>
  <span class="block" style="text-indent: 2em">1. 将接口返回的列表数据按照所有字段模糊匹配的规则处理</span>
  <span class="block" style="text-indent: 2em">2. 配置<a href="/dataseeddesigndocui/#/amis/zh-CN/components/crud?anchor=匹配函数"><code>matchFunc</code></a>，自定义处理过滤</span>
</div>

在`headerToolbar`或者`footerToolbar`数组中添加`pagination`字符串，并且在数据源接口中返回了数据总数`count`，即可以渲染分页组件；添加`switch-per-page`字符串，可以渲染切换每页条数组件

```schema: scope="body"
{
  "type": "crud",
  "syncLocation": false,
  "api": "/api/mock2/sample",
  "headerToolbar": [],
  "footerToolbar": ["pagination", "switch-per-page"],
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "grade",
      "label": "CSS grade",
      "quickEdit": {
        "mode": "inline",
        "type": "select",
        "size": "xs",
        "options": [
          "A",
          "B",
          "C",
          "D",
          "X"
        ],
        "saveImmediately": {
          "api": "/api/mock2/sample/$id"
        }
      }
    }
  ]
}
```

`crud`默认不会处理数据分页，只是会把分页参数传给后端，由后端实现分页，并返回需要展示的数据 和 总数据数`total`变量：

默认传给后端的分页参数格式为：

```json
{
  "page": 1,
  "perPage": 10
}
```

你可以通过配置`pageField`和`perPageField`来修改传给后端的分页数据格式，如：

```json
{
  "pageField": "pageNo",
  "perPageField": "pageSize"
}
```

这样传给后端的参数格式将为：

```json
{
  "pageNo": 1,
  "pageSize": 10
}
```

你可以通过[数据映射](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/data-mapping)，在`api`中获取这些参数。

```json
{
  "type": "crud",
  "api": {
    "method": "get",
    "url": "xxxxxx",
    "data": {
      "pageNo": "${page}",
      "pageSize": "${perPage}",
      ... // 一些其他参数
    }
  }
}
```

分页有两种模式：

###### 1. 知道数据总数

如果后端可以知道数据总数时，接口返回格式如下：

```json
{
  "status": 0,
  "msg": "",
  "data": {
    "items": [
      {
        // 每一行的数据。
        "id": 1,
        "xxx": "xxxx"
      }
    ],

    "total": 200 // 注意这里不是当前请求返回的 items 的长度，而是数据库一共有多少条数据，用于生成分页，
  }
}
```

该模式下，会自动计算总页码数，渲染出有页码的分页组件

###### 2. 不知道数据总数

如果后端无法知道数据总数，那么可以返回`hasNext`字段，来标识是否有下一页。

```json
{
  "status": 0,
  "msg": "",
  "data": {
    "items": [
      {
        // 每个成员的数据。
        "id": 1,
        "xxx": "xxxx"
      }
    ],

    "hasNext": true // 标识是否有下一页。
  }
}
```

这样 amis 会在配置分页组件的地方，渲染出一个简单的页面跳转控件。

> 如果总数据只够展示一页，则默认不显示该分页组件

###### 匹配函数

> `1.64.0` 及以上版本

支持自定义匹配函数`matchFunc`，当开启`loadDataOnce`时，会基于该函数计算的匹配结果进行过滤，主要用于处理列字段类型较为复杂或者字段值格式和后端返回不一致的场景，函数签名如下：

```typescript
interface CRUDMatchFunc {
  (
    /* 当前列表的全量数据 */
    items: any,
    /* 最近一次接口返回的全量数据 */
    itemsRaw: any,
    /** 相关配置 */
    options?: {
      /* 查询参数 */
      query: Record<string, any>;
      /* 列配置 */
      columns: any;
      /** match-sorter 匹配函数 */
      matchSorter: (
        items: any[],
        value: string,
        options?: MatchSorterOptions<any>,
      ) => any[];
    },
  ): any[];
}
```

从`1.64.0`版本开始，`options`中支持使用`matchSorter`函数处理复杂的过滤场景，比如前缀匹配、模糊匹配等，更多详细内容推荐查看[match-sorter](https://github.com/kentcdodds/match-sorter)。

#### 批量操作

在`headerToolbar`或者`footerToolbar`数组中添加`bulkActions`字符串，并且在 crud 上配置`bulkActions`行为按钮数组，可以实现选中表格项并批量操作的功能。

> 需要设置`primaryField`用于标识选中状态，配置当前行数据中的某一**唯一标识字段**，例如`id`，否则可能会出现无法选中的问题

```schema: scope="body"
{
  "type": "crud",
  "id": "crudPage",
  "syncLocation": false,
  "api": "/api/mock2/sample",
  "headerToolbar": [
    "bulkActions"
  ],
  "bulkActions": [
    {
      "label": "批量删除",
      "actionType": "ajax",
      "api": "delete:/api/mock2/sample/${ids|raw}",
      "confirmText": "确定要批量删除?"
    },
    {
      "label": "批量修改",
      "actionType": "dialog",
      "dialog": {
        "title": "批量编辑",
        "body": {
          "type": "form",
          "api": "/api/mock2/sample/bulkUpdate2",
          "body": [
            {
              "type": "hidden",
              "name": "ids"
            },
            {
              "type": "input-text",
              "name": "engine",
              "label": "Engine"
            }
          ],
          "onEvent": {
            "submitSucc": {
              "actions": [
                {
                  "actionType": "clearAll",
                  "componentId": "crudPage"
                }
              ]
            }
          }
        }
      }
    }
  ],
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "engine",
      "label": "Rendering engine"
    },
    {
      "name": "browser",
      "label": "Browser"
    },
    {
      "name": "platform",
      "label": "Platform(s)"
    },
    {
      "name": "version",
      "label": "Engine version"
    },
    {
      "name": "grade",
      "label": "CSS grade"
    }
  ]
}
```

如果不配置`headerToolbar`或者`footerToolbar`，只配置列选择，可通过配置`pickerMode` 和 `multiple` 来实现。
可通过配置`name`属性和`data`作用域上设置对应的`value`值，来实现列选择回显。

```schema
{
  "type": "page",
  "data": {
    "table": [
      {
        "id": 1
      }
    ]
  },
  "body": {
    "type": "crud",
    "syncLocation": false,
    "api": "/api/mock2/sample",
    "pickerMode": true,
    "multiple": true,
    "name": "table",
    "columns": [
      {
        "name": "id",
        "label": "ID"
      },
      {
        "name": "engine",
        "label": "Rendering engine"
      },
      {
        "name": "browser",
        "label": "Browser"
      },
      {
        "name": "platform",
        "label": "Platform(s)"
      },
      {
        "name": "version",
        "label": "Engine version"
      },
      {
        "name": "grade",
        "label": "CSS grade"
      }
    ]
  }
}
```

##### 批量操作数据域

批量操作会默认将下面数据添加到数据域中以供**按钮行为**使用，需要注意的是**静态**和**批量操作**时的数据域是不同的。**静态数据域**是指渲染批量操作区域时能够获取到的数据，**批量操作数据域**是指触发按钮动作时能够获取到的数据，具体区别参考下表：

| 属性名            | 类型                  | 所属数据域     | 说明                                                                                 | 版本 |
| ----------------- | --------------------- | -------------- | ------------------------------------------------------------------------------------ | ---- |
| `currentPageData` | `Array<Column>`       | 静态, 批量操作 | 当前分页数据集合，`Column`为当前 Table 数据结构定义                                  |      |
| `selectedItems`   | `Array<Column>`       | 静态, 批量操作 | 选中的行数据集合                                                                     |
| `unSelectedItems` | `Array<Column>`       | 静态, 批量操作 | 未选中的行数据集合                                                                   |
| `items`           | `Array<Column>`       | 批量操作       | `selectedItems` 的别名                                                               |
| `rows`            | `Array<Column>`       | 批量操作       | `selectedItems` 的别名，推荐用 `items`                                               |
| `ids`             | `string`              | 批量操作       | 多个 id 值用英文逗号隔开，前提是行数据中有 id 字段，或者有指定的 `primaryField` 字段 |
| `...rest`         | `Record<string, any>` | 批量操作       | 选中的行数据集合的首个元素的字段，注意列字段如果和以上字段重名时，会被上述字段值覆盖 |

你可以通过[数据映射](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/data-mapping)，在`api`中获取这些参数。

**约束批量操作**

有时候并不是勾选了就能支持批量操作的，比如想约束如果勾选了某条数据 owner 值不是当前用户的就不可以操作。

有两种方式来约束。

1. 批量操作按钮上配置 `disabledOn` 值为 `this.selectedItems.some(item => item.owner === this.amisUser.name)`
2. 给表格加上 `itemCheckableOn` 值为 `this.owner === this.amisUser.name` 表示只有 owner 是自己的才可以打勾。

**保留条目选择**

默认分页、搜索后，用户选择条目会被清空，配置`keepItemSelectionOnPageChange`属性后会保留用户选择，可以实现跨页面批量操作。
同时可以通过配置`maxKeepItemSelectionLength`属性限制最大勾选数

```schema: scope="body"
{
  "type": "crud",
  "syncLocation": false,
  "api": "/api/mock2/sample",
  "headerToolbar": [
    "bulkActions"
  ],
  "keepItemSelectionOnPageChange": true,
  "maxKeepItemSelectionLength": 4,
  "bulkActions": [
    {
      "label": "批量删除",
      "actionType": "ajax",
      "api": "delete:/api/mock2/sample/${ids|raw}",
      "confirmText": "确定要批量删除?"
    },
    {
      "label": "批量修改",
      "actionType": "dialog",
      "dialog": {
        "title": "批量编辑",
        "body": {
          "type": "form",
          "api": "/api/mock2/sample/bulkUpdate2",
          "body": [
            {
              "type": "hidden",
              "name": "ids"
            },
            {
              "type": "input-text",
              "name": "engine",
              "label": "Engine"
            }
          ]
        }
      }
    }
  ],
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "engine",
      "label": "Rendering engine"
    },
    {
      "name": "browser",
      "label": "Browser"
    },
    {
      "name": "platform",
      "label": "Platform(s)"
    },
    {
      "name": "version",
      "label": "Engine version"
    },
    {
      "name": "grade",
      "label": "CSS grade"
    }
  ]
}
```

还可以设置 `"checkOnItemClick": true` 属性来支持点击一行的触发选中状态切换

```schema: scope="body"
{
  "type": "crud",
  "syncLocation": false,
  "api": "/api/mock2/sample",
  "checkOnItemClick": true,
  "headerToolbar": [
    "bulkActions"
  ],
  "bulkActions": [
    {
      "label": "批量删除",
      "actionType": "ajax",
      "api": "delete:/api/mock2/sample/${ids|raw}",
      "confirmText": "确定要批量删除?"
    },
    {
      "label": "批量修改",
      "actionType": "dialog",
      "dialog": {
        "title": "批量编辑",
        "body": {
          "type": "form",
          "api": "/api/mock2/sample/bulkUpdate2",
          "body": [
            {
              "type": "hidden",
              "name": "ids"
            },
            {
              "type": "input-text",
              "name": "engine",
              "label": "Engine"
            }
          ]
        }
      }
    }
  ],
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "engine",
      "label": "Rendering engine"
    },
    {
      "name": "browser",
      "label": "Browser"
    },
    {
      "name": "platform",
      "label": "Platform(s)"
    },
    {
      "name": "version",
      "label": "Engine version"
    },
    {
      "name": "grade",
      "label": "CSS grade"
    }
  ]
}
```

### 弹框与数据链

一般 CRUD 中会有弹框，然后进行数据展示或进行二次编辑的需求，通过在列中配置按钮，然后配置弹框，弹框内配置相应的组件即可。

现在问题是，如何获取到当前操作行的数据呢？

实际上，你操作当前行数据，会成为弹框这层节点的父级节点，因此你可以通过 [数据链](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/datascope-and-datachain)，获取到上层，也就是点击的行的数据，具体获取方法和普通组件获取数据域中数据的方法相同，

```schema: scope="body"
{
  "type": "crud",
  "syncLocation": false,
  "api": "/api/mock2/sample",
  "draggable": true,
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "engine",
      "label": "Rendering engine"
    },
    {
      "name": "browser",
      "label": "Browser"
    },
    {
      "name": "platform",
      "label": "Platform(s)"
    },
    {
      "name": "version",
      "label": "Engine version"
    },
    {
      "name": "grade",
      "label": "CSS grade"
    },
    {
      "type": "button",
      "label": "一个弹框",
      "actionType": "dialog",
      "dialog": {
        "title": "一个弹框",
        "body": [
          {
            "type": "tpl",
            "tpl": "行数据中 Browser 值为：${browser}"
          },
          {
            "type": "divider"
          },
          {
            "type": "form",
            "api": "/api/mock2/sample/$id",
            "body": [
              {
                "type": "input-text",
                "name": "engine",
                "label": "Engine"
              }
            ]
          }
        ]
      }
    }
  ]
}
```

例如上例中 Tpl 用 `${browser}` 获取 `browser` 变量，Form 中配置`"name": "engine"` 映射 `engine` 变量。

> 遇到数据字段冲突时，可以在 弹框上通过配置数据映射 解决。

### 拖拽排序

通过配置`"draggable": true`和保存排序接口`saveOrderApi`，可以实现拖拽排序功能。

```schema: scope="body"
{
  "type": "crud",
  "syncLocation": false,
  "api": "/api/mock2/sample",
  "draggable": true,
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "engine",
      "label": "Rendering engine"
    },
    {
      "name": "browser",
      "label": "Browser"
    },
    {
      "name": "platform",
      "label": "Platform(s)"
    },
    {
      "name": "version",
      "label": "Engine version"
    },
    {
      "name": "grade",
      "label": "CSS grade"
    }
  ]
}
```

同样的，前端是不会处理排序结果，需要后端调用接口`saveOrderApi`来保存新的顺序

发送方式默认为`POST`，会包含以下信息。

- `ids` 字符串如： `2,3,1,4,5,6` 用 id 来记录新的顺序。 前提是你的列表接口返回了 id 字段。另外如果你的 primaryField 不是 `id`，则需要配置如： `primaryField: "order_id"`。注意：无论你配置成什么 primayField，这个字段名始终是 ids。
- `rows` `Array<Item>` 数组格式，新的顺序，数组里面包含所有原始信息。
- `page` `perPage` 在`1.4.1`版本中支持分页参数。
- `insertAfter` 或者 `insertBefore` 这是 amis 生成的 diff 信息，对象格式，key 为目标成员的 primaryField 值，即 id，value 为数组，数组中存放成员
  primaryField 值。如：

  ```json
  {
    "insertAfter": {
      "2": ["1", "3"],
      "6": ["4", "5"]
    }
  }
  ```

  表示：成员 1 和成员 3 插入到了成员 2 的后面。成员 4 和 成员 5 插入到了 成员 6 的后面。

你可以通过[数据映射](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/data-mapping)，在`api`中获取这些参数。

如下:

```json
{
  "saveOrderApi": {
    "url": "/api/xxxx",
    "data": {
      "ids": "${ids}",
      "page": "${page}"
    }
  }
}
```

这样就只会发送 ids 了。

#### 列排序

通过配置`headerToolbar` 中 `columns-toggler` 的 `"draggable": true`可以实现设置显示列和列排序功能。

```schema: scope="body"
{
  "type": "crud",
  "api": "/api/mock2/sample",
  "syncLocation": false,
  "columnsTogglable": true,
  "headerToolbar": [
    {
      "type": "columns-toggler",
      "align": "left",
      "draggable": true,
      "icon": "fas fa-cog",
      "overlay": true,
      "footerBtnSize": "sm"
    }
  ],
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "engine",
      "label": "Rendering engine"
    },
    {
      "name": "browser",
      "label": "Browser"
    },
    {
      "name": "platform",
      "label": "Platform(s)"
    },
    {
      "name": "version",
      "label": "Engine version"
    },
    {
      "name": "grade",
      "label": "CSS grade"
    }
  ]
}
```

#### 禁止显示列勾选操作

通过配置 `"toggable": false` 可以实现该列在 `点击选择显示列` 时禁止勾选操作
[配合 columnsTogglable 使用](/dataseeddesigndocui/#/amis/zh-CN/components/table#是否在选择显示列中显示)

```schema: scope="body"
{
  "type": "crud",
  "api": "/api/mock2/sample",
  "syncLocation": false,
  "headerToolbar": [
    {
      "type": "columns-toggler",
      "align": "left",
      "draggable": true,
      "icon": "fas fa-cog",
      "overlay": true,
      "footerBtnSize": "sm"
    }
  ],
  "columns": [
    {
      "name": "id",
      "label": "ID",
      "toggable": false
    },
    {
      "name": "engine",
      "label": "Rendering engine"
    },
    {
      "name": "browser",
      "label": "Browser"
    },
    {
      "name": "platform",
      "label": "Platform(s)"
    },
    {
      "name": "version",
      "label": "Engine version"
    },
    {
      "name": "grade",
      "label": "CSS grade"
    }
  ]
}
```

### 单条操作

当操作对象是单条数据时这类操作叫单条操作，比如：编辑、删除、通过、拒绝等等。CRUD 的 table 模式可以在 column 通过放置按钮来完成（其他模式参考 table 模式）。比如编辑就是添加个按钮行为是弹框类型的按钮或者添加一个页面跳转类型的按钮把当前行数据的 id 放在 query 中传过去、删除操作就是配置一个按钮行为是 AJAX 类型的按钮，将数据通过 api 发送给后端完成。

CRUD 中不限制有多少个单条操作、添加一个操作对应的添加一个按钮就行了。CRUD 在处理按钮行为的时候会把当前行的完整数据传递过去，如果你的按钮行为是弹出时，还会包含一下信息：

- `hasNext` `boolean` 当按钮行为是弹框时，还会携带这个数据可以用来判断当前页中是否有下一条数据。
- `hasPrev` `boolean` 当按钮行为是弹框时，还会携带这个数据可以判断用来当前页中是否有上一条数据。
- `index` `number` 当按钮行为是弹框时，还会携带这个数据可以用来获取当前行数据在这一页中的位置。
- `prevIndex` `number`
- `nextIndex` `number`

你可以通过[数据映射](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/data-mapping)，在`api`中获取这些参数。

如果你的按钮类型是 ajax，你也可以限定只发送部分数据比如。

```json
{
  "type": "button",
  "label": "删除",
  "actionType": "ajax",
  "api": "delete:/api/xxxx/$id",
  "confirmText": "确定要删除？"
}
```

上面这个例子就会发送 id 字段了，如果想要全部发送过去同时还想添加点别的字段就这样：

```json
{
  "type": "button",
  "label": "删除",
  "actionType": "ajax",
  "api": {
    "method": "post",
    "url": "/api/xxxx/$id",
    "data": {
      "&": "$$",
      "op": "delete"
    }
  },
  "confirmText": "确定要删除？"
}
```

> **注意：** 如果使用`feedback`弹窗，如果不想关闭弹窗时触发`crud`再次拉取数据，需要设置`button`的`"reload":"none"`

### 过滤条件参数同步地址栏

默认 CRUD 会将过滤条件参数同步至浏览器地址栏中，比如搜索条件、当前页数，这也做的目的是刷新页面的时候还能进入之前的分页。常见场景：从详情页返回至列表页希望保留原先列表页的查询参数，通常搭配 env 的 updateLocation 一起处理，注意，此时的重置按钮应该使用 clear，若使用 reset 会无法清空查询条件。

但也会导致地址栏中的参数数据合并到顶层的数据链中，例如：自动给同名的表单项设置默认值。如果不希望这个功能，可以设置 `syncLocation: false` 来关闭。

> 本文中的例子为了不相互影响都关闭了这个功能。
> 另外如果需要使用接口联动，需要设置`syncLocation: false`

### 前端一次性加载

如果你的数据并不是很大，而且后端不方便做分页和条件过滤操作，那么通过配置`loadDataOnce`实现前端一次性加载并支持分页和条件过滤操作。

```schema: scope="body"
{
  "type": "crud",
  "syncLocation": false,
  "api": "/api/mock2/sample",
  "loadDataOnce": true,
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "engine",
      "label": "Rendering engine"
    },
    {
      "name": "browser",
      "label": "Browser"
    },
    {
      "name": "platform",
      "label": "Platform(s)"
    },
    {
      "name": "version",
      "label": "Engine version"
    },
    {
      "name": "grade",
      "label": "CSS grade",
      "sortable": true
    }
  ]
}
```

配置一次性加载后，基本的分页、快速排序操作将会在前端进行完成。如果想实现前端检索(目前是模糊搜索)，可以在 table 的 `columns` 对应项配置 `searchable` 来实现。

```schema: scope="body"
{
  "type": "crud",
  "syncLocation": false,
  "api": "/api/mock2/sample",
  "loadDataOnce": true,
  "autoGenerateFilter": {
    "showBtnToolbar": false,
    "defaultExpanded": false
  },
  "filterSettingSource": ["browser", "version"],
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "engine",
      "label": "Rendering engine"
    },
    {
      "name": "browser",
      "label": "Browser"
    },
    {
      "name": "platform",
      "label": "Platform(s)"
    },
    {
      "name": "version",
      "label": "Engine version",
      "searchable": {
        "type": "select",
        "name": "version",
        "label": "Engine version",
        "clearable": true,
        "multiple": true,
        "searchable": true,
        "checkAll": true,
        "options": ["1.7", "3.3", "5.6"],
        "maxTagCount": 10,
        "extractValue": true,
        "joinValues": false,
        "delimiter": ',',
        "defaultCheckAll": false,
        "checkAllLabel": "全选"
      }
    },
    {
      "name": "grade",
      "label": "CSS grade"
    }
  ]
}
```

> **注意：**如果你的数据量较大，请务必使用服务端分页的方案，过多的前端数据展示，会显著影响前端页面的性能

### 动态列

可以直接通过 crud 的数据接口返回。
用这种方式可以简化动态列的实现，与 items 并列返回 columns 数组即可。

```schema: scope="body"
{
  "type": "crud",
  "api": "/api/mock2/crud/dynamic?waitSeconds=1"
}
```

### 使用数据链中的数据

可以通过 `source` 属性来自定义去返回数据的字段，或者取数据域中的数据，比如

```
{
  "type": "page",
  "data": {
    "myItems": [
      {
        "id": 1
      }
    ]
  },
  "body": {
    "type": "crud",
    "source": "${myItems}",
    "columns": [
      {
        "name": "id",
        "label": "ID"
      }
    ]
  }
}
```

#### table 模式使用数据链中的数据且本地分页

```schema
{
	"type": "page",
	"data": {
		"myItems": [{
			"id": 1
		}, {
			"id": 2
		}, {
			"id": 3
		}, {
			"id": 4
		}, {
			"id": 5
		}, {
			"id": 6
		}, {
			"id": 7
		}, {
			"id": 8
		}, {
			"id": 9
		}, {
			"id": 10
		}, {
			"id": 11
		}]
	},
	"body": [{
		"type": "crud",
		"source": "${myItems}",
		"perPage": 5,
		"alwaysShowPagination": true,
		"syncLocation": false,
    "footerToolbar": [
      {
        "type": "pagination",
        "layout": "total,pager,perPage,go",
        "perPageAvailable": [5, 10, 20, 50],
      }
    ],
		"columns": [{
			"name": "id",
			"label": "ID"
		}]
	}]
}
```

### 自定义点击行的行为

配置 `itemAction` 可以实现点击某一行后进行自定义操作，支持 [action](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/action) 里的所有配置，比如弹框、刷新其它组件等。

```schema: scope="body"
{
  "type": "crud",
  "api": "/api/mock2/sample",
  "syncLocation": false,
  "itemAction": {
    "type": "button",
    "actionType": "dialog",
    "dialog": {
      "title": "详情",
      "body": "当前行的数据 browser: ${browser}, version: ${version}",
      "actions": []
    }
  },
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "engine",
      "label": "Rendering engine"
    },
    {
      "name": "browser",
      "label": "Browser"
    },
    {
      "name": "platform",
      "label": "Platform(s)"
    },
    {
      "name": "version",
      "label": "Engine version"
    },
    {
      "name": "grade",
      "label": "CSS grade"
    }
  ]
}
```

注意这个属性和 `checkOnItemClick` 冲突，因为都是定义行的点击行为，开启 `itemAction` 后 `checkOnItemClick` 将会失效。

itemAction 里的 onClick 还能通过 `data` 参数拿到当前行的数据，方便进行下一步操作

```schema: scope="body"
{
  "type": "crud",
  "api": "/api/mock2/sample",
  "syncLocation": false,
  "itemAction": {
    "type": "button",
    "onClick": "console.log(data); alert(data.engine)"
  },
  "columns": [
    {
      "name": "id",
      "label": "ID"
    },
    {
      "name": "engine",
      "label": "Rendering engine"
    },
    {
      "name": "browser",
      "label": "Browser"
    },
    {
      "name": "platform",
      "label": "Platform(s)"
    },
    {
      "name": "version",
      "label": "Engine version"
    },
    {
      "name": "grade",
      "label": "CSS grade"
    }
  ]
}
```

### 特定列列自动滚动至视图内

当表格列非常多的时候可以通过在`columns`上设置`scrollIntoView: true` 使得此列在渲染后自动滚动至视图内部，也可以通过动作去指定某列滚动到视图内部，如果多列配置了`scrollIntoView`则只有第一列生效。

```schema
{
  "type": "page",
  "id": "scrollPage",
  "body": [
    {
      "type": "button",
      "label": "滚动到: id",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "scrollIntoView",
              "componentId": "crud",
              "args": {
                "columnName": "id"
              }
            }
          ]
        }
      }
    },
    {
      "type": "button",
      "label": "滚动到: day10",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "scrollIntoView",
              "componentId": "crud",
              "args": {
                "columnName": "day10"
              }
            }
          ]
        }
      }
    },
    {
      "type": "button",
      "label": "滚动到: day20",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "scrollIntoView",
              "componentId": "crud",
              "args": {
                "columnName": "day20"
              }
            }
          ]
        }
      }
    },
    {
      "type": "crud",
      "id": "crud",
      "api": "/api/mock2/sample",
      "syncLocation": false,
      "perPage": 5,
      "columns": [
        {
          "name": "id",
          "label": "ID",
          "fixed": "left"
        },
        {
          "name": "engine",
          "label": "Rendering engine"
        },
        {
          "name": "browser",
          "label": "Browser"
        },
        {
          "label": "1日",
          "name": "day1"
        },
        {
          "label": "2日",
          "name": "day2"
        },
        {
          "label": "3日",
          "name": "day3"
        },
        {
          "label": "4日",
          "name": "day4"
        },
        {
          "label": "5日",
          "name": "day5"
        },
        {
          "label": "6日",
          "name": "day6"
        },
        {
          "label": "7日",
          "name": "day7"
        },
        {
          "label": "8日",
          "name": "day8"
        },
        {
          "label": "9日",
          "name": "day9"
        },
        {
          "label": "10日",
          "name": "day10"
        },
        {
          "label": "11日",
          "name": "day11"
        },
        {
          "label": "12日",
          "name": "day12"
        },
        {
          "label": "13日",
          "name": "day13"
        },
        {
          "label": "14日",
          "name": "day14"
        },
        {
          "label": "15日",
          "name": "day15"
        },
        {
          "label": "16日",
          "name": "day16"
        },
        {
          "label": "17日",
          "name": "day17"
        },
        {
          "label": "18日",
          "name": "day18"
        },
        {
          "label": "19日",
          "name": "day19"
        },
        {
          "label": "20日",
          "name": "day20"
        },
        {
          "label": "21日",
          "name": "day21"
        },
        {
          "label": "22日",
          "name": "day22"
        },
        {
          "label": "23日(默认滚动)",
          "name": "day23",
          "scrollIntoView": true
        },
        {
          "label": "24日",
          "name": "day24"
        },
        {
          "label": "25日",
          "name": "day25"
        },
        {
          "label": "26日",
          "name": "day26"
        },
        {
          "label": "27日",
          "name": "day27"
        },
        {
          "label": "28日",
          "name": "day28"
        },
        {
          "label": "29日",
          "name": "day29"
        },
        {
          "label": "30日",
          "name": "day30"
        },
        {
          "label": "31日",
          "name": "day31"
        },
        {
          "type": "show-more",
          "label": "操作",
          "width": 80,
          "buttons": [
            {
              "label": "详情",
              "type": "button",
              "level": "link",
              "actionType": "dialog",
              "dialog": {
                "title": "查看详情",
                "body": {
                  "type": "form",
                  "body": [
                    {
                      "type": "input-text",
                      "name": "engine",
                      "label": "Engine"
                    },
                    {
                      "type": "input-text",
                      "name": "browser",
                      "label": "Browser"
                    },
                    {
                      "type": "input-text",
                      "name": "platform",
                      "label": "platform"
                    },
                    {
                      "type": "input-text",
                      "name": "version",
                      "label": "version"
                    },
                    {
                      "type": "control",
                      "label": "grade",
                      "body": {
                        "type": "tag",
                        "label": "${grade}",
                        "displayMode": "normal",
                        "color": "active"
                      }
                    }
                  ]
                }
              }
            },
            {
              "label": "删除",
              "type": "button",
              "level": "link",
              "disabledOn": "this.grade === 'A'"
            }
          ]
        }
      ]
    }
  ]
}
```

如要配置表达式则可使用`scrollIntoViewOn`

```schema
{
  "type": "page",
  "id": "myPage",
  "data": {
    "num": 1
  },
  "body": [
    {
      "type": "button",
      "label": "设置num: ${num}",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "setValue",
              "componentId": "myPage",
              "args": {
                "value": {
                  "num": 2
                }
              }
            }
          ]
        }
      }
    },
    {
      "type": "crud",
      "id": "myCrud",
      "api": "/api/mock2/sample",
      "syncLocation": false,
      "perPage": 5,
      "columns": [
        {
          "name": "id",
          "label": "ID",
          "fixed": "left"
        },
        {
          "name": "engine",
          "label": "Rendering engine"
        },
        {
          "name": "browser",
          "label": "Browser"
        },
        {
          "label": "1日",
          "name": "day1"
        },
        {
          "label": "2日",
          "name": "day2"
        },
        {
          "label": "3日",
          "name": "day3"
        },
        {
          "label": "4日",
          "name": "day4"
        },
        {
          "label": "5日",
          "name": "day5"
        },
        {
          "label": "6日",
          "name": "day6"
        },
        {
          "label": "7日",
          "name": "day7"
        },
        {
          "label": "8日",
          "name": "day8"
        },
        {
          "label": "9日",
          "name": "day9"
        },
        {
          "label": "10日",
          "name": "day10"
        },
        {
          "label": "11日",
          "name": "day11"
        },
        {
          "label": "12日",
          "name": "day12"
        },
        {
          "label": "13日",
          "name": "day13"
        },
        {
          "label": "14日",
          "name": "day14"
        },
        {
          "label": "15日",
          "name": "day15"
        },
        {
          "label": "16日",
          "name": "day16"
        },
        {
          "label": "17日",
          "name": "day17"
        },
        {
          "label": "18日",
          "name": "day18"
        },
        {
          "label": "19日",
          "name": "day19"
        },
        {
          "label": "20日",
          "name": "day20"
        },
        {
          "label": "21日",
          "name": "day21"
        },
        {
          "label": "22日",
          "name": "day22"
        },
        {
          "label": "23日",
          "name": "day23",
          "scrollIntoViewOn": "${num === 2}"
        },
        {
          "label": "24日",
          "name": "day24"
        },
        {
          "label": "25日",
          "name": "day25"
        },
        {
          "label": "26日",
          "name": "day26"
        },
        {
          "label": "27日",
          "name": "day27"
        },
        {
          "label": "28日",
          "name": "day28"
        },
        {
          "label": "29日",
          "name": "day29"
        },
        {
          "label": "30日",
          "name": "day30"
        },
        {
          "label": "31日",
          "name": "day31"
        },
        {
          "type": "show-more",
          "label": "操作",
          "width": 80,
          "buttons": [
            {
              "label": "详情",
              "type": "button",
              "level": "link",
              "actionType": "dialog",
              "dialog": {
                "title": "查看详情",
                "body": {
                  "type": "form",
                  "body": [
                    {
                      "type": "input-text",
                      "name": "engine",
                      "label": "Engine"
                    },
                    {
                      "type": "input-text",
                      "name": "browser",
                      "label": "Browser"
                    },
                    {
                      "type": "input-text",
                      "name": "platform",
                      "label": "platform"
                    },
                    {
                      "type": "input-text",
                      "name": "version",
                      "label": "version"
                    },
                    {
                      "type": "control",
                      "label": "grade",
                      "body": {
                        "type": "tag",
                        "label": "${grade}",
                        "displayMode": "normal",
                        "color": "active"
                      }
                    }
                  ]
                }
              }
            },
            {
              "label": "删除",
              "type": "button",
              "level": "link",
              "disabledOn": "this.grade === 'A'"
            }
          ]
        }
      ]
    }
  ]
}
```

### 特定列高亮

可以通过给`columns`配置`isHighLight: true` 使得某一列高亮，也可以使用动作来控制那一列高亮

```schema
{
  "type": "page",
  "id": "highLightPage",
  "data": {
    "num": 1
  },
  "body": [
    {
      "type": "button",
      "label": "高亮: id",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "setHighLightColumn",
              "componentId": "highLightCrud",
              "args": {
                "columnName": "id"
              }
            }
          ]
        }
      }
    },
    {
      "type": "button",
      "label": "高亮: engine",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "setHighLightColumn",
              "componentId": "highLightCrud",
              "args": {
                "columnName": "engine"
              }
            }
          ]
        }
      }
    },
    {
      "type": "button",
      "label": "高亮: browser",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "setHighLightColumn",
              "componentId": "highLightCrud",
              "args": {
                "columnName": "browser"
              }
            }
          ]
        }
      }
    },
    {
      "type": "button",
      "label": "取消高亮",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "removeHighLightColumn",
              "componentId": "highLightCrud"
            }
          ]
        }
      }
    },
    {
      "type": "crud",
      "id": "highLightCrud",
      "api": "/api/mock2/sample",
      "syncLocation": false,
      "perPage": 5,
      "columns": [
        {
          "name": "id",
          "label": "ID"
        },
        {
          "name": "engine",
          "label": "Rendering engine",
          "isHighLight": true
        },
        {
          "name": "browser",
          "label": "Browser"
        },
        {
          "type": "show-more",
          "label": "操作",
          "width": 80,
          "buttons": [
            {
              "label": "详情",
              "type": "button",
              "level": "link",
              "actionType": "dialog",
              "dialog": {
                "title": "查看详情",
                "body": {
                  "type": "form",
                  "body": [
                    {
                      "type": "input-text",
                      "name": "engine",
                      "label": "Engine"
                    },
                    {
                      "type": "input-text",
                      "name": "browser",
                      "label": "Browser"
                    },
                    {
                      "type": "input-text",
                      "name": "platform",
                      "label": "platform"
                    },
                    {
                      "type": "input-text",
                      "name": "version",
                      "label": "version"
                    },
                    {
                      "type": "control",
                      "label": "grade",
                      "body": {
                        "type": "tag",
                        "label": "${grade}",
                        "displayMode": "normal",
                        "color": "active"
                      }
                    }
                  ]
                }
              }
            },
            {
              "label": "删除",
              "type": "button",
              "level": "link",
              "disabledOn": "this.grade === 'A'"
            }
          ]
        }
      ]
    }
  ]
}
```

也可以使用`isHighLightOn`来配置表达式

```schema
{
  "type": "page",
  "id": "highLightPage1",
  "data": {
    "num": 1
  },
  "body": [
    {
      "type": "button",
      "label": "设置num: ${num}",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "setValue",
              "componentId": "highLightPage1",
              "args": {
                "value": {
                    "num": "${num + 1}"
                }
              }
            }
          ]
        }
      }
    },

    {
      "type": "button",
      "label": "取消高亮",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "removeHighLightColumn",
              "componentId": "highLightCrud1"
            }
          ]
        }
      }
    },
    {
      "type": "crud",
      "id": "highLightCrud1",
      "api": "/api/mock2/sample",
      "syncLocation": false,
      "perPage": 5,
      "columns": [
        {
          "name": "id",
          "label": "ID"
        },
        {
          "name": "engine",
          "label": "Rendering engine",
          "isHighLightOn": "${num % 2 === 1}"
        },
        {
          "name": "browser",
          "label": "Browser"
        },
        {
          "type": "show-more",
          "label": "操作",
          "width": 80,
          "buttons": [
            {
              "label": "详情",
              "type": "button",
              "level": "link",
              "actionType": "dialog",
              "dialog": {
                "title": "查看详情",
                "body": {
                  "type": "form",
                  "body": [
                    {
                      "type": "input-text",
                      "name": "engine",
                      "label": "Engine"
                    },
                    {
                      "type": "input-text",
                      "name": "browser",
                      "label": "Browser"
                    },
                    {
                      "type": "input-text",
                      "name": "platform",
                      "label": "platform"
                    },
                    {
                      "type": "input-text",
                      "name": "version",
                      "label": "version"
                    },
                    {
                      "type": "control",
                      "label": "grade",
                      "body": {
                        "type": "tag",
                        "label": "${grade}",
                        "displayMode": "normal",
                        "color": "active"
                      }
                    }
                  ]
                }
              }
            },
            {
              "label": "删除",
              "type": "button",
              "level": "link",
              "disabledOn": "this.grade === 'A'"
            }
          ]
        }
      ]
    }
  ]
}
```

### 属性表

| 属性名                         | 类型                                                                    | 默认值                          | 说明                                                                                                                                                          | 版本                      |
| ------------------------------ | ----------------------------------------------------------------------- | ------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------- |
| type                           | `string`                                                                |                                 | `type` 指定为 CRUD 渲染器                                                                                                                                     |
| mode                           | `string`                                                                | `"table"`                       | `"table" 、 "cards" 或者 "list"`                                                                                                                              | `1.22.0` 版本支持配置变量 |
| title                          | `string`                                                                | `""`                            | 可设置成空，当设置成空时，没有标题栏                                                                                                                          |
| className                      | `string`                                                                |                                 | 表格外层 Dom 的类名                                                                                                                                           |
| api                            | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api)                 |                                 | CRUD 用来获取列表数据的 api。                                                                                                                                 |
| loadDataOnce                   | `boolean`                                                               |                                 | 是否一次性加载所有数据（前端分页）                                                                                                                            |
| loadDataOnceFetchOnFilter      | `boolean`                                                               | `true`                          | 在开启 loadDataOnce 时，filter 时是否去重新请求 api                                                                                                           |
| supportEmptyStringOnFilter     | `boolean`                                                               |                                 | 本地过滤是否支持空字符串. "select"、"input-text" 等表单输入项，用户手动清空后，默认值是空字符串，关闭 loadDataOnceFetchOnFilter 场景下，开启后本地过滤支持 "" | `1.16.0`                  |
| source                         | `string`                                                                |                                 | 数据映射接口返回某字段的值，不设置会默认使用接口返回的`${items}`或者`${rows}`，也可以设置成上层数据源的内容                                                   |
| filter                         | [Form](/dataseeddesigndocui/#/amis/zh-CN/components/form/index)         |                                 | 设置过滤器，当该表单提交后，会把数据带给当前 `mode` 刷新列表。                                                                                                |
| filterTogglable                | `boolean`                                                               | `false`                         | 是否可显隐过滤器                                                                                                                                              |
| filterDefaultVisible           | `boolean`                                                               | `true`                          | 设置过滤器默认是否可见。                                                                                                                                      |
| headerFilter                   | [Form](/dataseeddesigndocui/#/amis/zh-CN/components/form/index)         |                                 | table 模式，设置非追随过滤条件，当表单项数据发生变化时，会把数据带给当前 `mode` 刷新列表。                                                                    | `1.21.0`                  |
| initFetch                      | `boolean`                                                               | `true`                          | 是否初始化的时候拉取数据, 只针对有 filter 的情况, 没有 filter 初始都会拉取数据                                                                                |
| interval                       | `number`                                                                | `3000`                          | 刷新时间(最低 1000)                                                                                                                                           |
| silentPolling                  | `boolean`                                                               | `false`                         | 配置刷新时是否隐藏加载动画                                                                                                                                    |
| stopAutoRefreshWhen            | `string`                                                                | `""`                            | 通过[表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression)来配置停止刷新的条件                                                                  |
| stopAutoRefreshWhenModalIsOpen | `boolean`                                                               | `false`                         | 当有弹框时关闭自动刷新，关闭弹框又恢复                                                                                                                        |
| syncLocation                   | `boolean`                                                               | `true`                          | 是否将过滤条件的参数同步到地址栏                                                                                                                              |
| draggable                      | `boolean`                                                               | `false`                         | 是否可通过拖拽排序。                                                                                                                                          |
| draggableOn                    | `SchemaExpression`                                                      |                                 | 是否可通过拖拽排序。支持表达式，`1.64.0`之后可通过`__query`、`__pristineQuery`获取当前查询数据和初始查询数据及`CRUD`的数据域。                                |
| resizable                      | `boolean`                                                               | `true`                          | 是否可以调整列宽度                                                                                                                                            |
| itemDraggableOn                | `boolean`                                                               |                                 | 用[表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression)来配置是否可拖拽排序                                                                    |
| saveOrderApi                   | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api)                 |                                 | 保存排序的 api。                                                                                                                                              |
| quickSaveApi                   | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api)                 |                                 | 快速编辑后用来批量保存的 API。                                                                                                                                |
| quickSaveItemApi               | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api)                 |                                 | 快速编辑配置成及时保存时使用的 API。                                                                                                                          |
| bulkActions                    | Array<[Action](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/action)> |                                 | 批量操作列表，配置后，表格可进行选中操作。                                                                                                                    |
| pickerMode                     | boolean                                                                 |                                 | 当需要列选择，但是无`bulkActions`配置，设置为`true`表格可进行列选择操作。                                                                                     |
| multiple                       | boolean                                                                 |                                 | 列选择是`checkbox`或者`radio`。                                                                                                                               |
| messages                       | `Object`                                                                |                                 | 覆盖消息提示，如果不指定，将采用 api 返回的 message                                                                                                           |
| messages.fetchFailed           | `string`                                                                |                                 | 获取失败时提示                                                                                                                                                |
| messages.saveOrderFailed       | `string`                                                                |                                 | 保存顺序失败提示                                                                                                                                              |
| messages.saveOrderSuccess      | `string`                                                                |                                 | 保存顺序成功提示                                                                                                                                              |
| messages.quickSaveFailed       | `string`                                                                |                                 | 快速保存失败提示                                                                                                                                              |
| messages.quickSaveSuccess      | `string`                                                                |                                 | 快速保存成功提示                                                                                                                                              |
| primaryField                   | `string`                                                                | `"id"`                          | 设置 ID 字段名。                                                                                                                                              |
| perPage                        | `number`                                                                | 10                              | 设置一页显示多少条数据。                                                                                                                                      |
| orderBy                        | `string`                                                                |                                 | 默认排序字段，这个是传给后端，需要后端接口实现                                                                                                                |
| orderDir                       | `asc` \| `desc`                                                         |                                 | 排序方向                                                                                                                                                      |
| defaultParams                  | `Object`                                                                |                                 | 设置默认 filter 默认参数，会在查询的时候一起发给后端                                                                                                          |
| pageField                      | `string`                                                                | `"page"`                        | 设置分页页码字段名。                                                                                                                                          |
| perPageField                   | `string`                                                                | `"perPage"`                     | 设置分页一页显示的多少条数据的字段名。注意：最好与 defaultParams 一起使用，请看下面例子。                                                                     |
| perPageAvailable               | `Array<number>`                                                         | `[5, 10, 20, 50, 100]`          | 设置一页显示多少条数据下拉框可选条数。                                                                                                                        |
| orderField                     | `string`                                                                |                                 | 设置用来确定位置的字段名，设置后新的顺序将被赋值到该字段中。                                                                                                  |
| hideQuickSaveBtn               | `boolean`                                                               | `false`                         | 隐藏顶部快速保存提示                                                                                                                                          |
| autoJumpToTopOnPagerChange     | `boolean`                                                               | `false`                         | 当切分页的时候，是否自动跳顶部。                                                                                                                              |
| syncResponse2Query             | `boolean`                                                               | `true`                          | 将返回数据同步到过滤器上。                                                                                                                                    |
| keepItemSelectionOnPageChange  | `boolean`                                                               | `false`                         | 保留条目选择，默认分页、搜索后，用户选择条目会被清空，开启此选项后会保留用户选择，可以实现跨页面批量操作。                                                    |
| labelTpl                       | `string`                                                                |                                 | 单条描述模板，`keepItemSelectionOnPageChange`设置为`true`后会把所有已选择条目列出来，此选项可以用来定制条目展示文案。                                         |
| topToolbar                     | SchemaNode                                                              |                                 | CRUD 页面级别（查询条件上方）工具栏配置                                                                                                                       | `1.4.0`                   |
| headerToolbar                  | Array                                                                   | `['bulkActions', 'pagination']` | 顶部工具栏配置                                                                                                                                                |
| footerToolbar                  | Array                                                                   | `['statistics', 'pagination']`  | 底部工具栏配置                                                                                                                                                |
| alwaysShowPagination           | `boolean`                                                               | `true`                          | 是否总是显示分页                                                                                                                                              | `1.24.3`默认值改为 true   |
| affixHeader                    | `boolean`                                                               | `false`                         | 是否固定表头(table 下)                                                                                                                                        |
| autoGenerateFilter             | `Object \| boolean`                                                     | `false`                         | 是否开启查询区域，开启后会根据列元素的 `searchable` 属性值，自动生成查询条件表单                                                                              | `1.11.0` 支持配置对象     |
| resetPageAfterAjaxItemAction   | `boolean`                                                               | `false`                         | 单条数据 ajax 操作后是否重置页码为第一页                                                                                                                      |
| autoFillHeight                 | `boolean` 丨 `{height: number}`                                         |                                 | 内容区域自适应高度                                                                                                                                            |
| canAccessSuperData     | `boolean`        | `false`      | 指定是否可以自动获取上层的数据并映射到表格行数据上，如果列也配置了该属性，则列的优先级更高      |
| spinnerSize                    | `"lg" ｜ "sm" ｜ ""`                                                    | ""                              | 设置 CRUD 依赖的 Spinner 的尺寸，只针对 table 模式                                                                                                            | `1.20.0`                  |
| unsetQueryParams               | `Array<string>`                                                         | []                              | 设置 CRUD 重置、清空过滤条件时，仍需保持不变的参数                                                                                                            | `1.26.0`                  |
| filterFormAdvanceSearchAble    | `boolean`                                                               | `false`                         | 是否支持 filter 模式的高级搜索                                                                                                                                | `1.29.0`                  |
| cascadeSelection               | `boolean`                                                               | `false`                         | 是否支持 amis 母子表格级连选择功能，非 subTable 模式                                                                                                          | `1.35.0`                  |
| updateAllRows                  | `boolean`                                                               | false                           | true，则全量更新行数据；false，则走 TableRow 优化流程，只更新数据变更行                                                                                       | `1.50.0`                  |
| columnSearchFormTooltip        | `TooltipObject`                                                         | `{}`                            | 列过滤文本较长后 tooltip 配置                                                                                                                                 | `1.49.0`                  |
| filterRowNum | `number` | `2` | 搜索表单的`form`默认显示的行数，超过该行数，则显示高级查询按钮 | `1.70.0` |
| filter.updatePristineAfterStoreDataReInit | `boolean` | true | 上层数据域存储数据初始化后，是否设置为filter表单的默认值 |  |
| showSelection | `boolean` | `true` | 是否展示多选状态选中信息 | `1.76.0` |
| placeholder | `Schema` | - | 没有数据的时候展示的内容 | |
| hiddenToggableCol | `boolean` | `false` | 在弹窗中是否隐藏没有toggleable为false的列 | `1.81.2` |

注意除了上面这些属性，CRUD 在不同模式下的属性需要参考各自的文档，比如

- 默认[Table](/dataseeddesigndocui/#/amis/zh-CN/components/table)模式里的[列配置](/dataseeddesigndocui/#/amis/zh-CN/components/table#列配置属表)。
- [Cards](/dataseeddesigndocui/#/amis/zh-CN/components/cards) 模式。
- [List](/dataseeddesigndocui/#/amis/zh-CN/components/list) 模式。

#### 列配置属性表

除了 Table 组件默认支持的列配置，CRUD 的列配置还额外支持以下属性：

| 属性名         | 类型                                                                                                    | 默认值  | 说明                                                                                                                              |
| -------------- | ------------------------------------------------------------------------------------------------------- | ------- | --------------------------------------------------------------------------------------------------------------------------------- |
| sortable       | `boolean`                                                                                               | `false` | 是否可排序                                                                                                                        |
| searchable     | `boolean` \| `Schema`                                                                                   | `false` | 是否可快速搜索，开启`autoGenerateFilter`后，`searchable`支持配置`Schema`                                                          |
| headSearchable | `boolean` \| `Schema`                                                                                   | `false` | 是否可快速搜索，且可在表头回显搜索条件。不开启`autoGenerateFilter`情况下，优先级是 `searchable` > `headSearchable` > `filterable` |
| filterable     | `boolean` \| [`QuickFilterConfig`](/dataseeddesigndocui/#/amis/zh-CN/components/crud#quickfilterconfig) | `false` | 是否可快速搜索，`options`属性为静态选项，支持设置`source`属性从接口获取选项                                                       |
| quickEdit      | `boolean` \| [`QuickEditConfig`](/dataseeddesigndocui/#/amis/zh-CN/components/crud#quickeditconfig)     | -       | 快速编辑，一般需要配合`quickSaveApi`接口使用                                                                                      |

#### QuickFilterConfig

| 属性名        | 类型                                                      | 默认值  | 说明                                                     | 版本 |
| ------------- | --------------------------------------------------------- | ------- | -------------------------------------------------------- | ---- |
| options       | `Array<any>`                                              | -       | 静态选项                                                 |      |
| multiple      | `boolean`                                                 | `false` | 是否支持多选                                             |      |
| source        | [`Api`](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api) | -       | 选项 API 接口                                            |      |
| refreshOnOpen | `boolean`                                                 | `false` | 配置 source 前提下，每次展开筛选浮层是否重新加载选项数据 |      |
| strictMode    | `boolean`                                                 | `false` | 严格模式，开启严格模式后，会采用 JavaScript 严格想等比较 |      |

#### QuickEditConfig

| 属性名             | 类型                      | 默认值      | 说明                                                                                                                                | 版本 |
| ------------------ | ------------------------- | ----------- | ----------------------------------------------------------------------------------------------------------------------------------- | ---- |
| type               | `SchemaType`              | -           | 表单项组件类型                                                                                                                      |      |
| body               | `SchemaCollection`        | -           | 组件容器，支持多个表单项组件                                                                                                        |      |
| mode               | `'inline' \| 'popOver'`   | `'popOver'` | 编辑模式，inline 为行内编辑，popOver 为浮层编辑                                                                                     |      |
| saveImmediately    | `boolean` 或 `{api: Api}` | `false`     | 是否修改后即时保存，一般需要配合`quickSaveItemApi`接口使用，也可以直接配置[`Api`](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api) |      |
| quickEditEnabledOn | `SchemaExpression`        | -           | 开启快速编辑条件[表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression)                                                |      |
| reload             | `string`                  | -           | 操作完后刷新目标对象，请填写目标组件设置的 name 值，如果不需要可以配置 none 或不配置                                                |      |
| resetOnFailed      | `boolean`                 | -           | 接口保存失败后，是否重置组件编辑状态                                                                                                |      |

#### columns-toggler 属性表

| 属性名          | 类型                           | 默认值    | 说明                                                                                 |
| --------------- | ------------------------------ | --------- | ------------------------------------------------------------------------------------ |
| label           | `string`                       |           | 按钮文字                                                                             |
| tooltip         | `string`                       |           | 按钮提示文字                                                                         |
| disabledTip     | `string`                       |           | 按钮禁用状态下的提示                                                                 |
| align           | `"left" \| "right"`            | `"left"`  | 点击内容是否关闭                                                                     |
| size            | `"xs" \| "sm" \| "md" \| "lg"` |           | 按钮大小，参考[按钮](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/action)         |
| footerBtnSize   | `"xs" \| "sm" \| "md" \| "lg"` |           | 弹窗底部按钮大小，参考[按钮](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/action) |
| level           | `string`                       | `default` | 按钮样式，参考[按钮](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/action)         |
| draggable       | `boolean`                      | `false`   | 是否可通过拖拽排序                                                                   |
| defaultIsOpened | `boolean`                      | `false`   | 默认是否展开                                                                         |
| hideExpandIcon  | `boolean`                      | `true`    | 是否隐藏展开的图标                                                                   |
| overlay         | `boolean`                      | `false`   | 是否显示遮罩层                                                                       |
| closeOnOutside  | `boolean`                      |           | 点击外部是否关闭                                                                     |
| closeOnClick    | `boolean`                      |           | 点击内容是否关闭                                                                     |
| iconOnly        | `boolean`                      | `false`   | 是否只显示图标。                                                                     |
| icon            | `string`                       |           | 按钮的图标                                                                           |
| className       | `string`                       |           | 外层 CSS 类名                                                                        |
| btnClassName    | `string`                       |           | 按钮的 CSS 类名                                                                      |

### 事件表

| 事件名称       | 事件参数                                                                                                           | 说明                 | 版本     |
| -------------- | ------------------------------------------------------------------------------------------------------------------ | -------------------- | -------- |
| selectedChange | `selectedItems: item[]` 已选择行<br/>`unSelectedItems: item[]` 未选择行                                            | 手动选择表格项时触发 |          |
| columnSort     | `orderBy: string` 列排序列名<br/>`orderDir: string` 列排序值<br/>`sortList: {orderBy: string; orderDir: string}[]` | 点击列排序时触发     | `1.21.0` |
| fetchInited   | `data: object` 接口返回的数据 | 接口响应后出发 | `1.75.0` |

### 动作表

当前组件对外暴露以下特性动作，其他组件可以通过指定`actionType: 动作名称`、`componentId: 该组件id`来触发这些动作，动作配置可以通过`args: {动作配置项名称: xxx}`来配置具体的参数，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action#触发其他组件的动作)。 |

| 动作名称              | 动作配置                                                                                                                                           | 说明                                          | 版本            |
| --------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------- | --------------- |
| changePage            | `{ page: number; perPage?: number; }`                                                                                                              | 切换分页，需要 props 传递`onPageChange`才生效 | `1.3.0`版本支持 |
| query                 | `{ setPristineQuery?: boolean; queryParams: object; }` <br /> `queryParams` 过滤条件 <br /> `setPristineQuery` 点击重置按钮，是否保留`queryParams` | 查询列表                                      | `1.22.0`        |
| select                | `selected: string` 条件表达式，表达式中可以访问变量`record:行数据`和`rowIndex:行索引`，例如: data.rowIndex === 1                                   | 设置表格的选中项                              | `1.24.0`        |
| clearAll              | -                                                                                                                                                  | 清空表格所有选中项                            | `1.24.0`        |
| scrollIntoView        | `columnName: string`                                                                                                                               | 将 columnName 所在列滚动视图中                | `1.56.0`        |
| setHighLightColumn    | `columnName: string`                                                                                                                               | 将 columnNam 所在的列高亮                     | `1.56.0`        |
| removeHighLightColumn | -                                                                                                                                                  | 将当前高亮的列取消高亮                        | `1.56.0`        |
| clear                 | `args.needSearch: boolean` 清空后是否触发一次查询，不传默认为 true                                                                                 | 清空查询参数动作                              | `1.61.0`        |
| reset                 | `args.needSearch: boolean` 清空后是否触发一次查询，不传默认为 true                                                                                 | 重置查询参数动作                              | `1.61.0`        |
