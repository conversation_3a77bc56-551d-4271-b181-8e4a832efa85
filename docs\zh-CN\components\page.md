---
title: Page 页面
description:
type: 0
group: ⚙ 组件
menuName: Page 页面
icon:
order: 23
standardMode: true
---


Page 组件是 amis 页面 JSON 配置中顶级容器组件，是整个页面配置的入口组件。每一个页面都需要配置 `Page` 组件，作为最顶层组件。

## 组件用法

### 基本用法

内容区可以渲染各种组件。

```schema: scope="body"
{
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "input-text",
        "name": "name",
        "label": "姓名"
      }
    ]
}
```

<!-- ## 在其他区域渲染组件

Page 默认将页面分为几个区域，分别是**内容区（`body`）** 和 **工具栏（`toolbar`）部分**，你可以在这些区域配置你想要的组件和内容。

```schema
{
  "type": "page",
  "toolbar": [
    {
      "type": "tpl",
      "tpl": "这是工具栏部分"
    }
  ],
  "body": [
    {
      "type": "tpl",
      "tpl": "这是内容区"
    }
  ]
}
```

> 不同区域都是`Page`的子节点，也就是说都可以使用`Page`下数据作用域。 -->

### 页面初始化请求

通过配置`initApi`，可以在初始化页面时请求所配置的接口。

```schema
{
    "type": "page",
  "initApi": "/api/mock2/page/initData",
  "body": [
    {
      "type": "tpl",
      "tpl": "当前时间是：${date}"
    }
  ]
}
```

具体 API 规范查看 [API 文档](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api)。

### 轮询初始化接口

想要在页面渲染后，轮询请求初始化接口，步骤如下：

1. 配置 initApi；
2. 配置 interval：单位为毫秒，最小 1000。

```schema
{
    "type": "page",
  "initApi": "/api/mock2/page/initData",
  "interval": 3000,
  "body": [
    {
      "type": "tpl",
      "tpl": "当前时间是：${date}"
    }
  ]
}
```

如果希望在满足某个条件的情况下停止轮询，配置`stopAutoRefreshWhen`表达式。

```schema
{
    "type": "page",
  "initApi": "/api/mock2/page/initData",
  "stopAutoRefreshWhen": "this.time % 5", // 当时间戳能被5整除时，停止轮询
  "interval": 3000,
  "body": [
    {
      "type": "tpl",
      "tpl": "当前时间戳是：${date}"
    }
  ]
}
```

### 属性表

| 属性名              | 类型                                      | 默认值                                     | 说明                                                                                  | 版本 |
| ------------------- | ----------------------------------------- | ------------------------------------------ | ------------------------------------------------------------------------------------- | -----|
| type                | `string`                                  | `"page"`                                   | 指定为 Page 组件                                                                      |
| body                | [SchemaNode](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode) |                                            | 往页面的内容区域加内容                                                                |
| className           | `string`                                  |                                            | 外层 dom 类名                                                                         |
| toolbarClassName    | `string`                                  |   | Toolbar dom 类名                                                                      |
| bodyClassName       | `string`                                  | `wrapper`                                  | Body dom 类名                                                                         |
| initApi             | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api)               |                                            | Page 用来获取初始数据的 api。返回的数据可以整个 page 级别使用。                       |
| initFetch           | `boolean`                                 | `true`                                     | 是否起始拉取 initApi                                                                  |
| initFetchOn         | [表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression)  |                                            | 是否起始拉取 initApi, 通过表达式配置                                                  |
| interval            | `number`                                  | `3000`                                     | 刷新时间(最小 1000)                                                                   |
| silentPolling       | `boolean`                                 | `false`                                    | 配置刷新时是否显示加载动画                                                            |
| stopAutoRefreshWhen | [表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression)  | `""`                                       | 通过表达式来配置停止刷新的条件                                                        |
| spinnerSize | `"lg" ｜ "sm" ｜ ""` | "" |   设置Page依赖的Spinner的尺寸 | `1.20.0` | 
| authorizedConfig | `boolean \| object` |  | 权限校验配置。配置为 `true` 或 `object` 时开启权限校验功能。 | `1.63.0` | 

### 事件表

当前组件会对外派发以下事件，可以通过`onEvent`来监听这些事件，并通过`actions`来配置执行的动作，在`actions`中可以通过`${事件参数名}`来获取事件产生的数据，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action)。

> `[name]`为当前数据域中的字段名，例如：当前数据域为 {username: 'amis'}，则可以通过${username}获取对应的值。

| 事件名称    | 事件参数                                                                                 | 说明                                                |
| ----------- | ---------------------------------------------------------------------------------------- | --------------------------------------------------- |
| init        | -                                                                                        | 组件实例被创建并插入 DOM 中时触发。 |
| inited      | `event.data` initApi 远程请求返回的初始化数据<br/>`[name]: any` 当前数据域中指定字段的值 | 远程初始化接口请求成功时触发                        |
| pullRefresh | -                                                                                        | 开启下拉刷新后，下拉释放后触发（仅用于移动端）      |

### 动作表

当前组件对外暴露以下特性动作，其他组件可以通过指定`actionType: 动作名称`、`componentId: 该组件id`来触发这些动作，动作配置可以通过`args: {动作配置项名称: xxx}`来配置具体的参数，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action#触发其他组件的动作)。

| 动作名称 | 动作配置                   | 说明                                     |
| -------- | -------------------------- | ---------------------------------------- |
| reload   | -                          | 重新加载，调用 `intiApi`，刷新数据域数据 |
| setValue | `value: object` 更新的数据 | 更新数据                                 |
