---
title: Page 页面
description:
type: 0
group: ⚙ 组件
menuName: Page 页面
icon:
order: 23
---


Page 组件是 amis 页面 JSON 配置中顶级容器组件，是整个页面配置的入口组件。

<font color=red>**注意事项：**</font>

1. 每一个页面都需要配置 `Page` 组件，作为最顶层组件。

## 组件用法

### 基本用法

内容区可以渲染各种组件。

```schema: scope="body"
{
    "type": "form",
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "input-text",
        "name": "name",
        "label": "姓名"
      }
    ]
}
```

<!-- ## 在其他区域渲染组件

Page 默认将页面分为几个区域，分别是**内容区（`body`）** 和 **工具栏（`toolbar`）部分**，你可以在这些区域配置你想要的组件和内容。

```schema
{
  "type": "page",
  "toolbar": [
    {
      "type": "tpl",
      "tpl": "这是工具栏部分"
    }
  ],
  "body": [
    {
      "type": "tpl",
      "tpl": "这是内容区"
    }
  ]
}
```

> 不同区域都是`Page`的子节点，也就是说都可以使用`Page`下数据作用域。 -->

### 页面初始化请求

通过配置`initApi`，可以在初始化页面时请求所配置的接口。

```schema
{
    "type": "page",
  "initApi": "/api/mock2/page/initData",
  "body": [
    {
      "type": "tpl",
      "tpl": "当前时间是：${date}"
    }
  ]
}
```

具体 API 规范查看 [API 文档](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api)。

### 轮询初始化接口

想要在页面渲染后，轮询请求初始化接口，步骤如下：

1. 配置 initApi；
2. 配置 interval：单位为毫秒，最小 1000。

```schema
{
    "type": "page",
  "initApi": "/api/mock2/page/initData",
  "interval": 3000,
  "body": [
    {
      "type": "tpl",
      "tpl": "当前时间是：${date}"
    }
  ]
}
```

如果希望在满足某个条件的情况下停止轮询，配置`stopAutoRefreshWhen`表达式。

```schema
{
    "type": "page",
  "initApi": "/api/mock2/page/initData",
  "stopAutoRefreshWhen": "this.time % 5", // 当时间戳能被5整除时，停止轮询
  "interval": 3000,
  "body": [
    {
      "type": "tpl",
      "tpl": "当前时间戳是：${date}"
    }
  ]
}
```

### 页面鉴权

页面鉴权功能，需要按照以下配置才可正常使用（v1.63.0支持）：

1. 使用 `amis-utils` 包的 `createEnv` 来创建 env, 并且需要传入 `authorizedConfig` 参数，使用 env 提供的鉴权能力。
2. `Page` 组件，配置需要配置 `authorizedConfig` 属性，开启鉴权功能。
3. 组件使用全局方法 `CHECKAUTHORITY` 来鉴权。

<font color=red>**注意：**</font> 鉴权功能仅支持在当前页面中，只有一个`Page` 组件时使用。如果页面内存在多个 `Page` 组件，则无法使用该功能。

使用示例：

```js
import { createEnv } from 'amis-utils'
import { render } from 'amis'

// 定义env配置
const env = {
  ...//其他env配置
  ...createEnv({
    ...// 其他 createEnv 参数
    authorizedConfig: {
      appName: 'xxxxui', // 当前应用名称
      version: 'v2', // 可配置 "v1"｜"v2"。 v1：旧版本权限中心， v2：身份一站式权限（默认）
    }
  })
}

// 页面json配置
const pageSchema = {
  type: 'page',
  /**
   * authorizedConfig 配置 true｜object， 时开启鉴权功能。
   * 
   * authorizedConfig：true，使用请求的鉴权数据是当前的路由URL
   * 
   * 如果当前页面获取的权数据不是当前路由页面，可配置 pathname，设置对应的页面路径
   * authorizedConfig: {
   *   pathname: '/xxxui/#/yyy/zzz'
   * }
   */
  authorizedConfig: true,
  body: [{
    type: 'button',
    label: '添加',
    // 'add' 字符串是 权限管理后台，配置的 权限唯一码。 
    visibleOn: '${CHECKAUTHORITY("add")}',
  }, {
    type: 'button',
    label: '删除',
    // 'delete' 字符串是 权限管理后台，配置的 权限唯一码。 
    visibleOn: '${CHECKAUTHORITY("delete")}',
  }]
}

// 路由页面
const RoutePage = () => {
  return render(pageSchema, {}, env)
}
```

### 属性表

| 属性名              | 类型                                      | 默认值                                     | 说明                                                                                  | 版本 |
| ------------------- | ----------------------------------------- | ------------------------------------------ | ------------------------------------------------------------------------------------- | -----|
| type                | `string`                                  | `"page"`                                   | 指定为 Page 组件                                                                      |
| body                | [SchemaNode](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode) |                                            | 往页面的内容区域加内容                                                                |
| className           | `string`                                  |                                            | 外层 dom 类名                                                                         |
| toolbarClassName    | `string`                                  |   | Toolbar dom 类名                                                                      |
| bodyClassName       | `string`                                  | `wrapper`                                  | Body dom 类名                                                                         |
| initApi             | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api)               |                                            | Page 用来获取初始数据的 api。返回的数据可以整个 page 级别使用。                       |
| initFetch           | `boolean`                                 | `true`                                     | 是否起始拉取 initApi                                                                  |
| initFetchOn         | [表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression)  |                                            | 是否起始拉取 initApi, 通过表达式配置                                                  |
| interval            | `number`                                  | `3000`                                     | 刷新时间(最小 1000)                                                                   |
| silentPolling       | `boolean`                                 | `false`                                    | 配置刷新时是否显示加载动画                                                            |
| stopAutoRefreshWhen | [表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression)  | `""`                                       | 通过表达式来配置停止刷新的条件                                                        |
| spinnerSize | `"lg" ｜ "sm" ｜ ""` | "" |   设置Page依赖的Spinner的尺寸 | `1.20.0` | 
| authorizedConfig | `boolean \| object` |  | 权限校验配置。配置为 `true` 或 `object` 时开启权限校验功能。 | `1.63.0` | 

### 事件表

当前组件会对外派发以下事件，可以通过`onEvent`来监听这些事件，并通过`actions`来配置执行的动作，在`actions`中可以通过`${事件参数名}`来获取事件产生的数据，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action)。

> `[name]`为当前数据域中的字段名，例如：当前数据域为 {username: 'amis'}，则可以通过${username}获取对应的值。

| 事件名称    | 事件参数                                                                                 | 说明                                                |
| ----------- | ---------------------------------------------------------------------------------------- | --------------------------------------------------- |
| init        | -                                                                                        | 组件实例被创建并插入 DOM 中时触发。 |
| inited      | `event.data` initApi 远程请求返回的初始化数据<br/>`[name]: any` 当前数据域中指定字段的值 | 远程初始化接口请求成功时触发                        |
| pullRefresh | -                                                                                        | 开启下拉刷新后，下拉释放后触发（仅用于移动端）      |
| authInited | `event.data.__permitComponents` 有权限的组件code组成的数组  | 权限组件初始化完成时触发 `1.77.0` 版本支持 |

### 动作表

当前组件对外暴露以下特性动作，其他组件可以通过指定`actionType: 动作名称`、`componentId: 该组件id`来触发这些动作，动作配置可以通过`args: {动作配置项名称: xxx}`来配置具体的参数，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action#触发其他组件的动作)。

| 动作名称 | 动作配置                   | 说明                                     |
| -------- | -------------------------- | ---------------------------------------- |
| reload   | -                          | 重新加载，调用 `intiApi`，刷新数据域数据 |
| setValue | `value: object` 更新的数据 | 更新数据                                 |
