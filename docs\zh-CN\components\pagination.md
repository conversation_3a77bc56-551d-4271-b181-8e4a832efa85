---
title: Pagination 分页组件
description:
type: 0
group: ⚙ 组件
menuName: Pagination
icon:
order: 73
standardMode: true
---

分页组件

```schema: scope="body"
{
    "type": "service",
    "api": "/api/mock2/crud/table",
    "body": [
        {
            "type": "pagination",
            "layout": "total,pager,perPage,go",
            "mode": "normal",
            "activePage": 2,
            "lastPage": 99999,
            "total": 999,
            "perPage": 10,
            "maxButtons": 7,
            "showPerPage": true,
            "perPageAvailable": [10, 20, 50, 100],
            "showPageInput": true,
            "disabled": false
        }
    ]
}
```

### 简易模式


```schema
{
  "type": "page",
  "body": {
    "type": "service",
    "api": "/api/mock2/crud/table",
    "body": [
      {
        "type": "pagination",
        "mode": "simple",
        "activePage": 2,
        "hasNext": true
      }
    ]
  }
}
```

## 属性表

| 属性名           | 类型                       | 默认值                                   | 说明                                                      | 版本 |
| ---------------- | -------------------------- | ---------------------------------------- | --------------------------------------------------------- | ---- |
| type             | `string`                   | `"pagination"`                           | 指定为 Pagination 渲染器                                  |      |
| mode             | `normal \| simple`         | `normal`                                 | 迷你版本/简易版本 只显示左右箭头，配合 hasNext 使用       |      |
| layout           | `string \| string[]`       | `["pager"]`                              | 通过控制 layout 属性的顺序，调整分页结构布局              |      |
| maxButtons       | `number`                   | `5`                                      | 最多显示多少个分页按钮，最小为 5                          |      |
| lastPage         | `number`                   |                                          | 总页数 （设置总条数 total 的时候，lastPage 会重新计算）   |      |
| total            | `number`                   |                                          | 总条数                                                    |      |
| activePage       | `number`                   | `1`                                      | 当前页数                                                  |      |
| perPage          | `number`                   | `10`                                     | 每页显示多条数据                                          |      |
| showPerPage      | `boolean`                  | false                                    | 是否展示 perPage 切换器 layout 和 showPerPage 都可以控制  |      |
| perPageAvailable | `number[]`                 | `[10, 20, 50, 100]`                      | 指定每页可以显示多少条                                    |      |
| showPageInput    | `boolean`                  | false                                    | 是否显示快速跳转输入框 layout 和 showPageInput 都可以控制 |      |
| disabled         | `boolean`                  | false                                    | 是否禁用                                                  |      |
| onPageChange     | page、perPage 改变时会触发 | (page: number, perPage: number) => void; | 分页改变触发                                              |      |
| popOverContainerSelector | `string`           |       | 分页条数弹层挂载位置选择器，会通过`querySelector`获取                                           |

## 事件表

| 事件名称  | 事件参数 | 说明 | 版本 |
| --------- | -------- | ---- | ---- |
| change    | `page: number` 当前页码的值<br/> `perPage: number` 每页显示的记录数     | 分页改变触发    | 1.83.0 |     



```schema
{
  "type": "page",
  "body": {
    "type": "service",
    "id": "service_02",
    "api": "/api/mock2/crud/table?page=${page}",
    "data": {
      "page": 1
    },
    "body": [
      {
        "type": "table",
        "title": "表格1",
        "source": "$rows",
        "columns": [
          {
            "name": "engine",
            "label": "Engine"
          },
          {
            "name": "version",
            "label": "Version"
          }
        ]
      },
      {
        "type": "pagination",
        "activePage": "${page}",
        "mode": "simple",
        "total": "${count}",
        "hasNext": true,
        "onEvent": {
          "change": {
            "actions": [
              {
                "actionType": "reload",
                "componentId": "service_02",
                "data": {
                  "page": "${event.data.page}"
                }
              }
            ]
          }
        }
      }
    ]
  }
}
```

## 动作表

当前组件对外暴露以下特性动作，其他组件可以通过指定`actionType: 动作名称`、`componentId: 该组件id`来触发这些动作，动作配置可以通过`args: {动作配置项名称: xxx}`来配置具体的参数，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action#触发其他组件的动作)。 |

| 动作名称   | 动作配置                              | 说明                                          | 版本            |
| ---------- | ------------------------------------- | --------------------------------------------- | --------------- |
| changePage | `{ page: number; perPage?: number; }` | 切换分页，需要 props 传递`onPageChange`才生效 | `1.3.0`版本支持 |
