---
title: Pdf
description: 
type: 0
group: ⚙ 组件
menuName: Pdf
icon:
order: 52
standardMode: true
---

预览pdf。缩略图模式仅展示第一张pdf。`1.46.0`版本支持。

## 基本使用

```schema
{
    "type": "page",
    "body": {
        "type": "pdf",
        "src": "https://static02.sit.yxmarketing01.com/tdmaterial/ea7d4945a20641bd97e8909ad09d4b5e.pdf"
    }
}
```


也可以配置`name`属性关联上下文数据

```schema
{
    "type": "page",
    "data": {
        "pdfUrl": "https://static02.sit.yxmarketing01.com/tdmaterial/ea7d4945a20641bd97e8909ad09d4b5e.pdf"
    },
    "body": {
        "type": "pdf",
        "name": "pdfUrl"
    }
}
```

## 配置标题和说明

```schema
{
    "type": "page",
    "body": {
        "type": "pdf",
        "src": "https://static02.sit.yxmarketing01.com/tdmaterial/ea7d4945a20641bd97e8909ad09d4b5e.pdf",
        "title": "这是标题",
        "pdfCaption": "这是一段说明"
    }
}
```

## 放大展示全文

### 缩略图触发
配置`"enlargeAble": true`，鼠标移动到pdf上会显示可点击图标，点击可展示全文。

```schema
{
    "type": "page",
    "body": {
        "type": "pdf",
        "src": "https://static02.sit.yxmarketing01.com/tdmaterial/1f05822a19e643e197746a043e2965fa.pdf",
        "enlargeAble": true
    }
}
```


`enlargeTitle`和`enlargeCaption`可以配置放大预览中的标题和描述

```schema
{
    "type": "page",
    "body": {
        "type": "pdf",
        "src": "https://static02.sit.yxmarketing01.com/tdmaterial/1f05822a19e643e197746a043e2965fa.pdf",
        "enlargeAble": true,
        "enlargeTitle": "这是一个标题",
        "enlargeCaption": "这是一段描述"
    }
}
```

### 其他容器触发
配置`"enlargeTrigger"`值为`ButtonSchema`格式，可自定义触发放大容器。`1.48.0`版本支持。

```schema
{
    "type": "page",
    "body": {
        "type": "pdf",
        "src": "https://static02.sit.yxmarketing01.com/tdmaterial/1f05822a19e643e197746a043e2965fa.pdf",
        "enlargeTrigger": {
            "label": "点击放大",
            "level": "primary"
        }
    }
}
```

`enlargeTrigger`的`body`属性支持`SchamaObject`格式，可自定义非按钮的触发容器。

```schema
{
    "type": "page",
    "body": {
        "type": "pdf",
        "src": "https://static02.sit.yxmarketing01.com/tdmaterial/1f05822a19e643e197746a043e2965fa.pdf",
        "enlargeTrigger": {
            "body": {
              "type": "tpl",
              "tpl": "点击放大"
            }
        }
    }
}
```

## 下载文件

配置`"downloadUrl": true`，鼠标移动到pdf上会显示下载图标，点击下载文件。

```schema
{
    "type": "page",
    "body": {
        "type": "pdf",
        "src": "https://static02.sit.yxmarketing01.com/tdmaterial/1f05822a19e643e197746a043e2965fa.pdf",
        "downloadUrl": true,
        "enlargeAble": true
    }
}
```

## 设置高宽

通过 `width` 和 `height` 可以设置缩率图显示的高宽

```schema
{
    "type": "page",
    "body": {
        "type": "pdf",
        "width": "200px",
        "height": "270px",
        "src": "https://lattebank-shuhematerail-dev.oss-cn-beijing.aliyuncs.com/shuhe/video/dfba09d5812f42d3a3d22893288eb810.pdf"
    }
}
```

## 全文模式

默认pdf为缩略图模式，可以通过配置 pdfMode: "original" 修改为原图模式，原图模式为块状展示，宽度尽可能占满。

```schema
{
    "type": "page",
    "data": {
        "pdfUrl": "https://static02.sit.yxmarketing01.com/tdmaterial/1f05822a19e643e197746a043e2965fa.pdf"
    },
    "body": {
        "type": "pdf",
        "pdfMode": "original",
        "name": "pdfUrl"
    }
}
```

## 打开外部链接

可以设置 href 属性来支持pdf点击打开链接，需要注意这和放大功能是冲突的，只能二选一。

```schema: scope="body"
{
    "type": "pdf",
    "src": "https://lattebank-shuhematerail-dev.oss-cn-beijing.aliyuncs.com/shuhe/video/dfba09d5812f42d3a3d22893288eb810.pdf",
    "href": "https://github.com/baidu/amis"
}
```

href 也可以是模板

```schema
{
    "type": "page",
    "data": {
        "pdfUrl": "https://lattebank-shuhematerail-dev.oss-cn-beijing.aliyuncs.com/shuhe/video/dfba09d5812f42d3a3d22893288eb810.pdf",
        "pdfHref": "https://github.com/baidu/amis"
    },
    "body": {
        "type": "pdf",
        "name": "pdfUrl",
        "href": "${pdfHref}"
    }
}
```

## 用作 Field 时

当用在 Table 的列配置 Column、List 的内容、Card 卡片的内容和表单的 Static-XXX 中时，可以设置`name`属性，映射同名变量

### Table 中的列类型

```schema: scope="body"
{
    "type": "table",
    "data": {
        "items": [
            {
                "id": "1",
                "pdf": "https://lattebank-shuhematerail-dev.oss-cn-beijing.aliyuncs.com/shuhe/video/dfba09d5812f42d3a3d22893288eb810.pdf"
            },
            {
                "id": "2",
                "pdf": "https://static02.sit.yxmarketing01.com/tdmaterial/1f05822a19e643e197746a043e2965fa.pdf"
            },
            {
                "id": "3",
                "pdf": "https://static02.sit.yxmarketing01.com/tdmaterial/ea7d4945a20641bd97e8909ad09d4b5e.pdf"
            }
        ]
    },
    "columns": [
        {
            "name": "id",
            "label": "Id"
        },

        {
            "name": "pdf",
            "label": "pdf",
            "type": "pdf"
        }
    ]
}
```

List 的内容、Card 卡片的内容配置同上

### Form 中静态展示

```schema: scope="body"
{
    "type": "form",
    "data": {
        "pdf": "https://lattebank-shuhematerail-dev.oss-cn-beijing.aliyuncs.com/shuhe/video/dfba09d5812f42d3a3d22893288eb810.pdf"
    },
    "body": [
        {
            "type": "static-pdf",
            "name": "pdf",
            "label": "颜色",
            "innerClassName": "no-border"
        }
    ]
}
```

## 自定义点击行为

可以通过 `clickAction` 设置点击触发行为。

```schema: scope="body"
{
    "type": "pdf",
    "src": "https://lattebank-shuhematerail-dev.oss-cn-beijing.aliyuncs.com/shuhe/video/dfba09d5812f42d3a3d22893288eb810.pdf",
    "class": "cursor-pointer",
    "clickAction": {
        "actionType": "dialog",
        "dialog": {
            "title": "弹框标题",
            "body": "这是一个弹框"
        }
    }
}
```

## 添加水印

通过`watemarkConfig`添加水印配置

```schema
{
  "type": "page",
  "data": {
    "pdfUrl": "https://static02.sit.yxmarketing01.com/tdmaterial/1f05822a19e643e197746a043e2965fa.pdf"
  },
  "body": {
    "type": "pdf",
    "pdfMode": "original",
    "name": "pdfUrl",
    "watermarkConfig": {
        "content": "amis"
    }
  }
}
```


## 属性表

| 属性名         | 类型                                 | 默认值    | 说明                                                                                   | 版本    |
| -------------- | ------------------------------------ | --------- | -------------------------------------------------------------------------------------- | ------- |
| type           | `string`                             |           | 如果在 Table、Card 和 List 中，为`"pdf"`；在 Form 中用作静态展示，为`"static-pdf"` |
| className      | `string`                             |           | 外层 CSS 类名                                                                          |
| innerClassName | `string`                             |           | 组件内层 CSS 类名                                                                      |
| pdfClassName   | `string`                             |           | pdf容器 CSS 类名                                                                          |
| thumbClassName | `string`                             |           | pdf缩率图 CSS 类名                                                                    |
| height         | `string`                             |           | pdf缩率图高度                                                                           |
| width          | `string`                             |           | pdf缩率图宽度                                                                           |
| title          | `string`                             |           | 标题                                                                                   |
| pdfCaption     | `string`                             |           | 描述                                                                                   |
| placeholder    | `string`                             |           | 占位文本                                                                               |
| defaultImage   | `string`                             |           | 无数据时显示的图片                                                                     |
| src            | `string \| { url: string, httpHeaders?: Record<string, string>, withCredentials?: boolean, data: string \| Record<string, any>}`           |           | pdf地址配置，同[react-pdf](https://github.com/wojtekmaj/react-pdf/)中`file`的配置，`1.54.0`支持配置data |
| href           | [模板](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/template) |           | 外部链接地址                                                                           |
| enlargeAble    | `boolean`                            |           | 支持放大预览                                                                           |
| enlargeTrigger  | `ButtonSchema`               |        | 放大预览触发器。版本：`1.48.0`                                                                           |
| downloadUrl      | `boolean` \| `string` \| `SchemaApi`            | `false`                        | 设置`true`时默认取`src`的路径。支持`post:http://xxx.com/${value}`这种写法。 |
| enlargeTitle   | `string`                             |           | 放大预览的标题                                                                         |
| enlargeCaption | `string`                             |           | 放大预览的描述                                                                         |
| pdfMode      | `string`                             | `thumb`   | 图片展示模式，可选：`'thumb'`, `'original'` 即：缩略图模式 或者 原图模式               |
|watemarkConfig| `WatermarkSchema` | - | 水印配置，参考[水印配置](/dataseeddesigndocui/#/amis/zh-CN/components/watermark) 版本：`1.61.0`|
