---
title: ResizeContainer 容器
description:
type: 0
group: ⚙ 组件
menuName: ResizeContainer
icon:
order: 83
standardMode: true
---

拖拽改变容器的大小。

> 1.28.0 及以上版本支持该组件

## 基本用法

```schema
{
  "type": "page",
  "body": {
    "type": "flex",
    "className": "border border-gray-100 border-solid h-md",
    "items": [
      {
        "type": "resize-container",
        "body": "这是一段提醒",
        "autoFillHeight": true
      },
      {
        "type": "tpl",
        "className": "flex-1",
        "tpl": "这是内容区"
      }
    ]
  }
}
```

## 可折叠

> 1.86.1 及以上版本支持 `collapsable` 配置

通过设置`collapsable`属性为`true`实现可折叠。**给按钮配置`closeResizable`和`openResizable`分别控制左侧区域折叠和展开。**

```schema
{
  "type": "page",
  "body": {
    "type": "flex",
    "items": [
      {
        "type": "resize-container",
        "defaultSize": {
          "width": "300px"
        },
        "collapsable": true,
        "minWidth": 100,
        "maxWidth": 600,
        "body": [
          {
            "type": "container",
            "className": "overflow-hidden bg-white",
            "body": [
              {
                "type": "title",
                "title": "页面大标题名称大标题名称Demo",
                "className": "border-solid border-0 border-b border-gray-200",
                "actions": [
                  {
                    "type": "button",
                    "body": {
                      "type": "icon",
                      "icon": "angle-double-left"
                    },
                    "closeResizable": true
                  }
                ]
              },
              {
                "type": "form",
                "wrapWithPanel": false,
                "body": [
                  {
                    "type": "input-tree",
                    "name": "tree",
                    "className": "m",
                    "searchable": false,
                    "label": false,
                    "options": [
                      {
                        "label": "Folder A",
                        "value": 1,
                        "children": [
                          {
                            "label": "file A",
                            "value": 2
                          },
                          {
                            "label": "Folder B",
                            "value": 3,
                            "children": [
                              {
                                "label": "file b1",
                                "value": 3.1
                              },
                              {
                                "label": "file b2",
                                "value": 3.2
                              }
                            ]
                          }
                        ]
                      },
                      {
                        "label": "file C",
                        "value": 4
                      },
                      {
                        "label": "file D",
                        "value": 5
                      }
                    ]
                  }
                ],
                "actions": []
              }
            ]
          },
          {
            "type": "button",
            "body": {
              "type": "icon",
              "icon": "angle-double-right"
            },
            "style": {
              "position": "absolute",
              "top": "0px",
              "right": "-15px"
            },
            "openResizable": true
          }
        ],
        "autoFillHeight": true,
      },
      {
        "type": "tpl",
        "className": "flex-1 ml-4",
        "tpl": "这是内容区"
      }
    ]
  }
}
```

## 属性表

| 属性名    | 类型     | 默认值                  | 说明          | 版本 |
| --------- | -------- | ----------------------- | ------------- | ----- |
| type      | `string` |                         | `resize-container`      |
| className | `string` |                         | 外层 CSS 类名 |
| bodyClassName | `string` |                         | 内容外层 CSS 类名 |
| defaultSize | `{width: number; height: number}` |                     | 可拖拽的默认宽高      |
| minWidth | `string` \|  `number` |                `32px`        | 可拖拽的最小宽度      |
| maxWidth   | `string` \|  `number` | `500px`    | 可拖拽的最大宽度     |
| autoFillHeight   | `boolean` |     | 设置为 true 可自适应高度    |
| autoFillWidth   | `boolean` |     | 设置为 true 可自适应宽度    |
