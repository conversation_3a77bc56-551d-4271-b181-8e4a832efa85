---
title: ResizeContainer 容器
description:
type: 0
group: ⚙ 组件
menuName: ResizeContainer
icon:
order: 83
---

拖拽改变容器的大小。

> 1.28.0 及以上版本支持该组件

## 基本用法

```schema
{
  "type": "page",
  "body": {
    "type": "flex",
    "className": "border border-gray-100 border-solid h-md",
    "items": [
      {
        "type": "resize-container",
        "body": "这是一段提醒",
        "autoFillHeight": true
      },
      {
        "type": "tpl",
        "className": "flex-1",
        "tpl": "这是内容区"
      }
    ]
  }
}
```

## 属性表

| 属性名    | 类型     | 默认值                  | 说明          | 版本 |
| --------- | -------- | ----------------------- | ------------- | ----- |
| type      | `string` |                         | `resize-container`      |
| className | `string` |                         | 外层 CSS 类名 |
| bodyClassName | `string` |                         | 内容外层 CSS 类名 |
| defaultSize | `{width: number; height: number}` |                     | 可拖拽的默认宽高      |
| minWidth | `string` \|  `number` |                `32px`        | 可拖拽的最小宽度      |
| maxWidth   | `string` \|  `number` | `500px`    | 可拖拽的最大宽度     |
| autoFillHeight   | `boolean` |     | 设置为 true 可自适应高度    |
| autoFillWidth   | `boolean` |     | 设置为 true 可自适应宽度    |
