---
title: ShowMore 组件
description:
type: 0
group: ⚙ 组件
menuName: ShowMore 组件
icon:
standardMode: true
---

用于单行多个操作项显示优化

## 基本用法

```schema
{
    "type": "page",
    "body": {
        "name": "show-more",
        "type": "show-more",
        "label": "操作",
        "buttons": [
            {
                "label": "按钮1",
                "type": "button",
                "level": "link",
                "actionType": "dialog",
                "dialog": {
                  "title": "弹框1",
                  "body": "这是个简单的弹框。"
                }
            },
            {
                "label": "按钮2",
                "type": "button",
                "level": "link",
                "actionType": "dialog",
                "dialog": {
                  "title": "弹框2",
                  "body": "这是个简单的弹框。"
                }
            },
            {
                "label": "按钮3",
                "type": "button",
                "level": "link",
                "actionType": "dialog",
                "dialog": {
                  "title": "弹框3",
                  "body": "这是个简单的弹框。"
                }
            }
        ]
    }
}
```

## 设置多项折叠

```schema
{
    "type": "page",
    "data": {
        "collapseOnExceed": 2
    },
    "body": [
      {
          "name": "show-more",
          "type": "show-more",
          "label": "操作",
          "collapseOnExceed": "${collapseOnExceed}",
          "buttons": [
              {
                  "label": "按钮1",
                  "type": "button",
                  "level": "link",
                  "actionType": "dialog",
                  "dialog": {
                    "title": "弹框1",
                    "body": "这是个简单的弹框。"
                  }
              },
              {
                  "label": "按钮2",
                  "type": "button",
                  "level": "link",
                  "actionType": "dialog",
                  "dialog": {
                    "title": "弹框2",
                    "body": "这是个简单的弹框。"
                  }
              },
              {
                  "label": "按钮3",
                  "type": "button",
                  "level": "link",
                  "actionType": "dialog",
                  "dialog": {
                    "title": "弹框3",
                    "body": "这是个简单的弹框。"
                  }
              },
              {
                  "label": "按钮4",
                  "type": "button",
                  "level": "link",
                  "actionType": "dialog",
                  "dialog": {
                    "title": "弹框4",
                    "body": "这是个简单的弹框。"
                  }
              },
              {
                  "label": "按钮5",
                  "type": "button",
                  "level": "link",
                  "actionType": "dialog",
                  "dialog": {
                    "title": "弹框5",
                    "body": "这是个简单的弹框。"
                  }
              }
          ]
      }
    ]
}
```

## 属性表

| 属性名           | 类型                           | 默认值                                                                                                                                                    | 说明                                                           | 版本    |
| ---------------- | ------------------------------ | --------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------- | ------- |
| type             | `'operation' \| 'show-more'`   |                                                                                                                                                           |                                                                |
| buttons          | `ActionSchema[]`               |                                                                                                                                                           | 多个操作项，参考 Action 组件                                   |
| collapseOnExceed | `numer \| string \| SchemaTpl` | `3`                                                                                                                                                       | 支持数字、纯数字字符串、模版变量                               |
| dropdownButton   | `DropdownButtonSchema`         | {<br/>&nbsp;&nbsp;type: "dropdown-button",<br/>&nbsp;&nbsp;level: "link",<br/>&nbsp;&nbsp;icon: "fa fa-ellipsis-h",<br/>&nbsp;&nbsp;hideCaret: true<br/>} | 参考 dropdownButton 配置<br/>DropdownButtonSchema.buttons 失效 |
| visibleDivider   | boolean                        | `show-more`类型默认值 true， `operation`类型默认值 false                                                                                                  | 两两之间显示`｜`分割线                                         | `1.7.0` |
