---
title: Space 间距
description:
type: 0
group: ⚙ 组件
menuName: Space 间距
icon:
order: 38
standardMode: true
---

`Space` 间距组件，用于控制多个组件之间的间距，可控制 横向或竖向 间距。组件之间需要添加间距的时候使用。

## 场景推荐

### 横向间距

横向排列时会给包裹的组件添加横向 `8px` 间距。包裹组件在横向方向上竖向居中对齐。

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "/api/mock2/saveForm?waitSeconds=2",
    "data": {
      "platform": "text1",
      "css": "text2",
      "bankList": [
        "4017"
      ]
    },
    "labelWidth": 70,
    "body": {
      "type": "space",
      "items": [
        {
          "type": "tag",
          "label": "GET",
          "color": "success"
        },
        {
          "type": "static",
          "label": false,
          "value": "/taskmgr/cmsmgr/clientview/layouts"
        }
      ]
    }
  }
}
```

### 竖向间距

竖向排列时会给包裹的组件添加竖向 `16px` 间距。

```schema
{
  "type": "page",
  "data": {
    "table": [
      {}
    ]
  },
  "body": {
    "type": "group-container",
    "items": [
      {
        "header": {
          "title": "测试结果"
        },
        "body": {
          "type": "space",
          "direction": "vertical",
          "items": [
            {
              "type": "flex",
              "justify": "start",
              "items": [
                {
                  "type": "tpl",
                  "tpl": "调用成功！",
                  "className": "text-success"
                },
                {
                  "type": "tpl",
                  "tpl": "结果如下："
                }
              ]
            },
            {
              "type": "table",
              "source": "$table",
              "columns": [
                {
                  "name": "a",
                  "label": "A"
                },
                {
                  "name": "b",
                  "label": "B"
                },
                {
                  "name": "c",
                  "label": "C"
                }
              ]
            },
            {
              "type": "tpl",
              "tpl": "接口响应："
            },
            {
              "type": "panel",
              "body": {
                "type": "code",
                "language": "json",
                "formatter": true,
                "value": "{\"a\":1, \"b\":2, \"c\": {\"c1\": 3}}"
              }
            }
          ]
        }
      }
    ]
  }
}
```

## 组件用法

### 排列方向

默认是横向排列。

```schema
{
    "type": "page",
    "body": {
        "type": "space",
        "items": [{
            "type": "wrapper",
            "bgColor": "white",
            "body": "内容1"
        }, {
            "type": "wrapper",
            "bgColor": "white",
            "body": "内容2"
        }]
    }
}
```

配置 `direction:"vertical"` 可转换为竖向排列。

```schema
{
    "type": "page",
    "body": {
        "type": "space",
        "direction": "vertical",
        "items": [{
            "type": "wrapper",
            "bgColor": "white",
            "body": "内容1"
        }, {
            "type": "wrapper",
            "bgColor": "white",
            "body": "内容2"
        }]
    }
}
```

## 属性表

| 属性名           | 类型                                      | 默认值        | 说明                    |
| ---------------- | ----------------------------------------- | ------------- | ----------------------- |
| type             | `string`                                  | `"space"` | 指定为 container 渲染器 |
| className        | `string`                                  |               | 外层 Dom 的类名     |
| direction        | `"horizontal"\|"vertical"`                |  `"horizontal"`        | 排列方向  "horizontal"横向， "vertical" 竖向       |
| items             | [SchemaNode](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode) | 放置组件    |    |
