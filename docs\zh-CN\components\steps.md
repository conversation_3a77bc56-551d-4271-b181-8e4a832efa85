---
title: Steps 步骤条
description:
type: 0
group: ⚙ 组件
menuName: Steps
icon:
order: 68
---

步骤条组件
## 推荐场景
### 步骤条带标题
```schema
{
  "type": "page",
  "data": {
    "name": "张三",
    "age": 20,
    "address": "金科路",
    "email": "<EMAIL>",
    "class": "三年二班",
    "company": "abc",
    "telephone": 18711223344,
    "eventSource": "FEATURE",
    "type": "caocao"
  },
  "body": [
    {
      "type": "title",
      "iconConfig": true,
      "title": "页面大标题名称大标题名称",
      "subTitle": "我是小标题"
    },
    {
      "type": "form",
      "static": true,
      "labelWidth": 130,
      "actions": [],
      "body": {
        "type": "wrapper",
        "bgColor": "white",
        "body": [
          {
            "type": "steps",
            "status": {
              "second": "error"
            },
            "steps": [
              {
                "title": "第一步",
                "value": "first"
              },
              {
                "title": "第二步",
                "value": "second"
              },
              {
                "title": "第三步",
                "value": "third"
              }
            ]
          },
          {
            "type": "group-container",
            "collapsible": false,
            "items": [
              {
                "type": "panel",
                "header": {
                  "title": "第一步，基础信息"
                },
                "body": [
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "input-text",
                        "name": "name",
                        "label": "姓名"
                      },
                      {
                        "type": "input-text",
                        "name": "age",
                        "label": "年龄"
                      },
                      {
                        "type": "input-text",
                        "name": "class",
                        "label": "班级"
                      }
                    ]
                  },
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "input-text",
                        "name": "email",
                        "label": "邮箱"
                      },
                      {
                        "type": "input-text",
                        "name": "telephone",
                        "label": "电话"
                      },
                      {
                        "type": "input-text",
                        "name": "address",
                        "label": "地址"
                      }
                    ]
                  },
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "select",
                        "name": "type",
                        "label": "类型",
                        "options": [
                          {
                            "label": "曹操",
                            "value": "caocao"
                          },
                          {
                            "label": "刘备",
                            "value": "liubei"
                          }
                        ]
                      },
                      {
                        "type": "select",
                        "name": "eventSource",
                        "label": "事件来源",
                        "options": [
                          {
                            "label": "业务系统",
                            "value": "SYSTEM"
                          },
                          {
                            "label": "特征系统",
                            "value": "FEATURE"
                          },
                          {
                            "label": "北斗系统",
                            "value": "EFUEL"
                          },
                          {
                            "label": "埋点系统",
                            "value": "STATS"
                          }
                        ]
                      },
                      {
                        "type": "input-text",
                        "name": "company",
                        "label": "工作地址"
                      }
                    ]
                  }
                ]
              },
              {
                "type": "panel",
                "header": {
                  "title": "第二步，复杂信息"
                },
                "body": [
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "input-text",
                        "name": "name",
                        "label": "姓名"
                      },
                      {
                        "type": "input-text",
                        "name": "age",
                        "label": "年龄"
                      },
                      {
                        "type": "input-text",
                        "name": "class",
                        "label": "班级"
                      }
                    ]
                  },
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "input-text",
                        "name": "email",
                        "label": "邮箱"
                      },
                      {
                        "type": "input-text",
                        "name": "telephone",
                        "label": "电话"
                      },
                      {
                        "type": "input-text",
                        "name": "address",
                        "label": "地址"
                      }
                    ]
                  },
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "select",
                        "name": "type",
                        "label": "类型",
                        "options": [
                          {
                            "label": "曹操",
                            "value": "caocao"
                          },
                          {
                            "label": "刘备",
                            "value": "liubei"
                          }
                        ]
                      },
                      {
                        "type": "select",
                        "name": "eventSource",
                        "label": "事件来源",
                        "options": [
                          {
                            "label": "业务系统",
                            "value": "SYSTEM"
                          },
                          {
                            "label": "特征系统",
                            "value": "FEATURE"
                          },
                          {
                            "label": "北斗系统",
                            "value": "EFUEL"
                          },
                          {
                            "label": "埋点系统",
                            "value": "STATS"
                          }
                        ]
                      },
                      {
                        "type": "input-text",
                        "name": "company",
                        "label": "工作地址"
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    }
  ]
}
```

### 垂直展示+crud

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "id": "page",
    "data": {
      "status": {
        "1": "finish",
        "2": "finish",
        "3": "finish",
        "4": "finish"
      },
      "currentStep": 3,
      "activeStep": "4"
    },
    "body": {
      "type": "flex",
      "justify": "start",
      "gap": true,
      "alignItems": "start",
      "items": [
        {
          "type": "steps",
          "mode": "vertical",
          "name": "${currentStep}",
          "status": "${status}",
          "clickable": true,
          "steps": [
            {
              "title": "1",
              "description": "this is description",
              "value": "1"
            },
            {
              "title": "2",
              "value": "2"
            },
            {
              "title": "3",
              "value": "3"
            },
            {
              "title": "4",
              "value": "4"
            },
            {
              "title": "5",
              "value": "5"
            }
          ],
          "onEvent": {
            "click": {
              "actions": [
                {
                  "actionType": "setValue",
                  "componentId": "page",
                  "expression": "${step.value !== '5'}",
                  "args": {
                    "value": {
                      "currentStep": 3,
                      "activeStep": "${step.value}",
                      "status": {
                        "1": "${step.value === '1' ? 'wait' : 'finish'}",
                        "2": "${step.value === '2' ? 'wait' : 'finish'}",
                        "3": "${step.value === '3' ? 'wait' : 'finish'}",
                        "4": "${step.value === '4' ? 'wait' : 'finish'}"
                      }
                    }
                  }
                }
              ]
            }
          }
        },
        {
          "type": "crud",
          "className": "flex-grow",
          "title": "这里是第${activeStep}步的标题",
          "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?waitSeconds=1",
          "footerToolbar": [
            {
              "type": "pagination",
              "maxButtons": 5,
              "layout": "total,pager,perPage"
            }
          ],
          "columns": [
            {
              "name": "id",
              "label": "ID"
            },
            {
              "name": "engine",
              "label": "Rendering engine"
            },
            {
              "name": "browser",
              "label": "Browser"
            },
            {
              "name": "platform",
              "label": "Platform(s)"
            },
            {
              "name": "engine",
              "label": "Engine"
            },
            {
              "name": "version",
              "label": "Engine Version"
            },
            {
              "name": "grade",
              "label": "CSS grade"
            }
          ]
        }
      ]
    }
  }
}
```
- 落地案例
 [获客平台（主营）- RTA服务-建模管理-建模项目管理-建模项目执行计划详情](http://moka.dmz.sit.caijj.net/tdrtaui/#/modelingManage/projectManage)   
 ![获客平台（主营）- RTA服务-建模管理-建模项目管理-建模项目执行计划详情](https://static02.sit.yxmarketing01.com/materialcenter/03e29ab4-5800-4bdb-88bb-28a20bf982b1.jpg)
## 组件用法
### 基本用法

```schema
{
  "type": "page",
  "body": [
    {
      "type": "steps",
      "value": 1,
      "steps": [
        {
          "title": "First",
          "subTitle": "this is subTitle",
          "description": "this is description"
        },
        {
          "title": "Second"
        },
        {
          "title": "Last"
        }
      ]
    }
  ]
}
```

### 设置状态

通过 `status` 配置步骤的状态。当 `status` 的值为字符串时，配置的是当前所处步骤的状态；当 `status` 的值为对象时，配置的是各步骤的状态，对象值中的 key 为步骤的 value 值。

```schema
{
  "type": "page",
  "body": [
    {
      "type": "steps",
      "value": 1,
      "status": "error",
      "steps": [
        {
          "title": "First"
        },
        {
          "title": "Second",
          "subTitle": "this is subTitle",
          "description": "this is description"
        },
        {
          "title": "Last"
        }
      ]
    },
    {
      "type": "steps",
      "value": "second",
      "status": {
        "first": "error",
        "second": "wait",
        "last": "finish"
      },
      "steps": [
        {
          "title": "First",
          "value": "first"
        },
        {
          "title": "Second",
          "subTitle": "this is subTitle",
          "description": "this is description",
          "value": "second"
        },
        {
          "title": "Last",
          "value": "last"
        }
      ]
    }
  ]
}
```

### 指定步骤条方向

通过 `mode` 指定步骤条方向。

```schema
{
  "type": "page",
  "body": {
    "type": "steps",
    "mode": "vertical",
    "value": 1,
    "steps": [
      {
        "title": "First",
        "subTitle": "this is subTitle",
        "description": "this is description"
      },
      {
        "title": "Second",
        "subTitle": "this is subTitle",
        "description": "this is description"
      },
      {
        "title": "Last",
        "subTitle": "this is subTitle",
        "description": "this is description"
      }
    ]
  }
}
```

### 指定标签放置位置

通过 `labelPlacement` 指定标签放置位置。

```schema
{
  "type": "page",
  "body": {
    "type": "steps",
    "value": 1,
    "labelPlacement": "vertical",
    "steps": [
      {
        "title": "First",
        "subTitle": "this is subTitle",
        "description": "this is description"
      },
      {
        "title": "Second",
        "subTitle": "this is subTitle",
        "description": "this is description"
      },
      {
        "title": "Last",
        "subTitle": "this is subTitle",
        "description": "this is description"
      }
    ]
  }
}
```

### 点状步骤条

通过 `progressDot` 可开启点状步骤条模式。

```schema
{
  "type": "page",
  "body": {
    "type": "steps",
    "value": 1,
    "progressDot": true,
    "steps": [
      {
        "title": "First",
        "subTitle": "this is subTitle",
        "description": "this is description"
      },
      {
        "title": "Second",
        "subTitle": "this is subTitle",
        "description": "this is description"
      },
      {
        "title": "Last",
        "subTitle": "this is subTitle",
        "description": "this is description"
      }
    ]
  }
}
```

### 数据映射

当前处于第几步统一通过 `name` 关联变量名，其他配置可通过 `"${xxx}"` 关联上下文变量。

```schema
{
  "type": "page",
  "data": {
    "step": 1,
    "status": "error",
    "secondTitle": "Second"
  },
  "body": [
    {
      "type": "steps",
      "name": "step",
      "status": "${status}",
      "steps": [
        {
          "title": "First",
          "subTitle": "this is subTitle",
          "description": "this is description"
        },
        {
          "title": "${secondTitle}"
        },
        {
          "title": "Last"
        }
      ]
    }
  ]
}
```

### 接口映射

接口返回的数据也是一样，都会在同一个数据域，所以取值方式是一样的。

```schema
{
  "type": "page",
  "initApi": "/api/mock2/steps/get",
  "body": [
    {
      "type": "steps",
      "name": "step",
      "status": "${status}",
      "steps": [
        {
          "title": "First",
          "subTitle": "this is subTitle",
          "description": "this is description"
        },
        {
          "title": "Second"
        },
        {
          "title": "Last"
        }
      ]
    }
  ]
}
```

### Form 中静态展示

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "initApi": "/api/mock2/steps/steps",
    "body": [
      {
        "type": "steps",
        "source": "${steps}",
        "name": "current"
      }
    ]
  }
}
```

### 动态数据

### 远程拉取

除了可以通过数据映射获取当前数据域中的变量以外，`source` 还支持配置接口，格式为 API，用于动态返回选项组。

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "body": [
      {
        "type": "steps",
        "name": "steps",
        "source": "/api/mock2/steps/steps"
      }
    ]
  }
}
```

远程拉取接口时，返回的数据结构除了需要满足 amis 接口要求的基本数据结构以外，必须用 "steps" 作为选项组的 key 值，如下：

```json
{
  "status": 0,
  "msg": "",
  "data": {
    "steps": [
      {
        "title": "First",
        "subTitle": "this is sub title",
        "value": "first"
      },
      {
        "title": "Second",
        "description": "this is description",
        "value": "secord"
      },
      {
        "title": "Last",
        "value": "last"
      }
    ],
    "value": "secord",
    "status": "error"
  }
}
```

### 数据域变量配置

```schema
{
  "type": "page",
  "data": {
    "steps": [
      {
        "title": "First",
        "subTitle": "this is subTitle",
        "description": "this is description"
      },
      {
        "title": "Second"
      },
      {
        "title": "Last"
      }
    ]
  },
  "body": [
    {
      "type": "steps",
      "name": "step",
      "source": "${steps}"
    }
  ]
}
```

### 可点击

通过 `clickable` 可开启步骤可点击模式。

```schema
{
  "type": "page",
  "id": "thePage",
  "data": {
    "steps": 1,
  },
  "body": [
    {
      "type": "steps",
      "name": "steps",
      "clickable": true,
      "steps": [
        {
          "title": "First",
          "subTitle": "this is subTitle",
          "description": "this is description"
        },
        {
          "title": "Second"
        },
        {
          "title": "Last"
        }
      ],
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "setValue",
              "componentId": "thePage",
              "args": {
                "value": "${{ steps: event.data.index }}"
              }
            }
          ]
        }
      }
    }
  ]
}
```

### 箭头连接器

通过 `delimiter` 开启箭头连接器。

```schema
{
  "type": "page",
  "body": [
    {
      "type": "steps",
      "delimiter": "arrow",
      "value": 1,
      "steps": [
        {
          "title": "First",
          "subTitle": "this is subTitle",
          "description": "this is description"
        },
        {
          "title": "Second"
        },
        {
          "title": "Last"
        }
      ]
    }
  ]
}
```

### 属性表

| 属性名         | 类型                                                                                                                                | 默认值       | 说明                                                                 | 版本 |
| -------------- | ----------------------------------------------------------------------------------------------------------------------------------- | ------------ | -------------------------------------------------------------------- | -------------------------------------------------------------------- |
| type           | `string`                                                                                                                            |              | `"steps"` 指定为 步骤条 渲染器                                       |   |
| steps          | `Array<Step>`                                                                                                                       | []           | 数组，配置步骤信息                                                   |   |
| source         | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api) 或 [数据映射](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/data-mapping) |              | 选项组源，可通过数据映射获取当前数据域变量、或者配置 API 对象        |   |
| name           | `string`                                                                                                                            |              | 关联上下文变量                                                       |   |
| value          | `string` \| `number`                                                                                                                | `-`          | 设置默认值，注意不支持表达式                                         |   |
| status         | `StepStatus` \| `{[propName: string]: stepStatus;} `                                                                                | `-`          | 状态                                                                 |   |
| className      | `string`                                                                                                                            | `-`          | 自定义类名                                                           |   |
| mode           | `horizontal` \| `vertical`                                                                                                          | `horizontal` | 指定步骤条方向。目前支持水平（horizontal）和竖直（vertical）两种方向 |   |
| labelPlacement | `horizontal` \| `vertical`                                                                                                          | `horizontal` | 指定标签放置位置，默认水平放图标右侧，可选 (vertical) 放图标下方     |   |
| progressDot    | `boolean`                                                                                                                           | `false`      | 点状步骤条                                                           |   |
| tooltipVisible    | `boolean`                                                                                                                           | `true`      | 是否显示默认Tooltip                                                           |  1.63.0 |

### Step

| 属性名      | 类型                                                                              | 默认值 | 说明                                    | 版本 |
| ----------- | --------------------------------------------------------------------------------- | ------ | --------------------------------------- | --------------------------------------- |
| title       | `string` \| [SchemaNode](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode) |        | 标题                                     |    |
| subTitle    | `string` \| [SchemaNode](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode) |        | 子标题                                  |    |
| description | `string` \| [SchemaNode](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode) |        | 详细描述                                |    |
| icon        | `string`                                                                          |        | icon 名，支持 fontawesome v4 或使用 url |    |
| value       | `string`                                                                          |        | value                                   |    |
| className   | `string`                                                                          |        | 自定义类名                              |    |
| tooltipVisible    | `boolean`                                                                                                                           | `true`      | 是否显示默认Tooltip                                                           |  1.63.0 |

### StepStatus

`wait` | `process` | `finish` | `error`

### 事件表

当前组件会对外派发以下事件，可以通过`onEvent`来监听这些事件，并通过`actions`来配置执行的动作，在`actions`中可以通过`${事件参数名}`来获取事件产生的数据，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action)。

| 事件名称 | 事件参数                                                               | 说明                                                                            | 版本  |
| -------- | ---------------------------------------------------------------------- | ------------------------------------------------------------------------------- | ----- |
| click    | `step: Step` 被点击步骤的配置信息<br/>`index: number` 被点击步骤的序号 | Steps.clickable 为 true 且 Step.clickable 为 true 时，点击步骤触发 click 事件。 | 1.5.0 |
