---
title: Table 表格
description:
type: 0
group: ⚙ 组件
menuName: Table 表格
icon:
order: 67
---

表格展示，不支持配置初始化接口初始化数据域，所以需要搭配类似像`Service`这样的，具有配置接口初始化数据域功能的组件，或者手动进行数据域初始化，然后通过`source`属性，获取数据链中的数据，完成数据展示。

## 场景推荐
### 基本场景

```schema
{
  "type": "page",
  "data": {
    "rows": [
      {
        "engine": "Trident",
        "browser": "Internet Explorer 4.0",
        "platform": "Win 95+",
        "version": "4",
        "grade": "X",
        "id": 1,
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 5.0",
        "platform": "Win 95+",
        "version": "5",
        "grade": "C",
        "id": 2
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 5.5",
        "platform": "Win 95+",
        "version": "5.5",
        "grade": "A",
        "id": 3
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 6",
        "platform": "Win 98+",
        "version": "6",
        "grade": "A",
        "id": 4
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 7",
        "platform": "Win XP SP2+",
        "version": "7",
        "grade": "A",
        "id": 5
      }
    ]
  },
  "body": {
    "type": "group-container",
    "activeKey": [
      "0"
    ],
    "items": [
      {
        "header": {
          "title": "复杂信息"
        },
        "body": [
          {
            "type": "table",
            "source": "$rows",
            "id": "parentTableId-1",
            "columnsTogglable": false,
            "selectable": true,
            "multiple": true,
            "columns": [
              {
                "name": "id",
                "label": "ID"
              },
              {
                "name": "version",
                "label": "Version"
              },
              {
                "name": "browser",
                "label": "Browser",
                "copyable": {
                  "content": "复制的内容是：${browser}"
                }
              },
              {
                "name": "platform",
                "label": "Platform"
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### 嵌套子表格+自定义展开/收起

嵌套子表格，以及子表格数据对比场景

```schema
{
  "type": "page",
  "data": {
    "rows": [
      {
        "engine": "Trident",
        "browser": "Internet Explorer 4.0",
        "platform": "Win 95+",
        "version": "4",
        "grade": "X",
        "id": 1,
        "childrens": [
          {
            "fieldName": "Method",
            "baselineVersion": "GET",
            "diffVersion": "POST",
            "status": "EDIT"
          },
          {
            "fieldName": "URL",
            "baselineVersion": "/users",
            "diffVersion": "/user-list",
            "status": "EDIT"
          },
          {
            "fieldName": "TEXT",
            "baselineVersion": "",
            "diffVersion": "描述文案补充",
            "status": "ADD"
          },
          {
            "fieldName": "DESC",
            "baselineVersion": "123",
            "diffVersion": "123",
            "status": "DELETE"
          }
        ]
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 5.0",
        "platform": "Win 95+",
        "version": "5",
        "grade": "C",
        "id": 2
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 5.5",
        "platform": "Win 95+",
        "version": "5.5",
        "grade": "A",
        "id": 3
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 6",
        "platform": "Win 98+",
        "version": "6",
        "grade": "A",
        "id": 4
      },
      {
        "engine": "Trident",
        "browser": "Internet Explorer 7",
        "platform": "Win XP SP2+",
        "version": "7",
        "grade": "A",
        "id": 5
      }
    ]
  },
  "body": {
    "type": "group-container",
    "activeKey": [
      "0"
    ],
    "items": [
      {
        "header": {
          "title": "复杂信息"
        },
        "body": [
          {
            "type": "table",
            "source": "$rows",
            "id": "parentTableId-2",
            "columnsTogglable": false,
            "showExpansionColumn": false,
            "columns": [
              {
                "name": "id",
                "label": "ID"
              },
              {
                "name": "version",
                "label": "Version"
              },
              {
                "name": "browser",
                "label": "Browser",
                "copyable": {
                  "content": "复制的内容是：${browser}"
                }
              },
              {
                "name": "platform",
                "label": "Platform"
              },
              {
                "type": "operation",
                "label": "操作",
                "width": 80,
                "buttons": [
                  {
                    "type": "button",
                    "label": "${_amisExpanded ? '收起' : '展开'}",
                    "level": "link",
                    "onEvent": {
                      "click": {
                        "actions": [
                          {
                            "actionType": "toggleExpanded",
                            "componentId": "parentTableId-2",
                            "args": {
                              "condition": "${id === currentId}",
                              "currentId": "${id}"
                            }
                          }
                        ]
                      }
                    }
                  }
                ]
              }
            ],
            "subTable": {
              "type": "table",
              "source": "$childrens",
              "columns": [
                {
                  "name": "fieldName",
                  "label": "字段名"
                },
                {
                  "name": "baselineVersion",
                  "label": "更新前"
                },
                {
                  "name": "diffVersion",
                  "label": "更新后",
                  "type": "container",
                  "body": [
                    {
                      "type": "tpl",
                      "tpl": "${diffVersion}",
                      "className": "pm-text-success",
                      "visibleOn": "${diffVersion && status ==='ADD'}"
                    },
                    {
                      "type": "tpl",
                      "tpl": "${diffVersion}",
                      "className": "pm-text-warning",
                      "visibleOn": "${diffVersion && status ==='EDIT'}"
                    },
                    {
                      "type": "tpl",
                      "tpl": "${diffVersion}",
                      "className": "pm-text-danger line-through",
                      "visibleOn": "${diffVersion && status ==='DELETE'}"
                    },
                    {
                      "type": "tpl",
                      "tpl": "-",
                      "visibleOn": "${!diffVersion}",
                      "className": "pm-text-muted"
                    }
                  ]
                }
              ],
              "subTable": {
                "label": "弹个框",
                "type": "button",
                "actionType": "dialog",
                "dialog": {
                  "title": "弹框",
                  "body": "这是个简单的弹框。"
                }
              }
            }
          }
        ]
      }
    ]
  }
}
```

## 组件用法
### 拖拽

```schema: scope="body"
{
    "type": "service",
    "api": "/api/mock2/sample?perPage=5",
    "body": [
        {
            "type": "table",
            "source": "$rows",
            "draggable": true,
            "columns": [
                {
                    "name": "engine",
                    "label": "Engine"
                },

                {
                    "name": "version",
                    "label": "Version"
                }
            ]
        }
    ]
}
```

### 列选择

1. `maxKeepItemSelectionLength`可设置最多选择几个，当设置了 number 之后，全选禁用。
2. `selected`可设置选择回显，分页切换会丢失选择状态。

```schema: scope="body"
{
    "type": "service",
    "api": "/api/mock2/sample?perPage=5",
    "body": [
        {
            "type": "table",
            "source": "$rows",
            "selectable": true,
            "multiple": true,
            "checkOnItemClick": false,
            "maxKeepItemSelectionLength": 3,
            "columns": [
                {
                    "name": "engine",
                    "label": "Engine"
                },

                {
                    "name": "version",
                    "label": "Version"
                }
            ],
            "onEvent": {
                "selectedChange": { // 监听点击事件
                    "actions": [ // 执行的动作列表
                        {
                            "actionType": "custom",
                            "script": "event.setData({selectRows: event.data.selectedItems })"
                        }
                    ]
                }
            }
        }
    ]
}
```
### 单列排序

```schema: scope="body"
{
    "type": "service",
    "api": {
        "url": "/api/mock2/sample",
        "method": "get",
        "data": {
            "perPage": "5",
            "engine": "${engine}",
            "version": "${version}",
            "orderBy": "${orderBy}",
            "orderDir": "${orderDir}"
        }
    },
    "data": {
        "enginOptions": [{
            "value": "Trident",
            "label": "测试"
        }, {
            "value": "Trident-1",
            "label": "测试1"
        }],
    },
    "body": [
        {
            "type": "table",
            "source": "$rows",
            "sortMultiple": false,
            "columns": [
                {
                    "name": "engine",
                    "label": "Engine",
                    "sortable": true
                },

                {
                    "name": "version",
                    "label": "Version",
                    "sortable": true
                }
            ],
            "onEvent": {
                "columnSearch": { // 监听点击事件
                    "actions": [ // 执行的动作列表
                        {
                            "actionType": "custom",
                            "script": "console.log(event, 'columnSearch')"
                        }
                    ]
                }
            }
        }
    ]
}
```

### 列筛选排序

在表头出现搜索，可配置 `searchable` 和 `headSearchable` 实现，且搜索后，在表头上方可回显搜索条件。

请注意：为了和`CRUD`的搜索回显保持一致，请使用`headSearchable`属性，`searchable`后续会被废弃。

```schema: scope="body"
{
    "type": "service",
    "api": {
        "url": "/api/mock2/sample",
        "method": "get",
        "data": {
            "perPage": "5",
            "engine": "${engine}",
            "version": "${version}",
            "sortList": "${sortList}"
        }
    },
    "data": {
        "enginOptions": [{
            "value": "Trident",
            "label": "测试"
        }, {
            "value": "Trident-1",
            "label": "测试1"
        }],
    },
    "body": [
        {
            "type": "table",
            "source": "$rows",
            "columns": [
                {
                    "name": "engine",
                    "label": "Engine",
                    "headSearchable": {
                        "type": "select",
                        "source": "$enginOptions",
                        "name": "engine",
                        "label": "主键"
                    },
                    "sortable": true
                },

                {
                    "name": "version",
                    "label": "Version",
                    "sortable": true,
                    "headSearchable": true
                }
            ],
            "onEvent": {
                "columnSearch": { // 监听点击事件
                    "actions": [ // 执行的动作列表
                        {
                            "actionType": "custom",
                            "script": "console.log(event, 'columnSearch')"
                        }
                    ]
                }
            }
        }
    ]
}
```

### 数据需求

数据是对象数组，比如前面的例子中 `rows` 的值类似：

```
[
    {
        "engine": "webkie",
        "version": 1
    }
]
```

### 空状态展示

```schema: scope="body"
{
    "type": "service",
    "api": "/api/mock2/sample?perPage=5",
    "body": [
        {
            "type": "table",
            "source": [],
            "columns": [
                {
                    "name": "engine",
                    "label": "Engine"
                },

                {
                    "name": "version",
                    "label": "Version"
                }
            ],
            "placeholder": "暂无数据"
        }
    ]
}
```

### 列配置

`columns`内，除了简单的配置`label`和`name`展示数据以外，还支持一些额外的配置项，可以帮助更好的展示数据。

#### 列类型

除了简单展示数据源所返回的数据以外，`list`的列支持除表单项以外所有组件类型，例如：

```schema
{
    "type": "page",
    "body": {
        "type": "table",
        "data": {
            "items": [
                {
                    "id": "91264",
                    "text": "衡 阎",
                    "progress": 22,
                    "type": 4,
                    "boolean": true,
                    "list": [
                        {
                            "title": "Forward Functionality Technician",
                            "description": "nisi ex eum"
                        },
                        {
                            "title": "District Applications Specialist",
                            "description": "ipsam ratione voluptas"
                        },
                    ],
                    "audio": "https://news-bos.cdn.bcebos.com/mvideo/%E7%9A%87%E5%90%8E%E5%A4%A7%E9%81%93%E4%B8%9C.aac",
                    "carousel": [
                        {
                            "image": "https://suda.cdn.bcebos.com/amis/images/alice-macaw.jpg"
                        },
                        {
                            "html": "<div style=\"width: 100%; height: 200px; background: #e3e3e3; text-align: center; line-height: 200px;\">carousel data in crud</div>"
                        },
                        {
                            "image": "https://video-react.js.org/assets/poster.png"
                        }
                    ],
                    "date": 1591270438,
                    "image": "https://suda.cdn.bcebos.com/amis/images/alice-macaw.jpg",
                    "json": {
                        "id": 1,
                        "text": "text"
                    }
                },
                {
                    "id": "34202",
                    "text": "吉 卢汉市",
                    "progress": 85,
                    "type": 1,
                    "boolean": true,
                    "list": [
                        {
                            "title": "Dynamic Assurance Orchestrator",
                            "description": "ea ullam voluptates"
                        },
                        {
                            "title": "Internal Division Assistant",
                            "description": "illum deleniti qui"
                        },
                    ],
                    "audio": "https://news-bos.cdn.bcebos.com/mvideo/%E7%9A%87%E5%90%8E%E5%A4%A7%E9%81%93%E4%B8%9C.aac",
                    "carousel": [
                        {
                            "image": "https://suda.cdn.bcebos.com/amis/images/alice-macaw.jpg"
                        },
                        {
                            "html": "<div style=\"width: 100%; height: 200px; background: #e3e3e3; text-align: center; line-height: 200px;\">carousel data in crud</div>"
                        },
                        {
                            "image": "https://video-react.js.org/assets/poster.png"
                        }
                    ],
                    "date": 1591270438,
                    "image": "https://suda.cdn.bcebos.com/amis/images/alice-macaw.jpg",
                    "json": {
                        "id": 1,
                        "text": "text"
                    }
                },
                {
                    "id": "37701",
                    "text": "立辉安市",
                    "progress": 72,
                    "type": 2,
                    "boolean": false,
                    "list": [
                        {
                            "title": "Corporate Metrics Liason",
                            "description": "aspernatur natus qui"
                        },
                        {
                            "title": "Central Paradigm Analyst",
                            "description": "sequi numquam ad"
                        },
                    ],
                    "audio": "https://news-bos.cdn.bcebos.com/mvideo/%E7%9A%87%E5%90%8E%E5%A4%A7%E9%81%93%E4%B8%9C.aac",
                    "carousel": [
                        {
                            "image": "https://suda.cdn.bcebos.com/amis/images/alice-macaw.jpg"
                        },
                        {
                            "html": "<div style=\"width: 100%; height: 200px; background: #e3e3e3; text-align: center; line-height: 200px;\">carousel data in crud</div>"
                        },
                        {
                            "image": "https://video-react.js.org/assets/poster.png"
                        }
                    ],
                    "date": 1591270438,
                    "image": "https://suda.cdn.bcebos.com/amis/images/alice-macaw.jpg",
                    "json": {
                        "id": 1,
                        "text": "text"
                    }
                }
            ]
        },
        "affixHeader": false,
        "syncLocation": false,
        "columns": [
            {
                "name": "id",
                "label": "ID",
                "type": "text"
            },
            {
                "name": "text",
                "label": "文本",
                "type": "text"
            },
            {
                "type": "image",
                "label": "图片",
                "name": "image"
            },
            {
                "name": "date",
                "type": "date",
                "label": "日期"
            },
            {
                "name": "progress",
                "label": "进度",
                "type": "progress"
            },
            {
                "name": "boolean",
                "label": "状态",
                "type": "status"
            },
            {
                "name": "type",
                "label": "映射",
                "type": "mapping",
                "map": {
                    "1": "<span class='label label-info'>漂亮</span>",
                    "2": "<span class='label label-success'>开心</span>",
                    "3": "<span class='label label-danger'>惊吓</span>",
                    "4": "<span class='label label-warning'>紧张</span>",
                    "*": "其他：${type}"
                }
            },
            {
                "name": "list",
                "type": "list",
                "label": "List",
                "placeholder": "-",
                "listItem": {
                    "title": "${title}",
                    "subTitle": "${description}"
                }
            }
        ]
    }
}
```

#### 列宽

可以给列配置 `width` 属性，控制列宽，共有两种方式：

##### 固定像素与百分比
可以配置数字，用于设置列宽像素，例如下面例子我们给`Rendering engine`列宽设置为`100px`。  
也可以百分比来指定列宽，例如下面例子我们给`Rendering engine`列宽设置为`50%`。

```schema: scope="body"
{
    "type": "service",
    "api": "/api/mock2/sample?perPage=5",
    "body": [
      {
        "type": "table",
        "source": "$rows",
        "columns": [
          {
            "name": "engine",
            "label": "Engine",
             "width": 100
          },
          {
            "name": "version",
           "label": "Version",
            "width": "50%",
          }
        ]
      }
    ]
}
```

#### 列对齐方式

通过 align 可以控制列文本对齐方式，比如

```schema: scope="body"
{
    "type": "service",
    "api": "/api/mock2/sample?perPage=5",
    "body": [
      {
        "type": "table",
        "source": "$rows",
        "columns": [
          {
            "name": "engine",
            "label": "Engine"
          },
          {
            "label": "Version",
            "type": "tpl",
            "tpl": "${version | number}",
            "align": "right"
          }
        ]
      }
    ]
}
```

<!-- 如果要单独设置标题的样式，可以使用 `labelClassName` 属性

```schema: scope="body"
{
    "type": "service",
    "api": "/api/mock2/sample?perPage=5",
    "body": [
      {
        "type": "table",
        "source": "$rows",
        "columns": [
          {
            "name": "engine",
            "label": "Engine"
          },
          {
            "name": "version",
            "label": "Version",
            "className": "text-primary",
            "labelClassName": "font-bold"
          }
        ]
      }
    ]
}
``` -->

#### 单元格样式

`classNameExpr` 可以根据数据动态添加 CSS 类，支持 [模板](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/template) 语法。

例如下例，`"<%= data.version > 5 ? "pm-text-danger" : "" %>"` 表示当行数据的 `version` 数据大于 5 的时候添加 `pm-text-danger` CSS 类名，使得文字颜色变红

```schema: scope="body"
{
    "type": "service",
    "api": "/api/mock2/sample?perPage=5",
    "body": [
        {
            "type": "table",
            "source": "$rows",

            "columns": [
                {
                    "name": "engine",
                    "label": "Engine"
                },
                {
                    "name": "version",
                    "label": "Version",
                    "classNameExpr": "<%= data.version > 5 ? 'pm-text-danger' : '' %>",
                },
                {
                    "name": "grade",
                    "label": "Grade"
                }
            ]
        }
    ]
}
```

<!-- #### 背景色阶

`backgroundScale` 可以用来根据数据控制自动分配色阶

```schema: scope="body"
{
    "type": "service",
    "data": {
        "rows": [
            {
                "engine": "Trident",
                "version": "1",
                "grade": "A"
            },
            {
                "engine": "Trident",
                "version": "7",
                "grade": "B"
            },
            {
                "engine": "Trident",
                "version": "4",
                "grade": "C"
            },
            {
                "engine": "Trident",
                "version": "3",
                "grade": "A"
            },
            {
                "engine": "Trident",
                "version": "4",
                "grade": "A"
            },
            {
                "engine": "Gecko",
                "version": "6",
                "grade": "A"
            },
            {
                "engine": "Gecko",
                "version": "2",
                "grade": "A"
            },
            {
                "engine": "Gecko",
                "version": "5",
                "grade": "B"
            },
            {
                "engine": "Gecko",
                "version": "10",
                "grade": "D"
            }
        ]
    },
    "body": [
        {
            "type": "table",
            "source": "$rows",
            "columns": [
                {
                    "name": "engine",
                    "label": "Engine"
                },
                {
                    "name": "version",
                    "label": "Version",
                    backgroundScale: {
                        min: 0,
                        max: 10,
                        colors: ['#FFEF9C', '#FF7127']
                    }
                },
                {
                    "name": "grade",
                    "label": "Grade"
                }
            ]
        }
    ]
}
``` 

`min` 和 `max` 都支持变量，如果为设置会自动计算当前列的最大和最小值。

默认会从当前列的 `name` 属性来获取数据，也可以通过 `backgroundScale.source` 使用变量及公式来获取数据。
-->
#### 默认是否显示

默认 `columnsTogglable` 配置为 `auto`，当列超过 5 列后，就会在工具栏多渲染出来一个列展示与否的开关。你可以设置成 `true` 或者 `false` 来强制开或者关。在列配置中可以通过配置 `toggled` 为 `false` 默认不展示这列，比如下面这个例子中 ID 这一栏。

```schema: scope="body"
{
    "type": "service",
    "api": "/api/mock2/sample?perPage=5",
    "body": [
        {
            "type": "table",
            "source": "$rows",
            "columns": [
                {
                    "name": "engine",
                    "label": "Engine"
                },

                {
                    "name": "grade",
                    "label": "Grade"
                },

                {
                    "name": "version",
                    "label": "Version"
                },

                {
                    "name": "browser",
                    "label": "Browser"
                },

                {
                    "name": "id",
                    "label": "ID",
                    "toggled": false
                },

                {
                    "name": "platform",
                    "label": "Platform"
                }
            ]
        }
    ]
}
```

列展示操作后会将勾选结果持久化到`localStorage`中，下次打开页面会根据上次勾选结果展示列。`1.64.0`版本调整存储key的生成规则`location.pathname + location.hash + self.path + schema的id + columns的name||label||index`。

#### 是否在选择显示列中显示

通过配置 `"toggable": false` 可以控制该列是否在选择显示列中显示
[在columns-toggler中使用](/dataseeddesigndocui/#/amis/zh-CN/components/crud#禁止显示列勾选操作)

```schema: scope="body"
{
    "type": "service",
    "api": "/api/mock2/sample?perPage=5",
    "body": [
        {
            "type": "table",
            "source": "$rows",
            "id":"toggable-table",
            "columns": [
                {
                    "name": "engine",
                    "label": "Engine",
                    "toggable": false
                },

                {
                    "name": "grade",
                    "label": "Grade"
                },

                {
                    "name": "version",
                    "label": "Version"
                },

                {
                    "name": "browser",
                    "label": "Browser"
                },

                {
                    "name": "id",
                    "label": "ID",
                    "toggled": false
                },

                {
                    "name": "platform",
                    "label": "Platform"
                }
            ]
        }
    ]
}
```

#### 固定列

列太多可以让重要的几列固定，可以配置固定在左侧还是右侧，只需要给需要固定的列上配置 `fixed` 属性，配置 `left` 或者 `right`。

```schema
{
  "type": "page",
  "body": {
    "type": "service",
    "api": "/api/mock2/sample?perPage=5",
    "body": [
        {
            "type": "table",
            "source": "$rows",
            "columnsTogglable": false,
            "columns": [
                {
                    "name": "id",
                    "label": "ID",
                    "fixed": "left"
                },
                {
                    "name": "engine",
                    "label": "Engine",
                    "groupName": "Group-1",
                    "fixed": "left"
                },
                {
                    "name": "grade",
                    "label": "Grade",
                },
                {
                    "name": "version",
                    "label": "Version"
                },
                {
                    "name": "version",
                    "label": "Version"
                },
                {
                    "name": "version",
                    "label": "Version"
                },
                {
                    "name": "version",
                    "label": "Version"
                },
                {
                    "name": "version",
                    "label": "Version"
                },
                {
                    "name": "version",
                    "label": "Version"
                },
                {
                    "name": "version",
                    "label": "Version"
                },
                {
                    "name": "browser",
                    "label": "Browser",
                    "groupName": "Group-2",
                    "fixed": "right"
                },
                {
                    "name": "platform",
                    "label": "Platform",
                    "groupName": "Group-2",
                    "fixed": "right"
                }
            ]
        }
    ]
  }
}
```
<!-- #### 表头样式

可以配置`"isHead": true`，来让当前列以表头的样式展示。应用场景是：

1. 所有列`label`配置空字符串，不显示表头
2. 配置`combineNum`，合并单元格，实现左侧表头的形式
3. 列上配置`"isHead": true`，调整样式

```schema: scope="body"
{
  "type": "page",
  "body": {
    "type": "service",
    "api": "/api/mock2/sample?perPage=5",
    "body": [
      {
        "type": "table",
        "source": "$rows",
        "affixHeader": false,
        "columnsTogglable": false,
        "combineNum": 1,
        "columns": [
          {
            "name": "engine",
            "label": "",
            "isHead": true
          },
          {
            "name": "grade",
            "label": ""
          },
          {
            "name": "version",
            "label": ""
          },
          {
            "name": "browser",
            "label": ""
          },
          {
            "name": "id",
            "label": ""
          },
          {
            "name": "platform",
            "label": ""
          }
        ]
      }
    ]
  }
}
``` 

还可以配置"offset"，实现弹出框位置调整自定义

```schema
{
  "type": "page",
  "body": {
    "type": "service",
    "api": "/api/mock2/sample?perPage=5",
    "body": [
      {
        "type": "table",
        "source": "$rows",
        "columnsTogglable": false,
        "columns": [
          {
            "name": "engine",
            "label": "engine"
          },
          {
            "name": "grade",
            "label": "grade"
          },
          {
            "name": "browser",
            "label": "browser",
            "popOver": {
              "body": {
                "type": "tpl",
                "tpl": "偏了一点的popover"
              },
              "offset": {
                "y": 100
              }
            }
          }
        ]
      }
    ]
  }
}
```
-->

### 嵌套

表格支持两种嵌套方式：默认嵌套和自定义嵌套。

**两种方式的对比**：

| 特性 | 默认嵌套 | 自定义嵌套(subTable) |
|------|----------|----------------------|
| 列配置 | 继承父表格的列配置 | 可自定义独立的列配置 |
| 功能特性 | 共享父表格的功能特性 | 支持独立的功能配置 |
| 数据结构 | 子表格数据结构需与父表格一致 | 支持不同的数据结构 |
| 数据获取 | 静态数据，从children属性获取 | 支持表达式或API获取 |
| 使用复杂度 | 简单，无需额外配置 | 需要配置subTable schema |

#### 默认嵌套（不配置subTable）

当行数据中存在 children 属性时，表格会自动嵌套显示下去，子表格继承父表格的列配置。

**适用场景**：
- 嵌套子表格不需要独立的表头，表头与父表格一致
- 嵌套子表格需要展示相同的列信息，列配置与父表格一致


```schema: scope="body"
{
    "type": "service",
    "data": {
        "rows": [
            {
                "engine": "Trident",
                "browser": "Internet Explorer 4.0",
                "platform": "Win 95+",
                "version": "4",
                "grade": "X",
                "id": 1,
                "children": [
                {
                    "engine": "Trident",
                    "browser": "Internet Explorer 4.0",
                    "platform": "Win 95+",
                    "version": "4",
                    "grade": "X",
                    "id": 1001
                },
                {
                    "engine": "Trident",
                    "browser": "Internet Explorer 5.0",
                    "platform": "Win 95+",
                    "version": "5",
                    "grade": "C",
                    "id": 1002
                }
                ]
            },
            {
                "engine": "Trident",
                "browser": "Internet Explorer 5.0",
                "platform": "Win 95+",
                "version": "5",
                "grade": "C",
                "id": 2,
                "children": [
                {
                    "engine": "Trident",
                    "browser": "Internet Explorer 4.0",
                    "platform": "Win 95+",
                    "version": "4",
                    "grade": "X",
                    "id": 2001
                },
                {
                    "engine": "Trident",
                    "browser": "Internet Explorer 5.0",
                    "platform": "Win 95+",
                    "version": "5",
                    "grade": "C",
                    "id": 2002
                }
                ]
            },
            {
                "engine": "Trident",
                "browser": "Internet Explorer 5.5",
                "platform": "Win 95+",
                "version": "5.5",
                "grade": "A",
                "id": 3,
                "children": [
                {
                    "engine": "Trident",
                    "browser": "Internet Explorer 4.0",
                    "platform": "Win 95+",
                    "version": "4",
                    "grade": "X",
                    "id": 3001
                },
                {
                    "engine": "Trident",
                    "browser": "Internet Explorer 5.0",
                    "platform": "Win 95+",
                    "version": "5",
                    "grade": "C",
                    "id": 3002
                }
                ]
            },
            {
                "engine": "Trident",
                "browser": "Internet Explorer 6",
                "platform": "Win 98+",
                "version": "6",
                "grade": "A",
                "id": 4,
                "children": [
                {
                    "engine": "Trident",
                    "browser": "Internet Explorer 4.0",
                    "platform": "Win 95+",
                    "version": "4",
                    "grade": "X",
                    "id": 4001
                },
                {
                    "engine": "Trident",
                    "browser": "Internet Explorer 5.0",
                    "platform": "Win 95+",
                    "version": "5",
                    "grade": "C",
                    "id": 4002
                }
                ]
            },
            {
                "engine": "Trident",
                "browser": "Internet Explorer 7",
                "platform": "Win XP SP2+",
                "version": "7",
                "grade": "A",
                "id": 5,
                "children": [
                {
                    "engine": "Trident",
                    "browser": "Internet Explorer 4.0",
                    "platform": "Win 95+",
                    "version": "4",
                    "grade": "X",
                    "id": 5001
                },
                {
                    "engine": "Trident",
                    "browser": "Internet Explorer 5.0",
                    "platform": "Win 95+",
                    "version": "5",
                    "grade": "C",
                    "id": 5002
                }
                ]
            }
        ]
    },
    "body": [
        {
            "type": "table",
            "source": "$rows",
            "columnsTogglable": false,
            "columns": [
                {
                    "name": "engine",
                    "label": "Engine"
                },

                {
                    "name": "grade",
                    "label": "Grade"
                },

                {
                    "name": "version",
                    "label": "Version"
                },

                {
                    "name": "browser",
                    "label": "Browser"
                },

                {
                    "name": "id",
                    "label": "ID"
                },

                {
                    "name": "platform",
                    "label": "Platform"
                }
            ]
        }
    ]
}
```

#### 自定义嵌套（配置subTable）

通过设置 subTable 属性来自定义嵌套的逻辑，可以完全控制嵌套的展示方式。

**适用场景**：
- 嵌套子表格需要独立的表头
- 嵌套的组件不是表格元素
- 嵌套组件的数据需要通过表达式或API动态获取

<font color=red>**重要提示**：</font>
1. `subTable`的`type`配置为`table`时可同时配置`api`，内部实现会给`table`组件包裹一层`service`组件。
2. `subTable`可以获取到父表格的行数据。比如：行数据有`children`时，subTable 可配置`"source": "$children"`。
2. `subTable`的`schema`内部可以通过`_subTableId`变量获取`subTable`的`id`，通过`_subTableServiceId`变量获取子表格`service`容器的`id`。
3. `column`获取行数据时，可以通过`_rowSubTableId`变量获取当前行子表格的`id`，通过`_rowSubTableServiceId`变量获取当前行子表格`service`容器的`id`。
4. `Table`、`CRUD`、`InputTable`组件等，暂不支持`subTable`和列属性中`fixed`属性一起使用。


> `1.19.0` 及以上版本支持触发 reload 动作更新子表格数据

```schema: scope="body"
{
    "type": "service",
    "debug": true,
    "data": {
      "rows": [
        {
          "engine": "Trident",
          "browser": "Internet Explorer 4.0",
          "platform": "Win 95+",
          "version": "4",
          "grade": "X",
          "id": 1,
        },
        {
          "engine": "Trident",
          "browser": "Internet Explorer 5.0",
          "platform": "Win 95+",
          "version": "5",
          "grade": "C",
          "id": 2,
        },
        {
          "engine": "Trident",
          "browser": "Internet Explorer 5.5",
          "platform": "Win 95+",
          "version": "5.5",
          "grade": "A",
          "id": 3,
        },
        {
          "engine": "Trident",
          "browser": "Internet Explorer 6",
          "platform": "Win 98+",
          "version": "6",
          "grade": "A",
          "id": 4,
        },
        {
          "engine": "Trident",
          "browser": "Internet Explorer 7",
          "platform": "Win XP SP2+",
          "version": "7",
          "grade": "A",
          "id": 5,
        }
      ]
    },
    "body": [
      {
        "type": "table",
        "source": "$rows",
        "id": "tableid",
        "columnsTogglable": true,
        "selectable": true,
        "multiple": true,
        "canAccessSuperData": true,
        "columns": [
          {
            "name": "id",
            "label": "ID"
          },
          {
            "name": "engine",
            "label": "Engine"
          },
          {
            "name": "grade",
            "label": "Grade"
          },
          {
            "name": "version",
            "label": "Version"
          },
          {
            "name": "browser",
            "label": "Browser"
          },
          {
            "name": "_rowSubTableServiceId",
            "label": "行子表格service id"
          },
          {
            "type": "operation",
            "label": "操作",
            "width": 80,
            "buttons": [
              {
                "label": "刷新子表格数据",
                "type": "button",
                "level": "link",
                "onEvent": {
                  "click": {
                    "actions": [
                      {
                        "actionType": "reload",
                        "componentId": "${_rowSubTableServiceId}"
                      }
                    ]
                  }
                }
              }
            ]
          }
        ],
        "subTable": {
          "type": "table",
          "api": {
            "url": "/api/mock2/sample?perPage=1&id=${id}",
            "responseData": {
              "subTableList": "${rows}"
            }
          },
          "source": "$subTableList",
          "canAccessSuperData": true,
          "columns": [
            {
              "name": "engine",
              "label": "Engine"
            },
            {
              "name": "grade",
              "label": "Grade"
            },
            {
              "name": "_rowSubTableId",
              "label": "行子表格id"
            },
            {
              "type": "operation",
              "label": "操作",
              "width": 80,
              "buttons": [
                {
                  "label": "刷新当前表格数据",
                  "type": "button",
                  "level": "link",
                  "onEvent": {
                    "click": {
                      "actions": [
                        {
                          "actionType": "reload",
                          "componentId": "${_subTableServiceId}"
                        }
                      ]
                    }
                  }
                }
              ]
            }
          ],
          "onEvent": {
            "fetchInited": {
              "actions": [
                {
                  "actionType": "custom",
                  "script": "console.log(event.data, 'event')"
                }
              ]
            },
            "selectedChange": {
              "actions": [
                {
                  "actionType": "custom",
                  "script": "console.log(event.data, 'event-selectedChange')"
                }
              ]
            },
          },
          "subTable": {
            "label": "弹个框",
            "type": "button",
            "actionType": "dialog",
            "dialog": {
              "title": "弹框",
              "body": "这是个简单的弹框。"
            }
          }
        }
      }
    ]
  }
```

### 底部展示 (Footable)

列太多时，内容没办法全部显示完，可以让部分信息在底部显示，可以让用户展开查看详情。配置很简单，只需要开启 `footable` 属性，同时将想在底部展示的列加个 `breakpoint` 属性为 `*` 即可。

```schema: scope="body"
{
    "type": "service",
    "api": "/api/mock2/sample?perPage=5",
    "body": [
        {
            "type": "table",
            "source": "$rows",
            "columnsTogglable": false,
            "id":"footable-table",
            "footable": true,
            "columns": [
                {
                    "name": "engine",
                    "label": "Engine1"
                },

                {
                    "name": "grade",
                    "label": "Grade2"
                },

                {
                    "name": "version",
                    "label": "Version",
                    "breakpoint": "*"
                },

                {
                    "name": "browser",
                    "label": "Browser",
                    "breakpoint": "*"
                },

                {
                    "name": "id",
                    "label": "ID",
                    "breakpoint": "*"
                },

                {
                    "name": "platform",
                    "label": "Platform",
                    "breakpoint": "*"
                }
            ]
        }
    ]
}
```

默认都不会展开，如果你想默认展开第一个就把 footable 配置成这样。

```json
{
  "footable": {
    "expand": "first"
  }
}
```

当配置成 `all` 时表示全部展开。

### 自定义规则合并单元格

需要在列上配置对应属性`customRowSpan`和`customColSpan`，优先级高于combineNumber
表达式可使用的变量包括行数据record、行索引rowIndex、列索引colIndex

合并行示例`customRowSpan`

```schema
{
  "type": "page",
  "body": {
    "type": "service",
    "data": {
      "rows": [
        {
          "id": 1,
          "engine": "SHEI_SHI_SIMICO",
          "grade": "贷中交易MOB1模型",
          "version": "资信编码1 资信描述1",
          "browser": "2",
          "platform": "4",
          "price": "1.4",
          "cost": "1.24"
        },
        {
          "id": 2,
          "engine": "SHEI_SHI_SIMICO",
          "grade": "贷中交易MOB1模型",
          "version": "资信编码1 资信描述1",
          "browser": "2",
          "platform": "4",
          "price": "1.4",
          "cost": "1.24"
        },
        {
          "id": 3,
          "engine": "SIMICO_GEN_2_SUB",
          "grade": "测试",
          "version": " ",
          "browser": " ",
          "cost": "3",
        }
      ],
    },
    "body": [
      {
        "type": "table",
        "source": "$rows",
        "columnsTogglable": true,
        "columns": [
          {
            "name": "id",
            "label": "ID"
          },
          {
            "name": "engine",
            "label": "模型服务编码",
            "customRowSpan": "${rowIndex === 0 ? 2 : rowIndex === 1 ? 0 : 1}"
          },
          {
            "name": "grade",
            "label": "模型服务名称",
            "customRowSpan": "${rowIndex === 0 ? 2 : rowIndex === 1 ? 0 : 1}"
          },
          {
            "name": "version",
            "label": "资信产品"
          },
          {
            "name": "price",
            "label": "产品费用(元)"
          },
          {
            "name": "cost",
            "label": "模型服务单笔费用",
            "customRowSpan": "${rowIndex === 0 ? 2 : rowIndex === 1 ? 0 : 1}"
          },
          {
            "type": "operation",
            "label": "操作",
            "width": 80,
            "buttons": [
              {
                "label": "删除",
                "type": "button",
                "level": "link",
                "onEvent": {
                  "click": {
                    "actions": [
                      {
                        "actionType": "reload",
                        "componentId": "subTableService"
                      }
                    ]
                  }
                }
              }
            ]
          }
        ]
      }
    ]
  }
}
```

合并列示例`customColSpan`

```schema
{
  "type": "page",
  "body": {
    "type": "service",
    "data": {
      "rows": [
        {
          "id": 1,
          "engine": "SHEI_SHI_SIMICO",
          "grade": "贷中交易MOB1模型",
          "version": "资信编码1 资信描述1",
          "browser": "2",
          "platform": "4",
          "price": "1.4",
          "cost": "1.24"
        },
        {
          "id": 2,
          "engine": "SHEI_SHI_SIMICO",
          "grade": "贷中交易MOB1模型",
          "version": "资信编码1 资信描述1",
          "browser": "2",
          "platform": "4",
          "price": "1.4",
          "cost": "1.24"
        },
        {
          "id": 3,
          "engine": "SIMICO_GEN_2_SUB",
          "grade": "测试",
          "version": " ",
          "browser": " ",
          "cost": "3",
        }
      ],
    },
    "body": [
      {
        "type": "table",
        "source": "$rows",
        "columnsTogglable": true,
        "columns": [
          {
            "name": "id",
            "label": "ID"
          },
          {
            "name": "engine",
            "label": "模型服务编码",
            "customColSpan": "${rowIndex === 0 ? 2 : 1}"
          },
          {
            "name": "grade",
            "label": "模型服务名称",
            "customColSpan": "${rowIndex === 0 ? 0 : 1}"
          },
          {
            "name": "platform",
            "label": "是否采集"
          },
          {
            "name": "price",
            "label": "产品费用(元)"
          },
          {
            "name": "cost",
            "label": "模型服务单笔费用",
          },
          {
            "type": "operation",
            "label": "操作",
            "width": 80,
            "buttons": [
              {
                "label": "删除",
                "type": "button",
                "level": "link",
                "onEvent": {
                  "click": {
                    "actions": [
                      {
                        "actionType": "reload",
                        "componentId": "subTableService"
                      }
                    ]
                  }
                }
              }
            ]
          }
        ]
      }
    ]
  }
}
```

同时合并示例`customColSpan`和`customRowSpan`

```schema
{
  "type": "page",
  "body": {
    "type": "service",
    "data": {
      "rows": [
        {
          "id": 1,
          "engine": "SHEI_SHI_SIMICO",
          "grade": "贷中交易MOB1模型",
          "version": "资信编码1 资信描述1",
          "browser": "2",
          "platform": "4",
          "price": "1.4",
          "cost": "1.24"
        },
        {
          "id": 2,
          "engine": "SHEI_SHI_SIMICO",
          "grade": "贷中交易MOB1模型",
          "version": "资信编码1 资信描述1",
          "browser": "2",
          "platform": "4",
          "price": "1.4",
          "cost": "1.24"
        },
        {
          "id": 3,
          "engine": "SIMICO_GEN_2_SUB",
          "grade": "测试",
          "version": " ",
          "browser": " ",
          "cost": "3",
        }
      ],
    },
    "body": [
      {
        "type": "table",
        "source": "$rows",
        "columnsTogglable": true,
        "columns": [
          {
            "name": "id",
            "label": "ID"
          },
          {
            "name": "engine",
            "label": "模型服务编码",
            "customRowSpan": "${rowIndex === 0 ? 2 : rowIndex === 1 ? 0 : 1}",
            "customColSpan": "${rowIndex === 0 ? 2 : 1}"
          },
          {
            "name": "grade",
            "label": "模型服务名称",
            "customRowSpan": "${rowIndex === 0 ? 2 : rowIndex === 1 ? 0 : 1}",
            "customColSpan": "${rowIndex === 0 ? 0 : 1}"
          },
          {
            "name": "version",
            "label": "资信产品"
          },
          {
            "name": "price",
            "label": "产品费用(元)"
          },
          {
            "name": "cost",
            "label": "模型服务单笔费用",
          },
          {
            "type": "operation",
            "label": "操作",
            "width": 80,
            "buttons": [
              {
                "label": "删除",
                "type": "button",
                "level": "link",
                "onEvent": {
                  "click": {
                    "actions": [
                      {
                        "actionType": "reload",
                        "componentId": "subTableService"
                      }
                    ]
                  }
                }
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### 合并单元格

只需要配置 `combineNum` 属性即可，他表示从左到右多少列内启动自动合并单元格，只要多行的同一个属性值是一样的，就会自动合并。

如果你不想从第一列开始合并单元格，可以配置 `combineFromIndex`，如果配置为 1，则会跳过第一列的合并。如果配置为 2，则会跳过第一列和第二列的合并，从第三行开始向右合并 `combineNum` 列。

> 注意：需要配置`updateAllRows`为true，否则切换每页条数时会有问题。

```schema: scope="body"
{
    "type": "service",
    "data": {
        "rows": [
            {
                "engine": "Trident",
                "browser": "Internet Explorer 4.2",
                "platform": "Win 95+",
                "version": "4",
                "grade": "A"
            },
            {
                "engine": "Trident",
                "browser": "Internet Explorer 4.2",
                "platform": "Win 95+",
                "version": "4",
                "grade": "B"
            },
            {
                "engine": "Trident",
                "browser": "AOL browser (AOL desktop)",
                "platform": "Win 95+",
                "version": "4",
                "grade": "C"
            },
            {
                "engine": "Trident",
                "browser": "AOL browser (AOL desktop)",
                "platform": "Win 98",
                "version": "3",
                "grade": "A"
            },
            {
                "engine": "Trident",
                "browser": "AOL browser (AOL desktop)",
                "platform": "Win 98",
                "version": "4",
                "grade": "A"
            },
            {
                "engine": "Gecko",
                "browser": "Firefox 1.0",
                "platform": "Win 98+ / OSX.2+",
                "version": "4",
                "grade": "A"
            },
            {
                "engine": "Gecko",
                "browser": "Firefox 1.0",
                "platform": "Win 98+ / OSX.2+",
                "version": "5",
                "grade": "A"
            },
            {
                "engine": "Gecko",
                "browser": "Firefox 2.0",
                "platform": "Win 98+ / OSX.2+",
                "version": "5",
                "grade": "B"
            },
            {
                "engine": "Gecko",
                "browser": "Firefox 2.0",
                "platform": "Win 98+ / OSX.2+",
                "version": "5",
                "grade": "C"
            },
            {
                "engine": "Gecko",
                "browser": "Firefox 2.0",
                "platform": "Win 98+ / OSX.2+",
                "version": "5",
                "grade": "D"
            }
        ]
    },
    "body": [
        {
            "type": "table",
            "source": "$rows",
            "combineNum": 3,
            "updateAllRows": true,
            "columnsTogglable": false,
            "columns": [
                {
                    "name": "engine",
                    "label": "Rendering engine"
                },
                {
                    "name": "browser",
                    "label": "Browser"
                },
                {
                    "name": "platform",
                    "label": "Platform(s)"
                },
                {
                    "name": "version",
                    "label": "Engine version"
                },
                {
                    "name": "grade",
                    "label": "CSS grade"
                }
            ]
        }
    ]
}
```

combineNum 支持使用变量，如下所示

```schema: scope="body"
{
    "type": "service",
    "data": {
        "rows": [
            {
                "engine": "Trident",
                "browser": "Internet Explorer 4.2",
                "platform": "Win 95+",
                "version": "4",
                "grade": "A"
            },
            {
                "engine": "Trident",
                "browser": "Internet Explorer 4.2",
                "platform": "Win 95+",
                "version": "4",
                "grade": "B"
            },
            {
                "engine": "Trident",
                "browser": "AOL browser (AOL desktop)",
                "platform": "Win 95+",
                "version": "4",
                "grade": "C"
            },
            {
                "engine": "Trident",
                "browser": "AOL browser (AOL desktop)",
                "platform": "Win 98",
                "version": "3",
                "grade": "A"
            },
            {
                "engine": "Trident",
                "browser": "AOL browser (AOL desktop)",
                "platform": "Win 98",
                "version": "4",
                "grade": "A"
            },
            {
                "engine": "Gecko",
                "browser": "Firefox 1.0",
                "platform": "Win 98+ / OSX.2+",
                "version": "4",
                "grade": "A"
            },
            {
                "engine": "Gecko",
                "browser": "Firefox 1.0",
                "platform": "Win 98+ / OSX.2+",
                "version": "5",
                "grade": "A"
            },
            {
                "engine": "Gecko",
                "browser": "Firefox 2.0",
                "platform": "Win 98+ / OSX.2+",
                "version": "5",
                "grade": "B"
            },
            {
                "engine": "Gecko",
                "browser": "Firefox 2.0",
                "platform": "Win 98+ / OSX.2+",
                "version": "5",
                "grade": "C"
            },
            {
                "engine": "Gecko",
                "browser": "Firefox 2.0",
                "platform": "Win 98+ / OSX.2+",
                "version": "5",
                "grade": "D"
            }
        ],
        combineNum: 3
    },
    "body": [
        {
            "type": "table",
            "source": "$rows",
            "combineNum": "$combineNum",
            "columnsTogglable": false,
            "columns": [
                {
                    "name": "engine",
                    "label": "Rendering engine"
                },
                {
                    "name": "browser",
                    "label": "Browser"
                },
                {
                    "name": "platform",
                    "label": "Platform(s)"
                },
                {
                    "name": "version",
                    "label": "Engine version"
                },
                {
                    "name": "grade",
                    "label": "CSS grade"
                }
            ]
        }
    ]
}
```

### 超级表头

超级表头意思是，表头还可以再一次进行分组。额外添加个 `groupName` 属性即可。

```schema: scope="body"
{
    "type": "service",
    "api": "/api/mock2/sample?perPage=5",
    "body": [
        {
            "type": "table",
            "source": "$rows",
            "columns": [
                {
                    "name": "engine",
                    "label": "Engine",
                    "groupName": "分组1"
                },

                {
                    "name": "grade",
                    "label": "Grade",
                    "groupName": "分组1"
                },

                {
                    "name": "version",
                    "label": "Version",
                    "groupName": "分组2"
                },

                {
                    "name": "browser",
                    "label": "Browser",
                    "groupName": "分组2"
                },

                {
                    "name": "id",
                    "label": "ID",
                    "toggled": false,
                    "groupName": "分组2"
                },

                {
                    "name": "platform",
                    "label": "Platform",
                    "groupName": "分组2"
                }
            ]
        }
    ]
}
```

<!-- ### 高亮行

可以通过配置`rowClassNameExpr`来为行添加 CSS 类，支持 [模板](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/template) 语法。

例如下例，`"<%= data.id % 2 ? "bg-success" : "" %>"` 表示当行数据的 `id` 变量为 不能被 `2` 整除时，给当前行添加`bg-success` CSS 类名，即绿色背景色

```schema: scope="body"
{
    "type": "service",
    "api": "/api/mock2/sample?perPage=10",
    "body": [
        {
            "type": "table",
            "source": "$rows",
            "rowClassNameExpr": "<%= data.id % 2 ? 'bg-success' : 'bg-blue-50' %>",
            "columns": [
                {
                    "name": "engine",
                    "label": "Engine"
                },

                {
                    "name": "grade",
                    "label": "Grade"
                },

                {
                    "name": "version",
                    "label": "Version"
                },

                {
                    "name": "browser",
                    "label": "Browser"
                },

                {
                    "name": "id",
                    "label": "ID"
                },

                {
                    "name": "platform",
                    "label": "Platform"
                }
            ]
        }
    ]
}
``` -->

### 总结行

可以通过配置 `prefixRow` 或 `affixRow` 来为表格顶部或底部添加总结行，

```schema: scope="body"
{
    "type": "service",
    "api": "/api/mock2/sample?perPage=10",
    "body": [
        {
            "type": "table",
            "source": "$rows",
            "columns": [
                {
                    "name": "browser",
                    "label": "Browser"
                },

                {
                    "name": "version",
                    "label": "Version"
                }
            ],
            "affixRow":[
                {
                    "type": "text",
                    "text": "总计"
                },
                {
                    "type": "tpl",
                    "tpl": "${rows|pick:version|sum}"
                }
            ]
        }
    ]
}
```

新增 `affixRowClassNameExpr`、`affixRowClassName`、`prefixRowClassNameExpr`、`prefixRowClassName` 来控制总结行样式，比如下面的例子

```schema: scope="body"
{
    "type": "service",
    "api": "/api/mock2/sample?perPage=10",
    "body": [
        {
            "type": "table",
            "source": "$rows",
            "columns": [
                {
                    "name": "browser",
                    "label": "Browser"
                },

                {
                    "name": "version",
                    "label": "Version"
                }
            ],
            "affixRowClassNameExpr": "${SUM(ARRAYMAP(rows, item => item.version)) > 30 ? 'text-success' : ''}",
            "affixRow":[
                {
                    "type": "text",
                    "text": "总计"
                },
                {
                    "type": "tpl",
                    "tpl": "${rows|pick:version|sum}"
                }
            ]
        }
    ]
}
```

#### 配置合并单元格

可以配置 `colSpan` 来设置一列所合并的列数，例如

```schema: scope="body"
{
    "type": "service",
    "api": "/api/mock2/sample?perPage=10",
    "body": [
        {
            "type": "table",
            "source": "$rows",
            "columns": [
                {
                    "name": "id",
                    "label": "ID"
                },

                {
                    "name": "browser",
                    "label": "Browser"
                },

                {
                    "name": "version",
                    "label": "Version"
                }
            ],
            "affixRow":[
                {
                    "type": "text",
                    "text": "总计",
                    "colSpan": 2
                },
                {
                    "type": "tpl",
                    "tpl": "${rows|pick:version|sum}"
                }
            ]
        }
    ]
}
```

上例中我们给 `总计` 列配置了 `"colSpan": 2`，它会合并两个单元格

#### 配置多行

可以配置二维数组来配置多行总结行

```schema: scope="body"
{
    "type": "service",
    "api": "/api/mock2/sample?perPage=10",
    "body": [
        {
            "type": "table",
            "source": "$rows",
            "columns": [
                {
                    "name": "browser",
                    "label": "Browser"
                },

                {
                    "name": "version",
                    "label": "Version"
                }
            ],
            "affixRow":[
                [
                    {
                        "type": "text",
                        "text": "总计1"
                    },
                    {
                        "type": "tpl",
                        "tpl": "${rows|pick:version|sum}"
                    }
                ],
                [
                    {
                        "type": "text",
                        "text": "总计2"
                    },
                    {
                        "type": "tpl",
                        "tpl": "${rows|pick:version|sum}"
                    }
                ]
            ]
        }
    ]
}
```

### 行操作按钮

通过 itemActions 可以设置鼠标移动到行上出现操作按钮

```schema: scope="body"
{
  "type": "service",
  "api": "/api/mock2/sample?perPage=10",
  "body": [{
    "type": "table",
    "source": "$rows",
    "itemActions": [{
      "label": "编辑",
      "type": "button",
      "actionType": "dialog",
      "dialog": {
        "title": "编辑",
        "body": "这是个简单的编辑弹框"
      }
    }, {
      "label": "删除",
      "type": "button",
      "actionType": "ajax",
      "confirmText": "确认要删除？",
      "api": "/api/mock2/form/saveForm"
    }],
    "columns": [{
        "name": "browser",
        "label": "Browser"
      },

      {
        "name": "version",
        "label": "Version"
      }
    ]
  }]
}
```

### 单行点击操作

处理前面的 itemActions，还可以配置 itemAction 来实现点击某一行后进行操作，支持 [action](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/action) 里的所有配置，比如弹框、刷新其它组件等。

```schema: scope="body"
{
  "type": "service",
  "api": "/api/mock2/sample?perPage=10",
  "body": [{
    "type": "table",
    "source": "$rows",
    "itemAction": {
      "type": "button",
      "actionType": "dialog",
      "dialog": {
        "title": "详情",
        "body": "当前行的数据 browser: ${browser}, version: ${version}"
      }
    },
    "columns": [{
        "name": "browser",
        "label": "Browser"
      },
      {
        "name": "version",
        "label": "Version"
      }
    ]
  }]
}
```

注意这个属性和 `checkOnItemClick` 冲突，因为都是定义行的点击行为，开启 `itemAction` 后 `checkOnItemClick` 将会失效。

<!-- ### 行角标

通过属性`itemBadge`，可以为表格行配置[角标](/dataseeddesigndocui/#/amis/zh-CN/components/badge)，可以使用[数据映射](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/data-mapping)为每一行添加特定的 Badge 属性。[`visibleOn`](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression)属性控制显示的条件，表达式中`this`可以取到行所在上下文的数据，比如行数据中有`badgeText`字段才显示角标，可以设置`"visibleOn": "this.badgeText"`

```schema: scope="body"
{
  "type": "service",
  "body": {
    "type": "table",
    "source": "${table}",
    "syncLocation": false,
    "itemBadge": {
      "text": "${badgeText}",
      "mode": "ribbon",
      "position": "top-left",
      "level": "${badgeLevel}",
      "visibleOn": "this.badgeText"
    },
    "columns": [
        {
            "name": "id",
            "label": "ID"
        },
        {
            "name": "engine",
            "label": "Rendering engine"
        },
        {
            "name": "browser",
            "label": "Browser"
        },
        {
            "name": "platform",
            "label": "Platform(s)"
        },
        {
            "name": "version",
            "label": "Engine version"
        },
        {
            "name": "grade",
            "label": "CSS grade"
        }
    ]
  },
  data: {
    table: [
      {
        "id": 1,
        "engine": "Trident",
        "browser": "Internet Explorer 4.0",
        "platform": "Win 95+",
        "version": "4",
        "grade": "X",
        "badgeText": "默认",
        "badgeLevel": "info"
      },
      {
        "id": 2,
        "engine": "Trident",
        "browser": "Internet Explorer 5.0",
        "platform": "Win 95+",
        "version": "5",
        "grade": "C",
        "badgeText": "危险",
        "badgeLevel": "danger"
      },
      {
        "id": 3,
        "engine": "Trident",
        "browser": "Internet Explorer 5.5",
        "platform": "Win 95+",
        "version": "5.5",
        "grade": "A"
      },
      {
        "id": 4,
        "engine": "Trident",
        "browser": "Internet Explorer 6",
        "platform": "Win 98+",
        "version": "6",
        "grade": "A"
      },
      {
        "id": 5,
        "engine": "Trident",
        "browser": "Internet Explorer 7",
        "platform": "Win XP SP2+",
        "version": "7",
        "grade": "A"
      }
    ]
  }
}
``` -->

### 动态列

columns 支持配置变量来定义列信息。

```schema
{
  "type": "page",
  "body": {
    "type": "service",
    "id": "service",
    "data": {
      "customColumns": [
        {
          "name": "engine",
          "label": "Engine"
        },
        {
          "name": "version",
          "label": "Version"
        }
      ]
    },
    "api": "/api/mock2/sample?perPage=5",
    "body": [
      {
        "type": "flex",
        "direction": "column",
        "gap": true,
        "items": [
          {
            "type": "button-toolbar",
            "buttons": [
              {
                "type": "button",
                "label": "更新列配置",
                "onEvent": {
                  "click": {
                    "actions": [
                      {
                        "actionType": "setValue",
                        "componentId": "service",
                        "args": {
                          "value": {
                            "customColumns": [
                              {
                                "name": "browser",
                                "label": "browser"
                              },
                              {
                                "name": "platform",
                                "label": "platform"
                              }
                            ]
                          }
                        }
                      }
                    ]
                  }
                }
              }
            ]
          },
          {
            "type": "table",
            "source": "$rows",
            "columns": "$customColumns"
          }
        ]
      }
    ]
  }
}
```

### 表格内容高度自适应

通过 `autoFillHeight` 可以让表格内容区自适应高度。

它的展现效果是整个内容区域高度自适应，表格内容较多时在内容区域内出滚动条，这样顶部筛选和底部翻页的位置都是固定的。

开启这个配置后会自动关闭 `affixHeader` 功能避免冲突。

### 属性表

| 属性名              | 类型                                                                                                                                  | 默认值                    | 说明                                                                                                                                                                                         | 版本     |
| ------------------- | ------------------------------------------------------------------------------------------------------------------------------------- | ------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------- |
| type                | `string`                                                                                                                              |                           | `"type"` 指定为 table 渲染器                                                                                                                                                                 |          |
| title               | `string`                                                                                                                              |                           | 标题                                                                                                                                                                                         |          |
| source              | `string`                                                                                                                              | `${items}`                | 数据源, 绑定当前环境变量                                                                                                                                                                     |          |
| affixHeader         | `boolean`                                                                                                                             | `false`                    | 是否固定表头                                                                                                                                                                                 |          |
| columnsTogglable    | `auto` 或者 `boolean`                                                                                                                 | `auto`                    | 展示列显示开关, 自动即：列数量大于或等于 5 个时自动开启                                                                                                                                      |          |
| placeholder         | `string` 或者 `SchemaTpl`                                                                                                             | `暂无数据`                | 当没数据的时候的文字提示                                                                                                                                                                     |          |
| className           | `string`                                                                                                                              | `panel-default`           | 外层 CSS 类名                                                                                                                                                                                |          |
| tableClassName      | `string`                                                                                                                              | `table-db table-striped`  | 表格 CSS 类名                                                                                                                                                                                |          |
| headerClassName     | `string`                                                                                                                              | `Action.md-table-header`  | 顶部外层 CSS 类名                                                                                                                                                                            |          |
| footerClassName     | `string`                                                                                                                              | `Action.md-table-footer`  | 底部外层 CSS 类名                                                                                                                                                                            |          |
| toolbarClassName    | `string`                                                                                                                              | `Action.md-table-toolbar` | 工具栏 CSS 类名                                                                                                                                                                              |          |
| columns             | `Array<Column>`                                                                                                                       |                           | 用来设置列信息      | `1.19.0` 版本支持配置变量                                                                                                                                                                         |          |
| combineNum          | `number`                                                                                                                              |                           | 自动合并单元格。需要配置`updateAllRows`为true。                                                                                                                                                                               |          |
| itemActions         | Array<[Action](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/action-button)>                                                        |                           | 悬浮行操作按钮组                                                                                                                                                                             |          |
| itemCheckableOn     | [表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression)                                                                  |                           | 配置当前行是否可勾选的条件，要用 [表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression)                                                                                        |          |
| itemDraggableOn     | [表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression)                                                                  |                           | 配置当前行是否可拖拽的条件，要用 [表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression)                                                                                        |          |
| draggableConfig     |  `{ disableDropTop?: 'number' \| 表达式, disableDropBottom?: 'number' \| 表达式, }`         |     | 行拖拽配置。 `disableDropTop`属性配置前N条区域不可放置；`disableDropBottom`属性配置后N条区域不可放置。          |     `1.84.0`     |
| checkOnItemClick    | `boolean`                                                                                                                             | `false`                   | 点击数据行是否可以勾选当前行                                                                                                                                                                 |          |
| rowClassName        | `string`                                                                                                                              |                           | 给行添加 CSS 类名                                                                                                                                                                            |          |
| rowClassNameExpr    | [模板](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/template)                                                                      |                           | 通过模板给行添加 CSS 类名                                                                                                                                                                    |          |
| prefixRow           | `Array`                                                                                                                               |                           | 顶部总结行                                                                                                                                                                                   |          |
| affixRow            | `Array`                                                                                                                               |                           | 底部总结行                                                                                                                                                                                   |          |
| itemBadge           | [`BadgeSchema`](/dataseeddesigndocui/#/amis/zh-CN/components/badge)                                                                   |                           | 行角标配置                                                                                                                                                                                   |          |
| autoFillHeight      | `boolean` 丨 `{height: number}` 丨 `{maxHeight: number}`                                                                              |                           | 内容区域自适应高度，可选择自适应、固定高度和最大高度                                                                                                                                         |          |
| resizable           | `boolean`                                                                                                                             | `true`                    | 列宽度是否支持调整                                                                                                                                                                           |          |
| selectable          | `boolean`                                                                                                                             | `false`                   | 支持勾选                                                                                                                                                                                     |          |
| multiple            | `boolean`                                                                                                                             | `false`                   | 勾选 icon 是否为多选样式`checkbox`， 默认为`radio`                                                                                                                                           |          |
| valueField          | `string`                                                                                                                              | `value`                   | 表示行数据的唯一 key                                                                                                                                                                         |          |
| selected            | `string` 或 `Array`                                                                                                                   | `[]`                      | 勾选回显，常搭配`valueField`使用，否则行唯一 key 取值 undefined，则无法回显                                                                                                                  | `1.2.0`  |
| subTable            | `schemaNode`                                                                                                                          |                           | 嵌套子内容schema。配置id无效，可通过`_subTableId`变量获取。                                                                                                                                                                             |          |
| expandConfig        | `{expand?: 'first' \| 'all' \| 'none'; expandAll?: boolean; accordion?: boolean;keepExpanded?: boolean}`                                                    |                           | 表格嵌套时，指定展开，建议数据是接口一次性返回使用该属性，如果是展开时接口懒加载会有问题                                                                                                     |          |
| unfoldIcon          | [模板](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/template) \| [Schema](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode) |                           | 自定义展开图标。如果传入模板，则其将作为`Icon`组件的 icon 属性值；如果传入`Schema`，请传入代表一个 Icon 的 Schema。<br />注意: 只配置`unfoldIcon`不配置`foldIcon`代表收起是展开的 180 度旋转 | `1.2.0`  |
| foldIcon            | [模板](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/template) \| [Schema](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode) |                           | 自定义展开图标。如果传入模板，则其将作为`Icon`组件的 icon 属性值；如果传入`Schema`，请传入代表一个 Icon 的 Schema。<br />注意: 只配置`foldIcon`不配置`unfoldIcon`代表配置无效，展示默认 icon | `1.2.0`  |
| showExpansionColumn | `boolean`                                                                                                                             | `true`                    | 是否显示默认的展开/收起操作列                                                                                                                                                                | `1.14.0` |
| spinnerSize | `"lg" ｜ "sm" ｜ ""` | "" |   设置Table依赖的Spinner的尺寸 | `1.20.0` | 
| sortMultiple | `boolean` | `true` |   支持单、多列排序 | `1.21.0` | 
| updateAllRows       | `boolean`       | false | true，则全量更新行数据；false，则走TableRow优化流程，只更新数据变更行        |   `1.46.0`    |

#### 列配置属性表

| 属性名         | 类型                                                             | 默认值 | 说明                                         | 版本    |
| -------------- | ---------------------------------------------------------------- | ------ | -------------------------------------------- | ------- |
| label          | [模板](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/template) |        | 表头文本内容                                 |         |
| name           | `string`                                                         |        | 通过名称关联数据                             |         |
| width          | `number` \| `string`                                             |        | 列宽                                         |         |
| remark         |                                                                  |        | 提示信息                                     |         |
| fixed          | `left` \| `right` \| `none`                                      |        | 是否固定当前列                               |         |
| popOver        |                                                                  |        | 弹出框                                       |         |
| copyable       | `boolean` 或 `{icon: string, content:string}`                    |        | 是否可复制                                   |         |
| style          | `object`                                                         |        | 单元格自定义样式                             |         |
| innerStyle     | `object`                                                         |        | 单元格内部组件自定义样式                     |         |
| searchable     | schema, 比如 `{type: "input-text", name: "id", label: "主键"}`   |        | 当列支持搜索, 新版本已被`headSearchable`替代 |         |
| headSearchable | schema, 比如 `{type: "input-text", name: "id", label: "主键"}`   |        | 当列支持搜索且回显                           | `1.5.0` |
| sortable       | `boolean`                                                        | false  | 是否可排序                                   |         |
| customRowSpan       | `string\|number`       | "" | 自定义当前列单元格行合并规则，支持表达式，number支持1-1000        |   `1.34.0`      |
| customColSpan       | `string\|number`       | "" | 自定义当前列单元列格合并规则，支持表达式，number支持1-1000        |   `1.34.0`    |
| toggable           | `boolean`                                                         |    true    | 设置该列是否可以进行显示隐藏操作                             |         |

### 事件表

当前组件会对外派发以下事件，可以通过`onEvent`来监听这些事件，并通过`actions`来配置执行的动作，详细查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action)。

| 事件名称       | 事件参数                                                                                                           | 说明                                                        |
| -------------- | ------------------------------------------------------------------------------------------------------------------ | ----------------------------------------------------------- |
| selectedChange | `selectedItems: item[]` 已选择行<br/>`unSelectedItems: item[]` 未选择行                                            | 手动选择表格项时触发                                        |
| columnSort     | `orderBy: string` 列排序列名<br/>`orderDir: string` 列排序值<br/>`sortList: {orderBy: string; orderDir: string}[]` | 点击列排序时触发， `orderBy、orderDir`支持`1.2.0`及以上版本 |
| columnFilter   | `filterName: string` 列筛选列名<br/>`filterValue: string` 列筛选值                                                 | 点击列筛选时触发                                            |
| columnSearch   | `searchName: string` 列搜索列名<br/>`searchValue: string` 列搜索数据<br />`searchFormData: object` 所有列筛选数据  | 点击列搜索时触发， `searchFormData`支持`1.2.0`及以上版本    |
| orderChange    | `movedItems: item[]` 已排序数据                                                                                    | 手动拖拽行排序时触发                                        |
| columnToggled  | `columns: item[]` 当前显示的列配置数据                                                                             | 点击自定义列时触发                                          |
| rowClick       | `rowItem: object` 行点击数据<br/>`item: object` 行点击数据<br/>`index: number` 行索引(`1.71.0`版本支持`item`、`index`)    | 点击整行时触发         |
| rowDbClick     | `item: object` 行点击数据<br/>`index: number` 行索引  | 双击整行时触发，`1.71.0`版本支持。       |

### rowDbClick

双击整行时触发。

```schema: scope="body"
{
    "type": "service",
    "api": "/api/mock2/sample?perPage=10",
    "body": [
        {
            "type": "table",
            "source": "$rows",
            "onEvent": {
                "rowDbClick": {
                    "actions": [
                        {
                            "actionType": "toast",
                            "args": {
                                "msgType": "info",
                                "msg": "行双击数据：${event.data.item|json}；行索引：${event.data.index}"
                            }
                        }
                    ]
                }
            },
            "columns": [
                {
                    "name": "id",
                    "label": "ID",
                    "searchable": true
                },
                {
                    "name": "engine",
                    "label": "Rendering engine",
                    "filterable": {
                        "options": [
                            "Internet Explorer 4.0",
                            "Internet Explorer 5.0"
                        ]
                    }
                },
                {
                    "name": "browser",
                    "label": "Browser",
                    "sortable": true
                },
                {
                    "name": "platform",
                    "label": "Platform(s)"
                },
                {
                    "name": "version",
                    "label": "Engine version"
                },
                {
                    "name": "grade",
                    "label": "CSS grade"
                }
            ]
        }
    ]
}
```

#### 列配置事件表

当前组件会对外派发以下事件，可以通过`onEvent`来监听这些事件，并通过`actions`来配置执行的动作，详细查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action)。

| 事件名称   | 事件参数                                                                       | 说明                                           |
| ---------- | ------------------------------------------------------------------------------ | ---------------------------------------------- |
| click      | `nativeEvent: MouseEvent` 鼠标事件对象<br/>`[columnName]: string` 对应列名的值 | 监听表格列点击事件，表格数据点击时触发         |
| mouseenter | `nativeEvent: MouseEvent` 鼠标事件对象<br/>`[columnName]: string` 对应列名的值 | 监听表格列鼠标移入事件，表格数据鼠标移入时触发 |
| mouseleave | `nativeEvent: MouseEvent` 鼠标事件对象<br/>`[columnName]: string` 对应列名的值 | 监听表格列鼠标移出事件，表格数据鼠标移出时触发 |

### 动作表

当前组件对外暴露以下特性动作，其他组件可以通过指定`actionType: 动作名称`、`componentId: 该组件id`来触发这些动作，动作配置可以通过`args: {动作配置项名称: xxx}`来配置具体的参数，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action#触发其他组件的动作)。

| 动作名称  | 动作配置                                                                                                         | 说明                 |
| --------- | ---------------------------------------------------------------------------------------------------------------- | -------------------- |
| select    | `selected: string` 条件表达式，表达式中可以访问变量`record:行数据`和`rowIndex:行索引`，例如: data.rowIndex === 1 | 设置表格的选中项     |
| selectAll | -                                                                                                                | 设置表格全部项选中   |
| clearAll  | -                                                                                                                | 清空表格所有选中项   |
| initDrag  | -                                                                                                               | 开启表格拖拽排序功能 |
| toggleExpanded  | `args.index` 可选，指定行数，支持表达式，支持树形路径（当为树形表格的时候使用） <br /> `args.condition` 可选，通过表达式指定更新哪些行，支持条件组合       | 切换某行数据是展开还是收起。`1.63.0`版本支持 |

#### toggleExpanded
通过`_subTableId`变量可以获取subTable的id，从而触发`table`的`toggleExpanded`动作。

- `args.index` 可选，指定行数，支持表达式，支持树形路径（当为树形表格的时候使用）
- `args.condition` 可选，通过表达式指定更新哪些行，支持条件组合

```schema
{
  "type": "page",
  "body": {
    "type": "service",
    "debug": true,
    "data": {
      "rows": [
        {
          "engine": "Trident",
          "browser": "Internet Explorer 4.0",
          "platform": "Win 95+",
          "version": "4",
          "grade": "X",
          "id": 1,
          "children": [
            {
              "engine": "Trident",
              "browser": "Internet Explorer 4.0",
              "platform": "Win 95+",
              "version": "4",
              "grade": "X",
              "id": 11,
              "children": [
                {
                  "engine": "Trident",
                  "browser": "Internet Explorer 4.0",
                  "platform": "Win 95+",
                  "version": "4",
                  "grade": "X",
                  "id": 111,
                },
                {
                  "engine": "Trident",
                  "browser": "Internet Explorer 4.0",
                  "platform": "Win 95+",
                  "version": "4",
                  "grade": "X",
                  "id": 112,
                }
              ]
            },
            {
              "engine": "Trident",
              "browser": "Internet Explorer 4.0",
              "platform": "Win 95+",
              "version": "4",
              "grade": "X",
              "id": 12,
              "children": [
                {
                  "engine": "Trident",
                  "browser": "Internet Explorer 4.0",
                  "platform": "Win 95+",
                  "version": "4",
                  "grade": "X",
                  "id": 121,
                },
                {
                  "engine": "Trident",
                  "browser": "Internet Explorer 4.0",
                  "platform": "Win 95+",
                  "version": "4",
                  "grade": "X",
                  "id": 122,
                }
              ]
            }
          ]
        },
        {
          "engine": "Trident",
          "browser": "Internet Explorer 5.0",
          "platform": "Win 95+",
          "version": "5",
          "grade": "C",
          "id": 2,
          "children": [
            {
              "engine": "Trident",
              "browser": "Internet Explorer 4.0",
              "platform": "Win 95+",
              "version": "4",
              "grade": "X",
              "id": 21,
              "children": [
                {
                  "engine": "Trident",
                  "browser": "Internet Explorer 4.0",
                  "platform": "Win 95+",
                  "version": "4",
                  "grade": "X",
                  "id": 211,
                },
                {
                  "engine": "Trident",
                  "browser": "Internet Explorer 4.0",
                  "platform": "Win 95+",
                  "version": "4",
                  "grade": "X",
                  "id": 212,
                }
              ]
            },
            {
              "engine": "Trident",
              "browser": "Internet Explorer 4.0",
              "platform": "Win 95+",
              "version": "4",
              "grade": "X",
              "id": 22,
              "children": [
                {
                  "engine": "Trident",
                  "browser": "Internet Explorer 4.0",
                  "platform": "Win 95+",
                  "version": "4",
                  "grade": "X",
                  "id": 221,
                },
                {
                  "engine": "Trident",
                  "browser": "Internet Explorer 4.0",
                  "platform": "Win 95+",
                  "version": "4",
                  "grade": "X",
                  "id": 222,
                }
              ]
            }
          ]
        },
        {
          "engine": "Trident",
          "browser": "Internet Explorer 5.5",
          "platform": "Win 95+",
          "version": "5.5",
          "grade": "A",
          "id": 3
        }
      ]
    },
    "body": [
      {
        "type": "table",
        "id": "tableId",
        "source": "$rows",
        "columnsTogglable": false,
        "columns": [
          {
            "name": "id",
            "label": "ID"
          },
          {
            "name": "engine",
            "label": "Engine"
          },
          {
            "name": "grade",
            "label": "Grade"
          },
          {
            "name": "version",
            "label": "Version"
          },
          {
            "type": "operation",
            "label": "操作",
            "width": 80,
            "buttons": [
              {
                "type": "button",
                "label": "${_amisExpanded ? '收起' : '展开'}",
                "level": "link",
                "onEvent": {
                  "click": {
                    "actions": [
                      {
                        "actionType": "toggleExpanded",
                        "componentId": "tableId",
                        "args": {
                          "condition": "${id === currentId}",
                          "currentId": "${id}"
                        }
                      }
                    ]
                  }
                }
              }
            ]
          }
        ],
        "subTable": {
          "type": "table",
          "columns": [
            {
              "name": "id",
              "label": "ID"
            },
            {
              "name": "grade",
              "label": "Grade"
            },
            {
              "type": "operation",
              "label": "操作",
              "width": 80,
              "buttons": [
                {
                  "type": "button",
                  "label": "${_amisExpanded ? '收起' : '展开'}",
                  "level": "link",
                  "onEvent": {
                    "click": {
                      "actions": [
                        {
                          "actionType": "toggleExpanded",
                          "componentId": "${_subTableId}",
                          "args": {
                            "condition": "${id === currentId}",
                            "currentId": "${id}"
                          }
                        }
                      ]
                    }
                  }
                }
              ]
            }
          ],
          "source": "$children",
          "subTable": {
            "type": "table",
            "columns": [
              {
                "name": "id",
                "label": "ID"
              },
              {
                "name": "grade",
                "label": "Grade"
              },
              {
                "type": "operation",
                "label": "操作",
                "width": 80,
                "buttons": [
                  {
                    "type": "button",
                    "label": "${_amisExpanded ? '收起' : '展开'}",
                    "level": "link",
                    "onEvent": {
                      "click": {
                        "actions": [
                          {
                            "actionType": "toggleExpanded",
                            "componentId": "${_subTableId}",
                            "args": {
                              "condition": "${id === currentId}",
                              "currentId": "${id}"
                            }
                          }
                        ]
                      }
                    }
                  }
                ]
              }
            ],
            "source": "$children",
            "subTable": {
              "label": "弹个框",
              "type": "button",
              "actionType": "dialog",
              "dialog": {
                "title": "弹框",
                "body": "这是个简单的弹框。"
              }
            }
          }
        }
      }
    ]
  }
}
```
