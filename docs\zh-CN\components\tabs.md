---
title: Tabs 选项卡
description:
type: 0
group: ⚙ 组件
menuName: Tabs
icon:
order: 68
standardMode: true
---

选项卡容器组件。

## 模块划分

![Tabs选项卡容器组件](https://static02.sit.yxmarketing01.com/materialcenter/97a978ef-f5db-412f-84e2-052c4a7fdc4e.png)

1. 标题区域（必有）  
   Tab 标题，最多允许 10 个中文字符，也可搭配 icon 使用。

2. 内容区域（必有）  
   内容区域暂无特殊限制，可嵌套任意组件。  
   **注意**：若再嵌套 Tabs 选项卡组件，建议最多可嵌套 3 层，嵌套过多不利于视觉上的客观性和用户操作，对于 2 层嵌套和 3 层嵌套在展示形式上存在差异，可参考场景推荐中[二级 Tabs](/dataseeddesigndocui/#/amis/zh-CN/components/tabs) 和 [三级 Tabs](/dataseeddesigndocui/#/amis/zh-CN/components/tabs)。

3. 单个 Tabs 全局保存类操作（非必有）  
   一般场景下，推荐走页面级别的保存布局，但是若对于单个 Tabs 有单独保存的诉求，可推荐使用该场景。  
   当编辑的内容不能占满屏幕高度时，保存类按钮右下角追随；若编辑的内容过多，保存类按钮右下角吸底。

## 场景推荐

### Tabs+Crud

```schema
{
  "type": "page",
  "body": {
    "type": "wrapper",
    "bgColor": "white",
    "body": {
      "type": "flex",
      "direction": "column",
      "gap": true,
      "items": [
        {
          "type": "button-toolbar",
          "label": false,
          "buttons": [
            {
              "type": "button",
              "label": "主按钮",
              "actionType": "url",
              "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
              "level": "primary",
              "blank": false,
            },
            {
              "type": "button",
              "label": "次按钮1",
              "actionType": "url",
              "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
            },
            {
              "type": "button",
              "label": "次按钮2",
              "actionType": "url",
              "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
            },
            {
              "type": "button",
              "label": "次按钮3",
              "actionType": "url",
              "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
            },
            {
              "type": "dropdown-button",
              "label": "更多",
              "buttons": [
                {
                  "type": "button",
                  "label": "次按钮4",
                  "disabled": true
                },
                {
                  "type": "button",
                  "label": "次按钮5"
                },
                {
                  "type": "button",
                  "label": "次按钮6"
                }
              ]
            }
          ]
        },
        {
          "type": "tabs",
          "tabs": [
            {
              "title": "带全局操作按钮的列表",
              "tab": {
                "type": "crud",
                "syncLocation": false,
                "columnsTogglable": false,
                "footerToolbar": [
                  {
                    "type": "pagination",
                    "layout": "total,pager,perPage,go"
                  }
                ],
                "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4",
                "filter": {
                  "title": "",
                  "body": [
                    {
                      "type": "group",
                      "mode": "horizontal",
                      "body": [
                        {
                          "type": "input-text",
                          "name": "keywords",
                          "label": "关键字",
                          "clearable": true,
                          "placeholder": "通过关键字搜索",
                          "columnRatio": 4
                        },
                        {
                          "type": "input-text",
                          "name": "engine",
                          "label": "Engine",
                          "clearable": true,
                          "columnRatio": 4
                        },
                        {
                          "type": "input-text",
                          "name": "platform",
                          "label": "Platform",
                          "clearable": true,
                          "columnRatio": 4
                        },
                        {
                          "type": "input-text",
                          "name": "keywords1",
                          "label": "关键字1",
                          "clearable": true,
                          "placeholder": "通过关键字搜索",
                          "columnRatio": 4
                        },
                        {
                          "type": "input-text",
                          "name": "engine1",
                          "label": "Engine1",
                          "clearable": true,
                          "columnRatio": 4
                        },
                        {
                          "type": "input-text",
                          "name": "platform1",
                          "label": "Platform1",
                          "clearable": true,
                          "columnRatio": 4
                        }
                      ]
                    }
                  ],
                  "actions": [
                    {
                      "type": "reset",
                      "label": "重 置"
                    },
                    {
                      "type": "submit",
                      "level": "primary",
                      "label": "查 询"
                    }
                  ]
                },
                "topToolbar": {
                  "type": "button",
                  "label": "新增",
                  "level": "primary"
                },
                "columns": [
                  {
                    "name": "id",
                    "label": "ID"
                  },
                  {
                    "name": "engine",
                    "label": "Rendering engine"
                  },
                  {
                    "name": "browser",
                    "label": "Browser"
                  },
                  {
                    "name": "platform",
                    "label": "Platform(s)"
                  },
                  {
                    "name": "version",
                    "label": "Engine version"
                  },
                  {
                    "name": "grade",
                    "label": "CSS grade"
                  },
                  {
                    "type": "operation",
                    "label": "操作",
                    "buttons": [
                      {
                        "label": "详情",
                        "type": "button",
                        "level": "link",
                        "actionType": "dialog",
                        "dialog": {
                          "title": "详情",
                          "showCloseButton": false,
                          "body": "这是个简单的弹框。"
                        }
                      },
                      {
                        "label": "删除",
                        "type": "button",
                        "actionType": "ajax",
                        "level": "link",
                        "disabled": true,
                        "confirmText": "确认要删除吗？",
                        "api": {
                          "method": "delete",
                          "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
                        }
                      },
                      {
                        "label": "编辑",
                        "type": "button",
                        "level": "link",
                        "actionType": "dialog",
                        "dialog": {
                          "title": "编辑",
                          "showCloseButton": false,
                          "body": "这是个简单的弹框。"
                        }
                      },
                      {
                        "label": "空跑",
                        "type": "button",
                        "level": "link",
                        "actionType": "dialog",
                        "dialog": {
                          "title": "空跑",
                          "showCloseButton": false,
                          "body": "这是个简单的弹框。"
                        }
                      }
                    ]
                  }
                ]
              }
            },
            {
              "title": "带Alert提示的列表",
              "tab": [
                {
                  "type": "alert",
                  "title": "提示类标题",
                  "body": "提示类文案",
                  "level": "info"
                },
                {
                  "type": "crud",
                  "syncLocation": false,
                  "columnsTogglable": false,
                  "footerToolbar": [
                    {
                      "type": "tpl",
                      "tpl": "共${count}条"
                    },
                    {
                      "type": "pagination",
                      "layout": "total,pager,perPage,go"
                    }
                  ],
                  "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4",
                  "filter": {
                    "title": "",
                    "body": [
                      {
                        "type": "group",
                        "mode": "horizontal",
                        "body": [
                          {
                            "type": "input-text",
                            "name": "keywords",
                            "label": "关键字",
                            "clearable": true,
                            "placeholder": "通过关键字搜索",
                            "columnRatio": 4
                          },
                          {
                            "type": "input-text",
                            "name": "engine",
                            "label": "Engine",
                            "clearable": true,
                            "columnRatio": 4
                          },
                          {
                            "type": "input-text",
                            "name": "platform",
                            "label": "Platform",
                            "clearable": true,
                            "columnRatio": 4
                          },
                          {
                            "type": "input-text",
                            "name": "keywords1",
                            "label": "关键字1",
                            "clearable": true,
                            "placeholder": "通过关键字搜索",
                            "columnRatio": 4
                          },
                          {
                            "type": "input-text",
                            "name": "engine1",
                            "label": "Engine1",
                            "clearable": true,
                            "columnRatio": 4
                          },
                          {
                            "type": "input-text",
                            "name": "platform1",
                            "label": "Platform1",
                            "clearable": true,
                            "columnRatio": 4
                          }
                        ]
                      }
                    ],
                    "actions": [
                      {
                        "type": "reset",
                        "label": "重 置"
                      },
                      {
                        "type": "submit",
                        "level": "primary",
                        "label": "查 询"
                      }
                    ]
                  },
                  "topToolbar": {
                    "type": "button",
                    "label": "新增",
                    "level": "primary"
                  },
                  "columns": [
                    {
                      "name": "id",
                      "label": "ID"
                    },
                    {
                      "name": "engine",
                      "label": "Rendering engine"
                    },
                    {
                      "name": "browser",
                      "label": "Browser"
                    },
                    {
                      "name": "platform",
                      "label": "Platform(s)"
                    },
                    {
                      "name": "version",
                      "label": "Engine version"
                    },
                    {
                      "name": "grade",
                      "label": "CSS grade"
                    },
                    {
                      "type": "operation",
                      "label": "操作",
                      "buttons": [
                        {
                          "label": "详情",
                          "type": "button",
                          "level": "link",
                          "actionType": "dialog",
                          "dialog": {
                            "title": "详情",
                            "showCloseButton": false,
                            "body": "这是个简单的弹框。"
                          }
                        },
                        {
                          "label": "删除",
                          "type": "button",
                          "actionType": "ajax",
                          "level": "link",
                          "disabled": true,
                          "confirmText": "确认要删除吗？",
                          "api": {
                            "method": "delete",
                            "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
                          }
                        },
                        {
                          "label": "编辑",
                          "type": "button",
                          "level": "link",
                          "actionType": "dialog",
                          "dialog": {
                            "title": "编辑",
                            "showCloseButton": false,
                            "body": "这是个简单的弹框。"
                          }
                        },
                        {
                          "label": "空跑",
                          "type": "button",
                          "level": "link",
                          "actionType": "dialog",
                          "dialog": {
                            "title": "空跑",
                            "showCloseButton": false,
                            "body": "这是个简单的弹框。"
                          }
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  }
}
```

- 落地案例  
  [信息化一站式-权限管理-组织管理](http://moka.dmz.sit.caijj.net/idaasui/#/outer/orgManagement?_shMenuId=tenant_menu_P0174_informatization_org_manager_m2vhpgs6)  
   ![信息化一站式-权限管理-组织管理](https://static02.sit.yxmarketing01.com/tdmaterial/cddfdfde374f4cf9bbe6bdd7247e0d58.png)


### 二级 Tabs

```schema
{
  "type": "page",
  "body": [
    {
      "type": "tabs",
      "tabs": [
        {
          "title": "一级Tab",
          "tab": [
            {
              "type": "tabs",
              "tabs": [
                {
                  "title": "二级Tab",
                  "tab": [
                    {
                      "type": "crud",
                      "api": "/api/mock2/crud/table4",
                      "columns": [
                        {
                          "name": "id",
                          "label": "ID"
                        },
                        {
                          "name": "engine",
                          "label": "Rendering engine"
                        },
                        {
                          "name": "browser",
                          "label": "Browser"
                        },
                        {
                          "name": "platform",
                          "label": "Platform(s)"
                        },
                        {
                          "name": "version",
                          "label": "Engine version"
                        },
                        {
                          "name": "grade",
                          "label": "CSS grade"
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

- 落地案例  
  [智能平台-插件-详情-创建工具-编辑](http://moka.dmz.sit.caijj.net/aiassistantui/#/personalPage/toolsInfo?toolkit_id=47&tool_base_id=79)  
   ![智能平台-插件-详情-创建工具-编辑](https://static02.sit.yxmarketing01.com/tdmaterial/0254a502514a463584f6aa377ff3bd0b.png)

### 三级 Tabs

```schema
{
  "type": "page",
  "data": {
    "text1": "营销中心",
    "text2": "张三",
    "text3": "李四",
    "text4": "0",
    "text5": "文本"
  },
  "body":{
    "type": "tabs",
    "tabs": [
      {
        "title": "一级Tab",
        "body": [
          {
            "type": "tabs",
            "tabsMode": "strong",
            "tabs": [
              {
                "title": "二级Tab",
                "tab": [
                  {
                    "type": "form",
                    "static": true,
                    "labelWidth": 70,
                    "body": [
                      {
                        "type": "group",
                        "body": [
                          {
                            "type": "static",
                            "name": "text1",
                            "label": "归属部门",
                            "columnRatio": 4
                          },
                          {
                            "type": "static",
                            "name": "text2",
                            "label": "负责人",
                            "columnRatio": 4
                          },
                          {
                            "type": "static",
                            "name": "text3",
                            "label": "创建人",
                            "columnRatio": 4
                          }
                        ]
                      },
                      {
                        "type": "group",
                        "body": [
                          {
                            "type": "static-mapping",
                            "name": "text4",
                            "label": "文本1",
                            "columnRatio": 4,
                            "map": {
                              "0": "<span class='label label-info'>这是一个映射</span>",
                              "1": "<span class='label label-success'>一</span>",
                              "2": "<span class='label label-info'>二</span>",
                              "3": "<span class='label label-warning'>四</span>",
                              "4": "<span class='label label-primary'>五</span>",
                              "*": "<span class='label label-default'>-</span>"
                            }
                          },
                          {
                            "type": "static",
                            "name": "text5",
                            "label": "文本2",
                            "columnRatio": 4
                          }
                        ]
                      }
                    ]
                  },
                  {
                    "type": "tabs",
                    "tabsMode": "strong",
                    "tabs": [
                      {
                        "title": "三级Tab",
                        "tab": [
                          {
                            "type": "crud",
                            "api": "/api/mock2/crud/table4",
                            "columns": [
                              {
                                "name": "id",
                                "label": "ID"
                              },
                              {
                                "name": "engine",
                                "label": "Rendering engine"
                              },
                              {
                                "name": "browser",
                                "label": "Browser"
                              },
                              {
                                "name": "platform",
                                "label": "Platform(s)"
                              },
                              {
                                "name": "version",
                                "label": "Engine version"
                              },
                              {
                                "name": "grade",
                                "label": "CSS grade"
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
}
```

- 落地案例

## 组件用法

### 基本用法

```schema: scope="body"
{
    "type": "tabs",
    "tabs": [
        {
            "title": "Tab 1",
            "tab": "Content 1"
        },
        {
            "title": "Tab 2",
            "tab": "Content 2"
        }
    ]
}
```

默认想要显示多少选项卡配置多少个 `tabs` 成员即可。但是有时候你可能会想根据某个数据来动态生成。这个时候需要额外配置 `source` 属性如。

```schema
{
    "type": "page",
    "data": {
        "arr": [
            {
                "a": "收入",
                "b": 199
            },

            {
                "a": "支出",
                "b": 299
            }
        ]
    },

    "body": [
        {
            "type": "tabs",
            "source": "${arr}",
            "tabs": [
                {
                    "title": "${a}",
                    "body": {
                        "type": "tpl",
                        "tpl": "金额：${b|number}元"
                    }
                }
            ]
        }
    ]
}
```

### 加强模式 

常出现在三级Tabs嵌套场景下，配置 tabsMode 实现。

```schema: scope="body"
{
    "type": "tabs",
    "tabsMode": "strong",
    "tabs": [
        {
            "title": "选项卡1",
            "body": "选项卡内容1"
        },
        {
            "title": "选项卡2",
            "body": "选项卡内容2"
        },
        {
            "title": "选项卡3",
            "body": "选项卡内容3"
        }
    ]
}
```

### 拖拽排序

```schema: scope="body"
{
    "type": "tabs",
    "draggable": true,
    "tabs": [
        {
            "title": "Tab 1",
            "tab": "Content 1"
        },
        {
            "title": "Tab 2",
            "tab": "Content 2"
        },
        {
            "title": "Tab 3",
            "tab": "Content 3"
        }
    ]
}
```

### 可增加、删除

`tab` 设置的 `closable` 优先级高于整体。

```schema: scope="body"
{
  "type": "tabs",
  "tabsMode": "strong",
  "closable": true,
  "addable": true,
  "tabs": [
    {
      "title": "Tab 1",
      "tab": "Content 1",
      "closable": false
    },
    {
      "title": "Tab 2",
      "tab": "Content 2"
    }
  ]
}
```

#### 配置新增 tab 时的默认值

> 配置 TabsSchema 时，defaultTabForAdd.tab 比 defaultTabForAdd.body 优先级高, 根据最新版本建议使用 body，不再建议配置 tab。

> defaultTabForAdd.title 自动追加的索引是从 3 开始的且是独立累加的。

```schema: scope="body"
{
  "type":"tabs",
  "tabsMode": "strong",
  "addable":true,
  "addBtnText":"新建",
  "closable":true,
  "defaultTabForAdd":{
    "title":"Tab-default",
    "body":"default - tab"
  },
  "tabs":[
    {
      "title":"Tab - 0",
      "tab":"line - 0"
    },
    {
      "title":"Tab - 1",
      "tab":"line - 1"
    }
  ]
}
```

### 可禁用

```schema: scope="body"
{
    "type": "tabs",
    "tabs": [
        {
            "title": "Tab 1",
            "tab": "Content 1"
        },
        {
            "title": "Tab 2",
            "tab": "Content 2",
            "disabled": true
        }
    ]
}
```

### 作为表单项的值

如果在表单里给 tabs 配置了 name，它可以作为一个表单提交项的值，默认情况下会取 title

```schema: scope="body"
{
    "type": "form",
    "debug": true,
    "body": [
        {
            "type": "tabs",
            "name": "tab",
            "tabs": [
                {
                    "title": "Tab 1",
                    "tab": "Content 1"
                },
                {
                    "title": "Tab 2",
                    "tab": "Content 2"
                }
            ]
        }
    ]
}
```

如果不想使用 title，可以给每个 tab 设置 value，这样就会取这个 value 作为表单项的值

```schema: scope="body"
{
    "type": "form",
    "debug": true,
    "body": [
        {
            "type": "tabs",
            "name": "tab",
            "tabs": [
                {
                    "title": "Tab 1",
                    "tab": "Content 1",
                    "value": 0
                },
                {
                    "title": "Tab 2",
                    "tab": "Content 2",
                    "value": 1
                }
            ]
        }
    ]
}
```

### 配置 hash

可以在单个tab下，配置hash属性，支持地址栏携带 hash 参数。

```schema: scope="body"
{
  "type": "tabs",
  "tabs": [
    {
      "title": "Tab 1",
      "hash": "tab1",
      "tab": "Content 1"
    },
    {
      "title": "Tab 2",
      "hash": "tab2",
      "tab": "Content 2"
    }
  ]
}
```

### 默认显示某个选项卡

主要配置activeKey属性来实现该效果，共有下面两种方法：

#### 配置 hash 值

支持变量，如 "tab${id}"

```schema: scope="body"
{
  "type": "tabs",
  "activeKey": "tab2",
  "tabs": [
    {
      "title": "Tab 1",
      "hash": "tab1",
      "tab": "Content 1"
    },
    {
      "title": "Tab 2",
      "hash": "tab2",
      "tab": "Content 2"
    }
  ]
}
```

#### 配置索引值

配置需要展示的`tab`索引值，`0`代表第一个。支持变量，如`"${id}"`

```schema: scope="body"
{
    "type": "tabs",
    "activeKey": "${1}",
    "tabs": [
        {
            "title": "Tab 1",
            "tab": "Content 1"
        },

        {
            "title": "Tab 2",
            "tab": "Content 2"
        }
    ]
}
```

#### 动态设置选项卡

```schema
{
  "type": "page",
  "data": {
    "key": 2
  },
  "body": [
    {
      "type": "form",
      "body": [
        {
          "type": "radios",
          "name": "key",
          "mode": "inline",
          "label": "激活的选项卡",
          "options": [
            {
              "label": "Tab 1",
              "value": 0
            },
            {
              "label": "Tab 2",
              "value": 1
            },
            {
              "label": "Tab 3",
              "value": 2
            }
          ]
        },
        {
          "type": "tabs",
          "activeKey": "${key|toInt}",
          "tabs": [
            {
              "title": "Tab 1",
              "tab": "Content 1"
            },
            {
              "title": "Tab 2",
              "tab": "Content 2"
            },
            {
              "title": "Tab 3",
              "tab": "Content 3"
            }
          ]
        }
      ]
    }
  ]
}
```

### 图标

通过 icon 可以设置 tab 的图标，具体参照[icon](/dataseeddesigndocui/#/amis/zh-CN/components/icon)。

```schema: scope="body"
{
  "type": "tabs",
  "tabs": [
    {
      "title": "Tab 1",
      "tab": "Content 1",
      "icon": "pencil"
    },
    {
      "title": "Tab 2",
      "tab": "Content 2",
      "icon": "file"
    }
  ]
}
```

### title 自定义

> 1.48.0 及以上版本
> 通过配置 tabs 数组中 title 为 schema，就能自定义 title 的显示。

```schema: scope="body"
{
    "type": "tabs",
    "tabs": [
        {
            "title": {
                "type": "container",
                "body": [
                    {
                        "type": "tpl",
                        "tpl": "容器内容区"
                    },
                    {
                      "type": "remark",
                      "content": "这是一段提醒"
                    }
                ]
            },
            "tab": "Content 1"
        },
        {
            "title": "Tab 2",
            "tab": "Content 2"
        }
    ]
}
```

### 配置超出折叠

通过配置 `collapseOnExceed` 可以用来实现超出折叠，额外还能通过 `collapseBtnLabel` 配置折叠按钮的文字

```schema: scope="body"
{
  "type": "tabs",
  "tabs": [
    {
      "title": "Tab 1",
      "tab": "Content 1",
    },
    {
      "title": "Tab 2",
      "tab": "Content 2"
    },
    {
      "title": "Tab 3",
      "tab": "Content 3",
    },
    {
      "title": "Tab 4",
      "tab": "Content 4"
    },
    {
      "title": "Tab 5",
      "tab": "Content 5"
    }
  ],
  "collapseOnExceed": 3
}
```

### mountOnEnter

只有在点击卡片的时候才会渲染，在内容较多的时候可以提升性能，但第一次点击的时候会有卡顿。

### unmountOnExit

如果你想在切换 tab 时，自动销毁掉隐藏的 tab，请配置`"unmountOnExit": true`。


### 配置 tab 的事件触发

> onEvent 事件监听时 数据域中的数据是有限的，目前仅支持 `${value}`, `${[name]}`, `${tab}`

> [name] 是特殊的占位，在实际使用时,参考下方 TabsSchema.name 字段的配置 应该换成 `${tabs-line-1}`

> 要触发 Tabs 的动作表，（ButtonSchema.onEvent）actions 里面配置的 componentId 必须 与 TabsSchema.id 一致且全局唯一才行（若不小心配置全局存在多个，那么只有最先加载的组件才能接收到）。

```schema
{
  "type": "page",
  "body": {
    "type": "panel",
    "body": {
      "type": "flex",
      "direction": "column",
      "gap": true,
      "items": [
        {
          "label": "激活第二个tab",
          "type": "button",
          "name": "tabs-btn-0",
          "onEvent": {
            "click": {
              "actions": [
                {
                  "actionType": "changeActiveKey",
                  "componentId": "tabs-line-1",
                  "args": {
                    "activeKey": 2
                  }
                },
                {
                  "actionType": "toast",
                  "args": {
                    "msgType": "info",
                    "msg": "派发鼠标点击事件"
                  }
                }
              ]
            }
          }
        },
        {
          "type": "tabs",
          "tabsMode": "strong",
          "id": "tabs-line-1",
          "name": "tabs-line-1",
          "addable": true,
          "closable": true,
          "onEvent": {
            "change": {
              "actions": [
                {
                  "actionType": "toast",
                  "args": {
                    "msgType": "info",
                    "msg": "派发change事件 ${tabs-line-1}|${value}|${tab.title}"
                  }
                }
              ]
            },
            "add": {
              "actions": [
                {
                  "actionType": "toast",
                  "args": {
                    "msgType": "info",
                    "msg": "派发add事件${tabs-line-1}|${value}|${tab.title}"
                  }
                }
              ]
            },
            "close": {
              "actions": [
                {
                  "actionType": "toast",
                  "args": {
                    "msgType": "info",
                    "msg": "派发close事件 ${tabs-line-1}|${value}|${tab.title}"
                  }
                }
              ]
            }
          },
          "tabs": [
            {
              "title": "Tab - 0",
              "tab": "line - 0"
            },
            {
              "title": "Tab - 1",
              "tab": "line - 1"
            }
          ]
        }
      ]
    }
  }
}
```


### 属性表

| 属性名                | 类型                                                                                 | 默认值                              | 说明                                                                                                                                 |
| --------------------- | ------------------------------------------------------------------------------------ | ----------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------ |
| type                  | `string`                                                                             | `"tabs"`                            | 指定为 Tabs 渲染器                                                                                                                   |
| activeKey             | `string` / `number`                                                                  |                                     | 激活的选项卡，hash 值或索引值，支持使用表达式，可响应上下文数据变化                                                                  |
| className             | `string`                                                                             |                                     | 外层 Dom 的类名                                                                                                                      |
| tabsMode              | `string`                                                                             |                                     | 展示模式，取值可以是 `line`、`strong`                                                                                                |
| tabsClassName         | `string`                                                                             |                                     | Tabs Dom 的类名                                                                                                                      |
| tabs                  | `Array`                                                                              |                                     | tabs 内容                                                                                                                            |
| source                | `string`                                                                             |                                     | tabs 关联数据，关联后可以重复生成选项卡                                                                                              |
| tabs[x].title         | `string` \| [SchemaNode](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode)    |                                     | Tab 标题，当是 [SchemaNode](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode) 时，该 title 不支持 editable 为 true 的双击编辑 |
| tabs[x].icon          | `icon`                                                                               |                                     | Tab 的图标                                                                                                                           |
| tabs[x].iconPosition  | `left` / `right`                                                                     | `left`                              | Tab 的图标位置                                                                                                                       |
| tabs[x].tab           | [SchemaNode](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode)                |                                     | 内容区                                                                                                                               |
| tabs[x].reload        | `boolean`                                                                            |                                     | 设置以后内容每次都会重新渲染，对于 crud 的重新拉取很有用                                                                             |
| tabs[x].unmountOnExit | `boolean`                                                                            |                                     | 每次退出都会销毁当前 tab 栏内容                                                                                                      |
| tabs[x].className     | `string`                                                                             | `"bg-white b-l b-r b-b wrapper-md"` | Tab 区域样式                                                                                                                         |
| tabs[x].closable      | `boolean`                                                                            | false                               | 是否支持删除，优先级高于组件的 `closable`                                                                                            |
| tabs[x].disabled      | `boolean`                                                                            | false                               | 是否禁用                                                                                                                             |
| mountOnEnter          | `boolean`                                                                            | false                               | 只有在点中 tab 的时候才渲染                                                                                                          |
| unmountOnExit         | `boolean`                                                                            | false                               | 切换 tab 的时候销毁                                                                                                                  |
| addable               | `boolean`                                                                            | false                               | 是否支持新增                                                                                                                         |
| closable              | `boolean`                                                                            | false                               | 是否支持删除                                                                                                                         |
| draggable             | `boolean`                                                                            | false                               | 是否支持拖拽                                                                                                                         |
| collapseOnExceed      | `number`                                                                             |                                     | 当 tabs 超出多少个时开始折叠                                                                                                         |
| collapseBtnLabel      | `string`                                                                             | `more`                              | 用来设置折叠按钮的文字                                                                                                               |
| defaultTabForAdd      | [SchemaNode](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode) / `tpl string` |                                     | 用来设置新增标签头默认的配置                                                                                                         |

### 事件表

当前组件会对外派发以下事件，可以通过`onEvent`来监听这些事件，并通过`actions`来配置执行的动作，在`actions`中可以通过`${事件参数名}`来获取事件产生的数据，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action)。

> `[name]`表示当前组件绑定的名称，即`name`属性，如果没有配置`name`属性，则通过`value`取值。

| 事件名称 | 事件参数                              | 说明             |
| -------- | ------------------------------------- | ---------------- |
| change   | `[name]: number \| string` 选项卡索引 | 切换选项卡时触发 |
| add      | `[name]: number \| string` 选项卡索引 | 新增选项卡时触发 |
| close    | `[name]: number \| string` 选项卡索引 | 关闭选项卡时触发 |

### 动作表

当前组件对外暴露以下特性动作，其他组件可以通过指定`actionType: 动作名称`、`componentId: 该组件id`来触发这些动作，动作配置可以通过`args: {动作配置项名称: xxx}`来配置具体的参数，详细请查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action#触发其他组件的动作)。

| 动作名称        | 动作配置                                 | 说明                                                                                      |
| --------------- | ---------------------------------------- | ----------------------------------------------------------------------------------------- |
| changeActiveKey | `activeKey: number \| string` 选项卡索引 | 激活指定的选项卡                                                                          |
| add             | 无                                       | 监听动作新增选项卡，可设置默认配置，具体参考属性：defaultTabForAdd (支持版本：1.5.0 以上) |
| close           | `activeKey: number \| string` 选项卡索引 | 关闭指定的选项卡，设置 `${navIndex}`可关闭自己(支持版本：1.5.0 以上)                      |
