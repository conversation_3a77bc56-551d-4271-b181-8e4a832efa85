---
title: Tag 标签
description:
type: 0
group: ⚙ 组件
menuName: Tabs
icon:
---

用于标记和选择的标签

## 场景推荐
### 流程类型    

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "mode": "horizontal",
    "title": "",
    "actions": [],
    "labelWidth": 200,
    "body": [
      {
        "label": "流程中",
        "type": "static-flex",
        "justify": "start",
        "items": [
          {
            "type": "tag",
            "label": "审核中",
            "displayMode": "bordered",
            "color": "active"
          }
        ]
      },
      {
        "label": "流程失败/严重问题",
        "type": "static-flex",
        "justify": "start",
        "items": [
          {
            "type": "tag",
            "label": "审核拒绝",
            "displayMode": "bordered",
            "color": "error"
          }
        ]
      },
      {
        "label": "流程成功",
        "type": "static-flex",
        "justify": "start",
        "items": [
          {
            "type": "tag",
            "label": "审核通过",
            "displayMode": "bordered",
            "color": "success"
          }
        ]
      },
      {
        "label": "流程未开始/已终止",
        "type": "static-flex",
        "justify": "start",
        "items": [
          {
            "type": "tag",
            "label": "未审核",
            "displayMode": "bordered",
            "color": "inactive"
          }
        ]
      }
    ]
  }
}
```

### 生命周期类型

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "mode": "horizontal",
    "title": "",
    "actions": [],
    "labelWidth": 200,
    "body": [
      {
        "label": "草稿/初始阶段",
        "type": "static-flex",
        "justify": "start",
        "items": [
          {
            "type": "tag",
            "label": "初始化",
            "displayMode": "bordered",
            "color": "active"
          }
        ]
      },
      {
        "label": "中间阶段",
        "type": "static-flex",
        "justify": "start",
        "items": [
          {
            "type": "tag",
            "label": "试运行中",
            "displayMode": "bordered",
            "color": "running"
          }
        ]
      },
      {
        "label": "正式可用阶段/上线/注册",
        "type": "static-flex",
        "justify": "start",
        "items": [
          {
            "type": "tag",
            "label": "已上线",
            "displayMode": "bordered",
            "color": "success"
          }
        ]
      },
      {
        "label": "禁用/注销/下线",
        "type": "static-flex",
        "justify": "start",
        "items": [
          {
            "type": "tag",
            "label": "已下线",
            "displayMode": "bordered",
            "color": "inactive"
          }
        ]
      }
    ]
  }
}
```

### API 接口类型
与表单文案同时存在时，在文档的左边 

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "mode": "horizontal",
    "title": "",
    "actions": [],
    "body": [
      {
        "type": "group",
        "label": "接口",
        "body": [
          {
            "type": "flex",
            "gap": true,
            "items": [
              {
                "type": "tag",
                "label": "GET",
                "color": "success"
              },
              {
                "type": "static",
                "label": false,
                "value": "/api/mock2/saveForm"
              }
            ]
          }
        ]
      },
      {
        "type": "group",
        "label": "接口",
        "body": [
          {
            "type": "flex",
            "gap": true,
            "items": [
              {
                "type": "tag",
                "label": "POST",
                "color": "warning"
              },
              {
                "type": "static",
                "label": false,
                "value": "/api/mock2/saveForm"
              }
            ]
          }
        ]
      },
      {
        "type": "group",
        "label": "接口",
        "body": [
          {
            "type": "flex",
            "gap": true,
            "items": [
              {
                "type": "tag",
                "label": "DELETE",
                "color": "error"
              },
              {
                "type": "static",
                "label": false,
                "value": "/api/mock2/saveForm"
              }
            ]
          }
        ]
      },
      {
        "type": "group",
        "label": "接口",
        "body": [
          {
            "type": "flex",
            "gap": true,
            "items": [
              {
                "type": "tag",
                "label": "PUT",
                "color": "active"
              },
              {
                "type": "static",
                "label": false,
                "value": "/api/mock2/saveForm"
              }
            ]
          }
        ]
      }
    ]
  }
}
```

### Tag+描述

```schema
{
  "type": "page",
  "data": {
    "crud": [
      {
        "a": "a",
        "b": "b",
        "c": "c",
        "d": "d"
      }
    ]
  },
  "body": {
    "type": "crud",
    "source": "${crud}",
    "columns": [
      {
        "type": "wrapper",
        "size": "none",
        "label": "标签",
        "width": "15%",
        "body": [
          {
            "type": "tag",
            "displayMode": "bordered",
            "label": "初始化",
            "color": "active",
            "description": {
              "type": "link",
              "href": "https://www.baidu.com",
              "body": "注册审批中",
              "blank": true
            }
          }
        ]
      },
      {
        "label": "a",
        "name": "a"
      },
      {
        "label": "b",
        "name": "b"
      },
      {
        "label": "c",
        "name": "c"
      },
      {
        "label": "d",
        "name": "d"
      }
    ]
  }
}
```

## 组件用法

### 基本用法

```schema
{
  "type": "page",
  "body": [
    {
      "type": "tag",
      "label": "这是一个很长长长长长长长长长长长长长的标签",
      "closable": true,
      "onEvent": {
        "close": {
          "actions": [
            {
              "actionType": "toast",
              "args": {
                "msg": "${event.data.label}"
              }
            }
          ]
        }
      }
    }
  ]
}
```

### 边框模式

通过配置`displayMode: "bordered"`，设置边框

```schema
{
  "type": "page",
  "body": [
    {
      "type": "tag",
      "label": "普通的标签",
      "displayMode": "bordered",
      "color": "active"
    }
  ]
}
```

<!-- ### 不同的模式

```schema
{
  "type": "page",
  "body": [
    {
      "type": "tag",
      "label": "面性标签",
      "displayMode": "normal",
      "color": "active"
    },
    {
      "type": "tag",
      "label": "线性标签",
      "displayMode": "rounded",
      "color": "inactive"
    },
    {
      "type": "tag",
      "label": "状态标签",
      "displayMode": "status",
      "color": "active",
      "closable": true
    },
    {
      "type": "tag",
      "label": "#4096ff",
      "displayMode": "rounded",
      "color": "#4096ff"
    },
    {
      "type": "tag",
      "label": "#f70e47",
      "displayMode": "rounded",
      "color": "#f70e47"
    }
  ]
}
``` -->

<!-- ### 标签颜色

1. 标签有几种预设的色彩样式，可以通过设置 color 属性为 active、error、warning、success 用作不同场景使用。
2. 对于不同场景推荐使用不同颜色：
     + 提示类：active
     + 警告类：warning
     + 成功类：success
     + 失败类：error

```schema
{
  "type": "page",
  "body": {
    "type": "flex",
    "gap": true,
    "items": [
      {
        "type": "tag",
        "label": "提示类",
        "displayMode": "normal",
        "color": "active"
      },
      {
        "type": "tag",
        "label": "警告类",
        "displayMode": "normal",
        "color": "warning"
      },
      {
        "type": "tag",
        "label": "成功类",
        "displayMode": "normal",
        "color": "success"
      },
      {
        "type": "tag",
        "label": "失败类",
        "displayMode": "normal",
        "color": "error"
      }
    ]
  }
}
``` -->

<!-- ### 自定义样式

可以通过 style 来控制背景、边框及文字颜色。如下

```schema
{
  "type": "page",
  "body": [
    {
      "type": "tag",
      "label": "面性标签",
      "displayMode": "normal",
      "color": "active"
    },
    {
      "type": "tag",
      "label": "自定义样式1",
      "displayMode": "normal",
      "style": {
        "backgroundColor": "#fff",
        "border": "1px solid #ccc",
        "color": "#666"
      }
    }
  ]
}
``` -->

### 属性表

| 属性名      | 类型                 | 默认值     | 说明  | 版本                                     | 
| ----------- | ------------------ | ---------- | ------- |------- |
| displayMode | `'normal' \| 'bordered'`                   | `normal`   | 展现模式 | `1.29.0`支持 `bordered` | 
| color       | `'active' \| 'inactive' \| 'error' \| 'success' \| 'warning' \| 具体色值` |            | 颜色主题，提供默认主题，并支持自定义颜色值 | |
| label       | `string`                                                                                  | `-`        | 标签内容                                   | |
| icon        | `SchemaIcon`                                                                              | `dot 图标` | status 模式下的前置图标                    |  |
| className   | `string`                                                                                  |            | 自定义 CSS 样式类名                        |  |
| closable    | `boolean`                                                                                 | `false`    | 是否展示关闭按钮                           |   |
| description    | `string \| schemaObject`                                                                                 |     | 描述                           |   |

### 事件表

当前组件会对外派发以下事件，可以通过`onEvent`来监听这些事件，并通过`actions`来配置执行的动作，详细查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action)。

| 事件名称   | 事件参数                                                | 说明             |
| ---------- | ------------------------------------------------------- | ---------------- |
| click      | `{nativeEvent: MouseEvent, label: string}` 鼠标事件对象 | `点击`时触发     |
| mouseenter | `{nativeEvent: MouseEvent, label: string}` 鼠标事件对象 | `鼠标移入`时触发 |
| mouseleave | `{nativeEvent: MouseEvent, label: string}` 鼠标事件对象 | `鼠标移出`时触发 |
| close      | `{nativeEvent: MouseEvent, label: string}` 鼠标事件对象 | `关闭`时触发     |
