---
title: Tags 标签组
description:
type: 0
group: ⚙ 组件
menuName: Tags
icon:
---

用于水平或垂直布局展示多个标签

## 场景推荐
### 基本用法

默认水平展示

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "mode": "horizontal",
    "title": "",
    "actions": [],
    "labelWidth": 200,
    "body": [
      {
        "type": "static-tags",
        "label": "流程中",
        "items": [
          {
            "type": "tag",
            "label": "执行中",
            "displayMode": "bordered",
            "color": "active"
          },
          {
            "type": "tag",
            "label": "审核中",
            "displayMode": "bordered",
            "color": "active"
          },
          {
            "type": "tag",
            "label": "运行中",
            "displayMode": "bordered",
            "color": "active"
          }
        ]
      },
      {
        "type": "static-tags",
        "label": "流程失败/严重问题",
        "items": [
          {
            "type": "tag",
            "label": "执行失败",
            "displayMode": "bordered",
            "color": "error"
          },
          {
            "type": "tag",
            "label": "审核拒绝",
            "displayMode": "bordered",
            "color": "error"
          }
        ]
      },
      {
        "type": "static-tags",
        "label": "流程成功",
        "items": [
          {
            "type": "tag",
            "label": "执行成功",
            "displayMode": "bordered",
            "color": "success"
          },
          {
            "type": "tag",
            "label": "审核通过",
            "displayMode": "bordered",
            "color": "success"
          }
        ]
      },
      {
        "type": "static-tags",
        "label": "流程未开始/已终止",
        "items": [
          {
            "type": "tag",
            "label": "未执行",
            "displayMode": "bordered",
            "color": "inactive"
          },
          {
            "type": "tag",
            "label": "未审核",
            "displayMode": "bordered",
            "color": "inactive"
          }
        ]
      }
    ]
  }
}
```

### 垂直排列

通常在crud中需要展示多个标签或标签和文案的情况下，可使用Tags组件垂直布局展示，垂直布局下，组件宽度跟随父元素宽度

```schema
{
  "type": "page",
  "data": {
    "crud": [
      {
        "a": "a",
        "b": "b",
        "c": "c",
        "d": "d"
      }
    ],
    "colorEnum": {
      "init": "active",
      "register": "success",
      "off": "inactive",
      "running": "running"
    },
    "tagInfo": {
      "string": "dfadsafdsafdsadsafddsafsa",
      "chinese": "代发价法搜丰富的撒旦法发撒发的",
      "number": "23143214321423143214321432143",
      "mixture": "dsaf这里13"
    }
  },
  "body": {
    "type": "crud",
    "source": "${crud}",
    "columns": [
      {
        "label": "标签",
        "type": "tags",
        "width": "5%",
        "direction": "vertical",
        "items": [
          {
            "type": "tooltip-wrapper",
            "placement": "right",
            "content": "${SPLIT(tagInfo[string])}",
            "body": {
              "type": "tag",
              "displayMode": "bordered",
              "label": "${tagInfo[string]}",
              "color": "${colorEnum[init]}"
            }
          },
          {
            "type": "tooltip-wrapper",
            "placement": "right",
            "content": "${SPLIT(tagInfo[chinese])}",
            "body": {
              "type": "tag",
              "displayMode": "bordered",
              "label": "${tagInfo[chinese]}",
              "color": "${colorEnum[init]}"
            }
          },
          {
            "type": "tooltip-wrapper",
            "placement": "right",
            "content": "${SPLIT(tagInfo[number])}",
            "body": {
              "type": "tag",
              "displayMode": "bordered",
              "label": "${tagInfo[number]}",
              "color": "${colorEnum[init]}"
            }
          }
        ]
      },
      {
        "label": "a",
        "name": "a"
      },
      {
        "label": "b",
        "name": "b"
      },
      {
        "label": "c",
        "name": "c"
      },
      {
        "label": "d",
        "name": "d"
      }
    ]
  }
}
```

## 组件用法
### 排列方向

默认水平展示，可通过设置`direction: vertical`配置为垂直方向，组件宽度跟随父元素宽度

```schema
{
  "type": "page",
  "data": {
    "colorEnum": {
      "init": "active",
      "register": "success",
      "off": "inactive",
      "running": "running"
    },
    "tagInfo": {
      "string": "dfadsafdsafdsadsafddsafsa",
      "chinese": "代发价法搜丰富的撒旦法发撒发的",
      "number": "23143214321423143214321432143",
      "mixture": "dsaf这里13"
    }
  },
  "body": [
    {
      "label": "标签",
      "type": "tags",
      "direction": "vertical",
      "items": [
        {
          "type": "tag",
          "label": "active",
          "displayMode": "normal",
          "color": "active"
        },
        {
          "type": "tag",
          "label": "inactive",
          "displayMode": "normal",
          "color": "inactive"
        },
        {
          "type": "tag",
          "label": "error",
          "displayMode": "normal",
          "color": "error"
        },
        {
          "type": "tag",
          "label": "success",
          "displayMode": "normal",
          "color": "success"
        },
        {
          "type": "tag",
          "label": "processing",
          "displayMode": "normal",
          "color": "processing"
        },
        {
          "type": "tag",
          "label": "warning",
          "displayMode": "normal",
          "color": "warning"
        },
        {
          "type": "tag",
          "label": "running",
          "displayMode": "normal",
          "color": "running"
        }
      ]
    }
  ]
}
```

### 使用 source 渲染

```schema
{
  "type": "page",
  "data": {
    "items": [
      "执行中",
      "审核中",
      "运行中"
    ]
  },
  "body": {
    "type": "form",
    "mode": "horizontal",
    "title": "",
    "actions": [],
    "labelWidth": 200,
    "body": [
      {
        "type": "static-tags",
        "label": "流程中",
        "source": "${items}",
        "items": [
          {
            "type": "tag",
            "label": "${item}",
            "displayMode": "bordered",
            "color": "active"
          }
        ]
      }
    ]
  }
}
```

### 属性表

| 属性名      | 类型                 | 默认值     | 说明  | 版本                                     | 
| ----------- | ------------------ | ---------- | ------- |------- |
| className | `'string'`                   |    | css 类名 |  | 
| direction       | `'horizontal'\|'vertical'` |    `horizontal`        | 布局方向 | |
| source      | `string` |          | 获取数据域中变量， 支持 [数据映射](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/data-mapping) |
| items[]       | [SchemaNode](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode)                                                                                 |         | 内容                                   |
