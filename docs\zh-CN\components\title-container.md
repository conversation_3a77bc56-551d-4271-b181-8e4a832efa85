---
title: 标题容器
description:
type: 0
group: ⚙ 布局
menuName: 标题容器
icon:
order: 3
standardMode: true
---

标题容器主要的作用主要是承担某块区域的总结和描述以及全局操作

标题容器可以作用在各个场景，比如[分组容器](/#/amis/zh-CN/components/group-container)的分组标题也遵循标题容器规范

## 模块划分

![列表展示模块图](https://static02.sit.yxmarketing01.com/materialcenter/32c66712-d98d-486f-82e2-774e8fd88676.png)

1. 图片区域（非必有）  
   在分组组件中前面的图片可能是分组的折叠 icon，在页面详情页时可能是返回按钮，icon 非必须，icon 与标题容器内其他元素居中对齐

2. 主标题区域（必有）  
   主标题区域是整个标题容器里必须展示的部分， 作为描述该标题容器的主要信息，所以字体和字号在视觉上都做了加重强调。主标题字数不超过 15 字

3. 副标题区域（非必有）  
   副标题区域一般是对主标题的补充说明或者是描述。

4. 辅助内容区域（非必有）  
   辅助内容区域可以放置一些 tag 标签，目前标题的辅助内容区域仅支持放置 tag 标签。

5. 操作按钮区域（非必有）  
   操作按钮区域主要是放置一些针对标题容器作用的场景下的全局操作按钮。

## 场景推荐

### 列表展示带页面标题

```schema
{
  "type": "page",
  "body": [
    {
      "type": "title",
      "title": "页面大标题名称大标题名称Demo",
      "iconConfig": true
    },
    {
      "type": "crud",
      "name": "crud",
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4",
      "topToolbar": [
        {
          "type": "button",
          "label": "主按钮",
          "actionType": "url",
          "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
          "level": "primary",
          "blank": false
        },
        {
          "type": "button",
          "label": "次按钮1",
          "actionType": "url",
          "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index"
        },
        {
          "type": "button",
          "label": "次按钮2",
          "actionType": "url",
          "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index"
        },
        {
          "type": "button",
          "label": "次按钮3",
          "actionType": "url",
          "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index"
        },
        {
          "type": "button",
          "label": "次按钮4",
          "disabled": true
        },
        {
          "type": "button",
          "label": "次按钮5"
        },
        {
          "type": "button",
          "label": "次按钮6"
        }
      ],
      "columns": [
        {
          "name": "id",
          "label": "ID",
          "searchable": true
        },
        {
          "name": "engine",
          "label": "Rendering engine",
          "searchable": true
        },
        {
          "name": "browser",
          "label": "Browser",
          "headSearchable": {
            "type": "input-text",
            "name": "browser",
            "label": "Browser"
          },
          "searchable": true
        },
        {
          "name": "platform",
          "label": "Platform(s)",
          "searchable": true
        },
        {
          "name": "version",
          "label": "Engine version",
          "searchable": true
        },
        {
          "name": "grade",
          "label": "CSS grade",
          "searchable": true
        },
        {
          "name": "grade1",
          "label": "CSS grade",
          "searchable": true
        },
        {
          "name": "grade2",
          "label": "CSS grade",
          "searchable": true
        }
      ],
      "bulkActions": [
        {
          "label": "批量删除",
          "actionType": "ajax",
          "api": "delete:/amis/api/mock2/sample/${ids|raw}",
          "confirmText": "确定要批量删除?"
        },
        {
          "label": "批量修改",
          "actionType": "dialog",
          "dialog": {
            "title": "批量编辑",
            "body": {
              "type": "form",
              "api": "/amis/api/mock2/sample/bulkUpdate2",
              "body": [
                {
                  "type": "hidden",
                  "name": "ids"
                },
                {
                  "type": "input-text",
                  "name": "engine",
                  "label": "Engine"
                }
              ]
            }
          }
        }
      ]
    }
  ]
}

```

#### 落地案列

### 基础表单带页面标题

```schema
{
  "type": "page",
  "body": [
    {
      "type": "title",
      "iconConfig": true,
      "title": "页面大标题名称大标题名称",
      "subTitle": "我是小标题",
      "assistContent": [
        {
          "type": "tag",
          "label": "普通标签",
          "color": "processing"
        }
      ],
      "actions": [
        {
          "type": "button",
          "label": "取消",
          "onEvent": {
            "click": {
              "actions": [
                {
                  "actionType": "toast",
                  "args": {
                    "msgType": "info",
                    "msg": "响应取消操作"
                  }
                }
              ]
            }
          }
        },
        {
          "type": "button",
          "level": "primary",
          "label": "保存",
          "onEvent": {
            "click": {
              "actions": [
                {
                  "componentId": "myForm",
                  "actionType": "submit"
                }
              ]
            }
          }
        }
      ]
    },
    {
      "type": "form",
      "api": "/api/mock2/saveForm?waitSeconds=2",
      "actions": [],
      "body": [
        {
          "type": "group",
          "body": [
            {
              "type": "select",
              "name": "department",
              "label": "归属部门"
            },
            {
              "type": "input-text",
              "name": "platform",
              "label": "Platform",
              "placeholder": "请输入"
            },
            {
              "type": "input-text",
              "name": "css",
              "label": "CSS",
              "required": true,
              "placeholder": "请输入"
            }
          ]
        },
        {
          "type": "group",
          "body": [
            {
              "type": "input-text",
              "name": "browser",
              "label": "Browser",
              "placeholder": "请输入"
            },
            {
              "type": "select",
              "name": "selected",
              "label": "用户选择",
              "placeholder": "请选择",
              "options": [
                {
                  "label": "a",
                  "value": "a"
                },
                {
                  "label": "b",
                  "value": "b"
                }
              ]
            },
            {
              "type": "input-text",
              "name": "browser2",
              "label": "Browser",
              "placeholder": "请输入"
            }
          ]
        },
        {
          "type": "group",
          "body": [
            {
              "type": "textarea",
              "name": "remark",
              "label": "备注",
              "showCounter": true,
              "maxLength": 30,
              "placeholder": "请输入",
              "trimContents": true
            }
          ]
        },
        {
          "type": "group",
          "body": [
            {
              "type": "input-rich-text",
              "name": "tip",
              "label": "底部提示",
              "placeholder": "请输入"
            }
          ]
        }
      ]
    }
  ]
}
```

#### 落地案列

### 静态基础表单带页面标题

```schema
{
  "type": "page",
  "data": {
    "department": "department",
    "platform": "platform",
    "css": "css",
    "browser": "browser",
    "selected": "a",
    "department1": "department1",
    "platform1": "platform1",
    "css1": "css1",
    "browser1": "browser1",
    "selected1": "b",
    "remark": "备注",
    "remark1": "备注"
  },
  "body": [
    {
      "type": "title",
      "iconConfig": true,
      "title": "刘亦菲",
      "assistContent": [
        {
          "type": "flex",
          "gap": true,
          "items": [
            {
              "type": "tags",
              "items": [
                {
                  "type": "tag",
                  "label": "有提额资格",
                  "displayMode": "bordered",
                  "color": "active"
                },
                {
                  "type": "tag",
                  "label": "随借随还",
                  "displayMode": "bordered",
                  "color": "error"
                },
                {
                  "type": "tag",
                  "label": "安全资产",
                  "displayMode": "bordered",
                  "color": "success"
                },
              ]
            },
            {
              "type": "form",
              "mode": "inline",
              "body": [
                {
                  "type": "input-rating",
                  "name": "rating",
                  "count": 5,
                  "value": 3.5,
                  "half": true
                }
              ],
              "actions": []
            }
          ]
        }
      ],
      "actions": [
        {
          "type": "button",
          "label": "刷新",
        },
        {
          "type": "button",
          "level": "primary",
          "label": "下一个",
        },
      ],
    },
    {
      "type": "form",
      "title": "",
      "api": "/api/mock2/saveForm?waitSeconds=2",
      "id": "myForm",
      "actions": [],
      "static": true,
      "body": [
        {
          "type": "group",
          "body": [
            {
              "type": "select",
              "name": "department",
              "label": "归属部门"
            },
            {
              "type": "input-text",
              "name": "platform",
              "label": "Platform",
              "placeholder": "请输入"
            },
            {
              "type": "input-text",
              "name": "css",
              "label": "CSS",
              "required": true,
              "placeholder": "请输入"
            }
          ]
        },
        {
          "type": "group",
          "body": [
            {
              "type": "input-text",
              "name": "browser",
              "label": "Browser",
              "placeholder": "请输入"
            },
            {
              "type": "select",
              "name": "selected",
              "label": "用户选择",
              "placeholder": "请选择",
              "options": [
                {
                  "label": "a",
                  "value": "a"
                },
                {
                  "label": "b",
                  "value": "b"
                }
              ]
            },
            {
              "type": "input-text",
              "name": "browser",
              "label": "Browser",
              "placeholder": "请输入"
            }
          ]
        },
        {
          "type": "group",
          "body": [
            {
              "type": "select",
              "name": "department1",
              "label": "归属部门1"
            },
            {
              "type": "input-text",
              "name": "platform1",
              "label": "Platform1",
              "placeholder": "请输入"
            },
            {
              "type": "input-text",
              "name": "css1",
              "label": "CSS1",
              "required": true,
              "placeholder": "请输入"
            }
          ]
        },
        {
          "type": "group",
          "body": [
            {
              "type": "input-text",
              "name": "browser1",
              "label": "Browser1",
              "placeholder": "请输入"
            },
            {
              "type": "select",
              "name": "selected1",
              "label": "用户选择1",
              "placeholder": "请选择",
              "options": [
                {
                  "label": "a",
                  "value": "a"
                },
                {
                  "label": "b",
                  "value": "b"
                }
              ]
            },
            {
              "type": "input-text",
              "name": "browser1",
              "label": "Browser1",
              "placeholder": "请输入"
            }
          ]
        },
        {
          "type": "group",
          "body": [
            {
              "type": "textarea",
              "name": "remark",
              "label": "备注",
              "showCounter": true,
              "maxLength": 30,
              "placeholder": "请输入",
              "trimContents": true
            }
          ]
        },
        {
          "type": "group",
          "body": [
            {
              "type": "input-rich-text",
              "name": "tip1",
              "label": "底部提示1",
              "placeholder": "请输入"
            }
          ]
        },
        {
          "type": "group",
          "body": [
            {
              "type": "textarea",
              "name": "remark1",
              "label": "备注1",
              "showCounter": true,
              "maxLength": 30,
              "placeholder": "请输入",
              "trimContents": true
            }
          ]
        },
        {
          "type": "group",
          "body": [
            {
              "type": "input-rich-text",
              "name": "tip",
              "label": "底部提示",
              "placeholder": "请输入"
            }
          ]
        }
      ]
    }
  ]
}
```

#### 落地案列

### 步骤表单带标题

```schema
{
  "type": "page",
  "data": {
    "name": "张三",
    "age": 20,
    "address": "金科路",
    "email": "<EMAIL>",
    "class": "三年二班",
    "company": "abc",
    "telephone": 18711223344,
    "eventSource": "FEATURE",
    "type": "caocao"
  },
  "body": [
    {
      "type": "title",
      "iconConfig": true,
      "title": "页面大标题名称大标题名称",
      "subTitle": "我是小标题"
    },
    {
      "type": "form",
      "static": true,
      "labelWidth": 130,
      "actions": [],
      "body": {
        "type": "wrapper",
        "bgColor": "white",
        "body": [
          {
            "type": "steps",
            "status": {
              "second": "error"
            },
            "steps": [
              {
                "title": "第一步",
                "value": "first"
              },
              {
                "title": "第二步",
                "value": "second"
              },
              {
                "title": "第三步",
                "value": "third"
              }
            ]
          },
          {
            "type": "group-container",
            "collapsible": false,
            "items": [
              {
                "type": "panel",
                "header": {
                  "title": "第一步，基础信息"
                },
                "body": [
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "input-text",
                        "name": "name",
                        "label": "姓名"
                      },
                      {
                        "type": "input-text",
                        "name": "age",
                        "label": "年龄"
                      },
                      {
                        "type": "input-text",
                        "name": "class",
                        "label": "班级"
                      }
                    ]
                  },
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "input-text",
                        "name": "email",
                        "label": "邮箱"
                      },
                      {
                        "type": "input-text",
                        "name": "telephone",
                        "label": "电话"
                      },
                      {
                        "type": "input-text",
                        "name": "address",
                        "label": "地址"
                      }
                    ]
                  },
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "select",
                        "name": "type",
                        "label": "类型",
                        "options": [
                          {
                            "label": "曹操",
                            "value": "caocao"
                          },
                          {
                            "label": "刘备",
                            "value": "liubei"
                          }
                        ]
                      },
                      {
                        "type": "select",
                        "name": "eventSource",
                        "label": "事件来源",
                        "options": [
                          {
                            "label": "业务系统",
                            "value": "SYSTEM"
                          },
                          {
                            "label": "特征系统",
                            "value": "FEATURE"
                          },
                          {
                            "label": "北斗系统",
                            "value": "EFUEL"
                          },
                          {
                            "label": "埋点系统",
                            "value": "STATS"
                          }
                        ]
                      },
                      {
                        "type": "input-text",
                        "name": "company",
                        "label": "工作地址"
                      }
                    ]
                  }
                ]
              },
              {
                "type": "panel",
                "header": {
                  "title": "第二步，复杂信息"
                },
                "body": [
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "input-text",
                        "name": "name",
                        "label": "姓名"
                      },
                      {
                        "type": "input-text",
                        "name": "age",
                        "label": "年龄"
                      },
                      {
                        "type": "input-text",
                        "name": "class",
                        "label": "班级"
                      }
                    ]
                  },
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "input-text",
                        "name": "email",
                        "label": "邮箱"
                      },
                      {
                        "type": "input-text",
                        "name": "telephone",
                        "label": "电话"
                      },
                      {
                        "type": "input-text",
                        "name": "address",
                        "label": "地址"
                      }
                    ]
                  },
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "select",
                        "name": "type",
                        "label": "类型",
                        "options": [
                          {
                            "label": "曹操",
                            "value": "caocao"
                          },
                          {
                            "label": "刘备",
                            "value": "liubei"
                          }
                        ]
                      },
                      {
                        "type": "select",
                        "name": "eventSource",
                        "label": "事件来源",
                        "options": [
                          {
                            "label": "业务系统",
                            "value": "SYSTEM"
                          },
                          {
                            "label": "特征系统",
                            "value": "FEATURE"
                          },
                          {
                            "label": "北斗系统",
                            "value": "EFUEL"
                          },
                          {
                            "label": "埋点系统",
                            "value": "STATS"
                          }
                        ]
                      },
                      {
                        "type": "input-text",
                        "name": "company",
                        "label": "工作地址"
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    }
  ]
}
```

#### 落地案列

### 卡片带标题

```schema
{
  "type": "page",
  "id": "page-header",
  "data": {
    "department": "department",
    "platform": "platform",
    "css": "css",
    "browser": "browser",
    "selected": "a",
    "department1": "department1",
    "platform1": "platform1",
    "css1": "css1",
    "browser1": "browser1",
    "selected1": "b",
    "remark": "备注",
    "remark1": "备注",
    "collection": true,
  },
  "body": [
    {
      "type": "title",
      "iconConfig": true,
      "title": "页面大标题名称大标题名称Demo",
      "subTitle": "这是小标题",
      "actions": [
        {
          "type": "button",
          "label": false,
          "icon": "fa fa-star-o",
          "level": "link",
          "linkWithoutPadding": true,
          "visibleOn": "${collection}",
          "onEvent": {
            "click": {
              "actions": [
                {
                  "actionType": "setValue",
                  "componentId": "page-header",
                  "args": {
                    "value": {
                      "collection": false
                    }
                  }
                }
              ]
            }
          }
        },
        {
          "type": "button",
          "label": false,
          "icon": "fa fa-star",
          "linkWithoutPadding": true,
          "level": "link",
          "visibleOn": "${!collection}",
          "onEvent": {
            "click": {
              "actions": [{
                "actionType": "setValue",
                "componentId": "page-header",
                "args": {
                  "value": {
                    "collection": true
                  }
                }
              }]
            }
          }
        }
      ]
     },
     {
      "type": "card",
      "header": {
        "title": "FLINK_TABLE_NAME",
        "subTitle": "副标题",
        "description": "这是一段描述",
        "avatar": "https://suda.cdn.bcebos.com/images/amis/ai-fake-face.jpg"
      },
      body: [
        {
          type: "group",
          body: [
            {
              "type": "tabs",
              "name": "tabs",
              "tabs": [
                {
                  "title": "列表",
                  "icon": "fab fa-apple",
                  "body": {
                    "type": "crud",
                    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?waitSeconds=1",
                    "columns": [
                      {
                        "name": "id",
                        "label": "ID"
                      },
                      {
                        "name": "engine",
                        "label": "Rendering engine"
                      },
                      {
                        "name": "browser",
                        "label": "Browser"
                      },
                      {
                        "name": "platform",
                        "label": "Platform(s)"
                      },
                      {
                        "name": "engine",
                        "label": "Engine"
                      },
                      {
                        "name": "version",
                        "label": "Engine Version"
                      },
                      {
                        "name": "grade",
                        "label": "CSS grade"
                      }
                    ]
                  }
                },
                {
                  "title": "基础表单2",
                  "icon": "fas fa-bug",
                  "body": {
                    "type": "form",
                    "wrapWithPanel": false,
                    "labelWidth": 60,
                    "body": [
                     {
                        "type": "static",
                        "name": "text1",
                        "label": "归属部门",
                        "columnRatio": 4
                      }
                    ]
                  }
                }
              ]
            }
          ]
        }
      ]
    }
  ]
};
```

#### 落地案列

## 组件用法

### 基本用法

传入`title`属性即可：

```schema
{
  "type": "page",
  "body": [
    {
      "type": "title",
      "iconConfig": true,
      "title":"页面大标题名称大标题名称Demo",
    },
  ]
};
```

### 吸顶

将`affixHeader`设置为 true，标题吸顶。

注意：**标题的父元素一定要是滚动元素**。

```schema
{
  "type": "page",
  "body": [
    {
      "type": "title",
      "iconConfig": true,
      "title":"页面大标题名称大标题名称Demo",
      "affixHeader": true,
    },
  ]
};
```

### 自定义 icon

通过设置`iconConfig`可以自定义 icon，可使用 icon 范围，参照[Icon](/#/amis/zh-CN/components/icon)组件，iconConfig 为`false`表示不展示 icon

```schema
{
  "type": "page",
  body: [
    {
      "type": "title",
      "title":"页面大标题名称大标题名称Demo",
      iconConfig: {
        "type": "icon",
        "icon": "chevron-left"
      }
    },
  ]
};
```

### 自定义辅助内容

通过配置`assistContent`可以自定义辅助区域：

```schema
{
  "type": "page",
  "body": [
    {
      "type": "title",
      "title": "页面大标题名称大标题名称Demo",
      "iconConfig": true,
      "assistContent": [
        {
          "type": "tag",
          "label": "普通标签",
          "color": "processing"
        }
      ]
    }
  ]
}
```

### 自定义按钮

通过配置`actions`可以自定义按钮：

```schema
{
  "type": "page",
  "body": [
    {
      "type": "title",
      "title": "页面大标题名称大标题名称Demo",
      "iconConfig": true,
      "actions": [
        {
          "type": "button",
          "label": "取消",
          "onEvent": {
            "click": {
              "actions": [
                {
                  "actionType": "toast",
                  "args": {
                    "msgType": "info",
                    "msg": "响应取消操作"
                  }
                }
              ]
            }
          }
        },
        {
          "type": "button",
          "level": "primary",
          "label": "保存",
          "onEvent": {
            "click": {
              "actions": [
                {
                  "componentId": "myForm",
                  "actionType": "submit"
                }
              ]
            }
          }
        }
      ]
    }
  ]
}
```

### 无边距

默认有 Padding 与 Margin 。配置 `"noPadding": true` 可只展示标题，去掉多余边距。用于一些已经有白色背景的容器内，只展示 `"title"` 等信息时。

```schema
{
  "type": "page",
  "body": [
    {
      "type": "title",
      "title": "页面大标题名称大标题名称Demo",
      "noPadding": true,
    }
  ]
}
```

### 属性表

| 属性名             | 类型                    | 默认值                         | 说明                                                                                                |
| ------------------ | ----------------------- | ------------------------------ | --------------------------------------------------------------------------------------------------- |
| title              | `string`                |                                | 主标题的名称                                                                                        |
| subTitle           | `string`                |                                | 副标题名称                                                                                          |
| noPadding          | `boolean`               | `true`                         | 去掉边距                                                                                            |
| iconConfig         | `shcemaNode \| boolean` | 配置为 true 时，默认为返回图标 | 自定义 icon                                                                                         |
| iconConfig.onEvent | `object`                | `-`                            | 监听`icon`的事件，支持的事件可查看[Icon 事件表](/dataseeddesigndocui/#//amis/zh-CN/components/icon) |
| affixHeader        | `boolean`               | false                          | 是否吸顶                                                                                            |
| assistContent      | `shcemaNode`            |                                | 自定义辅助内容                                                                                      |
| actions            | `shcemaNode`            |                                | 自定义操作按钮                                                                                      |

iconConfig.onEvent 使用示例

```js
{
    "type": "title",
    "title": "页面大标题名称大标题名称Demo",
    "iconConfig": {
      "onEvent": {
        "click": { // 监听点击事件
          "actions": [
            {
              "actionType": "toast",
              "args": {
                "msgType": "info",
                "msg": "派发点击事件"
              }
            }
          ]
        },
      }
    }
}
```
