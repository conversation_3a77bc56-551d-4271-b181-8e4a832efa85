---
title: Typography 排版
description:
type: 0
group: ⚙ 组件
menuName: Typography
icon:
order: 70
---
文本展示，可以自动识别文本是否溢出并使用tooltip展示。版本1.52.0支持。

## 基本用法

```schema
{
  "type": "page",
  "data": {
    "text1": "我是短文本。",
    "text2": "我是短文本，但我设置了tooltipMode: always。",
    "text3": "我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。",
  },
  "body": [
    {
      "type": "typography",
      "text": "Hello ${text1}"
    },
    {
      "type": "divider"
    },
    {
      "type": "typography",
      "text": "Hello ${text2}",
      "ellipsis": {
        "tooltipMode": "always"
      }
    },
    {
      "type": "divider"
    },
    {
      "type": "typography",
      "text": "Hello ${text3}"
    }
  ]
}
```

## 多行文本省略
`ellipsis`属性支持配置`rows`行数，文本溢出后自动tooltip展示。

```schema
{
  "type": "page",
  "data": {
    "text1": "我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。"
  },
  "body": [
    {
      "type": "typography",
      "text": "Hello ${text1}",
      "ellipsis": {
        "rows": 2
      }
    }
  ]
}
```

未溢出则不展示 tooltip 。

```schema
{
  "type": "page",
  "data": {
    "text1": "我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。我是长文本。"
  },
  "body": [
    {
      "type": "typography",
      "text": "Hello ${text1}",
      "ellipsis": {
        "rows": 2
      }
    }
  ]
}
```


## 自定义tooltip内容
`ellipsis`属性支持配置`tooltip`，自定义tooltip内容。

```schema
{
  "type": "page",
  "data": {
    "text1": "我是短文本。"
  },
  "body": [
    {
      "type": "typography",
      "text": "Hello ${text1}",
      "ellipsis": {
        "tooltip": "我是自定义的tooltip内容"
      }
    }
  ]
}
```


## 属性表

| 属性名          | 类型                                 | 默认值  | 说明                                         |
| --------------- | ------------------------------------ | ------- | -------------------------------------------- |
| type            | `string`                             | `"text"` | 指定为 Typography 组件                              |
| className       | `string`                             |         | 外层 Dom 的类名                              |
| style     | `object`    |        | 外层 dom 的样式       |
| text            | [表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression) |         | 文本内容                                     |
| placeholder            | `string`                                    | `-`           | 内容为空时占位符      |
| ellipsis            | `TypographyEllipsisObject`                                    | -           | 自动溢出省略，可设置省略行数，tooltip提示      |
| ellipsis.rows       | `number`                                    | -           | 省略行数，溢出自动省略      |
| ellipsis.width       | `number`                                    | -           | 外层 dom 宽度     |
| ellipsis.tooltipMode       | `auto` \| `always` \| `none`                    | -           | tooltip提示开启模式。默认`auto`，溢出自动tooltip提示；设置`always`时始终展示tooltip；设置`none`时从不展示tooltip。     |
| ellipsis.tooltip            | `'string' \| 'TooltipObject'`                                    | -           | 鼠标停留时弹出该段文字                               |

## 事件表

当前组件会对外派发以下事件，可以通过`onEvent`来监听这些事件，并通过`actions`来配置执行的动作，详细查看[事件动作](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/event-action)。

| 事件名称   | 事件参数                               | 说明           |
| ---------- | -------------------------------------- | -------------- |
| click      | `nativeEvent: MouseEvent` 鼠标事件对象 | 点击时触发     |
| mouseenter | `nativeEvent: MouseEvent` 鼠标事件对象 | 鼠标移入时触发 |
| mouseleave | `nativeEvent: MouseEvent` 鼠标事件对象 | 鼠标移出时触发 |
