---
title: VirtualList 虚拟列表
description:
type: 0
group: ⚙ 组件
menuName: VirtualList 虚拟列表
icon:
order: 47
standardMode: true
---

根据配置的可视区域大小，虚拟渲染可视区域元素，主要是用场景是渲染大量节点
## 场景推荐

### 虚拟渲染tpl

```schema: scope="body"
{
  type: "page",
  data: {
    virtualDom: "虚拟元素"
  },
  body: [
    {
      type: "virtual-list",
      itemCount: 100,
      itemSize: 32,
      body: {
        type: "tpl",
        tpl: "<div style='height: 32px'>${virtualDom}${__virtualIndex}</div>"
      }
    }
  ],
}
```

### 虚拟渲染图表

```schema: scope="body"
{
  type: "page",
  data: {
    virtualDom: "虚拟元素"
  },
  body: [
    {
      type: "virtual-list",
      itemCount: 10,
      itemSize: 300,
      body: {
        type: "chart",
        api: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/chart/chart"
      }
    }
  ],
}

```


## 组件用法

### 基本用法
#### 相同内容

`body`如果配置为单个元素，则需要配合`itemCount`一起使用

```schema
{
  type: "page",
  data: {
    virtualDom: "虚拟元素",
    itemCount: 100,
    itemSize: 32
  },
  body: [
    {
      type: "virtual-list",
      itemCount: '${itemCount}',
      itemSize: '${itemSize}',
      body: {
        type: "tpl",
        tpl: "<div style='height: 32px'>${virtualDom}${__virtualIndex}</div>"
      }
    }
  ],
}
```
#### 不同内容

`body`如果配置为数组，可以定制`相同高度`的不同内容

```schema
{
  type: "page",
  data: {
    virtualDom: "虚拟元素"
  },
  body: [
    {
      type: "virtual-list",
      itemSize: 300,
      body: [
        {
          type: "tpl",
          tpl: "<div style='height: 300px'>${virtualDom}${__virtualIndex}</div>"
        },
        {
          type: "chart",
          api: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/chart/chart",
        },
        {
          type: "tpl",
          tpl: "<div style='height: 300px'>${virtualDom}${__virtualIndex}</div>"
        },
        {
          type: "chart",
          api: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/chart/chart",
        },
        {
          type: "tpl",
          tpl: "<div style='height: 300px'>${virtualDom}${__virtualIndex}</div>"
        },
        {
          type: "chart",
          api: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/chart/chart",
        },
        {
          type: "tpl",
          tpl: "<div style='height: 300px'>${virtualDom}${__virtualIndex}</div>"
        },
        {
          type: "chart",
          api: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/chart/chart",
        },
      ]
    }
  ],
}
```

### 属性表

| 属性名     | 类型                                   | 默认值 | 说明                                                                                                |
| ---------- | -------------------------------------- | ------ | --------------------------------------------------------------------------------------------------- |
| itemCount       | `number`                               |  | 虚拟元素个数，当body为数组时无效                                                                                  |
| itemSize  | `number | string`                               |        | 单个虚拟元素高,也可以设置成上层数据源的内容度                                                                                            |
| virtualListHeight    | `number`                               |    `320`    | 虚拟列表容器最小高度	 |
| body | `SchemaNode`                               |        | 需要虚拟渲染元素                           |

