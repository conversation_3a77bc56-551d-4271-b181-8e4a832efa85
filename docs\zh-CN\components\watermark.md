---
title: Watermark 水印
description:
type: 0
group: ⚙ 组件
menuName: Watermark 水印
icon:
order: 73
---

页面需要添加水印标识版权时使用，适用于防止信息盗用。 1.61.0版本支持

## 基本使用

使用`content`设置水印内容

```schema
{
  "type": "page",
  "body": {
    "type": "watermark",
    "content": "amis",
    "body": {
      "type": "container",
      "className": "h-52",
      "body": "Hello world"
    }
  }
}
```

`content`支持从数据域中取值

```schema
{
  "type": "page",
  "data": {
    "text": "数据域中的文本"
  },
  "body": {
    "type": "watermark",
    "content": "${text}",
    "body": {
      "type": "container",
      "className": "h-52",
      "body": "Hello world"
    }
  }
}
```

## 多行水印

可以将`content`配置为一个数组，支持多行水印。

```schema
{
  "type": "page",
  "body": {
    "type": "watermark",
    "content": ["dataseed design", "amis"],
    "body": {
      "type": "container",
      "className": "h-52",
      "body": "Hello world"
    }
  }
}
```

## 自定义倾斜角度

通过`rotate`配置水印的倾斜角度。

```schema
{
  "type": "page",
  "body": {
    "type": "watermark",
    "content": "amis",
    "rotate": 90,
    "body": {
      "type": "container",
      "className": "h-52",
      "body": "Hello world"
    }
  }
}
```

## 水印样式

通过`color`设置水印的颜色，`fontSize`设置字体大小，`fontStyle`设置字体风格，比如让字体倾斜。

```schema
{
  "type": "page",
  "body": {
    "type": "watermark",
    "content": "amis",
    "color": "red",
    "fontSize": 20,
    "fontStyle": "italic",
    "body": {
      "type": "container",
      "className": "h-52",
      "body": "Hello world"
    }
  }
}
```

## 属性表

|属性名 | 类型 | 默认值 | 说明 |
|------|------|-------|------|
|content | string | - | 水印内容 |
|color | string | `rgba(16, 40, 76, 0.07)` | 水印颜色 |
|fontSize | number | 13 | 水印字体大小 |
|fontStyle | `'none' \| 'normal' \| 'italic' \| 'oblique'` | `normal` | 水印字体样式 |
| rotate | number | -22 | 水印旋转角度 |
| offset | `[number, number]` | - | 水印偏移量 [横坐标, 纵坐标] |
| gap | `[number, number]` | - | 水印之间的距离 [水平间距, 垂直间距]  |
| width | number | - | 水印的宽度，content 的默认值为自身的宽度  |
| height | number | - | 水印的高度，content 的默认值为自身的高度  |
