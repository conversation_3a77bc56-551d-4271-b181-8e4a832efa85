---
title: Wrapper 包裹容器
description:
type: 0
group: ⚙ 组件
menuName: Wrapper
icon:
order: 72
standardMode: true
---

简单的一个包裹容器组件，相当于用 div 包含起来，最大的用处是用来配合 css 进行布局。

## 组件用法

### 基本用法

```schema: scope="body"
{
    "type": "wrapper",
    "body": "内容",
    "className": "b"
}
```

> 上面例子中的 `"className": "b"` 是为了增加边框，不然看不出来。

### 不同内边距

通过配置`size`属性，可以调整内边距。默认为 `size:"md"`，设置 `size: "none"` 则不设置内边距。

```schema: scope="body"
[
  {
    "type": "wrapper",
    "body": "默认内边距，内边距是16px",
    "className": "b"
  },
  {
    "type": "divider"
  },
  {
    "type": "wrapper",
    "body": "无内边距",
    "size": "none",
    "className": "b"
  }
]
```

### 背景颜色

默认背景为透明色，配置 `bgColor` 属性，可设置背景颜色。目前仅支持 `"white"`。当某些容器需要白色背景时，可配置该属性为 `white`。

```schema: scope="body"
[
  {
    "type": "wrapper",
    "body": "白色背景容器",
    "bgColor": "white"
  }
]
```

### 属性表

| 属性名    | 类型                                      | 默认值      | 说明                         |
| --------- | ----------------------------------------- | ----------- | ---------------------------- |
| type      | `string`                                  | `"wrapper"` | 指定为 Wrapper 渲染器        |
| className | `string`                                  |             | 外层 Dom 的类名              |
| size      | `string`                                  |   `"md"`    | 内边距，支持: `md` 和`none` |
| bgColor | `string`                            |             | 背景颜色。 支持： `white`  |
| body      | [SchemaNode](/dataseeddesigndocui/#/amis/zh-CN/docs/types/schemanode) |             | 内容容器                     |
