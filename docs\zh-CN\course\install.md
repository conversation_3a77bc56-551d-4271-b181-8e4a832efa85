---
title: 快速上手
description:
---

@dataseed/amis 致力于提供给程序员愉悦的开发体验。

## react

如果在已有项目中，React 版本需要是 `>=16.8.6`，mobx 需要 `^4.5.0`。

### 安装

```
npm i -S @dataseed/amis
npm i -S @fortawesome/fontawesome-free
```

### 主题样式

目前主要支持主题：`antd（仿 Antd）`

1. 引入样式文件：

html 中引入：

```html
<link href="./node_modules/@dataseed/amis/sdk/iconfont.css" />
<link href="./node_modules/amis/lib/themes/antd.css" />
<link href="./node_modules/@fortawesome/fontawesome-free/css/all.css" />
<link href="./node_modules/@fortawesome/fontawesome-free/css/v4-shims.css" />
<link href="./node_modules/@dataseed/amis/lib/helper.css" />
```

css 中引入：

```css
@import '~@dataseed/amis/sdk/iconfont.css';
@import '~@dataseed/amis/lib/themes/antd.css';
@import '~@fortawesome/fontawesome-free/css/all.css';
@import '~@fortawesome/fontawesome-free/css/v4-shims.css';
@import '~@dataseed/amis/lib/helper.css';
```

> 上面只是示例，请根据自己的项目结构调整引用路径
> 如果要支持 IE 11 请引入 @dataseed/amis/sdk/antd-ie11.css，但这样就没法支持 CSS 变量了

2. 渲染器使用配置主题

```js
renderAmis(
  {
    type: 'page',
    title: '简单页面',
    body: '内容',
  },
  {
    // props
  },
  {
    // env...
    theme: 'antd',
  },
);
```

### 使用指南

代码实现（React Typescript）

```tsx
import * as React from 'react';
import axios from 'axios';
import copy from 'copy-to-clipboard';

import {render as renderAmis} from '@dataseed/amis';
import {
  ToastComponent,
  AlertComponent,
  alert,
  confirm,
  toast,
} from '@dataseed/amis-ui';

class MyComponent extends React.Component<any, any> {
  render() {
    let amisScoped;
    let theme = 'antd';
    let locale = 'zh-CN';

    // 请勿使用 React.StrictMode，目前还不支持
    return (
      <div>
        <p>通过 amis 渲染页面</p>
        <ToastComponent
          theme={theme}
          key="toast"
          position={'top-right'}
          locale={locale}
        />
        <AlertComponent theme={theme} key="alert" locale={locale} />
        {renderAmis(
          {
            // 这里是 amis 的 Json 配置。
            type: 'page',
            title: '简单页面',
            body: '内容',
          },
          {
            // props...
            // locale: 'en-US' // 请参考「多语言」的文档
            // scopeRef: (ref: any) => (amisScoped = ref)  // 功能和前面 SDK 的 amisScoped 一样
            // standardMode: true // v1.71.1开始支持新规范，配置该字段后开启
          },
          {
            // 下面三个接口必须实现
            fetcher: ({
              url, // 接口地址
              method, // 请求方法 get、post、put、delete
              data, // 请求数据
              responseType,
              config, // 其他配置
              headers, // 请求头
            }: any) => {
              config = config || {};
              config.withCredentials = true;
              responseType && (config.responseType = responseType);

              if (config.cancelExecutor) {
                config.cancelToken = new (axios as any).CancelToken(
                  config.cancelExecutor,
                );
              }

              config.headers = headers || {};

              if (method !== 'post' && method !== 'put' && method !== 'patch') {
                if (data) {
                  config.params = data;
                }

                return (axios as any)[method](url, config);
              } else if (data && data instanceof FormData) {
                config.headers = config.headers || {};
                config.headers['Content-Type'] = 'multipart/form-data';
              } else if (
                data &&
                typeof data !== 'string' &&
                !(data instanceof Blob) &&
                !(data instanceof ArrayBuffer)
              ) {
                data = JSON.stringify(data);
                config.headers = config.headers || {};
                config.headers['Content-Type'] = 'application/json';
              }

              return (axios as any)[method](url, data, config);
            },
            isCancel: (value: any) => (axios as any).isCancel(value),
            copy: content => {
              copy(content);
              toast.success('内容已复制到粘贴板');
            },
            theme,

            // 后面这些接口可以不用实现

            // 默认是地址跳转
            // jumpTo: (
            //   location: string /*目标地址*/,
            //   action: any /* action对象*/
            // ) => {
            //   // 用来实现页面跳转, actionType:link、url 都会进来。
            // },

            // updateLocation: (
            //   location: string /*目标地址*/,
            //   replace: boolean /*是replace，还是push？*/
            // ) => {
            //   // 地址替换，跟 jumpTo 类似
            // },

            // isCurrentUrl: (
            //   url: string /*url地址*/,
            // ) => {
            //   // 用来判断是否目标地址当前地址
            // },

            // notify: (
            //   type: 'error' | 'success' /**/,
            //   msg: string /*提示内容*/
            // ) => {
            //   toast[type]
            //     ? toast[type](msg, type === 'error' ? '系统错误' : '系统消息')
            //     : console.warn('[Notify]', type, msg);
            // },
            // alert,
            // confirm,
            // tracker: (eventTracke) => {}
          },
        )}
      </div>
    );
  }
}
```

render 有三个参数，后面会详细说明这三个参数内的属性

```js
(schema, props, env) => JSX.Element;
```

### schema

即页面配置，请前往 [配置与组件](http://moka.dmz.dev.caijj.net/dataseeddesigndocui/#/amis/zh-CN/components/page) 了解

### props

如果你想传递一些数据给渲染器内部使用，可以传递 data 数据进去。
`v1.71.1`版本开始支持新的规范，无需再使用辅助函数，配置 `standardMode` 字段后开启。 如：

```jsx
() =>
  renderAmis(schema, {
    data: {
      username: 'amis',
    },
    standardMode: true // v1.71.1开始支持新规范，配置该字段后开启
  });
```

传递 data 数据进去后，内部所有组件都能拿到 `username` 这个变量的值。当然，这里的 key 并不一定必须是 data , 你也可以是其它 key，但必须配合 schema 中的 `detectField` 属性一起使用。 如：

```jsx
() =>
  renderAmis(
    {
      //其它配置
      detectField: 'somekey',
    },
    {
      somekey: {
        username: 'amis',
      },
    },
  );
```

### env

环境变量，可以理解为这个渲染器工具的配置项，需要使用 amis 用户实现部分接口。它有下面若干参数，其中一部分可通过[工具库](http://moka.dmz.dev.caijj.net/dataseeddesigndocui/#/amis/zh-CN/course/utils)快速创建。

#### fetcher（必须实现）

接口请求器，实现该函数才可以实现 ajax 发送，函数签名如下：

```ts
(config: {
  url; // 接口地址
  method; // 请求方法 get、post、put、delete
  data; // 请求数据
  responseType;
  config; // 其他配置
  headers; // 请求头
}) => Promise<fetcherResult>;
```

你可以使用任何你喜欢的 ajax 请求库来实现这个接口。amis 内部会忽略网络请求错误，请在 fetcher 的实现中处理网络请求错误，或将网络请求错误转换成一个 fullfilled promise，逻辑就会执行到 [adaptor](https://aisuda.bce.baidu.com/amis/zh-CN/docs/types/api#%E9%85%8D%E7%BD%AE%E6%8E%A5%E6%94%B6%E9%80%82%E9%85%8D%E5%99%A8) 中，就像下面这样：

```ts
axios.interceptors.response.use(
  respones => {
    // 请求被正常响应
  },
  error => {
    // 请求出现网络错误，如 http 请求的 Status Code 为 500
    // 返回一个 fullfilled promise，逻辑就会执行到 adaptor 中
    return Promise.resolve({
      status: 500,
      msg: '网络错误',
    });
  },
);
```

#### isCancel

判断请求是否被取消。

```ts
(value: any) => boolean;
```

#### authorizedConfig

鉴权配置，可以通过该配置实现页面内组件鉴权，需与`Page` 组件的 `authorizedConfig` 配置，搭配使用。 当开启权限后，可使用 `${CHECKAUTHORITY('code')}` 表达式校验权限。1.72.0 及以上版本支持。

| 属性名 | 类型 | 必须 | 默值  | 说明 |
| --- | --- | --- | --- | --- |
| api | [API](/dataseeddesigndocui/#/amis/zh-CN/docs/types/api) | 否 | 无 | 请求权限数据API。**该API仅在页面初始化时请求1次，因此不支持该API动态更新。** |
| checkAuthority | `(authority, permitComponents) => boolean` | 否 | 无 | 权限校验函数，校验否具有权限。 |
| createAuthorizedConfig | `(options) => authorizedConfig` | 否 | 无 | 当需要更加自定义配置属性返回鉴权配置时，可以使用该配置 |

#### notify

```ts
(type: 'info' | 'success' | 'error' | 'warning', msg: string) => void
```

用来实现消息提示。

![info](http://oa.dmz.prod.caijj.net/resourceservice/cdn/preview/CUSTOM/2023-09-05/lattebank-static-resourceservice-prod/static/notify_info/c3z56grvlvy9.jpg)

![success](http://oa.dmz.prod.caijj.net/resourceservice/cdn/preview/CUSTOM/2023-09-05/lattebank-static-resourceservice-prod/static/notify_success/c3z56gwvebcx.jpg)

![error](http://oa.dmz.prod.caijj.net/resourceservice/cdn/preview/CUSTOM/2023-09-05/lattebank-static-resourceservice-prod/static/notify_error/c3z56gu3igow.jpg)

![warning](http://oa.dmz.prod.caijj.net/resourceservice/cdn/preview/CUSTOM/2023-09-05/lattebank-static-resourceservice-prod/static/notify_warning/c3z56gxpd3i8.jpg)

#### alert

```ts
(msg: string, title?: string) => void
```

用来实现警告提示。

![alert](http://oa.dmz.prod.caijj.net/resourceservice/cdn/preview/CUSTOM/2023-09-05/lattebank-static-resourceservice-prod/static/alert/c3z6k4xuu9z4.jpg)

#### confirm

```ts
(msg: string) => boolean | Promise<boolean>;
```

用来实现确认框。返回 boolean 值

#### jumpTo

```ts
(to: string, action?: Action, ctx?: object) => void
```

用来实现页面跳转，因为不清楚所在环境中是否使用了 spa 模式，所以用户自己实现吧。

#### updateLocation

```ts
(location: any, replace?: boolean) => void
```

地址替换，跟 jumpTo 类似。

#### blockRouting

设置阻止路由跳转的钩子函数，用来实现 form 未保存提前离开时出现确认框。

```ts
(fn: (nextLocation:any) => void | string) => () => void;
```

#### theme: string

目前支持主题：`antd`

#### isCurrentUrl

```ts
(link: string) => boolean;
```

判断目标地址是否为当前页面。

#### copy

```ts
(contents: string, options?: {silent: boolean, format?: string})
```

用来实现内容复制，其中 `format` 可以为 text/html，或 text/plain

#### tracker

用户行为跟踪，请参考[这里](https://aisuda.bce.baidu.com/amis/zh-CN/docs/index)

#### session

默认为 'global'，决定 store 是否为全局共用的，如果想单占一个 store，请设置不同的值。

#### getModalContainer

```ts
() => HTMLElement;
```

用来决定弹框容器。

#### loadRenderer

```ts
(schema: any, path: string) => Promise<Function>;
```

可以通过它懒加载自定义组件，比如： https://github.com/baidu/amis/blob/master/__tests__/factory.test.tsx#L64-L91。

#### affixOffsetTop: number

固顶间距，当你的有其他固顶元素时，需要设置一定的偏移量，否则会重叠。

#### affixOffsetBottom: number

固底间距，当你的有其他固底元素时，需要设置一定的偏移量，否则会重叠。

#### richTextToken: string

内置 rich-text 为 frolaEditor，想要使用，请自行购买，或者用免费的 Tinymce，不设置 token 默认就是 Tinymce。

#### hideValidateFailedDetail: boolean

Form 表单验证失败时在 notify 消息提示中是否隐藏详细信息，默认展示，设置为 true 时隐藏

#### replaceText

可以用来实现变量替换及多语言功能，比如下面的例子

```javascript
let amisScoped = amis.embed(
  '#root',
  {
    type: 'page',
    body: {
      type: 'service',
      api: 'service/api',
    },
  },
  {},
  {
    replaceText: {
      service: 'http://localhost',
    },
    replaceTextKeys: ['api'],
  },
);
```

它会替换 `api` 里的 `service` 字符串

#### replaceTextIgnoreKeys

和前面的 `replaceText` 配合使用，某些字段会禁用文本替换，默认有以下：

```
type, name, mode, target, reload
```

如果发现有字段被意外替换了，可以通过设置这个属性来避免

#### toastPosition

Toast 提示弹出位置，默认为`'top-center'`。
支持的属性值有：

```
'top-right' | 'top-center' | 'top-left' | 'bottom-center' | 'bottom-left' | 'bottom-right' | 'center'
```
