---
title: 规范应用说明
description: 规范应用说明
type: 0
group: 💡 规范应用说明
menuName: 规范应用说明
icon:
order: 8
---

组件库`v1.71.1`版本后开始支持新规范。

## 使用方式

新规范应用场景分两种：

- 新页面使用新规范
- 老页面个别组件需要使用新规范

> **注意：**使用新规范后，使用组件内所有元素将应用新规范，内部不允许再使用辅助函数，否则会有样式冲突问题。

### 新页面使用新规范

新页面需要使用新规范，在页面最顶层组件，renderAmis的第二个参数传递standardMode: true，即可应用新规范

```javascript
import {render as renderAmis} from '@dataseed/amis';

export default () => {
  return renderAmis(
      {
        // 这里是 amis 的 Json 配置。
        type: 'page',
        title: '简单页面',
        body: '内容',
      },
      {
        // 针对整个页面应用新规范
        standardMode: true, 
      },
      env,
    )
}
```

### 老页面个别组件需要使用新规范

老页面新增个别组件需要使用新规范的情况，给该组件传递standardMode: true，即可针对该组件及内部元素应用新规范

```javascript
import {render as renderAmis} from '@dataseed/amis';

export default () => {
  return renderAmis(
      {
        // 这里是 amis 的 Json 配置。
        type: 'page',
        title: '简单页面',
        body: {
          type: "form",
          // 针对form组件及内部元素应用新规范
          standardMode: true, 
          body: {
            type: "input-text",
            name: "name",
          }
        },
      },
      {},
      env,
    )
}
```

## 常见问题

### 怎么判断新规范是否生效？

- 新规范会增加一个`standard-xxx`的class，可以通过检查页面元素类名来判断新规范是否生效

### 组件库版本升级了并且传递了standardMode: true，但是新规范相关功能无效，怎么处理这种情况？

- 新规范相关代码在`@dataseed/amis-utils`这个包里，检查下项目是否全局引入了这个包，如果没有，手动引入下

```javascript
import '@dataseed/amis-utils';
```

- 如果仍然有问题，请提供复现demo和截图，方便我们排查

