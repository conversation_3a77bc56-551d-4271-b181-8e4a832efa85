---
title: 工具库
description: amis-utils 工具库
type: 0
group: 💡 教程
menuName: 工具库
icon:
order: 8
---

工具库提供一系列工具模块，当前仅包含用于创建 env 的模块。

## 安装

```
npm install -S @dataseed/amis-utils
```

## env 创建模块

env 创建模块用于创建供 O 端项目使用的标准 env，大多数情况下，只要使用本库来生成 amis 项目所需的 [env](http://moka.dmz.dev.caijj.net/dataseeddesigndocui/#/amis/zh-CN/course/install)。本模块当前支持 6 个 env API 的创建：fetcher、isCancel、jumpTo、updateLocation、isCurrentUrl、copy。

### 使用

```ts
// 1. 生成 env

import { createENV } from '@dataseed/amis-utils';
// $http 是一个 AxiosInstance，这里仅做演示使用，并非必须使用该模块。
import $http from '@lattebank/webadmin-http';
// 假设项目是基于 umi 的，通过如下方式拿到 history 模块，这里仅做演示使用，并非必须使用该模块。
import { history } from 'umi';
// 这里仅做演示使用，并非必须使用该模块。
import { toast } from '@dataseed/amis'

// 生成可供 amis 使用的 env 模块
const env = createENV({
  axiosInstance: $http,
  history,
  toast: toast.success,
})


// 2. 在需要渲染 amis 内容的地方使用 env

import { render } from '@dataseed/amis';

render(
  {...},
  {...},
  {
    ...env,
    // 其他 env 配置项
    theme: 'antd',
    ...
  }
);
```

### createENV

createENV 类型签名如下：

```ts
/** 生成 EVN */
type CreateENV = (options: {
  /** axios 实例，当本参数非空时返回值中包含 fetcher、isCancel */
  axiosInstance?: AxiosInstance;
  /** history 实例，当本参数非空时返回值中包含 jumpTo、updateLocation */
  history?: History;
  /** 提示成功消息的方法，当本参数非空时返回值中包含 copy */
  toast?: Toast;
}) => {
  fetcher?: Fetcher;
  isCancel?: IsCancel;
  jumpTo?: JumpTo;
  updateLocation?: UpdateLocation;
  isCurrentUrl: IsCurrentUrl;
  copy?: Copy;
};

// AxiosInstance 类型请参考你的项目中使用的 axios 库中的相关类型签名

interface History {
  /** goBack、back 至少存在一个 */
  goBack?: () => void;
  /** goBack、back 至少存在一个 */
  back?: () => void;
  /** goForward、forward 至少存在一个 */
  goForward?: () => void;
  /** goForward、forward 至少存在一个 */
  forward?: () => void;
  location: {
    pathname: string;
    search: string;
    hash: string;
  };
  replace: (path: string) => void;
  push: (path: string) => void;
}

type Toast = (hint: string) => void;
```

### fetcher

> 仅在 createENV 的入参选项中包含 axiosInstance 时，createEnv 的返回值中才会有 fetcher。

接口请求器，基于 createENV 的入参选项中的 axiosInstance 实现，类型签名如下：

```ts
type Fetcher = <fetcherResult extends Record<string, any>>(options: {
  /** 接口地址 */
  url: string;
  /** 请求方法，默认值 "get" */
  method?: 'get' | 'post' | 'put' | 'patch' | 'delete';
  /** 请求数据 */
  data?: any;
  /** 响应内容类型，默认值 "json" */
  responseType?:
    | 'json'
    | 'arraybuffer'
    | 'document'
    | 'text'
    | 'stream'
    | 'blob';
  /** 请求头，会与 config 中的 headers 合并，优先级高于 config.headers */
  headers?: Record<string, string>;
  /** 其他配置，参考 Axios 的请求配置 */
  config?: AxiosRequestConfig & {
    cancelExecutor?: (cancelFn: () => void) => void;
  };
}) => Promise<fetcherResult>;
```

本 fetcher 在调用 axiosInstance 时，参数 [config](https://www.axios-http.cn/docs/req_config) 中带有自定义属性 isAmis，值为 true，在 axiosInstance 实例的拦截器中可以基于该属性做一些专门面向来自 amis 页面的请求的处理。比如，如果你使用的是 [@lattebank/webadmin-http](http://gitlab.caijj.net/JiShuBaoZhang/QianDuanJiaGou/webadmin-http)，则可通过如下方式来仅面向 amis 页面请求开启网络请求错误的自动转换：

```ts
import $http from '@lattebank/webadmin-http';

$http.interceptors.request.use(config => {
  if (config.isAmis) {
    // 开启网络请求错误的自动转换
    config.rejectedToFulfilled = true;
  }

  return config;
});
```

### isCancel

> 仅在 createENV 的入参选项中包含 axiosInstance 时，createEnv 的返回值中才会有 isCancel。

判断请求是否被取消，基于 createENV 的入参选项中的 axiosInstance 实现，类型签名如下：

```ts
type IsCancel = (value: any) => boolean;
```

### jumpTo

> 仅在 createENV 的入参选项中包含 history 时，createEnv 的返回值中才会有 jumpTo。

面向 hash 路由的实现，基于 createENV 的入参选项 history 实现，类型签名如下：

```ts
/** 路由跳转，面向 hash 路由的实现。 */
type JumpTo = (
  /** 路由后退指令或目标页面 url，如果是路由指令，则会忽略 action 参数。 */
  to: 'goBack' | 'goForward' | string,
  action?: {
    /** 路由操作类型，默认为空。
     * 如果值为 "url"，则直接使用 window.location 的相关 API 打开链接，否则使用路由模块进行跳转。
     * 如果 to 匹配正则 "/^https?:\/\//"，则 actionType 强制为 "url"。
     */
    actionType?: 'url';
    /** 是否在新标签中打开，如果 blank 为 true 则忽略 replace 参数。默认值 false。 */
    blank?: boolean;
    /** 是否替换当前路由记录，默认值 false。 */
    replace?: boolean;
  },
) => void;
```

当入参 to 不匹配 "goBack" 和 "goForward" 时，则被视为 url 进行解析，解析规则如下：

1. 如果匹配正则 "/^https?\:\\\/\\\//"，则不做任何处理。
2. 如果以 "/" 开头，则不做任何处理。
3. 如果以 "#" 开头，则使用当前页面的 pathname、search 生成最终路径。
4. 如果以 "?" 开头，则使用当前页面的 pathname 生成最终路径。
5. 否则，认为是相对路径，基于当前路径解析生成最终路径。

入参解析完成后，如果匹配正则 "/^https?\:\\\/\\\//"，则调用 window.location 相关 API 做路由跳转，否则调用 history 相关方法做路由跳转。

以上 pathname、search 均通过 history.location 获取。

看一些示例。

```ts
// 假设在调用 createENV 时使用了 umi 中的 history 模块：https://v3.umijs.org/zh-CN/api#history
// 假设当前页面的 url 为 http://www.shuhe.com/path?s=1#/amis/test?q=1#x

// 以下 jupmTo 调用的右边的注释为目标跳转页面链接
jumpTo('#y'); // http://www.shuhe.com/path?s=1#/amis/test?q=1#y
jumpTo('?p=2'); // http://www.shuhe.com/path?s=1#/amis/test?p=2
jumpTo('/page2'); // http://www.shuhe.com/path?s=1#/page2
jumpTo('page2'); // http://www.shuhe.com/path?s=1#/amis/page2
jumpTo('./page2'); // http://www.shuhe.com/path?s=1#/amis/page2
jumpTo('../page2'); // http://www.shuhe.com/path?s=1#/page2
jumpTo('../../page2'); // http://www.shuhe.com/path?s=1#/page2
jumpTo('https://www.shuhe.com/path?s=1#/amis/test?q=1#x'); // https://www.shuhe.com/path?s=1#/amis/test?q=1#x

// Action 中跳转链接配置
// 以下举两个常用示例，如果配置了其他链接，可参考上面 jumpTo 的示例推断最终解析后的目标链接
{
  "type": "page",
  "body": [
    {
      "label": "单页跳转",
      "type": "button",
      "actionType": "link",
      "link": "/page2" // 跳转后的页面链接 http://www.shuhe.com/path?s=1#/page2
    },
    {
      "label": "直接跳转",
      "type": "button",
      "actionType": "url",
      "url": "http://www.baidu.com" // 跳转后的页面链接 http://www.baidu.com
    }
  ]
}
```

### updateLocation

> 仅在 createENV 的入参选项中包含 history 时，createEnv 的返回值中才会有 updateLocation。

面向 hash 路由的实现，基于 createENV 的入参选项 history 实现，功能类似 jumpTo，类型签名如下：

```ts
/** 路由跳转，类似 JumpTo，面向 hash 路由的实现。 */
export type UpdateLocation = (
  /** 目标页面 url */
  location: string,
  /** 是否替换当前路由记录 */
  replace?: boolean,
) => void;
```

### isCurrentUrl

createENV 的返回值中肯定会有 isCurrentUrl。

判断目标链接 url 是否与当前页面链接 currentUrl 匹配，面向 hash 路由的实现，类型签名如下：

```ts
type IsCurrentUrl = (url: string) => boolean;
```

url 首先会被解析成完整的链接（以 "http" 开头），然后再与 currentUrl 进行对比，满足以下条件表示匹配：

1. 两者的 protocol、host、path、search 完全相同。
2. 基于 hash 部分解析出来的 pathname、search、hash 符合以下条件：
   1. pathname 完全相同。
   2. currentUrl 的 search 中包含 url 的 search 中的所有字段且值相同。
   3. 如果 url 中包含 hash，需与 currentUrl 的 hash 完全相同。

看一些示例。

```ts
// 假设当前页面的 url 为 http://www.shuhe.com/path?s=1#/amis/test?q=1#x

// 以下调用的返回值都是 true
isCurrentUrl('#x');
isCurrentUrl('?q=1');
isCurrentUrl('?q=1#x');
isCurrentUrl('/amis/test');
isCurrentUrl('/amis/test#x');
isCurrentUrl('/amis/test?q=1');
isCurrentUrl('/amis/test?q=1#x');
isCurrentUrl('./test');
isCurrentUrl('../amis/test');
isCurrentUrl('../../amis/test');
isCurrentUrl('http://www.shuhe.com/path?s=1#/amis/test');
isCurrentUrl('http://www.shuhe.com/path?s=1#/amis/test?q=1');
isCurrentUrl('http://www.shuhe.com/path?s=1#/amis/test?q=1#x');

// 以下调用的返回值都是 false
isCurrentUrl('#xy');
isCurrentUrl('?q=1&p=2');
isCurrentUrl('/amis');
isCurrentUrl('/amis/test2');
isCurrentUrl('./test2');
isCurrentUrl('../amis2/test');
isCurrentUrl('http://www.shuhe.com/path?s=1');
isCurrentUrl('https://www.shuhe.com/path?s=1#/amis/test?q=1#x');
```

### copy

> 仅在 createENV 的入参选项中包含 toast 时，createEnv 的返回值中才会有 copy。

复制内容，基于 createENV 入参选项中的 toast 实现，类型签名如下：

```ts
/** 复制内容 */
type Copy = (
  /** 要被复制的内容 */
  contents: string,
  options?: {
    /** 是否不要提示“内容已拷贝到剪切板”，默认 false。 */
    silent?: boolean;
    /** 内容要被复制成的格式，可选值：
     * 1. "text/html"，默认值，复制为富文本。
     * 2. "text/plain"，复制为纯文本。
     */
    format?: 'text/html' | 'text/plain';
  },
) => boolean;
```

## download 文件下载

```ts
import {download} from '@dataseed/amis-utils';
download(new Blob()); // 参数为二进制流格式
```
