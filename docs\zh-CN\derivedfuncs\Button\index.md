---
title: Button 按钮
description:
type: 0
group: ⚙ 组件
menuName: Button 按钮
icon:
order: 25
---

## getButtonList

支持版本：**1.38.0**

### 实现逻辑

当schema项类型为button时，此辅助函数为button组件提供样式支持，
如果button是link类型的是默认加padding-left:8px的class(pl-2)，其他类型是默认给button添加上margin-left:8px的class（ml-2），

### 使用范例

```json
{
  type: "page",
  body: getButtonList([
  {
   // 正常写button的schema配置
    type: "button",
    label: "button1",
  }, 
  {
    type: "button",
    level:"link",
    label: "button2",
  }])
};


```

## getButtonGroupWithBadgeSchema
支持版本：**1.59.0**

### 属性表

| 属性名 | 类型     | 默认值 | 说明                 |
| ------ | -------- | ------ | -------------------- |
| schema | `object` | {}     | button 的 schema 配置, 在button组件属性中添加gapSize属性,如果不配置，默认为'sm'小号间距 |


### 实现逻辑

当schema项类型为`button`时，并且配置了角标`badge`, 此辅助函数为Button组件提供样式。
`badge`最大支持长度为3个字符，如果超过3个直接截取前3个字符；如果是数字类或者可转换为数字的字符串类型的角标值，固定角标封顶值`overflowCount`为99；

可配置角标距离下个button的间隙`gapSize`为 `sm,md,lg`3种，分别对应8px,16px 和24px;


### 使用范例

```json
 getButtonGroupWithBadgeSchema(
        [
          {
            "type": "button",
            "label": "主按钮",
            "actionType": "url",
            "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
            "level": "primary",
            "blank": false
          },
          {
            "type": "button",
            "label": "次按钮1",
            "actionType": "url",
            "gapSize":"sm",
            "badge": {
              "mode": "text",
              "text": 5,
            },
            "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index"
          },
          {
            "type": "button",
            "label": "次按钮2",
            "actionType": "url",
            "gapSize":"lg",
            "badge": {
              "mode": "text",
              "text": 10905,
            },
            "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index"
          },
          {
            "type": "button",
            "label": "次按钮3",
            "actionType": "url",
            "gapSize":"md",
            "badge": {
              "mode": "text",
              "text": 'notice',
            },
            "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index"
          },
          {
            "type": "dropdown-button",
            "label": "更多",
            "buttons": [
              {
                "type": "button",
                "label": "次按钮4",
                "disabled": true
              },
              {
                "type": "button",
                "label": "次按钮5"
              },
              {
                "type": "button",
                "label": "次按钮6"
              }
            ]
          }
        ],
      )
```
