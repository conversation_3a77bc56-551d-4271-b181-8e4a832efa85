---
title: CRUD 增删改查
description:
type: 0
group: ⚙ 组件
menuName: CRUD 增删改查
icon:
order: 25
---

## getResizePageSchema
支持版本：**版本过早**

用于左右布局的左侧区域
### 实现逻辑
- 将一些默认样式及属性内置在方法里面。
  - asideResizor: true
  - asideMinWidth: 32
  - asideMaxWidth: 500
  - className: `bg-light`
  - asideClassName: `w-xl page-aside-region pm-bg-white min-w-xl mr-4`
  - bodyClassName: `p-0 max-h-screen overflow-y-auto`

### 属性表
传入参数定义如下：

| 属性名          | 类型                                                                | 默认值   | 说明                                                                             |
|--------------|-------------------------------------------------------------------|-------|--------------------------------------------------------------------------------|  
| schema           | `Object`         |   {}    | aside侧边栏
### 使用范例
```json
getResizePageSchema({
    "aside": {
    "type": "input-tree",
    "treeContainerClassName": "pm-input-tree-container h-full",
    "name": "tree2",
    "multiple": false,
    "searchable": true,
    "autoCheckChildren": false,
    "options": [
      {
        "label": "A",
        "value": "a"
      },
      {
        "label": "B",
        "value": "b",
        "children": [
          {
            "label": "B-1",
            "value": "b-1"
          },
          {
            "label": "B-2",
            "value": "b-2"
          },
          {
            "label": "B-3",
            "value": "b-3"
          }
        ]
      },
      {
        "label": "C",
        "value": "c"
      }
    ],
  },
  "body": {
      "type": "crud",
      "api": {},
      "bulkActions": [],
      "headerToolBar": [],
      "columns": [],
    }
  })
```
效果见`列表页-左右布局-搜索+列表`
## getRightCRUDForTreeListSchema
支持版本：**版本过早**

用于左右布局的 右侧CRUD
### 实现逻辑
- 将一些默认样式内置在方法里面。
  - className: `max-h-screen overflow-y-auto flex-1`
### 属性表
传入参数定义如下：

| 属性名          | 类型                                                                | 默认值   | 说明                                                                             |
|--------------|-------------------------------------------------------------------|-------|--------------------------------------------------------------------------------|  
| schema           | `Object`         |   {}    | 常规crud
### 使用范例
```json
{
  "type": "page",
  "body": getResizePageSchema({
    "aside": {
    "type": "input-tree",
    "treeContainerClassName": "pm-input-tree-container h-full",
    "name": "tree2",
    "multiple": false,
    "searchable": true,
    "autoCheckChildren": false,
    "options": [
      {
        "label": "A",
        "value": "a"
      },
      {
        "label": "B",
        "value": "b",
        "children": [
          {
            "label": "B-1",
            "value": "b-1"
          },
          {
            "label": "B-2",
            "value": "b-2"
          },
          {
            "label": "B-3",
            "value": "b-3"
          }
        ]
      },
      {
        "label": "C",
        "value": "c"
      }
    ],
  },
  "body": getRightCRUDForTreeListSchema({
      "api": {},
      "bulkActions": [],
      "headerToolBar": [],
      "columns": [],
    })
  })
}
```
效果见`列表页-左右布局-搜索+列表`
## getGlobalActionButtonListSchemaV2
支持版本：**版本过早**

创建一个用于**全局操作按钮+批量搜索+高级搜索**的`CRUD`组件。

### 实现逻辑
- 将一些默认样式及属性内置在方法里面。
  - className: `pm-crud`
  - syncLocation: false,
  - columnsTogglable: false,
  - autoGenerateFilter: {
    showBtnToolbar: false,
    defaultExpanded: false
  },
  - footerToolbar: [
    {
      type: 'tpl',
      tpl: '共${count}条',
      className: 'pr-2'
    },
    {
      type: 'pagination',
      layout: 'pager,perPage,go'
    }
  ];
- 全局操作按钮配置超过5个，会展示出更多下拉按钮

### 属性表
传入参数定义如下：

| 属性名          | 类型                                                                | 默认值   | 说明                                                                             |
|--------------|-------------------------------------------------------------------|-------|--------------------------------------------------------------------------------|  
| schema           | `Object`         |   {}    | 常规crud
### 使用范例
```json
{
  "type": "page",
  "body": getGlobalActionButtonListSchemaV2({
    // 正常传入crud的schema配置
    "api": {},
    "bulkActions": [],
    "headerToolBar": [],
    "columns": [],
  })
}
```
效果见`列表页-常规列表-带全局操作按钮（列表crud）`
## getBatchOperateListSchemaV2
支持版本：**版本过早**

创建一个用于**列表搜索**的`CRUD`组件。
### 实现逻辑
- 将一些默认样式及属性内置在方法里面。
  - className: `pm-crud`
  - syncLocation: false,
  - columnsTogglable: false,
  - autoGenerateFilter: {
    showBtnToolbar: false,
    defaultExpanded: false
  },
  - footerToolbar: [
  {
    type: 'tpl',
    tpl: '共${count}条',
    className: 'pr-2'
  },
  {
    type: 'pagination',
    layout: 'pager,perPage,go'
  }
];
### 属性表
传入参数定义如下：

| 属性名          | 类型                                                                | 默认值   | 说明                                                                             |
|--------------|-------------------------------------------------------------------|-------|--------------------------------------------------------------------------------|  
| schema           | `Object`         |   {}    | 常规crud
### 使用范例
```json
{
  "type": "page",
  "body": getBatchOperateListSchemaV2({
    // 正常传入crud的schema配置
    "api": {},
    "bulkActions": [],
    "headerToolBar": [],
    "columns": [],
  })
}
```
效果见`列表页-常规列表-带批量操作（列表crud）`
## getBasicListSchema
支持版本：**版本过早**

创建一个用于**列表搜索**的`CRUD`组件。
### 实现逻辑
- 将一些默认样式及属性内置在方法里面。
  - className: `pm-crud`
  - syncLocation: false,
  - columnsTogglable: false,
  - footerToolbar: [
  {
    type: 'tpl',
    tpl: '共${count}条',
    className: 'pr-2'
  },
  {
    type: 'pagination',
    layout: 'pager,perPage,go'
  }
];
### 属性表
传入参数定义如下：

| 属性名          | 类型                                                                | 默认值   | 说明                                                                             |
|--------------|-------------------------------------------------------------------|-------|--------------------------------------------------------------------------------|  
| schema           | `Object`         |   {}    | 常规crud
### 使用范例

```json
{
  "type": "page",
  "body": getBasicListSchema({
    // 正常传入crud的schema配置
    "api": {},
    "bulkActions": [],
    "headerToolBar": [],
    "columns": [],
  })
}
```
效果见`列表页-常规列表-列搜索（列表crud）`

## getCompactModeListSchemaV2
支持版本：**版本过早**

创建一个用于**紧凑模式**的`CRUD`组件。
### 实现逻辑
- 将一些默认样式及属性内置在方法里面。
  - className: `compact-mode-v2 pm-crud`
  - syncLocation: false,
  - columnsTogglable: false,
  - autoGenerateFilter: {
    showBtnToolbar: false,
    defaultExpanded: false
  },
  - footerToolbar: [
  {
    type: 'tpl',
    tpl: '共${count}条',
    className: 'pr-2'
  },
  {
    type: 'pagination',
    layout: 'pager,perPage,go'
  }
];
### 属性表
传入参数定义如下：

| 属性名          | 类型                                                                | 默认值   | 说明                                                                             |
|--------------|-------------------------------------------------------------------|-------|--------------------------------------------------------------------------------|  
| schema           | `Object`         |   {}    | 常规crud
### 使用范例
```json
{
  "type": "page",
  "body": getCompactModeListSchemaV2({
    // 正常传入crud的schema配置
    "api": {},
    "bulkActions": [],
    "headerToolBar": [],
    "columns": [],
  })
}
```
效果见`列表页-常规列表-紧凑模式（列表crud）`
## getBasicListSchemaV2
支持版本：**版本过早**

创建一个支持配置`CRUD`组件。
### 实现逻辑
- 将一些默认样式及属性内置在方法里面。
  - className: `pm-crud`
  - syncLocation: false,
  - columnsTogglable: false,
  - autoGenerateFilter: {
    showBtnToolbar: false,
    defaultExpanded: false
  },
  - footerToolbar: [
  {
    type: 'tpl',
    tpl: '共${count}条',
    className: 'pr-2'
  },
  {
    type: 'pagination',
    layout: 'pager,perPage,go'
  }
];

### 属性表
传入参数定义如下：

| 属性名          | 类型                                                                | 默认值   | 说明                                                                             |
|--------------|-------------------------------------------------------------------|-------|--------------------------------------------------------------------------------|  
| schema           | `Object`         |   {}    | 常规crud
### 使用范例

```json
{
  "type": "page",
  "body": getBasicListSchemaV2({
    // 正常传入crud的schema配置
    "api": {},
    "bulkActions": [],
    "headerToolBar": [],
    "columns": [],
  })
}
```
效果见`列表页-常规列表-基础列表（列表crud）`
## getWithoutMarginsCRUDSchemaV2

支持版本：**1.49.1**

创建一个支持配置是否有padding或margin的`CRUD`组件，多用于`CRUD`组件和其它自带padding或margin属性的组件组合使用时。

### 属性表

- `schema`: crud功能的schema配置
- `noPadding`: 是否展示内边距，默认为`true`
- `noMargin`: 是否展示外边距，默认为`false`

### 实现逻辑

`CRUD`组件的margin仅存在于下方，因此noMargin只影响`CRUD`组件的下外边距。<br />
`CRUD`的左右padding在noPadding模式下均去除，而上padding受`schema`配置影响，从上到下的顺序依次为`topToolBar`，`filter`，`headerFilter`，`headerToolBar`及`title`，每当找到其中一项，去除对应场景下的上padding。

### 使用范例

```json
{
  "type": "page",
  "body": getWithoutMarginsCRUDSchemaV2({
    // 正常传入crud的schema配置
    "api": {},
    "bulkActions": [],
    "headerToolBar": [],
    "columns": [],
  }, true, true)
}
```

## getCRUDHeader

支持版本：**1.63.0**

当在`CRUD`组件内同时使用headerToolbar和headerFilter时，目前是上下结构，需要将headerFilter调整到右侧。

### 属性表
传入参数和crud组件传入的schmea相同，无需特殊处理

| 属性名          | 类型                                                                | 默认值   | 说明                                                                             |
|--------------|-------------------------------------------------------------------|-------|--------------------------------------------------------------------------------|  
| schema           | `Object`         |   {}    |

### 实现逻辑
当没有传入`headerToolbar`时，传入和输出的schema没有区别。当有`headerToolbar`时，通过`className`的样式处理将`headerFilter`移到右侧。

### 使用范例

```json
{
  "type": "page",
  "body": {
    "type": "crud",
    "headerToobar": [
      "bulkActions"
    ],
    "headerFilter": {
      "body": [
        {
          "type": "button-group-select",
          "name": "button-group-select",
          "align": "right",
          "options": [
            {
              "value": "all",
              "label": "查询全部"
            },
            {
              "value": "forme",
              "label": "待我审核"
            },
            {
              "value": "reject",
              "label": "已拒绝"
            }
          ],
          "onEvent": {
            "change": {
              "actions": [
                {
                  "actionType": "query",
                  "componentId": "custom-crud-id",
                  "args": {
                    "queryParams": {
                      "button-group-select":'${button-group-select}'
                    }
                  }
                }
              ]
            }
          }
        }
      ]
    },
    "bulkActions": [
      {
        "label": "批量删除",
        "actionType": "ajax",
        "api": "delete:/api/mock2/sample/${ids|raw}",
        "confirmText": "确定要批量删除?"
      },
      {
        "label": "批量修改",
        "actionType": "dialog",
        "dialog": {
          "title": "批量编辑",
          "showCloseButton": false,
          "body": {
            "type": "form",
            "api": "/api/mock2/sample/bulkUpdate2",
            "body": [
              {
                "type": "hidden",
                "name": "ids"
              },
              {
                "type": "input-text",
                "name": "engine",
                "label": "Engine"
              }
            ]
          }
        }
      }
    ],
    "columns": [
      {
        "name": "id",
        "label": "ID",
        "searchable": {
          "type": "input-text",
          "name": "id",
          "label": "主键",
          "placeholder": "输入id"
        }
      },
      {
        "name": "browser",
        "label": "Browser",
        "searchable": {
          "type": "input-text",
          "name": "browser",
          "label": "Browser",
          "placeholder": "输入Browser"
        }
      }
    ]
  }
}
```

效果可见示例中的`列表页-常规列表-批量操作+分段器(非追随)搜索`

## getWithoutMarginsCRUDSchema(废弃)

此辅助函数由于未考虑到CRUD组件内多场景，已**不推荐使用**，请使用`getWithoutMarginsCRUDSchemaV2`替代

