---
title: Collapse 折叠器
description:
type: 0
group: ⚙ 组件
menuName: Collapse 折叠器
icon:
order: 25
---

## generateNoPaddingCollapse

支持版本：**1.56.0**

创建一个支持配置是否有padding的`Collapse 折叠器`组件，用于`Collapse 折叠器`组件。

### 属性表
传入参数定义如下：

| 属性名          | 类型                                                                | 默认值   | 说明                                                                             |
|--------------|-------------------------------------------------------------------|-------|--------------------------------------------------------------------------------|
| noPadding        | `object`                                                          |   {left: true, top:true, right:true,bottom:true} 四边内边距都为0   | 上下左右配置   
| ...           | `SchemaNode`         |       | amis布局schema  



### 实现逻辑

`Collapse`组件包括`header`,`content`两部分，默认都有对应内边距。 如果要实现四边`nopadding`状态，
1. 去除`topPadding`， 需要去除`header`的`topPadding`;
2. 去除`bottomPadding` 
   - 如果是收起状态，需要去除`header`的`paddingBottom` ; 
   - 如果是展开状态，需要去除`content`的`bottomPadding`. 
3. 去除`leftPadding`,`rightPadding`,需要同时去除`head`和`content`的`leftPadding`和`rightPadding`. 

### 使用范例

```json
{
  "type": "page",
  "body": generateNoPaddingCollapse({
    noPadding:{
      left:false, // 设置左内边距不为0
    },
      "activeKey": [
        "1"
      ],
      "body": [
        {
          "type": "collapse",
          "key": "1",
          "header": "标题1",
          "body": "这里是内容1"
        },
        {
          "type": "collapse",
          "key": "2",
          "header": "标题2",
          "body": "这里是内容2"
        },
        {
          "type": "collapse",
          "key": "3",
          "header": "标题3",
          "body": "这里是内容3"
        }
      ]
  })

}
```

## getCollapsableGroupPanelForWhiteBgSchemaV2

支持版本：**1.60.0**

创建一个支持配置的`Collapse 折叠器`组件，用于`Collapse 折叠器`组件。

### 属性表
传入参数定义如下：

| 属性名          | 类型                                                                | 默认值   | 说明                                                                             |
|--------------|-------------------------------------------------------------------|-------|--------------------------------------------------------------------------------|  
| schema           | `Array`         |   []    | amis布局schema  



### 实现逻辑

`基于对于getCollapsableGroupPanelForWhiteBgSchema函数去除了className的p-2`. 

### 使用范例

```json
{
  "type": "page",
  "body": {
      "type": "collapse-group",
      "activeKey": [
        "1"
      ],
      "body": getCollapsableGroupPanelForWhiteBgSchemaV2([
        {
          "type": "collapse",
          "key": "1",
          "header": "标题1",
          "body": "这里是内容1"
        },
        {
          "type": "collapse",
          "key": "2",
          "header": "标题2",
          "body": "这里是内容2"
        },
        {
          "type": "collapse",
          "key": "3",
          "header": "标题3",
          "body": "这里是内容3"
        }
      ])
  })

}
```
效果见`详情页-弹窗-tabs模式带分组（可折叠分组）`

## getCollapsableGroupPanelForWhiteBgSchema(废弃)

此辅助函数由于未考虑到collapse-group组件内边距问题，已**不推荐使用**，请使用`getCollapsableGroupPanelForWhiteBgSchemaV2`替代
