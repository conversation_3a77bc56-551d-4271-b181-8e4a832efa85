---
title: generateBackground
description: 设置BackgroundColor
type: 0
group: ⚙ 组件
menuName: generateBackground
icon:
order: 25
---

### 属性表

| 属性名  | 类型             | 默认值   | 说明              | 版本      
| ------ | --------------- | ------  | ----------------  | --------- |
| schema | `SchemaNode`    | {}      | 需要设置样式的组件   | 
| config |  `IBackground`       | {}      | 需要设置的样式配置   | 


####  IBackground 属性表

| 属性名  | 类型             | 默认值   | 说明              | 版本      
| ------ | --------------- | ------  | ----------------  | --------- |
| color  | `参考可用枚举`    | '' | 设置背景颜色 | 

### 实现逻辑

会将传入的第一个参数视为一个整体，根据第二个参数的配置展示对应的样式效果。 配置枚举项和样式的对应规则如下，如传入枚举不在范围内，不会生效且会提示警告信息

#### 可用枚举

| 属性名        | 示例效果         |         
| ------       | --------------- | 
| success      |  <div class="w-24 h-6 bg-success"></div>   |
| error        |  <div class="w-24 h-6 bg-danger"></div>    |
| info         |  <div class="w-24 h-6 bg-info"></div>   |
| warning      |  <div class="w-24 h-6 bg-warning"></div>   |
| normal       |  <div class="w-24 h-6 bg-none"></div>   |
| transparent  |  <div class="w-24 h-6 bg-transparent"></div> |
| light        |  <div class="w-24 h-6 bg-light"></div>   |
| white        |  <div class="w-24 h-6 bg-white"></div>     |
| black        |  <div class="w-24 h-6 bg-black"></div>    |
| lightBlue    |  <div class="w-24 h-6 bg-blue-50"></div>    |

### 使用范例

#### 在generateStyle中使用

```json
{
  "type": "page",
  "body": generateStyle({
    "type": "container",
    "body": "内容",
  }, {
    "className": {
      "background":{
        "color":"white",
      },
      "spacing":{
        "margin":"md"
      }
    },
    "bodyClassName": {
      "background":{
        "color":"light",
      }
    }
  })
}
```

#### 单独使用

```json
{
  "type": "page",
  "body": generateBackground({
    "type": "wrapper",
    "body": "内容",
  }, {
    "className": {
        "color":"light",
    },
  })
}
```
