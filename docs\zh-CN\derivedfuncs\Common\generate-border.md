---
title: generateBorder
description: 设置Border相关样式
type: 0
group: ⚙ 组件
menuName: generateBorder
icon:
order: 25
---

### 属性表

| 属性名  | 类型             | 默认值   | 说明              | 版本      
| ------ | --------------- | ------  | ----------------  | --------- |
| schema | `SchemaNode`    | {}      | 需要设置样式的组件   | 
| config |  `IBorder`       | {}      | 需要设置的样式配置   | 


####  IBorder 属性表

| 属性名  | 类型             | 默认值   | 说明              | 版本      
| ------ | --------------- | ------  | ----------------  | --------- |
| width  | `Object ｜ String`    | {} | 设置边框宽度 | 
| radius | `Object ｜ String`    | {} | 设置圆角 | 
| color  | `String`    | '' | 设置边框颜色 | 
| style  | `String`    | '' | 设置边框样式 | 

### 实现逻辑

会将传入的第一个参数视为一个整体，根据第二个参数的配置展示对应的样式效果。 配置枚举项和样式的对应规则如下，如传入枚举不在范围内，不会生效且会提示警告信息

#### 可用枚举（width）

| 属性名  | 对应值     |         
| ------ | --------- | 
| none   | `0`       |
| xs     | `1px`     |
| sm     | `2px`     |
| md     | `4px`     |
| lg     | `8px`     |

#### 可用枚举（color）

| 属性名        | 示例效果             |         
| ------       | ---------------     | 
| success      | <div class="w-24 h-6 border-success border border-solid"></div>   |
| error        | <div class="w-24 h-6 border-danger border border-solid"></div>   |
| info         | <div class="w-24 h-6 border-info border border-solid"></div>    |
| warning      | <div class="w-24 h-6 border-warning border border-solid"></div>   |
| normal       | <div class="w-24 h-6 border-none border border-solid"></div>   |
| dark         | <div class="w-24 h-6 border-dark border border-solid"></div>     |
| light        | <div class="w-24 h-6 border-light border border-solid"></div>    |
| white        | <div class="w-24 h-6 border-white border border-solid"></div>   |
| black        | <div class="w-24 h-6 border-black border border-solid"></div>   |
| lightGray    | <div class="w-24 h-6 border-gray-200 border border-solid"></div>   |

#### 可用枚举（style）

| 属性名   | 示例效果           |         
| ------  | ---------       | 
| solid   | <div class="w-24 h-6 border-success border border-solid"></div>  |
| dashed  | <div class="w-24 h-6 border-success border border-dashed"></div> |
| dotted  | <div class="w-24 h-6 border-success border border-dotted"></div> |
| double  | <div class="w-24 h-6 border-success border border-double"></div> |
| none    | <div class="w-24 h-6 border-success border border-none"></div>  |

#### 可用枚举（radius）


##### key
| 属性名   | 简介          |         
| ------  | ---------       | 
| top   |  左上圆角和右上圆角  |
| bottom  | 左下圆角和右下圆角 |
| left  | 左上圆角和左下圆角 |
| right  | 右上圆角和右下圆角  |
| topRight    | 右上圆角   |
| topLeft  | 左上圆角 |
| bottomRight  |  右下圆角 |
| bottomLeft    |  左下圆角   |

##### value
| 属性名   | 对应类名          |         
| ------  | ---------       | 
| none   | `rounded-none`  |
| sm  | `rounded-sm` |
| md  | `rounded-md` |
| lg  | `rounded-lg` |
| xl    | `rounded-xl`   |
| 2xl  | `rounded-2xl` |
| 3xl  | `rounded-3xl` |
| full  | `rounded-full` |
| base    | `rounded`   |

### 使用范例

#### 在generateStyle中使用

```json
{
  "type": "page",
  "body": generateStyle({
    "type": "container",
    "body": "内容",
  }, {
    "className": {
      "border": {
        "width": {
          "top": "xs",
          "bottom": "md",
          "left": "lg",
          "right": "none"
        },
        "radius": {
          "top": "xs",
          "bottom": "md",
          "left": "lg",
          "right": "none"
        },
        "color": "warning",
        "style": "double"
      }
    }
  })
}
```

#### 单独使用

```json
{
  "type": "page",
  "body": generateBorder({
    "type": "wrapper",
    "body": "内容",
  }, {
    "className": {
      "width": {
        "top": "xs",
        "bottom": "md",
        "left": "lg",
        "right": "none"
      },
      "radius": {
        "top": "xs",
        "bottom": "md",
        "left": "lg",
        "right": "none"
      },
      "color": "warning",
      "style": "double"
    },
  })
}
```
