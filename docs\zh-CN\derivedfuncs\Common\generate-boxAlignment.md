---
title: generateBoxAlignment
description: 设置justify-content、justify-items、justify-self、align-content、align-items、align-self
type: 0
group: ⚙ 组件
menuName: generateBoxAlignment
icon:
order: 25
---

### 属性表

| 属性名  | 类型             | 默认值   | 说明              | 版本      
| ------ | --------------- | ------  | ----------------  | --------- |
| schema | `SchemaNode`    | {}      | 需要设置样式的组件   | 
| config |  `IBoxAlignment`       | {}      | 需要设置的样式配置   | 

####  IBoxAlignment 属性表

| 属性名  | 类型             | 默认值   | 说明              | 版本      
| ------ | --------------- | ------  | ----------------  | --------- |
| justifyContent  | `参考可用枚举`    | '' | css`justify-content`属性 | 
| justifyItems  | `参考可用枚举`    | '' | css`justify-items`属性 | 
| justifySelf  | `参考可用枚举`    | '' | css`justify-self`属性 | 
| alignContent  | `参考可用枚举`    | '' | css`align-content`属性 | 
| alignItems  | `参考可用枚举`    | '' | css`align-content`属性 | 
| alignSelf  | `参考可用枚举`    | '' | css`align-content`属性 | 

### 实现逻辑

会将传入的第一个参数视为一个整体，根据第二个参数的配置展示对应的样式效果。 配置枚举项和样式的对应规则如下，如传入枚举不在范围内，不会生效且会提示警告信息

#### 可用枚举（justifyContent）

| 属性名        | 对应样式         |         
| ------       | --------------- | 
| start      | `justify-content: flex-start`    |
| end        | `justify-content: flex-end`     |
| center         | `justify-content: center`       |
| between      | `justify-content: space-between`    |
| around       | `justify-content: space-around`       |
| evenly  | `justify-content: space-evenly`|

#### 可用枚举（justifyItems）

| 属性名        | 对应样式         |         
| ------       | --------------- | 
| start      | `justify-items: start`    |
| end        | `justify-items: end`     |
| center         | `justify-items: center`       |
| stretch      | `justify-items: stretch`    |
| auto       | `justify-items: auto`       |

#### 可用枚举（justifySelf）

| 属性名        | 对应样式         |         
| ------       | --------------- | 
| start      | `justify-self: start`    |
| end        | `justify-self: end`     |
| center         | `justify-self: center`       |
| stretch      | `justify-self: stretch`    |
| auto       | `justify-self: auto`       |

#### 可用枚举（alignContent）

| 属性名        | 对应样式         |         
| ------       | --------------- | 
| start      | `align-content: flex-start`    |
| end        | `align-content: flex-end`     |
| center         | `align-content: center`       |
| between      | `align-content: space-between`    |
| around       | `align-content: space-around`       |
| evenly  | `align-content: space-evenly`|

#### 可用枚举（alignItems）

| 属性名        | 对应样式         |         
| ------       | --------------- | 
| start      | `	align-items: flex-start`    |
| end        | `align-items: flex-end`     |
| center         | `align-items: center`       |
| stretch      | `align-items: stretch`    |
| baseline       | `align-items: baseline`       |

#### 可用枚举（alignSelf）

| 属性名        | 对应样式         |         
| ------       | --------------- | 
| start      | `align-self: start`    |
| end        | `align-self: end`     |
| center         | `align-self: center`       |
| stretch      | `align-self: stretch`    |
| auto       | `align-self: auto`       |


### 使用范例

#### 在generateStyle中使用

```json
generateStyle(
  {
    "type": "page",
    "body": "内容"
  },
  {
    "className": {
      "boxAlignment": {
        "justifyContent": "start",
        "justifyItems": "auto",
        "justifySelf": "start",
        "alignContent": "center",
        "alignItems": "center",
        "alignSelf": "stretch"
      }
    }
  }
)
```

#### 单独使用

```json
generateBoxAlignment(
  {
    "type": "page",
    "body": "内容"
  },
  {
    "className": {
      "justifyContent": "start",
      "justifyItems": "auto",
      "justifySelf": "start",
      "alignContent": "center",
      "alignItems": "center",
      "alignSelf": "stretch"
    }
  }
)
```
