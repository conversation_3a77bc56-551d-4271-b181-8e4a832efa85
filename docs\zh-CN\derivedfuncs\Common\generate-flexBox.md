---
title: generateFlexBox
description: 设置flex-direction、flex-wrap、flex、flex-grow、flex-shrink
type: 0
group: ⚙ 组件
menuName: generateFlexBox
icon:
order: 25
---

### 属性表

| 属性名  | 类型             | 默认值   | 说明              | 版本      
| ------ | --------------- | ------  | ----------------  | --------- |
| schema | `SchemaNode`    | {}      | 需要设置样式的组件   | 
| config |  `IFlexBox`       | {}      | 需要设置的样式配置   | 


####  IFlexBox 属性表

| 属性名  | 类型             | 默认值   | 说明              | 版本      
| ------ | --------------- | ------  | ----------------  | --------- |
| direction  | `参考可用枚举`    | '' | css`flex-direction`属性 | 
| wrap  | `参考可用枚举`    | '' | css`	flex-wrap`属性 | 
| flex  | `参考可用枚举`    | '' | css`flex`属性 | 
| grow  | `参考可用枚举`    | '' | css`	flex-grow`属性 | 
| shrink  | `参考可用枚举`    | '' | css`flex-shrink`属性 | 

### 实现逻辑

会将传入的第一个参数视为一个整体，根据第二个参数的配置展示对应的样式效果。 配置枚举项和样式的对应规则如下，如传入枚举不在范围内，不会生效且会提示警告信息

#### 可用枚举（direction）

| 属性名        | 对应样式       |         
| ------       | --------------- | 
| row      | `flex-direction: row`    |
| rowReverse        | `flex-direction: row-reverse`     |
| column         | `flex-direction: column`       |
| columnReverse      | `flex-direction: column-reverse`    |

#### 可用枚举（wrap）

| 属性名        | 对应样式       |         
| ------       | --------------- | 
| nowrap      | `flex-wrap: nowrap`    |
| wrap        | `flex-wrap: wrap`     |
| wrapReverse         | `flex-wrap: wrap-reverse`       |

#### 可用枚举（flex）

| 属性名        | 对应样式       |         
| ------       | --------------- | 
| auto      | `flex: 1 1 auto`    |
| initial        | `flex: 0 1 auto`     |
| normal         | `flex: none`       |
| 1         | `flex: 1 1 0%`       |

#### 可用枚举（grow）

| 属性名        | 对应样式       |         
| ------       | --------------- | 
| 0      | `flex-grow: 0`    |
| 1        | `flex-grow: 1`     |

#### 可用枚举（shrink）

| 属性名        | 对应样式       |         
| ------       | --------------- | 
| 0      | `flex-shrink: 0`    |
| 1        | `flex-shrink: 1`     |



### 使用范例

#### 在generateStyle中使用

```json
generateStyle({
  "type": "page",
  "body": '内容'
},
  {
    "className": {
      "flexBox": {
        "direction": "row",
        "wrap": "wrapReverse",
        "shrink": 1
      },
      "background": {
        "color": "white",
      }
    },
    "bodyClassName": {
      "flexBox": {
        "direction": "rowReverse",
        "flex": "1",
        "grow": 0
      }
    }
  }
)
```

#### 单独使用

```json
generateFlexBox(
  {
    "type": "page",
    "body": '内容'
  },
  {
    "className": {
      "direction": "row",
      "wrap": "wrapReverse",
      "shrink": 1
    },
    "bodyClassName": {
      "direction": "rowReverse",
      "flex": "1",
      "grow": 0
    }
  }
)
```
