---
title: generateLayout
description: 设置box-border、display、float、overflow
type: 0
group: ⚙ 组件
menuName: generateLayout
icon:
order: 25
---

### 属性表

| 属性名  | 类型             | 默认值   | 说明              | 版本      
| ------ | --------------- | ------  | ----------------  | --------- |
| schema | `SchemaNode`    | {}      | 需要设置样式的组件   | 
| config |  `ILayout`       | {}      | 需要设置的样式配置   | 


####  ILayout 属性表

| 属性名  | 类型             | 默认值   | 说明              | 版本      
| ------ | --------------- | ------  | ----------------  | --------- |
| boxSizing  | `参考可用枚举`    |  | Box Sizing 相关样式 | 
| display  | `参考可用枚举`    |  | 布局相关样式 | 
| floats  | `参考可用枚举`    |  | 浮动相关样式 | 
| overflow  | `参考可用枚举`    |  | 内容超出相关样式 | 

### 实现逻辑

会将传入的第一个参数视为一个整体，根据第二个参数的配置展示对应的样式效果。 配置枚举项和样式的对应规则如下，如传入枚举不在范围内，不会生效且会提示警告信息

#### 可用枚举（boxSizing）

| 属性名        | 对应样式         |         
| ------       | --------------- | 
| border      | `box-sizing: border-box;`    |
| content        | `box-sizing: content-box;`     |

#### 可用枚举（display）

| 属性名        | 对应样式         |         
| ------       | --------------- | 
| block      | `display: block;`    |
| inline-block        | `display: inline-block;`     |
| inline      | `display: inline;`    |
| flex        | `	display: flex;`     |
| inline-flex      | `display: inline-flex;`    |

#### 可用枚举（floats）

| 属性名        | 对应样式         |         
| ------       | --------------- | 
| right      | `float: right;`    |
| left       | `float: left;`     |
| none      | `float: none;`    |

#### 可用枚举（overflow）

overflow 可以设置为`String`或者`Object`类型,当设置为`Object`时，支持单独设置x或者y轴

| 属性名        | 对应样式         |         
| ------       | --------------- | 
| auto      | `overflow: auto;`    |
| hidden       | `overflow: hidden;`     |
| visible      | `overflow: visible;`    |
| scroll      | `overflow: scroll;`    |



### 使用范例

#### 在generateStyle中使用

```json
generateStyle(
  {
    "type": "page",
    "body": "内容"
  },
  {
    "className": {
      "layout": {
        "boxSizing": "content",
        "display": "inline-flex",
        "floats": "left",
        "overflow": {
          "x": "auto",
          "y": "visible"
        }
      },
      "background": {
        "color": "black"
      },
    },
  }
)
```

#### 单独使用

```json
generateLayout(
  {
    "type": "page",
    "body": "内容"
  },
  {
    "className": {
      "boxSizing": "content",
      "display": "inline-flex",
      "floats": "left",
      "overflow": {
        "x": "auto",
        "y": "visible"
      }
    },
  }
)
```
