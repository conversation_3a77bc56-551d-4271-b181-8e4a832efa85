---
title: generateSizings
description: 设置BackgroundColor
type: 0
group: ⚙ 组件
menuName: generateSizings
icon:
order: 25
---

### 属性表

| 属性名  | 类型             | 默认值   | 说明              | 版本      
| ------ | --------------- | ------  | ----------------  | --------- |
| schema | `SchemaNode`    | {}      | 需要设置样式的组件   | 
| config |  `ISizings`       | {}      | 需要设置的样式配置   | 


####  ISizings 属性表

| 属性名  | 类型             | 默认值   | 说明              | 版本      
| ------ | --------------- | ------  | ----------------  | --------- |
| height  | `参考可用枚举`    | '' | 设置高度 | 
| width  | `参考可用枚举`    | '' | 设置宽度 | 

### 实现逻辑

会将传入的第一个参数视为一个整体，根据第二个参数的配置展示对应的样式效果。 配置枚举项和样式的对应规则如下，如传入枚举不在范围内，不会生效且会提示警告信息

#### 可用枚举(height)

| 属性名        | 对应值         |         
| ------       | --------------- | 
| none      | `height: 0`    |
| full        | `height: 100%`     |
| screen         | `height: 100vh`       |
| minFull      | `min-height: 100%`    |
| minScreen       | `min-height: 100vh`       |
| maxFull  | `max-height: 100%`|
| maxScreen        | `max-height: 100vh`      |
| maxNone        | `max-height: none`      |
| half        | `height: 50%`      |

备注：为方便控制，可以直接传递以 `h-`、`min-`、`max-` 开头的值，可参考 <a href="/dataseeddesigndocui/#/amis/zh-CN/style/sizing/width">高度</a>


#### 可用枚举(width)

| 属性名        | 对应值         |         
| ------       | --------------- | 
| none      | `width: 0`    |
| full        | `width: 100%`     |
| screen         | `width: 100vh`       |
| minFull      | `min-width: 100%`    |
| minScreen       | `min-width: 100vh`       |
| maxFull  | `max-width: 100%`|
| maxScreen        | `max-width: 100vh`      |
| half        | `width: 50%`      |

备注：为方便控制，可以直接传递以 `w-`、`min-`、`max-` 开头的值 <a href="/dataseeddesigndocui/#/amis/zh-CN/style/sizing/height">宽度</a>

### 使用范例

#### 在generateStyle中使用

```json
{
  "type": "page",
  "body": generateStyle({
    "type": "container",
    "body": "内容",
  }, {
    "className": {
      "sizing": {
        "height": "screen",
        "width": "screen"
      },
    },
  })
}
```

#### 单独使用

```json
{
  "type": "page",
  "body": generateSizings({
    "type": "wrapper",
    "body": "内容",
  }, {
    "className": {
      "height": "full",
      "width": "full"
    },
  })
}
```
