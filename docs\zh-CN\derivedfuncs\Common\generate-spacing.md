---
title: generateSpacing
description: 设置margin和padding样式
type: 0
group: ⚙ 组件
menuName: generateSpacing
icon:
order: 25
---

### 属性表

| 属性名  | 类型             | 默认值   | 说明              | 版本      
| ------ | --------------- | ------  | ----------------  | --------- |
| schema | `SchemaNode`    | {}      | 需要设置样式的组件   | 
| config |  `ISpacing`       | {}      | 需要设置的样式配置   | 


####  ISpacing 属性表

| 属性名  | 类型             | 默认值   | 说明              | 版本      
| ------ | --------------- | ------  | ----------------  | --------- |
| margin  | `Object ｜ String`    | {} | 外间距配置 | 
| padding | `Object ｜ String`    | {} | 内间距配置 | 

### 实现逻辑

会将传入的第一个参数视为一个整体，根据第二个参数的配置展示对应的样式效果，默认均为 none。 配置枚举项和样式的对应规则如下，如传入枚举不在范围内，不会生效且会提示警告信息

#### 可用枚举

| 属性名  | 对应值     |         
| ------ | --------- | 
| none   | `0`       |
| xs     | `4px`     |
| sm     | `8px`     |
| md     | `16px`    |
| lg     | `24px`    |

### 使用范例

#### 在generateStyle中使用

```json
{
  "type": "page",
  "body": generateStyle({
    "type": "container",
    "body": "内容",
  }, {
    "className": {
      "spacing":{
        "margin":{
          "top": "xs",
          "bottom": "md",
          "left": "lg",
          "right": "none"
        },
        "padding":{
          "top": "xs",
          "bottom": "md",
          "left": "lg",
          "right": "none"
        }
      }
    },
    "bodyClassName": {
      "spacing":{
        "margin":"xs",
        "padding":"xs"
      }
    }
  })
}
```

#### 单独使用

```json
{
  "type": "page",
  "body": generateSpacing({
    "type": "wrapper",
    "body": "内容",
  }, {
    "className": {
      "margin":{
        "top": "xs",
        "bottom": "md",
        "left": "lg",
        "right": "none"
      },
      "padding":{
        "top": "xs",
        "bottom": "md",
        "left": "lg",
        "right": "none"
      }
    },
  })
}
```
