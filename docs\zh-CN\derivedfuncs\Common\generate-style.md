---
title: generateStyle
description: 样式设置函数
type: 0
group: ⚙ 组件
menuName: generateStyle
icon:
order: 25
---


支持版本： **待确定**

该函数会自动帮你调用指定的多个generate函数，避免同时需要使用多个generate函数而出现的深层嵌套

### 属性表

| 属性名  | 类型             | 默认值   | 说明              | 版本      
| ------ | --------------- | ------  | ----------------  | --------- |
| schema | `SchemaNode`    | {}      | 需要设置样式的组件   | 
| config |  `IConfig`       | {}      | 需要设置的样式配置   | 

#### IConfig 属性表

| 属性名  | 类型             | 默认值   | 说明              | 版本      
| ------ | --------------- | ------  | ----------------  | --------- |
| [key]  |  `String`   | 无      | 当前config对象的key，为当前组件样式需要加到那个class上面，例如：className、inputClassName等   | 
| [value] |  `参考对应的generate函数`       | {}      | 需要设置的样式配置   | 

#### 实现逻辑

会将传入的第一个参数视为一个整体，根据第二个参数的配置展示对应的样式效果。首先会根据用户传递的Config进行分组，然后循环遍历所有分组，根据分组的不同从而调用不同的generate函数，生成最终的样式效果

#### 使用范例

```json
{
  "type": "page",
  "body": generateStyle({
    "type": "wrapper",
    "body": "内容",
  }, {
    "className": {},
    "inputClassName": {}
  })
}
```

