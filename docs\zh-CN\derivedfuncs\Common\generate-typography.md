---
title: generateTypography
description: 设置 font-size、font-weight、text-align、color、	text-decoration、vertical-align、white-space、overflow-wrap、word-break、line-height
type: 0
group: ⚙ 组件
menuName: generateTypography
icon:
order: 25
---

### 属性表

| 属性名  | 类型             | 默认值   | 说明              | 版本      
| ------ | --------------- | ------  | ----------------  | --------- |
| schema | `SchemaNode`    | {}      | 需要设置样式的组件   | 
| config |  `ITypography`       | {}      | 需要设置的样式配置   | 


####  ITypography 属性表

| 属性名  | 类型             | 默认值   | 说明              | 版本      
| ------ | --------------- | ------  | ----------------  | --------- |
| size  | `参考可用枚举`    | '' | 字体大小 | 
| weight  | `参考可用枚举`    | '' | 字体加粗 | 
| align  | `参考可用枚举`    | '' | 块元素或者单元格框的行内内容的水平对齐 | 
| color  | `参考可用枚举`    | '' | 文本颜色 | 
| lineHeight  | `参考可用枚举`    | '' | 行高 | 
| decoration  | `参考可用枚举`    | '' | 文本上的装饰性线条的外观 | 
| verticalAlign  | `参考可用枚举`    | '' | 行内元素或表格单元格元素的垂直对齐方式 | 
| whiteSpace  | `参考可用枚举`    | '' | css`white-space`属性 | 
| wordBreak  | `参考可用枚举`    | '' | css`word-break`属性 | 

### 实现逻辑

会将传入的第一个参数视为一个整体，根据第二个参数的配置展示对应的样式效果。 配置枚举项和样式的对应规则如下，如传入枚举不在范围内，不会生效且会提示警告信息

#### 可用枚举(size)
| 属性名        | 对应样式         |         
| ------       | --------------- | 
| xs      | `font-size: 0.75rem; line-height: 1rem`    |
| sm        | `	font-size: 0.875rem; line-height: 1.25rem`     |
| base  | `font-size: 1rem; line-height: 1.5rem`|
| md         | `font-size: 1.125rem; line-height: 1.75rem`       |
| lg      | `	font-size: 1.25rem; line-height: 1.75rem`    |
| xl       | `font-size: 1.5rem; line-height: 2rem`       |

#### 可用枚举(weight)
| 属性名        | 示例效果         |         
| ------       | --------------- | 
| normal      | <span class="font-normal">不加粗</span>   |
| bold        | <span class="font-bold">加粗</span>    |

#### 可用枚举(align)
| 属性名        | 对应样式         |         
| ------       | --------------- | 
| left      | `text-align: left`  |
| center        | `text-align: center`   |
| right      | `text-align: right`   |
| justify        | `text-align: justify`   |

#### 可用枚举(color)
| 属性名        | 示例效果        |         
| ------       | --------------- | 
| success      | <span class="text-success">AaBbCcDd</span>  |
| error        | <span class="text-danger">AaBbCcDd</span>   |
| info      | <span class="text-info">AaBbCcDd</span>   |
| warning        | <span class="text-warning">AaBbCcDd</span>   |
| normal      | <span class="text-normal">AaBbCcDd</span>  |
| disable        | <span class="text-muted">AaBbCcDd</span>   |
| black      | <span class="text-black">AaBbCcDd</span>   |
| white        | <span class="text-white">AaBbCcDd</span>   |
| secondary        | <span class="text-secondary">AaBbCcDd</span>   |
| subTitleColor        | <span class="text-gray-500">小标题</span>   |

#### 可用枚举(lineHeight)
| 属性名        | 对应样式         |         
| ------       | --------------- | 
| xs           | `line-height: 0.75rem`    |
| sm           | `line-height: 1rem`     |
| md           | `line-height: 1.25rem`       |
| base         | `line-height: 1.5rem`|
| lg           | `line-height: 1.75rem`    |
| xl           | `line-height: 2rem`       |

#### 可用枚举(decoration)
| 属性名        | 简介         |         
| ------       | --------------- | 
| underline      | <span class="underline">下划线</span>  |
| lineThrough        | <span class="line-through">删除线</span>   |
| normal      | <span class="no-underline">无</span>   |

#### 可用枚举(verticalAlign)
| 属性名        | 对应样式         |         
| ------       | --------------- | 
| top      |  `vertical-align: top`  |
| middle        | `vertical-align: middle`   |
| bottom      | `vertical-align: bottom`   |
| baseline      | `vertical-align: baseline`  |
| textTop        | `vertical-align: text-top`   |
| textBottom      | `vertical-align: text-bottom`   |
#### 可用枚举(whiteSpace)
| 属性名        | 对应样式         |         
| ------       | --------------- | 
| normal      |  `align: normal`  |
| nowrap        | `white-space: nowrap`   |
| pre      | `white-space: pre`   |
| preLine      | `	white-space: pre-line`  |
| preWrap        | `white-space: pre-wrap`   |

#### 可用枚举(wordBreak)
| 属性名        | 对应样式         |         
| ------       | --------------- | 
| normal      |  `overflow-wrap: normal; word-break: normal`  |
| words        | `overflow-wrap: break-word`   |
| all      | `word-break: break-all`   |


### 使用范例

#### 在generateStyle中使用

```json
generateStyle(
  {
    "type": "page",
    "body": '内容',
  },
  {
    "className": {
      "background": {
        "color": 'black'
      },
      "typography": {
        "size": "xl",
        "weight": "bold",
        "align": "center",
        "color": "secondary",
        "decoration": "lineThrough",
        "verticalAlign": "top",
        "whiteSpace": "pre",
        "wordBreak": "all",
        "lineHeight": "md"
      }
    }
  }
)
```

#### 单独使用

```json
generateTypography(
  {
    "type": "page",
    "body": '内容',
  },
  {
    "className": {
      "size": "xl",
      "weight": "bold",
      "align": "center",
      "color": "success",
      "decoration": "lineThrough",
      "verticalAlign": "top",
      "whiteSpace": "pre",
      "wordBreak": "all",
      "lineHeight": "md"
    }
  }
)
```
