---
title: Common 通用
description:
type: 0
group: ⚙ 组件
menuName: Common 通用
icon:
order: 25
---

## generateNoSpacesComp

支持版本：**1.56.0**

### 属性表

| 属性名 | 类型     | 默认值 | 说明        |
| ------ | -------- | ------ | ----------- |
| schema | `object` | {}     | schema 配置 |

### 支持组件类型

| 组件类型    | 类型     | 支持版本 |
| ----------- | -------- | -------- |
| card        | 卡片     | 1.56.0   |
| input-table | 表格     | 1.56.0   |
| panel       | 面板     | 1.56.0   |
| wrapper     | 包裹容器 | 1.56.0   |
| collapse    | 折叠器   | 1.56.0   |

### 实现逻辑

为了让研发在使用各类组件的 noPadding/noMargin 模式不用到处查找辅助函数，generateNoSpacesComp 集成了目前所有支持无边距模式的组件的 type，并列出了支持的版本。此辅助函数会自动将传入 schema 中的`type`解构出来。
部分组件只有 padding 或 margin 属性，只会生效目前已经有的样式。

### 使用范例

```json
{
  "type": "page",
  "body": generateNoSpacesComp({
    // type支持上述列出的type
    "type": "panel",
    "noPadding": {
      //  保留上方和左侧的padding边距
      "top": false,
      "left": false
    },
    "noMargin": {
      //  保留上方和左侧的margin边距
      "top": false,
      "left": false
    },
    "title": "面板标题",
    "body": "面板内容"
  })
}
```

## generateSpace

支持版本： **1.62.0**

将 schema 作为第一个参数传入，根据第二个参数处理它的内外间距，目前提供了五种模式：`none`、`xs`、`sm`、`md`、`lg`

### 属性表

| 属性名    | 类型                  | 默认值 | 说明                                                                             |
|--------|---------------------|-----|--------------------------------------------------------------------------------|  
| schema | `SchemaNode`        | {}  | 要处理间距的组件
| config | `Object<ClassConfig>` | {}  | 要处理间距的组件，比如className,panelClassName,treeContainerClassName等

#### ClassConfig属性表
| config | `Object<SpaceConfig>` | {}  | 要处理间距的组件

#### SpaceConfig 属性表

| 属性名     | 类型          | 默认值                                              | 说明                                | 版本 |                                             
|---------|-------------|--------------------------------------------------|--------------------------------------------------------------------------------|---------| 
| margin  | `Object`    | {} | 外间距配置 | 1.62.0
| padding | `Object`    | {} | 内间距配置 | 1.62.0
| border  | `Object`    | {} | 圆角配置   | 1.64.0
| shadow  | `string`    | "" | 阴影属性   | 1.64.0

### 实现逻辑

`generateSpace`会将传入的第一个参数视为一个整体，根据第二个参数的配置展示对应的样式效果，默认均为 none。
配置枚举项和样式的对应规则如下，如传入枚举不在范围内，不会生效且会提示警告信息：
`none`表示`0px`
`xs`表示`4px`
`sm`表示`8px`
`md`表示`16px`
`lg`表示`24px`

`border` 对象有四个属性：`width`、`style`、`radius`、`color`。 `width`对应是字符串，可选值为`0`、`2`、`4`、`8`、`t`等
对应站点 `border-0`、`border-2`、`border-4`、`border-8`、`border-t`等, 设置`style`、`radius`、`color`同`width`。<br>
参考：[样式/BORDERS](/dataseeddesigndocui/#/amis/zh-CN/style/border/border-radius)

`Shadow` 属性是字符串，可选值为`none`、`sm`、`md`、`lg`、`xl`、`2xl`,
对应站点的 `shadow-none`、`shadow-sm`、`shadow-md`、`shadow-lg`、`shadow-xl`、`shadow-2xl`,<br>
参考：[样式/BoxShadow](/dataseeddesigndocui/#/amis/zh-CN/style/effect/box-shadow)

### 使用范例

```json
{
  "type": "page",
  "body": generateSpace({
    "type": "button",
    "label": "按钮"
  }, {
    "className": {
        "margin": {
        "top": "xs",
        "bottom": "md",
        "left": "lg",
        "right": "none"
        },
        "padding": {
          "top": "xs",
          "bottom": "md",
          "left": "lg",
          "right": "none"
        },
        "border": {
          "width": "2",
          "style": "solid",
          "radius": "sm",
          "color": "warning",
          },
          "shadow": "sm"
        }
      }   
  })
}
```

## generateTextStyle

支持版本： **1.64.0**

将schema作为一个参数传入，根据textStyle属性处理文字的样式，目前文字样式提供了六种属性：`color`, `size`, `weight`, `lineHeight`,`whitespace`, `wordBreak`

### 属性表

| 属性名    | 类型                  | 默认值 | 说明                                                                             |
|--------|---------------------|-----|--------------------------------------------------------------------------------|  
| schema | `SchemaNode`        | {}  | schema配置textStyle对象

#### textStyle属性表

| 属性名     | 类型          | 默认值                                              | 说明                                | 版本 |                                             
|---------|-------------|--------------------------------------------------|--------------------------------------------------------------------------------|---------| 
| color  | `string`  | "" | 文字颜色 | 1.64.0
| size  | `string`  | "" | 文字大小 | 1.64.0
| weight  | `string`  | "" | 文字的粗细 | 1.64.0
| lineHeight  | `string`  | "" | 文字行高 | 1.64.0
| whitespace  | `string`  | "" | 控制空白字符（如空格、制表符等）的处理方式以及文本的换行 | 1.64.0
| wordBreak  | `string`  | "" | 控制文本的断行行为 | 1.64.0

### 实现逻辑

`generateTextStyle`会将schema作为整体，根据他的属性textStyle设置对应的文字样式，
设置文本颜色`color`, 属性是字符串，可选值为`black`、`white`、`warning`、`danger`、`dark`、`pink-900`等,
对应站点的 `text-black`、`text-white`、`text-warning`、`text-danger`、`text-dark`、`text-pink-900`等

`size`、`weight`、`lineHeight`、`whitespace`、`wordBreak`属性同上通过拼接形成对应的站点文字样式。<br>
参考：[样式/TEXT](/dataseeddesigndocui/#/amis/zh-CN/style/typography/font-size)

### 使用范例

```json
{
  "type": "page",
  "body": {
    "type": "panel",
    "title": generateTextStyle({
      "type": "tpl",
      "tpl": "测试结果",
      "textStyle": {
      "color": "black",
      "size": "lg",
      "weight": "bold",
     }
    }),
    "body": "面板内容"
  }
}
```
## generateColor

支持版本： **1.64.0**

将 schema 作为第一个参数传入，根据第二个参数处理它的颜色，目前提供了五种模式：`success`、`error`、`info`、`warning`、`normal`、`disable`

### 属性表

| 属性名 | 类型                    | 默认值 | 说明              | 版本      
| ------ | --------------------- | ------ | ---------------- |
| schema | `SchemaNode`          | {}     | 要处理字体颜色的组件 | 1.64.0
| config | `String`              | "info" | 要处理字体颜色的组件 | 1.64.0

#### StatusConfig 属性表

| 属性名  | 类型      | 默认值   | 说明       |
| ------ | --------  | ------  | ---------- |
| config | `String`  | "info"  | 颜色状态配置 |

### 实现逻辑
`generateColor`会将传入的第一个参数视为一个整体，根据第二个参数的配置展示对应的样式效果，默认均为 info <br/>
配置枚举项和样式的对应规则如下，如传入枚举不在范围内，不会生效且会提示警告信息：
`success`表示`text-success`
`error`表示`text-danger`
`info`表示`text-info`
`warning`表示`text-warning`
`normal`表示`text-transparent`
`disable`表示`text-muted`

### 使用范例

```json
{
  "type": "page",
  "body": generateColor(
     {
        "type": "tpl",
        "tpl": "-",
      },
      "success"
  )
}
```

## generateBgColor

将 schema 作为第一个参数传入，根据第二个参数设置它的背景颜色

### 属性表

| 属性名 | 类型                  | 默认值 | 说明             |
| ------ | --------------------- | ------ | ---------------- |
| schema | `SchemaNode`          | {}     | 要设置背景的组件 |
| config | `Object<SpaceConfig>` | {}     | 背景配置属性 |

#### SpaceConfig 属性表

| 属性名     | 类型          | 默认值                                              | 说明                                | 版本 |                                             
|---------|-------------|--------------------------------------------------|--------------------------------------------------------------------------------|---------| 
| backgroundColor  | `string`    | "" | 背景颜色   | 

### 实现逻辑

`generateBgColor`会将传入的第一个参数视为一个整体，根据第二个参数的配置展示对应的样式效果。根据他的属性backgroundColor设置对应的背景样式， 属性是字符串，可选值为`black`、`white`等,
对应站点的 `bg-black`、`bg-white`等

### 使用范例

```json
{
  "type": "page",
  "body": generateBgColor({
    "type": "wrapper",
    "body": "内容",
  }, {
    "className": {
      "backgroundColor": "white"
    }
  })
}
```
