---
title: Dialog 对话框
description:
type: 0
group: ⚙ 组件
menuName: Dialog 对话框
icon:
order: 25
---

## getDialogGroupPanelNoPaddingSchema

支持版本：**1.53.0**

创建一个支持分组的`Dialog`组件。

### 实现逻辑
- 将一些默认样式内置在方法里面。
  - headerClassName: `antd-Panel-heading border-gray-100 border-none`
  - bodyClassName: `antd-Panel-body`
  - className: `shadow-none pm-bg-white border-0`
- 会根据每一个 type: panel 的最下方添加 type: "divider"，并且最后一个不添加。

### 属性表
传入参数定义如下：

| 属性名          | 类型                                                                | 默认值   | 说明                                                                             |
|--------------|-------------------------------------------------------------------|-------|--------------------------------------------------------------------------------|  
| schema           | `Array`         |   []    | 传入 panel 每组标题

### 使用范例

```json
{
  type: "page",
  body: [{
    "label": "提示弹框",
    "type": "button",
    "actionType": "dialog",
    "dialog": {
      "title": "弹框标题",
      "body": getDialogGroupPanelNoPaddingSchema([{
        "type": "panel",
        "title": "面板标题",
        "body": "面板内容"
      },{
        "type": "panel",
        "title": "面板标题",
        "body": "面板内容"
      }]),
      "actions": []
    }
  }]  
};
```
效果见`弹窗-分组表单（table上下布局）`

## getImagesDialogSchema

支持版本：**1.60.0**

配置带图片集的`dialog`弹框。其中图片集的数据来源可以是数据域中的值或者某个变量。

### 属性表

| 属性名 | 类型     | 默认值 | 说明                      |
| ------ | -------- | ------ | ------------------------- |
| schema | `object` | {}     |   schema 的配置 |

### 实现逻辑

1. 图片集的数据来源是数据域中的值，直接配置{source:"${imageData}"}; [source属性在1.60.0版本支持]
2. 图片集的数据来源是某个变量值，配置{imageList: fewImageData}

### 使用范例

```json
{
  'type': 'page',
  'className': 'bg-light',
  'body':  [{
      type: 'button',
      label: '中号弹窗-超出一行',
      actionType: 'dialog',
      dialog: getImagesDialogSchema({
        size: 'lg',
        source:"${imageData}",
        title: '图片集-中号弹窗'
      })
    },
    {
      type: 'button',
      label: '中号弹窗-超出一行',
      actionType: 'dialog',
      dialog: getImagesDialogSchema({
        size: 'lg',
        imageList: fewImageData
        title: '图片集-中号弹窗'
      })
    }
    
    ],
}
```

效果见`弹窗-图片集`

