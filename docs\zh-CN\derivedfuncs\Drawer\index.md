---
title: Drawer 抽屉
description:
type: 0
group: ⚙ 组件
menuName: Drawer 抽屉
icon:
order: 25
---

## getWizardInDrawer

支持版本：**1.56.1**

当抽屉嵌套向导组件时，此辅助函数为抽屉组件提供样式支持

### 属性表

| 属性名 | 类型     | 默认值 | 说明                      |
| ------ | -------- | ------ | ------------------------- |
| schema | `object` | {}     | drawer组件body的 schema 的配置 |

### 实现逻辑

- 将一些默认样式内置在方法里面。
  - 向导组件的className：`shadow-none pm-bg-white border-0`。
  - 向导组件的footerClassName: `pm-wizard-footer-fixed`。
  - 抽屉组件的bodyClassName：`pm-drawer-body-padding`。
  - 默认抽屉组件的actions为空数组

### 使用范例

```json
{
  'type': 'page',
  'className': 'bg-light',
  'body': [
    {
      "type": "button",
      "label": "分步向导+抽屉",
      "actionType": "drawer",
      "drawer": getWizardInDrawer({
        "showCloseButton": false,
        "title": "抽屉标题",
        "body": {
          "type": "wizard",
          "initApi": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm?waitSeconds=2",
          "delimiter": "arrow",
          "steps": [
            {
              "title": "第一步",
              "body": [
                {
                  "name": "website",
                  "label": "网址",
                  "type": "input-url",
                },
                {
                  "name": "email",
                  "label": "邮箱",
                  "type": "input-email",
                }
              ],
              "actions": [
                {
                  "type": "button",
                  "label": "下一步",
                  "actionType": "next"
                },
                {
                  "type": "button",
                  "label": "提交",
                  "actionType": "submit",
                  "level": "primary"
                }
              ]
            },
            {
              "title": "第二步",
              "body": [
                {
                  "name": "email2",
                  "label": "邮箱",
                  "type": "input-email",
                  "required": true
                }
              ],
              "actions": [
                {
                  "type": "button",
                  "label": "上一步",
                  "actionType": "prev"
                },
                {
                  "type": "button",
                  "label": "下一步",
                  "actionType": "next"
                },
                {
                  "type": "button",
                  "label": "提交",
                  "actionType": "submit",
                  "level": "primary"
                }
              ]
            },
            {
              "title": "第三步",
              "body": [
                "这是最后一步了"
              ],
              "actions": [
                {
                  "type": "button",
                  "label": "上一步",
                  "actionType": "prev"
                },
                {
                  "type": "button",
                  "label": "提交",
                  "actionType": "submit",
                  "level": "primary"
                }
              ]
            }
          ]
        }
      })
    }
  ]
}
```

效果见`抽屉-向导+抽屉`
