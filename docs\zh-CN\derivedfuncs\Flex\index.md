---
title: Flex 布局
description:
type: 0
group: ⚙ 组件
menuName: Flex 
icon:
order: 25
---

## generateFlexItems

支持版本：**1.62.0**

为了避免在使用`Flex`组件时给子节点增加`className`，内置了子节点的配置项

### 属性表

| 属性名 | 类型     | 默认值 | 说明            |
| ------ | -------- | ------ |---------------|
| schema | `object` | {}     | flex组件的schema |

#### items属性表

| 属性名        | 类型                  | 默认值       | 说明                                                                                                                                                     |
|------------|---------------------|-----------|--------------------------------------------------------------------------------------------------------------------------------------------------------|
| type       | `string`            | -         | items内的组件type                                                                                                                                          |
| flex       | `string`            | `initial` | 支持的配置: 1、auto、initial、none，参照：[Flex样式配置](http://moka.dmz.dev.caijj.net/dataseeddesigndocui/#/amis/zh-CN/style/flex/flex)                               |
| flexGrow   | `number`            | 0         | 支持的配置: 0、1，参照[Flex Grow样式配置](http://moka.dmz.dev.caijj.net/dataseeddesigndocui/#/amis/zh-CN/style/flex/grow)                                           |
| flexShrink | `number`            | 1         | 支持的配置: 0、1，参照[Flex Shrink样式配置](http://moka.dmz.dev.caijj.net/dataseeddesigndocui/#/amis/zh-CN/style/flex/shrink)                                       |
| alignSelf  | `string`            | `auto`    | 支持的配置: auto、start、end、center、stretch，参照[Align Self样式配置](http://moka.dmz.dev.caijj.net/dataseeddesigndocui/#/amis/zh-CN/style/box-alignment/align-self) |
| order      | `string` 或 `number` | 0         | 支持的配置：1～12、first、last，参照[Flex Order样式配置](http://moka.dmz.dev.caijj.net/dataseeddesigndocui/#/amis/zh-CN/style/flex/order)                                                                                                             |

### 实现逻辑

解析items中的`flex`、`flexGrow`、`flexShrink`、`alignSelf`、`order`参数，根据传入的配置拼接className，目前仍然预留了className透传入口，但**不推荐使用**

### 使用范例

```json
{
  "type": "page",
  "className": "bg-light",
  "body": {
    "type": "form",
    "mode": "horizontal",
    "body": [
      generateFlexItems({
        "type": "flex",
        "alignItems": "flex-start",
        "justify": "flex-start",
        "items": [{
          "type": "select",
          "name": "text8",
          "label": "类型",
          "options": [],
          "flexGrow": 1
        }, {
          "type": "button",
          "label": "新建",
          "level": "link"
        }]
      })
    ]
  }
}
```
