---
title: FormItem 普通表单项
description:
type: 0
group: ⚙ 组件
menuName: FormItem 普通表单项
icon:
order: 25
---

## getRangeItems

支持版本：**1.55.0**

当表单项为区间类型时，此辅助函数为区间组件提供样式支持

### 属性表

| 属性名          | 类型                                                                | 默认值   | 说明                                                                             |
|--------------|-------------------------------------------------------------------|-------|--------------------------------------------------------------------------------|
| label        | `string`                                                          |       | 区间组件表单项标签                                                                      |
| separatorStr | `string` 或 false                                                  | -     | 组件间的分割符，默认为-，为false则不显示分割符                                                     |
| required     | `boolean`                                                         | `false` | 是否为必填，当最外层设置required为true时，里面每一项都会变成必填项；当最外层设置required为false时，会读取里面子项的required |
| static       | `boolean`                                                         | `false` | 是否静态展示                                                                         |
| disabled     | `boolean`                                                         | `false` | 是否禁用，当最外层设置disabled为true时，里面每一项都会禁用；当最外层设置disabled为false时，会读取里面子项的disabled     |
| visible      | [表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression) |       | 当前区间组件是否展示                                                                     |
| labelRemark  | [Remark](/dataseeddesigndocui/#/amis/zh-CN/components/remark)     |       | 表单项标签描述                                                                        |
| description  | `string`                                                          |       | 区间组件描述                                                                         |
| items        | `Array<ItemSchema>`                                               | []    | 表单项                                                                            |

### ItemSchema属性表

除了下述列出的常用属性外，`ItemSchema`也支持`FormItem`的大部分属性

| 属性名            | 类型                                                               | 默认值   | 说明                                          |
|----------------|------------------------------------------------------------------|-------|---------------------------------------------|
| type           | `string`                                                         |  | 指定表单项类型                                     |
| name           | `string`                                                         |  | 字段名，指定该表单项提交时的 key |
| required       | `boolean`                                                        | `false` | 是否为必填，仅针对当前表单项生效，当有任一个表单项为必填时，区间组件前方会显示必填效果 |
| static         | `boolean`                                                        | `false` | 是否静态展示，仅对当前表单项生效                            |
| disabled       | `boolean`                                                        | `false` | 是否禁用，仅针对当前表单项生效                             |
| visible        | [表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression) |       | 当前表单项是否展示                                   |
| description    | `string`                                                         |       | 表单项描述                                       |
| addonAfterStr  | `string`                                                         |     | 表单项后缀，非必填                                   |
| validations    | [表达式](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/expression)                                                                |     | 表单项值格式验证，支持设置多个，多个规则用英文逗号隔开。                                   |


### 实现逻辑

在`getRangeItems`中，使用`group`包裹传入的items生成的组件，并将其和后缀进行结合。当传入的items数量正好为2时，子表单项会等分宽度，此场景多用于阶梯区间等；当传入的items数量大于2时，子表单项会根据组件实际宽度进行设置，此场景多用于联动多项。

### 使用范例

```json
{
  "type": "form",
  "body": getRangeItems({
    "label": "放款范围",
    "separatorStr": "~",
    "required": true,
    "labelRemark": {
      "type": "remark",
      "content": "这是一个提示"
    },
    "description": "放款范围规则为左闭右开",
    "items": [{
      "type": "input-number",
      "name": "minCount",
      "min": 0,
      "addonAfterStr": "元",
    }, {
      "type": "input-number",
      "name": "maxCount",
      "disabledOn": "${!minCount}",
      "addonAfterStr": "元",
    }]
  })
}
```

效果见`编辑页-基础表单-放款范围表单项`，各元素间距规范为`8px`；当需要进行校验时，在items中分别传递`validations`即可，可以通过自定义校验方法拿到当前表单项数据及前后表单项数据。
**需要特别注意，当在static的静态展示模式下使用此辅助函数，它虽然也能变成静态展示，但是样式无法自动处理，因为它无法读到form上配置的static，还是需要单独在辅助函数的schema中设置static。**
