---
title: Form表单
description:
type: 0
group: ⚙ 组件
menuName: Form表单
icon:
order: 25
---
## generateBasicFormV2

支持版本：**1.42.0**

创建一个`Form`组件，多用于编辑页面。

### 属性表

| 属性名 | 类型     | 默认值 | 说明                      |
| ------ | -------- | ------ | ------------------------- |
| schema | `object` | {}     | form 组件的 schema 的配置 |

### 实现逻辑

- 将一些默认样式内置在方法里面。
  - panelClassName：`border-0 shadow-none bg-transparent`。
  - bodyClassName：`pm-bg-white p-4`。
  - actionsClassName：`panel-footer border-none pt-4`。
- 增加 noPadding 属性（仅限于该方法，类型为 `boolean`），用来控制内容区域是否需要内边距，按照正常的`Form`组件属性传入即可生效。

### 使用范例

```json
{
  "type": "page",
  "body": generateBasicFormV2({
    // 正常传入form组件配置
    "api": "",
    "body": [],
    "noPadding": true,
    ...schema,
  })
}
```

## generateBasicForm(废弃)

此辅助函数样式不符合规范，已**不推荐使用**，请使用`generateBasicFormV2`替代。

## basicForm(废弃)

此辅助函数样式不符合规范，已**不推荐使用**，请使用`generateBasicFormV2`替代。
