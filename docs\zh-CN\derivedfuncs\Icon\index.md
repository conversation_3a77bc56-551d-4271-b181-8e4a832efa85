---
title: Icon 图标
description:
type: 0
group: ⚙ 组件
menuName: Icon 图标
icon:
order: 25
---

## getCopy

支持版本：**版本过早**

创建一个支持配置复制的`Icon`组件。

### 实现逻辑
- 内置了tooltip-wrapper在里面。
- 内置了className: `icon pm-copy`

### 属性表
传入参数定义如下：

| 属性名          | 类型                                                                | 默认值   | 说明                                                                             |
|--------------|-------------------------------------------------------------------|-------|--------------------------------------------------------------------------------|  
| schema           | `object`         |   {}    | 正常传入icon的schema配置 <br/>toolTipClassName: "flex-shrink-0 pm-icon-mr", 必传；<br/>content：可配置tooltip提示文案

### 使用范例

```json
{
  type: "page",
  body: getCopy({
    "type": "icon",
    "toolTipClassName": "flex-shrink-0 pm-icon-mr",
  })
};
```
效果见`列表页-常规列表-基础列表（列表名称字段）`

## getIconButton

去除`icon`图标`className`
### 属性表
| 属性名          | 类型                                                                | 默认值   | 说明                                                                             |
|--------------|-------------------------------------------------------------------|-------|--------------------------------------------------------------------------------|
| schema           | `object`         |   {}    | 正常传入icon的schema配置 |
| heightLight | `boolean`|false|图标是否高亮|
| marginSpace | `string`||图标与文字之间的间距,支持：mr-2,ml-2|

### 实现逻辑
- 内置className,传入heightLight为true时图标高量className:`icon text-info`
- heightLight不传时默认为false className:`icon`

### 使用范例
```json
{
  type: "page",
  body: [
    getIconButton({
      "type": "icon",
      "icon": "remark",
      "vendor": "",
      "marginSpace":"mr-2"
    }),
     getIconButton({
      "type": "icon",
      "icon": "fa fa-refresh",
      "heightLight":true,
      "vendor": "",
       "marginSpace":"mr-2"
    }),
    ]
};
```
效果见 `图标示例`
