---
title: InputFile 文件上传
description:
type: 0
group: ⚙ 组件
menuName: InputFile 文件上传
icon:
order: 25
---

## generateInputFile

支持版本：**1.57.1**

创建支持正常模式和静态模式的`InputFile`组件，用于`InputFile`组件。



### 属性表
传入参数定义如下：

| 属性名          | 类型      | 默认值   | 说明         |
|--------------|-------------------------------------------------------------------|-------|--------------------------------------------------------------------------------|
| static          | `object`                            |  false   |  是否静态展示
| nameField       | `string`                            |  'name'  |  标识哪个字段作为文件名
| name          | `string`                            |     |  数据映射name名
| multiple      | `object`                            |  false   |  是否为多选模式,静态展示时需要配置为true
| downloadConfig  | `object`         |   {canDownload: true,downloadAction: {}}    | 配置下载信息  



### 实现逻辑

1. 如果`static`为`false`或者没有，则为`input-file`正常状态;
2. 如果`static`为true, 则`input-file`为静态展示
3. downloadConfig 属性配置静态展示时是否展示下载入口，下载信息相关配置
   - canDownload 属性： 是否展示下载入口；为true 展示，否则不展示
   - downloadAction 属性：可配置下载请求的url地址， 下载入口名称（默认为'下载'）
     - 如果配置了downloadAction，则下载信息取配置信息，否则取现有方法中的配置，下载地址为urlField 字段

### 使用范例

```json
{
  "type": "page",
   body: [
      generateInputFile({
        type:'input-file',
        static:true,
        name:"downloads",
        label:"文件预览",
        multiple:true,
        urlField:"link",
        downloadConfig:{
          downloadAction:{
            label:"下载",
          }
        }
      })
   ]
}
```
