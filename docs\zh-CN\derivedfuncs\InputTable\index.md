---
title: InputTable 表格
description:
type: 0
group: ⚙ 组件
menuName: FormItem 表格
icon:
order: 25
---

## getGlobalActionButtonInputTableSchema

支持版本：**1.55.0**

创建一个支持配置是否有`topToolbar`的`InputTable`组件，多用于编辑页`InputTable`组件添加头部操作项，左侧（`操作按钮`），右侧（`搜索项`）。

更新版本：**1.57.0**

更新内容：`topToolbar`未配置或配置项为空时，底部边距`MarginBottom`仍存在的问题。
<!-- 当表单项为区间类型时，此辅助函数为区间组件提供样式支持 -->

### 属性表

| 属性名                   | 类型                                                                    | 默认值 | 说明                   |
| ------------------------ | ----------------------------------------------------------------------- | ------ | ---------------------- |
| topToolbar               | Object                                                                  | {}     | 表格顶部配置项         |
| topToolbar.leftItems     | `Array<any>`                                                            | []     | 表格顶部左侧按钮组     |
| topToolbar.rightItems    | `Array<any>`                                                            | []     | 表格顶部右侧配置项     |
| footerToolbar            | Object                                                                  | {}     | 表格底部配置项         |
| footerToolbar.bottomText | String                                                                  | -      | 表格底部总结性描述文案 |
| footerToolbar.buttons    | Array<[action](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/action)> | -      | 表格底部工具按钮组     |

### 实现逻辑

在`getGlobalActionButtonInputTableSchema`中，通过`flex`布局包裹传入的`topToolbar`属性内配置的组件，实现表格操作项位置自由定义，配置`leftItems`时，操作项显示在左侧，配置`rightItems`时，操作项显示在右侧。

### 使用范例

```json
  {
          "type": "page",
          "body": getGlobalActionButtonInputTableSchema({
            "type": "input-table",
            "name": "paramsConfig",
            "topToolbar": {
              "leftItems": [
                {
                  "type": "button",
                  "label": "功能按钮",
                  "level": "primary",
                  "rightIcon": "remark",
                  "rightIconTooltip": {
                    "tooltipPlacement": "top",
                    "content": "提示文案",
                    "tooltipTheme":"dark",
                  }
                },
                {
                  "type": "button",
                  "label": "批量修改",
                  "level": "primary",
                },
                {
                  "type": "button",
                  "label": "功能按钮",
                  "level": "primary",
                  "disabled": true,
                  "rightIcon":"remark",
                  "disabledRightIconTip":{
                    "tooltipTheme":"dark",
                    "content":"提示文案"
                  },
                  "tooltipPlacement": "top",
                },
                {
                  "type": "button",
                  "label": "批量导入"
                },
              ],
              "rightItems": [
                {
                  "type": "search-box"
                },
              ]
            },
           ...schema,

          }),
        },
```

效果见`编辑页-分组表单-第五步，可编辑表格`

## getFilterInputTableSchema

支持版本：**1.62.0**

创建一个支持搜索列表项的`InputTable`组件。

### 属性表

| 属性名                   | 类型                                                                    | 默认值 | 说明                   |
| ------------------------ | ----------------------------------------------------------------------- | ------ | ---------------------- |
| primaryField               | String                                                                  | id     | 表格数据唯一值字段，必传       |
| formId               | String                                                                   |  -    | 表格所在的form的id，必传         |
| searchField               | String                                                                   | -     | 搜索列表哪一列字段，必传          |
| topToolbar               | Object                                                                  | {}     | 表格顶部配置项         |
| topToolbar.leftItems     | `Array<any>`                                                            | []     | 表格顶部左侧按钮组     |
| topToolbar.rightItems    | `Array<any>`                                                            | []     | 表格顶部右侧配置项     |
| footerToolbar            | Object                                                                  | {}     | 表格底部配置项         |
| footerToolbar.bottomText | String                                                                  | -      | 表格底部总结性描述文案 |
| footerToolbar.buttons    | Array<[action](/dataseeddesigndocui/#/amis/zh-CN/docs/concepts/action)> | -      | 表格底部工具按钮组     |

### 实现逻辑

1. 通过`getGlobalActionButtonInputTableSchema`函数实现搜索区域布局
2. 点击搜索icon时，更新 `input-table` 的 `page` ,并将当前搜索框的值更新到数据域里，再触发formula组件更新，重新计算 `input-table` 展示的数据
3.  `input-table` 发生变化时，通过前端维护的唯一值字段再同步更新 `originTableData` 里对应的数据
4.  `input-table` 删除行的场景，需要同步从  `originTableData` 数据中删除当前对应数据，单独处理的原因是， 通过 `change` 更新 `originTableData` 时，并不清楚全量数据里异于 `input-table` 展示的数据，是过滤后存在的还是删除的，因此需要在删除的时候单独处理下

### 使用范例

```json
{
  "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "id": "myForm",
    "data": {
      // form数据域需要向下透传全量数据originTableData（这里字段名需要和辅助函数传入的name字段保持一致），在初始情况下，如果未返回唯一值字段，前端需要对数据做一层处理，添加唯一值字段
      "originTableData": [
        {
          "eventType": "a",
          "sourceName": "dwa.dwa_risk_wewewewe_rwewrwer1",
          "text1": "t1",
          "text2": "t2",
          "id": "a",
        },
        ...
      ],
    },
    "body": [
      // 使用支持input-table搜索的辅助函数
      getFilterInputTableSchema({
        // 透传的全量数据字段名，必传字段
        "name": "originTableData",
        // 当前input-table的id，必传字段
        "id": "myTable",
        "label": "触发事件",
        // 当前input-table所在form的id
        "formId": "myForm",
        // 全量数据里的唯一值字段名
        "primaryField": "id",
        // 搜索框的值匹配的列字段名
        "searchField": "eventType",
        "topToolbar": {
          // 表格上方左侧按钮区域
          "leftItems": [
            {
              "type": "button",
              "label": "功能按钮",
              "level": "primary",
              "rightIcon": "remark",
              "rightIconTooltip": {
                "tooltipPlacement": "top",
                "content": "提示文案",
                "tooltipTheme": "dark",
              },
              "className": " ml-2",
            },
          ]
        },
        "perPage": 5,
        "required": true,
        "addable": true,
        "editable": true,
        "removable": true,
        "showFooterAddBtn": false,
        // 底部自定义部分
        "footerToolbar": {
          "bottomText": "总件数：<strong>${filterTableData.length}件</strong>",
          // 自定义底部新增按钮
          "buttons": [{
            "type": "button",
            "level": "primary",
            "size": "sm",
            "label": "新增",
            "icon": "fa fa-plus",
            "className": " mr-2 pm-button-mt",
            // 点击触发input-table的新增动作
            "actionType": "add",
            // 这里需配置input-table的name
            "target": "originTableData"
          }]
        },
        "columns": [
          {
            "label": "事件类型",
            "name": "eventType",
          },
          {
            "label": "准实时数据源表",
            "name": "sourceName",
            "width": "20vw",
            "required": true,
            "quickEdit": {
              "type": "select",
              "placeholder": "请选择数据表",
              "required": true,
              "popOverContainerSelector": "body",
              "validations": {
                "isRequired": true,
              },
              "validationErrors": {
                "isRequired": "请选择数据表",
              },
              "options": [
                {
                  "label": "dwa.dwa_risk_wewewewe_rwewrwer1",
                  "value": "dwa.dwa_risk_wewewewe_rwewrwer1",
                },
                {
                  "label": "dwa.dwa_risk_wewewewe_rwewrwer2",
                  "value": "dwa.dwa_risk_wewewewe_rwewrwer2",
                },
                {
                  "label": "dwa.dwa_risk_wewewewe_rwewrwer3",
                  "value": "dwa.dwa_risk_wewewewe_rwewrwer3",
                },
                {
                  "label": "dwa.dwa_risk_wewewewe_rwewrwer4",
                  "value": "dwa.dwa_risk_wewewewe_rwewrwer4",
                }
              ],
            },
          },
          {
            "label": "过滤条件表达式",
            "required": true,
            "quickEdit": {
              "type": "input-text",
              "required": true,
              "placeholder": "请输入表达式，例如EVENT.param1='test'",
            },
            "name": "text1",
          },
        ],
      })
    ]
  },
}
```

