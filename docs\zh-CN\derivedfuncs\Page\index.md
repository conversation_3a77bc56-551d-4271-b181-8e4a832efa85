---
title: Page 页面
description:
type: 0
group: ⚙ 组件
menuName: Page 页面
icon:
order: 25
---

## generateCommonPage

支持版本：**1.62.0**

目前的`Page`组件总是和`className: "bg-light"`配套出现，此辅助函数统一创建含此 className 的页面组件。

### 属性表

| 属性名 | 类型         | 默认值 | 说明                    |
| ------ | ------------ | ------ | ----------------------- |
| schema | `SchemaNode` | {}     | page 组件的 schema 配置 |

### 使用范例

```json
generateCommonPage({
  "type": "page",
  "data": {},
  "body": []
})
```

## generateFixedWidthSchema

支持版本 **1.65.1**
目前左右布局缺少固定宽度【左侧宽度不可拖拽】场景【左侧宽度固定，右侧宽度自适应/右侧宽度固定，左侧宽度自适应】，此辅助函数统一创建左右布局的固定宽度页面组件。

### 属性表

| 属性名       | 类型         | 默认值 | 说明                    |
| ------------ | ------------ | -------- | ----------------------- |
| schema       | object       | {}     | 设置对象包含left、right |
| schema.left  | `SchemaNode` |       | amis 组件的 schema 配置 |
| schema.right | `SchemaNode` |      | amis 组件的 schema 配置 |

### 左右配置新增属性

左右两侧内容新增了以下属性，
| 属性名 | 类型 | 默认值 | 说明 |
|--------|--------------|-------|-----------------|
| customWidth | string | w-80 | 配置的固定宽度。当左右内容都未设置 customWidth 时，默认值为左侧宽度默认值-右侧内容自动撑开。当左右两侧都配置 customWidth 时，以左侧宽度为主【左侧宽度生效，右侧宽度自动撑开】。单独一侧配置 customWidth 属性，未配置的一侧宽度自适应 |

### 实现逻辑

通过`flex`+`wrapper`实现左右两侧宽度可固定,将传入的`customWidth`属性拼接到`wrapper`组件`className`里，实现左右两侧宽度可固定

### 使用范例

```json
generateCommonPage({
  "type": "page",
  "data": {},
  "body": generateFixedWidthSchema({
    "left":{

       "type": "input-tree",
        "treeContainerClassName": "pm-input-tree-container w-full",
        "name": "tree2",
        "multiple": false,
        "searchable": true,
        "autoCheckChildren": false,
        "customWidth":"w-1/2"
    },
    "right":{
        "type": "wrapper",
        "body": getBasicListSchemaV2({
            "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4",
            "id": "right-crud",
            "unsetQueryParams": [
              "tree2"
            ],
            "columns": [
              {
                "name": "id",
                "label": "ID",
              },
              {
                "name": "id",
                "label": "序号"
              },
              {
                "name": "engine",
                "label": "Rendering engine",
              },
              {
                "name": "browser",
                "label": "Browser",
              },
              {
                "name": "platform",
                "label": "Platform(s)",
              },
            ]
          })
        }
      })
})
```
效果见`列表页-左右布局-固定宽度左右布局`
