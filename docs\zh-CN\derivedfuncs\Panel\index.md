---
title: Panel 面板
description:
type: 0
group: ⚙ 组件
menuName: Panel 面板
icon:
order: 25
---
## generateGroupPanel

支持版本：**1.21.0**

创建一个`Panel`组件，多用于 panel 列表展示。

### 属性表

| 属性名 | 类型     | 默认值 | 说明                 |
| ------ | -------- | ------ | -------------------- |
| schema | `object` | {}     | panel 的 schema 配置 |

### 实现逻辑

- 将一些默认样式内置在方法里面。
  - className：`shadow-none pm-bg-white border-0`。
  - headerClassName：`antd-Panel-heading border-gray-100`。

### 使用范例

```json
{
  "type": "page",
  "body": generateGroupPanel({
    // 正常传入panel配置
    "title": "标题",
    "body": [],
    ...schema,
  })
}
```

## generateGroupPanelNoPadding

支持版本：**1.53.0**

创建一个支持配置有无默认 padding 或 margin 的 `Panel`组件，多用于页面单个分组展示，多个分组的场景采用 `getDialogGroupPanelNoPaddingSchema` 方法实现。

### 属性表

| 属性名 | 类型     | 默认值 | 说明                 |
| ------ | -------- | ------ | -------------------- |
| schema | `object` | {}     | panel 的 schema 配置 |

### 实现逻辑

- 增加 noPadding（仅限于该方法，类型为 `object`，具体传入格式参考下方使用范例）、noMargin（仅限于该方法，类型为 `object`，具体传入格式参考下方使用范例。注：由于`Panel`组件 margin 只有下边距，所以该属性仅对下边距生效） 属性，用来控制`Panel`组件是否需要某个方向的内/外边距，按照正常的`Panel`组件属性传入即可生效。

### 使用范例

```json
{
  "type": "page",
  "body": generateGroupPanelNoPadding({
    // 正常传入panel配置
    "title": "标题",
    "body": [],
    // 不配置该属性时默认没有padding
    "noPadding": {
      // 保留它的上内边距
      "top": false,
      // "left": false,
      // "right": false,
      // "bottom": false,
    },
    // 不配置该属性时默认没有margin
    // "noMargin": {
      // "bottom": false,
      // "right": false,
      // "top": false,
      // "bottom": false,
    // }
    ...schema,
  })
}
```

## getDialogGroupPanelNoPaddingSchema

支持版本：**1.53.0**

创建一组支持配置有无默认 padding 或 margin 的`Panel`组件，默认没有 padding 和 margin，多用于页面多个分组展示。

### 属性表

| 属性名 | 类型    | 默认值 | 说明                      |
| ------ | ------- | ------ | ------------------------- |
| schema | `array` | []     | 多个 panel 的 schema 配置 |

### 实现逻辑

- 将传入的 schema 数组遍历一下，用`generateGroupPanelNoPadding`方法包裹每一项，包裹后默认会将`Panel`内/外边距去除，也可以在每个`Panel`配置增加 noPadding 或 noMargin 属性，可以将某个方向的内/外边距保留。

### 使用范例

```json
{
  "type": "page",
  "body": getDialogGroupPanelNoPaddingSchema([
    {
      "type": "panel",
      "title": "标题1",
      "body": [
        // 展示内容配置
        ...schema,
      ],
      // 不配置该属性默认没有padding
      "noPadding": {
        // 保留它的左内边距
        "left": false,
        // "right": false,
        // "top": false,
        // "bottom": false,
      },
      // 不配置该属性默认没有margin
      // "noMargin": {
        // "bottom": false,
        // "right": false,
        // "top": false,
        // "bottom": false,
      // }
    },
    {
      "type": "panel",
      "title": "标题2",
      "body": [
        // 展示内容配置
        ...schema,
      ]
    },
  ])
}
```

## getDialogGroupPanelSchema(废弃)

此辅助函数由于未考虑到 Panel 组件内外边距与其他组件堆叠场景，已**不推荐使用**，请使用`getDialogGroupPanelNoPaddingSchema`替代
