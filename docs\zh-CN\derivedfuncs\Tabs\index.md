---
title: Tabs 选项卡
description:
type: 0
group: ⚙ 组件
menuName: 选项卡容器组件。
icon:
order: 25
---
## generateCustomPaddingTabs

支持版本：**1.58.0**

创建一个自带内外层 padding 的 `Tabs` 组件，多用于在页面顶层或内容区自定义组合调整 `Tabs` 内外层 padding，支持四个方向自定义去除 padding。

### 属性表

| 属性名           | 类型                                       | 默认值                    | 说明                                                                                                                                     |
| ---------------- | ------------------------------------------ | ------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------- |
| noPadding        | object                                     | defaultValue \| boolean | 1.设置对象包含 top、right、bottom 或 left，值为 true 时可去除对应方向外层 padding。</br>2.直接设置为 true ，则去除所有方向外层 padding。 |
| noPaddingContent | object                                     | defaultValue \| boolean | 1.设置对象包含 top、right、bottom 或 left，值为 true 时可去除对应方向内层 padding。</br>2.直接设置为 true ，则去除所有方向内层 padding。 |
| 其他同 Tabs 组件 | [SchemaTabs](#/amis/zh-CN/components/tabs) | { "type": "tabs" }        | -                                                                                                                                        |

#### defaultValue

```json
{
  "left": false,
  "top": false,
  "right": false,
  "bottom": false
}
````

### 实现逻辑

解析入参中的 `noPadding` 和 `noPaddingContent` 属性，分别设置外层 padding 和内层 padding等样式，其中：

- **默认属性**：

  - 添加默认属性 `type: "tabs"`。
  - 最外层添加默认样式 `pm-bg-white p-4`。
  - 单个 tab 项添加样式 `p-4`。

- **无 padding 配置**：

  - 如果 `noPadding` 为 `true`，去除所有外层 padding，替换默认样式为 `p-none`。

  - 如果 `noPaddingContent` 为 `true`，去除所有内层 padding，替换默认样式为 `p-none`。

- **方向性无 padding 配置**：

  - 如果 `noPadding` 为对象，根据对象中方向属性值，最外层追加对应方向的样式：

    - `pt-0`：去除顶部 padding
    - `pr-0`：去除右侧 padding
    - `pb-0`：去除底部 padding
    - `pl-0`：去除左侧 padding

  - 如果 `noPaddingContent` 为对象，根据对象中方向属性值，单个 tab 追加对应方向的样式：
    - `pt-0`：去除顶部 padding
    - `pr-0`：去除右侧 padding
    - `pb-0`：去除底部 padding
    - `pl-0`：去除左侧 padding

### 使用范例

```json
// 无 padding 配置
{
  "type": "page",
  "body": generateCustomPaddingTabs({
    "noPadding": true,
    "noPaddingContent": true,
    "tabs": [
      {
        "title": "Tab 1",
        "tab": "Content 1"
      },
      {
        "title": "Tab 2",
        "tab": "Content 2"
      }
    ]
  })
}

// 方向性无 padding 配置
{
  "type": "page",
  "body": generateCustomPaddingTabs({
    "noPadding": {
      "top": true, // 可选
      "right": true, // 可选
      "bottom": true, // 可选
      "left": true // 可选
    },
    "noPaddingContent": {
      "top": true, // 可选
      "right": true, // 可选
      "bottom": true, // 可选
      "left": true // 可选
    },
    "tabs": [
      {
        "title": "Tab 1",
        "tab": "Content 1"
      },
      {
        "title": "Tab 2",
        "tab": "Content 2"
      }
    ]
  })
}
```

## getTabDetailGroupPanelSchemaV3

支持版本：**1.59.0**

创建带分组的tab组件，分组支持插入间隔，用于tab组件中嵌套panel 组件。

### 属性表
传入参数定义如下：

| 属性名          | 类型                                                                | 默认值   | 说明                                                                             |
|--------------|-------------------------------------------------------------------|-------|--------------------------------------------------------------------------------|
| schema        | `object`                                                          |     | schema 配置
| noTitle           | `string`         |       | 是否有非Title的情况  



### 实现逻辑

1. `tab` 组件中包含`panel`,需要在每个`panel` 之间 插入间隔块， 间隔块通过div样式控制；最后一个间隔块隐藏。
2. 第二个参数`noTitle`如果为true，并且对应的panel 没有配置title,并且是第一个panel,则对应的panelBody 四周内边距都为0 ,否则只有左右和下边距为0；
3. 第三个参数`hasLastDivider`如果为true,额外需要再加一条divider

### 使用范例

```json
 {
        "title": "无标题分组",
        "tab": getFormTabDetailSchema({
          "labelWidth": 60,
          "className":"pm-tab-panel-formGroup",
          "panelClassName":"pm-tab-panel-body",
          "body": getTabDetailGroupPanelSchemaV3([
            {
              type: 'panel',
              body: [
                {
                  type: 'group',
                  body: [
                    {
                      "name": "text1",
                      "type": "static",
                      "label": "静态展示",
                      "quickEdit": {
                        "type": "input-text"
                      }
                    },
                    {
                      type: 'static',
                      name: 'text2',
                      label: '年龄',
                    },
                    {
                      type: 'static',
                      name: 'text3',
                      label: '班级',
                      required: true,
                    },
                  ]
                },
                {
                  type: "group",
                  body: [
                    {
                      type: 'static',
                      name: 'text4',
                      label: '邮箱',
                    },
                    {
                      type: 'static',
                      name: 'text5',
                      label: '电话',
                    },
                    {
                      type: 'static',
                      name: 'text6',
                      label: '地址',
                      columnRatio: 4,
                    }
                  ]
                },
                {
                  type: "group",
                  body: [
                    {
                      type: 'static',
                      name: 'text7',
                      label: '其它',
                      columnRatio: 4,
                    }
                  ]
                }
              ]
            },
            {
              type: 'panel',
              title: {
                type: 'tpl',
                tpl: '复杂信息',
              },
              body: [
                {
                  type: "group",
                  body: [
                    {
                      type: 'static',
                      name: 'second1',
                      label: '邮箱',
                    },
                    {
                      type: 'static',
                      name: 'second2',
                      label: '电话',
                    },
                    {
                      type: 'static',
                      name: 'second3',
                      label: '地址',
                      columnRatio: 4,
                    }
                  ]
                },
                {
                  type: 'group',
                  body: [
                    {
                      type: 'static',
                      name: 'second4',
                      label: '地址',
                      columnRatio: 4,
                    }
                  ]
                }
              ]
            },
            {
              type: 'panel',
              title: {
                type: 'tpl',
                tpl: '策略信息',
              },
              body: [
                {
                  type: "group",
                  body: [
                    {
                      type: 'static',
                      name: 'second1',
                      label: '邮箱',
                    },
                    {
                      type: 'static',
                      name: 'second2',
                      label: '电话',
                    },
                    {
                      type: 'static',
                      name: 'second3',
                      label: '地址',
                      columnRatio: 4,
                    }
                  ]
                },
                {
                  type: 'group',
                  body: [
                    {
                      type: 'static',
                      name: 'second4',
                      label: '地址',
                      columnRatio: 4,
                    }
                  ]
                }
              ]
            }
          ], true)
        }),
      },
```
