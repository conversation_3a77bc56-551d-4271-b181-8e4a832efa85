---
title: Tag 标签
description:
type: 0
group: ⚙ 组件
menuName: Tag 标签
icon:
order: 25
---

## getBasicTagSchema

支持版本：**版本过早**

创建一个支持配置`Tag`组件。

### 实现逻辑
- 内置了样式和属性
  - marginRight: `0` 
  - displayMode: `bordered`

### 属性表
传入参数定义如下：

| 属性名          | 类型                                                                | 默认值   | 说明                                                                             |
|--------------|-------------------------------------------------------------------|-------|--------------------------------------------------------------------------------|  
| schema           | `object`         |   {}    | 正常传入tag的schema配置  

### 使用范例

```json
{
  type: "page",
  body: getBasicTagSchema({
    "label": "普通的标签",
    "color": "active"
  })
};
```
效果见`列表页-常规列表-基础列表（列表名称字段）`
