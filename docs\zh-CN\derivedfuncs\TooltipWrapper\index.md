---
title: TooltipWrapper 文字提示容器
description:
type: 0
group: ⚙ 组件
menuName: TooltipWrapper 文字提示容器
icon:
order: 25
---

## generateTooltipDetailInfo

支持版本：**1.65.0**

创建一个类似于dialog的tooltipWrapper组件

### 实现逻辑
- 内置属性
  - type: `tooltip-wrapper`，不支持修改。
  - tooltipTheme: `light`， 支持修改。
  - trigger: `click`， 支持修改。
- 扩展属性
  - size: 提示框大小，支持`sm、xs、md、lg`，大小同dialog，默认: `xs`。
  - maxHeight: 提示框内容最大高度，默认: `200`。


### 属性表
传入参数定义如下：

| 属性名          | 类型                                                                | 默认值   | 说明                                                                             |
|--------------|-------------------------------------------------------------------|-------|--------------------------------------------------------------------------------|  
| schema           | `object`         |   {}    | 正常传入tooltipWrapper的schema配置  

### 使用范例

```json
{
  type: "page",
  body: generateTooltipDetailInfo({
     "size": "lg",
     "title": "标题",
     "body": "展示文案",
     "content": [
        // 提示内容schema配置
        ...schema
      ],
     "maxHeight": 300,
     // 更多tooltipWrapper配置
     ...schema,
  })
};
```
效果见`列表页-常规列表-基础列表（列表状态字段）`


## getTooltipSizeSchema

支持版本：1.64.0

此辅助函数为了给TooltipWrapper组件设置大小，避免内容过长显示不全的问题。

### 实现逻辑
将schema作为第一个参数传入，根据第二个参数处理设置它的大小，目前提供了三种模式：sm、md、lg。   
sm对应tooltipClassName的是"max-w-xs max-h-24 overflow-y-auto"  
md对应tooltipClassName的是"max-w-xs max-h-40 overflow-y-auto"  
lg对应tooltipClassName的是"max-w-xs max-h-96 overflow-y-auto"  

### 属性表
传入参数定义如下：

| 属性名          | 类型                                                                | 默认值   | 说明                                                                             |
|----------------|---------------------------------------------------------------------|--------|--------------------------------------------------------------------------------|  
| schema         | `object`                                                            |   {}    | 正常传入TooltipWrapper的schema配置  
| size           | `string`                                                            |   ''    | 设置大小的参数（sm、md、lg）

### 使用范例

```json
{
  type: "page",
  className: "p-4",
  body: getTooltipSizeSchema({
        "type": "tooltip-wrapper",
        "content": "提示文字提示文字提示文字提示文字提示文字提示提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字文字提示文字提示文字提示文字提示文字",
        "body": "hover 大号",
        "placement": "bottom",
        "showArrow": true
      }, "sm")
};
```


