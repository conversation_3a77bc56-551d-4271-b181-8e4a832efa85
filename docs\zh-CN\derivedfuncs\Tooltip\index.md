---
title: TooltipWrapper 文字提示容器
description:
type: 0
group: ⚙ 组件
menuName: TooltipWrapper 文字提示容器
icon:
order: 25
---

## getTooltipSizeSchema

支持版本：1.64.0

此辅助函数为了给TooltipWrapper组件设置大小，避免内容过长显示不全的问题。

### 实现逻辑
将schema作为第一个参数传入，根据第二个参数处理设置它的大小，目前提供了三种模式：sm、md、lg。   
sm对应tooltipClassName的是"max-w-xs max-h-24 overflow-y-auto"  
md对应tooltipClassName的是"max-w-xs max-h-40 overflow-y-auto"  
lg对应tooltipClassName的是"max-w-xs max-h-96 overflow-y-auto"  

### 属性表
传入参数定义如下：

| 属性名          | 类型                                                                | 默认值   | 说明                                                                             |
|----------------|---------------------------------------------------------------------|--------|--------------------------------------------------------------------------------|  
| schema         | `object`                                                            |   {}    | 正常传入TooltipWrapper的schema配置  
| size           | `string`                                                            |   ''    | 设置大小的参数（sm、md、lg）

### 使用范例

```json
{
  type: "page",
  className: "p-4",
  body: getTooltipSizeSchema({
        "type": "tooltip-wrapper",
        "content": "提示文字提示文字提示文字提示文字提示文字提示提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字提示文字文字提示文字提示文字提示文字提示文字",
        "body": "hover 大号",
        "placement": "bottom",
        "showArrow": true
      }, "sm")
};
```

