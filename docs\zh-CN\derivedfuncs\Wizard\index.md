---
title: Wizard 向导
description:
type: 0
group: ⚙ 组件
menuName: Wizard 向导
icon:
order: 25
---

## generateFixedWizard

支持版本：**1.55.0**

表单向导，能够配置多个步骤引导用户一步一步完成表单提交。

更新版本：**1.58.0**

generateFixedWizard步骤标题与body内容间距调整为16px.
### 属性表

| 属性名 | 类型     | 默认值 | 说明                      |
| ------ | -------- | ------ | ------------------------- |
| schema | `object` | {}     | Wizard组件的 schema 配置 |

### 实现逻辑

- 将一些默认样式内置在方法里面，规范组件样式。
  - className: `border-0 bg-light shadow-none h-full pm-mb-0`,
  - stepClassName: `pm-bg-white pt-4 mt-0 mb-4`,
  - stepItemClassName: `h-full flex flex-col`,
  - bodyClassName: `mt-0 pm-bg-white flex-1 overflow-y-auto`,
  - footerClassName: `flex justify-end pm-wizard-footer-fixed-paddingY`,
### 使用范例

```json
 generateFixedWizard({
      "type": "wizard",
      "steps": [
        {
          "title": "第一步",
          "mode": "horizontal",
          "body": [
            {
              "type": "group",
              "body": [
                {
                  "type": "select",
                  "name": "department",
                  "label": "归属部门",
                },
              ]
            },
          ]
        },
        {
          "title": "第二步",
          "mode": "horizontal",
          "body": [
            {
              "type": "group",
              "body": [
                {
                  "type":"select",
                  "name": "department",
                  "label": "归属部门",
                },
              
              ]
            },
          ]
        },
        {
          "title": "第三步",
          "mode": "horizontal",
         "body": [
            {
              "type": "group",
              "body": [
                {
                  "type": "select",
                  "name": "department",
                  "label": "归属部门",
                },
              
              ]
            },
          ]
        }
      ]
    })
```

效果见`编辑页-步骤表单（单步提交按钮固定底部）`
