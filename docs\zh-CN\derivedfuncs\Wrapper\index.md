---
title: Wrapper 包裹容器
description:
type: 0
group: ⚙ 组件
menuName: Wrapper 包裹容器
icon:
order: 25
---
## generateHeaderV2

支持版本：**1.53.0**

创建一个支持配置页面标题的 `wrapper` 组件，多用于在编辑页添加头部标题，主要包括左侧（ `返回图标` 、 `主标题` 、 `副标题` 、 `标签`） 以及右侧（ `操作栏` ） 两个区域。

### 属性表

| 属性名     | 类型                                              | 默认值          | 说明                                                |
| ---------- | ------------------------------------------------- | --------------- | --------------------------------------------------- |
| iconConfig | [SchemaIcon](#/amis/zh-CN/components/icon)        | IconConfigValue | 配置返回图标，可自定义配置 icon、onEvent、show 属性 |
| title      | string                                            | ""              | 配置主标题，文本过长时支持省略号显示                |
| subtitle   | string                                            | ""              | 配置副标题，同上                                    |
| tags       | [SchemaNode](#/amis/zh-CN/docs/types/schemanode)  | -               | 配置标签                                            |
| actions    | [SchemaArray](#/amis/zh-CN/docs/types/schemanode) | -               | 配置操作栏                                          |

#### IconConfigValue

```json
// 默认值，可根据需要修改单项或多项属性
{
  "onEvent": {
    "click": {
      "actions": [{"actionType": "goBack"}]
    }
  },
  "icon": "chevron-left",
  "show": true
}
```

### 实现逻辑

通过 `Wrapper` + `flex` 组件自定义左右区域布局并控制组件间隔，其中：

- Wrapper 外容器，默认样式：`p-2 pr-none pl-none mb-4 pm-bg-white`
  - 左侧区域，默认样式：`flex-1  w-full mr-2 pm-place-content-start`,但当右侧有内容时，默认样式是`flex-1 w-3/4 mr-2 pm-place-content-start`
    - 返回图标，默认样式：`pr-2`。默认添加了图标和返回事件，可通过 `iconConfig` 传入自定义配置，默认配置为 `IconConfigValue`
    - 主标题，默认样式：`text-lg text-black font-bold text-ellipsis`
    - 副标题，默认样式：`pm-subtitle-color text-ellipsis pm-subtitle-min-width pm-button-ml`
    - 标签，默认占位，紧跟在副标题之后
  - 右侧区域
    - 操作栏，默认样式： `mr-2`

### 使用范例

```json
{
  "type": "page",
  "body": generateHeaderV2({
    "title": "主标题",
    "subtitle": "副标题",
    "iconConfig":{
      "icon": "chevron-left",
      "show": true,
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "goBack"
            }
          ]
        }
      }
    },
    "tags": [
      {
        "type": "tag",
        "label": "普通标签",
        "className": "pm-button-ml",
        "color": "processing"
      },
    ],
    "actions": [
      {
        "type": "button",
        "label": "取消",
        "onEvent": {
            "click": {
              "actions": [
                {
                  "actionType": "toast",
                  "args": {
                    "msgType": "info",
                    "msg": "响应取消操作"
                  }
                }
              ]
            }
          }
      },
      {
        "type": "button",
        "level": "primary",
        "label": "保存",
        "onEvent": {
          "click": {
            "actions": [
              {
                "componentId": "myForm",
                "actionType": "submit"
              }
            ]
          }
        }
      }
    ]
  })
}
```

## generateHeader(废弃)

此辅助函数由于未考虑到页面标题超长情况，已**不推荐使用**，请使用`generateHeaderV2`替代

## generateNoPaddingWrapper

支持版本：**1.55.0**

创建一个支持配置有无默认 padding 的`Wrapper`组件，多用于`Wrapper`组件的内边距与其他组件内边距堆叠场景。

### 属性表

| 属性名 | 类型     | 默认值 | 说明                   |
| ------ | -------- | ------ | ---------------------- |
| schema | `object` |   -    | wrapper 的 schema 配置 |

### 实现逻辑

- 增加了一个`noPadding`（仅限于该方法，类型为 `object`，具体传入格式参考下方使用范例）属性，通过配置`noPadding`来控制某个方向的 padding 去除/保留，默认是没有 padding 的，按照正常的`Wrapper`组件属性传入即可生效。
- 内置默认样式
  - className: `pm-bg-white`

### 使用范例

```json
{
  "type": "page",
  "body": generateNoPaddingWrapper({
    // 正常传入配置
    "body": [],
    // 不配置该属性时默认没有padding
    "noPadding": {
      // 保留左边内边距
      "left": false,
      // "top": false,
      // "right": false,
      // "bottom": false,
    },
  })
}
```

## generatePanelHeader

支持版本： **1.62.0**

统一panel组件的title样式，无需再设置className，接收一个对象

### 属性表

| 属性名    | 类型       | 默认值 | 说明                                                                             |
|--------|----------|-----|--------------------------------------------------------------------------------|  
| titleConfig | `Object` | {}  | 要处理间距的组件

#### titleConfig

| 属性名      | 类型       | 默认值 | 说明                                          |
|----------|----------|-----|------------------------------------------------|  
| title    | `string` | {}  | 主标题 
| subTitle | `string` | {}  | 副标题
| tags     | `Array`  | {}  | 主副标题右侧展示的标签
| actions  | `Array`  | {}  | 固定在右侧展示的按钮

### 实现逻辑

`generatePanelHeader`实现思路上参考了目前的`generateHeaderV2`组件，但相比于通用的标题辅助函数，此辅助函数专用于panel内的场景，例如去除了返回按钮，内置的样式和panel的默认样式保持一致，调整了右侧按钮的间距等。

### 使用范例
```json
{
  "type": "page",
  "body": {
    "type": "panel",
    "title": [
      generatePanelHeader({
        "title": "第一步，基础信息",
        "tags": [{
          "type": "remark",
         "content": "这是一段提示"
       }],
        "actions": [
        {
          "type": "button",
          "label": "实验列表",
          "level": "link",
          "linkWithoutPadding": true
        }
      ]
      }),
    ],
    "body": [
      ...
    ]
  }
}
```

实际效果可参考`编辑页-分组表单`



