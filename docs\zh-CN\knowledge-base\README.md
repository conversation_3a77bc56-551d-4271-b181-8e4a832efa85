# 技术知识库

本知识库用于记录在开发过程中分析总结的技术信息，避免重复分析，提高开发效率。

## 文件组织结构

### 📚 amis-framework/
存储amis框架相关的技术信息
- `component-events.md` - 组件事件数据结构参考
- `data-flow.md` - 数据流和继承链机制
- `testing-patterns.md` - 测试模式和验证方法

### 📋 development-records/
存储开发过程中的记录和模板
- `issue-analysis-templates.md` - issue分析模板
- `verification-checklist.md` - 验证检查清单

### 💡 technical-insights/
存储技术洞察和经验总结
- `common-pitfalls.md` - 常见问题和解决思路
- `debugging-workflows.md` - 调试工作流程

### 🛠️ tools-and-frameworks/
存储工具和框架使用指南
- `mermaid-best-practices.md` - Mermaid 图表使用最佳实践和常见问题解决方案

## 使用指南

### 更新原则
- 发现新的技术信息时，优先更新到对应的知识库文件
- 避免在cursorrules中堆积具体技术细节
- 保持信息的时效性和准确性

### 引用规范
cursorrules中通过相对路径引用知识库文件：
```markdown
详见 `docs/zh-CN/knowledge-base/amis-framework/component-events.md`
```

### 维护责任
- 每次技术分析后，及时更新相关知识库文件
- 定期检查信息的准确性和完整性
- 删除过时或错误的信息 
