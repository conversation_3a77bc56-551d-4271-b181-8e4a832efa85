# amis组件事件数据结构参考

本文档记录已验证的amis组件事件数据结构，避免重复分析。

## Select组件

### onChange事件
**事件参数**：
- `[name]: string` - 组件的值
- `selectedItems: Option | Option[]` - 选中的项
- `items: Option[]` - 选项集合

**验证记录**：
- **Issue1242验证**：Select组件onChange事件确实提供 `selectedItems` 字段
- **真实事件数据示例**：`{selectedItems: ['CHROME'], value: 'CHROME'}`
- **数据结构定义**：基于 `packages/amis/src/renderers/Form/Select.tsx` 确认
- **实际场景**：在dialog -> form -> select的层级结构中，renderer.props.data包含表单和对话框上下文数据

**使用示例**：
```javascript
{
  type: 'select',
  name: 'trainingTableName',
  onEvent: {
    change: {
      actions: [
        {
          actionType: 'toast',
          args: {
            msg: '${event.data.selectedItems | json}'
          }
        }
      ]
    }
  }
}
```

## 其他组件

### List组件
**itemSelect事件**：
- `item` - 选中的项
- `selected` - 是否选中
- `selectedItems` - 选中项数组
- `unSelectedItems` - 未选中项数组

### CRUD组件
**selectedChange事件**：
- `selectedItems` - 选中的行数据集合
- `unSelectedItems` - 未选中的行数据集合
- `newItems` - 新选中的项
- `newUnSelectedItems` - 新取消选中的项

## 验证方法

### playground测试
- 访问模式：`http://localhost:8888/playground/index.html?issue={编号}#/`
- 示例文件位置：`playground/components/issues/issue{编号}.js`

### 代码查找
使用codebase_search搜索组件源码：
```
组件名 onChange event selectedItems data structure
```

### 文档查阅
查看组件文档的事件表：
```
docs/zh-CN/components/form/组件名.md
```

## 更新记录

- 2024-XX-XX：初始创建，添加Select组件事件数据结构
- Issue1242：验证Select组件selectedItems字段存在且数据结构正确 
