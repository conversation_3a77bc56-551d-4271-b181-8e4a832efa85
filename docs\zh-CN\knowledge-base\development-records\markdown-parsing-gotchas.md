# Markdown 解析常见陷阱

## 反引号嵌套问题

### 问题描述
误以为使用4个反引号能够阻止内部3个反引号的特殊解析。

### 实际情况
项目的markdown解析器使用的正则表达式：
```javascript
/```(schema|html|mermaid|collapsible)(?:[ \t]+(.*?))?\n([\s\S]*?)```/g
```

这个正则表达式会**贪婪匹配**，即使3个反引号被4个反引号包围，仍然会被匹配到。

### 验证代码
```javascript
const testContent = `
\`\`\`\`
\`\`\`mermaid
graph TD
    A[测试] --> B[结束]
\`\`\`
\`\`\`\`
`;

const regex = /```(mermaid)[\s\S]*?```/g;
const matches = testContent.match(regex);
// 结果：会匹配到内部的```mermaid...```
```

### 正确的解决方案

#### 方案1：HTML实体（推荐）
```html
<pre><code>
```mermaid
graph TD
    A[开始] --> B[处理数据]
    B --> C[结束]
```
</code></pre>
```

#### 方案2：HTML转义
```html
&grave;&grave;&grave;mermaid
graph TD
    A[开始] --> B[处理数据] 
    B --> C[结束]
&grave;&grave;&grave;
```

#### 方案3：缩进代码块
```
    ```mermaid
    graph TD
        A[开始] --> B[处理数据]
        B --> C[结束]
    ```
```

## 经验教训

1. **不要基于假设**：看到正则表达式时，应该实际测试验证，而不是基于直觉判断
2. **使用工具验证**：可以用简单的Node.js脚本或在线正则测试工具验证匹配结果
3. **理解贪婪匹配**：正则表达式的贪婪匹配特性可能导致意外的结果

## 相关技术细节

- 文件：`scripts/markdownPlugin.ts` 和 `scripts/md-parser.js`
- 正则表达式：`/```(schema|html|mermaid|collapsible)(?:[ \t]+(.*?))?\n([\s\S]*?)```/g`
- 处理阶段：构建时的第一阶段自定义处理 
