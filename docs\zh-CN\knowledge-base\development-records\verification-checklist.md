# 验证检查清单

本清单用于确保技术分析和问题修复的验证质量。

## Issue分析验证清单

### ✅ 问题复现
- [ ] 通过playground访问对应issue示例
- [ ] 确认问题现象与描述一致
- [ ] 记录复现步骤和预期vs实际结果

### ✅ 数据结构验证
- [ ] 查阅组件官方文档确认事件参数
- [ ] 通过codebase_search查找组件源码
- [ ] 验证数据结构的真实性，避免编造字段
- [ ] 更新 `knowledge-base/amis-framework/component-events.md`

### ✅ 根因分析
- [ ] 定位问题发生的代码位置
- [ ] 分析数据流和调用链
- [ ] 确认根本原因而非表面现象
- [ ] 创建可视化图表说明问题机制

### ✅ 解决方案验证
- [ ] 编写或修改测试用例
- [ ] 运行特定测试文件验证修复效果
- [ ] 确保向后兼容性
- [ ] 验证边界情况和异常处理

## 测试执行规范

### 测试命令优化
```bash
# ✅ 推荐：运行特定测试文件
npm test -- packages/amis-core/__tests__/actions.test.ts

# ❌ 避免：运行全量测试（性能差）
npm test
```

### 测试结果记录
- [ ] 记录通过的测试用例数量
- [ ] 记录关键验证场景
- [ ] 更新技术文档中的验证部分

## Playground验证流程

### URL访问模式
```
http://localhost:8888/playground/index.html?issue={issue编号}#/
```

### 示例文件检查
- [ ] 确认 `playground/components/issues/issue{编号}.js` 存在
- [ ] 检查示例配置是否完整
- [ ] 取消注释被注释的测试代码（如果需要）

### 实际验证
- [ ] 在浏览器中访问playground
- [ ] 执行问题操作步骤
- [ ] 观察修复效果
- [ ] 截图记录验证结果

## 文档更新标准

### 技术文档要求
- [ ] 使用真实数据结构，不编造字段
- [ ] 创建Mermaid图表展示技术方案
- [ ] 提供可折叠的代码示例
- [ ] 记录验证过程和结果

### 知识库维护
- [ ] 将新发现的信息更新到knowledge-base
- [ ] 避免在cursorrules中堆积具体技术细节
- [ ] 保持信息的准确性和时效性

## 代码修改规范

### 修改前确认
- [ ] 理解现有实现的设计意图
- [ ] 优先修改测试用例而非实现代码
- [ ] 与用户确认修改核心逻辑的必要性

### 修改后验证
- [ ] 运行相关测试用例
- [ ] 确认不引入新问题
- [ ] 验证性能影响可控
- [ ] 更新相关文档

## 质量检查点

### 分析质量
- [ ] 问题分析深入到根本原因
- [ ] 解决方案针对性强，不是表面修复
- [ ] 考虑了向后兼容性和边界情况

### 验证质量
- [ ] 使用多种方法验证（测试、playground、源码）
- [ ] 验证覆盖主要使用场景
- [ ] 记录完整的验证过程

### 文档质量
- [ ] 信息准确，基于实际验证
- [ ] 图表清晰，逻辑易懂
- [ ] 可作为后续参考和知识传承 
