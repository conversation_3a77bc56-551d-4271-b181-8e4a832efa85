# Mermaid 图表使用最佳实践

## 概述

本文档总结了在项目中使用 Mermaid 图表的常见问题、解决方案和最佳实践，避免重复踩坑。

## 常见语法错误及解决方案

### 1. 换行符问题

**❌ 错误用法**：
```mermaid
flowchart TB
    A[用户操作<br/>包含换行] --> B[下一步]
```

**✅ 正确用法**：
```mermaid
flowchart TB
    A["用户操作，包含换行"] --> B[下一步]
```

**原因**：
- `<br/>` 标签在某些 Mermaid 版本中可能引起解析错误
- 推荐使用中文逗号、句号等标点符号分隔，或使用引号包裹

### 2. 特殊字符处理

**❌ 容易出错的字符**：
- `{}` 花括号
- `===` 等号
- `${}` 变量语法
- `<>` 尖括号
- `()` 圆括号
- ````反引号（特别是代码块标识符）

**✅ 正确处理**：
```mermaid
flowchart TB
    A["current === original ✅"] --> B["${selectedItems} 表达式"]
    C["data.user != null"] --> D["HTML &lt;tag&gt; 标签"]
    E["包含 mermaid 代码块"] --> F["避免使用反引号"]
```

**最佳实践**：
- 包含特殊字符的文本都用双引号包裹
- HTML 标签使用转义：`&lt;` 和 `&gt;`
- 布尔比较、变量引用等都建议加引号
- **避免在节点文本中使用反引号**：特别是代码块标识符会引起语法冲突

### 3. 节点文本格式

**推荐格式**：
- **短文本**：`A[用户登录]`
- **包含标点/特殊字符**：`A["用户登录：验证身份"]`
- **多个描述**：`A["功能1，功能2，功能3"]`
- **技术术语**：`A["API 调用：/api/user"]`

### 4. 子图和样式

**✅ 良好的子图设计**：
```mermaid
graph TB
    subgraph "用户操作"
        A[点击按钮] --> B[输入数据]
    end
    
    subgraph "系统处理"
        C[验证数据] --> D[保存结果]
    end
    
    B --> C
    
    style A fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
```

**最佳实践**：
- 子图标题用引号包裹
- 样式使用标准颜色值
- 避免使用过多颜色，影响可读性

## 图表类型选择指南

### 流程图 (flowchart)
**适用场景**：
- 业务流程展示
- 算法步骤说明
- 系统架构图

**推荐布局**：
- `TB`（从上到下）：适合线性流程
- `LR`（从左到右）：适合时间轴流程
- 复杂流程可混合使用子图

### 时序图 (sequenceDiagram)
**适用场景**：
- 接口调用流程
- 用户交互流程
- 异步操作时序

**注意事项**：
- 参与者名称避免使用特殊字符
- 消息描述保持简洁

### 状态图 (stateDiagram)
**适用场景**：
- 状态机设计
- 生命周期管理
- 工作流状态

### 类图 (classDiagram)
**适用场景**：
- 数据结构设计
- 类关系展示
- API 接口设计

## 性能优化建议

### 1. 图表复杂度控制
- **节点数量**：单个图表建议不超过 20 个节点
- **层级深度**：流程图深度建议不超过 5 层
- **子图数量**：单图子图数量建议不超过 5 个

### 2. 渲染性能
- 避免在同一页面放置过多 Mermaid 图表
- 复杂图表考虑分拆为多个简单图表
- 使用懒加载渲染大型图表

### 3. 移动端适配
- 文字大小适中，避免过小
- 节点间距适当，避免重叠
- 考虑横屏显示复杂图表

## 调试技巧

### 1. 语法错误定位
**常见错误信息**：
- `Parse error on line X`：第X行语法错误
- `Expecting 'XXX'`：期望某个语法元素
- `Unexpected 'XXX'`：意外的字符或语法

**高频错误场景**：
- 连接线标签包含特殊字符：`-->|[text]|` 应改为 `-->|text|`
- 节点文本包含未转义的特殊符号
- 连接线标签中包含管道符嵌套：`|text|text|`

**调试步骤**：
1. 检查错误行的语法格式
2. 确认特殊字符是否正确转义
3. 验证引号配对是否正确
4. 简化图表内容逐步排查

### 2. 预防措施
- 文本内容优先使用双引号包裹
- 避免在节点文本中使用 HTML 标签
- 复杂图表分段编写和测试
- 使用在线 Mermaid 编辑器验证语法

## 团队协作规范

### 1. 命名约定
- **节点ID**：使用有意义的英文缩写（如 `userLogin`、`dataProcess`）
- **子图名称**：使用中文描述功能模块
- **样式类名**：统一前缀，如 `success`、`error`、`warning`

### 2. 文档标准
- 每个复杂图表提供文字说明
- 关键节点添加注释
- 图表更新时同步更新相关文档

### 3. 版本控制
- 图表代码保持格式化和缩进
- 重大修改记录变更原因
- 删除无用的样式和注释

## 工具推荐

### 1. 在线编辑器
- [Mermaid Live Editor](https://mermaid.live/)：官方在线编辑器
- 支持实时预览和语法检查
- 可导出 SVG、PNG 等格式

### 2. VS Code 插件
- **Mermaid Markdown Syntax Highlighting**：语法高亮
- **Mermaid Preview**：实时预览
- **Mermaid Editor**：可视化编辑

### 3. 构建工具
- **mermaid-cli**：命令行工具，支持批量转换
- **@mermaid-js/mermaid**：JavaScript 库，用于项目集成

## 故障排除检查清单

遇到 Mermaid 渲染问题时，按以下顺序检查：

- [ ] 检查语法是否正确（使用在线编辑器验证）
- [ ] 确认特殊字符是否正确转义
- [ ] 验证引号配对是否完整
- [ ] 检查子图和样式语法
- [ ] 确认图表类型是否正确
- [ ] 检查是否超出复杂度限制
- [ ] 验证 Mermaid 版本兼容性
- [ ] 检查浏览器控制台错误信息

## 更新记录

- **2024-01**: 初始版本，总结常见问题和最佳实践
- 后续版本将根据实际使用中遇到的新问题持续更新 
