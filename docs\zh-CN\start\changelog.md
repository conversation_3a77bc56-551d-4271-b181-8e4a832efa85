[组件库issue详设文档](http://wiki.caijj.net/pages/viewpage.action?pageId=251887263)

## v1.90.5

2025-08-26

- Feature
  - `InputTable`组件动作支持获取当前页偏移量`__offset` [#1326](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1326)
  - `confirmTitle`支持表达式 [#1323](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1323)
  - `id`属性支持表达式 [#1327](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1327)
  - `PipeIn`/`PipeOut`支持字符串函数 [#1335](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1335)
- Bug fix
  - `Form`组件配置`canAccessSuperData`失效问题修复 [#1321](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1321)
  - `CRUD`组件里面`InputTable`组件修改数据无效问题修复 [#1324](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1324)
  - `ConditionBuilder`组件自定义配置校验无效问题修复 [#1325](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1325)
  - `ListSelect`组件`disabled`选中值无样式问题修复 [#1328](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1328)
  - `Select`组件移动端单选配置`menuTpl`报错问题修复 [#1329](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1329)
  - `CRUD`组件高级搜索有联动时数据混乱问题修复 [#1333](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1333)

## v1.90.4

2025-08-19

- Bug fix
  - `Table`组件配置`autoFillHeight`时，浏览器窗口大小变化时更新表格高度 [#1316](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1316)
  - `InputTable`组件使用`InputGroup`时内部的表单项修改无法同步到`Form`数据域问题修复 [#1311](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1311)
  - `InputTable`组件`InputGroup`校验问题处理 [#1317](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1317)
  - `Tabs`组件的`title`取不到顶层数据域问题修复 [#1320](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1320)
  - `Container`组件配置`draggableOn`时，切换拖拽状态导致播放中断问题修复 [#1319](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1319)

## v1.90.3

2025-08-12

- Feature
  - `Combo`组件支持配置`tabsCollapseOnExceed`和`tabsCollapseBtnLabel`属性 [#1314](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1314)
  - `Select`组件多选时，`overflowTagPopover`里面的标签也展示`tooltip` [#1309](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1309)
  - `CRUD`组件文档补充`reload`动作及示例 [#1313](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1313)
- Bug fix
  - `CRUD`组件`filter`区域下拉框被`affixHeader`表头遮挡问题修复 [#1306](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1306)

## v1.90.2

2025-08-05

- Feature
  - `UserSelect`组件替换接口为`/idaas/v2/users`、`DepartmentSelect`组件替换接口为`/idaas/orgs/tree`

## v1.90.1

2025-08-05

- Bug fix
  - 弹窗中的`CRUD`配置`autoFillHeight`时高度一直变小问题修复 [#1294](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1294)
  - `InputTable`使用`radios`勾选时，会把值展示出来问题修复 [#1292](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1292)
  - `Combo`组件中`reload`动作导致`Form`数据异常问题修复 [#1302](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1302)
  - `InputFile`组件`value`格式为`Object`问题修复 [#1307](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1307)
  - `NestedSelect`点全选无效问题修复 [#1310](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1310)

## v1.90.0

2025-07-29

- Feature
  - 项目移除`fis`构建&`sdk`打包资源 [#1284](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1284)
  - `Audio`组件播放倍率可保留多位小数 [#1304](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1304)
- Bug fix
  - `GroupContainer`组件的`header`配置`visibleOn`无效问题修复 [#1305](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1305)
  - `Combo`组件开启`reverseMode`后必填校验失效问题修复 [#1303](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1303)
  - `Form`中动态显隐带默认值的`FormItem`，`Table`依赖数据变化但不触发更新问题修复 [#419](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/419)

## v1.89.4

2025-07-24

- Feature
  - `NestedSelect`组件支持配置`maxTagCount`属性 [#1298](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1298)
- Bug fix
  - `DataResourceAuthorized`授权组件异常时的message取值错误问题修复 [#1301](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1301)
  - `CRUD`组件`column`内部组件获取不到`name`对应值问题修复 [#1299](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1299)、[#1300](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1300)
  - `FormItem`配置一个无效的正则表达式校验失败后，提交按钮置灰问题修复 [#1285](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1285)
  - `CRUD`组件高级搜索`filterFormAdvanceSearchAble`开启后，`visibleOn`不展示表单项还是占位置问题修复 [#1289](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1289)
  - `TreeSelect`组件搜索框按回车触发提交问题修复 [#1293](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1293)
  - `CRUD`组件配置`headerFilter`和`filter`默认值时，重置功能失效问题修复 [#1295](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1295)
  - `Form`组件嵌套`CRUD`时，`filter`输入框回车触发`form`提交问题修复 [#1287](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1287)

## v1.89.3

2025-07-17

- Feature
  - `Transfer`相关组件的`tree`模式支持`initiallyOpen`和`unfoldedLevel`属性配置 [#904](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/904)
- Bug fix
  - `GroupContainer`、`Tabs`容器组件配置`validateName`支持`args.validateFields`指定校验失败后分组自动展开 [#1279](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1279)
  - `Form`组件内使用`service`配置动态表单时`label`不展示问题修复 [#1281](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1281)
  - `FormItem`服务端校验信息位置错误问题修复 [#1282](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1282)
  - `Select`组件在`InputGroup`中不会失焦问题处理 [#1283](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1283)

## v1.89.2

2025-07-08

- Feature
  - `DataResourceAuthorized`授权组件新增展示`nickname`字段
- Bug fix
  - `Page`配置`authorizedConfig`时，触发鉴权报错问题修复 [#1274](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1274)
  - `CRUD`组件点击重置不会清空表单问题修复 [#1276](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1276)

## v1.89.1

2025-07-04

- Feature
  - `InputFile`组件支持`static`静态模式 [#1268](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1268)
  - `Combo`组件`tabs`模式配置标题模版中支持配置图标`tabsIconTpl` [#1198](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1198)
  - `GroupContainer`组件的`activeKey`属性支持配置`true`时默认展开 [#1267](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1267)
  - `Combo`组件支持`validate`动作配置`componentName`校验`Combo`内部指定的`Form` [#1265](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1265)
- Bug fix
  - 修复`CRUD`组件配置`loadDataOnce`时在`subTable`中翻页问题 [#1269](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1269)
  - `Typography`组件文本的`tooltip`边界条件展示问题修复 [#916](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/916)
  - `Typography`组件展示空白`tooltip`问题修复 [#1262](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1262)
  - `NestedSelect`级联选择器文本太长时展示问题修复 [#1197](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1197)
  - `NestedSelect`组件`disabled`禁用时，不可触发下拉面板 [#1271](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1271)
  - `NestedSelect`组件单选模式下可以选中多个值问题修复 [#1194](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1194)
  - `CRUD`组件配置`loadDataOnce`后点击重置不会调用接口问题修复 [#1266](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1266)
  - `CRUD`组件使用`source`时，在最后一页`table`中操作`setValue`时翻页组件丢失问题修复 [#1270](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1270)

## v1.89.0

2025-06-26

- Feature
  - `InputImage`组件的tooltip文字支持配置`placeholder`、`placeholderPlacement` [#1206](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1206)
  - `InputTable`组件`FormItem`替换`Form`优化 [#686](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/686)
- Bug fix
  - `formItem`接口报错后，消息提示会自动拼接接口URL问题修复 [#1195](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1195)
  - `Select`组件`change`事件缺少`selectedItems`数据问题修复 [#1250](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1250)
  - `Select`组件`autofill`在`name`为`a.b`的场景下切换选项，值不变问题修复 [#1258](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1258)
  - `Combo`组件静态展示字体样式问题修复 [#1260](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1260)
  - `CRUD`组件的`headSearchable`在弹窗中被遮挡问题修复 [#1263](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1263)
  - `InputTable`组件父级数据域无法隔离问题修复 [#1251](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1251)

## v1.88.2

2025-06-23

- Bug fix
  - `InputTable`组件服务端校验一次后，报错未全部修改无法再次提交问题修复 [#1259](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1259)

## v1.88.1

2025-06-20

- Feature
  - `TableView`组件支持`tr`配置`visibleOn`控制显隐 [#1248](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1248)
  - 优化`Cards`鼠标样式 [#1247](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1247)
  - 站点markdown支持mermaid渲染
- Bug fix
  - `JsonSchemaEditor`组件重复校验不会清除问题修复 [#1246](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1246)
  - `TooltipWrapper`组件内嵌表单，表单校验失败禁止关闭tooltip [#1249](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1249)
  - `InputTable`组件使用schemaApi时，新增的数据操作栏没有内置操作按钮问题修复 [#1238](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1238)

## v1.88.0

2025-06-11

- Feature
  - `Image`组件预览画布支持拖拽 [#1236](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1236)
  - `Combo`组件下的表单项支持支持配置clearValueOnHidden [#1229](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1229)
  - `Icon`图标文档重新梳理 [#1240](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1240)
- Bug fix:
  - `Select`组件的change事件对象获取不到已选项问题修复 [#1242](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1242)
  - `quickEdit`快速编辑配置被Form的static属性影响问题修复 [#1243](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1243)
  - `CRUD`组件模式切换后，SearchBox搜索报错问题修复 [#1244](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1244)
  - `InputTable`组件新增配置`payload`无效问题修复 [#1237](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1237)
  - `InputTree`组件配置canCancelSelectedNode时无法取消选中问题修复 [#1235](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1235)

## v1.87.4

2025-06-04

- Feature
  - `InputTable`组件支持unique校验配置 [#927](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/927)
  - `InputTable`组件新增`isFullValidate`属性，支持跨页校验 [#1120](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1120)
  - `Combo`组件Tabs模式的unmountOnExit支持配置表达式 [#1225](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1225)
  - `DsDateRangePicker`组件支持根据第一个选择的日期时间动态设置结束禁用的日期时间范围 [#1200](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1200)
- Bug fix:
  - `Button`组件`loading`时文字不居中问题修复 [#1222](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1222)
  - `CRUD`组件的`headerFilter`初始化调用2次接口问题修复 [#1223](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1223)
  - `Tabs`组件作为表单项提交的默认值丢失问题修复 [#1228](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1228)
  - `Checkbox`组件的`change`事件中获取不到Form的data问题修复 [#1230](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1230)
  - 事件动作第一个动作有返回值，后续动作解析不到当前组件的值问题修复 [#1224](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1224)

## v1.87.3

2025-05-23

- Feature
  - `Divider`组件可配置标题 [#1220](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1220)
  - `GroupContainer`和`Tabs`组件新增`autoSwitchWhenValidated`属性，开启后表单校验失败自动展开分组&错误元素滚动至视口中 [#1216](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1216)
- Bug fix:
  - `Action`组件无法阻止倒计时问题修复 [#1221](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1221)

## v1.87.2

2025-05-21

- Feature
  - `ChatRoom`组件支持不展示头像 [#1218](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1218)
  - `Dialog`弹窗的`close`支持配置字符串 [#1215](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1215)
- Bug fix:
  - `Typography`组件`tooltip`配置无效问题处理 [#1219](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1219)

## v1.87.1

2025-05-16

- Feature
  - `Tabs`组件新增拖拽成功事件`dragSuccess` [#1208](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1208)
- Bug fix:
  - `Button`组件配置自身loading时，在`Wizard`组件的actions中样式问题修复 [#1209](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1209)
  - `ConditionBuilder`组件拖拽问题修复 [#1205](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1205)
  - `Formula`组件无法实时修改值问题修复 [#1211](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1211)

## v1.86.5

2025-05-12

- Bug fix:
  - `InputFormula`组件展示连接符“-”异常问题修复 [#1201](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1201)

## v1.86.4

2025-05-08

- Bug fix:
  - `InputTable`组件服务端校验问题修复 [#1192](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1192)
  - `Form`组件`validate`动作指定校验错误信息有误修复；支持`clearError`动作 [#1185](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1185)

## v1.86.3

2025-05-07

- Feature
  - `InputTag`组件的`separator`属性支持配置正则表达式 [#1189](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1189)
- Bug fix:
  - 表单组件配置static优先级问题处理 [#1178](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1178)

## v1.86.2

2025-04-29

- Feature
  - `LeftRightContainer`组件新增`collapsable`属性，支持可折叠 [#1155](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1155)
- Bug fix:
  - `NestedSelect`级联组件搜索带孩子节点路径异常问题处理 [#1173](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1173)

## v1.86.0

2025-04-28

- Feature
  - `Textarea`组件新增`insertValue`动作 [#1186](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1186)
- Bug fix:
  - `onEvent`触发动作异常问题处理 [#1177](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1177)

## v1.85.3

2025-04-25

- Bug fix:
  - `ConditionBuilder`组件没有id时节点异常 [#1172](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1172)
  - `InputNumber`组件开放`formtter`和`parser` [#1176](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1176)
  - `Step`组件设置`step`精度展示异常 [#1175](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1175)

## v1.85.2

2025-04-22

- Bug fix:
  - `InputTree`组件切换节点阻止行为异常问题修复 [#1160](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1160)
  - `InputNumber`组件升级rc-input-number解决format回调的input不正确问题 [#1182](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1182)
  - `CRUD`组件持久化排序失效问题修复 [#1183](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1183)

## v1.85.0

2025-04-17

- Feature
  - `InputNumber`组件支持小数模式 [#1176](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1176)
  - `InputNumber`组件支持裁切模式 [#1179](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1179)
- Bug fix:
  - `InputTable`组件包含radios组件方案修改 [#1167](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1167)
  - `TabsTransfer`组件筛选问题问题修复 [#903](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/903)

## v1.84.3

2025-04-14

- Feature
  - `Combo`组件的tabs模式拓展“复制”和禁止拖拽功能 [#1154](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1154)
- Bug fix:
  - `ConditionBuilder`组件再次输入初始值时数据域问题修复 [#1174](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1174)
  - `InputTable`组件在`Wizard`中来回切换时，内置操作列按钮一直增加问题修复 [#1163](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1163)
  - `CRUD`组件前端一次性加载总条数展示问题修复 [#1164](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1164)
  - `InputTable`组件默认值回显问题修复 [#1167](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1167)
  - `TabsTransfer`组件筛选问题问题修复 [#903](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/903)

## v1.84.1

2025-03-28

- Bug fix:
  - `CRUD`组件的搜索项，在被高级搜索收起后，重置功能失效 [#1148](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1148)
  - `InputGroup`组件在combo中设置单位，在静态模式下文字对不齐问题修复 [#1150](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1150)
  - `SearchBox`组件输入中文时按回车键会存在空格（单引号）问题修复 [#1151](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1151)
  - `Audio`组件配置`source`和`autoPlay: true`时，播放状态和图标对不上 [#1152](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1152)
  - `Combo`组件tabs模式标题文字过长时关闭按钮丢失 [#1162](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1162)

## v1.84.0

2025-03-20

- Feature:
  - `Wizard`组件的`step-validate`动作支持失败获取结果 [#1105](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1105)
  - 删除所有packages下的lock文件，通过写死版本号锁版本 [#1129](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1129)
  - `CRUD`、`Table`组件新增`draggableConfig`属性，支持前N条不能拖拽改变顺序 [#1142](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1142)
  - `CRUD`组件列拖拽优化 [#1107](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1107)
  - @dataseed/amis包版本管理优化，防止项目安装amis-core等包版本不一致 [#1131](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1131)
- Bug fix:
  - `InputNumber`组件配置precision精度后，删除会连着删小数点问题修复 [#1134](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1134)
  - `InputTable`组件子表单配置combo多行时，数据只能新增不能删除问题修复 [#1128](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1128)
  - `NestedSelect`级联组件配置`hideNodePathLabel`后，搜索高亮会失效问题修复 [#1126](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1126)
  - `Checkboxs`组件配置`disabledOn`后勾选样式不生效问题修复 [#1041](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1041)
  - `InputTree`组件虚拟滚动高度只展示一行问题修复 [#1116](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1116)
  - `CRUD`组件配置source表达式和headerFilter后翻页失效问题修复 [#1066](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1066)

## v1.83.6

2025-03-12

- Feature:
  - `CRUD`组件性能优化，支持`simplify`极简模式 [#1103](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1103)

## v1.83.4

2025-03-07

- Feature:
  - `CRUD`组件操作列配置tooltip时，阻止双击冒泡到行双击事件 [#1114](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1114)
  - `ConditionBuilder`条件组件初始化时二次渲染优化（`formInited`移除监听） [#1113](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1113)

## v1.83.3

2025-03-05

- Feature:
  - `Form`组件支持配置`renderDialog`、`renderTypography`属性 [#1124](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1124)
  - `ConditionBuilder`条件组件支持配置`strictMode`: true严格模式 [#1113](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1113)

## v1.83.2

2025-03-04

- Feature:
  - `FormItem`组件支持配置`renderDialog`、`renderTypography`属性
  - `DimensionTable`二维表格组件新增`hoverMode`控制鼠标悬浮显示 [#1100](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1100)
  - `ConditionBuilder`条件组件支持竖版风格 [#1123](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1123)
  - `InputTree`组件Maximun报错问题修复 [#1121](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1121)


## v1.83.1

2025-03-04

- Feature:
  - `DataResourceAuthorized`数据资源权限授权组件改动：身份管理平台升级了数据权限底层模型，将内置角色做了优先级判断

## v1.83.0

2025-02-27

- Feature:
  - `Select`多选模式，可自定义选中项tooltip [#1099](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1099)
  - `InputTree`组件支持操作按钮一直展示 [#1083](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1083)
  - `Pagination`分页组件支持简易模式 [#1082](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1082)
  -  `InputTree`组件source接口重新请求后，支持配置clearValueOnSourceChange清空已选项 [#1052](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1052)
- Bug fix:
  - `InputTable`嵌套时，内部的InputTable提交不会校验 [#1068](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1068)
  - dialog嵌套form嵌套左右布局组件，当配置leftTitle时有问题 [#1098](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1098)
  - `CRUD`组件配置一行四列高级搜索时布局问题 [#1096](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1096)
  - `ListSelect`列表组件card模式staitc静态模式样式问题修复 [#1079](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1079)
  - 修复bizui sentry报错：TypeError Cannot read properties of undefined (reading 'data') [#1020](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1020)

2025-02-20

- Feature:
  - 移动端适配
  - `InputFormula`公式编辑器支持`change`事件 [#1062](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1062)
  - `InputTree`组件新增后折叠状态保持 [#1037](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1037)
- Bug fix:
  - 小分组嵌套`FormItem`样式错乱问题修复 [#966](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/966)
  - 条件组件样式问题修复 [#1036](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1036)
  - 修复sentry线上报错：Unable to find node on an unmounted component [#1055](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1055)
  - 弹窗闪一下问题修复 [#980](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/980)
  - `InputTable`校验问题修复 [#971](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/971)
  - `InputTag`组件`static`模式不反显问题修复 [#990](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/990)
  - 分页组件当前激活页码不展示问题修复 [#1082](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1082)
  - `steps`组件value设置为数字0时会默认选中问题修复 [#1067](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1067)
  - 修复`Select`组件下拉框在上面时，搜索会导致样式异常问题 [#950](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/950)
  - `CRUD`组件source本地数据域，setValue新数据时，分页条数不对问题修复 [#1033](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1033)

## v1.81.2

2025-02-19

- Feature:
  - `Button`组件新增幽灵模式按钮 [#1072](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1072)
  - `CRUD`组件新增`hiddenToggableCol`属性，列设置排序禁用时隐藏字段 [#1073](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1073)
  - 电销需求icon扩展 [#1074](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1074)
- Bug fix:
  - 修复头像组件名称设置两个汉字只展示一个的问题 [#1075](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1075)

## v1.81.1

2025-02-17

- Bug fix:
  - `DimensionTable`二维表格组件js换成ts，打包产物优化
  - `LeftRightContainer`容器站点文档报错`window.$$SentryCustom is not a function`处理

## v1.81.0

2025-02-13

- Feature:
  - `CRUD`组件列排序弹窗支持展示tooltip [#1025](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1025)
  - `MatrixCheckboxes`组件支持合并title，并支持设置col宽度 [#1048](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1048)
  - 新增`DimensionTable`二维表格组件 [#997](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/997)
  - 添加权限校验文档 [#1058](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1058)
  - 新增`Control`组件 [#1008](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1008)
  - 错误上报到sentry（select 重复选项） [#1011](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1011)
  - `InputTag`组件新增`delete`事件 [#1045](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1045)
  - `InputTag`组件支持搜索 [#1046](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1046)
  - 事件动作新增`downloadFile`动作 [#1051](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1051)
- Bug fix:
  - `NestedSelect`组件下拉在react17版本不展示问题修复 [#1005](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1059)、[#1053](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1053)
  - 修复控制台css报错Deprecation [#1054](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1054)
  - `CRUD`组件修复columnsToggler详情页面query不同时持久化存储问题 [#1060](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1060)
  - 修复url携带参数进入子页面，离开子页面时，子页面初始化接口重复调用问题 [#1047](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1047)
  - `CRUD`组件dropdownButton“更多”sentry报错修复 [#1049](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1049)

## v1.80.1

2025-02-12

- Feature:
  -  `CRUD`紧凑模式样式优化；无数据不展示分页组件 [#1059](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1059)

## v1.80.0

2025-02-10

- Feature:
  -  `InputText`组件添加`insertValue`动作 [#1003](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1003)


## v1.79.1

2025-01-20

- Bug fix:
  - 【回滚】修复`Select`组件下拉框在上面时，搜索会导致样式异常问题 [#950](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/950)


## v1.79.0

2025-01-16

- Feature:
  - 文档强制开启`standardMode`标准模式（页面示例除外） [#983](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/983)
  - 矩阵组件`MatrixCheckboxes`支持鼠标框选选择功能 [#974](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/974)

- Bug fix:
  - 修复`InputTree`组件选中背景异常 [#935](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/935)
  - 修复`ConditionBuilder`自定义后静态展示问题 [#938](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/938)
  - 修复`Input`配置options模式时下拉遮挡问题 [#963](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/963)
  - 修复`CRUD`组件列搜索value过长不展示label [#954](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/954)
  - a标签样式隔离 [#972](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/972)

## v1.78.1

2025-01-09

- Bug fix:
  - 修复`Select`组件下拉框在上面时，搜索会导致样式异常问题 [#950](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/950)

## v1.78.0

2025-01-09

- Feature:
  - 增加`Select`、`TreeSelect`组件options选项中，value 重复后提示 [#864](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/864)
  - `InputTree`、`TreeSelect`组件新增checkAll属性 [#888](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/888)
  - `CRUD`组件兼容本地数据footerToolbar设置空数组之后不展示全量数据 [#961](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/961)

- Bug fix:
  - 修复`Panel`组件配置visibleOn后，body中的visibleOn无效 [#887](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/887)
  - 修复`Combo`配置unique后下拉选项数据丢失问题 [#923](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/923)
  - 修复 schema 配置 boolean 时死循环问题 [#928](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/928)
  - 修复`CRUD`组件配置affixHeader时表头超出样式问题 [#885](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/885)
  - 监听hash变化更新顶层__query数据域 [#959](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/959)
  - `ButtonToolbar`组件设置maxCount后，导致按钮重复渲染 [#969](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/969)
  - `TabsTransfer`组件数据过多时选中节点会自动滚动到底部，底部存在大量空白区域 [#922](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/922)
  - `InputTable`组件拖拽状态下隐藏操作按钮 [#918](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/918)
  - `InputTable`组件add动作报错 [#973](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/973)


## v1.77.0

2024-12-26

- Feature:
  - 删除“数据映射”文档中doAction解析$的示例 [#906](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/906)
  - 新增图例组件`Legend` [#912](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/912)
  - 权限组件改造，新增`authInited`属性 [#878](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/878)
  - `ListSelect`组件修改选中样式 [#910](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/910)

- Bug fix:
  - 修复tooltip渲染schema时位置偏移 [#909](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/909)
  - 修复`code`组件没有值时format报错 [#915](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/915)
  - 修复`CRUD`组件重置不会回到第一页 [#914](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/914)

## v1.76.1

2024-12-20

- Bug fix:
  - 修复#867引起的CRUD组件filter初始化回显问题

## v1.76.0 (废弃 ⚠️)

2024-12-19

- Feature:
  - 站点增加智能问答入口 [#876](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/876)
  - `CRUD`、`InputTable`组件嵌套子表格支持按数据唯一标识展开、折叠 [#824](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/824)
  - `Picker`组件内敛模式支持不展示已选中项 [#891](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/891)
  - `Audio`组件支持动作触发播放、暂停，且支持隐藏 [#895](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/895)
  - `FormItem`组件新增depencies属性，收集依赖字段做效验 [#905](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/905)

- Bug fix:
  - 修复`subTable`同级配置onEvent时，子层级配置的onEvent无效 [#870](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/870)
  - 【回滚】修复filter获取不到page层api响应数据问题 [#867](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/867)
  - 修复`InputTable`组件在非第一页时，使用setValue更新当前数据出现展示错误 [#896](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/896)
  - 修复`InputTable`组件确认模式点击取消后数据展示有问题 [#894](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/894)


## v1.75.0

2024-12-05

- Feature:
  - 事件支持配置 stopDomPropagation属性，阻止React原生事件冒泡 [#830](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/830)
  - CRUD组件添加fetchInited事件 [#854](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/854)
  - schemaApi支持自动转换 standardMode 模式 [#868](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/868)

- Bug fix:
  - npm发包打标问题修复 [#863](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/863)
  - CRUD组件重置默认搜索值异常问题修复 [#860](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/860)

## v1.74.0

2024-11-28

- Feature:
  - 站点编辑器支持注册自定义 filter [811](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/811)
  - select 组件新增 selectedTooltip 属性 [857](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/857)
  - inputTag 新增属性pasteImmediateAdd [764](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/764)
  - DsDatePicker日期组件支持自定义 presets [822](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/822)

- Bug fix:
  - 修复 CRUD 操作列 button 绑定 onEvent 事件偶现触发异常 [839](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/839)
  - 富文本sentry报错问题 [825](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/825)

## v1.73.2

2024-11-28

- Feature:
  - InputTable 组件新增分批渲染属性 batchNum [786](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/786)

## v1.73.0

2024-11-21

- Feature:
  - Button 组件添加 countDownConfig 配置支持细节控制倒计时功能 [795](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/795)
  - 新增虚拟列表组件 VirtualList [828](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/828)

- Bug fix:
  - 修复 TooltipWrapper 组件 Content 渲染被上层数据域干扰 [812](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/812)
  - 修复 文档代码编辑抽屉关闭问题 [841](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/841)
  - 修复 TabsTransfer 组件的搜索问题 [801](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/801)
  - 修复 TreeSelect 的禁用状态下 maxTagCount 没有 tooltip [819](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/819)
  - 修复 Crud 的 cards模式 headerFilter模式的问题 [814](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/814)

## v1.72.2

- Bug Fixed
  - 修复日期范围组件无法清空值 [840](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/840)


## v1.72.1

2024-11-18

- Docs
  - 规范更新
  - 最佳实践：表格单行轮询获取任务状态


## v1.72.0

2024-11-14

- Feature
  - 内置 CHECKAUTHORITY 鉴权方法 [548](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/548)
  - 日期选择器 change 事件新增 presetValue [779](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/779)
  - DataResourceAuthorized 数据资源权限授权组件, query接口调用监听入参 [821](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/821)

- Bug Fixed
  - 同步 url 参数，高级搜索项能记住但查询不生效 [797](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/797)
  - 弹窗中配置日期组件选择日期弹窗会关闭问题 [796](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/796)
  - 修复 Container 开启 draggable 属性时，意外出发内部组件click事件 [804](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/804)
  - 修复 ButtonGroupSelect 在开发环境下提示 KEY不唯一Warning [800](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/800)
  - 修复 typography 组件导致页面出现滚动条的问题 [807](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/807)


## v1.71.1

2024-11-08

- Feature
  - 新增标准规范功能 [755](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/755)、[754](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/754)

## v1.71.0

2024-11-07

- Feature
  - dev 环境添加 schemaCheck [778](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/778)
  - Card header.description 支持渲染schema[783](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/783)
  - tree-select 搜索交互优化[792](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/792)
  - crud&table 增加 rowDbClick 事件[777](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/777)
  - 添加 chatRoom 组件[780](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/780)
  - dataseed-pro add sideEffects: false [758](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/758)
  - input清空触发change事件 [693](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/693)
  - 添加 dialog schemaCheck [793](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/793)

- Bug Fixed
  - 修复 condition-builder 自定义节点，编辑节点消失 [790](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/790)
  - 优化鉴权组件数据同步问题 [688](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/688)
  - 修复 Card 组件文案未折叠问题 [785](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/785)
  - 修复 inputRating 垂直居中 [781](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/781)
  - 修复 input-table 切换分页数量之后，数据渲染错乱 [806](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/806)


## v1.70.0

2024-10-31

- Feature
  - itemClassNameExpr表达式支持获取折叠状态 [#722](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/722)
  - log amis version [#742](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/742)
  - crud 添加 `filterRowNum` 属性控制高级搜索行数 [#772](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/772)
  - CRUD 增加 defaultSeletecdItems 默认选项设置,添加 clearSelection 事件，增加子表格批量选择最佳实践 [#734](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/734)

- Bug Fixed
  - dialog open动作导致数据域变化问题 [#700](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/700)


## v1.69.0

2024-10-24

- Feature
  - editor组件的 size 属性增加full值 [#653](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/653)

- Bug Fixed
  - 修复 Tabs 组件 activeKey 配置确定值时不生效问题 [#759](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/759)
  - Typography 组件多行文本没超出时也会出现 tooltip [#737](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/737)
  - log 组件兼容接口返回空的情况 [#757](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/757)
  - InputRichText 组件输入操作异常[#751](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/757)


## v1.68.0

2024-10-17

- Feature
  - form 操作拦截提示[#387](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/387)
  - group 中 select样式问题[#667](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/667)
  - Action组件支持 downloadFile 动作，替换原有 download 动作[#712](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/712)
  - editor组件在iframe和qiankun模式下光标偏移问题处理 [#738](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/738)
  - 优化 Wirzard,Panel affixFooter 功能实现 [#718](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/718)
  - 扩展 schemaPlugin 插件机制 [#761](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/761)


- Bug Fixed
  - scrollIntoView 失效问题[#723](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/723)



## v1.67.2

2024-10-14

- Bug Fixed
  - 解决react降版本后`findDOMNode`报错 [#731](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/731)


## v1.67.0

2024-10-10

- Feature
  - 支持 日期范围组件预设区间 近XX天可选到今日 [#710](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/710)
  - 添加 Options addControls,editControls 文档描述[#514](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/514)
  - editor 的 editorDidMount 支持 string格式 [#575](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/575)
  - 编辑器支持运行辅助函数[#714](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/714)
  - formItem组件文档添加注意事项[#717](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/717)
  - inputTree 文档补充 [#719](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/719)
  - 调整dropdown 浏览器默认无效[#713](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/713)
  - Picker 组件选项选中之后展示 与 Select 展示样式保持统一[#560](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/560)
  - input-array 新增 change 事件[#374](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/374)

- Bug Fixed
  - 修复拖拽崩溃问题[#705](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/705)
  - 修复 input-table 新增行点取消也能成功[#566](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/566)
  - crud 批量操作按钮禁用状态失效问题[#711](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/711)



## v1.66.0

2024-09-26

- Feature
  - amis站点从react18.2.0降级到16.8.6 [#663](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/663)
  - `InputRichText`富文本组件添加预览模式 [#679](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/679)
  - 新增拖拽组件 [#698](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/698)
  - 支持当api响应的msg为空时，不弹toast提示 [#562](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/562)
- Bug Fixed
  - 修复`audio`组件音量调节UI展示问题 [#682](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/682)
  - 修复`Form`组件提交时，`ajax`动作获取表单项数据异常问题 [#691](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/691)
  - 修复`dialog`动作的dialog配置中获取不到`ajax`动作响应数据问题 [#691](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/691)
  - 修复`JsonSchema`组件value与schema值不匹配时展示异常问题 [#694](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/694)
  - 修复`InputTable`组件`reUseRow:match`时，可能导致报错问题 [#583](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/583)
  - 修复`Table`组件的“嵌套子表格”示例中刷新子表格问题 [#626](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/626)
  - 修复`Authorized`组件，默认权限校验改为false [#688](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/688)
  - 修复form表单动作校验时无法滚动到第一个错误位置 [#692](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/692)
  - form添加reactionFormLazyChange属性,控制被动修改form时是否延迟change [#642](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/642)

## v1.65.1

2024-09-23

- Bug Fixed
  - 修复`FormItem`组件配置`mode: normal`时冒号样式错位问题

## v1.65.0

2024-09-20

- Feature
  - `InputTree`添加`searchConfig`属性文档说明 [#630](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/630)
- Bug Fixed
  - `InputSubForm`在static模式下，编辑icon无法去除问题修复 [#543](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/543)
  - `FormItem`组件`label`属性支持数组schema问题修复 [#670](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/670)
  - `CRUD`组件配置filter的高级搜索时奔溃问题修复 [#685](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/685)
  - 修复`InputRichText`输入时会整个页面卡死 [#689](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/689)
  - `InputTable`修复配置`maxLength`属性后，数量超出新增按钮未隐藏问题 [#617](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/617)

## v1.64.1

2024-09-14

- Bug Fixed
  - `InputRichText`初始化赋值时偶尔报错问题修复 [#684](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/684)

## v1.64.0

2024-09-12

- Feature
  - `JSONSchemaEditor`组件增加必填校验 [#616](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/616)
  - `JsonSchema`组件同步Amis代码，新增`namePlaceholder`、`valuePlaceholder`属性和校验逻辑 [#628](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/628)
  - `InputFormula`表达式支持多行展示；修复虚拟滚动失效问题 [#631](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/631)
  - `audio`组件支持配置接口获取文件 [#640](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/640)
  - `CRUD`组件开启`loadDataOnce`后，支持列搜索 [#633](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/633)
  - 优化`ServerError`错误上报数据携带`httpStatus`，收敛sentry报错 [#607](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/607)
  - icon示例整理；添加导出icon、详情icon [#549](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/549)
- Bug Fixed
  - `CRUD`组件修复`headSearchable`配置无法获取数据域数据问题 [#641](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/641)
  - `Wizard`组件修复自定义actions按钮事件触发异常问题 [#639](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/639)
  - `FormItem`组件添加`blockFormInit`属性，解决`CRUD`的filter表单block列表加载 [#632](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/632)

## v1.63.3

2024-09-09

- Bug Fixed
  - `Table`组件的`toggled`属性为false时，本地持久化失效问题修复 [#639](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/639)

## v1.63.2

2024-09-05

- Bug Fixed
  - `CRUD`在弹窗中使用相同的CRUD会导致列设置被同步问题修复 [#601](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/601)
  - `Inputtree`组件defer模式下，hover行按钮不展示问题修复 [#657](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/657)

## v1.63.0

2024-09-05

- Feature
  - 集成 Authorized 鉴权功能 [#548](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/548)
  - `CRUD`组件的`draggableOn`表达式支持获取自己的数据域 [#615](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/615)
  - `Step`组件新增`tooltipVisible`属性 [#606](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/606)
  - `iframe`添加`iframeData`属性，用于`iframe`通信 [#620](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/620)
  - 新增`postMessage`动作，用于`iframe`通信 [#621](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/621)
  - `InputRichText`组件支持focus、blur、change事件，支持关闭上传图片 [#635](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/635)
  - `Table`支持`toggleExpanded`动作 [#544](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/544)
  - 新增uuid过滤器、公式 [#600](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/600)
  - 补充`CRUD`的`popover`的示例，位置：`列表页-常规列表-基础列表(不知总条数)`
  - 补充`JSONSchema Editor`的示例，位置：`编辑页-基础表单`
  - 补充`Steps`组件未通过状态，位置：`详情页-步骤表单(带标题)`
  - 补充`label`为`error`类型的示例，位置：`弹窗-列表（中号）-调用失败`
  - 补充`mapping`组件默认值为空的示例 [#505](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/505)
  - 补充图片集弹窗没有数据时的空状态 [#589](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/589)
  - 补充`Inputgroup`在`CRUD`的表单项搜索中的场景，位置：`列表页-常规列表-基础列表`
  - 新增辅助函数`getCRUDHeader`及文档
- Fix
  - 去除`编辑页-基础表单`内`picker组件`多余的右上角x及修复上方间距

## v1.62.1

2024-09-03

- Feature
  - `InputTable`新增`reUseRow`属性 [#483](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/483)

## v1.62.0

2024-08-29

- Feature
  - 新增身份管理平台权限组件`data-resource-authorized`组件 [#547](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/547)
  - `InputTable`新增弹窗支持自定义挂载节点 [#592](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/592)
  - `InputTable`新增“新增子级”按钮 [#590](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/590)
  - 重构`编辑页-分组表单`示例代码
  - 新增`generateCommonPage`辅助函数及文档
  - 新增`generateSpace`辅助函数及文档
  - 新增`generateFlexItems`辅助函数及文档
  - 新增`generatePanelHeader`辅助函数及文档
- Bug Fixed
  - `CRUD`和`InputTable`的selectedChange问题修复 [#483](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/483)、[#493](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/493)
  - `CRUD`配置headSearchable时，清空已选参数，放大镜表单中还存在问题修复 [#611](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/611)
  - `CRUD`的clear动作清理数据后接口还存在问题修复 [#579](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/579)
  - 修复`InputTable`配置`selectable`后新增项报错的问题 [#583](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/583)
  - `InputTable`的canAccessSuperData数据和界面不同步问题修复 [#553](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/553)
  - `InputTable`的columnsTogglable默认改成false [#593](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/593)
  - 修复`condition-builder`static模式展示dragable问题 [#595](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/595)
  - 修复`InputFormula`组件 variables 变量不更新问题及展示样式修复 [#573](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/573)
  - 修复辅助函数目录下`Dialog`目录重复问题

## v1.61.2

2024-08-26

- Bug Fixed
  - 修复`dialog`弹窗打不开问题

## v1.61.1(废弃 ⚠️)
废弃原因：dialog弹窗打不开

2024-08-23

- Feature
  - `CollapseGroup`组件的activeKey支持变量解析 [#541](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/541)
  - 新增`Watermark`水印组件；`Pdf`组件支持水印配置 [#545](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/545)
  - `Dialog`、`Drawer`组件支持自定义挂载位置 [#558](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/558)
  - `CRUD`组件新增reset、clear动作 [#400](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/400)
  - `Combo`的tabs模式新增activeKey属性和changeActiveKey动作 [#273](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/273)
  - `Table`组件使用sticky重构 [#489](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/489)
  - `Table`组件的affixRow、prefixRow支持固定列 [#538](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/538)
  - `InputTable`新增一行时跳转到新增位置 [#557](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/557)
  - 新增详情页card组件的示例，位置：`详情页-卡片带标题`
  - 新增左右布局场景，位置：`详情页-表单左右布局SQL查询`
  - 新增表单局部刷新场景，位置：`详情页-基础表单`中的付款金额表单项
  - 新增`InputTree`仅样式禁用，操作仍可用的示例，位置：`列表页-左右布局-左侧搜索+右侧带标题`中第一项
  - 新增按钮局部刷新示例，位置：`列表页-带全局操作按钮`
- Bug Fixed
  - 修复`link`的body不支持html标签 [#520](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/520)
  - `Table`组件affixHeader固顶表头样式问题修复 [#538](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/538)
  - 修复`弹窗-tabs模式带分组`中分组表单间距问题
  - 修复辅助函数模块内`InputFile`无法展示的问题

## v1.60.2

2024-08-16

- Bug Fixed
  - 修复`Pagination`组件下拉分页条数样式问题 [#564](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/564)

## v1.60.1

2024-08-15

- Feature
  - `inputFormula`组件升级 [#516](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/516)、[#528](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/528)、[#529](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/529)、[#204](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/204)
  - `InputGroup`配置prefix和suffix前后缀能将内容存到form表单里面 [#526](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/526)
  - 新增表单项带warning类型的`alert`示例，位置：`编辑页-基础表单`
  - 新增toast提示弹窗，包含带跳转、不带跳转、成功三种状态，位置：`弹框-提示类弹框`
  - 新增group内表单项必填的示例，位置：`编辑页-基础表单`
  - 补充图片类型的icon，位置：`图标示例`
  - 新增`弹框-步骤+分组表单`示例
  - 新增辅助函数文档说明，能确认版本号的已确认版本号，部分过于早期的辅助函数暂未标明版本号：`getResizePageSchema`、`getRightCRUDForTreeListSchema`、`getGlobalActionButtonListSchemaV2`、`getBatchOperateListSchemaV2`、`getBasicListSchema`、`getCompactModeListSchemaV2`、`getBasicListSchemaV2`、`getBasicTagSchema`、`getCopy`、`getCollapsableGroupPanelForWhiteBgSchemaV2`、`getCollapsableGroupPanelForWhiteBgSchema`、`getDialogGroupPanelNoPaddingSchema`、`getImagesDialogSchema`
- Bug Fixed
  - `Select`组件在window电脑上文本显示不全问题修复 [#177](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/177)
  - `InputPassword`组件static模式下，revealPassword属性无效问题修复 [#248](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/248)
  - `CRUD`组件配置headerFilter时，翻页失效问题修复 [#533](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/533)
  - `Modal`弹窗遮挡dropdown下拉列表(popover)问题修复 [#534](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/534)
  - `CRUD`组件批量选择展示区域添加背景色 [#522](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/522)
  - `CRUD`组件当未查询到数据时，下一页按钮仍可点击问题修复 [#476](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/476)
  - `TreeSelect`搜索结果异常问题修复 [#530](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/530)
  -  修复autoFill填充时未清除已填充的数据 [#527](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/527)
  -  `editor`编辑器处理groovy报错问题修复 [#517](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/517)
  - sum计算精度问题修复 [#249](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/249)
  - `列表-中号`优化内边距
  - `弹框-tabs模式带分组`优化内边距
  - `弹框-版本对比`去除多余符号，新增tooltip及修复左右高度不一致的问题
  - 修复`弹框-tabs模式带分组`中分组panel边距有误问题
  - 去除`弹框-试运行日志弹窗`多余下边距
  - `提示类弹窗`优化icon间距
  - 去除弹窗保存校验时的多余红色报错提示
  - 修复`getImagesDialogSchema`的数据源支持source

## v1.59.1

2024-08-13

- Feature
  - `InputFile`文件上传成功支持多信息展示 [#242](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/242)
  - `Combo`的tabs模式加强模式无数据时展示占位符 [#467](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/467)
- Bug Fixed
  - 修复`InputTable`组件 deleteItem 动作不能读取数据域上的数据 [#540](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/540)

## v1.59.0

2024-08-08

- Feature
  - `Action`组件支持请求时自身loading [#277](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/277)
  - `Form`表单事件动作validate、submit支持返回结果 [#377](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/377)
  - `TreeSelect`添加popOverContainerSelector，配置弹层挂载位置 [#523](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/523)
  - `InputTree`添加itemClassNameExpr属性 [#499](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/499)
  - `InputTree`动作expand新增openValues参数 [#270](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/270)
  - `Select`组件添加max属性，限制最大勾选数量 [#281](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/281)
  - 新增`列表页-常规列表-单个搜索`示例，用于展示只有单个搜索时搜索项在左侧的场景
  - 新增配置按钮组角标的辅助函数`getButtonGroupWithBadgeSchema`及配套文档
  - 补充不同`searchBox`宽度的示例
  - `编辑页-基础表单`补充`inputGroup`组件使用`prefix`场景
  - 优化详情页版本对比展示形式

## v1.58.0

2024-08-02

- Feature
  - `textarea`组件静态模式支持复制 [#508](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/508)
  - `log`日志组件改造，支持http请求 [#509](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/509)
  - `InputTree`组件支持itemDisabledOn，能够通过行数据及上层数据域来计算当前节点禁用 [#500](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/500)
- Bug Fixed
  - 修复`link`配置body时，当body为空，展示了href（body优先级应最高） [#520](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/520)
  - CRUD-卡片模式：顶部搜索栏先输入过滤条件，滚动列表页后吸顶的顶部搜索栏中，过滤条件不见了 [#266](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/266)
  - `InputText`组件文档移除 creatable 描述，不支持业务端配置 creatable，使用默认行为 [#507](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/507)
  - `NestedSelect`组件，当配置onlyLeaf、hideNodePathLabel、searchable时，会将父节点也搜索出来，但是不可选中问题修复 [#502](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/502)
  - 修复`Transfer`树形跟随模式 在 disabled 时 结果仍可删除 [#513](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/513)

## v1.57.1

2024-07-26

- Bug Fixed
  - 修复`link`的body为`SchemaNode`时无法显示的问题 [#520](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/520)

## v1.57.0

2024-07-25

- Feature
  - `InputGroup`组件支持前后缀 [#510](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/510)
  - `InputTree`组件支持取消选中 [#475](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/475)
  - `InputTag`支持labelField和valueField [#240](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/240)
  - `InputFile`和`InputImage`组件支持粘贴上传 [#495](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/495)
  - `Picker`组件支持传modalMode对应组件的属性 [#494](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/494)
  - `FormItem`的label和`CRUD`的查询区域label，超出宽度省略号且tooltip提示 [#469](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/469)
  - 新增`getGlobalActionButtonInputTableSchema`辅助函数文档说明并优化间距问题
  - 新增`getButtonList`辅助函数文档说明
- Bug Fixed
  - `InputTree`组件开启虚拟滚动后虚拟列表计算错误问题修复 [#511](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/511)
  - `InputTable`的delete需要触发change事件 [#486](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/486)
  - `InputTable`更改作用域问题，删除fullData [#269](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/269)、[#418](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/418)
  - `InputTable`每页条数修改时展开子表格问题修复 [#484](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/484)
  - 修复collapse和fieldSet样式问题 [#496](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/496)
  - `Wizard`组件的stepValidateSucc事件被触发多次问题修复 [#485](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/485)
  - `InputText`组件在disabled下去掉省略号 [#432](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/432)
  - `Select`组件在表格模式下，选项disabled时该项未展示为灰态问题修复 [#453](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/453)
  - 修复抽屉辅助函数文档无法显示的问题
  - 修复站点无法搜索最佳示例和辅助函数的问题
  - 修复`弹框-提示类弹窗-基础表单-下方提示`为`弹框-提示类弹窗-基础表单-上方提示`

## v1.56.1

2024-07-22

- Feature
  - 新增辅助函数文档：`getWizardInDrawer`
  - 新增辅助函数：`getWizardInDrawer`
- Bug Fixed
  - `Table`组件columns配置表达式页面崩溃问题修复 [#506](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/506)


## v1.56.0

2024-07-18

- Feature
  - 增加`trackExpression`属性，解决数据域被阻断的情况 [#472](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/472)
  - `condition-builder`组件支持按钮支持配置、自定义条件能获取index和层级参数、条件组能支持公共配置 [#404](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/404)
  - `condition-builder`组件支持static模式 [#399](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/399)
  - `CRUD`和`Table`组件支持列高亮 [#487](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/487)
  - `CRUD`和`Table`组件支持某列自动滚动在视口中 [#488](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/488)
  - `input-tree`组件节点禁用状态时，节点上的按钮可展示+操作 [#492](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/492)
  - 新增updatePristineAfterStoreDataReInit属性 [#337](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/337)、[#403](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/403)、[#461](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/461)
  - `编辑页-基础表单`新增`ButtonGroupSelect`
  - 新增辅助函数文档：`generateBasicForm`、`generateBasicFormV2`、`getDialogGroupPanelSchema`、`generateNoPaddingWrapper`、`generateNoPaddingCollapse`
  - 新增辅助函数：`generateNoPaddingWrapper`、`generateNoPaddingCollapse`
- Bug Fixed
  - `CRUD`组件，使用列的headSearchable+input-group功能，第一次点搜索时头部筛选条件中对下拉显示值为空，点击清空时，表头下拉值未清空问题修复 [#458](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/458)
  - `inputFile`组件分片上传需配置startChunkApi才能自动开启 [#454](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/454)
  - `select`的labelField属性设置后，会造成select设置maxTagCount样式异常 [#434](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/434)
  - 修复`列表页-左右布局-搜索+分组表单`示例间距
  - 修复`列表页-左右布局-顶部搜索+左右布局(左搜索 右分组表单)`示例间距
  - 修复`编辑页-分组表单`下方列表操作区域未对齐问题
  - 调整`generateCollapseGroup`间距为16px
  - 修复`编辑页-基础表单（带页面标题）`有多组保存按钮的问题


## v1.55.0

2024-07-11

- Feature
  - `Combo`Tabs模式在只读态模式下，当无数据时，展示样式调整 [#468](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/468)
  - image和images组件支持自定义容器触发预览 [#474](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/474)
  - `CRUD`组件废弃canAccessSuperDataInCell属性 [#371](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/371)
  - `编辑页-基础表单`新增可折叠的`fieldSet`示例
  - `编辑页-基础表单`新增Tab校验飘红及确认后新增功能示例
  - `弹框-提示类弹窗`新增多行弹窗示例
  - 扩展新辅助函数`generateNoPaddingWrapper`用于提供可配置边距`Wrapper`
  - 扩展辅助函数`generateMediaImageCard`用于提供可配置卡片图片比例
  - `基础表单-多对多`新增右上角复制、自定义按钮功能
- Bug Fixed
  - `Combo`组件向下间距问题修复 [#480](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/480)
  - `InputTable`组件列数过多超出父容器宽度，超出部分可以滚动 [#433](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/433)
  - `Combo`组件multiple模式下关闭icon的tooltip错位问题修复 [#462](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/462)
  - input-group中是select组件，当select多选选择多个时，已选中标签不会换行问题修复 [#431](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/431)
  - `列表页-Tab模式-卡片列表`使用`CRUD`替换原`Cards`组件写法
  - `列表页-卡片模式-带单个搜索`移动搜索框至右侧、调整样式


## v1.54.0

2024-07-04

- Feature
  - `Pdf`组件src支持data参数 [#455](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/455)
  - `Tooltip`支持`getPopupContainers`属性，自定义挂载到用户指定dom [#456](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/456)
  - inputGroup校验方法报错后，控制台没有抛出错误 [#451](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/451)
  - inputTable支持分页配置，如：切换每页条数、总条数展示、跳转 [#383](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/383)
  - input-file增加api下载示例说明 [#412](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/412)
  - SearchBox搜索框支持size属性 [#407](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/407)
  - input-tree添加/编辑表单增加level字段说明 [#478](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/478)
  - `编辑页-基础表单`新增邮箱的`InputArray`示例
  - 新增`列表页-常规列表-基础金额列表`示例，用于展示列对齐在右侧的场景
  - 新增`弹框-提示类弹框-基础表单-下方提示`示例，用于针对弹框按钮进行全局提示的场景
  - 新增`详情页-对比列表`示例，用于合并表头的对比场景
  - 新增`抽屉-基本抽屉-中号抽屉/大号抽屉`示例，根据实际业务场景选择抽屉尺寸
  - 补充`列表页-常规列表-基础列表`操作列第二行删除按钮的toolTip效果
  - 补充`图标示例`中提示icon
  - 新增辅助函数`generateInputFile`，用于替换`InputFile`，传入static参数即可实现纯展示效果
  - 新增辅助函数`getGlobalActionButtonInputTableSchema`，用于快捷使用`InputTable`组件
  - 新增辅助函数`getRangeItems`，用于处理表单项中的区间
  - 新增`详情页-版本对比(编辑器)`示例

- Bug Fixed
  - form中动态显隐带默认值的formItem, table数据域变化会导致不触发更新问题修复 [#419](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/419)
  - input-group组件支持validateOnChange属性功能 [#425](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/425)
  - textarea组件在static模式下高度问题修复 [#449](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/449)
  - input-kv组件新增会清空已输入值问题修复 [#460](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/460)
  - 修复`分组表单(小分组)`第二步复杂信息的示例对齐问题

## v1.53.1

2024-07-03

- Feature
  - `Combo`组件内置行新增按钮；`itemRemovableOn`表达式支持获取索引`index`和全量数据`__items` [#465](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/465)
  - `Combo`组件Tabs模式优化：溢出时新增固定右侧；static模式不展示删除icon。 [#439](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/439)
  - `Combo`组件Tabs模式拓展：校验失败对应title飘红，同时定位到第一个错误Tab；新增时支持弹窗表单。 [#440](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/440)

- Bug Fixed
  - `InputTag`组件separator配置空格 " "后，批量添加tag，回车无反应问题修复 [#471](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/471)


## v1.53.0

2024-06-27

- Feature
  - `fieldSet`支持展开、收起靠右对齐 [#441](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/441)
  - `Combo`组件支持复制和自定义按钮操作 [#409](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/409)
  - Button/Tabs/Alert等组件图标支持自定义icon；icon统一图标渲染器。 [#430](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/430)
  - 示例补充：`详情页-基础表单`图片预览示例
  - 示例补充：新增`弹框-穿梭框`示例
  - 示例补充：新增`颜色示例`中`接口类型_颜色示例`，在`详情页-基础表单`中展示接口示例
  - 示例补充：`列表页-常规列表-基础列表`中删除按钮的气泡确认框示例
  - 示例补充：新增`弹框-试运行日志弹窗`示例
  - 示例补充：新增详情页带全局操作按钮示例，`详情页-基础表单`展示下方全局操作按钮，`详情页-基础表单(保存按钮吸顶)`展示上方全局操作按钮
  - 示例补充：`编辑页-基础表单`新增自定义配置checkbox行数量示例
  - 示例补充：新增列表页内信息超阈值的红色示例，位于`列表页-常规列表-基础列表`中
  - 示例及辅助函数补充：新增辅助函数generateHeaderV2处理页面标题超长情况，示例位于`列表页-基础表单(带页面标题)`
  - 示例及辅助函数补充：新增`基础表单(中号)-中号垂直表单`示例及辅助函数getVerticalFormSchema用于垂直布局form
  - 示例及辅助函数补充：新增辅助函数`getDialogGroupPanelNoPaddingSchema`获取无padding的`Panel`组件
  - 示例及辅助函数补充：新增辅助函数`generateCardV2获取无padding`的`Card`组件
  - 示例及辅助函数补充：新增辅助函数`generateNoMarginInputTable获取无padding`的`InputTable`组件

- Bug Fixed
  - `Collapse`折叠器内嵌fieldSet组件点击收起时将折叠器一同收起问题修复 [#448](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/448)
  - 表单提交后，滚动到报错地方的API有兼容问题处理 [#447](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/447)
  - `InputFile`组件文件上传时如未配置提示文案，去除下方间距 [#444](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/444)
  - `InputTag `标签选择器max属性不生效和第一次复制进去不去重问题修复 [#443](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/443)
  - `Tabs`组件新增按钮样式问题优化 [ #446](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/446)
  - `code`组件没有值的时候控制台报错修复 [ #426](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/426)
  - service 包裹input-group后校验无法通过问题修复 [ #424](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/424)
  - `InputTable` 使用自定义addButton 在 needConfirm 校验时maxLength失效问题修复 [ #438](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/438)
  - `CRUD`组件新增属性canAccessSuperDataInCell阻断cell获取上层数据 [ #371](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/371)
  - 【回滚】`form`组件修复setValue修改顶层数据域后，表单reset失效问题 [#403](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/403)
  - 修复`详情页-Tab模式(带分组)`邮箱表单项的下边距问题
  - 修复`版本对比(表单)`的部分列toolTip不展示问题

## v1.52.1

2024-06-24

- Bug Fixes
  - `Form`组件无限触发onChange问题修复 [#420](https://github.com/baidu/amis/pull/9919)


## v1.52.0

2024-06-20

- Feature
  - 新增`Typography`组件，文本超出自动tooltip [#422](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/422)
  - `input-table`支持单独控制新增、删除、编辑按钮显示;配置新增时无需选择弹窗 [#241](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/241)
  - 示例优化：`编辑页-基础表单`新增带副标题的radio
  - 示例优化：`编辑页-tab模式`新增tabs带？提示
  - 示例优化：`详情页-tab模式`新增tabs带？提示

- Bug Fixes
  - `Combo`组件syncFileds数据和界面不同步问题修复 [#420](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/420)
  - `crud`嵌套子表格时，外层columns操作列右/左侧固定，展开子表格时固定列样式错乱问题修复 [#367](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/367)
  - `alert`组件在必填表单项中出现文字对齐样式问题修复 [#427](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/427)
  - `form`组件修复setValue修改顶层数据域后，表单reset失效问题 [#403](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/403)


## v1.51.1

2024-06-17

- Bug Fixes
  - condition-builder组件解决autoFill的问题 [#421](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/421)


## v1.51.0

2024-06-13

- Feature
  - `Options`组件增加clearValueOnSourceChange属性，source依赖数据域变化时自动清空已选值 [#405](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/405)

- Bug Fixes
  - form中的按钮会触发submit事件问题修复 [#423](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/423)
  - textarea数据为空，在static状态下未展示任何内容问题修复 [#382](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/382)
  - crud配置searchable后，点击搜索报错问题修复 [#429](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/429)
  - condition-builder组件拷贝问题修复；顶层组去掉删除组按钮 [#421](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/421)
  - tab模式（带分组）模版函数，使用visible控制panel显示问题修复 [#393](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/393)


## v1.50.1

2024-06-07

- Feature
  - `Button`组件的icon和rightIcon支持tooltip配置 [#363](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/363)
  - `editor`组件支持groovy高亮、添加代码格式化、自定义toolbar [#391](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/391)
  - `code`组件暴露属性wrapComponent到文档；增加maxHeight属性 [#408](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/408)

- Bug Fixes
  - `input-tree`节点自定义按钮dropdownbutton悬浮无法选中问题修复 [#257](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/257)
  - `tree-select`校验失败后边框不会标红问题修复 [#406](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/406)
  - 优化inputFile组件的description展示逻辑，和其他表单项样式统一 [#380](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/380)
  - `input-group`配置两个select, 高度不一致问题修复 [#402](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/402)
  - input-number、input-group等表单组件的placeholder缩进保持一致 [#372](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/372)


## v1.50.0(废弃 ⚠️)
废弃原因：`Button`组件的icon间距问题 [#363](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/363)

2024-06-06

- Feature
  - `Button`组件的icon和rightIcon支持tooltip配置 [#363](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/363)
  - `editor`组件支持groovy高亮、添加代码格式化、自定义toolbar [#391](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/391)
  - `code`组件暴露属性wrapComponent到文档；增加maxHeight属性 [#408](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/408)

- Bug Fixes
  - `input-tree`节点自定义按钮dropdownbutton悬浮无法选中问题修复 [#257](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/257)
  - `tree-select`校验失败后边框不会标红问题修复 [#406](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/406)
  - 优化inputFile组件的description展示逻辑，和其他表单项样式统一 [#380](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/380)
  - `input-group`配置两个select, 高度不一致问题修复 [#402](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/402)
  - input-number、input-group等表单组件的placeholder缩进保持一致 [#372](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/372)


## v1.49.1

2024-06-03

- Feature
  - `InputTable`新增`transferAllData`属性，控制表格行更新时，单元格元素是否渲染 [#416](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/416)
  - 示例场景补充：新增辅助函数getWithoutMarginsCRUDSchemaV2及示例，位置：弹框-列表（中号）

- Bug Fixes
  - `CRUD`列过滤配置select组件，当options为object类型时页面崩溃 [#415](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/415)


## v1.49.0(废弃 ⚠️)

2024-05-30

- Feature
  - `FormItem`补充`validateOnBlur`文档 [#280](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/280)
  - `placeholder`支持表达式 [#396](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/396)
  - `Wizard`向导组件支持副标题 [#397](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/397)

- Bug Fixes
  - `CRUD`表头展示的“已选条件”内容过程会造成和其他元素重叠问题修复 [#263](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/263)
  - `Combo`中select组件配置接口搜索时，部分场景新增按钮消失问题修复 [#388](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/388)
  - 在Dialog的actions中, 应用内跳转页面时，body标签上的is-modalOpened css类未及时清理问题修复 [#365](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/365)
  - `InputTable`里一个字段使用InputSubForm子表单，子表单里的InputTable点新增问题修复 [#276](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/276)


## v1.48.1

2024-05-28

- Feature
  - 新增中号弹窗辅助函数`getDialogGroupPanelSchemaV2`
  - 新增超过两行展示...的辅助函数`getCellByTwoLineEllipsis`
  - 示例场景补充：新增配置icon示例，位置：`图标示例`
  - 示例场景补充：`Form`表单项联动场景，包括带label和不带label，位置：`编辑页-基础表单`
  - 示例场景补充：tag标签随单元格宽度自适应，位置：`列表页-常规列表-基础列表`

## v1.48.0

2024-05-23

- Feature
  - `Tabs`组件`title`支持schema [#360](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/360)
  - `pdf`组件新增`enlargeTrigger`，支持自定义放大触发器 [#362](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/362)
  - 文档优化：在Form/FormItem的组件文档页面-属性表中，添加`staticOn`属性描述 [#366](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/366)
  - 示例场景补充: 新增弹窗点选跳转带收藏功能示例，位置：`弹框-卡片列表(中号)`
  - 示例场景补充: 新增标签对比场景，位置：`详情页-版本对比(表单)`
  - 示例场景补充: 新增标题及保存按钮上方吸顶的示例，位置：`编辑页-Tab模式(按钮吸顶)`
  - 示例场景补充：新增授权和工单icon示例
  - 新增页面示例：位置：`详情页-操作日志`
  - 示例优化：`编辑页-tab模式`新增按钮上分割线
  - 示例优化：`详情页-基础表单`新增归属部门加强态
  - 示例优化：`编辑页-基础表单`新增支持选择器模式的`InputText`
  - 示例优化：`编辑页-分组表单`中的可编辑表格新增`TextArea`在编辑默认1行最多2行，行数可配置的示例
  - 示例优化：优化左右布局中左侧的边框实现方法

- Bug Fixes
  - `CRUD`组建修复条件搜索区域和列搜索区域的查询，重置等问题 [#373](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/373)
  - `InputGroup`中禁用表单样式未置灰 [#353](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/353)


## v1.47.0

2024-05-16

- Feature

  - `Card`组件新增`metaClassName`属性
  - `Table`、`Crud`文档新增`toggable`属性示例 [#345](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/345)
  - `Combo`组件tab模式新增定位最新tab [#310](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/310)

- Bug Fixes

  - `Image`修复`enlargeCaption`无效问题 [#351](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/351)
  - icon点击后打印大量警告 [#318](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/318)
  - transfer在弹窗独自存在时，添加最大高度class样式
  - 解决合并单元格后padding不一致 [#338](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/338)
  - 解决日期时间组件圆角问题 [#364](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/364)


## v1.46.2

2024-05-14

- Feature

  - `Select`组件新增`selectedTooltipMode`和`selectedTooltipPlacement`处理已选值tooltip [#179](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/179)
  - 新增`pdf`预览组件 [#340](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/340)
  - `InputFile`组件新增预览模式 [#352](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/352)
  - 示例场景补充：弹出框增加图片集示例，位置：`弹框--图片集`
  - 示例场景补充：详情页新增数字卡片突出展示，位置：`卡片模式(单行居中)`
  - 示例优化：`详情页--版本对比(表单)`增加过长文字示例，展示省略号及`tooltip`提示
  - 示例优化：`编辑页--基础表单`的创建人选项增加`Select`组建的多`subTitle`示例

- Bug Fixes

  - 表单name为空时页面崩溃 [#342](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/342)
  - 日期选择器校验失败没有红色边框 [#348](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/348)
  - crud查询时，在input中输入值，然后点击清除按钮，再点击查询，会将空值带到请求的url中 [#347](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/347)
  - `inputTable`组件validate指定校验无效 [#344](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/344)
  - `inputTable`组件不同行联动校验必填项校验条件不能动态修改 [#355](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/355)
  - `InputNumber`适配`InputGroup`组件样式 [#357](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/357)
  - `service`设置loadingConfig: { show: false }时，包裹`button`组件loading问题修复 [#317](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/317)


## v1.46.1 (废弃 ⚠️)

2024-05-10

- Feature

    - 示例场景补充：弹出框增加图片集示例，位置：`弹框--图片集`
    - 示例场景补充：详情页新增数字卡片突出展示，位置：`卡片模式(单行居中)`
    - 示例优化：`详情页--版本对比(表单)`增加过长文字示例，展示省略号及`tooltip`提示
    - 示例优化：`编辑页--基础表单`的创建人选项增加`Select`组建的多`subTitle`示例

## v1.46.0 (废弃 ⚠️)

2024-05-09

- Feature

  - `Select`组件新增`selectedTooltipMode`和`selectedTooltipPlacement`处理已选值tooltip [#179](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/179)
  - 新增`pdf`预览组件 [#340](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/340)
  - `InputFile`组件新增预览模式 [#352](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/352)

- Bug Fixes

  - 表单name为空时页面崩溃 [#342](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/342)
  - 日期选择器校验失败没有红色边框 [#348](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/348)
  - 当表单的父数据域变化时，表单的初始值会被初始化为当前值 [#337](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/337)
  - crud查询时，在input中输入值，然后点击清除按钮，再点击查询，会将空值带到请求的url中 [#347](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/347)
  - `inputTable`组件validate指定校验无效 [#344](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/344)
  - `inputTable`组件不同行联动校验必填项校验条件不能动态修改 [#355](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/355)
  - `InputNumber`适配`InputGroup`组件样式 [#357](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/357)
  - `service`设置loadingConfig: { show: false }时，包裹`button`组件loading问题修复 [#317](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/317)


## v1.45.1

2024-04-28

- Feature

  - 基础表单增加上传图片示例，位置：`编辑页--基础表单` 上传图片表单项
  - 详情页combo多层嵌套，位置：`详情页--基础表单(多对多)`
  - 模式切换，不同模式下的内容支持自定义展示符合规范的内容，位置：`列表页--多模式切换 (通用)`

## v1.45.0

2024-04-26

- Feature

  - `Select`组件扩展`sameOptionChange`，可配置选择相同的选项是否执行change事件，默认执行 [#170](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/170)
  - `DsDatePicker`组件支持`static` [#168](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/168)
  - 示例优化：全局子标题样式统一
  - 示例场景补充：编辑页-基础表单(each批量显示多个)，位置：`编辑页--基础表单（each批量显示多个）`
  - 示例场景补充：详情页-基础表单(each批量显示多个)，位置：`详情页--基础表单（each批量显示多个）`

- Bug Fixes

  - `inputTree`设置`extraActions`，`type`为button且有多个元素时，行数据过长会导致样式问题 [#319](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/319)
  - `combo`嵌套的`inputTable`组件，当列设置`required`为true 时，必填项 红色* 位置跑偏 [#255](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/255)
  - `combo`性能问题，编辑后更新到form中的速度很慢 [#231](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/231)
  - `remark`组件和文字一起排列展示时，`remark`没有和文字居中对齐 [#323](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/323)


## v1.44.1

2024-04-23

- Feature

  - 编辑页左右布局：搜索+右侧编辑可折叠分组，位置：`编辑页--搜索+右侧编辑可折叠分组` [#329](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/329)
  - 给按钮增加 Tooltip 提示示例，位置：`编辑页–分组表单 第五步的功能按钮规范`
  - 表单中，n对m的场景示例，位置：`编辑页--基础表单（多对多）`
  - 表单项中，表单项有alert 提示场景示例：`编辑页 - 分组表单  其他富文本上方`
  - 列表页标题带有icon场景示例，位置：`列表页--左右布局--左侧搜索+右侧带标题`
  - 弹框tabs模式可折叠分组，位置：`弹框--tabs模式带分组`
  - 列表页增加无总数分页示例，位置：`列表页--常规列表页--基础列表（不知总条数）`

## v1.44.0

2024-04-19

- Feature

  - `Select` 组件扩功能，增加`clearSearchValue`动作清空搜索值和`autoClearSearchValue`属性配置下拉区域消失是否清空搜索值 [#115](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/115)
  - `TooltipWrapper` 组件悬浮层支持，在悬浮层内点击按钮可关闭悬浮层（仿antd Popconfirm组件） [#321](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/321)
  - 示例优化：列表页-卡片模式-基础列表示例优化
  - 示例场景补充：颜色示例-生命周期类型_颜色示例（补充中间阶段） [#328](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/328)
  - 示例场景补充：编辑页-基础表单（补充`Select`下拉区域tooltip和多行展示示例）
  - 示例场景补充：页面示例-详情页-分组表单(带搜索)（增加第三步-combo组件-文档下载）


- Bug Fixes

  - 修正 `Modal` 投屏后，弹窗按钮上方分割线看不到 [#315](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/315)
  - 修正 弹窗分组示例的panel在设置visibleOn: false时分割线divider未隐藏 [#309](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/309)


## v1.43.0

2024-04-11

- Feature

  - `Card` 组件支持标题、副标题配置schema，且优化Card作为选项时增加鼠标悬浮高亮效果 [#308](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/308)
  - `InputText` 组件多选模式下，对新增下拉选项改造，切支持自定义文案 [#300](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/300)
  - `CRUD` 组件保留条目选择功能，放在列搜索条件反显栏位下方功能优化 [#288](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/288)
  - `Link` 组件扩功能，支持自定义默认展示文档 [#312](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/312)

- Bug Fixes

  - 修正 `CRUD` 组件查询条件特定操作会出现异常 [#299](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/299)
  - 修正 `CRUD` 组件列设置+列排序操作报错 [#314](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/314)
  - 修正 `ConditionBuilder` 组件-添加条件-打开下拉框时页面崩溃 [#285](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/285)
  - 修正 `InputTable` 组件确认模式，点击保存时必填项没有校验 [#324](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/324)
  - 修正 `Combo` 组件内嵌套 inputTable 切换 column 数据没有渲染 [#330](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/330)
  - 修正 `Spinner` 组件包裹 editor 组件，并且 show 为false 时 editor 组件会一直loading [#245](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/245)
  - 修正 `Form` 组件校验指定字段，会校验到非指定字段 [#274](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/274)
  - 修正 `CRUD` 组件升级1.37.0后，更改其sendOn会重复2次请求 [#311](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/311)

- Docs

  - `CRUD` 组件补充 QuickEdit 配置信息


## v1.42.0

2024-04-06

- Feature

  - 新增示例: 编辑页-Tab模式、Tab模式带分组。
  - 新增示例：列表页-左右布局：左侧分组表单+右侧可折叠分组。
  - 新增示例：详情页-卡片示例。
  - 新增示例：详情页-带页面标题。
  - 新增示例：弹窗-卡片列表（中号）。
  - 新增示例：弹窗-提示类弹窗：加重关键词突出显示。
  - 示例优化: 将原示例中 className 优化提取用 pmhelp 中变量替代。
  - 示例优化: 列表-卡片模式一行一个场景、列表页-左右布局-分组表单+卡片列表场景。
  - 示例优化：列表页-基础列表：新增复制 icon 和提示类 icon 场景。
  - 示例优化：编辑页-分组表单：新增输入表格，带批量全局操作按钮和单个搜索且存在总结行场景。
  - 示例优化：弹窗-分组表单（table上下布局）：新增复制 icon 场景。


## v1.41.0

2024-03-28

- Feature

  - `Combo` 组件的tabsmode下增加切换tab销毁props。[#310](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/310)

## v1.40.0

2024-03-26

- Feature

  - `FormItem` 组件属性 name 支持表达式解析。[#192](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/192)
  - 新增示例: 编辑页-步骤表单（带分组）。
  - 新增示例: 弹窗-基础表单(一个主按钮)。
  - 新增示例: 详情页-表单紧凑模式。
  - 示例优化: 详情页-分组表单 & 可折叠分组表单，标题下划线保持一致，以分组表单为准。
  - 示例优化: 列表页-CRUD 批量操作按钮左右间距对齐规范，10px改为8px。
  - 示例优化：列表页-左右布局 搜索+分组表单（补充左右布局右侧区域缺省图）。
  - 示例优化：列表页-左右布局 顶部搜索+左右布局(左搜索 右分组表单)（补充左右布局右侧区域缺省图）。
  - 示例场景补充：列表页-常规列表 基础列表（补充状态 tag 展示和链接跳转示例）。
  - 示例场景补充：列表页-常规列表 基础列表（补充表格左下角统计总数示例）。
  - 示例场景补充：详情页-版本对比(表单) 增加嵌套表格。
  - 示例场景补充：编辑页-分组表单（补充 inputTable 嵌套字内容示例）。

- Bug Fixes

  - 修正 `InputTable` 表单项固定列校验状态未显示问题。[#178](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/178)。
  - 修复 `CRUD` 组件配置 footerToolbar 为分页时，共 XX 条，展示为共 XX 页问题。

- Style

  - 修复 `Select` 组件多选模式下，placeholder 左侧多出8px间距问题。[#295](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/295)。


## v1.39.1

2024-03-20

- Bug Fixes

  - 修复列表页-紧凑模式间距样式不一致问题。[#292](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/292)

## v1.39.0

2024-03-20

- Bug Fixes

  - 恢复 `InputTable` 组件删除逻辑

## v1.38.1

2024-03-19

- Feature

  - 增强 `Combo` 组件配置,增加添加成功回调`addSuccess`。[#298](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/298)。

## v1.38.0

2024-03-15

- Feature

  - 新增示例：编辑页-分组表单（补充 inputTable 示例）。
  - 新增示例：弹窗-详情基础表单。
  - 新增示例：弹窗-表单项 输入框栏位为上传文件按钮。
  - 示例优化：图标：新增警告类 icon、成功类 icon。
  - 示例优化: 列表页-左右布局-分组表单：树操作按钮放在左上角。
  - 示例优化: 编辑页-分组表单（带有辅助按钮的表单项）。
  - 示例优化: 弹窗-提示类（带有关联关系）（需突出的信息组）。
  - 示例优化: 编辑页-分组表单（补充左侧输入框+按钮占1/3场景）。
  - 示例优化: 列表页示例：去掉 dialog 右上角关闭按钮。

- Bug Fixes

  - 修正 `InputTable` 组件外包裹 form，行内取不到变更数据问题。[#246](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/246)
  - 修正 `InputTable` 嵌套表格场景，父子表格频繁更新，会出现更新失败问题。[#218](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/218)

- Style

  - 优化 `DsTimePicker` `DsTimeRangePicker` `DsDatePicker` `DsDateRangePicker` 圆角样式: 6px, 宽度自适应。[#265](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/265)

## v1.37.2

2024-03-11

- Bug Fixes

  - inputtable 新增删除问题，页面错乱，没有及时更新数据域，嵌套子表格带条件选择器。[#269](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/269)


## v1.37.1

2024-03-08

- Feature

  - 新增示例：详情页-Tabs模式下分组表单+无标题分组.
  - 新增示例：详情页-Tabs模式下可折叠分组表单+无标题分组.
  - 示例优化: 版本对比示例，删除、修改状态样式调整

## v1.37.0

2024-03-07

- Features
  - 增强 `CRUD` 组件配置 headSearchable 配置项时，自动收集参数并点亮列搜索 icon，同时回显列搜索选项。[#225](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/254)
  - 增强 `CRUD` 组件持久化缓存功能，区分hash路由和history路由，优化生成的存储key。[#254](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/254)
  - Input-Text 组件在多选模式下期望能通过hover查看到选择值的全部内容。[#267](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/267)

- Bug Fixes

  - 优化 `InputTable` 组件在嵌套在 Dialog 里面引起的性能优化。[#256](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/256)
  - 修正 `CRUD` 列快速搜索顶部已选条件无法回显的问题。[#259](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/259)
  - 修正 `CRUD` 表头筛选区的重置按钮点击后会清理掉列表表头的筛选条件问题。[#264](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/264)
  - 修正 `default` 过滤器设置默认值时，在处理值为0或空字符串时会取默认值问题。[#252](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/252)
  - 修正 `CRUD`头部列过滤点击搜索和重置分页未切换至第一页问题。


## v1.36.0

2024-03-01

- Bug Fixes

  - 修复 `InputNumber` 组件如果值是 0, static 为 true 时会展示为 - 问题。[#247](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/247)

- Docs

  - 示例调整：列表页示例命名优化。
  - 示例调整：列表页示例分类。
  - 示例调整：顶部搜索+左右布局（左边搜索右边分组表单）
  - 示例调整：tab模式相关示例，示例内容优化
  - 新增示例：详情页-版本对比(多条数据对比表格)。
  - 新增示例：详情页-Tab模式(带可折叠分组)。

## v1.35.0

2024-02-27

- Feature

  - `CRUD` 新增基于amis内置子母表格的级连选择功能。 [#233](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/233)

- Bug Fixes

  - 修复 `Images` 组件预览关闭按钮展示不明显问题。[#227](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/227)
  - 修正 `Tabs` 组件在使用source，切source是数组的场景下，下发的数据不是最新的data数据问题。[#236](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/236)
  - 修正 `table` 表格设置固定列，然后滑动，滑动到最边上的时候表格对齐会偏问题。[#124](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/124)


- Style

  - 优化table边框颜色。
  - 优化table单元格合并展示样式，表格展示边框

## v1.34.3

2024-02-23

- Bug Fixes

  - `CRUD` 修复开启 autoGenerateFilter 后，筛选区的筛选条件会回显在列表上方；点击 headSearchable 某一列的重置筛选条件，会清理筛选区的所有筛选条件。[#253](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/253)


## v1.34.2

2024-02-22

- Feature

  -  新增示例：可搜索 Card 列表-左右布局.

## v1.34.1

2024-02-20

- Feature

  - `Table` 组件为列增加合并单元规则配置功能，增加 column 自定义 type 的场景。[#235](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/235)


## v1.34.0

2024-02-01

- Feature

  - `Table` 组件为列增加合并单元规则配置功能，当前版本不支持自定义 column.type 的单元格合并，若遇到当前场景，请使用1.34.1版本。[#235](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/235)

- Bug Fixes

  - 修正 `inputText` 鼠标移入移出抖动问题。
  - 修正 `CRUD` 组件配置 bulkActions 属性，初始化渲染报错问题。[#244](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/244)

- Docs

  - 示例调整：列表页-批量操作+非追随查询。
  - 新增示例：列表页-CRUD 紧凑模式。
  - 新增示例：列表页-可搜索 Card 列表。
  - 新增示例：详情页-顶部搜索+左右布局。
  - 新增示例：列表页-全局按钮+批量按钮+非追随查询。
  - 新增示例：列表页-列设置+批量按钮+列搜索。
  - 新增示例：列表页-列设置+列搜索。
  - 新增示例：列表页-全局按钮+单个搜索。
  - 新增示例：列表页-标题+单个搜索。
  - 新增示例：列表页-顶部搜索+左右布局。
  - 新增示例：弹窗-二次确认+带详情信息。
  - 新增示例：弹窗-关联信息提示。
  - 新增示例：弹窗-步骤表单。
  - 新增 `CRUD` 组件 list 模式，分页和非分页功能。[#237](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/237)

## v1.33.2

2024-02-01

- Bug Fixes

  - 修复 `InputTable` 组件上层disabled变化后，未更新输入项disabled状态问题。

## v1.33.1

2024-01-29

- Bug Fixes

  - 列表页的辅助函数新增 (fn)V2 版本内置 autoGenerateFilter。
  - 列表页的辅助函数回退内置 autoGenerateFilter 逻辑。
  - 修正 `Tabs` 组件中文特定场景切换 tab 报错问题。[#239](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/239)


## v1.33.0

2024-01-22

- Feature

  - `Button` 拓展属性 linkWithoutPadding，支持去掉 link 类型按钮内边距。[#224](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/224)
  - `Group` 拓展属性 withoutMarginBottom，支持去掉Group FormItem下边距。[#224](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/224)
  - `Form` 拓展属性 withoutItemMarginBottom，支持去掉FormItem下边距。[#224](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/224)
  - `stepItemClassName` 拓展属性 stepItemClassName，支持自定义单个步骤容器 CSS 类名。[#224](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/224)
  - 卡片列表切换辅助函数内置事件处理。
  - 新增crud无内外边距辅助函数

- Bug Fixes

  - 修正 `ConditionBuilter` 组件自定义选项有默认值，options无效问题。[#216](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/216)
  - 修正 `CRUD` 组件不会触发 selectedChange 事件问题。[#191](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/191)

- Docs

  - 新增示例：编辑页-基础表单补充 inputGroup 场景。
  - 新增示例：列表页-tabs + 左右布局。
  - 新增示例：新增基本抽屉、Tab 模式抽屉。[#234](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/234)
  - 新增示例: 编辑页步骤表单 单步提交按钮固定底部。[#224](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/224)
  - 新增示例：列表页-Tabs + 搜索 + 分组列表。[#224](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/224)

## v1.32.2

2024-01-18
  - Bug Fixes

    - 修正 `InputTag` 组件中文输入问题以及回车触发表单提交造成的假死问题。[#228](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/228)
    - 修正 `amis` 在地板本 safari 浏览器报错问题。[#345](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/345)


## v1.32.1

2024-01-15

- Feature

  - 新增 `getFixedHeaderWrapperSchema` 辅助函数，支持页面标题吸顶功能。[#223](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/223)
  - 列表页的辅助函数内置autoGenerateFilter。 ⚠️

- Docs

  - 新增页面示例：弹窗：提示文案带icon、提示文案关键词用中括号标识。[#221](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/221)
  - 新增页面示例：列表页：设置列+列排序、columns 带tooltip(表达式形式)。
  - 新增页面示例：编辑页：基本表单(标题吸顶)。

## v1.32.0

2024-01-11

- Feature

  - `InpuTable` 组件拓展 extraFooterConfig 属性，支持自定义配置额外的footer内容。[#222](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/222)

- Bug Fixes

  - 修复 `InpuTable` 组件删除最后一页的所有数据，不会跳转到上一页问题。[#212](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/212)
  - 修复 `InpuTable` 组件设置showIndex属性，不同分页存在相同数据时序号错乱问题。[#219](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/219)
  - Select 组件在 tree 模式下，单选操作，搜索结果无法回显问题 [#206](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/206)
  - Transfer 组件分页后，list的搜索模式下，勾选问题。[#220](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/220)
  - InputFormula 组件条件筛选逻辑修复。[#213](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/213)

## v1.31.1

2024-01-05

- Bug Fixes

  - `TreeSelect` 组件删除单个item场景补充。[#201](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/201)
  - `Table` 补充 headSearchable 配置列搜索场景。[#205](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/205)
  - 修复 `Table` 发生数据切换，且前后数据差别不大场景下，合并单元格功能异常。

## v1.31.0 (废弃 ⚠️)

2024-01-03

- Feature

  - Transfer提供分页功能：包括服务端分页和前端加载全量数据分页。[#200](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/200)

- Bug Fixes

  - InputTable 组件操作栏字体大小统一调整为14px。[#215](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/215)
  - `Table` 修固定列展示空白问题。[#214](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/214)

- Docs

  - 优化版本对比-表单页面示例。
  - 新增版本对比-表格页面示例。
  - 新增弹窗-左右布局页面示例。
  - 新增列表页-左右布局+分组表单页面示例。
  - 在列表页-tab模式页面示例中新增左右布局。

## v1.30.0 (废弃 ⚠️)

2023-12-29

- Perf

  - 优化 `Table` 更新性能。

## v1.29.0

2023-12-27

- Features

  - `InputTree` 拓展属性 autoFillHeight，支持树组件高度自适应。
  - `CRUD` 拓展 filterFormAdvanceSearchAble 属性，支持 filter 查询表单高级搜索功能。[#187](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/187)
  - `Tag` 原 displayMode 属性拓展 bordered 类型。

- Bug Fixes

  - 修复 `Table` 底部展示模式，底部展示部分数据渲染格式错误。[#211](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/211)
  - 修复 `NestedSelect` 搜索功能，搜索结果中，搜索关键字对应的字符丢失问题。[#210](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/210)

- Style

  - 提供的样式 css 中 max-h-none 由 0 改为 none。

- Docs

  - 页面示例提供颜色示例。

## v1.28.0

2023-12-22

- Features

  - `InputTable` 优化性能。
  - `ResizeContainer` 拓展新组件，支持拖拽变改变容器宽高。

- Bug Fixes

  - 修复 `TreeSelect` 节点在禁用状态下，点击删除按钮清除选中态的问题。[#201](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/201)

- Style

  - 修复 `TreeSelect` 下拉箭头颜色异常问题。
  - 修复 `CRUD` 在排序后确认按钮的颜色问题。

## v1.27.1

2023-12-21

- Bug Fixes

  - 修复 `InputTable` 确认模式下，编辑更新单元格后，点击取消，数据未重置。
  - 修复 `CRUD` 列搜索与其他搜索方式同时使用时，列搜索提交时，其他搜索方式的参数仍是旧数据问题。[#205](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/205)
  - 修复 `InputTree` 搜索栏中文输入会带入拼音问题。[#203](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/203)
  - 调整 `InputTree` 搜索匹配规则：从单个字符进行匹配 改为 包含字符串进行匹配。

- Docs

  - 修复 `【详情页-Tab标签+分组】页面示例` 在设置动态visible时，两两分组间距异常问题。[#207](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/207)

## v1.27.0

2023-12-19

- Features

  - `InputTree` 组件拓展 setSearchValue 动作，支持设置搜索栏数据。[#203](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/203)

- Docs

  - 新增 `页面示例` 列表页左右布局，左侧可调整宽度。

## v1.26.0

2023-12-13

- Features

  - `CRUD` 组件拓展 unsetQueryParams 属性，支持设置重置、清空过滤条件时，仍需保持不变的查询参数。[#198](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/198)

- Docs

  - 新增 `页面示例` 版本对比、弹窗上下布局页面示例。
  - 补充 `页面示例` 分组带小标题场景。

## v1.25.0

2023-12-07

- Features

  - ConditionBuilder 增加操作符工具属性 toolbarMode，扩展支持百度 amis 样式。
  - InputFormula 拓展属性 removeDefaultFunctions，支持去除默认的函数配置。[#197](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/197)

## v1.24.3

2023-12-07

- Bug Fixes

  - 修复 `InputTable` 确认模式下，非第一页未进行校验问题。[#193](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/193)
  - 修复 `InputTable` 确认模式下，有编辑态数据时，新增按钮未禁用问题。[#193](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/193)
  - 修复 `CRUD` 配置的 topToolbar，会重复渲染在快速搜索的筛选栏中。
  - 调整 `CRUD` alwaysShowPagination 属性默认值为 true。
  - 修复 `Remark` 提示内容 content属性不支持 html 标签问题。[#194](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/194)


- Docs

  - 补充 `页面示例` 单个搜索 + 列表 / 卡片模式切换场景。
  - 补充 `页面示例` 页面标题右侧展示 tag 及输入框结合按钮场景。
  - 修复 `页面示例` 左右布局列表页示例，InputTree 的边框无法自定义问题。

## v1.24.2

2023-12-04

- Bug Fixes

  - 修复 `CRUD` 列搜索方式进行搜索，页码不重置问题。[#186](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/186)
  - 修复 `CRUD` 快速过滤、快速搜索组合使用时，快速搜索缓存的快速过滤对应数据不更新问题。[#186](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/186)

- Style

  - 优化 `CRUD` 操作为删除时，confirmText 弹窗确认按钮改为主题色。

- Docs

  - 新增 `页面示例` Form + Tabs 组合的详情页。
  - 优化带搜索的分组详情页页面模版样式。

## v1.24.1

2023-11-30

- Features

  - `CRUD` 组件拓展 select、clearAll 动作。[#188](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/188)

- Bug Fixes

  - 修复 `Tree` 相关组件渲染异常问题。
  - 修复 `CRUD` 搜索、重置逻辑，批量操作按钮设置禁用问题。[#188](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/188)
  - 修复 `Form` 在 service 更新 data 后，form body取值问题。[#159](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/159)

## v1.24.0 (废弃 ⚠️)

2023-11-30

- Features

  - `Button` 组件拓展 level 支持 text 类型。
  - `ButtonGroupSelect` 组件新增 visibleDivider 属性，支持两两按钮之间设置 | 分割线。
  - `InputTree` 组件拓展 unfoldedLevel 属性，支持变量或表达式。
  - `InputTree` 组件新增 extraActions 属性，支持自定义结点操作。
  - `InputTree` 组件新增 additionable 属性，支持配置单个结点上的extraActions。

- Bug Fixes

  - 调整 `Table` 组件 affixHeader 属性默认值，默认自动关闭固定表头。
  - 修复 `InputTree` 组件触发 expand 和 collapse 动作，结点不展示或收起问题


- Style

  - 优化 `CRUD` 顶部工具栏间距样式。
  - 优化 `Wizard` 按钮区间距样式。

- Docs

  - 新增 `页面示例` 带搜索的分组详情页。
  - 优化卡片切换列表页、纵向分步编辑页页面模版样式。

## v1.23.0

2023-11-28

- Features

  - `Remark` 组件支持 content 属性配置schema。
  - `TooltipWrapper` 组件支持 content 属性配置schema。
  - `FormItem` 组件调整 labelRemark 默认 icon。
  - `Table` 组件调整 remark 默认 icon。

- Style

  - 优化 `Remark` 边框、字体色样式。

- Docs

  - 新增 `页面示例` 可折叠分组表单、带 tooltip 提示的列表页。

## v1.22.0

2023-11-24

- Features

  - `CRUD` 组件支持 mode 配置动态变量。
  - `CRUD` 组件新增 query 动作，支持跨组件联动过滤。

- Style

  - 优化 `CRUD` card 模式下，内容展示区边距样式。

- Docs

  - 新增 `页面示例` 弹窗、详情页及卡片模式相关列表页、左右布局列表页、纵向分步编辑页。

## v1.21.1

2023-11-23

- Style

  - 优化 `Page` 样式：去掉padding。

- Docs

  - 优化 `页面示例` 列表页带全局操作按钮和编辑页分组表单。


## v1.21.0

2023-11-16

- Features

  - `Table` 组件拓展 sortMultiple 属性，支持单列排序。[#182](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/182)
  - `CRUD` 组件拓展 columnSort 事件。[#182](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/182)
  -  `CRUD` 组件新增 headerFilter 属性，支持 table 模式下设置非追随过滤条件，当表单项数据发生变化时，会把数据更新到当前 store，刷新列表。
  - `InputTree`、`Select` 组件支持 menuTpl 自定义配置html模版时，搜索关键字高亮。[#171](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/171)
  - 新增 `页面示例`。

- Bug Fixes

  - 修复 `DropDownButton` 组件应用在table操作列，且设置触发方式为 hover 时，鼠标移出按钮操作区，下拉菜单未自动关闭问题。[#172](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/172)
  - 修复 `CRUD` 组件分页持续点击重复调用接口。[#135](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/135)

- Style

  - 优化 `Dialog` 最大高度设置80%，超出滚动。

## v1.20.0

2023-11-09

- Features

  - `Page`、`Service`、`Table`、`Form`、`CRUD` 组件拓展 spinnerSize 属性，支持自定义设置 Spinner 尺寸。[#151](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/151)

- Bug Fixes

  - 修复 `CRUD` filter设置搜索栏后，点击clear按钮，请求接口中仍存在 url 中所携带的参数问题。[#175](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/175)

- Style

  - 优化 `InputTree` 选中行样式。
  - 优化 `Spinner` 尺寸。[#151](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/151)
  - 优化 `ShowMore` 组件字体大小为14，action的size默认值改为default。[#131](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/131)
  - 优化 `Remark` 圆角、背景色、提示内容的默认位置等样式。[#130](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/130)
  - 优化 `CRUD` 圆角样式。[#129](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/129)

- Docs

  - 补充Service组件隐藏loading动画的示例。[#152](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/152)

## v1.19.0

2023-11-02

- Features

  - `Form` 组件下的 body 属性，支持使用变量和表达式，变量和表达式的结果需要符合 body 的类型。[#38](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/38)
  - `InputTable` 组件 columns 属性支持变量和表达式。[#165](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/165)
  - `Table` 组件 columns 属性支持变量和表达式。[#165](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/165)

- Bug Fixes

  - 修复 `CRUD` 在 table 模式下，不配置 api, 通过 source 读取数据域，配置 perPage 分页无效。[#174](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/174)。

## v1.18.0

2023-10-26

- Features

  - `Radios` 组件下的 options 拓展了属性 extralLabel，支持自定义展示内容.[#164](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/164)
  - `Actions` 拓展 componentName 动作属性，指定赋值的目标组件 name。[#162](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/162)

- Style

  - 优化 `Combo` 横向展示时表单项必填星号的位置。[#163](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/163)

## v1.17.0

2023-10-20

- Features

  - `@dataseed/amis-utils` 包提供 download 文件下载工具函数。[#158](http://gitlab.sit.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/158)
  - `InputTable` 拓展 setSelectedItems 动作，支持自定义设置列勾选项数据。[#153](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/153)

- Bug Fixes

  - 修复 `Table` 去除在 column 的数据链上增加 expanded 属性功能，在行数据上新增 `_amisExpanded` 用于标识行是否展开，以支持实现自定义展开/收起按钮功能 ⚠️。[#60](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/60)。

- Style

  - 优化 `Table` 表头列间隔线样式。[#154](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/154)
  - 优化 `FormItem` 表单项描述样式。[#155](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/155)

## v1.16.0

2023-10-13

- Features

  - `Code` 拓展 formatter 属性支持配置 code 格式化。[#63](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/63)
  - `CRUD` 拓展 supportEmptyStringOnFilter 属性，支持本地过滤支持空字符串。[#138](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/138)

- Bug Fixes

  - 修复 `CRUD` 关闭 loadDataOnceFetchOnFilter 后，过滤条件搜索无 loading 问题。[#138](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/138)
  - 修复 `CRUD` 关闭 loadDataOnceFetchOnFilter 后，清空 select 类型过滤条件查询无反应问题。[#138](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/138)
  - 修复 `CRUD` 关闭 loadDataOnceFetchOnFilter 后，清空过滤条件，偶发仍调用接口问题。[#138](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/138)
  - 修复 `CRUD` 关闭 loadDataOnceFetchOnFilter 后，手动清空过滤条件后，数据展示顺序与初始顺序不一致问题。[#138](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/138)
  - 修复 `CRUD` 开启 loadDataOnce 后，通过 filter 配置过滤条件不生效问题。[#138](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/138)
  - 修复 `CRUD` 动态列的站点文档示例未展示问题。[#138](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/138)

- Docs

  - 文档站点的 mock api 服务对接到阿里云平台。[#43](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/43)

## v1.15.0

2023-09-27

- Features

  - `Dialog` 拓展 showCloseButton 支持配置 boolean 和表达式。[#150](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/150)
  - `Drawer` 拓展 showCloseButton 支持配置 boolean 和表达式。[#150](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/150)

- Bug Fixes

  - 修复 `InputTable` quickEdit 结合确认模式，配置的校验规则未执行问题。[#146](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/146)

## v1.14.3

2023-09-21

- Bug Fixes

  - 修复 `InputTable` 在弹窗中新增一行时，该新增行处于 disabled 状态，无法输入数据问题。[#143](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/143)
  - 还原 `InputTable` showFooterAddBtn 属性，控制是否显示表格下方新增按钮。
  - 修复 `DropDownButton` 触发条件为 hover，且 disabled 状态时，鼠标悬浮仍然可以操作下拉框内按钮问题。[#149](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/149)

## v1.14.2

2023-09-20

- Bug Fixes

  - 修复 `ConditionBuilder` 在自定义的 conditionItemBody 中配置 service 能编辑、回显正常。[#145](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/145)

## v1.14.1

2023-09-19

- Features

  - `InputTable` 新增 validateOnPageChange 属性，changePage 动作，支持自定义跳转切换页码是否校验当前页及跳转页码功能。[#142](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/142)

- Bug Fixes

  - 修复 `DsDatePicker` 组件初始值为空字符串，组件渲染异常。[#140](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/140)
  - 修复 `InputTable`、`Combo` 由于开发[#72](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/72)导致校验失效问题。
  - 修复 `InputTable` 由于开发[#52](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/52)导致非第一页新增，插入数据索引错误问题。

- Style

  - 优化 `Form` 必填项，\* 号字体大小与 label 文本间距。[#141](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/141)

## v1.14.0

2023-09-14

- Features

  - `Table` 新增 showExpansionColumn 属性，并在 column 的数据链上增加 expanded 属性，用于标识行是否展开，以支持实现自定义展开/收起按钮功能。[#60](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/60)
  - `InputTable` 新增 showAddChildrenBtn 属性，支持新增子级。[#52](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/52)
  - `InputRichText ` 新增 uploadMode 属性，支持图片上传对接 OSS。[#65](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/65)
  - `Toast ` 调整默认持续时间为 3s，宽度由固定值改为根据内容自适应。[#71](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/71)
  - `Form` validate 动作新增支持校验指定的表单项。[#72](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/72)
  - `InputGroup` 新增支持控制错误信息的索引是否显示。[#64](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/64)

- Bug Fixes

  - 修复 `CRUD` 组件 selectedChange 事件实际返回参数名与站点文档不一致问题。
  - 修复 `InputTable` 编辑状态点取消，会删除行问题。[#134](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/134)
  - 修复 `FormItem` label 文本鼠标移入无提示的问题。[#116](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/116)

## v1.13.0

2023-09-07

- Features

  新增 `amis-utils` 工具库，初始化 amis env。[参照文档](http://moka.dmz.dev.caijj.net/dataseeddesigndocui/#/amis/zh-CN/course/utils)

- Bug Fixes

  - 修复 `Table` 由于开发[#52](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/52)导致配置行操作按钮场景下，鼠标悬浮行样式异常问题。
  - 修复 `Table` 由于拓展 InputTable 支持嵌套子表格，导致嵌套子表格数据无法渲染问题。

## v1.12.0

2023-08-31

- Features

  - `ConditionBuilder` 支持 add、conjunctionChange 事件。[#34](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/34)
  - `ConditionBuilder` 支持 draggable 属性。[#117](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/117)
  - `NestedSelect` 支持父节点被选中时子节点可操作。[#19](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/19)
  - `List` 支持 selectedChange、itemSelect 事件。[#70](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/70)
  - `Form` 支持校验失败返回校验失败项信息。[#88](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/88)

- Bug Fixes

  - 修复 `NestedSelect` 下拉菜单中的勾选项跟输入框中的已选项不一致问题。[#118](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/118)
  - 修复 `CRUD` 组件 keepItemSelectionOnPageChange 为 false，翻页后 bulkActions 批量按钮未禁用问题。[#96](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/96)

## v1.11.0

2023-08-24

- Features

  - `InputTable` 嵌套表格场景下，表单校验包含被折叠子表格输入项。[#51](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/51)
  - `Combo` 支持 Tabs 模式下未点击的 tab 页能触发校验。[#111](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/111)
  - `CRUD` 拓展 autoGenerateFilter 支持隐藏设置查询字段的 icon 和 高级搜索默认展开功能。[#93](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/93)
  - `FormItem` 拓展 hintLabel 支持 label 鼠标移入有浏览器默认展示 title 字段提示功能。[#116](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/116)

- Bug Fixes

  - 修复 `CRUD` 组件 topToolbar 中无法获取顶层数据域 data 数据问题。[#92](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/92)
  - 修复 `CRUD` 组件 headToolbar 右对齐显示异常问题。
  - 修复 `InputTable` 嵌套表格场景，表格数据更新不同步问题。[#106](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/106)
  - 修复 `DsDateRangePicker` 回显后再编辑异常问题。[#114](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/114)

## v1.10.2

2023-08-18

- Bug Fixes
  - 修复 `CRUD` 配置每页多少条未生效问题。[#97](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/97)
  - 修复 `InputTable` 子表格设置 name 属性，数据域数据丢失导致渲染失败问题。[#107](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/107)

## v1.10.1(废弃 ⚠️)

2023-08-17

- Bug Fixes
  - 修复 `CRUD` 配置每页多少条未生效问题。[#97](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/97)
  - 修复 `InputTable` 子表格设置 name 属性，数据域数据丢失导致渲染失败问题。[#107](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/107)

## v1.10.0 ⚠️

2023-08-10

- Features

  - `DsDatePicker` 新增 disabledDate、disabledTime 属性。
  - `Form` 更新 showLabelColon 属性默认值为 true。⚠️

- Bug Fixes

  - 修复 `DiffEditor` 对比样式相关文件缺失问题。[#80](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/80)
  - 修改 `InputTable` disabled 状态下，行勾选功能未禁用。[#98](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/98)
  - 修改 `DsDatePicker` 配置 showTime，不配置 format 时，组件异常。[#105](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/105)

- Style

  - 修复 `Static` 快速编辑模式样式异常。[#86](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/86)
  - 修复 `FormItem` 的 label 在常规和静态模式下的样式。[#102](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/102)
  - 优化 `Crud` 的样式。[#95](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/95)
  - 优化 `Drawer` `Dialog` 的样式。当底部存在操作按钮时，右上角不会出现小叉；当底部无操作按钮时，右上角会有小叉。小叉与操作类按钮不会同时存在。[#99](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/99) ⚠️

## v1.9.1

2023-08-03

- Bug Fixes

  - 修复 `Switch` 站点上，背景色设置解析失败问题。[#45](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/45)
  - 修改 `TabsTransferPicker` 站点示例，解决错误使用引起的样式问题。[#46](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/46)
  - 修复 `Toast` 设置 `title` 属性，存在 JS 报错，导致页面崩溃问题。[#47](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/47)
  - 修复 `Transfer` 树形模式，设置全局 disabled 不生效问题。[#59](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/59)
  - 修复 `Transfer` 树形模式，叶子节点设置 disabled 后，仍可以通过父节点选中问题。[#59](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/59)
  - 修复 `Transfer` 树形模式，设置全局 disabled 后，收起按钮点击无效问题。[#59](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/59)

- Style

  - 修复 `Cards` 调整拖拽后卡片背景色。[#39](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/39)
  - 修复 `Radios` 调整边框颜色及禁用状态背景色。[#40](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/40)

## v1.9.0

2023-07-27

- Features

  - `Static` 新增 staticShowPlaceholder 属性。[#89](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/89)

- Bug Fixes

  - 修复 `Crud` 自动分页回车触发 form 提交问题。[#32](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/32)
  - 修复 `Crud` 快捷方式跳转至 XX 页，回车失效问题。
  - 修复 `Form` 触发校验失败后，不再继续触发校验，保留校验失败的状态。[#41](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/41)
  - 修复 `Service` 传入 tdata 参数，在 apdatpor 中访问不到的问题。[#23](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/23)
  - 修复` DsTimePicker` `DsTimeRangePicker` `DsDatePicker` `DsDateRangePicker` 在 `Dialog` 内使用时，日历/时间弹出层被 `Dialog` 遮挡。[#31](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/31)
  - 修复 `InputNumber` 在 `Form` 中设置为必填项，校验失败时边框未变红。[#33](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/33)
  - 修复 `InputTable` 子表格与主表格共用一个表头时，仅部分表单项进行校验。[#54](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/54)

## v1.8.0

2023-07-06

- Features

  - `InputTable` 新增 change 事件。[#36](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/36)。

- Bug Fixes

  - 修复 `TextArea` 字数提示模块背景色覆盖 TextArea 边框。[#35](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/35)

## v1.7.0

2023-06-29

- Features

  - `Select` 可搜索模式下，搜索规则由模糊匹配改为精准匹配。[#12](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/12)
  - `ShowMore`支持`visibleDivider`属性。

- Bug Fixes

  - 修复 `Button` 放在 `Wrapper` 或 `Container` 中后 disabled 属性无效问题。[#20](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/22)
  - 修复 `Combo` 中的 `FormItem` 被 `Wrapper` 或 `Container` 包裹后 static 属性失效问题。[#22](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/22)
  - 修复 `Crud` 和 `InputTable` 操作列 icon 鼠标移上去抖动问题。[#30](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/30)[#16](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/16)
  - 修复 `Crud` 配置`rowclassname` 设置背景色类名不生效问题。[#21](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/21)

## v1.6.0

2023-06-25

- Features

  - `InputKV`、`InputKVS` 有项目的 key 未填写时点击“新增”提示无法新增项目。
  - `InputTable` column 配置 required 时，展示必填星号\*。

- Bug Fixes

  - `InputKVS` 修复水平模式下删除 Icon 与输入框重叠问题。
  - `Table` 修复列设置宽度后，文本超出宽度不换行的问题。

## v1.5.2

2023-06-20

- Bug Fixes

  - `ConditionBuilder` 修复自定义 operators 重复选择时，数据不清理的情况。
  - `Crud` 修复 自动生成查询区域查询和重置按钮 loading 状态，loading 为 true 状态点击按钮禁止请求搜索接口。

- Style

  - `Tabs`调整加强、垂直模式的边框颜色。
  - `Collpase`调整 hover 背景颜色和 borderRadius。
  - `DropDown`调整 hover 背景颜色。
  - `ShowMore`调整 link 类型按钮两两之前添加竖线。

## v1.5.1

2023-06-16

- Style

  - 调整主题色。

## v1.5.0

2023-06-15

- Bug Fixes

  - `Steps` 修复[一系列样式错乱问题](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/2)。
  - `TreeSelect` 修复引发的[页面崩溃问题](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/1)。
  - `InputTree` 修复[新增节点后出现异常节点的问题](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/3)。
  - `ConditionBuilder` 修复 disabled 状态下，添加条件、添加条件组、删除组等操作按钮隐藏。
  - `ConditionBuilder` 修复自定义 operators 重复选择时，数据不清理的情况。
  - `Crud` 修复 List 模式列选择回显失效。

- Features

  - `Steps` 支持 clickable、delimiter 属性和 click 事件。
  - `Wizard` 支持 delimiter 属性，stepValidateSucc、stepValidateFail 事件和 step-validate 动作。
  - `Button` 设置 loading 为 true，点击事件禁用。
  - `Tabs` 支持 add、close 事件。
  - `Tabs` 新增 custom 模式和 navigationRender 属性，支持自定义渲染选项卡。
  - `Service`调整通用 api 配置支持传入 tdata 参数，作为临时数据缓存，不提交至接口入参，但在 responseData 中能访问到。
  - `ConditionBuilder`支持 maxLevel、minLevel 变量格式。

- Style

  - `TooltipWrapper`调整默认主题是 dark。
  - `Toast`调整动画效果为渐入渐出。

## v1.4.2

2023-06-13

- Bug Fixes

  - `Pagination`修复快捷方式跳至 XX 页数，未调用 api。
  - `CRUD`修复 List 模式，列选择-单选失效。

- Features

  - `CRUD` 支持 selectedChange 事件。
  - `CRUD` 支持 saveOrderApi 参数中包含分页参数。
  - `CRUD` 列支持 headSearchable 搜索且回显。
  - `Table` 列支持 headSearchable 搜索且回显。

## v1.4.1

2023-06-12

功能与 v1.4.0 版本无差异，不建议使用该版本。

## v1.4.0

2023-06-08

- Bug Fixes

  - `CRUD`修复 cards 模式设置 columnsCount 大于 4 布局异常。

- Features

  - `Form` 支持校验未渲染出来的表单数据。
  - `Form` 支持 label 后面自动补充冒号。

## v1.3.1

2023-06-01

- Bug Fixes

  - `DsDatePicker`修复 value 更新，视图未同步更新问题。
  - `DsDateRangePicker`修复 value 更新，视图未同步更新问题。
  - `DsTimePicker`修复 value 更新，视图未同步更新问题。
  - `DsTimeRangePicker`修复 value 更新，视图未同步更新问题。

- Features

  - `Form` 支持校验失败滚动至第一个失败的表单项。
  - `Form` 支持配置校验报错的提示信息。
  - `Form` 支持 label 自动添加冒号。
  - `InputFile`与 `InputImage` 新增 success 事件 和 fail 事件 拓展 files 入参。

## v1.3.0

2023-05-31

- Bug Fixes

  - `TreeSelect`修复输入框中内容溢出问题。

- Features

  - `InputTable`支持嵌套子表格。
  - `Pagination`新增 changePage 动作。
  - `CRUD`新增 changePage 动作。
  - `InputImage`支持直传阿里云 OSS、支持返回 asBase64, asBlob 格式。
  - `InputFile`支持直传阿里云 OSS。

- Style

  - `Pagination`修改字体大小 14px、 字体颜色 rgba(0, 0, 0, 0.88)、页数选中时加粗。

## v1.2.0

2023-05-24

- Bug Fixes

  - `ConditionBuilder`条件组为空时，隐藏『复制组』按钮。

- Features

  - `Dialog`新增 destroyOnClose 属性，支持弹窗关闭是否缓存状态，新增动作 open。
  - `Table`新增支持嵌套子表格自定义展开/收起图标。
  - `Table`新增列搜索在表头上方回显展示功能。
  - `Table`新增支持列选择回显功能。
  - `ShowMore`新增组件。
  - `DepartmentSelect`新增归属部门业务组件。
  - `UserSelect`新增用户选择业务组件。
  - `TreeSelect` 新增支持 showCheckedStrategy、switcherIcon、optionsPlaceholder、maxTagTextLength 属性。

- Style

  - `Table`暂无数据 icon 调整。

## v1.1.0-beta.0

2023-05-15

调整打包构建方式，并无组件功能调整，不建议使用该版本

## v1.0.0-beta

2023-05-10

- Bug Fixes

  - `InputTree`修复 api 请求方式为 delete 时未带参数的问题。
  - `InputText` `InputNumber` 修复原生 autoComplete 属性失效问题。

- Features
  - `Spinner`修改默认 icon、修复自定义 icon 无效问题。
  - `NestedSelect`支持懒加载，新增 dropdownTitles、valueTpl 属性。
  - `InputTree`支持 disabled、switcherIcon、blockNode 属性。
  - `InputTree`支持拖拽功能，支持 dragStart、dragEnd、dragEnter、dragOver 事件。
  - `DsDatePicker`新增组件。
  - `DsDateRangePicker`新增组件。
  - `DsTimePicker`新增组件。
  - `DsTimeRangePicker`新增组件。
  - `DsDatePicker`新增 static 模式。
  - `DsDatePicker` 与 `DsTimePicker` 添加事件表与动作表支持。
  - `ConditionBuilder`新增条件参数自定义、支持条件配置自定义、支持条件最大和最小数量自定义。
  - `Table`新增嵌套子表格、折叠展开支持动态加载数据、嵌套子表格有独立表头 功能。
  - `Table`去除全部展开收起功能。
  - `Table`支持多列搜索、多列排序功能。
  - `Select`支持组件远程过滤搜索。
  - `InputTag`支持配置 customTag 显示个性化标签。
  - `Tabs`新增属性 tabsPosition、defaultTabForAdd 等，操作事件 add、close 等。

基于 amis2.9.0 版本基础
