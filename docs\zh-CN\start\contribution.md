---
title: 贡献指南
---

这篇指南会指导你如何为 Dataseed Design 贡献一份自己的力量，请在你要提 issue 或者 pull request 之前花几分钟来阅读一遍这篇指南。

## 分支管理
我们长期维护两个分支 master 和 feature。master作为上线的主分支，feature作为开发分支。
1. 如果你要修一个 bug，那 pull request 请基于 master 分支，修复完成后从 master 发布一个 patch 版本；
2. 如果你要提一个增加新功能的 pull request，那么请基于 feature 分支来做，开发完成后会合并 feature 到 master，并发布一个包含新特性的 minor 版本。

## Bugs
我们使用 [wiki](http://wiki.caijj.net/pages/viewpage.action?pageId=237897491) 来做 bug 追踪。

在你报告一个 bug 之前，请先确保已经搜索过已有的 issue 和阅读了我们的 常见问题。

## 开发流程
在你 clone 了 @dataseed/amis 的代码，确保node版本为v16.16.0， npm版本为v8.11.0。
1. 执行`npm i --legacy-peer-deps && npm run hoist && npm run install-all` 安装完依赖后，你还可以运行下面几个常用的命令：
2. npm start 在本地运行启动。

### 测试
1. 执行测试用例
npm test --workspaces
2. 测试某个用例
npm test --workspace amis inputImage
3. 查看测试用例覆盖率
npm run coverage
4. 更新 snapshot
npm run update-snapshot

### 统一发版
发布规则：》一次发布，更新所有package的版本号
1. 生成changelog
npm run changedlog

2. 手动在lerna.json里设置版本号or遵循第一步lerna version 自动生成的版本号规则
cat lerna.json

3. 构建
npm run build

4. copy packages 到 tempPkg目录
npm run cp-pkg

5. 统一替换tempPkg目录里的发版版本号
npm run gen-version

6. 统一命名发版的包名称，比如统一加前缀@dataseed/
npm run rename

7. 发布数禾仓库
npm run publish
