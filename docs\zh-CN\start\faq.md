---
title: 常见问题
---

## CRUD 顶部有重叠遮挡

在初始化 amis 渲染器的时候设置 `affixOffsetTop`，或者通过 `"affixHeader": false` 关闭固定顶部功能。

## 如何换行

有时候返回结果中有 `\n`，在页面展现的时候默认不会有换行效果，解决办法有 3 个：

1. 使用 tpl、html、plain 或 static 组件，加上 `"wrapperComponent": "pre"` 配置项
2. 引入 `helper.css`，给组件加上 `"classname": "white-space-pre"` 配置项
3. 包在 `container` 容器中，使用 `style` 控制样式

前两种方法比较简单，这里就只演示第三种，如果熟悉 css 可以很灵活实现各种展现控制：

```schema
{
  "type": "page",
  "data": {
    "log": "line 1\nline 2"
  },
  "body": {
    "type": "container",
    "style": {
      "white-space": "pre"
    },
    "body": {
      "type": "tpl",
      "tpl": "${log}"
    }
  }
}
```

## 如何折行

折行需要给对应的组件加上 `"classname": "word-break"`。

## 集成到 React 项目中报错

一般都是因为 React、Mobx、mobx-react 版本有关，参考 amis 项目的 [package.json](http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/blob/master/package.json)，将版本保持一致，尤其是 Mobx，目前 amis 中使用的版本是 4，因为兼容性的考虑短期内不会升级到 5/6，使用 MobX 5/6 肯定会报错。

## 如何支持配置中的 URL 地址替换？

有个常用场景是在开发时使用 `localhost` 地址，而线上使用 `xxx.com`，这时可以使用 `replaceText` 属性，它是第四个参数，也就是 env 参数

```javascript
let amis = amisRequire('amis/embed');
let amisJSON = {
  type: 'page',
  body: {
    type: 'service',
    api: 'HOST/api',
  },
};
let amisScoped = amis.embed(
  '#root',
  amisJSON,
  {},
  {
    replaceText: {
      HOST: 'http://localhost',
    },
  },
);
```

## 如何更新全局 data？

使用下面的方式

```
amisScoped.updateProps({
  data: {
    xxx: 'yyy'
  }
})
```

## CRUD api 分页功能失效

如果 api 地址中有变量，比如 `/api/mock2/sample/${id}`，amis 就不会自动加上分页参数，需要自己加上，改成 `/api/mock2/sample/${id}?page=${page}&perPage=${perPage}`

## umi 项目混用 antd 和 amis 样式异常

### 在生产模式下，select 组件边框丢失。

真正引起样式问题的原因是 umi 在构建时对 antd.css 内容做了转译，本地开发模式、生产模式下的构建结果不一致，生产模式下的构建结果有错误。

在项目的 umi 配置中增加如下配置：
请参考此文档并验证其中记录的问题已修复http://wiki.caijj.net/pages/viewpage.action?pageId=246933119

```
  cssnano: {
    // 当前版本的 umi 对 css 缩写转译有问题，先关闭此功能，将来 umi 修复此问题后再开启此功能。
    mergeLonghand: false,
  }
```

### select 组件在下拉展开后，点击别的地方，下拉区域无法收起。

css 定位问题引起的，可通过配置属性`popOverContainerSelector: body`解决。
