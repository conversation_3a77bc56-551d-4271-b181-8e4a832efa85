[如有请求库问题，请在此提issue](http://gitlab.caijj.net/JiShuBaoZhang/QianDuanJiaGou/webadmin-http/-/issues)

npm：```npm i @lattebank/webadmin-http```

## v1.0.11

2025-05-21

- feature:
  - 请求库增加配置：`config.noToken`，请求发送的时候，增加该配置，请求库不会默认加X-Token
    - 适用场景：登录前请求(无token)


## v1.0.5

2025-02-19

- feature:
  - 请求库增加工厂函数：`CreateHttp(name)`，后续用于mainui下发请求库实例「暂未集成基座」。后续$http库的更新，请同步更新工厂函数`CreateHttp(name)`

## v1.0.4

2025-02-18

- feature:
  - 请求库发送请求的时候，统一在`request header`里增加 `x-PathLabel` 字段。 - [[issue5]](http://gitlab.caijj.net/JiShuBaoZhang/QianDuanJiaGou/webadmin-http/-/issues/5)
    - `X-PathLabel`：该字段代表微前端的菜单里暴露出的页面路径的「面包屑」，需求来源：陈沈伟。
  - `config`配置里新增配置：`SOURCERESPONSE`，如果为`true`，则不进行拦截器拦截，直接返回`axios`的源数据。需求来源：王强 - API平台项目。 - [[issue4]](http://gitlab.caijj.net/JiShuBaoZhang/QianDuanJiaGou/webadmin-http/-/issues/4)

## v0.2.20

2025-01-07

- feature:
  - 请求库发送请求的时候，统一在`request header`里增加 `X-Referer` 和 `X-Version` - [[issue2]](https://gitlab.caijj.net/JiShuBaoZhang/QianDuanJiaGou/webadmin-http/-/issues/2)
    - `X-Referer`：原请求通过网关，`http` 协议里的 `Referer` 只能获取到 `hash` 前的链接，会自动舍弃 `url` 中的 `hash` 部分，该字段补全这个场景能力。
    - `X-Version`：$http的使用版本号，方便后期排查问题和确认版本。


## v0.2.19

2024-12-24

- feature:
  - 将`token`失效的逻辑，迁移到后端，调用后端的`login-config`接口，然后跳转
