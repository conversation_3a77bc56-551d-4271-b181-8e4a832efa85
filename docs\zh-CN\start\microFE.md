[如有微前端问题，请在此提issue](http://gitlab.caijj.net/yanfaerbu/qianduan/mainui/-/issues)

## v1.0.2

2025-02-18

- feature:
  - 基座集成`sentry-sdk@1.0.3`的能力，收敛head-http的status为0的报错。[sentry-sdk #3](http://gitlab.caijj.net/yanfaerbu/qianduan/sentry-rule-plugin/-/issues/3)

## v1.0.1

2025-01-15

- feature:
  - 基座抽离sentry的规则到sentry-sdk里，且集成sentry-sdk规则和开放自定义上报能力 [#20](http://gitlab.caijj.net/yanfaerbu/qianduan/mainui/-/issues/20)、[sentry-sdk #1](http://gitlab.caijj.net/yanfaerbu/qianduan/sentry-rule-plugin/-/issues/1)
  - 基座支持子应用如果不允许在H5打开，则跳转H5提示页面，可在「简易业务配置」中配置「forbidMobileAppList」。 [#27](http://gitlab.caijj.net/yanfaerbu/qianduan/mainui/-/issues/27)

## v1.0.0

2024-12-24

- 功能迁移:
  - 将基座应用中的跳转登录页的逻辑迁移到后端，调用后端接口login-config，然后跳转
  - 更新最新的$http版本：0.2.19