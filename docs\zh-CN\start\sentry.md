[如有sentry相关问题，请在此提issue](http://gitlab.caijj.net/yanfaerbu/qianduan/sentry-rule-plugin/-/issues)

npm：```npm i @shuhejs/sentry-rule-sdk```

## v1.0.3

2025-02-18

- feature:
  - 收敛`head-http`请求库（idaasui中使用，等改造完，则下线此监控规则）`status`为0的时候的报错 [sentry-sdk #3](http://gitlab.caijj.net/yanfaerbu/qianduan/sentry-rule-plugin/-/issues/3)

## v1.0.1

2025-01-15

- feature:
  - 重构sentry规则，抽离到单独sdk里 [#10](http://gitlab.caijj.net/yanfaerbu/qianduan/mainui/-/issues/10)
    - 迁移所有规则到单独的sdk里
    - 支持不同环境加载不同插件（现阶段支持微前端环境+独立接入）
    - sentry规则插件化，支持舍弃插件、重写插件、功能插件。后面直接开发插件就可以了
    - 新增：浏览器插件报错统一收敛
    - 新增：网关层异常，导致微前端加载不到子应用报错异常告警
  - 支持自定义上报能力方法：`customReporting`，现阶段支持2种场景：`SameValueError`和`AmisFormError`。 [#1](http://gitlab.caijj.net/yanfaerbu/qianduan/sentry-rule-plugin/-/issues/1)