---
title: 快速开始
---

amis 是支持自定义样式的。

不过大部分场景，组件中都把规范的样式内置好了，使用者无需操心样式，也无需写样式。但某些场景很灵活，内置的样式不满足需求，所以也就开放出了一些类名以供使用，具体可看左侧目录。

下面我们先介绍一下如何使用自定义类名，提供部分场景示例以供参考。

## 使用className

大部分 amis 组件都有 `className` 或者 `xxxClassName` 的配置，比如下面的配置，期望在input-group中表单项宽度均分，可以通过给表单项配置`inputClassName: "flex-1"`来均分。

```schema
{
  "type": "page",
  "data": {
    "unita1": "day",
    "unit1": [
      "day",
      "month",
      "year",
      "days",
      "months",
      "years",
      "dayss",
      "monthss",
      "yearss"
    ]
  },
  "body": {
    "type": "form",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "mode": "horizontal",
    "body": [
      {
        "type": "input-group",
        "label": "账户开立日期",
        "name": "rule1",
        "body": [
          {
            "type": "select",
            "name": "unita1",
            "inputClassName": "flex-1",
            "options": [
              {
                "label": "账户开立日期距报告日期账户开立日期账户开立日期",
                "value": "day"
              },
              {
                "label": "月",
                "value": "month"
              },
              {
                "label": "年",
                "value": "year"
              }
            ],
          },
          {
            "type": "select",
            "name": "unit1",
            "multiple": true,
            "inputClassName": "flex-1",
            "options": [
              {
                "label": "3天",
                "value": "day"
              },
              {
                "label": "3月",
                "value": "month"
              },
              {
                "label": "3年",
                "value": "year"
              },
              {
                "label": "30天",
                "value": "days"
              },
              {
                "label": "30月",
                "value": "months"
              },
              {
                "label": "30年",
                "value": "years"
              },
              {
                "label": "300天",
                "value": "dayss"
              },
              {
                "label": "300月",
                "value": "monthss"
              },
              {
                "label": "300年",
                "value": "yearss"
              }
            ],
            "maxTagCount": 3,
            "checkAll": true
          }
        ]
      },
      {
        "type": "input-group",
        "label": "数据保留规则",
        "prefix": "近",
        "prefixName": "customPrefix",
        "body": [
          {
            "type": "input-text",
            "label": false,
            "name": "days",
            "placeholder": "请输入",
          },
          {
            "type": "select",
            "name": "unit",
            "options": [
              {
                "label": "天",
                "value": "day"
              },
              {
                "label": "月",
                "value": "month"
              },
              {
                "label": "年",
                "value": "year"
              }
            ],
            "value": "day"
          },
        ],
      },
      {
        "type": "input-group",
        "label": "金额",
        "suffix": "元",
        "body": [
          {
            "type": "input-text",
            "name": "groupfix",
            "placeholder": "请输入"
          },
        ]
      },
    ],
  },
};
```


## 场景示例

为了规范 class 的使用场景，在下方举了些例子来介绍哪些场景才是真正有必要自定义样式的

### 宽度自适应

flex 布局下，部分 flex 元素可能有自适应的需求，支持使用者根据业务需要给对应的 flex 元素设置 flex-grow 类名。

```schema
{
  "type": "page",
  "body": {
    "type": "form",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
    "data": {
      "platform": "text1",
      "css": "text2",
      "bankList": [
        "4017"
      ]
    },
    "body": [
      {
        "type": "group",
        "body": [
          {
            "type": "flex",
            "gap": true,
            "items": [
              {
                "type": "select",
                "name": "eventSource",
                "label": "事件平台",
                "options": [
                  {
                    "label": "业务系统",
                    "value": "SYSTEM"
                  },
                  {
                    "label": "特征系统",
                    "value": "FEATURE"
                  },
                  {
                    "label": "北斗系统",
                    "value": "EFUEL"
                  },
                  {
                    "label": "埋点系统",
                    "value": "STATS"
                  }
                ],
                "className": "flex-grow"
              },
              {
                "type": "button",
                "label": "新建",
                "level": "link"
              },
              {
                "type": "button",
                "label": "详情",
                "level": "link"
              }
            ]
          }
        ]
      },
      {
        "type": "group",
        "body": [
          {
            "type": "flex",
            "gap": true,
            "items": [
              {
                "type": "select",
                "name": "eventSource",
                "label": "系统平台",
                "options": [
                  {
                    "label": "业务系统",
                    "value": "SYSTEM"
                  },
                  {
                    "label": "特征系统",
                    "value": "FEATURE"
                  },
                  {
                    "label": "北斗系统",
                    "value": "EFUEL"
                  },
                  {
                    "label": "埋点系统",
                    "value": "STATS"
                  }
                ],
                "className": " flex-grow"
              },
              {
                "type": "button",
                "label": "新建",
                "level": "link"
              },
              {
                "type": "button",
                "label": "详情",
                "level": "link"
              }
            ]
          },
          {
            "type": "input-text",
            "name": "css",
            "label": "CSS",
            "required": true,
            "placeholder": "请输入"
          }
        ]
      }
    ]
  }
}
```

### 颜色自定义

颜色必须是一个灵活的配置。比如对比场景，需要鲜明的颜色区分对比的结果，通常，我们只要用好 pm-text-muted、pm-text-success、pm-text-warning	、pm-text-danger 这几个类名，就能应对很多场景了。

```schema
{
  "type": "page",
  "data": {
    "tableData": [
      {
        "fieldName": "API ID",
        "baselineVersion": "12345",
        "diffVersion": "12345"
      },
      {
        "fieldName": "API名称",
        "baselineVersion": "查询用户列表",
        "diffVersion": "查询用户列表"
      },
      {
        "fieldName": "API状态",
        "baselineVersion": "在线",
        "diffVersion": "在线"
      },
      {
        "fieldName": "Method",
        "baselineVersion": "GET",
        "diffVersion": "POST",
        "status": "EDIT"
      },
      {
        "fieldName": "URL",
        "baselineVersion": "/users",
        "diffVersion": "/user-list",
        "status": "EDIT"
      },
      {
        "fieldName": "描述",
        "baselineVersion": "",
        "diffVersion": "查询用户列表",
        "status": "ADD"
      },
      {
        "fieldName": "补充",
        "baselineVersion": "查询用户列表",
        "diffVersion": "查询用户列表",
        "status": "DELETE"
      }
    ]
  },
  "body": {
    "type": "wrapper",
    "bgColor": "white",
    "body": {
      "type": "flex",
      "direction": "column",
      "gap": true,
      "items": [
        {
          "type": "tags",
          "items": [
            {
              "type": "tag",
              "label": "新增",
              "displayMode": "bordered",
              "color": "add-status"
            },
            {
              "type": "tag",
              "label": "修改",
              "displayMode": "bordered",
              "color": "edit-status"
            },
            {
              "type": "tag",
              "label": "删除",
              "displayMode": "bordered",
              "color": "delete-status"
            }
          ]
        },
        {
          "type": "form",
          "title": "",
          "actions": [],
          "mode": "horizontal",
          "wrapWithPanel": false,
          "labelWidth": 80,
          "body": [
            {
              "type": "between",
              "label": "基准版本",
              "labelRemark": {
                "type": "remark",
                "content": "这是一个提示"
              },
              "items": [
                {
                  "type": "select",
                  "label": "基准版本",
                  "name": "baselineVersion",
                  "value": "v1",
                  "options": [
                    {
                      "label": "V1",
                      "value": "v1"
                    },
                    {
                      "label": "V2",
                      "value": "v2"
                    }
                  ]
                },
                {
                  "type": "select",
                  "label": "对比版本",
                  "name": "diffVersion",
                  "value": "v2",
                  "options": [
                    {
                      "label": "V1",
                      "value": "v1"
                    },
                    {
                      "label": "V2",
                      "value": "v2"
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          "type": "crud",
          "syncLocation": false,
          "columnsTogglable": false,
          "autoGenerateFilter": {
            "showBtnToolbar": false,
            "defaultExpanded": false
          },
          "footerToolbar": [],
          "columns": [
            {
              "name": "fieldName",
              "label": "字段名"
            },
            {
              "name": "baselineVersion",
              "label": "基准版本"
            },
            {
              "name": "diffVersion",
              "label": "对比版本",
              "type": "tpl",
              "tpl": "<span class='${status === 'ADD' ? 'pm-versionDiff-add' : (status === 'EDIT' ? 'pm-versionDiff-edit' : (status === 'DELETE' ? 'pm-versionDiff-delete' : ''))}'>${diffVersion}</span>"
            }
          ],
          "source": "${tableData}"
        }
      ]
    }
  }
}
```
