# 数据链更新机制设计

## 概述

AMIS 框架中的数据链更新机制是通过 `WithStore` 高阶组件实现的。该机制负责在组件层级间传递数据变化，确保子组件能够及时响应上级数据的更新。

## 核心概念

### 数据链结构
```
Page Data
  └── CRUD Data (__super → Page Data)
      └── Table Data (__super → CRUD Data)
          └── SubTable Data (__super → Table Data)
```

### 关键配置项
- **trackExpression**: 自定义跟踪表达式，优先级最高
- **extendsData**: 控制是否继承上级数据，默认为 true
- **syncSuperStore**: 控制是否同步上级 Store 数据
- **shouldSyncSuperStore**: 自定义同步条件函数

## 数据更新流程

### 主流程图

```mermaid
flowchart TD
    A[组件更新] --> B{允许同步?}
    B -->|否| END[结束]
    B -->|是| C{数据继承策略}

    C -->|独立数据模式| D{独立模式触发条件}
    C -->|继承数据模式| E{继承模式触发条件}

    D --> D1{强制同步?}
    D1 -->|是| ACTION1[基础: props.data<br/>扩展: 远程数据 + 默认数据 + 当前数据]
    D1 -->|否| D2{默认值变化?}
    D2 -->|是| ACTION1
    D2 -->|否| D3{有表达式跟踪?}
    D3 -->|是| D4{表达式值变化?}
    D4 -->|是| ACTION1
    D4 -->|否| END
    D3 -->|否| D5{data字段变化?}
    D5 -->|是| ACTION1
    D5 -->|否| D6{super引用变化?}
    D6 -->|是| ACTION1
    D6 -->|否| END

    E --> E1{强制同步?}
    E1 -->|是| ACTION2[选择继承策略]
    E1 -->|否| E2{有表达式跟踪?}
    E2 -->|是| E3{表达式值变化?}
    E3 -->|是| ACTION2
    E3 -->|否| BOUNDARY[检查边界情况]
    E2 -->|否| E4{data字段变化?}
    E4 -->|是| ACTION2
    E4 -->|否| E5{启用智能同步?<br/>syncSuperStore !== false}
    E5 -->|是| E6{智能字段检测变化?}
    E6 -->|是| ACTION2
    E6 -->|否| BOUNDARY
    E5 -->|否| BOUNDARY

    ACTION2 --> BRANCH{数据来源判断}
    BRANCH -->|数据源共享<br/>props.store.data === props.data| ACTION2A[原型链: props.store.data<br/>自身属性: 同步结果或本地数据]
    BRANCH -->|有数据链<br/>data.__super存在| ACTION2B_STEP1[使用extendObject平铺合并<br/>基于props.data]
    BRANCH -->|普通场景| ACTION2C[原型链: props.scope<br/>自身属性: props.data]

    ACTION2B_STEP1 --> ACTION2B_BRANCH{组件类型判断}
    ACTION2B_BRANCH -->|远程数据或页面组件<br/>hasRemoteData or path=page| ACTION2B1[平铺合并: store.data + props.data<br/>到props.data基础上]
    ACTION2B_BRANCH -->|Combo组件<br/>storeType=ComboStore| ACTION2B2[保持props.data原样<br/>不做任何扩展]
    ACTION2B_BRANCH -->|普通组件| ACTION2B3[平铺合并: syncDataFromSuper结果<br/>到props.data基础上]

    BOUNDARY --> B1{super内容变化?<br/>检查data.super内容}
    B1 -->|是| ACTION3[基础: props.data.__super<br/>扩展: props.data + this.store.data]
    B1 -->|否| B2{data引用变化?<br/>prevData !== data}
    B2 -->|是| ACTION4[基础: props.scope<br/>扩展: this.store.data]
    B2 -->|否| END

    ACTION1 --> END
    ACTION2A --> END
    ACTION2B1 --> END
    ACTION2B2 --> END
    ACTION2B3 --> END
    ACTION2C --> END
    ACTION3 --> END
    ACTION4 --> END

    style D1 fill:#ffeb3b
    style E1 fill:#ffeb3b
    style D4 fill:#4caf50
    style E3 fill:#4caf50
    style D5 fill:#f44336
    style E4 fill:#f44336
```

### 变化检测策略

#### 检测优先级
1. **强制同步**: 组件配置要求强制同步
2. **表达式跟踪**: 自定义表达式值发生变化（最精确）
3. **数据对象变化**: 组件接收的数据对象发生变化
4. **数据链变化**: 上级数据链中的数据发生变化
5. **默认值变化**: 组件默认数据发生变化

#### 独立数据模式 vs 继承数据模式的检测差异

**独立数据模式** (`extendsData = false`):
- **强制同步检测**: `shouldSync === true`
- **默认值检测**: `prevProps.defaultData !== props.defaultData` （仅独立模式有）
- **表达式跟踪**: 或者检查 `trackExpression` 值变化
- **数据变化检测**: 或者检查 `props.data` 浅比较 + `__super` 引用变化
- **设计目的**: 主要用于 CRUD toolbar 等场景

**继承数据模式** (`extendsData = true`):
- **强制同步检测**: `shouldSync === true`
- **表达式跟踪**: 或者检查 `trackExpression` 值变化
- **数据变化检测**: 或者检查 `props.data` 浅比较 + 智能字段检测
- **注意**: 继承模式**没有**默认值检测
- **设计目的**: 用于需要继承上级数据的普通组件

#### CRUD Toolbar 的特殊设计

CRUD 的 toolbar 使用了特殊的数据结构来避免表单提交时传递大量数据：

```javascript
// toolbar 的 data 构造
data: createObject(
  createObject(store.filterData, {items, selectedItems, ...}),
  {},  // 空对象作为 data
),
```

- `data` 本身是空对象，不会随 CRUD 数据变化
- `__super` 指向包含实际数据的对象
- 当 CRUD 数据更新时，`__super` 引用会变化
- 通过检查 `__super` 引用变化来触发 toolbar 更新

## 数据构建策略详解

### 独立数据模式构建
- **初始化**: 使用 createObject，原型链指向 `props.data.__super`，自身属性为 `defaultData + props.data`
- **更新时**: 使用 extendObject 平铺合并，包含远程数据（如果存在）+ defaultData + props.data
- **远程数据处理**: 通过 `store.hasRemoteData ? store.data : null` 决定是否包含
- **特点**: 更新时不创建原型链，所有数据平铺在一层

### 继承数据模式构建

#### 策略1：共享数据源 (`props.store.data === props.data`)
- **原型链和__super指向**: props.store.data
- **自身属性数据来源**:
  - 禁用同步时：{...store.data}
  - 启用同步时：syncDataFromSuper的结果

#### 策略2：有上级数据 (`props.data.__super` 存在)
使用 `extendObject` 平铺合并，不创建原型链：
- **远程数据/页面组件**: 将 `{...store.data, ...props.data}` 平铺合并到 props.data
- **Combo组件**: 保持 props.data 原样，不做任何扩展
- **其他组件**: 将 syncDataFromSuper 的结果平铺合并到 props.data
- **__super 指向**: 保持原有的 props.data.__super

#### 策略3：其他情况
- **原型链和__super指向**: props.scope
- **自身属性数据来源**: props.data

### 边界情况构建

#### 边界1：__super 内容变化
- **数据合并方式**: 使用 createObject 创建原型链
- **原型链和__super指向**: props.data.__super（变化后的上级数据）
- **自身属性**: props.data + store.data（合并）

#### 边界2：data 引用变化（scope 相关）
- **数据合并方式**: 使用 createObject 创建原型链
- **原型链和__super指向**: props.scope
- **自身属性**: store.data（只保留本地数据）

## 核心工具函数

### 数据变化检测函数

#### isSuperDataModified(data, prevData, store)
**功能**: 智能检测业务相关数据的变化

**检测策略**:
- **FormStore**: 检测表单项字段 + store.data 中的所有字段
- **其他 Store**: 检测 store.data 中的所有字段

**示例**:
```javascript
// FormStore 场景：只检测表单相关字段
const formStore = { storeType: 'FormStore', items: [{name: 'userName'}, {name: 'userAge'}] };
const currentData = { userName: 'Jane', userAge: 25, extraField: 'extra' };
const prevData = { userName: 'John', userAge: 25, extraField: 'extra' };
// 返回 true，因为 userName 发生了变化

// 普通 Store 场景：检测所有字段
const normalStore = { storeType: 'ServiceStore', data: { field1: 'value1', field2: 'value2' } };
// 检测 field1, field2 的变化
```

#### isObjectShallowModified(obj1, obj2, strictMode)
**功能**: 浅层对比两个对象的属性变化

**特点**:
- 只检测第一层属性，不递归检测嵌套对象
- 性能优秀，适合快速检测直接属性变化
- strictMode 控制比较的严格程度

**示例**:
```javascript
const obj1 = { a: 1, b: { c: 2 } };
const obj2 = { a: 1, b: { c: 3 } };
// 返回 false，因为 b 对象引用相同（浅比较）

const obj3 = { a: 2, b: { c: 2 } };
// 返回 true，因为 a 属性值不同
```

### 数据同步函数

#### syncDataFromSuper(data, superObject, prevSuperObject, store, force)
**功能**: 从上级数据源智能同步数据到当前组件

**参数说明**:
- **data**: 当前组件的数据，作为同步的基础
- **superObject**: 上级数据源（当前值）
- **prevSuperObject**: 上级数据源（之前的值）
- **store**: 当前组件的store实例，用于确定同步策略
- **force**: 是否强制同步

**同步策略**:
- **FormStore**: 同步表单项字段 + 当前数据中已存在的字段（去重）
- **普通 Store + force=true**: 同步当前数据中已存在的所有字段
- **普通 Store + force=false**: 不同步任何字段（keys数组为空）

**同步条件**: 只有当字段在上级数据中发生变化时才会被同步
- 即 `prevSuperObject[key] !== superObject[key]`

### 数据构建函数

#### createObject(superData, data)
**功能**: 创建具有原型链的数据对象

**参数作用**:
- **superData**: 作为新对象的原型链，同时设置为__super属性的值
- **data**: 作为新对象的自身属性添加

**特点**:
- 新对象可以通过原型链访问 superData 中的属性
- 新对象有不可写且不可枚举的 __super 属性指向 superData
- data 中的属性会覆盖原型链中的同名属性

#### extendObject(superData, data)
**功能**: 扩展数据对象

**特点**:
- 将 superData 和 data 合并为一个新对象
- 常用于独立数据模式

### 数据格式化函数

#### formatData(data)
**功能**: 格式化组件接收的数据，确保数据格式符合组件要求

**格式化规则**:
- **数组数据**: 转换为 `{items: data}` 格式
- **其他数据**: 原样返回

**使用场景**:
- 独立数据模式初始化时格式化 defaultData 和 props.data
- 组件更新时格式化传入的数据
- 确保数组类型的数据能正确被组件识别和使用

**示例**:
```javascript
// 数组数据格式化
formatData([1, 2, 3]); // 返回 {items: [1, 2, 3]}

// 对象数据保持原样
formatData({name: 'test', value: 123}); // 返回 {name: 'test', value: 123}

// null/undefined 保持原样
formatData(null); // 返回 null
```

### 表达式计算函数

#### tokenize(expression, data)
**功能**: 计算表达式在给定数据上下文中的值

**用途**:
- trackExpression 的值计算
- 通过比较表达式值的变化来检测数据更新

## 层级限制问题

### 问题描述
当前实现中存在层级限制，导致深层数据变化无法被检测到：

```javascript
// 只检查一层 __super
props.data.__super !== prevProps.data.__super

// 无法检测到更深层的变化
props.data.__super.__super.someValue !== prevProps.data.__super.__super.someValue
```

### 解决方案

#### 1. 使用 trackExpression（推荐）
```javascript
{
  "type": "crud",
  "trackExpression": "${ids}",  // 直接跟踪顶层数据
  // 其他配置...
}
```

#### 2. 配置 syncSuperStore
```javascript
{
  "type": "crud", 
  "syncSuperStore": true,  // 强制启用深层检查
  // 其他配置...
}
```

#### 3. 自定义 shouldSyncSuperStore
```javascript
// 在组件定义中
shouldSyncSuperStore: (store, props, prevProps) => {
  // 自定义同步逻辑
  return someCondition;
}
```

## 性能考虑

### 检查成本
- **trackExpression**: 表达式计算成本，但精确控制
- **浅比较**: 成本低，但可能遗漏变化
- **深层检查**: 成本高，但覆盖全面

### 优化建议
1. **精确跟踪**: 使用 `trackExpression` 只跟踪必要的数据
2. **合理分层**: 避免过深的组件嵌套
3. **缓存策略**: 在数据变化频繁的场景中使用防抖

## 最佳实践

### 1. 数据设计
- 保持数据结构扁平化
- 避免过深的嵌套关系
- 合理使用数据继承

### 2. 组件配置
- 明确指定 `trackExpression` 跟踪关键数据
- 根据场景选择合适的 `extendsData` 配置
- 在性能敏感场景中自定义 `shouldSyncSuperStore`

### 3. 调试技巧
- 使用 `console.log` 跟踪数据变化
- 检查组件的 `props.data` 和 `store.data`
- 验证数据链的完整性

## 常见问题

### Q: 为什么子组件没有响应数据变化？
A: 检查以下几点：
1. 数据变化是否超过了层级限制
2. 是否配置了正确的 `trackExpression`
3. `extendsData` 和 `syncSuperStore` 配置是否正确

### Q: 如何调试数据链问题？
A: 
1. 在关键组件中添加调试代码
2. 检查 `props.data.__super` 链的完整性
3. 验证 `WithStore` 的同步条件是否满足

### Q: 性能优化建议？
A:
1. 使用精确的 `trackExpression` 而不是全量检查
2. 避免在高频更新的组件中使用深层检查
3. 合理设计数据结构，减少不必要的嵌套
