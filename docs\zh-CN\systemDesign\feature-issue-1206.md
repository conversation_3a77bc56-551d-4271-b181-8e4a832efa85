# Input-image 组件 Tooltip 遮挡问题分析与解决方案

## 问题概述

在使用 input-image 组件时，当 hover 到上传按钮上时，提示语"点击选择图片或拖拽图片到这里"会被遮挡，无法完整显示。此问题在特定布局环境下出现，特别是当组件位于 `left-right-container` 的右侧区域时。

问题复现代码：
```js
{
  "type": "page",
  "bodyClassName": "h-screen",
  "body": {
    "type": "form",
    "autoFillHeight": true,
    "body": {
      "type": "left-right-container",
      "defaultWidth": 250,
      "left": {
        // 左侧内容
      },
      "right": {
        "type": "form",
        "static": true,
        "mode": "vertical",
        "actions": [],
        "body": [
          {
            "type": "input-image",
            "label": "图片"
          }
        ]
      }
    }
  }
}
```

## 根因分析

通过分析源代码，发现问题的根本原因有：

1. **CSS 定位机制**：`input-image` 组件的提示 tooltip 使用了 `position: absolute` 和 `left: 100%` 定位，使其显示在组件右侧：
   ```css
   &-pasteTip {
     pointer-events: none;
     position: absolute;
     left: 100%; 
     top: 50%;
     transform: translateY(-50%);
     white-space: nowrap;
     /* 其他样式... */
     margin: 30px 0 0 var(--Tooltip--attr-gap);
   }
   ```

2. **容器限制**：`left-right-container` 会限制右侧容器的宽度，并且通常会设置 `overflow: auto` 或 `overflow: hidden`，导致超出容器部分的 tooltip 被截断。

3. **DOM 层级问题**：tooltip 是在 `ImageControl-addBtn` 内部渲染的，受父容器的 `overflow` 属性限制，无法突破容器边界显示。

## 解决方案

### 方案一：Portal 渲染（最佳方案）

将 tooltip 通过 React Portal 渲染到 `body` 层级，这样就不会被任何容器的 `overflow` 属性限制。

实现步骤：
1. 在 `ImageControl` 组件中创建 Portal 渲染 tooltip
2. 计算 tooltip 相对于视口的绝对位置
3. 在 hover 时显示 Portal tooltip

```tsx
// 修改 InputImage.tsx
import ReactDOM from 'react-dom';

// 添加 Portal tooltip 组件
function TooltipPortal({content, targetElement, visible}) {
  if (!visible || !targetElement) return null;
  
  // 计算位置
  const rect = targetElement.getBoundingClientRect();
  const style = {
    position: 'fixed',
    left: rect.right + 8, // 显示在元素右侧
    top: rect.top + rect.height / 2, // 垂直居中
    transform: 'translateY(-50%)',
    // 其他样式保持不变...
  };
  
  return ReactDOM.createPortal(
    <div className="ImageControl-portalTooltip" style={style}>{content}</div>,
    document.body
  );
}

// 修改组件 render 方法
render() {
  // 已有代码...
  
  return (
    <>
      {/* 已有渲染内容... */}
      
      {/* 添加 Portal tooltip */}
      <TooltipPortal 
        content={__('Image.pasteTip')} 
        targetElement={this.frameImageRef.current}
        visible={this.state.isHover}
      />
    </>
  );
}
```

### 方案二：自适应方向

修改 tooltip 显示逻辑，根据可用空间自动调整显示位置（上/右/下/左）：

```tsx
// 在 handleMouseEnter 方法中计算最佳显示方向
handleMouseEnter() {
  this.setState({
    isHover: true
  });
  
  // 延迟一帧计算位置
  requestAnimationFrame(() => {
    const element = this.frameImageRef.current;
    if (!element) return;
    
    const rect = element.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    
    // 计算各方向可用空间
    const rightSpace = viewportWidth - rect.right;
    const leftSpace = rect.left;
    const topSpace = rect.top;
    const bottomSpace = viewportHeight - rect.bottom;
    
    // 确定最佳方向
    let direction = 'right'; // 默认右侧
    const maxSpace = Math.max(rightSpace, leftSpace, topSpace, bottomSpace);
    
    if (maxSpace === rightSpace && rightSpace >= 100) {
      direction = 'right';
    } else if (maxSpace === leftSpace && leftSpace >= 100) {
      direction = 'left';
    } else if (maxSpace === topSpace && topSpace >= 50) {
      direction = 'top';
    } else if (maxSpace === bottomSpace && bottomSpace >= 50) {
      direction = 'bottom';
    }
    
    this.setState({ tooltipDirection: direction });
  });
}
```

### 方案三：临时解决方案（`fixedSize` 属性）

作为临时解决方案，可以使用 `fixedSize: true` 和 `fixedSizeClassName: "h-[200px]"` 为组件设置足够的显示空间。但这不是理想方案，因为它改变了组件的原始布局需求。

```js
{
  "type": "input-image",
  "label": "图片",
  "fixedSize": true,
  "fixedSizeClassName": "h-[200px]"
}
```

## 技术实现

推荐使用方案一（Portal 渲染），需要修改以下文件：

1. `packages/amis/src/renderers/Form/InputImage.tsx`
   - 添加 Portal tooltip 组件
   - 修改 hover 处理逻辑

2. `packages/amis-ui/scss/components/form/_image.scss`
   - 添加 Portal tooltip 样式

实现此方案不会影响现有功能，同时能确保 tooltip 在任何布局环境下都能正常显示。

## Mermaid 图解

```mermaid
flowchart TD
    A["用户 hover input-image 上传按钮"] --> B["触发 handleMouseEnter()"]
    B --> C{"当前实现: 显示内部 tooltip"}
    C --> D["tooltip 使用 position: absolute 定位"]
    D --> E["tooltip 设置 left: 100% 显示在右侧"]
    E --> F["父容器 overflow 限制，导致 tooltip 被截断"]
    
    B --> G{"改进方案: Portal 渲染 tooltip"}
    G --> H["计算上传按钮在视口中的位置"]
    H --> I["在 body 中创建 Portal tooltip"]
    I --> J["设置 tooltip 的 position: fixed"]
    J --> K["tooltip 不受任何容器限制"]
```

## 测试验证

使用 Playwright 测试改进后的组件：

1. 验证普通布局下 tooltip 显示正常
2. 验证在 left-right-container 右侧时 tooltip 显示正常
3. 验证在各种容器嵌套情况下 tooltip 显示正常

完整测试代码示例：

```javascript
const { chromium } = require('playwright');

(async () => {
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  try {
    // 访问测试页面
    await page.goto('http://localhost:8888/playground/index.html?issue=1206#/');
    
    // 悬停在上传按钮上
    await page.hover('button:has-text("图片上传")');
    
    // 验证 tooltip 是否可见且完整显示
    const tooltip = await page.waitForSelector('text=点击选择图片或拖拽图片到这里');
    const isVisible = await tooltip.isVisible();
    
    if (isVisible) {
      console.log('✅ Tooltip 显示正常');
    } else {
      console.log('❌ Tooltip 不可见或被截断');
    }
  } finally {
    await browser.close();
  }
})();
``` 
