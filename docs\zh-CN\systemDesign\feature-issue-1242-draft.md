# Issue1242 问题分析草稿

## 我们达成的一致理解

### 核心问题
Issue1242 的根本问题是 `extractDataFromChainExcluding` 函数的 `excludeObj` 机制失效了。

### 失效原因（已确认）
1. **原始设计**：`extractDataFromChainExcluding` 函数设计时接受一个 `excludeObj` 参数，用于在遍历数据链时进行引用比较 (`current !== excludeObj`)，当遇到需要排除的对象时停止遍历

2. **克隆触发条件（已确认）**：在 `resolveEventData` 函数中调用 `createObject(props.data, eventData)` 时，如果 `props.data` 是被冻结的对象（`Object.isFrozen()`），`createObject` 会自动调用 `cloneObject` 进行克隆

3. **数据冻结来源（已确认）**：`renderer.props.data` 被冻结的原因不是在 mobx-state-tree 中被冻结，而是在某个环节中被 `Object.freeze()` 处理，具体冻结时机需要进一步分析

4. **引用链变化**：`cloneObject` 的关键问题在于克隆逻辑：
   ```typescript
   const obj = target && target.__super
     ? Object.create(target.__super, { __super: { value: target.__super, writable: false, enumerable: false } })
     : Object.create(Object.prototype);
   ```
   克隆后的对象的 `__super` 指向原对象的 `__super`，而不是原对象本身，导致引用链发生变化

5. **比较失败**：当 `extractDataFromChainExcluding` 执行时，它用来比较的引用已经不是原来的引用了，导致引用比较 (`current !== excludeObj`) 失败，函数继续遍历原本应该停止的层级

6. **数据污染**：因为过度遍历，业务数据覆盖了系统字段（如 `selectedItems`），导致用户无法获取到正确的事件数据

### 原来 excludeObj 的设计原理（重新理解）

查看 `runAction` 中的数据构建逻辑：

```typescript
const mergeData = createObject(
  createObject(
    renderer.props.data.__super
      ? createObject(renderer.props.data.__super, additional)
      : additional,
    renderer.props.data
  ),
  eventDataExtracted  // 使用提取的完整 event.data 链
);
```

**原来的 excludeObj 设计意图（重新理解）**：
1. **传入 `renderer.props.data` 是正确的**：目的是从 `event.data` 中提取数据时，遇到 `renderer.props.data` 这一层就停止
2. **避免数据重复**：因为 `renderer.props.data` 会在 `mergeData` 构建时单独添加到中间层，如果从 `event.data` 中也提取它，会导致数据重复
3. **保护优先级**：防止 `eventDataExtracted` 中的组件数据覆盖 `mergeData` 中间层的组件数据，破坏预期的数据优先级

**引用比较失效的根本原因**：
- 原本 `event.data.__super` 指向 `renderer.props.data`（原始引用）
- 但因为 `createObject` 检测到 `renderer.props.data` 被冻结，自动调用 `cloneObject` 克隆
- 克隆后 `event.data.__super` 指向的是克隆对象，不再是原始的 `renderer.props.data`
- 所以 `extractDataFromChainExcluding(event.data, renderer.props.data)` 的引用比较失败
- **问题**：过度遍历导致组件数据被错误提取，在 `mergeData` 中造成重复和优先级混乱

### 当前解决方案的正确性（已确认）

经过代码分析，当前的 `__isActionResult` 标记机制是**正确且精准的**：

1. **精准提取**：只提取有 `__isActionResult` 标识的动作结果层 + 第一个没有标识的初始事件数据层
2. **避免重复**：不会提取 `renderer.props.data` 层，因为它没有 `__isActionResult` 标识且不是第一个无标识层
3. **保持完整性**：确保所有动作执行结果和初始事件数据都被保留
4. **维护优先级**：`eventDataExtracted` 只包含事件相关数据，不包含组件数据，避免了数据重复和优先级混乱

### 具体流程
1. Select组件触发change事件
2. `resolveEventData` 创建事件数据，设置原型链 `event.data.__super = renderer.props.data` (克隆后的引用)
3. 某个环节中，`renderer.props.data` 被克隆为新对象（引用变化）
4. 用户在动作中访问 `event.data.selectedItems` 
5. `runAction` 调用 `extractDataFromChainExcluding(event.data)` 提取数据
6. 引用比较失败，过度遍历，业务数据污染事件数据
7. 用户获取到空值而不是预期的选中数据

### 根本原因（我的推测）
`extractDataFromChainExcluding` 函数在遍历数据链时，可能因为某种原因（比如引用比较失效、遍历逻辑问题等）没有正确提取到初始事件数据中的 `selectedItems`，导致用户在动作中无法获取到这些数据。

## 解决方案机制
- **`__isActionResult` 标记**：替代不可靠的引用比较
- **新的遍历逻辑**：基于标记而不是引用进行数据提取控制 
