# Select组件事件数据获取失效问题

## 问题概述

用户在Select组件的onChange事件中通过 `event.data.selectedItems` 表达式无法获取到选中项数据，返回空值。

**复现代码**：
```collapsible title="Issue1242 复现示例" lang="javascript" open="false"
!!!includeFile(playground/components/issues/issue1242.js)!!!
```

## 整体调用流程

```mermaid
sequenceDiagram
    participant User as "👤 用户"
    participant Select as "📋 Select组件"
    participant ResolveData as "🔧 resolveEventData"
    participant EventSystem as "📡 事件系统"
    participant Action as "🚀 Action执行"
    participant Extract as "📊 数据提取"
    
    User->>Select: 选择选项
    Select->>Select: changeValue触发
    Select->>ResolveData: "resolveEventData(props, eventData, 'value')"
    Note over ResolveData: "创建事件数据对象，createObject(renderer.props.data, eventData)"
    ResolveData-->>Select: 返回事件数据对象
    
    Select->>EventSystem: "props.dispatchEvent('change', eventData)"
    Note over EventSystem: "创建RendererEvent对象，执行事件监听器"
    
    EventSystem->>Action: "runActions(actions, renderer, event)"
    
    loop 每个Action
        Action->>Action: "runAction(action, renderer, event)"
        Action->>Extract: "extractDataFromChainExcluding(event.data)"
        Note over Extract: "逐层提取event.data链中的数据，遇到renderer.props.data时停止，排除该层及其后续链路"
        Extract-->>Action: eventDataExtracted
        Action->>Action: "创建4层mergeData数据链，eventDataExtracted → renderer.props.data → additional → renderer.props.data.__super"
        Action->>Action: 使用mergeData解析动作args参数中的表达式
        Action->>Action: "执行具体动作逻辑(ajax、setValue等)"
        Note over Action: 动作返回值通过event.setData链入数据链
    end
    
    EventSystem-->>Select: 返回事件处理结果
    Select-->>User: 响应用户操作
```

## 根因分析

### 现象观察

在使用 Select 组件的 onChange 事件时，我们发现了一个奇怪的现象：
- ❌ `${event.data.selectedItems}` - 无法获取数据
- ✅ `${selectedItems}` - 可以正常获取数据

这说明 `event.data` 中的数据出现了异常，但 `selectedItems` 仍然存在于数据链的其他位置。

### 数据流追踪

```mermaid
flowchart TD
    A["用户点击按钮"] --> B["Dialog弹窗打开"]
    B --> C["弹窗创建数据链环境，包含弹窗打开的event对象"]
    
    C --> D["用户操作Select组件"]
    D --> E["Select onChange触发，创建新的事件数据"]
    E --> F["事件数据包含selectedItems"]
    
    F --> G["第一个动作开始执行"]
    G --> H["调用extractDataFromChainExcluding，提取事件数据"]
    H --> I["创建4层数据原型链，用于表达式解析"]
    
    I --> J["第一个动作完成"]
    J --> K{"还有其他动作?"}
    K -->|是| L["下一个动作执行"]
    L --> H
    K -->|否| M["所有动作执行完成"]
    
    subgraph "关键环境"
        N["Select组件处于弹窗数据链中，上层包含弹窗的event对象"]
    end
    
    C -.-> N
    N -.-> H
    
    style N fill:#ffebee,stroke:#c62828,stroke-width:2px
```

### 数据提取合并方案设计

当每个动作执行时，系统需要提取组合数据，解析表达式参数用。原始的数据提取合并方案：

```mermaid
flowchart TD
    subgraph "4层数据原型链结构"
        A["第1层: eventDataExtracted，extractDataFromChainExcluding提取出来的数据，• 组件事件本身的数据 (如selectedItems)，• 前面动作执行结果的数据"] 
        
        A --> B["第2层: renderer.props.data"]
        
        B --> C["第3层: additional，• event (表达式${event.data.selectedItems}的预期来源)"]
        
        C --> D["第4层: renderer.props.data.__super"]
    end
    
    E["优先级: 第1层 > 第2层 > 第3层 > 第4层"]
    
    style C fill:#ffebee,stroke:#c62828,stroke-width:2px
```

### 数据提取逻辑分析

`extractDataFromChainExcluding` 负责为合并方案提供数据来源：

> **排除对象**：指定的边界对象，当遍历到这个对象时停止向上提取，防止提取不应该提取的上层数据

```mermaid
flowchart TD
    A["开始数据提取"] --> B["遍历数据链"]
    B --> C{"当前对象是否为排除对象?"}
    C -->|是| D["停止遍历"]
    C -->|否| E["提取当前层数据"]
    E --> F["移动到上一层 __super"]
    F --> G{"还有上层数据?"}
    G -->|是| C
    G -->|否| H["遍历结束"]
    D --> I["返回提取的数据"]
    H --> I
    
    J["排除对象: renderer.props.data"] -.-> C
    
    style C fill:#ff9999,stroke:#cc0000,stroke-width:2px
    style C stroke-dasharray: 5 5
    style J fill:#e1f5fe,stroke:#01579b,stroke-width:1px
```

**关键问题**：上图中红色虚线标记的"排除对象判断"会失效！

### 引用比较失效原因

为什么排除对象判断必然会失效？因为 `renderer.props.data` 在数据链传递过程中被克隆，导致引用关系改变：

```mermaid
flowchart TD
    A["事件数据创建"] --> B["resolveEventData 调用"]
    B --> C["createObject 执行"]
    C --> D{"检测 renderer.props.data 是否冻结"}
    D -->|是| E["自动调用 cloneObject"]
    D -->|否| F["直接使用原对象"]
    E --> G["克隆 renderer.props.data，用克隆对象替代原对象作为原型"]
    G --> H["event.data.__super 指向克隆对象，而非原始 renderer.props.data"]
    H --> I["current === original ❌"]
    F --> J["引用关系保持"]
    J --> K["current === original ✅"]
    
    K --> L["比较成功，正确停止遍历"]
    I --> M["比较失败，继续向上遍历"]
    M --> N["错误提取上层数据"]
    
    style I fill:#ffebee,stroke:#c62828,stroke-width:2px
    style M fill:#ffebee,stroke:#c62828,stroke-width:2px  
    style N fill:#ffebee,stroke:#c62828,stroke-width:2px
```



### 问题触发的完整条件链

```mermaid
flowchart TD
    A["触发条件1: 多动作场景"] --> B["Dialog动作执行"]
    B --> C["event对象传递到上层数据链"]
    C --> D["触发条件2: 数据冻结检测"]
    D --> E["createObject检测到冻结对象"]
    E --> F["触发条件3: 自动克隆机制"]
    F --> G["cloneObject破坏引用关系"]
    G --> H["触发条件4: 引用比较失效"]
    H --> I["extractDataFromChainExcluding无法停止"]
    I --> J["问题结果: 数据覆盖"]
    
    style J fill:#ffebee,stroke:#c62828,stroke-width:2px
```





### 现象解释：为什么 selectedItems 表达式仍然有效

这个现象揭示了数据提取机制的一个重要特性：

```mermaid
flowchart TD
    subgraph "问题核心：数据提取过程"
        A["extractDataFromChainExcluding 执行"] --> B["边界控制失效，过度遍历"]
        B --> C["提取到正确的 selectedItems 字段"]
        B --> D["同时提取到错误的 event 对象"]
    end
    
    subgraph "结果：mergeData 数据污染"
        C --> E["第1层放入: selectedItems ✅"]
        D --> F["第1层放入: event ❌ 覆盖正确的"]
        E --> G["selectedItems 能直接访问到正确数据"]
        F --> H["event.data.selectedItems 访问到错误对象"]
    end
    
    subgraph "最终表现：表达式差异"
        G --> I["获得正确数据 ✅"]
        H --> J["event.data 不存在"]
        J --> K["返回 undefined ❌"]
    end
```



## 解决方案设计

### 数据提取的初心

```mermaid
flowchart TD
    subgraph "多动作场景的数据传递问题"
        A["Select onChange: event.data包含selectedItems"] --> B["第1个动作setValue执行"]
        B --> C["setValue动作产生新的event.data，原始selectedItems变成第二层"]
        C --> D["第2个动作Toast执行"]
        D --> E["Toast需要访问第二层的selectedItems"]
    end
    
    subgraph "问题：如果不做数据提取"
        F["每次动作执行需要合并数据"] --> G["只能获取当前层数据，即上一个动作的结果数据"]
        G --> H["无法获取完整的事件数据链"]
        H --> I["mergeData缺少原始selectedItems"]
        I --> J["mergeData缺少更早的动作结果"]
        J --> K["${selectedItems}失效"]
        J --> L["${event.data.selectedItems}失效"]
    end
    
    subgraph "解决方案：数据链提取"
        M["extractDataFromChainExcluding执行"] --> N["遍历完整的事件数据链"]
        N --> O["提取原始事件数据: selectedItems"]
        N --> P["提取各动作的结果数据"]
        O --> Q["合并到mergeData统一上下文"]
        P --> Q
        Q --> R["${selectedItems}正常工作"]
        Q --> S["${event.data.selectedItems}正常工作"]
    end
    
    E --> F
    L -.->|需要解决| M
    
    style I fill:#ffebee,stroke:#c62828,stroke-width:2px
    style J fill:#ffebee,stroke:#c62828,stroke-width:2px
    style K fill:#ffebee,stroke:#c62828,stroke-width:2px
    style L fill:#ffebee,stroke:#c62828,stroke-width:2px
```

### 解决思路：从引用依赖到标记驱动

```mermaid
flowchart TD
    subgraph "旧方案问题"
        A[依赖引用比较判断边界] --> B[克隆破坏引用关系]
        B --> C[边界控制失效]
        C --> D[过度提取错误数据]
    end
    
    subgraph "新方案：动作结果标记机制"
        E[动作执行时调用event.setData] --> F[给新添加的动作结果数据添加__isActionResult标记]
        F --> G[动作结果数据自带身份标识]
        G --> H[标记不受引用变化影响]
    end
    
    subgraph "新的数据提取逻辑"
        I[extractDataFromChainExcluding开始遍历] --> J{当前层有__isActionResult?}
        J -->|有标记| K[识别为动作结果层<br/>提取数据，继续遍历]
        J -->|无标记| L{之前是否找到过动作结果层?}
        L -->|是| M[识别为原始事件层<br/>提取数据并停止]
        L -->|否| N[识别为组件数据层<br/>跳过，继续遍历]
        K --> O[foundActionResult = true]
        O --> P[移动到__super继续]
        P --> J
        N --> P
    end
    
    D -.->|解决| E
    H --> I
    
    style D fill:#ffebee,stroke:#c62828,stroke-width:2px
```



## 验证

### Dialog 中 Select 组件的事件数据访问

Dialog 中的 Select 组件 onChange 事件。点击按钮打开弹窗，选择选项后会显示两条 Toast，验证 `${event.data.selectedItems}` 表达式是否正常工作。

```schema
{
  "type": "page",
  "body": {
    "type": "button",
    "label": "打开弹窗",
    "onEvent": {
      "click": {
        "actions": [
          {
            "actionType": "dialog",
            "dialog": {
              "title": "验证 Issue1242",
              "body": {
                "type": "form",
                "body": [
                  {
                    "type": "select",
                    "name": "browser",
                    "label": "选择浏览器",
                    "options": [
                      {
                        "label": "Internet Explorer",
                        "value": "IE"
                      },
                      {
                        "label": "Firefox", 
                        "value": "FIREFOX"
                      },
                      {
                        "label": "Chrome",
                        "value": "CHROME"
                      }
                    ],
                    "onEvent": {
                      "change": {
                        "actions": [
                          {
                            "actionType": "toast",
                            "args": {
                              "msgType": "info",
                              "msg": "event.data.selectedItems: ${event.data.selectedItems | json}"
                            }
                          },
                          {
                            "actionType": "toast",
                            "args": {
                              "msgType": "success", 
                              "msg": "selectedItems: ${selectedItems | json}"
                            }
                          }
                        ]
                      }
                    }
                  }
                ]
              }
            }
          }
        ]
      }
    }
  }
}
```



## 总结

**问题**：`extractDataFromChainExcluding` 的引用比较机制在对象克隆时失效，导致边界控制失败，提取到错误的上层数据，最终使 `${event.data.selectedItems}` 表达式返回 undefined。

**解决方案**：用 `__isActionResult` 标记替代引用比较，通过标记识别数据层类型，精确控制提取范围。

## 待办
为什么官网能工作
