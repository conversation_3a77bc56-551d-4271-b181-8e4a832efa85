# Issue 1292: Radio 组件在 input-table 中显示 extraLabel 问题

## 问题描述

在 input-table 中使用 radios 组件时，每个 radio 选项会同时显示 label 和 value，例如：
- 应该显示：`OptionA`
- 实际显示：`OptionAa`（OptionA + a）

## 问题复现

访问：http://localhost:8888/playground/index.html?issue=1292#/

配置示例：
```javascript
{
  "type": "input-table",
  "columns": [
    {
      "name": "radios",
      "label": "radios",
      "type": "radios",
      "options": [
        {"label": "OptionA", "value": "a"},
        {"label": "OptionB", "value": "b"},
        {"label": "OptionC", "value": "c"},
        {"label": "OptionD", "value": "d"}
      ]
    }
  ]
}
```

## 问题分析

### 根本原因

问题出现在 radio 组件的 `translateLabel` 方法中，当 `extraLabel` 为 `undefined` 时，错误地渲染了空字符串，导致在 input-table 环境下显示了当前选中的 value 值。

### extraLabel 的设计用途

extraLabel 是用于在 radio 选项的 label 后面显示额外内容的功能，通常用于：
- tooltip 提示信息
- 图标说明
- 复杂的 schema 组件

当 extraLabel 为 `undefined` 时，应该不渲染任何内容。

### 关键代码位置和问题流程

1. **问题触发**：`packages/amis/src/renderers/Form/Radios.tsx` 第105和111行
   ```typescript
   extraLabel: render(`options/${index}`, item.extraLabel ?? ''),
   ```
   当 `item.extraLabel` 为 `undefined` 时，`??` 操作符返回空字符串

2. **字符串转换**：`packages/amis-core/src/Root.tsx` 第186-189行
   ```typescript
   let schema: Schema =
     typeofnode === 'string' || typeofnode === 'number'
       ? {type: 'tpl', tpl: String(node)}
       : (node as Schema);
   ```
   空字符串被转换为 `{type: 'tpl', tpl: ''}`

3. **value 传递差异**：`packages/amis/src/renderers/Table/TableCell.tsx` 第105行
   ```typescript
   render('field', schema, {
     // ...
     value,  // input-table 中明确传递了 value 属性
     // ...
   });
   ```

4. **Tpl 回退逻辑**：`packages/amis/src/renderers/Tpl.tsx` 第80-84行
   ```typescript
   return value == null || value === ''
     ? `<span class="text-muted">${placeholder}</span>`
     : typeof value === 'string'
     ? value
     : JSON.stringify(value);
   ```
   在 input-table 中，Tpl 组件的 `props.value` 是当前选中值（如 "a"），直接返回

### 问题流程

```mermaid
flowchart TD
    A[用户选择 radio 选项] --> B[radio 组件重新渲染]
    B --> C[调用 translateLabel 方法]
    C --> D{检查 option.extraLabel}
    D -->|undefined| E[item.extraLabel ?? '' 返回空字符串]
    E --> F[调用 render options/0 空字符串]
    F --> G[renderChild 转换为 type: tpl, tpl: 空字符串]
    G --> H[创建 Tpl 组件]
    H --> I{检查环境}
    I -->|input-table| J[TableCell 传递 value=a 给 Tpl]
    I -->|普通 form| K[Form 不传递 value 给 Tpl]
    J --> L[Tpl.getPropValue 返回 props.value=a]
    K --> M[Tpl.getPropValue 返回 undefined]
    L --> N[extraLabel 显示为 a]
    M --> O[extraLabel 不显示]
    N --> P[最终显示: OptionA + a = OptionAa]
    O --> Q[最终显示: OptionA]

    style A fill:#e1f5fe
    style P fill:#ffebee
    style Q fill:#e8f5e8
    style J fill:#fff3e0
    style L fill:#fff3e0
```

**关键差异**：
- **input-table**：TableCell 明确传递 `value` 属性给子组件
- **普通 form**：Form 不传递 `value` 属性给子组件

**问题时机**：
- 不是初始渲染时发生，而是在**用户选择选项后的重新渲染**中发生
- 初始状态下所有选项显示正常，选择后才出现 "OptionAa" 问题

### 为什么只在 input-table 中出现

关键差异在于**value 属性的传递**：

```mermaid
flowchart LR
    subgraph "普通 Form 中的 radio"
        A1[Form.renderChild] --> B1[不传递 value 属性]
        B1 --> C1[Tpl 组件 props.value = undefined]
        C1 --> D1[getPropValue 返回 undefined]
        D1 --> E1[extraLabel 不显示内容]
    end

    subgraph "input-table 中的 radio"
        A2[TableCell.render] --> B2[明确传递 value 属性]
        B2 --> C2[Tpl 组件 props.value = 当前选中值]
        C2 --> D2[getPropValue 返回选中值 如 a]
        D2 --> E2[extraLabel 显示为 a]
    end

    style A2 fill:#e1f5fe
    style B2 fill:#fff3e0
    style D2 fill:#ffebee
    style E2 fill:#ffebee
```

#### 核心差异

**TableCell 中的 render 调用**：
```typescript
render('field', schema, {
  // ...
  value,  // ← 明确传递了 value 属性
  // ...
});
```

**Form 中的 render 调用**：
```typescript
// SchemaRenderer 第486行注释：
// value: defaultValue, // 备注: 此处并没有将value传递给渲染器
```

#### getPropValue 函数的行为差异

```typescript
export function getPropValue(props) {
  const { name, value, data, defaultValue } = props;
  return (
    value ??                    // input-table: 'a', 普通form: undefined
    getter?.(props) ??
    resolveValueByName(data, name) ??
    defaultValue
  );
}
```

- **input-table**：`props.value = 'a'`，直接返回选中值
- **普通 form**：`props.value = undefined`，继续查找其他来源，最终返回 `undefined`

## 修复方案

### 推荐方案：条件渲染 extraLabel

基于 extraLabel 的设计用途，当 extraLabel 为 `undefined` 时，不应该渲染任何内容。

**修改位置**：`packages/amis/src/renderers/Form/Radios.tsx` 第105和111行

```typescript
// 修改前
extraLabel: render(`options/${index}`, item.extraLabel ?? ''),

// 修改后
extraLabel: item.extraLabel ? render(`options/${index}`, item.extraLabel) : undefined,
```

### 方案优势

1. **符合设计初衷**：extraLabel 本来就是可选的，undefined 时不应该渲染
2. **避免错误渲染**：不会创建不必要的 Tpl 组件
3. **性能更好**：减少组件创建和渲染开销
4. **逻辑清晰**：只有真正有内容的 extraLabel 才会被处理
5. **影响范围可控**：只影响 radio 组件的 extraLabel 渲染逻辑

### 其他备选方案

**方案二：传递 null 而非空字符串**
```typescript
extraLabel: render(`options/${index}`, item.extraLabel ?? null),
```
但这仍然会创建不必要的组件，不如方案一直接。

## 相关文件

- `packages/amis/src/renderers/Form/Radios.tsx` - 问题触发点和修复位置
- `packages/amis/src/renderers/Table/TableCell.tsx` - value 属性传递
- `packages/amis-core/src/utils/helper.ts` - getPropValue 函数
- `packages/amis/src/renderers/Tpl.tsx` - Tpl 组件回退逻辑
- `playground/components/issues/issue1292.js` - 问题复现示例

## extraLabel 设计说明

extraLabel 是用于在 radio 选项后显示额外内容的功能：
- 通常用于 tooltip、图标、复杂组件等
- 应该是完整的 schema 对象，而非简单文本
- 当为 `undefined` 时，不应该渲染任何内容

## 测试验证

修复后需要验证：
1. input-table 中的 radio 组件不再显示额外的 value 值
2. 正常的 extraLabel 功能仍然工作（tooltip、图标等）
3. 普通 Form 中的 radio 组件不受影响
4. 其他表格组件不受影响
5. 性能改善：减少了不必要的 Tpl 组件创建
