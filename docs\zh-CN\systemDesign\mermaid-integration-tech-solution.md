# Mermaid 图表集成技术方案

## 需求背景

现有 Markdown 文档支持 `schema`、`html` 代码块，但缺少 `mermaid` 图表支持。需要集成 Mermaid 功能，提升技术文档的可视化表达能力。

同时，项目存在 Vite（开发环境）和 FIS3（生产环境）双构建环境，两套独立的 Markdown 解析方案导致输出格式不一致、维护成本高。在添加 Mermaid 支持的过程中，我们统一了双构建环境的 Markdown 解析方案。

## 文档系统整体架构

```mermaid
flowchart TD
    A["docs 目录下的 Markdown 文档"]
    B["SCSS 文件中的内嵌文档"]
    C["统一的 Markdown 处理流程"]
    D["输出为 JS 模块"]
    E["前端统一渲染展示"]

    A --> C
    B --> C
    C --> D
    D --> E

    style A fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style B fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style C fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style D fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    style E fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
```

### 文档来源说明

**两种文档来源**：
- **独立 Markdown 文件**：存放组件文档、概念说明、最佳实践等主要内容
- **SCSS 内嵌文档**：在 CSS 辅助类文件中嵌入使用说明，保持代码和文档同步

**统一处理流程**：
- 两种来源的文档都经过相同的 Markdown 处理器
- 输出格式完全一致，包含 HTML 内容、目录结构、元数据
- 支持相同的特殊代码块语法（schema、html、mermaid 等）

## Markdown 解析调用流程

```mermaid
flowchart TD
    subgraph 构建阶段
        direction TB
        A1["Markdown输入"]
        A2["预处理（YAML头、include指令）"]
        A3["处理特殊代码块（mermaid/schema/html/collapsible）"]
        A4["标准Markdown语法解析"]
        A5["输出JS模块（含HTML、目录、元数据）"]
        A1 --> A2 --> A3 --> A4 --> A5
    end

    subgraph 渲染阶段
        direction TB
        B1["前端加载页面"]
        B2["扫描并激活自定义代码块（mermaid/schema/html/collapsible等）"]
        B3["动态渲染/交互（如mermaid.render、amis渲染、折叠交互等）"]
        B4["最终文档展示"]
        B1 --> B2 --> B3 --> B4
    end

    A5 --> B1

    style A1 fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style A5 fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    style B4 fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
```

> 渲染阶段不仅包括 mermaid 图表的动态渲染，还包括 schema 预览、html 代码块预览、collapsible 折叠等功能的运行时激活和交互支持。

## 内容流转示例

下面用一个简单的 Markdown 示例，配合流程图，说明文档的构建与渲染全过程：

### 🔸 阶段一：原始 Markdown

````markdown
---
title: 组件使用指南
description: 详细介绍组件的配置和使用方法
tags: [组件, 配置, 最佳实践]
---

# 我的文档

```mermaid
flowchart TD
    A --> B
```

```schema
{
  "type": "form",
  "body": [
    {"type": "input-text", "name": "a"}
  ]
}
```
````

> **特点**：包含 YAML 头信息、标题、mermaid 和 schema 代码块

---

### 🔸 阶段二：构建阶段处理

```js
export default {
  title: "组件使用指南",
  description: "详细介绍组件的配置和使用方法",
  tags: ["组件", "配置", "最佳实践"],
  html: "<h1>我的文档</h1><pre class='mermaid'>flowchart TD A --> B</pre><pre class='schema'>{...}</pre>",
  toc: [
    { anchor: "我的文档", level: 1, title: "我的文档" }
  ],
  meta: { /* 其他元数据 */ }
}
```

> **处理结果**：YAML 头信息被解析为 JS 对象的元数据字段，Markdown 内容转换为 HTML，特殊代码块保留原始内容

---

### 🔸 阶段三：渲染阶段激活

**页面加载后的动态处理：**

- ✅ **mermaid 代码块** → 渲染为 SVG 图表
- ✅ **schema 代码块** → 渲染为可交互表单  
- ✅ **页面标题** → 显示为"组件使用指南"
- ✅ **元数据** → 用于搜索、分类等功能

**最终用户看到的效果：**
- 带有正确标题的页面
- 可视化的流程图表
- 可交互的表单组件
- 完整的文档导航和搜索功能

> **核心机制**：前端运行时动态激活各种自定义代码块，YAML 元数据用于页面展示和功能增强

---

### 💡 流程总结

这个例子展示了：
- **YAML 头信息**：在构建阶段被解析为元数据，在渲染阶段用于页面标题、搜索、分类等功能
- **Markdown 内容**：包含 mermaid 和 schema 代码块，构建阶段统一输出为 JS 模块
- **渲染阶段**：前端分别激活 mermaid 和 schema 代码块，动态渲染 SVG 图表和表单
- **最终效果**：用户看到的是带有正确标题的可视化图表和可交互的表单

## 总结