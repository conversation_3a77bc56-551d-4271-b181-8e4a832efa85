# Mermaid 修复测试

## 测试1：正常的mermaid图表（应该渲染）

```mermaid
graph TD
    A[开始] --> B[处理数据]
    B --> C{判断条件}
    C -->|是| D[执行操作A]
    C -->|否| E[执行操作B]
    D --> F[结束]
    E --> F
```

## 测试2：中文字符mermaid图表（应该渲染）

```mermaid
flowchart TD
    A["用户登录 🔐"] --> B{"验证 token === valid"}
    B -->|有效| C["进入系统 ✅"]
    B -->|无效| D["显示错误 ❌"]
    C --> E["data.user != null"]
    E --> F["加载用户数据 ${userId}"]
```

## 测试3：使用缩进代码块展示mermaid代码（不应该渲染，应该显示为代码）

    ```mermaid
    graph TD
        G[这个不应该被渲染] --> H[应该显示为代码]
    ```

## 测试4：时序图（应该渲染）

```mermaid
sequenceDiagram
    participant A as 用户
    participant B as 系统
    
    A->>B: 发送请求
    B-->>A: 返回响应
```

## 测试5：多个混合情况

正常图表1：
```mermaid
graph LR
    X --> Y
```

缩进代码块示例（不应该渲染）：

    ```mermaid
    graph LR
        Z --> W
    ```

正常图表2：
```mermaid
graph TB
    P --> Q
```

## 测试6：使用 ```text 展示代码（应该显示为代码）

```text
```mermaid
graph TD
    X[这个应该显示为文本] --> Y[不应该被渲染]
```
```

## 测试7：collapsible 代码块（应该可以折叠）

```collapsible title="测试折叠代码块" lang="javascript" open="false"
const test = {
  name: "collapsible test",
  value: 123,
  items: [1, 2, 3]
};
console.log(test);
```
