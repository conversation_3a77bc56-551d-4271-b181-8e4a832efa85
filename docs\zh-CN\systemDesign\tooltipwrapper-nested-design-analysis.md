# TooltipWrapper嵌套设计分析与解决方案

## 问题背景

在Select组件的多选模式下，当设置了`maxTagCount`时，超过数量的项会被折叠起来。用户希望在折叠项的弹出层中，每个具体项也能显示tooltip，但遇到了嵌套TooltipWrapper的冲突问题。

## TooltipWrapper核心机制分析

### 1. 核心工作原理总结

TooltipWrapper通过**全局单例 + enterable保护**机制管理tooltip显示：

1. ✅ **全局单例**：只有一个tooltip会显示
2. ✅ **target mouseover**：触发show + 立即隐藏其他tooltip
3. ✅ **target mouseout**：触发延时隐藏自己（如300ms）
4. ✅ **tooltip mouseover**：取消延时隐藏（enterable保护）
5. ✅ **tooltip mouseout**：**立即隐藏**自己

### 2. 事件流程图

```mermaid
graph TB
    subgraph "TooltipWrapper事件处理机制"
        A[target元素] --> A1[onMouseOver]
        A --> A2[onMouseOut]

        B[tooltip内容] --> B1[onMouseEnter]
        B --> B2[onMouseLeave]

        A1 --> C1[handleShow]
        A2 --> C2[handleHide]
        B1 --> C3[tooltipMouseEnter]
        B2 --> C4[tooltipMouseLeave]

        C1 --> D1["waitToHide && waitToHide()<br/>(立即隐藏其他tooltip)"]
        C1 --> D2["setTimeout(this.show, delay)<br/>(延时显示自己)"]

        C2 --> D3["waitToHide = this.hide<br/>(注册到全局)"]
        C2 --> D4["setTimeout(this.hide, 300ms)<br/>(延时隐藏自己)"]

        C3 --> D5["clearTimeout(this.timer)<br/>(取消延时隐藏)"]

        C4 --> D6["this.hide()<br/>(立即隐藏自己)"]
    end

    style D1 fill:#ffcccc
    style D2 fill:#ccffcc
    style D3 fill:#ffffcc
    style D4 fill:#ffcccc
    style D5 fill:#ccffcc
    style D6 fill:#ffcccc
```

### 3. 关键代码结构

```typescript
// 全局单例管理
let waitToHide: Function | null = null;

export class TooltipWrapper extends React.Component {
  // target元素事件：延时处理
  handleShow() {
    waitToHide && waitToHide(); // 立即隐藏其他tooltip
    this.timer = setTimeout(this.show, mouseEnterDelay);
  }

  handleHide() {
    waitToHide = this.hide.bind(this); // 注册到全局
    this.timer = setTimeout(this.hide, mouseLeaveDelay); // 延时隐藏
  }

  // tooltip内容事件：立即处理
  tooltipMouseEnter() {
    clearTimeout(this.timer); // 取消延时隐藏（enterable保护）
  }

  tooltipMouseLeave() {
    this.hide(); // 立即隐藏
  }
}
```

### 4. 嵌套冲突的根本原因

当我们在外层tooltip内容中添加内层TooltipWrapper时：

```typescript
// 外层tooltip内容
<div className="Select-overflow-wrapper">
  <TooltipWrapper> {/* 内层TooltipWrapper */}
    <div className="Select-value">项目1</div>
  </TooltipWrapper>
</div>
```

**冲突发生**：
1. 用户hover到内层TooltipWrapper的target元素
2. 触发内层的`handleShow()`
3. 执行`waitToHide && waitToHide()` - **立即隐藏外层tooltip**
4. 外层tooltip消失，破坏了用户体验

**关键问题**：全局`waitToHide`机制无法区分父子关系，会强制隐藏所有其他tooltip。

## 解决方案：双重管理机制

### 核心思路

使用两个独立的全局变量分别管理正常tooltip和嵌套tooltip：

```typescript
let waitToHide: Function | null = null;        // 管理正常tooltip
let waitToHideNested: Function | null = null;  // 管理嵌套tooltip
```

### 实现方案

#### 1. 添加nested属性

```typescript
export interface TooltipWrapperProps {
  // 现有props...
  nested?: boolean; // 标识这是一个嵌套tooltip
}
```

#### 2. 修改核心逻辑

```typescript
handleShow() {
  this.timer && clearTimeout(this.timer);

  if (this.props.nested) {
    // 嵌套tooltip：只隐藏其他嵌套tooltip
    waitToHideNested && waitToHideNested();
  } else {
    // 正常tooltip：隐藏所有其他tooltip
    waitToHide && waitToHide();
    waitToHideNested && waitToHideNested();
  }

  this.timer = setTimeout(this.show, delay);
}

handleHide() {
  clearTimeout(this.timer);

  if (this.props.nested) {
    waitToHideNested = this.hide.bind(this);
  } else {
    waitToHide = this.hide.bind(this);
  }

  this.timer = setTimeout(this.hide, hideDelay);
}
```

#### 3. 使用方式

```typescript
// 在Select组件的折叠项中
<TooltipWrapper
  nested={true}  // 标识为嵌套tooltip
  tooltip={{content: tooltipContent}}
>
  <div className="Select-value">...</div>
</TooltipWrapper>
```

### 方案优势

1. **简单直接**：只需要添加一个`nested`属性和一个全局变量
2. **向后兼容**：现有TooltipWrapper使用方式完全不变
3. **逻辑清晰**：嵌套tooltip有独立的管理机制
4. **解决核心问题**：嵌套tooltip不会破坏外层tooltip的enterable保护

### 交互场景分析

| 场景 | 当前tooltip | 目标tooltip | 预期行为 | 实现逻辑 |
|------|------------|------------|----------|----------|
| 1 | 正常 | 正常 | 旧tooltip隐藏，新tooltip显示 | `waitToHide()` |
| 2 | 正常 | 嵌套 | 正常tooltip保持，嵌套tooltip显示 | 嵌套tooltip不调用`waitToHide` |
| 3 | 嵌套 | 正常 | 嵌套tooltip隐藏，正常tooltip显示 | 正常tooltip调用`waitToHideNested()` |
| 4 | 嵌套 | 嵌套 | 旧嵌套隐藏，新嵌套显示 | `waitToHideNested()` |

## 结论

**双重管理机制**是解决TooltipWrapper嵌套冲突的最优方案：

1. **根本解决问题**：让嵌套tooltip不破坏外层tooltip的enterable保护机制
2. **实现简单**：只需约30行代码修改，风险可控
3. **向后兼容**：现有代码完全不受影响
4. **满足需求**：完美解决Select组件折叠项tooltip的问题

这个方案在简单性、可靠性和实用性之间找到了最佳平衡点。
