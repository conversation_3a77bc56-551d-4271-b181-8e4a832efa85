import {
  <PERSON><PERSON><PERSON><PERSON>po<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>er,
  eachTree,
  Layout,
  NotFound,
  SearchBox,
  Spinner,
  ToastComponent,
} from 'amis';
import 'amis-ui/lib/locale/en-US';
import React from 'react';
import {withRouter} from 'react-router';
// @ts-ignore
import ChangedLog from './ChangedLog';
import BestPractices from './BestPractices';
import DerivedFuncs from './DerivedFuncs';
import Course from './Course';
import Doc from './Doc';
import DocSearch from './DocSearch';
import HomeDoc from './HomeDoc';
import UnderPrinciple from './UnderPrinciple';
import SystemDesign from './SystemDesign';

// @ts-ignore
import {
  HashRouter,
  Link,
  NavLink,
  Redirect,
  Route,
  Switch,
} from 'react-router-dom';
import Components from './Components';
import CSSDocs from './CssDocs';
import Example from './ExampleV3';
declare const _hmt: any;

const ContextPath = '/amis';
const isPrd = true;

// if (process.env.NODE_ENV === 'production') {
//   ContextPath = '/amis';
// }

// const isPrd = process.env.NODE_ENV === 'production';

export function getContextPath() {
  return ContextPath;
}

// 此处不可随意注释云舍的主题，除非所有的主题都默认改好，否则不能删除
const themes = [
  {
    label: 'AntD',
    ns: 'antd-',
    value: 'antd',
  },
  {
    label: '云舍',
    ns: 'cxd-',
    value: 'cxd',
  },
];

const docVersions = [
  {
    label: '1.x',
    value: '',
    url: 'http://moka.dmz.dev.caijj.net/dataseeddesigndocui/#/amis/zh-CN/components/page',
  },
];

function getPath(path: string) {
  return path
    ? path[0] === '/'
      ? ContextPath + path
      : `${ContextPath}/${path}`
    : '';
}

class BackTop extends React.PureComponent {
  state = {
    show: false,
  };

  componentDidMount() {
    document.addEventListener('scroll', this.handleScroll);
  }

  componentWillUnmount() {
    document.removeEventListener('scroll', this.handleScroll);
  }

  handleScroll = (e: any) => {
    this.setState({
      show: e.target.scrollingElement?.scrollTop > 350,
    });
  };

  render() {
    return (
      <div
        className={`Backtop ${this.state.show ? 'visible' : ''}`}
        onClick={() => scrollTo({top: 0})}
      >
        <i className="fa fa-rocket"></i>
      </div>
    );
  }
}
// @ts-ignore
@withRouter // @ts-ignore
export class App extends React.PureComponent<{
  location: Location;
}> {
  state = {
    viewMode: localStorage.getItem('amis-viewMode') || 'pc',
    offScreen: false,
    folded: false,
    headerVisible: true,
    themes: themes,
    theme:
      themes.find(item => item?.value === localStorage.getItem('amis-theme')) ||
      themes[0],
    locale: localStorage.getItem('amis-locale')
      ? localStorage.getItem('amis-locale')?.replace('zh-cn', 'zh-CN')
      : '',
    navigations: [],
    filter: '', // 导航过滤，方便找组件
  };

  constructor(props: any) {
    super(props);
    this.setNavigations = this.setNavigations.bind(this);
    this.setNavigationFilter = this.setNavigationFilter.bind(this);
    document.querySelector('body')?.classList.add(this.state.theme.value);
  }

  componentDidUpdate(preProps: any, preState: any) {
    const props = this.props;

    if (preState.theme.value !== this.state.theme.value) {
      [].slice
        .call(document.querySelectorAll('link[title]'))
        .forEach((item: HTMLLinkElement) => {
          const theme = item.getAttribute('title');
          item.disabled = theme !== this.state.theme.value;
        });
      const body = document.body;
      body.classList.remove(preState.theme.value);
      body.classList.add(this.state.theme.value);
    }

    if (
      (isPrd ? props.location.hash : props.location.pathname) !==
      (isPrd ? preProps.location.hash : preProps.location.pathname)
    ) {
      this.setState(
        {
          offScreen: false,
        },
        () => window.scrollTo(0, 0),
      );

      _hmt &&
        _hmt.push([
          '_trackPageview',
          isPrd ? props.location.hash : props.location.pathname,
        ]);
    }
  }

  setNavigations(items: any, resetFilter = true) {
    this.setState({
      navigations: items,
      filter: resetFilter ? '' : this.state.filter,
    });
  }

  renderHeader(docPage = true) {
    const location = this.props.location;
    const theme = this.state.theme;

    if (isPrd ? location.hash === '#/edit' : location.pathname === '/edit') {
      return (
        <div id="headerBar" className="box-shadow bg-dark">
          <div className={`${theme.ns}Layout-brand`}>amis 可视化编辑器</div>
        </div>
      );
    }

    return (
      <>
        <div
          className={`${theme.ns}Layout-brandBar ${
            docPage ? 'DocLayout-brandBar' : ''
          }`}
        >
          <div
            onClick={() => this.setState({offScreen: !this.state.offScreen})}
            className={`${theme.ns}Layout-offScreen-btn ${
              docPage ? 'DocLayout-offScreen-btn' : ''
            } pull-right visible-xs`}
          >
            <i className="bui-icon iconfont icon-collapse"></i>
          </div>

          {docPage ? (
            <div
              className={`${theme.ns}Layout-brand  ${
                docPage ? 'DocLayout-brand' : ''
              }`}
            >
              <Link to={`${ContextPath}/home`}>
                <div className="logo"></div>
              </Link>
            </div>
          ) : (
            <div className={`${theme.ns}Layout-brand text-ellipsis`}>
              <i className="fa fa-paw" />
              <span className="hidden-folded m-l-sm">常见示例</span>
            </div>
          )}
        </div>

        <div
          className={`${theme.ns}Layout-headerBar shadow-sm bg-white ${
            docPage ? 'DocLayout-headerBar pc:inline-flex' : 'pc:flex'
          } items-center`}
        >
          {docPage ? null : (
            <Button
              onClick={() => this.setState({folded: !this.state.folded})}
              type="button"
              level="link"
              className="navbar-btn"
            >
              <i
                className={`fa fa-${
                  this.state.folded ? 'indent' : 'dedent'
                } fa-fw`}
              ></i>
            </Button>
          )}

          <ul className={`HeaderLinks`}>
            <NavLink
              to={`${ContextPath}/zh-CN/course`}
              activeClassName="is-active"
            >
              教程
            </NavLink>
            <NavLink
              to={`${ContextPath}/zh-CN/components`}
              activeClassName="is-active"
            >
              组件
            </NavLink>
            <NavLink
              to={`${ContextPath}/zh-CN/style`}
              activeClassName="is-active"
            >
              样式
            </NavLink>
            <NavLink
              to={`${ContextPath}/zh-CN/changedlog`}
              activeClassName="is-active"
            >
              版本
            </NavLink>
            <NavLink to={`${ContextPath}/zh-CN/bestpractices`} activeClassName="is-active">
              最佳实践
            </NavLink>
            <NavLink to={`${ContextPath}/zh-CN/underprinciple`} activeClassName="is-active">
              底层原理
            </NavLink>
            {/* <NavLink to={`${ContextPath}/zh-CN/systemDesign`} activeClassName="is-active">
              系统设计
            </NavLink> */}
            <NavLink to={`${ContextPath}/zh-CN/derivedfuncs`} activeClassName="is-active">
              辅助函数
            </NavLink>
            <NavLink to={`${ContextPath}/examples`} activeClassName="is-active">
              页面示例
            </NavLink>
            <a target="_blank" href={`https://appcenter.bigmodel.cn/console/appcenter_v2/chat?share_code=HtzqC4yXULrReUthjAZa-`}>
              智能问答
            </a>
          </ul>
          <div id="Header-toolbar"></div>
        </div>

        {docPage ? (
          <>
            <div
              className={`${theme.ns}Layout-searchBar ${
                docPage ? 'DocLayout-searchBar' : ''
              } hidden-xs hidden-sm`}
            >
              <DocSearch theme={theme} />
            </div>
            <a
              className="gh-icon"
              href="http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design"
              target="_blank"
            >
              <i className="fa fa-github" />
            </a>
          </>
        ) : null}
      </>
    );
  }

  setNavigationFilter(value: string) {
    this.setState({
      filter: value,
    });
  }

  renderNavigation() {
    return (
      <div className="Doc-navigation">
        {location.hash.indexOf('components') > 0 && (
          <SearchBox
            className="m-b m-r-md"
            placeholder="输入组件名称"
            value={this.state.filter}
            onSearch={this.setNavigationFilter}
            onChange={this.setNavigationFilter}
            clearable={true}
            mini={false}
            history={{enable: true}}
          />
        )}

        {this.renderAsideNav()}
      </div>
    );
  }

  renderAsideNav() {
    const filterReg = new RegExp(
      this.state.filter.replace(/[|\\{}()[\]^$+*?.]/g, '\\$&'),
      'i',
    );

    return (
      <AsideNav
        navigations={this.state.navigations.map((item: any) => ({
          ...item,
          children: item.children
            ? item.children
                .filter((item: any) => {
                  if (item.label) {
                    return filterReg.exec(item.label);
                  }
                  return true;
                })
                .map((item: any) => ({
                  ...item,
                  className: 'is-top',
                }))
            : [],
        }))}
        renderLink={({
          link,
          active,
          toggleExpand,
          classnames: cx,
          depth,
        }: any) => {
          let children = [];

          if (link.children && link.children.length) {
            children.push(
              <span
                key="expand-toggle"
                className={cx('AsideNav-itemArrow')}
                onClick={e => toggleExpand(link, e)}
              ></span>,
            );
          }

          link.badge &&
            children.push(
              <b
                key="badge"
                className={cx(
                  `AsideNav-itemBadge`,
                  link.badgeClassName || 'bg-info',
                )}
              >
                {link.badge}
              </b>,
            );

          if (link.icon) {
            children.push(
              <i key="icon" className={cx(`AsideNav-itemIcon`, link.icon)} />,
            );
          } else if (this.state.folded && depth === 1) {
            children.push(
              <i
                key="icon"
                className={cx(
                  `AsideNav-itemIcon`,
                  link.children ? 'fa fa-folder' : 'fa fa-info',
                )}
              />,
            );
          }

          children.push(
            <span className={cx('AsideNav-itemLabel')} key="label">
              {link.label}
            </span>,
          );

          return link.path ? (
            /^https?\:/.test(link.path) ? (
              <a target="_blank" href={link.path} rel="noopener">
                {children}
              </a>
            ) : (
              <Link
                to={
                  getPath(link.path) ||
                  (link.children && getPath(link.children[0].path))
                }
              >
                {children}
              </Link>
            )
          ) : (
            <a onClick={link.children ? () => toggleExpand(link) : undefined}>
              {children}
            </a>
          );
        }}
        isActive={(link: any) => isActive(link, location)}
      />
    );
  }

  renderExamples() {
    const theme = this.state.theme;

    return (
      <Layout
        theme={theme.value}
        offScreen={this.state.offScreen}
        folded={this.state.folded}
        header={this.renderHeader(false)}
        aside={this.renderAsideNav()}
      >
        <ToastComponent theme={theme.value} locale={this.state.locale} />
        <AlertComponent theme={theme.value} locale={this.state.locale} />

        {/* {React.cloneElement(this.props.children as any, {
          key: theme.value,
          ...(this.props.children as any).props,
          setNavigations: this.setNavigations,
          theme: theme.value,
          classPrefix: theme.ns,
          viewMode: this.state.viewMode,
          locale: this.state.locale,
          offScreen: this.state.offScreen,
          ContextPath
        })} */}
        {this.renderContent()}
      </Layout>
    );
  }

  renderContent() {
    const locale = 'zh-CN'; // 暂时不支持切换，因为目前只有中文文档
    const {theme} = this.state;

    return (
      <React.Suspense
        fallback={<Spinner overlay spinnerClassName="m-t-lg" size="lg" />}
      >
        <Switch>
          <Redirect
            from={`/`}
            to={`${ContextPath}/${locale}/components/page`}
            exact
          />
          <Redirect
            from={`${ContextPath}/`}
            to={`${ContextPath}/${locale}/components/page`}
            exact
          />

          {/* docs */}
          <Redirect
            from={`${ContextPath}/docs`}
            to={`${ContextPath}/${locale}/docs/index`}
            exact
          />
          <Redirect
            from={`${ContextPath}/docs/index`}
            to={`${ContextPath}/${locale}/docs/start/getting-started`}
            exact
          />
          <Redirect
            from={`${ContextPath}/docs/*`}
            to={`${ContextPath}/${locale}/docs/*`}
          />
          <Redirect
            from={`${ContextPath}/${locale}/docs`}
            to={`${ContextPath}/${locale}/docs/index`}
            exact
          />

          {/* components */}
          <Redirect
            from={`${ContextPath}/components`}
            to={`${ContextPath}/${locale}/components/page`}
            exact
          />
          <Redirect
            from={`${ContextPath}/components/page`}
            to={`${ContextPath}/${locale}/components/page`}
            exact
          />
          <Redirect
            from={`${ContextPath}/components/*`}
            to={`${ContextPath}/${locale}/components/*`}
            exact
          />
          <Redirect
            from={`${ContextPath}/${locale}/components`}
            to={`${ContextPath}/${locale}/components/page`}
            exact
          />

          {/* expamles */}
          <Redirect
            from={`${ContextPath}/examples`}
            to={`${ContextPath}/examples/introduce`}
            exact
          />
          <Redirect
            from={`${ContextPath}/${locale}/bestpractices`}
            to={`${ContextPath}/${locale}/bestpractices/index`}
            exact
          />
          <Redirect
            from={`${ContextPath}/${locale}/derivedfuncs`}
            to={`${ContextPath}/${locale}/derivedfuncs/index`}
            exact
          />
          <Redirect
            from={`${ContextPath}/${locale}/underprinciple`}
            to={`${ContextPath}/${locale}/underprinciple/index`}
            exact
          />
          <Redirect
            from={`${ContextPath}/${locale}/systemDesign`}
            to={`${ContextPath}/${locale}/systemDesign/index`}
            exact
          />
          <Redirect
            from={`${ContextPath}/${locale}/style`}
            to={`${ContextPath}/${locale}/style/index`}
            exact
          />
          <Redirect
            from={`${ContextPath}/${locale}/changedlog`}
            to={`${ContextPath}/${locale}/changedlog/index`}
            exact
          />
          <Redirect
            from={`${ContextPath}/${locale}/course`}
            to={`${ContextPath}/${locale}/course/index`}
            exact
          />
          <Route
            path={`${ContextPath}/${locale}/course`}
            render={(props: any) => (
              <Course
                {...{
                  setNavigations: this.setNavigations,
                  theme: theme.value,
                  classPrefix: theme.ns,
                  viewMode: this.state.viewMode,
                  locale: this.state.locale,
                  offScreen: this.state.offScreen,
                  ContextPath,
                  showCode: false,
                }}
                {...props}
              />
            )}
          />
          <Route
            path={`${ContextPath}/${locale}/systemDesign`}
            render={(props: any) => (
              <SystemDesign
                {...{
                  setNavigations: this.setNavigations,
                  theme: theme.value,
                  classPrefix: theme.ns,
                  viewMode: this.state.viewMode,
                  locale: this.state.locale,
                  offScreen: this.state.offScreen,
                  ContextPath,
                  showCode: false,
                }}
                {...props}
              />
            )}
          />
          <Route
            path={`${ContextPath}/${locale}/changedlog`}
            render={(props: any) => (
              <ChangedLog
                {...{
                  setNavigations: this.setNavigations,
                  theme: theme.value,
                  classPrefix: theme.ns,
                  viewMode: this.state.viewMode,
                  locale: this.state.locale,
                  offScreen: this.state.offScreen,
                  ContextPath,
                  showCode: false,
                }}
                {...props}
              />
            )}
          />
          <Route
            path={`${ContextPath}/${locale}/bestpractices`}
            render={(props: any) => (
              <BestPractices
                {...{
                  setNavigations: this.setNavigations,
                  theme: theme.value,
                  classPrefix: theme.ns,
                  viewMode: this.state.viewMode,
                  locale: this.state.locale,
                  offScreen: this.state.offScreen,
                  ContextPath,
                  showCode: false,
                }}
                {...props}
              />
            )}
          />
          <Route
            path={`${ContextPath}/${locale}/derivedfuncs`}
            render={(props: any) => (
              <DerivedFuncs
                {...{
                  setNavigations: this.setNavigations,
                  theme: theme.value,
                  classPrefix: theme.ns,
                  viewMode: this.state.viewMode,
                  locale: this.state.locale,
                  offScreen: this.state.offScreen,
                  ContextPath,
                  showCode: false,
                }}
                {...props}
              />
            )}
          />
          <Route
            path={`${ContextPath}/${locale}/underprinciple`}
            render={(props: any) => (
              <UnderPrinciple
                {...{
                  setNavigations: this.setNavigations,
                  theme: theme.value,
                  classPrefix: theme.ns,
                  viewMode: this.state.viewMode,
                  locale: this.state.locale,
                  offScreen: this.state.offScreen,
                  ContextPath,
                  showCode: false,
                }}
                {...props}
              />
            )}
          />
          <Route
            path={`${ContextPath}/home`}
            render={(props: any) => (
              <HomeDoc
                {...{
                  setNavigations: this.setNavigations,
                  theme: theme.value,
                  classPrefix: theme.ns,
                  viewMode: this.state.viewMode,
                  locale: this.state.locale,
                  offScreen: this.state.offScreen,
                  ContextPath,
                  showCode: false,
                }}
                {...props}
              />
            )}
          />
          <Route
            path={`${ContextPath}/${locale}/docs`}
            render={(props: any) => (
              <Doc
                {...{
                  setNavigations: this.setNavigations,
                  theme: theme.value,
                  classPrefix: theme.ns,
                  viewMode: this.state.viewMode,
                  locale: this.state.locale,
                  offScreen: this.state.offScreen,
                  ContextPath,
                  showCode: false,
                }}
                {...props}
              />
            )}
          />
          <Route
            path={`${ContextPath}/${locale}/components`}
            render={(props: any) => (
              <Components
                {...{
                  setNavigations: this.setNavigations,
                  theme: theme.value,
                  classPrefix: theme.ns,
                  viewMode: this.state.viewMode,
                  locale: this.state.locale,
                  offScreen: this.state.offScreen,
                  ContextPath,
                  showCode: false,
                }}
                {...props}
              />
            )}
          />
          <Route
            path={`${ContextPath}/examples`}
            render={(props: any) => (
              <Example
                {...{
                  setNavigations: this.setNavigations,
                  theme: theme.value,
                  classPrefix: theme.ns,
                  viewMode: this.state.viewMode,
                  locale: this.state.locale,
                  offScreen: this.state.offScreen,
                  ContextPath,
                  showCode: false,
                }}
                {...props}
              />
            )}
          />
          <Route
            path={`${ContextPath}/${locale}/style`}
            render={(props: any) => (
              <CSSDocs
                {...{
                  setNavigations: this.setNavigations,
                  theme: theme.value,
                  classPrefix: theme.ns,
                  viewMode: this.state.viewMode,
                  locale: this.state.locale,
                  offScreen: this.state.offScreen,
                  ContextPath,
                  showCode: false,
                }}
                {...props}
              />
            )}
          />
          <Route
            render={() => (
              <div className="Doc-content">
                <NotFound />
              </div>
            )}
          />
        </Switch>
      </React.Suspense>
    );
  }

  render() {
    const theme = this.state.theme;
    const location = this.props.location;

    if (/examples\/app/.test(isPrd ? location.hash : location.pathname)) {
      return (
        <>
          <ToastComponent theme={theme.value} locale={this.state.locale} />
          <AlertComponent theme={theme.value} locale={this.state.locale} />
          {this.renderContent()}
        </>
      );
    } else if (/examples/.test(isPrd ? location.hash : location.pathname)) {
      return this.renderExamples();
    }

    const cls = !!(/examples/.test(isPrd ? window.location.hash : location.pathname))? 'example-doc' : '';

    return (
      <Layout
        className={`:DocLayout ${cls}`}
        theme={theme.value}
        boxed={true}
        offScreen={this.state.offScreen}
        header={this.state.headerVisible ? this.renderHeader() : null}
        headerClassName={':DocLayout-header'}
      >
        <ToastComponent theme={theme.value} locale={this.state.locale} />
        <AlertComponent theme={theme.value} locale={this.state.locale} />

        <div className="Doc">
          {location.pathname?.includes('/home') ? null : (
            <div className="Doc-nav hidden-xs hidden-sm">
              {this.renderNavigation()}
            </div>
          )}

          <Drawer
            size="xs"
            className="Doc-navDrawer"
            overlay
            closeOnOutside
            onHide={() => this.setState({offScreen: false})}
            show={this.state.offScreen}
            position="left"
          >
            <ul className={`HeaderLinks`}>
              <NavLink
                to={`${ContextPath}/zh-CN/course`}
                activeClassName="is-active"
              >
                教程
              </NavLink>
              <NavLink
                to={`${ContextPath}/zh-CN/docs`}
                activeClassName="is-active"
              >
                文档
              </NavLink>

              <NavLink
                to={`${ContextPath}/zh-CN/components`}
                activeClassName="is-active"
              >
                组件
              </NavLink>
              <NavLink
                to={`${ContextPath}/zh-CN/style`}
                activeClassName="is-active"
              >
                样式
              </NavLink>
              <NavLink
                to={`${ContextPath}/zh-CN/changedlog`}
                activeClassName="is-active"
              >
                版本
              </NavLink>
              <NavLink
                to={`${ContextPath}/zh-CN/systemDesign`}
                activeClassName="is-active"
              >
                系统设计
              </NavLink>
            </ul>
          </Drawer>

          <BackTop />

          {/* {React.cloneElement(this.props.children as any, {
            key: theme.value,
            ...(this.props.children as any).props,
            setNavigations: this.setNavigations,
            theme: theme.value,
            classPrefix: theme.ns,
            viewMode: this.state.viewMode,
            locale: this.state.locale,
            offScreen: this.state.offScreen,
            ContextPath
          })} */}
          {this.renderContent()}
        </div>
      </Layout>
    );
  }
}

function isActive(link: any, location: any) {
  return !!(link.path && `#${getPath(link.path)}` === `${location.hash}`);
}

export function navigations2route(
  navigations: any,
  additionalProperties?: any,
) {
  let routes: any = [];

  navigations.forEach((root: any) => {
    root.children &&
      eachTree(root.children, (item: any) => {
        if (item.path && item.component) {
          routes.push(
            additionalProperties ? (
              <Route
                key={routes.length + 1}
                path={
                  item.path[0] === '/'
                    ? ContextPath + item.path
                    : `${ContextPath}/${item.path}`
                }
                render={(props: any) => (
                  <item.component {...additionalProperties} {...props} />
                )}
              />
            ) : (
              <Route
                key={routes.length + 1}
                path={
                  item.path[0] === '/'
                    ? ContextPath + item.path
                    : `${ContextPath}/${item.path}`
                }
                component={item.component}
              />
            ),
          );
        }
      });
  });

  return routes;
}

export default function entry() {
  // PathPrefix = pathPrefix || DocPathPrefix;

  return (
    <HashRouter>
      <Switch>
        <Route component={App}></Route>
        <Route component={NotFound} />
      </Switch>
    </HashRouter>
  );
}
