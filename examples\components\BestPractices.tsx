import React from 'react';
import { Switch } from 'react-router-dom';
import { navigations2route } from './App';
import makeMarkdownRenderer from './MdRenderer';
import utils from './utils';
function wrapDoc(doc: any) {
  return {
    default: makeMarkdownRenderer(doc),
  };
}

const bestpracticesDocs = [
  {
    label: '简介',
    children: [
      {
        label: '关于最佳实践',
        path: '/zh-CN/bestpractices/index',
        component: React.lazy(() =>
          import('../../docs/zh-CN/bestpractices/index.md').then(wrapDoc),
        ),
      },
    ],
  },
  {
    label: '功能',
    children: [
      {
        label: 'Select 改变 弹窗提醒',
        path: '/zh-CN/bestpractices/practice1',
        component: React.lazy(() =>
          import('../../docs/zh-CN/bestpractices/practice1/index.md').then(
            wrapDoc,
          ),
        ),
      },
      {
        label: '根据接口展示不同的弹框',
        path: '/zh-CN/bestpractices/practice2',
        component: React.lazy(() =>
          import('../../docs/zh-CN/bestpractices/practice2/index.md').then(
            wrapDoc,
          ),
        ),
      },
      {
        label: 'Dialog 弹窗列表选择',
        path: '/zh-CN/bestpractices/practice6',
        component: React.lazy(() =>
          import('../../docs/zh-CN/bestpractices/practice6/index.md').then(
            wrapDoc,
          ),
        ),
      },
      {
        label: '相互排除的下拉列表',
        path: '/zh-CN/bestpractices/practice7',
        component: React.lazy(() =>
          import('../../docs/zh-CN/bestpractices/practice7/index.md').then(
            wrapDoc,
          ),
        ),
      },
      {
        label: '批量操作按钮依赖条件禁用',
        path: '/zh-CN/bestpractices/practice8',
        component: React.lazy(() =>
          import('../../docs/zh-CN/bestpractices/practice8/index.md').then(
            wrapDoc,
          ),
        ),
      },
      {
        label: '根据数据渲染动态 Schema',
        path: '/zh-CN/bestpractices/schemaApi',
        component: React.lazy(() =>
          import('../../docs/zh-CN/bestpractices/schemaApi/index.md').then(
            wrapDoc,
          ),
        ),
      },
      {
        label: 'Formula 反显表单项的计算值',
        path: '/zh-CN/bestpractices/practice9',
        component: React.lazy(() =>
          import('../../docs/zh-CN/bestpractices/practice9/index.md').then(
            wrapDoc,
          ),
        ),
      },
      {
        label: '自定义组件定义及触发动作',
        path: '/zh-CN/bestpractices/practice10',
        component: React.lazy(() =>
          import('../../docs/zh-CN/bestpractices/practice10/index.md').then(
            wrapDoc,
          ),
        ),
      },
      {
        label: 'Combo 与其他表单项联动',
        path: '/zh-CN/bestpractices/practice11',
        component: React.lazy(() =>
          import('../../docs/zh-CN/bestpractices/practice11/index.md').then(
            wrapDoc,
          ),
        ),
      },
      {
        label: '轮训日志场景',
        path: '/zh-CN/bestpractices/logDemo',
        component: React.lazy(() =>
          import('../../docs/zh-CN/bestpractices/logDemo/index.md').then(
            wrapDoc,
          ),
        ),
      },
      {
        label: '事件对应的响应动作防抖',
        path: '/zh-CN/bestpractices/practice12',
        component: React.lazy(() =>
          import('../../docs/zh-CN/bestpractices/practice12/index.md').then(
            wrapDoc,
          ),
        ),
      },
      {
        label: 'InputTable 动态列',
        path: '/zh-CN/bestpractices/inputTableDynamicColumns',
        component: React.lazy(() =>
          import(
            '../../docs/zh-CN/bestpractices/inputTableDynamicColumns/index.md'
          ).then(wrapDoc),
        ),
      },
      {
        label: '自定义组件触发及监听事件',
        path: '/zh-CN/bestpractices/practice14',
        component: React.lazy(() =>
          import('../../docs/zh-CN/bestpractices/practice14/index.md').then(
            wrapDoc,
          ),
        ),
      },
      {
        label: '页面展示依赖多个 Api 数据',
        path: '/zh-CN/bestpractices/multiInitApi',
        component: React.lazy(() =>
          import('../../docs/zh-CN/bestpractices/multiInitApi/index.md').then(
            wrapDoc,
          ),
        ),
      },
      {
        label: 'InputTable 多列自动填充',
        path: '/zh-CN/bestpractices/practice15',
        component: React.lazy(() =>
          import('../../docs/zh-CN/bestpractices/practice15/index.md').then(
            wrapDoc,
          ),
        ),
      },
      {
        label: 'InputTable 搜索列表项',
        path: '/zh-CN/bestpractices/practice16',
        component: React.lazy(() =>
          import('../../docs/zh-CN/bestpractices/practice16/index.md').then(
            wrapDoc,
          ),
        ),
      },
      {
        label: 'CRUD 服务端设置 filter 默认值',
        path: '/zh-CN/bestpractices/practiceFilterDefaultValue',
        component: React.lazy(() =>
          import(
            '../../docs/zh-CN/bestpractices/practiceSelectDefaultValue/index.md'
          ).then(wrapDoc),
        ),
      },
      {
        label: 'CRUD 批量操作与搜索条件联动',
        path: '/zh-CN/bestpractices/batchButtonAndSearchParams',
        component: React.lazy(() =>
          import(
            '../../docs/zh-CN/bestpractices/batchButtonAndSearchParams/index.md'
          ).then(wrapDoc),
        ),
      },
      {
        label: 'Amis 与 IFrame 之间通信',
        path: '/zh-CN/bestpractices/practiceIframe',
        component: React.lazy(() =>
          import('../../docs/zh-CN/bestpractices/practiceIframe/index.md').then(
            wrapDoc,
          ),
        ),
      },
      // practiceCacheApi
      {
        label: '接口数据缓存',
        path: '/zh-CN/bestpractices/practiceCacheApi',
        component: React.lazy(() =>
          import(
            '../../docs/zh-CN/bestpractices/practiceCacheApi/index.md'
          ).then(wrapDoc),
        ),
      },
      {
        label: '提交表单，表单项包含$符号',
        path: '/zh-CN/bestpractices/practiceSpecialSub',
        component: React.lazy(() =>
          import(
            '../../docs/zh-CN/bestpractices/practiceSpecialSub/index.md'
          ).then(wrapDoc),
        ),
      },
      {
        label: '表格单行轮询获取任务状态',
        path: '/zh-CN/bestpractices/practiceTableIntervalStatus',
        component: React.lazy(() =>
          import(
            '../../docs/zh-CN/bestpractices/practiceTableIntervalStatus/index.md'
          ).then(wrapDoc),
        ),
      },
      {
        label: 'Crud嵌套子表格批量选中',
        path: '/zh-CN/bestpractices/crudSubTableSelection',
        component: React.lazy(() =>
          import(
            '../../docs/zh-CN/bestpractices/crudSubTableSelection/index.md'
          ).then(wrapDoc),
        ),
      },
      {
        label: '表单未保存拦截',
        path: '/zh-CN/bestpractices/formModifiedPrompt',
        component: React.lazy(() =>
          import(
            '../../docs/zh-CN/bestpractices/formModifiedPrompt/index.md'
          ).then(wrapDoc),
        ),
      },
      {
        label: 'Editor自定义按钮JSON格式化',
        path: '/zh-CN/bestpractices/practiceEditorFormat',
        component: React.lazy(() =>
          import(
            '../../docs/zh-CN/bestpractices/practiceEditorFormat/index.md'
          ).then(wrapDoc),
        ),
      },
      {
        label: '数据域中取不到数据的问题汇总',
        path: '/zh-CN/bestpractices/dataDomain',
        component: React.lazy(() =>
          import('../../docs/zh-CN/bestpractices/dataDomain/index.md').then(
            wrapDoc,
          ),
        ),
      },
      {
        label: '虚拟滚动一行展示多个',
        path: '/zh-CN/bestpractices/virtualScroll',
        component: React.lazy(() =>
          import('../../docs/zh-CN/bestpractices/practice17/index.md').then(
            wrapDoc,
          ),
        ),
      },
      {
        label: '定时刷新API',
        path: '/zh-CN/bestpractices/timeOutRefreshApi',
        component: React.lazy(() =>
          import('../../docs/zh-CN/bestpractices/practice18/index.md').then(
            wrapDoc,
          ),
        ),
      },
      {
        label: '动态渲染step组件并联动crud',
        path: '/zh-CN/bestpractices/stepAndCrud',
        component: React.lazy(() =>
          import('../../docs/zh-CN/bestpractices/stepAndCrud/index.md').then(
            wrapDoc,
          ),
        ),
      }
    ],
  },
];

export default class BestPractices extends React.PureComponent<any> {
  componentDidMount() {
    this.props.setNavigations(bestpracticesDocs);
    utils.scrollToAnchor();
  }

  componentDidUpdate() {
    this.props.setNavigations(bestpracticesDocs, false);
  }

  render() {
    return (
      <Switch>
        {navigations2route(bestpracticesDocs, {
          theme: this.props.theme,
          classPrefix: this.props.classPrefix,
          locale: this.props.locale,
          viewMode: this.props.viewMode,
          offScreen: this.props.offScreen,
        })}
      </Switch>
    );
  }
}
