import React from 'react';
import {Switch} from 'react-router-dom';
import {navigations2route} from './App';
import makeMarkdownRenderer from './MdRenderer';
import utils from './utils';
function wrapDoc(doc: any) {
  return {
    default: makeMarkdownRenderer(doc)
  };
}

const changedlogDocs = [
  {
    label: '日志',
    children: [
      {
        label: '更新日志',
        path: '/zh-CN/changedlog/index',
        component: React.lazy(() =>
          import('../../docs/zh-CN/start/changelog.md').then(wrapDoc)
        ),
      }
    ]
  }
]

export default class ChangedLog extends React.PureComponent<any> {
  componentDidMount() {
    this.props.setNavigations(changedlogDocs);
    utils.scrollToAnchor();
  }

  componentDidUpdate() {
    this.props.setNavigations(changedlogDocs, false);
  }

  render() {
    return (
      <Switch>
        {navigations2route(changedlogDocs, {
          theme: this.props.theme,
          classPrefix: this.props.classPrefix,
          locale: this.props.locale,
          viewMode: this.props.viewMode,
          offScreen: this.props.offScreen
        })}
      </Switch>
    );
  }
}

