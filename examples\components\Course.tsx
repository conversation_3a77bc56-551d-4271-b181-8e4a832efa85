import React from 'react';
import {Switch} from 'react-router-dom';
import {navigations2route} from './App';
import makeMarkdownRenderer from './MdRenderer';
import utils from './utils';
function wrapDoc(doc: any) {
  return {
    default: makeMarkdownRenderer(doc),
  };
}

const courseDocs = [
  {
    label: '简介',
    children: [
      {
        label: '关于O端统一组件库',
        path: '/zh-CN/course/index',
        component: React.lazy(() =>
          import('../../docs/zh-CN/course/introduction.md').then(wrapDoc),
        ),
      },
      {
        label: '与Amis框架的关系',
        path: '/zh-CN/course/with-amis',
        component: React.lazy(() =>
          import('../../docs/zh-CN/course/with-amis.md').then(wrapDoc),
        ),
      },
    ],
  },
  {
    label: '上手',
    children: [
      {
        label: '安装与使用',
        path: '/zh-CN/course/install',
        component: React.lazy(() =>
          import('../../docs/zh-CN/course/install.md').then(wrapDoc),
        ),
      },
      {
        label: '工具库',
        path: '/zh-CN/course/utils',
        component: React.lazy(() =>
          import('../../docs/zh-CN/course/utils.md').then(wrapDoc),
        ),
      },
      {
        label: '脚手架',
        path: '/zh-CN/course/cli',
        component: React.lazy(() =>
          import('../../docs/zh-CN/course/cli.md').then(wrapDoc),
        ),
      },
      {
        label: 'FAQ',
        path: '/zh-CN/course/faq',
        component: React.lazy(() =>
          import('../../docs/zh-CN/start/faq.md').then(wrapDoc),
        ),
      },
    ],
  },
  {
    label: '扩展',
    children: [
      {
        label: '自定义组件库',
        path: '/zh-CN/course/dataseedui',
        component: React.lazy(() =>
          import('../../docs/zh-CN/course/dataseedui.md').then(wrapDoc),
        ),
      },
    ],
  },
  {
    label: '💡  概念',
    children: [
      {
        label: '配置与组件',
        path: '/zh-CN/course/concepts/schema',
        component: React.lazy(() =>
          import('../../docs/zh-CN/concepts/schema.md').then(wrapDoc)
        )
      },
      {
        label: '数据域与数据链',
        path: '/zh-CN/course/concepts/datascope-and-datachain',
        component: React.lazy(() =>
          import('../../docs/zh-CN/concepts/datascope-and-datachain.md').then(
            wrapDoc
          )
        )
      },
      {
        label: '模板',
        path: '/zh-CN/course/concepts/template',
        component: React.lazy(() =>
          import('../../docs/zh-CN/concepts/template.md').then(wrapDoc)
        )
      },
      {
        label: '数据映射',
        path: '/zh-CN/course/concepts/data-mapping',
        component: React.lazy(() =>
          import('../../docs/zh-CN/concepts/data-mapping.md').then(wrapDoc)
        )
      },
      {
        label: '表达式',
        path: '/zh-CN/course/concepts/expression',
        component: React.lazy(() =>
          import('../../docs/zh-CN/concepts/expression.md').then(wrapDoc)
        )
      },
      {
        label: '联动',
        path: '/zh-CN/course/concepts/linkage',
        component: React.lazy(() =>
          import('../../docs/zh-CN/concepts/linkage.md').then(wrapDoc)
        )
      },
      {
        label: '事件动作',
        path: '/zh-CN/course/concepts/event-action',
        component: React.lazy(() =>
          import('../../docs/zh-CN/concepts/event-action.md').then(wrapDoc)
        )
      },
      {
        label: '行为',
        path: '/zh-CN/course/concepts/action',
        component: React.lazy(() =>
          import('../../docs/zh-CN/concepts/action.md').then(wrapDoc)
        )
      },
      // {
      //   label: '样式',
      //   path: '/zh-CN/course/concepts/style',
      //   component: React.lazy(() =>
      //     import('../../docs/zh-CN/concepts/style.md').then(wrapDoc)
      //   )
      // }
    ]
  },

  {
    label: '类型',
    children: [
      {
        label: 'SchemaNode',
        path: '/zh-CN/course/types/schemanode',
        component: React.lazy(() =>
          import('../../docs/zh-CN/types/schemanode.md').then(wrapDoc)
        )
      },
      {
        label: 'ClassName',
        path: '/zh-CN/course/types/classname',
        component: React.lazy(() =>
          import('../../docs/zh-CN/types/classname.md').then(wrapDoc)
        )
      },
      {
        label: 'API',
        path: '/zh-CN/course/types/api',
        component: React.lazy(() =>
          import('../../docs/zh-CN/types/api.md').then(wrapDoc)
        )
      },
      {
        label: 'Definitions',
        path: '/zh-CN/course/types/definitions',
        component: React.lazy(() =>
          import('../../docs/zh-CN/types/definitions.md').then(wrapDoc)
        )
      }
    ]
  },

  {
    label: '💎  高级',
    children: [
      {
        label: '工作原理',
        path: '/zh-CN/course/extend/internal',
        component: React.lazy(() =>
          import('../../docs/zh-CN/extend/internal.md').then(wrapDoc)
        )
      },
      {
        label: '自定义组件 - SDK',
        path: '/zh-CN/course/extend/custom-sdk',
        component: React.lazy(() =>
          import('../../docs/zh-CN/extend/custom-sdk.md').then(wrapDoc)
        )
      },
      {
        label: '自定义组件 - React',
        path: '/zh-CN/course/extend/custom-react',
        component: React.lazy(() =>
          import('../../docs/zh-CN/extend/custom-react.md').then(wrapDoc)
        )
      },
      {
        label: '将 amis 当成 UI 库用',
        path: '/zh-CN/course/extend/ui-library',
        component: React.lazy(() =>
          import('../../docs/zh-CN/extend/ui-library.md').then(wrapDoc)
        )
      },
      {
        label: '扩展现有组件',
        path: '/zh-CN/course/extend/addon',
        component: React.lazy(() =>
          import('../../docs/zh-CN/extend/addon.md').then(wrapDoc)
        )
      },
      {
        label: '页面交互行为跟踪',
        path: '/zh-CN/course/extend/tracker',
        component: React.lazy(() =>
          import('../../docs/zh-CN/extend/tracker.md').then(wrapDoc)
        )
      },
      {
        label: '调试工具',
        path: '/zh-CN/course/extend/debug',
        component: React.lazy(() =>
          import('../../docs/zh-CN/extend/debug.md').then(wrapDoc)
        )
      },
      {
        label: '移动端定制',
        path: '/zh-CN/course/extend/mobile',
        component: React.lazy(() =>
          import('../../docs/zh-CN/extend/mobile.md').then(wrapDoc)
        )
      },
      {
        label: '多语言',
        path: '/zh-CN/course/extend/i18n',
        component: React.lazy(() =>
          import('../../docs/zh-CN/extend/i18n.md').then(wrapDoc)
        )
      },
      {
        label: '可视化编辑器',
        path: '/zh-CN/course/extend/editor',
        component: React.lazy(() =>
          import('../../docs/zh-CN/extend/editor.md').then(wrapDoc)
        )
      },
      {
        label: '如何贡献代码',
        path: '/zh-CN/course/extend/contribute',
        component: React.lazy(() =>
          import('../../docs/zh-CN/extend/contribute.md').then(wrapDoc)
        )
      }
    ]
  }
];

export default class Course extends React.PureComponent<any> {
  componentDidMount() {
    this.props.setNavigations(courseDocs);
    utils.scrollToAnchor();
  }

  componentDidUpdate() {
    this.props.setNavigations(courseDocs, false);
  }

  render() {
    return (
      <Switch>
        {navigations2route(courseDocs, {
          theme: this.props.theme,
          classPrefix: this.props.classPrefix,
          locale: this.props.locale,
          viewMode: this.props.viewMode,
          offScreen: this.props.offScreen,
        })}
      </Switch>
    );
  }
}
