import { generateBasicFormV2 ,generateCommonPage,generateNoMarginInputTable,generateStyle} from 'amis-utils';

export default generateCommonPage({
  "type": "page",

  "data": {
    "table-0": [
      {
        "input": 111,
        "select": "s1",
        "text": "text"
      },
      {
        "input": 333,
        "select": "s3",
        "text": "text-3"
      },
    ],
    "table-1": [
      {
        "input": 222,
        "select": "s2",
        "text": "text-1"
      },
    ],
    "batchFormData": [
      {
        "department": "部门1",
        "platform": "iOS",
        "css": "css-1",
        "table": [
          {
            "input": 111,
            "select": "s1",
            "text": "text"
          },
          {}
        ]
      },
      {
        "department": "部门2",
        "platform": "Android",
        "table": [
          {
            "input": 222,
            "select": "s2",
            "text": "text-1"
          },
          {}
        ]
      },
      {
        "department": "部门3",
        "platform": "Window Phone"
      },
    ],
  },
  "body": generateBasicFormV2({
    "title": "",
    "static": true,
    "actions": [],
    "body": {
      "type": "each",
      "name": "batchFormData",
      "items": [
       {
          "type": "group",
          "body": [
            {
              "type": "static",
              "name": "department",
              "label": "归属部门",
            },
            {
              "type": "static",
              "name": "platform",
              "label": "Platform",
            },
            {
              "type": "static",
              "name": "css",
              "label": "CSS",
            },
          ]
        },
        {
          "type": "group",
          "withoutMarginBottom": true,
          "body": [
            generateNoMarginInputTable({
              "type": "input-table",
              "name": "table-${index}",
              "label": "Table",
              "canAccessSuperData": true,
              "strictMode": false,
              "source": [
                {
                  "input": 111,
                  "select": "s1",
                  "text": "text"
                },
                {}
              ],
              "columns": [
                {
                  "label": "数字输入",
                  "name": "input",
                  "type": "input-text",
                  "placeholder": "请输入数字",
                  "required": false,
                  "validations": {
                    "isNumeric": true
                  },
                  "validationErrors": {
                    "isNumeric": "请输入数字"
                  }
                },
                {
                  "label": "选项",
                  "name": "select",
                  "type": "select",
                  "required": false,
                  "options": [
                    "s1",
                    "s2",
                    "s3"
                  ]
                },
                {
                  "label": "普通文本",
                  "name": "text"
                }
              ]
            }),
          ]
        },
        generateStyle({
          "type": "divider",
          "visibleOn": '${index < batchFormData.length - 1}',
        },{
          "className":{
            "spacing":{
              "padding":{
                "top":"sm"
              }
            }
          },
        })
      ]

    },
  })
});
