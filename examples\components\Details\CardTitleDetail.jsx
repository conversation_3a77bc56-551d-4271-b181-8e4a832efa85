import { generateHeaderV2,  generateCustomPaddingTabs, getFormTabDetailSchema, getWithoutMarginsCRUDSchemaV2,generateStyle, generateCommonPage, } from "amis-utils";

const SCHEMA = [
  {
    "type": "group",
    "body": [
      {
        "type": "static",
        "name": "text1",
        "label": "归属部门",
        "columnRatio": 4
      },
      {
        "type": "static-mapping",
        "name": "text2",
        "label": "文本2",
        "columnRatio": 4,
        "map": {
          "0": "<span class='label label- info'>一</span>",
        "1": "<span class='label label- success'>二</span>",
      "2": "这是一个映射",
      "3": "<span class='label label - warning'>四</span>",
      "4": "<span class='label label - primary'>五</span>",
      "*": "<span class='label label -default'>-</span>"
  }
      },
{
  "type": "static-date",
    "name": "text3",
      "label": "文本3",
        "columnRatio": 4
}
    ]
  },
{
  "type": "group",
    "body": [
      {
        "type": "static",
        "name": "text4",
        "label": "负责人",
        "columnRatio": 4
      },
      {
        "type": "static",
        "name": "text5",
        "label": "文本5",
        "columnRatio": 4
      },
      {
        "type": "static-datetime",
        "name": "text6",
        "label": "文本6",
        "columnRatio": 4
      }
    ]
},
{
  "type": "group",
    "body": [
      {
        "type": "static",
        "name": "text7",
        "label": "营销中心",
        "columnRatio": 4
      },
      {
        "type": "static",
        "name": "text8",
        "label": "文本8",
        "columnRatio": 4
      },
      {
        "type": "static",
        "name": "text9",
        "label": "文本9",
        "columnRatio": 4
      }
    ]
}
];

export default
generateCommonPage(
  {
  "type": "page",
  "id": "page-header",
  "data": {
    "department": "department",
    "platform": "platform",
    "css": "css",
    "browser": "browser",
    "selected": "a",
    "department1": "department1",
    "platform1": "platform1",
    "css1": "css1",
    "browser1": "browser1",
    "selected1": "b",
    "remark": "备注",
    "remark1": "备注",
    "collection": true,
  },
  "body": [
     generateHeaderV2({
      "title": "页面大标题名称大标题名称Demo",
      "subtitle": "这是小标题",
      "actions": [
        {
          "type": "button",
          "label": false,
          "icon": "fa fa-star-o",
          "level": "link",
          "linkWithoutPadding": true,
          "visibleOn": "${collection}",
          "onEvent": {
            "click": {
              "actions": [
                {
                  "actionType": "setValue",
                  "componentId": "page-header",
                  "args": {
                    "value": {
                      "collection": false
                    }
                  }
                }
              ]
            }
          }
        },
        {
          "type": "button",
          "label": false,
          "icon": "fa fa-star",
          "linkWithoutPadding": true,
          "level": "link",
          "visibleOn": "${!collection}",
          "onEvent": {
            "click": {
              "actions": [{
                "actionType": "setValue",
                "componentId": "page-header",
                "args": {
                  "value": {
                    "collection": true
                  }
                }
              }]
            }
          }
        }
      ]
     }),
     generateStyle({
      "type": "card",
      "header": {
        "title": "FLINK_TABLE_NAME",
        "subTitle": "副标题",
        "description": "这是一段描述",
        "avatarClassName": "pull-left thumb-md avatar b-3x m-r",
        "avatar": "https://suda.cdn.bcebos.com/images/amis/ai-fake-face.jpg"
      },
      body: [
        {
          type: "group",
          body: [
            generateCustomPaddingTabs({
              "name": "tabs",
              "noPadding": true,
              "noPaddingContent": {
                "bottom": true
              },
              "tabs": [
                {
                  "title": "基础表单1",
                  "icon": "fa fa-home",
                  "body": getFormTabDetailSchema({
                    "initApi": "",
                    "wrapWithPanel": false,
                    "labelWidth": 60,
                    "body": SCHEMA
                  })
                },
                {
                  "title": "列表",
                  "icon": "fab fa-apple",
                  "body": getWithoutMarginsCRUDSchemaV2({
                    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?waitSeconds=1",
                    "columns": [
                      {
                        "name": "id",
                        "label": "ID"
                      },
                      {
                        "name": "engine",
                        "label": "Rendering engine"
                      },
                      {
                        "name": "browser",
                        "label": "Browser"
                      },
                      {
                        "name": "platform",
                        "label": "Platform(s)"
                      },
                      {
                        "name": "engine",
                        "label": "Engine"
                      },
                      {
                        "name": "version",
                        "label": "Engine Version"
                      },
                      {
                        "name": "grade",
                        "label": "CSS grade"
                      }
                    ]
                  }, true, true)
                },
                {
                  "title": "基础表单2",
                  "icon": "fas fa-bug",
                  "body": getFormTabDetailSchema({
                    "wrapWithPanel": false,
                    "labelWidth": 60,
                    "body": SCHEMA
                  })
                }
              ]
            })
          ]
        }
      ]
    },{
      "className":{
        "border":{
          "style":"none",
        }
      }
    })
  ]
});
