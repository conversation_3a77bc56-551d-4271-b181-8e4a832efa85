import { getCardListSchema,generateCommonPage, getPortalCard } from "amis-utils";

export default
generateCommonPage({
  "type": "page",

  "data": {
    "items": [
      {
        "title": "标题",
        "description": "描述",
        "avatarText": "1",
        "body": "这里是内容"
      },
      {
        "title": "数据集",
        "description": `<span>相比上周: <span class="text-success">+100</span></span>`,
        "avatarText": "2",
        "body": "这里是内容",
      },
    ]
  },
  "body":  getCardListSchema({
    "source": "$items",
    "columnsCount": 1,
    "footerToolbar": [],
    "card": getPortalCard({
      "href": "https://github.com/baidu/amis",
      "header": {
        "title": "${title}",
        "subTitle": "${subTitle}",
        "description": "${description|raw}",
        "avatarText": "${avatarText}",
        "avatar": "${avatar}",
      },
      "body": "${body}"
    })
  })
})

