import {generateCommonPage,  getNumberCard } from "amis-utils";

export default
generateCommonPage({
  "type": "page",
  "data": {
    "items": [
      {
        "num": 123452717281,
        "title": "标题1"
      },
      {
        "num": 13123,
        "title": "标题2",
      },
      {
        "num": 123456787626,
        "title": "标题3",
      },
    ]
  },
  "body": {
    "type": "cards",
    "source": "${items}",
    "columnsCount": 3,
    "card": getNumberCard({
      "header": {
        "title": "${title}",
      },
      "body": [
        {
          "name": "num",
          "type": "tpl",
          "tpl": "${num|number}",
        }
      ]
    })
  }
})

