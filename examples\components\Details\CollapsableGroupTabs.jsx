import {
  generateCollapseGroupOfTabs,
  generateHeaderTitle,
  getTabsDetailsSchema,
  generateCustomPaddingTabs,
  generateCommonPage,
  getTabDetailCollapsableGroupPanelSchema,
  getNoPaddingTabs,
  getFormTabDetailPanelSchema,
  getTabDetailGroupPanelSchemaV3,
  generateStyle,

  generateFloat
} from 'amis-utils';

const paddingBtmNoneObj = {
  "className": {
    "spacing":{
      "padding": {
        "bottom": "none"
      }
    }

  }
}
export default generateCommonPage({
  "type": "page",
  "data": {
    "text1": "aaaa",
    "text2": 18,
    "text3": "7年级",
    "text4": "<EMAIL>",
    "text5": "12345678",
    "text6": "上海市浦东新区",
    "text7": "测试",
    "text8": "text8",
    "text9": "text9",
    "second1": "<EMAIL>",
    "second2": "12345678",
    "second3": "上海市浦东新区",
    "second4": "上海市浦东新区",
    "third4": "sdsd",
  },
  "body": generateStyle(
    getTabsDetailsSchema({
      "tabs": getNoPaddingTabs([
        {
          "title": "可折叠分组",
          "tab": generateStyle(getFormTabDetailPanelSchema({
            "static": true,
            "body": [
              generateCollapseGroupOfTabs({
                "type": "collapse-group",
                "activeKey": [
                  "1"
                ],
                "body": getTabDetailCollapsableGroupPanelSchema([
                  {
                    "key": "1",
                    "header": [
                      generateHeaderTitle({
                        type: 'tpl',
                        tpl: '第一步，基础信息'
                      }),
                      {
                        type: 'remark',
                        content: '这是一段提示',
                      },
                    generateStyle({
                        type: "tpl",
                        tpl: "这是小标题",
                        "textStyle": {
                          "color": "gray-500",
                          "size": "md",
                        }
                      },{
                        "className": {
                          "spacing":{
                            "margin": {
                              "left": "sm"
                            }
                          }

                        }
                      },),
                      generateFloat({
                        type: "button",
                        label: "实验列表",
                        level: "link"
                      }, {
                        className: {
                          float: 'right'
                        }
                      }),

                    ],
                    body: [
                      {
                        type: 'group',
                        body: [
                          {
                            type: 'input-text',
                            name: 'text1',
                            label: '姓名',
                          },
                          {
                            type: 'input-text',
                            name: 'text2',
                            label: '年龄',
                          },
                          {
                            type: 'input-text',
                            name: 'text3',
                            label: '班级',
                            required: true,
                          },
                        ]
                      },
                      {
                        type: "group",
                        body: [
                          {
                            type: 'input-text',
                            name: 'text4',
                            label: '邮箱',
                          },
                          {
                            type: 'input-text',
                            name: 'text5',
                            label: '电话',
                          },
                          {
                            type: 'input-text',
                            name: 'text6',
                            label: '地址',
                            columnRatio: 4,
                          }
                        ]
                      },
                      {
                        type: "group",
                        body: [
                          {
                            type: 'input-text',
                            name: 'text7',
                            label: '其它',
                            columnRatio: 4,
                          }
                        ]
                      }
                    ]
                  },
                  {
                    "key": "2",
                    "header": generateHeaderTitle({
                      type: 'tpl',
                      tpl: '第二步，复杂信息'
                    }),
                    collapsed: false,
                    body: [
                      {
                        type: "group",
                        body: [
                          {
                            type: 'input-text',
                            name: 'second1',
                            label: '邮箱',
                          },
                          {
                            type: 'input-text',
                            name: 'second2',
                            label: '电话',
                          },
                          {
                            type: 'input-text',
                            name: 'second3',
                            label: '地址',
                            columnRatio: 4,
                          }
                        ]
                      },
                      {
                        type: 'group',
                        body: [
                          {
                            type: 'input-text',
                            name: 'second3',
                            label: '姓名',
                            placehold: "请输入"
                          }
                        ]
                      }
                    ]
                  },
                  {
                    "key": "3",
                    "header": generateHeaderTitle({
                      type: 'tpl',
                      tpl: '第三步，策略信息'
                    }),
                    collapsed: false,
                    body: [
                      {
                        type: "group",
                        body: [
                          generateCustomPaddingTabs({
                            "noPaddingContent": {
                              "bottom": true
                            },
                            "noPadding": true,
                            type: 'tabs',
                            tabs: [
                              {
                                title: "策略分支1",
                                tab: [
                                  {
                                    type: "group",
                                    body: [
                                      {
                                        type: "input-text",
                                        name: "third1",
                                        label: "sjksajkd"
                                      },
                                      {
                                        type: "input-text",
                                        name: "third2",
                                        label: "sjksajkd"
                                      },
                                      {
                                        type: "input-text",
                                        name: "third3",
                                        label: "sjksajkd"
                                      }
                                    ]
                                  },
                                  {
                                    type: "group",
                                    body: [
                                      {
                                        type: "input-text",
                                        name: "third5",
                                        label: "sjksajkd"
                                      },
                                      {
                                        type: "select",
                                        name: "third4",
                                        label: "sjksajkd",
                                        options: [
                                          {
                                            label: 'a',
                                            value: 'a'
                                          },
                                          {
                                            label: 'b',
                                            value: 'b'
                                          }
                                        ]
                                      },
                                      {
                                        type: "input-text",
                                        name: "third6",
                                        label: "sjksajkd"
                                      }
                                    ]
                                  }
                                ]
                              },
                              {
                                title: "策略分支2",
                                tab: [
                                  {
                                    type: "group",
                                    body: [
                                      {
                                        type: "input-text",
                                        name: "third7",
                                        label: "sjksajkd"
                                      },
                                      {
                                        type: "input-text",
                                        name: "third8",
                                        label: "sjksajkd"
                                      },
                                      {
                                        type: "input-text",
                                        name: "third9",
                                        label: "sjksajkd"
                                      }
                                    ]
                                  },
                                  {
                                    type: "group",
                                    body: [
                                      {
                                        type: "input-text",
                                        name: "third10",
                                        label: "sjksajkd"
                                      },
                                      {
                                        type: "select",
                                        name: "third4",
                                        label: "sjksajkd",
                                        options: [
                                          {
                                            label: 'a',
                                            value: 'a'
                                          },
                                          {
                                            label: 'b',
                                            value: 'b'
                                          }
                                        ]
                                      },
                                      {
                                        type: "input-text",
                                        name: "third12",
                                        label: "sjksajkd"
                                      }
                                    ]
                                  }
                                ]
                              },
                              {
                                title: "策略分支3",
                                tab: [
                                  {
                                    type: "group",
                                    body: [
                                      {
                                        type: "input-text",
                                        name: "third13",
                                        label: "sjksajkd"
                                      },
                                      {
                                        type: "input-text",
                                        name: "third14",
                                        label: "sjksajkd"
                                      },
                                      {
                                        type: "input-text",
                                        name: "third15",
                                        label: "sjksajkd"
                                      }
                                    ]
                                  },
                                  {
                                    type: "group",
                                    body: [
                                      {
                                        type: "input-text",
                                        name: "third16",
                                        label: "sjksajkd"
                                      },
                                      {
                                        type: "select",
                                        name: "third4",
                                        label: "sjksajkd",
                                        options: [
                                          {
                                            label: 'a',
                                            value: 'a'
                                          },
                                          {
                                            label: 'b',
                                            value: 'b'
                                          }
                                        ]
                                      },
                                      {
                                        type: "input-text",
                                        name: "third18",
                                        label: "sjksajkd"
                                      }
                                    ]
                                  }
                                ]
                              }
                            ]
                          })

                        ]
                      }
                    ]
                  }
                ])
              }, true),
            ],
          }), paddingBtmNoneObj
          )
        },
        {
          "title": "无标题分组+可折叠分组",
          "tab": getFormTabDetailPanelSchema({
            "static": true,
            "labelWidth": 60,
            "body": [
              ...getTabDetailGroupPanelSchemaV3([
                {
                  type: 'panel',
                  body: [
                    {
                      type: 'group',
                      body: [
                        {
                          "name": "text1",
                          "type": "static",
                          "label": "静态展示",
                          "quickEdit": {
                            "type": "input-text"
                          }
                        },
                        {
                          type: 'static',
                          name: 'text2',
                          label: '年龄',
                        },
                        {
                          type: 'static',
                          name: 'text3',
                          label: '班级',
                          required: true,
                        },
                      ]
                    },
                    {
                      type: "group",
                      body: [
                        {
                          type: 'static',
                          name: 'text4',
                          label: '邮箱',
                        },
                        {
                          type: 'static',
                          name: 'text5',
                          label: '电话',
                        },
                        {
                          type: 'static',
                          name: 'text6',
                          label: '地址',
                          columnRatio: 4,
                        }
                      ]
                    },
                    {
                      type: "group",
                      body: [
                        {
                          type: 'static',
                          name: 'text7',
                          label: '其它',
                          columnRatio: 4,
                        }
                      ]
                    }
                  ]
                }
              ], true, true),
              generateCollapseGroupOfTabs({
                "type": "collapse-group",
                "activeKey": [
                  "1"
                ],
                "body": getTabDetailCollapsableGroupPanelSchema([
                  {
                    "key": "2",
                    "header": generateHeaderTitle({
                      type: 'tpl',
                      tpl: '复杂信息'
                    }),
                    collapsed: false,
                    body: [
                      {
                        type: "group",
                        body: [
                          {
                            type: 'input-text',
                            name: 'second1',
                            label: '邮箱',
                          },
                          {
                            type: 'input-text',
                            name: 'second2',
                            label: '电话',
                          },
                          {
                            type: 'input-text',
                            name: 'second3',
                            label: '地址',
                            columnRatio: 4,
                          }
                        ]
                      },
                      {
                        type: 'group',
                        body: [
                          {
                            type: 'input-text',
                            name: 'second3',
                            label: '姓名',
                            placehold: "请输入"
                          }
                        ]
                      }
                    ]
                  },
                  {
                    "key": "3",
                    "header": generateHeaderTitle({
                      type: 'tpl',
                      tpl: '策略信息'
                    }),
                    collapsed: false,
                    body: [
                      {
                        type: "group",
                        body: [
                          {
                            type: 'tabs',
                            tabs: [
                              {
                                title: "策略分支1",
                                tab: [
                                  {
                                    type: "group",
                                    body: [
                                      {
                                        type: "input-text",
                                        name: "third1",
                                        label: "sjksajkd"
                                      },
                                      {
                                        type: "input-text",
                                        name: "third2",
                                        label: "sjksajkd"
                                      },
                                      {
                                        type: "input-text",
                                        name: "third3",
                                        label: "sjksajkd"
                                      }
                                    ]
                                  },
                                  {
                                    type: "group",
                                    body: [
                                      {
                                        type: "input-text",
                                        name: "third5",
                                        label: "sjksajkd"
                                      },
                                      {
                                        type: "select",
                                        name: "third4",
                                        label: "sjksajkd",
                                        options: [
                                          {
                                            label: 'a',
                                            value: 'a'
                                          },
                                          {
                                            label: 'b',
                                            value: 'b'
                                          }
                                        ]
                                      },
                                      {
                                        type: "input-text",
                                        name: "third6",
                                        label: "sjksajkd"
                                      }
                                    ]
                                  }
                                ]
                              },
                              {
                                title: "策略分支2",
                                tab: [
                                  {
                                    type: "group",
                                    body: [
                                      {
                                        type: "input-text",
                                        name: "third7",
                                        label: "sjksajkd"
                                      },
                                      {
                                        type: "input-text",
                                        name: "third8",
                                        label: "sjksajkd"
                                      },
                                      {
                                        type: "input-text",
                                        name: "third9",
                                        label: "sjksajkd"
                                      }
                                    ]
                                  },
                                  {
                                    type: "group",
                                    body: [
                                      {
                                        type: "input-text",
                                        name: "third10",
                                        label: "sjksajkd"
                                      },
                                      {
                                        type: "select",
                                        name: "third4",
                                        label: "sjksajkd",
                                        options: [
                                          {
                                            label: 'a',
                                            value: 'a'
                                          },
                                          {
                                            label: 'b',
                                            value: 'b'
                                          }
                                        ]
                                      },
                                      {
                                        type: "input-text",
                                        name: "third12",
                                        label: "sjksajkd"
                                      }
                                    ]
                                  }
                                ]
                              },
                              {
                                title: "策略分支3",
                                tab: [
                                  {
                                    type: "group",
                                    body: [
                                      {
                                        type: "input-text",
                                        name: "third13",
                                        label: "sjksajkd"
                                      },
                                      {
                                        type: "input-text",
                                        name: "third14",
                                        label: "sjksajkd"
                                      },
                                      {
                                        type: "input-text",
                                        name: "third15",
                                        label: "sjksajkd"
                                      }
                                    ]
                                  },
                                  {
                                    type: "group",
                                    body: [
                                      {
                                        type: "input-text",
                                        name: "third16",
                                        label: "sjksajkd"
                                      },
                                      {
                                        type: "select",
                                        name: "third4",
                                        label: "sjksajkd",
                                        options: [
                                          {
                                            label: 'a',
                                            value: 'a'
                                          },
                                          {
                                            label: 'b',
                                            value: 'b'
                                          }
                                        ]
                                      },
                                      {
                                        type: "input-text",
                                        name: "third18",
                                        label: "sjksajkd"
                                      }
                                    ]
                                  }
                                ]
                              }
                            ]
                          },
                        ]
                      }
                    ]
                  }
                ])
              }, true),
            ],
          })
        }
      ])
    }), paddingBtmNoneObj
  )
})



