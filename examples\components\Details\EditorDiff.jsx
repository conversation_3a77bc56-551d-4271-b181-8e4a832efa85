import {generateCommonPage,generateBackground,generateSpacing} from 'amis-utils';
export default
generateCommonPage({
  "type": "page",
  "data": {
    "value1": "hello word",
    "value2": "hello friend",
  },
  "body": generateBackground(
    {
    "type": "wrapper",
    "body": [
      {
        "type": 'form',
        "title": '',
        "actions": [],
        "mode": "horizontal",
        "static": true,
        "wrapWithPanel": false,
        "body": generateSpacing({
          "type": 'group',
          "body": [
            {
              "type": "select",
              "label": "对比版本",
              "name": 'baselineVersion',
              "value": "v2",
              "labelWidth": 60,
              "options": [
                {
                  "label": '版本1 (李冰清，2022-11-08 11:27:38)',
                  "value": "v1"
                },
                {
                  "label": '版本2 (申铭，2024-04-28 05:00:06)',
                  "value": "v2"
                }
              ]
            },

            {
              "type": "select",
              "label": "基准版本",
              "name": 'diffVersion',
              "value": 'v1',
              "labelWidth": 60,
              "options": [
                {
                  "label": '版本1 (李冰清，2022-11-08 11:27:38)',
                  "value": "v1"
                },
                {
                  "label": '版本2 (申铭，2024-04-28 05:00:06)',
                  "value": "V2"
                }
              ]
            }
          ]
        },{
          "className":{
            "padding":{
              "bottom":'md'
            }
          }
        }),
      },
      {
        "type": "diff-editor",
        "name": "value2",
        "label": "",
        "disabled":true,
        "diffValue": "${value1}"
      }
    ],
  },{
    "className":{
        "color":"white"
    }
  })
})
