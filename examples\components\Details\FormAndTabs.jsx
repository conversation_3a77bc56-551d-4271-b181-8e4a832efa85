import { generateBasicFormV2, generateSpacing, generateStaticEach, generateCommonPage, generateStyle, getWithoutMarginsCRUDSchemaV2, generateCustomPaddingTabs, getFormTabDetailSchema, generateTypography,generateStaticEachBtn } from "amis-utils";

const SCHEMA = [
  {
    "type": "group",
    "body": [
      {
        "type": "static",
        "name": "text1",
        "label": "归属部门",
        "columnRatio": 4
      },
      {
        "type": "static-mapping",
        "name": "text2",
        "label": "文本2",
        "columnRatio": 4,
        "map": {
          "0": "<span class='label label-info'>一</span>",
          "1": "<span class='label label-success'>二</span>",
          "2": "这是一个映射",
          "3": "<span class='label label-warning'>四</span>",
          "4": "<span class='label label-primary'>五</span>",
          "*": "<span class='label label-default'>-</span>"
        }
      },
      {
        "type": "static-date",
        "name": "text3",
        "label": "文本3",
        "columnRatio": 4
      }
    ]
  },
  {
    "type": "group",
    "body": [
      {
        "type": "static",
        "name": "text4",
        "label": "负责人",
        "columnRatio": 4
      },
      {
        "type": "static",
        "name": "text5",
        "label": "文本5",
        "columnRatio": 4
      },
      {
        "type": "static-datetime",
        "name": "text6",
        "label": "文本6",
        "columnRatio": 4
      }
    ]
  },
  {
    "type": "group",
    "body": [
      {
        "type": "static",
        "name": "text7",
        "label": "营销中心",
        "columnRatio": 4
      },
      {
        "type": "static",
        "name": "text8",
        "label": "文本8",
        "columnRatio": 4
      },
      {
        "type": "static",
        "name": "text9",
        "label": "文本9",
        "columnRatio": 4
      }
    ]
  }
];
const API_METHOD_COLOR = {
  POST: "warning",
  GET: 'success',
  DELETE: 'error',
  PUT: 'active'
}

export default generateCommonPage({
  "type": "page",
  "data": {
    "text1": "营销中心",
    "text2": 2,
    "text3": 1593327764,
    "text4": "负责人",
    "text5": "text5",
    "text6": 1593327764,
    "text7": "创建人",
    "text8": "text8",
    "department": "营销中心",
    "platform": 2,
    "css": 1593327764,
    "browser": "负责人",
    "selected": "text5",
    "browser2": 1593327764,
    "remark": "创建人",
    "tip": "text8",
    "text9": "text9",
    "isEditPage": false,
    "groupfix": 100,
    "input-sub-form": [
      {
        "title": "123",
        "b": "456"
      }
    ],
    "comment": '这是一段很长的描述信息，这是一段很长的描述信息这是一段很长的描述信息这是一段很长的描述信息这是一段很长的描述信息这是一段很长的描述信息这是一段很长的描述信息这是一段很长的描述信息这是一段很长的描述信息这是一段很长的描述信息这是一段很长的描述信息这是一段很长的描述信息这是一段很长的描述信息这是一段很长的描述信息这是一段很长的描述信息',

    "apiUrl": "/api/mock2/saveForm",
    "apiMethod": 'POST',
    "comboData": [],
    "myFile": [
      {
        "fileType": "pdf",
        "url": "https://static02.sit.yxmarketing01.com/materialcenter/a938e8c4-6f9c-4d39-8bcf-5aceee6d9751.pdf",
      }, {
        "fileType": "jpg",
        "url": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
      }],
    API_METHOD_COLOR,
  },
  "body": [
    generateBasicFormV2({
      title: '',
      static: true,
      id: 'static_form_id',
      api: '/api/mock2/saveForm?waitSeconds=2',
      labelWidth: 75,
      "actions": [
        {
          "type": "button",
          "label": "取消"
        },
        {
          "type": "button",
          "level": "primary",
          "label": "确认",
        },
      ],
      body: [
        {
          type: "group",
          body: [
            generateTypography({
              type: 'select',
              name: 'department',
              label: '归属部门',
              "revealStaicValue": "*********",
              "revealStaicReg": '/.*/',
            }, {
              "className": {
                "weight": "bold",
              }
            }),
            {
              type: 'input-text',
              name: 'platform',
              label: 'Platform',
              placeholder: "请输入",
            },
            {
              type: 'input-text',
              name: 'css',
              label: 'CSS',
              required: true,
              placeholder: "请输入",
            },
          ]
        },
        {
          type: "group",
          body: [
            {
              type: 'input-text',
              name: 'browser',
              label: 'Browser',
              placeholder: "请输入",
            },
            {
              type: 'select',
              name: 'selected',
              label: '用户选择',
              placeholder: "请选择",
              options: [
                {
                  label: 'a',
                  value: 'a'
                },
                {
                  label: 'b',
                  value: 'b'
                }
              ]
            },
            {
              type: 'input-text',
              name: 'browser2',
              label: 'Browser',
              placeholder: "请输入",
            },
          ]
        },
        {
          "type": "group",
          "body": [
            {
              "type": "input-sub-form",
              "name": "input-sub-form",
              "label": "子表单配置",
              "multiple": true,
              "btnLabel": "设置${title}",
              "addableOn": "${isEditPage}",
              "removableOn": "${isEditPage}",
              "form": {
                "title": "配置子表单",
                "actions": [],
                "body": [
                  {
                    "name": "title",
                    "label": "标题",
                    "required": true,
                    "type": "input-text"
                  },
                  {
                    "name": "b",
                    "label": "其他",
                    "type": "input-text"
                  }
                ]
              }
            },
            {
              "type": "group",
              "body": [
                {
                  "type": "flex",
                  "justify": "flex-start",
                  "alignItems": "center",
                  "label": "邮箱集合",
                  "items": [
                    generateSpacing(
                      {
                        "type": "static",
                        "label": "付款金额",
                        "name": "staticText",
                        "value": "近1000元",
                      },
                      {
                        "className": {
                          "margin": {
                            "bottom": "xs",
                          }
                        }
                      }),
                    generateStyle(
                      {
                        "type": "icon",
                        "icon": "fa-refresh",
                        "onEvent": {
                          "click": {
                            "actions": [
                              {
                                "actionType": "setValue",
                                "componentId": "static_form_id",
                                "args": {
                                  "value": {
                                    "staticText": "2000元"
                                  }
                                }
                              }
                            ]
                          }
                        }
                      }, {
                      "className": {
                        "spacing": {
                          "margin": {
                            "left": "xs"
                          },
                        },

                        "typography": {
                          "size": "xs",
                          "color": "secondary"
                        }
                      }
                    }),
                  ]
                },

              ]
            },

            {
              "type": "static-mapping",
              "name": "neverText",
              "label": "空映射",
              "columnRatio": 4,
              "map": {
                "0": "<span class='label label-info'>一</span>",
                "1": "<span class='label label-success'>二</span>"
              }
            },
          ],
        },
        {
          "type": "group",
          "label": "描述信息",
          "body": [
            {
              "type": "typography",
              "text": "${comment}",
              "style": {
                "lineHeight": "32px"
              },
              "ellipsis": {
                "rows": '2rem'
              },
              "columnRatio": 4,
            }
          ]
        },
        {
          type: "group",
          body: [
            generateStaticEach({
              "type": "static-each",
              "name": "myFile",
              "label": "查看素材",

              "items": [
                generateStyle({
                  "type": "image",
                  "src": "${url}",
                  "enlargeAble": true,
                  "visibleOn": '${fileType === "jpg"}',
                },{
                  "className":{
                    "spacing":{
                      "margin":{
                        'right':"xs"
                      }
                    }
                  }
                }),
                generateStyle({
                  "type": "pdf",
                  "src": "${url}",
                  "enlargeAble": true,
                  "visibleOn": '${fileType === "pdf"}',
                },{
                  "className":{
                    "spacing":{
                      "margin":{
                        'right':"xs"
                      }
                    }
                  }
                }),
              ]
            }),
          ]
        },
        {
          type: "group",
          body: [
            generateStaticEachBtn({
              "type": "static-each",
              "name": "myFile",
              "label": "按钮查看",
              "items": [
                generateStyle({
                  "type": "image",
                  "src": "${url}",
                  "enlargeAble": true,
                  "visibleOn": '${fileType === "jpg"}',
                  "enlargeTrigger": {
                    "label": "点击查看",
                  }
                },{
                  "className":{
                    "spacing":{
                      "margin":{
                        'right':"xs"
                      }
                    }
                  }
                }),
              ]
            })

          ]
        },
        {
          type: "group",
          body: [
            {
              type: 'textarea',
              name: 'remark',
              label: '备注',
              showCounter: true,
              maxLength: 30,
              placeholder: "请输入",
              trimContents: true
            },
          ]
        },
        {
          type: "group",
          body: [
            {
              type: 'group',
              label: '接口',
              body: [
                {
                  type: 'flex',
                  justify: 'start',
                  items: [
                    {
                      "label": '${apiMethod}',
                      "type": "tag",
                      "displayMode": "normal",
                      "color": "${API_METHOD_COLOR[apiMethod]}"
                    },
                    {
                      "type": 'static',
                      "name": 'apiUrl',
                      "label": false,
                    }
                  ]
                },
              ]
            }
          ]
        },
        {
          type: 'group',
          body: [
            {
              type: 'input-rich-text',
              name: 'tip',
              label: '底部提示',
              placeholder: "请输入",
            }
          ]
        },
        {
          type: 'group',
          body: [
            {
              "type": "combo",
              "name": "comboData",
              "label": "策略方法",
              "subFormMode": "horizontal",
              "labelAlign": "right",
              "subFormHorizontal": {
                "labelWidth": 100
              },
              "static": true,
              "multiple": true,
              "minLength": 1,
              "multiLine": true,
              "tabsStyle": "strong",
              "tabsMode": true,
              "addable": true,
              "closable": true,
              "editable": true,
              "tabsLabelTpl": "策略分支${index|plus}",
              "items": [
                {
                  "type": "group",
                  "body": [
                    {
                      "type": "input-text",
                      "name": "third1",
                      "label": "third1",
                      "required": true
                    },
                    {
                      "type": "input-text",
                      "name": "third2",
                      "label": "third2"
                    },
                    {
                      "type": "input-text",
                      "name": "third3",
                      "label": "third3",
                      "required": true
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          type: 'group',
          body: [

            generateCustomPaddingTabs({
              "name": "tabs",
              "noPadding": true,
              "noPaddingContent": {
                "bottom": true
              },
              "tabs": [
                {
                  "title": "基础表单1",
                  "body": getFormTabDetailSchema({
                    "initApi": "",
                    "wrapWithPanel": false,
                    "labelWidth": 60,
                    "body": SCHEMA
                  })
                },
                {
                  "title": "列表",
                  "body": getWithoutMarginsCRUDSchemaV2({
                    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?waitSeconds=1",
                    "columns": [
                      {
                        "name": "id",
                        "label": "ID"
                      },
                      {
                        "name": "engine",
                        "label": "Rendering engine"
                      },
                      {
                        "name": "browser",
                        "label": "Browser"
                      },
                      {
                        "name": "platform",
                        "label": "Platform(s)"
                      },
                      {
                        "name": "engine",
                        "label": "Engine"
                      },
                      {
                        "name": "version",
                        "label": "Engine Version"
                      },
                      {
                        "name": "grade",
                        "label": "CSS grade"
                      }
                    ]
                  }, true, true)
                },
                {
                  "title": "基础表单2",
                  "body": getFormTabDetailSchema({
                    "wrapWithPanel": false,
                    "labelWidth": 60,
                    "body": SCHEMA
                  })
                },
                {
                  "title": "Property 属性表",
                  "body": {
                    "type": "property",
                    "title": "机器配置",
                    "items": [
                      {
                        "label": "cpu",
                        "content": "1 core"
                      },
                      {
                        "label": "memory",
                        "content": "4G"
                      },
                      {
                        "label": "disk",
                        "content": "80G"
                      },
                      {
                        "label": "network",
                        "content": "4M",
                        "span": 2
                      },
                      {
                        "label": "IDC",
                        "content": "beijing"
                      },
                      {
                        "label": "Note",
                        "content": "其它说明",
                        "span": 3
                      }
                    ]
                  }
                }
              ]
            })
          ]
        }
      ]
    })
  ]
})

