import { generateBasicFormV2, generateCommonPage, generateCustomPaddingTabs, getFormTabDetailSchema, getWithoutMarginsCRUDSchemaV2, getFixedHeaderWrapperSchema, generateHeaderV2, generateStyle } from "amis-utils";

const SCHEMA = [
  {
    "type": "group",
    "body": [
      {
        "type": "static",
        "name": "text1",
        "label": "归属部门",
        "columnRatio": 4
      },
      {
        "type": "static-mapping",
        "name": "text2",
        "label": "文本2",
        "columnRatio": 4,
        "map": {
          "0": "<span class='label label-info'>一</span>",
          "1": "<span class='label label-success'>二</span>",
          "2": "这是一个映射",
          "3": "<span class='label label-warning'>四</span>",
          "4": "<span class='label label-primary'>五</span>",
          "*": "<span class='label label-default'>-</span>"
        }
      },
      {
        "type": "static-date",
        "name": "text3",
        "label": "文本3",
        "columnRatio": 4
      }
    ]
  },
  {
    "type": "group",
    "body": [
      {
        "type": "static",
        "name": "text4",
        "label": "负责人",
        "columnRatio": 4
      },
      {
        "type": "static",
        "name": "text5",
        "label": "文本5",
        "columnRatio": 4
      },
      {
        "type": "static-datetime",
        "name": "text6",
        "label": "文本6",
        "columnRatio": 4
      }
    ]
  },
  {
    "type": "group",
    "body": [
      {
        "type": "static",
        "name": "text7",
        "label": "营销中心",
        "columnRatio": 4
      },
      {
        "type": "static",
        "name": "text8",
        "label": "文本8",
        "columnRatio": 4
      },
      {
        "type": "static",
        "name": "text9",
        "label": "文本9",
        "columnRatio": 4
      }
    ]
  }
];

export default generateCommonPage({
  "type": "page",
  "id": "page-header",
  "data": {
    "text1": "营销中心",
    "text2": 2,
    "text3": 1593327764,
    "text4": "负责人",
    "text5": "text5",
    "text6": 1593327764,
    "text7": "创建人",
    "text8": "text8",
    "department": "营销中心",
    "platform": 2,
    "css": 1593327764,
    "browser": "负责人",
    "selected": "text5",
    "browser2": 1593327764,
    "remark": "创建人",
    "tip": "text8",
    "text9": "text9",
    "collection": true,
  },
  "body": getFixedHeaderWrapperSchema([
    generateHeaderV2(
      generateStyle({
        "title": "页面大标题名称大标题名称Demo",
        "subtitle": "这是小标题",
        "actions": [
          {
            "type": "button",
            "label": false,
            "icon": "fa fa-star-o",
            "level": "link",
            "linkWithoutPadding": true,
            "visibleOn": "${collection}",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "actionType": "setValue",
                    "componentId": "page-header",
                    "args": {
                      "value": {
                        "collection": false
                      }
                    }
                  }
                ]
              }
            }
          },
          {
            "type": 'button',
            "label": false,
            "icon": "fa fa-star",
            "linkWithoutPadding": true,
            "level": 'link',
            "visibleOn": "${!collection}",
            "onEvent": {
              "click": {
                "actions": [{
                  "actionType": 'setValue',
                  "componentId": 'page-header',
                  "args": {
                    "value": {
                      "collection": true
                    }
                  }
                }]
              }
            }
          }
        ]
      }, {
        "className": {
          "spacing": {
            "padding": "none",
            "margin": "none"
          }
        }

      })),
    generateBasicFormV2({
      title: '',
      static: true,
      api: '/api/mock2/saveForm?waitSeconds=2',
      labelWidth: 60,
      actions: [],
      body: [
        {
          type: "group",
          body: [
            {
              type: 'select',
              name: 'department',
              label: '归属部门',
            },
            {
              type: 'input-text',
              name: 'platform',
              label: 'Platform',
              placeholder: "请输入",
            },
            {
              type: 'input-text',
              name: 'css',
              label: 'CSS',
              required: true,
              placeholder: "请输入",
            },
          ]
        },
        {
          type: "group",
          body: [
            {
              type: 'input-text',
              name: 'browser',
              label: 'Browser',
              placeholder: "请输入",
            },
            {
              type: 'select',
              name: 'selected',
              label: '用户选择',
              placeholder: "请选择",
              options: [
                {
                  label: 'a',
                  value: 'a'
                },
                {
                  label: 'b',
                  value: 'b'
                }
              ]
            },
            {
              type: 'input-text',
              name: 'browser2',
              label: 'Browser',
              placeholder: "请输入",
            },
          ]
        },
        {
          type: "group",
          body: [
            {
              type: 'textarea',
              name: 'remark',
              label: '备注',
              showCounter: true,
              maxLength: 30,
              placeholder: "请输入",
              trimContents: true
            },
          ]
        },
        {
          type: 'group',
          body: [
            {
              type: 'input-rich-text',
              name: 'tip',
              label: '底部提示',
              placeholder: "请输入",
            }
          ]
        },
        {
          type: 'group',
          body: [
            generateCustomPaddingTabs({
              "name": "tabs",
              "noPadding": true,
              "noPaddingContent": {
                "bottom": true
              },
              "tabs": [
                {
                  "title": "基础表单1",
                  "icon": "fa fa-home",
                  "body": getFormTabDetailSchema({
                    "initApi": "",
                    "wrapWithPanel": false,
                    "labelWidth": 60,
                    "body": SCHEMA
                  })
                },
                {
                  "title": "列表",
                  "icon": "fab fa-apple",
                  "body": getWithoutMarginsCRUDSchemaV2({
                    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?waitSeconds=1",
                    "columns": [
                      {
                        "name": "id",
                        "label": "ID"
                      },
                      {
                        "name": "engine",
                        "label": "Rendering engine"
                      },
                      {
                        "name": "browser",
                        "label": "Browser"
                      },
                      {
                        "name": "platform",
                        "label": "Platform(s)"
                      },
                      {
                        "name": "engine",
                        "label": "Engine"
                      },
                      {
                        "name": "version",
                        "label": "Engine Version"
                      },
                      {
                        "name": "grade",
                        "label": "CSS grade"
                      }
                    ]
                  }, true, true)
                },
                {
                  "title": "基础表单2",
                  "icon": "fas fa-bug",
                  "body": getFormTabDetailSchema({
                    "wrapWithPanel": false,
                    "labelWidth": 60,
                    "body": SCHEMA
                  })
                }
              ]
            })
          ]
        }
      ]
    })
  ])
})
