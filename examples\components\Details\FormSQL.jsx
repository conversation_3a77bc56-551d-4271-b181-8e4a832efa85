import { generateBasicFormV2, generateCommonPage, generateStyle,  getFixedHeaderWrapperSchema, generateHeaderV2 } from "amis-utils";

export default generateCommonPage({
  "type": "page",
  "id": "page-header",
  "body":[ generateHeaderV2({
    "title": "页面大标题名称大标题名称Demo",
    "subtitle": "这是小标题",
    "actions": [
      {
        "type": "button",
        "label": false,
        "icon": "fa fa-star-o",
        "level": "link",
        "linkWithoutPadding": true,
        "visibleOn": "${collection}",
        "onEvent": {
          "click": {
            "actions": [
              {
                "actionType": "setValue",
                "componentId": "page-header",
                "args": {
                  "value": {
                    "collection": false
                  }
                }
              }
            ]
          }
        }
      },
      {
        "type": "button",
        "label": false,
        "icon": "fa fa-star",
        "linkWithoutPadding": true,
        "level": "link",
        "visibleOn": "${!collection}",
        "onEvent": {
          "click": {
            "actions": [{
              "actionType": "setValue",
              "componentId": "page-header",
              "args": {
                "value": {
                  "collection": true
                }
              }
            }]
          }
        }
      }
    ]
   }), getFixedHeaderWrapperSchema([
    generateBasicFormV2({
      title: '',
      api: '/api/mock2/saveForm?waitSeconds=2',
      actions: [],
      body: [
        {
          "type": "group",
          "body": [
            {
              "type": "input-text",
              "name": "second1",
              "label": "邮箱"
            }
          ]
        },
        {
          "type": "group",
          "label": "Flink SQL",
          "body": [
            {
              "type": "flex",
              "direction": "row",
              "alignItems": "flex-start",
              "justify": "flex-start",
              "items": [
                generateStyle(
                  {
                    type: 'resize-container',
                    minWidth: 0,
                    defaultSize: {
                      width: 300,
                    },
                    body: [
                      generateStyle(
                        {
                          type: 'wrapper',
                          body: {
                            type: 'search-box',
                            name: 'keywords',
                            size: 'full',
                            onQuery: () => { },
                            placeholder: '请输入Flink Table表名',
                            onEvent: {
                              search: {
                                actions: [
                                  {
                                    actionType: 'setValue',
                                    componentId: 'develop-job-page',
                                    args: {
                                      value: {
                                        inputTreeSearchVal: '${keywords}', // 设置值
                                      },
                                    },
                                    preventDefault: true, // 去掉默认事件
                                  },
                                ],
                              },
                            },

                          },
                        }, {
                        "className": {
                          "spacing": {
                            "padding": {
                              "top": "md",
                              "bottom": "md",
                            }
                          },
                        }
                      }),
                      generateStyle({
                        type: 'input-tree',
                        label: false,
                        searchable: false,
                        name: 'tree2',
                        initiallyOpen: false, // 默认表不展开
                        multiple: false,
                        autoCheckChildren: false,
                        "options": [
                          {
                            "label": "A1",
                            "value": "a1",
                            "children": [
                              {
                                "label": "A1-1",
                                "value": "a1-1"
                              },
                              {
                                "label": "A1-2",
                                "value": "a1-2"
                              },
                              {
                                "label": "A1-3",
                                "value": "a1-3"
                              }
                            ]
                          },
                          {
                            "label": "B1",
                            "value": "b1",
                            "children": [
                              {
                                "label": "B1-1",
                                "value": "b1-1"
                              },
                              {
                                "label": "B1-2",
                                "value": "b1-2"
                              },
                              {
                                "label": "B1-3",
                                "value": "b1-3"
                              }
                            ]
                          },
                          {
                            "label": "C1",
                            "value": "c1"
                          },
                          {
                            "label": "A2",
                            "value": "a2",
                            "children": [
                              {
                                "label": "A2-1",
                                "value": "a2-1"
                              },
                              {
                                "label": "A2-2",
                                "value": "a2-2"
                              },
                              {
                                "label": "A2-3",
                                "value": "a2-3"
                              }
                            ]
                          },
                          {
                            "label": "B2",
                            "value": "b2",
                            "children": [
                              {
                                "label": "B2-1",
                                "value": "b2-1"
                              },
                              {
                                "label": "B2-2",
                                "value": "b2-2"
                              },
                              {
                                "label": "B2-3",
                                "value": "b2-3"
                              }
                            ]
                          },
                          {
                            "label": "C2",
                            "value": "c2"
                          },
                          {
                            "label": "A3",
                            "value": "a3",
                            "children": [
                              {
                                "label": "A3-1",
                                "value": "a3-1"
                              },
                              {
                                "label": "A3-2",
                                "value": "a3-2"
                              },
                              {
                                "label": "A3-3",
                                "value": "a3-3"
                              }
                            ]
                          },
                          {
                            "label": "B3",
                            "value": "b3",
                            "children": [
                              {
                                "label": "B3-1",
                                "value": "b3-1"
                              },
                              {
                                "label": "B3-2",
                                "value": "b3-2"
                              },
                              {
                                "label": "B3-3",
                                "value": "b3-3"
                              }
                            ]
                          },
                          {
                            "label": "C3",
                            "value": "c3"
                          }
                        ],
                      }, {
                        "className": {
                          "background": {
                            "color": "white",
                          },
                          "sizing": {
                            "width": "w-72",
                            "height": "h-80"
                          }
                        },
                        "treeContainerClassName": {
                          "sizing": {
                            "height": "max-h-none",
                          },
                          "border": {
                            "width": 'none'
                          }
                        }
                      })
                    ]
                  },
                  {
                    "className": {
                      "spacing": {
                        "margin": {
                          "right": 'sm'
                        },
                      },
                      "background": {
                        "color": "white"
                      },

                      "border": {
                        "width": 'sm',
                        "style": "solid",
                        "color": "light"
                      }
                    },
                    "bodyClassName": {
                      "sizing": {
                        "height": "full",
                      },
                      "layout": {
                        "overflow": "auto"
                      }
                    }
                  }),
                generateStyle({
                  type: 'wrapper',
                  body: [
                    {
                      type: 'group',
                      name: 'editor',
                      required: true,
                      direction: 'vertical',
                      body: [
                        generateStyle({
                          type: 'tpl',
                          tpl: 'Flink SQL',
                        }, {
                          "className": {
                            "typography": {
                              "color": "black",
                              "weight": "bold",
                            },
                            "spacing": {
                              "margin": {
                                "top": "sm",
                                "bottom": "sm"
                              }
                            },
                            "layout": {
                              "display": "block"
                            }
                          }
                        }),
                        {
                          type: 'editor',
                          name: 'sql',
                          label: false,
                          editorTheme: 'vs-dark',
                          placeholder: '请输入',
                          allowFullscreen: false,
                          size: 'md',
                          language: 'sql',
                          "toolbar": [
                            {
                              "type": "button",
                              "level": "link",
                              "label": "试跑",
                              "actionType": "dialog",
                              "dialog": {
                                "showCloseButton": false,
                                "title": "系统提示",
                                "body": "试跑成功"
                              }
                            },
                            {
                              "type": "button",
                              "level": "link",
                              "label": "运行",
                              "onEvent": {
                                "click": {
                                  "actions": [
                                    {
                                      "actionType": "toast",
                                      "args": {
                                        "msgType": "success",
                                        "msg": "运行成功"
                                      }
                                    }
                                  ]
                                }
                              }
                            }
                          ],
                        },
                      ],
                    }
                  ],
                }, {
                  "className": {
                    "sizing": {
                      "height": "full",
                      "width": "full"
                    },
                    "background": {
                      "color": "white"
                    },
                    "spacing":{
                      "padding":{
                        "top":"none"
                      }
                    },
                    "layout":{
                      "overflow":"auto"
                    }

                  }
                }),
              ]
            }
          ]
        }
      ]
    })
  ])]

})
