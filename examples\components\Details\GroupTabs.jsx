import { getTabsForEditPageSchema, generateSpace, getNoPaddingTabsForEdit, generateHeaderTitle, generateTextStyle, getCopy, generateGroupForm, generateGroupPanelNoPadding, generateCommonPage, getTabDetailGroupPanelSchemaV3, getFormTabDetailSchema, getGroupPanelForWhiteBgSchema } from "amis-utils";

const CRUD_SCHEMA = [
  {
    "type": "group",
    "body": [
      {
        "type": "static",
        "name": "text1",
        "label": "归属部门",
        "columnRatio": 4
      },
      {
        "type": "static-mapping",
        "name": "text2",
        "label": "文本2",
        "columnRatio": 4,
        "map": {
          "0": "这是一个映射",
          "1": "这是一个映射",
          "2": "这是一个映射",
          "3": "这是一个映射",
          "4": "这是一个映射",
          "*": "这是一个映射",
        }
      },
      {
        "type": "static-date",
        "name": "text3",
        "label": "文本3",
        "columnRatio": 4
      }
    ]
  },
  {
    "type": "group",
    "body": [
      {
        "type": "static",
        "name": "text4",
        "label": "负责人",
        "columnRatio": 4
      },
      {
        "type": "static",
        "name": "text5",
        "label": "文本5",
        "columnRatio": 4
      },
      {
        "type": "static-datetime",
        "name": "text6",
        "label": "文本6",
        "columnRatio": 4
      }
    ]
  },
  {
    "type": "group",
    "body": [
      {
        "type": "static",
        "name": "text7",
        "label": "营销中心",
        "columnRatio": 4
      },
      {
        "type": "static",
        "name": "text8",
        "label": "文本8",
        "columnRatio": 4
      },
      {
        "type": "static",
        "name": "text9",
        "label": "文本9",
        "columnRatio": 4
      }
    ]
  }
];

export default generateCommonPage({
  "type": "page",
  "data": {
    "text1": "aaaa",
    "text2": 18,
    "text3": "7年级",
    "text4": "<EMAIL>",
    "text5": "12345678",
    "text6": "上海市浦东新区",
    "text7": "测试",
    "text8": "text8",
    "text9": "text9",
    "second1": "<EMAIL>",
    "second2": "12345678",
    "second3": "上海市浦东新区",
    "second4": "上海市浦东新区",
  },
  "body": getTabsForEditPageSchema({
    "tabs": getNoPaddingTabsForEdit([
      {
        "title": "分组表单",
        "tab": generateGroupForm({
          actions: [],
          "body": getGroupPanelForWhiteBgSchema([
            generateGroupPanelNoPadding({
              "type": "panel",
              "title": {
                type: "flex",
                justify: "flex-start",
                alignItems: "baseline",
                items: [
                  generateHeaderTitle({
                    "type": "tpl",
                    "tpl": "第一步，基础信息"
                  }),
                  generateSpace(
                    generateTextStyle(
                      {
                        type: "tpl",
                        textStyle: {
                          size: "md",
                          color: "gray-500",
                        },
                        tpl: "这是小标题"
                      }
                    ),
                    {
                      className: {
                        margin: {
                          left: 'sm'
                        }
                      }
                    })
                ]
              },
              "body": [
                {
                  type: 'group',
                  body: [
                    {
                      "name": "text1",
                      "type": "static",
                      "label": "静态展示",
                      "quickEdit": {
                        "type": "input-text"
                      }
                    },
                    {
                      type: 'static',
                      name: 'text2',
                      label: '年龄',
                    },
                    {
                      type: 'static',
                      name: 'text3',
                      label: '班级',
                      required: true,
                    },
                  ]
                },
                {
                  "type": "group",
                  "body": [
                    {
                      "type": "flex",
                      "justify": "flex-start",
                      "alignItems": "center",
                      "label":"邮箱集合",
                      "items": [
                        generateSpace(
                          {
                            "type": "static",
                            "name": "text4",
                            "label": "邮箱",
                          },
                          {
                            className: {
                              margin: {
                                bottom: "none",
                                right: "sm"
                              }
                            }
                          }),
                        getCopy({
                          "type": "icon",
                          "onEvent": {
                            "click": {
                              "actions": [
                                {
                                  "actionType": "copy",
                                  "args": {
                                    "content": "邮箱"
                                  }
                                }
                              ]
                            }
                          }
                        }),
                      ]
                    },
                    {
                      type: 'static',
                      name: 'text5',
                      label: '电话',
                    },
                    {
                      type: 'static',
                      name: 'text6',
                      label: '地址',
                      columnRatio: 4,
                    }
                  ]
                },
                {
                  type: "group",
                  body: [
                    {
                      type: 'static',
                      name: 'text7',
                      label: '其它',
                      columnRatio: 4,
                    }
                  ]
                }
              ]
            }),
            generateGroupPanelNoPadding({
              "type": "panel",
              "title": generateHeaderTitle({
                "type": "tpl",
                "tpl": "第二步，复杂信息"
              }),
              "body": [
                {
                  type: "group",
                  body: [
                    {
                      type: 'static',
                      name: 'second1',
                      label: '邮箱',
                    },
                    {
                      type: 'static',
                      name: 'second2',
                      label: '电话',
                    },
                    {
                      type: 'static',
                      name: 'second3',
                      label: '地址',
                      columnRatio: 4,
                    }
                  ]
                },
                {
                  type: 'group',
                  body: [
                    {
                      type: 'static',
                      name: 'second4',
                      label: '地址',
                      columnRatio: 4,
                    }
                  ]
                }
              ]
            }),
            generateGroupPanelNoPadding({
              "type": "panel",
              "title": generateHeaderTitle({
                "type": "tpl",
                "tpl": "第三步，策略信息"
              }),
              "body": [
                {
                  type: "group",
                  body: [
                    {
                      type: 'static',
                      name: 'second1',
                      label: '邮箱',
                    },
                    {
                      type: 'static',
                      name: 'second2',
                      label: '电话',
                    },
                    {
                      type: 'static',
                      name: 'second3',
                      label: '地址',
                      columnRatio: 4,
                    }
                  ]
                },
                {
                  type: 'group',
                  body: [
                    {
                      type: 'static',
                      name: 'second4',
                      label: '地址',
                      columnRatio: 4,
                    }
                  ]
                }
              ]
            })
          ], true),
        }, true),
      },
      {
        "title": "无标题分组",
        "tab": generateGroupForm({
          actions: [],
          "body": getGroupPanelForWhiteBgSchema([
            generateGroupPanelNoPadding({
              type: 'panel',
              body: [
                {
                  type: 'group',
                  body: [
                    {
                      "name": "text1",
                      "type": "static",
                      "label": "静态展示",
                      "quickEdit": {
                        "type": "input-text"
                      }
                    },
                    {
                      type: 'static',
                      name: 'text2',
                      label: '年龄',
                    },
                    {
                      type: 'static',
                      name: 'text3',
                      label: '班级',
                      required: true,
                    },
                  ]
                },
                {
                  type: "group",
                  body: [
                    {
                      type: 'static',
                      name: 'text4',
                      label: '邮箱',
                    },
                    {
                      type: 'static',
                      name: 'text5',
                      label: '电话',
                    },
                    {
                      type: 'static',
                      name: 'text6',
                      label: '地址',
                      columnRatio: 4,
                    }
                  ]
                },
                {
                  type: "group",
                  body: [
                    {
                      type: 'static',
                      name: 'text7',
                      label: '其它',
                      columnRatio: 4,
                    }
                  ]
                }
              ]
            }),
            generateGroupPanelNoPadding({
              type: 'panel',
              body: [
                {
                  type: "group",
                  body: [
                    {
                      type: 'static',
                      name: 'second1',
                      label: '邮箱',
                    },
                    {
                      type: 'static',
                      name: 'second2',
                      label: '电话',
                    },
                    {
                      type: 'static',
                      name: 'second3',
                      label: '地址',
                      columnRatio: 4,
                    }
                  ]
                },
                {
                  type: 'group',
                  body: [
                    {
                      type: 'static',
                      name: 'second4',
                      label: '地址',
                      columnRatio: 4,
                    }
                  ]
                }
              ]
            }),
            generateGroupPanelNoPadding({
              type: 'panel',
              "title": generateHeaderTitle({
                "type": "tpl",
                "tpl": "策略信息"
              }),
              body: [
                {
                  type: "group",
                  body: [
                    {
                      type: 'static',
                      name: 'second1',
                      label: '邮箱',
                    },
                    {
                      type: 'static',
                      name: 'second2',
                      label: '电话',
                    },
                    {
                      type: 'static',
                      name: 'second3',
                      label: '地址',
                      columnRatio: 4,
                    }
                  ]
                },
                {
                  type: 'group',
                  body: [
                    {
                      type: 'static',
                      name: 'second4',
                      label: '地址',
                      columnRatio: 4,
                    }
                  ]
                }
              ]
            })
          ], true)
        }, true),
      },
    ])
  })
})
