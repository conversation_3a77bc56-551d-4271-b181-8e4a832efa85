import { generateBasicFormV2,generateCommonPage,generateStyle } from 'amis-utils';

export default generateCommonPage({
  "type": "page",
  "body": generateBasicFormV2({
    "static": true,
    "mode": "horizontal",
    "labelWidth": 80,
    "api": "/api/mock2/saveForm?waitSeconds=2",
    "data": {
      "combos": [
        {
          "combo1": [{
            "text": "333",
            "select": "a",
          }, {
            "text": "333",
            "select": "a",
          }, {
            "text": "333",
            "select": "a",
          }],
          "text": "1"
        },
        {
          "combo1": [{
            "text": "333sss",
            "select": "a",
          }],
          "text": "1"
        },
        {
          "combo1": [{
            "text": "333",
            "select": "a",
          }],
          "text": "1"
        }
      ]
    },
    "body": [
      {
        "type": "group",
        "body": [
          {
            "type": "combo",
            "name": "combos",
            "label": "产品配置",
            "multiple": true,
            "multiLine": true,
            "subFormMode": 'horizontal',
            "subFormHorizontal": {
              "labelWidth": 60,
            },
            "items": [
              {
                "type": 'group',
                "label": "产品名称",
                "body": [{
                  "name": "text",
                  "label": false,
                  "type": "input-text"
                }]
              },
              generateStyle({
                "type": "combo",
                "name": "combo1",
                "label": false,
                "multiple": true,
                "items": [
                  {
                    "name": "text",
                    "label": "结算方式",
                    "type": "input-text"
                  },
                  {
                    "name": "select",
                    "label": "结算单价",
                    "type": "select",
                    "options": [
                      "a",
                      "b",
                      "c"
                    ]
                  }
                ]
              },{
                "itemsWrapperClassName":{
                  "spacing":{
                    "margin":{
                      "bottom":'none'
                    }
                  }
                }
              }),
            ]
          }
        ]
      },
    ],
    "actions": []
  }
  )
})
