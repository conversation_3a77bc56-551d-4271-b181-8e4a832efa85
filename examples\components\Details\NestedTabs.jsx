import { generateCustomPaddingTabs,generateCommonPage,generateStyle, getFormTabDetailSchema, getBasicListSchema, generateGroupForm, generateHeaderTpl, getDialogGroupPanelSchema, noPaddingYPanelSchema } from "amis-utils";

const CRUD_SCHEMA = [
  {
    "type": "group",
    "body": [
      {
        "type": "static",
        "name": "text1",
        "label": "归属部门",
        "columnRatio": 4
      },
      {
        "type": "static-mapping",
        "name": "text2",
        "label": "文本2",
        "columnRatio": 4,
        "map": {
          "0": "<span class='label label-info'>一</span>",
          "1": "<span class='label label-success'>二</span>",
          "2": "这是一个映射",
          "3": "<span class='label label-warning'>四</span>",
          "4": "<span class='label label-primary'>五</span>",
          "*": "<span class='label label-default'>-</span>"
        }
      },
      {
        "type": "static-date",
        "name": "text3",
        "label": "文本3",
        "columnRatio": 4
      }
    ]
  },
  {
    "type": "group",
    "body": [
      {
        "type": "static",
        "name": "text4",
        "label": "负责人",
        "columnRatio": 4
      },
      {
        "type": "static",
        "name": "text5",
        "label": "文本5",
        "columnRatio": 4
      },
      {
        "type": "static-datetime",
        "name": "text6",
        "label": "文本6",
        "columnRatio": 4
      }
    ]
  },
  {
    "type": "group",
    "body": [
      {
        "type": "static",
        "name": "text7",
        "label": "营销中心",
        "columnRatio": 4
      },
      {
        "type": "static",
        "name": "text8",
        "label": "文本8",
        "columnRatio": 4
      },
      {
        "type": "static",
        "name": "text9",
        "label": "文本9",
        "columnRatio": 4
      }
    ]
  }
];

export default generateCommonPage({
  "type": "page",
  "data": {
    "text1": "营销中心",
    "text2": 2,
    "text3": 1593327764,
    "text4": "负责人",
    "text5": "text5",
    "text6": 1593327764,
    "text7": "创建人",
    "text8": "text8",
    "text9": "text9",
  },
  "body": generateCustomPaddingTabs({
    "noPadding": {
      "bottom": true
    },
    "noPaddingContent": {
      "bottom": true
    },
    "tabs": [
      {
        "title": "带Alert提示表单1",
        "body": [
          generateStyle({
            "type": "alert",
            "title": "提示类标题",
            "body": "提示类文案",
            "level": "info",
          },{
            "className":{
              "spacing":{
                "margin":{
                  "bottom":"md"
                }
              }
            }
          }),
          generateCustomPaddingTabs({
            "noPadding": true,
            "noPaddingContent": true,
            "tabs": [
              {
                "title": "列表",
                "tab": getBasicListSchema({
                  "api": "/api/mock2/crud/table4",
                  "columns": [
                    {
                      "name": "id",
                      "label": "ID"
                    },
                    {
                      "name": "engine",
                      "label": "Rendering engine"
                    },
                    {
                      "name": "browser",
                      "label": "Browser"
                    },
                    {
                      "name": "platform",
                      "label": "Platform(s)"
                    },
                    {
                      "name": "version",
                      "label": "Engine version"
                    },
                    {
                      "name": "grade",
                      "label": "CSS grade"
                    }
                  ]
                })
              },
              {
                "title": "分组表单",
                "body": generateGroupForm({
                  "type": 'form',
                  "actions": [],
                  "static": true,
                  "body": getDialogGroupPanelSchema([
                    generateStyle({
                      type: 'panel',
                      title: generateHeaderTpl({
                        type: 'tpl',
                        tpl: '第一步，基础信息'
                      }),
                      body: noPaddingYPanelSchema({
                        "body": CRUD_SCHEMA
                      })
                    },{
                      "className":{
                        "spacing":{
                          "margin":{
                            "top":"md",
                            "bottom":"none"
                          }
                        }
                      }
                    }),
                    {
                      type: 'panel',
                      title: generateHeaderTpl({
                        type: 'tpl',
                        tpl: '第二步，复杂信息'
                      }),
                      body: noPaddingYPanelSchema({
                        "body": CRUD_SCHEMA
                      })
                    }
                  ])
                })
              },
              {
                "title": "基础表单",
                "body": getFormTabDetailSchema({
                  "body": CRUD_SCHEMA
                })
              }
            ]
          })
        ]
      },
      {
        "title": "带Alert提示表单2",
        "body": [
          generateStyle({
            "type": "alert",
            "title": "提示类标题",
            "body": "提示类文案",
            "level": "info",
          },{
            "className":{
              "spacing":{
                "margin":{
                  "bottom":"md"
                }
              }
            }
          }),
          generateCustomPaddingTabs({
            "noPadding": true,
            "noPaddingContent": true,
            "tabs": [
              {
                "title": "基础表单1",
                "body": getFormTabDetailSchema({
                  "body": CRUD_SCHEMA
                })
              },
              {
                "title": "基础表单2",
                "body": getFormTabDetailSchema({
                  "body": CRUD_SCHEMA
                })
              }
            ]
          })
        ]
      },
      {
        "title": "带Alert提示表单3",
        "body": [
          generateStyle({
            "type": "alert",
            "title": "提示类标题",
            "body": "提示类文案",
            "level": "info",
          },{
            "className":{
              "spacing":{
                "margin":{
                  "bottom":"md"
                }
              }
            }
          }),
          generateCustomPaddingTabs({
            "noPadding": true,
            "noPaddingContent": true,
            "tabs": [
              {
                "title": "基础表单1",
                "body": getFormTabDetailSchema({
                  "body": CRUD_SCHEMA
                })
              },
              {
                "title": "基础表单2",
                "body": getFormTabDetailSchema({
                  "body": CRUD_SCHEMA
                })
              }
            ]
          })
        ]
      }
    ]
  })
})
