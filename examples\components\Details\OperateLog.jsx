import { getBasicListSchemaV2, generateCommonPage, generateStyle, getButtonIconLink, getVersionDiffTag } from "amis-utils";


export default generateCommonPage({
  "type": "page",
  "data": {
    "OPTIONS": [
      {
        "label": "新增",
        "value": "add"
      }, {
        "label": "修改",
        "value": "edit"
      }, {
        "label": "删除",
        "value": "delete"
      }
    ]

  },
  "body": getBasicListSchemaV2({
    "api": "/api/mock2/crud/operatelog",
    "headerToolbar": [
      getVersionDiffTag()
    ],
    "showExpansionColumn": false,
    "columns": [
      {
        "name": "updateBy",
        "label": "更新人",
        "searchable": {
          "type": "input-text",
          "name": "updateBy",
          "label": "更新人",
          "placeholder": "请输入更新人"
        }
      },
      {
        "name": "updateDate",
        "label": "更新时间",
        "format": "YYYY-MM-DD HH:mm:ss",
        "valueFormat": "x",
        "type": "date",
        "searchable": {
          "type": "ds-date-range-picker",
          "label": "更新时间",
        }
      },
      {
        "label": "操作状态",
        "name": "statusName",
        "searchable": {
          "type": "select",
          "source": "${OPTIONS}",
          "placeholder": "请选择状态",
          "clearable": true,
          "name": "status"
        },
      },
      {
        "label": "更新内容",
        "type": "container",
        "body": [
          getButtonIconLink({
            "label": "展开",
            "icon": "fas fa-angle-down",
            "visibleOn": "${!_amisExpanded}",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "actionType": "custom",
                    "script": "context.props.row.toggleExpanded()"
                  }
                ]
              }
            }
          }),
          getButtonIconLink({
            "label": "收起",
            "icon": "fas fa-angle-up",
            "visibleOn": "${_amisExpanded}",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "actionType": "custom",
                    "script": "context.props.row.toggleExpanded()"
                  }
                ]
              }
            }
          })
        ]
      }
    ],
    "subTable": {
      "type": "table",
      "source": "$updateFileds",
      "showExpansionColumn": false,
      "columns": [
        {
          "name": "fieldName",
          "label": "字段名"
        },
        {
          "name": "baselineVersion",
          "label": "更新前",
        },
        {
          "name": "diffVersion",
          "label": "更新后",
          "type": "container",
          "body": [
            {
              "type": "tpl",
              "tpl": '<span class="${ status === "ADD" ? "pm-versionDiff-add" : (status === "EDIT" ? "pm-versionDiff-edit" : (status === "DELETE" ? "pm-versionDiff-delete" : ""))}">${diffVersion}</span>',
              "visibleOn": "${diffVersion}",
            },
            generateStyle({
              "type": "tpl",
              "tpl": "-",
              "visibleOn": "${!diffVersion}"
            }, {
              "className": {
                "typography": {
                  "color": "disable"
                }
              }
            })
          ]
        }
      ]
    }
  })
});
