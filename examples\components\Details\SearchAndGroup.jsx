
import { getBasicSearchSchema, generateCommonPage,generateGroupForm,generateSpace,generateTextStyle, generateGroupPanel, generateHeaderTitle ,generateInputFile} from 'amis-utils'

export default generateCommonPage({
  type: 'page',

  data: {
    downloads: [
      {
        name: "测试环境堡垒机操作手册测试环境堡垒机操作手册测试环境堡垒机操作手册测试环境堡垒机操作手册测试环境堡垒机操作手册测试环境堡垒机操作手册测试环境堡垒机操作手册测试环境堡垒机操作手册测试环境堡垒机操作手册.pdf",
        link: "https://www.baidu.com"
      },
      {
        name: "跳板机操作手册.pdf",
        link: "https://www.baidu.com"
      },
      {
        name: "运维日常问题排查手册.pdf",
        link: "https://www.baidu.com"
      }
    ]
  },
  body: [
    getBasicSearchSchema({
      title: "",
      target: "targetFormId",
      id: "sourceFormId",
      labelWidth: 65,
      body: [
        {
          "type": "group",
          "body": [
            {
              "type": "input-text",
              "name": "keywords",
              "label": "关键字",
              "clearable": true,
              "placeholder": "通过关键字搜索",
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "engine",
              "label": "Engine",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "platform",
              "label": "Platform",
              "clearable": true,
              "columnRatio": 4
            }
          ]
        },
        {
          "type": "group",
          "body": [
            {
              "type": "input-text",
              "name": "keywords1",
              "label": "关键字1",
              "clearable": true,
              "placeholder": "通过关键字搜索",
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "engine1",
              "label": "Engine1",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "platform1",
              "label": "Platform1",
              "clearable": true,
              "columnRatio": 4
            }
          ]
        }
      ],
      actions: [
        {
          type: "button",
          label: "重 置",
          onEvent: {
            click: {
              actions: [
                {
                  actionType: 'clear',
                  componentId: 'sourceFormId'
                },
                {
                  actionType: 'submit',
                  componentId: 'sourceFormId'
                },
              ],
            },
          },
        },
        {
          type: "submit",
          level: "primary",
          label: "查 询",
        }
      ]
    }),
    generateGroupForm({
      type: 'form',
      title: '',
      mode: "horizontal",
      id: "targetFormId",
      actions: [],
      initApi: {
        method: 'get',
        url: '/api/mock2/form/initForm',
        data: {
          test1: '${keywords}',
          test2: '${engine}',
        },
      },
      body: [
        generateGroupPanel({
          type: 'panel',
          title: {
            type: "flex",
            justify: 'flex-start',
            alignItems: 'baseline',
            items: [
              generateHeaderTitle({
                type: "tpl",
                tpl: "第一步，基础信息",
              }),
              generateSpace(
                generateTextStyle(
                  {
                    type: "tpl",
                    textStyle: {
                      size: "md",
                      color: "gray-500",
                    },
                    tpl: "这是小标题"
                  }
                ),
                {
                  className: {
                    margin: {
                      left: 'sm'
                    }
                  }
                })

            ]
          },
          body: [
            {
              type: 'group',
              body: [
                {
                  "name": "test1",
                  "type": "static",
                  "label": "静态展示"
                },
                {
                  type: 'static',
                  name: 'test3',
                  label: '年龄',
                },
                {
                  type: 'static',
                  name: 'test3',
                  label: '班级'
                },
              ]
            },
            {
              type: "group",
              body: [
                {
                  type: 'static',
                  name: 'test1',
                  label: '邮箱',
                },
                {
                  type: 'static',
                  name: 'test3',
                  label: '电话',
                },
                {
                  type: 'static',
                  name: 'test3',
                  label: '地址',
                  columnRatio: 4,
                }
              ]
            },
            {
              type: "group",
              body: [
                {
                  type: 'static',
                  name: 'test1',
                  label: '其它',
                  columnRatio: 4,
                }
              ]
            }
          ]
        }),
        generateGroupPanel({
          type: 'panel',
          title: generateHeaderTitle({
            type: 'tpl',
            tpl: '第二步，复杂信息',
          }),
          body: [
            {
              type: "group",
              body: [
                {
                  type: 'static',
                  name: 'test1',
                  label: '邮箱',
                },
                {
                  type: 'static',
                  name: 'test3',
                  label: '电话',
                },
                {
                  type: 'static',
                  name: 'test3',
                  label: '地址',
                  columnRatio: 4,
                }
              ]
            },
            {
              type: 'group',
              body: [
                {
                  type: 'static',
                  name: 'test1',
                  label: '地址',
                  columnRatio: 4,
                }
              ]
            }
          ]
        }),
        generateGroupPanel({
          type: 'panel',
          title: generateHeaderTitle({
            type: 'tpl',
            tpl: '第三步，额外信息',
          }),
          body: [
            generateInputFile({
              type:'input-file',
              static:true,
              name:"downloads",
              label:"文件预览",
              multiple:true,
              urlField:"link",
              downloadConfig:{
                downloadAction:{
                  label:"下载",
                }
              }
            }),

          ]
        }),
      ]
    })
  ]
});
