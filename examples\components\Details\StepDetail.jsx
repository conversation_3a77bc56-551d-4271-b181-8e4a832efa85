/**
 * 详情页带头部+步骤+分组的示例
 * */
import {
  generateGroupForm,
  generateGroupPanel,
  generateHeaderV2,
  generateWizard,
  getGroupPanelForWhiteBgSchema,
  generateGroupPanelNoPadding,
  generateHeaderTitle,
  generateBasicFormV2,
  generateCommonPage,
  generateSpace
} from 'amis-utils';

export default generateCommonPage({
  "type": "page",
  "data": {
    "name": "张三",
    "age": 20,
    "address": "金科路",
    "email": "<EMAIL>",
    "class": "三年二班",
    "company": "abc",
    "telephone": 18711223344,
    "eventSource": "FEATURE",
    "type": "caocao"
  },
  "body": [
    generateHeaderV2({
      "title": "页面大标题名称大标题名称",
      "subtitle": "我是小标题",
    }),
    generateBasicFormV2({
      type: 'form',
      static: true,
      labelWidth: 130,
      actions: [],
      body: [
        generateSpace({
          type: 'steps',
          status: {
            second: 'error'
          },
          steps: [{
            title: '第一步',
            value: 'first'
          }, {
            title: '第二步',
            value: 'second'
          }, {
            title: '第三步',
            value: 'third'
          }]
        },{
          className: {
            margin: {
              top: "none",
            },
          }
        }),
        ...getGroupPanelForWhiteBgSchema([
          generateGroupPanelNoPadding({
            "type": "panel",
            "title": generateHeaderTitle({
              "type": "tpl",
              "tpl": "基础信息"
            }),
            "body": [
              {
                "type": "group",
                "body": [
                  {
                    "type": "input-text",
                    "name": "name",
                    "label": "姓名",
                  },
                  {
                    "type": "input-text",
                    "name": "age",
                    "label": "年龄",
                  },
                  {
                    "type": "input-text",
                    "name": "class",
                    "label": "班级",
                  },
                ]
              },
              {
                "type": "group",
                "body": [
                  {
                    "type": "input-text",
                    "name": "email",
                    "label": "邮箱",
                  },
                  {
                    "type": "input-text",
                    "name": "telephone",
                    "label": "电话",
                  },
                  {
                    "type": "input-text",
                    "name": "address",
                    "label": "地址",
                  }
                ]
              },
              {
                "type": "group",
                "body": [
                  {
                    "type": "select",
                    "name": "type",
                    "label": "类型",
                    "options": [
                      {
                        "label": "曹操",
                        "value": "caocao",
                      },
                      {
                        "label": "刘备",
                        "value": "liubei",
                      }
                    ]
                  },
                  {
                    "type": "select",
                    "name": "eventSource",
                    "label": "事件来源",
                    "options": [
                      {
                        "label": "业务系统",
                        "value": "SYSTEM",
                      },
                      {
                        "label": "特征系统",
                        "value": "FEATURE",
                      },
                      {
                        "label": "北斗系统",
                        "value": "EFUEL",
                      },
                      {
                        "label": "埋点系统",
                        "value": "STATS",
                      }
                    ],
                  },
                  {
                    "type": "input-text",
                    "name": "company",
                    "label": "工作地址",
                  },
                ]
              }
            ]
          }),
          generateGroupPanelNoPadding({
            "type": "panel",
            "noMargin": {
              "bottom": true
            },
            "title": generateHeaderTitle({
              "type": "tpl",
              "tpl": "额外信息"
            }),
            "body": [
              {
                "type": "group",
                "body": [
                  {
                    "type": "input-text",
                    "name": "name",
                    "label": "姓名",
                  },
                  {
                    "type": "input-text",
                    "name": "age",
                    "label": "年龄",
                  },
                  {
                    "type": "input-text",
                    "name": "class",
                    "label": "班级",
                  },
                ]
              },
              {
                "type": "group",
                "body": [
                  {
                    "type": "input-text",
                    "name": "email",
                    "label": "邮箱",
                  },
                  {
                    "type": "input-text",
                    "name": "telephone",
                    "label": "电话",
                  },
                  {
                    "type": "input-text",
                    "name": "address",
                    "label": "地址",
                  }
                ]
              },
              {
                "type": "group",
                "body": [
                  {
                    "type": "select",
                    "name": "type",
                    "label": "类型",
                    "options": [
                      {
                        "label": "曹操",
                        "value": "caocao",
                      },
                      {
                        "label": "刘备",
                        "value": "liubei",
                      }
                    ]
                  },
                  {
                    "type": "select",
                    "name": "eventSource",
                    "label": "事件来源",
                    "options": [
                      {
                        "label": "业务系统",
                        "value": "SYSTEM",
                      },
                      {
                        "label": "特征系统",
                        "value": "FEATURE",
                      },
                      {
                        "label": "北斗系统",
                        "value": "EFUEL",
                      },
                      {
                        "label": "埋点系统",
                        "value": "STATS",
                      }
                    ],
                  },
                  {
                    "type": "input-text",
                    "name": "company",
                    "label": "工作地址",
                  },
                ]
              }
            ]
          }),
        ]),
      ]
    })
  ]
});
