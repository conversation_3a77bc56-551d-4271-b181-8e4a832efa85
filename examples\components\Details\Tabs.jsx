
import { generateCustomPaddingTabs, generateCommonPage,generateStyle, getTabsIconAndRemarkTitle, getFormTabDetailSchema, getBasicListSchema, getCard } from "amis-utils";
const CRUD_SCHEMA = [
  {
    "type": "group",
    "body": [
      {
        "type": "static",
        "name": "text1",
        "label": "归属部门",
        "columnRatio": 4
      },
      {
        "type": "static-mapping",
        "name": "text2",
        "label": "文本2",
        "columnRatio": 4,
        "map": {
          "0": "<span class='label label-info'>一</span>",
          "1": "<span class='label label-success'>二</span>",
          "2": "这是一个映射",
          "3": "<span class='label label-warning'>四</span>",
          "4": "<span class='label label-primary'>五</span>",
          "*": "<span class='label label-default'>-</span>"
        }
      },
      {
        "type": "static-date",
        "name": "text3",
        "label": "文本3",
        "columnRatio": 4
      }
    ]
  },
  {
    "type": "group",
    "body": [
      {
        "type": "static",
        "name": "text4",
        "label": "负责人",
        "columnRatio": 4
      },
      {
        "type": "static",
        "name": "text5",
        "label": "文本5",
        "columnRatio": 4
      },
      {
        "type": "static-datetime",
        "name": "text6",
        "label": "文本6",
        "columnRatio": 4
      }
    ]
  },
  {
    "type": "group",
    "body": [
      {
        "type": "static",
        "name": "text7",
        "label": "营销中心",
        "columnRatio": 4
      },
      {
        "type": "static",
        "name": "text8",
        "label": "文本8",
        "columnRatio": 4
      },
      {
        "type": "static",
        "name": "text9",
        "label": "文本9",
        "columnRatio": 4
      }
    ]
  }
];

export default generateCommonPage({
  "type": "page",

  "data": {
    "text1": "营销中心",
    "text2": 2,
    "text3": 1593327764,
    "text4": "负责人",
    "text5": "text5",
    "text6": 1593327764,
    "text7": "创建人",
    "text8": "text8",
    "text9": "text9",
    "itemLists": [
      {
        "title": "客户账",
        // "subTitle": "这是一段副标题文案",
        isLike: false
      },
    ],
  },
  "body": generateCustomPaddingTabs({
    "noPadding": {
      "bottom": true
    },
    "noPaddingContent": true,
    "tabs": [
      {
        "title": "基础表单1",
        "body": getFormTabDetailSchema({
          "initApi": "",
          "body": CRUD_SCHEMA
        })
      },
      {
        "title": [
          getTabsIconAndRemarkTitle({
            "type": "tpl",
            "tpl": "列表"
          }),
          {
            "type": "remark",
            "content": "这是一段提醒"
          }
        ],
        "body": getBasicListSchema({
          "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?waitSeconds=1",
          "columns": [
            {
              "name": "id",
              "label": "ID"
            },
            {
              "name": "engine",
              "label": "Rendering engine"
            },
            {
              "name": "browser",
              "label": "Browser"
            },
            {
              "name": "platform",
              "label": "Platform(s)"
            },
            {
              "name": "engine",
              "label": "Engine"
            },
            {
              "name": "version",
              "label": "Engine Version"
            },
            {
              "name": "grade",
              "label": "CSS grade"
            }
          ]
        })
      },
      {
        "title": "基础表单2",
        "body": getFormTabDetailSchema({
          "body": CRUD_SCHEMA
        })
      },
      {
        "title": "跳转按钮",
        "body": getFormTabDetailSchema({
          "body": [
            {
              "type": "cards",
              "columnsCount": 5,
              "source": "${itemLists}",
              "card": getCard(
                generateStyle({
                "header": {
                  "title": "${title}",
                  "subTitle": "${subTitle}",
                },
                "toolbar": [
                  generateStyle({
                    "type": "button",
                    "label": "查看",
                    "linkWithoutPadding": true,
                    "level": "link",
                    "actionType": "link",
                    "link": "http://moka.dmz.dev.caijj.net/dataseeddesigndocui/#/amis/zh-CN/components/action",
                  }, {
                    "className": {
                      "sizing": {
                        "height": "none"
                      }
                    }
                  })
                ],
              },{
                "metaClassName":{
                  "layout":{
                    "overflow":{
                      "x":"hidden"
                    }
                  }
                }
              }))
            },

          ]
        })
      }
    ]
  })
})
