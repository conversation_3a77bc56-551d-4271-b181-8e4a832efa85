import { getWithoutMarginsCRUDSchema,generateVersionDiffPanel,generateVersionDiffFlexFrow,generateStyle,generateDiffSelect, generateGroupForm,generateCommonPage,generateSpacing, getDialogGroupPanelSchema, generateHeaderTpl, getDiffPageSchema } from "amis-utils";

const getDiffCell = (preName, name, status) => {
  if (!name && !status) {
    if (preName === 'text1-version1') {
      return ([
        {
          "type": "group",
          "mode": "inline",
          "body": [{
            "type": "tpl",
            "tpl": "更新前"
          }, generateSpacing({
            "type": "button",
            "level": "link",
            "label": "详情",
            "actionType": "dialog",
            "linkWithoutPadding": true,
            "style": { height: "22px" },
            "dialog": {
              "title": "详情",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },{
            "className":{
              "padding":{
                "left":"none"
              }
            }
          }),]
        },
      ])
    } else {
      return ([
        {
          "type": "typography",
          "text": "${" + preName + "}",
          "visibleOn": "${" + preName + "}",
          "ellipsis": {
            "rows": 1
          }
        },
        generateStyle( {
          "type": "tpl",
          "tpl": "-",
          "visibleOn": "${!" + preName + "}",
        }, {
          "className": {
            "typography": {
              "color": "disable"
            }
          }
        })

      ])
    }

  }

  if (preName === 'text1-version1') {

    return ([
      {
        "type": "group",
        "mode": "inline",
        "width": 100,
        "body": [{
          "type": "tpl",
          "tpl": "<div title='${" + name + "}' class='pm-ellipsis ${" + status + "=== 'ADD' ? 'pm-versionDiff-add' : (" + status + " === 'EDIT' ? 'pm-versionDiff-edit' : (" + status + " === 'DELETE' ? 'pm-versionDiff-delete' : ''))}'>更新后</div>"
        }, generateSpacing({
          "type": "button",
          "level": "link",
          "label": "详情",
          "actionType": "dialog",
          "linkWithoutPadding": true,
          "style": { height: "22px" },
          "dialog": {
            "title": "详情",
            "showCloseButton": false,
            "body": "这是个简单的弹框。"
          }
        },{
          "className":{
            "padding":{
              "left":"none"
            }
          }
        }),]
      },
    ])
  } else {
    return ([
      {
        "type": "typography",
        "visibleOn": "${!" + status + "}",
        "text": "${" + name + "}",
        "ellipsis": {
          "rows": 1
        }
      },
      {
        "type": "typography",
        "visibleOn": "${" + status + " === 'ADD'}",
        "text": "<span class='pm-versionDiff-add'>${" + name + "}</span>",
        "ellipsis": {
          "rows": 1
        }
      },

      {
        "type": "typography",
        "visibleOn": "${" + status + " === 'DELETE'}",
        "text": "<span class='pm-versionDiff-delete'>${" + name + "}</span>",
        "ellipsis": {
          "rows": 1
        }
      },
      {
        "type": "typography",
        "visibleOn": "${" + status + " === 'EDIT'}",
        "text": "<span title='${" + name + "}'><span class='pm-versionDiff-edit'>${" + name + "}</span></span>",
        "ellipsis": {
          "rows": 1
        }
      },
    ])
  }

}


const getTpl = (name, status) => {
  let tpl;
  if (status) {
    tpl = "<div title='${" + name + "}' class='pm-ellipsis ${" + status + "=== 'ADD' ? 'pm-versionDiff-add' : (" + status + " === 'EDIT' ? 'pm-versionDiff-edit' : (" + status + " === 'DELETE' ? 'pm-versionDiff-delete' : ''))}'>${" + name + "}</div>";
  } else {
    tpl = "<div title=${" + name + "} class='pm-ellipsis'>${" + name + "}</div>";
  }
  return tpl;
}

export default generateCommonPage({
  "type": "page",

  "data": {
    "text1": "营销中心",
    "status": "ADD",
    "text1Status": "DELETE",
    "text2Status": "EDIT",
    "text2": 2,
    "text3": 1593327764,
    "text4": "负责人等哈时间段哈就是打开就撒打撒肯定是大空间圣诞贺卡撒较大",
    "text5": "123423456576",
    "text55": "12342345090",
    "text6": 1593327764,
    "text40": "12342dsadsadsadsajdhasjkdh345090",
    "text7": "创建人",
    "text8": "text8",
    "text9": "text9",
    "queryList1": [
      {
        "changeType": "",
        "key-version1": "k1",
        "key-version2": "k1",
        "keyStatus": "",
        "id-version1": "userId",
        "id-version2": "userId",
        "idStatus": "",
        "text1Status": "",
        "text4Status": "",
        "text5Status": "",
        "text1-version1": "string",
        "text1-version2": "string",
        "text4-version1": "是",
        "text4-version2": "是",
        "text5-version1": "用户Id",
        "text5-version2": "用户Id",
        "grade": "ABCD",
        "grade1": "ABCD",
        "grade2": "B",
        "grade3": "C",
        "gradeStatus": "inactive",
        "grade1Status": "inactive",
        "grade2Status": "inactive",
        "grade3Status": "inactive",
      },
      {
        "changeType": "增",
        "key-version1": "",
        "key-version2": "k2",
        "keyStatus": "ADD",
        "id-version1": "",
        "id-version2": "userType",
        "idStatus": "ADD",
        "text1Status": "ADD",
        "text4Status": "ADD",
        "text5Status": "ADD",
        "text1-version1": "",
        "text1-version2": "string",
        "text4-version1": "",
        "text4-version2": "是",
        "text5-version1": "",
        "text5-version2": "用户类型",
      },
      {
        "changeType": "改",
        "key-version1": "k3大家啊数控刀具撒肯德基",
        "key-version2": "k3",
        "keyStatus": "EDIT",
        "id-version1": "userId大家家",
        "id-version2": "userI爸爸d",
        "idStatus": "EDIT",
        "text1Status": "EDIT",
        "text4Status": "EDIT",
        "text5Status": "EDIT",
        "text1-version1": "stringstringstringstringstringstringstring",
        "text1-version2": "integer带回家收到撒黄金时代stringstringstringstringstringstring",
        "text4-version1": "",
        "text4-version2": "string",
        "text5-version1": "",
        "text5-version2": "用户Id",
        "grade": "这里有更多撒金凤卡少女反馈加啊是南非进口三反馈加三分快三方面小美女你发怒发怒发怒",
        "grade1": "1232178346816421641326426147126473214143214",
        "gradeStatus": "inactive",
        "grade1Status": "inactive",
      },
      {
        "changeType": "删",
        "key-version1": "k4",
        "key-version2": "k4",
        "keyStatus": "DELETE",
        "id-version1": "userId",
        "id-version2": "userId",
        "idStatus": "DELETE",
        "text1Status": "DELETE",
        "text4Status": "DELETE",
        "text5Status": "DELETE",
        "text1-version1": "string",
        "text1-version2": "string",
        "text4-version1": "是",
        "text4-version2": "是",
        "text5-version1": "用户Id",
        "text5-version2": "用户Id",
        "grade": "这里有更多撒金凤卡少女反馈加啊是南非进口三反馈加三分快三方面小美女你发怒发怒发怒",
        "grade1": "1232178346816421641326426147126473214143214",
        "grade2": "sadkjhsakdfhskahfkashfdksahfkasdksafkadsfkasf",
        "gradeStatus": "inactive",
        "grade1Status": "inactive",
        "grade2Status": "inactive",
      },
    ],
    "queryList2": [
      {
        "changeType": "",
        "key-version1": "k1",
        "key-version2": "k1",
        "keyStatus": "",
        "id-version1": "userId",
        "id-version2": "userId",
        "idStatus": "",
        "text1Status": "",
        "text4Status": "",
        "text5Status": "",
        "text1-version1": "string",
        "text1-version2": "string",
        "text4-version1": "是",
        "text4-version2": "是",
        "text5-version1": "用户Id",
        "text5-version2": "用户Id",
        "grade": "ABCD",
        "grade1": "ABCD",
        "grade2": "B",
        "grade3": "C",
        "gradeStatus": "inactive",
        "grade1Status": "inactive",
        "grade2Status": "inactive",
        "grade3Status": "inactive",
      },
      {
        "changeType": "增",
        "key-version1": "",
        "key-version2": "k2",
        "keyStatus": "ADD",
        "id-version1": "",
        "id-version2": "userType",
        "idStatus": "ADD",
        "text1Status": "ADD",
        "text4Status": "ADD",
        "text5Status": "ADD",
        "text1-version1": "",
        "text1-version2": "string",
        "text4-version1": "",
        "text4-version2": "是",
        "text5-version1": "",
        "text5-version2": "用户类型",
        "grade": "A这里有更多撒金凤卡少女反馈加啊是南非进口三反馈加三分快三方面小美女你发怒发怒发怒",
        "grade1": "1232178346816421641326426147126473214143214",
        "grade2": "sadkjhsakdfhskahfkashfdksahfkasdksafkadsfkasf",
        "grade3": "sadkjhsakdfhskahfkashfdksahfkasdksafkadsfkasf",
        "gradeStatus": "success",
        "grade1Status": "success",
        "grade2Status": "success",
        "grade3Status": "success",
      },
      {
        "changeType": "改",
        "key-version1": "k3大家啊数控刀具撒肯德基",
        "key-version2": "k3",
        "keyStatus": "EDIT",
        "id-version1": "userId大家家",
        "id-version2": "userI爸爸d",
        "idStatus": "EDIT",
        "text1Status": "EDIT",
        "text4Status": "EDIT",
        "text5Status": "EDIT",
        "text1-version1": "stringstringstringstringstringstringstring",
        "text1-version2": "integer带回家收到撒黄金时代stringstringstringstringstringstring",
        "text4-version1": "",
        "text4-version2": "是",
        "text5-version1": "",
        "text5-version2": "用户Id",
        "grade": "这里有更多撒金凤卡少女反馈加啊是南非进口三反馈加三分快三方面小美女你发怒发怒发怒",
        "grade1": "1232178346816421641326426147126473214143214",
        "grade2": "dsfdsafdsafjwofewjcxnvfew",
        "grade3": "dsafsdafsafsda",
        "gradeStatus": "error",
        "grade1Status": "error",
        "grade2Status": "success",
        "grade3Status": "success",
      },
      {
        "changeType": "删",
        "key-version1": "k4",
        "key-version2": "k4",
        "keyStatus": "DELETE",
        "id-version1": "userId",
        "id-version2": "userId",
        "idStatus": "DELETE",
        "text1Status": "DELETE",
        "text4Status": "DELETE",
        "text5Status": "DELETE",
        "text1-version1": "string",
        "text1-version2": "string",
        "text4-version1": "是",
        "text4-version2": "是",
        "text5-version1": "用户Id",
        "text5-version2": "用户Id",
        "grade": "这里有更多撒金凤卡少女反馈加啊是南非进口三反馈加三分快三方面小美女你发怒发怒发怒",
        "grade1": "1232178346816421641326426147126473214143214",
        "grade2": "sadkjhsakdfhskahfkashfdksahfkasdksafkadsfkasf",
        "gradeStatus": "error",
        "grade1Status": "error",
        "grade2Status": "error",
      },
    ]
  },
  "body": getDiffPageSchema({
    "body": [
      {
        "type": 'form',
        "title": '',
        "actions": [],
        "mode": "horizontal",
        "static": true,
        "wrapWithPanel": false,
        "body": generateSpacing({
          "type": 'flex',
          "justify": "space-between",
          "alignItems": 'center',
          "items": [
            generateDiffSelect({
              "type": "select",
              "label": "基准版本",
              "name": 'baselineVersion',
              "value": "v1",
              "labelWidth": 60,

              "options": [
                {
                  "label": 'V1',
                  "value": "v1"
                },
                {
                  "label": 'V2',
                  "value": "v2"
                }
              ]
            }),

            generateDiffSelect({
              "type": "select",
              "label": "对比版本",
              "name": 'diffVersion',
              "value": 'v2',
              "labelWidth": 60,

              "options": [
                {
                  "label": 'V1',
                  "value": "v1"
                },
                {
                  "label": 'V2',
                  "value": "v2"
                }
              ]
            })
          ]
        },{
          "className":{
            "margin":{
              "bottom":"md"
            }
          }
        }),
      },
      {
        "type": 'flex',
        "justify": "space-between",
        "alignItems": 'start',
        "items": [
          generateVersionDiffFlexFrow({
            "type": "panel",
            "title": "",
            "body": generateGroupForm({
              type: 'form',
              static: true,
              actions: [],
              body: getDialogGroupPanelSchema([
                {
                  type: 'panel',
                  title: generateHeaderTpl({
                    type: 'tpl',
                    tpl: '基础信息'
                  }),
                  body: [
                    {
                      type: 'group',
                      body: [
                        {
                          type: "tooltip-wrapper",
                          content: "${text1}",
                          columnRatio: 6,
                          body:{
                            type: 'static-tpl',
                            name: 'text1',
                            label: '姓名',
                            tpl: getTpl('text1')
                          },
                        },
                        {
                          type: 'static-tpl',
                          name: 'text2',
                          label: '年龄',
                          columnRatio: 6,
                          tpl: getTpl('text2')
                        }
                      ]
                    },
                    {
                      type: "group",
                      body: [
                        {
                          type: "tooltip-wrapper",
                          content: "${text40}",
                          columnRatio: 6,
                          body: {
                            type: 'static-tpl',
                            name: 'text40',
                            label: '邮箱',
                            tpl: getTpl('text40')
                          },
                        },
                        {
                          type: 'static-tpl',
                          name: 'text5',
                          label: '电话',
                          columnRatio: 6,
                          tpl: getTpl('text5')
                        }
                      ]
                    },
                    {
                      type: "group",
                      body: [
                        {
                          type: 'static-tpl',
                          name: 'text7',
                          label: '其它',
                          columnRatio: 6,
                          tpl: getTpl('text7')
                        }
                      ]
                    }
                  ]
                },
                generateVersionDiffPanel({
                  type: 'panel',
                  title: generateHeaderTpl({
                    type: 'tpl',
                    tpl: '入参'
                  }),
                  body: [
                    {
                      type: 'group',
                      body: [
                        {
                          type: 'static-tpl',
                          name: 'text1',
                          label: '资信token',
                        },
                        {
                          type: 'static-tpl',
                          name: 'text2',
                          label: '年龄',
                        }
                      ]
                    },
                    {
                      type: 'group',
                      body: [
                        getWithoutMarginsCRUDSchema({
                          "columns": [
                            {
                              "name": "key-version1",
                              "label": "参数key",
                              "width": 100,
                              "type": "group",
                              "direction": "vertical",
                              "body": getDiffCell('key-version1')
                            },
                            {
                              "name": "id-version1",
                              "label": "参数名称",
                              "width": 100,
                              "type": "group",
                              "direction": "vertical",
                              "body": getDiffCell('id-version1')
                            },
                            {
                              "name": "text1-version1",
                              "label": "数据类型",
                              "width": 100,
                              "type": "group",
                              "direction": "vertical",
                              "body": getDiffCell('text1-version1')
                            },
                            {
                              "name": "text4-version1",
                              "label": "是否必填",
                              "width": 100,
                              "type": "group",
                              "direction": "vertical",
                              "body": getDiffCell('text4-version1')
                            },
                            {
                              "name": "text5-version1",
                              "label": "说明",
                              "width": 100,
                              "type": "group",
                              "direction": "vertical",
                              "body": getDiffCell('text5-version1')
                            },

                          ],
                          "source": "${queryList1}",
                          "footerToolbar": [],
                          "title": '入模参数'
                        })
                      ],
                    }
                  ]
                }),
              ])
            })
          }),
          {
            "type": "tpl",
            "tpl": "",
            "style": {
              "textAlign": "center",
              "width": 40
            }
          },
          generateVersionDiffFlexFrow({
            "type": "panel",
            "title": "",
            "body": generateGroupForm({
              "type": 'form',
              "static": true,
              "withoutItemMarginBottom": true,
              "actions": [],
              "body": getDialogGroupPanelSchema([
                {
                  "type": 'panel',
                  "title": generateHeaderTpl({
                    "type": 'tpl',
                    "tpl": '基础信息'
                  }),
                  "body": [
                    {
                      type: 'group',
                      body: [
                        {
                          type: 'static-tpl',
                          name: 'text1',
                          label: '姓名',
                          columnRatio: 6,
                          tpl: getTpl('text1', 'text1Status')
                        },
                        {
                          type: 'static-tpl',
                          name: 'text2',
                          label: '年龄',
                          columnRatio: 6,
                          tpl: getTpl('text2', 'text2Status')
                        }
                      ]
                    },
                    {
                      type: "group",
                      body: [

                        {
                          type: "tooltip-wrapper",
                          content: "${text4}",
                          columnRatio: 6,
                          body: {
                            type: 'static-tpl',
                            name: 'text4',
                            label: '邮箱',
                            tpl: getTpl('text4', 'status')
                          },
                        },
                        {
                          type: 'static-tpl',
                          name: 'text5',
                          label: '电话',
                          tpl: getTpl('text5', 'text5Status'),
                          columnRatio: 6,
                        },
                      ]
                    },
                    {
                      type: "group",
                      body: [
                        {
                          type: 'static-tpl',
                          name: 'text7',
                          label: '其它',
                          columnRatio: 6,
                          tpl: getTpl('text7', 'text7Status')
                        }
                      ]
                    }
                  ]
                },
                generateVersionDiffPanel({
                  type: 'panel',
                  title: generateHeaderTpl({
                    type: 'tpl',
                    tpl: '入参'
                  }),
                  body: [
                    {
                      type: 'group',
                      body: [
                        {
                          type: 'static-tpl',
                          name: 'text1',
                          label: '资信token',
                        },
                        {
                          type: 'static-tpl',
                          name: 'text2',
                          label: '年龄',
                        }
                      ]
                    },
                    {
                      type: 'group',
                      body: [
                        getWithoutMarginsCRUDSchema({
                          "columns": [
                            {
                              "name": "key-version1",
                              "label": "参数key",
                              "type": "group",
                              "direction": "vertical",
                              "body": getDiffCell('key-version1', 'key-version2', 'keyStatus'),
                              "width": 100
                            },
                            {
                              "name": "id-version1",
                              "label": "参数名称",
                              "type": "group",
                              "direction": "vertical",
                              "body": getDiffCell('id-version1', 'id-version2', 'idStatus'),
                              "width": 100
                            },
                            {
                              "name": "text1-version2",
                              "label": "数据类型",
                              "type": "group",
                              "direction": "vertical",
                              "body": getDiffCell('text1-version1', 'text1-version2', 'text1Status'),
                              "width": 100,
                            },
                            {
                              "name": "text4-version1",
                              "label": "是否必填",
                              "type": "group",
                              "direction": "vertical",
                              "body": getDiffCell('text4-version1', 'text4-version2', 'text4Status'),
                              "width": 100
                            },
                            {
                              "name": "text5-version1",
                              "label": "说明",
                              "type": "group",
                              "direction": "vertical",
                              "body": getDiffCell('text5-version1', 'text5-version2', 'text5Status'),
                              "width": 100
                            },
                          ],
                          "source": "${queryList2}",
                          "footerToolbar": [],
                          "title": '入模参数'
                        }),
                      ],
                    }
                  ]
                }),
              ])
            })
          })
        ]
      },
    ]
  })
})
