import { getWithoutMarginsCRUDSchema, generateCommonPage, generateStyle, getDiffPageSchema } from "amis-utils";

export default generateCommonPage({
  "type": "page",

  "data": {
    "tableData": [
      {
        "fieldName": 'API ID',
        "baselineVersion": "12345",
        "diffVersion": "12345",
      },
      {
        "fieldName": 'API名称',
        "baselineVersion": "查询用户列表",
        "diffVersion": "查询用户列表",
      },
      {
        "fieldName": 'API状态',
        "baselineVersion": "在线",
        "diffVersion": "在线",
      },
      {
        "fieldName": 'Method',
        "baselineVersion": "GET",
        "diffVersion": "POST",
        "status": "EDIT"
      },
      {
        "fieldName": 'URL',
        "baselineVersion": "/users",
        "diffVersion": "/user-list",
        "status": "EDIT"
      },
      {
        "fieldName": '描述',
        "baselineVersion": "",
        "diffVersion": "查询用户列表",
        "status": "ADD"
      },
      {
        "fieldName": '补充',
        "baselineVersion": "查询用户列表",
        "diffVersion": "查询用户列表",
        "status": "DELETE"
      }
    ]
  },
  "body": getDiffPageSchema({
    "body": [
      {
        "type": 'form',
        "title": '',
        "actions": [],
        "mode": "horizontal",
        "wrapWithPanel": false,
        "labelWidth": 60,
        "body": generateStyle({
          "type": 'flex',
          "justify": "space-between",
          "alignItems": 'center',
          "items": [
            generateStyle({
              "type": "select",
              "label": "基准版本",
              "name": 'baselineVersion',
              "value": "v1",
              "options": [
                {
                  "label": 'V1',
                  "value": "v1"
                },
                {
                  "label": 'V2',
                  "value": "v2"
                }
              ]
            }, {
              "className": {
                'spacing': {
                  "margin": {
                    "bottom": 'none'
                  },

                },
                "flexBox": {
                  "grow": 1
                }
              }
            }),
            {
              "type": "plain",
              "text": "",
              "style": {
                "textAlign": "center",
                "width": 40
              }
            },
            generateStyle({
              "type": "select",
              "label": "对比版本",
              "name": 'diffVersion',
              "value": 'v2',
              "options": [
                {
                  "label": 'V1',
                  "value": "v1"
                },
                {
                  "label": 'V2',
                  "value": "v2"
                }
              ]
            }, {
              "className": {
                'spacing': {
                  "margin": {
                    "bottom": 'none'
                  },

                },
                "flexBox": {
                  "grow": 1
                }
              }
            })
          ]
        }, {
          "className": {
            'spacing': {
              "margin": {
                "bottom": 'md'
              }
            }
          }
        }),
      },
      getWithoutMarginsCRUDSchema({
        "columns": [
          {
            "name": "fieldName",
            "label": "字段名"
          },
          {
            "name": "baselineVersion",
            "label": "基准版本",
          },
          {
            "name": "diffVersion",
            "label": "对比版本",
            "type": "tpl",
            "tpl": "<span class='${status === 'ADD' ? 'pm-versionDiff-add' : (status === 'EDIT' ? 'pm-versionDiff-edit' : (status === 'DELETE' ? 'pm-versionDiff-delete' : ''))}'>${diffVersion}</span>"
          },
        ],
        "source": "${tableData}",
        "footerToolbar": []
      })
    ]
  })
})
