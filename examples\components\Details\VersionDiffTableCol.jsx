import { getBasicListSchemaV2,generateCommonPage } from 'amis-utils'

export default generateCommonPage({
  "type": "page",

  "body": getBasicListSchemaV2(
    {
      "type": "crud",

      "api": "/api/mock2/crud/table7",
      "columns": [
        {
          "name": "engine",
          "label": "Engine",
          "align": "left"
        },
        {
          "name": "grade",
          "label": "Grade",
          "align": "left"
        },
        {
          "name": "versionBefore",
          "label": "修改前",
          "groupName": "版本",
          "align": "left",
        },
        {
          "name": "versionAfter",
          "label": "修改后",
          "groupName": "版本",
          "type": "group",
          "direction": "vertical",
          "align": "left",
          "body": [
            {
              "type": "tpl",
              "tpl": "<span class='${versionAfterStatus === 'ADD' ? 'pm-versionDiff-add' : (versionAfterStatus === 'EDIT' ? 'pm-versionDiff-edit' : (versionAfterStatus === 'DELETE' ? 'pm-versionDiff-delete' : ''))}'>${versionAfter}</span>"
            },

          ]
        },
        {
          "name": "browserBefore",
          "label": "修改前",
          "groupName": "浏览器",
          "align": "left",
        },
        {
          "name": "browserAfter",
          "label": "修改后",
          "groupName": "浏览器",
          "align": "left",
          "type": "group",
          "direction": "vertical",
          "body": [
            {
              "type": "tpl",
              "tpl": "<span class='${browserAfterStatus === 'ADD' ? 'pm-versionDiff-add' : (browserAfterStatus === 'EDIT' ? 'pm-versionDiff-edit' : (browserAfterStatus === 'DELETE' ? 'pm-versionDiff-delete' : ''))}'>${browserAfter}</span>"
            }
          ]
        },
      ]
    })
})
