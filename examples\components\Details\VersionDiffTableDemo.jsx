import { getWithoutMarginsCRUDSchema,generateStyle,generateCommonPage, generateGroupForm, getDialogGroupPanelSchema, generateHeaderTpl, getDiffPageSchema } from "amis-utils";

const getDiffCell = (preName, name, status) => ([
  {
    "type": "tpl",
    "visibleOn": "${!" + status + "}",
    "tpl": "${" + name + "}"
  },
  {
    "type": "tpl",
    "visibleOn": "${" + status + " === 'ADD'}",
    "tpl": "<span class='pm-versionDiff-add'>${" + name + "}</span>"
  },
  {
    "type": "tpl",
    "visibleOn": "${" + status + " === 'DELETE'}",
    "tpl": "<span class='pm-versionDiff-delete'>${" + name + "}</span>"
  },
  {
    "type": "tpl",
    "visibleOn": "${" + status + " === 'EDIT'}",
    "tpl": "<span class='pm-diff-edit-preValue-text-color'>${" + preName + "}</span>&nbsp;&#8594;&nbsp;"
  },
  {
    "type": "tpl",
    "visibleOn": "${" + status + " === 'EDIT'}",
    "tpl": "<span class='pm-versionDiff-edit'>${" + name + "}</span>"
  },
])

const getDiffCellWithTag = (item) => {
  return ['grade', 'grade1', 'grade2', 'grade3'].map(item => {
    return [
      generateStyle({
      "type": "tooltip-wrapper",
      "placement": "left",
      "content": "${" + item + "}",
      "body": {
        "type": "tag",
        "displayMode": "bordered",
        "label": "${" + item + "}",
        "visibleOn": `this.${item + 'Status'} === 'inactive' && ${item}`,
        "color": "${" + item + "Status}",
      }
    },{
      "className":{
        "spacing":{
          "margin":{
            "bottom":"xs"
          }
        }
      }
    }),
    generateStyle({
      "type": "tooltip-wrapper",
      "placement": "left",
      "content": "${" + item + "}",
      "body": {
        "type": "tag",
        "displayMode": "bordered",
        "label": "${" + item + "}",
        "visibleOn": `this.${item + 'Status'} === 'success' && ${item }`,
        "color": "${" + item + "Status}",
      },
    },{
      "className":{
        "spacing":{
          "margin":{
            "bottom":"xs"
          }
        }
      }
    }),
    generateStyle({
      "type": "tooltip-wrapper",
      "placement": "left",
      "content": "${" + item + "}",
      "body": {
        "type": "tag",
        "displayMode": "bordered",
        "label": "${" + item + "}",
        "visibleOn": `this.${item + 'Status'} === 'error' && ${item}`,
        "style": {
          "textDecoration": "line-through",
        },
        "color": "${" + item + "Status}",
      },
    },{
      "className":{
        "spacing":{
          "margin":{
            "bottom":"xs"
          }
        }
      }
    })]
  });
};

export default generateCommonPage({
  "type": "page",
  "data": {
    "basicInfo": [
      {
        "changeType": "",
        "key-version1": "12345",
        "key-version2": "12345",
        "keyStatus": "",
        "id-version1": "查询用户列表",
        "id-version2": "查询用户列表",
        "idStatus": "",
        "text1Status": "",
        "text4Status": "EDIT",
        "text5Status": "DELETE",
        "text6Status": "ADD",
        "text1-version1": "在线",
        "text1-version2": "在线",
        "text4-version1": "GET",
        "text4-version2": "POST",
        "text5-version1": "/users",
        "text5-version2": "/users",
        "text6-version1": "",
        "text6-version2": "查询用户列表",
      }
    ],
    "queryList": [
      {
        "changeType": "",
        "key-version1": "k1",
        "key-version2": "k1",
        "keyStatus": "",
        "id-version1": "userId",
        "id-version2": "userId",
        "idStatus": "",
        "text1Status": "",
        "text4Status": "",
        "text5Status": "",
        "text1-version1": "string",
        "text1-version2": "string",
        "text4-version1": "是",
        "text4-version2": "是",
        "text5-version1": "用户Id",
        "text5-version2": "用户Id",
        "grade": "ABCD",
        "grade1": "ABCD",
        "grade2": "B",
        "grade3": "C",
        "gradeStatus": "inactive",
        "grade1Status": "inactive",
        "grade2Status": "inactive",
        "grade3Status": "inactive",
      },
      {
        "changeType": "增",
        "key-version1": "-",
        "key-version2": "k2",
        "keyStatus": "ADD",
        "id-version1": "",
        "id-version2": "userType",
        "idStatus": "ADD",
        "text1Status": "ADD",
        "text4Status": "ADD",
        "text5Status": "ADD",
        "text1-version1": "",
        "text1-version2": "string",
        "text4-version1": "",
        "text4-version2": "是",
        "text5-version1": "",
        "text5-version2": "用户类型",
        "grade": "A这里有更多撒金凤卡少女反馈加啊是南非进口三反馈加三分快三方面小美女你发怒发怒发怒",
        "grade1": "1232178346816421641326426147126473214143214",
        "grade2": "sadkjhsakdfhskahfkashfdksahfkasdksafkadsfkasf",
        "grade3": "sadkjhsakdfhskahfkashfdksahfkasdksafkadsfkasf",
        "gradeStatus": "success",
        "grade1Status": "success",
        "grade2Status": "success",
        "grade3Status": "success",
      },
      {
        "changeType": "改",
        "key-version1": "k3",
        "key-version2": "k3",
        "keyStatus": "",
        "id-version1": "userId",
        "id-version2": "userId",
        "idStatus": "",
        "text1Status": "EDIT",
        "text4Status": "",
        "text5Status": "",
        "text1-version1": "string",
        "text1-version2": "integer",
        "text4-version1": "是",
        "text4-version2": "是",
        "text5-version1": "用户Id",
        "text5-version2": "用户Id",
        "grade": "这里有更多撒金凤卡少女反馈加啊是南非进口三反馈加三分快三方面小美女你发怒发怒发怒",
        "grade1": "1232178346816421641326426147126473214143214",
        "grade2": "sadkjhsakdfhskahfkashfdksahfkasdksafkadsfkasf",
        "grade3": "23914729310",
        "gradeStatus": "error",
        "grade1Status": "error",
        "grade2Status": "success",
        "grade3Status": "success",
      },
      {
        "changeType": "删",
        "key-version1": "k4",
        "key-version2": "k4",
        "keyStatus": "DELETE",
        "id-version1": "userId",
        "id-version2": "userId",
        "idStatus": "DELETE",
        "text1Status": "DELETE",
        "text4Status": "DELETE",
        "text5Status": "DELETE",
        "text1-version1": "string",
        "text1-version2": "string",
        "text4-version1": "是",
        "text4-version2": "是",
        "text5-version1": "用户Id",
        "text5-version2": "用户Id",
        "grade": "这里有更多撒金凤卡少女反馈加啊是南非进口三反馈加三分快三方面小美女你发怒发怒发怒",
        "grade1": "1232178346816421641326426147126473214143214",
        "grade2": "sadkjhsakdfhskahfkashfdksahfkasdksafkadsfkasf",
        "gradeStatus": "error",
        "grade1Status": "error",
        "grade2Status": "error",
        "grade3Status": "error",
      },
    ]
  },
  "body": getDiffPageSchema({
    "body": [
      {
        "type": 'form',
        "title": '',
        "actions": [],
        "mode": "horizontal",
        "wrapWithPanel": false,
        "labelWidth": 60,
        "body": [
          {
            "type": "group",
            "body": [
              {
                "type": "radios",
                "value": "byKey",
                "name": "diffType",
                "label": "对比模式",
                "columnRatio": 4,
                "options": [
                  {
                    "label": "按参数Key",
                    "value": "byKey"
                  },
                  {
                    "label": "按参数Name",
                    "value": "byName"
                  }
                ]
              },
            ]
          },
          {
            "type": "group",
            "body": [
              {
                "type": "select",
                "label": "基准版本",
                "name": 'baselineVersion',
                "value": 'v1',
                "columnRatio": 4,
                "options": [
                  {
                    "label": 'V1',
                    "value": "v1"
                  },
                  {
                    "label": 'V2',
                    "value": "v2"
                  }
                ]
              },
              {
                "type": "select",
                "label": "对比版本",
                "name": 'diffVersion',
                "value": 'v2',
                "columnRatio": 4,
                "options": [
                  {
                    "label": 'V1',
                    "value": "v1"
                  },
                  {
                    "label": 'V2',
                    "value": "v2"
                  }
                ]
              },
            ]
          },
        ]
      },
      generateStyle({
        "type": "alert",
        "level": "info",
        "body": [
          {
            "type": "tpl",
            "tpl": "结论"
          },
          {
            "type": "remark",
            "content": "破坏性变更：对消费者影响严重；兼容性变更：对消费者影响轻微或无影响"
          },
          {
            "type": "tpl",
            "tpl": "："
          },
          {
            "type": "tpl",
            "tpl": "兼容性变更"
          },
        ]
      },{
        "className":{
          "spacing":{
            "margin":{
              "top":'md'
            }
          }
        }
      }),
      generateGroupForm({
        type: 'form',
        static: true,
        actions: [],
        body: getDialogGroupPanelSchema([
          {
            type: 'panel',
            title: generateHeaderTpl({
              type: 'tpl',
              tpl: '基础信息'
            }),
            body: [
              getWithoutMarginsCRUDSchema(
                generateStyle({
                "source": "$basicInfo",
                "columns": [
                  {
                    "name": "key-version1",
                    "label": "API ID",
                    "type": "group",
                    "direction": "vertical",
                    "body": getDiffCell('key-version1', 'key-version2', 'keyStatus')
                  },
                  {
                    "name": "id-version1",
                    "label": "API名称",
                    "type": "group",
                    "direction": "vertical",
                    "body": getDiffCell('id-version1', 'id-version2', 'idStatus')
                  },
                  {
                    "name": "text1-version2",
                    "label": "API 状态",
                    "type": "group",
                    "direction": "vertical",
                    "body": getDiffCell('text1-version1', 'text1-version2', 'text1Status')
                  },
                  {
                    "name": "text4-version1",
                    "label": "Method",
                    "type": "group",
                    "direction": "vertical",
                    "body": getDiffCell('text4-version1', 'text4-version2', 'text4Status')
                  },
                  {
                    "name": "text5-version1",
                    "label": "URL",
                    "type": "group",
                    "direction": "vertical",
                    "body": [
                      {
                        "type": "tpl",
                        "visibleOn": "${text5Status !== 'EDIT'}",
                        "tpl": "<span class='${text5Status === 'ADD' ? 'pm-versionDiff-add' : (text5Status === 'DELETE' ? 'pm-versionDiff-delete  pm-versionDiff-delete' : '')}'>${text5-version2}</span>"
                      },
                      {
                        "type": "tpl",
                        "visibleOn": "${text5Status === 'EDIT' }",
                        "tpl": "修改前：<span>${text5-version1}</span></br>"
                      },
                      {
                        "type": "tpl",
                        "visibleOn": "${text5Status === 'EDIT' }",
                        "tpl": "修改后：<span class='pm-versionDiff-edit'>${text5-version2}</span>"
                      },
                    ]
                  },
                  {
                    "name": "text6-version1",
                    "label": "描述",
                    "type": "group",
                    "direction": "vertical",
                    "body": getDiffCell('text6-version1', 'text6-version2', 'text6Status')
                  },
                ],
                "footerToolbar": []
              },{
                "className":{
                  "spacing":{
                    "padding":{
                      "left":"md"
                    }
                  }
                }
              }))
            ]
          },
          {
            type: 'panel',
            title: generateHeaderTpl({
              type: 'tpl',
              tpl: '请求参数'
            }),
            body: [
              getWithoutMarginsCRUDSchema(
                generateStyle({
                title: "Header(请求头参数)",
                "columns": [
                  {
                    "name": "key-version1",
                    "label": "参数key",
                    "type": "group",
                    "direction": "vertical",
                    "body": getDiffCell('key-version1', 'key-version2', 'keyStatus')
                  },
                  {
                    "name": "id-version1",
                    "label": "参数名称",
                    "type": "group",
                    "direction": "vertical",
                    "body": getDiffCell('id-version1', 'id-version2', 'idStatus')
                  },
                  {
                    "name": "text1-version2",
                    "label": "数据类型",
                    "type": "group",
                    "direction": "vertical",
                    "body": getDiffCell('text1-version1', 'text1-version2', 'text1Status')
                  },
                  {
                    "name": "text4-version1",
                    "label": "是否必填",
                    "type": "group",
                    "direction": "vertical",
                    "body": getDiffCell('text4-version1', 'text4-version2', 'text4Status')
                  },
                  {
                    "name": "text5-version1",
                    "label": "说明",
                    "type": "group",
                    "direction": "vertical",
                    "body": getDiffCell('text5-version1', 'text5-version2', 'text5Status')
                  },
                  {
                    "name": "tag-version1",
                    "label": "标签",
                    "type": "flex",
                    "direction": "column",
                    "width": "160px",
                    "alignItems": "flex-start",
                    "justify": "flex-start",
                    "items": getDiffCellWithTag()
                  },
                ],
                "source": "${queryList}",
                "footerToolbar": []
              },{
                "className":{
                  "spacing":{
                    "padding":{
                      "left":"md"
                    }
                  }
                }
              })),
              getWithoutMarginsCRUDSchema(
                generateStyle({
                title: "Cookie(缓存参数)",
                "columns": [
                  {
                    "name": "key-version1",
                    "label": "参数key",
                    "type": "group",
                    "direction": "vertical",
                    "body": getDiffCell('key-version1', 'key-version2', 'keyStatus')
                  },
                  {
                    "name": "id-version1",
                    "label": "参数名称",
                    "type": "group",
                    "direction": "vertical",
                    "body": getDiffCell('id-version1', 'id-version2', 'idStatus')
                  },
                  {
                    "name": "text1-version2",
                    "label": "数据类型",
                    "type": "group",
                    "direction": "vertical",
                    "body": getDiffCell('text1-version1', 'text1-version2', 'text1Status')
                  },
                  {
                    "name": "text4-version1",
                    "label": "是否必填",
                    "type": "group",
                    "direction": "vertical",
                    "body": getDiffCell('text4-version1', 'text4-version2', 'text4Status')
                  },
                  {
                    "name": "text5-version1",
                    "label": "说明",
                    "type": "group",
                    "direction": "vertical",
                    "body": getDiffCell('text5-version1', 'text5-version2', 'text5Status')
                  },
                  {
                    "name": "tag-version1",
                    "label": "标签",
                    "type": "flex",
                    "direction": "column",
                    "width": "160px",
                    "alignItems": "flex-start",
                    "justify": "flex-start",
                    "items": getDiffCellWithTag()
                  },
                ],
                "source": "${queryList}",
                "footerToolbar": []
              },{
                "className":{
                  "spacing":{
                    "padding":{
                      "left":'md'
                    }
                  }
                }
              })),
            ]
          },
          {
            type: 'panel',
            title: generateHeaderTpl({
              type: 'tpl',
              tpl: '响应参数'
            }),
            body: [
              getWithoutMarginsCRUDSchema(
                generateStyle({
                title: "Header(请求头参数)",
                "columns": [
                  {
                    "name": "key-version1",
                    "label": "参数key",
                    "type": "group",
                    "direction": "vertical",
                    "body": getDiffCell('key-version1', 'key-version2', 'keyStatus')
                  },
                  {
                    "name": "id-version1",
                    "label": "参数名称",
                    "type": "group",
                    "direction": "vertical",
                    "body": getDiffCell('id-version1', 'id-version2', 'idStatus')
                  },
                  {
                    "name": "text1-version2",
                    "label": "数据类型",
                    "type": "group",
                    "direction": "vertical",
                    "body": getDiffCell('text1-version1', 'text1-version2', 'text1Status')
                  },
                  {
                    "name": "text4-version1",
                    "label": "是否必填",
                    "type": "group",
                    "direction": "vertical",
                    "body": getDiffCell('text4-version1', 'text4-version2', 'text4Status')
                  },
                  {
                    "name": "text5-version1",
                    "label": "说明",
                    "type": "group",
                    "direction": "vertical",
                    "body": getDiffCell('text5-version1', 'text5-version2', 'text5Status')
                  },
                ],
                "source": "${queryList}",
                "footerToolbar": []
              },{
                "className":{
                  "spacing":{
                    "padding":{
                      "left":'md'
                    }
                  }
                }
              }
            )),
              getWithoutMarginsCRUDSchema(
                generateStyle({
                title: "Cookie(缓存参数)",

                "columns": [
                  {
                    "name": "key-version1",
                    "label": "参数key",
                    "type": "group",
                    "direction": "vertical",
                    "body": getDiffCell('key-version1', 'key-version2', 'keyStatus')
                  },
                  {
                    "name": "id-version1",
                    "label": "参数名称",
                    "type": "group",
                    "direction": "vertical",
                    "body": getDiffCell('id-version1', 'id-version2', 'idStatus')
                  },
                  {
                    "name": "text1-version2",
                    "label": "数据类型",
                    "type": "group",
                    "direction": "vertical",
                    "body": getDiffCell('text1-version1', 'text1-version2', 'text1Status')
                  },
                  {
                    "name": "text4-version1",
                    "label": "是否必填",
                    "type": "group",
                    "direction": "vertical",
                    "body": getDiffCell('text4-version1', 'text4-version2', 'text4Status')
                  },
                  {
                    "name": "text5-version1",
                    "label": "说明",
                    "type": "group",
                    "direction": "vertical",
                    "body": getDiffCell('text5-version1', 'text5-version2', 'text5Status')
                  },
                ],
                "source": "${queryList}",
                "footerToolbar": []
              },{
                "className":{
                  "spacing":{
                    "padding":{
                      "left":'md'
                    }
                  }
                }
              })),
            ]
          }
        ])
      })
    ]
  })
})
