import { generateCommonPage, generateBasicFormV2,generateHeaderV2,generateStyle } from 'amis-utils'

export default generateCommonPage(
  generateStyle({
  type: 'page',
  "data": {
    department: 'department',
    platform: 'platform',
    css: "css",
    browser: "browser",
    selected: "a",
    department1: 'department1',
    platform1: 'platform1',
    css1: "css1",
    browser1: "browser1",
    selected1: "b",
    remark: "备注",
    remark1: "备注"

  },
  body: [
    generateHeaderV2({
      title: "页面大标题名称大标题名称",
      subtitle: "我是小标题",
      iconConfig: {
        icon: 'chevron-left',
      },
      actions: [
        {
          type: 'button',
          label: '取消',
          onEvent: {
            click: {
              actions: [
                {
                  actionType: "toast",
                  args: {
                    msgType: "info",
                    msg: "响应取消操作"
                  }
                }
              ]
            }
          }
        },
        {
          type: 'button',
          level: 'primary',
          label: '保存',
          onEvent: {
            click: {
              actions: [
                {
                  componentId: "myForm",
                  actionType: "submit"
                }
              ]
            }
          }
        },
      ],
    }),
    generateBasicFormV2({
      title: '',
      api: '/api/mock2/saveForm?waitSeconds=2',
      id: "myForm",
      actions: [],
      static: true,

      body: [
        {
          type: "group",
          body: [
            {
              type: 'select',
              name: 'department',
              label: '归属部门',
            },
            {
              type: 'input-text',
              name: 'platform',
              label: 'Platform',
              placeholder: "请输入",
            },
            {
              type: 'input-text',
              name: 'css',
              label: 'CSS',
              required: true,
              placeholder: "请输入",
            },
          ]
        },
        {
          type: "group",
          body: [
            {
              type: 'input-text',
              name: 'browser',
              label: 'Browser',
              placeholder: "请输入",
            },
            {
              type: 'select',
              name: 'selected',
              label: '用户选择',
              placeholder: "请选择",
              options: [
                {
                  label: 'a',
                  value: 'a'
                },
                {
                  label: 'b',
                  value: 'b'
                }
              ]
            },
            {
              type: 'input-text',
              name: 'browser',
              label: 'Browser',
              placeholder: "请输入",
            },
          ]
        },
        {
          type: "group",
          body: [
            {
              type: 'select',
              name: 'department1',
              label: '归属部门1',
            },
            {
              type: 'input-text',
              name: 'platform1',
              label: 'Platform1',
              placeholder: "请输入",
            },
            {
              type: 'input-text',
              name: 'css1',
              label: 'CSS1',
              required: true,
              placeholder: "请输入",
            },
          ]
        },
        {
          type: "group",
          body: [
            {
              type: 'input-text',
              name: 'browser1',
              label: 'Browser1',
              placeholder: "请输入",
            },
            {
              type: 'select',
              name: 'selected1',
              label: '用户选择1',
              placeholder: "请选择",
              options: [
                {
                  label: 'a',
                  value: 'a'
                },
                {
                  label: 'b',
                  value: 'b'
                }
              ]
            },
            {
              type: 'input-text',
              name: 'browser1',
              label: 'Browser1',
              placeholder: "请输入",
            },
          ]
        },
        {
          type: "group",
          body: [
            {
              type: 'textarea',
              name: 'remark',
              label: '备注',
              showCounter: true,
              maxLength: 30,
              placeholder: "请输入",
              trimContents: true
            },
          ]
        },
        {
          type: 'group',
          body: [
            {
              type: 'input-rich-text',
              name: 'tip1',
              label: '底部提示1',
              placeholder: "请输入",
            }
          ]
        },
        {
          type: "group",
          body: [
            {
              type: 'textarea',
              name: 'remark1',
              label: '备注1',
              showCounter: true,
              maxLength: 30,
              placeholder: "请输入",
              trimContents: true
            },
          ]
        },
        {
          type: 'group',
          body: [
            {
              type: 'input-rich-text',
              name: 'tip',
              label: '底部提示',
              placeholder: "请输入",
            }
          ]
        }
      ]
    })
  ]
},{
  "bodyClassName":{
    "layout":{
      "overflow": {
        "y": "scroll"
      }
    }
  }
}));
