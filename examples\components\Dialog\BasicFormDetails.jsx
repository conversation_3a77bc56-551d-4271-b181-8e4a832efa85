import { getMiddleSizeDialogSchema, generateBasicForm, getButtonList } from 'amis-utils';


export default {
  type: 'page',
  "data": {
    "text1": "营销中心",
    "text2": 2,
    "text3": 1593327764,
    "text4": "负责人",
    "text5": "text5",
    "text6": 1593327764,
    "text7": "创建人",
    "text8": "text8",
    "department": "营销中心",
    "platform": 2,
    "css": 1593327764,
    "browser": "负责人",
    "selected": "text5",
    "browser2": 1593327764,
    "remark": "创建人",
    "tip": "text8",
    "text9": "text9",
  },
  body: getButtonList([{
    type: 'button',
    label: '详情(基础表单)(中号)',
    actionType: 'dialog',
    dialog: getMiddleSizeDialogSchema({
      title: '详情(基础表单)',
      actions: [],
      body: generateBasicForm({
        title: '',
        static: true,
        api: '/api/mock2/saveForm?waitSeconds=2',
        labelWidth: 60,
        actions: [],
        body: [
          {
            type: "group",
            body: [
              {
                type: 'select',
                name: 'department',
                label: '归属部门',
              },
              {
                type: 'input-text',
                name: 'platform',
                label: 'Platform',
                placeholder: "请输入",
              },
            ]
          },
          {
            type: "group",
            body: [
              {
                type: 'input-text',
                name: 'css',
                label: 'CSS',
                required: true,
                placeholder: "请输入",
              },
              {
                type: 'input-text',
                name: 'browser',
                label: 'Browser',
                placeholder: "请输入",
              },
            ]
          },
          {
            type: "group",
            body: [
              {
                type: 'select',
                name: 'selected',
                label: '用户选择',
                placeholder: "请选择",
                options: [
                  {
                    label: 'a',
                    value: 'a'
                  },
                  {
                    label: 'b',
                    value: 'b'
                  }
                ]
              },
              {
                type: 'input-text',
                name: 'browser2',
                label: 'Browser',
                placeholder: "请输入",
              },
            ]
          },
          {
            type: "group",
            body: [
              {
                type: 'textarea',
                name: 'remark',
                label: '备注',
                showCounter: true,
                maxLength: 30,
                placeholder: "请输入",
                trimContents: true
              },
            ]
          },
        ]
      })
    })
  }]),
};
