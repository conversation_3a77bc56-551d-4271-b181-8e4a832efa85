import { getButtonList ,generateTextStyle} from 'amis-utils'

export default {
  type: 'page',
  body: getButtonList([{
    type: 'button',
    label: '标准-基础表单',
    actionType: 'dialog',
    dialog: {
      showErrorMsg: false,
      title: [
        generateTextStyle({
          type: 'tpl',
          tpl: '标准-基础表单',
          textStyle: {
            weight: 'medium'
          }
        }),
        {
          type: 'remark',
          content: '这是一段提示',
        },
      ],
      showCloseButton: false,
      body: {
        type: 'form',
        api: '/api/mock2/form/saveForm?waitSeconds=2',
        body: [
          {
            type: 'input-text',
            name: 'platform',
            placeholder: '请输入PlatForm(s)',
            label: 'PlatForm(s)'
          },
          {
            type: 'input-text',
            name: 'cssGrade',
            label: 'CSS grade',
            required: true,
            placeholder: '请输入CSS grade'
          },
          {
            type: 'input-text',
            name: 'brower',
            placeholder: '请输入Brower',
            label: 'Brower'
          },
          {
            type: 'input-text',
            name: 'version',
            label: 'Version',
            required: true,
            placeholder: '请输入Version'
          }
        ]
      }
    }
  },
  ])
};
