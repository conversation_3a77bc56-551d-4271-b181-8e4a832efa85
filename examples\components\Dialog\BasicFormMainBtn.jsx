import { getButtonList } from 'amis-utils'

export default {
  "type": "page",
  "body": getButtonList([{
    "type": "button",
    label: "标准-基础表单(只有一个主按钮)",
    actionType: "dialog",
    dialog: {
      showErrorMsg: false,
      title: "标准-基础表单(只有一个主按钮)",
      showCloseButton: false,
      actions: [
        {
          type: 'button',
          actionType: 'cancel',
          label: '取消',

        }, {
          type: 'button',
          actionType: 'confirm',
          label: '确认',

        }, {
          type: 'button',
          actionType: 'confirm',
          primary: true,
          label: '下一步'
        }
      ],
      body: {
        type: "form",
        api: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm?waitSeconds=2",
        body: [
          {
            type: "input-text",
            name: "platform",
            placeholder: "请输入PlatForm(s)",
            label: "PlatForm(s)"
          },
          {
            type: "input-text",
            name: "cssGrade",
            label: "CSS grade",
            required: true,
            placeholder: "请输入CSS grade"
          },
          {
            type: "input-text",
            name: "brower",
            placeholder: "请输入Brower",
            label: "Brower"
          },
          {
            type: "input-text",
            name: "version",
            label: "Version",
            required: true,
            placeholder: "请输入Version"
          }
        ]
      }
    }
  }])
}
