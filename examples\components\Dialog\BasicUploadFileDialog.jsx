import { getButtonList, generateUploadFile } from 'amis-utils';

export default {
  type: 'page',
  body: getButtonList([{
    type: 'button',
    label: '文件上传',
    actionType: 'dialog',
    dialog: {
      title: '文件上传',
      showCloseButton: false,
      body: {
        type: 'form',
        api: '/api/mock2/form/saveForm?waitSeconds=2',
        body: [
          {
            type: "group",
            body: [
              {
                type: 'radios',
                name: 'radios',
                required: true,
                label: '选择填充方式',
                value: 'file',
                options: [
                  {
                    label: "",
                    value: "file",
                    extraLabel: {
                      type: "wrapper",
                      size: "none",
                      body: [
                        {
                          type: "tpl",
                          tpl: "导入文件"
                        },
                        {
                          "type": "remark",
                          "content": "请上传符合规范的文件类型"
                        }
                      ]
                    }
                  }
                ]
              }
            ]
          },
          {
            type: "group",
            body: [
              generateUploadFile([
                {
                  type: 'input-file',
                  name: 'file',
                  label: '上传文件',
                  hideUploadButton: true,
                  required: true,
                  receiver: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/upload/file"
                },
                {
                  type: "button",
                  level: "link",
                  linkWithoutPadding: true,
                  label: "下载模版"
                }
              ])
            ]
          }
        ]
      }
    }
  }])
};
