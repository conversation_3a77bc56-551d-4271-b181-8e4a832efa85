import { getImagesDialogSchema ,getButtonList} from 'amis-utils';

/* mock少量图片数据 */
const fewImageData = [
  "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
]

/* mock超过一行的图片数据 */
const imageData = [
  "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
  "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
  "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
  "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg@s_0,w_216,l_1,f_jpg,q_80",
  "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_216,l_1,f_jpg,q_80",
  "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
  "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
  "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
  "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg@s_0,w_216,l_1,f_jpg,q_80",
  "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_216,l_1,f_jpg,q_80",
  "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
  "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
  "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
  "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg@s_0,w_216,l_1,f_jpg,q_80",
  "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_216,l_1,f_jpg,q_80",
]

export default {
  type: 'page',
  data: {
    imageData,
    fewImageData
  },
  body: getButtonList([
    {
      type: 'button',
      label: '中号弹窗-不满一行',
      actionType: 'dialog',
      dialog: getImagesDialogSchema({
        size: 'lg',
        source:"${fewImageData}",
        title: '图片集-中号弹窗',
        imageList:fewImageData
      })
    },
    {
      type: 'button',
      label: '中号弹窗-超出一行',
      actionType: 'dialog',
      dialog: getImagesDialogSchema({
        size: 'lg',
        source:"${imageData}",
        title: '图片集-中号弹窗'
      })
    },
    {
      type: 'button',
      label: '中号弹窗-空',
      actionType: 'dialog',
      dialog: getImagesDialogSchema({
        size: 'lg',
        source: '${null}',
        title: '图片集-中号弹窗'
      })
    },
    // 由于大号弹窗目前在某些屏幕下有问题，因此先支持中号
    // {
    //   type: 'button',
    //   className: 'mr-2',
    //   label: '大号弹窗-不满一行',
    //   actionType: 'dialog',
    //   dialog: getImagesDialogSchema({
    //     size: 'xl',
    //     imageList: fewImageData,
    //     title: '图片集-大号弹窗'
    //   })
    // },
    // {
    //   type: 'button',
    //   className: 'mr-2',
    //   label: '大号弹窗-超出一行',
    //   actionType: 'dialog',
    //   dialog: getImagesDialogSchema({
    //     size: 'xl',
    //     imageList: imageData,
    //     title: '图片集-大号弹窗'
    //   })
    // }
  ])
}
