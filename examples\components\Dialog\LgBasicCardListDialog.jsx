import { getButtonList, generateStyle, generateMediaImageCard, getMiddleSizeDialogSchema, generateGroupForm, getCard, generateHeaderTitle, getDialogGroupPanelSchemaV2, getDialogGroupPanelNoPaddingSchema } from 'amis-utils';
/**
 * 弹窗 - 卡片组件
 * 使用中号弹窗 一行三个卡片
 * */
export default {
  type: 'page',
  data: {
    items2: [{
      title: '复合指标',
      subTitle: '多个基础指标或派生指标的四则运算，如：人均交易额=交易金额/交易用户数',
      avatar: '../../../examples/static/card-demo3.png'
    }, {
      title: '基础指标',
      subTitle: '基于业务度量字段进行聚合，如：交易金额',
      avatar: '../../../examples/static/card-demo3.png'
    },
    {
      title: '派生指标',
      subTitle: '基于基础指标、时间限定、业务限定和衍生方式定义的指标',
      avatar: '../../../examples/static/card-demo3.png'
    }
    ],
    "linkArr": [
      {
        "title": "资方额度",
        "url": "http://moka.dmz.dev.caijj.net/dataseeddesigndocui/#/amis/zh-CN/components/card"
      },
      {
        "title": "对客额度上限",
        "url": "http://moka.dmz.dev.caijj.net/dataseeddesigndocui/#/amis/zh-CN/components/card"
      }
    ],
    "itemLists": [
      {
        "title": "资方额度",
        // "subTitle": "这是一段副标题文案",
        isLike: false
      },
      {
        "title": "对客额度上限",
        // "subTitle": "这是一段副标题文案",
        isLike: false
      },
      {
        "title": "主标题",
        // "subTitle": "这是一段副标题文案",
        isLike: false
      },
      {
        "title": "主标题",
        // "subTitle": "这是一段副标题文案这是一段副标题文案",
        isLike: false,
      },
      {
        "title": "主标题",
        // "subTitle": "这是一段副标题文案这是一段副标题文案",
        isLike: false,
      },
      {
        "title": "主标题423424撒大大的大萨达撒",
        // "subTitle": "这是一段副标题文案这是一段副标题文案",
        isLike: false,
      }
    ],
    2: [{
      title: '主标题',
      subTitle: '这是一段副标题文案这是一段副标题文案',
      avatar: 'data:image/svg+xml,%3C%3Fxml version=\'1.0\' standalone=\'no\'%3F%3E%3C!DOCTYPE svg PUBLIC \'-//W3C//DTD SVG 1.1//EN\' \'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\'%3E%3Csvg t=\'1631083237695\' class=\'icon\' viewBox=\'0 0 1024 1024\' version=\'1.1\' xmlns=\'http://www.w3.org/2000/svg\' p-id=\'2420\' xmlns:xlink=\'http://www.w3.org/1999/xlink\' width=\'1024\' height=\'1024\'%3E%3Cdefs%3E%3Cstyle type=\'text/css\'%3E%3C/style%3E%3C/defs%3E%3Cpath d=\'M959.872 128c0.032 0.032 0.096 0.064 0.128 0.128v767.776c-0.032 0.032-0.064 0.096-0.128 0.128H64.096c-0.032-0.032-0.096-0.064-0.128-0.128V128.128c0.032-0.032 0.064-0.096 0.128-0.128h895.776zM960 64H64C28.8 64 0 92.8 0 128v768c0 35.2 28.8 64 64 64h896c35.2 0 64-28.8 64-64V128c0-35.2-28.8-64-64-64z\' p-id=\'2421\' fill=\'%23bfbfbf\'%3E%3C/path%3E%3Cpath d=\'M832 288c0 53.024-42.976 96-96 96s-96-42.976-96-96 42.976-96 96-96 96 42.976 96 96zM896 832H128V704l224-384 256 320h64l224-192z\' p-id=\'2422\' fill=\'%23bfbfbf\'%3E%3C/path%3E%3C/svg%3E'
    }, {
      title: '主标题',
      subTitle: '这是一段副标题文案',
      avatar: 'data:image/svg+xml,%3C%3Fxml version=\'1.0\' standalone=\'no\'%3F%3E%3C!DOCTYPE svg PUBLIC \'-//W3C//DTD SVG 1.1//EN\' \'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\'%3E%3Csvg t=\'1631083237695\' class=\'icon\' viewBox=\'0 0 1024 1024\' version=\'1.1\' xmlns=\'http://www.w3.org/2000/svg\' p-id=\'2420\' xmlns:xlink=\'http://www.w3.org/1999/xlink\' width=\'1024\' height=\'1024\'%3E%3Cdefs%3E%3Cstyle type=\'text/css\'%3E%3C/style%3E%3C/defs%3E%3Cpath d=\'M959.872 128c0.032 0.032 0.096 0.064 0.128 0.128v767.776c-0.032 0.032-0.064 0.096-0.128 0.128H64.096c-0.032-0.032-0.096-0.064-0.128-0.128V128.128c0.032-0.032 0.064-0.096 0.128-0.128h895.776zM960 64H64C28.8 64 0 92.8 0 128v768c0 35.2 28.8 64 64 64h896c35.2 0 64-28.8 64-64V128c0-35.2-28.8-64-64-64z\' p-id=\'2421\' fill=\'%23bfbfbf\'%3E%3C/path%3E%3Cpath d=\'M832 288c0 53.024-42.976 96-96 96s-96-42.976-96-96 42.976-96 96-96 96 42.976 96 96zM896 832H128V704l224-384 256 320h64l224-192z\' p-id=\'2422\' fill=\'%23bfbfbf\'%3E%3C/path%3E%3C/svg%3E'
    }, {
      title: '主标题',
      subTitle: '这是一段副标题文案',
      avatar: 'data:image/svg+xml,%3C%3Fxml version=\'1.0\' standalone=\'no\'%3F%3E%3C!DOCTYPE svg PUBLIC \'-//W3C//DTD SVG 1.1//EN\' \'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\'%3E%3Csvg t=\'1631083237695\' class=\'icon\' viewBox=\'0 0 1024 1024\' version=\'1.1\' xmlns=\'http://www.w3.org/2000/svg\' p-id=\'2420\' xmlns:xlink=\'http://www.w3.org/1999/xlink\' width=\'1024\' height=\'1024\'%3E%3Cdefs%3E%3Cstyle type=\'text/css\'%3E%3C/style%3E%3C/defs%3E%3Cpath d=\'M959.872 128c0.032 0.032 0.096 0.064 0.128 0.128v767.776c-0.032 0.032-0.064 0.096-0.128 0.128H64.096c-0.032-0.032-0.096-0.064-0.128-0.128V128.128c0.032-0.032 0.064-0.096 0.128-0.128h895.776zM960 64H64C28.8 64 0 92.8 0 128v768c0 35.2 28.8 64 64 64h896c35.2 0 64-28.8 64-64V128c0-35.2-28.8-64-64-64z\' p-id=\'2421\' fill=\'%23bfbfbf\'%3E%3C/path%3E%3Cpath d=\'M832 288c0 53.024-42.976 96-96 96s-96-42.976-96-96 42.976-96 96-96 96 42.976 96 96zM896 832H128V704l224-384 256 320h64l224-192z\' p-id=\'2422\' fill=\'%23bfbfbf\'%3E%3C/path%3E%3C/svg%3E'
    }, {
      title: '主标题',
      subTitle: '这是一段副标题文案这是一段副标题文案',
      avatar: 'data:image/svg+xml,%3C%3Fxml version=\'1.0\' standalone=\'no\'%3F%3E%3C!DOCTYPE svg PUBLIC \'-//W3C//DTD SVG 1.1//EN\' \'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\'%3E%3Csvg t=\'1631083237695\' class=\'icon\' viewBox=\'0 0 1024 1024\' version=\'1.1\' xmlns=\'http://www.w3.org/2000/svg\' p-id=\'2420\' xmlns:xlink=\'http://www.w3.org/1999/xlink\' width=\'1024\' height=\'1024\'%3E%3Cdefs%3E%3Cstyle type=\'text/css\'%3E%3C/style%3E%3C/defs%3E%3Cpath d=\'M959.872 128c0.032 0.032 0.096 0.064 0.128 0.128v767.776c-0.032 0.032-0.064 0.096-0.128 0.128H64.096c-0.032-0.032-0.096-0.064-0.128-0.128V128.128c0.032-0.032 0.064-0.096 0.128-0.128h895.776zM960 64H64C28.8 64 0 92.8 0 128v768c0 35.2 28.8 64 64 64h896c35.2 0 64-28.8 64-64V128c0-35.2-28.8-64-64-64z\' p-id=\'2421\' fill=\'%23bfbfbf\'%3E%3C/path%3E%3Cpath d=\'M832 288c0 53.024-42.976 96-96 96s-96-42.976-96-96 42.976-96 96-96 96 42.976 96 96zM896 832H128V704l224-384 256 320h64l224-192z\' p-id=\'2422\' fill=\'%23bfbfbf\'%3E%3C/path%3E%3C/svg%3E'
    }, {
      title: '主标题',
      subTitle: '这是一段副标题文案这是一段副标题文案',
      avatar: 'data:image/svg+xml,%3C%3Fxml version=\'1.0\' standalone=\'no\'%3F%3E%3C!DOCTYPE svg PUBLIC \'-//W3C//DTD SVG 1.1//EN\' \'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\'%3E%3Csvg t=\'1631083237695\' class=\'icon\' viewBox=\'0 0 1024 1024\' version=\'1.1\' xmlns=\'http://www.w3.org/2000/svg\' p-id=\'2420\' xmlns:xlink=\'http://www.w3.org/1999/xlink\' width=\'1024\' height=\'1024\'%3E%3Cdefs%3E%3Cstyle type=\'text/css\'%3E%3C/style%3E%3C/defs%3E%3Cpath d=\'M959.872 128c0.032 0.032 0.096 0.064 0.128 0.128v767.776c-0.032 0.032-0.064 0.096-0.128 0.128H64.096c-0.032-0.032-0.096-0.064-0.128-0.128V128.128c0.032-0.032 0.064-0.096 0.128-0.128h895.776zM960 64H64C28.8 64 0 92.8 0 128v768c0 35.2 28.8 64 64 64h896c35.2 0 64-28.8 64-64V128c0-35.2-28.8-64-64-64z\' p-id=\'2421\' fill=\'%23bfbfbf\'%3E%3C/path%3E%3Cpath d=\'M832 288c0 53.024-42.976 96-96 96s-96-42.976-96-96 42.976-96 96-96 96 42.976 96 96zM896 832H128V704l224-384 256 320h64l224-192z\' p-id=\'2422\' fill=\'%23bfbfbf\'%3E%3C/path%3E%3C/svg%3E'
    }],
  },
  body: getButtonList([{
    type: 'button',
    label: '卡片列表',
    actionType: 'dialog',
    dialog: getMiddleSizeDialogSchema({
      title: '卡片列表',
      actions: [],
      body: {
        type: 'cards',
        columnsCount: 3,
        source: '${items2}',
        card: getCard({
          header: {
            title: '${title}',
            subTitle: '${subTitle}',
            avatar: '${avatar}'
          },
          itemAction: {
            type: 'button',
            actionType: 'url',
            url: '/dataseeddesigndocui/#/amis/zh-CN/components/card',
            blank: true
          }
        })
      }
    })
  },
  {
    type: 'button',
    label: '图片比例可配置卡片列表',
    actionType: 'dialog',
    dialog: getMiddleSizeDialogSchema({
      title: '卡片列表',
      actions: [],
      body: {
        type: 'cards',
        columnsCount: 3,
        source: '${items2}',
        card: generateMediaImageCard({
          imageRatio: "4:3", // 仅支持4:3， 3:4， 1:1，默认 为1:1
          header: {
            title: '${title}',
            subTitle: '${subTitle}',
          },
          media: {
            type: "image",
            url: '${avatar}',
            position: "left"
          },
          itemAction: {
            type: 'button',
            actionType: 'url',
            url: '/dataseeddesigndocui/#/amis/zh-CN/components/card',
            blank: true
          }
        })
      }
    })
  },
  {

    "label": "弹窗跳转带收藏功能",
    "type": "button",
    "actionType": "dialog",
    "dialog":
    {
      "title": "弹框标题",
      "size": "lg",
      "actions": [],
      "body": generateGroupForm(
        {
          "mode": "normal",
          "title": "",
          "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
          "actions": [],
          "type": "form",
          "body": getDialogGroupPanelNoPaddingSchema([
            {
              "type": "panel",
              "body": [
                {
                  "type": "flex",
                  "justify": "flex-start",
                  "alignItems": "flex-start",
                  "items": [
                    {
                      "type": "tpl",
                      "tpl": "我收藏的："
                    },
                    {
                      "type": "each",
                      "name": "linkArr",
                      "items": [
                        generateStyle(
                          {
                            "type": "link",
                            "href": "${url}",
                            "body": "${title}",
                          },
                          {
                            "className": {
                              "spacing": {
                                "margin": {
                                  "right": "md"
                                }
                              },
                              "layout": {
                                "display": "inline-block"
                              }
                            }
                          }
                        )
                      ]
                    }
                  ]
                }
              ],
            },
            {
              "type": "panel",
              "title": generateHeaderTitle({
                "type": "tpl",
                "tpl": "商务条件调整",
              }),
              "body": [
                {
                  "type": "cards",
                  "columnsCount": 5,
                  "source": "${itemLists}",
                  "card": generateStyle(
                    getCard({
                      "header": {
                        "title": "${title}",
                        "subTitle": "${subTitle}",
                      },
                      "toolbar": [
                        generateStyle(
                          {
                            "type": "button",
                            "icon": "fa fa-star-o",
                            "actionType": "dialog",
                            "level": "link",
                            "linkWithoutPadding": true,
                            "dialog": {
                              "title": "操作",
                              "body": "你正在编辑该卡片"
                            }
                          },
                          {
                            "className": {
                              "sizing": {
                                "height": "none"
                              }
                            }
                          }
                        )
                      ],
                      "itemAction": {
                        "type": "button",
                        "actionType": "url",
                        "url": "/dataseeddesigndocui/#/amis/zh-CN/components/card",
                        "blank": true
                      }
                    }),
                    {
                      "metaClassName": {
                        "layout": {
                          "overflow": {
                            "x": "hidden"
                          }
                        }
                      }
                    }
                  )
                }
              ],
            },
            {
              "type": "panel",
              "title": generateHeaderTitle({
                "type": "tpl",
                "tpl": "产品要素调整",
              }),
              "body": [
                {
                  "type": "cards",
                  "columnsCount": 5,
                  "source": "${itemLists}",
                  "card": generateStyle(
                    {
                      "header": {
                        "title": "${title}",
                        "subTitle": "${subTitle}",
                      },
                      "toolbar": [
                        generateStyle(
                          {
                            "type": "button",
                            "icon": "fa fa-star-o",
                            "actionType": "dialog",
                            "level": "link",
                            "linkWithoutPadding": true,
                            "dialog": {
                              "title": "操作",
                              "body": "你正在编辑该卡片"
                            }
                          },
                          {
                            "className": {
                              "sizing": {
                                "height": "none"
                              }
                            }
                          }
                        )
                      ],
                      "itemAction": {
                        "type": "button",
                        "actionType": "url",
                        "url": "/dataseeddesigndocui/#/amis/zh-CN/components/card",
                        "blank": true
                      }
                    },
                    {
                      "metaClassName": {
                        "layout": {
                          "overflow": {
                            "x": "hidden"
                          }
                        }
                      }
                    }
                  )
                }
              ],

            }])
        }
      )
    }
  }
  ])
}
