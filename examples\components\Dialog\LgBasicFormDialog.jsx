import { getLargeSizeDialogSchema, generateGroupForm, getButtonList } from 'amis-utils';

export default {
  type: 'page',
  body: getButtonList([{
    type: 'button',
    label: '大号基础表单',
    actionType: 'dialog',
    dialog: getLargeSizeDialogSchema({
      showErrorMsg: false,
      title: '大号基础表单',
      showCloseButton: false,
      body: generateGroupForm({
        type: 'form',
        api: '/api/mock2/saveForm?waitSeconds=2',
        body: [
          {
            type: "input-file",
            name: "file",
            accept: "*",
            label: false,
            receiver: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/upload/file",
            drag: true
          },
          {
            type: 'group',
            body: [
              {
                type: 'input-text',
                name: 'text1',
                label: '姓名',
              },
              {
                type: 'input-text',
                name: 'text2',
                label: '年龄',
              },
              {
                type: 'input-text',
                name: 'text3',
                label: '班级',
                required: true,
              },
            ]
          },
          {
            type: "group",
            body: [
              {
                type: 'input-text',
                name: 'text4',
                label: '邮箱'
              },
              {
                type: 'input-text',
                name: 'text5',
                label: '电话'
              },
              {
                type: 'input-text',
                name: 'text6',
                label: '地址'
              }
            ]
          },
          {
            type: 'group',
            body: [
              {
                type: 'textarea',
                name: 'textarea',
                label: '姓名',
                maxLength: 30,
                placehold: "请输入"
              }
            ]
          },
          {
            type: "group",
            body: [
              {
                type: 'input-rich-text',
                name: 'second5',
                label: '其它'
              }
            ]
          }
        ]
      })
    })
  }])
};
