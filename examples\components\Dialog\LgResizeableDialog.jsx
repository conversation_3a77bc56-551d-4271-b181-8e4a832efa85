import { getLargeSizeDialogSchema, generateSizing, generateStyle, getBasicListSchema, getButtonList, generateSpace, generateConfig } from 'amis-utils';

export default {
  type: 'page',
  body: getButtonList([{
    type: 'button',
    label: '大号左右布局',
    actionType: 'dialog',
    dialog: getLargeSizeDialogSchema({
      title: '大号左右布局',
      showCloseButton: true,
      actions: [],
      body: {
        "type": "flex",
        "direction": "row",
        "alignItems": "flex-start",
        "justify": "flex-start",
        "style": {
          "height": "calc(80vh - 120px)"
        },
        "items": [
          generateSizing(
            generateSpace({
              "type": "resize-container",
              "autoFillHeight": true,
              "minWidth": 0,
              "defaultSize": {
                "width": 288,
              },
              "body": [
                generateSpace({
                  "type": "button",
                  "label": "主按钮",
                  "level": "primary",
                }, {
                  "className": {
                    "margin": {
                      "top": "md"
                    },
                  }
                }),
                generateStyle(
                  {
                    "type": "input-tree",
                    "name": "tree2",
                    "multiple": false,
                    "searchable": true,
                    "autoCheckChildren": false,
                    "autoFillHeight": true,
                    "options": [
                      {
                        "label": "A",
                        "value": "a"
                      },
                      {
                        "label": "B",
                        "value": "b",
                        "children": [
                          {
                            "label": "B-1",
                            "value": "b-1"
                          },
                          {
                            "label": "B-2",
                            "value": "b-2"
                          },
                          {
                            "label": "B-3",
                            "value": "b-3"
                          }
                        ]
                      },
                      {
                        "label": "C",
                        "value": "c"
                      }
                    ],
                    "onEvent": {
                      "change": {
                        "actions": [
                          {
                            "actionType": "query",
                            "componentId": "right-crud",
                            "args": {
                              "queryParams": {
                                "tree2": "${event.data.value}"
                              }
                            },
                          }
                        ]
                      }
                    }
                  },
                  {
                    "className": {
                      "sizing": {
                        "height": 'full',
                      },
                      "layout": {
                        "overflow": {
                          "x": "hidden"
                        }
                      }
                    },
                    "treeContainerClassName": {
                      "border": {
                        "width": "none",
                      },
                      "spacing": {
                        "padding": {
                          "left": 'none',
                          "right": "none"
                        }
                      },
                      "sizing": {
                        "height": 'full',
                      },
                    }
                  }
                )
              ]
            }, {
              "className": {
                "border": {
                  "width": "",
                  "style": "solid",
                  "color": "gray-300"
                },
                "margin": {
                  "right": "sm"
                },
                "padding": {
                  "left": "sm",
                  "right": "sm"
                },

              }
            }), {
            "bodyClassName": {
              "height": "h-full"
            }
          }),
          getBasicListSchema(generateConfig({
            "api": "/api/mock2/crud/table4",
            "id": "right-crud",
            "autoGenerateFilter": {
              "showBtnToolbar": false,
              "defaultExpanded": false
            },
            "unsetQueryParams": ['tree2'],
            "columns": [
              {
                "name": "id",
                "label": "ID",
                "searchable": {
                  "type": "input-text",
                  "name": "id",
                  "label": "主键",
                  "placeholder": "输入id"
                }
              },
              {
                "name": "id",
                "label": "序号"
              },
              {
                "name": "engine",
                "label": "Rendering engine",
                "searchable": {
                  "type": "input-text",
                  "name": "engine",
                  "label": "Rendering engine",
                  "placeholder": "输入Rendering engine"
                }
              },
              {
                "name": "browser",
                "label": "Browser",
                "searchable": {
                  "type": "input-text",
                  "name": "browser",
                  "label": "Browser",
                  "placeholder": "输入Browser"
                }
              },
              {
                "name": "platform",
                "label": "Platform(s)",
                "searchable": {
                  "type": "input-text",
                  "name": "platform",
                  "label": "Platform(s)",
                  "placeholder": "输入Platform(s)"
                }
              },
              {
                "name": "version",
                "label": "Engine version",
                "searchable": {
                  "type": "input-text",
                  "name": "version",
                  "label": "Engine version",
                  "placeholder": "输入Engine version"
                }
              },
              {
                "name": "grade",
                "label": "CSS grade"
              },
              {
                "type": "operation",
                "label": "操作",
                "align": "left",
                "buttons": [
                  {
                    "name": "show-more",
                    "type": "show-more",
                    "label": "操作 ",
                    "visibleDivider": false,
                    "buttons": [
                      {
                        "label": "详情",
                        "type": "button",
                        "level": "link",
                        "actionType": "dialog",
                        "dialog": {
                          "title": "详情",
                          "body": "这是个简单的弹框。"
                        }
                      },
                      {
                        "label": "删除",
                        "type": "button",
                        "actionType": "ajax",
                        "level": "link",
                        "disabled": true,
                        "confirmText": "确认要删除吗？",
                        "api": {
                          "method": "delete",
                          "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
                        }
                      },
                      {
                        "label": "编辑",
                        "type": "button",
                        "level": "link",
                        "actionType": "dialog",
                        "dialog": {
                          "title": "编辑",
                          "body": "这是个简单的弹框。"
                        }
                      },
                      {
                        "label": "空跑",
                        "type": "button",
                        "level": "link",
                        "actionType": "dialog",
                        "dialog": {
                          "title": "空跑",
                          "body": "这是个简单的弹框。"
                        }
                      }
                    ]
                  }
                ]
              }
            ]
          }, {
            generateSizing: {
              className: { height: "h-full" }
            },
            generateOverFlow: {
              className: { overflow: 'auto' }
            },
            generateFlex: {
              className: { flex: '1' }
            }
          }))
        ]
      }
    })
  }])
};
