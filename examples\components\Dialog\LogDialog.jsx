import { getMiddleSizeDialogSchema, getButtonList, generateStyle } from 'amis-utils';

export default {
  type: 'page',
  body: getButtonList([
    {
      type: 'button',
      label: '试运行日志弹窗',
      actionType: 'dialog',
      dialog: getMiddleSizeDialogSchema({
        title: '试运行任务',
        actions: [],
        body: [
          {
            "type": "alert",
            "title": "提示类标题",
            "body": "提示类文案",
            "level": "info",
            "showIcon": true,
          },
          generateStyle(
            {
              "type": "code",
              "language": "typescript",
              "tagSize": 4,
              "wordWrap": false,
              "value": `2024-12-12 dsafdsafdsafdsdsa7fdsa9f78ds6a78fds7a8f90ds7afdsfdsafds
2024-12-12 afy8dsa7fds8a90f7ds9a7fdskjlahfjkdoshafjkdhsakfhdsakhfds
2024-12-12 khfjkdshakflhdskfhdsjhfjdksafds
2024-12-12 dsfds89af78dsaf6dsa9fdsaf8dsa7f89ds7af0dsa890f7dsa
2024-12-12 dsafds789fdsa89fdsf9ds0a7f89dsafjkdsahfdsafdsaidsaf
2024-12-12 dsfdsaf97d8s9a7f98dsaf89ds7a89f7ds89af`,
              editorTheme: "vs-dark",
            },
            {
              "className": {
                "spacing": {
                  "margin": {
                    "bottom": "none"
                  }
                }
              }
            }
          )
        ]
      })
    },
  ])
};
