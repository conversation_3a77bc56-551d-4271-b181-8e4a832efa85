import { generateStyle,getMiddleSizeDialogSchema, generateNoMarginInputTable,getVerticalFormSchema, generateHeaderTitle, getButtonList, getDialogGroupPanelNoPaddingSchema } from 'amis-utils';

export default {
  type: 'page',
  data: {
    table: [
      {
        a: "a1",
        b: "b1"
      },
      {
        a: "a2",
        b: "b2"
      },
      {
        a: "a3",
        b: "b3"
      }
    ]
  },
  body: getButtonList([{
    type: 'button',
    label: '中号基础表单',
    actionType: 'dialog',
    dialog: getMiddleSizeDialogSchema({
      title: '中号基础表单',
      showCloseButton: false,
      showErrorMsg: false,
      body: {
        type: 'form',
        api: '/api/mock2/form/saveForm?waitSeconds=2',
        body: [
          {
            type: "group",
            body: [
              {
                type: 'input-text',
                name: 'platform',
                required: true,
                placeholder: '请输入PlatForm(s)',
                label: 'PlatForm(s)',
                columnRatio: 6
              },
              {
                type: 'input-text',
                name: 'cssGrade',
                required: true,
                placeholder: '请输入CSS grade',
                label: 'CSS grade',
                columnRatio: 6
              }
            ]
          },
          {
            type: "group",
            body: [
              {
                type: 'input-text',
                name: 'brower',
                required: true,
                placeholder: '请输入Brower',
                label: 'Brower',
                columnRatio: 6
              },
              {
                type: 'input-text',
                name: 'version',
                required: true,
                placeholder: '请输入Version',
                label: 'Version',
                columnRatio: 6
              },
            ]
          },
          generateNoMarginInputTable({
            type: "input-table",
            name: "table",
            noMargin:{
              bottom: true
            },
            label: false,
            columns: [
              {
                label: "A",
                name: "a",
                type: "input-text"
              },
              {
                label: "B",
                name: "b",
                type: "select",
                options: [
                  "b1",
                  "b2",
                  "b3"
                ]
              }
            ]
          })

        ]
      }
    })
  }, {
    type: 'button',
    label: '中号垂直表单',
    actionType: 'dialog',
    dialog: getMiddleSizeDialogSchema({
      showErrorMsg: false,
      title: '中号垂直表单',
      showCloseButton: false,
      body: getVerticalFormSchema({
        "type": "form",
        "data": {
          "combos": [{}, {}, {}],
        },
        "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",

        "body":
          getDialogGroupPanelNoPaddingSchema([{
            "type": "panel",
            "title": [
              generateHeaderTitle({
                "type": "tpl",
                "tpl": "第一步，基础信息"
              }),
              {
                "type": "remark",
                "content": "这是一段提示",
              },
              generateStyle(
                {
                  "type": "button",
                  "label": "实验列表",
                  "level": "link",
                  "linkWithoutPadding": true
                },
                {
                  "className":{
                    "layout":{
                      "floats":"right"
                    }
                  }
                }
              )

            ],
            "body": [
              {
                "type": "group",
                "body": [
                  {
                    "type": "input-text",
                    "name": "text1",
                    "label": "姓名",
                  },
                  {
                    "type": "input-text",
                    "name": "text2",
                    "label": "年龄",
                  },
                  {
                    "type": "input-text",
                    "name": "text3",
                    "label": "班级",
                    "required": true,
                  },
                ]
              },
              {
                "type": "group",
                "body": [
                  {
                    "type": "input-text",
                    "name": "text4",
                    "description": "调整数量大小查看效果吧！",
                    "label": "邮箱",
                  },
                  {
                    "type": "input-text",
                    "name": "text5",
                    "label": "电话",
                  },
                  {
                    "type": "input-text",
                    "name": "text6",
                    "label": "地址",
                    "columnRatio": 4,
                  }
                ]
              },
              {
                "type": "group",
                "body": [
                  {
                    "type": "select",
                    "name": "text8",
                    "label": "类型",
                    "columnRatio": 4,
                    "options": [
                      {
                        "label": "曹操",
                        "value": "caocao",
                      },
                      {
                        "label": "刘备",
                        "value": "liubei",
                      }
                    ]
                  },
                ]
              }
            ]
          }, {
            "type": "panel",
            "title": generateHeaderTitle({
              "type": "tpl",
              "tpl": "第二步，复杂信息"
            }),
            "body": [
              {
                "type": "group",
                "body": [
                  {
                    "type": "input-text",
                    "name": "second1",
                    "label": "邮箱",
                  },
                  {
                    "type": "group",
                    "label": "事件来源",
                    "body": [
                      {
                        "type": "flex",
                        "alignItems": "flex-start",
                        "justify": "flex-start",
                        "items": [
                          generateStyle(                          {
                            "type": "select",
                            "name": "eventSource",
                            "label": false,
                            "options": [
                              {
                                "label": "业务系统",
                                "value": "SYSTEM",
                              },
                              {
                                "label": "特征系统",
                                "value": "FEATURE",
                              },
                              {
                                "label": "北斗系统",
                                "value": "EFUEL",
                              },
                              {
                                "label": "埋点系统",
                                "value": "STATS",
                              }
                            ],
                            "columnRatio": 8
                          },
                          {
                            "className":{
                              "flexBox":{
                                "flex":"1"
                              },
                              "spacing":{
                                "margin":{
                                  "bottom":"none"
                                }
                              }
                            }

                          }
                        ),
                          {
                            "type": "wrapper",
                            "size": "none",
                            "columnRatio": 4,
                            "body": getButtonList([
                              {
                                "type": "button",
                                "label": "新建",
                                "level": "link",
                                "linkWithoutPadding": true,
                              },
                              {
                                "type": "button",
                                "label": "详情",
                                "level": "link",
                                "linkWithoutPadding": true,
                              }
                            ])
                          }
                        ]
                      }
                    ]
                  },
                  {
                    "type": "input-text",
                    "name": "second3",
                    "label": "地址",
                    "columnRatio": 4,
                  }
                ]
              },
              {
                "type": "group",
                "body": [
                  {
                    "type": "textarea",
                    "name": "textarea",
                    "label": "其他",
                    "placeholder": "请输入"
                  }
                ]
              },
            ]
          }]),

        "actions": [
          {
            "type": "button",
            "label": "取消",
          },
          {
            "type": "submit",
            "level": "primary",
            "label": "保存",
          },
        ]
      })
    })
  },])
};
