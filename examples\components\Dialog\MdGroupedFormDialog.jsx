import { getMiddleSizeDialogSchema, generateGroupForm, generateHeaderTpl, getDialogGroupPanelSchema, generateHeaderTitle } from 'amis-utils';

export default {
  type: 'page',
  className: 'p-4',
  body: {
    type: 'button',
    label: '中号分组表单',
    actionType: 'dialog',
    dialog: getMiddleSizeDialogSchema({
      title: '中号分组表单',
      showCloseButton: false,
      body: generateGroupForm({
        type: 'form',
        api: '/api/mock2/saveForm?waitSeconds=2',
        body: getDialogGroupPanelSchema([
          {
            type: 'panel',
            title: {
              type: "flex",
              justify: 'flex-start',
              items: [
                generateHeaderTitle({
                  type: "tpl",
                  tpl: "第一步，基础信息",
                }),
                {
                  type: "tpl",
                  className: "pm-button-ml pm-subtitle-color pm-subtitle-font-size",
                  tpl: "这是小标题"
                }
              ]
            },
            body: [
              {
                type: 'group',
                body: [
                  {
                    type: 'input-text',
                    name: 'text1',
                    label: '姓名',
                  },
                  {
                    type: 'input-text',
                    name: 'text2',
                    label: '年龄',
                  }
                ]
              },
              {
                type: "group",
                body: [
                  {
                    type: 'input-text',
                    name: 'text4',
                    label: '邮箱',
                  },
                  {
                    type: 'input-text',
                    name: 'text5',
                    label: '电话',
                  }
                ]
              },
              {
                type: "group",
                body: [
                  {
                    type: 'input-text',
                    name: 'text7',
                    label: '其它',
                    columnRatio: 6,
                  }
                ]
              }
            ]
          },
          {
            type: 'panel',
            title: generateHeaderTpl({
              type: 'tpl',
              tpl: '第二步，复杂信息'
            }),
            body: [
              {
                type: "group",
                body: [
                  {
                    type: 'input-text',
                    name: 'second1',
                    label: '邮箱',
                  },
                  {
                    type: 'input-text',
                    name: 'second2',
                    label: '电话',
                  }
                ]
              },
              {
                type: 'group',
                body: [
                  {
                    type: 'textarea',
                    name: 'textarea',
                    label: '姓名',
                    maxLength: 30,
                    showCounter: true,
                    placehold: "请输入"
                  }
                ]
              },
              {
                type: "group",
                body: [
                  {
                    type: 'input-rich-text',
                    name: 'second5',
                    label: '其它',
                  }
                ]
              }
            ]
          }
        ])
      })
    })
  },
};
