import { generateSpace, generateFlexConfig, generateTextStyle, generateConfig, generateHeaderTitle, getCollapsableGroupPanelForWhiteBgSchemaV2, generateCollapseGroupOfTabs, getFormTabDetailSchema, getMiddleSizeDialogSchema, generateCardV2, getButtonList } from 'amis-utils';

export default {
  type: 'page',
  body: getButtonList([{
    type: 'button',
    label: '中号分组表单',
    actionType: 'dialog',
    dialog: getMiddleSizeDialogSchema({
      title: '中号分组表单',
      showCloseButton: false,
      body: getFormTabDetailSchema({
        "body": [
          generateCollapseGroupOfTabs({
            "type": "collapse-group",
            "activeKey": ["1"],
            "classConfig": true, // 本示例需要配置为true
            "body": getCollapsableGroupPanelForWhiteBgSchemaV2([
              {
                "key": "1",
                "headingClsConfig": true, // 带小标题的标题 需要配置此属性
                "header": {
                  type: "flex",
                  justify: 'flex-start',
                  items: [
                    generateHeaderTitle({
                      type: "tpl",
                      tpl: "第一步，转换文本",
                    }),
                    generateTextStyle(
                      {
                        ...generateConfig({
                          type: "tpl",
                          tpl: "这是小标题"
                        }, {
                          generateSpace: {
                            className: {
                              margin: {
                                left: 'sm'
                              }
                            }
                          },

                        }),
                        textStyle: {
                          color: "gray-500",
                          size: "sm"
                        }
                      }
                    ),
                  ]
                },
                body: [
                  {
                    type: 'group',
                    direction: 'vertical',
                    body: [
                      generateSpace({
                        label: '请输入要转换的文本',
                        labelWidth: 160,
                        labelRemark: '提示信息',
                        type: "textArea",
                      }, {
                        className: {
                          margin: {
                            bottom: "md"
                          }
                        }
                      }),
                      {
                        type: 'button',
                        label: '转换',
                        level: 'primary',
                      }
                    ]
                  }
                ]
              },
              {
                "key": "2",
                "header": generateHeaderTitle({
                  "type": "tpl",
                  "tpl": "第二步，转换结果"
                }),
                "collapsed": false,
                body: [
                  generateCardV2({
                    type: "card",
                    header: {
                      title: "精准匹配到特征",
                    },
                    body: [
                      generateSpace({
                        type: "group",
                        body: [
                          generateFlexConfig(
                            {
                              // type: "select",
                              name: "drive",
                              label: "已选特征",
                              required: true,
                              columnRatio: 12,
                              // static: true,
                              type: 'group',
                              mode: 'inline',
                              body: [
                                {
                                  "type": "tag",
                                  "label": "这是一个很长长长长长长长长长长长长长的标签",
                                  "color": "success",
                                  "displayMode": "bordered",
                                  // "closable": true
                                },
                                {
                                  "type": "tag",
                                  "label": "这是一个很长长长长长长长长长长长长长的标签",
                                  "color": "success",
                                  "displayMode": "bordered",
                                  // "closable": true
                                },
                                {
                                  "type": "tag",
                                  "label": "这是一个很长长长长长长长长长长长长长的标签",
                                  "color": "success",
                                  "displayMode": "bordered",
                                  // "closable": true
                                },
                                {
                                  "type": "tag",
                                  "label": "这是一个很长长长长长长长长长长长长长的标签",
                                  "color": "success",
                                  "displayMode": "bordered",
                                  // "closable": true
                                },
                              ]
                            },
                            {
                              className: {
                                flex: "flex",
                                itemsAlign: "items-center"
                              }
                            }
                          )
                        ]
                      }, {
                        className: {
                          padding: {
                            bottom: "lg"
                          }
                        }
                      }),
                      {
                        type: "table",
                        name: "table",
                        selectable: true,
                        multiple: true,
                        items: [
                          {
                            a: "a1",
                            b: "a2",
                            c: "a3",
                            d: "a4"
                          },
                          {
                            a: "a1",
                            b: "a2",
                            c: "a3",
                            d: "a4"
                          },
                          {
                            a: "a1",
                            b: "a2",
                            c: "a3",
                            d: "a4"
                          }
                        ],
                        columns: [
                          {
                            name: "a",
                            label: "指标名称"
                          },
                          {
                            name: "b",
                            label: "列表2"
                          },
                          {
                            name: "c",
                            label: "列表3"
                          },
                          {
                            name: "d",
                            label: "列表4"
                          }
                        ]
                      }
                    ]
                  }),
                  generateCardV2({
                    type: "card",
                    header: {
                      title: "匹配到多个特征",
                    },
                    body: [
                      generateSpace(
                        {
                          type: "group",
                          body: [
                            generateFlexConfig({
                              name: "drive",
                              label: "已选特征",
                              required: true,
                              columnRatio: 12,
                              type: 'group',
                              mode: 'inline',
                              body: [
                                {
                                  "type": "tag",
                                  "label": "这是一个很长长长长长长长长长长长长长的标签",
                                  "color": "success",
                                  "displayMode": "bordered",
                                  // "closable": true
                                },
                                {
                                  "type": "tag",
                                  "label": "这是一个很长长长长长长长长长长长长长的标签",
                                  "color": "success",
                                  "displayMode": "bordered",
                                  // "closable": true
                                },
                                {
                                  "type": "tag",
                                  "label": "这是一个很长长长长长长长长长长长长长的标签",
                                  "color": "success",
                                  "displayMode": "bordered",
                                  // "closable": true
                                },
                                {
                                  "type": "tag",
                                  "label": "这是一个很长长长长长长长长长长长长长的标签",
                                  "color": "success",
                                  "displayMode": "bordered",
                                  // "closable": true
                                },
                              ]
                            }, {
                              className: {
                                flex: "flex",
                                itemsAlign: "items-center"
                              }
                            })
                          ]
                        }, {
                        className: {
                          padding: {
                            bottom: "lg"
                          }
                        }
                      }),
                      {
                        type: "table",
                        name: "table",
                        selectable: true,
                        multiple: true,
                        items: [
                          {
                            a: "a1",
                            b: "a2",
                            c: "a3",
                            d: "a4"
                          },
                          {
                            a: "a1",
                            b: "a2",
                            c: "a3",
                            d: "a4"
                          },
                          {
                            a: "a1",
                            b: "a2",
                            c: "a3",
                            d: "a4"
                          }
                        ],
                        columns: [
                          {
                            name: "a",
                            label: "指标名称"
                          },
                          {
                            name: "b",
                            label: "列表2"
                          },
                          {
                            name: "c",
                            label: "列表3"
                          },
                          {
                            name: "d",
                            label: "列表4"
                          }
                        ]
                      }
                    ]
                  }),
                  generateCardV2({
                    type: "card",

                    header: {
                      title: "匹配无结果",
                    },
                    body: [
                      {
                        type: "group",

                        body: [
                          generateFlexConfig(
                            {
                              // type: "select",
                              name: "drive",
                              label: "关键字",
                              required: true,
                              columnRatio: 12,
                              // static: true,
                              type: 'group',
                              mode: 'inline',
                              body: [
                                {
                                  "type": "tag",
                                  "label": "这是一个很长长长长长长长长长长长长长的标签",
                                  "color": "error",
                                  "displayMode": "bordered",
                                  // "closable": true
                                },
                                {
                                  "type": "tag",
                                  "label": "这是一个很长长长长长长长长长长长长长的标签",
                                  "color": "error",
                                  "displayMode": "bordered",
                                  // "closable": true
                                },
                                {
                                  "type": "tag",
                                  "label": "这是一个很长长长长长长长长长长长长长的标签",
                                  "color": "error",
                                  "displayMode": "bordered",
                                  // "closable": true
                                },
                                {
                                  "type": "tag",
                                  "label": "这是一个很长长长长长长长长长长长长长的标签",
                                  "color": "error",
                                  "displayMode": "bordered",
                                  // "closable": true
                                },
                              ]
                            }, {
                            className: {
                              flex: "flex",
                              itemsAlign: "items-center"
                            }
                          })
                        ]
                      }
                    ]
                  }),
                ]
              },
              {
                "key": "3",
                "header": generateHeaderTitle({
                  "type": "tpl",
                  "tpl": "第三步，已选特征"
                }),
                "collapsed": false,
                body: [
                  generateCardV2({
                    type: "card",
                    body: {
                      type: 'group',
                      body: [
                        generateFlexConfig({
                          name: "drive",
                          label: "已选特征4个",
                          required: true,
                          columnRatio: 12,
                          type: 'group',
                          mode: 'inline',
                          body: [
                            {
                              "type": "tag",
                              "label": "这是一个很长长长长长长长长长长长长长的标签",
                              "color": "success",
                              "displayMode": "bordered",
                              "closable": true
                            },
                            {
                              "type": "tag",
                              "label": "这是一个很长长长长长长长长长长长长长的标签",
                              "color": "success",
                              "displayMode": "bordered",
                              "closable": true
                            },
                            {
                              "type": "tag",
                              "label": "这是一个很长长长长长长长长长长长长长的标签",
                              "color": "success",
                              "displayMode": "bordered",
                              "closable": true
                            },
                            {
                              "type": "tag",
                              "label": "这是一个很长长长长长长长长长长长长长的标签",
                              "color": "success",
                              "displayMode": "bordered",
                              "closable": true
                            },
                          ]
                        }, {
                          className: {
                            flex: "flex",
                            itemsAlign: "items-center"
                          }
                        }
                        )
                      ]
                    }
                  })

                ]
              }
            ])
          }),
        ],
      })
    })
  },])
};
