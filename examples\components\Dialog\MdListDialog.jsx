import { generateSpace, generateColor, getListDialogSchema, getWithoutMarginsCRUDSchemaV2, getButtonList } from 'amis-utils';

export default generateSpace({
  type: 'page',
  body: getButtonList([{
    type: 'button',
    label: '中号列表',
    actionType: 'dialog',
    dialog: getListDialogSchema({
      title: '中号列表',
      body: [
        {
          type: 'tpl',
          tpl: '<span>测试结果：</span>',
        },
        generateColor({
          type: 'tpl',
          tpl: '调用成功！',
        }, "success"),
        {
          type: 'tpl',
          tpl: '<span>结果如下：</span>',
        },
        generateSpace(getWithoutMarginsCRUDSchemaV2({
          api: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?waitSeconds=1",
          columns: [
            {
              name: "id",
              label: "ID"
            },
            {
              name: "engine",
              label: "Rendering engine"
            },
            {
              name: "browser",
              label: "Browser"
            },
            {
              name: "platform",
              label: "Platform(s)"
            },
            {
              name: "engine",
              label: "Engine"
            },
            {
              name: "version",
              label: "Engine Version"
            },
            {
              name: "grade",
              label: "CSS grade"
            }
          ]
        }, true, true), {
          className: {
            margin: {
              top: 'sm'
            }
          }
        })
      ]
    })
  }, {
    type: 'button',
    label: '中号列表-调用失败',
    actionType: 'dialog',
    dialog: getListDialogSchema({
      title: '中号列表-调用失败',
      body: [
        {
          type: 'tpl',
          tpl: '<div>测试结果：<span style="color: red">调用失败！</span>错误信息如下：</div>'
        },
        generateSpace(getWithoutMarginsCRUDSchemaV2({
          api: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?waitSeconds=1",
          columns: [
            {
              name: "id",
              label: "ID"
            },
            {
              name: "engine",
              label: "Rendering engine"
            },
            {
              name: "browser",
              label: "Browser"
            },
            {
              name: "platform",
              label: "Platform(s)"
            },
            {
              name: "engine",
              label: "Engine"
            },
            {
              name: "version",
              label: "Engine Version"
            },
            {
              name: "grade",
              label: "CSS grade"
            }
          ]
        }, true, true), {
          className: {
            margin: {
              top: 'sm'
            }
          }
        })
      ]
    })
  }, {
    type: 'button',
    label: '无边距列表',
    actionType: 'dialog',
    dialog: getListDialogSchema({
      title: '无边距列表',
      body: [
        getWithoutMarginsCRUDSchemaV2({
          keepItemSelectionOnPageChange: true,
          multiple: true,
          api: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?waitSeconds=1",
          autoGenerateFilter: false,
          footerToolbar: null,
          "headerToolbar": [
            "bulkActions",
            {
              "type": "button-group-select",
              "name": "button-group-select",
              "align": "right",
              "options": [
                {
                  "value": "all",
                  "label": "查询全部"
                },
                {
                  "value": "forme",
                  "label": "待我审核"
                },
                {
                  "value": "reject",
                  "label": "已拒绝"
                }
              ],
              "onEvent": {
                "change": {
                  "actions": [
                    {
                      "actionType": "query",
                      "componentId": "custom-crud-id",
                      "args": {
                        "queryParams": {
                          "button-group-select": "${button-group-select}"
                        }
                      }
                    }
                  ]
                }
              }
            }
          ],
          "bulkActions": [
            {
              "label": "批量删除",
              "actionType": "ajax",
              "api": "delete:https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample/${ids|raw}",
              "confirmText": "确定要批量删除?"
            },
            {
              "label": "批量修改",
              "actionType": "dialog",
              "dialog": {
                "title": "批量编辑",
                "showCloseButton": false,
                "body": {
                  "type": "form",
                  "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample/bulkUpdate2",
                  "body": [
                    {
                      "type": "hidden",
                      "name": "ids"
                    },
                    {
                      "type": "input-text",
                      "name": "engine",
                      "label": "Engine"
                    }
                  ]
                }
              }
            }
          ],
          columns: [
            {
              name: "id",
              label: "ID",
            },
            {
              name: "engine",
              label: "Rendering engine"
            },
            {
              name: "browser",
              label: "Browser"
            },
            {
              name: "platform",
              label: "Platform(s)"
            },
            {
              name: "engine",
              label: "Engine"
            },
            {
              name: "version",
              label: "Engine Version"
            },
            {
              name: "grade",
              label: "CSS grade"
            }
          ]
        }, true, true)
      ]
    })
  }])
}, {
  className: {
    padding: {
      top: "md",
      bottom: "md",
      left: "md",
      right: "md"
    }
  }
});
