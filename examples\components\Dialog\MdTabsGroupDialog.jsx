import {
  getButtonList,
  getNoPaddingTabs,
  getFormTabDetailSchema,
  generateCollapseGroupOfTabs,
  generateHeaderTitle,
  generateHeaderTpl,
  getMiddleSizeDialogSchema,
  getCollapsableGroupPanelForWhiteBgSchemaV2,
  generateNoPaddingCollapse,
  getDialogGroupPanelNoPaddingSchema,
  generateCommonPage,
  generateFormPanel,
  generateCollapseGroupResizeable
} from 'amis-utils';

export default generateCommonPage({
  "type": "page",
  "data": {
    "text1": "aaaa",
    "text2": 18,
    "text3": "7年级",
    "text4": "<EMAIL>",
    "text5": "12345678",
    "text6": "上海市浦东新区",
    "text7": "测试",
    "text8": "text8",
    "text9": "text9",
    "second1": "<EMAIL>",
    "second2": "12345678",
    "second3": "上海市浦东新区",
    "second4": "上海市浦东新区",
    "third4": "sdsd",
  },
  "body": getButtonList([
    {
      "type": "button",
      "label": "中号tabs模式",
      "actionType": "dialog",
      "dialog": getMiddleSizeDialogSchema({
        "title": "tabs模式",
        "actions": [],
        "body": {
          "type": "tabs",
          "tabs": getNoPaddingTabs([
            {
              "title": "可折叠分组",
              "tab": getFormTabDetailSchema(generateFormPanel({
                "static": true,
                "body": [
                  generateCollapseGroupOfTabs(generateCollapseGroupResizeable({
                    "type": "collapse-group",
                    "activeKey": [
                      "1"
                    ],
                    "body": getCollapsableGroupPanelForWhiteBgSchemaV2([
                      generateNoPaddingCollapse({
                        noPadding:{
                          left: false,
                          right: false,
                        },
                        "key": "1",
                        "header": [
                          generateHeaderTitle({
                            "type": "tpl",
                            "tpl": "第一步，基础信息"
                          }),

                        ],
                        "body": [
                          {
                            "type": "group",
                            "body": [
                              {
                                "type": "input-text",
                                "name": "text1",
                                "label": "姓名",
                              },
                              {
                                "type": "input-text",
                                "name": "text2",
                                "label": "年龄",
                              },
                              {
                                "type": "input-text",
                                "name": "text3",
                                "label": "班级",
                                "required": true,
                              },
                            ]
                          },
                          {
                            "type": "group",
                            "body": [
                              {
                                "type": "input-text",
                                "name": "text4",
                                "label": "邮箱",
                              },
                              {
                                "type": "input-text",
                                "name": "text5",
                                "label": "电话",
                              },
                              {
                                "type": "input-text",
                                "name": "text6",
                                "label": "地址",
                                "columnRatio": 4,
                              }
                            ]
                          },
                          {
                            "type": "group",
                            "body": [
                              {
                                "type": "input-text",
                                "name": "text7",
                                "label": "其它",
                                "columnRatio": 4,
                              }
                            ]
                          }
                        ]
                      }),
                      generateNoPaddingCollapse({
                        noPadding:{
                          left: false, // 设置左内边距不为0
                          right: false,
                        },
                        "key": "2",
                        "header": generateHeaderTitle({
                          "type": "tpl",
                          "tpl": "第二步，复杂信息"
                        }),
                        "collapsed": false,
                        "body": [
                          {
                            "type": "group",
                            "body": [
                              {
                                "type": "input-text",
                                "name": "second1",
                                "label": "邮箱",
                              },
                              {
                                "type": "input-text",
                                "name": "second2",
                                "label": "电话",
                              },
                              {
                                "type": "input-text",
                                "name": "second3",
                                "label": "地址",
                                "columnRatio": 4,
                              }
                            ]
                          },
                          {
                            "type": "group",
                            "body": [
                              {
                                "type": "input-text",
                                "name": "second3",
                                "label": "姓名",
                                "placehold": "请输入"
                              }
                            ]
                          }
                        ]
                      }),
                      generateNoPaddingCollapse({
                        noPadding:{
                          left: false, // 设置左内边距不为0
                          right: false,
                        },
                        "key": "3",
                        "header": generateHeaderTitle({
                          "type": "tpl",
                          "tpl": "第三步，策略信息"
                        }),
                        "collapsed": false,
                        "body": [
                          {
                            "type": "group",
                            "body": [
                              {
                                "type": "tabs",
                                "tabs": [
                                  {
                                    "title": "策略分支1",
                                    "tab": [
                                      {
                                        "type": "group",
                                        "body": [
                                          {
                                            "type": "input-text",
                                            "name": "third1",
                                            "label": "sjksajkd"
                                          },
                                          {
                                            "type": "input-text",
                                            "name": "third2",
                                            "label": "sjksajkd"
                                          },
                                          {
                                            "type": "input-text",
                                            "name": "third3",
                                            "label": "sjksajkd"
                                          }
                                        ]
                                      },
                                      {
                                        "type": "group",
                                        "body": [
                                          {
                                            "type": "input-text",
                                            "name": "third5",
                                            "label": "sjksajkd"
                                          },
                                          {
                                            "type": "select",
                                            "name": "third4",
                                            "label": "sjksajkd",
                                            "options": [
                                              {
                                                "label": "a",
                                                "value": "a"
                                              },
                                              {
                                                "label": "b",
                                                "value": "b"
                                              }
                                            ]
                                          },
                                          {
                                            "type": "input-text",
                                            "name": "third6",
                                            "label": "sjksajkd"
                                          }
                                        ]
                                      }
                                    ]
                                  },
                                  {
                                    "title": "策略分支2",
                                    "tab": [
                                      {
                                        "type": "group",
                                        "body": [
                                          {
                                            "type": "input-text",
                                            "name": "third7",
                                            "label": "sjksajkd"
                                          },
                                          {
                                            "type": "input-text",
                                            "name": "third8",
                                            "label": "sjksajkd"
                                          },
                                          {
                                            "type": "input-text",
                                            "name": "third9",
                                            "label": "sjksajkd"
                                          }
                                        ]
                                      },
                                      {
                                        "type": "group",
                                        "body": [
                                          {
                                            "type": "input-text",
                                            "name": "third10",
                                            "label": "sjksajkd"
                                          },
                                          {
                                            "type": "select",
                                            "name": "third4",
                                            "label": "sjksajkd",
                                            "options": [
                                              {
                                                "label": "a",
                                                "value": "a"
                                              },
                                              {
                                                "label": "b",
                                                "value": "b"
                                              }
                                            ]
                                          },
                                          {
                                            "type": "input-text",
                                            "name": "third12",
                                            "label": "sjksajkd"
                                          }
                                        ]
                                      }
                                    ]
                                  },
                                  {
                                    "title": "策略分支3",
                                    "tab": [
                                      {
                                        "type": "group",
                                        "body": [
                                          {
                                            "type": "input-text",
                                            "name": "third13",
                                            "label": "sjksajkd"
                                          },
                                          {
                                            "type": "input-text",
                                            "name": "third14",
                                            "label": "sjksajkd"
                                          },
                                          {
                                            "type": "input-text",
                                            "name": "third15",
                                            "label": "sjksajkd"
                                          }
                                        ]
                                      },
                                      {
                                        "type": "group",
                                        "body": [
                                          {
                                            "type": "input-text",
                                            "name": "third16",
                                            "label": "sjksajkd"
                                          },
                                          {
                                            "type": "select",
                                            "name": "third4",
                                            "label": "sjksajkd",
                                            "options": [
                                              {
                                                "label": "a",
                                                "value": "a"
                                              },
                                              {
                                                "label": "b",
                                                "value": "b"
                                              }
                                            ]
                                          },
                                          {
                                            "type": "input-text",
                                            "name": "third18",
                                            "label": "sjksajkd"
                                          }
                                        ]
                                      }
                                    ]
                                  }
                                ]
                              },
                            ]
                          }
                        ]
                      })
                    ])
                  })),
                ],
              })),
            },
            {
              "title": "分组表单",
              "tab": getFormTabDetailSchema({
                "body": getDialogGroupPanelNoPaddingSchema([
                  {
                    "type": 'panel',
                    "title": generateHeaderTpl({
                      "type": "tpl",
                      "tpl": "第一步，基础信息"
                    }),
                    "body": [
                      {
                        "type": 'group',
                        "body": [
                          {
                            "name": "text1",
                            "type": "static",
                            "label": "静态展示",
                            "quickEdit": {
                              "type": "input-text"
                            }
                          },
                          {
                            "type": 'static',
                            "name": 'text2',
                            "label": '年龄',
                          },
                          {
                            "type": 'static',
                            "name": 'text3',
                            "label": '班级',
                            "required": true,
                          },
                        ]
                      },
                      {
                        "type": "group",
                        "body": [
                          {
                            "type": "flex",
                            "justify": "flex-start",
                            "alignItems": "baseline",
                            "items": [
                              {
                                "type": "static",
                                "name": "text4",
                                "label": "邮箱",
                              }
                            ]
                          },
                          {
                            "type": 'static',
                            "name": 'text5',
                            "label": '电话',
                          },
                          {
                            "type": 'static',
                            "name": 'text6',
                            "label": '地址',
                            "columnRatio": 4,
                          }
                        ]
                      },

                    ]
                  },
                  {
                    "type": 'panel',
                    "title":
                      generateHeaderTpl({
                        "type": "tpl",
                        "tpl": '第二步，复杂信息',
                      }),
                    "body": [
                      {
                        "type": "group",
                        "body": [
                          {
                            "type": 'static',
                            "name": 'second1',
                            "label": '邮箱',
                          },
                          {
                            "type": 'static',
                            "name": 'second2',
                            "label": '电话',
                          },
                          {
                            "type": 'static',
                            "name": 'second3',
                            "label": '地址',
                            "columnRatio": 4,
                          }
                        ]
                      },
                      {
                        "type": 'group',
                        "body": [
                          {
                            "type": 'static',
                            "name": 'second4',
                            "label": '地址',
                            "columnRatio": 4,
                          }
                        ]
                      }
                    ]
                  },
                  {
                    "type": 'panel',
                    "title": generateHeaderTpl({
                      "type": "tpl",
                      "tpl": '第三步，策略信息',
                    }),
                    "body": [
                      {
                        "type": "group",
                        "body": [
                          {
                            "type": 'static',
                            "name": 'second1',
                            "label": '邮箱',
                          },
                          {
                            "type": 'static',
                            "name": 'second2',
                            "label": '电话',
                          },
                          {
                            "type": 'static',
                            "name": 'second3',
                            "label": '地址',
                            "columnRatio": 4,
                          }
                        ]
                      },
                      {
                        "type": 'group',
                        "body": [
                          {
                            "type": 'static',
                            "name": 'second4',
                            "label": '地址',
                            "columnRatio": 4,
                          }
                        ]
                      }
                    ]
                  }
                ])
              }),
            },
          ])
        }
      })

    }
  ])


})
