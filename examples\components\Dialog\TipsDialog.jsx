
import { generateStyle, getButtonList, getIconButton } from 'amis-utils'

export default {
  type: 'page',
  "data": {
    "arr": [
      {
        "name": "FP-2234523434"
      },
      {
        "name": "JC-123452345t"
      },
      {
        "name": "JP-1234562345r"
      },
      {
        "name": "JP-123456234ss5r"
      }
    ]
  },
  body: getButtonList([
    {
      type: 'button',
      label: '提示类弹窗',
      actionType: 'dialog',
      dialog: {
        title: '提示类弹窗',
        showCloseButton: false,
        body: '确定删除这条信息吗？'
      }
    },
    {
      type: 'button',
      label: '二次确认(带详情)',
      actionType: 'dialog',
      dialog: {
        title: '解除关联',
        showCloseButton: false,
        body: [
          generateStyle(
            {
              type: "tpl",
              tpl: '确定删除这条信息吗？',
            },
            {
              "className": {
                "spacing": {
                  "margin": {
                    "bottom": 'sm'
                  }
                },
                "layout": {
                  "display": "inline-block"
                }
              }
            }
          ),
          generateStyle(
            {
              "type": "alert",
              "body": {
                type: 'form',
                mode: "horizontal",
                withoutItemMarginBottom: true,
                data: {
                  platform: '技术中心 / App及前台研发部',
                  cssGrade: '张三',
                  brower: '张三'
                },
                static: true,
                labelWidth: 60,
                wrapWithPanel: false,
                body: [
                  {
                    type: 'static',
                    name: 'platform',
                    label: '归属部门'
                  },
                  {
                    type: 'static',
                    name: 'cssGrade',
                    label: '负责人',
                  },
                  {
                    type: 'static',
                    name: 'brower',
                    label: '创建人'
                  }
                ]
              },
              "level": "warning"
            },
            {
              "className": {
                "spacing": {
                  "margin": {
                    "bottom": 'none'
                  }
                }
              }
            }
          ),
        ]
      }
    },
    {
      type: 'button',
      label: '提示+关联信息',
      actionType: 'dialog',
      dialog: {
        title: '节点下线',
        showCloseButton: false,
        body: [
          {
            type: "tpl",
            tpl: '节点【XXXX】被如下在线状态策略关联，请先解除关联：'
          },
          {
            "type": "each",
            "name": "arr",
            "items": [
              {
                "type": "link",
                "href": "${name}",
                "body": "${name}"
              },
              {
                "type": "tpl",
                "visibleOn": "${(index + 1) < arr.length}",
                "tpl": '、'
              }
            ]
          }
        ]
      }
    },
    {
      type: 'button',
      label: '带有icon的提示类',
      actionType: 'dialog',
      dialog: {
        title: '提示类弹窗',
        showCloseButton: false,
        body: {
          type: 'wrapper',
          size: 'none',
          body: [
            generateStyle(getIconButton(
              {
                type: 'icon',
                icon: 'alert-warning',
              }
            ),
              {
                "className": {
                  "spacing": {
                    "margin": {
                      "right": 'xs'
                    }
                  },
                }
              }),
            '确定删除这条信息吗？',
          ],
        },
      }
    },
    {
      type: 'button',
      label: '关键词突出显示',
      actionType: 'dialog',
      dialog: {
        title: '提示类弹窗',
        showCloseButton: false,
        body: {
          type: 'wrapper',
          size: 'none',
          body: [
            generateStyle(getIconButton(
              {
                type: 'icon',
                icon: 'alert-warning',
              }
            ),
              {
                "className": {
                  "spacing": {
                    "margin": {
                      "right": 'xs'
                    }
                  },
                }
              }),
            '回滚后会将【版本5】更新为’正式运行‘状态，是否继续操作？',
          ],
        },
      }
    },
    {
      type: 'button',
      label: '加重关键词突出显示',
      actionType: 'dialog',
      dialog: {
        title: '提示类弹窗',
        showCloseButton: false,
        body: {
          type: 'wrapper',
          size: 'none',
          body: [
            generateStyle(getIconButton(
              {
                type: 'icon',
                icon: 'alert-warning',
              }
            ),
              {
                "className": {
                  "spacing": {
                    "margin": {
                      "right": 'xs'
                    }
                  },
                }
              }),
            {
              "type": "html",
              "html": "回滚后会将<b>【版本5】</b>更新为’正式运行‘状态，是否继续操作？"
            }
          ],
        },
      }
    },
    {
      type: 'button',
      label: '多行弹窗',
      actionType: 'dialog',
      dialog: {
        title: '提示类弹窗',
        showCloseButton: false,
        body: [
          generateStyle(
            {
              type: "tpl",
              tpl: "工单已提交，工单审批完成后当日敏感数据明文查询次数将增加"
            },
            {
              "className": {
                "spacing":{
                  "margin":{
                    "bottom": 'sm'
                  }
                },
                "layout": {
                  "display": "inline-block"
                }
              }
            }
          ),
          {
            "type": "each",
            "name": "arr",
            "items": [
              {
                "visibleOn": "${index === 0}",
                type: "tpl",
                tpl: '查看工单：'
              },
              {
                "type": "link",
                "href": "${name}",
                "body": "${name}"
              },
              {
                "type": "tpl",
                "visibleOn": "${(index + 1) < arr.length}",
                "tpl": '、'
              }
            ]
          }
        ]
      }
    },
    {
      type: 'button',
      label: '基础表单-上方提示',
      actionType: 'dialog',
      dialog: {
        title: '基础表单-上方提示',
        showCloseButton: false,
        body: [
          {
            type: "alert",
            title: "提示类标题",
            body: "提示类文案",
            level: "info",
            showIcon: true,
          },
          {
            type: 'form',
            api: '/api/mock2/form/saveForm?waitSeconds=2',
            body: [
              {
                type: 'input-text',
                name: 'platform',
                placeholder: '请输入PlatForm(s)',
                label: 'PlatForm(s)'
              },
              {
                type: 'input-text',
                name: 'cssGrade',
                label: 'CSS grade',
                required: true,
                placeholder: '请输入CSS grade'
              },
              {
                type: 'input-text',
                name: 'brower',
                placeholder: '请输入Brower',
                label: 'Brower'
              },
              {
                type: 'input-text',
                name: 'version',
                label: 'Version',
                required: true,
                placeholder: '请输入Version'
              }
            ]
          },
        ],
      },
    },
    {
      type: 'button',
      label: '校验未通过提示带跳转链接',
      "actionType": "toast",
      "toast": {
        "items": [
          {
            "level": "error",
            "timeout": 5000,
            "body": {
              type: 'wrapper',
              size: 'none',
              body: [{
                type: "tpl",
                tpl: "校验未通过，资源配置不符合规范，详见：",
              },
              generateStyle(
                {
                  "type": "link",
                  "href": "https://www.baidu.com",
                  "body": "资源配置规范"
                },
                {
                  "className": {
                    "typography": {
                      "size": "xs"
                    }
                  }
                }
              )
              ]
            }
          }
        ]
      }
    },
    {
      type: 'button',
      label: '校验未通过提示',
      "actionType": "toast",
      "toast": {
        "items": [
          {
            "level": "error",
            "body": "校验未通过，SQL校验不符合规范，错误信息：？"
          }
        ]
      }
    },
    {
      type: 'button',
      label: '校验通过',
      "actionType": "toast",
      "toast": {
        "items": [
          {
            "level": "success",
            "body": "校验成功"
          }
        ]
      }
    }
  ])
};
