import { getMiddleSizeDialogSchema, generateColor, generateFlexItems, generateSpace, generateGroupForm, getDialogGroupPanelNoPaddingSchema, generateHeaderTitle, getCopy, getButtonList } from 'amis-utils';

export default {
  "type": 'page',
  "data": {
    "table": [
      {
        "a": "a1",
        "b": "b1",
        "c": "c1"
      },
      {
        "a": "a2",
        "b": "b2",
        "c": "c2"
      }
    ],
    "title": '获取衍生特征值时，需传入以下参数，请赋值:',
  },
  "body": getButtonList([
    {
      "type": 'button',
      "label": 'table 上下布局',
      "actionType": 'dialog',
      "dialog": getMiddleSizeDialogSchema({
        "title": '在线测试',
        "showCloseButton": false,
        "body": generateGroupForm({
          "title": '',
          "mode": 'normal',
          "api": '/api/mock2/saveForm?waitSeconds=2',
          "actions": [],
          "body": getDialogGroupPanelNoPaddingSchema([
            generateSpace({
              "type": 'panel',
              "title": generateHeaderTitle({
                "type": "tpl",
                "tpl": "获取衍生特征值时，需传入以下参数，请赋值"
              }),
              "body": [
                generateSpace({
                  "type": "input-table",
                  "name": "table",
                  "label": "特征参数",
                  "perPage": 1,
                  "columnRatio": 12,
                  "columns": [
                    {
                      "name": "a",
                      "label": "A"
                    },
                    {
                      "name": "b",
                      "label": "B"
                    },
                    generateFlexItems({
                      "name": "c",
                      "label": "C",
                      "type": "flex",
                      "alignItems": "center",
                      "items": [
                        generateSpace(
                          {
                            "type": "input-text",
                            "placeholder": "请输入数字",
                            "flexGrow": 1,
                          },
                          {
                            "className": {
                              "margin": {
                                "bottom": "none",
                                "right": "sm"
                              }
                            }
                          }
                        ),
                        getCopy({
                          "type": "icon",
                          "flexGrow": 0,
                          "onEvent": {
                            "click": {
                              "actions": [
                                {
                                  "actionType": "copy",
                                  "args": {
                                    "content": "${c}"
                                  }
                                }
                              ]
                            }
                          }
                        })
                      ]
                    })

                  ]
                }, {
                  "className": {
                    "margin": {
                      "bottom": "md"
                    }
                  }
                }),
                {
                  "type": "button",
                  "level": "primary",
                  "columnRatio": 12,
                  "label": "测试"
                }
              ]
            },
              {
                "headerClassName": {
                  "padding": {
                    "bottom": "none"
                  }
                },
                "bodyClassName": {
                  "padding": {
                    "left": "md",
                    "right": "md",
                  }
                },
              }
            ),
            generateSpace({
              "type": 'panel',
              "title": generateHeaderTitle({
                "type": 'tpl',
                "tpl": '测试结果'
              }),
              "body": [
                generateColor(
                  {
                     "type": "tpl",
                     "tpl": "调用成功！",
                   },
                   "success"
               ),
                {
                  "type": 'tpl',
                  "tpl": '结果如下：'
                },
                generateSpace(
                  {
                    "type": "table",
                    "source": "$table",
                    "columns": [
                      {
                        "name": "a",
                        "label": "A"
                      },
                      {
                        "name": "b",
                        "label": "B"
                      },
                      {
                        "name": "c",
                        "label": "C"
                      }
                    ]
                  },
                  {
                    "className": {
                      "margin": {
                        "top": "sm",
                        "bottom": "lg"
                      }
                    }
                  }
                ),
                {
                  "type": 'tpl',
                  "tpl": '接口响应：'
                },
                generateSpace(
                  {
                    "type": "panel",
                    "body": {
                      "type": "code",
                      "language": "json",
                      "formatter": true,
                      "value": "{\"a\":1, \"b\":2, \"c\": {\"c1\": 3}}"
                    }
                  },
                  {
                    "className": {
                      "margin": {
                        "top": "sm",
                        "bottom": "none"
                      }
                    }
                  }
                )
              ]
            },
              {
                "headerClassName": {
                  "padding": {
                    "bottom": "none"
                  }
                },
                "bodyClassName": {
                  "padding": {
                    "left": "md",
                    "right": "md",
                  }
                },
              })
          ], true)
        })
      })
    },
  ])
};
