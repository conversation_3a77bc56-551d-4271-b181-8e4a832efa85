import { getMiddleSizeDialogSchema, getButtonList,getDialogTransfer } from 'amis-utils';


export default {
  type: 'page',
  data: {},
  body: getButtonList([
    {
      type: 'button',
      label: '穿梭弹窗',
      actionType: 'dialog',
      dialog: getMiddleSizeDialogSchema(
        {
          showCloseButton: false,
          title: "标题",
          body: [
            getDialogTransfer({
              "label": "分组",
              "type": "transfer",
              "name": "transfer",
              "selectMode": "tree",
              "searchable": true,
              "sortable": true,
              "options": [
                {
                  "label": "法师",
                  "children": [
                    {
                      "label": "诸葛亮",
                      "value": "zhugeliang"
                    },
                    {
                      "label": "诸葛",
                      "value": "zhugeliang1"
                    },
                    {
                      "label": "诸葛亮撒娇",
                      "value": "zhugeliang2"
                    },
                    {
                      "label": "诸葛亮",
                      "value": "zhugeliang3"
                    },
                    {
                      "label": "诸葛大萨达撒多多少度亮",
                      "value": "zhugeliang4"
                    },
                    {
                      "label": "诸亮",
                      "value": "zhugeliang5"
                    },
                    {
                      "label": "亮",
                      "value": "zhugeliang6"
                    },
                    {
                      "label": "诸葛亮对对对",
                      "value": "zhugeliang7"
                    }
                  ]
                },
                {
                  "label": "法师",
                  "children": [
                    {
                      "label": "诸葛亮",
                      "value": "zhugeliang"
                    },
                    {
                      "label": "诸葛",
                      "value": "zhugeliang1"
                    },
                    {
                      "label": "诸葛亮撒娇",
                      "value": "zhugeliang2"
                    },
                    {
                      "label": "诸葛亮",
                      "value": "zhugeliang3"
                    },
                    {
                      "label": "诸葛大萨达撒多多少度亮",
                      "value": "zhugeliang4"
                    },
                    {
                      "label": "诸亮",
                      "value": "zhugeliang5"
                    },
                    {
                      "label": "亮",
                      "value": "zhugeliang6"
                    },
                    {
                      "label": "诸葛亮对对对",
                      "value": "zhugeliang7"
                    }
                  ]
                },
                {
                  "label": "战士",
                  "children": [
                    {
                      "label": "曹操",
                      "value": "caocao"
                    },
                    {
                      "label": "钟无艳",
                      "value": "zhongwuyan"
                    }
                  ]
                },
                {
                  "label": "打野",
                  "children": [
                    {
                      "label": "李白",
                      "value": "libai"
                    },
                    {
                      "label": "韩信",
                      "value": "hanxin"
                    },
                    {
                      "label": "云中君",
                      "value": "yunzhongjun"
                    }
                  ]
                }
              ]
            })
          ]
        }
      )

    },


  ])
}
