import { getDiffPageSchema, generateStyle, getButtonList, getListDialogSchema, generateGroupForm, generateHeaderTpl, getDialogGroupPanelNoPaddingSchema, generateGroupPanelNoPadding } from 'amis-utils';


const getTpl = (name, status) => {
  let tpl;
  if (status) {
    tpl = "<div title='${" + name + "}' class='pm-ellipsis ${" + status + "=== 'ADD' ? 'pm-versionDiff-add' : (" + status + " === 'EDIT' ? 'pm-versionDiff-edit' : (" + status + " === 'DELETE' ? 'pm-versionDiff-delete' : ''))}'>${" + name + "}</div>";
  } else {
    tpl = "<div title=${" + name + "} class='pm-ellipsis'>${" + name + "}</div>";
  }
  return tpl;
}

export default {
  type: 'page',
  data: {
    table: [
      {
        "a": "a1",
        "b": "b1",
        "c": "c1"
      },
      {
        "a": "a2",
        "b": "b2",
        "c": "c2"
      }
    ],
    "text1": "营销中心",
    "status": "ADD",
    "text1Status": "DELETE",
    "text2Status": "EDIT",
    "text2": 2,
    "text3": 1593327764,
    "text4": "负责人",
    "text5": "12342345657621313",
    "text55": "12342345090",
    "text6": 1593327764,
    "text7": "创建人",
    "text8": "text8",
    "text9": "text9",
    title: '获取衍生特征值时，需传入以下参数，请赋值:',
  },
  body: getButtonList([
    {
      type: 'button',
      label: '版本对比',
      actionType: 'dialog',
      dialog: getListDialogSchema({
        title: '版本对比',
        body: getDiffPageSchema({
          size: "none",
          body: [
            {
              "type": 'form',
              "title": '',
              "actions": [],
              "mode": "horizontal",
              "wrapWithPanel": false,
              "body": generateStyle(
                {
                  "type": 'flex',
                  "justify": "space-between",
                  "alignItems": 'center',
                  "items": [
                    generateStyle(
                      {
                        "type": "select",
                        "label": "基准版本",
                        "name": 'baselineVersion',
                        "value": "v1",
                        "labelWidth": 60,
                        "options": [
                          {
                            "label": 'V1',
                            "value": "v1"
                          },
                          {
                            "label": 'V2',
                            "value": "v2"
                          }
                        ]
                      },
                      {
                        "className": {
                          "spacing": {
                            "margin": {
                              "bottom": "none"
                            }
                          },
                          "flexBox": {
                            "grow": "1"
                          }
                        }
                      }
                    ),
                    {
                      "type": "tpl",
                      "text": "",
                      "style": {
                        "textAlign": "center",
                        "width": 40
                      }
                    },
                    generateStyle(
                      {
                        "type": "select",
                        "label": "对比版本",
                        "name": 'diffVersion',
                        "value": 'v2',
                        "labelWidth": 60,
                        "options": [
                          {
                            "label": 'V1',
                            "value": "v1"
                          },
                          {
                            "label": 'V2',
                            "value": "v2"
                          }
                        ]
                      },
                      {
                        "className": {
                          "spacing": {
                            "margin": {
                              "bottom": "none"
                            }
                          },
                          "flexBox": {
                            "grow": "1"
                          }
                        }
                      }
                    )
                  ]
                },
                {
                  "className": {
                    "spacing": {
                      "margin": {
                        "bottom": "md"
                      }
                    }
                  }
                }
              ),
            },
            {
              "type": 'flex',
              "justify": "space-between flex-grow",
              "alignItems": 'center',
              "items": [
                generateStyle(
                  {
                    "type": "panel",
                    "title": "",
                    "body": generateGroupForm({
                      type: 'form',
                      static: true,
                      actions: [],
                      body: getDialogGroupPanelNoPaddingSchema([
                        {
                          type: 'panel',
                          title: generateHeaderTpl({
                            type: 'tpl',
                            tpl: '第一步，基础信息'
                          }),
                          body: [
                            {
                              type: "group",
                              body: [
                                {
                                  columnRatio: 6,
                                  type: "static-tpl",
                                  name: "text1",
                                  label: "姓名",
                                  tpl: getTpl("text1")
                                },
                                {
                                  type: "static-tpl",
                                  name: "text2",
                                  label: "年龄",
                                  columnRatio: 6,
                                  tpl: getTpl("text2")
                                }
                              ]
                            },
                            {
                              type: "group",
                              body: [
                                {
                                  columnRatio: 6,
                                  type: "static-tpl",
                                  name: "text4",
                                  label: "邮箱",
                                  tpl: getTpl("text4")
                                },
                                {
                                  columnRatio: 6,
                                  type: "tooltip-wrapper",
                                  content: "${text5}",
                                  body: {
                                    type: "static-tpl",
                                    name: "text5",
                                    label: "电话",
                                    tpl: getTpl("text5")
                                  }
                                }
                              ]
                            },
                            {
                              type: "group",
                              body: [
                                {
                                  type: "static-tpl",
                                  name: "text7",
                                  label: "其它",
                                  columnRatio: 6,
                                  tpl: getTpl("text7")
                                }
                              ]
                            }
                          ]
                        },
                        {
                          type: 'panel',
                          title: generateHeaderTpl({
                            type: 'tpl',
                            tpl: '第二步，复杂信息'
                          }),
                          body: [
                            {
                              type: 'group',
                              body: [
                                {
                                  columnRatio: 6,
                                  type: "static-tpl",
                                  name: "text1",
                                  label: "姓名",
                                  tpl: getTpl("text1")
                                },
                                {
                                  columnRatio: 6,
                                  type: "static-tpl",
                                  name: "text2",
                                  label: "年龄",
                                  tpl: getTpl("text2")
                                }
                              ]
                            },
                            {
                              type: "group",
                              body: [
                                {
                                  columnRatio: 6,
                                  type: "static-tpl",
                                  name: "text4",
                                  label: "邮箱",
                                  tpl: getTpl("text4")
                                },
                                {
                                  columnRatio: 6,
                                  type: "tooltip-wrapper",
                                  content: "${text5}",
                                  body: {
                                    type: "static-tpl",
                                    name: "text5",
                                    label: "电话",
                                    tpl: getTpl("text5")
                                  }
                                }
                              ]
                            },
                            {
                              type: "group",
                              body: [
                                {
                                  columnRatio: 6,
                                  type: 'static-tpl',
                                  name: 'text7',
                                  label: '其它',
                                  tpl: getTpl('text7'),
                                },
                              ]
                            }
                          ]
                        }
                      ])
                    })
                  },
                  {
                    "className": {
                      "flexBox": {
                        "grow": "1"
                      }
                    }
                  }
                ),
                {
                  "type": "tpl",
                  "tpl": "",
                  "style": {
                    "textAlign": "center",
                    "width": 40
                  }
                },
                generateStyle(
                  {
                    "type": "panel",
                    "title": "",
                    "body": generateGroupForm({
                      "type": 'form',
                      "static": true,
                      "withoutItemMarginBottom": true,
                      "actions": [],
                      "body": getDialogGroupPanelNoPaddingSchema([
                        {
                          "type": 'panel',
                          "title": generateHeaderTpl({
                            "type": 'tpl',
                            "tpl": '第一步，基础信息'
                          }),
                          "body": [
                            {
                              type: 'group',
                              body: [
                                {
                                  columnRatio: 6,
                                  type: "static-tpl",
                                  name: "text1",
                                  label: "姓名",
                                  tpl: getTpl("text1")
                                },
                                {
                                  columnRatio: 6,
                                  type: "static-tpl",
                                  name: "text2",
                                  label: "年龄",
                                  tpl: getTpl("text2")
                                }
                              ]
                            },
                            {
                              type: "group",
                              body: [
                                {
                                  columnRatio: 6,
                                  type: 'static-tpl',
                                  name: 'text4',
                                  label: '邮箱',
                                  tpl: getTpl('text4', 'status')
                                },
                                {
                                  columnRatio: 6,
                                  type: "tooltip-wrapper",
                                  content: "${text5}",
                                  body: {
                                    type: 'static-tpl',
                                    name: 'text5',
                                    label: '电话',
                                    tpl: getTpl('text5'),
                                  }
                                },
                              ]
                            },
                            {
                              type: "group",
                              body: [
                                {
                                  columnRatio: 6,
                                  type: 'static-tpl',
                                  name: 'text7',
                                  label: '其它',
                                  tpl: getTpl('text7'),
                                },
                              ]
                            }
                          ]
                        },
                        {
                          "type": 'panel',
                          "title": generateHeaderTpl({
                            "type": 'tpl',
                            "tpl": '第二步，复杂信息'
                          }),
                          "body": [
                            {
                              type: 'group',
                              body: [
                                {
                                  columnRatio: 6,
                                  type: 'static-tpl',
                                  label: '姓名',
                                  name: 'text1',
                                  tpl: getTpl('text1', 'text1Status')
                                },
                                {
                                  columnRatio: 6,
                                  type: 'input-text',
                                  name: 'text2',
                                  label: '年龄',
                                  tpl: getTpl('text2', 'text1Status')

                                }
                              ]
                            },
                            {
                              type: "group",
                              body: [
                                {
                                  columnRatio: 6,
                                  type: 'input-text',
                                  name: 'text4',
                                  label: '邮箱',
                                  tpl: getTpl('text4')
                                },
                                {
                                  columnRatio: 6,
                                  type: 'static-tpl',
                                  name: 'text55',
                                  label: '电话',
                                  tpl: getTpl('text55', 'text2Status')
                                }
                              ]
                            },
                            {
                              type: "group",
                              body: [
                                {
                                  columnRatio: 6,
                                  type: 'input-text',
                                  name: 'text7',
                                  label: '其它',
                                  tpl: getTpl('text7')

                                }
                              ]
                            }
                          ]
                        }
                      ])
                    })
                  },
                  {
                    "className": {
                      "flexBox": {
                        "grow": "1"
                      }
                    }
                  }
                )
              ]
            },
          ]
        })
      })
    },
  ])
};
