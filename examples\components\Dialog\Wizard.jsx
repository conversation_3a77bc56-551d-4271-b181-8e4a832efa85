import { generateWizardForDialog, generateSpace,getButtonList } from 'amis-utils';

export default {
  type: 'page',
  body: getButtonList([{
    type: 'button',
    label: '步骤表单',
    actionType: 'dialog',
    dialog: generateSpace( {
      title: '步骤表单',
      showCloseButton: false,
      componentId: "dialog-id",
      actions: [],
      body: {
        type: 'form',
        wrapWithPanel: false,
        api: '/api/mock2/form/saveForm?waitSeconds=2',
        body: generateWizardForDialog({
          api: '/api/mock2/saveForm?waitSeconds=2',
          steps: [
            {
              title: "第一步",
              mode: 'horizontal',
              body: [
                {
                  type: 'input-text',
                  name: 'platform',
                  placeholder: '请输入PlatForm(s)',
                  label: 'PlatForm(s)'
                },
                {
                  type: 'input-text',
                  name: 'cssGrade',
                  label: 'CSS grade',
                  required: true,
                  placeholder: '请输入CSS grade'
                },
                {
                  type: 'input-text',
                  name: 'brower',
                  placeholder: '请输入Brower',
                  label: 'Brower'
                },
                {
                  type: 'input-text',
                  name: 'version',
                  label: 'Version',
                  required: true,
                  placeholder: '请输入Version'
                }
              ],
              actions: [
                {
                  type: "button",
                  label: "取消",
                  actionType: "cancel",
                  componentId: "dialog-id"
                },
                {
                  type: "button",
                  label: "下一步",
                  level: "primary",
                  actionType: "next"
                }
              ],
            },
            {
              title: '第二步',
              mode: 'horizontal',
              body: [
                {
                  type: 'input-text',
                  name: 'platform2',
                  placeholder: '请输入PlatForm(s)',
                  label: 'PlatForm(s)'
                },
                {
                  type: 'input-text',
                  name: 'cssGrade2',
                  label: 'CSS grade',
                  required: true,
                  placeholder: '请输入CSS grade'
                },
                {
                  type: 'input-text',
                  name: 'brower2',
                  placeholder: '请输入Brower',
                  label: 'Brower'
                },
                {
                  type: 'input-text',
                  name: 'version2',
                  label: 'Version',
                  required: true,
                  placeholder: '请输入Version'
                }
              ],
              actions: [
                {
                  type: "button",
                  label: "取消",
                  actionType: "cancel",
                  componentId: "dialog-id"
                },
                {
                  type: "button",
                  label: "上一步",
                  actionType: "prev"
                },
                {
                  type: "button",
                  label: "下一步",
                  level: "primary",
                  actionType: "next"
                }
              ],
            },
            {
              title: '第三步',
              mode: 'horizontal',
              body: [
                {
                  type: 'input-text',
                  name: 'platform3',
                  placeholder: '请输入PlatForm(s)',
                  label: 'PlatForm(s)'
                },
                {
                  type: 'input-text',
                  name: 'cssGrade3',
                  label: 'CSS grade',
                  required: true,
                  placeholder: '请输入CSS grade'
                },
                {
                  type: 'input-text',
                  name: 'brower3',
                  placeholder: '请输入Brower',
                  label: 'Brower'
                },
                {
                  type: 'input-text',
                  name: 'version3',
                  label: 'Version',
                  required: true,
                  placeholder: '请输入Version'
                }
              ],
              actions: [
                {
                  type: "button",
                  label: "取消",
                  actionType: "cancel",
                  componentId: "dialog-id"
                },
                {
                  type: "button",
                  label: "上一步",
                  actionType: "prev"
                },
                {
                  type: "button",
                  label: "下一步",
                  level: "primary",
                  actionType: "next"
                }
              ],
            }
          ]
        })
      }
    },{
      bodyClassName:{
        padding:{
          bottom:"none"
        }
      }
    })

  }])
};
