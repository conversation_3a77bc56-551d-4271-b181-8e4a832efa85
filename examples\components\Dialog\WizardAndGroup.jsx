import { generateStyle, generateW<PERSON>rdForDialog, getCollapseGroup, getCollapsableGroupPanelForWhiteBgSchemaV2, generateCollapseGroupOfTabs, getButtonList, getMiddleSizeDialogSchema, generateGroupPanel, generateHeaderTitle, generateHeaderV2, generateCardV2, generateOperationV2, generateNoMarginInputTable } from 'amis-utils';

export default {
  type: 'page',
  body: getButtonList([{
    type: 'button',
    label: '步骤+分组表单',
    actionType: 'dialog',
    dialog: generateStyle(
      getMiddleSizeDialogSchema({
        title: '步骤+分组表单',
        showCloseButton: false,
        id: "dialog-id",
        data: {
          step: 1
        },
        "actions": [
          {
            "type": "button",
            "label": "取消",
            "actionType": "cancel",
          },
          {
            "type": "button",
            "label": "上一步",
            "hiddenOn": "${step===1}",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "actionType": "prev",
                    "componentId": "dialog-wizard-id"
                  }
                ]
              }
            }
          },
          {
            "type": "button",
            "label": "下一步",
            "level": "primary",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "actionType": "next",
                    "componentId": "dialog-wizard-id"
                  },

                ]
              }
            }
          }
        ],
        body: {
          type: 'form',
          wrapWithPanel: false,
          api: '/api/mock2/form/saveForm?waitSeconds=2',
          body: generateWizardForDialog({
            api: '/api/mock2/saveForm?waitSeconds=2',
            id: "dialog-wizard-id",
            onEvent: {
              stepChange: {
                actions: [
                  {
                    "actionType": "setValue",
                    "componentId": "dialog-id",
                    "args": {
                      "value": {
                        "step": "${step}"
                      }
                    }
                  }
                ]
              }
            },
            steps: [
              {
                title: "第一步",
                mode: 'horizontal',
                body: generateCollapseGroupOfTabs(
                  getCollapseGroup(
                    {
                      "type": "collapse-group",
                      "activeKey": ["1"],
                      "body": getCollapsableGroupPanelForWhiteBgSchemaV2([
                        {
                          "key": '1',
                          "header": generateHeaderTitle({
                            "type": 'tpl',
                            "tpl": '第一步，基础信息'
                          }),
                          "body": [
                            {
                              "type": 'group',
                              "body": [
                                {
                                  "type": 'input-text',
                                  "name": 'text1',
                                  "label": '姓名',
                                },
                                {
                                  "type": 'input-text',
                                  "name": 'text2',
                                  "label": '年龄',
                                },
                                {
                                  "type": 'input-text',
                                  "name": 'text3',
                                  "label": '班级',
                                  "required": true,
                                },
                              ]
                            },
                            {
                              "type": "group",
                              "body": [
                                {
                                  "type": 'input-text',
                                  "name": 'text4',
                                  "label": '邮箱',
                                },
                                {
                                  "type": 'input-text',
                                  "name": 'text5',
                                  "label": '电话',
                                },
                                {
                                  "type": 'input-text',
                                  "name": 'text6',
                                  "label": '地址',
                                  "columnRatio": 4,
                                }
                              ]
                            },
                            {
                              "type": "group",
                              "body": [
                                {
                                  "type": 'input-text',
                                  "name": 'text7',
                                  "label": '其它',
                                  "columnRatio": 4,
                                }
                              ]
                            }
                          ]
                        },
                        {
                          "key": '2',
                          "header": generateHeaderTitle({
                            "type": 'tpl',
                            "tpl": '第二步，复杂信息'
                          }),
                          "body": [
                            {
                              "type": "tpl",
                              "tpl": "系统默认设置信水平：100%"
                            },
                            generateCardV2({
                              "type": 'card',
                              "header": {
                                "title": '驱动指标配置',
                              },
                              "noPadding": {
                                "top": false,
                                "right": false,
                                "bottom": false,
                                "left": false,
                              },
                              "body": [
                                generateStyle(
                                  {
                                    "type": 'group',
                                    "body": [
                                      {
                                        "type": 'select',
                                        "name": 'drive',
                                        "label": '驱动指标',
                                        "required": true,
                                        "columnRatio": 10,
                                        "options": [
                                          {
                                            "label": 'a',
                                            "value": 'a'
                                          },
                                          {
                                            "label": 'b',
                                            "value": 'b'
                                          }
                                        ]
                                      },
                                      generateOperationV2({
                                        "buttons": [
                                          {
                                            "type": 'link',
                                            "body": '新增指标',
                                            "href": 'http://moka.dmz.dev.caijj.net/dataseeddesigndocui/#/amis/zh-CN/components/form/condition-builder'
                                          },
                                          {
                                            "type": "link",
                                            "body": "查看相关实验",
                                            "href": 'http://moka.dmz.dev.caijj.net/dataseeddesigndocui/#/amis/zh-CN/components/form/condition-builder'
                                          }
                                        ]
                                      })
                                    ]
                                  },
                                  {
                                    "className": {
                                      "spacing": {
                                        "padding": {
                                          "bottom": "lg"
                                        }
                                      }
                                    }
                                  }
                                ),
                                generateNoMarginInputTable({
                                  "name": "inputTable",
                                  "columns": [
                                    {
                                      "label": "A",
                                      "name": "a"
                                    },
                                    {
                                      "label": "B",
                                      "name": "b"
                                    }
                                  ]
                                }),
                              ]
                            }),
                            generateCardV2({
                              "type": 'card',
                              "header": {
                                "title": '关注指标配置',
                              },
                              "noPadding": {
                                "top": true,
                                "bottom": true,
                                "right": false,
                                "left": false,
                              },
                              "body": [
                                generateStyle(
                                  {
                                    "type": 'group',
                                    "body": [
                                      {
                                        "type": 'select',
                                        "name": 'drive',
                                        "label": '关注指标',
                                        "required": true,
                                        "columnRatio": 10,
                                        "options": [
                                          {
                                            "label": 'a',
                                            "value": 'a'
                                          },
                                          {
                                            "label": 'b',
                                            "value": 'b'
                                          }
                                        ]
                                      },
                                      generateOperationV2({
                                        "buttons": [
                                          {
                                            "type": 'link',
                                            "body": '新增指标',
                                            "href": 'http://moka.dmz.dev.caijj.net/dataseeddesigndocui/#/amis/zh-CN/components/form/condition-builder'
                                          }
                                        ]
                                      })
                                    ]
                                  },
                                  {
                                    "className": {
                                      "spacing": {
                                        "padding": {
                                          "bottom": "lg"
                                        }
                                      }
                                    }
                                  }
                                ),

                                generateNoMarginInputTable(
                                  {
                                    "name": 'operateTable',
                                    "columns": [
                                      {
                                        "name": 'a',
                                        "label": '指标名称'
                                      },
                                      {
                                        "name": 'b',
                                        "label": '列表2'
                                      },
                                      {
                                        "name": 'c',
                                        "label": '列表3'
                                      },
                                      {
                                        "name": 'd',
                                        "label": '列表4'
                                      },
                                      {
                                        "type": "operation",
                                        "label": "操作",
                                        "buttons": [
                                          {
                                            "label": "",
                                            "type": "button",
                                            "actionType": "dialog",
                                            "icon": "fa fa-plus",
                                            "dialog": {
                                              "title": "弹框",
                                              "showCloseButton": false,
                                              "body": "这是个简单的弹框。"
                                            }
                                          },
                                          {
                                            "label": "",
                                            "type": "button",
                                            "actionType": "dialog",
                                            "icon": "fa fa-pencil-square-o",
                                            "dialog": {
                                              "title": "弹框",
                                              "showCloseButton": false,
                                              "body": "这是个简单的弹框。"
                                            }
                                          },
                                          {
                                            "label": "",
                                            "type": "button",
                                            "actionType": "dialog",
                                            "icon": "fa fa-minus",
                                            "dialog": {
                                              "title": "弹框",
                                              "showCloseButton": false,
                                              "body": "这是个简单的弹框。"
                                            }
                                          }
                                        ]
                                      }
                                    ]
                                  }
                                )
                              ]
                            })
                          ]
                        }
                      ])
                    }
                  )
                ),
                actions: []

              },
              {
                title: '第二步',
                mode: 'horizontal',
                body: [
                  {
                    type: 'input-text',
                    name: 'platform2',
                    placeholder: '请输入PlatForm(s)',
                    label: 'PlatForm(s)'
                  },
                  {
                    type: 'input-text',
                    name: 'cssGrade2',
                    label: 'CSS grade',
                    required: true,
                    placeholder: '请输入CSS grade'
                  },
                  {
                    type: 'input-text',
                    name: 'brower2',
                    placeholder: '请输入Brower',
                    label: 'Brower'
                  },
                  {
                    type: 'input-text',
                    name: 'version2',
                    label: 'Version',
                    required: true,
                    placeholder: '请输入Version'
                  }
                ],
                actions: []

              },
              {
                title: '第三步',
                mode: 'horizontal',
                body: [
                  {
                    type: 'input-text',
                    name: 'platform3',
                    placeholder: '请输入PlatForm(s)',
                    label: 'PlatForm(s)'
                  },
                  {
                    type: 'input-text',
                    name: 'cssGrade3',
                    label: 'CSS grade',
                    required: true,
                    placeholder: '请输入CSS grade'
                  },
                  {
                    type: 'input-text',
                    name: 'brower3',
                    placeholder: '请输入Brower',
                    label: 'Brower'
                  },
                  {
                    type: 'input-text',
                    name: 'version3',
                    label: 'Version',
                    required: true,
                    placeholder: '请输入Version'
                  }
                ],
                actions: []
              }
            ]
          })
        }
      }),
      {
        "bodyClassName": {
          "spacing": {
            "padding": {
              "bottom": "none"
            }
          }
        }
      }
    )
  }])
};
