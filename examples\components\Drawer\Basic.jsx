import { getDefaultDrawer, generateHeaderTitle, getButtonList, generateCommonPage, generateStyle } from "amis-utils";

export default generateCommonPage({
  "type": "page",
  "body": getButtonList([
    {
      "type": "button",
      "label": "标准抽屉",
      "actionType": "drawer",
      "drawer": getDefaultDrawer({
        "showCloseButton": false,
        "title": {
          "type": "flex",
          "justify": 'flex-start',
          "items": [
            generateHeaderTitle({
              "type": "tpl",
              "tpl": "提示",
            }),
            generateStyle(
              {
                "type": "tpl",
                "tpl": "这是小标题"
              },
              {
                "className": {
                  "typography": {
                    "color": "subTitleColor",
                    "size": "md",
                  },
                  "spacing": {
                    "margin": {
                      "left": "sm"
                    }
                  }
                }
              }
            )
          ]
        },
        "body": {
          "type": "form",
          "api": "/api/mock2/form/saveForm?waitSeconds=2",
          "body": [
            {
              "type": "tpl",
              "tpl": "抽屉内容"
            }
          ]
        },

      })
    }, {
      "type": "button",
      "label": "中号抽屉",
      'actionType': "drawer",
      "drawer": {
        "position": "right",
        "showCloseButton": false,
        "size": "lg",
        "title": "提示",
        "body": {
          "type": "form",
          "api": "/api/mock2/form/saveForm?waitSeconds=2",
          "body": [
            {
              "type": "tpl",
              "tpl": "抽屉内容"
            }
          ]
        }
      }
    },
    {
      "type": "button",
      "label": "大号抽屉",
      "actionType": "drawer",
      "drawer": {
        "size": "xl",
        "position": "right",
        "showCloseButton": false,
        "title": "提示",
        "body": {
          "type": "form",
          "api": "/api/mock2/form/saveForm?waitSeconds=2",
          "body": [
            {
              "type": "tpl",
              "tpl": "抽屉内容"
            }
          ]
        }
      }
    },
  ]),
})
