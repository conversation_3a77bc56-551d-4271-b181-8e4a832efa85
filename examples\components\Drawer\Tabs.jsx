import { generateStyle,generateCommonPage,getPaddingTopWrapper, getLgDrawer, getButtonList, getFormTabDetailSchema, getWithoutMarginsCRUDSchema, generateHeaderTitle } from "amis-utils";

const CRUD_SCHEMA = [
  {
    "type": "group",
    "body": [
      {
        "type": "static",
        "name": "text1",
        "label": "归属部门",
        "columnRatio": 4
      },
      {
        "type": "static-mapping",
        "name": "text2",
        "label": "文本2",
        "columnRatio": 4,
        "map": {
          "0": "<span class='label label-info'>一</span>",
          "1": "<span class='label label-success'>二</span>",
          "2": "这是一个映射",
          "3": "<span class='label label-warning'>四</span>",
          "4": "<span class='label label-primary'>五</span>",
          "*": "<span class='label label-default'>-</span>"
        }
      },
      {
        "type": "static-date",
        "name": "text3",
        "label": "文本3",
        "columnRatio": 4
      }
    ]
  },
  {
    "type": "group",
    "body": [
      {
        "type": "static",
        "name": "text4",
        "label": "负责人",
        "columnRatio": 4
      },
      {
        "type": "static",
        "name": "text5",
        "label": "文本5",
        "columnRatio": 4
      },
      {
        "type": "static-datetime",
        "name": "text6",
        "label": "文本6",
        "columnRatio": 4
      }
    ]
  },
  {
    "type": "group",
    "body": [
      {
        "type": "static",
        "name": "text7",
        "label": "营销中心",
        "columnRatio": 4
      },
      {
        "type": "static",
        "name": "text8",
        "label": "文本8",
        "columnRatio": 4
      },
      {
        "type": "static",
        "name": "text9",
        "label": "文本9",
        "columnRatio": 4
      }
    ]
  }
];

export default generateCommonPage({
  "type": "page",
  "data": {
    "text1": "营销中心",
    "text2": 2,
    "text3": 1593327764,
    "text4": "负责人",
    "text5": "text5",
    "text6": 1593327764,
    "text7": "创建人",
    "text8": "text8",
    "text9": "text9",
  },
  "body": getButtonList([
    {
      "type": "button",
      "label": "Tab模式",
      "actionType": "drawer",
      "drawer": getLgDrawer({
        "title": {
          "type": "flex",
          "justify": 'flex-start',
          "items": [
            generateHeaderTitle({
              "type": "tpl",
              "tpl": "详情",
            }),
            generateStyle(
              {
                "type": "tpl",
                "tpl": "这是小标题"
              },
              {
                "className": {
                  "typography": {
                    "color": "subTitleColor",
                    "size": "md",
                  },
                  "spacing": {
                    "margin": {
                      "left": "sm"
                    }
                  }
                }
              }
            )
          ]
        },
        "body": {
          "type": "tabs",
          "tabs": [
            {
              "title": "JSON组件",
              "icon": "fa fa-home",
              "body": [
                {
                  "type": "button",
                  "label": "复制",
                  "level": "primary"
                },
                getPaddingTopWrapper({
                  "body": {
                    "type": "json",
                    "value": {
                      "table": [
                        {
                          "a": "a1",
                          "b": "b1",
                          "id": 1,
                          "children": [
                            {
                              "a": "a1-child1",
                              "b": "b1-child1",
                              "id": "1-1"
                            },
                            {
                              "a": "a1-child2",
                              "b": "b1-child2",
                              "id": "1-2"
                            }
                          ]
                        },
                        {
                          "a": "a2",
                          "b": "b2",
                          "id": 2,
                          "children": [
                            {
                              "a": "a1-child1",
                              "b": "b1-child1",
                              "id": "2-1"
                            },
                            {
                              "a": "a1-child2",
                              "b": "b1-child2",
                              "id": "2-2"
                            }
                          ]
                        },
                        {
                          "a": "a3",
                          "b": "b3",
                          "id": 3,
                          "children": [
                            {
                              "a": "a1-child1",
                              "b": "b1-child1",
                              "id": "3-1"
                            },
                            {
                              "a": "a1-child2",
                              "b": "b1-child2",
                              "id": "3-2"
                            }
                          ]
                        }
                      ]
                    }
                  }
                })
              ]
            },
            {
              "title": "列表",
              "icon": "fab fa-apple",
              "body": getWithoutMarginsCRUDSchema({
                "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?waitSeconds=1",
                "columns": [
                  {
                    "name": "id",
                    "label": "ID"
                  },
                  {
                    "name": "engine",
                    "label": "Rendering engine"
                  },
                  {
                    "name": "browser",
                    "label": "Browser"
                  },
                  {
                    "name": "platform",
                    "label": "Platform(s)"
                  },
                  {
                    "name": "engine",
                    "label": "Engine"
                  },
                  {
                    "name": "version",
                    "label": "Engine Version"
                  },
                  {
                    "name": "grade",
                    "label": "CSS grade"
                  }
                ]
              })
            },
            {
              "title": "基础表单",
              "icon": "fas fa-bug",
              "body": getFormTabDetailSchema({
                "labelWidth": 60,
                "wrapWithPanel": false,
                "body": CRUD_SCHEMA
              })
            }
          ]
        },
        "actions": []
      })
    }
  ])
})
