import { getWizardInDrawer,generateCommonPage } from 'amis-utils';

export default generateCommonPage({
  'type': 'page',
  'body': [
    {
      "type": "button",
      "label": "分步向导+抽屉",
      "actionType": "drawer",
      "drawer": getWizardInDrawer({
        "showCloseButton": false,
        "title": "抽屉标题",
        "body": {
          "type": "wizard",
          "initApi": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm?waitSeconds=2",
          "delimiter": "arrow",
          "steps": [
            {
              "title": "第一步",
              "body": [
                {
                  "name": "website",
                  "label": "网址",
                  "type": "input-url",
                },
                {
                  "name": "email",
                  "label": "邮箱",
                  "type": "input-email",
                }
              ],
              "actions": [
                {
                  "type": "button",
                  "label": "下一步",
                  "actionType": "next"
                },
                {
                  "type": "button",
                  "label": "提交",
                  "actionType": "submit",
                  "level": "primary"
                }
              ]
            },
            {
              "title": "第二步",
              "body": [
                {
                  "name": "email2",
                  "label": "邮箱",
                  "type": "input-email",
                  "required": true
                }
              ],
              "actions": [
                {
                  "type": "button",
                  "label": "上一步",
                  "actionType": "prev"
                },
                {
                  "type": "button",
                  "label": "下一步",
                  "actionType": "next"
                },
                {
                  "type": "button",
                  "label": "提交",
                  "actionType": "submit",
                  "level": "primary"
                }
              ]
            },
            {
              "title": "第三步",
              "body": [
                "这是最后一步了"
              ],
              "actions": [
                {
                  "type": "button",
                  "label": "上一步",
                  "actionType": "prev"
                },
                {
                  "type": "button",
                  "label": "提交",
                  "actionType": "submit",
                  "level": "primary"
                }
              ]
            }
          ]
        }
      })
    }
  ]
})
