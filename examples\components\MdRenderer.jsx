/* eslint-disable no-unused-vars */
import React from 'react';
import ReactDOM, {findDOMNode} from 'react-dom';
import {getTheme, render} from 'amis';
import {LazyComponent} from 'amis';
import {Overlay} from 'amis';
import {PopOver} from 'amis';
import classnames from 'classnames';
import {Link} from 'react-router-dom';
import Play from './Play';
// import mermaid from 'mermaid';
// 使用全局 mermaid 对象，由 mermaid.js UMD 文件提供
import MonacoCodeBlock from './MonacoCodeBlock';

// 动态加载 mermaid
let mermaidLoaded = false;
let mermaidPromise = null;

function loadMermaid() {
  if (mermaidLoaded && window.mermaid) {
    return Promise.resolve(window.mermaid);
  }

  if (mermaidPromise) {
    return mermaidPromise;
  }

  mermaidPromise = new Promise((resolve, reject) => {
    if (window.mermaid) {
      mermaidLoaded = true;
      resolve(window.mermaid);
      return;
    }

    // 动态加载 mermaid.js，使用 ES6 动态导入
    import('mermaid').then(mermaidModule => {
      const mermaid = mermaidModule.default || mermaidModule;
      window.mermaid = mermaid;
      mermaidLoaded = true;

      // 初始化 mermaid
      mermaid.initialize({
        startOnLoad: false,
        theme: 'default',
        securityLevel: 'loose',
        fontFamily: 'inherit',
        // 支持中文和特殊字符
        htmlLabels: true,
        flowchart: {
          htmlLabels: true,
          useMaxWidth: false // 禁用自动宽度调整，保持SVG原始尺寸
        },
        // 解决中文解析问题
        deterministicIds: true,
        deterministicIDSeed: 'mermaid'
      });

      resolve(mermaid);
    }).catch(reject);
  });

  return mermaidPromise;
}

// 移除原来的初始化代码
/*
// 初始化 mermaid
mermaid.initialize({
  startOnLoad: false,
  theme: 'default',
  securityLevel: 'loose',
  fontFamily: 'inherit',
  // 支持中文和特殊字符
  htmlLabels: true,
  flowchart: {
    htmlLabels: true,
    useMaxWidth: true
  },
  // 解决中文解析问题
  deterministicIds: true,
  deterministicIDSeed: 'mermaid'
});
*/

class CodePreview extends React.Component {
  state = {
    PlayGround: null
  };

  render() {
    const {container, setAsideFolded, setHeaderVisible, ...rest} = this.props;

    return <Play {...rest} mini />;
  }
}

function eachDom(dom, iterator) {
  if (!dom) {
    return;
  }

  iterator(dom);

  if (dom.children && dom.children.length) {
    [].slice.call(dom.children).forEach(dom => eachDom(dom, iterator));
  }
}

class Preview extends React.Component {
  static displayName = 'MarkdownRenderer';
  ref = null;
  rootDOMs = [];
  constructor(props) {
    super(props);
    this.divRef = this.divRef.bind(this);
  }

  componentDidMount() {
    this.renderSchema();
    this.renderMermaid();
    this.fixHtmlPreview();

    // 延迟执行代码折叠初始化，确保所有内容都已渲染
    setTimeout(() => {
      this.initCodeFolding();
    }, 100);

    if (location.hash && location.hash.length > 1) {
      // 禁用自动跳转
      if (window.history && 'scrollRestoration' in window.history) {
        window.history.scrollRestoration = 'manual';
      }

      const dom = document.querySelector(
        `[name="${location.hash.substring(1)}"]`
      );
      dom && dom.scrollIntoView();
    }
  }

  componentDidUpdate() {
    this.renderSchema();
    this.renderMermaid();
    this.fixHtmlPreview();

    // 延迟执行代码折叠初始化
    setTimeout(() => {
      this.initCodeFolding();
    }, 100);
  }

  componentWillUnmount() {
    this.rootDOMs.forEach(dom => ReactDOM.unmountComponentAtNode(dom));
  }

  divRef(ref) {
    this.ref = ref;

    if (ref) {
      ref.innerHTML = this.props.doc.html;
    }
  }

  renderSchema() {
    const scripts = document.querySelectorAll('script[type="text/schema"]');
    if (!scripts && !scripts.length) {
      return;
    }

    for (let i = 0, len = scripts.length; i < len; i++) {
      let script = scripts[i];
      let props = {};
      [].slice.apply(script.attributes).forEach(item => {
        props[item.name] = item.value;
      });

      let dom = document.createElement('div');
      let height = props.height ? parseInt(props.height, 10) : 200;

      if (this.props.viewMode === 'mobile') {
        // 移动端下高度不能太低
        if (height < 500) {
          height = 500;
        }
      }

      dom.setAttribute('class', 'doc-play-ground');
      // dom.setAttribute('style', `min-height: ${height}px;`);
      const origin = script.parentNode;
      origin.parentNode.replaceChild(dom, origin);

      ReactDOM.render(
        <LazyComponent
          {...this.props}
          container={() => findDOMNode(this)}
          component={CodePreview}
          code={script.innerText}
          scope={props.scope}
          // unMountOnHidden
          height={height}
          placeholder="加载中，请稍后。。。"
        />,
        dom
      );
      this.rootDOMs.push(dom);
    }
  }

  fixHtmlPreview() {
    const htmlPreviews = document.querySelectorAll('.amis-doc>.preview');
    if (!htmlPreviews && !htmlPreviews.length) {
      return;
    }
    const ns = getTheme(this.props.theme)?.classPrefix;
    [].slice.call(htmlPreviews).forEach(dom => {
      eachDom(dom, dom => {
        if (typeof dom.className !== 'string') {
          return;
        }

        dom.className = dom.className.replace(
          /(^|\s)([A-Z])/g,
          '$1' + ns + '$2'
        );
      });
    });
  }

  renderMermaid() {
    const scripts = document.querySelectorAll('script[type="text/mermaid"]');
    if (!scripts || !scripts.length) {
      return;
    }

    for (let i = 0, len = scripts.length; i < len; i++) {
      let script = scripts[i];
      let mermaidCode = script.innerText.trim();

      if (!mermaidCode) {
        continue;
      }

      let dom = document.createElement('div');
      dom.setAttribute('class', 'mermaid-preview');

      const origin = script.parentNode;

      // 安全检查：确保父节点存在
      if (!origin || !origin.parentNode) {
        console.warn('Mermaid script parent node not found, skipping render');
        continue;
      }

      origin.parentNode.replaceChild(dom, origin);

      // 使用动态加载的 mermaid
      loadMermaid().then(mermaid => {
        try {
          const graphId = `mermaid-graph-${Date.now()}-${i}`;

          // 先验证语法
          mermaid.parse(mermaidCode)
          .then(() => {
            // 语法正确，开始渲染
            return mermaid.render(graphId, mermaidCode);
          })
          .then(({ svg }) => {
            // 创建带全屏功能的容器
            const container = document.createElement('div');
            container.className = 'mermaid-container';
            container.style.cssText = `
              position: relative;
              border: 1px solid #e8e8e8;
              border-radius: 8px;
              padding: 8px;
              margin: 8px 0;
              background: #fafafa;
              overflow-x: auto;
              display: flex;
              justify-content: center;
            `;

            // 创建全屏按钮
            const fullscreenBtn = document.createElement('button');
            fullscreenBtn.className = 'mermaid-fullscreen-btn';
            fullscreenBtn.innerHTML = '⛶ 全屏';
            fullscreenBtn.title = '点击全屏查看流程图';
            fullscreenBtn.style.cssText = `
              position: absolute;
              top: -1px;
              right: 0;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              border: none;
              border-radius: 0 7px 0 8px;
              padding: 6px 12px;
              cursor: pointer;
              font-size: 12px;
              font-weight: 500;
              z-index: 10;
              transition: all 0.3s ease;
              opacity: 0.8;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            `;

            // 容器悬停时显示按钮，增强可发现性
            container.addEventListener('mouseenter', () => {
              fullscreenBtn.style.opacity = '1';
              fullscreenBtn.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';
              container.style.borderColor = '#d0d0d0';
            });

            container.addEventListener('mouseleave', () => {
              fullscreenBtn.style.opacity = '0.8';
              fullscreenBtn.style.transform = 'translateY(0)';
              fullscreenBtn.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
              container.style.borderColor = '#e8e8e8';
            });

            // 按钮悬停效果
            fullscreenBtn.addEventListener('mouseenter', () => {
              fullscreenBtn.style.background = 'linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%)';
              fullscreenBtn.style.boxShadow = '0 6px 12px rgba(0,0,0,0.2)';
            });

            fullscreenBtn.addEventListener('mouseleave', () => {
              fullscreenBtn.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            });

            // 全屏查看功能
            fullscreenBtn.addEventListener('click', () => {
              this.showMermaidFullscreen(svg, mermaidCode);
            });

            // 设置容器内容
            container.innerHTML = svg;

            // 优化SVG样式，减少空白
            const svgElement = container.querySelector('svg');
            if (svgElement) {
              svgElement.style.cssText = `
                max-width: 100%;
                height: auto;
                display: block;
              `;
            }

            container.appendChild(fullscreenBtn);

            dom.innerHTML = '';
            dom.appendChild(container);
          })
          .catch(error => {
            console.error('Mermaid render error:', error);
            // 提供更友好的错误显示
            dom.innerHTML = `
              <div class="mermaid-error-container" style="border: 1px solid #f5c6cb; background: #f8d7da; color: #721c24; padding: 1rem; border-radius: 4px; margin: 1rem 0;">
                <h4 style="margin: 0 0 0.5rem 0; color: #721c24;">🚫 Mermaid 图表渲染失败</h4>
                <p style="margin: 0 0 0.5rem 0; font-size: 0.9em;">错误信息：${error.message}</p>
                <details style="margin-top: 0.5rem;">
                  <summary style="cursor: pointer; color: #495057;">📝 查看原始代码</summary>
                  <pre style="background: #f8f9fa; padding: 0.5rem; border-radius: 3px; margin-top: 0.5rem; overflow-x: auto;"><code>${mermaidCode}</code></pre>
                </details>
              </div>
            `;
          });
      } catch (error) {
        console.error('Mermaid render error:', error);
        dom.innerHTML = `
          <div class="mermaid-error-container" style="border: 1px solid #f5c6cb; background: #f8d7da; color: #721c24; padding: 1rem; border-radius: 4px; margin: 1rem 0;">
            <h4 style="margin: 0 0 0.5rem 0; color: #721c24;">🚫 Mermaid 图表渲染失败</h4>
            <p style="margin: 0 0 0.5rem 0; font-size: 0.9em;">错误信息：${error.message}</p>
            <details style="margin-top: 0.5rem;">
              <summary style="cursor: pointer; color: #495057;">📝 查看原始代码</summary>
              <pre style="background: #f8f9fa; padding: 0.5rem; border-radius: 3px; margin-top: 0.5rem; overflow-x: auto;"><code>${mermaidCode}</code></pre>
            </details>
          </div>
        `;
        }
      }).catch(loadError => {
        console.error('Failed to load mermaid:', loadError);
        dom.innerHTML = `
          <div class="mermaid-error-container" style="border: 1px solid #f5c6cb; background: #f8d7da; color: #721c24; padding: 1rem; border-radius: 4px; margin: 1rem 0;">
            <h4 style="margin: 0 0 0.5rem 0; color: #721c24;">🚫 Mermaid 加载失败</h4>
            <p style="margin: 0 0 0.5rem 0; font-size: 0.9em;">错误信息：${loadError.message}</p>
            <details style="margin-top: 0.5rem;">
              <summary style="cursor: pointer; color: #495057;">📝 查看原始代码</summary>
              <pre style="background: #f8f9fa; padding: 0.5rem; border-radius: 3px; margin-top: 0.5rem; overflow-x: auto;"><code>${mermaidCode}</code></pre>
            </details>
          </div>
        `;
      });

      this.rootDOMs.push(dom);
    }
  }

  async showMermaidFullscreen(svg, mermaidCode) {
    try {
      // 保存当前滚动位置
      const savedScrollX = window.scrollX || window.pageXOffset;
      const savedScrollY = window.scrollY || window.pageYOffset;

      // 创建全屏容器
      const fullscreenContainer = document.createElement('div');
      fullscreenContainer.className = 'mermaid-native-fullscreen';
      fullscreenContainer.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: #ffffff;
        z-index: 9999;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      `;

      // 创建顶部工具栏
      const toolbar = document.createElement('div');
      toolbar.style.cssText = `
        position: absolute;
        top: 20px;
        right: 20px;
        z-index: 10;
        display: flex;
        gap: 10px;
      `;

      // 创建关闭按钮
      const closeBtn = document.createElement('button');
      closeBtn.innerHTML = '✕ 退出全屏 (ESC)';
      closeBtn.title = '退出全屏';
      closeBtn.style.cssText = `
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        backdrop-filter: blur(10px);
      `;

      // 关闭按钮悬停效果
      closeBtn.addEventListener('mouseenter', () => {
        closeBtn.style.background = 'linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%)';
        closeBtn.style.transform = 'translateY(-2px) scale(1.05)';
        closeBtn.style.boxShadow = '0 6px 20px rgba(102, 126, 234, 0.4)';
      });
      closeBtn.addEventListener('mouseleave', () => {
        closeBtn.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        closeBtn.style.transform = 'translateY(0) scale(1)';
        closeBtn.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.3)';
      });

            // 创建 SVG 容器
      const svgContainer = document.createElement('div');
      svgContainer.className = 'mermaid-native-svg-container';
      svgContainer.innerHTML = svg;
      svgContainer.style.cssText = `
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 60px 20px 20px 20px;
        box-sizing: border-box;
        overflow: auto;
      `;

      // 让SVG自适应容器大小，全屏时放大显示
      const svgElement = svgContainer.querySelector('svg');
      if (svgElement) {
        // 获取SVG原始尺寸
        const originalWidth = svgElement.getAttribute('width') || svgElement.viewBox?.baseVal?.width || 800;
        const originalHeight = svgElement.getAttribute('height') || svgElement.viewBox?.baseVal?.height || 600;

        // 计算合适的缩放比例，确保图表充分利用屏幕空间
        const containerWidth = window.innerWidth - 80; // 留出边距
        const containerHeight = window.innerHeight - 120; // 留出工具栏和边距

        const scaleX = containerWidth / originalWidth;
        const scaleY = containerHeight / originalHeight;
        const scale = Math.min(scaleX, scaleY, 2.5); // 最大放大2.5倍，避免过度放大

        svgElement.style.cssText = `
          max-width: 100%;
          max-height: 100%;
          width: ${originalWidth * scale}px;
          height: ${originalHeight * scale}px;
          display: block;
          transform-origin: center;
        `;
      }

      // 组装元素
      toolbar.appendChild(closeBtn);
      fullscreenContainer.appendChild(toolbar);
      fullscreenContainer.appendChild(svgContainer);
      document.body.appendChild(fullscreenContainer);

      // 请求原生全屏
      if (fullscreenContainer.requestFullscreen) {
        await fullscreenContainer.requestFullscreen();
      } else if (fullscreenContainer.webkitRequestFullscreen) {
        await fullscreenContainer.webkitRequestFullscreen();
      } else if (fullscreenContainer.msRequestFullscreen) {
        await fullscreenContainer.msRequestFullscreen();
      } else {
        // 不支持全屏API，回退到模拟全屏
        throw new Error('Fullscreen API not supported');
      }

      // 退出全屏功能
      function exitFullscreen() {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
      }

      closeBtn.addEventListener('click', exitFullscreen);

      // 监听全屏状态变化
      function handleFullscreenChange() {
        const isFullscreen = document.fullscreenElement ||
                           document.webkitFullscreenElement ||
                           document.msFullscreenElement;

        if (!isFullscreen) {
          // 退出全屏，清理元素
          if (document.body.contains(fullscreenContainer)) {
            document.body.removeChild(fullscreenContainer);
          }

          // 恢复滚动位置
          setTimeout(() => {
            window.scrollTo(savedScrollX, savedScrollY);
          }, 100); // 稍微延迟确保浏览器完成退出全屏的处理

          // 移除事件监听器
          document.removeEventListener('fullscreenchange', handleFullscreenChange);
          document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
          document.removeEventListener('msfullscreenchange', handleFullscreenChange);
          document.removeEventListener('keydown', handleEscape);
        }
      }

      // ESC键退出
      function handleEscape(e) {
        if (e.key === 'Escape') {
          exitFullscreen();
        }
      }

      // 添加事件监听器
      document.addEventListener('fullscreenchange', handleFullscreenChange);
      document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.addEventListener('msfullscreenchange', handleFullscreenChange);
      document.addEventListener('keydown', handleEscape);

    } catch (error) {
      console.warn('Fullscreen failed:', error);
    }
  }

      initCodeFolding() {
    // 查找所有标记为需要Monaco的可折叠代码块容器
    const collapsibleContainers = document.querySelectorAll('.collapsible-code-block[data-monaco="true"]');

    collapsibleContainers.forEach((container, index) => {
      const codeBlock = container.querySelector('pre code');
      if (codeBlock) {
        // 扩展支持的语言类型，包括没有明确语言标识的
        const isSupported = codeBlock.classList.contains('lang-javascript') ||
                           codeBlock.classList.contains('lang-json') ||
                           codeBlock.classList.contains('lang-js') ||
                           codeBlock.className === '' || // 没有语言标识的代码块
                           !codeBlock.className.includes('lang-'); // 或者没有 lang- 前缀的

        if (isSupported) {
          this.replaceWithMonaco(codeBlock, container, index);
        }
      }
    });
  }

            replaceWithMonaco(codeBlock, collapsibleContainer, blockIndex) {
    const code = codeBlock.textContent || codeBlock.innerText;

    // 更智能的语言检测
    let language = 'javascript'; // 默认
    if (codeBlock.classList.contains('lang-json')) {
      language = 'json';
    } else if (codeBlock.classList.contains('lang-javascript') || codeBlock.classList.contains('lang-js')) {
      language = 'javascript';
    } else {
      // 尝试根据代码内容判断
      const trimmedCode = code.trim();
      try {
        JSON.parse(trimmedCode);
        language = 'json'; // 如果能解析为 JSON，就认为是 JSON
      } catch (e) {
        language = 'javascript'; // 否则认为是 JavaScript
      }
    }

    // 检测是否有多个 export default
    const exportMatches = code.match(/export\s+default\s+/g);
    const hasMultipleExports = exportMatches && exportMatches.length > 1;

    if (hasMultipleExports) {
      this.createMultipleMonacoBlocks(code, language, collapsibleContainer);
    } else {
      this.createSingleMonacoBlock(code, language, collapsibleContainer, blockIndex);
    }
  }

    createMultipleMonacoBlocks(code, language, collapsibleContainer) {
    // 更智能的拆分逻辑：将每个export default与其前面的注释组合
    const lines = code.split('\n');
    const parts = [];
    let currentPart = [];
    let foundFirstExport = false;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // 检查是否是export default行
      if (/export\s+default\s+/.test(line)) {
        if (foundFirstExport) {
          // 如果已经找到过export default，保存当前部分，开始新部分
          if (currentPart.length > 0) {
            parts.push(currentPart.join('\n'));
          }
          currentPart = [line];
        } else {
          // 第一个export default，将之前的内容也包含进来
          currentPart.push(line);
          foundFirstExport = true;
        }
      } else {
        currentPart.push(line);
      }
    }

    // 添加最后一个部分
    if (currentPart.length > 0) {
      parts.push(currentPart.join('\n'));
    }

    const validParts = parts.filter(part => part.trim().length > 0 && /export\s+default\s+/.test(part));

    // 创建容器包装多个 Monaco 编辑器
    const wrapperContainer = document.createElement('div');
    wrapperContainer.style.margin = '20px 0';

    // 替换原容器
    if (collapsibleContainer.parentNode) {
      collapsibleContainer.parentNode.replaceChild(wrapperContainer, collapsibleContainer);

      // 为每个部分创建独立的 Monaco 编辑器
      validParts.forEach((part, index) => {
        const partContainer = document.createElement('div');
        partContainer.style.marginBottom = index < validParts.length - 1 ? '16px' : '0';
        wrapperContainer.appendChild(partContainer);

        ReactDOM.render(
          <MonacoCodeBlock
            code={part.trim()}
            language={language}
            title={`示例 ${index + 1}`}
            height="auto"
          />,
          partContainer
        );

        this.rootDOMs.push(partContainer);
      });
    }
  }

    createSingleMonacoBlock(code, language, collapsibleContainer, blockIndex) {
    // 统一使用简洁的示例标题
    const title = `示例 1`;

    // 创建 Monaco 容器
    const monacoContainer = document.createElement('div');
    monacoContainer.style.margin = '20px 0';

    // 替换整个可折叠容器
    if (collapsibleContainer.parentNode) {
      collapsibleContainer.parentNode.replaceChild(monacoContainer, collapsibleContainer);

      // 使用 React 渲染 Monaco 组件
      ReactDOM.render(
        <MonacoCodeBlock
          code={code}
          language={language}
          title={title}
          height="auto"
        />,
        monacoContainer
      );

      // 保存到 rootDOMs 用于清理
      this.rootDOMs.push(monacoContainer);
    }
  }

  render() {
    return (
      <div className="MDPreview">
        <div className="markdown" ref={this.divRef}>
          Doc
        </div>
      </div>
    );
  }
}

export default function (doc) {
  doc = doc.default || doc;
  return class extends React.Component {
    popoverDom = null;

    originTitle = document.title;

    state = {
      headingPopover: false
    };

    popoverRef = ref => {
      this.popoverDom = ref;
    };

    jump2Anchor(id) {
      if (!id) {
        return;
      }
      const dom = document.querySelector(
        `[name="${id}"]`
      );
      dom && dom.scrollIntoView();

      return false;
    }

    renderHeading(children) {
      return children.map((child, idx) => (
        <div
          key={`${child.fullPath}-${idx}`}
          className={classnames('Doc-headingList-item', {
            'is-active': this.props.location.hash === child.fullPath
          })}
        >
          {/* <a href={`#${child.fragment}`}>{child.label}</a> */}
          <a id={`#${decodeURIComponent(child.fragment)}`} onClick={() => {this.jump2Anchor(child.fragment)}}>{child.label}</a>

          {child.children && child.children.length
            ? this.renderHeading(child.children)
            : null}
        </div>
      ));
    }

    handlePopOverClick = e => {
      this.setState({headingPopover: false});
      e.stopPropagation();
      // e.preventDefault();
    };

    renderHeadingPopover() {
      return this.state.headingPopover ? (
        <Overlay
          target={this.popoverDom}
          container={this.popoverDom}
          rootClose={false}
          placement="right-bottom-right-top"
          show
        >
          <PopOver
            classPrefix="antd-"
            className=":Doc-headingPopover"
            onHide={() => this.setState({headingPopover: false})}
            overlay
            onClick={this.handlePopOverClick}
          >
            {this.renderHeading(doc.toc.children)}
          </PopOver>
        </Overlay>
      ) : null;
    }

    componentDidMount() {
      if (doc.title) {
        document.title = doc.title;
      }
    }

    componentWillUnmount() {
      document.title = this.originTitle;
    }

    pathJoin(...parts) {
      const separator = '/';
      const normalizedParts = parts
        .filter(
          part =>
            part != null &&
            (typeof part === 'string' || typeof part === 'number')
        )
        .map((item, index, arr) => {
          let part = `${item}`;

          // 去除首个元素之外的"/"前缀
          if (index > 0) {
            part = part.replace(/^[\/]+/, '');
          }

          // 去除中间元素的"/"后缀，最后一个元素的多个"/"后缀改为1个
          return index < arr.length - 1
            ? part.replace(/[\/]+$/, '')
            : part.replace(/[\/]+$/, '/');
        });

      return normalizedParts.join(separator);
    }

    getDocEditLink() {
      const {ContextPath} = this.props;
      const basePath = 'https://github.com/baidu/amis/edit/master';

      try {
        const [urlPath, locale, moduleName, relativePath] = location.pathname
          .replace(ContextPath, '')
          .match(/^\/(zh-CN)\/(docs|components|style|)(([\/]?[\w-]+)*)/);

        if (moduleName === 'docs') {
          return this.pathJoin(
            basePath,
            `/docs/${locale}/`,
            `${relativePath}.md`
          );
        } else if (
          moduleName === 'style' &&
          !/style\/(index|css-vars|responsive-design|state)$/.test(urlPath)
        ) {
          const fileName = location.pathname.split('/')?.slice(-1)?.[0];

          return this.pathJoin(
            basePath,
            `/packages/amis-ui/scss/helper`,
            relativePath.replace(fileName, `/_${fileName}.scss`)
          );
        } else {
          return this.pathJoin(
            basePath,
            `/docs/${locale}/${moduleName}`,
            `/${relativePath}.md`
          );
        }
      } catch (error) {
        return this.pathJoin(basePath, 'docs');
      }
    }

    render() {
      const {prevDoc, nextDoc, ContextPath} = this.props;

      return (
        <>
          <div className="Doc-content">
            {doc.title ? (
              <div className="Doc-title">
                <h1>{doc.title}</h1>

                {doc?.toc.children?.length ? (
                  <div
                    ref={this.popoverRef}
                    onClick={e =>
                      this.setState({
                        headingPopover: !this.state.headingPopover
                      })
                    }
                    className="Doc-headingPopBtn visible-xs"
                  >
                    <i className="fa fa-align-right"></i>
                    {this.renderHeadingPopover()}
                  </div>
                ) : null}
              </div>
            ) : null}

            <Preview {...this.props} doc={doc} />

            {/* <div className="Doc-footer">
              <div className="Doc-navLinks">
                {prevDoc ? (
                  <Link
                    className="Doc-navLinks--prev"
                    to={`${ContextPath}${prevDoc.path}`}
                  >
                    <div className="Doc-navLinks-icon">
                      <i className="iconfont icon-arrow-left"></i>
                    </div>

                    <div className="Doc-navLinks-body text-right">
                      <div className="Doc-navLinks-subtitle">
                        上一篇 - {prevDoc.group || '其他'}
                      </div>
                      <div className="Doc-navLinks-title">{prevDoc.label} </div>
                    </div>
                  </Link>
                ) : null}

                {nextDoc ? (
                  <Link
                    className="Doc-navLinks--next"
                    to={`${ContextPath}${nextDoc.path}`}
                  >
                    <div className="Doc-navLinks-body">
                      <div className="Doc-navLinks-subtitle">
                        下一篇 - {nextDoc.group || '其他'}
                      </div>
                      <div className="Doc-navLinks-title">{nextDoc.label}</div>
                    </div>

                    <div className="Doc-navLinks-icon">
                      <i className="iconfont icon-arrow-right"></i>
                    </div>
                  </Link>
                ) : null}
              </div>
              <div className="Doc-footer-divider"></div>
              <div className="Doc-footer-fixme">
                文档有误？
                <a
                  href={this.getDocEditLink()}
                  rel="noopener noreferrer"
                  target="_blank"
                >
                  在 Github 上编辑此页！
                </a>
              </div>
            </div> */}
          </div>
          {doc.toc && doc.toc.children && doc.toc.children.length > 0 ? (
            <div className="Doc-toc hidden-xs hidden-sm">
              <div>
                <div className="Doc-headingList">
                  {this.renderHeading(doc.toc.children)}
                </div>
              </div>
            </div>
          ) : null}
        </>
      );
    }
  };
}
