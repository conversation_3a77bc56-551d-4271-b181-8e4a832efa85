import React, { useEffect, useRef } from 'react';

// 使用 CDN 加载 Monaco Editor，避免 webpack 配置问题
const loadMonaco = () => {
  return new Promise((resolve) => {
    if (window.monaco) {
      resolve(window.monaco);
      return;
    }

    // 设置 Monaco Environment，配置 Worker
    window.MonacoEnvironment = {
      getWorkerUrl: function (moduleId, label) {
        if (label === 'json') {
          return 'https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs/language/json/json.worker.js';
        }
        if (label === 'css' || label === 'scss' || label === 'less') {
          return 'https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs/language/css/css.worker.js';
        }
        if (label === 'html' || label === 'handlebars' || label === 'razor') {
          return 'https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs/language/html/html.worker.js';
        }
        if (label === 'typescript' || label === 'javascript') {
          return 'https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs/language/typescript/ts.worker.js';
        }
        return 'https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs/editor/editor.worker.js';
      }
    };

    // 动态加载 Monaco Editor
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs/loader.js';
    script.onload = () => {
      window.require.config({
        paths: {
          vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs'
        }
      });

      window.require(['vs/editor/editor.main'], () => {
        resolve(window.monaco);
      });
    };
    document.head.appendChild(script);
  });
};

const MonacoCodeBlock = ({
  code,
  language = 'javascript',
  title = '代码示例',
  height = 'auto'
}) => {
  const containerRef = useRef(null);
  const [editor, setEditor] = React.useState(null);
  const [isOpen, setIsOpen] = React.useState(false);
  const [copied, setCopied] = React.useState(false);
  const [monaco, setMonaco] = React.useState(null);

  // 自动计算高度
  const calculateHeight = (code) => {
    const lines = code.split('\n').length;
    const lineHeight = 19; // Monaco 默认行高
    const padding = 20; // 上下内边距
    const maxHeight = 500; // 最大高度
    const minHeight = 100; // 最小高度

    const calculatedHeight = Math.min(Math.max(lines * lineHeight + padding, minHeight), maxHeight);
    return calculatedHeight;
  };

  const actualHeight = height === 'auto' ? calculateHeight(code) : height;

  // 提前加载 Monaco
  useEffect(() => {
    if (!window.monaco) {
      loadMonaco().then((monaco) => {
        setMonaco(monaco);
      });
    } else {
      setMonaco(window.monaco);
    }
  }, []);

  // 只在展开时创建编辑器实例
  useEffect(() => {
    if (isOpen && containerRef.current && monaco && !editor) {
      const editorInstance = monaco.editor.create(containerRef.current, {
        value: code,
        language: language,
        theme: 'vs', // 可以改为 'vs-dark' 支持暗色主题
        readOnly: true,
        minimap: { enabled: false },
        scrollBeyondLastLine: false,
        automaticLayout: true,
        folding: true, // 启用 Monaco 内部代码折叠
        foldingStrategy: 'indentation', // 基于缩进的折叠策略
        showFoldingControls: 'always', // 总是显示折叠控件
        wordWrap: 'on',
        lineNumbers: 'on',
        glyphMargin: false,
        lineDecorationsWidth: 0,
        lineNumbersMinChars: 3,
        fontSize: 13,
        fontFamily: "'Consolas', 'Monaco', 'Courier New', monospace",
        contextmenu: false, // 禁用右键菜单
        selectOnLineNumbers: false,
        scrollbar: {
          verticalScrollbarSize: 8,
          horizontalScrollbarSize: 8
        }
      });

      setEditor(editorInstance);

      // 监听主题变化
      const checkTheme = () => {
        const isDark = document.body.classList.contains('dark');
        editorInstance.updateOptions({
          theme: isDark ? 'vs-dark' : 'vs'
        });
      };

      // 初始主题设置
      checkTheme();

      // 监听主题变化
      const observer = new MutationObserver(checkTheme);
      observer.observe(document.body, {
        attributes: true,
        attributeFilter: ['class']
      });

      return () => {
        observer.disconnect();
        editorInstance.dispose();
      };
    } else if (!isOpen && editor) {
      // 关闭时销毁编辑器
      editor.dispose();
      setEditor(null);
    }
  }, [isOpen, monaco, code, language]);

  // 清理编辑器
  useEffect(() => {
    return () => {
      if (editor) {
        editor.dispose();
      }
    };
  }, [editor]);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      // 2秒后恢复原状态
      setTimeout(() => {
        setCopied(false);
      }, 2000);
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

        return (
    <div className="monaco-code-block" style={{ margin: '20px 0' }}>
      {/* 外层折叠控制 */}
      <div
        className="collapsible-header"
        onClick={() => setIsOpen(!isOpen)}
        style={{
          display: 'flex',
          alignItems: 'center',
          padding: '8px 0',
          cursor: 'pointer',
          userSelect: 'none',
          borderBottom: isOpen ? '1px solid #e1e5e9' : 'none',
          marginBottom: isOpen ? '0' : '0'
        }}
      >
        <span style={{
          display: 'inline-block',
          width: 0,
          height: 0,
          marginRight: '8px',
          borderLeft: '6px solid #666',
          borderTop: '4px solid transparent',
          borderBottom: '4px solid transparent',
          transition: 'transform 0.2s ease',
          transform: isOpen ? 'rotate(90deg)' : 'rotate(0deg)'
        }}>
        </span>
        <span style={{
          fontWeight: 500,
          color: '#333',
          fontSize: '14px',
          flex: 1
        }}>
          {title}
        </span>
                <button
          className="collapsible-copy-btn"
          onClick={(e) => {
            e.stopPropagation();
            copyToClipboard();
          }}
          title={copied ? "已复制!" : "复制代码"}
          style={{
            padding: '4px 8px',
            marginLeft: '8px',
            background: copied ? '#28a745' : '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            fontSize: '12px',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            minWidth: '60px'
          }}
          onMouseEnter={(e) => {
            if (!copied) {
              e.target.style.backgroundColor = '#0056b3';
            }
          }}
          onMouseLeave={(e) => {
            if (!copied) {
              e.target.style.backgroundColor = '#007bff';
            }
          }}
        >
          {copied ? '已复制!' : '复制'}
        </button>
      </div>

            {/* Monaco Editor 容器 - 只在展开时显示 */}
      {isOpen && (
        <div
          ref={containerRef}
          style={{
            height: `${actualHeight}px`,
            border: '1px solid #e1e5e9',
            borderRadius: '4px',
            marginTop: '8px'
          }}
        />
      )}
    </div>
  );
};

export default MonacoCodeBlock;
