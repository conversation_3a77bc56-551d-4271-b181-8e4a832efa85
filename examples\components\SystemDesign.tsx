import React from 'react';
import {Switch} from 'react-router-dom';
import {navigations2route} from './App';
import makeMarkdownRenderer from './MdRenderer';
import utils from './utils';

function wrapDoc(doc: any) {
  return {
    default: makeMarkdownRenderer(doc),
  };
}

// 使用 Vite 的 import.meta.glob 自动发现所有 markdown 文件
const markdownModules = import.meta.glob('../../docs/zh-CN/systemDesign/*.md');

// 动态生成文档路由配置
function generateSystemDesignDocs() {
  const children: any[] = [];

  // 遍历所有发现的 markdown 文件
  Object.keys(markdownModules).forEach((filePath: string) => {
    // 从路径中提取文件名
    // 例如: '../../docs/zh-CN/systemDesign/react-scheduler-mechanism.md' -> 'react-scheduler-mechanism'
    const fileName = filePath
      .split('/')
      .pop()
      ?.replace(/\.md$/, '') || '';

    // 跳过测试文件和草稿文件
    if (fileName.startsWith('test-') || fileName.includes('-draft')) {
      return;
    }

    // 生成路由路径
    const routePath = `/zh-CN/systemDesign/${fileName}`;

    // 智能生成标题
    const getDocumentTitle = (fileName: string) => {
      // 预定义的标题映射
      const titleMap: Record<string, string> = {
        'index': '概览',
        'react-scheduler-mechanism': 'React调度机制深度解析',
        'feature-issue-1242': 'Select组件事件数据获取失效问题',
        'feature-issue-1206': 'Feature Issue 1206',
        'mermaid-integration-tech-solution': 'Mermaid图表集成技术方案'
      };

      // 如果有预定义标题，使用预定义的
      if (titleMap[fileName]) {
        return titleMap[fileName];
      }

      // 否则自动生成：将连字符转换为空格，首字母大写
      return fileName
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    };

    children.push({
      path: routePath,
      label: getDocumentTitle(fileName),
      component: React.lazy(() =>
        markdownModules[filePath]().then(wrapDoc)
      ),
      // 添加排序权重，index 最先，其他按字母顺序
      order: fileName === 'index' ? 0 : 1
    });
  });

  // 排序：index 在前，其他按标题排序
  children.sort((a, b) => {
    if (a.order !== b.order) {
      return a.order - b.order;
    }
    return a.label.localeCompare(b.label);
  });

  return [
    {
      label: '系统设计',
      path: '/zh-CN/systemDesign',
      children,
    },
  ];
}

// 生成文档配置
const systemDesignDocs = generateSystemDesignDocs();

export default class SystemDesign extends React.PureComponent<any> {
  componentDidMount() {
    this.props.setNavigations(systemDesignDocs);
    utils.scrollToAnchor();
  }

  componentDidUpdate() {
    this.props.setNavigations(systemDesignDocs, false);
  }

  render() {
    return (
      <Switch>
        {navigations2route(systemDesignDocs, {
          theme: this.props.theme,
          classPrefix: this.props.classPrefix,
          locale: this.props.locale,
          viewMode: this.props.viewMode,
          offScreen: this.props.offScreen,
        })}
      </Switch>
    );
  }
}
