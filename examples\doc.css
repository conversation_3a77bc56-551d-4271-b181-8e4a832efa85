/* csshint-disable */
@font-face {
  font-family: octicons-link;
  src: url(data:font/woff;charset=utf-8;base64,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)
    format('woff');
}

.HomeMDPreview .markdown {
  padding: 0;
}

.HomeMDPreview .markdown .markdown-body p {
  margin-bottom: 0;
}

.HomeMDPreview .markdown .markdown-body video {
  width: 100%;
}

.markdown {
  padding: 25px 45px 25px;
}

.markdown-body {
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  color: #333;
  font-family: 'PingFang SC', 'Microsoft YaHei', 'Hiragino Sans GB', 'STHeiti',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  word-wrap: break-word;
  box-sizing: border-box;
  min-width: 200px;
  /* max-width: 980px; */
  /* margin: 0 auto; */
}

.markdown-body a:not(.btn) {
  background-color: transparent;
  -webkit-text-decoration-skip: objects;
}

.markdown-body a:active:not(.btn),
.markdown-body a:hover:not(.btn) {
  outline-width: 0;
}

.markdown-body strong {
  font-weight: inherit;
}

.markdown-body strong {
  font-weight: bolder;
}

.markdown-body > h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

.markdown-body img {
  border-style: none;
}

.markdown-body svg:not(:root) {
  overflow: hidden;
}

.markdown-body code,
.markdown-body kbd,
.markdown-body pre {
  font-family: monospace, monospace;
  font-size: 1em;
}

.markdown-body hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}

.markdown-body input {
  font: inherit;
  margin: 0;
}

.markdown-body input {
  overflow: visible;
}

.markdown-body button:-moz-focusring,
.markdown-body [type='button']:-moz-focusring,
.markdown-body [type='reset']:-moz-focusring,
.markdown-body [type='submit']:-moz-focusring {
  outline: 1px dotted ButtonText;
}

.markdown-body [type='checkbox'] {
  box-sizing: border-box;
  padding: 0;
}

.markdown-body table:not(.table) {
  border-spacing: 0;
  border-collapse: collapse;
}

.markdown-body table:not(.table) td,
.markdown-body table:not(.table) th {
  padding: 0;
}

.markdown-body * {
  box-sizing: border-box;
}

.markdown-body input {
  font: 13px/1.4 Helvetica, arial, nimbussansl, liberationsans, freesans, clean,
    sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
}

/* .markdown-body a:not(.btn) {
  color: #4078c0;
  text-decoration: none;
}

.markdown-body a:hover:not(.btn),
.markdown-body a:active:not(.btn) {
  color: #4078c0;
  text-decoration: underline;
} */

.markdown-body hr {
  height: 0;
  margin: 15px 0;
  overflow: hidden;
  background: transparent;
  border: 0;
  border-bottom: 1px solid #ddd;
}

.markdown-body hr::before {
  display: table;
  content: '';
}

.markdown-body hr::after {
  display: table;
  clear: both;
  content: '';
}

.markdown-body > h1,
.markdown-body > h2,
.markdown-body > h3,
.markdown-body > h4,
.markdown-body > h5,
.markdown-body > h6 {
  margin-top: 0;
  margin-bottom: 0;
  line-height: 1.5;
}

.markdown-body > h1 {
  font-size: 30px;
}

.markdown-body > h2 {
  font-size: 21px;
}

.markdown-body > h3 {
  font-size: 16px;
}

.markdown-body > h4 {
  font-size: 14px;
}

.markdown-body > h5 {
  font-size: 12px;
}

.markdown-body > h6 {
  font-size: 11px;
}

.markdown-body p {
  margin-top: 0;
  margin-bottom: 10px;
}

.markdown-body blockquote {
  margin: 0;
}

.markdown-body ul,
.markdown-body ol {
  padding-left: 0;
  margin-top: 0;
  margin-bottom: 0;
}

.markdown-body ol ol,
.markdown-body ul ol {
  list-style-type: lower-roman;
}

.markdown-body ul ul ol,
.markdown-body ul ol ol,
.markdown-body ol ul ol,
.markdown-body ol ol ol {
  list-style-type: lower-alpha;
}

.markdown-body dd {
  margin-left: 0;
}

.markdown-body code {
  font-family: Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  font-size: 12px;
}

.markdown-body pre {
  margin-top: 0;
  margin-bottom: 0;
  font: 12px Consolas, 'Liberation Mono', Menlo, Courier, monospace;
}

.markdown-body .pl-0 {
  padding-left: 0 !important;
}

.markdown-body .pl-1 {
  padding-left: 3px !important;
}

.markdown-body .pl-2 {
  padding-left: 6px !important;
}

.markdown-body .pl-3 {
  padding-left: 12px !important;
}

.markdown-body .pl-4 {
  padding-left: 24px !important;
}

.markdown-body .pl-5 {
  padding-left: 36px !important;
}

.markdown-body .pl-6 {
  padding-left: 48px !important;
}

.markdown-body .form-select::-ms-expand {
  opacity: 0;
}

.markdown-body:before {
  display: table;
  content: '';
}

.markdown-body:after {
  display: table;
  clear: both;
  content: '';
}

.markdown-body > *:first-child {
  margin-top: 0 !important;
}

.markdown-body > *:last-child {
  margin-bottom: 0 !important;
}

.markdown-body a:not([href]) {
  color: inherit;
  text-decoration: none;
}

.markdown-body a.anchor {
  display: inline-block;
  padding-right: 2px;
  margin-left: -18px;
}

.markdown-body a.anchor:focus {
  outline: none;
}

.markdown-body > h1,
.markdown-body > h2,
.markdown-body > h3,
.markdown-body > h4,
.markdown-body > h5,
.markdown-body > h6 {
  margin-top: 1em;
  margin-bottom: 16px;
  font-weight: bold;
  line-height: 1.4;
}

.markdown-body > h1 .octicon-link,
.markdown-body > h2 .octicon-link,
.markdown-body > h3 .octicon-link,
.markdown-body > h4 .octicon-link,
.markdown-body > h5 .octicon-link,
.markdown-body > h6 .octicon-link {
  color: #000;
  vertical-align: middle;
  visibility: hidden;
}

.markdown-body > h1:hover .anchor,
.markdown-body > h2:hover .anchor,
.markdown-body > h3:hover .anchor,
.markdown-body > h4:hover .anchor,
.markdown-body > h5:hover .anchor,
.markdown-body > h6:hover .anchor {
  text-decoration: none;
}

.markdown-body > h1:hover .anchor .octicon-link,
.markdown-body > h2:hover .anchor .octicon-link,
.markdown-body > h3:hover .anchor .octicon-link,
.markdown-body > h4:hover .anchor .octicon-link,
.markdown-body > h5:hover .anchor .octicon-link,
.markdown-body > h6:hover .anchor .octicon-link {
  visibility: visible;
}

.markdown-body > h1 {
  padding-bottom: 0.3em;
  font-size: 2.25em;
  line-height: 1.2;
  border-bottom: 1px solid #dee5e7;
}

.markdown-body > h1 .anchor {
  line-height: 1;
}

.markdown-body > h2 {
  padding-bottom: 0.3em;
  font-size: 1.75em;
  line-height: 1.225;
  border-bottom: 1px solid #dee5e7;
}

.markdown-body > h2 .anchor {
  line-height: 1;
}

.markdown-body > h3 {
  font-size: 1.5em;
  line-height: 1.43;
}

.markdown-body > h3 .anchor {
  line-height: 1.2;
}

.markdown-body > h4 {
  font-size: 1.25em;
}

.markdown-body > h4 .anchor {
  line-height: 1.2;
}

.markdown-body > h5 {
  font-size: 1em;
}

.markdown-body > h5 .anchor {
  line-height: 1.1;
}

.markdown-body > h6 {
  font-size: 1em;
  color: #777;
}

.markdown-body h6 .anchor {
  line-height: 1.1;
}

.markdown-body p,
.markdown-body blockquote,
.markdown-body ul,
.markdown-body ol,
.markdown-body dl,
.markdown-body table,
.markdown-body pre {
  margin-top: 0;
  margin-bottom: 16px;
}

.markdown-body hr {
  height: 4px;
  padding: 0;
  margin: 16px 0;
  background-color: #e7e7e7;
  border: 0 none;
}

.markdown-body ul:not(.dropdown-menu):not(.nav),
.markdown-body ol {
  padding-left: 2em;
}

.markdown-body ul ul,
.markdown-body ul ol,
.markdown-body ol ol,
.markdown-body ol ul {
  margin-top: 0;
  margin-bottom: 0;
}

.markdown-body li > p {
  margin-top: 16px;
}

.markdown-body dl {
  padding: 0;
}

.markdown-body dl dt {
  padding: 0;
  margin-top: 16px;
  font-size: 1em;
  font-style: italic;
  font-weight: bold;
}

.markdown-body dl dd {
  padding: 0 16px;
  margin-bottom: 16px;
}

.markdown-body blockquote {
  padding: 0 15px;
  color: #777;
  border-left: 4px solid #ddd;
}

.markdown-body blockquote > :first-child {
  margin-top: 0;
}

.markdown-body blockquote > :last-child {
  margin-bottom: 0;
}

.markdown-body table:not(.table) {
  display: block;
  width: 100%;
  overflow: auto;
  word-break: normal;
  /* word-break: keep-all; */
}

.markdown-body table:not(.table) th {
  font-weight: bold;
}

.markdown-body table:not(.table) th,
.markdown-body table:not(.table) td {
  padding: 6px 13px;
  border: 1px solid #ddd;
}

.markdown-body table:not(.table) tr {
  border-top: 1px solid #ccc;
}

.markdown-body table:not(.table) tr:nth-child(2n) {
  background-color: #f8f8f8;
}

body.dark .markdown-body table:not(.table) tr:nth-child(2n) {
  background: none;
}

/* modified by zhangjun08
 * 更改文档中的图片展示样式
 */
.markdown-body img {
  max-width: 90%;
  margin-left: 5%;
  margin-right: 5%;
  box-sizing: content-box;
  background-color: #fff;
  border-radius: 5px;
  border: 1px solid #ddd;
  box-shadow: 0 8px 18px 0 rgba(0, 0, 0, 0.3);
}

@media (min-width: 1200px) {
  .markdown-body img {
    max-width: 800px;
  }
}

.markdown-body code {
  padding: 0;
  padding-top: 0.2em;
  padding-bottom: 0.2em;
  margin: 0;
  font-size: 85%;
  background-color: rgba(0, 0, 0, 0.04);
  border-radius: 3px;
}

.markdown-body code:before,
.markdown-body code:after {
  letter-spacing: -0.2em;
  content: '\00a0';
}

.markdown-body pre > code {
  padding: 0;
  margin: 0;
  font-size: 100%;
  word-break: normal;
  white-space: pre;
  background: transparent;
  border: 0;
}

.markdown-body .highlight {
  margin-bottom: 16px;
}

.markdown-body .highlight pre,
.markdown-body pre {
  padding: 16px;
  overflow: auto;
  font-size: 85%;
  line-height: 1.45;
  background-color: #f7f7f7;
  border-radius: 3px;
}

.markdown-body .highlight pre {
  margin-bottom: 0;
  word-break: normal;
}

.markdown-body pre {
  word-wrap: normal;
}

.markdown-body pre code {
  display: inline;
  max-width: initial;
  padding: 0;
  margin: 0;
  overflow: initial;
  line-height: inherit;
  word-wrap: normal;
  background-color: transparent;
  border: 0;
}

.markdown-body pre code:before,
.markdown-body pre code:after {
  content: normal;
}

.markdown-body kbd {
  display: inline-block;
  padding: 3px 5px;
  font-size: 11px;
  line-height: 10px;
  color: #555;
  vertical-align: middle;
  background-color: #fcfcfc;
  border: solid 1px #ccc;
  border-bottom-color: #bbb;
  border-radius: 3px;
  box-shadow: inset 0 -1px 0 #bbb;
}

.markdown-body .pl-c {
  color: #969896;
}

.markdown-body .pl-c1,
.markdown-body .pl-s .pl-v {
  color: #0086b3;
}

.markdown-body .pl-e,
.markdown-body .pl-en {
  color: #795da3;
}

.markdown-body .pl-s .pl-s1,
.markdown-body .pl-smi {
  color: #333;
}

.markdown-body .pl-ent {
  color: #63a35c;
}

.markdown-body .pl-k {
  color: #a71d5d;
}

.markdown-body .pl-pds,
.markdown-body .pl-s,
.markdown-body .pl-s .pl-pse .pl-s1,
.markdown-body .pl-sr,
.markdown-body .pl-sr .pl-cce,
.markdown-body .pl-sr .pl-sra,
.markdown-body .pl-sr .pl-sre {
  color: #183691;
}

.markdown-body .pl-v {
  color: #ed6a43;
}

.markdown-body .pl-id {
  color: #b52a1d;
}

.markdown-body .pl-ii {
  background-color: #b52a1d;
  color: #f8f8f8;
}

.markdown-body .pl-sr .pl-cce {
  color: #63a35c;
  font-weight: bold;
}

.markdown-body .pl-ml {
  color: #693a17;
}

.markdown-body .pl-mh,
.markdown-body .pl-mh .pl-en,
.markdown-body .pl-ms {
  color: #1d3e81;
  font-weight: bold;
}

.markdown-body .pl-mq {
  color: #008080;
}

.markdown-body .pl-mi {
  color: #333;
  font-style: italic;
}

.markdown-body .pl-mb {
  color: #333;
  font-weight: bold;
}

.markdown-body .pl-md {
  background-color: #ffecec;
  color: #bd2c00;
}

.markdown-body .pl-mi1 {
  background-color: #eaffea;
  color: #55a532;
}

.markdown-body .pl-mdr {
  color: #795da3;
  font-weight: bold;
}

.markdown-body .pl-mo {
  color: #1d3e81;
}

.markdown-body kbd {
  display: inline-block;
  padding: 3px 5px;
  font: 11px Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  line-height: 10px;
  color: #555;
  vertical-align: middle;
  background-color: #fcfcfc;
  border: solid 1px #ccc;
  border-bottom-color: #bbb;
  border-radius: 3px;
  box-shadow: inset 0 -1px 0 #bbb;
}

.markdown-body .full-commit .btn-outline:not(:disabled):hover {
  color: #4078c0;
  border: 1px solid #4078c0;
}

.markdown-body :checked + .radio-label {
  position: relative;
  z-index: 1;
  border-color: #4078c0;
}

.markdown-body .octicon {
  display: inline-block;
  vertical-align: text-top;
  fill: currentColor;
}

.markdown-body .task-list-item {
  list-style-type: none;
}

.markdown-body .task-list-item + .task-list-item {
  margin-top: 3px;
}

.markdown-body .task-list-item input {
  margin: 0 0.2em 0.25em -1.6em;
  vertical-align: middle;
}

.markdown-body hr {
  border-bottom-color: #eee;
}

.doc-play-ground {
  height: auto;
  position: relative;
  width: 100%;
  margin: 20px 0;
}

.doc-play-ground > .visibility-sensor {
  position: relative;
  width: 100%;
  height: 100%;
}

.doc-play-ground .nav-tabs {
  margin-bottom: 0;
}

.markdown-body .CodeMirror pre {
  padding: 0 4px;
  margin: 0;
  background-color: transparent;
}

.markdown-body .CodeMirror,
.markdown-body .CodeMirror * {
  box-sizing: content-box;
}

.markdown-body .CodeMirror-lines {
  padding: 0;
}

.markdown-body .tree-view ul {
  padding-left: 0 !important;
}

.markdown-body > .amis-preview {
  margin-bottom: 15px;
}

.amis-doc {
  margin: 20px 0;
}

.amis-doc > .preview {
  padding-top: 3rem;
  padding-bottom: 3rem;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  overflow: hidden;
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
  background-color: #dbeafe;
}

body.dark .amis-doc > .preview {
  background: #191c22;
  color: #fff;
}

.amis-doc > pre {
  margin-top: 0;
  margin-bottom: 0;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

/* Mermaid 图表样式 */
.mermaid-chart,
.mermaid-preview {
  margin: 20px 0;
  padding: 15px;
  background-color: #fff;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  overflow-x: auto; /* 允许水平滚动以显示较宽的图表 */
  overflow-y: visible; /* 确保垂直方向图表不被截断 */
  text-align: center;
  min-height: 200px; /* 确保有足够高度 */
}

.mermaid-chart svg,
.mermaid-preview svg {
  max-width: 100%;
  height: auto;
  display: block; /* 确保SVG正确显示 */
  margin: 0 auto; /* 居中显示 */
}

.mermaid-error {
  background-color: #f8f8f8;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #666;
  overflow-x: auto;
}

/* 深色主题下的 Mermaid 样式 */
body.dark .mermaid-chart,
body.dark .mermaid-preview {
  background-color: #2b2f3a;
  border-color: #444c5b;
}

body.dark .mermaid-error {
  background-color: #2b2f3a;
  border-color: #444c5b;
  color: #bbb;
}

/* 图表节点文字优化 */
.mermaid-chart .node .label,
.mermaid-preview .node .label {
  font-family: 'PingFang SC', 'Microsoft YaHei', 'Hiragino Sans GB', 'STHeiti', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* 流程图样式优化 */
.mermaid-chart .flowchart .node rect,
.mermaid-chart .flowchart .node circle,
.mermaid-chart .flowchart .node polygon,
.mermaid-preview .flowchart .node rect,
.mermaid-preview .flowchart .node circle,
.mermaid-preview .flowchart .node polygon {
  stroke-width: 2px;
}

/* 时序图样式优化 */
.mermaid-chart .sequenceDiagram .actor,
.mermaid-preview .sequenceDiagram .actor {
  stroke-width: 2px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mermaid-chart,
  .mermaid-preview {
    margin: 15px 0;
    padding: 10px;
  }

  .mermaid-chart svg,
  .mermaid-preview svg {
    font-size: 12px;
  }
}

/* 可折叠代码块样式 */
.collapsible-code-block {
  margin: 20px 0;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  overflow: hidden;
  background-color: #fff;
}

.collapsible-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
}

.collapsible-header:hover {
  background-color: #e9ecef;
}

.collapsible-icon {
  display: inline-block;
  width: 0;
  height: 0;
  margin-right: 8px;
  border-left: 6px solid #666;
  border-top: 4px solid transparent;
  border-bottom: 4px solid transparent;
  transition: transform 0.2s ease;
}

.collapsible-code-block.collapsed .collapsible-icon {
  transform: rotate(-90deg);
}

.collapsible-title {
  font-weight: 500;
  color: #333;
  font-size: 14px;
  flex: 1;
}

.collapsible-copy-btn {
  padding: 4px 8px;
  margin-left: 8px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  min-width: 48px;
}

.collapsible-copy-btn:hover {
  background-color: #0056b3;
}

.collapsible-copy-btn:active {
  background-color: #004085;
}

.collapsible-content {
  overflow: hidden;
  transition: opacity 0.2s ease, transform 0.2s ease;
  opacity: 1;
  transform: scaleY(1);
  transform-origin: top;
}

.collapsible-code-block.collapsed .collapsible-content {
  opacity: 0;
  transform: scaleY(0);
  height: 0;
}

.collapsible-content pre {
  margin: 0;
  border-radius: 0;
  border: none;
  background-color: #f8f9fa;
}

.collapsible-content pre code {
  display: block;
  padding: 16px;
  font-size: 13px;
  line-height: 1.5;
  overflow-x: auto;
}

/* 深色主题下的可折叠代码块样式 */
body.dark .collapsible-code-block {
  background-color: #2b2f3a;
  border-color: #444c5b;
}

body.dark .collapsible-header {
  background-color: #3c4043;
  border-color: #444c5b;
}

body.dark .collapsible-header:hover {
  background-color: #484b52;
}

body.dark .collapsible-icon {
  border-left-color: #bbb;
}

body.dark .collapsible-title {
  color: #e8eaed;
}

body.dark .collapsible-content pre {
  background-color: #3c4043;
}

body.dark .collapsible-content pre code {
  color: #e8eaed;
}

body.dark .collapsible-copy-btn {
  background-color: #1a73e8;
  color: #fff;
}

body.dark .collapsible-copy-btn:hover {
  background-color: #1557b0;
}

body.dark .collapsible-copy-btn:active {
  background-color: #0f4c75;
}

/* VSCode风格代码折叠样式 */
.code-fold-line {
  display: block;
  position: relative;
}

.code-fold-btn {
  color: #666;
  cursor: pointer;
  font-size: 12px;
  width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  display: inline-block;
  user-select: none;
  margin-right: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  vertical-align: top;
}

.code-fold-line:hover .code-fold-btn {
  opacity: 1;
}

.code-fold-btn:hover {
  background-color: #e8e8e8;
  border-radius: 2px;
}

.code-line-content {
  display: inline;
}

.code-fold-placeholder {
  color: #666;
  font-style: italic;
}

.code-fold-content {
  margin-left: 20px;
}

/* 深色主题 */
body.dark .code-fold-btn {
  color: #bbb;
}

body.dark .code-fold-btn:hover {
  background-color: #3c3c3c;
}

body.dark .code-fold-placeholder {
  color: #888;
}
