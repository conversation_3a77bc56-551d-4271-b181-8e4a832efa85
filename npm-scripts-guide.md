# NPM Scripts 使用指南

本文档详细说明项目中各个 npm scripts 的作用和使用场景。

## 🔧 工具和清单

### `npm run manifest`
- **作用**：生成组件清单文件
- **命令**：`node ./scripts/manifestTool.js`
- **使用场景**：更新组件列表时

## 🚀 Dataseed Pro 组件相关

### `npm run dataseed-pro`
- **作用**：启动 Pro 组件开发服务器
- **命令**：`npm run start --workspace @dataseed/pro-components`
- **使用场景**：开发 Pro 组件时

### `npm run dataseed-pro:link`
- **作用**：链接 Pro 组件到全局
- **命令**：`npm link --workspace @dataseed/pro-components`
- **使用场景**：本地调试 Pro 组件时

### `npm run dataseed-pro:docs:build`
- **作用**：构建 Pro 组件文档
- **命令**：`npm run docs:build --workspace @dataseed/pro-components`
- **使用场景**：发布 Pro 组件文档时

## 🏗️ FIS3 构建系统（传统构建方式）

### `npm run fis3-serve`
- **作用**：启动 FIS3 静态文件服务器（仅服务，不构建）
- **命令**：`fis3 server start --www ./public --port 8888 --no-daemon --no-browse`
- **端口**：8888
- **使用场景**：只需要静态文件服务，不需要文件监听时

### `npm run fis3-dev`
- **作用**：FIS3 文件监听和自动构建（不含服务器）
- **命令**：`fis3 release -cwd ./public`
- **使用场景**：只需要文件构建，不需要服务器时

### `npm run fis3`
- **作用**：同时启动 FIS3 服务器和文件监听构建
- **命令**：`concurrently --restart-tries -1 npm:fis3-serve npm:fis3-dev`
- **使用场景**：完整的 FIS3 开发环境
- **注意**：可能遇到 `EMFILE: too many open files` 错误

### `npm run fis3-stop`
- **作用**：停止 FIS3 服务器
- **命令**：`fis3 server stop`
- **使用场景**：清理 FIS3 服务器进程时

## ⚡ Vite 构建系统（现代构建方式，推荐）

### `npm run gen-docs`
- **作用**：生成文档搜索数据
- **命令**：`node ./scripts/generate-search-data.js`
- **使用场景**：更新文档搜索索引时

### `npm start`
- **作用**：启动 Vite 开发服务器（默认开发环境）
- **命令**：`npm run gen-docs && vite`
- **端口**：8888
- **使用场景**：日常开发（推荐）

### `npm run vite:build`
- **作用**：Vite 生产构建
- **命令**：`npm run gen-docs && NODE_OPTIONS='--max-old-space-size=4096' vite build && mv dist/dataseeddesigndocui/monacoeditorwork dist && rm -r dist/dataseeddesigndocui && cp -r public ./dist`
- **使用场景**：构建生产版本

## 📚 文档构建

### `npm run docs`
- **作用**：构建文档（生产环境）
- **命令**：`sh ./sh/build-docs.sh`
- **使用场景**：生产环境文档构建

### `npm run docs:dev`
- **作用**：构建文档（开发环境）
- **命令**：`sh ./sh/dev-docs.sh`
- **使用场景**：开发环境文档构建

## 📦 Monorepo 管理

### `npm run build`
- **作用**：构建所有子包
- **命令**：`npm run build --workspaces`
- **使用场景**：构建整个项目

### `npm test`
- **作用**：运行所有子包的测试
- **命令**：`npm test --workspaces`
- **使用场景**：运行完整测试套件

### `npm run update-snapshot`
- **作用**：更新所有子包的测试快照
- **命令**：`npm run update-snapshot --workspaces`
- **使用场景**：更新 Jest 快照测试

## 🔍 测试覆盖率

### `npm run coverage:noreport`
- **作用**：生成测试覆盖率（不生成报告）
- **命令**：`jest --coverage --collectCoverage=v8`
- **使用场景**：快速检查覆盖率

### `npm run coverage`
- **作用**：生成测试覆盖率报告
- **命令**：`jest --coverage`
- **使用场景**：生成详细的覆盖率报告

## 🏷️ 版本管理

### `npm run version`
- **作用**：Lerna 版本管理
- **命令**：`lerna version --force-publish`
- **使用场景**：发布新版本时

### `npm run reversion`
- **作用**：重新生成版本信息
- **命令**：`ts-node ./scripts/generate-version.ts`
- **使用场景**：修复版本信息时

### `npm run revision`
- **作用**：生成修订版本信息
- **命令**：`ts-node ./scripts/generate-revision.ts`
- **使用场景**：生成构建版本信息

## 🚢 发布相关

### `npm run release`
- **作用**：发布到 npm
- **命令**：`npm run build --workspaces && lerna publish from-package --registry=https://registry.npmjs.org --ignore-scripts`
- **使用场景**：发布到公共 npm 仓库

### `npm run publish`
- **作用**：发布脚本
- **命令**：`sh ./sh/publish.sh`
- **使用场景**：执行发布流程

### `npm run publish:bcds`
- **作用**：发布到内部仓库（跳过构建）
- **命令**：`npm run build && npm run docs && sh ./sh/publish.sh nobuild`
- **使用场景**：发布到内部仓库

## 🛠️ 工具脚本

### `npm run refile`
- **作用**：文件传输工具
- **命令**：`node ./scripts/transfer-file.js`
- **使用场景**：文件迁移或处理

### `npm run clean`
- **作用**：清理构建文件
- **命令**：`sh ./sh/clean.sh`
- **使用场景**：清理临时文件和构建产物

### `npm run stylelint`
- **作用**：SCSS 代码检查
- **命令**：`npx stylelint 'packages/**/*.scss'`
- **使用场景**：检查样式代码规范

### `npm run upremote`
- **作用**：同步上游 amis 仓库
- **命令**：`git pull https://github.com/baidu/amis.git master --no-tags`
- **使用场景**：同步上游代码更新

## 🎯 常用开发流程

### 日常开发
```bash
npm start  # 启动 Vite 开发服务器（推荐）
```

### FIS3 开发（如果需要）
```bash
npm run fis3-serve  # 只启动服务器，避免文件监听问题
```

### 运行测试
```bash
npm test -- packages/amis/__tests__/path/to/specific.test.tsx  # 运行特定测试
npm run coverage  # 生成覆盖率报告
```

### 构建和发布
```bash
npm run build      # 构建所有包
npm run docs       # 构建文档
npm run publish    # 发布
``` 
