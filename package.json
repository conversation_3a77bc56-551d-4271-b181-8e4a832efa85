{"name": "dataseed-design", "workspaces": ["packages/amis-formula", "packages/amis-core", "packages/amis-utils", "packages/dataseed-ui", "packages/amis-ui", "packages/amis", "packages/dataseed-pro", "packages/amis-editor-core", "packages/amis-editor"], "scripts": {"manifest": "node ./scripts/manifestTool.js", "dataseed-pro": "npm run start --workspace @dataseed/pro-components", "dataseed-pro:link": "npm link --workspace @dataseed/pro-components", "dataseed-pro:docs:build": "npm run docs:build --workspace @dataseed/pro-components", "fis3-serve": "fis3 server start --www ./public --port 8888 --no-daemon --no-browse", "fis3": "concurrently --restart-tries -1 npm:fis3-serve npm:fis3-dev", "fis3-stop": "fis3 server stop", "gen-docs": "node ./scripts/generate-search-data.js", "start": "npm run gen-docs && vite", "vite:build": "npm run gen-docs && NODE_OPTIONS='--max-old-space-size=4096' vite build && mv dist/dataseeddesigndocui/monacoeditorwork dist && rm -r dist/dataseeddesigndocui && cp -r public ./dist", "fis3-dev": "fis3 release -cwd ./public", "docs": "sh ./sh/build-docs.sh", "docs:dev": "sh ./sh/dev-docs.sh", "build": "npm run build --workspaces", "test": "npm test --workspaces", "update-snapshot": "npm run update-snapshot --workspaces", "prepare": "husky install", "coverage:noreport": "jest --coverage --collectCoverage=v8", "coverage": "jest --coverage", "version": "lerna version --force-publish", "reversion": "ts-node ./scripts/generate-version.ts", "refile": "node ./scripts/transfer-file.js", "release": "npm run build --workspaces && lerna publish from-package --registry=https://registry.npmjs.org --ignore-scripts", "revision": "ts-node ./scripts/generate-revision.ts", "publish": "sh ./sh/publish.sh", "clean": "sh ./sh/clean.sh", "stylelint": "npx stylelint 'packages/**/*.scss'", "upremote": "git pull https://github.com/baidu/amis.git master --no-tags", "publish:bcds": "npm run build && npm run docs && sh ./sh/publish.sh nobuild"}, "dependencies": {"ahooks": "^3.8.1", "postcss": "^8.4.14", "qs": "6.9.7"}, "devDependencies": {"@rollup/plugin-replace": "^5.0.1", "@testing-library/react": "12.1.5", "@types/express": "^4.17.14", "@types/jest": "^28.1.0", "@types/js-yaml": "^4.0.5", "@types/marked": "^4.0.7", "@types/prismjs": "^1.26.0", "@types/react": "^18.0.24", "@types/react-dom": "^18.0.8", "@vitejs/plugin-react": "^2.2.0", "axios": "^1.3.5", "copy-to-clipboard": "3.3.1", "echarts": "5.4.0", "express": "^4.18.2", "fis-parser-sass": "^1.2.0", "fis-parser-svgr": "^1.0.0", "fis3": "^3.5.0-beta.2", "fis3-deploy-skip-packed": "0.0.5", "fis3-hook-commonjs": "^0.1.31", "fis3-hook-node_modules": "^2.3.1", "fis3-hook-relative": "^2.0.3", "fis3-packager-deps-pack": "^0.1.2", "fis3-parser-typescript": "^1.4.0", "fis3-postpackager-loader": "^2.1.12", "fis3-prepackager-stand-alone-pack": "^1.0.0", "fis3-preprocessor-js-require-css": "^0.1.3", "fis3-preprocessor-js-require-file": "^0.1.3", "husky": "^8.0.0", "jest": "^29.0.3", "jest-environment-jsdom": "^29.0.3", "js-yaml": "^4.1.0", "lerna": "^6.6.1", "magic-string": "^0.26.7", "marked": "^4.2.1", "monaco-editor": "0.30.1", "plugin-react-i18n": "^0.0.20", "postcss-custom-properties": "13.1.4", "postcss-scss": "4.0.6", "prismjs": "^1.29.0", "react": "^16.8.6", "react-dom": "^16.8.6", "react-overlays": "5.1.1", "rollup-pluginutils": "^2.8.2", "setprototypeof": "^1.2.0", "ts-jest": "^29.0.2", "vite": "4.5.0", "vite-plugin-monaco-editor": "^1.1.0", "vite-plugin-svgr": "^2.2.2", "zrender": "^5.3.2"}, "jest": {"verbose": true, "testEnvironment": "jsdom", "collectCoverageFrom": ["<rootDir>/packages/amis-core/src/**/*", "<rootDir>/packages/amis-formula/src/**/*", "<rootDir>/packages/amis/src/**/*"], "moduleFileExtensions": ["ts", "tsx", "js"], "transform": {"\\.(ts|tsx)$": ["ts-jest", {"diagnostics": false}]}, "preset": "ts-jest", "setupFiles": ["jest-canvas-mock"], "testRegex": "/packages/(amis-core|amis-formula|amis)/.*\\.test\\.(ts|tsx|js)$", "moduleNameMapper": {"\\.(css|less|sass|scss)$": "<rootDir>/__mocks__/styleMock.js", "\\.(svg)$": "<rootDir>/__mocks__/svgMock.js", "\\.svg\\.js$": "<rootDir>/__mocks__/svgJsMock.js", "^amis\\-ui$": "<rootDir>/packages/amis-ui/src/index.tsx", "^amis\\-ui/lib/(.*)": "<rootDir>/packages/amis-ui/src/$1", "^amis\\-core$": "<rootDir>/packages/amis-core/src/index.tsx", "^amis\\-core/lib/(.*)": "<rootDir>/packages/amis-core/src/$1", "^amis\\-formula$": "<rootDir>/packages/amis-formula/src/index.ts", "^amis\\-formula/lib/(.*)": "<rootDir>/packages/amis-formula/src/$1", "^office\\-viewer$": "<rootDir>/packages/ooxml-viewer/src/index.ts", "^amis$": "<rootDir>/packages/amis/src/index.tsx", "^dataseed\\-ui$": "<rootDir>/packages/dataseed-ui/src/index.tsx"}, "setupFilesAfterEnv": ["<rootDir>/packages/amis-core/__tests__/jest.setup.js"], "testPathIgnorePatterns": ["/node_modules/", "/.rollup.cache/"], "snapshotFormat": {"escapeString": false, "printBasicPrototype": false}}, "publishConfig": {"access": "public", "registry": "http://registry.caijj.net/repository/npm-caijiajia/"}, "authors": ["<EMAIL>"]}