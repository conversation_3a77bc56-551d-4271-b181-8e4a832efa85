import {render} from '@testing-library/react';
import 'amis';
import React from 'react';
import type {IScopedContext, ScopedComponentType} from '../src/Scoped';
import {HocScoped, ScopedContext} from '../src/Scoped';
import type {RendererData} from '../src/types';

describe('Scoped', () => {
  // 模拟组件
  class MockComponent extends React.Component<any> {
    static displayName = 'MockComponent';
    receive = jest.fn(() => {
      // console.log('Received values');
    });
    reload = jest.fn(() => {
      // console.log('Reloading target');
    });
    constructor(props: any) {
      super(props);
    }
    render() {
      return (
        <div>
          <span>Mock Component</span>
          {this.props.children}
        </div>
      );
    }
  }

  // 创建一个带有必要属性的组件实例
  const createMockComponent = (props: any = {}): ScopedComponentType => {
    const defaultName = 'defaultName';
    return new MockComponent({
      $path: props.$path || 'test/path',
      name: props.name || defaultName,
      ...props,
    }) as ScopedComponentType;
  };

  describe('ScopedContext', () => {
    it('应该创建一个新的作用域实例', () => {
      let scoped: IScopedContext | null = null;
      const WrappedComponent = HocScoped(MockComponent);

      render(
        <WrappedComponent
          env={{}}
          $path="test/path"
          scopeRef={(s: IScopedContext) => {
            scoped = s;
          }}
        />,
      );

      expect(scoped).toBeDefined();
      expect(scoped!.parent).toBeDefined();
      expect(scoped!.getComponents()).toHaveLength(0);
    });

    it('应该正确设置父子关系', () => {
      let parentScoped: IScopedContext | null = null;
      let childScoped: IScopedContext | null = null;

      render(
        <ScopedContext.Provider
          value={
            {
              parent: undefined,
              children: [] as IScopedContext[],
              registerComponent: (component: ScopedComponentType) => {},
              unRegisterComponent: (component: ScopedComponentType) => {},
              getComponentByName: (name: string) => ({} as ScopedComponentType),
              getComponentById: (id: string) =>
                undefined as ScopedComponentType | undefined,
              getComponents: () => [] as ScopedComponentType[],
              getChildrenFormItems: (path: string) =>
                [] as ScopedComponentType[],
              reload: (target: string, ctx: RendererData) => {},
              send: (target: string, ctx: RendererData) => {},
              close: (target: string) => {},
              closeById: (target: string) => {},
            } as IScopedContext
          }
        >
          {(() => {
            const WrappedComponent = HocScoped(MockComponent);
            return (
              <WrappedComponent
                env={{}}
                $path="parent/path"
                scopeRef={(s: IScopedContext) => {
                  parentScoped = s;
                }}
              >
                <WrappedComponent
                  env={{}}
                  $path="child/path"
                  scopeRef={(s: IScopedContext) => {
                    childScoped = s;
                  }}
                />
              </WrappedComponent>
            );
          })()}
        </ScopedContext.Provider>,
      );

      expect(childScoped!.parent).toBe(parentScoped);
      expect(parentScoped!.children).toContain(childScoped);
    });
  });

  describe('registerComponent', () => {
    it('应该正确注册组件', () => {
      let scoped: IScopedContext | null = null;
      const component = createMockComponent();
      const WrappedComponent = HocScoped(MockComponent);

      render(
        <WrappedComponent
          env={{}}
          $path="parent/path"
          scopeRef={(s: IScopedContext) => {
            scoped = s;
            if (s) {
              s.registerComponent(component);
            }
          }}
        />,
      );

      expect(scoped!.getComponents()).toContain(component);
    });

    it('不应重复注册相同的组件', () => {
      let scoped: IScopedContext | null = null;
      const component = createMockComponent();
      const WrappedComponent = HocScoped(MockComponent);

      render(
        <WrappedComponent
          env={{}}
          $path="parent/path"
          scopeRef={(s: IScopedContext) => {
            scoped = s;
            scoped?.registerComponent(component);
            scoped?.registerComponent(component);
          }}
        />,
      );

      expect(scoped!.getComponents()).toHaveLength(1);
    });

    it('当组件路径与父作用域路径匹配时，组件应注册到父作用域', () => {
      let parentScoped: IScopedContext | null = null;
      let childScoped: IScopedContext | null = null;
      const component = createMockComponent({$path: 'test/path'});

      render(
        <ScopedContext.Provider
          value={
            {
              parent: undefined,
              children: [] as IScopedContext[],
              registerComponent: (component: ScopedComponentType) => {},
              unRegisterComponent: (component: ScopedComponentType) => {},
              getComponentByName: (name: string) => ({} as ScopedComponentType),
              getComponentById: (id: string) =>
                undefined as ScopedComponentType | undefined,
              getComponents: () => [] as ScopedComponentType[],
              getChildrenFormItems: (path: string) =>
                [] as ScopedComponentType[],
              reload: (target: string, ctx: RendererData) => {},
              send: (target: string, ctx: RendererData) => {},
              close: (target: string) => {},
              closeById: (target: string) => {},
            } as IScopedContext
          }
        >
          {(() => {
            const WrappedComponent = HocScoped(MockComponent);
            return (
              <WrappedComponent
                env={{}}
                $path="parent/path"
                scopeRef={(s: IScopedContext) => {
                  parentScoped = s;
                }}
              >
                <WrappedComponent
                  env={{}}
                  $path="test/path"
                  scopeRef={(s: IScopedContext) => {
                    childScoped = s;
                    childScoped?.registerComponent(component);
                  }}
                />
              </WrappedComponent>
            );
          })()}
        </ScopedContext.Provider>,
      );

      expect(parentScoped!.getComponents()).toContain(component);
      expect(childScoped!.getComponents()).not.toContain(component);
    });
  });

  describe('unRegisterComponent', () => {
    it('应该正确注销组件', () => {
      let scoped: IScopedContext | null = null;
      const component = createMockComponent();
      const WrappedComponent = HocScoped(MockComponent);

      render(
        <WrappedComponent
          env={{}}
          $path="parent/path"
          scopeRef={(s: IScopedContext) => {
            scoped = s;
            scoped?.registerComponent(component);
            scoped?.unRegisterComponent(component);
          }}
        />,
      );

      expect(scoped!.getComponents()).not.toContain(component);
    });
  });

  describe('getComponentByName', () => {
    it('应该通过名称找到组件', () => {
      let scoped: IScopedContext | null = null;
      const component = createMockComponent({name: 'testName'});
      const WrappedComponent = HocScoped(MockComponent);

      render(
        <WrappedComponent
          env={{}}
          $path="parent/path"
          scopeRef={(s: IScopedContext) => {
            scoped = s;
            s?.registerComponent(component);
          }}
        />,
      );

      expect(scoped!.getComponentByName('testName')).toBe(component);
    });

    it('应该通过点号分隔的路径找到组件', () => {
      let parentScoped: IScopedContext | null = null;
      let childScoped: IScopedContext | null = null;
      const parentComponent = createMockComponent({name: 'parent'});
      const childComponent = createMockComponent({name: 'child'});

      const WrappedComponent = HocScoped(MockComponent);
      render(
        <WrappedComponent
          env={{}}
          $path="outer/path"
          scopeRef={(s: IScopedContext) => {
            parentScoped = s;
            s?.registerComponent(parentComponent);
          }}
        >
          <WrappedComponent
            env={{}}
            name="parent"
            $path="parent/path"
            scopeRef={(s: IScopedContext) => {
              childScoped = s;
              parentComponent.context = childScoped;
              s?.registerComponent(childComponent);
            }}
          />
        </WrappedComponent>,
      );

      const foundComponent = parentScoped!.getComponentByName('parent');
      expect(foundComponent).toBe(parentComponent);
      expect(foundComponent.context).toBe(childScoped);
      expect(childScoped!.getComponentByName('child')).toBe(childComponent);
      expect(parentScoped!.getComponentByName('parent.child')).toBe(
        childComponent,
      );
    });

    it('当前作用域找不到组件时应该向上查找父作用域', () => {
      let parentScoped: IScopedContext | null = null;
      let childScoped: IScopedContext | null = null;
      const parentComponent = createMockComponent({name: 'parentOnly'});

      const WrappedComponent = HocScoped(MockComponent);
      render(
        <WrappedComponent
          env={{}}
          $path="parent/path"
          scopeRef={(s: IScopedContext) => {
            parentScoped = s;
            s?.registerComponent(parentComponent);
          }}
        >
          <WrappedComponent
            env={{}}
            $path="child/path"
            scopeRef={(s: IScopedContext) => {
              childScoped = s;
            }}
          />
        </WrappedComponent>,
      );

      // 在子作用域中查找只注册在父作用域的组件
      const foundComponent = childScoped!.getComponentByName('parentOnly');
      expect(foundComponent).toBe(parentComponent);
    });
  });

  describe('getComponentById', () => {
    it('应该通过ID找到组件', () => {
      let parentScoped: IScopedContext | null = null;
      let childScoped: IScopedContext | null = null;
      const component = createMockComponent({id: 'testId'});
      const WrappedComponent = HocScoped(MockComponent);

      render(
        <WrappedComponent
          env={{}}
          $path="parent/path"
          scopeRef={(s: IScopedContext) => {
            parentScoped = s;
            s?.registerComponent(component);
          }}
        >
          <WrappedComponent
            env={{}}
            $path="child/path"
            scopeRef={(s: IScopedContext) => {
              childScoped = s;
            }}
          />
        </WrappedComponent>,
      );

      expect(childScoped!.getComponentById('testId')).toBe(component);
      expect(parentScoped!.getComponentById('testId')).toBe(component);
    });

    it('应该能在父作用域中找到子作用域中注册的组件', () => {
      let parentScoped: IScopedContext | null = null;
      let childScoped: IScopedContext | null = null;
      const childComponent = createMockComponent({id: 'childId'});
      const WrappedComponent = HocScoped(MockComponent);

      render(
        <WrappedComponent
          env={{}}
          $path="parent/path"
          scopeRef={(s: IScopedContext) => {
            parentScoped = s;
          }}
        >
          <WrappedComponent
            env={{}}
            $path="child/path"
            scopeRef={(s: IScopedContext) => {
              childScoped = s;
              s?.registerComponent(childComponent);
            }}
          />
        </WrappedComponent>,
      );

      expect(parentScoped!.getComponentById('childId')).toBe(childComponent);
    });
  });

  describe('HocScoped', () => {
    it('应该正确包装组件', () => {
      const WrappedComponent = HocScoped(MockComponent);
      const {container} = render(<WrappedComponent env={{}} />);

      expect(container.textContent).toBe('Mock Component');
    });

    it('应该通过scopeRef提供作用域实例', () => {
      const WrappedComponent = HocScoped(MockComponent);
      let scopedInstance: IScopedContext | null = null;

      render(
        <WrappedComponent
          env={{}}
          scopeRef={(scoped: IScopedContext) => {
            scopedInstance = scoped;
          }}
        />,
      );

      expect(scopedInstance).toBeDefined();
      expect(scopedInstance!.getComponents).toBeDefined();
    });
  });

  describe('getChildrenFormItems', () => {
    it('应该返回指定路径下的表单项组件', () => {
      let parentScoped: IScopedContext | null = null;
      let childScoped: IScopedContext | null = null;
      const inputTextComponent = createMockComponent({
        type: 'input-text',
        $path: 'form/path/text',
      });
      const selectComponent = createMockComponent({
        type: 'select',
        $path: 'form/path/select',
      });
      const WrappedComponent = HocScoped(MockComponent);

      render(
        <WrappedComponent
          env={{}}
          $path="parent/path"
          scopeRef={(s: IScopedContext) => {
            parentScoped = s;
            s?.registerComponent(inputTextComponent);
          }}
        >
          <WrappedComponent
            env={{}}
            $path="form/path"
            scopeRef={(s: IScopedContext) => {
              childScoped = s;
              s?.registerComponent(selectComponent);
            }}
          />
        </WrappedComponent>,
      );

      const formItems = parentScoped!.getChildrenFormItems('form/path');
      expect(formItems).toContain(inputTextComponent);
      expect(formItems).toContain(selectComponent);
    });
  });

  describe('reload, send, close, closeById', () => {
    it('应该正确执行reload和send操作', () => {
      let scoped: IScopedContext | null = null;
      const component = createMockComponent({name: 'testComponent'});
      const WrappedComponent = HocScoped(MockComponent);
      const mockCtx = {foo: 'bar'};

      render(
        <WrappedComponent
          env={{}}
          $path="test/path"
          scopeRef={(s: IScopedContext) => {
            scoped = s;
            s?.registerComponent(component);
          }}
        />,
      );

      scoped!.reload('testComponent', mockCtx);
      scoped!.send('testComponent', mockCtx);

      expect(component.reload).toHaveBeenCalled();
      expect(component.receive).toHaveBeenCalled();
      expect(scoped).toBeDefined();
    });

    it('应该正确执行close和closeById操作', () => {
      let scoped: IScopedContext | null = null;
      const dialogComponent = createMockComponent({
        type: 'dialog',
        show: true,
        id: 'testDialog',
        onClose: jest.fn(),
      });
      const WrappedComponent = HocScoped(MockComponent);

      render(
        <WrappedComponent
          env={{}}
          $path="test/path"
          scopeRef={(s: IScopedContext) => {
            scoped = s;
            s?.registerComponent(dialogComponent);
            dialogComponent.context = s;
          }}
        />,
      );

      scoped!.close('testDialog');
      expect(dialogComponent.props.onClose).toHaveBeenCalledTimes(1);

      scoped!.closeById('testDialog');
      expect(dialogComponent.props.onClose).toHaveBeenCalledTimes(2);
    });
  });

  describe('getComponentsByRefPath', () => {
    it('应该通过引用路径找到组件', () => {
      let scoped: IScopedContext | null = null;
      const component = createMockComponent({
        $schema: {
          value: '${testVar}'
        },
        env: {
          session: 'testSession'
        }
      });
      const WrappedComponent = HocScoped(MockComponent);

      render(
        <WrappedComponent
          env={{}}
          $path="test/path"
          scopeRef={(s: IScopedContext) => {
            scoped = s;
            s?.registerComponent(component);
          }}
        />,
      );

      const foundComponents = scoped!.getComponentsByRefPath('testSession', 'testVar');
      expect(foundComponents).toContain(component);
    });

    it('找不到组件时应返回空数组', () => {
      let scoped: IScopedContext | null = null;
      const WrappedComponent = HocScoped(MockComponent);

      render(
        <WrappedComponent
          env={{}}
          $path="test/path"
          scopeRef={(s: IScopedContext) => {
            scoped = s;
          }}
        />,
      );

      expect(scoped!.getComponentsByRefPath('testSession', 'nonExistentVar')).toHaveLength(0);
    });
  });

  describe('异常处理', () => {
    it('应该正确处理异常情况', () => {
      let scoped: IScopedContext | null = null;
      const component = createMockComponent({name: 'testComponent'});
      const WrappedComponent = HocScoped(MockComponent);

      render(
        <WrappedComponent
          env={{}}
          $path="test/path"
          scopeRef={(s: IScopedContext) => {
            scoped = s;
            s?.registerComponent(component);
          }}
        />,
      );

      expect(()=>scoped!.reload('nonExistentComponent', {})).not.toThrow();
      expect(()=>scoped!.send('nonExistentComponent', {})).not.toThrow();
    });

    it('应该处理边界条件', () => {
      let scoped: IScopedContext | null = null;
      const component = createMockComponent({name: 'testComponent'});
      const WrappedComponent = HocScoped(MockComponent);

      render(
        <WrappedComponent
          env={{}}
          $path="test/path"
          scopeRef={(s: IScopedContext) => {
            scoped = s;
            s?.registerComponent(component);
          }}
        />,
      );

      expect(scoped!.getComponentByName('')).toBeUndefined();
      expect(scoped!.getComponentById('')).toBeUndefined();
    });
  })
});


