import {createObject} from '../src/utils/helper';
import {extractDataFromChainExcluding} from '../src/actions/Action';

describe('Action Data Chain Extraction', () => {
  describe('extractDataFromChainExcluding', () => {
    test('should extract data from simple object', () => {
      const obj = {
        name: 'test',
        value: 123
      };

      const result = extractDataFromChainExcluding(obj);

      expect(result).toEqual({
        name: 'test',
        value: 123
      });
    });

    test('should extract data from __super chain without action results', () => {
      const baseData = {
        baseValue: 'base',
        commonValue: 'from-base'
      };

      const middleData = createObject(baseData, {
        middleValue: 'middle',
        commonValue: 'from-middle'
      });

      const topData = createObject(middleData, {
        topValue: 'top',
        commonValue: 'from-top'
      });

      // 没有 __isActionResult 标识，应该只提取第一层
      const result = extractDataFromChainExcluding(topData);

      expect(result).toEqual({
        topValue: 'top',
        commonValue: 'from-top'
        // 不应该包含 middle 和 base 的数据
      });
    });

    test('should handle null/undefined input', () => {
      expect(extractDataFromChainExcluding(null)).toEqual({});
      expect(extractDataFromChainExcluding(undefined)).toEqual({});
      expect(extractDataFromChainExcluding('string')).toEqual({});
      expect(extractDataFromChainExcluding(123)).toEqual({});
    });

    test('should not include __super property itself', () => {
      const baseData = {
        baseValue: 'base'
      };

      const topData = createObject(baseData, {
        topValue: 'top'
      });

      const result = extractDataFromChainExcluding(topData);

      expect(result.__super).toBeUndefined();
      expect(result).toEqual({
        topValue: 'top'
        // baseValue 不应该包含，因为没有动作结果标识
      });
    });

    test('should handle actual circular references safely', () => {
      // 创建真正的循环引用
      const obj1: any = {
        value1: 'test1'
      };

      const obj2: any = {
        value2: 'test2'
      };

      // 创建循环引用
      obj1.circularRef = obj2;
      obj2.circularRef = obj1;

      const topData = createObject(obj1, {
        topValue: 'top'
      });

      // 函数应该能安全处理循环引用，只提取第一层
      const result = extractDataFromChainExcluding(topData);

      expect(result.topValue).toBe('top');
      // 不应该包含 value1，因为没有动作结果标识
      expect(result.value1).toBeUndefined();
    });

    test('should handle empty objects in chain', () => {
      const emptyBase = {};
      const middleData = createObject(emptyBase, {
        middleValue: 'middle'
      });
      const topData = createObject(middleData, {
        topValue: 'top'
      });

      const result = extractDataFromChainExcluding(topData);

      expect(result).toEqual({
        topValue: 'top'
        // middleValue 不应该包含，因为没有动作结果标识
      });
    });

    test('should handle objects with special property names', () => {
      // 测试特殊的属性名和边界情况
      const topData = {
        // 测试特殊字符的属性名
        'with-dash': 'dash-value',
        'with.dot': 'dot-value',
        'with space': 'space-value',
        'with$pecial': 'special-value',
        '123numeric': 'numeric-value',
        '': 'empty-key-value', // 空字符串作为键
        // 测试覆盖方法名（但不影响原型链）
        'customHasOwnProperty': 'custom-hasOwnProperty',
        'customToString': 'custom-toString',
        'customValueOf': 'custom-valueOf'
      };

      const result = extractDataFromChainExcluding(topData);

      // 验证所有属性都被正确提取
      expect(result['with-dash']).toBe('dash-value');
      expect(result['with.dot']).toBe('dot-value');
      expect(result['with space']).toBe('space-value');
      expect(result['with$pecial']).toBe('special-value');
      expect(result['123numeric']).toBe('numeric-value');
      expect(result['']).toBe('empty-key-value');
      expect(result.customHasOwnProperty).toBe('custom-hasOwnProperty');
      expect(result.customToString).toBe('custom-toString');
      expect(result.customValueOf).toBe('custom-valueOf');

      // 验证特殊字符属性名确实存在于结果对象中
      expect(Object.prototype.hasOwnProperty.call(result, 'with-dash')).toBe(true);
      expect(Object.prototype.hasOwnProperty.call(result, '')).toBe(true);
    });
  });

  describe('Data Merging Logic', () => {
    test('should simulate original merging behavior', () => {
      // 模拟原始的数据合并逻辑
      const rendererData = {
        rendererValue: 'renderer',
        commonValue: 'from-renderer'
      };

      const additional = {
        event: {},
        __rendererData: rendererData,
        additionalValue: 'additional',
        commonValue: 'from-additional'
      };

      const eventData = {
        eventValue: 'event',
        commonValue: 'from-event'
      };

      // 原始逻辑
      const originalMergeData = createObject(
        createObject(
          (rendererData as any).__super
            ? createObject((rendererData as any).__super, additional)
            : additional,
          rendererData
        ),
        eventData
      ) as any;

      // 验证优先级：event > renderer > additional
      expect(originalMergeData.commonValue).toBe('from-event');
      expect(originalMergeData.eventValue).toBe('event');
      expect(originalMergeData.rendererValue).toBe('renderer');
      expect(originalMergeData.additionalValue).toBe('additional');
    });

    test('should simulate new merging behavior with data extraction', () => {
      // 模拟新的数据合并逻辑（解决数据丢失问题）
      const baseData = {
        baseValue: 'base',
        commonValue: 'from-base'
      };

      const rendererData = createObject(baseData, {
        rendererValue: 'renderer',
        commonValue: 'from-renderer'
      });

      const additional = {
        event: {},
        __rendererData: rendererData,
        additionalValue: 'additional',
        commonValue: 'from-additional'
      };

      // 模拟 event.data 被 CmptAction 修改的情况
      const originalEventData = createObject(rendererData, {
        originalEventValue: 'original-event',
        commonValue: 'from-original-event'
      });

      const modifiedEventData = createObject(originalEventData, {
        newEventValue: 'new-event',
        commonValue: 'from-new-event',
        __isActionResult: true  // 添加动作结果标识
      });

      // 使用新的提取逻辑
      const eventDataExtracted = extractDataFromChainExcluding(modifiedEventData);

      const newMergeData = createObject(
        createObject(
          (rendererData as any).__super
            ? createObject((rendererData as any).__super, additional)
            : additional,
          rendererData
        ),
        eventDataExtracted
      ) as any;

      // 验证数据不丢失
      expect(newMergeData.newEventValue).toBe('new-event');
      expect(newMergeData.originalEventValue).toBe('original-event');
      expect(newMergeData.rendererValue).toBe('renderer');
      expect(newMergeData.baseValue).toBe('base');
      expect(newMergeData.additionalValue).toBe('additional');

      // 验证优先级：extracted event data > renderer > additional > base
      expect(newMergeData.commonValue).toBe('from-new-event');
    });

    test('should handle complex data chain scenario', () => {
      // 模拟复杂的数据链场景
      const globalData = {
        globalValue: 'global',
        sharedValue: 'from-global'
      };

      const pageData = createObject(globalData, {
        pageValue: 'page',
        sharedValue: 'from-page'
      });

      const rendererData = createObject(pageData, {
        rendererValue: 'renderer',
        sharedValue: 'from-renderer'
      });

      const additional = {
        event: {},
        __rendererData: rendererData
      };

      // 模拟多次事件数据修改，都有动作结果标识
      const event1 = createObject(rendererData, {
        event1Value: 'event1',
        sharedValue: 'from-event1'
      });

      const event2 = createObject(event1, {
        event2Value: 'event2',
        sharedValue: 'from-event2',
        __isActionResult: true  // 第二个动作的标识
      });

      const event3 = createObject(event2, {
        event3Value: 'event3',
        sharedValue: 'from-event3',
        __isActionResult: true  // 第三个动作的标识
      });

      // 提取完整的事件数据链
      const eventDataExtracted = extractDataFromChainExcluding(event3);

      const finalMergeData = createObject(
        createObject(
          (rendererData as any).__super
            ? createObject((rendererData as any).__super, additional)
            : additional,
          rendererData
        ),
        eventDataExtracted
      ) as any;

      // 验证动作结果和初始事件数据都被保留
      expect(finalMergeData.event3Value).toBe('event3');
      expect(finalMergeData.event2Value).toBe('event2');
      expect(finalMergeData.event1Value).toBe('event1');  // 初始事件数据
      expect(finalMergeData.rendererValue).toBe('renderer');
      expect(finalMergeData.pageValue).toBe('page');
      expect(finalMergeData.globalValue).toBe('global');

      // 验证优先级正确
      expect(finalMergeData.sharedValue).toBe('from-event3');
    });

    test('should simulate CmptAction setValue scenario', () => {
      // 模拟 CmptAction setValue 的实际场景
      const formData = {
        name: 'John',
        age: 25,
        city: 'Beijing'
      };

      const rendererData = createObject(formData, {
        componentId: 'input-name',
        componentType: 'input'
      });

      const additional = {
        event: {},
        __rendererData: rendererData
      };

      // 初始事件数据
      const initialEventData = createObject(rendererData, {
        value: 'John',
        name: 'John'
      });

      // CmptAction 修改后的事件数据
      const afterCmptAction = createObject(initialEventData, {
        value: 'Jane',
        name: 'Jane',
        actionType: 'setValue',
        __isActionResult: true  // 动作结果标识
      });

      // 使用新的提取逻辑
      const eventDataExtracted = extractDataFromChainExcluding(afterCmptAction);

      const mergeData = createObject(
        createObject(
          (rendererData as any).__super
            ? createObject((rendererData as any).__super, additional)
            : additional,
          rendererData
        ),
        eventDataExtracted
      ) as any;

      // 验证数据完整性
      expect(mergeData.value).toBe('Jane'); // 最新值
      expect(mergeData.name).toBe('Jane'); // 最新值
      expect(mergeData.actionType).toBe('setValue'); // CmptAction 添加的数据
      expect(mergeData.componentId).toBe('input-name'); // renderer 数据
      expect(mergeData.age).toBe(25); // 原始表单数据
      expect(mergeData.city).toBe('Beijing'); // 原始表单数据
    });

    test('should handle data loss prevention in ajax action chain', () => {
      // 模拟 ajax 动作链中的数据丢失预防
      const apiData = {
        userId: 123,
        token: 'abc123'
      };

      const rendererData = createObject(apiData, {
        api: '/api/user',
        method: 'POST'
      });

      const additional = {
        event: {},
        __rendererData: rendererData
      };

      // 第一个 ajax 请求的结果
      const ajax1Result = createObject(rendererData, {
        responseData: {id: 1, name: 'User1'},
        status: 'success'
      });

      // 第二个 ajax 请求基于第一个结果
      const ajax2Result = createObject(ajax1Result, {
        responseData: {id: 2, name: 'User2', detail: 'Updated'},
        status: 'success',
        requestId: 'req-002',
        __isActionResult: true  // 第二个动作结果标识
      });

      // 第三个动作可能修改事件数据
      const finalEventData = createObject(ajax2Result, {
        finalResult: 'completed',
        timestamp: Date.now(),
        __isActionResult: true  // 第三个动作结果标识
      });

      // 提取完整的事件数据链
      const eventDataExtracted = extractDataFromChainExcluding(finalEventData);

      const mergeData = createObject(
        createObject(
          (rendererData as any).__super
            ? createObject((rendererData as any).__super, additional)
            : additional,
          rendererData
        ),
        eventDataExtracted
      ) as any;

      // 验证所有动作结果都被保留
      expect(mergeData.finalResult).toBe('completed');
      expect(mergeData.requestId).toBe('req-002');
      expect(mergeData.responseData).toEqual({id: 2, name: 'User2', detail: 'Updated'});
      expect(mergeData.status).toBe('success');
      expect(mergeData.api).toBe('/api/user');
      expect(mergeData.userId).toBe(123);
      expect(mergeData.token).toBe('abc123');
    });
  });

  describe('Action Result Data Extraction with __isActionResult', () => {
    test('should extract action result data with __isActionResult marker', () => {
      // 模拟初始事件数据
      const rendererData = {
        formValue: 'initial',
        commonField: 'from-renderer'
      };

      const initialEventData = {
        selectedItems: ['A'],
        value: 'A',
        commonField: 'from-event',
        __super: rendererData
      };

      // 模拟第一个动作执行后（有 __isActionResult 标识）
      const afterFirstAction = {
        setValueResult: {
          error: '',
          payload: { success: true }
        },
        __isActionResult: true,  // 动作结果标识
        __super: initialEventData
      };

      // 模拟第二个动作执行后
      const afterSecondAction = {
        ajaxResult: {
          data: { id: 123 },
          status: 200
        },
        __isActionResult: true,  // 动作结果标识
        __super: afterFirstAction
      };

      // 提取数据
      const extracted = extractDataFromChainExcluding(afterSecondAction);

      // 验证结果：应该包含所有动作结果 + 初始事件数据
      expect(extracted).toEqual({
        ajaxResult: {
          data: { id: 123 },
          status: 200
        },
        setValueResult: {
          error: '',
          payload: { success: true }
        },
        selectedItems: ['A'],
        value: 'A',
        commonField: 'from-event'
        // 不应该包含 rendererData 中的数据
      });

      // 验证 rendererData 中的字段没有被提取
      expect(extracted.formValue).toBeUndefined();
    });

    test('should handle conflict with business field named "event"', () => {
      // 模拟包含同名 'event' 业务字段的情况
      const rendererData = {
        formValue: 'initial'
      };

      const businessData = {
        event: 'business event value',  // 业务数据中的 event 字段
        __super: rendererData
      };

      const initialEventData = {
        selectedItems: ['A'],
        value: 'A',
        __super: businessData
      };

      // 模拟动作执行后（有 __isActionResult 标识）
      const afterAction = {
        actionResult: {
          success: true
        },
        __isActionResult: true,
        __super: initialEventData
      };

      // 提取数据
      const extracted = extractDataFromChainExcluding(afterAction);

      // 验证：应该包含动作结果和初始事件数据，但不包含业务数据中的 event 字段
      expect(extracted).toEqual({
        actionResult: {
          success: true
        },
        selectedItems: ['A'],
        value: 'A'
        // 不应该包含业务数据中的 event 字段
      });

      expect(extracted.event).toBeUndefined();  // 业务数据中的 event 应该被排除
    });

    test('should fallback to original logic when no __isActionResult found', () => {
      // 测试向后兼容：没有 __isActionResult 标识时应该只提取第一层
      const rendererData = {
        rendererValue: 'renderer',
        commonValue: 'from-renderer'
      };

      const eventData = {
        eventValue: 'event',
        commonValue: 'from-event',
        __super: rendererData
      };

      const extracted = extractDataFromChainExcluding(eventData);

      // 应该只提取第一层：事件数据
      expect(extracted).toEqual({
        eventValue: 'event',
        commonValue: 'from-event'
        // 不应该包含 rendererData 中的数据
      });
    });
  });
});
