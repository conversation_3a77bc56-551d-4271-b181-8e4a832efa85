import {dataMapping, resolveMapping, resolveMappingObject} from '../src';

describe('resolveMappingObject', () => {
  test('基本对象映射', () => {
    const context = {user: {name: '张三'}};
    const result = resolveMappingObject(
      {
        name: '$user.name',
        age: 18
      },
      context
    );

    expect(result).toEqual({
      name: '张三',
      age: 18
    });
  });

  test('多层嵌套对象', () => {
    const context = {name: '李四', id: 1};
    const result = resolveMappingObject(
      {
        user: {name: '$name'},
        id: '$id'
      },
      context
    );

    expect(result).toEqual({
      user: {name: '$name'},
      id: 1
    });
  });

  test('非字符串属性', () => {
    const context = {user: {name: '王五'}};
    const result = resolveMappingObject(
      {
        name: '$user.name',
        age: 18,
        active: true
      },
      context
    );

    expect(result).toEqual({
      name: '王五',
      age: 18,
      active: true
    });
  });
});

describe('dataMapping', () => {
  test('基本数据映射', () => {
    const context = {a: 1, b: 'text', c: true};
    const result = dataMapping(
      {
        num: '${a}',
        str: 'name ${b}',
        bool: '${c}',
        arr: [1,2]
      },
      context,
    );

    expect(result).toEqual({
      num: 1,
      str: 'name text',
      bool: true,
      arr: [1,2]
    });
  });

  test('数组处理', () => {
    const context = {
      arr: [1, 2, 3],
      objArr: [
        {id: 1, name: 'a'},
        {id: 2, name: 'b'},
      ],
    };

    const result = dataMapping(
      {
        simpleArr: '${arr}',
        mappedArr: '${ARRAYMAP(objArr, item => {id: item.id})}',
      },
      context,
    );

    expect(result).toEqual({
      simpleArr: [1, 2, 3],
      mappedArr: [{id: 1}, {id: 2}],
    });
  });

  test('嵌套对象处理', () => {
    const context = {
      user: {
        name: 'John',
        address: {
          city: 'New York',
          zip: '10001',
        },
      },
    };

    const result = dataMapping(
      {
        name: '${user.name}',
        city: '${user.address.city}',
      },
      context,
    );

    expect(result).toEqual({
      name: 'John',
      city: 'New York',
    });
  });

  test('边界值处理', () => {
    const context = {
      empty: '',
      nullVal: null,
      undefinedVal: undefined,
      zero: 0,
      falseVal: false,
    };

    const result = dataMapping(
      {
        empty: '${empty}',
        nullVal: '${nullVal}',
        undefinedVal: '${undefinedVal}',
        zero: '${zero}',
        falseVal: '${falseVal}',
      },
      context,
    );

    expect(result).toEqual({
      empty: '',
      nullVal: '',
      undefinedVal: '',
      zero: 0,
      falseVal: false,
    });
  });

  test('复杂表达式', () => {
    const context = {
      a: 1,
      b: 2,
      c: [
        {a: 1, b: 1},
        {a: 2, b: 2},
      ],
    };

    const result = dataMapping(
      {
        a: '${a}',
        b: '${c}',
        c: '${ARRAYMAP(c, item => {a: item.a, c: item.b})}',
      },
      context,
    );

    expect(result).toMatchObject({
      a: 1,
      b: [
        {a: 1, b: 1},
        {a: 2, b: 2},
      ],
      c: [
        {a: 1, c: 1},
        {a: 2, c: 2},
      ],
    });
  });

  test('特殊字符处理', () => {
    const context = {};
    const result = dataMapping('含有$符号的非变量', context);
    expect(result).toBe('含有$符号的非变量');
  });

  test('变量缺失处理', () => {
    const context = {};
    const result1 = dataMapping('$nonExistVar', context, false, false, true);
    expect(result1).toBe('$nonExistVar');

    const result2 = dataMapping('$nonExistVar', context, false, false, false);
    expect(result2).toBe('');
  });

  test('多级嵌套对象', () => {
    const context = {
      user: {
        contact: {
          address: {
            city: '北京',
          },
        },
      },
    };
    const result = dataMapping('$user.contact.address.city', context);
    expect(result).toBe('北京');
  });

  test('特殊符号&和$$处理', () => {
    const context = {x: 1, y: 2};
    const result1 = dataMapping({'&': '$$'}, context);
    expect(result1).toEqual({x: 1, y: 2});

    const result2 = dataMapping({key: '$$'}, context);
    expect(result2).toEqual({key: {x: 1, y: 2}});

    // 测试'&'单独使用的情况
    expect(dataMapping({'&': {a: 3}}, context)).toEqual({a: 3});

    // 测试数组映射场景
    const arrContext = {
      items: [
        {id: 1, name: 'a'},
        {id: 2, name: 'b'},
      ],
    };
    const result3 = dataMapping(
      {
        $items: {
          id: '$id',
          name: '$name',
        },
      },
      arrContext,
    );
    expect(result3).toEqual([
      {id: 1, name: 'a'},
      {id: 2, name: 'b'},
    ]);

    // 测试特殊字符'&'与数组组合，ignoreIfNotMatch为true。
    const result4 = dataMapping(
      {
        '&': {
          $items: {
            id: '$id',
            name: '$name',
          },
        },
      },
      arrContext,
    );
    expect(result4).toEqual([
      {id: 1, name: 'a'},
      {id: 2, name: 'b'},
    ]);


    // 测试数组映射但源数据不是数组的情况，ignoreIfNotMatch为false。
    const nonArrayContext = {items: {id: 1, name: 'a'}};
    const result5 = dataMapping(
      {
        $items: {
          id: '$item.id',
          name: '$item.name',
        },
      },
      nonArrayContext,
      false,
      false,
      false,
    );
    expect(result5).toEqual([]);

    // 测试'&'符号处理函数返回值
    const fnContext = {x: 1};
    const mockFn = jest.fn(() => ({y: 2}));
    const result6 = dataMapping({'&': mockFn}, fnContext);
    expect(result6).toEqual({y: 2});
    expect(mockFn).toHaveBeenCalledWith(fnContext);
  });

  test('convertKeyToPath参数功能', () => {
    const context = {name: '张三'};
    const result1 = dataMapping({'user.name': '$name'}, context, false, true);
    expect(result1).toEqual({user: {name: '张三'}});

    const result2 = dataMapping({'user.name': '$name'}, context, false, false);
    expect(result2).toEqual({'user.name': '张三'});
  });

  test('函数值处理', () => {
    const mockFn = jest.fn(() => 'test');
    const result1 = dataMapping({key: mockFn}, {});
    expect(result1).toEqual({key: 'test'});
    expect(mockFn).toHaveBeenCalled();

    const result2 = dataMapping({_key: mockFn}, {}, key => key.startsWith('_'));
    expect(result2).toEqual({_key: mockFn});

    // 测试ignoreFunction为true时的处理
    const result3 = dataMapping({key: mockFn}, {}, true);
    expect(result3).toEqual({key: mockFn});
  });

  test('特殊字符&与字符串值组合', () => {
    const context = {x: 1};
    const result = dataMapping({'&': 'test'}, context);
    expect(result).toBe('test');
  });

  test('非字符串输入处理', () => {
    const context = {a: 1};
    expect(dataMapping(123, context)).toBe(123);
    expect(dataMapping(true, context)).toBe(true);
    expect(dataMapping(null, context)).toBeNull();
  });

  test('特殊字符组合处理', () => {
    const context = {x: 1};
    const result1 = dataMapping({'&': {key: 'value'}}, context);
    expect(result1).toEqual({key: 'value'});

    const result2 = dataMapping({'&': ['a', 'b']}, context);
    expect(result2).toEqual(['a', 'b']);
  });

  test('__undefined值处理', () => {
    const context = {
      someVar: '__undefined'
    };
    const result = dataMapping(
      {
        key1: '__undefined',
        key2: '$someVar',
        key3: 'normal',
      },
      context,
    );
    expect(result).toEqual({
      key3: 'normal',
    });
  });

  test('特殊字符&与数组映射处理', () => {
    const context = {items: [{id: 1}, {id: 2}]};
    const result = dataMapping(
      {
        '&': {
          $items: {
            id: '$id',
          },
        },
      },
      context,
    );
    expect(result).toEqual([{id: 1}, {id: 2}]);
  });

  test('变量解析失败处理', () => {
    const context = {};
    const result = dataMapping(
      {
        key: '$nonExistVar',
      },
      context,
      false,
      false,
      false,
    );
    expect(result).toEqual({
      key: '',
    });
  });

  test('特殊字符&与函数返回值处理', () => {
    const context = {x: 1};
    const mockFn = jest.fn(() => ({}));
    const result = dataMapping(
      {
        '&': mockFn,
      },
      context,
    );
    expect(result).toEqual({});
    expect(mockFn).toHaveBeenCalledWith(context);
  });

  test('convertKeyToPath与特殊字符组合处理', () => {
    const context = {name: '张三'};
    const result = dataMapping(
      {
        'user.name': '$name',
        '&': '$$',
      },
      context,
      false,
      true,
    );
    expect(result).toEqual({
      user: {name: '张三'},
      name: '张三',
    });
  });

  test('数组映射源数据非数组且ignoreIfNotMatch为false', () => {
    const context = {items: {id: 1, name: 'a'}};
    const result = dataMapping(
      {
        $items: {
          id: '$id',
          name: '$name',
        },
      },
      context,
      false,
      false,
      false,
    );
    expect(result).toEqual([]);
  });
});

describe('resolveMapping', () => {
  test('纯变量解析', () => {
    const context = {user: {name: '张三'}};
    const result = resolveMapping('$user.name', context);
    expect(result).toBe('张三');
  });

  test('边界值处理', () => {
    const context = {
      nullValue: null,
      undefinedValue: undefined,
      emptyString: '',
      zero: 0,
      falseValue: false
    };

    expect(resolveMapping('${nullValue}', context)).toBe('');
    expect(resolveMapping('${undefinedValue}', context)).toBe('');
    expect(resolveMapping('${emptyString}', context)).toBe('');
    expect(resolveMapping('${zero}', context)).toBe(0);
    expect(resolveMapping('${falseValue}', context)).toBe(false);
  });

  test('混合字符串解析', () => {
    const context = {name: '李四'};
    const result = resolveMapping('姓名:${name}', context);
    expect(result).toBe('姓名:李四');
  });

  test('非字符串类型处理', () => {
    const context = {user: {age: 18}};
    const result = resolveMapping({key: '$user.age'}, context);
    expect(result).toEqual({key: '$user.age'});
  });

  test('带过滤器格式', () => {
    const context = {price: 10000};
    expect(resolveMapping('$price', context, '| number')).toBe('10,000');
    expect(resolveMapping('${price | number}', context)).toBe('10,000');
    expect(resolveMapping('${price | number}', context, '| number')).toBe('10,000');
  });

  test('特殊字符处理', () => {
    const context = {};
    const result = resolveMapping('含有$符号的非变量', context);
    expect(result).toBe('含有$符号的非变量');
  });

  test('变量缺失处理', () => {
    const context = {};
    const result1 = resolveMapping('$nonExistVar', context, '| raw', true);
    expect(result1).toBe('$nonExistVar');

    const result2 = resolveMapping('$nonExistVar', context, '| raw', false);
    expect(result2).toBe('');

    const result3 = resolveMapping('a $nonExistVar', context, '| raw', false);
    expect(result3).toBe('a ');
    const result4 = resolveMapping('a $nonExistVar', context, '| raw', true);
    expect(result4).toBe('a ');
  });

  test('多级嵌套对象', () => {
    const context = {
      user: {
        contact: {
          address: {
            city: '北京',
          },
        },
      },
    };
    const result = resolveMapping('$user.contact.address.city', context);
    expect(result).toBe('北京');
  });
});
