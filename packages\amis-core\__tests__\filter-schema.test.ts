import {getExprProperties} from '../src/utils/filter-schema';

describe('getExprProperties', () => {
  test('基本事件处理函数', () => {
    const schema = {
      submitOn: 'click',
      cancelOn: 'data.canCancel'
    };
    const data = {canCancel: true};
    const result = getExprProperties(schema, data);

    expect(result).toEqual({
      submit: false, // 'click' 作为表达式计算，由于 data 中没有 click 变量，所以返回 false
      cancel: true
    });
  });

  test('表达式计算', () => {
    const schema = {
      titleExpr: '${title}', // 直接访问变量，不需要 data. 前缀
      contentExpr: 'Hello ${name}' // 直接访问变量，不需要 data. 前缀
    };
    const data = {title: 'Welcome', name: 'John'};
    const result = getExprProperties(schema, data);

    expect(result).toEqual({
      title: 'Welcome',
      content: 'Hello John'
    });
  });

  test('类名表达式处理', () => {
    const schema = {
      wrapperClassName: {
        'is-active': '${active}', // 直接访问变量，不需要 data. 前缀
        'is-disabled': '${disabled}' // 直接访问变量，不需要 data. 前缀
      }
    };
    const data = {active: true, disabled: false};
    const result = getExprProperties(schema, data);

    expect(result).toEqual({
      wrapperClassName: 'is-active',
      wrapperClassNameRaw: {
        'is-active': '${active}',
        'is-disabled': '${disabled}'
      }
    });
  });

  test('props注入处理', () => {
    const schema = {
      submitOn: '__props.onSubmit',
      cancelOn: '__props.onCancel'
    };
    const data = {};
    const props = {
      submit: () => 'submit', // 键名应该匹配处理后的属性名
      cancel: () => 'cancel'  // 键名应该匹配处理后的属性名
    };
    const result = getExprProperties(schema, data, undefined, props);

    expect(result.submit()).toBe('submit');
    expect(result.cancel()).toBe('cancel');
  });

  test('黑名单属性过滤', () => {
    const schema = {
      submitOn: 'click',
      addOn: 'something',
      ref: 'reference'
    };
    const result = getExprProperties(schema, {});

    expect(result).toEqual({
      submit: false // 'click' 作为表达式计算，由于 data 中没有 click 变量，所以返回 false
    });
    expect(result.addOn).toBeUndefined();
    expect(result.ref).toBeUndefined();
  });

  test('自定义黑名单', () => {
    const schema = {
      submitOn: 'click',
      customOn: 'customAction'
    };
    const result = getExprProperties(schema, {}, ['submitOn']);

    expect(result).toEqual({
      custom: false // 'customAction' 作为表达式计算，由于 data 中没有 customAction 变量，所以返回 false
    });
    expect(result.submit).toBeUndefined();
  });

  test('空值和边界条件处理', () => {
    const schema = {
      emptyOn: '',
      nullExpr: null,
      undefinedExpr: undefined,
      numberExpr: 123,
      booleanExpr: true
    };
    const result = getExprProperties(schema, {});

    expect(result).toEqual({});
  });
});
