import {getVariable} from '../src/utils/getVariable';

describe('getVariable', () => {
  test('基础变量解析', () => {
    const data = {
      name: 'World',
      price: 100,
      empty: '',
      zero: 0
    };

    expect(getVariable(data, 'name')).toBe('World');
    expect(getVariable(data, 'price')).toBe(100);
    expect(getVariable(data, 'empty')).toBe('');
    expect(getVariable(data, 'zero')).toBe(0);
    expect(getVariable(data, 'nonexistent')).toBe(undefined);
  });

  test('嵌套对象处理', () => {
    const data = {
      user: {
        name: '<PERSON>',
        profile: {
          age: 25,
          email: '<EMAIL>'
        }
      },
      settings: {
        theme: 'dark',
        notifications: {
          email: true,
          sms: false
        }
      }
    };

    expect(getVariable(data, 'user.name')).toBe('John');
    expect(getVariable(data, 'user.profile.age')).toBe(25);
    expect(getVariable(data, 'user.profile.email')).toBe('<EMAIL>');
    expect(getVariable(data, 'settings.theme')).toBe('dark');
    expect(getVariable(data, 'settings.notifications.email')).toBe(true);
    expect(getVariable(data, 'settings.notifications.sms')).toBe(false);
    expect(getVariable(data, 'user.profile.nonexistent')).toBe(undefined);
  });

  test('数组索引访问', () => {
    const data = {
      items: ['first', 'second', 'third'],
      users: [
        {name: 'Alice', age: 20},
        {name: 'Bob', age: 30}
      ],
      matrix: [
        [1, 2],
        [3, 4]
      ]
    };

    expect(getVariable(data, 'items[0]')).toBe('first');
    expect(getVariable(data, 'users[0].name')).toBe('Alice');
    expect(getVariable(data, 'users[1].age')).toBe(30);
    expect(getVariable(data, 'matrix[0][1]')).toBe(2);
    expect(getVariable(data, 'items[3]')).toBe(undefined);
    expect(getVariable(data, 'users[2].name')).toBe(undefined);
  });

  test('空值处理', () => {
    expect(getVariable({}, 'key')).toBe(undefined);
    expect(getVariable({key: null}, 'key')).toBe(null);
    expect(getVariable({key: undefined}, 'key')).toBe(undefined);
  });

  test('canAccessSuper参数', () => {
    const parent = {
      name: 'parent',
      value: 'parent_value'
    };
    const child = Object.create(parent);
    child.name = 'child';

    expect(getVariable(child, 'name', true)).toBe('child');
    expect(getVariable(child, 'value', true)).toBe('parent_value');
    expect(getVariable(child, 'value', false)).toBe(undefined);
  });

  test('边界条件处理', () => {
    const data = {
      'special.key': 'special value',
      'array': [1, 2, 3],
      'object': {key: 'value'}
    };

    expect(getVariable(data, '')).toBe(undefined);
    expect(getVariable(data, undefined)).toBe(undefined);
    expect(getVariable(data, 'special.key')).toBe('special value');
    expect(getVariable('not an object' as any, 'key')).toBe(undefined);
  });
});