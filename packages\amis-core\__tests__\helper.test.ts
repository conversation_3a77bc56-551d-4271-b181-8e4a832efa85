import { filterTree, searchTreeNotOnlyLeaf, eachTree, isArrayChildrenModified, isObjectShallowModified } from '../src/utils/helper';

describe('filterTree', () => {
  test('基本树形结构过滤', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B'},
          {id: 3, label: 'C'}
        ]
      },
      {
        id: 4,
        label: 'D',
        children: [
          {id: 5, label: 'E'},
          {id: 6, label: 'F'}
        ]
      }
    ];

    let filtered = filterTree(tree, item => item.id !== 3);
    expect(filtered).toHaveLength(2);
    expect(filtered[0].children).toHaveLength(1);
    expect(filtered[0].children?.[0].label).toBe('B');

    const tree1 = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B'},
          {id: 3, label: 'C'}
        ]
      },
      {
        id: 4,
        label: 'D',
        children: [
          {id: 5, label: 'E'},
          {id: 6, label: 'F'}
        ]
      }
    ];

    filtered = filterTree(tree1, item => {
      item.id !== 4
    });
    expect(filtered).toHaveLength(1);
    expect(filtered[0].label).toBe('A');
  });

  test('基本树形结构过滤-深度优先遍历', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B'},
          {id: 3, label: 'C'}
        ]
      },
      {
        id: 4,
        label: 'D',
        children: [
          {id: 5, label: 'E'},
          {id: 6, label: 'F'}
        ]
      }
    ];

    const filtered = filterTree(tree, item => {
      item.id !== 4
    }, 1, true);
    expect(filtered).toHaveLength(1);
    expect(filtered[0].label).toBe('A');
  });

  test('深度优先遍历过滤 - 只保留有子节点的节点', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B', children: [{id: 3, label: 'C'}]},
          {id: 4, label: 'D'}
        ]
      }
    ];

    const filtered = filterTree(tree, item => {
      Array.isArray(item.children) && item.children.length > 0
    }, 1, true);
    expect(filtered).toHaveLength(0);
  });

  test('广度优先遍历过滤 - 只保留有子节点的节点', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B', children: [{id: 3, label: 'C'}]},
          {id: 4, label: 'D'}
        ]
      }
    ];

    const filtered = filterTree(tree, item => Array.isArray(item.children) && item.children.length > 0);
    expect(filtered).toHaveLength(1);
    expect(filtered[0].label).toBe('A');
    expect(filtered[0].children).toHaveLength(1);
    expect(filtered[0].children[0].label).toBe('B');
  });

  test('空树过滤', () => {
    const tree: any[] = [];
    const filtered = filterTree(tree, () => true);
    expect(filtered).toHaveLength(0);
  });

  test('过滤所有节点', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [{id: 2, label: 'B'}]
      }
    ];

    const filtered = filterTree(tree, () => false);
    expect(filtered).toHaveLength(0);
  });

  test('保留所有节点', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [{id: 2, label: 'B'}]
      }
    ];

    const filtered = filterTree(tree, () => true);
    expect(filtered).toEqual(tree);
  });

  test('根据节点层级过滤', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B', children: [{id: 3, label: 'C'}]},
          {id: 4, label: 'D'}
        ]
      }
    ];

    const filtered = filterTree(tree, (item, index, level) => level <= 2);
    expect(filtered).toHaveLength(1);
    expect(filtered[0].children).toHaveLength(2);
    expect(filtered[0].children?.[0].children).toHaveLength(0);
  });

  test('根据节点路径过滤', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B', children: [{id: 3, label: 'C'}]},
          {id: 4, label: 'D'}
        ]
      }
    ];

    const filtered = filterTree(tree, (item, index, level, paths) => {
      // 过滤掉路径中包含label为'B'的节点及其子节点
      return !paths.some(node => node.label === 'B');
    });

    expect(filtered).toHaveLength(1);
    expect(filtered[0].children).toHaveLength(2);
    expect(filtered[0].children?.[0].label).toBe('B');
    expect(filtered[0].children?.[0].children).toHaveLength(0);
    expect(filtered[0].children?.[1].label).toBe('D');
  });
});

describe('searchTreeNotOnlyLeaf', () => {
  test('基本树形结构搜索', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B'},
          {id: 3, label: 'C'}
        ]
      },
      {
        id: 4,
        label: 'D',
        children: [
          {id: 5, label: 'E'},
          {id: 6, label: 'F'}
        ]
      }
    ];

    let filtered = searchTreeNotOnlyLeaf(tree, item => item.id === 1);
    expect(filtered).toHaveLength(1);
    expect(filtered[0].label).toBe('A');
    expect(filtered[0].children).toHaveLength(2);

    filtered = searchTreeNotOnlyLeaf(tree, item => item.id === 4);
    expect(filtered).toHaveLength(1);
    expect(filtered[0].label).toBe('D');
    expect(filtered[0].children).toHaveLength(2);
  });

  test('多层节点搜索', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B', children: [{id: 3, label: 'C'}]},
          {id: 4, label: 'D'}
        ]
      }
    ];

    const filtered = searchTreeNotOnlyLeaf(tree, item => item.id === 2);
    expect(filtered).toHaveLength(1);
    expect(filtered[0].label).toBe('A');
    expect(filtered[0].children).toHaveLength(1);
    expect(filtered[0].children[0].label).toBe('B');
    expect(filtered[0].children[0].children).toHaveLength(1);
    expect(filtered[0].children[0].children[0].label).toBe('C');
  });

  test('搜索叶子节点', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B', children: [{id: 3, label: 'C'}]},
          {id: 4, label: 'D'}
        ]
      }
    ];

    const filtered = searchTreeNotOnlyLeaf(tree, item => item.id === 3);
    expect(filtered).toHaveLength(1);
    expect(filtered[0].label).toBe('A');
    expect(filtered[0].children).toHaveLength(1);
    expect(filtered[0].children[0].label).toBe('B');
    expect(filtered[0].children[0].children).toHaveLength(1);
    expect(filtered[0].children[0].children[0].label).toBe('C')
  });

  test('搜索多个兄弟节点', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B', children: [{id: 5, label: 'E'}]},
          {id: 3, label: 'B', children: [{id: 6, label: 'F'}]},
          {id: 4, label: 'C'}
        ]
      }
    ];

    const filtered = searchTreeNotOnlyLeaf(tree, item => item.label === 'B');
    expect(filtered).toHaveLength(1);
    expect(filtered[0].label).toBe('A');
    expect(filtered[0].children).toHaveLength(2);
    expect(filtered[0].children[0].label).toBe('B');
    expect(filtered[0].children[0].children).toHaveLength(1);
    expect(filtered[0].children[0].children[0].label).toBe('E');
    expect(filtered[0].children[1].label).toBe('B');
    expect(filtered[0].children[1].children).toHaveLength(1);
    expect(filtered[0].children[1].children[0].label).toBe('F');
  });

  test('空树搜索', () => {
    const tree: any[] = [];
    const filtered = searchTreeNotOnlyLeaf(tree, () => true);
    expect(filtered).toHaveLength(0);
  });

  test('搜索不存在的节点', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [{id: 2, label: 'B'}]
      }
    ];

    const filtered = searchTreeNotOnlyLeaf(tree, item => item.id === 999);

    expect(filtered).toHaveLength(0);
  });

  test('搜索所有节点', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [{id: 2, label: 'B'}]
      }
    ];

    const filtered = searchTreeNotOnlyLeaf(tree, () => true);
    expect(filtered).toEqual(tree);
  });
});

describe('eachTree', () => {
  test('基本树形结构遍历', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B'},
          {id: 3, label: 'C'}
        ]
      },
      {
        id: 4,
        label: 'D',
        children: [
          {id: 5, label: 'E'},
          {id: 6, label: 'F'}
        ]
      }
    ];

    const result: string[] = [];
    eachTree(tree, item => result.push(item.label));
    expect(result).toEqual(['A', 'B', 'C', 'D', 'E', 'F']);
  });

  test('深度优先遍历 - childFirst=true', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B', children: [{id: 3, label: 'C'}]},
          {id: 4, label: 'D'}
        ]
      }
    ];

    const result: string[] = [];
    eachTree(tree, item => {
      result.push(item.label)
    }, 1, [], true);
    expect(result).toEqual(['C', 'B', 'D', 'A']);
  });

  test('空树遍历', () => {
    const tree: any[] = [];
    const result: any[] = [];
    eachTree(tree, item => result.push(item));
    expect(result).toHaveLength(0);
  });

  test('多层节点遍历和层级记录', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B', children: [{id: 3, label: 'C'}]},
          {id: 4, label: 'D'}
        ]
      }
    ];

    const result: Array<{label: string; level: number}> = [];
    eachTree(tree, (item, index, level) => {
      result.push({label: item.label, level});
    });

    expect(result).toEqual([
      {label: 'A', level: 1},
      {label: 'B', level: 2},
      {label: 'C', level: 3},
      {label: 'D', level: 2}
    ]);
  });

  test('节点路径记录', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B', children: [{id: 3, label: 'C'}]},
          {id: 4, label: 'D'}
        ]
      }
    ];

    const paths: Array<string[]> = [];
    eachTree(tree, (item, index, level, nodePaths) => {
      paths.push(nodePaths.map(node => node.label));
    });

    expect(paths).toEqual([
      ['A'],
      ['A', 'B'],
      ['A', 'B', 'C'],
      ['A', 'D']
    ]);
  });
});

describe('isArrayChildrenModified', () => {
  test('基本数组比较', () => {
    const prev = [1, 2, 3];
    const next = [1, 2, 3];
    expect(isArrayChildrenModified(prev, next)).toBe(false);

    const next2 = [1, 2, 4];
    expect(isArrayChildrenModified(prev, next2)).toBe(true);
  });

  test('非数组输入比较', () => {
    const prev = 'test';
    const next = 'test';
    expect(isArrayChildrenModified(prev as any, next as any)).toBe(false);

    const next2 = 'test2';
    expect(isArrayChildrenModified(prev as any, next2 as any)).toBe(true);
  });

  test('数组长度不同比较', () => {
    const prev = [1, 2, 3];
    const next = [1, 2];
    expect(isArrayChildrenModified(prev, next)).toBe(true);

    const next2 = [1, 2, 3, 4];
    expect(isArrayChildrenModified(prev, next2)).toBe(true);
  });

  test('嵌套数组children比较', () => {
    const prev = [
      {id: 1, children: [{id: 2}]},
      {id: 3, children: [{id: 4}]}
    ];
    const next = [
      {id: 1, children: [{id: 2}]},
      {id: 3, children: [{id: 4}]}
    ];
    expect(isArrayChildrenModified(prev, next)).toBe(true);

    const next2 = [
      {id: 1, children: [{id: 2}]},
      {id: 3, children: [{id: 5}]}
    ];
    expect(isArrayChildrenModified(prev, next2)).toBe(true);
  });

  test('非严格模式比较', () => {
    const prev = [1, '2', 3];
    const next = [1, 2, 3];
    expect(isArrayChildrenModified(prev, next, false)).toBe(false);

    const prev2 = [{id: 1, children: [{id: '2'}]}];
    const next2 = [{id: 1, children: [{id: 2}]}];
    expect(isArrayChildrenModified(prev2, next2, false)).toBe(true);
  });

  test('数组元素为null或undefined的比较', () => {
    const prev = [1, null, 3];
    const next = [1, null, 3];
    expect(isArrayChildrenModified(prev, next)).toBe(false);

    const next2 = [1, undefined, 3];
    expect(isArrayChildrenModified(prev, next2)).toBe(true);
  });

  test('部分元素包含children属性的比较', () => {
    const prev = [
      {id: 1, children: [{id: 2}]},
      {id: 3},
      {id: 4, children: [{id: 5}]}
    ];
    const next = [
      {id: 1, children: [{id: 2}]},
      {id: 3},
      {id: 4, children: [{id: 5}]}
    ];
    expect(isArrayChildrenModified(prev, next)).toBe(true);
    expect(isArrayChildrenModified(prev, next, false)).toBe(true);
  });

  test('多层嵌套children的比较', () => {
    const prev = [
      {
        id: 1,
        children: [{
          id: 2,
          children: [{
            id: 3,
            children: [{id: 4}]
          }]
        }]
      }
    ];
    const next = [
      {
        id: 1,
        children: [{
          id: 2,
          children: [{
            id: 3,
            children: [{id: 4}]
          }]
        }]
      }
    ];
    expect(isArrayChildrenModified(prev, next)).toBe(true);

    const next2 = [
      {
        id: 1,
        children: [{
          id: 2,
          children: [{
            id: 3,
            children: [{id: 5}]
          }]
        }]
      }
    ];
    expect(isArrayChildrenModified(prev, next2)).toBe(true);
  });
});

describe('isObjectShallowModified', () => {
  test('基本对象比较', () => {
    const prev = { a: 1, b: 2 };
    const next = { a: 1, b: 2 };
    expect(isObjectShallowModified(prev, next)).toBe(false);

    const next2 = { a: 1, b: 3 };
    expect(isObjectShallowModified(prev, next2)).toBe(true);
  });

  test('数组比较', () => {
    const prev = [1, 2, 3];
    const next = [1, 2, 3];
    expect(isObjectShallowModified(prev, next)).toBe(false);

    const next2 = [1, 2, 4];
    expect(isObjectShallowModified(prev, next2)).toBe(true);

    const next3 = [1, 2];
    expect(isObjectShallowModified(prev, next3)).toBe(true);
  });

  test('NaN值比较', () => {
    const prev = { a: NaN };
    const next = { a: NaN };
    expect(isObjectShallowModified(prev, next)).toBe(false);
  });

  test('null和undefined处理', () => {
    expect(isObjectShallowModified(null, null)).toBe(false);
    expect(isObjectShallowModified(undefined, undefined)).toBe(false);
    expect(isObjectShallowModified(null, undefined)).toBe(true);
    expect(isObjectShallowModified({}, null)).toBe(true);
  });

  test('循环引用处理', () => {
    const prev: any = { a: 1 };
    prev.self = prev;
    const next: any = { a: 1 };
    next.self = next;

    expect(isObjectShallowModified(prev, next)).toBe(false);

    next.a = 2;
    expect(isObjectShallowModified(prev, next)).toBe(true);
  });

  test('非严格模式比较', () => {
    const prev = { a: 1, b: '2' };
    const next = { a: 1, b: 2 };
    expect(isObjectShallowModified(prev, next, true)).toBe(true);
    expect(isObjectShallowModified(prev, next, false)).toBe(false);
  });

  test('忽略undefined比较', () => {
    const prev = { a: 1, b: undefined, c: 2 };
    const next = { a: 1, c: 2 };
    expect(isObjectShallowModified(prev, next)).toBe(true);
    expect(isObjectShallowModified(prev, next, true, true)).toBe(false);
  });

  test('嵌套对象比较', () => {
    const prev = { a: { b: 1 }, c: 2 };
    const next = { a: { b: 1 }, c: 2 };
    expect(isObjectShallowModified(prev, next)).toBe(false);

    const next2 = { a: { b: 2 }, c: 2 };
    expect(isObjectShallowModified(prev, next2)).toBe(true);
  });

  test('对象属性顺序不同比较', () => {
    const prev = { a: 1, b: 2 };
    const next = { b: 2, a: 1 };
    expect(isObjectShallowModified(prev, next)).toBe(false);
  });

  test('嵌套对象数组比较', () => {
    const prev = [
      {id: 1, data: {value: 1}},
      {id: 2, data: {value: 2}}
    ];
    const next = [
      {id: 1, data: {value: 1}},
      {id: 2, data: {value: 2}}
    ];
    expect(isObjectShallowModified(prev, next)).toBe(false);

    const next2 = [
      {id: 1, data: {value: 1}},
      {id: 2, data: {value: 3}}
    ];
    expect(isObjectShallowModified(prev, next2)).toBe(true);

    const next3 = [
      {id: 1, data: {value: 1}},
      {id: 2, data: {value: '2'}}
    ];
    expect(isObjectShallowModified(prev, next3, true)).toBe(true);
    expect(isObjectShallowModified(prev, next3, false)).toBe(false);
  });

  test('嵌套对象数组引用比较', () => {
    const data = {value: 1};
    const prev = [
      {id: 1, data},
      {id: 2, data: {value: 2}}
    ];
    const next = [
      {id: 1, data},
      {id: 2, data: {value: 2}}
    ];
    expect(isObjectShallowModified(prev, next)).toBe(false);

    const next2 = [
      {id: 1, data: {...data}},
      {id: 2, data: {value: 2}}
    ];
    expect(isObjectShallowModified(prev, next2)).toBe(false);
  });

  test('对象属性顺序不同比较', () => {
    const prev = { a: 1, b: 2 };
    const next = { b: 2, a: 1 };
    expect(isObjectShallowModified(prev, next)).toBe(false);
  });

  test('复用对象&循环引用比较', () => {
    const o = { c: 1 };
    const o1 = {
      a: o,
      d: {
        b: o,
      },
    };
    const o2 = {
      a: {
        c: 1
      },
      d: {
        b: o,
      },
    };
    expect(isObjectShallowModified(o1, o2)).toBe(false);

    const o3 = {
      a: {
        b: o,
      },
      d: o,
    };
    const o4 = {
      a: {
        b: o,
      },
      d: {
        c: 1
      },
    };
    expect(isObjectShallowModified(o3, o4)).toBe(false);

    let o5 = {
      a: {
        c: 1
      }
    }
    o5.b = o5;
    let o6 = {
      a: o
    }
    o6.b = o6;
    expect(isObjectShallowModified(o5, o6)).toBe(false);

    const oa = { c: 1 }
    const o7 = {
      d: o,
      b: oa,
      a: o,
    };
    const o8 = {
      d: { c: 1 },
      b: { c: 1 },
      a: oa,
    };
    expect(isObjectShallowModified(o7, o8)).toBe(false);
  });
});
