import { filterTree, searchTreeNotOnlyLeaf, eachTree, isArrayChildrenModified, isObjectShallowModified, isSuperDataModified, syncDataFromSuper } from '../src/utils/helper';

describe('filterTree', () => {
  test('基本树形结构过滤', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B'},
          {id: 3, label: 'C'}
        ]
      },
      {
        id: 4,
        label: 'D',
        children: [
          {id: 5, label: 'E'},
          {id: 6, label: 'F'}
        ]
      }
    ];

    let filtered = filterTree(tree, item => item.id !== 3);
    expect(filtered).toHaveLength(2);
    expect(filtered[0].children).toHaveLength(1);
    expect(filtered[0].children?.[0].label).toBe('B');

    const tree1 = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B'},
          {id: 3, label: 'C'}
        ]
      },
      {
        id: 4,
        label: 'D',
        children: [
          {id: 5, label: 'E'},
          {id: 6, label: 'F'}
        ]
      }
    ];

    filtered = filterTree(tree1, item => {
      item.id !== 4
    });
    expect(filtered).toHaveLength(1);
    expect(filtered[0].label).toBe('A');
  });

  test('基本树形结构过滤-深度优先遍历', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B'},
          {id: 3, label: 'C'}
        ]
      },
      {
        id: 4,
        label: 'D',
        children: [
          {id: 5, label: 'E'},
          {id: 6, label: 'F'}
        ]
      }
    ];

    const filtered = filterTree(tree, item => {
      item.id !== 4
    }, 1, true);
    expect(filtered).toHaveLength(1);
    expect(filtered[0].label).toBe('A');
  });

  test('深度优先遍历过滤 - 只保留有子节点的节点', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B', children: [{id: 3, label: 'C'}]},
          {id: 4, label: 'D'}
        ]
      }
    ];

    const filtered = filterTree(tree, item => {
      Array.isArray(item.children) && item.children.length > 0
    }, 1, true);
    expect(filtered).toHaveLength(0);
  });

  test('广度优先遍历过滤 - 只保留有子节点的节点', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B', children: [{id: 3, label: 'C'}]},
          {id: 4, label: 'D'}
        ]
      }
    ];

    const filtered = filterTree(tree, item => Array.isArray(item.children) && item.children.length > 0);
    expect(filtered).toHaveLength(1);
    expect(filtered[0].label).toBe('A');
    expect(filtered[0].children).toHaveLength(1);
    expect(filtered[0].children[0].label).toBe('B');
  });

  test('空树过滤', () => {
    const tree: any[] = [];
    const filtered = filterTree(tree, () => true);
    expect(filtered).toHaveLength(0);
  });

  test('过滤所有节点', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [{id: 2, label: 'B'}]
      }
    ];

    const filtered = filterTree(tree, () => false);
    expect(filtered).toHaveLength(0);
  });

  test('保留所有节点', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [{id: 2, label: 'B'}]
      }
    ];

    const filtered = filterTree(tree, () => true);
    expect(filtered).toEqual(tree);
  });

  test('根据节点层级过滤', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B', children: [{id: 3, label: 'C'}]},
          {id: 4, label: 'D'}
        ]
      }
    ];

    const filtered = filterTree(tree, (item, index, level) => level <= 2);
    expect(filtered).toHaveLength(1);
    expect(filtered[0].children).toHaveLength(2);
    expect(filtered[0].children?.[0].children).toHaveLength(0);
  });

  test('根据节点路径过滤', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B', children: [{id: 3, label: 'C'}]},
          {id: 4, label: 'D'}
        ]
      }
    ];

    const filtered = filterTree(tree, (item, index, level, paths) => {
      // 过滤掉路径中包含label为'B'的节点及其子节点
      return !paths.some(node => node.label === 'B');
    });

    expect(filtered).toHaveLength(1);
    expect(filtered[0].children).toHaveLength(2);
    expect(filtered[0].children?.[0].label).toBe('B');
    expect(filtered[0].children?.[0].children).toHaveLength(0);
    expect(filtered[0].children?.[1].label).toBe('D');
  });
});

describe('searchTreeNotOnlyLeaf', () => {
  test('基本树形结构搜索', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B'},
          {id: 3, label: 'C'}
        ]
      },
      {
        id: 4,
        label: 'D',
        children: [
          {id: 5, label: 'E'},
          {id: 6, label: 'F'}
        ]
      }
    ];

    let filtered = searchTreeNotOnlyLeaf(tree, (item: any) => item.id === 1);
    expect(filtered).toHaveLength(1);
    expect(filtered[0].label).toBe('A');
    expect(filtered[0].children).toHaveLength(2);

    filtered = searchTreeNotOnlyLeaf(tree, (item: any) => item.id === 4);
    expect(filtered).toHaveLength(1);
    expect(filtered[0].label).toBe('D');
    expect(filtered[0].children).toHaveLength(2);
  });

  test('多层节点搜索', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B', children: [{id: 3, label: 'C'}]},
          {id: 4, label: 'D'}
        ]
      }
    ];

    const filtered = searchTreeNotOnlyLeaf(tree, (item: any) => item.id === 2);
    expect(filtered).toHaveLength(1);
    expect(filtered[0].label).toBe('A');
    expect(filtered[0].children).toHaveLength(1);
    expect(filtered[0].children[0].label).toBe('B');
    expect(filtered[0].children[0].children).toHaveLength(1);
    expect(filtered[0].children[0].children[0].label).toBe('C');
  });

  test('搜索叶子节点', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B', children: [{id: 3, label: 'C'}]},
          {id: 4, label: 'D'}
        ]
      }
    ];

    const filtered = searchTreeNotOnlyLeaf(tree, (item: any) => item.id === 3);
    expect(filtered).toHaveLength(1);
    expect(filtered[0].label).toBe('A');
    expect(filtered[0].children).toHaveLength(1);
    expect(filtered[0].children[0].label).toBe('B');
    expect(filtered[0].children[0].children).toHaveLength(1);
    expect(filtered[0].children[0].children[0].label).toBe('C')
  });

  test('搜索多个兄弟节点', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B', children: [{id: 5, label: 'E'}]},
          {id: 3, label: 'B', children: [{id: 6, label: 'F'}]},
          {id: 4, label: 'C'}
        ]
      }
    ];

    const filtered = searchTreeNotOnlyLeaf(tree, (item: any) => item.label === 'B');
    expect(filtered).toHaveLength(1);
    expect(filtered[0].label).toBe('A');
    expect(filtered[0].children).toHaveLength(2);
    expect(filtered[0].children[0].label).toBe('B');
    expect(filtered[0].children[0].children).toHaveLength(1);
    expect(filtered[0].children[0].children[0].label).toBe('E');
    expect(filtered[0].children[1].label).toBe('B');
    expect(filtered[0].children[1].children).toHaveLength(1);
    expect(filtered[0].children[1].children[0].label).toBe('F');
  });

  test('空树搜索', () => {
    const tree: any[] = [];
    const filtered = searchTreeNotOnlyLeaf(tree, () => true);
    expect(filtered).toHaveLength(0);
  });

  test('搜索不存在的节点', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [{id: 2, label: 'B'}]
      }
    ];

    const filtered = searchTreeNotOnlyLeaf(tree, (item: any) => item.id === 999);

    expect(filtered).toHaveLength(0);
  });

  test('搜索所有节点', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [{id: 2, label: 'B'}]
      }
    ];

    const filtered = searchTreeNotOnlyLeaf(tree, () => true);
    expect(filtered).toEqual(tree);
  });
});

describe('eachTree', () => {
  test('基本树形结构遍历', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B'},
          {id: 3, label: 'C'}
        ]
      },
      {
        id: 4,
        label: 'D',
        children: [
          {id: 5, label: 'E'},
          {id: 6, label: 'F'}
        ]
      }
    ];

    const result: string[] = [];
    eachTree(tree, item => result.push(item.label));
    expect(result).toEqual(['A', 'B', 'C', 'D', 'E', 'F']);
  });

  test('深度优先遍历 - childFirst=true', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B', children: [{id: 3, label: 'C'}]},
          {id: 4, label: 'D'}
        ]
      }
    ];

    const result: string[] = [];
    eachTree(tree, item => {
      result.push(item.label)
    }, 1, [], true);
    expect(result).toEqual(['C', 'B', 'D', 'A']);
  });

  test('空树遍历', () => {
    const tree: any[] = [];
    const result: any[] = [];
    eachTree(tree, item => result.push(item));
    expect(result).toHaveLength(0);
  });

  test('多层节点遍历和层级记录', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B', children: [{id: 3, label: 'C'}]},
          {id: 4, label: 'D'}
        ]
      }
    ];

    const result: Array<{label: string; level: number}> = [];
    eachTree(tree, (item, index, level) => {
      result.push({label: item.label, level});
    });

    expect(result).toEqual([
      {label: 'A', level: 1},
      {label: 'B', level: 2},
      {label: 'C', level: 3},
      {label: 'D', level: 2}
    ]);
  });

  test('节点路径记录', () => {
    const tree = [
      {
        id: 1,
        label: 'A',
        children: [
          {id: 2, label: 'B', children: [{id: 3, label: 'C'}]},
          {id: 4, label: 'D'}
        ]
      }
    ];

    const paths: Array<string[]> = [];
    eachTree(tree, (item, index, level, nodePaths) => {
      paths.push(nodePaths.map(node => node.label));
    });

    expect(paths).toEqual([
      ['A'],
      ['A', 'B'],
      ['A', 'B', 'C'],
      ['A', 'D']
    ]);
  });
});

describe('isArrayChildrenModified', () => {
  test('基本数组比较', () => {
    const prev = [1, 2, 3];
    const next = [1, 2, 3];
    expect(isArrayChildrenModified(prev, next)).toBe(false);

    const next2 = [1, 2, 4];
    expect(isArrayChildrenModified(prev, next2)).toBe(true);
  });

  test('非数组输入比较', () => {
    const prev = 'test';
    const next = 'test';
    expect(isArrayChildrenModified(prev as any, next as any)).toBe(false);

    const next2 = 'test2';
    expect(isArrayChildrenModified(prev as any, next2 as any)).toBe(true);
  });

  test('数组长度不同比较', () => {
    const prev = [1, 2, 3];
    const next = [1, 2];
    expect(isArrayChildrenModified(prev, next)).toBe(true);

    const next2 = [1, 2, 3, 4];
    expect(isArrayChildrenModified(prev, next2)).toBe(true);
  });

  test('嵌套数组children比较', () => {
    const prev = [
      {id: 1, children: [{id: 2}]},
      {id: 3, children: [{id: 4}]}
    ];
    const next = [
      {id: 1, children: [{id: 2}]},
      {id: 3, children: [{id: 4}]}
    ];
    expect(isArrayChildrenModified(prev, next)).toBe(true);

    const next2 = [
      {id: 1, children: [{id: 2}]},
      {id: 3, children: [{id: 5}]}
    ];
    expect(isArrayChildrenModified(prev, next2)).toBe(true);
  });

  test('非严格模式比较', () => {
    const prev = [1, '2', 3];
    const next = [1, 2, 3];
    expect(isArrayChildrenModified(prev, next, false)).toBe(false);

    const prev2 = [{id: 1, children: [{id: '2'}]}];
    const next2 = [{id: 1, children: [{id: 2}]}];
    expect(isArrayChildrenModified(prev2, next2, false)).toBe(true);
  });

  test('数组元素为null或undefined的比较', () => {
    const prev = [1, null, 3];
    const next = [1, null, 3];
    expect(isArrayChildrenModified(prev, next)).toBe(false);

    const next2 = [1, undefined, 3];
    expect(isArrayChildrenModified(prev, next2)).toBe(true);
  });

  test('部分元素包含children属性的比较', () => {
    const prev = [
      {id: 1, children: [{id: 2}]},
      {id: 3},
      {id: 4, children: [{id: 5}]}
    ];
    const next = [
      {id: 1, children: [{id: 2}]},
      {id: 3},
      {id: 4, children: [{id: 5}]}
    ];
    expect(isArrayChildrenModified(prev, next)).toBe(true);
    expect(isArrayChildrenModified(prev, next, false)).toBe(true);
  });

  test('多层嵌套children的比较', () => {
    const prev = [
      {
        id: 1,
        children: [{
          id: 2,
          children: [{
            id: 3,
            children: [{id: 4}]
          }]
        }]
      }
    ];
    const next = [
      {
        id: 1,
        children: [{
          id: 2,
          children: [{
            id: 3,
            children: [{id: 4}]
          }]
        }]
      }
    ];
    expect(isArrayChildrenModified(prev, next)).toBe(true);

    const next2 = [
      {
        id: 1,
        children: [{
          id: 2,
          children: [{
            id: 3,
            children: [{id: 5}]
          }]
        }]
      }
    ];
    expect(isArrayChildrenModified(prev, next2)).toBe(true);
  });
});

describe('isObjectShallowModified', () => {
  test('基本对象比较', () => {
    const prev = { a: 1, b: 2 };
    const next = { a: 1, b: 2 };
    expect(isObjectShallowModified(prev, next)).toBe(false);

    const next2 = { a: 1, b: 3 };
    expect(isObjectShallowModified(prev, next2)).toBe(true);
  });

  test('数组比较', () => {
    const prev = [1, 2, 3];
    const next = [1, 2, 3];
    expect(isObjectShallowModified(prev, next)).toBe(false);

    const next2 = [1, 2, 4];
    expect(isObjectShallowModified(prev, next2)).toBe(true);

    const next3 = [1, 2];
    expect(isObjectShallowModified(prev, next3)).toBe(true);
  });

  test('NaN值比较', () => {
    const prev = { a: NaN };
    const next = { a: NaN };
    expect(isObjectShallowModified(prev, next)).toBe(false);
  });

  test('null和undefined处理', () => {
    expect(isObjectShallowModified(null, null)).toBe(false);
    expect(isObjectShallowModified(undefined, undefined)).toBe(false);
    expect(isObjectShallowModified(null, undefined)).toBe(true);
    expect(isObjectShallowModified({}, null)).toBe(true);
  });

  test('循环引用处理', () => {
    const prev: any = { a: 1 };
    prev.self = prev;
    const next: any = { a: 1 };
    next.self = next;

    expect(isObjectShallowModified(prev, next)).toBe(false);

    next.a = 2;
    expect(isObjectShallowModified(prev, next)).toBe(true);
  });

  test('非严格模式比较', () => {
    const prev = { a: 1, b: '2' };
    const next = { a: 1, b: 2 };
    expect(isObjectShallowModified(prev, next, true)).toBe(true);
    expect(isObjectShallowModified(prev, next, false)).toBe(false);
  });

  test('忽略undefined比较', () => {
    const prev = { a: 1, b: undefined, c: 2 };
    const next = { a: 1, c: 2 };
    expect(isObjectShallowModified(prev, next)).toBe(true);
    expect(isObjectShallowModified(prev, next, true, true)).toBe(false);
  });

  test('嵌套对象比较', () => {
    const prev = { a: { b: 1 }, c: 2 };
    const next = { a: { b: 1 }, c: 2 };
    expect(isObjectShallowModified(prev, next)).toBe(false);

    const next2 = { a: { b: 2 }, c: 2 };
    expect(isObjectShallowModified(prev, next2)).toBe(true);
  });

  test('对象属性顺序不同比较', () => {
    const prev = { a: 1, b: 2 };
    const next = { b: 2, a: 1 };
    expect(isObjectShallowModified(prev, next)).toBe(false);
  });

  test('嵌套对象数组比较', () => {
    const prev = [
      {id: 1, data: {value: 1}},
      {id: 2, data: {value: 2}}
    ];
    const next = [
      {id: 1, data: {value: 1}},
      {id: 2, data: {value: 2}}
    ];
    expect(isObjectShallowModified(prev, next)).toBe(false);

    const next2 = [
      {id: 1, data: {value: 1}},
      {id: 2, data: {value: 3}}
    ];
    expect(isObjectShallowModified(prev, next2)).toBe(true);

    const next3 = [
      {id: 1, data: {value: 1}},
      {id: 2, data: {value: '2'}}
    ];
    expect(isObjectShallowModified(prev, next3, true)).toBe(true);
    expect(isObjectShallowModified(prev, next3, false)).toBe(false);
  });

  test('嵌套对象数组引用比较', () => {
    const data = {value: 1};
    const prev = [
      {id: 1, data},
      {id: 2, data: {value: 2}}
    ];
    const next = [
      {id: 1, data},
      {id: 2, data: {value: 2}}
    ];
    expect(isObjectShallowModified(prev, next)).toBe(false);

    const next2 = [
      {id: 1, data: {...data}},
      {id: 2, data: {value: 2}}
    ];
    expect(isObjectShallowModified(prev, next2)).toBe(false);
  });

  test('对象属性顺序不同比较', () => {
    const prev = { a: 1, b: 2 };
    const next = { b: 2, a: 1 };
    expect(isObjectShallowModified(prev, next)).toBe(false);
  });

  test('复用对象&循环引用比较', () => {
    const o = { c: 1 };
    const o1 = {
      a: o,
      d: {
        b: o,
      },
    };
    const o2 = {
      a: {
        c: 1
      },
      d: {
        b: o,
      },
    };
    expect(isObjectShallowModified(o1, o2)).toBe(false);

    const o3 = {
      a: {
        b: o,
      },
      d: o,
    };
    const o4 = {
      a: {
        b: o,
      },
      d: {
        c: 1
      },
    };
    expect(isObjectShallowModified(o3, o4)).toBe(false);

    let o5: any = {
      a: {
        c: 1
      }
    }
    o5.b = o5;
    let o6: any = {
      a: o
    }
    o6.b = o6;
    expect(isObjectShallowModified(o5, o6)).toBe(false);

    const oa = { c: 1 }
    const o7 = {
      d: o,
      b: oa,
      a: o,
    };
    const o8 = {
      d: { c: 1 },
      b: { c: 1 },
      a: oa,
    };
    expect(isObjectShallowModified(o7, o8)).toBe(false);
  });
});

describe('isSuperDataModified', () => {
  // 模拟FormStore
  const createFormStore = (items: Array<{name: string}>, data: any = {}) => ({
    storeType: 'FormStore',
    items,
    data
  });

  // 模拟普通Store
  const createNormalStore = (data: any = {}) => ({
    storeType: 'ServiceStore',
    data
  });

  describe('FormStore场景', () => {
    test('表单字段发生变化时应返回true', () => {
      const store = createFormStore([
        { name: 'userName' },
        { name: 'userAge' }
      ], { userName: 'John', userAge: 25, localState: 'xxx' });

      const currentData = { userName: 'Jane', userAge: 25, otherField: 'changed' };
      const prevData = { userName: 'John', userAge: 25, otherField: 'original' };

      expect(isSuperDataModified(currentData, prevData, store as any)).toBe(true);
    });

    test('表单字段未发生变化时应返回false', () => {
      const store = createFormStore([
        { name: 'userName' },
        { name: 'userAge' }
      ], { userName: 'John', userAge: 25 });

      const currentData = { userName: 'John', userAge: 25, otherField: 'changed' };
      const prevData = { userName: 'John', userAge: 25, otherField: 'original' };

      expect(isSuperDataModified(currentData, prevData, store as any)).toBe(false);
    });

    test('嵌套字段名应只检查顶级字段', () => {
      const store = createFormStore([
        { name: 'user.name' },
        { name: 'user.age' },
        { name: 'profile.avatar' }
      ], { user: { name: 'John', age: 25 }, profile: { avatar: 'url' } });

      // user字段发生变化
      const currentData = {
        user: { name: 'Jane', age: 25 },
        profile: { avatar: 'url' },
        other: 'changed'
      };
      const prevData = {
        user: { name: 'John', age: 25 },
        profile: { avatar: 'url' },
        other: 'original'
      };

      expect(isSuperDataModified(currentData, prevData, store as any)).toBe(true);
    });

    test('只有非表单字段变化时应返回false', () => {
      const store = createFormStore([
        { name: 'userName' }
      ], { userName: 'John', localState: 'xxx' });

      const currentData = { userName: 'John', otherField: 'changed', anotherField: 'new' };
      const prevData = { userName: 'John', otherField: 'original', anotherField: 'old' };

      expect(isSuperDataModified(currentData, prevData, store as any)).toBe(false);
    });

    test('空表单项时应回退检查store.data中的字段', () => {
      // 当FormStore的items为空数组时，函数应该回退到检查store.data中的字段
      const store = createFormStore([], { field1: 'value1', field2: 'value2' });

      // field1发生变化，应该被检测到
      const currentData = { field1: 'newValue', field2: 'value2', field3: 'ignored' };
      const prevData = { field1: 'value1', field2: 'value2', field3: 'original' };

      expect(isSuperDataModified(currentData, prevData, store as any)).toBe(true);

      // 只有非store.data字段变化时，应该返回false
      const currentData2 = { field1: 'value1', field2: 'value2', field3: 'changed' };
      const prevData2 = { field1: 'value1', field2: 'value2', field3: 'original' };

      expect(isSuperDataModified(currentData2, prevData2, store as any)).toBe(false);
    });

    test('表单项和store.data字段去重检查', () => {
      const store = createFormStore([
        { name: 'userName' },
        { name: 'userAge' }
      ], { userName: 'John', userAge: 25, extraField: 'extra' });

      const currentData = { userName: 'John', userAge: 25, extraField: 'changed', otherField: 'new' };
      const prevData = { userName: 'John', userAge: 25, extraField: 'extra', otherField: 'old' };

      expect(isSuperDataModified(currentData, prevData, store as any)).toBe(true);
    });
  });

  describe('普通Store场景', () => {
    test('store.data中的字段发生变化时应返回true', () => {
      const store = createNormalStore({ field1: 'value1', field2: 'value2' });

      const currentData = { field1: 'newValue', field2: 'value2', field3: 'ignored' };
      const prevData = { field1: 'value1', field2: 'value2', field3: 'original' };

      expect(isSuperDataModified(currentData, prevData, store as any)).toBe(true);
    });

    test('store.data中的字段未发生变化时应返回false', () => {
      const store = createNormalStore({ field1: 'value1', field2: 'value2' });

      const currentData = { field1: 'value1', field2: 'value2', field3: 'changed' };
      const prevData = { field1: 'value1', field2: 'value2', field3: 'original' };

      expect(isSuperDataModified(currentData, prevData, store as any)).toBe(false);
    });

    test('只有非store字段变化时应返回false', () => {
      const store = createNormalStore({ importantField: 'value' });

      const currentData = { importantField: 'value', otherField: 'changed' };
      const prevData = { importantField: 'value', otherField: 'original' };

      expect(isSuperDataModified(currentData, prevData, store as any)).toBe(false);
    });

    test('空store.data时应返回false', () => {
      const store = createNormalStore({});

      const currentData = { field1: 'newValue', field2: 'value2' };
      const prevData = { field1: 'value1', field2: 'value2' };

      expect(isSuperDataModified(currentData, prevData, store as any)).toBe(false);
    });
  });

  describe('边界情况', () => {
    test('当superObject为null时不应该同步', () => {
      const store = createNormalStore();
      const currentData = { field1: 'value1' };
      const superData = null;
      const prevSuperData = { field1: 'oldValue' };

      // 当superObject为null时，会抛出错误，这是当前实现的行为
      expect(() => {
        syncDataFromSuper(currentData, superData, prevSuperData, store as any, true);
      }).toThrow();
    });

    test('当prevSuperObject为null时应该同步superObject中的值', () => {
      const store = createNormalStore();
      const currentData = { field1: 'value1' };
      const superData = { field1: 'newValue' };
      const prevSuperData = null;

      const result = syncDataFromSuper(currentData, superData, prevSuperData, store as any, true);

      expect(result).toEqual({
        field1: 'newValue' // 会被同步，因为prevSuperObject为null时条件满足
      });
    });

    test('当字段名为空字符串时应该跳过', () => {
      const store = createFormStore([
        { name: '' },
        { name: 'validField' }
      ]);

      const currentData = { '': 'emptyKey', validField: 'value' };
      const superData = { '': 'newEmptyKey', validField: 'newValue' };
      const prevSuperData = { '': 'emptyKey', validField: 'value' };

      const result = syncDataFromSuper(currentData, superData, prevSuperData, store as any, false);

      expect(result).toEqual({
        '': 'emptyKey',        // 空字符串字段被跳过
        validField: 'newValue' // 有效字段被同步
      });
    });

    test('当store为null时应该正常工作', () => {
      const currentData = { field1: 'value1' };
      const superData = { field1: 'newValue' };
      const prevSuperData = { field1: 'value1' };

      const result = syncDataFromSuper(currentData, superData, prevSuperData, null as any, false);

      expect(result).toEqual({
        field1: 'value1' // 保持不变，因为没有store且force=false
      });
    });

    it('当父级数据没有变化但子级数据与父级不同时不应该同步', () => {
      const store = { storeType: 'ServiceStore' };
      const currentData = { field1: 'childValue', field2: 'value2' };
      const superData = { field1: 'parentValue', field2: 'value2' };
      const prevSuperData = { field1: 'parentValue', field2: 'value2' }; // 父级数据没有变化

      const result = syncDataFromSuper(currentData, superData, prevSuperData, store, true);

      // 因为父级数据没有变化（prevSuperData[field1] === superData[field1]），
      // 所以即使子级数据与父级不同，也不应该同步
      expect(result).toEqual({ field1: 'childValue', field2: 'value2' });
    });

    it('当父级数据发生变化且子级数据与父级不同时应该同步', () => {
      const store = { storeType: 'ServiceStore' };
      const currentData = { field1: 'childValue', field2: 'value2' };
      const superData = { field1: 'newParentValue', field2: 'value2' };
      const prevSuperData = { field1: 'oldParentValue', field2: 'value2' }; // 父级数据发生了变化

      const result = syncDataFromSuper(currentData, superData, prevSuperData, store, true);

      // 因为父级数据发生了变化（prevSuperData[field1] !== superData[field1]），
      // 所以应该同步父级的新值
      expect(result).toEqual({ field1: 'newParentValue', field2: 'value2' });
    });
  });

  describe('性能相关', () => {
    test('大量字段时应只检查相关字段', () => {
      const formItems = Array.from({ length: 10 }, (_, i) => ({ name: `field${i}` }));
      const storeData = Object.fromEntries(formItems.map((item, i) => [item.name, `value${i}`]));
      const store = createFormStore(formItems, storeData);

      // 创建包含1000个字段的数据对象
      const createLargeData = (changedField?: string) => {
        const data: any = {};
        for (let i = 0; i < 1000; i++) {
          data[`extraField${i}`] = `extraValue${i}`;
        }
        formItems.forEach((item, i) => {
          data[item.name] = changedField === item.name ? 'changed' : `value${i}`;
        });
        return data;
      };

      const currentData = createLargeData('field5');
      const prevData = createLargeData();

      expect(isSuperDataModified(currentData, prevData, store as any)).toBe(true);
    });
  });
});

describe('syncDataFromSuper', () => {
  // 模拟FormStore
  const createFormStore = (items: Array<{name: string}>) => ({
    storeType: 'FormStore',
    items
  });

  // 模拟普通Store
  const createNormalStore = () => ({
    storeType: 'ServiceStore'
  });

  describe('FormStore场景', () => {
    test('应该只同步表单项相关字段', () => {
      const store = createFormStore([
        { name: 'userName' },
        { name: 'userAge' }
      ]);

      const currentData = { userName: 'John', userAge: 25, localState: 'local' };
      const superData = { userName: 'Jane', userAge: 30, globalState: 'global' };
      const prevSuperData = { userName: 'John', userAge: 25, globalState: 'global' };

      const result = syncDataFromSuper(currentData, superData, prevSuperData, store as any, false);

      expect(result).toEqual({
        userName: 'Jane',  // 同步了变化
        userAge: 30,       // 同步了变化
        localState: 'local' // 保持不变
      });
    });

    test('应该同步表单项字段和当前数据中的字段', () => {
      const store = createFormStore([
        { name: 'userName' }
      ]);

      const currentData = { userName: 'John', extraField: 'extra' };
      const superData = { userName: 'Jane', extraField: 'newExtra', otherField: 'other' };
      const prevSuperData = { userName: 'John', extraField: 'extra', otherField: 'other' };

      const result = syncDataFromSuper(currentData, superData, prevSuperData, store as any, false);

      expect(result).toEqual({
        userName: 'Jane',      // 表单项字段，同步了变化
        extraField: 'newExtra' // 当前数据中的字段，同步了变化
      });
    });

    test('应该处理复杂字段名（去掉点号后缀）', () => {
      const store = createFormStore([
        { name: 'user.name' },
        { name: 'user.profile.age' }
      ]);

      const currentData = { user: { name: 'John', age: 25 } };
      const superData = { user: { name: 'Jane', age: 30 } };
      const prevSuperData = { user: { name: 'John', age: 25 } };

      const result = syncDataFromSuper(currentData, superData, prevSuperData, store as any, false);

      expect(result).toEqual({
        user: { name: 'Jane', age: 30 } // user字段被同步
      });
    });
  });

  describe('普通Store场景', () => {
    test('force=false时不应该同步任何字段', () => {
      const store = createNormalStore();
      const currentData = { field1: 'value1', field2: 'value2' };
      const superData = { field1: 'newValue1', field3: 'value3' };
      const prevSuperData = { field1: 'value1', field3: 'oldValue3' };

      const result = syncDataFromSuper(currentData, superData, prevSuperData, store as any, false);

      expect(result).toEqual({
        field1: 'value1',  // 保持不变
        field2: 'value2'   // 保持不变
      });
    });

    test('force=true时应该同步当前数据中存在的字段', () => {
      const store = createNormalStore();
      const currentData = { field1: 'value1', field2: 'value2' };
      const superData = { field1: 'newValue1', field3: 'value3' };
      const prevSuperData = { field1: 'value1', field3: 'oldValue3' };

      const result = syncDataFromSuper(currentData, superData, prevSuperData, store as any, true);

      expect(result).toEqual({
        field1: 'newValue1', // 被同步
        field2: 'value2'     // 保持不变（父级数据中没有）
      });
    });
  });

  describe('数据变化检测', () => {
    test('只有发生变化的字段才会被同步', () => {
      const store = createNormalStore();
      const currentData = { a: 1, b: 2, c: 3 };
      const superData = { a: 1, b: 3, c: 3 }; // 只有b发生了变化
      const prevSuperData = { a: 1, b: 2, c: 3 };

      const result = syncDataFromSuper(currentData, superData, prevSuperData, store as any, true);

      expect(result).toEqual({
        a: 1, // 未变化，保持原值
        b: 3, // 发生变化，被同步
        c: 3  // 未变化，保持原值
      });
    });

    test('当父级数据从有值变为undefined时应该同步', () => {
      const store = createNormalStore();
      const currentData = { field1: 'value1' };
      const superData = { field1: undefined };
      const prevSuperData = { field1: 'value1' };

      const result = syncDataFromSuper(currentData, superData, prevSuperData, store as any, true);

      expect(result).toEqual({
        field1: undefined // 被同步为undefined
      });
    });

    test('当父级数据从undefined变为有值时应该同步', () => {
      const store = createNormalStore();
      const currentData = { field1: 'value1' };
      const superData = { field1: 'newValue' };
      const prevSuperData = { field1: undefined };

      const result = syncDataFromSuper(currentData, superData, prevSuperData, store as any, true);

      expect(result).toEqual({
        field1: 'newValue' // 被同步
      });
    });
  });

  describe('边界情况', () => {
    test('当superObject为null时不应该同步', () => {
      const store = createNormalStore();
      const currentData = { field1: 'value1' };
      const superData = null;
      const prevSuperData = { field1: 'oldValue' };

      // 当superObject为null时，会抛出错误，这是当前实现的行为
      expect(() => {
        syncDataFromSuper(currentData, superData, prevSuperData, store as any, true);
      }).toThrow();
    });

    test('当prevSuperObject为null时应该同步superObject中的值', () => {
      const store = createNormalStore();
      const currentData = { field1: 'value1' };
      const superData = { field1: 'newValue' };
      const prevSuperData = null;

      const result = syncDataFromSuper(currentData, superData, prevSuperData, store as any, true);

      expect(result).toEqual({
        field1: 'newValue' // 会被同步，因为prevSuperObject为null时条件满足
      });
    });

    test('当字段名为空字符串时应该跳过', () => {
      const store = createFormStore([
        { name: '' },
        { name: 'validField' }
      ]);

      const currentData = { '': 'emptyKey', validField: 'value' };
      const superData = { '': 'newEmptyKey', validField: 'newValue' };
      const prevSuperData = { '': 'emptyKey', validField: 'value' };

      const result = syncDataFromSuper(currentData, superData, prevSuperData, store as any, false);

      expect(result).toEqual({
        '': 'emptyKey',        // 空字符串字段被跳过
        validField: 'newValue' // 有效字段被同步
      });
    });

    test('当store为null时应该正常工作', () => {
      const currentData = { field1: 'value1' };
      const superData = { field1: 'newValue' };
      const prevSuperData = { field1: 'value1' };

      const result = syncDataFromSuper(currentData, superData, prevSuperData, null as any, false);

      expect(result).toEqual({
        field1: 'value1' // 保持不变，因为没有store且force=false
      });
    });
  });

  describe('数据不可变性', () => {
    test('应该返回新的数据对象，不修改原始数据', () => {
      const store = createNormalStore();
      const currentData = { field1: 'value1' };
      const superData = { field1: 'newValue' };
      const prevSuperData = { field1: 'value1' };

      const result = syncDataFromSuper(currentData, superData, prevSuperData, store as any, true);

      expect(result).not.toBe(currentData); // 返回新对象
      expect(currentData).toEqual({ field1: 'value1' }); // 原始数据未被修改
    });
  });
});
