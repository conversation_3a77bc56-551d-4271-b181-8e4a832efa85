import { stubTrue } from 'lodash';
import {isPureVariable} from '../src/utils/tpl-builtin';

describe('isPureVariable', () => {
  test('纯变量表达式', () => {
    expect(isPureVariable('$foo.bar')).toBe(true);
    expect(isPureVariable('$my_variable')).toBe(true);
    expect(isPureVariable('$item2')).toBe(true);
    expect(isPureVariable('${user.name}')).toBe(true);
    expect(isPureVariable('${foo | raw}')).toBe(true);
    expect(isPureVariable('${foo()}')).toBe(true);
    expect(isPureVariable('${items[0]}')).toBe(true);
    expect(isPureVariable('${a > b}')).toBe(true);
    expect(isPureVariable('${ARRAYMAP(objArr, item => {id: item.id})}')).toBe(true);
  });

  test('非纯变量表达式', () => {
    expect(isPureVariable('user')).toBe(false);
    expect(isPureVariable('$(foo + bar)')).toBe(false);
    expect(isPureVariable('${user?.name}')).toBe(false);
    expect(isPureVariable('$items[0]')).toBe(false);
    expect(isPureVariable('${user.profile?.name}')).toBe(false);
    expect(isPureVariable('name: $items')).toBe(false);
  });

  test('边界条件处理', () => {
    // 测试非字符串输入
    expect(isPureVariable(undefined)).toBe(false);
    expect(isPureVariable(null)).toBe(false);
    expect(isPureVariable(123)).toBe(false);
    expect(isPureVariable({})).toBe(false);

    // 测试空字符串和特殊字符
    expect(isPureVariable('')).toBe(false);
    expect(isPureVariable(' ')).toBe(false);
    expect(isPureVariable('\n')).toBe(false);
  });

  test('特殊场景', () => {
    // 测试无效的变量表达式
    expect(isPureVariable('$1abc')).toBe(true);
    expect(isPureVariable('$foo.')).toBe(false);
    expect(isPureVariable('$.foo')).toBe(false);
  });
});
