import { cloneObject,createObject,createObjectFromChain,deleteVariable,extendObject,extractObjectChain,getPrototypeProperties,injectObjectChain,isObject,pickValues,setVariable } from '../src/utils/object';

describe('extendObject', () => {
  test('基本对象扩展', () => {
    const target = {a: 1, b: 2};
    const src = {c: 3, d: 4};
    const result = extendObject(target, src);

    expect(result).toEqual({a: 1, b: 2, c: 3, d: 4});
    // 确保返回的是新对象
    expect(result).not.toBe(target);
    expect(result).not.toBe(src);
  });

  test('不保留目标对象属性', () => {
    const target = {a: 1, b: 2};
    const src = {c: 3};
    const result = extendObject(target, src, false);

    expect(result).toEqual({c: 3});
    expect(result).not.toHaveProperty('a');
    expect(result).not.toHaveProperty('b');
  });

  test('原型链继承对象处理', () => {
    const proto = {inheritedProp: 'value'};
    const target = Object.create(proto);
    target.ownProp = 'own';
    const src = {newProp: 'new'};
    const result = extendObject(target, src);

    // 只保留自身属性和新添加的属性
    expect(result.ownProp).toBe('own');
    expect(result.newProp).toBe('new');
    // 原型链上的属性不会被保留
    expect(result.inheritedProp).toBeUndefined();
    expect(Object.getPrototypeOf(result)).toBe(Object.prototype);
  });

  test('处理空值和边界条件', () => {
    // 空源对象
    const result1 = extendObject({a: 1});
    expect(result1).toEqual({a: 1});

    // 空目标对象
    const result2 = extendObject({}, {b: 2});
    expect(result2).toEqual({b: 2});

    // 都为空对象
    const result3 = extendObject({}, {});
    expect(result3).toEqual({});
  });

  test('属性覆盖', () => {
    const target = {a: 1, b: 2};
    const src = {b: 3, c: 4};
    const result = extendObject(target, src);

    expect(result).toEqual({a: 1, b: 3, c: 4});
  });
});

describe('setVariable', () => {

  test('基本键值设置', () => {
    const obj = {};
    setVariable(obj, 'foo', 'bar');
    expect(obj).toEqual({foo: 'bar'});

    setVariable(obj, 'num', 123);
    expect(obj).toEqual({foo: 'bar', num: 123});
  });

  test('嵌套对象设置', () => {
    const data = {};
    setVariable(data, 'a.b.c', 123);
    expect(data).toEqual({a: {b: {c: 123}}});

    setVariable(data, 'x.y', 'value');
    expect(data).toEqual({a: {b: {c: 123}}, x: {y: 'value'}});
  });

  test('数组处理', () => {
    const arr = {list: [1, 2, 3]};
    setVariable(arr, 'list.0', 100);
    expect(arr).toEqual({list: [100, 2, 3]});

    setVariable(arr, 'list.3', 4);
    expect(arr).toEqual({list: [100, 2, 3, 4]});
  });

  test('不转换键路径', () => {
    const raw = {};
    setVariable(raw, 'a.b', 'value', false);
    expect(raw).toEqual({'a.b': 'value'});
  });

  test('空值和边界条件处理', () => {
    const obj = null;
    setVariable(obj as any, 'key', 'value');
    expect(obj).toBe(null);

    const emptyObj = {};
    setVariable(emptyObj, '', 'value');
    expect(emptyObj).toEqual({'undefined': 'value'});

    setVariable(emptyObj, undefined as any, 'value');
    expect(emptyObj).toEqual({'undefined': 'value'});
  });

  test('非纯对象转换', () => {
    // Date实例
    const dateObj = new Date();
    setVariable(dateObj, 'a.b', 'new');
    expect((dateObj as any).a.b).toEqual('new');

    // 数组
    const arr = [1, 2, 3];
    setVariable(arr, 'a.b', 'new');
    expect((arr as any).a.b).toEqual('new');

    // 自定义类实例
    class CustomClass {
      value: string;
      constructor() {
        this.value = 'original';
      }
    }
    const instance = new CustomClass();
    setVariable(instance, 'a.b', 'new');
    expect((instance as any).a.b).toBe('new');

    const data = {
      str: 'string value',
      num: 123,
      bool: true
    };

    // 测试字符串值被强制转换为对象
    setVariable(data, 'str.nested', 'new value');
    expect((data.str as any).nested).toBe('new value');

    // 测试数字值被强制转换为对象
    setVariable(data, 'num.nested', 'new value');
    expect((data.num as any).nested).toBe('new value');

    // 测试布尔值被强制转换为对象
    setVariable(data, 'bool.nested', 'new value');
    expect((data.bool as any).nested).toBe('new value');
  });

  test('已存在键的更新', () => {
    const data = {key: 'old'};
    setVariable(data, 'key', 'new');
    expect(data).toEqual({key: 'new'});

    const nested = {a: {b: 'old'}};
    setVariable(nested, 'a.b', 'new');
    expect(nested).toEqual({a: {b: 'new'}});
  });
});

describe('isObject', () => {
  test('对象类型判断', () => {
    // 普通对象
    expect(isObject({})).toBe(true);
    expect(isObject({key: 'value'})).toBe(true);

    // 内置对象实例
    expect(isObject(new Date())).toBe(true);
    expect(isObject(new RegExp('test'))).toBe(true);

    // 自定义类实例
    class TestClass {}
    expect(isObject(new TestClass())).toBe(true);
  });

  test('非对象类型判断', () => {
    // 原始类型
    // 注意：由于实现中使用了obj作为条件判断，0、null和undefined会直接返回自身而不是false
    // 这是一个已知的行为，使用时需要注意这些特殊值的处理
    expect(isObject('string')).toBe(false);
    expect(isObject(123)).toBe(false);
    expect(isObject(0)).toBe(0);
    expect(isObject(true)).toBe(false);
    expect(isObject(null)).toBe(null);
    expect(isObject(undefined)).toBe(undefined);

    // 函数
    expect(isObject(() => {})).toBe(false);
    expect(isObject(function() {})).toBe(false);

    // 数组
    expect(isObject([])).toBe(false);
    expect(isObject([1, 2, 3])).toBe(false);
  });
});

describe('pickValues', () => {
  test('基本键值提取', () => {
    const data = {name: '张三'};
    expect(pickValues('name', data)).toBe('张三');
  });

  test('特殊符号键处理', () => {
    const data = {'user-name': '李四'};
    expect(pickValues('user~user-name', data)).toEqual({user: '李四'});
  });

  test('多值提取', () => {
    const data = {name: '王五', age: 25};
    expect(pickValues('name,age', data)).toEqual({name: '王五', age: 25});
  });

  test('多值带别名', () => {
    const data = {name: '赵六', age: 30};
    expect(pickValues('userName~name,userAge~age', data)).toEqual({
      userName: '赵六',
      userAge: 30
    });
  });

  test('空值处理', () => {
    expect(pickValues('', {})).toBe(undefined);
  });
});

describe('deleteVariable', () => {
  test('基本键值删除', () => {
    const obj = {foo: 'bar', num: 123};
    deleteVariable(obj, 'foo');
    expect(obj).toEqual({num: 123});

    deleteVariable(obj, 'num');
    expect(obj).toEqual({});
  });

  test('嵌套对象删除', () => {
    const data = {a: {b: {c: 123}}, x: {y: 'value'}};
    deleteVariable(data, 'a.b.c');
    expect(data).toEqual({a: {b: {}}, x: {y: 'value'}});

    deleteVariable(data, 'x.y');
    expect(data).toEqual({a: {b: {}}, x: {}});
  });

  test('数组操作测试', () => {
    // 删除数组中间元素
    const arr1 = [1, 2, 3, 4, 5];
    deleteVariable(arr1, '2');
    expect(arr1).toEqual([1, 2, undefined, 4, 5]);

    // 删除数组最后一个元素
    const arr2 = [1, 2, 3];
    deleteVariable(arr2, '2');
    expect(arr2).toEqual([1, 2, undefined]);

    // 删除数组对象的属性
    const arr3 = [{id: 1, name: 'test'}, {id: 2, name: 'test2'}];
    deleteVariable(arr3, '0.name');
    expect(arr3).toEqual([{id: 1}, {id: 2, name: 'test2'}]);

    // 删除不存在的数组索引
    const arr5 = [1, 2, 3];
    deleteVariable(arr5, '5');
    expect(arr5).toEqual([1, 2, 3]);
  });

  test('空值和边界条件处理', () => {
    const obj = null;
    deleteVariable(obj as any, 'key');
    expect(obj).toBe(null);

    const emptyObj = {};
    deleteVariable(emptyObj, '');
    expect(emptyObj).toEqual({});

    expect(() => deleteVariable(emptyObj, undefined as any)).toThrow();

    // 删除不存在的路径
    const data = {a: {b: 1}};
    deleteVariable(data, 'x.y.z');
    expect(data).toEqual({a: {b: 1}});
  });

  test('非纯对象处理', () => {
    // Date实例
    const dateObj = new Date();
    expect(() => deleteVariable(dateObj as any, 'a.b')).not.toThrow('目标路径不是纯对象，不能修改');

    // 删除嵌套数组元素
    const arr4 = {data: [[1, 2], [3, 4]]};
    expect(() => deleteVariable(arr4, 'data.0.1')).toThrow('目标路径不是纯对象，不能修改');

    // 自定义类实例
    class CustomClass {
      value: string;
      constructor() {
        this.value = 'original';
      }
    }
    const instance = new CustomClass();
    deleteVariable(instance, 'value')
    expect(instance.value).toEqual(undefined);
  });
});

describe('extractObjectChain', () => {
  test('基本对象提取', () => {
    const obj = {prop: 'value'};
    const chain = extractObjectChain(obj);
    expect(chain).toEqual([obj]);
  });

  test('__super对象提取', () => {
    const proto = {parentProp: 'parent'};
    const obj = Object.create(proto, {
      __super: {
        value: proto,
        writable: false,
        enumerable: false
      }
    });
    obj.childProp = 'child';

    const chain = extractObjectChain(obj);
    expect(chain).toHaveLength(2);
    expect(chain[0]).toBe(proto);
    expect(chain[1]).toBe(obj);
    expect((chain[0] as any).parentProp).toBe('parent');
    expect((chain[1] as any).childProp).toBe('child');
  });

  test('多层__super链提取', () => {
    const grandParent = {grandProp: 'grand'};
    const parent = Object.create(grandParent, {
      __super: {
        value: grandParent,
        writable: false,
        enumerable: false
      }
    });
    parent.parentProp = 'parent';
    const child = Object.create(parent, {
      __super: {
        value: parent,
        writable: false,
        enumerable: false
      }
    });
    child.childProp = 'child';

    const chain = extractObjectChain(child);
    expect(chain).toHaveLength(3);
    expect(chain[0]).toBe(grandParent);
    expect(chain[1]).toBe(parent);
    expect(chain[2]).toBe(child);
  });

  test('边界条件处理', () => {
    // null值
    expect(extractObjectChain(null)).toEqual([]);

    // undefined值
    expect(extractObjectChain(undefined)).toEqual([]);

    // 普通对象（无__super）
    const plainObj = {prop: 'value'};
    expect(extractObjectChain(plainObj)).toEqual([plainObj]);
  });
});

describe('getPrototypeProperties', () => {
  test('基本对象测试', () => {
    const obj = {a: 1};
    const result = getPrototypeProperties(obj);
    expect(result.a).toBe(1);
    expect(typeof result.toString).toBe('function');
  });

  test('原型链对象测试', () => {
    class Parent {
      parentProp: string;
      parentMethod: () => void;
      constructor() {
        this.parentProp = 'parent';
      }
    }
    Parent.prototype.parentMethod = function () {};
    function Child(this: {childProp: string}) {
      this.childProp = 'child';
    }
    Child.prototype = new Parent();

    const child = new (Child as any)();
    const result = getPrototypeProperties(child);

    expect(result.childProp).toBe('child');
    expect(result.parentProp).toBe('parent');
    expect(typeof result.parentMethod).toBe('function');
    expect(typeof result.toString).toBe('function');
  });

  test('边界条件测试', () => {
    // null/undefined输入
    expect(getPrototypeProperties(null)).toEqual({});
    expect(() => getPrototypeProperties(undefined)).toThrow();

    // 原始类型
    expect(getPrototypeProperties(123)).toEqual({});
    expect(getPrototypeProperties('string')[0]).toBe('s');

    // 数组
    const arr = [1, 2, 3];
    const arrResult = getPrototypeProperties(arr);
    expect(arrResult[0]).toBe(1);
    expect(typeof arrResult.push).toBe('undefined');
  });
});

describe('createObjectFromChain', () => {
  test('基本对象链创建', () => {
    const chain = [{parentProp: 'parent'}, {childProp: 'child'}];
    const result = createObjectFromChain(chain) as any;

    expect(result.parentProp).toBe('parent');
    expect(result.childProp).toBe('child');
    expect(Object.getPrototypeOf(result)).toBe(chain[0]);
    // 确保返回的是新对象
    expect(result).not.toBe(chain[1]);
  });

  test('冻结对象处理', () => {
    const frozenParent = Object.freeze({frozen: true});
    const chain = [frozenParent, {child: 'value'}];
    const result = createObjectFromChain(chain) as any;

    expect(result.frozen).toBe(true);
    expect(result.child).toBe('value');
    expect(Object.isFrozen(result.__super)).toBe(false);
    // 验证可以修改克隆后的对象
    result.__super.newProp = 'test';
    expect(result.__super.newProp).toBe('test');
  });

  test('空值过滤', () => {
    const chain = [{a: 1}, null, undefined, {b: 2}];
    const result = createObjectFromChain(chain as any) as any;

    expect(result.a).toBe(1);
    expect(result.b).toBe(2);
    expect(Object.getPrototypeOf(result)).toBe(chain[0]);
  });

  test('单个对象处理', () => {
    const obj = {prop: 'value'};
    const result = createObjectFromChain([obj]) as any;

    expect(result.prop).toBe('value');
    expect(result).toBe(obj);
    expect(Object.getPrototypeOf(result)).toBe(Object.prototype);
  });

  test('空数组处理', () => {
    expect(() => createObjectFromChain([])).toThrow();
  });
});


describe('createObject', () => {
  test('基本对象创建', () => {
    const superProps = {a: 1};
    const props = {b: 2};
    const result = createObject(superProps, props);

    expect((result as any).a).toBe(1);
    expect(result.hasOwnProperty('a')).toBe(false);
    expect((result as any).b).toBe(2);
    expect(result.hasOwnProperty('b')).toBe(true);
    // 确保返回的是新对象
    expect(result).not.toBe(superProps);
    expect(result).not.toBe(props);
  });

  test('原型链继承对象处理', () => {
    const superProps = {inheritedProp: 'value'};
    const props = {ownProp: 'own'};
    const result = createObject(superProps, props) as any;

    expect(result.ownProp).toBe('own');
    expect(result.inheritedProp).toBe('value');
    expect(Object.getPrototypeOf(result)).toBe(superProps);
  });

  test('属性描述符设置', () => {
    const properties = {
      readonlyProp: {
        value: 'readonly',
        writable: false,
        enumerable: true
      }
    };
    const result = createObject({}, {}, properties) as any;

    expect(result.readonlyProp).toBe('readonly');
    const descriptor = Object.getOwnPropertyDescriptor(result, 'readonlyProp');
    expect(descriptor?.writable).toBe(false);
    expect(descriptor?.enumerable).toBe(true);
  });

  test('空值和边界条件处理', () => {
    // 无参数
    const result1 = createObject();
    expect(Object.getPrototypeOf(result1)).toBe(Object.prototype);

    // 只有superProps
    const result2 = createObject({a: 1}) as any;
    expect(result2.a).toBe(1);

    // 只有props
    const result3 = createObject(undefined, {b: 2}) as any;
    expect(result3.b).toBe(2);
    expect(Object.getPrototypeOf(result3)).toBe(Object.prototype);
  });

  test('冻结对象处理', () => {
    const superProps = {a: 1};
    Object.freeze(superProps);
    const props = {b: 2};
    const result = createObject(superProps, props) as any;

    expect(result.a).toBe(1);
    expect(result.b).toBe(2);
    expect(Object.isFrozen(result)).toBe(false);
    // 验证superProps被克隆
    expect(Object.getPrototypeOf(result)).not.toBe(superProps);
  });

  test('__super属性设置', () => {
    const superProps = {parentProp: 'parent'};
    const result = createObject(superProps);

    // 验证__super属性设置正确
    const descriptor = Object.getOwnPropertyDescriptor(result, '__super');
    expect(descriptor?.value).toBe(superProps);
    expect(descriptor?.writable).toBe(false);
    expect(descriptor?.enumerable).toBe(false);
  });
});

describe('cloneObject', () => {
  test('基本对象克隆', () => {
    const target = {a: 1, b: 2};
    const result = cloneObject(target);

    expect(result).toEqual({a: 1, b: 2});
    // 确保返回的是新对象
    expect(result).not.toBe(target);
  });

  test('不保留目标对象属性', () => {
    const target = {a: 1, b: 2};
    const result = cloneObject(target, false);

    expect(result).toEqual({});
    expect(result).not.toHaveProperty('a');
    expect(result).not.toHaveProperty('b');
  });

  test('继承对象处理', () => {
    const proto = {inheritedProp: 'value'};
    const target = Object.create(proto);
    target.ownProp = 'own';
    const result = cloneObject(target);

    // 只保留自身属性
    expect(result.ownProp).toBe('own');
    // 原型链上的属性不会被保留
    expect(result.inheritedProp).toBeUndefined();
    expect(Object.getPrototypeOf(result)).toBe(Object.prototype);
  });

  test('普通对象属性克隆', () => {
    const target = {prop: 'value', data: {nested: 'test'}};
    const result = cloneObject(target);

    expect(result.prop).toBe('value');
    expect(result.data).toBe(target.data);
  });

  test('空值和边界条件处理', () => {
    // 空对象
    const result1 = cloneObject({});
    expect(result1).toEqual({});

    // null和undefined
    const result2 = cloneObject(null);
    expect(result2).toEqual({});

    const result3 = cloneObject(undefined);
    expect(result3).toEqual({});
  });

  describe('__super属性处理', () => {
    test('当目标对象有__super属性时，应创建以__super为原型的对象', () => {
      const superObj = { superProp: 'super' };
      const target = Object.create(null);
      Object.defineProperty(target, '__super', {
        value: superObj,
        writable: false,
        enumerable: false
      });
      target.ownProp = 'own';

      const cloned = cloneObject(target);

      // 验证__super属性设置正确
      expect(Object.getPrototypeOf(cloned)).toBe(superObj);
      // 验证__super属性描述符
      const descriptor = Object.getOwnPropertyDescriptor(cloned, '__super');
      expect(descriptor?.writable).toBe(false);
      expect(descriptor?.enumerable).toBe(false);
      // 验证自身属性被正确复制
      expect(cloned.ownProp).toBe('own');
    });

    test('当目标对象没有__super属性时，应使用Object.prototype', () => {
      const target = { prop: 'value' };
      const cloned = cloneObject(target);

      expect(Object.getPrototypeOf(cloned)).toBe(Object.prototype);
      expect(cloned.prop).toBe('value');
    });

    test('当persistOwnProps为false时，不应复制自身属性', () => {
      const superObj = { superProp: 'super' };
      const target = Object.create(null);
      Object.defineProperty(target, '__super', {
        value: superObj,
        writable: false,
        enumerable: false
      });
      target.ownProp = 'own';

      const cloned = cloneObject(target, false);

      expect(Object.getPrototypeOf(cloned)).toBe(superObj);
      expect(cloned.ownProp).toBeUndefined();
    });

    test('应正确处理带有__super的冻结对象', () => {
      const superObj = { superProp: 'super' };
      const target = Object.create(null);
      Object.defineProperty(target, '__super', {
        value: superObj,
        writable: false,
        enumerable: false
      });
      target.ownProp = 'own';
      Object.freeze(target);

      const cloned = cloneObject(target);

      expect(Object.getPrototypeOf(cloned)).toBe(superObj);
      expect(cloned.ownProp).toBe('own');
      expect(Object.isFrozen(cloned)).toBe(false);
    });
  });
});


describe('injectObjectChain', () => {
  test('基本对象注入', () => {
    const target = {prop: 'target'};
    const inject = {injectProp: 'inject'};
    const result = injectObjectChain(target, inject) as any;

    expect(result.prop).toBe('target');
    expect(result.injectProp).toBe('inject');
    expect(Object.getPrototypeOf(result)).toBe(inject);
  });

  test('多层__super链注入', () => {
    const grandParent = {grandProp: 'grand'};
    const parent = Object.create(null);
    Object.defineProperty(parent, '__super', {
      value: grandParent,
      writable: false,
      enumerable: false
    });
    parent.parentProp = 'parent';

    const inject = {injectProp: 'inject'};
    const result = injectObjectChain(parent, inject) as any;

    expect(result.parentProp).toBe('parent');
    expect(result.injectProp).toBe('inject');
    expect(Object.keys(Object.getPrototypeOf(result))).toEqual(Object.keys(inject))
    expect(Object.keys(Object.getPrototypeOf(Object.getPrototypeOf(result)))).toEqual(Object.keys(grandParent));
  });

  test('边界条件处理', () => {
    // null值处理
    const result1 = injectObjectChain(null, {prop: 'value'}) as any;
    expect(result1.prop).toBe('value');
    expect(Object.getPrototypeOf(result1)).toBe(Object.prototype);

    // 注入null或undefined
    const target = {prop: 'target'};
    const result3 = injectObjectChain(target, null) as any;
    expect(result3.prop).toBe('target');
    expect(Object.getPrototypeOf(result3)).toBe(Object.prototype);

    const result4 = injectObjectChain(target, undefined) as any;
    expect(result4.prop).toBe('target');
    expect(Object.getPrototypeOf(result4)).toBe(Object.prototype);
  });
});

