import {resolveEventData} from '../src/utils/renderer-event';

describe('resolveEventData', () => {
  test('基本数据合并', () => {
    const props = {
      data: {foo: 'bar'}
    };
    const data = {baz: 'qux'};
    const result = resolveEventData(props, data);
    expect(result).toEqual({
      baz: 'qux'
    });
  });

  test('name和valueKey参数处理', () => {
    const props = {
      data: {foo: 'bar'},
      name: 'field'
    };
    const data = {value: 'test'};
    const result = resolveEventData(props, data, 'value');
    expect(result).toEqual({
      value: 'test',
      field: 'test',
      __rendererData: {
        foo: 'bar',
        field: 'test'
      }
    });
  });

  test('空数据处理', () => {
    const props = {
      data: {}
    };
    const data = {};
    const result = resolveEventData(props, data);
    expect(result).toEqual({});
  });

  test('覆盖数据处理', () => {
    const props = {
      data: {foo: 'bar'},
      name: 'foo'
    };
    const data = {value: 'newBar'};
    const result = resolveEventData(props, data, 'value');
    expect(result).toEqual({
      foo: 'newBar',
      value: 'newBar',
      __rendererData: {
        foo: 'newBar'
      }
    });
  });

  test('复杂数据结构处理', () => {
    const props = {
      data: {
        nested: {
          foo: 'bar'
        }
      },
      name: 'field'
    };
    const data = {
      value: {
        test: 'complex'
      }
    };
    const result = resolveEventData(props, data, 'value');
    expect(result).toEqual({
      value: {
        test: 'complex'
      },
      field: {
        test: 'complex'
      },
      __rendererData: {
        nested: {
          foo: 'bar'
        },
        field: {
          test: 'complex'
        }
      }
    });
  });
});
