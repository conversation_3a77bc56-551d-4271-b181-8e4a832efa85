import {resolveEventData} from '../src/utils/renderer-event';

describe('resolveEventData', () => {
  test('基本数据合并', () => {
    const props = {
      data: {foo: 'bar'}
    };
    const data = {baz: 'qux'};
    const result = resolveEventData(props, data) as any;
    expect(result).toEqual({
      baz: 'qux'
    });
    // 验证原型链：应该能访问到 props.data 中的数据
    expect(result.foo).toBe('bar');
  });

  test('name和valueKey参数处理', () => {
    const props = {
      data: {foo: 'bar'},
      name: 'field'
    };
    const data = {value: 'test'};
    const result = resolveEventData(props, data, 'value') as any;
    expect(result).toEqual({
      value: 'test',
      field: 'test',
      __rendererData: {
        foo: 'bar',
        field: 'test'
      }
    });
    // 验证原型链
    expect(result.foo).toBe('bar');
  });

  test('只有name没有valueKey：不进行字段映射', () => {
    const props = {
      data: {foo: 'bar'},
      name: 'field'
    };
    const data = {value: 'test'};
    const result = resolveEventData(props, data) as any; // 不传 valueKey
    expect(result).toEqual({
      value: 'test'
    });
    // 验证原型链
    expect(result.foo).toBe('bar');
    // 验证没有字段映射
    expect(result.field).toBeUndefined();
    expect(result.__rendererData).toBeUndefined();
  });

  test('只有valueKey没有name：不进行字段映射', () => {
    const props = {
      data: {foo: 'bar'}
      // 没有 name 属性
    };
    const data = {value: 'test'};
    const result = resolveEventData(props, data, 'value') as any;
    expect(result).toEqual({
      value: 'test'
    });
    // 验证原型链
    expect(result.foo).toBe('bar');
    // 验证没有字段映射
    expect(result.__rendererData).toBeUndefined();
  });

  test('空数据处理', () => {
    const props = {
      data: {}
    };
    const data = {};
    const result = resolveEventData(props, data) as any;
    expect(result).toEqual({});
  });

  test('覆盖数据处理', () => {
    const props = {
      data: {foo: 'bar'},
      name: 'foo'
    };
    const data = {value: 'newBar'};
    const result = resolveEventData(props, data, 'value') as any;
    expect(result).toEqual({
      foo: 'newBar',
      value: 'newBar',
      __rendererData: {
        foo: 'newBar'
      }
    });
  });

  test('复杂数据结构处理', () => {
    const props = {
      data: {
        nested: {
          foo: 'bar'
        }
      },
      name: 'field'
    };
    const data = {
      value: {
        test: 'complex'
      }
    };
    const result = resolveEventData(props, data, 'value') as any;
    expect(result).toEqual({
      value: {
        test: 'complex'
      },
      field: {
        test: 'complex'
      },
      __rendererData: {
        nested: {
          foo: 'bar'
        },
        field: {
          test: 'complex'
        }
      }
    });
    // 验证原型链
    expect(result.nested).toEqual({foo: 'bar'});
  });

  test('valueKey不存在时的处理', () => {
    const props = {
      data: {foo: 'bar'},
      name: 'field'
    };
    const data = {other: 'test'};
    const result = resolveEventData(props, data, 'nonexistent') as any;
    expect(result).toEqual({
      other: 'test',
      field: undefined, // valueKey 不存在，所以映射的值为 undefined
      __rendererData: {
        foo: 'bar',
        field: undefined
      }
    });
  });

  test('多个事件数据字段处理', () => {
    const props = {
      data: {foo: 'bar'},
      name: 'selectedValue'
    };
    const data = {
      value: 'test',
      label: 'Test Label',
      extra: 'additional info'
    };
    const result = resolveEventData(props, data, 'value') as any;
    expect(result).toEqual({
      value: 'test',
      label: 'Test Label',
      extra: 'additional info',
      selectedValue: 'test',
      __rendererData: {
        foo: 'bar',
        selectedValue: 'test'
      }
    });
  });

  test('null和undefined值处理', () => {
    const props = {
      data: {foo: 'bar'},
      name: 'field'
    };

    // 测试 null 值
    const result1 = resolveEventData(props, {value: null}, 'value') as any;
    expect(result1.field).toBeNull();

    // 测试 undefined 值
    const result2 = resolveEventData(props, {value: undefined}, 'value') as any;
    expect(result2.field).toBeUndefined();
  });

  test('空字符串name处理', () => {
    const props = {
      data: {foo: 'bar'},
      name: '' // 空字符串
    };
    const data = {value: 'test'};
    const result = resolveEventData(props, data, 'value') as any;
    // 空字符串被认为是 falsy，所以不进行字段映射
    expect(result).toEqual({
      value: 'test'
    });
    expect(result.__rendererData).toBeUndefined();
  });

  test('数组数据处理', () => {
    const props = {
      data: {items: ['a', 'b']},
      name: 'selectedItems'
    };
    const data = {
      value: ['x', 'y', 'z']
    };
    const result = resolveEventData(props, data, 'value') as any;
    expect(result).toEqual({
      value: ['x', 'y', 'z'],
      selectedItems: ['x', 'y', 'z'],
      __rendererData: {
        items: ['a', 'b'],
        selectedItems: ['x', 'y', 'z']
      }
    });
  });

  test('原型链数据访问验证', () => {
    const props = {
      data: {
        level1: 'value1',
        nested: {
          level2: 'value2'
        }
      }
    };
    const data = {
      eventValue: 'test'
    };
    const result = resolveEventData(props, data) as any;

    // 验证可以访问原型链上的数据
    expect(result.level1).toBe('value1');
    expect(result.nested).toEqual({level2: 'value2'});
    expect(result.eventValue).toBe('test');

    // 验证 hasOwnProperty 的行为
    expect(result.hasOwnProperty('eventValue')).toBe(true);
    expect(result.hasOwnProperty('level1')).toBe(false); // 在原型链上
  });
});
