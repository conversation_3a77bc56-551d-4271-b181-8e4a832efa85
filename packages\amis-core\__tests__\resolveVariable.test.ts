import {resolveVariable} from '../src/utils/tpl-builtin';

describe('resolveVariable', () => {
  test('纯变量解析', () => {
    expect(resolveVariable('name', {name: 'World'})).toBe('World');
    expect(resolveVariable('$name', {name: 'World'})).toBe('World');
    expect(resolveVariable('${name}', {name: 'World'})).toBe(undefined);
    expect(resolveVariable('price', {price: 100})).toBe(100);
    expect(resolveVariable('obj.key', {obj: {key: 'value'}})).toBe('value');
    expect(resolveVariable('arr[0]', {arr: ['first']})).toBe('first');
  });

  test('嵌套对象处理', () => {
    expect(resolveVariable('user.name', {
      user: {name: '<PERSON>'},
      field: 'name'
    })).toBe('<PERSON>');

    expect(resolveVariable('users[0].name', {
      users: [{name: 'Alice'}]
    })).toBe('Alice');
  });

  test('空值处理', () => {
    expect(resolveVariable('missing', {})).toBe(undefined);
    expect(resolveVariable('missing.key', {missing: {}})).toBe(undefined);
    expect(resolveVariable('a', {a: ''})).toBe('');
    expect(resolveVariable('a', {a: null})).toBe(null);
    expect(resolveVariable('a', {a: undefined})).toBe(undefined);
  });

  test('边界条件处理', () => {
    expect(resolveVariable(undefined, {})).toBe(undefined);
    expect(resolveVariable('', {})).toBe(undefined);
    expect(resolveVariable('invalid.expression', {})).toBe(undefined);
    // 特殊路径处理
    expect(resolveVariable('&', {a:1})).toStrictEqual({a:1});
    expect(resolveVariable('$$', {a:1})).toStrictEqual({a:1});
  });

  test('数组边界条件处理', () => {
    // 越界数组访问
    expect(resolveVariable('arr[5]', {arr: [1,2,3]})).toBe(undefined);
    // 负索引访问
    expect(resolveVariable('arr[-1]', {arr: ['first']})).toBe(undefined);
    // 空数组访问
    expect(resolveVariable('arr[0]', {arr: []})).toBe(undefined);
  });

  test('非字符串路径参数', () => {
    // 数字类型路径
    expect(resolveVariable(123 as any, {123: 'value'})).toBe(undefined);
    // 对象类型路径
    expect(resolveVariable({key: 'name'} as any, {name: 'test'})).toBe(undefined);
    // null路径
    expect(resolveVariable(null as any, {})).toBe(undefined);
  });

  test('复杂对象路径解析', () => {
    // 深层嵌套对象
    expect(resolveVariable('obj.nested[1].deep.key', {
      obj: {
        nested: [
          {deep: {key: 'wrong'}},
          {deep: {key: 'correct'}}
        ]
      }
    })).toBe('correct');

    // 原型链属性访问
    const objWithProto = Object.create({protoKey: 'protoValue'});
    expect(resolveVariable('protoKey', objWithProto)).toBe('protoValue');
  });

  beforeEach(() => {
    // Mock localStorage
    let storage: { [key: string]: string } = {};
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn((key) => {
          return storage[key] ?? null;
        }),
        setItem: jest.fn((key, value) => {
          storage[key] = value.toString();
        }),
        removeItem: jest.fn((key) => {
          delete storage[key];
        }),
        clear: jest.fn(() => {
          storage = {};
        })
      },
      writable: true
    });
  });

  test('命名空间变量解析', () => {
    // 带命名空间的合法路径
    (window as any).mockNamespace = { key: 'windowValue' };
    expect(resolveVariable('window:mockNamespace.key')).toBe('windowValue');

    // localStorage命名空间测试
    localStorage.setItem('user', JSON.stringify({ name: 'lsUser' }));
    expect(resolveVariable('ls:user.name')).toBe('lsUser');

    // 不存在的命名空间
    expect(resolveVariable('invalidNamespace:key')).toBe(undefined);

    // 无效的命名空间语法
    expect(resolveVariable('invalid:namespace:path')).toBe(undefined);
  });
});