import {resolveVariableAndFilter} from '../src/utils/tpl-builtin';

describe('resolveVariableAndFilter', () => {
  test('纯变量解析', () => {
    expect(resolveVariableAndFilter('$name', {name: 'World'})).toBe('World');
    expect(resolveVariableAndFilter('name', {name: 'World'})).toBe('name');
    expect(resolveVariableAndFilter('$price', {price: 100})).toBe("100");
    expect(resolveVariableAndFilter('$obj.key', {obj: {key: 'value'}})).toBe('value');
    expect(resolveVariableAndFilter('${arr[0]}', {arr: ['first']})).toBe('first');
  });

  test('带过滤器处理', () => {
    expect(resolveVariableAndFilter('${name | upperCase}', {name: 'world'})).toBe('WORLD');
    expect(resolveVariableAndFilter('$name | upperCase', {name: 'world'})).toBe('world | upperCase');
    expect(resolveVariableAndFilter('${name | lowerCase}', {name: 'WORLD'})).toBe('world');
    expect(resolveVariableAndFilter('${date | date:YYYY-MM-DD}', {date: new Date(2023, 0, 1)})).toBe('2023-01-01');
    expect(resolveVariableAndFilter('${text | truncate:5}', {text: 'Hello World'})).toBe('Hello...');
    expect(resolveVariableAndFilter('${number | number:2}', {number: 123456})).toBe('123,456');
    expect(resolveVariableAndFilter('${html | raw}', {html: '<div>test</div>'})).toBe('<div>test</div>');
    expect(resolveVariableAndFilter('${html | html}', {html: '<div>test</div>'})).toBe('&lt;div&gt;test&lt;&#x2F;div&gt;');
  });

  test('空值处理', () => {
    expect(resolveVariableAndFilter('$missing', {})).toBe(undefined);
    expect(resolveVariableAndFilter('$missing.key', {missing: {}})).toBe(undefined);
    expect(resolveVariableAndFilter('$a', {a: ''})).toBe('');
    expect(resolveVariableAndFilter('$a', {a: null})).toBe(null);
    expect(resolveVariableAndFilter('$a', {a: undefined})).toBe(undefined);
    expect(resolveVariableAndFilter('$a.b.c', {a: null})).toBe(undefined);
  });

  test('特殊场景', () => {
    // 嵌套变量
    expect(resolveVariableAndFilter('$user.name', {
      user: {name: 'John'},
      field: 'name'
    })).toBe('John');

    // 函数返回值
    expect(resolveVariableAndFilter('$fn', {fn: () => 'value'})).toBe('function () { return &#39;value&#39;; }');

    // 多个过滤器串联
    // expect(resolveVariableAndFilter('${text | upperCase | truncate:3}', {
    //   text: 'hello'
    // })).toBe('HEL...');

    // 数组索引和对象属性混合
    expect(resolveVariableAndFilter('${users[0].name}', {
      users: [{name: 'Alice'}]
    })).toBe('Alice');
  });

  test('边界条件处理', () => {
    // 测试空输入
    expect(resolveVariableAndFilter(undefined, {})).toBe(undefined);
    expect(resolveVariableAndFilter('', {})).toBe(undefined);

    // 测试异常处理
    expect(resolveVariableAndFilter('$invalid.expression', {})).toBe(undefined);
  });

  test('异常处理场景', () => {
    const consoleWarn = jest.spyOn(console, 'warn');

    // 测试无效表达式
    expect(resolveVariableAndFilter('${expression 2}', {})).toBe(undefined);
    expect(consoleWarn).toHaveBeenCalled();

    consoleWarn.mockRestore();
  });

  test('混合字符串处理', () => {
    // 文本与变量混合
    expect(resolveVariableAndFilter('Hello $name!', {name: 'World'})).toBe('Hello World!');
    expect(resolveVariableAndFilter('Welcome to ${city}, ${country}!', {
      city: 'Beijing',
      country: 'China'
    })).toBe('Welcome to Beijing, China!');

    // 变量与过滤器混合
    expect(resolveVariableAndFilter('Hello ${name | upperCase}!', {name: 'world'})).toBe('Hello WORLD!');
    expect(resolveVariableAndFilter('Current date: ${date | date:YYYY-MM-DD}', {
      date: new Date(2023, 0, 1)
    })).toBe('Current date: 2023-01-01');

    // 复杂混合场景
    expect(resolveVariableAndFilter('User ${user.name} (${user.age}) from ${user.address.city}', {
      user: {
        name: 'John',
        age: 30,
        address: {city: 'New York'}
      }
    })).toBe('User John (30) from New York');
  });
});