import {resolveVariableAndFilterForAsync} from '../src/utils/resolveVariableAndFilterForAsync';
import {registerFunction} from '../src/utils/tpl-builtin'; // 扩充 formula 里面的 filter

describe('resolveVariableAndFilterForAsync', () => {
  test('基本变量解析', async () => {
    expect(await resolveVariableAndFilterForAsync('${user.name}', {user: {name: 'amis'}})).toBe('amis');
    expect(await resolveVariableAndFilterForAsync('${items | join}', {items: [1, 2, 3]})).toBe('1,2,3');
    expect(await resolveVariableAndFilterForAsync('${obj.key}', {obj: {key: 'value'}})).toBe('value');
    expect(await resolveVariableAndFilterForAsync('${arr[0]}', {arr: ['first']})).toBe('first');
  });

  test('带过滤器的变量解析', async () => {
    expect(await resolveVariableAndFilterForAsync('${text | upperCase}', {text: 'hello'})).toBe('HELLO');
    expect(await resolveVariableAndFilterForAsync('${text | lowerCase}', {text: 'WORLD'})).toBe('world');
    expect(await resolveVariableAndFilterForAsync('${html | html}', {html: '<p>content</p>'})).toBe('&lt;p&gt;content&lt;&#x2F;p&gt;');
    expect(await resolveVariableAndFilterForAsync('${html | raw}', {html: '<p>content</p>'})).toBe('<p>content</p>');
  });

  test('异步处理场景', async () => {
    registerFunction('getData', async () => ({id: 1}));
    expect(await resolveVariableAndFilterForAsync('${getData()}', {})).toStrictEqual({"id":1});

    registerFunction('getDelayedValue', async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
      return 'delayed';
    });
    expect(await resolveVariableAndFilterForAsync('${getDelayedValue()}', {})).toBe('delayed');
  });

  test('空值处理', async () => {
    expect(await resolveVariableAndFilterForAsync('${missing}', {})).toBe(undefined);
    expect(await resolveVariableAndFilterForAsync('${missing.key}', {missing: {}})).toBe(undefined);
    expect(await resolveVariableAndFilterForAsync('${value}', {value: null})).toBe(null);
    expect(await resolveVariableAndFilterForAsync('${value}', {value: undefined})).toBe(undefined);
    expect(await resolveVariableAndFilterForAsync('${a.b.c}', {a: null})).toBe(undefined);
  });

  test('边界条件处理', async () => {
    expect(await resolveVariableAndFilterForAsync(undefined, {})).toBe(undefined);
    expect(await resolveVariableAndFilterForAsync('', {})).toBe(undefined);
    expect(await resolveVariableAndFilterForAsync('${expression 0}', {})).toBe(undefined);
  });

  test('异常处理场景', async () => {
    const consoleWarn = jest.spyOn(console, 'warn');
    expect(await resolveVariableAndFilterForAsync('${invalid expression}', {})).toBe(undefined);
    expect(consoleWarn).toHaveBeenCalled();
    consoleWarn.mockRestore();
  });
});