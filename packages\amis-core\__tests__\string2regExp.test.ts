import {string2regExp} from '../src/utils/string2regExp';

describe('string2regExp', () => {
  test('基本字符串转换', () => {
    const regex = string2regExp('hello');
    expect(regex.test('hello')).toBe(true);
    expect(regex.test('HELLO')).toBe(true);
    expect(regex.test('world')).toBe(false);
  });

  test('特殊字符转义', () => {
    const specialChars = '.[]{}()*+?^$|\\';
    const regex = string2regExp(specialChars);
    expect(regex.test(specialChars)).toBe(true);
    expect(regex.test('abc')).toBe(false);

    // 测试点号转义
    const dotRegex = string2regExp('hello.world');
    expect(dotRegex.test('hello.world')).toBe(true);
    expect(dotRegex.test('helloaworld')).toBe(false);
  });

  test('大小写敏感性', () => {
    const caseInsensitive = string2regExp('Test');
    expect(caseInsensitive.test('test')).toBe(true);
    expect(caseInsensitive.test('TEST')).toBe(true);

    const caseSensitive = string2regExp('Test', true);
    expect(caseSensitive.test('Test')).toBe(true);
    expect(caseSensitive.test('test')).toBe(false);
  });

  test('连字符转义', () => {
    const regex = string2regExp('a-b');
    expect(regex.test('a-b')).toBe(true);
    expect(regex.test('ab')).toBe(false);
  });

  test('错误处理', () => {
    expect(() => string2regExp(null as any)).toThrow('Expected a string');
    expect(() => string2regExp(undefined as any)).toThrow('Expected a string');
    expect(() => string2regExp(123 as any)).toThrow('Expected a string');
  });
});
