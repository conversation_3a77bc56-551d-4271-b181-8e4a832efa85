import {tokenize} from '../src/utils/tpl-builtin';

describe('tokenize', () => {
  test('纯字符串处理', () => {
    expect(tokenize('Hello', {})).toBe('Hello');
    expect(tokenize('', {})).toBe('');
    expect(tokenize('   ', {})).toBe('   ');
    expect(tokenize('特殊字符!@#$%^&*()', {})).toBe('特殊字符!@#$%^&*()');
  });

  test('变量解析', () => {
    expect(tokenize('Hello ${name}', {name: 'World'})).toBe('Hello World');
    expect(tokenize('价格：${price}元', {price: 100})).toBe('价格：100元');
    expect(tokenize('${value}', {value: 0})).toBe('0');
    expect(tokenize('${value}', {value: false})).toBe('false');
    expect(tokenize('${value}', {value: NaN})).toBe('NaN');
    expect(tokenize('${value}', {value: Infinity})).toBe('Infinity');
    expect(tokenize('${obj.key}', {obj: {key: 'value'}})).toBe('value');
    expect(tokenize('${arr[0]}', {arr: ['first']})).toBe('first');
  });

  test('空值处理', () => {
    expect(tokenize('abc${a}', {a: ''})).toBe('abc');
    expect(tokenize('abc${a}', {a: null})).toBe('abc');
    expect(tokenize('abc${a}', {a: undefined})).toBe('abc');
    expect(tokenize('价格：${price}元', {price: null})).toBe('价格：元');
    expect(tokenize('${missing}', {})).toBe('');
    expect(tokenize('${missing.key}', {missing: {}})).toBe('');
  });

  test('过滤器处理', () => {
    expect(tokenize('${name | upperCase}', {name: 'world'})).toBe('WORLD');
    expect(tokenize('${name | lowerCase}', {name: 'WORLD'})).toBe('world');
    expect(tokenize('${html}', {html: '<div>test</div>'}, '| html')).toBe('&lt;div&gt;test&lt;&#x2F;div&gt;');
    expect(tokenize('${html | raw}', {html: '<div>test</div>'}, '')).toBe('<div>test</div>');
    expect(tokenize('${date | date:YYYY-MM-DD}', {date: new Date(2023, 0, 1)})).toBe('2023-01-01');
    expect(tokenize('${text | truncate:5}', {text: 'Hello World'})).toBe('Hello...');
    expect(tokenize('${name | upperCase | truncate:3}', {name: 'world'})).toBe('WOR...');
  });

  test('混合字符串变量解析', () => {
    expect(tokenize('Hello ${firstName} ${lastName}', {
      firstName: 'John',
      lastName: 'Doe'
    })).toBe('Hello John Doe');

    expect(tokenize('${greeting} ${user | upperCase}!', {
      greeting: 'Hello',
      user: 'world'
    })).toBe('Hello WORLD!');

    expect(tokenize('订单号：${orderId}，金额：${amount | number:2}元', {
      orderId: '12345',
      amount: 99.99
    })).toBe('订单号：12345，金额：99.99元');

    expect(tokenize('${a}${b}${c}', {
      a: '1',
      b: '2',
      c: '3'
    })).toBe('123');
  });

  test('特殊场景', () => {
    // 嵌套变量
    expect(tokenize('${user.${field}}', {
      user: {name: 'John'},
      field: 'name'
    })).toBe('John');

    // 多行字符串
    expect(tokenize('第一行\n第二行${var}第三行', {
      var: '变量'
    })).toBe('第一行\n第二行变量第三行');

    // 转义字符
    // expect(tokenize('\\${var}', {var: 'value'})).toBe('\\${var}');
  });

  test('错误处理', () => {
    // 无效的变量表达式
    expect(tokenize('${invalid expression}', {})).toBe('${invalid expression}');

    // 无效的过滤器语法
    expect(tokenize('${var | invalid filter}', {var: 'value'})).toBe('${var | invalid filter}');

    // 无效的JSON数据
    expect(tokenize('${var}', {var: () => {}})).toBe('function () { }');
  });
});
