import {validateItems} from '../src/utils/validations';
import {getRendererByName} from '../src/factory';

// Mock getRendererByName 来模拟渲染器
jest.mock('../src/factory', () => ({
  getRendererByName: jest.fn((type) => {
    // 根据组件类型返回不同的渲染器配置
    if (type === 'combo') {
      return {
        isFormItem: true,
        isMultiple: false,
        isFlat: false
      };
    }
    return {
      isFormItem: true,
      isMultiple: false,
      isFlat: false
    };
  }),
  getVariable: jest.requireActual('../src/factory').getVariable,
  createObject: jest.requireActual('../src/factory').createObject,
  isVisible: jest.requireActual('../src/factory').isVisible,
  isDisabled: jest.requireActual('../src/factory').isDisabled
}));

describe('validateItems', () => {
  test('应该正确处理 requiredOn 动态必填条件', () => {
    const items = [
      {
        type: 'input-text',
        name: 'name',
        required: true
      },
      {
        type: 'input-text',
        name: 'email',
        requiredOn: 'this.name === "admin"' // 当 name 为 "admin" 时，email 为必填
      }
    ];

    // 测试1：name 不是 "admin"，email 不必填，应该通过验证
    const value1 = [{name: 'user', email: ''}];
    const result1 = validateItems(value1, items, {});
    expect(result1).toBe('');

    // 测试2：name 是 "admin"，email 为空，应该验证失败
    const value2 = [{name: 'admin', email: ''}];
    const result2 = validateItems(value2, items, {});
    expect(result2).toEqual({
      rowIndex: 0,
      colIndex: 1,
      msg: 'validate.isRequired' // 必填验证失败的错误消息
    });

    // 测试3：name 是 "admin"，email 有值，应该通过验证
    const value3 = [{name: 'admin', email: '<EMAIL>'}];
    const result3 = validateItems(value3, items, {});
    expect(result3).toBe('');
  });

  test('应该正确处理 disabledOn 等其他表达式属性', () => {
    const items = [
      {
        type: 'input-text',
        name: 'status',
        required: true
      },
      {
        type: 'input-text',
        name: 'reason',
        required: true,
        disabledOn: 'this.status === "active"' // 当 status 为 "active" 时禁用
      }
    ];

        // 当字段被禁用时，通常不应该进行必填验证
    // 这里主要测试表达式属性是否被正确处理
    const value = [{status: 'active', reason: ''}];
    const result = validateItems(value, items, {});

    // 这个测试主要确保不会因为表达式处理而抛出错误
    // 由于 reason 字段仍然是必填的，所以会返回错误对象
    expect(result).toEqual({
      rowIndex: 0,
      colIndex: 1,
      msg: 'validate.isRequired'
    });
  });

  test('应该正确处理复杂的表达式条件', () => {
    const items = [
      {
        type: 'input-number',
        name: 'age',
        required: true
      },
      {
        type: 'input-text',
        name: 'guardian',
        requiredOn: 'this.age < 18' // 年龄小于18时需要监护人
      }
    ];

    // 测试1：成年人，不需要监护人
    const value1 = [{age: 25, guardian: ''}];
    const result1 = validateItems(value1, items, {});
    expect(result1).toBe('');

    // 测试2：未成年人，需要监护人但未填写
    const value2 = [{age: 16, guardian: ''}];
    const result2 = validateItems(value2, items, {});
    expect(result2).toEqual({
      rowIndex: 0,
      colIndex: 1,
      msg: 'validate.isRequired'
    });

    // 测试3：未成年人，填写了监护人
    const value3 = [{age: 16, guardian: 'John Doe'}];
    const result3 = validateItems(value3, items, {});
    expect(result3).toBe('');
  });

  test('测试表达式处理时机问题 - visibleOn 表达式', () => {
    const items = [
      {
        type: 'input-text',
        name: 'showType',
        required: true
      },
      {
        type: 'input-text',
        name: 'conditionalField',
        required: true,
        visibleOn: 'this.showType === "show"' // 只有当 showType 为 "show" 时才显示
      }
    ];

    // 测试1：字段应该隐藏，不参与验证
    const value1 = [{showType: 'hide', conditionalField: ''}];
    const result1 = validateItems(value1, items, {});
    console.log('测试1 - 隐藏字段结果:', result1);
    expect(result1).toBe(''); // 期望：隐藏字段不参与验证

    // 测试2：字段应该显示，参与验证且失败
    const value2 = [{showType: 'show', conditionalField: ''}];
    const result2 = validateItems(value2, items, {});
    console.log('测试2 - 显示字段结果:', result2);
    expect(result2).toEqual({
      rowIndex: 0,
      colIndex: 1,
      msg: 'validate.isRequired'
    }); // 期望：显示字段参与验证并失败

    // 测试3：字段显示且有值，验证通过
    const value3 = [{showType: 'show', conditionalField: 'value'}];
    const result3 = validateItems(value3, items, {});
    console.log('测试3 - 显示有值字段结果:', result3);
    expect(result3).toBe(''); // 期望：验证通过
  });

  test('测试表达式处理时机问题 - 复杂的 visibleOn 表达式', () => {
    const items = [
      {
        type: 'input-number',
        name: 'count',
        required: true
      },
      {
        type: 'input-text',
        name: 'details',
        required: true,
        visibleOn: 'this.count > 5' // 当 count > 5 时才显示
      }
    ];

    // 测试：count <= 5 时，details 字段应该隐藏
    const value1 = [{count: 3, details: ''}];
    const result1 = validateItems(value1, items, {});
    console.log('复杂表达式测试 - count=3 结果:', result1);
    expect(result1).toBe(''); // 期望：details 字段隐藏，不参与验证

    // 测试：count > 5 时，details 字段应该显示并验证
    const value2 = [{count: 10, details: ''}];
    const result2 = validateItems(value2, items, {});
    console.log('复杂表达式测试 - count=10 结果:', result2);
    expect(result2).toEqual({
      rowIndex: 0,
      colIndex: 1,
      msg: 'validate.isRequired'
    }); // 期望：details 字段显示，验证失败
  });

  test('应该正确处理 visibleOn 和 disabledOn 的优先级', () => {
    const items = [
      {
        type: 'input-text',
        name: 'type',
        required: true
      },
      {
        type: 'input-text',
        name: 'hiddenField',
        required: true,
        visibleOn: 'this.type === "show"' // 只有当 type 为 "show" 时才显示
      },
      {
        type: 'input-text',
        name: 'disabledField',
        required: true,
        disabledOn: 'this.type === "readonly"' // 当 type 为 "readonly" 时禁用
      }
    ];

    // 测试1：隐藏字段不应该被验证
    const value1 = [{type: 'hide', hiddenField: '', disabledField: 'value'}];
    const result1 = validateItems(value1, items, {});
    expect(result1).toBe(''); // hiddenField 被隐藏，不参与验证

    // 测试2：禁用字段仍然应该被验证（按照要求，disabled字段也要保持校验）
    const value2 = [{type: 'readonly', hiddenField: 'value', disabledField: ''}];
    const result2 = validateItems(value2, items, {});
    expect(result2).toEqual({
      rowIndex: 0,
      colIndex: 2,
      msg: 'validate.isRequired'
    }); // disabledField 被禁用但仍然参与验证并失败

    // 测试3：显示且启用的字段应该被验证
    const value3 = [{type: 'show', hiddenField: '', disabledField: 'value'}];
    const result3 = validateItems(value3, items, {});
    expect(result3).toEqual({
      rowIndex: 0,
      colIndex: 1,
      msg: 'validate.isRequired' // hiddenField 显示但为空，验证失败
    });
  });

  test('应该正确处理递归验证 - 嵌套组件中的表达式', () => {
    const items = [
      {
        type: 'input-text',
        name: 'parentType',
        required: true
      },
      {
        type: 'combo', // 复合组件
        name: 'nestedData',
        items: [
          {
            type: 'input-text',
            name: 'childField1',
            required: true
          },
          {
            type: 'input-text',
            name: 'childField2',
            requiredOn: 'this.childField1 === "trigger"' // 基于同级字段的条件
          }
        ]
      }
    ];

    // 测试1：嵌套字段的 requiredOn 条件不满足
    const value1 = [{
      parentType: 'test',
      nestedData: {
        childField1: 'normal',
        childField2: '' // childField1 不是 "trigger"，所以 childField2 不必填
      }
    }];
    const result1 = validateItems(value1, items, {});
    console.log('递归测试1 - 条件不满足:', result1);
    expect(result1).toBe(''); // 应该通过验证

    // 测试2：嵌套字段的 requiredOn 条件满足但未填写
    const value2 = [{
      parentType: 'test',
      nestedData: {
        childField1: 'trigger',
        childField2: '' // childField1 是 "trigger"，所以 childField2 必填但为空
      }
    }];
    const result2 = validateItems(value2, items, {});
    console.log('递归测试2 - 条件满足但未填写:', result2);
    expect(result2).toEqual({
      rowIndex: 0,
      colIndex: 1,
      msg: 'validate.isRequired'
    }); // 应该验证失败

    // 测试3：嵌套字段的 requiredOn 条件满足且已填写
    const value3 = [{
      parentType: 'test',
      nestedData: {
        childField1: 'trigger',
        childField2: 'filled' // childField1 是 "trigger"，childField2 已填写
      }
    }];
    const result3 = validateItems(value3, items, {});
    console.log('递归测试3 - 条件满足且已填写:', result3);
    expect(result3).toBe(''); // 应该通过验证
  });

  test('应该正确处理递归验证 - 基于父级数据的表达式', () => {
    const items = [
      {
        type: 'input-text',
        name: 'parentStatus',
        required: true
      },
      {
        type: 'combo',
        name: 'details',
        items: [
          {
            type: 'input-text',
            name: 'description',
            required: true
          },
          {
            type: 'input-text',
            name: 'approver',
            requiredOn: 'data.parentStatus === "pending"' // 基于父级数据的条件
          }
        ]
      }
    ];

    // 测试1：父级状态不是 pending，approver 不必填
    const value1 = [{
      parentStatus: 'active',
      details: {
        description: 'test description',
        approver: ''
      }
    }];
    const result1 = validateItems(value1, items, {});
    console.log('父级数据测试1 - 条件不满足:', result1);
    expect(result1).toBe('');

    // 测试2：父级状态是 pending，approver 必填但为空
    const value2 = [{
      parentStatus: 'pending',
      details: {
        description: 'test description',
        approver: ''
      }
    }];
    const result2 = validateItems(value2, items, {});
    console.log('父级数据测试2 - 条件满足但未填写:', result2);
    expect(result2).toEqual({
      rowIndex: 0,
      colIndex: 1,
      msg: 'validate.isRequired'
    });

    // 测试3：父级状态是 pending，approver 已填写
    const value3 = [{
      parentStatus: 'pending',
      details: {
        description: 'test description',
        approver: 'John Doe'
      }
    }];
    const result3 = validateItems(value3, items, {});
    console.log('父级数据测试3 - 条件满足且已填写:', result3);
    expect(result3).toBe('');
  });

  test('应该正确处理递归验证 - 多层嵌套的表达式', () => {
    const items = [
      {
        type: 'input-text',
        name: 'level1',
        required: true
      },
      {
        type: 'combo',
        name: 'level2',
        items: [
          {
            type: 'input-text',
            name: 'level2Field',
            required: true
          },
          {
            type: 'combo',
            name: 'level3',
            items: [
              {
                type: 'input-text',
                name: 'level3Field',
                requiredOn: 'data.level1 === "deep" && this.level2Field === "nested"'
              }
            ]
          }
        ]
      }
    ];

    // 测试：多层嵌套的复杂条件
    const value1 = [{
      level1: 'deep',
      level2: {
        level2Field: 'nested',
        level3: {
          level3Field: '' // 条件满足但未填写
        }
      }
    }];
    const result1 = validateItems(value1, items, {});
    console.log('多层嵌套测试 - 复杂条件:', result1);
         // 这个测试主要是确保不会因为复杂的嵌套结构而出错
     // 具体的验证结果取决于实际的数据传递机制
   });

   test('应该正确处理模板字符串格式的表达式 - ${} 语法', () => {
     const items = [
       {
         type: 'input-text',
         name: 'userType',
         required: true
       },
       {
         type: 'input-text',
         name: 'adminEmail',
         requiredOn: '${userType === "admin"}' // 使用 ${} 模板语法
       },
       {
         type: 'input-number',
         name: 'age',
         required: true
       },
       {
         type: 'input-text',
         name: 'guardian',
         requiredOn: '${age < 18}' // 使用 ${} 模板语法的数值比较
       }
     ];

     // 测试1：userType 不是 admin，adminEmail 不必填
     const value1 = [{
       userType: 'user',
       adminEmail: '',
       age: 25,
       guardian: ''
     }];
     const result1 = validateItems(value1, items, {});
     console.log('模板语法测试1 - userType=user:', result1);
     expect(result1).toBe(''); // 应该通过验证

     // 测试2：userType 是 admin，adminEmail 必填但为空
     const value2 = [{
       userType: 'admin',
       adminEmail: '',
       age: 25,
       guardian: ''
     }];
     const result2 = validateItems(value2, items, {});
     console.log('模板语法测试2 - userType=admin, adminEmail为空:', result2);
     expect(result2).toEqual({
       rowIndex: 0,
       colIndex: 1,
       msg: 'validate.isRequired'
     }); // adminEmail 验证失败

     // 测试3：age < 18，guardian 必填但为空
     const value3 = [{
       userType: 'user',
       adminEmail: '',
       age: 16,
       guardian: ''
     }];
     const result3 = validateItems(value3, items, {});
     console.log('模板语法测试3 - age=16, guardian为空:', result3);
     expect(result3).toEqual({
       rowIndex: 0,
       colIndex: 3,
       msg: 'validate.isRequired'
     }); // guardian 验证失败

     // 测试4：复杂条件都满足且已填写
     const value4 = [{
       userType: 'admin',
       adminEmail: '<EMAIL>',
       age: 16,
       guardian: 'Parent Name'
     }];
     const result4 = validateItems(value4, items, {});
     console.log('模板语法测试4 - 所有条件满足且已填写:', result4);
     expect(result4).toBe(''); // 应该通过验证
   });

   test('应该正确处理递归组件中的模板字符串表达式', () => {
     const items = [
       {
         type: 'input-text',
         name: 'parentMode',
         required: true
       },
       {
         type: 'combo',
         name: 'nestedConfig',
         items: [
           {
             type: 'input-text',
             name: 'configType',
             required: true
           },
                       {
              type: 'input-text',
              name: 'advancedOption',
              requiredOn: '${configType === "advanced" && parentMode === "expert"}' // 正确的模板语法：直接使用变量名
            }
         ]
       }
     ];

     // 测试1：条件不满足
     const value1 = [{
       parentMode: 'basic',
       nestedConfig: {
         configType: 'simple',
         advancedOption: ''
       }
     }];
     const result1 = validateItems(value1, items, {});
     console.log('递归模板语法测试1 - 条件不满足:', result1);
     expect(result1).toBe('');

     // 测试2：条件满足但未填写
     const value2 = [{
       parentMode: 'expert',
       nestedConfig: {
         configType: 'advanced',
         advancedOption: ''
       }
     }];
           const result2 = validateItems(value2, items, {});
      console.log('递归模板语法测试2 - 条件满足但未填写:', result2);

      // 调试：直接测试表达式计算
      const {getExprProperties} = require('../src/utils/filter-schema');
      const testItem = {
        type: 'input-text',
        name: 'advancedOption',
        requiredOn: '${configType === "advanced" && parentMode === "expert"}'
      };
      const testRowValue = {
        configType: 'advanced',
        parentMode: 'expert',
        advancedOption: ''
      };
      const exprResult = getExprProperties(testItem, testRowValue);
      console.log('调试 - 表达式计算结果:', exprResult);
     expect(result2).toEqual({
       rowIndex: 0,
       colIndex: 1,
       msg: 'validate.isRequired'
     });

     // 测试3：条件满足且已填写
     const value3 = [{
       parentMode: 'expert',
       nestedConfig: {
         configType: 'advanced',
         advancedOption: 'expert setting'
       }
     }];
     const result3 = validateItems(value3, items, {});
     console.log('递归模板语法测试3 - 条件满足且已填写:', result3);
     expect(result3).toBe('');
   });
 });
