{"name": "amis-core", "version": "1.89.4", "description": "amis-core", "main": "lib/index.js", "module": "esm/index.js", "types": "lib/index.d.ts", "author": "fex", "exports": {".": {"require": "./lib/index.js", "import": "./esm/index.js"}, "./src": "./src/index.tsx", "./*": "./src/*"}, "license": "Apache-2.0", "devDependencies": {"@rollup/plugin-commonjs": "^22.0.2", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^14.1.0", "@rollup/plugin-replace": "^5.0.1", "@rollup/plugin-typescript": "^8.3.4", "@testing-library/jest-dom": "^5.16.4", "@types/big.js": "^6.2.2", "@types/file-saver": "^2.0.1", "@types/hoist-non-react-statics": "^3.3.1", "@types/jest": "^28.1.0", "@types/react": "^18.0.24", "@types/react-dom": "^18.0.8", "immutable": "^4.1.0", "jest": "^29.0.3", "jest-environment-jsdom": "^29.0.3", "moment-timezone": "^0.5.34", "react": "^18.2.0", "react-dom": "^18.2.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-auto-external": "^2.0.0", "rollup-plugin-license": "^2.7.0", "ts-jest": "^29.0.2", "typescript": "^4.6.4"}, "scripts": {"build": "npm run clean-dist && NODE_ENV=production rollup -c ", "dev": "rollup -c -w", "test": "jest", "update-snapshot": "jest --updateSnapshot", "coverage": "jest --coverage", "clean-dist": "rimraf lib/** esm/** tsconfig.tsbuildinfo .rollup.cache/**"}, "files": ["lib", "esm"], "dependencies": {"amis-formula": "^1.89.4", "big.js": "^5.2.2", "classnames": "2.3.1", "file-saver": "^2.0.2", "hoist-non-react-statics": "^3.3.2", "lodash": "^4.17.15", "match-sorter": "^6.3.1", "mobx": "^4.5.0", "mobx-react": "^6.3.1", "mobx-state-tree": "^3.17.3", "moment": "^2.19.4", "papaparse": "^5.3.0", "qs": "6.9.7", "react-intersection-observer": "9.5.2", "react-json-view": "1.21.3", "tslib": "^2.3.1", "uncontrollable": "7.2.1"}, "peerDependencies": {"react": ">=16.8.6", "react-dom": ">=16.8.6"}, "jest": {"testEnvironment": "jsdom", "collectCoverageFrom": ["src/**/*"], "moduleFileExtensions": ["ts", "tsx", "js"], "transform": {"\\.(ts|tsx)$": ["ts-jest", {"diagnostics": false}]}, "setupFiles": ["jest-canvas-mock"], "testRegex": "/.*\\.test\\.(ts|tsx|js)$", "moduleNameMapper": {"\\.(css|less|sass|scss)$": "<rootDir>/../../__mocks__/styleMock.js", "\\.(svg)$": "<rootDir>/../../__mocks__/svgMock.js", "^amis\\-ui$": "<rootDir>/../amis-ui/src/index.tsx", "^amis\\-ui/lib/(.*)": "<rootDir>/../amis-ui/src/$1", "^amis\\-formula$": "<rootDir>/../amis-formula/src/index.ts", "^amis\\-formula/lib/(.*)": "<rootDir>/../amis-formula/src/$1"}, "setupFilesAfterEnv": ["<rootDir>/../amis-core/__tests__/jest.setup.js"], "testPathIgnorePatterns": ["/node_modules/", "/.rollup.cache/"]}, "publishConfig": {"access": "public", "registry": "http://registry.caijj.net/repository/npm-caijiajia/"}, "authors": ["<EMAIL>"], "gitHead": "37d23b4a8eb1c663bc38e8dd9040889ea1526ec4"}