import {Api, ApiObject} from '../types';
import {normalizeApiResponseData} from '../utils/api';
import {ServerError} from '../utils/errors';
import {createObject, isEmpty} from '../utils/helper';
import {RendererEvent} from '../utils/renderer-event';
import { normalizeApi } from '../utils/api';
import { attachmentAdpator } from '../utils/attachmentAdpator'
import { str2function } from '../utils/api'
import { makeTranslator } from "../locale";
import {
  RendererAction,
  ListenerAction,
  ListenerContext,
  registerAction
} from './Action';

export interface IAjaxAction extends ListenerAction {
  action: 'ajax';
  args: {
    api: Api;
    messages?: {
      success: string;
      failed: string;
    };
    options?: Record<string, any>;
    [propName: string]: any;
  };
}

/**
 * 发送请求动作
 *
 * @export
 * @class AjaxAction
 * @implements {Action}
 */
export class AjaxAction implements RendererAction {
  fetcherType: string;
  constructor(fetcherType: string = 'ajax') {
    this.fetcherType = fetcherType;
  }

  async run(
    action: IAjaxAction,
    renderer: ListenerContext,
    event: RendererEvent<any>
  ) {
    if (!renderer.props.env?.fetcher) {
      throw new Error('env.fetcher is required!');
    }
    if (this.fetcherType === 'downloadFile' && action.actionType === 'downloadFile'){
      const apiObject = normalizeApi(action?.api);
      apiObject.responseType = 'blob';
      const originAdaptor = typeof apiObject.adaptor === 'string'
        ? str2function(apiObject.adaptor,
          'payload',
          'response',
          'api'
        ) as any
        : apiObject.adaptor

      apiObject.adaptor = async (payload: object, response: any, api: ApiObject) => {
        let apiPayload: any = payload
        if (originAdaptor) {
          apiPayload = originAdaptor(payload, response, api)
        }

        if (!response.headers?.['content-disposition'] || action.args.fileName) {
          const fileName = action.args.fileName
          if (!response.headers) {
            response.headers = {}
          }
          response.headers['content-disposition'] = `attachment; filename=${fileName || '未命名文件'}`
        }

        const isSuccess = response.status === 200 && ([0, undefined].includes(apiPayload.status))

        // 请求成功时 调用下载方法
        if (isSuccess) {
          attachmentAdpator({
            ...response,
            data: apiPayload.data || apiPayload
          });
        }
        if (!isSuccess && Object.prototype.toString.call(apiPayload) === '[object Blob]') {
          let blobText = ''
          try {
            blobText = await apiPayload.text();
            apiPayload = JSON.parse(blobText);
          } catch (error) {
          }
        }

        return {
          status: apiPayload.status || 0,
          msg: apiPayload.msg || apiPayload.message || (
            isSuccess
              ? (makeTranslator('zh')?.('Embed.downloading') || '文件开始下载')
              : (makeTranslator('zh')?.('Embed.downloadError') || '文件下载失败')
          )
        }
      }
      action.api = apiObject;
    }
    if (this.fetcherType === 'download' && action.actionType === 'download') {
      if ((action as any)?.api) {
        (action as any).api.responseType = 'blob';
      }
    }

    const env = event.context.env;
    try {
      const result = await env.fetcher(
        action?.api,
        action.data ?? {},
        action?.options ?? {}
      );
      const responseData =
        !isEmpty(result.data) || result.ok
          ? normalizeApiResponseData(result.data)
          : null;

      // 记录请求返回的数据
      event.setData(
        createObject(event.data, {
          ...responseData, // 兼容历史配置
          responseData: responseData,
          [action.outputVar || 'responseResult']: {
            ...responseData,
            responseData,
            responseStatus: result.status,
            responseMsg: result.msg
          }
        })
      );

      if (!action?.options?.silent) {
        if (!result.ok) {
          throw new ServerError(
            (action?.api as ApiObject)?.messages?.failed ??
              action.messages?.failed ??
              result.msg,
            result
          );
        } else {
          const msg =
            (action.api as ApiObject)?.messages?.success ??
            action.messages?.success ??
            result.msg ??
            result.defaultMsg;
          msg &&
            env.notify(
              'success',
              msg,
              result.msgTimeout !== undefined
                ? {
                    closeButton: true,
                    timeout: result.msgTimeout
                  }
                : undefined
            );
        }
      }

      return result.data;
    } catch (e) {
      if (!action?.options?.silent) {
        if (e.type === 'ServerError') {
          const result = (e as ServerError).response;
          env.notify(
            'error',
            e.message,
            result.msgTimeout !== undefined
              ? {
                  closeButton: true,
                  timeout: result.msgTimeout
                }
              : undefined
          );
        } else {
          env.notify('error', e.message);
        }
      }

      // 不阻塞后面执行
      // throw e;
    }
  }
}

registerAction('ajax', new AjaxAction());

registerAction('download', new AjaxAction('download'));

registerAction('downloadFile', new AjaxAction('downloadFile'));
