import { qsstringify } from '../utils';
import {RendererEvent} from '../utils/renderer-event';
import {
  RendererAction,
  ListenerAction,
  ListenerContext,
  registerAction
} from './Action';

export interface ILocationAction extends ListenerAction {
  actionType: 'updateLocation',
  args: {
    query: object
  }
}

export class UpdateLocation implements RendererAction {
  async run(
    action: ILocationAction,
    renderer: ListenerContext,
    event: RendererEvent<any>
  ) {
    renderer.props.onAction?.(
      event,
      {
        ...action,
        actionType: 'updateLocation'
      },
      action.data
    );
  }
}

registerAction('updateLocation', new UpdateLocation());
