/** @license amis v@version
 *
 * Copyright Baidu
 *
 * This source code is licensed under the Apache license found in the
 * LICENSE file in the root directory of this source tree.
 */

import {
  Renderer,
  getRendererByName,
  getRenderers,
  registerRenderer,
  unRegisterRenderer,
  resolveRenderer,
  filterSchema,
  clearStoresCache,
  updateEnv,
  stores,
  defaultOptions,
  addSchemaFilter,
  extendDefaultEnv,
} from './factory';
import type {RenderOptions, RendererConfig, RendererProps} from './factory';
import './polyfills';
import './renderers/builtin';
import './renderers/register';
import {
  initSchemaPlugins,
  extendsSchemaComponent,
  addSchemaEnhancer,
  transToStandardModeSchema
} from './schemaPlugins';
export * from './utils/index';
export * from './types';
export * from './store';
export * from './schemaPlugins';
export * from './schemaCheck';
import * as utils from './utils/helper';
import {getEnv} from 'mobx-state-tree';

import {RegisterStore, RendererStore} from './store';
import {
  setDefaultLocale,
  getDefaultLocale,
  makeTranslator,
  register as registerLocale,
  extendLocale,
  removeLocaleData,
  localeable,
} from './locale';
import type {LocaleProps, TranslateFn} from './locale';

import Scoped, {ScopedContext} from './Scoped';
import type {IScopedContext} from './Scoped';

import {
  classnames,
  getClassPrefix,
  setDefaultTheme,
  theme,
  getTheme,
  themeable,
  makeClassnames,
} from './theme';
import type {ClassNamesFn, ThemeProps} from './theme';
const classPrefix = getClassPrefix();

export * from './actions';
import FormItem, {
  FormItemWrap,
  registerFormItem,
  getFormItemByName,
} from './renderers/Item';
import type {
  FormBaseControl,
  FormControlProps,
  FormItemProps,
} from './renderers/Item';
import {OptionsControl, registerOptionsControl} from './renderers/Options';
import type {OptionsControlProps} from './renderers/Options';
import type {FormOptionsControl} from './renderers/Options';
import {Schema} from './types';
import ScopedRootRenderer, {addRootWrapper, RootRenderProps} from './Root';
import {envOverwrite} from './envOverwrite';
import {EnvContext} from './env';
import type {RendererEnv, IAuthorizedConfig} from './env';
import React from 'react';
import {
  evaluate,
  evaluateForAsync,
  Evaluator,
  AsyncEvaluator,
  extendsFilters,
  filters,
  getFilters,
  lexer,
  parse,
  registerFilter,
  registerFunction,
} from 'amis-formula';
import type {FilterContext} from 'amis-formula';
import LazyComponent from './components/LazyComponent';
import Overlay from './components/Overlay';
import PopOver from './components/PopOver';
import {FormRenderer} from './renderers/Form';
import type {FormHorizontal} from './renderers/Form';
import {enableDebug, promisify, replaceText, wrapFetcher} from './utils/index';
import {RENDERER_TRANSMISSION_OMIT_PROPS} from './SchemaRenderer';

// 初始化插件
initSchemaPlugins();

// @ts-ignore
export const version = '__buildVersion';

export {
  clearStoresCache,
  updateEnv,
  Renderer,
  RendererProps,
  RenderOptions,
  RendererEnv,
  IAuthorizedConfig,
  EnvContext,
  RegisterStore,
  FormItem,
  FormItemWrap,
  FormItemProps,
  OptionsControl,
  FormRenderer,
  FormHorizontal,
  // 其他功能类方法
  utils,
  getRendererByName,
  registerRenderer,
  unRegisterRenderer,
  getRenderers,
  registerFormItem,
  getFormItemByName,
  registerOptionsControl,
  resolveRenderer,
  filterSchema,
  Scoped,
  ScopedContext,
  IScopedContext,
  setDefaultTheme,
  theme,
  themeable,
  ThemeProps,
  getTheme,
  classPrefix,
  getClassPrefix,
  classnames,
  makeClassnames,
  // 多语言相关
  getDefaultLocale,
  setDefaultLocale,
  registerLocale,
  makeTranslator,
  extendLocale,
  removeLocaleData,
  localeable,
  LocaleProps,
  TranslateFn,
  ClassNamesFn,
  // amis-formula 相关
  parse,
  lexer,
  Evaluator,
  AsyncEvaluator,
  FilterContext,
  filters,
  getFilters,
  registerFilter,
  extendsFilters,
  registerFunction,
  evaluate,
  evaluateForAsync,
  // 其他
  LazyComponent,
  Overlay,
  PopOver,
  addSchemaFilter,
  extendsSchemaComponent,
  addSchemaEnhancer,
  transToStandardModeSchema,
  OptionsControlProps,
  FormOptionsControl,
  FormControlProps,
  FormBaseControl,
  extendDefaultEnv,
  addRootWrapper,
  RendererConfig,
  RENDERER_TRANSMISSION_OMIT_PROPS
};

export function render(
  schema: Schema,
  props: RootRenderProps = {},
  options: RenderOptions = {},
  pathPrefix: string = '',
): JSX.Element {
  let locale = props.locale || getDefaultLocale();
  // 兼容 locale 的不同写法
  locale = locale.replace('_', '-');
  locale = locale === 'en' ? 'en-US' : locale;
  locale = locale === 'zh' ? 'zh-CN' : locale;
  locale = locale === 'cn' ? 'zh-CN' : locale;
  const translate = props.translate || makeTranslator(locale);
  let store = stores[options.session || 'global'];

  // 根据环境覆盖 schema，这个要在最前面做，不然就无法覆盖 validations
  envOverwrite(schema, locale);

  // 每次调 render 都用独立的 env 配置
  const mergedOptions = {
    ...defaultOptions,
    ...options,
    fetcher: options.fetcher
      ? wrapFetcher(options.fetcher, options.tracker)
      : defaultOptions.fetcher,
    confirm: promisify(
      options.confirm || defaultOptions.confirm || window.confirm,
    ),
    locale,
    translate,
  } as any;

  if (!store) {
    if (mergedOptions.enableAMISDebug) {
      // 因为里面还有 render
      setTimeout(() => {
        enableDebug();
      }, 10);
    }

    store = RendererStore.create({}, mergedOptions);
    stores[mergedOptions.session || 'global'] = store;
  } else {
    // 更新 env
    const env = getEnv(store);
    Object.assign(env, mergedOptions);
  }

  (window as any).amisStore = store; // 为了方便 debug.
  (window as any).amisSchema = schema; // 为了方便 debug.
  const env = getEnv(store);

  let theme = props.theme || options.theme || 'antd';
  if (theme === 'default') {
    theme = 'antd';
  }
  env.theme = getTheme(theme);

  if (props.locale !== undefined) {
    env.translate = translate;
    env.locale = locale;
  }

  // 默认将开启移动端原生 UI
  if (options.useMobileUI !== false) {
    props.useMobileUI = true;
  }

  schema = replaceText(schema, options.replaceText, env.replaceTextIgnoreKeys);

  return (
    <EnvContext.Provider value={env}>
      <ScopedRootRenderer
        {...props}
        schema={schema}
        pathPrefix={pathPrefix}
        rootStore={store}
        env={env}
        theme={theme}
        locale={locale}
        translate={translate}
      />
    </EnvContext.Provider>
  );
}
