import hoistNonReactStatic from 'hoist-non-react-statics';
import { reaction } from 'mobx';
import React from 'react';
import { IFormItemStore,IFormStore } from '../store/form';

import debounce from 'lodash/debounce';
import { observer } from 'mobx-react';
import { findDOMNode } from 'react-dom';
import Overlay from '../components/Overlay';
import PopOver from '../components/PopOver';
import {
registerRenderer,RendererConfig,RendererProps,renderersMap,TestFunc
} from '../factory';
import {
BaseApiObject,
BaseSchemaWithoutType,
ClassName,
Schema
} from '../types';
import { dataMapping,insertCustomStyle } from '../utils';
import { isApiOutdated,isEffectiveApi } from '../utils/api';
import {
anyChanged,autobind,createObject,
getVariable,getWidthRate,isMobile,ucFirst
} from '../utils/helper';
import { filter } from '../utils/tpl';
import { HocStoreFactory } from '../WithStore';
import { FormHorizontal,FormSchemaBase } from './Form';
import { wrapControl } from './wrapControl';

export type LabelAlign = 'right' | 'left';

export interface FormBaseControl extends BaseSchemaWithoutType {
  /**
   * 表单项大小
   */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'full';

  /**
   * 描述标题
   */
  label?: string | false;

  /**
   * 描述标题
   */
  labelAlign?: LabelAlign;

  /**
   * label自定义宽度，默认单位为px
   */
  labelWidth?: number | string;

  /**
   * 配置 label className
   */
  labelClassName?: string;

  /**
   * 字段名，表单提交时的 key，支持多层级，用.连接，如： a.b.c
   */
  name?: string;

  /**
   * 显示一个小图标, 鼠标放上去的时候显示提示内容
   */
  remark?: any;

  /**
   * 显示一个小图标, 鼠标放上去的时候显示提示内容, 这个小图标跟 label 在一起
   */
  labelRemark?: any;

  /**
   * 输入提示，聚焦的时候显示
   */
  hint?: string;

  /**
   * 当修改完的时候是否提交表单。
   */
  submitOnChange?: boolean;

  /**
   * 是否只读
   */
  readOnly?: boolean;

  /**
   * 只读条件
   */
  readOnlyOn?: string;

  /**
   * 不设置时，当表单提交过后表单项每次修改都会触发重新验证，
   * 如果设置了，则由此配置项来决定要不要每次修改都触发验证。
   */
  validateOnChange?: boolean;

  /**
   * 描述内容，支持 Html 片段。
   */
  description?: string;

  /**
   * @deprecated 用 description 代替
   */
  desc?: string;

  /**
   * 配置描述上的 className
   */
  descriptionClassName?: ClassName;

  /**
   * 配置当前表单项展示模式
   */
  mode?: 'normal' | 'inline' | 'horizontal';

  /**
   * 当配置为水平布局的时候，用来配置具体的左右分配。
   */
  horizontal?: FormHorizontal;

  /**
   * 表单 control 是否为 inline 模式。
   */
  inline?: boolean;

  /**
   * 配置 input className
   */
  inputClassName?: ClassName;

  /**
   * 占位符
   */
  placeholder?: string;

  /**
   * 是否为必填
   */
  required?: boolean;

  /**
   * 验证失败的提示信息
   */
  validationErrors?: {
    isAlpha?: string;
    isAlphanumeric?: string;
    isEmail?: string;
    isFloat?: string;
    isInt?: string;
    isJson?: string;
    isLength?: string;
    isUnique?: string;
    isNumeric?: string;
    isRequired?: string;
    isUrl?: string;
    matchRegexp?: string;
    matchRegexp2?: string;
    matchRegexp3?: string;
    matchRegexp4?: string;
    matchRegexp5?: string;
    maxLength?: string;
    maximum?: string;
    minLength?: string;
    minimum?: string;
    isDateTimeSame?: string;
    isDateTimeBefore?: string;
    isDateTimeAfter?: string;
    isDateTimeSameOrBefore?: string;
    isDateTimeSameOrAfter?: string;
    isDateTimeBetween?: string;
    isTimeSame?: string;
    isTimeBefore?: string;
    isTimeAfter?: string;
    isTimeSameOrBefore?: string;
    isTimeSameOrAfter?: string;
    isTimeBetween?: string;
    [propName: string]: any;
  };

  validations?:
    | string
    | {
        /**
         * 是否是字母
         */
        isAlpha?: boolean;

        /**
         * 是否为字母数字
         */
        isAlphanumeric?: boolean;

        /**
         * 是否为邮箱地址
         */
        isEmail?: boolean;

        /**
         * 是否为浮点型
         */
        isFloat?: boolean;

        /**
         * 是否为整型
         */
        isInt?: boolean;

        /**
         * 是否为 json
         */
        isJson?: boolean;

        /**
         * 长度等于指定值
         */
        isLength?: number;

        /**
         * 跨Form表单唯一字段
         */
        isUnique?: Array<string | Array<string>>;

        /**
         * 是否为数字
         */
        isNumeric?: boolean;

        /**
         * 是否为必填
         */
        isRequired?: boolean;

        /**
         * 是否为 URL 地址
         */
        isUrl?: boolean;

        /**
         * 内容命中指定正则
         */
        matchRegexp?: string;
        /**
         * 内容命中指定正则
         */
        matchRegexp1?: string;
        /**
         * 内容命中指定正则
         */
        matchRegexp2?: string;
        /**
         * 内容命中指定正则
         */
        matchRegexp3?: string;
        /**
         * 内容命中指定正则
         */
        matchRegexp4?: string;
        /**
         * 内容命中指定正则
         */
        matchRegexp5?: string;

        /**
         * 最大长度为指定值
         */
        maxLength?: number;

        /**
         * 最大值为指定值
         */
        maximum?: number;

        /**
         * 最小长度为指定值
         */
        minLength?: number;

        /**
         * 最小值为指定值
         */
        minimum?: number;

        /**
         * 和目标日期相同，支持指定粒度，默认到毫秒
         * @version 2.2.0
         */
        isDateTimeSame?: string | string[];

        /**
         * 早于目标日期，支持指定粒度，默认到毫秒
         * @version 2.2.0
         */
        isDateTimeBefore?: string | string[];

        /**
         * 晚于目标日期，支持指定粒度，默认到毫秒
         * @version 2.2.0
         */
        isDateTimeAfter?: string | string[];

        /**
         * 早于目标日期或和目标日期相同，支持指定粒度，默认到毫秒
         * @version 2.2.0
         */
        isDateTimeSameOrBefore?: string | string[];

        /**
         * 晚于目标日期或和目标日期相同，支持指定粒度，默认到毫秒
         * @version 2.2.0
         */
        isDateTimeSameOrAfter?: string | string[];

        /**
         * 日期处于目标日期范围，支持指定粒度和区间的开闭形式，默认到毫秒, 左右开区间
         * @version 2.2.0
         */
        isDateTimeBetween?: string | string[];

        /**
         * 和目标时间相同，支持指定粒度，默认到毫秒
         * @version 2.2.0
         */
        isTimeSame?: string | string[];

        /**
         * 早于目标时间，支持指定粒度，默认到毫秒
         * @version 2.2.0
         */
        isTimeBefore?: string | string[];

        /**
         * 晚于目标时间，支持指定粒度，默认到毫秒
         * @version 2.2.0
         */
        isTimeAfter?: string | string[];

        /**
         * 早于目标时间或和目标时间相同，支持指定粒度，默认到毫秒
         * @version 2.2.0
         */
        isTimeSameOrBefore?: string | string[];

        /**
         * 晚于目标时间或和目标时间相同，支持指定粒度，默认到毫秒
         * @version 2.2.0
         */
        isTimeSameOrAfter?: string | string[];

        /**
         * 时间处于目标时间范围，支持指定粒度和区间的开闭形式，默认到毫秒, 左右开区间
         * @version 2.2.0
         */
        isTimeBetween?: string | string[];

        [propName: string]: any;
      };

  /**
   * 默认值，切记只能是静态值，不支持取变量，跟数据关联是通过设置 name 属性来实现的。
   */
  value?: any;

  /**
   * 表单项隐藏时，是否在当前 Form 中删除掉该表单项值。注意同名的未隐藏的表单项值也会删掉
   */
  clearValueOnHidden?: boolean;

  /**
   * 远端校验表单项接口
   */
  validateApi?: string | BaseApiObject;
}

export interface FormItemBasicConfig extends Partial<RendererConfig> {
  type?: string;
  wrap?: boolean;
  renderLabel?: boolean;
  renderDescription?: boolean;
  test?: RegExp | TestFunc;
  storeType?: string;
  validations?: string;
  strictMode?: boolean;
  renderDialog?: boolean;
  renderTypography?: boolean;
  /**
   * schema变化使视图更新的属性白名单
   */
  detectProps?: Array<string>;
  shouldComponentUpdate?: (props: any, prevProps: any) => boolean;
  descriptionClassName?: string;
  storeExtendsData?: boolean;
  sizeMutable?: boolean;
  weight?: number;
  extendsData?: boolean;
  showErrorMsg?: boolean;

  // 兼容老用法，新用法直接在 Component 里面定义 validate 方法即可。
  validate?: (values: any, value: any) => string | boolean;
}

// 自己接收到属性。
export interface FormItemProps extends RendererProps {
  name?: string;
  formStore?: IFormStore;
  formItem?: IFormItemStore;
  formInited: boolean;
  formMode: 'normal' | 'horizontal' | 'inline' | 'row' | 'default';
  formHorizontal: FormHorizontal;
  formLabelAlign: LabelAlign;
  formLabelWidth?: number | string;
  defaultSize?: 'xs' | 'sm' | 'md' | 'lg' | 'full';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'full';
  labelAlign?: LabelAlign;
  labelWidth?: number | string;
  disabled?: boolean;
  btnDisabled: boolean;
  defaultValue: any;
  value?: any;
  prinstine: any;
  setPrinstineValue: (value: any) => void;
  onChange: (
    value: any,
    submitOnChange?: boolean,
    changeImmediately?: boolean,
  ) => void;
  onBulkChange?: (
    values: {[propName: string]: any},
    submitOnChange?: boolean,
  ) => void;
  addHook: (fn: Function, mode?: 'validate' | 'init' | 'flush') => () => void;
  removeHook: (fn: Function, mode?: 'validate' | 'init' | 'flush') => void;
  renderFormItems: (
    schema: Partial<FormSchemaBase>,
    region: string,
    props: any,
  ) => JSX.Element;
  onFocus: (e: any) => void;
  onBlur: (e: any) => void;

  formItemValue: any; // 不建议使用 为了兼容 v1
  getValue: () => any; // 不建议使用 为了兼容 v1
  setValue: (value: any, key: string) => void; // 不建议使用 为了兼容 v1

  inputClassName?: string;
  renderControl?: (props: FormControlProps) => JSX.Element;

  inputOnly?: boolean;
  renderLabel?: boolean;
  renderDescription?: boolean;
  sizeMutable?: boolean;
  wrap?: boolean;
  hint?: string;
  description?: string;
  descriptionClassName?: string;
  // error 详情
  errors?: {
    [propName: string]: string;
  };
  // error string
  error?: string;
  showErrorMsg?: boolean;
  showLabelColon?: boolean;
  hintLabel?: string; // 设置鼠标移入到label上要提示的文本
}

// 下发下去的属性
export type FormControlProps = RendererProps & {
  onOpenDialog: (schema: Schema, data: any) => Promise<any>;
} & Exclude<
    FormItemProps,
    | 'inputClassName'
    | 'renderControl'
    | 'defaultSize'
    | 'size'
    | 'error'
    | 'errors'
    | 'hint'
    | 'descriptionClassName'
    | 'inputOnly'
    | 'renderLabel'
    | 'renderDescription'
    | 'sizeMutable'
    | 'wrap'
  >;

export type FormItemComponent = React.ComponentType<FormItemProps>;
export type FormControlComponent = React.ComponentType<FormControlProps>;

export interface FormItemConfig extends FormItemBasicConfig {
  component: FormControlComponent;
}

const getItemLabelClassName = (props: FormItemProps) => {
  const {staticLabelClassName, labelClassName} = props;
  return props.static && staticLabelClassName
    ? staticLabelClassName
    : labelClassName;
};

const getItemInputClassName = (props: FormItemProps) => {
  const {staticInputClassName, inputClassName} = props;
  return props.static && staticInputClassName
    ? staticInputClassName
    : inputClassName;
};

const getHintTextForLabel = (props: FormItemProps) => {
  const {hintLabel, label, data} = props;

  // 没配置 hintLabel 尝试解析 label
  if (typeof hintLabel === 'undefined' && typeof label === 'string') {
    return filter(label, data);
  }

  let hintTextForLabel;
  if (typeof hintLabel === 'string') {
    hintTextForLabel = filter(hintLabel, data);
  }

  return hintTextForLabel;
};

export class FormItemWrap extends React.Component<FormItemProps> {
  reaction: Array<() => void> = [];
  lastSearchTerm: any;
  target: HTMLElement;

  constructor(props: FormItemProps) {
    super(props);

    this.state = {
      isOpened: false,
    };

    const {formItem: model} = props;

    if (model) {
      this.reaction.push(
        reaction(
          () => `${model.errors.join('')}${model.isFocused}${model.dialogOpen}`,
          () => this.forceUpdate(),
        ),
      );
      this.reaction.push(
        reaction(
          () => model?.filteredOptions,
          () => this.forceUpdate(),
        ),
      );
      this.reaction.push(
        reaction(
          () => JSON.stringify(model.tmpValue),
          () => this.syncAutoFill(model.tmpValue),
        ),
      );
    }
  }

  componentDidUpdate(prevProps: FormItemProps) {
    const props = this.props;
    const {formItem: model, dependencies} = props;

    // issue#905 添加 dependencies 变化时重新校验
    if(Array.isArray(dependencies)) {
      const data = props.data;
      const prevData = prevProps.data;
      let depsChanged = false;

      for(let i = 0; i < dependencies.length; i++) {
        const dep = dependencies[i];
        const value = getVariable(data, dep);
        const prevValue = getVariable(prevData, dep);

        if(value !== prevValue) {
          depsChanged = true;
          break;
        }
      }

      if(depsChanged) {
        model?.validate(data);
      }
    }

    if (
      isEffectiveApi(props.autoFill?.api, props.data) &&
      isApiOutdated(
        prevProps.autoFill?.api,
        props.autoFill?.api,
        prevProps.data,
        props.data,
      )
    ) {
      this.syncAutoFill(model?.tmpValue, true);
    }
  }

  componentDidMount() {
    this.target = findDOMNode(this) as HTMLElement;
  }

  componentWillUnmount() {
    this.reaction.forEach(fn => fn());
    this.reaction = [];
    this.syncAutoFill.cancel();
  }

  @autobind
  handleFocus(e: any) {
    const {formItem: model, autoFill} = this.props;
    model && model.focus();
    this.props.onFocus && this.props.onFocus(e);

    if (
      !autoFill ||
      (autoFill && !autoFill?.hasOwnProperty('showSuggestion'))
    ) {
      return;
    }
    this.handleAutoFill('focus');
  }

  @autobind
  handleBlur(e: any) {
    const {formItem: model} = this.props;
    model && model.blur();
    this.props.onBlur && this.props.onBlur(e);
  }

  handleAutoFill(type: string) {
    const {autoFill, onBulkChange, formItem, data} = this.props;
    const {trigger, mode} = autoFill;
    if (trigger === type && mode === 'popOver') {
      // 参照录入 popOver形式
      this.setState({
        isOpened: true,
      });
    } else if (
      // 参照录入 dialog | drawer
      trigger === type &&
      (mode === 'dialog' || mode === 'drawer')
    ) {
      formItem?.openDialog(this.buildSchema(), data, result => {
        if (!result?.selectedItems) {
          return;
        }

        this.updateAutoFillData(result.selectedItems);
      });
    }
  }

  updateAutoFillData(context: any) {
    const {formStore, autoFill, onBulkChange} = this.props;
    const {fillMapping, multiple} = autoFill;
    // form原始数据
    const data = formStore?.data;
    const contextData = createObject(
      {items: !multiple ? [context] : context, ...data},
      {...context},
    );
    let responseData: any = {};
    responseData = dataMapping(fillMapping, contextData);

    if (!multiple && !fillMapping) {
      responseData = context;
    }

    onBulkChange?.(responseData);
  }

  syncAutoFill = debounce(
    (term: any, reload?: boolean) => {
      (async (term: string, reload?: boolean) => {
        const {autoFill, onBulkChange, formItem, data} = this.props;

        // 参照录入
        if (!autoFill || (autoFill && !autoFill?.hasOwnProperty('api'))) {
          return;
        }
        if (autoFill?.showSuggestion) {
          this.handleAutoFill('change');
        } else {
          // 自动填充
          const itemName = formItem?.name;
          const ctx = createObject(data, {
            [itemName || '']: term,
          });
          if (
            (onBulkChange &&
              isEffectiveApi(autoFill.api, ctx) &&
              this.lastSearchTerm !== term) ||
            reload
          ) {
            let result = await formItem?.loadAutoUpdateData(
              autoFill.api,
              ctx,
              !!(autoFill.api as BaseApiObject)?.silent,
            );
            this.lastSearchTerm =
              (result && getVariable(result, itemName)) ?? term;

            if (autoFill?.fillMapping) {
              result = dataMapping(autoFill.fillMapping, result);
            }
            result && onBulkChange?.(result);
          }
        }
      })(term, reload).catch(e => console.error(e));
    },
    250,
    {
      trailing: true,
      leading: false,
    },
  );

  buildSchema() {
    const {
      render,
      autoFill,
      classPrefix: ns,
      classnames: cx,
      translate: __,
    } = this.props;
    if (!autoFill || (autoFill && !autoFill?.hasOwnProperty('api'))) {
      return;
    }
    const {
      api,
      mode,
      size,
      offset,
      position,
      multiple,
      filter,
      columns,
      labelField,
      popOverContainer,
      popOverClassName,
      valueField,
    } = autoFill;
    const form = {
      type: 'form',
      // debug: true,
      title: '',
      className: 'suggestion-form',
      body: {
        type: 'picker',
        embed: true,
        joinValues: false,
        label: false,
        labelField,
        valueField: valueField || 'value',
        multiple,
        name: 'selectedItems',
        options: [],
        required: true,
        source: api,
        pickerSchema: {
          type: 'crud',
          affixHeader: false,
          alwaysShowPagination: true,
          keepItemSelectionOnPageChange: true,
          headerToolbar: [],
          footerToolbar: [
            {
              type: 'pagination',
              align: 'left',
            },
            {
              type: 'bulkActions',
              align: 'right',
              className: 'ml-2',
            },
          ],
          multiple,
          filter,
          columns: columns || [],
        },
      },
      actions: [
        {
          type: 'button',
          actionType: 'cancel',
          label: __('cancel'),
        },
        {
          type: 'submit',
          actionType: 'submit',
          level: 'primary',
          label: __('confirm'),
        },
      ],
    };
    const schema = {
      type: mode,
      className: 'auto-fill-dialog',
      title: __('FormItem.autoFillSuggest'),
      size,
      body: form,
      actions: [
        {
          type: 'button',
          actionType: 'cancel',
          label: __('cancel'),
        },
        {
          type: 'submit',
          actionType: 'submit',
          level: 'primary',
          label: __('confirm'),
        },
      ],
    };
    if (mode === 'popOver') {
      return (
        <Overlay
          container={popOverContainer || this.target}
          target={() => this.target}
          placement={position || 'left-bottom-left-top'}
          show
        >
          <PopOver
            classPrefix={ns}
            className={cx(`${ns}auto-fill-popOver`, popOverClassName)}
            style={{
              minWidth: this.target ? this.target.offsetWidth : undefined,
            }}
            offset={offset}
            onHide={this.hanldeClose}
            overlay
          >
            {render('popOver-auto-fill-form', form, {
              onSubmit: this.hanldeSubmit,
            })}
          </PopOver>
        </Overlay>
      );
    } else {
      return schema;
    }
  }

  // 参照录入popOver提交
  @autobind
  hanldeSubmit(values: any) {
    const {onBulkChange, autoFill} = this.props;
    if (!autoFill || (autoFill && !autoFill?.hasOwnProperty('api'))) {
      return;
    }

    this.updateAutoFillData(values.selectedItems);

    this.hanldeClose();
  }

  @autobind
  hanldeClose() {
    this.setState({
      isOpened: false,
    });
  }

  @autobind
  async handleOpenDialog(schema: Schema, data: any) {
    const {formItem: model} = this.props;
    if (!model) {
      return;
    }

    return new Promise(resolve =>
      model.openDialog(schema, data, (result?: any) => resolve(result)),
    );
  }

  @autobind
  handleDialogConfirm([values]: Array<any>) {
    const {formItem: model} = this.props;
    if (!model) {
      return;
    }

    model.closeDialog(values);
  }

  @autobind
  handleDialogClose(confirmed = false) {
    const {formItem: model} = this.props;
    if (!model) {
      return;
    }
    model.closeDialog(confirmed);
  }

  renderControl(): JSX.Element | null {
    const {
      // 这里解构，不可轻易删除，避免被rest传到子组件
      inputClassName,
      formItem: model,
      classnames: cx,
      children,
      type,
      renderControl,
      formItemConfig,
      sizeMutable,
      size,
      defaultSize,
      useMobileUI,
      ...rest
    } = this.props;
    const mobileUI = useMobileUI && isMobile();

    if (renderControl) {
      const controlSize = size || defaultSize;
      return renderControl({
        ...rest,
        onOpenDialog: this.handleOpenDialog,
        type,
        classnames: cx,
        formItem: model,
        className: cx(
          `Form-control`,
          {
            'is-mobile': mobileUI,
            'is-inline': !!rest.inline && !mobileUI,
            'is-error': model && !model.valid,
            [`Form-control--withSize Form-control--size${ucFirst(
              controlSize,
            )}`]:
              sizeMutable !== false &&
              typeof controlSize === 'string' &&
              !!controlSize &&
              controlSize !== 'full',
          },
          model?.errClassNames,
          getItemInputClassName(this.props),
        ),
      });
    }

    return null;
  }

  /**
   * 布局扩充点，可以自己扩充表单项的布局方式
   */
  static layoutRenderers: {
    [propsName: string]: (
      props: FormItemProps,
      renderControl: () => JSX.Element | null,
    ) => JSX.Element;
  } = {
    horizontal: (props: FormItemProps, renderControl: () => JSX.Element) => {
      let {
        className,
        style,
        classnames: cx,
        description,
        descriptionClassName,
        captionClassName,
        desc,
        label,
        render,
        required,
        caption,
        remark,
        labelRemark,
        env,
        formItem: model,
        renderLabel,
        renderDescription,
        hint,
        data,
        showErrorMsg,
        useMobileUI,
        translate: __,
        static: isStatic,
        staticClassName,
        showLabelColon = false,
        hiddenBottomLine,
        renderTypography = true,
      } = props;

      // 强制不渲染 label 的话
      if (renderLabel === false) {
        label = label === false ? false : '';
      }

      description = description || desc;
      const horizontal = props.horizontal || props.formHorizontal || {};
      const left = getWidthRate(horizontal.left);
      const right = getWidthRate(horizontal.right);
      const labelAlign = props.labelAlign || props.formLabelAlign;
      const labelWidth = props.labelWidth || props.formLabelWidth || horizontal.labelWidth;
      const mobileUI = useMobileUI && isMobile();

      return (
        <div
          data-role="form-item"
          className={cx(
            `Form-item Form-item--horizontal`,
            isStatic && staticClassName ? staticClassName : className,
            {
              'Form-item--horizontal-justify': horizontal.justify,
              [`is-error`]: model && !model.valid,
              [`is-required`]: required,
              'is-mobile': mobileUI,
              'hidden-bottom-line': hiddenBottomLine,
            },
            model?.errClassNames,
          )}
          style={style}
        >
          {label !== false ? (
            <label
              className={cx(
                `Form-label`,
                {
                  [`Form-itemColumn--${
                    typeof horizontal.leftFixed === 'string'
                      ? horizontal.leftFixed
                      : 'normal'
                  }`]: horizontal.leftFixed,
                  [`Form-itemColumn--${left}`]: !horizontal.leftFixed,
                  'Form-label--left': labelAlign === 'left',
                  'Form-label--hasColon': label && showLabelColon,
                },
                getItemLabelClassName(props),
              )}
              style={labelWidth != null ? {width: labelWidth} : undefined}
              title={getHintTextForLabel(props)}
            >
              <span>
                {label
                  ? render(
                      'label',
                      // issue#469 label溢出后自动展示tooltip
                      renderTypography ?
                      {
                        type: 'typography',
                        text: typeof label === 'string' ? filter(label, data) : label,
                      } : typeof label === 'string' ? filter(label, data) : label
                    )
                  : null}
                {required && (label || labelRemark) ? (
                  <span className={cx(`Form-star`)}>*</span>
                ) : null}
                {labelRemark
                  ? render('label-remark', {
                      type: 'remark',
                      icon: labelRemark.icon || 'question-mark',
                      tooltip: labelRemark,
                      useMobileUI,
                      className: cx(`Form-labelRemark`),
                      container: props.popOverContainer
                        ? props.popOverContainer
                        : env && env.getModalContainer
                        ? env.getModalContainer
                        : undefined,
                    })
                  : null}
              </span>
            </label>
          ) : null}

          <div
            className={cx(`Form-value`, {
              // [`Form-itemColumn--offset${getWidthRate(horizontal.offset)}`]: !label && label !== false,
              [`Form-itemColumn--${right}`]:
                !horizontal.leftFixed && !!right && right !== 12 - left,
            })}
          >
            {renderControl()}

            {caption
              ? render('caption', caption, {
                  className: cx(`Form-caption`, captionClassName),
                })
              : null}

            {remark
              ? render('remark', {
                  type: 'remark',
                  icon: remark.icon || 'warning-mark',
                  tooltip: remark,
                  className: cx(`Form-remark`),
                  useMobileUI,
                  container: props.popOverContainer
                    ? props.popOverContainer
                    : env && env.getModalContainer
                    ? env.getModalContainer
                    : undefined,
                })
              : null}

            {hint && model && model.isFocused
              ? render('hint', hint, {
                  className: cx(`Form-hint`),
                })
              : null}

            {model &&
            !model.valid &&
            showErrorMsg !== false &&
            Array.isArray(model.errors) ? (
              <ul className={cx(`Form-feedback`)}>
                {model.errors.map((msg: string, key: number) => (
                  <li key={key}>{msg}</li>
                ))}
              </ul>
            ) : null}

            {renderDescription !== false && description
              ? render('description', description, {
                  className: cx(`Form-description`, descriptionClassName),
                })
              : null}
          </div>
        </div>
      );
    },

    normal: (props: FormItemProps, renderControl: () => JSX.Element) => {
      let {
        className,
        style,
        classnames: cx,
        desc,
        description,
        label,
        render,
        required,
        caption,
        remark,
        labelRemark,
        env,
        descriptionClassName,
        captionClassName,
        formItem: model,
        renderLabel,
        renderDescription,
        hint,
        data,
        showErrorMsg,
        useMobileUI,
        translate: __,
        static: isStatic,
        staticClassName,
        showLabelColon = false,
        hiddenBottomLine,
        renderTypography = true,
      } = props;

      const labelWidth = props.labelWidth || props.formLabelWidth;
      description = description || desc;
      const mobileUI = useMobileUI && isMobile();

      return (
        <div
          data-role="form-item"
          className={cx(
            `Form-item Form-item--normal`,
            isStatic && staticClassName ? staticClassName : className,
            {
              'is-error': model && !model.valid,
              [`is-required`]: required,
              'is-mobile': mobileUI,
              'hidden-bottom-line': hiddenBottomLine,
            },
            model?.errClassNames,
          )}
          style={style}
        >
          {label && renderLabel !== false ? (
            <label
              className={cx(
                `Form-label`,
                {
                  'Form-label--hasColon': label && showLabelColon,
                },
                getItemLabelClassName(props),
              )}
              title={getHintTextForLabel(props)}
            >
              <span>
                {label
                  ? render(
                      'label',
                      // issue#469 label溢出后自动展示tooltip
                      renderTypography ?
                      {
                        type: 'typography',
                        text: typeof label === 'string' ? filter(label, data) : label,
                      } : typeof label === 'string' ? filter(label, data) : label,
                    )
                  : null}
                {required && (label || labelRemark) ? (
                  <span className={cx(`Form-star`)}>*</span>
                ) : null}
                {labelRemark
                  ? render('label-remark', {
                      type: 'remark',
                      icon: labelRemark.icon || 'question-mark',
                      tooltip: labelRemark,
                      className: cx(`Form-lableRemark`),
                      useMobileUI,
                      container: props.popOverContainer
                        ? props.popOverContainer
                        : env && env.getModalContainer
                        ? env.getModalContainer
                        : undefined,
                    })
                  : null}
              </span>
            </label>
          ) : null}

          {renderControl()}

          {caption
            ? render('caption', caption, {
                className: cx(`Form-caption`, captionClassName),
              })
            : null}

          {remark
            ? render('remark', {
                type: 'remark',
                icon: remark.icon || 'warning-mark',
                className: cx(`Form-remark`),
                tooltip: remark,
                useMobileUI,
                container:
                  env && env.getModalContainer
                    ? env.getModalContainer
                    : undefined,
              })
            : null}

          {hint && model && model.isFocused
            ? render('hint', hint, {
                className: cx(`Form-hint`),
              })
            : null}

          {model &&
          !model.valid &&
          showErrorMsg !== false &&
          Array.isArray(model.errors) ? (
            <ul className={cx(`Form-feedback`)}>
              {model.errors.map((msg: string, key: number) => (
                <li key={key}>{msg}</li>
              ))}
            </ul>
          ) : null}

          {renderDescription !== false && description
            ? render('description', description, {
                className: cx(`Form-description`, descriptionClassName),
              })
            : null}
        </div>
      );
    },

    inline: (props: FormItemProps, renderControl: () => JSX.Element) => {
      let {
        className,
        style,
        classnames: cx,
        desc,
        description,
        label,
        render,
        required,
        caption,
        descriptionClassName,
        captionClassName,
        formItem: model,
        remark,
        labelRemark,
        env,
        hint,
        renderLabel,
        renderDescription,
        data,
        showErrorMsg,
        useMobileUI,
        translate: __,
        static: isStatic,
        staticClassName,
        showLabelColon = false,
        hiddenBottomLine,
        renderTypography = true,
      } = props;

      const labelAlign = props.labelAlign || props.formLabelAlign;
      const labelWidth = props.labelWidth || props.formLabelWidth;
      description = description || desc;
      const mobileUI = useMobileUI && isMobile();

      return (
        <div
          data-role="form-item"
          className={cx(
            `Form-item Form-item--inline`,
            isStatic && staticClassName ? staticClassName : className,
            {
              'is-error': model && !model.valid,
              [`is-required`]: required,
              'is-mobile': mobileUI,
              'hidden-bottom-line': hiddenBottomLine,
            },
            model?.errClassNames,
          )}
          style={style}
        >
          {label && renderLabel !== false ? (
            <label
              className={cx(
                `Form-label`,
                {
                  'Form-label--hasColon': label && showLabelColon,
                  'Form-label--left': labelAlign === 'left',
                },
                getItemLabelClassName(props),
              )}
              style={labelWidth != null ? {width: labelWidth} : undefined}
              title={getHintTextForLabel(props)}
            >
              <span>
                {label
                  ? render(
                      'label',
                      // issue#469 label溢出后自动展示tooltip
                      renderTypography ?
                      {
                        type: 'typography',
                        text: typeof label === 'string' ? filter(label, data) : label,
                      } : typeof label === 'string' ? filter(label, data) : label,
                    )
                  : label}
                {required && (label || labelRemark) ? (
                  <span className={cx(`Form-star`)}>*</span>
                ) : null}
                {labelRemark
                  ? render('label-remark', {
                      type: 'remark',
                      icon: labelRemark.icon || 'question-mark',
                      tooltip: labelRemark,
                      className: cx(`Form-lableRemark`),
                      useMobileUI,
                      container: props.popOverContainer
                        ? props.popOverContainer
                        : env && env.getModalContainer
                        ? env.getModalContainer
                        : undefined,
                    })
                  : null}
              </span>
            </label>
          ) : null}

          <div className={cx(`Form-value`)}>
            {renderControl()}

            {caption
              ? render('caption', caption, {
                  className: cx(`Form-caption`, captionClassName),
                })
              : null}

            {remark
              ? render('remark', {
                  type: 'remark',
                  icon: remark.icon || 'warning-mark',
                  className: cx(`Form-remark`),
                  tooltip: remark,
                  useMobileUI,
                  container: props.popOverContainer
                    ? props.popOverContainer
                    : env && env.getModalContainer
                    ? env.getModalContainer
                    : undefined,
                })
              : null}

            {hint && model && model.isFocused
              ? render('hint', hint, {
                  className: cx(`Form-hint`),
                })
              : null}

            {model &&
            !model.valid &&
            showErrorMsg !== false &&
            Array.isArray(model.errors) ? (
              <ul className={cx(`Form-feedback`)}>
                {model.errors.map((msg: string, key: number) => (
                  <li key={key}>{msg}</li>
                ))}
              </ul>
            ) : null}

            {renderDescription !== false && description
              ? render('description', description, {
                  className: cx(`Form-description`, descriptionClassName),
                })
              : null}
          </div>
        </div>
      );
    },

    row: (props: FormItemProps, renderControl: () => JSX.Element) => {
      let {
        className,
        style,
        classnames: cx,
        desc,
        description,
        label,
        render,
        required,
        caption,
        remark,
        labelRemark,
        env,
        descriptionClassName,
        captionClassName,
        formItem: model,
        renderLabel,
        renderDescription,
        hint,
        data,
        showErrorMsg,
        useMobileUI,
        translate: __,
        static: isStatic,
        staticClassName,
        showLabelColon = false,
        hiddenBottomLine,
        renderTypography = true,
      } = props;
      const labelAlign = props.labelAlign || props.formLabelAlign;
      const labelWidth = props.labelWidth || props.formLabelWidth;
      description = description || desc;
      const mobileUI = useMobileUI && isMobile();

      return (
        <div
          data-role="form-item"
          className={cx(
            `Form-item Form-item--row`,
            isStatic && staticClassName ? staticClassName : className,
            {
              'is-error': model && !model.valid,
              'Form-label--left': labelAlign === 'left',
              [`is-required`]: required,
              'is-mobile': mobileUI,
              'hidden-bottom-line': hiddenBottomLine,
            },
            model?.errClassNames,
          )}
          style={style}
        >
          <div className={cx('Form-rowInner')}>
            {label && renderLabel !== false ? (
              <label
                className={cx(
                  `Form-label`,
                  {
                    'Form-label--hasColon': label && showLabelColon,
                  },
                  getItemLabelClassName(props),
                )}
                style={labelWidth != null ? {width: labelWidth} : undefined}
                title={getHintTextForLabel(props)}
              >
                <span>
                  {render(
                    'label',
                    // issue#469 label溢出后自动展示tooltip
                    renderTypography ?
                    {
                      type: 'typography',
                      text: typeof label === 'string' ? filter(label, data) : label,
                    } : typeof label === 'string' ? filter(label, data) : label,
                  )}
                  {required && (label || labelRemark) ? (
                    <span className={cx(`Form-star`)}>*</span>
                  ) : null}
                  {labelRemark
                    ? render('label-remark', {
                        type: 'remark',
                        icon: labelRemark.icon || 'question-mark',
                        tooltip: labelRemark,
                        className: cx(`Form-lableRemark`),
                        useMobileUI,
                        container: props.popOverContainer
                          ? props.popOverContainer
                          : env && env.getModalContainer
                          ? env.getModalContainer
                          : undefined,
                      })
                    : null}
                </span>
              </label>
            ) : null}

            {renderControl()}

            {caption
              ? render('caption', caption, {
                  className: cx(`Form-caption`, captionClassName),
                })
              : null}

            {remark
              ? render('remark', {
                  type: 'remark',
                  icon: remark.icon || 'warning-mark',
                  className: cx(`Form-remark`),
                  tooltip: remark,
                  container:
                    env && env.getModalContainer
                      ? env.getModalContainer
                      : undefined,
                })
              : null}
          </div>

          {hint && model && model.isFocused
            ? render('hint', hint, {
                className: cx(`Form-hint`),
              })
            : null}

          {model &&
          !model.valid &&
          showErrorMsg !== false &&
          Array.isArray(model.errors) ? (
            <ul className={cx('Form-feedback')}>
              {model.errors.map((msg: string, key: number) => (
                <li key={key}>{msg}</li>
              ))}
            </ul>
          ) : null}

          {description && renderDescription !== false
            ? render('description', description, {
                className: cx(`Form-description`, descriptionClassName),
              })
            : null}
        </div>
      );
    },
  };

  render() {
    const {
      formMode,
      inputOnly,
      wrap,
      render,
      formItem: model,
      css,
      id,
      labelClassName,
      descriptionClassName,
      renderDialog = true, // 控制是否渲染formitem下的dialog
      $schema,
      $path,
    } = this.props;
    const mode = this.props.mode || formMode;

    insertCustomStyle(
      css,
      [
        {
          key: 'labelClassName',
          value: labelClassName,
        },
      ],
      id + '-label',
    );
    insertCustomStyle(
      css,
      [
        {
          key: 'descriptionClassName',
          value: descriptionClassName,
        },
      ],
      id + '-description',
    );

    if (wrap === false || inputOnly) {
      return this.renderControl();
    }

    const renderLayout =
      FormItemWrap.layoutRenderers[mode] ||
      FormItemWrap.layoutRenderers['normal'];

    return (
      <>
        {renderLayout(this.props, this.renderControl.bind(this))}

        {model && renderDialog
          ? render(
              'modal',
              {
                type: 'dialog',
                ...model.dialogSchema,
              },
              {
                show: model.dialogOpen,
                onClose: this.handleDialogClose,
                onConfirm: this.handleDialogConfirm,
                data: model.dialogData,
                formStore: undefined,
              },
            )
          : null}
      </>
    );
  }
}

// 白名单形式，只有这些属性发生变化，才会往下更新。
// 除非配置  strictMode
export const detectProps = [
  'formPristine', // 这个千万不能干掉。
  'formInited',
  'addable',
  'addButtonClassName',
  'addButtonText',
  'addOn',
  'btnClassName',
  'btnLabel',
  'style',
  'btnDisabled',
  'className',
  'clearable',
  'columns',
  'columnsCount',
  'controls',
  'desc',
  'description',
  'disabled',
  'static',
  'staticClassName',
  'staticLabelClassName',
  'staticInputClassName',
  'draggable',
  'editable',
  'editButtonClassName',
  'formHorizontal',
  'formMode',
  'hideRoot',
  'horizontal',
  'icon',
  'inline',
  'inputClassName',
  'label',
  'labelClassName',
  'labelField',
  'language',
  'level',
  'max',
  'maxRows',
  'min',
  'minRows',
  'multiLine',
  'multiple',
  'option',
  'placeholder',
  'removable',
  'required',
  'remark',
  'hint',
  'rows',
  'searchable',
  'showCompressOptions',
  'size',
  'step',
  'showInput',
  'unit',
  'value',
  'diffValue',
  'borderMode',
  'items',
  'showCounter',
  'minLength',
  'maxLength',
  'embed',
  'displayMode',
  'revealPassword',
  'loading',
];

export function asFormItem(config: Omit<FormItemConfig, 'component'>) {
  return (Control: FormControlComponent) => {
    const isSFC = !(Control.prototype instanceof React.Component);

    // 兼容老的 FormItem 用法。
    if (config.validate && !Control.prototype.validate) {
      const fn = config.validate;
      Control.prototype.validate = function () {
        const host = {
          input: this,
        };

        return fn.apply(host, arguments);
      };
    } else if (config.validate) {
      console.error(
        'FormItem配置中的 validate 将不起作用，因为类的成员函数中已经定义了 validate 方法，将优先使用类里面的实现。',
      );
    }

    if (config.storeType) {
      Control = HocStoreFactory({
        storeType: config.storeType,
        extendsData: config.extendsData,
      })(observer(Control));
      delete config.storeType;
    }

    return wrapControl(
      hoistNonReactStatic(
        class extends FormItemWrap {
          static defaultProps = {
            className: '',
            renderLabel: config.renderLabel,
            renderDescription: config.renderDescription,
            sizeMutable: config.sizeMutable,
            wrap: config.wrap,
            showErrorMsg: config.showErrorMsg,
            ...Control.defaultProps,
          };
          static propsList: any = [
            'value',
            'defaultValue',
            'onChange',
            'setPrinstineValue',
            'readOnly',
            'strictMode',
            ...((Control as any).propsList || []),
          ];

          static displayName = `FormItem${
            config.type ? `(${config.type})` : ''
          }`;
          static ComposedComponent = Control;

          ref: any;

          constructor(props: FormItemProps) {
            super(props);
            this.refFn = this.refFn.bind(this);
            this.getData = this.getData.bind(this);

            const {validations, formItem: model} = props;
            // 组件注册的时候可能默认指定验证器类型
            if (model && !validations && config.validations) {
              model.config({
                rules: config.validations,
              });
            }
          }

          shouldComponentUpdate(nextProps: FormControlProps) {
            // FIX: issue 905 dependencies 变化了，需要更新。
            const props = this.props;
            const {dependencies} = this.props;

            if (Array.isArray(dependencies)) {
              const data = nextProps.data;
              const prevData = props.data;
              let depsChanged = false;

              for (let i = 0; i < dependencies.length; i++) {
                const dep = dependencies[i];
                const value = getVariable(data, dep);
                const prevValue = getVariable(prevData, dep);

                if (value !== prevValue) {
                  depsChanged = true;
                  break;
                }
              }

              if (depsChanged) {
                return true;
              }
            }

            /*
              condition-builder由于历史原因，config.strictMode=false，
              为了性能优化，需要支持schema配置strictMode，见issue#1113
            */
            if (
              nextProps.$schema.type === 'condition-builder' &&
              nextProps.$schema.strictMode === true
            ) {
              // condition-builder配置了strictMode=true，走白名单更新逻辑；否者走原来的逻辑
            } else if (
              config.shouldComponentUpdate?.(this.props, nextProps) ||
              nextProps.strictMode === false ||
              config.strictMode === false
            ) {
              return true;
            }

            // 把可能会影响视图的白名单弄出来，减少重新渲染次数。
            if (
              anyChanged(
                detectProps.concat(config.detectProps || []),
                this.props,
                nextProps,
              )
            ) {
              return true;
            }

            return false;
          }

          getWrappedInstance() {
            return this.ref;
          }

          refFn(ref: any) {
            this.ref = ref;
          }

          getData() {
            return this.props.data;
          }

          renderControl() {
            const {
              // 这里解构，不可轻易删除，避免被rest传到子组件
              inputClassName,
              formItem: model,
              classnames: cx,
              children,
              type,
              size,
              defaultSize,
              useMobileUI,
              ...rest
            } = this.props;

            const controlSize = size || defaultSize;
            const mobileUI = useMobileUI && isMobile();
            //@ts-ignore
            const isOpened = this.state.isOpened;
            return (
              <>
                <Control
                  {...rest}
                  // 因为 formItem 内部可能不会更新到最新的 data，所以暴露个方法可以获取到最新的
                  // 获取不到最新的因为做了限制，只有表单项目 name 关联的数值变化才更新
                  getData={this.getData}
                  useMobileUI={useMobileUI}
                  onOpenDialog={this.handleOpenDialog}
                  size={config.sizeMutable !== false ? undefined : size}
                  onFocus={this.handleFocus}
                  onBlur={this.handleBlur}
                  type={type}
                  classnames={cx}
                  ref={isSFC ? undefined : this.refFn}
                  forwardedRef={isSFC ? this.refFn : undefined}
                  formItem={model}
                  className={cx(
                    `Form-control`,
                    {
                      'is-inline': !!rest.inline && !mobileUI,
                      'is-error': model && !model.valid,
                      [`Form-control--withSize Form-control--size${ucFirst(
                        controlSize,
                      )}`]:
                        config.sizeMutable !== false &&
                        typeof controlSize === 'string' &&
                        !!controlSize &&
                        controlSize !== 'full',
                    },
                    model?.errClassNames,
                    getItemInputClassName(this.props),
                  )}
                ></Control>
                {isOpened ? this.buildSchema() : null}
              </>
            );
          }
        },
        Control,
      ) as any,
    );
  };
}

export function registerFormItem(config: FormItemConfig): RendererConfig {
  let Control = asFormItem(config)(config.component);
  return registerRenderer({
    ...config,
    weight: typeof config.weight !== 'undefined' ? config.weight : -100, // 优先级高点
    component: Control as any,
    isFormItem: true,
  });
}

export function FormItem(config: FormItemBasicConfig) {
  return function (component: FormControlComponent): any {
    const renderer = registerFormItem({
      ...config,
      component,
    });

    return renderer.component as any;
  };
}

export function getFormItemByName(name: string) {
  return renderersMap[name];
}

export default FormItem;
