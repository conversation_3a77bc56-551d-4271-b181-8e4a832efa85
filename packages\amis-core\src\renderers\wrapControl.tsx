import React from 'react';
import {IFormStore, IFormItemStore} from '../store/form';
import debouce from 'lodash/debounce';
import isEqual from 'lodash/isEqual';

import {RendererProps, Renderer} from '../factory';
import {ComboStore, IComboStore, IUniqueGroup} from '../store/combo';
import {
  anyChanged,
  promisify,
  guid,
  isEmpty,
  autobind,
  getVariable,
  createObject
} from '../utils/helper';
import {
  isNeedFormula,
  isExpression,
  FormulaExec,
  replaceExpression,
  isNowFormula
} from '../utils/formula';
import {IIRendererStore, IRendererStore} from '../store';
import {ScopedContext, IScopedContext} from '../Scoped';
import {reaction} from 'mobx';
import {FormItemStore} from '../store/formItem';
import {isAlive} from 'mobx-state-tree';
import {observer} from 'mobx-react';
import hoistNonReactStatic from 'hoist-non-react-statics';
import {withRootStore} from '../WithRootStore';
import {FormBaseControl, FormItemWrap} from './Item';
import {Api} from '../types';
import {TableStore} from '../store/table';
import pick from 'lodash/pick';
import { tokenize, resolveVariableAndFilter, SchemaError } from '../utils';
import { schemaCheck } from '../schemaCheck';

export interface ControlOutterProps extends RendererProps {
  formStore?: IFormStore;
  name?: string;
  value?: any;
  id?: string;
  type?: string;
  required?: boolean;
  validations: string | {[propsName: string]: any};
  validationErrors: {[propsName: string]: any};
  validateOnChange: boolean;
  multiple?: boolean;
  delimiter?: string;
  joinValues?: boolean;
  extractValue?: boolean;
  valueField?: string;
  labelField?: string;
  unique?: boolean;
  selectFirst?: boolean;
  autoComplete?: any;
  autoFill?: any;
  clearValueOnHidden?: boolean;
  validateApi?: Api;
  submitOnChange?: boolean;
  validate?: (value: any, values: any, name: string) => any;
  formItem?: IFormItemStore;
  addHook?: (fn: { (): any, validateName?: string }, type?: 'validate' | 'init' | 'flush') => void;
  removeHook?: (fn: () => any, type?: 'validate' | 'init' | 'flush') => void;
  $schema: {
    pipeIn?: (value: any, data: any) => any;
    pipeOut?: (value: any, originValue: any, data: any) => any;
    [propName: string]: any;
  };
  store?: IIRendererStore;
  onChange?: (
    value: any,
    name: string,
    submit?: boolean,
    changePristine?: boolean
  ) => void;
  formItemDispatchEvent: (type: string, data: any) => void;
}

export interface ControlProps {
  onBulkChange?: (values: Object) => void;
  onChange?: (value: any, name: string, submit: boolean) => void;
  store: IIRendererStore;
}

export function wrapControl<
  T extends React.ComponentType<React.ComponentProps<T> & ControlProps>
>(ComposedComponent: T) {
  type OuterProps = JSX.LibraryManagedAttributes<
    T,
    Omit<React.ComponentProps<T>, keyof ControlProps>
  > &
    ControlOutterProps;

  const result = hoistNonReactStatic(
    withRootStore(
      observer(
        class extends React.Component<OuterProps> {
          public model: IFormItemStore | undefined;
          control?: any;
          value?: any = undefined;
          hook?: {(): any; validateName?: string};
          hook2?: {(): any; validateName?: string};
          hook3?: () => any;
          reaction?: () => void;

          static contextType = ScopedContext;
          static defaultProps = {};

          lazyEmitChange = debouce(this.emitChange.bind(this), 250, {
            trailing: true,
            leading: false
          });

          constructor(props: OuterProps) {
            super(props);

            const {
              formStore: form,
              formItem,
              rootStore,
              store,
              onChange,
              data,
              inputGroupControl,
              $schema: {
                id,
                type,
                required,
                validations,
                validationErrors,
                unique,
                value,
                multiple,
                delimiter,
                valueField,
                labelField,
                joinValues,
                extractValue,
                selectFirst,
                autoComplete, // 主要处理在combo下下拉框远程搜索时导致新增按钮消失的场景
                autoFill,
                clearValueOnHidden,
                validateApi,
                minLength,
                maxLength,
                validateOnChange,
                label,
                pagination
              }
            } = this.props;

            this.getValue = this.getValue.bind(this);
            this.setValue = this.setValue.bind(this);
            this.handleChange = this.handleChange.bind(this);
            this.setPrinstineValue = this.setPrinstineValue.bind(this);
            this.controlRef = this.controlRef.bind(this);
            this.handleBlur = this.handleBlur.bind(this);
            let name = this.props.$schema?.name;

            // 添加schema check
            schemaCheck(this.props, ['form-item', 'input-table']);

            // 如果 name 是表达式
            if (isExpression(name)) {
              name = tokenize(name, data);
            }
            if (!name) {
              // 一般情况下这些表单项都是需要 name 的，提示一下
              if (
                typeof type === 'string' &&
                (type.startsWith('input-') ||
                  type.endsWith('select') ||
                  type === 'switch' ||
                  type === 'textarea' ||
                  type === 'radios') &&
                type !== 'input-group'
              ) {
                console.warn('name is required', this.props.$schema);
              }

              return;
            }

            let propValue = this.props.value;
            const model = rootStore.addStore({
              id: guid(),
              path: this.props.$path,
              storeType: FormItemStore.name,
              parentId: store?.id,
              name
            }) as IFormItemStore;
            this.model = model;
            // @issue 打算干掉这个
            formItem?.addSubFormItem(model);
            model.config({
              // 理论上需要将渲染器的 defaultProps 全部生效，此处保险起见先只处理 multiple
              ...pick(
                {...ComposedComponent.defaultProps, ...this.props.$schema},
                ['multiple']
              ),
              id,
              type,
              required,
              unique,
              value,
              isValueSchemaExp: isExpression(value),
              rules: validations,
              messages: validationErrors,
              delimiter,
              valueField,
              labelField,
              joinValues,
              extractValue,
              selectFirst,
              autoComplete,
              autoFill,
              clearValueOnHidden,
              validateApi,
              minLength,
              maxLength,
              validateOnChange,
              label,
              inputGroupControl,
              pagination
            });

            // issue 这个逻辑应该在 combo 里面自己实现。
            if (
              this.model.unique &&
              form?.parentStore?.storeType === ComboStore.name
            ) {
              const combo = form.parentStore as IComboStore;
              combo.bindUniuqueItem(model);
            }

            if (propValue !== undefined && propValue !== null) {
              // 同步 value: 优先使用 props 中的 value
              model.changeTmpValue(propValue);
            } else {
              // 备注: 此处的 value 是 schema 中的 value（和props.defaultValue相同）
              const curTmpValue = isExpression(value)
                ? FormulaExec['formula'](value, data) // 对组件默认值进行运算
                : store?.getValueByName(model.name) ?? replaceExpression(value); // 优先使用公式表达式
              // 同步 value
              model.changeTmpValue(curTmpValue);

              if (
                onChange &&
                value !== undefined &&
                curTmpValue !== undefined
              ) {
                // 组件默认值支持表达式需要: 避免初始化时上下文中丢失组件默认值
                onChange(model.tmpValue, model.name, false, true);
              }
            }

            if (
              onChange &&
              typeof propValue === 'undefined' &&
              typeof store?.getValueByName(model.name, false) === 'undefined' &&
              // todo 后续再优化这个判断，
              // 目前 input-table 中默认值会给冲掉，所以加上这个判断
              // 对应 issue 为 https://github.com/baidu/amis/issues/2674
              store?.storeType !== TableStore.name
            ) {
              // 如果没有初始值，通过 onChange 设置过去
              onChange(model.tmpValue, model.name, false, true);
            }
          }

          componentDidMount() {
            const {
              store,
              formStore: form,
              $schema: {validate},
              addHook
            } = this.props;
            const name  = this.model?.name;

            // 提交前先把之前的 lazyEmit 执行一下。
            this.hook3 = () => {
              this.lazyEmitChange.flush();
            };
            addHook?.(this.hook3, 'flush');

            const formItem = this.model as IFormItemStore;
            if (formItem && validate) {
              let finalValidate = promisify(validate.bind(formItem));
              this.hook2 = () => {
                formItem.clearError('control:valdiate');
                return finalValidate(
                  this.props.data,
                  this.getValue(),
                  name
                ).then((ret: any) => {
                  if ((typeof ret === 'string' || Array.isArray(ret)) && ret) {
                    formItem.addError(ret, 'control:valdiate');
                  }
                });
              };
              this.hook2.validateName = formItem.name;
              addHook?.(this.hook2);
            }
          }

          componentDidUpdate(prevProps: OuterProps) {
            const props = this.props;
            const form = props.formStore;
            const model = this.model;
            let name = this.props.$schema?.name;
            // 如果 name 是表达式
            if (isExpression(name)) {
              name = tokenize(name, props.data);
            }

            // 如果name为空的情况，model会是undefined，这时候表示model没有存过值，不需要进行删除操作
            // http://gitlab.caijj.net/yanfaerbu/qianduan/dataseed-design/-/issues/342
            if (model && name !== model.name) {
              form?.deleteValueByName(model.name as string)
              model.setName(name)
              model.changeTmpValue(undefined)
            }

            if (
              model &&
              anyChanged(
                [
                  'id',
                  'validations',
                  'validationErrors',
                  'value',
                  'defaultValue',
                  'required',
                  'unique',
                  'multiple',
                  'delimiter',
                  'valueField',
                  'labelField',
                  'joinValues',
                  'extractValue',
                  'selectFirst',
                  'autoFill',
                  'clearValueOnHidden',
                  'validateApi',
                  'minLength',
                  'maxLength',
                  'label',
                  'pagination'
                ],
                prevProps.$schema,
                props.$schema
              )
            ) {
              model.config({
                required: props.$schema.required,
                id: props.$schema.id,
                unique: props.$schema.unique,
                value: props.$schema.value,
                isValueSchemaExp: isExpression(props.$schema.value),
                rules: props.$schema.validations,
                multiple: props.$schema.multiple,
                delimiter: props.$schema.delimiter,
                valueField: props.$schema.valueField,
                labelField: props.$schema.labelField,
                joinValues: props.$schema.joinValues,
                extractValue: props.$schema.extractValue,
                messages: props.$schema.validationErrors,
                selectFirst: props.$schema.selectFirst,
                autoFill: props.$schema.autoFill,
                clearValueOnHidden: props.$schema.clearValueOnHidden,
                validateApi: props.$schema.validateApi,
                minLength: props.$schema.minLength,
                maxLength: props.$schema.maxLength,
                label: props.$schema.label,
                inputGroupControl: props?.inputGroupControl
              });
            }

            // 此处需要同时考虑 defaultValue 和 value
            if (model && typeof props.value !== 'undefined') {
              // 渲染器中的 value 优先
              if (
                !isEqual(props.value, prevProps.value) &&
                !isEqual(props.value, model.tmpValue)
              ) {
                // 外部直接传入的 value 无需执行运算器
                model.changeTmpValue(props.value);
              }
            } else if (
              model &&
              typeof props.defaultValue !== 'undefined' &&
              isExpression(props.defaultValue)
            ) {
              const nowFormulaChecked = isNowFormula(props.defaultValue);
              // 渲染器中的 defaultValue 优先（备注: SchemaRenderer中会将 value 改成 defaultValue）
              if (
                !isEqual(props.defaultValue, prevProps.defaultValue) ||
                (props.data !== prevProps.data &&
                  (isNeedFormula(
                    props.defaultValue,
                    props.data,
                    prevProps.data
                  ) ||
                    nowFormulaChecked))
              ) {
                const curResult = FormulaExec['formula'](
                  props.defaultValue,
                  props.data
                );
                const prevResult = FormulaExec['formula'](
                  prevProps.defaultValue,
                  prevProps.data
                );
                if (
                  !isEqual(curResult, prevResult) &&
                  !isEqual(curResult, model.tmpValue)
                ) {
                  // 识别上下文变动、自身数值变动、公式运算结果变动
                  model.changeTmpValue(curResult);
                  if (props.onChange) {
                    props.onChange(curResult, model.name, false);
                  }
                } else if (nowFormulaChecked) {
                  const nowData = props.data[model.name];
                  // now 表达式，计算后的值永远相同
                  model.changeTmpValue(nowData);
                  if (props.onChange) {
                    props.onChange(nowData, model.name, false);
                  }
                }
              }
            } else if (model) {
              const valueByName = getVariable(props.data, model.name);

              if (isEqual(props.defaultValue, prevProps.defaultValue)) {
                // value 非公式表达式时，name 值优先，若 defaultValue 主动变动时，则使用 defaultValue
                if (
                  // 然后才是查看关联的 name 属性值是否变化
                  props.data !== prevProps.data &&
                  (!model.emitedValue ||
                    isEqual(model.emitedValue, model.tmpValue))
                ) {
                  model.changeEmitedValue(undefined);
                  const prevValueByName = getVariable(props.data, model.name);
                  if (
                    (!isEqual(valueByName, prevValueByName) ||
                      getVariable(props.data, model.name, false) !==
                        getVariable(prevProps.data, model.name, false)) &&
                    !isEqual(valueByName, model.tmpValue)
                  ) {
                    model.changeTmpValue(valueByName);
                    this.checkValidate();
                  }
                }
              } else if (
                !isEqual(props.defaultValue, prevProps.defaultValue) &&
                !isEqual(props.defaultValue, model.tmpValue)
              ) {
                // 组件默认值非公式
                const curValue = replaceExpression(props.defaultValue);
                model.changeTmpValue(curValue);
                if (props.onChange) {
                  props.onChange(curValue, model.name, false);
                }
              }
            }
          }

          componentWillUnmount() {
            this.hook && this.props.removeHook?.(this.hook);
            this.hook2 && this.props.removeHook?.(this.hook2);
            this.hook3 && this.props.removeHook?.(this.hook3, 'flush');
            // this.lazyEmitChange.flush();

            this.lazyEmitChange.cancel();
            this.reaction?.();
            this.disposeModel();
          }

          disposeModel() {
            const {formStore: form, formItem, rootStore} = this.props;

            if (
              this.model &&
              this.model.unique &&
              form?.parentStore &&
              form?.parentStore.storeType === ComboStore.name
            ) {
              const combo = form.parentStore as IComboStore;
              combo.unBindUniuqueItem(this.model);
            }

            if (this.model) {
              formItem &&
                isAlive(formItem) &&
                formItem.removeSubFormItem(this.model);

              this.model.clearValueOnHidden &&
                this.model.form?.deleteValueByName(this.model.name);

              isAlive(rootStore) && rootStore.removeStore(this.model);
            }
            delete this.model;
          }

          controlRef(control: any) {
            const {
              addHook,
              removeHook,
              formStore: form,
            } = this.props;

            const name = this.model?.name;

            // 因为 control 有可能被 n 层 hoc 包裹。
            while (control && control.getWrappedInstance) {
              control = control.getWrappedInstance();
            }

            if (control && control.validate && this.model) {
              const formItem = this.model as IFormItemStore;
              let validate = promisify(control.validate.bind(control));
              this.hook = () => {
                formItem.clearError('component:valdiate');

                return validate(this.props.data, this.getValue(), name).then(
                  ret => {
                    if (
                      (typeof ret === 'string' || Array.isArray(ret)) &&
                      ret
                    ) {
                      formItem.setError(ret, 'component:valdiate');
                    }
                  }
                );
              };
              this.hook.validateName = name;
              addHook?.(this.hook);
            } else if (!control && this.hook) {
              removeHook?.(this.hook);
              this.hook = undefined;
            }

            // 注册到 Scoped 上
            const originRef = this.control;
            this.control = control;
            const scoped = this.context as IScopedContext;

            if (control) {
              scoped.registerComponent(this.control);
            } else if (originRef) {
              scoped.unRegisterComponent(originRef);
            }
          }

          checkValidate() {
            if (!this.model) return; // 如果 model 为 undefined 则直接返回
            const validated = this.model.validated;
            const {formSubmited, validateOnChange} = this.props;

            if (
              // 如果配置了 minLength 或者 maxLength 就切成及时验证
              // this.model.rules.minLength ||
              // this.model.rules.maxLength ||
              validateOnChange === true ||
              (validateOnChange !== false && (formSubmited || validated))
            ) {
              this.validate();
            } else if (validateOnChange === false) {
              this.model?.reset();
            }
          }

          async validate() {
            if (!this.model) return;
            const {formStore: form, data: originData, row, formItemDispatchEvent} = this.props;
            /*
              因为TableRow做了性能优化，一般不会回rerender，导致renderCell不执行，传递下来的data是旧数据
              在跨行取数据的场景下，无法满足业务需求。所以在这里判断如果是嵌套在table下的formitem，使用row.locals替换data

              2024.5.7 提供了updateAllRows属性，用来控制是否更新所有的行
            */
            let data = originData;
            if (row) {
              data = row.locals ?? originData;
            }
            let result;
            if (
              this.model.unique &&
              form?.parentStore &&
              form.parentStore.storeType === ComboStore.name
            ) {
              const combo = form.parentStore as IComboStore;
              const group = combo.uniques.get(
                this.model.name
              ) as IUniqueGroup;
              const validPromises = group.items.map(item =>
                item.validate(data)
              );
              result = await Promise.all(validPromises);
            } else {
              result = [await this.model.validate(data)];
            }
            if (result && result.length) {
              if (result.indexOf(false) > -1) {
                formItemDispatchEvent('formItemValidateError', createObject(data, { errorFields: form?.errorFields }));
              } else {
                formItemDispatchEvent('formItemValidateSucc', data);
              }
            }
          }

          handleChange(
            value: any,
            submitOnChange: boolean = this.props.$schema.submitOnChange,
            changeImmediately: boolean = false
          ) {
            const {
              formStore: form,
              onChange,
              $schema: {
                type,
                pipeOut,
                changeImmediately: conrolChangeImmediately
              },
              formInited,
              data
            } = this.props;

            if (
              !this.model ||
              // todo 以后想办法不要強耦合类型。
              ~[
                'service',
                'group',
                'hbox',
                'panel',
                'grid',
                'input-group'
              ].indexOf(type)
            ) {
              onChange && onChange.apply(null, arguments as any);
              return;
            }

            if (pipeOut) {
              const oldValue = this.model.value;
              value = pipeOut.call(this, value, oldValue, data);
            }

            this.model.changeTmpValue(value);

            if (changeImmediately || conrolChangeImmediately || !formInited) {
              this.emitChange(submitOnChange);
            } else {
              // this.props.onTmpValueChange?.(value, this.model.name);
              this.lazyEmitChange(submitOnChange);
            }
          }

          emitChange(
            submitOnChange: boolean = this.props.$schema.submitOnChange
          ) {
            const {
              formStore: form,
              onChange,
              $schema: {
                id,
                label,
                type,
                onChange: onFormItemChange,
                maxLength,
                minLength
              },
              data,
              env,
              validateOnChange,
              formSubmited
            } = this.props;

            if (!this.model) {
              return;
            }
            const value = this.model.tmpValue;
            const name = this.model.name;
            const oldValue = getVariable(data, name, false);

            if (oldValue === value) {
              return;
            }

            if (type !== 'input-password') {
              env?.tracker(
                {
                  eventType: 'formItemChange',
                  eventData: {
                    id,
                    name,
                    label,
                    type,
                    value
                  }
                },
                this.props
              );
            }

            this.model.changeEmitedValue(value);
            if (
              onFormItemChange?.(value, oldValue, this.model, form) === false
            ) {
              return;
            }

            // onFormItemChange 可能会触发组件销毁，再次读取 this.model 为 undefined
            if (!this.model) {
              return;
            }

            onChange?.(value, name!, submitOnChange === true);
            this.checkValidate();
          }

          handleBlur(e: any) {
            const {
              onBlur,
              $schema: {validateOnBlur}
            } = this.props;

            if (validateOnBlur && this.model) {
              this.validate();
            }

            onBlur && onBlur(e);
          }

          setPrinstineValue(value: any) {
            if (!this.model) {
              return;
            }

            const {
              formStore: form,
              $schema: {pipeOut},
              onChange,
              value: oldValue,
              data
            } = this.props;

            const name = this.model.name;

            if (pipeOut) {
              value = pipeOut.call(this, value, oldValue, data);
            }

            onChange?.(value, name!, false, true);
          }

          getValue() {
            const {formStore: data, $schema: control} = this.props;
            let value: any = this.model ? this.model.tmpValue : control.value;

            if (control.pipeIn) {
              value = control.pipeIn.call(this, value, data);
            }

            return value;
          }

          // 兼容老版本用法，新版本直接用 onChange 就可以。
          setValue(value: any, key?: string) {
            const {
              onBulkChange
            } = this.props;
            const name = this.model?.name;

            if (!key || key === name) {
              this.handleChange(value);
            } else {
              onBulkChange &&
                onBulkChange({
                  [key]: value
                });
            }
          }

          render() {
            const {
              controlWidth,
              disabled,
              formMode,
              $schema: control,
              store,
              data,
              invisible,
              placeholder,
              defaultStatic,
            } = this.props;

            if (invisible) {
              return null;
            }

            const value = this.getValue();
            const model = this.model;

            const injectedProps: any = {
              defaultSize: controlWidth,
              disabled: disabled ?? control.disabled,
              static: control.static ?? defaultStatic,
              formItem: this.model,
              formMode: control.mode || formMode,
              ref: this.controlRef,
              data: data || store?.data,
              name: model?.name ?? control.name,
              value,
              defaultValue: control.value,
              formItemValue: value, // 为了兼容老版本的自定义组件
              onChange: this.handleChange,
              onBlur: this.handleBlur,
              setValue: this.setValue,
              getValue: this.getValue,
              prinstine: model ? model.prinstine : undefined,
              setPrinstineValue: this.setPrinstineValue,
              // !没了这个， tree 里的 options 渲染会出问题
              _filteredOptions: this.model?.filteredOptions,
              // 仅字符串格式的 placeholder 支持表达式
              placeholder: typeof placeholder === 'string'
                ? resolveVariableAndFilter(placeholder, data || store?.data, '| raw')
                : placeholder
            };

            return (
              <ComposedComponent
                {...(this.props as JSX.LibraryManagedAttributes<
                  T,
                  React.ComponentProps<T>
                >)}
                {...injectedProps}
              />
            );
          }
        }
      ) as any
    ),
    ComposedComponent
  );

  return result as typeof result & {
    ComposedComponent: T;
  };
}
