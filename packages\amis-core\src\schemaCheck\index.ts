import { validateFormItem } from './validateFormItem';
import { validateInputTableColumns } from './validateInputTableColumn'
import { validateDialog } from './validateDialog'

const checkMap = {
  'form-item': validateFormItem,
  // 'input-table': validateInputTableColumns, // #612 原因是table默认canAccessSuperData：false导致，不需要检查
  'dialog': validateDialog,
};

type CheckType = keyof typeof checkMap;

// check schema 方法
export function schemaCheck(props: any, type: CheckType | CheckType[]) {
  if(process.env.NODE_ENV === 'development') {
    const { ignoreSchemaCheck } = props;
    if(!ignoreSchemaCheck) {
      if(Array.isArray(type)) {
        type.forEach(item => {
          const check = checkMap[item];
          check && check(props);
        })
      } else {
        const check = checkMap[type];
        check && check(props);
      }
    }
  }
}




