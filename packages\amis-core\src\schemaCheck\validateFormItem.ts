import { SchemaError } from '../utils/errors';
import { isExpression } from '../utils/formula';

// 有一部分组件虽然是使用 formItem注册的，但是不需要必须在form中
const ignoreFormItem = [
  'drag-node',
  'drop-container',
  'button-toolbar',
  'static',
  'button-group-select',
  'button-toolbar'
];

/**
 *  判断当前路径是否在form中
 * @param path 
 * @returns 
 */
export function isInForm(path: string) {
  const pathArr = path.split('/');
  let isIn = false;
  
  pathArr.forEach(item => {
    if(item === 'form') {
      isIn = true;
    } else if(item === 'dialog' || item === 'drawer') {
      isIn = false;
    }
  })

  return isIn;
}

// check formItem 是否同时配置了name和value
// check formItem 是否在form内部
export function validateFormItem(props: any) {
  const { $schema: schema, $path: path } = props;
  // @ts-ignore
  const { name, value, type, static: isStatic } = schema;

  // 静态类组件不做校验
  // @ts-ignore
  if(type.includes('static') || isStatic) return;

  if(name && isExpression(value)) {
    // 过滤掉hidden组件
    if(type !== 'hidden') {
      throw new SchemaError(`表单组件 ${type} 不能同时设置 name 和 value， 请检查schema`, schema);
    }
  }

  if(!isInForm(path) && !ignoreFormItem.includes(type)) {
    throw new SchemaError(`表单组件 ${type} 只能在 form 组件中使用，错误schema：`, schema);
  }
}