import { registerSchemaPlugin } from './plugin';

import { dragPlugin } from './dragPlugin';
import { dropPlugin } from './dropPlugin';
import { schemaComponentPlugin } from './schemaComponentPlugin';
import { schemaEnhancerPlugin } from './schemaEnhancerPlugin'
import { componentStandardPlugin } from './componentStandardPlugin'

export * from './plugin';
export { transToStandardModeSchema } from './componentStandardPlugin'
export { extendsSchemaComponent } from './schemaComponentPlugin';
export { addSchemaEnhancer } from './schemaEnhancerPlugin';

// 初始化插件
export const initSchemaPlugins = () => {
  registerSchemaPlugin(componentStandardPlugin);
  registerSchemaPlugin(schemaComponentPlugin);
  registerSchemaPlugin(schemaEnhancerPlugin);
  registerSchemaPlugin(dragPlugin);
  registerSchemaPlugin(dropPlugin);
};
