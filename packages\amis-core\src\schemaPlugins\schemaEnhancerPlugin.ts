/**
 * 增强指定的 RendererComponent 的配置。
 * 作用： 给已经存在的 @renderer 注册过的 type, 进行 schema 转换（type不能被转换）
 */

import  upperFirst from 'lodash/upperFirst'
import camelCase from 'lodash/camelCase'
import get from 'lodash/get'
import { renderChildProps } from "../Root"
import { Schema } from "../types"
import { SchemaPlugin } from "./plugin"
import { addStandardModeSchemaType } from './componentStandardPlugin'

type TransformFn =  (schema: Schema, props?: renderChildProps) => Partial<Schema>
/**
 * 转换模式，在那些场景下进行转换
 */
type TransformMode =
  /**
   * 仅在 schema.standardMode 配置后生效, 即只对用户配置json进行转换
   * 在 componentStandardPlugin中，在 page 配置 standardMode 之后，会自动对指定 type 的添加 schema.standardMode，因此无需额外在 json 中配置。
   */
  | 'schemaStandardMode' // 默认
  /**
   *  顶层配置 standardMode:true 之后，
   * 使用该方式转化，对用户配置的JSON 转换，也会对 amis/amis-core 组件内部, 调用的 render('xx') 转换
   */
  | 'pageStandardMode'

type TransFormItem = {
  // 增强的属性（当非 standardMode，且配置了转换增强属性，未进行转换时，会提示错误）
  type: string
  transformFn: TransformFn
  enhanceProps?: string[]
  transformMode?: TransformMode
}
interface TransformMap {
  [type: string]: Array<TransFormItem>
}

const transformMap: TransformMap = {
  //
}

/**
 * 添加 Schema 增强转换
 * @param type 组件type
 * @param transformFn 转换函数
 * @param transformMode 在什么场景下转换，（默认在 standardMode 模式下转换）
 */
export const addSchemaEnhancer = (options: TransFormItem) => {
  const { type, transformFn, transformMode = 'schemaStandardMode' } = options || {}

  if (!type || !transformFn) {
    return
  }

  const transformItem = {
    ...options,
    transformMode,
  }

  // 将 type 添加，标准 schema 转换列表
  if (transformMode === 'schemaStandardMode') {
    addStandardModeSchemaType(type)
  }

  if (transformMap[type]?.length) {
    transformMap[type].push(transformItem)
  } else {
    transformMap[type] = [transformItem]
  }
}

/**
 * 增强组件属性转换组件
 */
export const schemaEnhancerPlugin: SchemaPlugin = (schema: Schema, props: renderChildProps) => {
  const schemaType = schema?.type
  const transformFnArr = transformMap[schemaType]

  // 当前 schema 开启标准模式之后，才启用件增强功能
  if (transformFnArr?.length) {
    const { type, ...resetSchema } = schema

    let isEnhanced = false
    const enhancedSchema = transformFnArr.reduce((sumSchema: Schema, transformItem) => {
      const { transformMode, transformFn, enhanceProps } = transformItem

      const selfStandard = typeof schema.standardMode !== 'undefined'

      // 配置了 schema.standardMode 则，按照配置确定是否转换
      const shouldTransform = selfStandard
        ? schema.standardMode
        // 如果是 pageStandardMode 则，看 顶级是否存在 standardMode
        : (transformMode === 'pageStandardMode' && !!props.standardMode)

      if (shouldTransform) {
        isEnhanced = true
        return transformFn(sumSchema)
      }

      // 仅在开发环境校验
      if (process.env.NODE_ENV === 'development') {
        // 增强属性未被正常转换时，给出提示
        if (enhanceProps?.length) {
          enhanceProps.forEach((prop) => {
            if (typeof get(sumSchema, prop) !== 'undefined') {
              console.error(`[type:${type}] "${prop}" 属性，仅在 "standardMode:true" 模式下支持，请在当前 [${type}] 中配置 "standardMode:true" 再查看效果`, sumSchema)
            }
          })
        }
      }

      // 不进行转换
      return sumSchema
    }, resetSchema);

    // 未增强时直接返回之前的 schema
    if (!isEnhanced) {
      return schema
    }

    // 增强之后统一添加公共逻辑
    const { className = '' } = enhancedSchema || {}

    return {
      ...enhancedSchema,
      // 增强schema时，无法转换 type
      type,
      // 默认增强组件 增加 standard-Type className
      className: `${
        typeof className === 'string' && className.indexOf('standard-') === -1
          ? `standard-${upperFirst(camelCase(type))} ${className}`
          : className
      }`,
    }
  }

  return schema;
}
