import {
  types,
  getParent,
  SnapshotIn,
  IAnyModelType,
  isAlive,
  Instance,
  detach,
} from 'mobx-state-tree';
import {iRendererStore} from './iRenderer';
import {
  resolveVariable,
  resolveVariableAndFilter,
  isPureVariable,
} from '../utils/tpl-builtin';
import { filter } from '../utils/tpl';
import isEqual from 'lodash/isEqual';
import find from 'lodash/find';
import sortBy from 'lodash/sortBy';
import {
  isBreakpoint,
  createObject,
  isObject,
  isVisible,
  guid,
  findTree,
  flattenTree,
  eachTree,
  difference,
  immutableExtends,
  extendObject,
  hasVisibleExpression,
  hasAbility,
} from '../utils/helper';
import {evalExpression} from '../utils/tpl';
import {IFormStore} from './form';
import {getStoreById} from './manager';
import { UniqueStore } from './unique';

/**
 * 内部列的数量 '__checkme' | '__dragme' | '__expandme'
 */
const PARTITION_INDEX = 3;

export const Column = types
  .model('Column', {
    label: types.optional(types.frozen(), undefined),
    type: types.optional(types.string, 'plain'),
    name: types.maybe(types.string),
    value: types.frozen(),
    id: '',
    groupName: '',
    toggled: false,
    toggable: true,
    expandable: false,
    checkdisable: false,
    isPrimary: false,
    searchable: types.maybe(types.frozen()),
    headSearchable: types.maybe(types.frozen()),
    enableSearch: true,
    sortable: false,
    filterable: types.optional(types.frozen(), undefined),
    fixed: '',
    index: 0,
    rawIndex: 0,
    width: 0, // 设置拖动后列的宽度
    minWidth: 0, // tableLayout: fixed布局中，拖动调整列宽时限制最小宽度
    realWidth: 0, // 单元格渲染后的真实宽度
    breakpoint: types.optional(types.frozen(), undefined),
    pristine: types.optional(types.frozen(), undefined),
    remark: types.optional(types.frozen(), undefined),
    className: types.union(types.string, types.frozen()),
    required: false,
    customColSpan: '',
    customRowSpan: '',
    isHighLight: false,
  })
  .actions(self => ({
    toggleToggle() {
      self.toggled = !self.toggled;
      const table = getParent(self, 2) as ITableStore;

      if (!table.activeToggaleColumns.length) {
        self.toggled = true;
      }

      table.persistSaveToggledColumns();
    },

    setToggled(value: boolean) {
      self.toggled = value;
    },

    setEnableSearch(value: boolean) {
      self.enableSearch = value;

      const table = getParent(self, 2) as ITableStore;
      table.persistSaveToggledColumns();
    },

    setIsHighLight(value: boolean) {
      self.isHighLight = value;
    },

    setWidth(value: number) {
      self.width = value;
    },

    setMinWidth(value: number) {
      self.minWidth = value;
    },

    setRealWidth(value: number) {
      self.realWidth = value;
    },
  }));

export type IColumn = Instance<typeof Column>;
export type SColumn = SnapshotIn<typeof Column>;

export const Row = types
  .model('Row', {
    storeType: 'Row',
    id: types.identifier,
    parentId: '',
    key: types.string,
    pristine: types.frozen({} as any),
    data: types.frozen({} as any),
    rowSpans: types.frozen({} as any),
    colSpans: types.frozen({} as any),
    index: types.number,
    newIndex: types.number,
    path: '', // 行数据的位置
    expandable: false,
    checkdisable: false,
    isHover: false,
    children: types.optional(
      types.array(types.late((): IAnyModelType => Row)),
      [],
    ),
    depth: types.number, // 当前children位于第几层，便于使用getParent获取最顶层TableStore
  })
  .views(self => ({
    get checked(): boolean {
      return (getParent(self, self.depth * 2) as ITableStore).isSelected(
        self as IRow,
      );
    },

    get allChecked(): boolean {
      let result = true;
      if (self.children.length > 0) {
        result = (getParent(self, self.depth * 2) as ITableStore).isChildrenSelected(self as IRow);
      }

      return result;
    },

    get someChecked(): boolean {
      // @ts-ignore checked是view定义的，ts无法感知
      let result = self.checked;
      if (self.children.length > 0) {
        result = self.children.some((row) => (getParent(row, row.depth * 2) as ITableStore).isSelected(
          row as IRow,
        ));
      }

      return result;
    },

    get modified() {
      if (!self.data) {
        return false;
      }

      return Object.keys(self.data).some(
        key => !isEqual(self.data[key], self.pristine[key]),
      );
    },

    getDataWithModifiedChilden() {
      let data = {
        ...self.data,
      };

      if (data.children && self.children) {
        data.children = self.children.map(item =>
          item.getDataWithModifiedChilden(),
        );
      }

      return data;
    },

    get collapsed(): boolean {
      const table = getParent(self, self.depth * 2) as ITableStore;
      if (table.dragging) {
        return true;
      }

      let from: IRow = self as any;

      while (from && (from as any) !== table) {
        if (!table.isExpanded(from)) {
          return true;
        }

        from = getParent(from, 2);
      }

      return false;
    },

    get expanded(): boolean {
      return !this.collapsed;
    },

    get moved() {
      return self.index !== self.newIndex;
    },

    get locals(): any {
      let children: Array<any> | null = null;
      if (self.children.length) {
        children = self.children.map(item => item.locals);
      }

      const parent = getParent(self, 2) as ITableStore;
      return createObject(
        extendObject((getParent(self, self.depth * 2) as ITableStore).data, {
          index: self.index,
          // todo 以后再支持多层，目前先一层
          parent: parent.storeType === Row.name ? parent.data : undefined,
        }),
        children
          ? {
              ...self.data,
              children,
            }
          : self.data,
      );
    },

    get checkable(): boolean {
      const table = getParent(self, self.depth * 2) as ITableStore;
      return table && table.itemCheckableOn
        ? evalExpression(table.itemCheckableOn, (self as IRow).locals)
        : true;
    },

    get draggable(): boolean {
      const table = getParent(self, self.depth * 2) as ITableStore;
      return table && table.itemDraggableOn
        ? evalExpression(table.itemDraggableOn, (self as IRow).locals)
        : true;
    },

    /**
     * 判断当前行点击后是否应该继续触发check
     * 用于限制checkOnItemClick触发的check事件
     */
    get isCheckAvaiableOnClick(): boolean {
      const table = getParent(self, self.depth * 2) as ITableStore;
      const keepItemSelectionOnPageChange =
        table?.keepItemSelectionOnPageChange;
      const selectionUpperLimit = table?.maxKeepItemSelectionLength;

      // 如果未做配置，或者配置不合法直接通过检查
      if (
        !keepItemSelectionOnPageChange ||
        !Number.isInteger(selectionUpperLimit) ||
        selectionUpperLimit === Infinity
      ) {
        return true;
      }

      // 使用内置ID，不会重复
      const selectedIds = (table?.selectedRows ?? []).map(
        (item: IRow) => item.id,
      );
      // 此时syncSelected还没有触发，所以需要比较点击之后的数量
      const selectedCount = selectedIds.includes(self.id)
        ? selectedIds.length - 1
        : selectedIds.length + 1;

      if (selectedCount > selectionUpperLimit) {
        return false;
      }

      return true;
    },
  }))
  .actions(self => ({
    toggle() {
      (getParent(self, self.depth * 2) as ITableStore).toggle(self as IRow);
    },

    cascadeToggle() {
      (getParent(self, self.depth * 2) as ITableStore).cascadeToggle(self as IRow);
    },

    toggleExpanded() {
      (getParent(self, self.depth * 2) as ITableStore).toggleExpanded(
        self as IRow,
      );
    },

    change(values: object, savePristine?: boolean) {
      self.data = immutableExtends(self.data, values);
      savePristine && (self.pristine = self.data);
    },

    reset() {
      self.newIndex = self.index;
      self.data = self.pristine;
    },

    setCheckdisable(bool: boolean) {
      self.checkdisable = bool;
    },

    setIsHover(value: boolean) {
      self.isHover = value;
    },

    replaceWith(data: any) {
      Object.keys(data).forEach(key => {
        if (key !== 'id') {
          (self as any)[key] = data[key];
        }
      });

      if (Array.isArray(data.children)) {
        const arr = data.children;
        const pool = arr.concat();

        // 把多的删了先
        if (self.children.length > arr.length) {
          self.children.splice(arr.length, self.children.length - arr.length);
        }

        let index = 0;
        const len = self.children.length;
        while (pool.length) {
          // 因为父级id未更新，所以需要将子级的parentId正确指向父级id
          const item = {
            ...pool.shift(),
            parentId: self.id,
          }!;

          if (index < len) {
            self.children[index].replaceWith(item);
          } else {
            const row = Row.create(item);
            self.children.push(row);
          }
          index++;
        }
      }
    },
  }));

export type IRow = Instance<typeof Row>;
export type SRow = SnapshotIn<typeof Row>;

export const SortRow = types.model('Sort', {
  orderBy: '',
  orderDir: types.optional(
    types.union(types.literal('asc'), types.literal('desc'), types.literal('')),
    'asc',
  ),
});

export const TableStore = UniqueStore
  .named('TableStore')
  .props({
    isCompositeStore: true,
    columns: types.array(Column),
    columnsRaw: types.optional(types.array(types.frozen()), []), // #1183 原始columns，用于持久化缓存getPersistDataKey拼接key值，解决column中visible:false排序失效问题
    rows: types.array(Row),
    selectedRows: types.array(types.reference(Row)),
    expandedRows: types.array(types.string),
    primaryField: 'id',
    valueField: '',
    orderBy: '',
    orderDir: types.optional(
      types.union(
        types.literal('asc'),
        types.literal('desc'),
        types.literal(''),
      ),
      'asc',
    ),
    sortList: types.array(SortRow), // 多列排序
    draggable: false,
    dragging: false,
    selectable: false,
    multiple: true,
    footable: types.frozen(),
    expandConfig: types.frozen(),
    isNested: false,
    columnsTogglable: types.optional(
      types.union(types.boolean, types.literal('auto')),
      'auto',
    ),
    toggledColumnsPersistId: '', // 用来拼到columnsTogglable持久化的路径
    columnsTogglerPersistKey: '', // 用户自定义columnsTogglable持久化的key
    itemCheckableOn: '',
    itemDraggableOn: '',
    hideCheckToggler: false,
    combineNum: 0,
    combineFromIndex: 0,
    isMergedCell: false, // 用来标识是否合并单元格
    formsRef: types.optional(types.array(types.frozen()), []),
    formItemsRef: types.optional(types.array(types.frozen()), []),
    maxKeepItemSelectionLength: Infinity,
    keepItemSelectionOnPageChange: false,
    searchFormExpanded: false, // 用来控制搜索框是否展开了，那个自动根据 searchable 生成的表单 autoGenerateFilter
    subTable: types.maybe(types.frozen()),
    searchFormData: types.frozen({}), // 多列搜索字段集合
    showExpansionColumn: true, // 展开/收起列是否显示
    cascadeSelection: false, // 是否开启级连选择功能
    routerType: 'hash', // 业务应用使用的路由类型【hash、history】
    canAccessSuperData: false,
    tableLayout: 'auto', // fixed布局精准列宽，不会因其中一列宽度改变影响其他列宽，auto模式会
    theadHeight: 0, // fix: visibility属性的collapse兼容safari。https://github.com/baidu/amis/issues/8708
  })
  .views(self => {
    function getColumnsExceptBuiltinTypes() {
      return self.columns.filter(
        item =>
          /** 排除掉内置的列和不可见的列 */
          !/^__/.test(item.type) &&
          isVisible(
            item.pristine,
            hasVisibleExpression(item.pristine) ? self.data : {},
          ),
      );
    }

    function getForms() {
      return self.formsRef.map(item => ({
        store: getStoreById(item.id) as IFormStore,
        rowIndex: item.rowIndex,
      }));
    }

    function getFormItems() {
      return self.formItemsRef.map(item => ({
        formItem: getStoreById(item.id),
        rowIndex: item.rowIndex,
        colIndex: item.colIndex,
        isFormItem: item.isFormItem,
      }));
    }

    function getExportedColumns() {
      return self.columns.filter(item => {
        return (
          item &&
          isVisible(
            item.pristine,
            hasVisibleExpression(item.pristine) ? self.data : {},
          ) &&
          (item.toggled || !item.toggable) &&
          !/^__/.test(item.type)
        );
      });
    }

    function getFilteredColumns() {
      return self.columns.filter(
        item =>
          item &&
          isVisible(
            item.pristine,
            hasVisibleExpression(item.pristine) ? self.data : {},
          ) &&
          (item.type === '__checkme'
            ? self.selectable &&
              !self.dragging &&
              !self.hideCheckToggler &&
              self.rows.length
            : item.type === '__dragme'
            ? self.dragging
            : item.type === '__expandme'
            ? (getFootableColumns().length || self.isNested) && !self.dragging
            : (item.toggled || !item.toggable) &&
              (!self.footable ||
                !item.breakpoint ||
                !isBreakpoint(item.breakpoint))),
      );
    }

    function getFootableColumns() {
      return self.columns.filter(item =>
        item.type === '__checkme' ||
        item.type === '__dragme' ||
        item.type === '__expandme'
          ? false
          : (item.toggled || !item.toggable) &&
            self.footable &&
            item.breakpoint &&
            isBreakpoint(item.breakpoint),
      );
    }

    function isSelected(row: IRow): boolean {
      return !!~self.selectedRows.indexOf(row);
    }

    function isChildrenSelected(row: IRow): boolean {
      const flattedRows = flattenTree<IRow>([row]).filter(
        (item: IRow) => item.checkable,
      );
      const result = flattedRows.every((item: IRow) => (getParent(item, item.depth * 2) as ITableStore).isSelected(item))
      return result;
    }

    /**
     * 获取行的数据标示ID。不存在时回退到 row.id
     */
    function getRowDataId(row: IRow): string {
      // crud/table 使用 primaryField 做唯一key
      // inputTable 使用 valueField 做唯一key（需要单独配置）
      const dateKey = self?.valueField || self?.primaryField

      let dataId = row?.data?.[dateKey]
      // 仅识别 数字/字符串 类型 的 dataId
      dataId = ['number','string'].includes(typeof dataId)
        ? `${dataId}`
        : ''

      return dataId || row.id;
    }

    function isExpanded(row: IRow): boolean {
      return self.expandedRows.includes(getRowDataId(row));
    }

    function getTogglable() {
      if (self.columnsTogglable === 'auto') {
        return self.columns.filter(item => !/^__/.test(item.type)).length > 5;
      }

      return self.columnsTogglable;
    }

    function getToggableColumns() {
      return self.columns.filter(
        item => isVisible(item.pristine, self.data) && item.toggable !== false,
      );
    }

    function getActiveToggableColumns() {
      return getToggableColumns().filter(item => item.toggled);
    }

    function getModifiedRows(rows: IRow[] = [], modifiedRows: IRow[] = []) {
      rows = rows && rows.length ? rows : self.rows;
      rows.forEach((item: IRow) => {
        if (item.children && item.children.length) {
          getModifiedRows(item.children, modifiedRows);
        }
        let diff = difference(item.data, item.pristine);
        let hasDifference = Object.keys(diff).length;
        if (hasDifference) {
          modifiedRows.push(item);
        }
      });
      return modifiedRows;
    }

    function getModified() {
      return getModifiedRows().length;
    }

    function getMovedRows() {
      return flattenTree(self.rows).filter((item: IRow) => item.moved);
    }

    function getMoved() {
      return getMovedRows().length;
    }

    function getHovedRow(): IRow | undefined {
      return flattenTree<IRow>(self.rows).find((item: IRow) => item.isHover);
    }

    function getUnSelectedRows() {
      return flattenTree<IRow>(self.rows).filter((item: IRow) => !item.checked);
    }

    function getData(superData: any): any {
      return createObject(superData, {
        items: self.rows.map(item => item.data),
        selectedItems: self.selectedRows.map(item => item.data),
        unSelectedItems: getUnSelectedRows().map(item => item.data),
      });
    }

    function hasColumnHidden() {
      return self.columns.findIndex(column => !column.toggled) !== -1;
    }

    function getColumnGroup() {
      const columns = getFilteredColumns();
      const len = columns.length;

      if (!len) {
        return [];
      }

      const groups: Array<{
        /** Group单元格显示名称，从1开始 */
        label: string;
        /** Group单元格包含的首列的索引值，范围[1, columns.length] */
        index: number;
        /** Group单元格包含列数 */
        colSpan: number;
        /** Group单元格包含行数 */
        rowSpan: number;
        /** Group单元格包含列信息 */
        has: Array<any>;
      }> = [
        {
          label: columns[0].groupName,
          colSpan: 1,
          rowSpan: 1,
          index: columns[0].index,
          has: [columns[0]],
        },
      ];

      // 用户是否启用了 groupName
      const hasGroupName = columns.some(column => column.groupName);

      for (let i = 1; i < len; i++) {
        let prev = groups[groups.length - 1];
        const current = columns[i];

        const groupNameIsSame =
          current.groupName === prev.label ||
          resolveVariableAndFilter(current.groupName, self.data) ===
            resolveVariableAndFilter(prev.label, self.data);

        if (
          groupNameIsSame &&
          ((hasGroupName && current.groupName) || !hasGroupName)
        ) {
          prev.colSpan++;
          prev.has.push(current);
        } else {
          groups.push({
            /**
             * 如果中间没有配置groupName，那么样式会错乱，这里设置列的label配置，lable也没有则设置一个空字符串
             * 注：内部列需要设置为undefined，保证rowSpan在下面计算为2
             */
            label: !!~['__checkme', '__expandme'].indexOf(current.type)
              ? undefined
              : current.groupName || current.label || ' ',
            colSpan: 1,
            rowSpan: 1,
            index: current.index,
            has: [current],
          });
        }
      }

      if (groups.length === 1 && !groups[0].label) {
        groups.pop();
      }

      return groups.map(item => {
        const rowSpan =
          !item.label ||
          (item.has.length === 1 && item.label === item.has[0].label)
            ? 2
            : 1;
        return {
          ...item,
          rowSpan,
          label: rowSpan === 2 ? item.label || item.has[0].label : item.label,
          /** 合并表头的fixed属性，供后续getStickyStyles布局使用 */
          fixed: item.has.every(column => column.fixed)
            ? item.has[0].fixed
            : undefined,
          get width() {
            return item.has.reduce((a, b) => a + b.width, 0);
          },
        };
      });
    }

    function getFirstToggledColumnIndex() {
      const column = self.columns.find(
        column => !/^__/.test(column.type) && column.toggled,
      );

      return column == null ? null : column.index;
    }

    function getSearchableColumns() {
      return self.columns.filter(column => column.searchable);
    }

    return {
      get columnsData() {
        return getColumnsExceptBuiltinTypes();
      },

      get forms() {
        return getForms();
      },

      get formItems() {
        return getFormItems();
      },

      get searchableColumns() {
        return getSearchableColumns();
      },

      get activedSearchableColumns() {
        return getSearchableColumns().filter(column => column.enableSearch);
      },

      /** 导出excel列（包含breakpoint列） */
      get exportColumns() {
        return getExportedColumns();
      },

      get filteredColumns() {
        return getFilteredColumns();
      },

      get footableColumns() {
        return getFootableColumns();
      },

      get toggableColumns() {
        return getToggableColumns();
      },

      get activeToggaleColumns() {
        return getActiveToggableColumns();
      },

      get someChecked() {
        return !!self.selectedRows.length;
      },

      get allChecked(): boolean {
        return !!(
          self.selectedRows.length ===
            (self as ITableStore).checkableRows.length &&
          (self as ITableStore).checkableRows.length
        );
      },

      isSelected,

      isChildrenSelected,

      get allExpanded() {
        return !!(
          self.expandedRows.length === this.expandableRows.length &&
          this.expandableRows.length
        );
      },

      isExpanded,
      getRowDataId,

      get toggable() {
        return getTogglable();
      },

      get modified() {
        return getModified();
      },

      get modifiedRows() {
        return getModifiedRows();
      },

      get unSelectedRows() {
        return getUnSelectedRows();
      },

      get falttenedRows() {
        return flattenTree<IRow>(self.rows);
      },

      get checkableRows() {
        return flattenTree<IRow>(self.rows).filter(
          (item: IRow) => item.checkable,
        );
      },

      get expandableRows() {
        return self.rows.filter(item => item.expandable);
      },

      get moved() {
        return getMoved();
      },

      get movedRows() {
        return getMovedRows();
      },

      get hoverRow() {
        return getHovedRow();
      },

      /** 已选择item是否达到数量上限, 达到上限，不可操作全选 */
      get isSelectionThresholdReached() {
        const selectedLength = self.data?.selectedItems?.length;
        const maxLength = self.maxKeepItemSelectionLength;

        if (
          typeof self.maxKeepItemSelectionLength === 'number' &&
          self.maxKeepItemSelectionLength !== Infinity &&
          self.maxKeepItemSelectionLength > 0
        ) {
          return true;
        }

        if (!self.data || !self.keepItemSelectionOnPageChange || !maxLength) {
          return false;
        }

        return maxLength <= selectedLength;
      },

      get firstToggledColumnIndex() {
        return getFirstToggledColumnIndex();
      },

      getData,

      get columnGroup() {
        return getColumnGroup();
      },

      getRowById(id: string) {
        return findTree(self.rows, item => item.id === id);
      },

      getItemsByName(name: string): any {
        const rowIndex = parseInt(name, 10);

        // 收集forms
        const forms = this.forms
          .filter(form => form.rowIndex === rowIndex)
          .map(item => item.store);

        // 收集formItems
        const formItems = this.formItems
          .filter(formItem => formItem.rowIndex === rowIndex)
          .map(item => item.formItem);

        // 合并并返回两种类型
        return [...forms, ...formItems];
      },

      // 是否隐藏了某列
      hasColumnHidden() {
        return hasColumnHidden();
      },

      getExpandedRows() {
        const list: Array<IRow> = [];

        eachTree(self.rows, i => {
          if (self.expandedRows.includes(getRowDataId(i as any))) {
            list.push(i as any);
          }
        });

        return list;
      },

      // 表格是否全部渲染完成
      get columnWidthReady() {
        return getFilteredColumns().every(column => column.realWidth);
      },

      // 获取column的sticky粘性样式
      getStickyStyles(column: IColumn, columns: Array<IColumn>) {
        let stickyClassName = '';
        const style: any = {};
        // 默认的内置列操作固定左侧
        const autoFixLeftColumns = ['__checkme', '__dragme', '__expandme'];

        if (
          column.fixed === 'left' ||
          autoFixLeftColumns.includes(column.type)
        ) {
          // 如果当前列是左固定的，则添加is-sticky、is-sticky-left类名
          stickyClassName = 'is-sticky is-sticky-left';
          let index = columns.indexOf(column) - 1;

          if (
            columns
              .slice(index + 2)
              .every(
                col =>
                  !(
                    (col && col.fixed === 'left') ||
                    autoFixLeftColumns.includes(col.type)
                  )
              )
          ) {
            // 如果当前列是左固定的最后一列，则添加is-sticky-last-left类名
            stickyClassName += ' is-sticky-last-left';
          }

          let left = []; // 收集当前列左侧所有左固定的列的宽度，通过CSS变量来获取
          while (index >= 0) {
            const col = columns[index];
            if (
              (col && col.fixed === 'left') ||
              autoFixLeftColumns.includes(col.type)
            ) {
              left.push(`var(--Table-column-${col.index}-width)`);
            }
            index--;
          }
          // 计算当前列left值，左侧所有左固定的列的宽度（CSS变量）之和
          style.left = left.length
            ? left.length === 1
              ? left[0]
              : `calc(${left.join(' + ')})`
            : 0;
        } else if (column.fixed === 'right') {
          // 如果当前列是右固定的，则添加is-sticky、is-sticky-right类名
          stickyClassName = 'is-sticky is-sticky-right';
          let index = columns.indexOf(column) + 1;

          if (columns.slice(0, index - 1).every(col => col.fixed !== 'right')) {
            // 如果当前列是右固定的第一列，则添加is-sticky-first-right类名
            stickyClassName += ' is-sticky-first-right';
          }

          let right = []; // 收集当前列右侧所有右固定的列的宽度，通过CSS变量来获取
          const len = columns.length;
          while (index < len) {
            const col = columns[index];
            if (col && col.fixed === 'right') {
              right.push(`var(--Table-column-${col.index}-width)`);
            }
            index++;
          }
          // 计算当前列right值，右侧所有右固定的列的宽度（CSS变量）之和
          style.right = right.length
            ? right.length === 1
              ? right[0]
              : `calc(${right.join(' + ')})`
            : 0;
        }
        return [style, stickyClassName];
      },

      // 将每一列渲染后的真实宽度realWidth设置到对应`--Table-column-${column.index}-width`变量中
      // 后续计算sticky的left/right就可以通过CSS变量来获取每一列的宽度了
      buildStyles(style: any) {
        style = {...style, '--Table-thead-height': self.theadHeight + 'px'};

        getFilteredColumns().forEach(column => {
          style[`--Table-column-${column.index}-width`] =
            column.realWidth + 'px';
        });

        return style;
      },
    };

  })
  .actions(self => {
    let tableRef: HTMLElement | null = null;

    function setTable(ref: HTMLElement | null) {
      tableRef = ref;
    }

    function getTable() {
      return tableRef;
    }

    function updateColumnsRaw(columns: Array<any>) {
      self.columnsRaw.replace(columns);
    }

    function update(config: Partial<STableStore>) {
      config.primaryField !== void 0 &&
        (self.primaryField = config.primaryField);
      config.valueField !== void 0 &&
        (self.valueField = config.valueField);
      config.selectable !== void 0 && (self.selectable = config.selectable);
      config.columnsTogglable !== void 0 &&
        (self.columnsTogglable = config.columnsTogglable);
      config.toggledColumnsPersistId !== void 0 &&
        (self.toggledColumnsPersistId = config.toggledColumnsPersistId);
      config.columnsTogglerPersistKey !== void 0 &&
        (self.columnsTogglerPersistKey = config.columnsTogglerPersistKey);
      config.draggable !== void 0 && (self.draggable = config.draggable);

      if (typeof config.orderBy === 'string') {
        setOrderByInfo(
          config.orderBy,
          config.orderDir === 'desc' ? 'desc' : 'asc',
        );
      }

      if (config.sortList && Array.isArray(config.sortList)) {
        setSortList(config.sortList);
      }

      // 筛选集合
      config.searchFormData !== void 0 &&
        (self.searchFormData = isObject(config.searchFormData)
          ? config.searchFormData || {}
          : {});

      config.multiple !== void 0 && (self.multiple = config.multiple);
      config.footable !== void 0 && (self.footable = config.footable);
      config.expandConfig !== void 0 &&
        (self.expandConfig = config.expandConfig);
      config.itemCheckableOn !== void 0 &&
        (self.itemCheckableOn = config.itemCheckableOn);
      config.itemDraggableOn !== void 0 &&
        (self.itemDraggableOn = config.itemDraggableOn);
      config.hideCheckToggler !== void 0 &&
        (self.hideCheckToggler = !!config.hideCheckToggler);

      config.combineNum !== void 0 &&
        (self.combineNum = parseInt(config.combineNum as any, 10) || 0);
      config.combineFromIndex !== void 0 &&
        (self.combineFromIndex =
          parseInt(config.combineFromIndex as any, 10) || 0);

      config.maxKeepItemSelectionLength !== void 0 &&
        (self.maxKeepItemSelectionLength = config.maxKeepItemSelectionLength);
      config.keepItemSelectionOnPageChange !== void 0 &&
        (self.keepItemSelectionOnPageChange =
          config.keepItemSelectionOnPageChange);

      typeof config.tableLayout === 'string' &&
        (self.tableLayout = config.tableLayout);

      // 嵌套子表格需要有折叠
      config.subTable !== void 0 && (self.subTable = config.subTable);

      config.showExpansionColumn !== undefined &&
        (self.showExpansionColumn = config.showExpansionColumn);

      if (config.columns && Array.isArray(config.columns)) {
        let columns: Array<SColumn> = config.columns
          .filter(column => column)
          .concat();

        // 更新列顺序，afterCreate生命周期中更新columns不会触发组件的render
        const key = getPersistDataKey();
        const data = localStorage.getItem(key);
        let tableMetaData = null;

        if (data) {
          try {
            tableMetaData = JSON.parse(data);
          } catch (error) {}

          const order = tableMetaData?.columnOrder;

          if (Array.isArray(order) && order.length != 0) {
            columns = sortBy(columns, (item, index) =>
              order.indexOf(item.name || item.label || index),
            );
          }
        }

        updateColumns(columns);
      }

      config.cascadeSelection !== undefined && (self.cascadeSelection = config.cascadeSelection as boolean);
      config.routerType !== undefined && (self.routerType = config.routerType as string);
      config.canAccessSuperData !== undefined && (self.canAccessSuperData = config.canAccessSuperData as boolean);
    }

    function updateColumns(columns: Array<SColumn>) {
      if (columns && Array.isArray(columns)) {
        columns = columns.filter(column => column).concat();

        if (!columns.length) {
          columns.push({
            type: 'text',
            label: '空',
          });
        }

        if (self.showExpansionColumn) {
          columns.unshift({
            type: '__expandme',
            toggable: false,
            className: 'Table-expandCell',
          });
        }

        columns.unshift({
          type: '__checkme',
          fixed: 'left',
          toggable: false,
          className: 'Table-checkCell',
        });

        columns.unshift({
          type: '__dragme',
          toggable: false,
          className: 'Table-dragCell',
        });

        const originColumns = self.columns.concat();
        const ids: Array<any> = [];
        columns = columns.map((item, index) => {
          const origin = item.id
            ? originColumns.find(column => column.id === item.id)
            : originColumns[index];

          let id = origin?.id || guid();

          // 还不知道为何会出现这个，先用这种方式避免 id 重复
          if (ids.includes(id)) {
            id = guid();
          }

          ids.push(id);

          return {
            ...item,
            id,
            index,
            width: origin?.width || 0,
            minWidth: origin?.minWidth || 0,
            realWidth: origin?.realWidth || 0,
            rawIndex: index - PARTITION_INDEX,
            type: item.type || 'plain',
            pristine: item.pristine || item,
            toggled: item.toggled !== false,
            breakpoint: item.breakpoint,
            isPrimary: index === PARTITION_INDEX,
            /** 提前映射变量，方便后续view中使用 */
            label: isPureVariable(item.label)
              ? resolveVariableAndFilter(item.label, self.data)
              : item.label,
            required:
              item.required || hasAbility(item, 'required', self.data, false),
          }
        });
        detach(self.columns);
        self.columns.replace(columns as any);
      }
    }

    // TODO: 非fixed模式貌似可以删掉
    // 1. fixed 模式下，初始化后获取列宽width和最小列宽minWidth
    // 2. column schema设置列宽时，设置对应column列的width
    function initTableWidth() {
      const table = tableRef;
      if (!table) {
        return;
      }
      const thead = table.querySelector(':scope>thead')!;
      // fix: thead在inputTable嵌套子表格时会是undefined
      if (!thead) {
        return;
      }

      // 获取table父元素（容器）的宽度
      const tableWidth = table.parentElement!.offsetWidth;
      let tbody: HTMLElement | null = null;
      const htmls: Array<string> = [];
      const isFixed = self.tableLayout === 'fixed';
      const someSettedWidth = self.columns.some(
        column => column.pristine.width
      );

      const minWidths: {
        [propName: string]: number;
      } = {};

      // fixed 模式需要参考 auto 获得列最小宽度
      if (isFixed) {
        tbody = table.querySelector(':scope>tbody');
        htmls.push(
          `<table style="table-layout:auto!important;width:0!important;min-width:0!important;" class="${table.className}">${thead.outerHTML}</table>`
        );
      }

      // 如果有设置列宽或 fixed 模式，则参考 auto 获得列宽
      if (someSettedWidth || isFixed) {
        htmls.push(
          `<table style="table-layout:auto!important;min-width:${tableWidth}px!important;width:${tableWidth}px!important;" class="${table.className.replace(
            'is-layout-fixed',
            ''
          )}">${thead.outerHTML}${
            tbody ? `<tbody>${tbody.innerHTML}</tbody>` : ''
          }</table>`
        );
      }

      if (!htmls.length) {
        return;
      }

      const div = document.createElement('div');
      div.className = 'amis-scope'; // jssdk 里面 css 会在这一层
      div.style.cssText += `visibility: hidden!important;`;
      div.innerHTML = htmls.join('');
      let ths1: Array<HTMLTableCellElement> = []; // 收集th渲染后的最小宽度minWidth
      let ths2: Array<HTMLTableCellElement> = []; // 收集th渲染后的width

      if (isFixed) {
        ths1 = [].slice.call(
          div.querySelectorAll(
            ':scope>table:first-child>thead>tr>th[data-index]'
          )
        );
      }

      if (someSettedWidth || isFixed) {
        ths2 = [].slice.call(
          div.querySelectorAll(
            ':scope>table:last-child>thead>tr>th[data-index]'
          )
        );
      }

      ths1.forEach(th => {
        th.style.cssText += 'width: 0';
      });
      ths2.forEach(th => {
        const index = parseInt(th.getAttribute('data-index')!, 10);
        const column = self.columns[index];

        th.style.cssText += `${
          typeof column.pristine.width === 'number'
            ? `width: ${column.pristine.width}px;`
            : column.pristine.width
            ? `width: ${column.pristine.width};min-width: ${column.pristine.width};`
            : '' // todo 可能需要让修改过列宽的保持相应宽度，目前这样相当于重置了
        }`;
      });

      document.body.appendChild(div);

      ths1.forEach((th: HTMLTableCellElement) => {
        const index = parseInt(th.getAttribute('data-index')!, 10);
        minWidths[index] = th.clientWidth;
        const column = self.columns[index];
        column.setMinWidth(minWidths[index]); // 设置column的最小宽度minWidth
      });

      ths2.forEach((col: HTMLElement) => {
        const index = parseInt(col.getAttribute('data-index')!, 10);
        const column = self.columns[index];
        // fixed模式或者column schema设置width，则设置column的width
        if (column.pristine.width || isFixed) {
          column.setWidth(
            Math.max(
              typeof column.pristine.width === 'number'
                ? column.pristine.width
                : col.clientWidth, // 处理百分比情况
              minWidths[index] || 0
            )
          );
        }
      });

      document.body.removeChild(div);
    }

    // 同步设置column的realWidth为表头单元格th的渲染后宽度
    function syncTableWidth() {
      const table = tableRef;
      if (!table) {
        return;
      }
      const thead = table.querySelector(':scope>thead') as HTMLElement;
      // fix: thead在inputTable嵌套子表格时会是undefined
      if (!thead) {
        return;
      }

      const cols = [].slice.call(thead.querySelectorAll('tr>th[data-index]'));
      self.theadHeight = thead.offsetHeight;
      cols.forEach((col: HTMLElement) => {
        const index = parseInt(col.getAttribute('data-index')!, 10);
        const column = self.columns[index];
        column.setRealWidth(col.offsetWidth);
      });
    }

    function combineCell(arr: Array<SRow>, keys: Array<string>): Array<SRow> {
      if (!keys.length || !arr.length) {
        return arr;
      }

      const key: string = keys.shift() as string;
      let rowIndex = 0;
      let row = arr[rowIndex];
      row.rowSpans[key] = 1;
      let value = resolveVariable(key, row.data);
      for (let i = 1, len = arr.length; i < len; i++) {
        const current = arr[i];
        if (isEqual(resolveVariable(key, current.data), value)) {
          row.rowSpans[key] += 1;
          current.rowSpans[key] = 0;
        } else {
          if (row.rowSpans[key] > 1) {
            combineCell(arr.slice(rowIndex, i), keys.concat());
          }

          rowIndex = i;
          row = current;
          row.rowSpans[key] = 1;
          value = resolveVariable(key, row.data);
        }
      }

      if (row.rowSpans[key] > 1 && keys.length) {
        combineCell(arr.slice(rowIndex, arr.length), keys.concat());
      }

      return arr;
    }

    function autoCombineCell(
      arr: Array<SRow>,
      columns: Array<IColumn>,
      maxCount: number,
      fromIndex = 0,
    ): Array<SRow> {
      if (!columns.length || !maxCount || !arr.length) {
        return arr;
      }
      // 如果是嵌套模式，通常第一列都是存在差异的，所以从第二列开始。
      fromIndex =
        fromIndex ||
        (arr.some(item => Array.isArray(item.children) && item.children.length)
          ? 1
          : 0);

      const keys: Array<string> = [];

      for (let i = 0; i < columns.length; i++) {
        if (keys.length === maxCount) {
          break;
        }

        const column = columns[i];

        if ('__' === column.type.substring(0, 2)) {
          continue;
        }

        const key = column.name;
        if (!key) {
          break;
        }

        if (fromIndex > 0) {
          fromIndex--;
        } else {
          keys.push(key);
        }
      }

      return combineCell(arr, keys);
    }

    function initChildren(
      children: Array<any>,
      depth: number,
      pindex: number,
      parentId: string,
      path: string = '',
      getEntryId?: (entry: any, index: number) => string
    ): any {
      depth += 1;
      return children.map((item, index) => {
        item = isObject(item)
          ? item
          : {
              item,
            };
        const id = String(
          getEntryId ? getEntryId(item, index) : item.__id ?? guid()
        );

        return {
          // id: String(item && (item as any)[self.primaryField] || `${pindex}-${depth}-${key}`),
          id,
          parentId: String(parentId),
          key: String(`${pindex}-${depth}-${index}`),
          path: `${path}${index}`,
          depth: depth,
          index: index,
          newIndex: index,
          pristine: item,
          data: item,
          rowSpans: {},
          children:
            item && Array.isArray(item.children)
              ? initChildren(
                  item.children,
                  depth,
                  index,
                  id,
                  `${path}${index}.`,
                  getEntryId
                )
              : [],
          // expandable: !!(
          //   (item && Array.isArray(item.children) && item.children.length) ||
          //   (self.footable && self.footableColumns.length)
          expandable: !!(
            (item && Array.isArray(item.children) && item.children.length) ||
            (self.footable && self.footableColumns.length) ||
            self.subTable
          ),
        };
      });
    }

    function initRows(
      rows: Array<any>,
      getEntryId?: (entry: any, index: number) => string,
      reUseRow?: boolean | 'match'
    ) {
      self.selectedRows.clear();
      // self.expandedRows.clear();

      /* 避免输入内容为非数组挂掉 */
      rows = !Array.isArray(rows) ? [] : rows;

      let arr: Array<SRow> = rows.map((item, index) => {
        if (!isObject(item)) {
          item = {
            item,
          };
        }

        let id = String(
          getEntryId ? getEntryId(item, index) : item.__id ?? guid(),
        );
        return {
          // id: getEntryId ? getEntryId(item, key) : String(item && (item as any)[self.primaryField] || `${key}-1-${key}`),
          id,
          key: String(`${index}-1-${index}`),
          depth: 1, // 最大父节点默认为第一层，逐层叠加
          index: index,
          newIndex: index,
          pristine: item,
          path: `${index}`,
          data: item,
          rowSpans: {},
          colSpans: {},
          children:
            item && Array.isArray(item.children)
              ? initChildren(
                  item.children,
                  1,
                  index,
                  id,
                  `${index}.`,
                  getEntryId
                )
              : [],
          // expandable: !!(
          //   (item && Array.isArray(item.children) && item.children.length) ||
          //   (self.footable && self.footableColumns.length)
          expandable: !!(
            (item && Array.isArray(item.children) && item.children.length) ||
            (self.footable && self.footableColumns.length) ||
            self.subTable
          ),
        };
      });

      const customCombine = self.columns.some((column) => Boolean(column.customColSpan) || Boolean(column.customRowSpan));
      if (customCombine) {
        const currentColumns = self.columns.filter((column => (column.type !== "__checkme" && column.type !== "__dragme" && column.type !== "__expandme")));
        self.isMergedCell = true;
        for (let i = 0, len = arr.length; i < len; i++) {
          const row = arr[i] || {};
          currentColumns.forEach((column, index) => {
            const { customColSpan, customRowSpan, name } = column;
            if (name) {
              if (typeof customColSpan === "string" && customColSpan !== "") {
                const colspan = +filter(customColSpan, { record: row?.pristine || {}, rowIndex: i, colIndex: index });
                row.colSpans[name] = colspan;
              } else if (typeof customColSpan === "number" && customColSpan > 0) {
                row.colSpans[name] = Math.min(customColSpan, 1000) ;
              } else {
                row.colSpans[name] = 1;
              }
              if (typeof customRowSpan === "string" && customRowSpan !== "") {
                const rowspan = +filter(customRowSpan, { record: row?.pristine || {}, rowIndex: i, colIndex: index });
                row.rowSpans[name] = rowspan;
              } else if (typeof customRowSpan === "number" && customRowSpan > 0) {
                row.rowSpans[name] = Math.min(customRowSpan, 1000);
              } else {
                row.rowSpans[name] = 1;
              }
            }
          })
        }
      } else if (self.combineNum) {
        self.isMergedCell = true;
        arr = autoCombineCell(
          arr,
          self.columns,
          self.combineNum,
          self.combineFromIndex,
        );
      }

      replaceRow(arr, reUseRow);
      // self.isNested = self.rows.some(item => item.children.length);
      self.isNested = !!(
        self.rows.some(item => item.children.length) || Object.keys(self.subTable || {}).length
      ); // 限定嵌套子表格的条件

      const expand = self.footable && self.footable.expand;
      if (
        expand === 'first' ||
        (self.expandConfig && self.expandConfig.expand === 'first')
      ) {
        self.rows.length && self.expandedRows.push(self.getRowDataId(self.rows[0]));
      } else if (
        (expand === 'all' && !self.footable.accordion) ||
        (self.expandConfig &&
          self.expandConfig.expand === 'all' &&
          !self.expandConfig.accordion)
      ) {
        self.expandedRows.replace(getExpandAllRows(self.rows));
      }

      self.dragging = false;
    }

    // 获取所有层级的子节点id
    function getExpandAllRows(arr: Array<IRow>): string[] {
      return arr.reduce((result: string[], current) => {
        result.push(self.getRowDataId(current));

        if (current.children && current.children.length) {
          result = result.concat(getExpandAllRows(current.children));
        }

        return result;
      }, []);
    }

    // 尽可能的复用 row
    function replaceRow(arr: Array<SRow>, reUseRow?: boolean | 'match'): boolean {
      if (reUseRow === false) {
        self.rows.replace(arr.map(item => Row.create(item)));
        return false;
      }

      // 尽可能寻找相同 row.id 进行替换
      if (reUseRow === 'match') {
        const rows = self.falttenedRows;
        let allMatched = true;
        self.rows.replace(
          arr.map(item => {
            const exist = rows.find(row => row.id === item.id);
            if (exist) {
              exist.replaceWith(item);
              return exist;
            }

            allMatched = false;
            return Row.create(item);
          })
        );
        return allMatched;
      }

      const pool = arr.concat();

      // 把多的删了先
      if (self.rows.length > arr.length) {
        self.rows.splice(arr.length, self.rows.length - arr.length);
      }

      let index = 0;
      const len = self.rows.length;
      while (pool.length) {
        const item = pool.shift()!;

        if (index < len) {
          self.rows[index].replaceWith(item);
        } else {
          const row = Row.create(item);
          self.rows.push(row);
        }
        index++;
      }

      return false;
    }

    function updateSelected(selected: Array<any>, valueField?: string) {
      self.selectedRows.clear();

      eachTree(self.rows, item => {
        if (~selected.indexOf(item.pristine)) {
          self.selectedRows.push(item.id);
        } else if (
          find(
            selected,
            a =>
              a[valueField || 'value'] &&
              a[valueField || 'value'] == item.pristine[valueField || 'value'],
          )
        ) {
          self.selectedRows.push(item.id);
        }
      });

      updateCheckDisable();
    }

    function getSelectedRows() {
      const maxLength = self.maxKeepItemSelectionLength;
      const keep = self.keepItemSelectionOnPageChange;

      const selectedItems = self.data?.selectedItems;

      if (
        keep &&
        maxLength &&
        selectedItems &&
        maxLength >= selectedItems.length
      ) {
        const restCheckableRows = self.checkableRows.filter(
          item => !item.checked,
        );
        const checkableRows = restCheckableRows.filter(
          (item, i) => i < maxLength - selectedItems.length,
        );

        return [...self.selectedRows, ...checkableRows];
      } else {
        return self.checkableRows;
      }
    }

    function toggleAll() {
      if (self.allChecked) {
        self.selectedRows.clear();
      } else {
        self.selectedRows.replace(getSelectedRows());
      }
    }

    // 记录最近一次点击的多选框，主要用于 shift 多选时判断上一个选的是什么
    let lastCheckedRow: any = null;

    function toggle(row: IRow) {
      if (!row.checkable) {
        return;
      }

      lastCheckedRow = row;

      const idx = self.selectedRows.indexOf(row);

      if (self.multiple) {
        ~idx ? self.selectedRows.splice(idx, 1) : self.selectedRows.push(row);
      } else {
        ~idx
          ? self.selectedRows.splice(idx, 1)
          : self.selectedRows.replace([row]);
      }
    }

    function cascadeToggle(row: IRow) {
      if (!row.checkable) {
        return;
      }
      // 首先要计算出来勾选当前元素的父元素
      const selectedParents: IRow[] = [] as any;
      let rowId: any = row.parentId;
      while (rowId) {
        const pRow: IRow = self.getRowById(rowId) as IRow;
        if (pRow) {
          selectedParents.push(pRow)
          rowId = pRow.parentId;
        } else {
          rowId = undefined
        }
      }
      // 其次计算出来勾选节点的所有子元素
      const selectedChildren: IRow[] = flattenTree([row]);
      const filterSelectedChildren = selectedChildren.filter((cRow) => !self.selectedRows.some((row) => row.id === cRow.id))
      if (!row.checked) {
        selectedParents.reverse();
        const filterSelectedParents = selectedParents.filter((pRow) => !self.selectedRows.some((item) => item.id === pRow.id))
        const allSelected: IRow[] = filterSelectedParents.concat(filterSelectedChildren);
        self.selectedRows.push(...allSelected);
      } else if (row.allChecked) {
        let newSelectedRows = self.selectedRows.filter((elem: IRow) => !selectedChildren.some((item: IRow) => item.id === elem.id));
        for(let i = 0; i < selectedParents.length; i++) {
          const currRow = selectedParents[i];
          const pChildren = flattenTree([currRow]).filter((item: IRow) => item.id !== currRow.id)
          const currRowCheckd = pChildren.some((child) => newSelectedRows.some((item) => item.id === child.id));

          if (currRowCheckd) {
            break;
          } else {
            newSelectedRows = newSelectedRows.filter((item) => item.id !== currRow.id);
          }
        }

        if (newSelectedRows.length > 0) {
          self.selectedRows.replace(newSelectedRows);
        } else {
          self.selectedRows.clear();
        }
      } else {
        self.selectedRows.push(...filterSelectedChildren);
      }
    }

    // 按住 shift 的时候点击选项
    function toggleShift(row: IRow) {
      // 如果是同一个或非 multiple 模式下就和不用 shift 一样
      if (!lastCheckedRow || row === lastCheckedRow || !self.multiple) {
        toggle(row);
        return;
      }

      const maxLength = self.maxKeepItemSelectionLength;
      const checkableRows = self.checkableRows;
      const lastCheckedRowIndex = checkableRows.findIndex(
        row => row === lastCheckedRow,
      );
      const rowIndex = checkableRows.findIndex(rowItem => row === rowItem);
      const minIndex =
        lastCheckedRowIndex > rowIndex ? rowIndex : lastCheckedRowIndex;
      const maxIndex =
        lastCheckedRowIndex > rowIndex ? lastCheckedRowIndex : rowIndex;

      const rows = checkableRows.slice(minIndex, maxIndex);
      rows.push(row); // 将当前行也加入进行判断
      for (const rowItem of rows) {
        const idx = self.selectedRows.indexOf(rowItem);
        if (idx === -1) {
          // 如果上一个是选中状态，则将之间的所有 check 都变成可选
          if (lastCheckedRow.checked) {
            if (maxLength) {
              if (self.selectedRows.length < maxLength) {
                self.selectedRows.push(rowItem);
              }
            } else {
              self.selectedRows.push(rowItem);
            }
          }
        } else {
          if (!lastCheckedRow.checked) {
            self.selectedRows.splice(idx, 1);
          }
        }
      }

      lastCheckedRow = row;
    }

    function updateCheckDisable() {
      if (!self.data) {
        return;
      }
      if (self.cascadeSelection) {
        return;
      }
      const maxLength = self.maxKeepItemSelectionLength;
      const selectedItems = self.data.selectedItems || [];

      self.selectedRows.map(item => item.setCheckdisable(false));
      if (maxLength && maxLength <= selectedItems.length) {
        self.unSelectedRows.map(
          item => !item.checked && item.setCheckdisable(true),
        );
      } else {
        self.unSelectedRows.map(
          item => item.checkdisable && item.setCheckdisable(false),
        );
      }
    }

    function clear() {
      self.selectedRows.clear();
    }

    function toggleExpandAll(allExpanded?: boolean) {
      // 如果传入参数则参数为准，否则按当前是否展开项进行切换
      const nextAllExpand = typeof allExpanded === 'boolean'
        ? allExpanded
        : !self.allExpanded

      if (nextAllExpand) {
        self.expandedRows.replace(
          self.rows.filter(item => item.expandable).map(self.getRowDataId),
        );
      } else {
        self.expandedRows.clear();
      }
    }

    function toggleExpanded(row: IRow) {
      const idx = self.expandedRows.indexOf(self.getRowDataId(row));

      if (~idx) {
        self.expandedRows.splice(idx, 1);
      } else if (self.footable && self.footable.accordion) {
        self.expandedRows.replace([self.getRowDataId(row)]);
      } else if (self.expandConfig && self.expandConfig.accordion) {
        let rows = self
          .getExpandedRows()
          .filter(item => item.depth !== row.depth);
        rows.push(row);
        self.expandedRows.replace(rows.map(self.getRowDataId));
      } else {
        self.expandedRows.push(self.getRowDataId(row));
      }
    }

    function collapseAllAtDepth(depth: number) {
      let rows = self.getExpandedRows().filter(item => item.depth !== depth);
      self.expandedRows.replace(rows.map(self.getRowDataId));
    }

    function setOrderByInfo(key: string, direction: 'asc' | 'desc' | '') {
      self.orderBy = key;
      self.orderDir = key ? direction : '';
    }

    function setSortList(orderList: any) {
      self.sortList = orderList;
    }

    function reset() {
      self.rows.forEach(item => item.reset());
      let rows = self.rows.concat();
      eachTree(rows, item => {
        if (item.children) {
          let rows = item.children.concat().sort((a, b) => a.index - b.index);
          rows.forEach(item => item.reset());
          item.children.replace(rows);
        }
      });
      rows.forEach(item => item.reset());
      rows = rows.sort((a, b) => a.index - b.index);
      self.rows.replace(rows);
      self.dragging = false;
    }

    function toggleDragging() {
      self.dragging = !self.dragging;
    }

    function stopDragging() {
      self.dragging = false;
    }

    function exchange(fromIndex: number, toIndex: number, item?: IRow) {
      item = item || self.rows[fromIndex];

      if (item.parentId) {
        const parent: IRow = self.getRowById(item.parentId) as any;
        const offset = parent.children.indexOf(item) - fromIndex;
        toIndex += offset;
        fromIndex += offset;

        const newRows = parent.children.concat();
        newRows.splice(fromIndex, 1);
        newRows.splice(toIndex, 0, item);
        newRows.forEach((item, index) => (item.newIndex = index));
        parent.children.replace(newRows);
        return;
      }

      const newRows = self.rows.concat();
      newRows.splice(fromIndex, 1);
      newRows.splice(toIndex, 0, item);

      newRows.forEach((item, index) => (item.newIndex = index));
      self.rows.replace(newRows);
    }

    /**
     * 前端持久化记录列排序，查询字段，显示列信息
     */
    function persistSaveToggledColumns() {
      const key = getPersistDataKey();
      const toggledColumns = self.activeToggaleColumns;

      localStorage.setItem(
        key,
        JSON.stringify({
          unToggledColumns: self.columnsData
            .filter(item => !toggledColumns.includes(item))
            .map(item => item.name || item.label || item.rawIndex),
          // 列排序，name，label可能不存在
          columnOrder: self.columnsData.map(
            item => item.name || item.label || item.rawIndex,
          ),
          // 已激活的可查询列
          enabledSearchableColumn: self.activedSearchableColumns.map(
            item => item.name,
          ),
        }),
      );
    }

    function addForm(form: IFormStore, rowIndex: number) {
      self.formsRef.push({
        id: form.id,
        rowIndex,
      });
    }

    function addFormItem(formItem: any, rowIndex: number, colIndex?: number) {
      self.formItemsRef.push({
        id: formItem.id,
        rowIndex,
        colIndex,
      });
    }

    function onChildStoreDispose(child: any) {
      // 处理FormStore类型的移除
      if (child.storeType === 'FormStore') {
        const idx = self.formsRef.findIndex(item => item.id === child.id);
        if (idx !== -1) {
          self.formsRef.splice(idx, 1);
        }
      }

      // 处理FormItemStore类型的移除
      if (child.storeType === 'FormItemStore') {
        const idx = self.formItemsRef.findIndex(item => item.id === child.id);
        if (idx !== -1) {
          self.formItemsRef.splice(idx, 1);
        }
      }

      // 调用父类的移除方法
      self.removeChildId && self.removeChildId(child.id);
    }

    function toggleAllColumns() {
      if (self.activeToggaleColumns.length) {
        if (self.activeToggaleColumns.length === self.toggableColumns.length) {
          self.toggableColumns.map(column => column.setToggled(false));
        } else {
          self.toggableColumns.map(column => column.setToggled(true));
        }
      } else {
        // 如果没有一个激活的，那就改成全选
        self.toggableColumns.map(column => column.setToggled(true));
      }
      persistSaveToggledColumns();
    }

    function getPersistDataKey() {
      const columns = self.columnsRaw;
      // issue#1060 优先使用用户配置的key
      if (self.columnsTogglerPersistKey) {
        return self.columnsTogglerPersistKey;
      }

      // 这里的columns使用除了__开头的所有列
      // sort保证存储和读取的key值保持一致
      const toggledColumnsPersistId = self.toggledColumnsPersistId === ''
        ? ''
        : `/${self.toggledColumnsPersistId}`;

      let cacheKey = location.pathname +
        self.path +
        toggledColumnsPersistId +
        sortBy(
          columns.map((item, index) => item.name || item.label || index),
        ).join('-');
      // 如果是hash路由，添加hash到key
      if (self.routerType === 'hash') {
        const cacheData = localStorage.getItem(cacheKey);
        cacheKey = location.pathname + location.hash?.split('?')[0] + '/' + self.path + toggledColumnsPersistId + sortBy(
          columns.map((item, index) => item.name || item.label || index),
        ).join('-');
        if (cacheData) {
          // 为了兼容旧数据
          localStorage.setItem(cacheKey, cacheData);
        }
      }
      return cacheKey;
    }

    function setSearchFormExpanded(value: any) {
      self.searchFormExpanded = !!value;
    }
    function toggleSearchFormExpanded() {
      self.searchFormExpanded = !self.searchFormExpanded;
    }

    return {
      setTable,
      getTable,
      update,
      updateColumns,
      initTableWidth,
      syncTableWidth,
      initRows,
      updateSelected,
      toggleAll,
      getSelectedRows,
      toggle,
      cascadeToggle,
      toggleShift,
      toggleExpandAll,
      toggleExpanded,
      collapseAllAtDepth,
      clear,
      setOrderByInfo,
      setSortList,
      reset,
      toggleDragging,
      stopDragging,
      exchange,
      addForm,
      addFormItem,
      onChildStoreDispose,
      toggleAllColumns,
      persistSaveToggledColumns,
      setSearchFormExpanded,
      toggleSearchFormExpanded,
      updateColumnsRaw,

      // events
      afterCreate() {
        setTimeout(() => {
          if (!isAlive(self)) {
            return;
          }
          const key = getPersistDataKey();
          const data = localStorage.getItem(key);

          if (data) {
            const tableMetaData = JSON.parse(data);
            const unToggledColumns = Array.isArray(tableMetaData.unToggledColumns)
              ? tableMetaData.unToggledColumns
              : [];
            self.toggableColumns.forEach(item =>
              item.setToggled(!unToggledColumns.includes(item.name || item.label || item.rawIndex)),
            );

            self.searchableColumns.forEach(item => {
              item.setEnableSearch(
                !!~(tableMetaData?.enabledSearchableColumn ?? []).indexOf(
                  item.name,
                ),
              );
            });
          }
        }, 200);
      },
    };
  });

export type ITableStore = Instance<typeof TableStore>;
export type STableStore = SnapshotIn<typeof TableStore>;
