import {Instance, types} from 'mobx-state-tree';
import type {IFormItemStore} from './form';
import {iRendererStore} from './iRenderer';
import {getStoreById} from './manager';
import { reaction } from 'mobx';

export const UniqueGroup = types
  .model('UniqueGroup', {
    name: types.identifier,
    itemsRef: types.array(types.string),
  })
  .views(self => ({
    get items() {
      return self.itemsRef.map(id => getStoreById(id) as any as IFormItemStore);
    },
  }))
  .actions(self => ({
    removeItem(item: IFormItemStore) {
      self.itemsRef.replace(self.itemsRef.filter(id => id !== item.id));
    },

    addItem(item: IFormItemStore) {
      self.itemsRef.push(item.id);
    },
  }));

export type IUniqueGroup = typeof UniqueGroup.Type;

export const UniqueStore = iRendererStore
  .named('UniqueStore')
  .props({
    uniques: types.map(UniqueGroup),
    isUniqueStore: true,
  })
  .views(self => ({
    getUniqueGroup(name: string): IUniqueGroup | undefined {
      return self.uniques.get(name) as IUniqueGroup;
    },
  }))
  .actions(self => {
    const disposers = new Map<string, () => void>();

    function bindUniuqueItem(item: IFormItemStore) {
      if (!self.uniques.has(item.name)) {
        self.uniques.put({
          name: item.name,
        });
      }
      let group: IUniqueGroup = self.uniques.get(item.name) as IUniqueGroup;
      group.addItem(item);

      // // 监听 tmpValue 变化
      // const disposer = reaction(
      //   () => item.tmpValue,
      //   () => {
      //     syncUniqueFormItems(item.name);
      //   }
      // )
      // // 保存 disposer 以便后续清理
      // disposers.set(item.id, disposer);

      // 立即同步一次
      // syncUniqueFormItems(item.name);
    }

    function unBindUniuqueItem(item: IFormItemStore) {
      // // 清理监听器
      // const disposer = disposers.get(item.id);
      // if (disposer) {
      //   disposer();
      //   disposers.delete(item.id);
      // }
      // 从组中移除该项
      let group: IUniqueGroup = self.uniques.get(item.name) as IUniqueGroup;
      group.removeItem(item);
      if (!group.items.length) {
        self.uniques.delete(item.name);
      } else {
        // 如果组内还有其他项，则同步一次
        syncUniqueFormItems(item.name);
      }
    }

    function syncUniqueFormItems(name?: string) {
      if (name) {
        // 只同步指定 name 的组
        const group = self.uniques.get(name);
        if (group) {
          group.items.forEach((item: IFormItemStore) => {
            item.syncOptions();
          });
        }
      } else {
        // uniques里存储了所有收集到到的带unique标记的FormItemStore
        self.uniques.forEach((group: IUniqueGroup) => {
          group.items.forEach((item) => {
            item.syncOptions();
          });
        });
      }
    }

    return {
      bindUniuqueItem,
      unBindUniuqueItem,
      syncUniqueFormItems,
    };
  });

export type IUniqueStore = Instance<typeof UniqueStore>;
