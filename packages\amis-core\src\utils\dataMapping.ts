import {PlainObject} from '../types';
import {isPureVariable} from './isPureVariable';
import {resolveVariableAndFilter} from './resolveVariableAndFilter';
import {tokenize} from './tokenize';
import isPlainObject from 'lodash/isPlainObject';
import {createObject, deleteVariable, setVariable} from './object';

/**
 * 解析带有变量标记的字符串，根据数据上下文进行值替换
 *
 * @param {any} value - 需要解析的值，可以是：
 *                - 纯变量字符串（如"$var"）
 *                - 包含变量标记的字符串（如"name:${name}"）
 *                - 任意其他类型值（非字符串类型会原样返回）
 * @param {PlainObject} data - 数据上下文对象，用于变量解析
 * @param {string} [defaultFilter='| raw'] - 默认过滤器
 * @param {boolean} [ignoreIfNotMatch=false] - 当解析失败时：
 *                        true - 返回原始值
 *                        false - 返回undefined
 * @returns {any} 解析后的值，保持原始数据类型
 *
 * @remarks
 * 与resolveVariable函数的主要区别：
 * 1. 变量解析方式：
 *    - resolveMapping支持混合字符串模板（如"name:${name}"）和纯变量（如"$var"）
 *    - resolveVariable仅支持纯变量形式，不支持字符串模板
 * 2. 返回值处理：
 *    - resolveMapping对非字符串类型（对象、数组等）原样返回，不会尝试递归解析
 *    - resolveVariable也不会递归解析对象和数组中的变量，而是直接返回变量值
 * 3. 过滤器支持：
 *    - resolveMapping通过defaultFilter参数统一设置默认过滤器
 *    - resolveVariable需要在变量表达式中显式指定过滤器
 *
 * @example 纯变量解析
 * resolveMapping('$user.name', { user: { name: '张三' } }); // 返回'张三'
 *
 * @example 混合字符串解析
 * resolveMapping('姓名:${name}', { name: '李四' }); // 返回'姓名:李四'
 *
 * @example 非字符串类型处理
 * resolveMapping({ key: '$user.age' }, { user: { age: 18 } }); // 返回原对象 { key: '$user.age' }
 *
 * @example 数组变量解析
 * resolveMapping(['$a', 'text_$b'], { a: 1, b: 2 }); // 返回原数组 ['$a', 'text_$b']
 *
 * @example 多级嵌套对象
 * resolveMapping('$user.contact.address.city', { user: { contact: { address: { city: '北京' } } } }); // 返回'北京'
 *
 * @example 带过滤器格式
 * resolveMapping('$price|number', { price: 100 }, '| number'); // 返回'10,000'
 *
 * @example 特殊字符处理
 * resolveMapping('含有$符号的非变量', {}); // 返回原始字符串'含有$符号的非变量'
 *
 * @example 变量缺失处理
 * resolveMapping('$nonExistVar', {}, '| raw', true); // 返回'$nonExistVar'
 * resolveMapping('$nonExistVar', {}, '| raw', false); // 返回''
 * resolveMapping('a $nonExistVar', {}, '| raw', true); // 返回'a '
 * resolveMapping('a $nonExistVar', {}, '| raw', false); // 返回'a '
 *
 * @note 注意：此函数只处理字符串类型的变量解析，对于对象和数组类型会原样返回。如需处理对象或数组中的变量，请使用dataMapping函数。
 */
export function resolveMapping(
  value: any,
  data: PlainObject,
  defaultFilter: string = '| raw',
  ignoreIfNotMatch: boolean = false
) {
  const result =
    typeof value === 'string' && isPureVariable(value)
      ? resolveVariableAndFilter(value, data, defaultFilter, () => '')
      : typeof value === 'string' && ~value.indexOf('$')
      ? tokenize(value, data, defaultFilter)
      : value;

  if (ignoreIfNotMatch && (result == null || result === '')) {
    return value;
  }

  return result;
}

/**
 * 遍历对象，对每个字符串类型的属性值进行数据映射
 *
 * @param {PlainObject} value - 要映射的对象，只处理其中的字符串类型属性值
 * @param {PlainObject} data - 数据上下文对象，用于变量解析
 * @returns {PlainObject} 返回处理后的对象，保持原对象结构，不会修改原始对象
 *
 * @example 基本用法
 * resolveMappingObject({ name: '$user.name', age: 18 }, { user: { name: '张三' } }); // 返回 { name: '张三', age: 18 }
 *
 * @example 多层嵌套对象
 * resolveMappingObject({ user: { name: '$name' }, id: '$id' }, { name: '李四', id: 1 }); // 返回 { user: { name: '$name' }, id: 1 }
 *
 * @example 非字符串属性
 * resolveMappingObject({ name: '$user.name', age: 18, active: true }, { user: { name: '王五' } }); // 返回 { name: '王五', age: 18, active: true }
 *
 * @note 注意：此函数只处理对象中的字符串类型属性值，对于非字符串类型的属性值会原样返回。每个字符串属性值都会通过resolveMapping函数进行处理。修改后的版本不会修改原始对象。
 */
export function resolveMappingObject(value: PlainObject, data: PlainObject) {
  const result = {...value}; // 浅拷贝避免修改原始对象
  for (const key of Object.keys(result)) {
    if (typeof result[key] === 'string') {
      result[key] = resolveMapping(result[key], data);
    }
  }
  return result;
}

/**
 * 数据映射函数，用于将源数据按照指定的映射规则转换成目标数据结构
 *
 * @param {any} to - 目标数据结构模板，支持以下类型：
 *                 - 数组：递归处理每个元素
 *                 - 字符串：作为变量表达式解析
 *                 - 对象：按照特定规则处理
 * @param {PlainObject} from - 源数据对象，默认为空对象
 * @param {boolean|Function} ignoreFunction - 忽略函数处理的配置：
 *                                        - true：所有函数都不执行
 *                                        - false：所有函数都执行
 *                                        - Function：自定义判断函数，返回true时忽略处理
 *                                        例如：(key, value) => key.startsWith('_')
 *                                        可用于忽略以下划线开头的字段的函数处理
 * @param {boolean} convertKeyToPath - 是否将键名转换为路径格式，用于支持多层级的键名设置
 *                                  当设置为true时，"user.name"会被解析为{user: {name: ...}}
 *                                  当设置为false时，"user.name"会作为一个完整的键名
 * @param {boolean} ignoreIfNotMatch - 当变量解析失败时的处理：
 *                                  true - 保留原始值，例如："$nonExist"保持不变
 *                                  false - 返回undefined，用于清除无效的映射
 * @returns {any} 映射转换后的数据，返回值类型取决于输入参数to的类型：
 *              - 当to为数组时，返回数组
 *              - 当to为字符串时，返回解析后的值（可能是字符串、数字、布尔值等）
 *              - 当to为对象时，返回处理后的对象
 *              - 当to为函数且不被忽略时，返回函数执行结果
 *              - 其他类型原样返回
 *
 * @example 基本用法
 * dataMapping({name: '$user.name'}, {user: {name: '张三'}}); // 返回 {name: '张三'}
 *
 * @example 数组映射
 * dataMapping(['$items[0]', '$items[1]'], {items: ['a', 'b']}); // 返回 ['a', 'b']
 *
 * @example 对象映射（使用$符号前缀的特殊处理）
 * dataMapping({
 *   '$rows': {
 *     id: '$id',
 *     name: '$name'
 *   }
 * }, {
 *   rows: [{id: 1, name: '张三'}, {id: 2, name: '李四'}]
 * }); // 返回 [{id: 1, name: '张三'}, {id: 2, name: '李四'}]
 *
 * @example 特殊字符'&'的使用
 * dataMapping({'&': '$$'}, {x: 1, y: 2}); // 返回整个数据源 {x: 1, y: 2}
 *
 * @example 函数值处理（使用ignoreFunction控制）
 * // 默认执行所有函数
 * dataMapping({time: () => Date.now()}, {}); // 返回 {time: 1234567890}
 * // 使用函数过滤器忽略特定函数
 * dataMapping({time: () => Date.now(), _skip: () => 'hidden'}, {}, (key) => key.startsWith('_'));
 * // 返回 {time: 1234567890, _skip: () => 'hidden'}
 *
 * @example 键名路径转换（convertKeyToPath的影响）
 * // convertKeyToPath = true
 * dataMapping({"user.name": "$name"}, {name: "张三"}, false, true);
 * // 返回 {user: {name: "张三"}}
 * // convertKeyToPath = false
 * dataMapping({"user.name": "$name"}, {name: "张三"}, false, false);
 * // 返回 {"user.name": "张三"}
 *
 * @example 变量解析失败处理（ignoreIfNotMatch的影响）
 * // ignoreIfNotMatch = true
 * dataMapping({name: "$notExist"}, {}, false, false, true);
 * // 返回 {name: "$notExist"}
 * // ignoreIfNotMatch = false
 * dataMapping({name: "$notExist"}, {}, false, false, false);
 * // 返回 {name: undefined}
 *
 * @example 边界条件处理
 * // 空对象处理
 * dataMapping({}, {a: 1}); // 返回 {}
 * // 空数组处理
 * dataMapping([], {a: 1}); // 返回 []
 * // 非字符串、非对象、非数组输入
 * dataMapping(123, {a: 1}); // 返回 123
 * dataMapping(true, {a: 1}); // 返回 true
 * dataMapping(null, {a: 1}); // 返回 null
 *
 * @note 注意事项
 * 1. 当to参数为对象且包含'&'键时，会特殊处理整个数据源
 * 2. 当to参数为对象且键以'$'开头时，会特殊处理数组映射
 * 3. 字符串中的'$$'会被解析为整个数据源
 * 4. 函数执行时会将from参数作为上下文传入
 * 5. 当变量解析失败且ignoreIfNotMatch为false时，字符串变量会返回空字符串而非undefined
 */
export function dataMapping(
  to: any,
  from: PlainObject = {},
  ignoreFunction: boolean | ((key: string, value: any) => boolean) = false,
  convertKeyToPath?: boolean,
  ignoreIfNotMatch = false
): any {
  if (Array.isArray(to)) {
    return to.map(item =>
      dataMapping(
        item,
        from,
        ignoreFunction,
        convertKeyToPath,
        ignoreIfNotMatch
      )
    );
  } else if (typeof to === 'string') {
    return resolveMapping(to, from, undefined, ignoreIfNotMatch);
  } else if (!isPlainObject(to)) {
    return to;
  }

  let ret = {};
  const keys = Object.keys(to);

  if (keys.length === 1 && keys[0][0] === '$' && isPlainObject(to[keys[0]])) {
    // from[keys[0].substring(1)] &&
    // Array.isArray(from[keys[0].substring(1)])
    // 支持只取数组中的部分值这个需求
    // 如:
    // data: {
    //   items: {
    //     '$rows': {
    //        id: '$id',
    //        forum_id: '$forum_id'
    //      }
    //   }
    // }
    const left = resolveMapping(keys[0], from, '| raw');
    if (!Array.isArray(left) && ignoreIfNotMatch) {
      (ret as PlainObject)[keys[0]] = to[keys[0]];
    } else {
      const arr = Array.isArray(left) ? left : [];
      const mapping = to[keys[0]];

      ret = arr.map((raw: object) =>
        dataMapping(
          mapping,
          createObject(from, {
            item: raw,
            ...raw
          }),
          ignoreFunction,
          convertKeyToPath,
          ignoreIfNotMatch
        )
      );
    }
  } else {
    Object.keys(to).forEach(key => {
      const value = to[key];
      let keys: Array<string>;

      if (typeof ignoreFunction === 'function' && ignoreFunction(key, value)) {
        // 如果被ignore，不做数据映射处理。
        setVariable(ret, key, value, convertKeyToPath);
      } else if (key === '&' && value === '$$') {
        ret = {
          ...ret,
          ...from
        };
      } else if (key === '&') {
        const v =
          isPlainObject(value) &&
          (keys = Object.keys(value)) &&
          keys.length === 1 &&
          from[keys[0].substring(1)] &&
          Array.isArray(from[keys[0].substring(1)])
            ? from[keys[0].substring(1)].map((raw: object) =>
                dataMapping(
                  value[keys[0]],
                  createObject(from, raw),
                  ignoreFunction,
                  convertKeyToPath,
                  ignoreIfNotMatch
                )
              )
            : resolveMapping(value, from, undefined, ignoreIfNotMatch);

        if (Array.isArray(v) || typeof v === 'string') {
          ret = v;
        } else if (typeof v === 'function') {
          ret = {
            ...ret,
            ...v(from)
          };
        } else {
          ret = {
            ...ret,
            ...v
          };
        }
      } else if (value === '$$') {
        setVariable(ret, key, from, convertKeyToPath);
      } else if (value && value[0] === '$') {
        const v = resolveMapping(value, from, undefined, ignoreIfNotMatch);
        setVariable(ret, key, v, convertKeyToPath);

        if (v === '__undefined') {
          deleteVariable(ret, key);
        }
      } else if (isPlainObject(value) || Array.isArray(value)) {
        setVariable(
          ret,
          key,
          dataMapping(
            value,
            from,
            ignoreFunction,
            convertKeyToPath,
            ignoreIfNotMatch
          ),
          convertKeyToPath
        );
      } else if (typeof value == 'string' && ~value.indexOf('$')) {
        setVariable(
          ret,
          key,
          resolveMapping(value, from, undefined, ignoreIfNotMatch),
          convertKeyToPath
        );
      } else if (typeof value === 'function' && ignoreFunction !== true) {
        setVariable(ret, key, value(from), convertKeyToPath);
      } else {
        setVariable(ret, key, value, convertKeyToPath);

        if (value === '__undefined') {
          deleteVariable(ret, key);
        }
      }
    });
  }

  return ret;
}
