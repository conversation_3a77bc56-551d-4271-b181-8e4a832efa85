import {evalExpression, filter} from './tpl';
import {PlainObject} from '../types';
import {injectPropsToObject, mapObject} from './helper';
import isPlainObject from 'lodash/isPlainObject';
import cx from 'classnames';

/**
 * 处理 Props 数据，提取并计算带有特定后缀的属性值。主要处理以下几种情况：
 * 1. xxxOn：将字符串表达式计算为布尔值或从 props 中获取函数
 * 2. xxxExpr：将字符串模板编译为实际的值
 * 3. xxxClassName：处理类名表达式对象，生成最终类名和原始对象
 *
 * 属性名转换规则：
 * - xxxOn → xxx
 * - xxxExpr → xxx
 * - xxxClassName → xxxClassName + xxxClassNameRaw
 *
 * 过滤规则：
 * - 只处理非空字符串值（空字符串、null、undefined、数字、布尔值会被忽略）
 * - 支持黑名单过滤，默认排除 ['addOn', 'ref']
 * - 支持 __props 语法注入外部属性
 *
 * @param schema - 包含属性的对象
 * @param data - 用于表达式计算的数据对象
 * @param blackList - 需要排除的属性名列表，默认为 ['addOn', 'ref']
 * @param props - 额外的属性对象，用于注入到计算上下文中
 * @returns 返回一个新对象，包含所有计算后的表达式属性
 *
 * @example
 * // 1. 处理事件处理函数（xxxOn）
 * const schema = {
 *   submitOn: "click",        // 表达式计算：data 中无 click 变量 → false
 *   cancelOn: "canCancel"     // 表达式计算：data.canCancel → true
 * };
 * const data = { canCancel: true };
 * getExprProperties(schema, data);
 * // => { submit: false, cancel: true }
 *
 * // 2. 处理模板表达式（xxxExpr）
 * const schema = {
 *   titleExpr: "${title}",           // 模板渲染：${title} → "Welcome"
 *   contentExpr: "Hello ${name}"     // 模板渲染：Hello ${name} → "Hello John"
 * };
 * const data = { title: "Welcome", name: "John" };
 * getExprProperties(schema, data);
 * // => { title: "Welcome", content: "Hello John" }
 *
 * // 3. 处理类名表达式（xxxClassName）
 * const schema = {
 *   wrapperClassName: {
 *     "is-active": "${active}",      // 条件类名：active=true → 包含此类名
 *     "is-disabled": "${disabled}"   // 条件类名：disabled=false → 不包含此类名
 *   }
 * };
 * const data = { active: true, disabled: false };
 * getExprProperties(schema, data);
 * // => {
 * //   wrapperClassName: "is-active",
 * //   wrapperClassNameRaw: { "is-active": "${active}", "is-disabled": "${disabled}" }
 * // }
 *
 * // 4. Props 注入处理
 * const schema = {
 *   submitOn: "__props.submit",      // 从 props 中获取 submit 函数
 *   cancelOn: "__props.cancel"       // 从 props 中获取 cancel 函数
 * };
 * const props = {
 *   submit: () => "submit action",
 *   cancel: () => "cancel action"
 * };
 * getExprProperties(schema, {}, undefined, props);
 * // => { submit: [Function], cancel: [Function] }
 *
 * // 5. 黑名单过滤
 * const schema = {
 *   submitOn: "click",
 *   addOn: "something",              // 默认黑名单，会被过滤
 *   customOn: "action"
 * };
 * getExprProperties(schema, {}, ['submitOn']);  // 自定义黑名单
 * // => { custom: false }  // submitOn 被过滤，addOn 默认被过滤
 *
 * // 6. 边界值处理（这些都会被忽略）
 * const schema = {
 *   emptyOn: "",                     // 空字符串 → 忽略
 *   nullExpr: null,                  // null → 忽略
 *   numberExpr: 123,                 // 数字 → 忽略
 *   booleanExpr: true                // 布尔值 → 忽略
 * };
 * getExprProperties(schema, {});
 * // => {}  // 所有属性都被过滤
 */
export function getExprProperties(
  schema: PlainObject,
  data: object = {},
  blackList: Array<string> = ['addOn', 'ref'],
  props?: any
): PlainObject {
  const exprProps: PlainObject = {};
  let ctx: any = null;

  Object.getOwnPropertyNames(schema).forEach(key => {
    if (blackList && ~blackList.indexOf(key)) {
      return;
    }

    let parts = /^(.*)(On|Expr|(?:c|C)lassName)(Raw)?$/.exec(key);
    let value: any = schema[key];

    if (
      value &&
      typeof value === 'string' &&
      parts?.[1] &&
      (parts[2] === 'On' || parts[2] === 'Expr')
    ) {
      key = parts[1];

      if (parts[2] === 'On' || parts[2] === 'Expr') {
        if (
          !ctx &&
          props &&
          typeof value === 'string' &&
          ~value.indexOf('__props')
        ) {
          ctx = injectPropsToObject(data, {
            __props: props
          });
        }

        if (parts[2] === 'On') {
          value = props?.[key] || evalExpression(value, ctx || data);
        } else {
          value = filter(value, ctx || data);
        }
      }

      exprProps[key] = value;
    } else if (
      value &&
      isPlainObject(value) &&
      (parts?.[2] === 'className' || parts?.[2] === 'ClassName')
    ) {
      key = parts[1] + parts[2];
      exprProps[`${key}Raw`] = value;
      exprProps[key] = cx(
        mapObject(value, (value: any) =>
          typeof value === 'string' ? evalExpression(value, data) : value
        )
      );
    }
  });

  return exprProps;
}

export default getExprProperties;
