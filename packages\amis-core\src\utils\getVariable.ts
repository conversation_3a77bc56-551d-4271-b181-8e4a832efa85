import {keyToPath} from './keyToPath';

/**
 * 从对象中获取指定路径的值
 *
 * @param data - 数据对象
 * @param key - 访问路径，支持点号分隔的路径，如 'a.b.c'
 * @param canAccessSuper - 是否可以访问原型链上的属性，默认为 true
 * @returns 获取到的值，如果路径不存在则返回 undefined
 *
 * @example
 * // 简单键值访问
 * getVariable({foo: 'bar'}, 'foo') // 返回 'bar'
 *
 * // 点号分隔的路径访问
 * getVariable({a: {b: {c: 1}}}, 'a.b.c') // 返回 1
 *
 * // 访问原型链上的属性
 * const proto = {inheritedProp: 'value'};
 * const obj = Object.create(proto);
 * getVariable(obj, 'inheritedProp', true) // 返回 'value'
 * getVariable(obj, 'inheritedProp', false) // 返回 undefined
 */
export function getVariable(
  data: {[propName: string]: any},
  key: string | undefined,
  canAccessSuper: boolean = true
): any {
  if (!data || !key || typeof data !== 'object') {
    return undefined;
  } else if (canAccessSuper ? key in data : data.hasOwnProperty(key)) {
    return data[key];
  }

  return keyToPath(key).reduce(
    (obj, key) =>
      obj &&
      typeof obj === 'object' &&
      (canAccessSuper ? key in obj : obj.hasOwnProperty(key))
        ? obj[key]
        : undefined,
    data
  );
}
