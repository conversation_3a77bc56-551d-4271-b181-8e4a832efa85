import isPlainObject, { hasOwnProperty } from 'lodash/isPlainObject';
import isEqual from 'lodash/isEqual';
import isNaN from 'lodash/isNaN';
import uniq from 'lodash/uniq';
import last from 'lodash/last';
import merge from 'lodash/merge';
import { Schema, PlainObject, FunctionPropertyNames } from '../types';
import { evalExpression, filter } from './tpl';
import qs from 'qs';
import { IIRendererStore } from '../store';
import { IFormStore } from '../store/form';
import { autobindMethod } from './autobind';
import {
  isPureVariable,
  resolveVariable,
  resolveVariableAndFilter,
} from './tpl-builtin';
import { isObservable, isObservableArray } from 'mobx';
import {
  cloneObject,
  createObject,
  deleteVariable,
  extendObject,
  isObject,
  setVariable,
} from './object';
import { string2regExp } from './string2regExp';
import { getVariable } from './getVariable';
import { keyToPath } from './keyToPath';

export {
  createObject,
  cloneObject,
  isObject,
  string2regExp,
  getVariable,
  setVariable,
  deleteVariable,
  keyToPath,
  extendObject,
};

export function preventDefault(event: TouchEvent | Event): void {
  if (typeof event.cancelable !== 'boolean' || event.cancelable) {
    event.preventDefault();
  }
}

export function isMobile() {
  return (window as any).matchMedia?.('(max-width: 768px)').matches;
}

export function range(num: number, min: number, max: number): number {
  return Math.min(Math.max(num, min), max);
}

/**
 * 给目标对象添加其他属性，可读取但是不会被遍历。
 * @param target
 * @param props
 */
export function injectPropsToObject(target: any, props: any) {
  const sup = Object.create(target.__super || null);
  Object.keys(props).forEach(key => (sup[key] = props[key]));
  const result = Object.create(sup);
  Object.keys(target).forEach(key => (result[key] = target[key]));
  return result;
}

/**
 * 检测父级数据中与当前组件相关的字段是否发生了变化
 *
 * 该函数用于智能数据同步机制，只检查与当前组件相关的字段变化，
 * 避免因为父级数据的无关变化而触发不必要的组件更新。
 *
 * @param data 当前的父级数据
 * @param prevData 之前的父级数据
 * @param store 当前组件的store实例，用于确定需要检查的字段
 * @returns {boolean} 如果相关字段发生变化返回true，否则返回false
 *
 * @example
 * // FormStore场景：检查表单项字段和store.data中的所有字段（去重后）
 * const formStore = {
 *   storeType: 'FormStore',
 *   items: [
 *     { name: 'userName' },
 *     { name: 'userAge' }
 *   ],
 *   data: { userName: 'John', userAge: 25, extraField: 'extra' }
 * };
 *
 * const currentData = { userName: 'Jane', userAge: 25, extraField: 'extra', otherField: 'changed' };
 * const prevData = { userName: 'John', userAge: 25, extraField: 'extra', otherField: 'original' };
 *
 * // 返回 true，因为 userName 发生了变化
 * // 检查的字段：['userName', 'userAge', 'extraField']（去重后）
 * isSuperDataModified(currentData, prevData, formStore);
 *
 * @example
 * // 普通Store场景：检查store.data中所有字段
 * const normalStore = {
 *   storeType: 'ServiceStore',
 *   data: { field1: 'value1', field2: 'value2' }
 * };
 *
 * const currentData = { field1: 'newValue', field2: 'value2', otherField: 'changed' };
 * const prevData = { field1: 'value1', field2: 'value2', otherField: 'original' };
 *
 * // 返回 true，因为 field1 发生了变化
 * // 检查的字段：['field1', 'field2']
 * isSuperDataModified(currentData, prevData, normalStore);
 */
export function isSuperDataModified(
  data: any,
  prevData: any,
  store: IIRendererStore,
) {
  let keys: Array<string> = [];

  if (store && store.storeType === 'FormStore') {
    keys = uniq(
      (store as IFormStore).items
        .map(item => `${item.name}`.replace(/\..*$/, ''))
        .concat(Object.keys(store.data)),
    );
  } else {
    keys = Object.keys(store.data);
  }

  if (Array.isArray(keys) && keys.length) {
    return keys.some(key => data[key] !== prevData[key]);
  }

  return false;
}

/**
 * 从父级数据源同步数据到当前组件的数据对象中
 *
 * 该函数用于实现数据链中的智能数据同步机制，根据组件类型和配置，
 * 选择性地从父级数据源同步特定字段的变化到当前组件的数据中。
 *
 * @param data 当前组件的数据对象
 * @param superObject 当前的父级数据对象
 * @param prevSuperObject 之前的父级数据对象，用于检测变化
 * @param store 当前组件的store实例，用于确定同步策略
 * @param force 是否强制同步所有字段，默认为false
 * @returns {object} 同步后的新数据对象
 *
 * @description
 * 同步策略：
 * 1. FormStore场景：同步表单项字段 + 当前数据中已存在的所有字段
 * 2. 普通Store + force=true：同步当前数据中已存在的所有字段
 * 3. 普通Store + force=false：不同步任何字段
 *
 * 同步条件：只有当字段在父级数据中发生变化时才会被同步
 * - 即使子级数据与父级数据不同，如果父级数据本身没有变化，也不会同步
 * - 这样可以避免覆盖子组件的本地修改，只在父级数据真正变化时才进行同步
 *
 * @example
 * // FormStore场景：同步表单项字段和当前数据中已存在的字段
 * const formStore = {
 *   storeType: 'FormStore',
 *   items: [
 *     { name: 'userName' },
 *     { name: 'userAge' }
 *   ]
 * };
 *
 * const currentData = { userName: 'John', userAge: 25, localState: 'local' };
 * const superData = { userName: 'Jane', userAge: 30, localState: 'newLocal', globalState: 'global' };
 * const prevSuperData = { userName: 'John', userAge: 25, localState: 'local', globalState: 'global' };
 *
 * const result = syncDataFromSuper(currentData, superData, prevSuperData, formStore, false);
 * // 结果: { userName: 'Jane', userAge: 30, localState: 'newLocal' }
 * // 同步了表单项字段 userName、userAge 和当前数据中已存在的字段 localState
 * // globalState 不会被同步，因为它不在当前数据中，也不是表单项字段
 *
 * @example
 * // 普通Store场景：force=true时同步当前数据中已存在的字段
 * const normalStore = { storeType: 'ServiceStore' };
 * const currentData = { field1: 'value1', field2: 'value2' };
 * const superData = { field1: 'newValue1', field3: 'value3' };
 * const prevSuperData = { field1: 'value1', field3: 'oldValue3' };
 *
 * const result = syncDataFromSuper(currentData, superData, prevSuperData, normalStore, true);
 * // 结果: { field1: 'newValue1', field2: 'value2' }
 * // field1 被同步（因为在当前数据中存在且发生了变化）
 * // field2 保持不变
 * // field3 不会被同步（因为不在当前数据中）
 *
 * @example
 * // 普通Store场景：force=false时不同步任何字段
 * const normalStore = { storeType: 'ServiceStore' };
 * const currentData = { field1: 'value1', field2: 'value2' };
 * const superData = { field1: 'newValue1', field2: 'newValue2' };
 * const prevSuperData = { field1: 'value1', field2: 'value2' };
 *
 * const result = syncDataFromSuper(currentData, superData, prevSuperData, normalStore, false);
 * // 结果: { field1: 'value1', field2: 'value2' }
 * // 没有字段被同步，因为 force=false 且不是 FormStore
 */
export function syncDataFromSuper(
  data: any,
  superObject: any,
  prevSuperObject: any,
  store: IIRendererStore,
  force: boolean,
) {
  const obj = {
    ...data,
  };

  let keys: Array<string> = [];

  // 如果是 form store，则从父级同步 formItem 种东西。
  if (store && store.storeType === 'FormStore') {
    keys = uniq(
      (store as IFormStore).items
        .map(item => `${item.name}`.replace(/\..*$/, ''))
        .concat(Object.keys(obj)),
    );
  } else if (force) {
    keys = Object.keys(obj);
  }

  if (superObject || prevSuperObject) {
    keys.forEach(key => {
      if (!key) {
        return;
      }

      if (
        ((superObject && typeof superObject[key] !== 'undefined') ||
          (prevSuperObject && typeof prevSuperObject[key] !== 'undefined')) &&
        ((prevSuperObject && !superObject) ||
          (!prevSuperObject && superObject) ||
          prevSuperObject[key] !== superObject[key])
      ) {
        obj[key] = superObject[key];
      }
    });
  }

  return obj;
}

/**
 * 生成 8 位随机数字。
 *
 * @return {string} 8位随机数字
 */
export function guid() {
  function s4() {
    return Math.floor((1 + Math.random()) * 0x10000)
      .toString(16)
      .substring(1);
  }
  return s4() + s4() + s4();
}

export function findIndex(
  arr: Array<any>,
  detect: (item?: any, index?: number) => boolean,
) {
  for (let i = 0, len = arr.length; i < len; i++) {
    if (detect(arr[i], i)) {
      return i;
    }
  }

  return -1;
}

export function hasOwnPropertyInPath(
  data: { [propName: string]: any },
  key: string,
): boolean {
  const parts = keyToPath(key);

  while (parts.length) {
    let key = parts.shift() as string;
    if (!isObject(data) || !data.hasOwnProperty(key)) {
      return false;
    }

    data = data[key];
  }

  return true;
}

export function noop() { }

export function anyChanged(
  attrs: string | Array<string>,
  from: { [propName: string]: any },
  to: { [propName: string]: any },
  strictMode: boolean = true,
): boolean {
  return (typeof attrs === 'string' ? attrs.split(/\s*,\s*/) : attrs).some(
    key => (strictMode ? from[key] !== to[key] : from[key] != to[key]),
  );
}
type Mutable<T> = {
  -readonly [k in keyof T]: T[k];
};
/**
 * 当属性发生变化时执行回调，类似 react.useEffect
 * @param attrs 需要对比的属性
 * @param origin 原数据
 * @param data 最新数据
 * @param effect 属性存在变化时出发的回调
 * @param strictMode 是否强对比，默认 true
 */
export function changedEffect<T extends Record<string, any>>(
  attrs: string | Array<string>,
  origin: T,
  data: T,
  effect: (changes: Partial<Mutable<T>>) => void,
  strictMode: boolean = true
) {
  const changes: Partial<T> = {};
  const keys =
    typeof attrs === 'string'
      ? attrs.split(',').map(item => item.trim())
      : attrs;

  keys.forEach(key => {
    if (
      strictMode ? !Object.is(origin[key], data[key]) : origin[key] != data[key]
    ) {
      (changes as any)[key] = data[key];
    }
  });

  Object.keys(changes).length && effect(changes);
}

export function rmUndefined(obj: PlainObject) {
  const newObj: PlainObject = {};

  if (typeof obj !== 'object') {
    return obj;
  }

  const keys = Object.keys(obj);
  keys.forEach(key => {
    if (obj[key] !== undefined) {
      newObj[key] = obj[key];
    }
  });

  return newObj;
}

function is(x: any, y: any): boolean {
  // SameValue algorithm
  if (x === y) { // Steps 1-5, 7-10
    // Steps 6.b-6.e: +0 != -0
    // Added the nonzero y check to make Flow happy, but it is redundant
    return x !== 0 || y !== 0 || 1 / x === 1 / y;
  } else {
    // Step 6.a: NaN == NaN
    return x !== x && y !== y;
  }
}

// react PureComponent 浅对比逻辑
export function shallowEqual(objA: any, objB: any): boolean {
  // 判断是否相等
  if (is(objA, objB)) {
    return true;
  }

  // 不是对象直接返回fasle
  if (
    typeof objA !== 'object' ||
    objA === null ||
    typeof objB !== 'object' ||
    objB === null
  ) {
    return false;
  }

  const keysA = Object.keys(objA);
  const keysB = Object.keys(objB);

  // 先比较长度
  if (keysA.length !== keysB.length) {
    return false;
  }

  // 再比较值
  for (let i = 0; i < keysA.length; i++) {
    if (
      !hasOwnProperty.call(objB, keysA[i]) ||
      !is(objA[keysA[i]], objB[keysA[i]])
    ) {
      return false;
    }
  }

  return true;
}

// 判断是否为合法的WeakMap键
function isValidWeakMapKey(key: any) {
  return (
    key !== null &&
    typeof key === 'object'
  );
}

/**
 * 深度比较两个对象是否发生变化，支持数组、对象、NaN等多种数据类型的比较。
 * 函数名称里的Shallow指的是不比较__super属性。
 *
 * @param prev 原始对象
 * @param next 新对象
 * @param strictMode 是否使用严格比较模式（默认为true）。
 *                  true: 使用 === 进行比较
 *                  false: 使用 == 进行比较，会进行类型转换
 * @param ignoreUndefined 是否忽略undefined值的比较（默认为false）
 * @param stack 用于内部递归调用时防止循环引用导致死循环的对象栈，外部调用时无需传入该参数
 * @returns 如果对象发生变化则返回true，否则返回false
 *
 * @example
 * // 基本对象比较
 * isObjectShallowModified({a: 1}, {a: 1}) // false
 * isObjectShallowModified({a: 1}, {a: 2}) // true
 *
 * // 数组比较
 * isObjectShallowModified([1, 2], [1, 2]) // false
 * isObjectShallowModified([1, 2], [1, 3]) // true
 *
 * // NaN值比较
 * isObjectShallowModified({a: NaN}, {a: NaN}) // false
 *
 * // 非严格模式比较
 * isObjectShallowModified({a: '1'}, {a: 1}, false) // false
 *
 * // 忽略undefined
 * isObjectShallowModified({a: 1, b: undefined}, {a: 1}, true, true) // false
 *
 * // 循环引用处理
 * const obj1 = {a: 1}; obj1.self = obj1;
 * const obj2 = {a: 1}; obj2.self = obj2;
 * isObjectShallowModified(obj1, obj2) // false
 *
 * // 嵌套对象比较
 * isObjectShallowModified(
 *   {user: {name: 'amis', profile: {age: 20}}},
 *   {user: {name: 'amis', profile: {age: 20}}}
 * ) // false
 *
 * // 嵌套数组对象比较
 * isObjectShallowModified(
 *   {users: [{id: 1, name: 'amis'}, {id: 2, name: 'baidu'}]},
 *   {users: [{id: 1, name: 'amis'}, {id: 2, name: 'baidu'}]}
 * ) // false
 */
export function isObjectShallowModified(
  prev: any,
  next: any,
  strictMode: boolean = true,
  ignoreUndefined: boolean = false,
  stack: WeakMap<any, any> = new WeakMap()
): boolean {
  if (Array.isArray(prev) && Array.isArray(next)) {
    return prev.length !== next.length
      ? true
      : prev.some((prev, index) =>
        isObjectShallowModified(
          prev,
          next[index],
          strictMode,
          ignoreUndefined,
          stack,
        ),
      );
  } else if (isNaN(prev) && isNaN(next)) {
    return false;
  } else if (
    null == prev ||
    null == next ||
    !isObject(prev) ||
    !isObject(next) ||
    isObservable(prev) ||
    isObservable(next)
  ) {
    return strictMode ? prev !== next : prev != next;
  }

  if (ignoreUndefined) {
    prev = rmUndefined(prev);
    next = rmUndefined(next);
  }

  const keys = Object.keys(prev);
  const nextKeys = Object.keys(next);
  if (
    keys.length !== nextKeys.length ||
    keys.sort().join(',') !== nextKeys.sort().join(',')
  ) {
    return true;
  }

  // 避免循环引用死循环。
  if (stack.has(prev) && stack.has(next)) {
    const prevMapped = stack.get(prev);
    const nextMapped = stack.get(next);

    // 如果他们在栈中相互映射，认为他们是相等的
    if (prevMapped === next && nextMapped === prev) {
      return false;
    }
  }
  // 记录当前正在比较的对象对，以防止循环引用
  isValidWeakMapKey(prev) && stack.set(prev, next);
  isValidWeakMapKey(next) && stack.set(next, prev);

  for (let i: number = keys.length - 1; i >= 0; i--) {
    let key = keys[i];
    if (
      isObjectShallowModified(
        prev[key],
        next[key],
        strictMode,
        ignoreUndefined,
        stack,
      )
    ) {
      return true;
    }
  }
  return false;
}

/**
 * 比较两个数组及其嵌套children属性是否发生变化
 *
 * @param prev 原始数组
 * @param next 新数组
 * @param strictMode 是否使用严格比较模式（默认为true）。
 *                  true: 使用 === 进行比较
 *                  false: 使用 == 进行比较，会进行类型转换
 * @returns 如果数组或其children属性发生变化则返回true，否则返回false
 *
 * @example
 * // 基本数组比较
 * isArrayChildrenModified([1, 2, 3], [1, 2, 3]) // false
 * isArrayChildrenModified([1, 2, 3], [1, 2, 4]) // true
 *
 * // 嵌套children比较
 * const prev = [{id: 1, children: [{id: 2}]}, {id: 3}];
 * const next = [{id: 1, children: [{id: 2}]}, {id: 3}];
 * isArrayChildrenModified(prev, next) // true，因为引用不同
 * isArrayChildrenModified(prev, next, false) // true，因为引用不同
 *
 * // 非严格模式比较
 * isArrayChildrenModified([1, '2', 3], [1, 2, 3], false) // false，会进行类型转换
 */
export function isArrayChildrenModified(
  prev: Array<any>,
  next: Array<any>,
  strictMode: boolean = true,
) {
  if (!Array.isArray(prev) || !Array.isArray(next)) {
    return strictMode ? prev !== next : prev != next;
  }

  if (prev.length !== next.length) {
    return true;
  }

  for (let i: number = prev.length - 1; i >= 0; i--) {
    if (
      strictMode
        ? prev[i] !== next[i]
        : prev[i] != next[i] ||
        isArrayChildrenModified(
          prev[i].children,
          next[i].children,
          strictMode,
        )
    ) {
      return true;
    }
  }

  return false;
}

export function immutableExtends(to: any, from: any, deep = false) {
  // 不是对象，不可以merge
  if (!isObject(to) || !isObject(from)) {
    return to;
  }

  let ret = to;

  Object.keys(from).forEach(key => {
    const origin = to[key];
    const value = from[key];

    // todo 支持深度merge
    if (origin !== value) {
      // 一旦有修改，就创建个新对象。
      ret = ret !== to ? ret : { ...to };
      ret[key] = value;
    }
  });

  return ret;
}

// 即将抛弃
export function makeColumnClassBuild(
  steps: number,
  classNameTpl: string = 'col-sm-$value',
) {
  let count = 12;
  let step = Math.floor(count / steps);

  return function (schema: Schema) {
    if (
      schema.columnClassName &&
      /\bcol-(?:xs|sm|md|lg)-(\d+)\b/.test(schema.columnClassName)
    ) {
      const flex = parseInt(RegExp.$1, 10);
      count -= flex;
      steps--;
      step = Math.floor(count / steps);
      return schema.columnClassName;
    } else if (schema.columnClassName) {
      count -= step;
      steps--;
      return schema.columnClassName;
    }

    count -= step;
    steps--;
    return classNameTpl.replace('$value', '' + step);
  };
}

export function hasVisibleExpression(schema: {
  visibleOn?: string;
  hiddenOn?: string;
  visible?: boolean;
  hidden?: boolean;
}) {
  return schema?.visibleOn || schema?.hiddenOn;
}

export function isVisible(
  schema: {
    visibleOn?: string;
    hiddenOn?: string;
    visible?: boolean;
    hidden?: boolean;
  },
  data?: object,
) {
  return !(
    schema.hidden ||
    schema.visible === false ||
    (schema.hiddenOn && evalExpression(schema.hiddenOn, data) === true) ||
    (schema.visibleOn && evalExpression(schema.visibleOn, data) === false)
  );
}

export function isUnfolded(
  node: any,
  config: {
    foldedField?: string;
    unfoldedField?: string;
  },
): boolean {
  let { foldedField, unfoldedField } = config;

  unfoldedField = unfoldedField || 'unfolded';
  foldedField = foldedField || 'folded';

  let ret: boolean = false;
  if (unfoldedField && typeof node[unfoldedField] !== 'undefined') {
    ret = !!node[unfoldedField];
  } else if (foldedField && typeof node[foldedField] !== 'undefined') {
    ret = !node[foldedField];
  }

  return ret;
}

/**
 * 过滤掉被隐藏的数组元素
 */
export function visibilityFilter(items: any, data?: object) {
  return items.filter((item: any) => {
    return isVisible(item, data);
  });
}

export function isDisabled(
  schema: {
    disabledOn?: string;
    disabled?: boolean;
  },
  data?: object,
) {
  return (
    schema.disabled ||
    (schema.disabledOn && evalExpression(schema.disabledOn, data))
  );
}

export function hasAbility(
  schema: any,
  ability: string,
  data?: object,
  defaultValue: boolean = true,
): boolean {
  return schema.hasOwnProperty(ability)
    ? schema[ability]
    : schema.hasOwnProperty(`${ability}On`)
      ? evalExpression(schema[`${ability}On`], data || schema)
      : defaultValue;
}

export function makeHorizontalDeeper(
  horizontal: {
    left: string;
    right: string;
    offset: string;
    leftFixed?: any;
  },
  count: number,
): {
  left: string | number;
  right: string | number;
  offset: string | number;
  leftFixed?: any;
} {
  if (count > 1 && /\bcol-(xs|sm|md|lg)-(\d+)\b/.test(horizontal.left)) {
    const flex = parseInt(RegExp.$2, 10) * count;
    return {
      leftFixed: horizontal.leftFixed,
      left: flex,
      right: 12 - flex,
      offset: flex,
    };
  } else if (count > 1 && typeof horizontal.left === 'number') {
    const flex = horizontal.left * count;

    return {
      leftFixed: horizontal.leftFixed,
      left: flex,
      right: 12 - flex,
      offset: flex,
    };
  }

  return horizontal;
}

export function promisify<T extends Function>(
  fn: T,
): (...args: Array<any>) => Promise<any> & {
  raw: T;
} {
  // 避免重复处理
  if ((fn as any)._promisified) {
    return fn as any;
  }
  let promisified = function () {
    try {
      const ret = fn.apply(null, arguments);
      if (ret && ret.then) {
        return ret;
      } else if (typeof ret === 'function') {
        // thunk support
        return new Promise((resolve, reject) =>
          ret((error: boolean, value: any) =>
            error ? reject(error) : resolve(value),
          ),
        );
      }
      return Promise.resolve(ret);
    } catch (e) {
      return Promise.reject(e);
    }
  };
  (promisified as any).raw = fn;
  (promisified as any)._promisified = true;
  return promisified;
}

/**
 *
 * @param node 当前元素
 * @param compute 自定义计算，找到的父元素是否满足特殊场景
 * @returns 返回控制当前元素滚动的父元素
 */
export function getScrollParent(
  node: HTMLElement,
  compute: (parent: HTMLElement) => boolean = () => true,
): HTMLElement | null {
  if (node == null) {
    return null;
  }

  const style = getComputedStyle(node);

  if (!style) {
    return null;
  }

  const text =
    style.getPropertyValue('overflow') +
    style.getPropertyValue('overflow-x') +
    style.getPropertyValue('overflow-y');

  if (node.nodeName === 'BODY' || (/auto|scroll/.test(text) && compute(node))) {
    return node;
  }

  return getScrollParent(node.parentNode as HTMLElement, compute);
}

/**
 * Deep diff between two object, using lodash
 * @param  {Object} object Object compared
 * @param  {Object} base   Object to compare with
 * @return {Object}        Return a new object who represent the diff
 */
export function difference<
  T extends { [propName: string]: any },
  U extends { [propName: string]: any },
>(object: T, base: U, keepProps?: Array<string>): { [propName: string]: any } {
  function changes(object: T, base: U) {
    if (isObject(object) && isObject(base)) {
      const keys: Array<keyof T & keyof U> = uniq(
        Object.keys(object).concat(Object.keys(base)),
      );
      let result: any = {};

      keys.forEach(key => {
        const a: any = object[key as keyof T];
        const b: any = base[key as keyof U];

        if (keepProps && ~keepProps.indexOf(key as string)) {
          result[key] = a;
        }

        if (isEqual(a, b)) {
          return;
        }

        if (!object.hasOwnProperty(key)) {
          result[key] = undefined;
        } else if (Array.isArray(a) && Array.isArray(b)) {
          result[key] = a;
        } else {
          result[key] = changes(a as any, b as any);
        }
      });

      return result;
    } else {
      return object;
    }
  }
  return changes(object, base);
}

export const padArr = (
  arr: Array<any>,
  size = 4,
  fillUndefined = false,
): Array<Array<any>> => {
  const ret: Array<Array<any>> = [[]];
  const pool: Array<any> = arr.concat();
  let from = 0;

  while (pool.length || (fillUndefined && ret[ret.length - 1].length < size)) {
    let host: Array<any> = ret[from] || (ret[from] = []);

    if (host.length >= size) {
      from += 1;
      continue;
    }

    host.push(pool.shift());
  }

  return ret;
};

export function __uri(id: string) {
  return id;
}

// xs < 768px
// sm >= 768px
// md >= 992px
// lg >= 1200px
export function isBreakpoint(str: string): boolean {
  if (typeof str !== 'string') {
    return !!str;
  }

  const breaks = str.split(/\s*,\s*|\s+/);

  if ((window as any).matchMedia) {
    return breaks.some(
      item =>
        item === '*' ||
        (item === 'xs' &&
          matchMedia(`screen and (max-width: 767px)`).matches) ||
        (item === 'sm' &&
          matchMedia(`screen and (min-width: 768px) and (max-width: 991px)`)
            .matches) ||
        (item === 'md' &&
          matchMedia(`screen and (min-width: 992px) and (max-width: 1199px)`)
            .matches) ||
        (item === 'lg' && matchMedia(`screen and (min-width: 1200px)`).matches),
    );
  } else {
    const width = window.innerWidth;
    return breaks.some(
      item =>
        item === '*' ||
        (item === 'xs' && width < 768) ||
        (item === 'sm' && width >= 768 && width < 992) ||
        (item === 'md' && width >= 992 && width < 1200) ||
        (item === 'lg' && width >= 1200),
    );
  }
}

export function until(
  fn: () => Promise<any>,
  when: (ret: any) => boolean,
  getCanceler: (fn: () => any) => void,
  interval: number = 5000,
) {
  let timer: ReturnType<typeof setTimeout>;
  let stoped: boolean = false;

  return new Promise((resolve, reject) => {
    let cancel = () => {
      clearTimeout(timer);
      stoped = true;
    };

    let check = async () => {
      try {
        const ret = await fn();

        if (stoped) {
          return;
        } else if (when(ret)) {
          stoped = true;
          resolve(ret);
        } else {
          timer = setTimeout(check, interval);
        }
      } catch (e) {
        reject(e);
      }
    };

    check();
    getCanceler && getCanceler(cancel);
  });
}

export function omitControls(
  controls: Array<any>,
  omitItems: Array<string>,
): Array<any> {
  return controls.filter(
    control => !~omitItems.indexOf(control.name || control._name),
  );
}

export function isEmpty(thing: any) {
  if (isObject(thing) && Object.keys(thing).length) {
    return false;
  }

  return true;
}

/**
 * 基于时间戳的 uuid
 *
 * @returns uniqueId
 */
export const uuid = () => {
  return (+new Date()).toString(36);
};

// 参考 https://github.com/streamich/v4-uuid
const str = () =>
  (
    '00000000000000000' + (Math.random() * 0xffffffffffffffff).toString(16)
  ).slice(-16);

export const uuidv4 = () => {
  const a = str();
  const b = str();
  return (
    a.slice(0, 8) +
    '-' +
    a.slice(8, 12) +
    '-4' +
    a.slice(13) +
    '-a' +
    b.slice(1, 4) +
    '-' +
    b.slice(4)
  );
};

export interface TreeItem {
  children?: TreeArray;
  [propName: string]: any;
}
export interface TreeArray extends Array<TreeItem> { }

/**
 * 类似于 arr.map 方法，此方法主要针对类似下面示例的树形结构。
 * [
 *     {
 *         children: []
 *     },
 *     // 其他成员
 * ]
 *
 * @param {Tree} tree 树形数据
 * @param {Function} iterator 处理函数，返回的数据会被替换成新的。
 * @return {Tree} 返回处理过的 tree
 */
export function mapTree<T extends TreeItem>(
  tree: Array<T>,
  iterator: (item: T, key: number, level: number, paths: Array<T>) => T,
  level: number = 1,
  depthFirst: boolean = false,
  paths: Array<T> = [],
) {
  return tree.map((item: any, index) => {
    if (depthFirst) {
      let children: TreeArray | undefined = item.children
        ? mapTree(
          item.children,
          iterator,
          level + 1,
          depthFirst,
          paths.concat(item),
        )
        : undefined;
      children && (item = { ...item, children: children });
      item = iterator(item, index, level, paths) || { ...(item as object) };
      return item;
    }

    item = iterator(item, index, level, paths) || { ...(item as object) };

    if (item.children && item.children.splice) {
      item.children = mapTree(
        item.children,
        iterator,
        level + 1,
        depthFirst,
        paths.concat(item),
      );
    }

    return item;
  });
}

/**
 * 深度优先遍历树
 * @param tree
 * @param iterator
 * @param childFirst 是否先在子元素上执行 iterator，默认值 false
 */
/**
 * 遍历树形结构数据，支持深度优先遍历和广度优先遍历。
 *
 * @param tree 要遍历的树形数据
 * @param iterator 遍历函数，接收四个参数：
 *                - item: 当前节点
 *                - index: 当前节点在同级中的索引
 *                - level: 当前节点的层级，从1开始
 *                - paths: 从根节点到当前节点的路径数组
*               返回值：
*                   - 如果返回 false，则停止遍历。
*                   - 否则，返回 true 或 undefined，继续遍历。
 * @param level 当前节点的层级，默认为1。主要用于函数内部递归调用时跟踪节点路径，外部调用时可忽略此参数。
 * @param paths 从根节点到当前节点的路径数组，默认为空数组。主要用于函数内部递归调用时跟踪节点路径，外部调用时可忽略此参数。
 * @param childFirst 是否先在子元素上执行 iterator，默认值 false。
 *                   - true: 深度优先，先在子元素上执行 iterator (后序遍历)
 *                   - false: 广度优先，先在当前元素上执行 iterator(先序遍历)
 * @example
 * const tree = [{
 *   id: 1,
 *   label: 'A',
 *   children: [
 *     {id: 2, label: 'B'},
 *     {id: 3, label: 'C'}
 *   ]
 * }];
 *
 * // 先序遍历 (默认)
 * eachTree(tree, item => console.log(item.label));
 * // 输出: A, B, C
 *
 * // 后序遍历
 * eachTree(tree, item => console.log(item.label), 1, [], true);
 * // 输出: B, C, A
 */
export function eachTree<T extends TreeItem>(
  tree: Array<T>,
  iterator: (item: T, index: number, level: number, paths: Array<T>) => any,
  level: number = 1,
  paths: Array<T> = [],
  childFirst: boolean = false,
) {
  tree.map((item, index) => {
    const currentPath = paths.concat(item);

    if (!childFirst) {
      iterator(item, index, level, currentPath);
    }

    if (item.children?.splice) {
      eachTree(item.children, iterator, level + 1, currentPath, childFirst);
    }

    if (childFirst) {
      iterator(item, index, level, currentPath);
    }
  });
}

/**
 * 在树中查找节点。
 * @param tree
 * @param iterator
 */
export function findTree<T extends TreeItem>(
  tree: Array<T>,
  iterator: (item: T, key: number, level: number, paths: Array<T>) => any,
  isEveryTreeV2 = false
): T | null {
  let result: T | null = null;

  if (isEveryTreeV2) {
    /*
      true为新增逻辑，收敛影响范围；false｜undefined为旧逻辑
      修复transfer分页组件后，引起的transfer搜索模式的问题：
        点选checkbox，没有勾选checkbox，且重复选中当前数据，并没有取消反而增加了一条数据的问题
    */
    if (!Array.isArray(tree) && !isObservableArray(tree)) {
      result = null;
    }
    const allTreeNodes: T[] = flattenTree(tree);
    result = allTreeNodes.find((item, index) => iterator(item, index, 1, [])) ?? null
  } else {
    everyTree(tree, (item, key, level, paths) => {
      if (iterator(item, key, level, paths)) {
        result = item;
        return false;
      }
      return true;
    });
  }

  return result;
}

/**
 * 在树中查找节点, 返回下标数组。
 * @param tree
 * @param iterator
 */
export function findTreeIndex<T extends TreeItem>(
  tree: Array<T>,
  iterator: (item: T, key: number, level: number, paths: Array<T>) => any,
): Array<number> | undefined {
  let idx: Array<number> = [];

  findTree(tree, (item, index, level, paths) => {
    if (iterator(item, index, level, paths)) {
      idx = [index];

      paths = paths.concat();
      paths.unshift({
        children: tree,
      } as any);

      for (let i = paths.length - 1; i > 0; i--) {
        const prev = paths[i - 1];
        const current = paths[i];
        idx.unshift(prev.children!.indexOf(current));
      }

      return true;
    }
    return false;
  });

  return idx.length ? idx : undefined;
}

export function getTree<T extends TreeItem>(
  tree: Array<T>,
  idx: Array<number> | number,
): T | undefined | null {
  const indexes = Array.isArray(idx) ? idx.concat() : [idx];
  const lastIndex = indexes.pop()!;
  let list: Array<T> | null = tree;
  for (let i = 0, len = indexes.length; i < len; i++) {
    const index = indexes[i];
    if (!list![index]) {
      list = null;
      break;
    }
    list = list![index].children as any;
  }
  return list ? list[lastIndex] : undefined;
}

/**
 * 过滤树节点，返回符合条件的节点组成的新树。
 * 支持深度优先（从子节点开始过滤）和广度优先（从父节点开始过滤）两种遍历方式。
 *
 * @param tree 要过滤的树形数据
 * @param iterator 过滤函数，接收以下参数：
 *   - item: T 当前遍历到的节点数据
 *   - key: number 当前节点在同级节点中的索引
 *   - level: number 当前节点的层级深度，从1开始
 *   - paths: Array<T> 从根节点到当前节点的路径数组（不包含当前节点）
 *   返回true则保留该节点，false则过滤掉该节点
 * @param level 当前节点的层级，默认为1。主要用于函数内部递归调用时跟踪节点路径，外部调用时可忽略此参数
 * @param depthFirst 是否深度优先遍历，默认为false。如果为true，则从子节点开始过滤；如果为false，则从父节点开始过滤
 * @param paths 当前节点的父节点路径，内部使用，外部调用时可忽略此参数
 * @returns 过滤后的新树
 *
 * @example
 * // 基本用法：过滤掉特定节点
 * const tree = [{
 *   text: 'parent',
 *   children: [{
 *     text: 'child1',
 *     children: []
 *   }]
 * }];
 * const filtered = filterTree(tree, item => item.text !== 'child1');
 * // 结果: [{text: 'parent', children: []}]
 *
 * @example
 * // 深度优先遍历：先处理子节点，再处理父节点
 * // 当过滤条件依赖于子节点状态时，可能导致所有节点都被过滤掉
 * const tree = [{
 *   id: 1,
 *   label: 'A',
 *   children: [
 *     {id: 2, label: 'B', children: [{id: 3, label: 'C'}]},
 *     {id: 4, label: 'D'}
 *   ]
 * }];
 * const filtered = filterTree(tree, item => Array.isArray(item.children) && item.children.length > 0, 1, true);
 * // 结果: [] // 因为先处理子节点，所有节点都被过滤掉了
 *
 * @example
 * // 广度优先遍历：先处理父节点，再处理子节点
 * // 当过滤条件依赖于子节点状态时，父节点的过滤结果不依赖于子节点的状态
 * const tree = [{
 *   id: 1,
 *   label: 'A',
 *   children: [
 *     {id: 2, label: 'B', children: [{id: 3, label: 'C'}]},
 *     {id: 4, label: 'D'}
 *   ]
 * }];
 * const filtered = filterTree(tree, item => Array.isArray(item.children) && item.children.length > 0);
 * // 结果: [{id: 1, label: 'A', children: [{id: 2, label: 'B', children: []}]}]
 * const filtered = filterTree(tree, item => item.id !== 2);
 * // 结果: [{id: 1, label: 'A', children: [{id: 4, label: 'D'}]}]
 *
 * @example
 * // 使用paths参数获取节点路径信息
 * const tree = [{
 *   id: 1,
 *   label: 'A',
 *   children: [{id: 2, label: 'B'}]
 * }];
 * const filtered = filterTree(tree,
 *   (item, index, level, paths) => {
 *     console.log('当前节点路径:', paths.map(p => p.label));
 *     return true;
 *   }
 * );
 * // 输出: 当前节点路径: ['A']
 */
export function filterTree<T extends TreeItem>(
  tree: Array<T>,
  iterator: (item: T, key: number, level: number, paths: Array<T>) => any,
  level: number = 1,
  depthFirst: boolean = false,
  paths: Array<T> = []
) {
  if (depthFirst) {
    return tree
      .map(item => {
        let children: TreeArray | undefined = item.children
          ? filterTree(
              item.children,
              iterator,
              level + 1,
              depthFirst,
              paths.concat(item)
            )
          : undefined;

        if (Array.isArray(children) && Array.isArray(item.children)) {
          item = {...item, children: children};
        }

        return item as T;
      })
      .filter((item, index) => iterator(item, index, level, paths));
  }

  return tree
    .filter((item, index) => iterator(item, index, level, paths))
    .map(item => {
      if (item.children?.splice) {
        let children = filterTree(
          item.children,
          iterator,
          level + 1,
          depthFirst,
          paths.concat(item)
        );

        if (Array.isArray(children) && Array.isArray(item.children)) {
          item = {...item, children: children};
        }
      }
      return item as T;
    });
}

/**
 * 搜索树节点，返回包含所有命中节点及其子节点，同时保留命中节点的父节点路径。
 * 与filterTree不同，该函数会同时保留：
 * 1. 命中节点的完整父节点路径
 * 2. 命中节点的所有子节点
 *
 * @param tree 要搜索的树形数据
 * @param fn 搜索函数，接收以下参数：
 *   - item: T 当前遍历到的节点数据
 *   返回true则保留该节点及其子节点，false则继续搜索子节点
 * @returns 过滤后的新树，包含所有匹配节点、其父节点路径及子节点
 *
 * @example
 * const tree = [{
 *   id: 1,
 *   label: 'A',
 *   children: [
 *     {id: 2, label: 'B'},
 *     {id: 3, label: 'C'}
 *   ]
 * }];
 *
 * // 示例1：搜索根节点，返回完整的子树
 * const result1 = searchTreeNotOnlyLeaf(tree, item => item.id === 1);
 * // 结果: [{
 * //   id: 1,
 * //   label: 'A',
 * //   children: [
 * //     {id: 2, label: 'B'},
 * //     {id: 3, label: 'C'}
 * //   ]
 * // }]
 *
 * // 示例2：搜索中间节点，返回包含父节点的完整路径
 * const tree2 = [{
 *   id: 1,
 *   label: 'A',
 *   children: [
 *     {id: 2, label: 'B', children: [{id: 3, label: 'C'}]},
 *     {id: 4, label: 'D'}
 *   ]
 * }];
 * const result2 = searchTreeNotOnlyLeaf(tree2, item => item.id === 2);
 * // 结果: [{
 * //   id: 1,
 * //   label: 'A',
 * //   children: [{
 * //     id: 2,
 * //     label: 'B',
 * //     children: [{id: 3, label: 'C'}]
 * //   }]
 * // }]
 *
 * // 示例3：搜索叶子节点，返回包含父节点的完整路径
 * const result3 = searchTreeNotOnlyLeaf(tree2, item => item.id === 3);
 * // 结果: [{
 * //   id: 1,
 * //   label: 'A',
 * //   children: [{
 * //     id: 2,
 * //     label: 'B',
 * //     children: [{id: 3, label: 'C'}]
 * //   }]
 * // }]
 *
 * // 示例4：搜索多个兄弟节点
 * const tree3 = [{
 *   id: 1,
 *   label: 'A',
 *   children: [
 *     {id: 2, label: 'B', children: [{id: 5, label: 'E'}]},
 *     {id: 3, label: 'B', children: [{id: 6, label: 'F'}]},
 *     {id: 4, label: 'C'}
 *   ]
 * }];
 * const result4 = searchTreeNotOnlyLeaf(tree3, item => item.label === 'B');
 * // 结果: [{
 * //   id: 1,
 * //   label: 'A',
 * //   children: [
 * //     {id: 2, label: 'B', children: [{id: 5, label: 'E'}]},
 * //     {id: 3, label: 'B', children: [{id: 6, label: 'F'}]}
 * //   ]
 * // }]
 */
export function searchTreeNotOnlyLeaf(tree, fn) {
  return tree.flatMap(x => {
    const hit = fn(x);
    if (hit) {
      return x;
    } else {
      if (x.children) {
        const filteredChilds = searchTreeNotOnlyLeaf(x.children, fn);
        if (filteredChilds.length) {
          return {...x, children: filteredChilds };
        } else {
          return [];
        }
      }
      return [];
    }
  })
}

/**
 * 过滤树节点，
 * 修复inputformular问题引入改版本，减少影响范围
 *
 * @param tree
 * @param iterator
 */
export function filterTreeV2<T extends TreeItem>(
  tree: Array<T>,
  iterator: (item: T, key: number, level: number, paths: Array<T>) => any,
  level: number = 1,
  depthFirst: boolean = false,
  paths: Array<T> = []
) {
  if (depthFirst) {
    return tree
      .map(item => {
        let children: TreeArray | undefined = item.children
          ? filterTreeV2(item.children, iterator, level + 1, depthFirst, paths.concat(item))
          : undefined;

        if (Array.isArray(children) && Array.isArray(item.children)) {
          item = { ...item, children: children };
        }

        return item;
      })
      .filter((item, index) => iterator(item, index, level, paths));
  }

  return tree
    .filter((item, index) => iterator(item, index, level, paths))
    .map(item => {
      if (item.children?.splice) {
        let children = filterTreeV2(
          item.children,
          iterator,
          level + 1,
          depthFirst,
          paths.concat(item)
        );

        if (Array.isArray(children) && Array.isArray(item.children)) {
          item = { ...item, children: children };
        }
      }
      return item;
    });
}

/**
 * 判断树中每个节点是否满足某个条件。
 * @param tree
 * @param iterator
 */
export function everyTree<T extends TreeItem>(
  tree: Array<T>,
  iterator: (
    item: T,
    key: number,
    level: number,
    paths: Array<T>,
    indexes: Array<number>,
  ) => boolean,
  level: number = 1,
  paths: Array<T> = [],
  indexes: Array<number> = [],
): boolean {
  if (!Array.isArray(tree) && !isObservableArray(tree)) {
    return false;
  }
  return tree.every((item, index) => {
    const value: any = iterator(item, index, level, paths, indexes);

    if (value && item.children?.splice) {
      return everyTree(
        item.children,
        iterator,
        level + 1,
        paths.concat(item),
        indexes.concat(index),
      );
    }

    return value;
  });
}

/**
 * 判断树中是否有某些节点满足某个条件。
 * @param tree
 * @param iterator
 */
export function someTree<T extends TreeItem>(
  tree: Array<T>,
  iterator: (item: T, key: number, level: number, paths: Array<T>) => boolean,
): boolean {
  let result = false;

  everyTree(tree, (item: T, key: number, level: number, paths: Array<T>) => {
    if (iterator(item, key, level, paths)) {
      result = true;
      return false;
    }
    return true;
  });

  return result;
}

/**
 * 将树打平变成一维数组，可以传入第二个参数实现打平节点中的其他属性。
 *
 * 比如：
 *
 * flattenTree([
 *     {
 *         id: 1,
 *         children: [
 *              { id: 2 },
 *              { id: 3 },
 *         ]
 *     }
 * ], item => item.id); // 输出为 [1, 2, 3]
 *
 * @param tree
 * @param mapper
 */
export function flattenTree<T extends TreeItem>(tree: Array<T>): Array<T>;
export function flattenTree<T extends TreeItem, U>(
  tree: Array<T>,
  mapper: (value: T, index: number, level: number, paths?: Array<T>) => U,
): Array<U>;
export function flattenTree<T extends TreeItem, U>(
  tree: Array<T>,
  mapper?: (value: T, index: number, level: number, paths?: Array<T>) => U,
): Array<U> {
  let flattened: Array<any> = [];
  eachTree(tree, (item, index, level, paths) =>
    flattened.push(mapper ? mapper(item, index, level, paths) : item),
  );
  return flattened;
}

/**
 * 将树打平变成一维数组，用法和flattenTree类似，区别是结果仅保留叶节点
 *
 * 比如：
 *
 * flattenTreeWithLeafNodes([
 *     {
 *         id: 1,
 *         children: [
 *              { id: 2 },
 *              { id: 3 },
 *         ]
 *     }
 * ], item => item.id); // 输出为 [2, 3]
 *
 * @param tree
 * @param mapper
 */
export function flattenTreeWithLeafNodes<T extends TreeItem>(
  tree: Array<T>,
): Array<T>;
export function flattenTreeWithLeafNodes<T extends TreeItem, U>(
  tree: Array<T>,
  mapper: (value: T, index: number) => U,
): Array<U>;
export function flattenTreeWithLeafNodes<T extends TreeItem, U>(
  tree: Array<T>,
  mapper?: (value: T, index: number) => U,
): Array<U> {
  let flattened: Array<any> = [];
  eachTree(tree, (item, index) => {
    if (!item.hasOwnProperty('children')) {
      flattened.push(mapper ? mapper(item, index) : item);
    }
  });
  return flattened;
}

/**
 * 操作树，遵循 imutable, 每次返回一个新的树。
 * 类似数组的 splice 不同的地方这个方法不修改原始数据，
 * 同时第二个参数不是下标，而是下标数组，分别代表每一层的下标。
 *
 * 至于如何获取下标数组，请查看 findTreeIndex
 *
 * @param tree
 * @param idx
 * @param deleteCount
 * @param ...items
 */
export function spliceTree<T extends TreeItem>(
  tree: Array<T>,
  idx: Array<number> | number,
  deleteCount: number = 0,
  ...items: Array<T>
): Array<T> {
  const list = tree.concat();
  if (typeof idx === 'number') {
    list.splice(idx, deleteCount, ...items);
  } else if (Array.isArray(idx) && idx.length) {
    idx = idx.concat();
    const lastIdx = idx.pop()!;
    let host = idx.reduce((list: Array<T>, idx) => {
      const child = {
        ...list[idx],
        children: list[idx].children ? list[idx].children!.concat() : [],
      };
      list[idx] = child;
      return child.children;
    }, list);
    host.splice(lastIdx, deleteCount, ...items);
  }

  return list;
}

/**
 * 计算树的深度
 * @param tree
 */
export function getTreeDepth<T extends TreeItem>(tree: Array<T>): number {
  return Math.max(
    ...tree.map(item => {
      if (Array.isArray(item.children)) {
        return 1 + getTreeDepth(item.children);
      }

      return 1;
    }),
  );
}

/**
 * 从树中获取某个值的所有祖先
 * @param tree
 * @param value
 * @param includeSelf 默认值 false
 */
export function getTreeAncestors<T extends TreeItem>(
  tree: Array<T>,
  value: T,
  includeSelf = false,
): Array<T> | null {
  let ancestors: Array<T> | null = null;

  findTree(tree, (item, index, level, paths) => {
    if (item === value) {
      ancestors = paths;
      if (includeSelf) {
        ancestors.push(item);
      }
      return true;
    }
    return false;
  });

  return ancestors;
}

/**
 * 从树中获取某个值的上级
 * @param tree
 * @param value
 */
export function getTreeParent<T extends TreeItem>(tree: Array<T>, value: T) {
  const ancestors = getTreeAncestors(tree, value);
  return ancestors?.length ? ancestors[ancestors.length - 1] : null;
}

export function ucFirst(str?: string) {
  return typeof str === 'string'
    ? str.substring(0, 1).toUpperCase() + str.substring(1)
    : str;
}

export function lcFirst(str?: string) {
  return str ? str.substring(0, 1).toLowerCase() + str.substring(1) : '';
}

export function camel(str?: string) {
  return str
    ? str
      .split(/[\s_\-]/)
      .map((item, index) => (index === 0 ? lcFirst(item) : ucFirst(item)))
      .join('')
    : '';
}

export function getWidthRate(value: any, strictMode = false): number {
  if (typeof value === 'string' && /\bcol\-\w+\-(\d+)\b/.test(value)) {
    return parseInt(RegExp.$1, 10);
  }

  return strictMode ? 0 : value || 0;
}

export function getLevelFromClassName(
  value: string,
  defaultValue: string = 'default',
) {
  if (
    /\b(?:btn|text)-(link|primary|secondary|info|success|warning|danger|light|dark|text)\b/.test(
      value,
    )
  ) {
    return RegExp.$1;
  }

  return defaultValue;
}

export function pickEventsProps(props: any) {
  const ret: any = {};
  props &&
    Object.keys(props).forEach(
      key => /^on/.test(key) && (ret[key] = props[key]),
    );
  return ret;
}

export const autobind = autobindMethod;

export const bulkBindFunctions = function <
  T extends {
    [propName: string]: any;
  },
>(context: T, funNames: Array<FunctionPropertyNames<T>>) {
  funNames.forEach(key => (context[key] = context[key].bind(context)));
};

export function sortArray<T extends any>(
  items: Array<T>,
  field: string,
  dir: -1 | 1,
): Array<T> {
  return items.sort((a: any, b: any) => {
    let ret: number;
    const a1 = a[field];
    const b1 = b[field];

    if (typeof a1 === 'number' && typeof b1 === 'number') {
      ret = a1 < b1 ? -1 : a1 === b1 ? 0 : 1;
    } else {
      ret = String(a1).localeCompare(String(b1));
    }

    return ret * dir;
  });
}

// 只判断一层, 如果层级很深，form-data 也不好表达。
export function hasFile(object: any): boolean {
  return Object.keys(object).some(key => {
    let value = object[key];

    return (
      value instanceof File ||
      (Array.isArray(value) && value.length && value[0] instanceof File)
    );
  });
}

export function qsstringify(
  data: any,
  options: any = {
    arrayFormat: 'indices',
    encodeValuesOnly: true,
  },
  keepEmptyArray?: boolean,
) {
  // qs会保留空字符串。fix: Combo模式的空数组，无法清空。改为存为空字符串；只转换一层
  keepEmptyArray &&
    Object.keys(data).forEach((key: any) => {
      Array.isArray(data[key]) && !data[key].length && (data[key] = '');
    });
  return qs.stringify(data, options);
}

export function qsparse(
  data: string,
  options: any = {
    arrayFormat: 'indices',
    encodeValuesOnly: true,
    depth: 1000, // 默认是 5， 所以condition-builder只要来个条件组就会导致报错
    arrayLimit: 1000 /** array元素数量超出限制，会被自动转化为object格式，默认值1000 */,
  },
) {
  return qs.parse(data, options);
}

export function object2formData(
  data: any,
  options: any = {
    arrayFormat: 'indices',
    encodeValuesOnly: true,
  },
  fd: FormData = new FormData(),
): any {
  let fileObjects: any = [];
  let others: any = {};

  Object.keys(data).forEach(key => {
    const value = data[key];

    if (value instanceof File) {
      fileObjects.push([key, value]);
    } else if (
      Array.isArray(value) &&
      value.length &&
      value[0] instanceof File
    ) {
      value.forEach(value => fileObjects.push([`${key}[]`, value]));
    } else {
      others[key] = value;
    }
  });

  // 因为 key 的格式太多了，偷个懒，用 qs 来处理吧。
  qsstringify(others, options)
    .split('&')
    .forEach(item => {
      let parts = item.split('=');
      // form-data/multipart 是不需要 encode 值的。
      parts[0] && fd.append(parts[0], decodeURIComponent(parts[1]));
    });

  // Note: File类型字段放在后面，可以支持第三方云存储鉴权
  fileObjects.forEach((fileObject: any[]) =>
    fd.append(fileObject[0], fileObject[1], fileObject[1].name),
  );

  return fd;
}

export function chainFunctions(
  ...fns: Array<(...args: Array<any>) => void>
): (...args: Array<any>) => void {
  return (...args: Array<any>) =>
    fns.reduce(
      (ret: any, fn: any) =>
        ret === false
          ? false
          : typeof fn == 'function'
            ? fn(...args)
            : undefined,
      undefined,
    );
}

export function chainEvents(props: any, schema: any) {
  const ret: any = {};

  Object.keys(props).forEach(key => {
    if (
      key.substr(0, 2) === 'on' &&
      typeof props[key] === 'function' &&
      typeof schema[key] === 'function' &&
      schema[key] !== props[key]
    ) {
      // 表单项里面的 onChange 很特殊，这个不要处理。
      if (props.formStore && key === 'onChange') {
        ret[key] = props[key];
      } else {
        ret[key] = chainFunctions(schema[key], props[key]);
      }
    } else {
      ret[key] = props[key];
    }
  });

  return ret;
}

export function mapObject(
  value: any,
  fn: Function,
  skipFn?: (value: any) => boolean,
): any {
  // 如果value值满足skipFn条件则不做map操作
  skipFn =
    skipFn && typeof skipFn === 'function'
      ? skipFn
      : (value: any): boolean => {
        // File类型处理之后会变成plain object
        if (value instanceof File) {
          return true;
        }

        return false;
      };

  if (!!skipFn(value)) {
    return value;
  }

  if (Array.isArray(value)) {
    return value.map(item => mapObject(item, fn));
  }

  if (isObject(value)) {
    let tmpValue = { ...value };
    Object.keys(tmpValue).forEach(key => {
      (tmpValue as PlainObject)[key] = mapObject(
        (tmpValue as PlainObject)[key],
        fn,
      );
    });
    return tmpValue;
  }
  return fn(value);
}

export function loadScript(src: string) {
  return new Promise<void>((ok, fail) => {
    const script = document.createElement('script');
    script.onerror = reason => fail(reason);

    if (~src.indexOf('{{callback}}')) {
      const callbackFn = `loadscriptcallback_${uuid()}`;
      (window as any)[callbackFn] = () => {
        ok();
        delete (window as any)[callbackFn];
      };
      src = src.replace('{{callback}}', callbackFn);
    } else {
      script.onload = () => ok();
    }

    script.src = src;
    document.head.appendChild(script);
  });
}

export class SkipOperation extends Error { }

export class ValidateError extends Error {
  name: 'ValidateError';
  detail: {[propName: string]: Array<string> | string};

  constructor(
    message: string,
    error: {[propName: string]: Array<string> | string}
  ) {
    super();
    this.name = 'ValidateError';
    this.message = message;
    this.detail = error;
  }
}

/**
 * 检查对象是否有循环引用，来自 https://stackoverflow.com/a/34909127
 * @param obj
 */
function isCyclic(obj: any): boolean {
  const seenObjects: any = [];
  function detect(obj: any) {
    if (obj && typeof obj === 'object') {
      if (seenObjects.indexOf(obj) !== -1) {
        return true;
      }
      seenObjects.push(obj);
      for (var key in obj) {
        if (obj.hasOwnProperty(key) && detect(obj[key])) {
          return true;
        }
      }
    }
    return false;
  }
  return detect(obj);
}

function internalFindObjectsWithKey(obj: any, key: string) {
  let objects: any[] = [];
  for (const k in obj) {
    if (!obj.hasOwnProperty(k)) continue;
    if (k === key) {
      objects.push(obj);
    } else if (typeof obj[k] === 'object') {
      objects = objects.concat(internalFindObjectsWithKey(obj[k], key));
    }
  }
  return objects;
}

/**
 * 深度查找具有某个 key 名字段的对象，实际实现是 internalFindObjectsWithKey，这里包一层是为了做循环引用检测
 * @param obj
 * @param key
 */
export function findObjectsWithKey(obj: any, key: string) {
  // 避免循环引用导致死循环
  if (isCyclic(obj)) {
    return [];
  }
  return internalFindObjectsWithKey(obj, key);
}

let scrollbarWidth: number;

/**
 * 获取浏览器滚动条宽度 https://stackoverflow.com/a/13382873
 */

export function getScrollbarWidth() {
  if (typeof scrollbarWidth !== 'undefined') {
    return scrollbarWidth;
  }
  // Creating invisible container
  const outer = document.createElement('div');
  outer.style.visibility = 'hidden';
  outer.style.overflow = 'scroll'; // forcing scrollbar to appear
  // @ts-ignore
  outer.style.msOverflowStyle = 'scrollbar'; // needed for WinJS apps
  document.body.appendChild(outer);

  // Creating inner element and placing it in the container
  const inner = document.createElement('div');
  outer.appendChild(inner);

  // Calculating difference between container's full width and the child width
  scrollbarWidth = outer.offsetWidth - inner.offsetWidth;

  // Removing temporary elements from the DOM
  // @ts-ignore
  outer.parentNode.removeChild(outer);

  return scrollbarWidth;
}

// 后续改用 FormulaExec['formula']
function resolveValueByName(
  data: any,
  name?: string,
  canAccessSuper?: boolean,
) {
  return isPureVariable(name)
    ? resolveVariableAndFilter(name, data)
    : resolveVariable(name, data, canAccessSuper);
}

// 统一的获取 value 值方法
export function getPropValue<
  T extends {
    value?: any;
    name?: string;
    data?: any;
    defaultValue?: any;
    canAccessSuperData?: boolean;
  },
>(
  props: T,
  getter?: (props: T) => any,
  canAccessSuper = props.canAccessSuperData
) {
  const { name, value, data, defaultValue } = props;
  return (
    value ??
    getter?.(props) ??
    resolveValueByName(data, name, canAccessSuper) ??
    defaultValue
  );
}

// 检测 value 是否有变化，有变化就执行 onChange
export function detectPropValueChanged<
  T extends {
    value?: any;
    name?: string;
    data?: any;
    defaultValue?: any;
  },
>(
  props: T,
  prevProps: T,
  onChange: (value: any) => void,
  getter?: (props: T) => any,
) {
  let nextValue: any;
  if (typeof props.value !== 'undefined') {
    props.value !== prevProps.value && onChange(props.value);
  } else if ((nextValue = getter?.(props)) !== undefined) {
    nextValue !== getter!(prevProps) && onChange(nextValue);
  } else if (
    typeof props.name === 'string' &&
    (nextValue = resolveValueByName(props.data, props.name)) !== undefined
  ) {
    nextValue !== resolveValueByName(prevProps.data, prevProps.name) &&
      onChange(nextValue);
  } else if (props.defaultValue !== prevProps.defaultValue) {
    onChange(props.defaultValue);
  }
}

// 去掉字符串中的 html 标签，不完全准确但效率比较高
export function removeHTMLTag(str: string) {
  return typeof str === 'string' ? str.replace(/<\/?[^>]+(>|$)/g, '') : str;
}

/**
 * 将路径格式的value转换成普通格式的value值
 *
 * @example
 *
 * 'a/b/c' => 'c';
 * {label: 'A/B/C', value: 'a/b/c'} => {label: 'C', value: 'c'};
 * 'a/b/c,a/d' => 'c,d';
 * ['a/b/c', 'a/d'] => ['c', 'd'];
 * [{label: 'A/B/C', value: 'a/b/c'},{label: 'A/D', value: 'a/d'}] => [{label: 'C', value: 'c'},{label: 'D', value: 'd'}]
 */
export function normalizeNodePath(
  value: any,
  enableNodePath: boolean,
  labelField: string = 'label',
  valueField: string = 'value',
  pathSeparator: string = '/',
  delimiter: string = ',',
) {
  const nodeValueArray: any[] = [];
  const nodePathArray: any[] = [];
  const getLastNodeFromPath = (path: any) =>
    last(path ? path.toString().split(pathSeparator) : []);

  if (typeof value === 'undefined' || !enableNodePath) {
    return { nodeValueArray, nodePathArray };
  }

  // 尾节点为当前options中value值
  if (Array.isArray(value)) {
    value.forEach(nodePath => {
      if (nodePath && nodePath.hasOwnProperty(valueField)) {
        nodeValueArray.push({
          ...nodePath,
          [labelField]: getLastNodeFromPath(nodePath[labelField]),
          [valueField]: getLastNodeFromPath(nodePath[valueField]),
        });
        nodePathArray.push(nodePath[valueField]);
      } else {
        nodeValueArray.push(getLastNodeFromPath(nodePath));
        nodePathArray.push(nodePath);
      }
    });
  } else if (typeof value === 'string') {
    value
      .toString()
      .split(delimiter)
      .forEach(path => {
        nodeValueArray.push(getLastNodeFromPath(path));
        nodePathArray.push(path);
      });
  } else {
    nodeValueArray.push({
      ...value,
      [labelField]: getLastNodeFromPath(value[labelField]),
      [valueField || 'value']: getLastNodeFromPath(value[valueField]),
    });
    nodePathArray.push(value[valueField]);
  }

  return { nodeValueArray, nodePathArray };
}

// 主要用于排除点击输入框和链接等情况
export function isClickOnInput(e: React.MouseEvent<HTMLElement>) {
  const target: HTMLElement = e.target as HTMLElement;
  let formItem;
  if (
    !e.currentTarget.contains(target) ||
    ~['INPUT', 'TEXTAREA'].indexOf(target.tagName) ||
    ((formItem = target.closest(`button, a, [data-role="form-item"]`)) &&
      e.currentTarget.contains(formItem))
  ) {
    return true;
  }
  return false;
}

// 计算字符串 hash
export function hashCode(s: string): number {
  return s.split('').reduce((a, b) => {
    a = (a << 5) - a + b.charCodeAt(0);
    return a & a;
  }, 0);
}

/**
 * 遍历 schema
 * @param json
 * @param mapper
 */
export function JSONTraverse(
  json: any,
  mapper: (value: any, key: string | number, host: Object) => any,
) {
  Object.keys(json).forEach(key => {
    const value: any = json[key];
    if (!isObservable(value)) {
      if (isPlainObject(value) || Array.isArray(value)) {
        JSONTraverse(value, mapper);
      } else {
        mapper(value, key, json);
      }
    }
  });
}

export function convertArrayValueToMoment(
  value: number[],
  types: string[],
  mom: moment.Moment,
): moment.Moment {
  if (value.length === 0) return mom;
  for (let i = 0; i < types.length; i++) {
    const type = types[i];
    // @ts-ignore
    mom.set(type, value[i]);
  }
  return mom;
}

export function getRange(min: number, max: number, step: number = 1) {
  const arr = [];
  for (let i = min; i <= max; i += step) {
    arr.push(i);
  }
  return arr;
}

export function repeatCount(count: number, iterator: (index: number) => any) {
  let result: Array<any> = [];
  let index = 0;

  while (count--) {
    result.push(iterator(index++));
  }

  return result;
}

export function isNumeric(value: any): boolean {
  if (typeof value === 'number') {
    return true;
  }
  return /^[-+]?(?:\d*[.])?\d+$/.test(value);
}

/**
 * 获取URL链接中的query参数（包含hash mode）
 *
 * @param location Location对象，或者类Location结构的对象
 */
export function parseQuery(
  location?: Location | { query?: any; search?: any;[propName: string]: any },
): Record<string, any> {
  const query =
    (location && !(location instanceof Location) && location?.query) ||
    (location && location?.search && qsparse(location.search.substring(1))) ||
    (window.location.search && qsparse(window.location.search.substring(1)));
  /* 处理hash中的query */
  const hashQuery =
    window.location?.hash && typeof window.location?.hash === 'string'
      ? qsparse(window.location.hash.replace(/^#.*\?/gi, ''))
      : {};
  const normalizedQuery = isPlainObject(query) ? query : {};

  return merge(normalizedQuery, hashQuery);
}

/**
 * 递归计算树形数据最大的层级数
 */
export function calculationDataLevel(arr: any) {
  let maxLevel = 0;
  function multiArr(arr: any, level: number) {
    ++level;
    maxLevel = Math.max(level, maxLevel);
    for (let i = 0; i < arr.length; i++) {
      let item = arr[i];
      if (Array.isArray(item.children) && item.children.length > 0) {
        multiArr(item.children, level);
      }
    }
  }
  multiArr(arr, 0);
  return maxLevel;
}

export function autoGetRenderColumnsNum(arr: Array<any>, rowNum: number = 2) {
  const pool: Array<any> = arr.concat();
  // 需要显示的搜索项
  let columnsNum: number = 0;
  let columnRatioTotal: number = 0;
  let line: number = 0;

  while (pool.length) {
    const column = pool.shift();
    columnRatioTotal += column.searchable?.columnRatio || column?.columnRatio || 4;

    // 依赖于column 十二等分的设计
    if (columnRatioTotal > 12 * (line + 1)) {
      line++;
    }

    if (line === rowNum) break;

    columnsNum++;
  }

  return columnsNum;
}

export const TEST_ID_KEY: 'data-testid' = 'data-testid';

export class TestIdBuilder {
  testId?: string;

  static fast(testId: string) {
    return {
      [TEST_ID_KEY]: testId
    };
  }

  // 为空就表示没有启用testId，后续一直返回都将是空
  constructor(testId?: string) {
    this.testId = testId;
  }

  // 生成子区域的testid生成器
  getChild(childPath: string | number, data?: object) {
    if (this.testId == null) {
      return new TestIdBuilder();
    }

    return new TestIdBuilder(
      data
        ? filter(`${this.testId}-${childPath}`, data)
        : `${this.testId}-${childPath}`
    );
  }

  // 获取当前组件的testid
  getTestId(data?: object) {
    if (this.testId == null) {
      return undefined;
    }

    return {
      [TEST_ID_KEY]: data ? filter(this.testId, data) : this.testId
    };
  }

  getTestIdValue(data?: object) {
    if (this.testId == null) {
      return undefined;
    }

    return data ? filter(this.testId, data) : this.testId;
  }
}

/**
 * 判断元素是否在视图内部
 * @param el
 * @returns
 */
export function isElementInViewport(el: Element | null | undefined) {
  if(!el) return false;
  // 获取元素的边界框信息
  const rect = el.getBoundingClientRect();

  // 检查元素是否在视窗内部
  return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  );
}

/**
 * 让元素滚动到视图内部的异步方法
 * @param el
 * @returns
 */
export function scrollElementIntoView(el: Element | null | undefined, options = {}) {
  return new Promise((resolve, reject) => {
    if(!el) return reject();
    el.scrollIntoView(options);

    // 如果500ms没有滚动成功，那么就认为滚动失败
    setTimeout(() => {
      if(!isElementInViewport(el)) {
        reject(false);
      } else {
        resolve(true);
      }
    }, 500)
  })
}

export function isNotEmptyArray(aa) {
  return Array.isArray(aa) && aa.length > 0;
}
