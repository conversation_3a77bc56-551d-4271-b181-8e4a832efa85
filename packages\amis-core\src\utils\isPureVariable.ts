import {parse} from 'amis-formula';

/**
 * 判断给定的字符串是否为纯变量表达式
 *
 * @param path - 要检查的表达式字符串
 * @returns 如果表达式是纯变量（只包含一个script类型的节点），返回true；否则返回false。
 * 纯变量表达式包括以下形式：
 * 1. 以$开头的变量路径，如'$foo.bar'
 * 2. ${expression}格式的表达式，支持过滤器、函数调用和数组访问
 *
 * @example
 * // 纯变量表达式
 * isPureVariable('$foo.bar'); // true
 * isPureVariable('${user.name}'); // true
 * isPureVariable('${foo | raw}'); // true
 * isPureVariable('${foo()}'); // true
 * isPureVariable('${items[0]}'); // true
 * isPureVariable('${a > b}'); // true
 * isPureVariable('${ARRAYMAP(objArr, item => {id: item.id})}'); // true
 *
 * // 非纯变量表达式
 * isPureVariable('user'); // false
 * isPureVariable('$(foo + bar)'); // false
 * isPureVariable('${user?.name}'); // false
 */
export function isPureVariable(path?: any): path is string {
  if (typeof path === 'string') {
    try {
      const ast = parse(path);
      // 只有一个成员说明是纯表达式模式
      return ast.body.length === 1 && ast.body[0].type === 'script';
    } catch (err) {
      return false;
    }
  }

  return false;
}
