/**
 * 将字符串路径转换为路径数组
 * 支持以下格式：
 * 1. 点号分隔的路径，如：'a.b.c'
 * 2. 数组索引路径，如：'a[0].b'
 * 3. 带引号的路径，如：'a["b"].c' 或 "a['b'].c"
 * 4. 转义字符的路径，如：'a["b\\"]"].c'
 * 5. 以点号开头的路径，如：'.a.b'
 *
 * @param string 要转换的字符串路径
 * @returns 返回路径片段数组
 *
 * @example
 * // 基础点号路径
 * keyToPath('a.b.c')  // 返回 ['a', 'b', 'c']
 *
 * // 数组索引路径
 * keyToPath('a[0].b')  // 返回 ['a', '0', 'b']
 *
 * // 带引号的路径
 * keyToPath('a["b"].c')  // 返回 ['a', 'b', 'c']
 * keyToPath("a['b'].c")  // 返回 ['a', 'b', 'c']
 *
 * // 带转义字符的路径
 * keyToPath('a["b\\"]"].c')  // 返回 ['a', 'b"]', 'c']
 *
 * // 以点号开头的路径
 * keyToPath('.a.b')  // 返回 ['', 'a', 'b']
 */
export const keyToPath = (string: string) => {
  const result = [];

  if (string.charCodeAt(0) === '.'.charCodeAt(0)) {
    result.push('');
  }

  string.replace(
    new RegExp(
      '[^.[\\]]+|\\[(?:([^"\'][^[]*)|(["\'])((?:(?!\\2)[^\\\\]|\\\\.)*?)\\2)\\]|(?=(?:\\.|\\[\\])(?:\\.|\\[\\]|$))',
      'g'
    ),
    (match, expression, quote, subString) => {
      let key = match;
      if (quote) {
        key = subString.replace(/\\(\\)?/g, '$1');
      } else if (expression) {
        key = expression.trim();
      }
      result.push(key);
      return '';
    }
  );

  return result;
};
