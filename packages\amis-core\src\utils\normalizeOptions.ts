import {Options} from '../types';
import isPlainObject from 'lodash/isPlainObject';
import { defaultOptions } from '../factory'

export function normalizeOptions(
  options: string | {[propName: string]: string} | Array<string> | Options,
  share: {
    values: Array<any>;
    options: Array<any>;
    allValues: Array<any>; // tree结构时使用
  } = {
    values: [],
    options: [],
    allValues: [],
  },
  valueField = 'value'
): Options {
  let newOptions: Options = [] // 经过转换后的 options
  let duplicateValues: any[] = [] // 重复的值

  if (typeof options === 'string') {
    newOptions = options.split(',').map(item => {
      const idx = share.values.indexOf(item);

      const option = {
        label: item,
        // 添加 option 的 value 根据 valueField 来
        // 否则某些情况下多余字段会有影响
        [valueField]: item
      };

      if (~idx) {
        duplicateValues.push(share.values[idx])
        return option;
      }

      share.values.push(option.value);
      share.options.push(option);

      return option;
    });
  } else if (
    Array.isArray(options as Array<string>) &&
    typeof (options as Array<string>)[0] === 'string'
  ) {
    newOptions = (options as Array<string>).map(item => {
      const idx = share.values.indexOf(item);

      const option = {
        label: item,
        [valueField]: item
      };

      if (~idx) {
        duplicateValues.push(share.values[idx])
        return option;
      }

      share.values.push(option[valueField]);
      share.options.push(option);

      return option;
    });
  } else if (Array.isArray(options as Options)) {
    newOptions = (options as Options).map(item => {
      const value = item && item[valueField];

      const idx =
        value !== undefined && !item.children
          ? share.values.indexOf(value)
          : -1;

      /**
       * 使用 share.allValues 来判断，嵌套 children，判断 value 字段重复问题
       */
      const allIdx = share.allValues.indexOf(value)
      if (value !== undefined && ~allIdx) {
        duplicateValues.push(share.values[allIdx])
      }

      const option = {
        ...item,
        [valueField]: value
      };

      /**
       * 此处只是剔除了所有叶子结点的重复
       */
      if (~idx) {
        return option
      }

      /**
       * share.values 只存放了所有 叶子结点的 value,
       * share.values.indexOf(value) 判断重复时，无法识别 父节点与叶子的重复，
       * 因此扩展一个 allValues 来解决该问题
       */
      if (value !== undefined) {
        share.allValues.push(value);
      }

      if (typeof option.children !== 'undefined') {
        option.children = normalizeOptions(option.children, share, valueField);
      } else if (value !== undefined) {
        share.values.push(value);
        share.options.push(option);
      }

      return option;
    });
  } else if (isPlainObject(options)) {
    newOptions = Object.keys(options).map(key => {
      const idx = share.values.indexOf(key);


      const option = {
        label: (options as {[propName: string]: string})[key] as string,
        [valueField]: key
      };

      if (~idx) {
        duplicateValues.push(share.values[idx])
        return option;
      }

      share.values.push(option.value);
      share.options.push(option);

      return option;
    });
  }

  if (duplicateValues.length) {
    const errInfo = {
      duplicateValues,
      valueField,
      resolvedOptions: newOptions
    }
    if (defaultOptions.throwErrorToSentry) defaultOptions.throwErrorToSentry(new Error(JSON.stringify(errInfo)),'SameValueError')
    console.warn('表单项 options 中，存在重复 valueField 字段，请保持 options 中 valueField 唯一', {
      ...errInfo,
      rawOptions: options,
    })
  }

  return newOptions;
}
