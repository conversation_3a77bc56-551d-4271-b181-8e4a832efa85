import isPlainObject from 'lodash/isPlainObject';
import {keyToPath} from './keyToPath';
import {resolveVariable} from './resolveVariable';

/**
 * 创建一个新对象，支持原型链继承和属性设置
 *
 * @param superProps - 作为新对象原型的对象，如果提供的对象是冻结的，会先克隆一份
 * @param props - 要添加到新对象的属性集合
 * @param properties - 要添加到新对象的属性描述符集合，遵循Object.create的第二个参数格式
 * @returns 返回创建的新对象，该对象具有以下特点：
 *   1. 如果提供了superProps，新对象会继承superProps的属性
 *   2. 新对象会有一个不可写且不可枚举的__super属性，指向superProps
 *   3. 如果提供了props，这些属性会被添加到新对象上
 *   4. 如果提供了properties，这些属性描述符会被应用到新对象上
 *
 * @example
 * // 基本对象创建
 * const superProps = { parentProp: 'parent' };
 * const props = { childProp: 'child' };
 * const obj = createObject(superProps, props);
 * console.log(obj.parentProp); // 'parent'
 * console.log(obj.childProp); // 'child'
 * console.log(obj.__super === superProps); // true
 *
 * // 处理冻结的原型对象
 * const frozenProps = Object.freeze({ frozen: true });
 * const objFromFrozen = createObject(frozenProps);
 * // frozenProps会被克隆，不会直接使用
 * console.log(Object.isFrozen(objFromFrozen.__super)); // false
 *
 * // 使用属性描述符
 * const objWithDesc = createObject(null, null, {
 *   readonlyProp: {
 *     value: 'readonly',
 *     writable: false,
 *     enumerable: true
 *   }
 * });
 * console.log(objWithDesc.readonlyProp); // 'readonly'
 * objWithDesc.readonlyProp = 'new'; // 无效，属性是只读的
 */
export function createObject(
  superProps?: {[propName: string]: any},
  props?: {[propName: string]: any},
  properties?: any
): object {
  if (superProps && Object.isFrozen(superProps)) {
    superProps = cloneObject(superProps);
  }

  const obj = superProps
    ? Object.create(superProps, {
        ...properties,
        __super: {
          value: superProps,
          writable: false,
          enumerable: false
        }
      })
    : Object.create(Object.prototype, properties);

  props &&
    isObject(props) &&
    Object.keys(props).forEach(key => (obj[key] = props[key]));

  return obj;
}

/**
 * 将对象的__super链转换为数组形式。通过遍历对象的__super属性，从最顶层对象开始，按照链式结构将每个对象添加到数组中。
 * 当前对象会作为数组的最后一个元素。
 *
 * @param {any} value - 要提取__super链的对象。如果为null或undefined，将返回空数组。
 * @returns {Array<object>} 返回一个数组，包含__super链上的所有对象，顺序从最顶层对象到当前对象。
 *
 * @example
 * // 基本对象（无__super链）
 * const obj = {prop: 'value'};
 * const chain = extractObjectChain(obj);
 * // chain = [{prop: 'value'}]
 *
 * @example
 * // 带有__super链的对象
 * const superObj = {superProp: 'super'};
 * const obj = createObject(superObj, {childProp: 'child'});
 * const chain = extractObjectChain(obj);
 * // chain = [{superProp: 'super'}, {childProp: 'child'}]
 *
 * @example
 * // 多层__super链
 * const level1 = {prop1: 'level1'};
 * const level2 = createObject(level1, {prop2: 'level2'});
 * const level3 = createObject(level2, {prop3: 'level3'});
 * const chain = extractObjectChain(level3);
 * // chain = [{prop1: 'level1'}, {prop2: 'level2'}, {prop3: 'level3'}]
 *
 * @example
 * // 处理特殊值
 * extractObjectChain(null);      // 返回 []
 * extractObjectChain(undefined); // 返回 []
 * extractObjectChain({});       // 返回 [{}]
 */
export function extractObjectChain(value: any) {
  const result: Array<object> = value ? [value] : [];
  while (value?.__super) {
    result.unshift(value.__super);
    value = value.__super;
  }
  return result;
}

/**
 * 根据提供的对象链数组创建一个新的对象，保持原型链关系。该函数将数组中的对象按顺序组装成一个继承链，
 * 每个对象都会继承其前一个对象的属性，并通过__super属性维护原型链关系。
 *
 * @param {Array<object>} chain - 要组装的对象链数组。数组中的每个元素都应该是一个对象，
 *                               数组的顺序决定了对象的继承关系（从前到后）。
 * @returns {object} 返回组装后的对象，该对象具有以下特点：
 *   1. 继承了数组中所有对象的属性
 *   2. 每个层级都有一个指向其父对象的__super属性（不可写且不可枚举）
 *   3. 如果遇到冻结的原型对象，会自动创建一个解冻的副本，以便后续可以修改
 *   4. 忽略数组中的空值（null/undefined）
 *   5. 如果数组为空，返回undefined
 *   6. 如果数组只有一个对象，返回的对象的__super属性指向Object.prototype
 *
 * @example
 * // 基本对象链创建
 * const chain = [
 *   {parentProp: 'parent'},
 *   {childProp: 'child'}
 * ];
 * const obj = createObjectFromChain(chain);
 * console.log(obj.parentProp); // 'parent'
 * console.log(obj.childProp); // 'child'
 * console.log(obj.__super.parentProp); // 'parent'
 * console.log(Object.getOwnPropertyDescriptor(obj, '__super').writable); // false
 * console.log(Object.getOwnPropertyDescriptor(obj, '__super').enumerable); // false
 *
 * @example
 * // 处理冻结的原型对象
 * const frozenParent = Object.freeze({frozen: true});
 * const chain = [frozenParent, {child: 'value'}];
 * const obj = createObjectFromChain(chain);
 * console.log(obj.frozen); // true
 * console.log(obj.child); // 'value'
 * // frozenParent会被克隆，不会直接使用
 * console.log(Object.isFrozen(obj.__super)); // false
 * obj.__super.newProp = 'test'; // 可以修改克隆后的对象
 *
 * @example
 * // 处理空值
 * const chain = [{a: 1}, null, undefined, {b: 2}];
 * const obj = createObjectFromChain(chain);
 * // null和undefined值会被过滤掉
 * console.log(obj.a); // 1
 * console.log(obj.b); // 2
 * console.log(obj.__super.a); // 1
 */
export function createObjectFromChain(chain: Array<object>) {
  return chain
    .filter(item => item)
    .reduce((proto, value) => {
      proto = proto || Object.prototype;
      if (Object.isFrozen(proto)) {
        proto = cloneObject(proto);
      }

      return Object.assign(
        Object.create(proto, {
          __super: {
            value: proto,
            writable: false,
            enumerable: false
          }
        }),
        value
      );
    });
}

/**
 * 在对象的__super链中注入一个新对象作为原型。该函数会将注入的对象设置为目标对象的直接原型，
 * 并重建整个对象链，保持原有的继承关系。这对于动态扩展对象的功能特别有用。
 *
 * @param {any} obj - 要注入原型的目标对象。如果对象具有__super链，注入的对象将被放置在链的倒数第二层；
 *                    如果对象没有__super链，将创建一个新的链。
 * @param {any} value - 要注入的对象。这个对象将作为结果对象的直接原型。如果为null或undefined，
 *                     将使用Object.prototype作为原型。
 * @returns {object} 返回一个新的对象，该对象以注入的对象为原型，并保持原有的__super链关系。
 *
 * @example
 * // 基本对象注入
 * const target = {prop: 'target'};
 * const inject = {injectProp: 'inject'};
 * const result = injectObjectChain(target, inject);
 * // result.prop === 'target'
 * // result.injectProp === 'inject'
 * // Object.getPrototypeOf(result) === inject
 *
 * @example
 * // 多层__super链注入
 * const grandParent = {grandProp: 'grand'};
 * const parent = createObject(grandParent, {parentProp: 'parent'});
 * const inject = {injectProp: 'inject'};
 * const result = injectObjectChain(parent, inject);
 * // result.parentProp === 'parent'
 * // result.injectProp === 'inject'
 * // result.grandProp === 'grand'
 *
 * @example
 * // 空值处理
 * const result1 = injectObjectChain(null, {prop: 'value'});
 * // result1.prop === 'value'
 * // Object.getPrototypeOf(result1) === Object.prototype
 *
 * const target = {prop: 'target'};
 * const result2 = injectObjectChain(target, null);
 * // result2.prop === 'target'
 * // Object.getPrototypeOf(result2) === Object.prototype
 */
export function injectObjectChain(obj: any, value: any) {
  const chain = extractObjectChain(obj);
  chain.splice(chain.length - 1, 0, value);
  return createObjectFromChain(chain);
}


/**
 * 创建一个新的对象，将目标对象的所有属性复制到新对象中，并可选择是否保留目标对象自身的属性。
 * 如果目标对象具有__super属性，新对象将继承该属性作为其原型。
 *
 * @param {any} target - 要克隆的目标对象
 * @param {boolean} persistOwnProps - 是否保留目标对象自身的属性，默认为true
 * @returns {object} 克隆后的新对象
 *
 * @example
 * // 基本对象克隆
 * const obj = {a: 1, b: 2};
 * const clone = cloneObject(obj);
 * // clone = {a: 1, b: 2}
 *
 * @example
 * // 不保留目标对象属性
 * const obj = {a: 1, b: 2};
 * const clone = cloneObject(obj, false);
 * // clone = {}
 *
 * @example
 /**
 * // 带有__super属性的对象克隆
 * const superObj = {x: 1};
 * // 使用Object.create创建继承自superObj的对象，并通过Object.defineProperty设置__super
 * const obj = Object.create(superObj, {
 * __super: {
 * value: superObj,
 * writable: false,
 * enumerable: false
 * }
 * });
 * obj.y = 2;
 * const clone = cloneObject(obj);
 * // clone = {y: 2}，并且通过__super属性继承了superObj的属性
 */
export function cloneObject(target: any, persistOwnProps: boolean = true) {
  const obj =
    target && target.__super
      ? Object.create(target.__super, {
          __super: {
            value: target.__super,
            writable: false,
            enumerable: false
          }
        })
      : Object.create(Object.prototype);
  persistOwnProps &&
    target &&
    Object.keys(target).forEach(key => (obj[key] = target[key]));
  return obj;
}

/**
 * 扩展目标对象的功能，将源对象的属性复制到目标对象中，并可选择是否保留目标对象自身的属性。
 * 这个函数会创建一个新的对象，该对象继承自目标对象的原型链，并可以选择性地保留目标对象的属性。
 * 然后将源对象的所有属性复制到新对象中。
 *
 * 特别说明：该函数在创建新对象时，不会保留原对象的原型链。如果目标对象有__super属性，
 * 新对象的原型会指向该__super属性；如果没有__super属性，则使用默认的Object.prototype作为原型。
 *
 * @param {any} target - 要扩展的目标对象
 * @param {any} src - 可选的源对象，其属性将被复制到新对象中
 * @param {boolean} persistOwnProps - 是否保留目标对象自身的属性，默认为true
 * @returns {object} 扩展后的新对象，包含源对象的属性和可选的目标对象属性
 *
 * @example
 * // 基本对象扩展
 * const target = {a: 1, b: 2};
 * const src = {c: 3, d: 4};
 * const result = extendObject(target, src);
 * // result = {a: 1, b: 2, c: 3, d: 4}
 *
 * @example
 * // 不保留目标对象属性
 * const target = {a: 1, b: 2};
 * const src = {c: 3};
 * const result = extendObject(target, src, false);
 * // result = {c: 3}
 *
 * @example
 * // 原型链继承
 * const proto = {inheritedProp: 'value'};
 * const target = Object.create(proto);
 * target.ownProp = 'own';
 * const src = {newProp: 'new'};
 * const result = extendObject(target, src);
 * // result = {ownProp: 'own', newProp: 'new'}
 * // 注意：函数不会保留原型链上的属性，新对象的原型取决于target是否有__super属性
 */
export function extendObject(
  target: any,
  src?: any,
  persistOwnProps: boolean = true
) {
  const obj = cloneObject(target, persistOwnProps);
  src && Object.keys(src).forEach(key => (obj[key] = src[key]));
  return obj;
}

/**
 * 判断一个值是否为对象类型。此函数会排除字符串、数字、布尔值、函数和数组类型。
 *
 * 特别注意：由于实现中使用obj作为条件判断，对于以下特殊值会有特殊的返回行为：
 * - 0：直接返回0而不是false
 * - null：直接返回null而不是false
 * - undefined：直接返回undefined而不是false
 * 这是一个已知的行为，使用时需要注意这些特殊值的处理。
 *
 * @param {any} obj - 要检查的值
 * @returns {boolean|any} 如果值是对象类型则返回true；如果是0、null或undefined则返回原值；其他情况返回false
 *
 * @example
 * // 返回 true
 * isObject({});
 * isObject({key: 'value'});
 * isObject(new Date());
 *
 * // 返回特殊值
 * isObject(0);        // 返回 0
 * isObject(null);     // 返回 null
 * isObject(undefined);// 返回 undefined
 *
 * // 返回 false
 * isObject('string');
 * isObject(123);
 * isObject(true);
 * isObject(() => {});
 * isObject([1, 2, 3]);
 */
export function isObject(obj: any): obj is Record<string, any> {
  const typename = typeof obj;
  return (
    obj &&
    typename !== 'string' &&
    typename !== 'number' &&
    typename !== 'boolean' &&
    typename !== 'function' &&
    !Array.isArray(obj)
  );
}

/**
 * 在对象中设置指定路径的值。支持通过点号分隔的路径来设置深层对象的值。
 * 如果路径中的某些部分不存在，将自动创建对象。如果遇到非纯对象的值，会将其转换为空对象。
 *
 * @param {object} data - 要设置值的目标对象
 * @param {string} key - 要设置的键路径，支持点号分隔，如 'a.b.c'
 * @param {any} value - 要设置的值
 * @param {boolean} [convertKeyToPath=true] - 是否将键转换为路径数组，默认为true
 *
 * @example
 * // 简单键值设置
 * let obj = {};
 * setVariable(obj, 'foo', 'bar');
 * // obj = {foo: 'bar'}
 *
 * // 设置嵌套对象的值
 * let data = {};
 * setVariable(data, 'a.b.c', 123);
 * // data = {a: {b: {c: 123}}}
 *
 * // 处理数组
 * let arr = {list: [1, 2, 3]};
 * setVariable(arr, 'list.0', 100);
 * // arr = {list: [100, 2, 3]}
 *
 * // 不转换键路径
 * let raw = {};
 * setVariable(raw, 'a.b', 'value', false);
 * // raw = {'a.b': 'value'}
 */

export function setVariable(
  data: {[propName: string]: any},
  key: string,
  value: any,
  convertKeyToPath?: boolean
) {
  data = data || {};

  if (key in data) {
    data[key] = value;
    return;
  }

  const parts = convertKeyToPath !== false ? keyToPath(key) : [key];
  const last = parts.pop() as string;

  while (parts.length) {
    let key = parts.shift() as string;
    if (isPlainObject(data[key])) {
      data = data[key] = {
        ...data[key]
      };
    } else if (Array.isArray(data[key])) {
      data[key] = data[key].concat();
      data = data[key];
    } else if (data[key]) {
      // throw new Error(`目标路径不是纯对象，不能覆盖`);
      // 强行转成对象
      data[key] = {};
      data = data[key];
    } else {
      data[key] = {};
      data = data[key];
    }
  }

  data[last] = value;
}

/**
 * 从对象中删除指定路径的值。支持通过点号分隔的路径来删除深层对象的值。
 * 如果路径中的某些部分不存在，将停止删除操作。如果遇到非纯对象的值，会抛出错误。
 *
 * @param {object} data - 要删除值的目标对象
 * @param {string} key - 要删除的键路径，支持点号分隔，如 'a.b.c'
 *
 * @example
 * // 删除简单键值
 * let obj = {foo: 'bar'};
 * deleteVariable(obj, 'foo');
 * // obj = {}
 *
 * // 删除嵌套对象的值
 * let data = {a: {b: {c: 123}}};
 * deleteVariable(data, 'a.b.c');
 * // data = {a: {b: {}}}
 *
 * // 处理不存在的路径
 * let empty = {};
 * deleteVariable(empty, 'a.b.c');
 * // empty = {}
 *
 * // 处理嵌套数组元素（会抛出错误）
 * let arr = {data: [[1, 2], [3, 4]]};
 * deleteVariable(arr, 'data.0.1');
 * // 抛出错误：目标路径不是纯对象，不能修改
 */
export function deleteVariable(data: {[propName: string]: any}, key: string) {
  if (!data) {
    return;
  } else if (data.hasOwnProperty(key)) {
    delete data[key];
    return;
  }

  const parts = keyToPath(key);
  const last = parts.pop() as string;

  while (parts.length) {
    let key = parts.shift() as string;
    if (isPlainObject(data[key])) {
      data = data[key] = {
        ...data[key]
      };
    } else if (data[key]) {
      throw new Error(`目标路径不是纯对象，不能修改`);
    } else {
      break;
    }
  }

  if (data && data.hasOwnProperty && data.hasOwnProperty(last)) {
    delete data[last];
  }
}

/**
 * 从数据对象中提取指定名称的值。支持通过逗号分隔多个名称，并支持使用~符号指定别名。
 * 当输入单个名称时，直接返回对应的值；当输入多个名称时，返回一个包含所有指定值的对象。
 *
 * @param {string} names - 要提取的值名称，支持以下格式：
 *   - 单个名称：直接返回对应的值
 *   - 逗号分隔的多个名称：返回包含所有指定值的对象
 *   - 使用~符号指定别名：格式为"key~target"，其中key是返回对象的属性名，target是数据对象中的属性路径
 * @param {object} data - 包含数据值的对象
 * @returns {any} 提取的值或包含多个值的对象
 *
 * @example
 * // 提取单个值
 * pickValues('name', {name: '张三'}); // 返回 '张三'
 *
 * @example
 * // 提取多个值
 * pickValues('name,age', {name: '张三', age: 20}); // 返回 {name: '张三', age: 20}
 *
 * @example
 * // 使用别名
 * pickValues('userName~name,userAge~age', {name: '张三', age: 20}); // 返回 {userName: '张三', userAge: 20}
 */
export function pickValues(names: string, data: object) {
  let arr: Array<string>;
  if (!names || ((arr = names.split(',')) && arr.length < 2)) {
    let idx = names.indexOf('~');
    if (~idx) {
      let key = names.substring(0, idx);
      let target = names.substring(idx + 1);
      return {
        [key]: resolveVariable(target, data)
      };
    }
    return resolveVariable(names, data);
  }

  let ret: any = {};
  arr.forEach(name => {
    let idx = name.indexOf('~');
    let target = name;

    if (~idx) {
      target = name.substring(idx + 1);
      name = name.substring(0, idx);
    }

    setVariable(ret, name, resolveVariable(target, data));
  });
  return ret;
}

/**
 * 将一个对象原型上的属性拍平至一个新对象中
 * @param {object} obj
 * @returns {object}
 */
/**
 * 将一个对象原型链上的所有属性拍平至一个新对象中，包括自身属性和原型链上的可枚举属性。
 * 如果原型链上的属性在更近的原型中已被定义，则不会被覆盖。
 *
 * @param {any} obj - 要提取原型属性的对象
 * @returns {object} 包含对象自身及所有原型链上可枚举属性的新对象
 *
 * @example
 * // 基本对象
 * const obj = {a: 1};
 * const result = getPrototypeProperties(obj);
 * // result = {a: 1, ...Object.prototype上的可枚举方法}
 *
 * @example
 * // 原型链对象
 * function Parent() { this.parentProp = 'parent'; }
 * Parent.prototype.parentMethod = function() {};
 * function Child() { this.childProp = 'child'; }
 * Child.prototype = new Parent();
 * const child = new Child();
 * const result = getPrototypeProperties(child);
 * // result = {childProp: 'child', parentMethod: function() {}, ...Object.prototype上的可枚举方法}
 */
export function getPrototypeProperties(obj: any) {
    let current = obj;
    const result: any = {...obj};

    while(current !== null) {
      const proto = Object.getPrototypeOf(current);

      if (proto) {
        Object.keys(proto).forEach(key => {
          if (!result.hasOwnProperty(key)) {
            result[key] = proto[key];
          }
        });
      }
      current = proto;
    }

    return result;
}
