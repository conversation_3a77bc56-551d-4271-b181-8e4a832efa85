import {ListenerAction, ListenerContext, runActions} from '../actions/Action';
import {RendererProps} from '../factory';
import {IScopedContext} from '../Scoped';
import { createObject } from './object';
import { isExpression } from './formula';
import {TreeItem, eachTree, findTree, getTree} from './helper';
import debounce from 'lodash/debounce';
import get from 'lodash/get';
import {resolveVariableAndFilterForAsync} from './resolveVariableAndFilterForAsync';
import {evalExpressionWithConditionBuilder} from './tpl';

export interface debounceConfig {
  maxWait?: number;
  wait?: number;
  leading?: boolean;
  trailing?: boolean;
}
// 事件监听器
export interface EventListeners {
  [propName: string]: {
    debounce?: debounceConfig;
    weight?: number; // 权重
    actions: ListenerAction[]; // 执行的动作集
  };
}

// 事件动作属性
export interface OnEventProps {
  onEvent?: {
    [propName: string]: {
      weight?: number; // 权重
      actions: ListenerAction[]; // 执行的动作集,
      debounce?: debounceConfig;
    };
  };
}

// 渲染器事件监听器
export interface RendererEventListener {
  renderer: React.Component<RendererProps>;
  type: string;
  weight: number;
  debounce: debounceConfig | null;
  actions: ListenerAction[];
  executing?: boolean;
  debounceInstance?: any;
}
// 将事件上下文转成事件对象
export type RendererEvent<T, P = any> = {
  context: T;
  type: string;
  prevented?: boolean; // 阻止原有动作执行
  stoped?: boolean; // 阻止后续动作执行
  data?: P;
  preventDefault: () => void;
  stopPropagation: () => void;
  setData: (data: P) => void;
};

export interface RendererEventContext {
  data?: any;
  [propName: string]: any;
}

let rendererEventListeners: RendererEventListener[] = [];

// 创建渲染器事件对象
export function createRendererEvent<T extends RendererEventContext>(
  type: string,
  context: T
): RendererEvent<T> {
  const rendererEvent: RendererEvent<T> = Object.defineProperties(
    {
      context,
      type,
      prevented: false,
      stoped: false,
      preventDefault() {
        rendererEvent.prevented = true;
      },

      stopPropagation() {
        rendererEvent.stoped = true;
      },

      get data() {
        return rendererEvent.context.data;
      },

      setData(data: any) {
        rendererEvent.context.data = data;
      },
    },
    {
      context: {
        // 设置不可枚举，防止resolveVariableAndFilter解析到context.nativeEvent中的react fiber对象循环引用时报错
        enumerable: false
      },
      preventDefault: {
        enumerable: false
      },
      stopPropagation: {
        enumerable: false
      },
      setData: {
        enumerable: false
      },
    }
  );
  return rendererEvent;
}

// 绑定事件
export const bindEvent = (renderer: any) => {
  if (!renderer) {
    return undefined;
  }
  const listeners: EventListeners = renderer.props.$schema.onEvent;
  if (listeners) {
    // 暂存
    for (let key of Object.keys(listeners)) {
      const listener = rendererEventListeners.find(
        (item: RendererEventListener) =>
          item.renderer === renderer && item.type === key
      );
      if (listener?.executing) {
        listener?.debounceInstance?.cancel?.();
        rendererEventListeners = rendererEventListeners.filter(
          (item: RendererEventListener) =>
            !(
              item.renderer === listener.renderer && item.type === listener.type
            )
        );
        rendererEventListeners.push({
          renderer,
          type: key,
          debounce: listener.debounce || null,
          weight: listener.weight || 0,
          actions: listener.actions
        });
      }
      if (!listener) {
        rendererEventListeners.push({
          renderer,
          type: key,
          debounce: listeners[key].debounce || null,
          weight: listeners[key].weight || 0,
          actions: listeners[key].actions
        });
      }
    }

    return () => {
      rendererEventListeners = rendererEventListeners.filter(
        (item: RendererEventListener) => item.renderer !== renderer
      );
    };
  }

  return undefined;
};

// 触发事件
export async function dispatchEvent(
  e: string | React.MouseEvent<any>,
  renderer: React.Component<RendererProps>,
  scoped: IScopedContext,
  data: any,
  broadcast?: RendererEvent<any>
): Promise<RendererEvent<any> | void> {
  let unbindEvent: (() => void) | null | undefined = null;
  const eventName = typeof e === 'string' ? e : e.type;

  renderer?.props?.env?.beforeDispatchEvent?.(
    e,
    renderer,
    scoped,
    data,
    broadcast
  );

  if (!broadcast) {
    const eventConfig = renderer?.props?.onEvent?.[eventName];

    if (!eventConfig) {
      // 没命中也没关系
      return Promise.resolve();
    }

    unbindEvent = bindEvent(renderer);
  }
  // 没有可处理的监听
  if (!rendererEventListeners.length) {
    return Promise.resolve();
  }
  // 如果是广播动作，就直接复用
  const rendererEvent =
    broadcast ||
    createRendererEvent(eventName, {
      env: renderer?.props?.env,
      nativeEvent: e,
      data,
      scoped
    });
  // 过滤&排序
  const listeners = rendererEventListeners
    .filter(
      (item: RendererEventListener) =>
        item.type === eventName &&
        (broadcast ? true : item.renderer === renderer)
    )
    .sort(
      (prev: RendererEventListener, next: RendererEventListener) =>
        next.weight - prev.weight
    );
  let executedCount = 0;
  const checkExecuted = () => {
    executedCount++;
    if (executedCount === listeners.length) {
      unbindEvent?.();
    }
  };
  for (let listener of listeners) {
    const {
      wait = 100,
      trailing = true,
      leading = false,
      maxWait = 10000
    } = listener?.debounce || {};
    if (listener?.debounce) {
      const debounced = debounce(
        async () => {
          await runActions(listener.actions, listener.renderer, rendererEvent);
          checkExecuted();
        },
        wait,
        {
          trailing,
          leading,
          maxWait
        }
      );
      rendererEventListeners.forEach(item => {
        // 找到事件队列中正在执行的事件加上标识，下次待执行队列就会把这个事件过滤掉
        if (
          item.renderer === listener.renderer &&
          listener.type === item.type
        ) {
          item.executing = true;
          item.debounceInstance = debounced;
        }
      });
      debounced();
    } else {
      await runActions(listener.actions, listener.renderer, rendererEvent);
      checkExecuted();
    }

    // 停止后续监听器执行
    if (rendererEvent.stoped) {
      break;
    }
  }
  return Promise.resolve(rendererEvent);
}

export const getRendererEventListeners = () => {
  return rendererEventListeners;
};

/**
 * 兼容历史配置，追加对应name的值
 * 处理渲染器事件数据，合并props.data与事件数据
 *
 * @param props - 组件属性对象，包含:
 *   - data: 组件当前数据
 *   - name: 可选，指定字段名称
 * @param data - 事件数据对象
 * @param valueKey - 可选，事件数据中的值字段名
 *
 * @returns 合并后的数据对象
 *   - 当提供name和valueKey时:
 *     - 将data[valueKey]的值赋给name指定的字段
 *     - 在__rendererData中保存完整数据
 *   - 否则直接返回合并后的数据
 *
 * @example
 * // 基本数据合并
 * resolveEventData({data: {foo: 'bar'}}, {baz: 'qux'})
 * // => {baz: 'qux'}
 *
 * // 使用name和valueKey
 * resolveEventData(
 *   {data: {foo: 'bar'}, name: 'field'},
 *   {value: 'test'},
 *   'value'
 * )
 * // => {
 * //   value: 'test',
 * //   field: 'test',
 * //   __rendererData: {
 * //     foo: 'bar',
 * //     field: 'test'
 * //   }
 * // }、
 */
export const resolveEventData = (props: any, data: any, valueKey?: string) => {
  return createObject(
    props.data,
    props.name && valueKey
      ? {
          ...data,
          [props.name]: data[valueKey],
          __rendererData: {
            ...props.data,
            [props.name]: data[valueKey]
          }
        }
      : data
  );
};

/**
 * 基于 index、condition、ids 获取匹配的事件目标
 * @param tree
 * @param ctx
 * @param index
 * @param condition
 * @param ids
 * @returns
 */
export async function getMatchedEventTargets<T extends TreeItem>(
  tree: Array<T>,
  ctx: any,
  args: any = {},
) {
  const targets: Array<T> = [];
  let { index, condition, ids } = args;
  if (typeof index === 'number') {
    const row = tree[index];
    row && targets.push(row);
  } else if (typeof index === 'string') {
    index = isExpression(index)
      // NOTE: index表达式作为args会提前被解析，暂不清楚什么情况会走到这里
      ? await resolveVariableAndFilterForAsync(index, ctx)
      : index;
    (index as string).split(',').forEach(i => {
      i = i.trim();
      if (i) {
        const indexes = i.split('.').map(ii => parseInt(ii, 10));
        const row: any = getTree(tree, indexes);
        row && targets.push(row);
      }
    });
  } else if (condition) {
    const promies: Array<() => Promise<void>> = [];
    eachTree(tree, item => {
      const data = item.storeType ? item.data : item;
      promies.push(async () => {
        // 解析condition表达式
        const result = await evalExpressionWithConditionBuilder(
          condition,
          createObject(ctx, {...data, ...args})
        );
        result && targets.push(item);
      });
    });
    await Promise.all(promies.map(fn => fn()));
  } else if (ids !== undefined) {
    // 需要进行去重，存在相同的项可能有问题
    const uniqIds: string[] = typeof ids === 'string'
      ? Array.from(new Set(ids.split(',')))
      : [ids];

    uniqIds.forEach(id => {
      id = typeof id === 'string' ? id.trim() : id;
      const findItem = findTree(tree, item => item?.data?.id == id);
      findItem && targets.push(findItem);
    })
  }
  return targets;
}

/**
 * 根据 onEvent 对应事件的 stopDomPropagation 配置（暂不支持变量解析）
 * 判断是否停止 dom事件冒泡，一般用于 click 等事件，不继续冒泡触发 祖先dom 绑定的事件
 * @param renderer 组件
 * @param domEvent DOM事件
 */
export function stopDomPropagation(component: any, domEvent: any) {
  if (!component || !domEvent || typeof domEvent === 'string') {
    return
  }
  const isStop = get(component, `props.$schema.onEvent[${domEvent.type}].stopDomPropagation`);
  if (isStop) {
    // 调用 dom事件的 stopPropagation，阻止冒泡
    domEvent?.stopPropagation?.()
  }
}


export default {};
