import {Evaluator, parse} from 'amis-formula';
import {getVariable} from './getVariable';

/**
 * 解析变量表达式，支持简单变量访问和带命名空间的变量访问
 *
 * @param path - 变量路径，支持以下格式：
 *   - 简单变量访问：'foo.bar' 或 '$foo.bar'
 *   - 带命名空间访问：'window:foo' 或 'ls:foo.bar'
 *   - 特殊值：'&' 或 '$$' 返回整个数据对象
 * @param data - 数据源对象
 * @param canAccessSuper - 是否可以访问原型链上的属性
 * @returns 解析后的变量值，如果解析失败则返回undefined
 *
 * @remarks
 * 与resolveVariableAndFilter函数和getVariable函数的主要区别：
 * 1. 命名空间支持：resolveVariable支持带命名空间的变量访问（如'window:foo'），而getVariable和resolveVariableAndFilter不支持
 * 2. 过滤器语法：resolveVariable不支持过滤器语法，而resolveVariableAndFilter支持过滤器处理
 * 3. 变量访问方式：
 *    - 对于简单变量访问（如'foo.bar'），resolveVariable内部调用getVariable函数来获取值
 *    - 对于带命名空间的访问（如'window:foo'），resolveVariable使用amis-formula解析和求值
 *    - getVariable仅支持简单的对象属性访问，通过递归遍历对象属性链来获取值
 * 4. 特殊值处理：resolveVariable支持通过'&'或'$$'获取整个数据对象，而getVariable和resolveVariableAndFilter没有这个功能
 * 5. 安全性：
 *    - resolveVariable对带命名空间的访问使用amis-formula严格解析，避免出现不合法的变量名
 *    - getVariable则相对宽松，直接通过属性访问获取值
 * 6. $前缀支持：
 *    - resolveVariable支持带$前缀和不带$前缀的变量访问（如'$foo.bar'和'foo.bar'）
 *    - resolveVariableAndFilter和resolveMapping必须使用$前缀（如'$foo.bar'）
 *    - resolveVariable不支持${expression}语法，而resolveVariableAndFilter和resolveMapping支持
 *
 * @example
 * // 简单变量访问（带$前缀和不带$前缀均支持）
 * resolveVariable('user.name', {user: {name: 'amis'}}); // 返回 'amis'
 * resolveVariable('$user.name', {user: {name: 'amis'}}); // 返回 'amis'
 * resolveVariable('items.0.title', {items: [{title: 'First'}]}); // 返回 'First'
 * resolveVariable('$items.0.title', {items: [{title: 'First'}]}); // 返回 'First'
 *
 * // 带命名空间访问
 * resolveVariable('window:location.href', window); // 返回当前页面URL
 * resolveVariable('ls:theme', {theme: 'dark'}); // 返回 'dark'
 *
 * // 特殊值处理
 * const data = {user: {name: 'amis'}, theme: 'dark'};
 * resolveVariable('&', data); // 返回整个data对象: {user: {name: 'amis'}, theme: 'dark'}
 * resolveVariable('$$', data); // 同上，返回整个data对象
 *
 * // ${expression}语法不支持（需使用resolveVariableAndFilter或resolveMapping）
 * resolveVariable('${user.name}', {user: {name: 'amis'}}); // 返回undefined
 * resolveVariable('Hello ${user.name}', {user: {name: 'amis'}}); // 返回undefined
 */
export function resolveVariable(
  path?: string,
  data: any = {},
  canAccessSuper?: boolean
): any {
  if (path === '&' || path == '$$') {
    return data;
  } else if (!path || typeof path !== 'string') {
    return undefined;
  } else if (!~path.indexOf(':')) {
    // 简单用法直接用 getVariable
    return getVariable(
      data,
      path[0] === '$' ? path.substring(1) : path,
      canAccessSuper
    );
  }

  // window:xxx  ls:xxx.xxx
  // 带 namespace 的用公式
  // 主要是用公式会严格点，不能出现奇怪的变量名
  try {
    return new Evaluator(data).evalute(
      parse(path, {
        variableMode: true,
        allowFilter: false
      })
    );
  } catch (e) {
    return undefined;
  }
}
