import {Evaluator, parse} from 'amis-formula';

/**
 * 解析变量和过滤器表达式，支持通过amis-formula进行变量解析和过滤器处理
 * 注意：该函数仅支持以$开头的变量路径解析，不带$前缀的输入会直接返回原始值
 *
 * @param path - 变量路径表达式，必须以$开头，支持过滤器语法，如: "$foo.bar | html"
 * @param data - 数据源对象，用于解析变量值
 * @param defaultFilter - 默认过滤器，当表达式中未指定过滤器时使用
 * @param fallbackValue - 回退值处理函数，当解析结果为null且path中不包含default/now时调用。
 *                       不包含default/now的原因是这两个过滤器已经内置了空值处理逻辑：
 *                       default过滤器会在值为空时返回指定的默认值，
 *                       now过滤器会在值为空时返回当前时间戳。
 *
 * @returns 解析后的值，如果解析失败则返回undefined
 *
 * @remarks
 * 与resolveVariableAndFilterForAsync函数的主要区别：
 * 1. 异步支持：resolveVariableAndFilterForAsync支持异步函数调用和异步过滤器处理，而resolveVariableAndFilter只支持同步操作
 * 2. 求值器类型：resolveVariableAndFilterForAsync使用AsyncEvaluator进行表达式求值，而resolveVariableAndFilter使用Evaluator
 * 3. 返回值类型：resolveVariableAndFilterForAsync返回Promise对象，需要使用await等待结果，而resolveVariableAndFilter直接返回结果
 *
 * 与tokenize函数的主要区别：
 * 1. 错误处理：tokenize在解析失败时返回原始字符串，而resolveVariableAndFilter返回undefined
 * 2. 返回值类型：tokenize总是将结果转换为字符串类型并返回，而resolveVariableAndFilter保持原始类型不变
 * @example
 * // 纯变量
 * resolveVariableAndFilter("$user.name", {user: {name: "John"}}); // "John"
 *
 * // 带过滤器的变量
 * resolveVariableAndFilter("$user.name | upper", {user: {name: "John"}}); // "JOHN"
 *
 * // 使用默认过滤器
 * resolveVariableAndFilter("$user.html", {user: {html: "<p>content</p>"}}); // 转义后的HTML
 *
 * // 自定义过滤器覆盖默认过滤器
 * resolveVariableAndFilter("$user.html | raw", {user: {html: "<p>content</p>"}}); // "<p>content</p>"
 *
 * // 使用default过滤器
 * resolveVariableAndFilter("$user.age | default:18", {user: {}}); // 18
 *
 * // 使用now过滤器
 * resolveVariableAndFilter("$time | now", {}); // 当前时间戳
 *
 * // 回退值处理
 * resolveVariableAndFilter("$user.age", {user: {}}, "| html", v => 0); // 0
 *
 * // 混合字符串和变量
 * resolveVariableAndFilter("Welcome ${user.name}!", {user: {name: "John"}}); // "Welcome John!"
 *
 * // 多个变量混合
 * resolveVariableAndFilter("${user.name} is ${user.age} years old", {user: {name: "John", age: 25}}); // "John is 25 years old"
 *
 * // ${} 语法与过滤器组合
 * resolveVariableAndFilter("${user.name | upper} joined on ${joinDate | date}", {user: {name: "john"}, joinDate: "2023-01-01"}); // "JOHN joined on 2023-01-01"
 */
export const resolveVariableAndFilter = (
  path?: string,
  data: object = {},
  defaultFilter: string = '| html',
  fallbackValue = (value: any) => value
) => {
  if (!path || typeof path !== 'string') {
    return undefined;
  }

  try {
    const ast = parse(path, {
      evalMode: false,
      allowFilter: true
    });

    const ret = new Evaluator(data, {
      defaultFilter
    }).evalute(ast);

    return ret == null && !~path.indexOf('default') && !~path.indexOf('now')
      ? fallbackValue(ret)
      : ret;
  } catch (e) {
    console.warn(e);
    return undefined;
  }
};
