import {AsyncEvaluator, parse} from 'amis-formula';

/**
 * 异步解析变量和过滤器表达式
 *
 * @param path - 变量路径表达式，支持使用过滤器，如："${var | html}"
 * @param data - 数据源对象，用于解析变量值
 * @param defaultFilter - 默认过滤器，当表达式中未指定过滤器时使用
 * @param fallbackValue - 回退值处理函数，当解析结果为null且路径中不包含default和now时调用
 * @returns 返回解析后的值，如果解析失败则返回undefined
 *
 * @remarks
 * 与resolveVariableAndFilter函数的主要区别：
 * 1. 异步支持：resolveVariableAndFilterForAsync支持异步函数调用和异步过滤器处理，而resolveVariableAndFilter只支持同步操作
 * 2. 求值器类型：resolveVariableAndFilterForAsync使用AsyncEvaluator进行表达式求值，而resolveVariableAndFilter使用Evaluator
 * 3. 返回值类型：resolveVariableAndFilterForAsync返回Promise对象，需要使用await等待结果，而resolveVariableAndFilter直接返回结果
 *
 * @example
 * // 基本变量解析
 * await resolveVariableAndFilterForAsync("${user.name}", {user: {name: "amis"}})
 * // 结果: "amis"
 *
 * // 带过滤器的变量解析
 * await resolveVariableAndFilterForAsync("${items | join}", {items: [1, 2, 3]})
 * // 结果: "1,2,3"
 *
 * // 异步处理场景
 * registerFunction('getData', async () => ({id: 1}));
 * await resolveVariableAndFilterForAsync("${getData()}", {})
 * // 结果: {"id":1}
 */
export const resolveVariableAndFilterForAsync = async (
  path?: string,
  data: object = {},
  defaultFilter: string = '| html',
  fallbackValue = (value: any) => value
) => {
  if (!path || typeof path !== 'string') {
    return undefined;
  }

  try {
    const ast = parse(path, {
      evalMode: false,
      allowFilter: true
    });

    const ret = await new AsyncEvaluator(data, {
      defaultFilter
    }).evalute(ast);

    return ret == null && !~path.indexOf('default') && !~path.indexOf('now')
      ? fallbackValue(ret)
      : ret;
  } catch (e) {
    console.warn(e);
    return undefined;
  }
};
