/**
 * 将普通字符串转换为正则表达式，自动处理特殊字符的转义
 *
 * @param value - 要转换的字符串
 * @param caseSensitive - 是否区分大小写，默认为 false（不区分大小写）
 * @returns 返回一个新的正则表达式对象
 * @throws {TypeError} 当输入值不是字符串类型时抛出错误
 *
 * @example
 * ```ts
 * // 转换普通字符串，自动转义特殊字符
 * string2regExp('hello.world') // 结果: /hello\.world/i
 *
 * // 区分大小写模式
 * string2regExp('Test', true) // 结果: /Test/
 * ```
 */
export function string2regExp(value: string, caseSensitive = false) {
  if (typeof value !== 'string') {
    throw new TypeError('Expected a string');
  }

  return new RegExp(
    value.replace(/[|\\{}()[\]^$+*?.]/g, '\\$&').replace(/-/g, '\\x2d'),
    !caseSensitive ? 'i' : ''
  );
}
