import {Evaluator, parse} from 'amis-formula';

/**
 * 解析字符串中的变量和过滤器表达式
 *
 * @param str - 要解析的字符串，可以包含变量表达式（如：${xxx}）和过滤器（如：${xxx | html}）
 * @param data - 数据源对象，用于解析字符串中的变量表达式
 * @param defaultFilter - 默认的过滤器，当变量表达式没有指定过滤器时使用，默认为'| html'
 * @returns 解析后的字符串。如果解析失败，则返回原始字符串
 *
 * @remarks
 * 与resolveVariableAndFilter函数的主要区别：
 * 1. 错误处理：tokenize在解析失败时返回原始字符串，而resolveVariableAndFilter返回undefined
 * 2. 返回值类型：tokenize总是将结果转换为字符串类型并返回，而resolveVariableAndFilter保持原始类型不变
 *
 * @example
 * // 纯字符串
 * tokenize("Hello", {}) // 输出: "Hello"
 *
 * // 带变量的字符串
 * tokenize("Hello ${name}", {name: "World"}) // 输出: "Hello World"
 *
 * // 带过滤器的变量
 * tokenize("${name | upperCase}", {name: "world"}) // 输出: "WORLD"
 */
export const tokenize = (
  str: string,
  data: object,
  defaultFilter: string = '| html'
) => {
  if (!str || typeof str !== 'string') {
    return str;
  }

  try {
    const ast = parse(str, {
      evalMode: false,
      allowFilter: true
    });
    const result = new Evaluator(data, {
      defaultFilter
    }).evalute(ast);

    return `${result == null ? '' : result}`;
  } catch (e) {
    console.warn(e);
    return str;
  }
};
