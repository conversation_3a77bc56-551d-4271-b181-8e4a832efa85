import moment from 'moment';
import {filter,} from './tpl';
import { isDisabled, isVisible } from '.';
import { ConditionRule, ConditionGroupValue } from '../types';
import { createObject, getVariable } from '.';
import { getRendererByName } from '../factory';
import {constant, isEqual} from 'lodash';
import {isPureVariable, resolveVariableAndFilter} from './tpl-builtin';
import {getExprProperties} from './filter-schema';
import type {MomentInput, unitOfTime, MomentFormatSpecification} from 'moment';

const isExisty = (value: any) => value !== null && value !== undefined;
const isEmpty = (value: any) => value === '';
const makeRegexp = (reg: string | RegExp) => {
  try {
    if (reg instanceof RegExp) {
      return reg;
    } else if (/^(?:matchRegexp\:)?\/(.+)\/([gimuy]*)$/.test(reg)) {
      return new RegExp(RegExp.$1, RegExp.$2 || '');
    } else if (typeof reg === 'string') {
      return new RegExp(reg);
    }

    return /^$/;
  } catch (e) {
    console.warn('Invalid regexp pattern:', reg, e);
    return /^$/;
  }
};
import memoize from 'lodash/memoize';
import isPlainObject from 'lodash/isPlainObject';

const makeUrlRegexp = memoize(function (options: any) {
  options = {
    schemes: ['http', 'https', 'ftp', 'sftp'],
    allowLocal: true,
    allowDataUrl: false,
    ...(isPlainObject(options) ? options : {})
  };

  // https://github.com/ansman/validate.js/blob/master/validate.js#L1098-L1164
  let {schemes, allowLocal, allowDataUrl} = options;

  if (!Array.isArray(schemes)) {
    schemes = ['http', 'https', 'ftp', 'sftp'];
  }

  let regex =
    '^' +
    // protocol identifier
    '(?:(?:' +
    schemes.join('|') +
    ')://)' +
    // user:pass authentication
    '(?:\\S+(?::\\S*)?@)?' +
    '(?:';

  var tld = '(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))';

  if (allowLocal) {
    tld += '?';
  } else {
    regex +=
      // IP address exclusion
      // private & local networks
      '(?!(?:10|127)(?:\\.\\d{1,3}){3})' +
      '(?!(?:169\\.254|192\\.168)(?:\\.\\d{1,3}){2})' +
      '(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})';
  }

  regex +=
    // IP address dotted notation octets
    // excludes loopback network 0.0.0.0
    // excludes reserved space >= *********
    // excludes network & broacast addresses
    // (first & last IP address of each class)
    '(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])' +
    '(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}' +
    '(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))' +
    '|' +
    // host name
    '(?:(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)' +
    // domain name
    '(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*' +
    tld +
    ')' +
    // port number
    '(?::\\d{2,5})?' +
    // resource path
    '(?:[/?#]\\S*)?' +
    '$';

  if (allowDataUrl) {
    // RFC 2397
    var mediaType = '\\w+\\/[-+.\\w]+(?:;[\\w=]+)*';
    var urlchar = "[A-Za-z0-9-_.!~\\*'();\\/?:@&=+$,%]*";
    var dataurl = 'data:(?:' + mediaType + ')?(?:;base64)?,' + urlchar;
    regex = '(?:' + regex + ')|(?:^' + dataurl + '$)';
  }

  return new RegExp(regex, 'i');
});

export interface ValidateFn {
  (
    values: {[propsName: string]: any},
    value: any,
    arg1?: any,
    arg2?: any,
    arg3?: any,
    arg4?: any,
    arg5?: any
  ): boolean | {
    error: boolean;
    msg?: string;
  };
}

export const validations: {
  [propsName: string]: ValidateFn;
} = {
  isRequired: function (values, value: any) {
    return (
      value !== undefined &&
      value !== '' &&
      value !== null &&
      (!Array.isArray(value) || !!value.length)
    );
  },
  isRequiredForConditionBuilder: function (values, value: any) {
    // 组件没值，或者组件没有添加任何条件/条件组。
    // 添加空条件组的情况先不判断，看实际使用再调整。
    if (value === undefined || value.children.length === 0) {
      return false;
    }

    // const isAllChildrenGroup = value.children.every((item: ConditionRule | ConditionGroupValue) => {
    //   return 'children' in item
    // })

    // if (isAllChildrenGroup) {
    //   return value.children.every((item: ConditionGroupValue) => {
    //     return validations.isRequiredForConditionBuilder(values, item)
    //   })
    // }

    return true
  },
  isExisty: function (values, value) {
    return isExisty(value);
  },
  matchRegexp: function (values, value, regexp) {
    return !isExisty(value) || isEmpty(value) || makeRegexp(regexp).test(value);
  },
  isUndefined: function (values, value) {
    return value === undefined;
  },
  isEmptyString: function (values, value) {
    return isEmpty(value);
  },
  isEmail: function (values, value) {
    return validations.matchRegexp(
      values,
      value,
      /^((([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+(\.([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+)*)|((\x22)((((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(([\x01-\x08\x0b\x0c\x0e-\x1f\x7f]|\x21|[\x23-\x5b]|[\x5d-\x7e]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(\\([\x01-\x09\x0b\x0c\x0d-\x7f]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))))*(((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(\x22)))@((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))$/i
    );
  },
  isUrl: function (values, value, options) {
    return validations.matchRegexp(values, value, makeUrlRegexp(options));
  },
  isTrue: function (values, value) {
    return value === true;
  },
  isFalse: function (values, value) {
    return value === false;
  },
  isNumeric: function (values, value) {
    if (typeof value === 'number') {
      return true;
    }
    return validations.matchRegexp(values, value, /^[-+]?(?:\d*[.])?\d+$/);
  },
  isAlpha: function (values, value) {
    return validations.matchRegexp(values, value, /^[A-Z]+$/i);
  },
  isAlphanumeric: function (values, value) {
    return validations.matchRegexp(values, value, /^[0-9A-Z]+$/i);
  },
  isInt: function (values, value) {
    return validations.matchRegexp(values, value, /^(?:[-+]?(?:0|[1-9]\d*))$/);
  },
  isFloat: function (values, value) {
    return validations.matchRegexp(
      values,
      value,
      /^(?:[-+]?(?:\d+))?(?:\.\d*)?(?:[eE][\+\-]?(?:\d+))?$/
    );
  },
  isWords: function (values, value) {
    return validations.matchRegexp(values, value, /^[A-Z\s]+$/i);
  },
  isSpecialWords: function (values, value) {
    return validations.matchRegexp(values, value, /^[A-Z\s\u00C0-\u017F]+$/i);
  },
  isLength: function (values, value, length) {
    // 此方法应该判断文本长度，如果传入数据为number，导致 maxLength 和 maximum 表现一致了，默认转成string
    if (typeof value === 'number') {
      value = String(value);
    }

    return !isExisty(value) || isEmpty(value) || value.length === length;
  },
  equals: function (values, value, eql) {
    return !isExisty(value) || isEmpty(value) || value == eql;
  },
  equalsField: function (values, value, field) {
    return value == values[field];
  },
  isUnique: function (values, value, fields, columns) {
    // value必须是数组，才有比较的意义
    if (!fields || !Array.isArray(value)) {
      return true;
    }
    if (!Array.isArray(fields)) {
      fields = [fields];
    }

    // 使用Map存储已经出现过的值
    const seen = new Map();
    // 遍历数组，检查每个元素与其他元素是否存在重复
    for (let i = 0; i < value.length; i++) {
      // 生成当前元素的唯一键
      const key = fields.map((field : string) => {
        const fieldValue = getVariable(value[i], field);
        return JSON.stringify(fieldValue);
      }).join('|');
       // 如果键已经存在，说明有重复
      if (seen.has(key)) {
        const erorFields = (columns || fields).join('+');
        return {error: true, msg: `${erorFields} 的值不唯一, 第${seen.get(key) + 1}行和第${i + 1}行重复`};
      }
      seen.set(key, i);
    }

    return true;
  },
  arrayMinLength: function (values, value, length) {
    if (Array.isArray(value)) {
      return value.length >= length;
    }
    return false;
  },
  arrayMaxLength: function (values, value, length) {
    if (Array.isArray(value)) {
      return value.length <= length;
    }
    return false;
  },
  maxLength: function (values, value, length) {
    // 此方法应该判断文本长度，如果传入数据为number，导致 maxLength 和 maximum 表现一致了，默认转成string
    if (typeof value === 'number') {
      value = String(value);
    }
    return !isExisty(value) || value.length <= length;
  },
  minLength: function (values, value, length) {
    // 此方法应该判断文本长度，如果传入数据为number，导致 maxLength 和 maximum 表现一致了，默认转成string
    if (typeof value === 'number') {
      value = String(value);
    }
    return !isExisty(value) || isEmpty(value) || value.length >= length;
  },
  isUrlPath: function (values, value, regexp) {
    return !isExisty(value) || isEmpty(value) || /^[a-z0-9_\\-]+$/i.test(value);
  },
  maximum: function (values, value, maximum) {
    return (
      !isExisty(value) ||
      isEmpty(value) ||
      (parseFloat(value) || 0) <= (parseFloat(maximum) || 0)
    );
  },
  lt: function (values, value, maximum) {
    return (
      !isExisty(value) ||
      isEmpty(value) ||
      (parseFloat(value) || 0) < (parseFloat(maximum) || 0)
    );
  },
  minimum: function (values, value, minimum) {
    return (
      !isExisty(value) ||
      isEmpty(value) ||
      (parseFloat(value) || 0) >= (parseFloat(minimum) || 0)
    );
  },
  gt: function (values, value, minimum) {
    return (
      !isExisty(value) ||
      isEmpty(value) ||
      (parseFloat(value) || 0) > (parseFloat(minimum) || 0)
    );
  },
  isJson: function (values, value, minimum) {
    if (isExisty(value) && !isEmpty(value) && typeof value === 'string') {
      try {
        const result = JSON.parse(value);
        if (typeof result === 'object' && result) {
          return true;
        }
        return false;
      } catch (e) {
        return false;
      }
    }
    return true;
  },
  isPhoneNumber: function (values, value) {
    return (
      !isExisty(value) || isEmpty(value) || /^[1]([3-9])[0-9]{9}$/.test(value)
    );
  },
  isTelNumber: function (values, value) {
    return (
      !isExisty(value) ||
      isEmpty(value) ||
      /^(\(\d{3,4}\)|\d{3,4}-|\s)?\d{7,14}$/.test(value)
    );
  },
  isZipcode: function (values, value) {
    return !isExisty(value) || isEmpty(value) || /^\d{6}$/.test(value);
  },
  isId: function (values, value) {
    return (
      !isExisty(value) ||
      isEmpty(value) ||
      /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}$)/.test(
        value
      )
    );
  },
  notEmptyString: function (values, value) {
    return !isExisty(value) || !(String(value) && String(value).trim() === '');
  },
  matchRegexp1: function (values, value, regexp) {
    return validations.matchRegexp(values, value, regexp);
  },
  matchRegexp2: function (values, value, regexp) {
    return validations.matchRegexp(values, value, regexp);
  },
  matchRegexp3: function (values, value, regexp) {
    return validations.matchRegexp(values, value, regexp);
  },
  matchRegexp4: function (values, value, regexp) {
    return validations.matchRegexp(values, value, regexp);
  },
  matchRegexp5: function (values, value, regexp) {
    return validations.matchRegexp(values, value, regexp);
  },
  matchRegexp6: function (values, value, regexp) {
    return validations.matchRegexp(values, value, regexp);
  },
  matchRegexp7: function (values, value, regexp) {
    return validations.matchRegexp(values, value, regexp);
  },
  matchRegexp8: function (values, value, regexp) {
    return validations.matchRegexp(values, value, regexp);
  },
  matchRegexp9: function (values, value, regexp) {
    return validations.matchRegexp(values, value, regexp);
  },
  /** ============================ 日期时间相关 ============================= */
  isDateTimeSame: (
    values,
    value: MomentInput,
    targetDate: MomentInput,
    granularity?: unitOfTime.StartOf
  ) => {
    return moment(value).isSame(moment(targetDate), granularity);
  },
  isDateTimeBefore: (
    values,
    value: MomentInput,
    targetDate: MomentInput,
    granularity?: unitOfTime.StartOf
  ) => {
    return moment(value).isBefore(moment(targetDate), granularity);
  },
  isDateTimeAfter: (
    values,
    value: MomentInput,
    targetDate: MomentInput,
    granularity?: unitOfTime.StartOf
  ) => {
    return moment(value).isAfter(moment(targetDate), granularity);
  },
  isDateTimeSameOrBefore: (
    values,
    value: MomentInput,
    targetDate: MomentInput,
    granularity?: unitOfTime.StartOf
  ) => {
    return moment(value).isSameOrBefore(moment(targetDate), granularity);
  },
  isDateTimeSameOrAfter: (
    values,
    value: MomentInput,
    targetDate: MomentInput,
    granularity?: unitOfTime.StartOf
  ) => {
    return moment(value).isSameOrAfter(moment(targetDate), granularity);
  },
  isDateTimeBetween: (
    values,
    value: MomentInput,
    lhs: MomentInput,
    rhs: MomentInput,
    granularity?: unitOfTime.StartOf,
    inclusivity?: '()' | '[)' | '(]' | '[]'
  ) => {
    return moment(value).isBetween(
      moment(lhs),
      moment(rhs),
      granularity,
      inclusivity
    );
  },
  /** ============================ 时间相关 ============================= */
  isTimeSame: (
    values,
    value: MomentInput,
    targetTime: MomentInput,
    granularity?: unitOfTime.StartOf,
    format?: MomentFormatSpecification
  ) => {
    // 直接使用时间构造的moment object是不合法的，所以需要额外指定一下格式
    format = format ?? 'hh:mm:ss';
    return moment(value, format).isSame(
      moment(targetTime, format),
      granularity
    );
  },
  isTimeBefore: (
    values,
    value: MomentInput,
    targetTime: MomentInput,
    granularity?: unitOfTime.StartOf,
    format?: MomentFormatSpecification
  ) => {
    format = format ?? 'hh:mm:ss';
    return moment(value, format).isBefore(
      moment(targetTime, format),
      granularity
    );
  },
  isTimeAfter: (
    values,
    value: MomentInput,
    targetTime: MomentInput,
    granularity?: unitOfTime.StartOf,
    format?: MomentFormatSpecification
  ) => {
    format = format ?? 'hh:mm:ss';
    return moment(value, format).isAfter(
      moment(targetTime, format),
      granularity
    );
  },
  isTimeSameOrBefore: (
    values,
    value: MomentInput,
    targetTime: MomentInput,
    granularity?: unitOfTime.StartOf,
    format?: MomentFormatSpecification
  ) => {
    format = format ?? 'hh:mm:ss';
    return moment(value, format).isSameOrBefore(
      moment(targetTime, format),
      granularity
    );
  },
  isTimeSameOrAfter: (
    values,
    value: MomentInput,
    targetTime: MomentInput,
    granularity?: unitOfTime.StartOf,
    format?: MomentFormatSpecification
  ) => {
    format = format ?? 'hh:mm:ss';
    return moment(value, format).isSameOrAfter(
      moment(targetTime, format),
      granularity
    );
  },
  isTimeBetween: (
    values,
    value: MomentInput,
    lhs: MomentInput,
    rhs: MomentInput,
    granularity?: unitOfTime.StartOf,
    inclusivity?: '()' | '[)' | '(]' | '[]',
    format?: MomentFormatSpecification
  ) => {
    format = format ?? 'hh:mm:ss';
    return moment(value, format).isBetween(
      moment(lhs, format),
      moment(rhs, format),
      granularity,
      inclusivity
    );
  },
  isVariableName: function (values, value, regexp) {
    return validations.matchRegexp(
      values,
      value,
      regexp instanceof RegExp ? regexp : /^[a-zA-Z_]+[a-zA-Z0-9_]*$/
    );
  }
};

export function addRule(
  ruleName: string,
  fn: ValidateFn,
  message: string = ''
) {
  validations[ruleName] = fn;
  validateMessages[ruleName] = message;
}

export const validateMessages: {
  [propName: string]: string;
} = {
  isEmail: 'validate.isEmail',
  isRequired: 'validate.isRequired',
  isRequiredForConditionBuilder: 'validate.isRequired',
  isUrl: 'validate.isUrl',
  isInt: 'validate.isInt',
  isAlpha: 'validate.isAlpha',
  isNumeric: 'validate.isNumeric',
  isAlphanumeric: 'validate.isAlphanumeric',
  isFloat: 'validate.isFloat',
  isWords: 'validate.isWords',
  isUrlPath: 'validate.isUrlPath',
  matchRegexp: 'validate.matchRegexp',
  minLength: 'validate.minLength',
  maxLength: 'validate.maxLength',
  minLengthArray: 'validate.array.minLength',
  maxLengthArray: 'validate.array.maxLength',
  arrayMinLength: 'validate.arrayMinLength',
  arrayMaxLength: 'validate.arrayMaxLength',
  maximum: 'validate.maximum',
  lt: 'validate.lt',
  minimum: 'validate.minimum',
  gt: 'validate.gt',
  isJson: 'validate.isJson',
  isLength: 'validate.isLength',
  notEmptyString: 'validate.notEmptyString',
  equalsField: 'validate.equalsField',
  equals: 'validate.equals',
  isPhoneNumber: 'validate.isPhoneNumber',
  isTelNumber: 'validate.isTelNumber',
  isZipcode: 'validate.isZipcode',
  isId: 'validate.isId',
  isDateTimeSame: 'validate.isDateTimeSame',
  isDateTimeBefore: 'validate.isDateTimeBefore',
  isDateTimeAfter: 'validate.isDateTimeAfter',
  isDateTimeSameOrBefore: 'validate.isDateTimeSameOrBefore',
  isDateTimeSameOrAfter: 'validate.isDateTimeSameOrAfter',
  isDateTimeBetween: 'validate.isDateTimeBetween',
  isTimeSame: 'validate.isTimeSame',
  isTimeBefore: 'validate.isTimeBefore',
  isTimeAfter: 'validate.isTimeAfter',
  isTimeSameOrBefore: 'validate.isTimeSameOrBefore',
  isTimeSameOrAfter: 'validate.isTimeSameOrAfter',
  isTimeBetween: 'validate.isTimeBetween',
  isVariableName: 'validate.isVariableName'
};

/**
 * 表单验证函数，用于验证单个表单项的值是否符合指定的验证规则
 *
 * @param value - 需要验证的表单项的值
 * @param values - 整个表单的值集合，用于跨字段验证
 * @param rules - 验证规则配置对象，key为规则名称，value为规则参数
 *               规则可以是预定义的验证函数名称(如isEmail、isRequired等)
 *               也可以是自定义的验证规则
 * @param messages - 自定义错误消息配置对象，key为规则名称，value为错误消息
 * @param __ - 国际化翻译函数，默认直接返回原字符串
 *
 * @returns 返回验证错误信息数组
 *          如果验证通过，返回空数组
 *          如果验证失败，返回包含rule(规则名)和msg(错误消息)的对象数组
 *
 * @example
 * // 基础验证
 * validate('<EMAIL>', {value: '<EMAIL>'}, {isEmail: true})
 *
 * // 带自定义消息的验证
 * validate('123', {value: '123'}, {isEmail: true}, {isEmail: '请输入有效的邮箱地址'})
 *
 * // 跨字段验证
 * validate('password2', {password1: 'xxx', password2: 'password2'}, {equalsField: 'password1'})
 */
export function validate(
  value: any,
  values: {[propName: string]: any},
  rules: {[propName: string]: any},
  messages?: {[propName: string]: string},
  __ = (str: string) => str
): Array<{
  rule: string;
  msg: string;
}> {
  const errors: Array<{
    rule: string;
    msg: string;
  }> = [];


  if (rules) {
    const ruleNames = Object.keys(rules);
    const length = ruleNames.length;
    for (let index = 0; index < length; index++) {
      const ruleName = ruleNames[index];
      const validationName = ruleName.replace(/\$+$/, '');
      if (!rules[ruleName] && rules[ruleName] !== 0) {
        continue;
      } else if (typeof validations[validationName] !== 'function') {
        throw new Error('Validation `' + validationName + '` not exists!');
      }

      const fn = validations[validationName];
      const args = (
        Array.isArray(rules[ruleName]) ? rules[ruleName] : [rules[ruleName]]
      ).map((item: any) => {
        if (typeof item === 'string' && isPureVariable(item)) {
          return resolveVariableAndFilter(item, values, '|raw');
        }

        return item;
      });

      let validateRes = fn(values, value, ...args);

      // addRule 允许返回
      //   {error: true, msg: '错误提示'}
      // 格式的信息来灵活展示错误
      let fnResErrorMsg = '';
      if (
        typeof validateRes === 'object' &&
        validateRes.error === true
      ) {
        fnResErrorMsg = validateRes?.msg ?? '';
      }

      if (!validateRes || fnResErrorMsg) {
        let msgRuleName = validationName;
        if (Array.isArray(value)) {
          msgRuleName = `${validationName}Array`;
        }

        return [{
          rule: ruleName,
          msg: filter(
            __(
                (messages && messages[ruleName]) ||
                fnResErrorMsg ||
                validateMessages[msgRuleName] ||
                validateMessages[validationName]
            ),
            {
              ...[''].concat(args)
            }
          )
        }];
      }
    }
  }

  return errors;
}

/**
 * 对象级别的表单验证函数，用于验证一个对象中多个字段的值是否符合指定的验证规则
 *
 * @param values - 需要验证的对象，包含多个字段的值集合
 * @param rules - 验证规则配置对象，key为字段名称，value为该字段的验证规则
 *               如果value为true，则表示该字段为必填项
 *               如果value为对象，则包含该字段的具体验证规则
 * @param messages - 自定义错误消息配置对象，key为字段名称.规则名称，value为错误消息
 * @param __ - 国际化翻译函数，默认直接返回原字符串
 *
 * @returns 返回验证错误信息对象
 *          如果所有字段验证通过，返回空对象
 *          如果有字段验证失败，返回以字段名为key，包含rule(规则名)和msg(错误消息)的数组为value的对象
 *
 * @example
 * // 基础对象验证
 * validateObject(
 *   {name: 'amis', age: 1},
 *   {name: {isRequired: true}, age: {minimum: 18}}
 * )
 *
 * // 带自定义消息的验证
 * validateObject(
 *   {email: '123'},
 *   {email: {isEmail: true}},
 *   {email: {isEmail: '请输入有效的邮箱地址'}}
 * )
 *
 * // 必填项简写验证
 * validateObject(
 *   {name: ''},
 *   {name: true} // 等同于 {name: {isRequired: true}}
 * )
 */
export function validateObject(
  values: {[propName: string]: any},
  rules: {[propName: string]: any},
  messages?: {[propName: string]: string},
  __ = (str: string) => str
) {
  const ret: {
    [propName: string]: {
      rule: string;
      msg: string;
    }[];
  } = {};

  Object.keys(rules).forEach(key => {
    const msgs = validate(
      values[key],
      values,
      rules[key] === true
        ? {
            isRequired: true
          }
        : rules[key],
      messages,
      __
    );

    if (msgs.length) {
      ret[key] = msgs;
    }
  });

  return ret;
}

// 将直接配置在组件层的校验规则和配置在validation里的校验规则，转换为统一格式。
// 这里先根据组件类型hard code，后续改造为组件注册的时候可提供自己的normalziedValidations函数。
export function normalizeValidations(item: {[key: string]: any}): {[key: string]: any} {
  let rules = item.validations || {};
  rules = {
    ...rules,
    isRequired: item.required || rules?.isRequired
  };

  if (item.type === 'condition-builder') {
    if (rules.isRequired !== undefined) {
      rules.isRequiredForConditionBuilder = rules.isRequired;
      delete rules.isRequired;
    }
  }

  // 优化下校验性能，如果没有配置required，就删除掉isRequired。
  if (rules.isRequired === undefined) {
    delete rules.isRequired;
  }

  if (~['input-text', 'textarea'].indexOf(item.type)) {
    if (typeof item.minLength === 'number') {
      rules.minLength = item.minLength;
    }

    if (typeof item.maxLength === 'number') {
      rules.maxLength = item.maxLength;
    }
  }

  if (~['combo', 'input-kvs', 'input-kv', 'input-table', 'input-array'].indexOf(item.type)) {
    // 这里先不判断类型，有可能是表达式。
    if (item.minLength !== undefined) {
      rules.arrayMinLength = item.minLength;
    }

    if (item.maxLength !== undefined) {
      rules.arrayMaxLength = item.maxLength;
    }
  }

  return rules;
}

/**
 * 根据复合FormItem组件（如InputTable、Combo等组件）的items定义，对组件的最终值进行验证
 *
 * 该函数主要用于验证表格、组合表单等复合组件中每一行、每一列的数据是否符合验证规则。
 * 支持嵌套组件的递归验证，以及动态表达式属性的处理（如requiredOn、visibleOn等）。
 *
 * @param value - 需要验证的数据值，通常是数组格式（每个元素代表一行数据）
 *                如果不是数组，会自动转换为单元素数组进行处理
 * @param items - 复合组件配置的每项渲染的schema数组，定义了每一列的组件类型和验证规则
 *                如果不是数组，会自动转换为单元素数组进行处理
 * @param data - 全局数据上下文，用于表达式计算和数据链构建
 * @param __ - 国际化翻译函数，默认直接返回原字符串，用于错误消息的本地化
 * @param omitItems - 可选参数，指定需要跳过验证的行范围
 *                    通常用于分页场景，跳过已在界面上展示的行
 *                    @param startIndex 开始跳过的行索引（包含）
 *                    @param endIndex 结束跳过的行索引（包含）
 *
 * @returns 验证结果
 *          - 返回空字符串 '' 表示所有验证都通过
 *          - 返回错误对象 {rowIndex, colIndex, msg} 表示验证失败
 *            @param rowIndex 出错的行索引（从0开始）
 *            @param colIndex 出错的列索引（从0开始）
 *            @param msg 具体的错误消息
 *
 * @example
 * // 基础用法：验证表格数据
 * const tableValue = [
 *   {name: 'John', email: '<EMAIL>', age: 25},
 *   {name: '', email: 'invalid-email', age: 16}
 * ];
 * const tableItems = [
 *   {type: 'input-text', name: 'name', required: true},
 *   {type: 'input-email', name: 'email', required: true},
 *   {type: 'input-number', name: 'age', required: true}
 * ];
 * const result = validateItems(tableValue, tableItems, {});
 * // 返回: {rowIndex: 1, colIndex: 0, msg: '请填写必填项'}
 *
 * @example
 * // 动态验证：使用表达式属性
 * const dynamicItems = [
 *   {type: 'input-text', name: 'userType', required: true},
 *   {type: 'input-text', name: 'adminCode', requiredOn: '${userType === "admin"}'}
 * ];
 * const dynamicValue = [{userType: 'admin', adminCode: ''}];
 * const result = validateItems(dynamicValue, dynamicItems, {});
 * // 当userType为admin时，adminCode变为必填，验证失败
 *
 * @example
 * // 嵌套组件验证：Combo组件
 * const nestedItems = [
 *   {type: 'input-text', name: 'title', required: true},
 *   {
 *     type: 'combo',
 *     name: 'details',
 *     items: [
 *       {type: 'input-text', name: 'description', required: true},
 *       {type: 'input-text', name: 'note'}
 *     ]
 *   }
 * ];
 * const nestedValue = [{
 *   title: 'Test',
 *   details: {description: '', note: 'optional'}
 * }];
 * const result = validateItems(nestedValue, nestedItems, {});
 * // 递归验证嵌套组件，description为必填但为空，验证失败
 *
 * @example
 * // 跳过指定行的验证
 * const result = validateItems(
 *   tableValue,
 *   tableItems,
 *   {},
 *   __,
 *   {startIndex: 0, endIndex: 2} // 跳过前3行的验证
 * );
 */
export function validateItems(
  value: any,
  items: Array<{[key: string]: any}>, // 复合组件配置的每项渲染的schema
  data: any,
  __ = (str: string) => str,
  omitItems?: {
    startIndex: number;
    endIndex: number;
  }
): '' | {rowIndex: number; colIndex: number; msg: string} {
  // 数据标准化：确保value和items都是数组格式，便于统一处理
  if (!Array.isArray(value)) {
    value = [value];
  }

  if (!Array.isArray(items)) {
    items = [items];
  }

  // 遍历每一行数据进行验证
  for (let valueIndex = 0; valueIndex < value.length; valueIndex++) {
    // 跳过需要忽略的行，通常用于分页场景，避免重复验证已在界面上展示的行
    if (omitItems && omitItems.startIndex <= valueIndex && omitItems.endIndex >= valueIndex) {
      continue;
    }

    // 构建当前行的数据链：将全局数据(data)作为原型，当前行数据作为自身属性
    // 这样可以在表达式中同时访问全局数据和当前行数据
    let rowValue : {[key: string]: any} = createObject(data, value[valueIndex] || {});

    // 遍历每一列的配置项进行验证
    for (let colIndex = 0; colIndex < items.length; colIndex++) {
      let item = items[colIndex];

      // 跳过不可见的元素（通过visibleOn等表达式控制）
      // 注意：这个工具函数没有考虑元素通过actionType: 'hide'隐藏的情况。后续可以补充下。
      if (!isVisible(item, rowValue)) {
        continue;
      }

      // 兼容InputTable的QuickEdit快速编辑功能
      // QuickEdit允许在表格中直接编辑单元格，需要将quickEdit配置合并到item中
      if (!!item.quickEdit) {
        if (!isDisabled(item, rowValue)) {
          if (item.quickEdit?.type) {
            // quickEdit有具体的type配置，合并所有quickEdit属性
            item = {
              ...item,
              ...item.quickEdit
            }
          } else if (item.quickEdit === true) {
            // quickEdit为true时，默认使用input-text类型
            item = {
             ...item,
             type: 'input-text'
            }
          }
        }
      }

      // InputTable组件兼容性处理：当没有指定type时，默认使用plain类型
      if (item.type === undefined) {
        item = {
        ...item,
         type: 'plain'
        }
      }

      // 处理动态表达式属性（如 requiredOn, disabledOn, visibleOn 等）
      // getExprProperties会计算所有以On/Expr结尾的属性，将表达式转换为实际值
      const exprProps = getExprProperties(item, rowValue);

      // 将计算后的表达式结果合并到item配置中
      // 例如：requiredOn: "${userType === 'admin'}" 会被计算为 required: true/false
      item = {
        ...item,
        ...exprProps
      };

      // 标准化验证规则：将item中的各种验证配置转换为统一的rules对象
      const rules = normalizeValidations(item);

      // 获取当前列的值：从行数据中根据item.name提取对应的值
      const colValue = getVariable(rowValue, item.name);

      // 获取组件的渲染器定义，用于判断组件类型和特性
      const render = getRendererByName(item.type)!;

      // 只对表单项组件进行验证，且必须有验证规则
      // 即使schema里没有name，也要校验，因为会出现组件给子组件补充name的情况（如InputArray组件）
      if (render.isFormItem && Object.keys(rules).length !== 0) {
        // 执行具体的验证逻辑：根据验证规则校验当前列的值
        const msgs = validate(colValue, rowValue, rules, item.messages, __);
        if (msgs.length) {
          // 一旦发现验证错误，立即返回错误信息（包含行列位置）
          return {rowIndex: valueIndex, colIndex: colIndex, msg: msgs[0].msg};
        }
      }

      // 处理嵌套组件的递归验证
      // 检查当前组件是否包含子组件（复合组件），如果是则需要递归验证子组件
      // 主要处理InputGroup、Combo、InputTable等嵌套组件的情况
      let subItems = item.body || item.items;
      if (subItems) {
        // 判断是否为数据透明组件：
        // - body属性的组件（如InputGroup）：数据透明，子组件直接访问父级数据
        // - items属性的组件（如Combo）：数据封装，子组件访问当前组件的值作为数据源
        const isDataTransparentComponent = !!item.body
        let subValue = rowValue;  // 子组件的数据源
        let subData = data;       // 子组件的全局数据上下文

        // 处理非数据透明组件（如Combo组件）
        if (!isDataTransparentComponent) {
          // FormItem组件必须有name属性，否则无法获取数据，跳过递归验证
          if (!item.name) {
            continue;
          }

          // 检查组件是否支持多值（数组类型的值）
          const isMultiple = typeof render.isMultiple === 'function'? render.isMultiple(item) : render.isMultiple;
          // 如果组件本身没有值，且是multiple类型，则跳过子组件验证
          // 因为没有数据可供子组件验证
          if (colValue === undefined && isMultiple) {
            continue;
          }

          // 设置子组件的数据源为当前组件的值
          subValue = colValue || {};
          subData = rowValue;

          // 处理值打平的特殊情况（主要用于某些特殊组件）
          const isFlat = typeof render.isFlat === 'function'? render.isFlat(item) : render.isFlat;
          // 值打平：将数组中的每个值包装成对象，通常用于简化子组件的数据结构
          if (isFlat) {
            subValue = subValue.map((v: any) => {
              return {'flat': v};
            });
            // 值打平时，通常只有一个子组件，如果subItems是数组则取第一个元素进行解构
            const targetSubItem = Array.isArray(subItems) ? subItems[0] : subItems;
            subItems = {
              ...targetSubItem,
              name: 'flat'
            }
          }
        }

        // 递归验证子组件
        // 注意：这里暂不考虑子组件中也有条件渲染（visibleOn等）的复杂情况
        const msg = validateItems(subValue, subItems, subData, __);
        if (msg !== '') {
          // 子组件验证失败时，返回当前组件的位置信息，但错误消息来自子组件
          return {rowIndex: valueIndex, colIndex: colIndex, msg: msg.msg};
        }
      }
    }
  }

  // 所有行、所有列的验证都通过，返回空字符串表示验证成功
  return '';
}

const splitValidations = function (str: string): Array<string> {
  let i = 0;
  const placeholder: {[propName: string]: string} = {};

  return str
    .replace(/matchRegexp\d*\s*\:\s*\/.*?\/[igm]*/g, raw => {
      placeholder[`__${i}`] = raw;
      return `__${i++}`;
    })
    .split(/,(?![^{\[]*[}\]])/g)
    .map(str => (/^__\d+$/.test(str) ? placeholder[str] : str.trim()));
};

export function str2rules(validations: string | {[propName: string]: any}): {
  [propName: string]: any;
} {
  if (typeof validations === 'string') {
    return validations
      ? splitValidations(validations).reduce(function (
          validations: {[propName: string]: any},
          validation
        ) {
          const idx = validation.indexOf(':');
          let validateMethod = validation;
          let args = [];

          if (~idx) {
            validateMethod = validation.substring(0, idx);
            args = /^matchRegexp/.test(validateMethod)
              ? [validation.substring(idx + 1).trim()]
              : validation
                  .substring(idx + 1)
                  .split(',')
                  .map(function (arg) {
                    try {
                      return JSON.parse(arg);
                    } catch (e) {
                      return arg;
                    }
                  });
          }

          validations[validateMethod] = args.length ? args : true;
          return validations;
        },
        {})
      : {};
  }

  return validations || {};
}
