{"name": "amis-formula", "version": "1.75.0", "description": "负责 amis 里面的表达式实现，内置公式，编辑器等", "main": "lib/index.js", "module": "esm/index.js", "types": "lib/index.d.ts", "scripts": {"build": "npm run clean-dist && npm run genDoc && cross-env NODE_ENV=production rollup -c && cp src/doc.md lib/doc.md && cp src/doc.md esm/doc.md", "lib": "npm run clean-dist && cross-env NODE_ENV=production IS_LIB=1 rollup -c", "clean-dist": "rimraf lib/** esm/** tsconfig.tsbuildinfo .rollup.cache/**", "declaration": "tsc --project tsconfig-for-declaration.json --allowJs --declaration --emitDeclarationOnly --declarationDir ./lib --rootDir ./src", "test": "jest", "update-snapshot": "jest --updateSnapshot", "coverage": "jest --coverage", "genDoc": "ts-node ./scripts/genDoc.ts"}, "exports": {".": {"require": "./lib/index.js", "import": "./esm/index.js"}, "./lib/*": {"require": "./lib/*.js", "import": "./esm/*.js"}}, "repository": {"type": "git", "url": "git+https://github.com/aisuda/amis-tpl.git"}, "keywords": ["amis", "tpl", "parser", "formula"], "files": ["lib", "esm"], "author": "fex", "license": "MIT", "bugs": {"url": "https://github.com/aisuda/amis-tpl/issues"}, "homepage": "https://github.com/aisuda/amis-tpl#readme", "dependencies": {"lodash": "^4.17.15", "moment": "^2.29.4", "tslib": "^2.3.1"}, "devDependencies": {"@rollup/plugin-commonjs": "^22.0.2", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^14.1.0", "@rollup/plugin-typescript": "^8.3.4", "@types/doctrine": "0.0.5", "@types/jest": "^28.1.0", "@types/lodash": "^4.14.175", "cross-env": "^7.0.3", "doctrine": "^3.0.0", "jest": "^29.0.3", "jest-canvas-mock": "^2.3.0", "jest-environment-jsdom": "^29.0.3", "mini-css-extract-plugin": "^2.4.5", "moment-timezone": "^0.5.33", "rimraf": "^3.0.2", "rollup": "^2.60.2", "rollup-plugin-license": "^2.6.0", "rollup-plugin-terser": "^7.0.2", "sass": "^1.32", "sass-loader": "^10.0.5", "style-loader": "^3.2.1", "stylelint": "^13.0.0", "ts-jest": "^29.0.2", "ts-loader": "^9.2.3", "ts-node": "^10.4.0", "typescript": "^4.3.5"}, "browserslist": "IE >= 11", "jest": {"testEnvironment": "jsdom", "collectCoverageFrom": ["src/**/*"], "moduleFileExtensions": ["ts", "tsx", "js"], "transform": {"\\.(ts|tsx)$": ["ts-jest", {"diagnostics": false}]}, "setupFiles": ["jest-canvas-mock"], "testRegex": "/.*\\.test\\.(ts|tsx|js)$", "moduleNameMapper": {"\\.(css|less|sass|scss)$": "<rootDir>/../__mocks__/styleMock.js", "\\.(svg)$": "<rootDir>/../__mocks__/svgMock.js", "^amis\\-ui$": "<rootDir>/../amis-ui/src/index.tsx", "^amis\\-ui/lib/(.*)": "<rootDir>/../amis-ui/src/$1", "^amis\\-core$": "<rootDir>/../amis-core/src/index.tsx", "^amis\\-core/lib/(.*)": "<rootDir>/../amis-core/src/$1"}, "setupFilesAfterEnv": ["<rootDir>/__tests__/jest.setup.js"], "snapshotFormat": {"escapeString": false, "printBasicPrototype": false}}, "publishConfig": {"access": "public", "registry": "http://registry.caijj.net/repository/npm-caijiajia/"}, "authors": ["<EMAIL>"], "gitHead": "37d23b4a8eb1c663bc38e8dd9040889ea1526ec4"}