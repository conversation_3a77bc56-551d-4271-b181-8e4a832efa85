:root {
  --button-default-default-top-border-color: var(--colors-neutral-line-8);
  --button-default-default-top-border-style: var(--borders-style-2);
  --button-default-default-top-border-width: var(--borders-width-2);
  --button-default-default-left-border-color: var(--colors-neutral-line-8);
  --button-default-default-left-border-style: var(--borders-style-2);
  --button-default-default-left-border-width: var(--borders-width-2);
  --button-default-default-right-border-color: var(--colors-neutral-line-8);
  --button-default-default-right-border-style: var(--borders-style-2);
  --button-default-default-right-border-width: var(--borders-width-2);
  --button-default-default-bottom-border-color: var(--colors-neutral-line-8);
  --button-default-default-bottom-border-style: var(--borders-style-2);
  --button-default-default-bottom-border-width: var(--borders-width-2);
  --button-default-default-shadow: var(--shadows-shadow-none);
  --button-default-default-bg-color: var(--colors-neutral-fill-11);
  --button-default-default-font-color: var(--colors-neutral-text-2);
  --button-default-hover-top-border-color: var(--colors-brand-5);
  --button-default-hover-top-border-style: var(--borders-style-2);
  --button-default-hover-top-border-width: var(--borders-width-2);
  --button-default-hover-left-border-color: var(--colors-brand-5);
  --button-default-hover-left-border-style: var(--borders-style-2);
  --button-default-hover-left-border-width: var(--borders-width-2);
  --button-default-hover-right-border-color: var(--colors-brand-5);
  --button-default-hover-right-border-style: var(--borders-style-2);
  --button-default-hover-right-border-width: var(--borders-width-2);
  --button-default-hover-bottom-border-color: var(--colors-brand-5);
  --button-default-hover-bottom-border-style: var(--borders-style-2);
  --button-default-hover-bottom-border-width: var(--borders-width-2);
  --button-default-hover-shadow: var(--shadows-shadow-none);
  --button-default-hover-bg-color: var(--colors-neutral-fill-11);
  --button-default-hover-font-color: var(--colors-brand-5);
  --button-default-active-top-border-color: var(--colors-brand-4);
  --button-default-active-top-border-style: var(--borders-style-2);
  --button-default-active-top-border-width: var(--borders-width-2);
  --button-default-active-left-border-color: var(--colors-brand-4);
  --button-default-active-left-border-style: var(--borders-style-2);
  --button-default-active-left-border-width: var(--borders-width-2);
  --button-default-active-right-border-color: var(--colors-brand-4);
  --button-default-active-right-border-style: var(--borders-style-2);
  --button-default-active-right-border-width: var(--borders-width-2);
  --button-default-active-bottom-border-color: var(--colors-brand-4);
  --button-default-active-bottom-border-style: var(--borders-style-2);
  --button-default-active-bottom-border-width: var(--borders-width-2);
  --button-default-active-shadow: var(--shadows-shadow-none);
  --button-default-active-bg-color: var(--colors-neutral-fill-11);
  --button-default-active-font-color: var(--colors-brand-4);
  --button-default-disabled-top-border-color: var(--colors-neutral-line-10);
  --button-default-disabled-top-border-style: var(--borders-style-2);
  --button-default-disabled-top-border-width: var(--borders-width-2);
  --button-default-disabled-left-border-color: var(--colors-neutral-line-10);
  --button-default-disabled-left-border-style: var(--borders-style-2);
  --button-default-disabled-left-border-width: var(--borders-width-2);
  --button-default-disabled-right-border-color: var(--colors-neutral-line-10);
  --button-default-disabled-right-border-style: var(--borders-style-2);
  --button-default-disabled-right-border-width: var(--borders-width-2);
  --button-default-disabled-bottom-border-color: var(--colors-neutral-line-10);
  --button-default-disabled-bottom-border-style: var(--borders-style-2);
  --button-default-disabled-bottom-border-width: var(--borders-width-2);
  --button-default-disabled-shadow: var(--shadows-shadow-none);
  --button-default-disabled-bg-color: var(--colors-neutral-fill-10);
  --button-default-disabled-font-color: var(--colors-neutral-text-6);
  --button-primary-default-top-border-color: var(--colors-brand-5);
  --button-primary-default-top-border-style: var(--borders-style-2);
  --button-primary-default-top-border-width: var(--borders-width-2);
  --button-primary-default-left-border-color: var(--colors-brand-5);
  --button-primary-default-left-border-style: var(--borders-style-2);
  --button-primary-default-left-border-width: var(--borders-width-2);
  --button-primary-default-right-border-color: var(--colors-brand-5);
  --button-primary-default-right-border-style: var(--borders-style-2);
  --button-primary-default-right-border-width: var(--borders-width-2);
  --button-primary-default-bottom-border-color: var(--colors-brand-5);
  --button-primary-default-bottom-border-style: var(--borders-style-2);
  --button-primary-default-bottom-border-width: var(--borders-width-2);
  --button-primary-default-shadow: var(--shadows-shadow-none);
  --button-primary-default-bg-color: var(--colors-brand-5);
  --button-primary-default-font-color: var(--colors-neutral-text-11);
  --button-primary-hover-top-border-color: var(--colors-brand-6);
  --button-primary-hover-top-border-style: var(--borders-style-2);
  --button-primary-hover-top-border-width: var(--borders-width-2);
  --button-primary-hover-left-border-color: var(--colors-brand-6);
  --button-primary-hover-left-border-style: var(--borders-style-2);
  --button-primary-hover-left-border-width: var(--borders-width-2);
  --button-primary-hover-right-border-color: var(--colors-brand-6);
  --button-primary-hover-right-border-style: var(--borders-style-2);
  --button-primary-hover-right-border-width: var(--borders-width-2);
  --button-primary-hover-bottom-border-color: var(--colors-brand-6);
  --button-primary-hover-bottom-border-style: var(--borders-style-2);
  --button-primary-hover-bottom-border-width: var(--borders-width-2);
  --button-primary-hover-shadow: var(--shadows-shadow-none);
  --button-primary-hover-bg-color: var(--colors-brand-6);
  --button-primary-hover-font-color: var(--colors-neutral-text-11);
  --button-primary-active-top-border-color: var(--colors-brand-4);
  --button-primary-active-top-border-style: var(--borders-style-2);
  --button-primary-active-top-border-width: var(--borders-width-2);
  --button-primary-active-left-border-color: var(--colors-brand-4);
  --button-primary-active-left-border-style: var(--borders-style-2);
  --button-primary-active-left-border-width: var(--borders-width-2);
  --button-primary-active-right-border-color: var(--colors-brand-4);
  --button-primary-active-right-border-style: var(--borders-style-2);
  --button-primary-active-right-border-width: var(--borders-width-2);
  --button-primary-active-bottom-border-color: var(--colors-brand-4);
  --button-primary-active-bottom-border-style: var(--borders-style-2);
  --button-primary-active-bottom-border-width: var(--borders-width-2);
  --button-primary-active-shadow: var(--shadows-shadow-none);
  --button-primary-active-bg-color: var(--colors-brand-4);
  --button-primary-active-font-color: var(--colors-neutral-text-11);
  --button-primary-disabled-top-border-color: var(--colors-neutral-line-10);
  --button-primary-disabled-top-border-style: var(--borders-style-2);
  --button-primary-disabled-top-border-width: var(--borders-width-2);
  --button-primary-disabled-left-border-color: var(--colors-neutral-line-10);
  --button-primary-disabled-left-border-style: var(--borders-style-2);
  --button-primary-disabled-left-border-width: var(--borders-width-2);
  --button-primary-disabled-right-border-color: var(--colors-neutral-line-10);
  --button-primary-disabled-right-border-style: var(--borders-style-2);
  --button-primary-disabled-right-border-width: var(--borders-width-2);
  --button-primary-disabled-bottom-border-color: var(--colors-neutral-line-10);
  --button-primary-disabled-bottom-border-style: var(--borders-style-2);
  --button-primary-disabled-bottom-border-width: var(--borders-width-2);
  --button-primary-disabled-shadow: var(--shadows-shadow-none);
  --button-primary-disabled-bg-color: var(--colors-neutral-fill-10);
  --button-primary-disabled-font-color: var(--colors-neutral-text-6);
  --button-secondary-default-top-border-color: var(--colors-neutral-line-6);
  --button-secondary-default-top-border-style: var(--borders-style-2);
  --button-secondary-default-top-border-width: var(--borders-width-2);
  --button-secondary-default-left-border-color: var(--colors-neutral-line-6);
  --button-secondary-default-left-border-style: var(--borders-style-2);
  --button-secondary-default-left-border-width: var(--borders-width-2);
  --button-secondary-default-right-border-color: var(--colors-neutral-line-6);
  --button-secondary-default-right-border-style: var(--borders-style-2);
  --button-secondary-default-right-border-width: var(--borders-width-2);
  --button-secondary-default-bottom-border-color: var(--colors-neutral-line-6);
  --button-secondary-default-bottom-border-style: var(--borders-style-2);
  --button-secondary-default-bottom-border-width: var(--borders-width-2);
  --button-secondary-default-shadow: var(--shadows-shadow-none);
  --button-secondary-default-bg-color: var(--colors-neutral-fill-6);
  --button-secondary-default-font-color: var(--colors-neutral-text-11);
  --button-secondary-hover-top-border-color: var(--colors-neutral-line-5);
  --button-secondary-hover-top-border-style: var(--borders-style-2);
  --button-secondary-hover-top-border-width: var(--borders-width-2);
  --button-secondary-hover-left-border-color: var(--colors-neutral-line-5);
  --button-secondary-hover-left-border-style: var(--borders-style-2);
  --button-secondary-hover-left-border-width: var(--borders-width-2);
  --button-secondary-hover-right-border-color: var(--colors-neutral-line-5);
  --button-secondary-hover-right-border-style: var(--borders-style-2);
  --button-secondary-hover-right-border-width: var(--borders-width-2);
  --button-secondary-hover-bottom-border-color: var(--colors-neutral-line-5);
  --button-secondary-hover-bottom-border-style: var(--borders-style-2);
  --button-secondary-hover-bottom-border-width: var(--borders-width-2);
  --button-secondary-hover-shadow: var(--shadows-shadow-none);
  --button-secondary-hover-bg-color: var(--colors-neutral-fill-5);
  --button-secondary-hover-font-color: var(--colors-neutral-text-11);
  --button-secondary-active-top-border-color: var(--colors-neutral-line-4);
  --button-secondary-active-top-border-style: var(--borders-style-2);
  --button-secondary-active-top-border-width: var(--borders-width-2);
  --button-secondary-active-left-border-color: var(--colors-neutral-line-4);
  --button-secondary-active-left-border-style: var(--borders-style-2);
  --button-secondary-active-left-border-width: var(--borders-width-2);
  --button-secondary-active-right-border-color: var(--colors-neutral-line-4);
  --button-secondary-active-right-border-style: var(--borders-style-2);
  --button-secondary-active-right-border-width: var(--borders-width-2);
  --button-secondary-active-bottom-border-color: var(--colors-neutral-line-4);
  --button-secondary-active-bottom-border-style: var(--borders-style-2);
  --button-secondary-active-bottom-border-width: var(--borders-width-2);
  --button-secondary-active-shadow: var(--shadows-shadow-none);
  --button-secondary-active-bg-color: var(--colors-neutral-fill-4);
  --button-secondary-active-font-color: var(--colors-neutral-text-11);
  --button-secondary-disabled-top-border-color: var(--colors-neutral-line-10);
  --button-secondary-disabled-top-border-style: var(--borders-style-2);
  --button-secondary-disabled-top-border-width: var(--borders-width-2);
  --button-secondary-disabled-left-border-color: var(--colors-neutral-line-10);
  --button-secondary-disabled-left-border-style: var(--borders-style-2);
  --button-secondary-disabled-left-border-width: var(--borders-width-2);
  --button-secondary-disabled-right-border-color: var(--colors-neutral-line-10);
  --button-secondary-disabled-right-border-style: var(--borders-style-2);
  --button-secondary-disabled-right-border-width: var(--borders-width-2);
  --button-secondary-disabled-bottom-border-color: var(
    --colors-neutral-line-10
  );
  --button-secondary-disabled-bottom-border-style: var(--borders-style-2);
  --button-secondary-disabled-bottom-border-width: var(--borders-width-2);
  --button-secondary-disabled-shadow: var(--shadows-shadow-none);
  --button-secondary-disabled-bg-color: var(--colors-neutral-fill-10);
  --button-secondary-disabled-font-color: var(--colors-neutral-text-6);
  --button-enhance-default-top-border-color: var(--colors-brand-5);
  --button-enhance-default-top-border-style: var(--borders-style-2);
  --button-enhance-default-top-border-width: var(--borders-width-2);
  --button-enhance-default-left-border-color: var(--colors-brand-5);
  --button-enhance-default-left-border-style: var(--borders-style-2);
  --button-enhance-default-left-border-width: var(--borders-width-2);
  --button-enhance-default-right-border-color: var(--colors-brand-5);
  --button-enhance-default-right-border-style: var(--borders-style-2);
  --button-enhance-default-right-border-width: var(--borders-width-2);
  --button-enhance-default-bottom-border-color: var(--colors-brand-5);
  --button-enhance-default-bottom-border-style: var(--borders-style-2);
  --button-enhance-default-bottom-border-width: var(--borders-width-2);
  --button-enhance-default-shadow: var(--shadows-shadow-none);
  --button-enhance-default-bg-color: var(--colors-neutral-fill-11);
  --button-enhance-default-font-color: var(--colors-brand-5);
  --button-enhance-hover-top-border-color: var(--colors-brand-6);
  --button-enhance-hover-top-border-style: var(--borders-style-2);
  --button-enhance-hover-top-border-width: var(--borders-width-2);
  --button-enhance-hover-left-border-color: var(--colors-brand-6);
  --button-enhance-hover-left-border-style: var(--borders-style-2);
  --button-enhance-hover-left-border-width: var(--borders-width-2);
  --button-enhance-hover-right-border-color: var(--colors-brand-6);
  --button-enhance-hover-right-border-style: var(--borders-style-2);
  --button-enhance-hover-right-border-width: var(--borders-width-2);
  --button-enhance-hover-bottom-border-color: var(--colors-brand-6);
  --button-enhance-hover-bottom-border-style: var(--borders-style-2);
  --button-enhance-hover-bottom-border-width: var(--borders-width-2);
  --button-enhance-hover-shadow: var(--shadows-shadow-none);
  --button-enhance-hover-bg-color: var(--colors-neutral-fill-11);
  --button-enhance-hover-font-color: var(--colors-brand-6);
  --button-enhance-active-top-border-color: var(--colors-brand-4);
  --button-enhance-active-top-border-style: var(--borders-style-2);
  --button-enhance-active-top-border-width: var(--borders-width-2);
  --button-enhance-active-left-border-color: var(--colors-brand-4);
  --button-enhance-active-left-border-style: var(--borders-style-2);
  --button-enhance-active-left-border-width: var(--borders-width-2);
  --button-enhance-active-right-border-color: var(--colors-brand-4);
  --button-enhance-active-right-border-style: var(--borders-style-2);
  --button-enhance-active-right-border-width: var(--borders-width-2);
  --button-enhance-active-bottom-border-color: var(--colors-brand-4);
  --button-enhance-active-bottom-border-style: var(--borders-style-2);
  --button-enhance-active-bottom-border-width: var(--borders-width-2);
  --button-enhance-active-shadow: var(--shadows-shadow-none);
  --button-enhance-active-bg-color: var(--colors-neutral-fill-11);
  --button-enhance-active-font-color: var(--colors-brand-4);
  --button-enhance-disabled-top-border-color: var(--colors-neutral-line-10);
  --button-enhance-disabled-top-border-style: var(--borders-style-2);
  --button-enhance-disabled-top-border-width: var(--borders-width-2);
  --button-enhance-disabled-left-border-color: var(--colors-neutral-line-10);
  --button-enhance-disabled-left-border-style: var(--borders-style-2);
  --button-enhance-disabled-left-border-width: var(--borders-width-2);
  --button-enhance-disabled-right-border-color: var(--colors-neutral-line-10);
  --button-enhance-disabled-right-border-style: var(--borders-style-2);
  --button-enhance-disabled-right-border-width: var(--borders-width-2);
  --button-enhance-disabled-bottom-border-color: var(--colors-neutral-line-10);
  --button-enhance-disabled-bottom-border-style: var(--borders-style-2);
  --button-enhance-disabled-bottom-border-width: var(--borders-width-2);
  --button-enhance-disabled-shadow: var(--shadows-shadow-none);
  --button-enhance-disabled-bg-color: var(--colors-neutral-fill-10);
  --button-enhance-disabled-font-color: var(--colors-neutral-text-6);
  --button-info-default-top-border-color: var(--colors-info-5);
  --button-info-default-top-border-style: var(--borders-style-2);
  --button-info-default-top-border-width: var(--borders-width-2);
  --button-info-default-left-border-color: var(--colors-info-5);
  --button-info-default-left-border-style: var(--borders-style-2);
  --button-info-default-left-border-width: var(--borders-width-2);
  --button-info-default-right-border-color: var(--colors-info-5);
  --button-info-default-right-border-style: var(--borders-style-2);
  --button-info-default-right-border-width: var(--borders-width-2);
  --button-info-default-bottom-border-color: var(--colors-info-5);
  --button-info-default-bottom-border-style: var(--borders-style-2);
  --button-info-default-bottom-border-width: var(--borders-width-2);
  --button-info-default-shadow: var(--shadows-shadow-none);
  --button-info-default-bg-color: var(--colors-info-5);
  --button-info-default-font-color: var(--colors-neutral-text-11);
  --button-info-hover-top-border-color: var(--colors-info-6);
  --button-info-hover-top-border-style: var(--borders-style-2);
  --button-info-hover-top-border-width: var(--borders-width-2);
  --button-info-hover-left-border-color: var(--colors-info-6);
  --button-info-hover-left-border-style: var(--borders-style-2);
  --button-info-hover-left-border-width: var(--borders-width-2);
  --button-info-hover-right-border-color: var(--colors-info-6);
  --button-info-hover-right-border-style: var(--borders-style-2);
  --button-info-hover-right-border-width: var(--borders-width-2);
  --button-info-hover-bottom-border-color: var(--colors-info-6);
  --button-info-hover-bottom-border-style: var(--borders-style-2);
  --button-info-hover-bottom-border-width: var(--borders-width-2);
  --button-info-hover-shadow: var(--shadows-shadow-none);
  --button-info-hover-bg-color: var(--colors-info-6);
  --button-info-hover-font-color: var(--colors-neutral-text-11);
  --button-info-active-top-border-color: var(--colors-info-4);
  --button-info-active-top-border-style: var(--borders-style-2);
  --button-info-active-top-border-width: var(--borders-width-2);
  --button-info-active-left-border-color: var(--colors-info-4);
  --button-info-active-left-border-style: var(--borders-style-2);
  --button-info-active-left-border-width: var(--borders-width-2);
  --button-info-active-right-border-color: var(--colors-info-4);
  --button-info-active-right-border-style: var(--borders-style-2);
  --button-info-active-right-border-width: var(--borders-width-2);
  --button-info-active-bottom-border-color: var(--colors-info-4);
  --button-info-active-bottom-border-style: var(--borders-style-2);
  --button-info-active-bottom-border-width: var(--borders-width-2);
  --button-info-active-shadow: var(--shadows-shadow-none);
  --button-info-active-bg-color: var(--colors-info-4);
  --button-info-active-font-color: var(--colors-neutral-text-11);
  --button-info-disabled-top-border-color: var(--colors-neutral-line-10);
  --button-info-disabled-top-border-style: var(--borders-style-2);
  --button-info-disabled-top-border-width: var(--borders-width-2);
  --button-info-disabled-left-border-color: var(--colors-neutral-line-10);
  --button-info-disabled-left-border-style: var(--borders-style-2);
  --button-info-disabled-left-border-width: var(--borders-width-2);
  --button-info-disabled-right-border-color: var(--colors-neutral-line-10);
  --button-info-disabled-right-border-style: var(--borders-style-2);
  --button-info-disabled-right-border-width: var(--borders-width-2);
  --button-info-disabled-bottom-border-color: var(--colors-neutral-line-10);
  --button-info-disabled-bottom-border-style: var(--borders-style-2);
  --button-info-disabled-bottom-border-width: var(--borders-width-2);
  --button-info-disabled-shadow: var(--shadows-shadow-none);
  --button-info-disabled-bg-color: var(--colors-neutral-fill-10);
  --button-info-disabled-font-color: var(--colors-neutral-text-6);
  --button-success-default-top-border-color: var(--colors-success-5);
  --button-success-default-top-border-style: var(--borders-style-2);
  --button-success-default-top-border-width: var(--borders-width-2);
  --button-success-default-left-border-color: var(--colors-success-5);
  --button-success-default-left-border-style: var(--borders-style-2);
  --button-success-default-left-border-width: var(--borders-width-2);
  --button-success-default-right-border-color: var(--colors-success-5);
  --button-success-default-right-border-style: var(--borders-style-2);
  --button-success-default-right-border-width: var(--borders-width-2);
  --button-success-default-bottom-border-color: var(--colors-success-5);
  --button-success-default-bottom-border-style: var(--borders-style-2);
  --button-success-default-bottom-border-width: var(--borders-width-2);
  --button-success-default-shadow: var(--shadows-shadow-none);
  --button-success-default-bg-color: var(--colors-success-5);
  --button-success-default-font-color: var(--colors-neutral-text-11);
  --button-success-hover-top-border-color: var(--colors-success-6);
  --button-success-hover-top-border-style: var(--borders-style-2);
  --button-success-hover-top-border-width: var(--borders-width-2);
  --button-success-hover-left-border-color: var(--colors-success-6);
  --button-success-hover-left-border-style: var(--borders-style-2);
  --button-success-hover-left-border-width: var(--borders-width-2);
  --button-success-hover-right-border-color: var(--colors-success-6);
  --button-success-hover-right-border-style: var(--borders-style-2);
  --button-success-hover-right-border-width: var(--borders-width-2);
  --button-success-hover-bottom-border-color: var(--colors-success-6);
  --button-success-hover-bottom-border-style: var(--borders-style-2);
  --button-success-hover-bottom-border-width: var(--borders-width-2);
  --button-success-hover-shadow: var(--shadows-shadow-none);
  --button-success-hover-bg-color: var(--colors-success-6);
  --button-success-hover-font-color: var(--colors-neutral-text-11);
  --button-success-active-top-border-color: var(--colors-success-4);
  --button-success-active-top-border-style: var(--borders-style-2);
  --button-success-active-top-border-width: var(--borders-width-2);
  --button-success-active-left-border-color: var(--colors-success-4);
  --button-success-active-left-border-style: var(--borders-style-2);
  --button-success-active-left-border-width: var(--borders-width-2);
  --button-success-active-right-border-color: var(--colors-success-4);
  --button-success-active-right-border-style: var(--borders-style-2);
  --button-success-active-right-border-width: var(--borders-width-2);
  --button-success-active-bottom-border-color: var(--colors-success-4);
  --button-success-active-bottom-border-style: var(--borders-style-2);
  --button-success-active-bottom-border-width: var(--borders-width-2);
  --button-success-active-shadow: var(--shadows-shadow-none);
  --button-success-active-bg-color: var(--colors-success-4);
  --button-success-active-font-color: var(--colors-neutral-text-11);
  --button-success-disabled-top-border-color: var(--colors-neutral-line-10);
  --button-success-disabled-top-border-style: var(--borders-style-2);
  --button-success-disabled-top-border-width: var(--borders-width-2);
  --button-success-disabled-left-border-color: var(--colors-neutral-line-10);
  --button-success-disabled-left-border-style: var(--borders-style-2);
  --button-success-disabled-left-border-width: var(--borders-width-2);
  --button-success-disabled-right-border-color: var(--colors-neutral-line-10);
  --button-success-disabled-right-border-style: var(--borders-style-2);
  --button-success-disabled-right-border-width: var(--borders-width-2);
  --button-success-disabled-bottom-border-color: var(--colors-neutral-line-10);
  --button-success-disabled-bottom-border-style: var(--borders-style-2);
  --button-success-disabled-bottom-border-width: var(--borders-width-2);
  --button-success-disabled-shadow: var(--shadows-shadow-none);
  --button-success-disabled-bg-color: var(--colors-neutral-fill-10);
  --button-success-disabled-font-color: var(--colors-neutral-text-6);
  --button-warning-default-top-border-color: var(--colors-warning-5);
  --button-warning-default-top-border-style: var(--borders-style-2);
  --button-warning-default-top-border-width: var(--borders-width-2);
  --button-warning-default-left-border-color: var(--colors-warning-5);
  --button-warning-default-left-border-style: var(--borders-style-2);
  --button-warning-default-left-border-width: var(--borders-width-2);
  --button-warning-default-right-border-color: var(--colors-warning-5);
  --button-warning-default-right-border-style: var(--borders-style-2);
  --button-warning-default-right-border-width: var(--borders-width-2);
  --button-warning-default-bottom-border-color: var(--colors-warning-5);
  --button-warning-default-bottom-border-style: var(--borders-style-2);
  --button-warning-default-bottom-border-width: var(--borders-width-2);
  --button-warning-default-shadow: var(--shadows-shadow-none);
  --button-warning-default-bg-color: var(--colors-warning-5);
  --button-warning-default-font-color: var(--colors-neutral-text-11);
  --button-warning-hover-top-border-color: var(--colors-warning-6);
  --button-warning-hover-top-border-style: var(--borders-style-2);
  --button-warning-hover-top-border-width: var(--borders-width-2);
  --button-warning-hover-left-border-color: var(--colors-warning-6);
  --button-warning-hover-left-border-style: var(--borders-style-2);
  --button-warning-hover-left-border-width: var(--borders-width-2);
  --button-warning-hover-right-border-color: var(--colors-warning-6);
  --button-warning-hover-right-border-style: var(--borders-style-2);
  --button-warning-hover-right-border-width: var(--borders-width-2);
  --button-warning-hover-bottom-border-color: var(--colors-warning-6);
  --button-warning-hover-bottom-border-style: var(--borders-style-2);
  --button-warning-hover-bottom-border-width: var(--borders-width-2);
  --button-warning-hover-shadow: var(--shadows-shadow-none);
  --button-warning-hover-bg-color: var(--colors-warning-6);
  --button-warning-hover-font-color: var(--colors-neutral-text-11);
  --button-warning-active-top-border-color: var(--colors-warning-4);
  --button-warning-active-top-border-style: var(--borders-style-2);
  --button-warning-active-top-border-width: var(--borders-width-2);
  --button-warning-active-left-border-color: var(--colors-warning-4);
  --button-warning-active-left-border-style: var(--borders-style-2);
  --button-warning-active-left-border-width: var(--borders-width-2);
  --button-warning-active-right-border-color: var(--colors-warning-4);
  --button-warning-active-right-border-style: var(--borders-style-2);
  --button-warning-active-right-border-width: var(--borders-width-2);
  --button-warning-active-bottom-border-color: var(--colors-warning-4);
  --button-warning-active-bottom-border-style: var(--borders-style-2);
  --button-warning-active-bottom-border-width: var(--borders-width-2);
  --button-warning-active-shadow: var(--shadows-shadow-none);
  --button-warning-active-bg-color: var(--colors-warning-4);
  --button-warning-active-font-color: var(--colors-neutral-text-11);
  --button-warning-disabled-top-border-color: var(--colors-neutral-line-10);
  --button-warning-disabled-top-border-style: var(--borders-style-2);
  --button-warning-disabled-top-border-width: var(--borders-width-2);
  --button-warning-disabled-left-border-color: var(--colors-neutral-line-10);
  --button-warning-disabled-left-border-style: var(--borders-style-2);
  --button-warning-disabled-left-border-width: var(--borders-width-2);
  --button-warning-disabled-right-border-color: var(--colors-neutral-line-10);
  --button-warning-disabled-right-border-style: var(--borders-style-2);
  --button-warning-disabled-right-border-width: var(--borders-width-2);
  --button-warning-disabled-bottom-border-color: var(--colors-neutral-line-10);
  --button-warning-disabled-bottom-border-style: var(--borders-style-2);
  --button-warning-disabled-bottom-border-width: var(--borders-width-2);
  --button-warning-disabled-shadow: var(--shadows-shadow-none);
  --button-warning-disabled-bg-color: var(--colors-neutral-fill-10);
  --button-warning-disabled-font-color: var(--colors-neutral-text-6);
  --button-danger-default-top-border-color: var(--colors-error-5);
  --button-danger-default-top-border-style: var(--borders-style-2);
  --button-danger-default-top-border-width: var(--borders-width-2);
  --button-danger-default-left-border-color: var(--colors-error-5);
  --button-danger-default-left-border-style: var(--borders-style-2);
  --button-danger-default-left-border-width: var(--borders-width-2);
  --button-danger-default-right-border-color: var(--colors-error-5);
  --button-danger-default-right-border-style: var(--borders-style-2);
  --button-danger-default-right-border-width: var(--borders-width-2);
  --button-danger-default-bottom-border-color: var(--colors-error-5);
  --button-danger-default-bottom-border-style: var(--borders-style-2);
  --button-danger-default-bottom-border-width: var(--borders-width-2);
  --button-danger-default-shadow: var(--shadows-shadow-none);
  --button-danger-default-bg-color: var(--colors-error-5);
  --button-danger-default-font-color: var(--colors-neutral-text-11);
  --button-danger-hover-top-border-color: var(--colors-error-6);
  --button-danger-hover-top-border-style: var(--borders-style-2);
  --button-danger-hover-top-border-width: var(--borders-width-2);
  --button-danger-hover-left-border-color: var(--colors-error-6);
  --button-danger-hover-left-border-style: var(--borders-style-2);
  --button-danger-hover-left-border-width: var(--borders-width-2);
  --button-danger-hover-right-border-color: var(--colors-error-6);
  --button-danger-hover-right-border-style: var(--borders-style-2);
  --button-danger-hover-right-border-width: var(--borders-width-2);
  --button-danger-hover-bottom-border-color: var(--colors-error-6);
  --button-danger-hover-bottom-border-style: var(--borders-style-2);
  --button-danger-hover-bottom-border-width: var(--borders-width-2);
  --button-danger-hover-shadow: var(--shadows-shadow-none);
  --button-danger-hover-bg-color: var(--colors-error-6);
  --button-danger-hover-font-color: var(--colors-neutral-text-11);
  --button-danger-active-top-border-color: var(--colors-error-4);
  --button-danger-active-top-border-style: var(--borders-style-2);
  --button-danger-active-top-border-width: var(--borders-width-2);
  --button-danger-active-left-border-color: var(--colors-error-4);
  --button-danger-active-left-border-style: var(--borders-style-2);
  --button-danger-active-left-border-width: var(--borders-width-2);
  --button-danger-active-right-border-color: var(--colors-error-4);
  --button-danger-active-right-border-style: var(--borders-style-2);
  --button-danger-active-right-border-width: var(--borders-width-2);
  --button-danger-active-bottom-border-color: var(--colors-error-4);
  --button-danger-active-bottom-border-style: var(--borders-style-2);
  --button-danger-active-bottom-border-width: var(--borders-width-2);
  --button-danger-active-shadow: var(--shadows-shadow-none);
  --button-danger-active-bg-color: var(--colors-error-4);
  --button-danger-active-font-color: var(--colors-neutral-text-11);
  --button-danger-disabled-top-border-color: var(--colors-neutral-line-10);
  --button-danger-disabled-top-border-style: var(--borders-style-2);
  --button-danger-disabled-top-border-width: var(--borders-width-2);
  --button-danger-disabled-left-border-color: var(--colors-neutral-line-10);
  --button-danger-disabled-left-border-style: var(--borders-style-2);
  --button-danger-disabled-left-border-width: var(--borders-width-2);
  --button-danger-disabled-right-border-color: var(--colors-neutral-line-10);
  --button-danger-disabled-right-border-style: var(--borders-style-2);
  --button-danger-disabled-right-border-width: var(--borders-width-2);
  --button-danger-disabled-bottom-border-color: var(--colors-neutral-line-10);
  --button-danger-disabled-bottom-border-style: var(--borders-style-2);
  --button-danger-disabled-bottom-border-width: var(--borders-width-2);
  --button-danger-disabled-shadow: var(--shadows-shadow-none);
  --button-danger-disabled-bg-color: var(--colors-neutral-fill-10);
  --button-danger-disabled-font-color: var(--colors-neutral-text-6);
  --button-light-default-top-border-color: var(--colors-brand-10);
  --button-light-default-top-border-style: var(--borders-style-2);
  --button-light-default-top-border-width: var(--borders-width-2);
  --button-light-default-left-border-color: var(--colors-brand-10);
  --button-light-default-left-border-style: var(--borders-style-2);
  --button-light-default-left-border-width: var(--borders-width-2);
  --button-light-default-right-border-color: var(--colors-brand-10);
  --button-light-default-right-border-style: var(--borders-style-2);
  --button-light-default-right-border-width: var(--borders-width-2);
  --button-light-default-bottom-border-color: var(--colors-brand-10);
  --button-light-default-bottom-border-style: var(--borders-style-2);
  --button-light-default-bottom-border-width: var(--borders-width-2);
  --button-light-default-shadow: var(--shadows-shadow-none);
  --button-light-default-bg-color: var(--colors-brand-10);
  --button-light-default-font-color: var(--colors-neutral-text-2);
  --button-light-hover-top-border-color: var(--colors-brand-9);
  --button-light-hover-top-border-style: var(--borders-style-2);
  --button-light-hover-top-border-width: var(--borders-width-2);
  --button-light-hover-left-border-color: var(--colors-brand-9);
  --button-light-hover-left-border-style: var(--borders-style-2);
  --button-light-hover-left-border-width: var(--borders-width-2);
  --button-light-hover-right-border-color: var(--colors-brand-9);
  --button-light-hover-right-border-style: var(--borders-style-2);
  --button-light-hover-right-border-width: var(--borders-width-2);
  --button-light-hover-bottom-border-color: var(--colors-brand-9);
  --button-light-hover-bottom-border-style: var(--borders-style-2);
  --button-light-hover-bottom-border-width: var(--borders-width-2);
  --button-light-hover-shadow: var(--shadows-shadow-none);
  --button-light-hover-bg-color: var(--colors-brand-9);
  --button-light-hover-font-color: var(--colors-neutral-text-2);
  --button-light-active-top-border-color: var(--colors-brand-7);
  --button-light-active-top-border-style: var(--borders-style-2);
  --button-light-active-top-border-width: var(--borders-width-2);
  --button-light-active-left-border-color: var(--colors-brand-7);
  --button-light-active-left-border-style: var(--borders-style-2);
  --button-light-active-left-border-width: var(--borders-width-2);
  --button-light-active-right-border-color: var(--colors-brand-7);
  --button-light-active-right-border-style: var(--borders-style-2);
  --button-light-active-right-border-width: var(--borders-width-2);
  --button-light-active-bottom-border-color: var(--colors-brand-7);
  --button-light-active-bottom-border-style: var(--borders-style-2);
  --button-light-active-bottom-border-width: var(--borders-width-2);
  --button-light-active-shadow: var(--shadows-shadow-none);
  --button-light-active-bg-color: var(--colors-brand-7);
  --button-light-active-font-color: var(--colors-neutral-text-2);
  --button-light-disabled-top-border-color: var(--colors-neutral-line-10);
  --button-light-disabled-top-border-style: var(--borders-style-2);
  --button-light-disabled-top-border-width: var(--borders-width-2);
  --button-light-disabled-left-border-color: var(--colors-neutral-line-10);
  --button-light-disabled-left-border-style: var(--borders-style-2);
  --button-light-disabled-left-border-width: var(--borders-width-2);
  --button-light-disabled-right-border-color: var(--colors-neutral-line-10);
  --button-light-disabled-right-border-style: var(--borders-style-2);
  --button-light-disabled-right-border-width: var(--borders-width-2);
  --button-light-disabled-bottom-border-color: var(--colors-neutral-line-10);
  --button-light-disabled-bottom-border-style: var(--borders-style-2);
  --button-light-disabled-bottom-border-width: var(--borders-width-2);
  --button-light-disabled-shadow: var(--shadows-shadow-none);
  --button-light-disabled-bg-color: var(--colors-neutral-fill-10);
  --button-light-disabled-font-color: var(--colors-neutral-text-6);
  --button-dark-default-top-border-color: var(--colors-neutral-line-3);
  --button-dark-default-top-border-style: var(--borders-style-2);
  --button-dark-default-top-border-width: var(--borders-width-2);
  --button-dark-default-left-border-color: var(--colors-neutral-line-3);
  --button-dark-default-left-border-style: var(--borders-style-2);
  --button-dark-default-left-border-width: var(--borders-width-2);
  --button-dark-default-right-border-color: var(--colors-neutral-line-3);
  --button-dark-default-right-border-style: var(--borders-style-2);
  --button-dark-default-right-border-width: var(--borders-width-2);
  --button-dark-default-bottom-border-color: var(--colors-neutral-line-3);
  --button-dark-default-bottom-border-style: var(--borders-style-2);
  --button-dark-default-bottom-border-width: var(--borders-width-2);
  --button-dark-default-shadow: var(--shadows-shadow-none);
  --button-dark-default-bg-color: var(--colors-neutral-fill-3);
  --button-dark-default-font-color: var(--colors-neutral-text-11);
  --button-dark-hover-top-border-color: var(--colors-neutral-line-4);
  --button-dark-hover-top-border-style: var(--borders-style-2);
  --button-dark-hover-top-border-width: var(--borders-width-2);
  --button-dark-hover-left-border-color: var(--colors-neutral-line-4);
  --button-dark-hover-left-border-style: var(--borders-style-2);
  --button-dark-hover-left-border-width: var(--borders-width-2);
  --button-dark-hover-right-border-color: var(--colors-neutral-line-4);
  --button-dark-hover-right-border-style: var(--borders-style-2);
  --button-dark-hover-right-border-width: var(--borders-width-2);
  --button-dark-hover-bottom-border-color: var(--colors-neutral-line-4);
  --button-dark-hover-bottom-border-style: var(--borders-style-2);
  --button-dark-hover-bottom-border-width: var(--borders-width-2);
  --button-dark-hover-shadow: var(--shadows-shadow-none);
  --button-dark-hover-bg-color: var(--colors-neutral-fill-4);
  --button-dark-hover-font-color: var(--colors-neutral-text-11);
  --button-dark-active-top-border-color: var(--colors-neutral-line-5);
  --button-dark-active-top-border-style: var(--borders-style-2);
  --button-dark-active-top-border-width: var(--borders-width-2);
  --button-dark-active-left-border-color: var(--colors-neutral-line-5);
  --button-dark-active-left-border-style: var(--borders-style-2);
  --button-dark-active-left-border-width: var(--borders-width-2);
  --button-dark-active-right-border-color: var(--colors-neutral-line-5);
  --button-dark-active-right-border-style: var(--borders-style-2);
  --button-dark-active-right-border-width: var(--borders-width-2);
  --button-dark-active-bottom-border-color: var(--colors-neutral-line-5);
  --button-dark-active-bottom-border-style: var(--borders-style-2);
  --button-dark-active-bottom-border-width: var(--borders-width-2);
  --button-dark-active-shadow: var(--shadows-shadow-none);
  --button-dark-active-bg-color: var(--colors-neutral-fill-5);
  --button-dark-active-font-color: var(--colors-neutral-text-11);
  --button-dark-disabled-top-border-color: var(--colors-neutral-line-10);
  --button-dark-disabled-top-border-style: var(--borders-style-2);
  --button-dark-disabled-top-border-width: var(--borders-width-2);
  --button-dark-disabled-left-border-color: var(--colors-neutral-line-10);
  --button-dark-disabled-left-border-style: var(--borders-style-2);
  --button-dark-disabled-left-border-width: var(--borders-width-2);
  --button-dark-disabled-right-border-color: var(--colors-neutral-line-10);
  --button-dark-disabled-right-border-style: var(--borders-style-2);
  --button-dark-disabled-right-border-width: var(--borders-width-2);
  --button-dark-disabled-bottom-border-color: var(--colors-neutral-line-10);
  --button-dark-disabled-bottom-border-style: var(--borders-style-2);
  --button-dark-disabled-bottom-border-width: var(--borders-width-2);
  --button-dark-disabled-shadow: var(--shadows-shadow-none);
  --button-dark-disabled-bg-color: var(--colors-neutral-fill-10);
  --button-dark-disabled-font-color: var(--colors-neutral-text-6);
  --button-link-default-top-border-color: transparent;
  --button-link-default-top-border-style: var(--borders-style-1);
  --button-link-default-top-border-width: var(--borders-width-1);
  --button-link-default-left-border-color: transparent;
  --button-link-default-left-border-style: var(--borders-style-1);
  --button-link-default-left-border-width: var(--borders-width-1);
  --button-link-default-right-border-color: transparent;
  --button-link-default-right-border-style: var(--borders-style-1);
  --button-link-default-right-border-width: var(--borders-width-1);
  --button-link-default-bottom-border-color: transparent;
  --button-link-default-bottom-border-style: var(--borders-style-1);
  --button-link-default-bottom-border-width: var(--borders-width-1);
  --button-link-default-shadow: var(--shadows-shadow-none);
  --button-link-default-bg-color: transparent;
  --button-link-default-font-color: var(--colors-link-5);
  --button-link-hover-top-border-color: transparent;
  --button-link-hover-top-border-style: var(--borders-style-1);
  --button-link-hover-top-border-width: var(--borders-width-1);
  --button-link-hover-left-border-color: transparent;
  --button-link-hover-left-border-style: var(--borders-style-1);
  --button-link-hover-left-border-width: var(--borders-width-1);
  --button-link-hover-right-border-color: transparent;
  --button-link-hover-right-border-style: var(--borders-style-1);
  --button-link-hover-right-border-width: var(--borders-width-1);
  --button-link-hover-bottom-border-color: transparent;
  --button-link-hover-bottom-border-style: var(--borders-style-1);
  --button-link-hover-bottom-border-width: var(--borders-width-1);
  --button-link-hover-shadow: var(--shadows-shadow-none);
  --button-link-hover-bg-color: transparent;
  --button-link-hover-font-color: var(--colors-link-6);
  --button-link-active-top-border-color: transparent;
  --button-link-active-top-border-style: var(--borders-style-1);
  --button-link-active-top-border-width: var(--borders-width-1);
  --button-link-active-left-border-color: transparent;
  --button-link-active-left-border-style: var(--borders-style-1);
  --button-link-active-left-border-width: var(--borders-width-1);
  --button-link-active-right-border-color: transparent;
  --button-link-active-right-border-style: var(--borders-style-1);
  --button-link-active-right-border-width: var(--borders-width-1);
  --button-link-active-bottom-border-color: transparent;
  --button-link-active-bottom-border-style: var(--borders-style-1);
  --button-link-active-bottom-border-width: var(--borders-width-1);
  --button-link-active-shadow: var(--shadows-shadow-none);
  --button-link-active-bg-color: transparent;
  --button-link-active-font-color: var(--colors-link-4);
  --button-link-disabled-top-border-color: transparent;
  --button-link-disabled-top-border-style: var(--borders-style-1);
  --button-link-disabled-top-border-width: var(--borders-width-1);
  --button-link-disabled-left-border-color: transparent;
  --button-link-disabled-left-border-style: var(--borders-style-1);
  --button-link-disabled-left-border-width: var(--borders-width-1);
  --button-link-disabled-right-border-color: transparent;
  --button-link-disabled-right-border-style: var(--borders-style-1);
  --button-link-disabled-right-border-width: var(--borders-width-1);
  --button-link-disabled-bottom-border-color: transparent;
  --button-link-disabled-bottom-border-style: var(--borders-style-1);
  --button-link-disabled-bottom-border-width: var(--borders-width-1);
  --button-link-disabled-shadow: var(--shadows-shadow-none);
  --button-link-disabled-bg-color: transparent;
  --button-link-disabled-font-color: var(--colors-neutral-text-6);

  --button-text-default-top-border-color: var(--colors-brand-10);
  --button-text-default-top-border-style: var(--borders-style-2);
  --button-text-default-top-border-width: var(--borders-width-1);
  --button-text-default-left-border-color: var(--colors-brand-10);
  --button-text-default-left-border-style: var(--borders-style-2);
  --button-text-default-left-border-width: var(--borders-width-1);
  --button-text-default-right-border-color: var(--colors-brand-10);
  --button-text-default-right-border-style: var(--borders-style-2);
  --button-text-default-right-border-width: var(--borders-width-1);
  --button-text-default-bottom-border-color: var(--colors-brand-10);
  --button-text-default-bottom-border-style: var(--borders-style-2);
  --button-text-default-bottom-border-width: var(--borders-width-1);
  --button-text-default-shadow: var(--shadows-shadow-none);
  --button-text-default-bg-color: var(--colors-brand-10);
  --button-text-default-font-color: var(--colors-neutral-text-2);
  --button-text-hover-top-border-color: var(--colors-brand-10);
  --button-text-hover-top-border-style: var(--borders-style-2);
  --button-text-hover-top-border-width: var(--borders-width-1);
  --button-text-hover-left-border-color: var(--colors-brand-10);
  --button-text-hover-left-border-style: var(--borders-style-2);
  --button-text-hover-left-border-width: var(--borders-width-1);
  --button-text-hover-right-border-color: var(--colors-brand-10);
  --button-text-hover-right-border-style: var(--borders-style-2);
  --button-text-hover-right-border-width: var(--borders-width-1);
  --button-text-hover-bottom-border-color: var(--colors-brand-10);
  --button-text-hover-bottom-border-style: var(--borders-style-2);
  --button-text-hover-bottom-border-width: var(--borders-width-1);
  --button-text-hover-shadow: var(--shadows-shadow-none);
  --button-text-hover-bg-color: var(--colors-neutral-fill-11);
  --button-text-hover-font-color: var(--colors-neutral-text-2);
  --button-text-active-top-border-color: var(--colors-brand-10);
  --button-text-active-top-border-style: var(--borders-style-2);
  --button-text-active-top-border-width: var(--borders-width-1);
  --button-text-active-left-border-color: var(--colors-brand-10);
  --button-text-active-left-border-style: var(--borders-style-2);
  --button-text-active-left-border-width: var(--borders-width-1);
  --button-text-active-right-border-color: var(--colors-brand-10);
  --button-text-active-right-border-style: var(--borders-style-2);
  --button-text-active-right-border-width: var(--borders-width-1);
  --button-text-active-bottom-border-color: var(--colors-brand-10);
  --button-text-active-bottom-border-style: var(--borders-style-2);
  --button-text-active-bottom-border-width: var(--borders-width-1);
  --button-text-active-shadow: var(--shadows-shadow-none);
  --button-text-active-bg-color: var(--colors-neutral-fill-11);
  --button-text-active-font-color: var(--colors-neutral-text-2);
  --button-text-disabled-top-border-color: var(--colors-neutral-line-10);
  --button-text-disabled-top-border-style: var(--borders-style-2);
  --button-text-disabled-top-border-width: var(--borders-width-2);
  --button-text-disabled-left-border-color: var(--colors-neutral-line-10);
  --button-text-disabled-left-border-style: var(--borders-style-2);
  --button-text-disabled-left-border-width: var(--borders-width-2);
  --button-text-disabled-right-border-color: var(--colors-neutral-line-10);
  --button-text-disabled-right-border-style: var(--borders-style-2);
  --button-text-disabled-right-border-width: var(--borders-width-2);
  --button-text-disabled-bottom-border-color: var(--colors-neutral-line-10);
  --button-text-disabled-bottom-border-style: var(--borders-style-2);
  --button-text-disabled-bottom-border-width: var(--borders-width-2);
  --button-text-disabled-shadow: var(--shadows-shadow-none);
  --button-text-disabled-bg-color: var(--colors-neutral-fill-10);
  --button-text-disabled-font-color: var(--colors-neutral-text-6);

  --button-size-default-top-left-border-radius: var(--borders-radius-3);
  --button-size-default-top-right-border-radius: var(--borders-radius-3);
  --button-size-default-bottom-left-border-radius: var(--borders-radius-3);
  --button-size-default-bottom-right-border-radius: var(--borders-radius-3);
  --button-size-default-height: var(--sizes-base-16);
  --button-size-default-fontSize: var(--fonts-size-7);
  --button-size-default-minWidth: var(--sizes-size-1);
  --button-size-default-icon-size: var(--sizes-size-8);
  --button-size-default-fontWeight: var(--fonts-weight-6);
  --button-size-default-lineHeight: var(--fonts-lineHeight-2);
  --button-size-default-icon-margin: var(--sizes-size-3);
  --button-size-default-marginTop: var(--sizes-size-0);
  --button-size-default-marginLeft: var(--sizes-size-0);
  --button-size-default-paddingTop: var(--sizes-size-3);
  --button-size-default-marginRight: var(--sizes-size-0);
  --button-size-default-paddingLeft: var(--sizes-size-7);
  --button-size-default-marginBottom: var(--sizes-size-0);
  --button-size-default-paddingRight: var(--sizes-size-7);
  --button-size-default-paddingBottom: var(--sizes-size-3);
  --button-size-xs-top-left-border-radius: var(--borders-radius-3);
  --button-size-xs-top-right-border-radius: var(--borders-radius-3);
  --button-size-xs-bottom-left-border-radius: var(--borders-radius-3);
  --button-size-xs-bottom-right-border-radius: var(--borders-radius-3);
  --button-size-xs-height: var(--sizes-base-11);
  --button-size-xs-fontSize: var(--fonts-size-8);
  --button-size-xs-minWidth: var(--sizes-size-1);
  --button-size-xs-icon-size: var(--sizes-size-8);
  --button-size-xs-fontWeight: var(--fonts-weight-6);
  --button-size-xs-lineHeight: var(--fonts-lineHeight-2);
  --button-size-xs-icon-margin: var(--sizes-size-3);
  --button-size-xs-marginTop: var(--sizes-size-0);
  --button-size-xs-marginLeft: var(--sizes-size-0);
  --button-size-xs-paddingTop: var(--sizes-size-2);
  --button-size-xs-marginRight: var(--sizes-size-0);
  --button-size-xs-paddingLeft: var(--sizes-size-3);
  --button-size-xs-marginBottom: var(--sizes-size-0);
  --button-size-xs-paddingRight: var(--sizes-size-3);
  --button-size-xs-paddingBottom: var(--sizes-size-2);
  --button-size-sm-top-left-border-radius: var(--borders-radius-3);
  --button-size-sm-top-right-border-radius: var(--borders-radius-3);
  --button-size-sm-bottom-left-border-radius: var(--borders-radius-3);
  --button-size-sm-bottom-right-border-radius: var(--borders-radius-3);
  --button-size-sm-height: var(--sizes-base-15);
  --button-size-sm-fontSize: var(--fonts-size-8);
  --button-size-sm-minWidth: var(--sizes-size-1);
  --button-size-sm-icon-size: var(--sizes-size-8);
  --button-size-sm-fontWeight: var(--fonts-weight-6);
  --button-size-sm-lineHeight: var(--fonts-lineHeight-2);
  --button-size-sm-icon-margin: var(--sizes-size-3);
  --button-size-sm-marginTop: var(--sizes-size-0);
  --button-size-sm-marginLeft: var(--sizes-size-0);
  --button-size-sm-paddingTop: var(--sizes-size-3);
  --button-size-sm-marginRight: var(--sizes-size-0);
  --button-size-sm-paddingLeft: var(--sizes-size-6);
  --button-size-sm-marginBottom: var(--sizes-size-0);
  --button-size-sm-paddingRight: var(--sizes-size-6);
  --button-size-sm-paddingBottom: var(--sizes-size-3);
  --button-size-md-top-left-border-radius: var(--borders-radius-3);
  --button-size-md-top-right-border-radius: var(--borders-radius-3);
  --button-size-md-bottom-left-border-radius: var(--borders-radius-3);
  --button-size-md-bottom-right-border-radius: var(--borders-radius-3);
  --button-size-md-height: var(--sizes-base-16);
  --button-size-md-fontSize: var(--fonts-size-7);
  --button-size-md-minWidth: var(--sizes-size-1);
  --button-size-md-icon-size: var(--sizes-size-8);
  --button-size-md-fontWeight: var(--fonts-weight-6);
  --button-size-md-lineHeight: var(--fonts-lineHeight-2);
  --button-size-md-icon-margin: var(--sizes-size-3);
  --button-size-md-marginTop: var(--sizes-size-0);
  --button-size-md-marginLeft: var(--sizes-size-0);
  --button-size-md-paddingTop: var(--sizes-size-3);
  --button-size-md-marginRight: var(--sizes-size-0);
  --button-size-md-paddingLeft: var(--sizes-size-7);
  --button-size-md-marginBottom: var(--sizes-size-0);
  --button-size-md-paddingRight: var(--sizes-size-7);
  --button-size-md-paddingBottom: var(--sizes-size-3);
  --button-size-lg-top-left-border-radius: var(--borders-radius-3);
  --button-size-lg-top-right-border-radius: var(--borders-radius-3);
  --button-size-lg-bottom-left-border-radius: var(--borders-radius-3);
  --button-size-lg-bottom-right-border-radius: var(--borders-radius-3);
  --button-size-lg-height: var(--sizes-base-19);
  --button-size-lg-fontSize: var(--fonts-size-7);
  --button-size-lg-minWidth: var(--sizes-size-1);
  --button-size-lg-icon-size: var(--sizes-size-8);
  --button-size-lg-fontWeight: var(--fonts-weight-6);
  --button-size-lg-lineHeight: var(--fonts-lineHeight-2);
  --button-size-lg-icon-margin: var(--sizes-size-3);
  --button-size-lg-marginTop: var(--sizes-size-0);
  --button-size-lg-marginLeft: var(--sizes-size-0);
  --button-size-lg-paddingTop: var(--sizes-size-6);
  --button-size-lg-marginRight: var(--sizes-size-0);
  --button-size-lg-paddingLeft: var(--sizes-size-9);
  --button-size-lg-marginBottom: var(--sizes-size-0);
  --button-size-lg-paddingRight: var(--sizes-size-9);
  --button-size-lg-paddingBottom: var(--sizes-size-6);
  --transfer-base-top-border-color: var(--colors-neutral-line-8);
  --transfer-base-top-border-style: var(--borders-style-2);
  --transfer-base-top-border-width: var(--borders-width-2);
  --transfer-base-left-border-color: var(--colors-neutral-line-8);
  --transfer-base-left-border-style: var(--borders-style-2);
  --transfer-base-left-border-width: var(--borders-width-2);
  --transfer-base-right-border-color: var(--colors-neutral-line-8);
  --transfer-base-right-border-style: var(--borders-style-2);
  --transfer-base-right-border-width: var(--borders-width-2);
  --transfer-base-bottom-border-color: var(--colors-neutral-line-8);
  --transfer-base-bottom-border-style: var(--borders-style-2);
  --transfer-base-bottom-border-width: var(--borders-width-2);
  --transfer-base-top-left-border-radius: var(--borders-radius-3);
  --transfer-base-top-right-border-radius: var(--borders-radius-3);
  --transfer-base-bottom-left-border-radius: var(--borders-radius-2);
  --transfer-base-bottom-right-border-radius: var(--borders-radius-2);
  --transfer-base-shadow: var(--shadows-shadow-none);
  --transfer-base-title-bg: var(--colors-neutral-fill-10);
  --transfer-base-title-fontSize: var(--fonts-size-7);
  --transfer-base-content-fontSize: var(--fonts-size-7);
  --transfer-base-title-font-color: var(--colors-neutral-text-2);
  --transfer-base-title-fontWeight: var(--fonts-weight-6);
  --transfer-base-title-lineHeight: var(--fonts-lineHeight-2);
  --transfer-base-content-font-color: var(--colors-neutral-text-2);
  --transfer-base-content-fontWeight: var(--fonts-weight-6);
  --transfer-base-content-lineHeight: var(--fonts-lineHeight-2);
  --transfer-base-body-marginTop: var(--sizes-size-1);
  --transfer-base-body-marginLeft: var(--sizes-size-1);
  --transfer-base-body-paddingTop: var(--sizes-size-1);
  --transfer-base-body-marginRight: var(--sizes-size-1);
  --transfer-base-body-paddingLeft: var(--sizes-size-1);
  --transfer-base-body-marginBottom: var(--sizes-size-1);
  --transfer-base-body-paddingRight: var(--sizes-size-1);
  --transfer-base-body-paddingBottom: var(--sizes-size-1);
  --transfer-base-header-marginTop: var(--sizes-size-1);
  --transfer-base-header-marginLeft: var(--sizes-size-1);
  --transfer-base-header-paddingTop: var(--sizes-size-5);
  --transfer-base-header-marginRight: var(--sizes-size-1);
  --transfer-base-header-paddingLeft: var(--sizes-size-8);
  --transfer-base-header-marginBottom: var(--sizes-size-1);
  --transfer-base-header-paddingRight: var(--sizes-size-8);
  --transfer-base-header-paddingBottom: var(--sizes-size-5);
  --transfer-base-option-marginTop: var(--sizes-size-1);
  --transfer-base-option-marginLeft: var(--sizes-size-1);
  --transfer-base-option-paddingTop: var(--sizes-size-5);
  --transfer-base-option-marginRight: var(--sizes-size-1);
  --transfer-base-option-paddingLeft: var(--sizes-size-8);
  --transfer-base-option-marginBottom: var(--sizes-size-1);
  --transfer-base-option-paddingRight: var(--sizes-size-8);
  --transfer-base-option-paddingBottom: var(--sizes-size-5);
  --transfer-tree-top-left-border-radius: var(--borders-radius-2);
  --transfer-tree-top-right-border-radius: var(--borders-radius-2);
  --transfer-tree-bottom-left-border-radius: var(--borders-radius-2);
  --transfer-tree-bottom-right-border-radius: var(--borders-radius-2);
  --transfer-tree-bg-hover-color: var(--colors-neutral-fill-10);
  --transfer-tree-bg-active-color: var(--colors-brand-13);
  --transfer-tree-marginTop: var(--sizes-size-1);
  --transfer-tree-marginLeft: var(--sizes-size-1);
  --transfer-tree-paddingTop: var(--sizes-size-3);
  --transfer-tree-marginRight: var(--sizes-size-1);
  --transfer-tree-paddingLeft: var(--sizes-size-7);
  --transfer-tree-marginBottom: var(--sizes-size-2);
  --transfer-tree-paddingRight: var(--sizes-size-7);
  --transfer-tree-paddingBottom: var(--sizes-size-3);
  --transfer-tree-option-marginTop: var(--sizes-size-1);
  --transfer-tree-option-marginLeft: var(--sizes-size-1);
  --transfer-tree-option-paddingTop: var(--sizes-size-1);
  --transfer-tree-option-marginRight: var(--sizes-size-1);
  --transfer-tree-option-paddingLeft: var(--sizes-size-6);
  --transfer-tree-option-marginBottom: var(--sizes-size-4);
  --transfer-tree-option-paddingRight: var(--sizes-size-1);
  --transfer-tree-option-paddingBottom: var(--sizes-size-1);
  --transfer-group-fontSize: var(--fonts-size-7);
  --transfer-group-font-color: var(--colors-neutral-text-5);
  --transfer-group-fontWeight: var(--fonts-weight-6);
  --transfer-group-lineHeight: var(--fonts-lineHeight-2);
  --transfer-table-last-paddingRight: var(--sizes-base-9);
  --transfer-table-header-marginTop: var(--sizes-size-1);
  --transfer-table-header-marginLeft: var(--sizes-size-1);
  --transfer-table-header-paddingTop: var(--sizes-size-5);
  --transfer-table-header-marginRight: var(--sizes-size-1);
  --transfer-table-header-paddingLeft: var(--sizes-size-7);
  --transfer-table-header-marginBottom: var(--sizes-size-1);
  --transfer-table-header-paddingRight: var(--sizes-size-7);
  --transfer-table-header-paddingBottom: var(--sizes-size-5);
  --transfer-table-option-marginTop: var(--sizes-size-1);
  --transfer-table-option-marginLeft: var(--sizes-size-1);
  --transfer-table-option-paddingTop: var(--sizes-size-4);
  --transfer-table-option-marginRight: var(--sizes-size-1);
  --transfer-table-option-paddingLeft: var(--sizes-size-7);
  --transfer-table-option-marginBottom: var(--sizes-size-2);
  --transfer-table-option-paddingRight: var(--sizes-size-7);
  --transfer-table-option-paddingBottom: var(--sizes-size-5);
  --transfer-search-top-border-color: var(--colors-neutral-line-8);
  --transfer-search-top-border-style: var(--borders-style-2);
  --transfer-search-top-border-width: var(--borders-width-2);
  --transfer-search-left-border-color: var(--colors-neutral-line-8);
  --transfer-search-left-border-style: var(--borders-style-2);
  --transfer-search-left-border-width: var(--borders-width-2);
  --transfer-search-right-border-color: var(--colors-neutral-line-8);
  --transfer-search-right-border-style: var(--borders-style-2);
  --transfer-search-right-border-width: var(--borders-width-2);
  --transfer-search-bottom-border-color: var(--colors-neutral-line-8);
  --transfer-search-bottom-border-style: var(--borders-style-2);
  --transfer-search-bottom-border-width: var(--borders-width-2);
  --transfer-search-top-left-border-radius: var(--borders-radius-3);
  --transfer-search-top-right-border-radius: var(--borders-radius-3);
  --transfer-search-bottom-left-border-radius: var(--borders-radius-3);
  --transfer-search-bottom-right-border-radius: var(--borders-radius-3);
  --transfer-search-shadow: var(--shadows-shadow-none);
  --transfer-search-fontSize: var(--fonts-size-7);
  --transfer-search-font-color: var(--colors-neutral-text-2);
  --transfer-search-fontWeight: var(--fonts-weight-6);
  --transfer-search-lineHeight: var(--fonts-lineHeight-2);
  --transfer-search-border-hover-color: var(--colors-brand-4);
  --transfer-search-marginTop: var(--sizes-size-1);
  --transfer-search-marginLeft: var(--sizes-size-1);
  --transfer-search-paddingTop: var(--sizes-size-6);
  --transfer-search-marginRight: var(--sizes-size-1);
  --transfer-search-paddingLeft: var(--sizes-size-6);
  --transfer-search-marginBottom: var(--sizes-size-1);
  --transfer-search-paddingRight: var(--sizes-size-6);
  --transfer-search-paddingBottom: var(--sizes-size-6);
  --transfer-search-border-active-color: var(--colors-brand-4);
  --transfer-search-placeholder-font-color: var(--colors-neutral-text-6);
  --transfer-search-input-marginTop: var(--sizes-size-1);
  --transfer-search-input-marginLeft: var(--sizes-size-1);
  --transfer-search-input-paddingTop: var(--sizes-size-4);
  --transfer-search-input-marginRight: var(--sizes-size-1);
  --transfer-search-input-paddingLeft: var(--sizes-size-7);
  --transfer-search-input-marginBottom: var(--sizes-size-1);
  --transfer-search-input-paddingRight: var(--sizes-size-7);
  --transfer-search-input-paddingBottom: var(--sizes-size-4);
  --transfer-chained-marginTop: var(--sizes-size-1);
  --transfer-chained-marginLeft: var(--sizes-size-1);
  --transfer-chained-paddingTop: var(--sizes-size-5);
  --transfer-chained-marginRight: var(--sizes-size-1);
  --transfer-chained-paddingLeft: var(--sizes-size-6);
  --transfer-chained-marginBottom: var(--sizes-size-1);
  --transfer-chained-paddingRight: var(--sizes-size-6);
  --transfer-chained-paddingBottom: var(--sizes-size-5);
  --input-default-default-top-border-color: var(--colors-neutral-line-8);
  --input-default-default-top-border-width: var(--borders-width-2);
  --input-default-default-top-border-style: var(--borders-style-2);
  --input-default-default-right-border-color: var(--colors-neutral-line-8);
  --input-default-default-right-border-width: var(--borders-width-2);
  --input-default-default-right-border-style: var(--borders-style-2);
  --input-default-default-bottom-border-color: var(--colors-neutral-line-8);
  --input-default-default-bottom-border-width: var(--borders-width-2);
  --input-default-default-bottom-border-style: var(--borders-style-2);
  --input-default-default-left-border-color: var(--colors-neutral-line-8);
  --input-default-default-left-border-width: var(--borders-width-2);
  --input-default-default-left-border-style: var(--borders-style-2);
  --input-default-default-top-right-border-radius: var(--borders-radius-3);
  --input-default-default-top-left-border-radius: var(--borders-radius-3);
  --input-default-default-bottom-right-border-radius: var(--borders-radius-3);
  --input-default-default-bottom-left-border-radius: var(--borders-radius-3);
  --input-default-default-paddingTop: var(--sizes-size-3);
  --input-default-default-paddingBottom: var(--sizes-size-3);
  --input-default-default-paddingLeft: var(--sizes-size-6);
  --input-default-default-paddingRight: var(--sizes-size-6);
  --input-default-default-bg-color: var(--colors-neutral-fill-11);
  --input-default-hover-top-border-color: var(--colors-brand-5);
  --input-default-hover-top-border-width: var(--borders-width-2);
  --input-default-hover-top-border-style: var(--borders-style-2);
  --input-default-hover-right-border-color: var(--colors-brand-5);
  --input-default-hover-right-border-width: var(--borders-width-2);
  --input-default-hover-right-border-style: var(--borders-style-2);
  --input-default-hover-bottom-border-color: var(--colors-brand-5);
  --input-default-hover-bottom-border-width: var(--borders-width-2);
  --input-default-hover-bottom-border-style: var(--borders-style-2);
  --input-default-hover-left-border-color: var(--colors-brand-5);
  --input-default-hover-left-border-width: var(--borders-width-2);
  --input-default-hover-left-border-style: var(--borders-style-2);
  --input-default-hover-top-right-border-radius: var(--borders-radius-3);
  --input-default-hover-top-left-border-radius: var(--borders-radius-3);
  --input-default-hover-bottom-right-border-radius: var(--borders-radius-3);
  --input-default-hover-bottom-left-border-radius: var(--borders-radius-3);
  --input-default-hover-paddingTop: var(--sizes-size-3);
  --input-default-hover-paddingBottom: var(--sizes-size-3);
  --input-default-hover-paddingLeft: var(--sizes-size-6);
  --input-default-hover-paddingRight: var(--sizes-size-6);
  --input-default-hover-bg-color: var(--colors-neutral-fill-11);
  --input-default-active-top-border-color: var(--colors-brand-5);
  --input-default-active-top-border-width: var(--borders-width-2);
  --input-default-active-top-border-style: var(--borders-style-2);
  --input-default-active-right-border-color: var(--colors-brand-5);
  --input-default-active-right-border-width: var(--borders-width-2);
  --input-default-active-right-border-style: var(--borders-style-2);
  --input-default-active-bottom-border-color: var(--colors-brand-5);
  --input-default-active-bottom-border-width: var(--borders-width-2);
  --input-default-active-bottom-border-style: var(--borders-style-2);
  --input-default-active-left-border-color: var(--colors-brand-5);
  --input-default-active-left-border-width: var(--borders-width-2);
  --input-default-active-left-border-style: var(--borders-style-2);
  --input-default-active-top-right-border-radius: var(--borders-radius-3);
  --input-default-active-top-left-border-radius: var(--borders-radius-3);
  --input-default-active-bottom-right-border-radius: var(--borders-radius-3);
  --input-default-active-bottom-left-border-radius: var(--borders-radius-3);
  --input-default-active-paddingTop: var(--sizes-size-3);
  --input-default-active-paddingBottom: var(--sizes-size-3);
  --input-default-active-paddingLeft: var(--sizes-size-6);
  --input-default-active-paddingRight: var(--sizes-size-6);
  --input-default-active-shadow: var(--shadows-shadow-none);
  --input-default-active-bg-color: var(--colors-neutral-fill-11);
  --input-default-disabled-top-border-color: var(--colors-neutral-line-8);
  --input-default-disabled-top-border-width: var(--borders-width-2);
  --input-default-disabled-top-border-style: var(--borders-style-2);
  --input-default-disabled-right-border-color: var(--colors-neutral-line-8);
  --input-default-disabled-right-border-width: var(--borders-width-2);
  --input-default-disabled-right-border-style: var(--borders-style-2);
  --input-default-disabled-bottom-border-color: var(--colors-neutral-line-8);
  --input-default-disabled-bottom-border-width: var(--borders-width-2);
  --input-default-disabled-bottom-border-style: var(--borders-style-2);
  --input-default-disabled-left-border-color: var(--colors-neutral-line-8);
  --input-default-disabled-left-border-width: var(--borders-width-2);
  --input-default-disabled-left-border-style: var(--borders-style-2);
  --input-default-disabled-top-right-border-radius: var(--borders-radius-3);
  --input-default-disabled-top-left-border-radius: var(--borders-radius-3);
  --input-default-disabled-bottom-right-border-radius: var(--borders-radius-3);
  --input-default-disabled-bottom-left-border-radius: var(--borders-radius-3);
  --input-default-disabled-paddingTop: var(--sizes-size-3);
  --input-default-disabled-paddingBottom: var(--sizes-size-3);
  --input-default-disabled-paddingLeft: var(--sizes-size-6);
  --input-default-disabled-paddingRight: var(--sizes-size-6);
  --input-default-disabled-bg-color: var(--colors-neutral-fill-10);
  --input-clearable-icon: '<svg t="1642652418667" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3606"><path d="M512 39.384615C250.092308 39.384615 39.384615 250.092308 39.384615 512s210.707692 472.615385 472.615385 472.615385 472.615385-210.707692 472.615385-472.615385S773.907692 39.384615 512 39.384615z m96.492308 488.369231l153.6 153.6c7.876923 7.876923 7.876923 19.692308 0 27.569231l-55.138462 55.138461c-7.876923 7.876923-19.692308 7.876923-27.569231 0L525.784615 610.461538c-7.876923-7.876923-19.692308-7.876923-27.56923 0l-153.6 153.6c-7.876923 7.876923-19.692308 7.876923-27.569231 0L261.907692 708.923077c-7.876923-7.876923-7.876923-19.692308 0-27.569231l153.6-153.6c7.876923-7.876923 7.876923-19.692308 0-27.569231l-155.56923-155.56923c-7.876923-7.876923-7.876923-19.692308 0-27.569231l55.138461-55.138462c7.876923-7.876923 19.692308-7.876923 27.569231 0l155.569231 155.569231c7.876923 7.876923 19.692308 7.876923 27.56923 0l153.6-153.6c7.876923-7.876923 19.692308-7.876923 27.569231 0l55.138462 55.138462c7.876923 7.876923 7.876923 19.692308 0 27.56923l-153.6 153.6c-5.907692 7.876923-5.907692 19.692308 0 27.569231z" p-id="3607"></path></svg>';
  --input-clearable-icon-size: var(--sizes-size-8);
  --input-clearable-default-color: var(--colors-neutral-text-7);
  --input-clearable-hover-color: var(--colors-neutral-text-4);
  --input-clearable-active-color: var(--colors-neutral-text-4);
  --input-count-single-fontSize: var(--fonts-size-7);
  --input-count-single-color: var(--colors-neutral-text-6);
  --input-count-multi-fontSize: var(--fonts-size-7);
  --input-count-multi-color: var(--colors-neutral-text-5);
  --input-prefix-fontSize: var(--fonts-size-7);
  --input-prefix-color: var(--colors-neutral-text-1);
  --input-password-invisible-icon: '<svg viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>不可见</title><g id="不可见" stroke="none" stroke-width="1" fill="currentColor" fill-rule="evenodd"><g id="编组"><rect id="矩形" stroke="none" fill="currentColor" opacity="0" x="0.5" y="0.5" width="15" height="15"></rect><path d="M2.91972703,5.00035128 C3.15932221,5.56062137 3.48954828,6.0784548 3.89678565,6.53485922 C4.96673844,7.73914737 6.49518581,8.43995 8.14,8.43995 C9.76664693,8.43995 11.2813058,7.75315198 12.3523764,6.57033395 C12.6920742,6.19520277 12.9803798,5.7761243 13.209327,5.32420638 L13.3395085,5.04920376 L14.2544915,5.45269624 C13.9653387,6.10839593 13.572991,6.71219666 13.0936273,7.24156203 C12.7623988,7.60734835 12.3948705,7.93285848 11.9982387,8.21395897 L12.9566,9.87395 L12.0906,10.37395 L11.1412434,8.72942071 C10.3784723,9.11337429 9.54082663,9.35086388 8.66757967,9.41933209 L8.668,10.97185 L7.668,10.97185 L7.66735222,9.42343888 C6.75745885,9.35969244 5.88560233,9.11282413 5.09602954,8.70830726 L4.1485,10.34855 L3.2825,9.84855 L4.2424457,8.18636156 C3.84593988,7.9008387 3.4793171,7.57058753 3.14992355,7.19983732 C2.73988365,6.74029373 2.39560013,6.22662333 2.12776836,5.67339306 L2.00027297,5.39354872 L2.91972703,5.00035128 Z" id="形状结合"></path></g></g></svg>';
  --input-password-invisible-icon-size: var(--sizes-size-8);
  --input-password-invisible-icon-color: var(--colors-neutral-text-5);
  --input-password-view-icon: '<svg viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>可见</title><g id="可见" stroke="none" stroke-width="1" fill="currentColor" fill-rule="evenodd"><g id="编组"><rect id="矩形" opacity="0" x="0.5" y="0.5" width="15" height="15"></rect><path d="M7.9999,3.0001 C11.9889,3.0001 14.9999,6.8731 14.9999,8.0001 C14.9999,8.8831 11.9889,13.0001 7.9999,13.0001 C3.9609,13.0001 0.9999,8.8831 0.9999,8.0001 C0.9999,6.8731 3.9609,3.0001 7.9999,3.0001 Z M7.9999,4.0001 C4.7329,4.0001 2.2179,7.0861 2.0089,7.9731 C2.2749,8.7711 4.7189,12.0001 7.9999,12.0001 C11.2099,12.0001 13.7339,8.7311 13.9929,7.9631 C13.8069,7.1261 11.2709,4.0001 7.9999,4.0001 Z M7.975,5.879 C9.08,5.879 9.975,6.775 9.975,7.879 C9.975,8.983 9.08,9.879 7.975,9.879 C6.871,9.879 5.975,8.983 5.975,7.879 C5.975,6.775 6.871,5.879 7.975,5.879 Z M7.975,6.879 C7.424,6.879 6.975,7.327 6.975,7.879 C6.975,8.43 7.424,8.879 7.975,8.879 C8.527,8.879 8.975,8.43 8.975,7.879 C8.975,7.327 8.527,6.879 7.975,6.879 Z" id="图标"></path></g</g></svg>';
  --input-password-view-icon-size: var(--sizes-size-8);
  --input-password-view-icon-color: var(--colors-neutral-text-5);
  --input-textarea-paddingTop: var(--sizes-size-3);
  --input-textarea-paddingBottom: var(--sizes-size-3);
  --input-textarea-paddingLeft: var(--sizes-size-6);
  --input-textarea-paddingRight: var(--sizes-base-11);
  --input-addon-text-bg-color-default: var(--colors-neutral-text-11);
  --input-addon-text-bg-color-hover: var(--colors-neutral-text-11);
  --input-addon-text-top-border-color: var(--colors-neutral-line-8);
  --input-addon-text-top-border-width: var(--borders-width-2);
  --input-addon-text-top-border-style: var(--borders-style-2);
  --input-addon-text-right-border-color: var(--colors-neutral-line-8);
  --input-addon-text-right-border-width: var(--borders-width-2);
  --input-addon-text-right-border-style: var(--borders-style-2);
  --input-addon-text-bottom-border-color: var(--colors-neutral-line-8);
  --input-addon-text-bottom-border-width: var(--borders-width-2);
  --input-addon-text-bottom-border-style: var(--borders-style-2);
  --input-addon-text-left-border-color: var(--colors-neutral-line-8);
  --input-addon-text-left-border-width: var(--borders-width-2);
  --input-addon-text-left-border-style: var(--borders-style-2);
  --input-size-sm-height: var(--sizes-base-15);
  --input-size-default-height: var(--sizes-base-16);
  --input-size-md-height: var(--sizes-base-16);
  --input-size-lg-height: var(--sizes-base-19);

  // 数字输入框
  --Form-inputNumber-base-width: var(--sizes-base-12);
  --Form-inputNumber-base-height: var(--sizes-base-16);

  --inputNumber-base-default-top-border-color: var(--colors-neutral-line-8);
  --inputNumber-base-default-top-border-width: var(--borders-width-2);
  --inputNumber-base-default-top-border-style: var(--borders-style-2);
  --inputNumber-base-default-right-border-color: var(--colors-neutral-line-8);
  --inputNumber-base-default-right-border-width: var(--borders-width-2);
  --inputNumber-base-default-right-border-style: var(--borders-style-2);
  --inputNumber-base-default-bottom-border-color: var(--colors-neutral-line-8);
  --inputNumber-base-default-bottom-border-width: var(--borders-width-2);
  --inputNumber-base-default-bottom-border-style: var(--borders-style-2);
  --inputNumber-base-default-left-border-color: var(--colors-neutral-line-8);
  --inputNumber-base-default-left-border-width: var(--borders-width-2);
  --inputNumber-base-default-left-border-style: var(--borders-style-2);
  --inputNumber-base-default-top-right-border-radius: var(--borders-radius-3);
  --inputNumber-base-default-top-left-border-radius: var(--borders-radius-3);
  --inputNumber-base-default-bottom-right-border-radius: var(
    --borders-radius-3
  );
  --inputNumber-base-default-bottom-left-border-radius: var(--borders-radius-3);
  --inputNumber-base-default-paddingTop: var(--sizes-size-3);
  --inputNumber-base-default-paddingBottom: var(--sizes-size-3);
  --inputNumber-base-default-paddingLeft: var(--sizes-size-6);
  --inputNumber-base-default-paddingRight: var(--sizes-size-6);
  --inputNumber-base-default-bg-color: var(--colors-neutral-fill-11);
  --inputNumber-base-hover-top-border-color: var(--colors-brand-5);
  --inputNumber-base-hover-top-border-width: var(--borders-width-2);
  --inputNumber-base-hover-top-border-style: var(--borders-style-2);
  --inputNumber-base-hover-right-border-color: var(--colors-brand-5);
  --inputNumber-base-hover-right-border-width: var(--borders-width-2);
  --inputNumber-base-hover-right-border-style: var(--borders-style-2);
  --inputNumber-base-hover-bottom-border-color: var(--colors-brand-5);
  --inputNumber-base-hover-bottom-border-width: var(--borders-width-2);
  --inputNumber-base-hover-bottom-border-style: var(--borders-style-2);
  --inputNumber-base-hover-left-border-color: var(--colors-brand-5);
  --inputNumber-base-hover-left-border-width: var(--borders-width-2);
  --inputNumber-base-hover-left-border-style: var(--borders-style-2);
  --inputNumber-base-hover-top-right-border-radius: var(--borders-radius-3);
  --inputNumber-base-hover-top-left-border-radius: var(--borders-radius-3);
  --inputNumber-base-hover-bottom-right-border-radius: var(--borders-radius-3);
  --inputNumber-base-hover-bottom-left-border-radius: var(--borders-radius-3);
  --inputNumber-base-hover-paddingTop: var(--sizes-size-3);
  --inputNumber-base-hover-paddingBottom: var(--sizes-size-3);
  --inputNumber-base-hover-paddingLeft: var(--sizes-size-7);
  --inputNumber-base-hover-paddingRight: var(--sizes-size-7);
  --inputNumber-base-hover-bg-color: var(--colors-neutral-fill-11);
  --inputNumber-base-active-top-border-color: var(--colors-brand-5);
  --inputNumber-base-active-top-border-width: var(--borders-width-2);
  --inputNumber-base-active-top-border-style: var(--borders-style-2);
  --inputNumber-base-active-right-border-color: var(--colors-brand-5);
  --inputNumber-base-active-right-border-width: var(--borders-width-2);
  --inputNumber-base-active-right-border-style: var(--borders-style-2);
  --inputNumber-base-active-bottom-border-color: var(--colors-brand-5);
  --inputNumber-base-active-bottom-border-width: var(--borders-width-2);
  --inputNumber-base-active-bottom-border-style: var(--borders-style-2);
  --inputNumber-base-active-left-border-color: var(--colors-brand-5);
  --inputNumber-base-active-left-border-width: var(--borders-width-2);
  --inputNumber-base-active-left-border-style: var(--borders-style-2);
  --inputNumber-base-active-top-right-border-radius: var(--borders-radius-3);
  --inputNumber-base-active-top-left-border-radius: var(--borders-radius-3);
  --inputNumber-base-active-bottom-right-border-radius: var(--borders-radius-3);
  --inputNumber-base-active-bottom-left-border-radius: var(--borders-radius-3);
  --inputNumber-base-active-paddingTop: var(--sizes-size-3);
  --inputNumber-base-active-paddingBottom: var(--sizes-size-3);
  --inputNumber-base-active-paddingLeft: var(--sizes-size-7);
  --inputNumber-base-active-paddingRight: var(--sizes-size-7);
  --inputNumber-base-active-shadow: var(--Form-input-boxShadow);
  --inputNumber-base-active-bg-color: var(--colors-neutral-fill-11);
  --inputNumber-base-disabled-top-border-color: var(--colors-neutral-line-8);
  --inputNumber-base-disabled-top-border-width: var(--borders-width-2);
  --inputNumber-base-disabled-top-border-style: var(--borders-style-2);
  --inputNumber-base-disabled-right-border-color: var(--colors-neutral-line-8);
  --inputNumber-base-disabled-right-border-width: var(--borders-width-2);
  --inputNumber-base-disabled-right-border-style: var(--borders-style-2);
  --inputNumber-base-disabled-bottom-border-color: var(--colors-neutral-line-8);
  --inputNumber-base-disabled-bottom-border-width: var(--borders-width-2);
  --inputNumber-base-disabled-bottom-border-style: var(--borders-style-2);
  --inputNumber-base-disabled-left-border-color: var(--colors-neutral-line-8);
  --inputNumber-base-disabled-left-border-width: var(--borders-width-2);
  --inputNumber-base-disabled-left-border-style: var(--borders-style-2);
  --inputNumber-base-disabled-top-right-border-radius: var(--borders-radius-3);
  --inputNumber-base-disabled-top-left-border-radius: var(--borders-radius-3);
  --inputNumber-base-disabled-bottom-right-border-radius: var(
    --borders-radius-3
  );
  --inputNumber-base-disabled-bottom-left-border-radius: var(
    --borders-radius-3
  );
  --inputNumber-base-disabled-paddingTop: var(--sizes-size-3);
  --inputNumber-base-disabled-paddingBottom: var(--sizes-size-3);
  --inputNumber-base-disabled-paddingLeft: var(--sizes-size-7);
  --inputNumber-base-disabled-paddingRight: var(--sizes-size-7);
  --inputNumber-base-disabled-bg-color: var(--colors-neutral-fill-10);
  --inputNumber-base-default-icon-fontSize: var(--fonts-size-8);
  --inputNumber-base-default-icon-color: var(--colors-neutral-text-2);
  --inputNumber-base-hover-icon-color: var(--colors-brand-5);
  --inputNumber-base-active-icon-color: var(--colors-brand-5);
  --inputNumber-base-default-step-bg: var(--colors-neutral-fill-11);
  --inputNumber-base-hover-step-bg: var(--colors-neutral-fill-11);
  --inputNumber-base-active-step-bg: var(--colors-neutral-fill-11);
  --inputNumber-base-default-unit-width: var(--sizes-base-28);
  --inputNumber-base-default-unit-paddingTop: calc(
    (
        var(--Form-selectOption-height) - var(--Form-input-lineHeight) *
          var(--Form-input-fontSize) - var(--Form-input-borderWidth) * 2
      ) / 2
  );
  --inputNumber-base-default-unit-paddingBottom: calc(
    (
        var(--Form-selectOption-height) - var(--Form-input-lineHeight) *
          var(--Form-input-fontSize) - var(--Form-input-borderWidth) * 2
      ) / 2
  );
  --inputNumber-base-default-unit-paddingLeft: var(--sizes-size-6);
  --inputNumber-base-default-unit-paddingRight: var(--sizes-size-6);
  --inputNumber-size-sm-height: var(--sizes-base-16);
  --inputNumber-size-default-height: var(--sizes-base-16);
  --inputNumber-size-md-height: var(--sizes-base-16);
  --inputNumber-size-lg-height: var(--sizes-base-16);
  --Number-handler-bg: var(--inputNumber-base-default-step-bg);
  --Number-handler-fontSize: var(--inputNumber-base-default-icon-fontSize);
  --Number-handler-color: var(--inputNumber-base-default-icon-color);
  --Number-handler-onHover-bg: var(--inputNumber-base-hover-step-bg);
  --Number-handler-onHover-color: var(--inputNumber-base-hover-icon-color);
  --Number-handler-onActive-bg: var(--inputNumber-base-active-step-bg);
  // 加强版数字输入框
  --inputNumber-enhance-default-top-border-color: var(--colors-neutral-line-7);
  --inputNumber-enhance-default-top-border-width: var(--borders-width-2);
  --inputNumber-enhance-default-top-border-style: var(--borders-style-2);
  --inputNumber-enhance-default-right-border-color: var(
    --colors-neutral-line-7
  );
  --inputNumber-enhance-default-right-border-width: var(--borders-width-2);
  --inputNumber-enhance-default-right-border-style: var(--borders-style-2);
  --inputNumber-enhance-default-bottom-border-color: var(
    --colors-neutral-line-7
  );
  --inputNumber-enhance-default-bottom-border-width: var(--borders-width-2);
  --inputNumber-enhance-default-bottom-border-style: var(--borders-style-2);
  --inputNumber-enhance-default-left-border-color: var(--colors-neutral-line-7);
  --inputNumber-enhance-default-left-border-width: var(--borders-width-2);
  --inputNumber-enhance-default-left-border-style: var(--borders-style-2);
  --inputNumber-enhance-default-top-right-border-radius: var(
    --borders-radius-3
  );
  --inputNumber-enhance-default-top-left-border-radius: var(--borders-radius-3);
  --inputNumber-enhance-default-bottom-right-border-radius: var(
    --borders-radius-3
  );
  --inputNumber-enhance-default-bottom-left-border-radius: var(
    --borders-radius-3
  );
  --inputNumber-enhance-default-paddingTop: var(--sizes-size-3);
  --inputNumber-enhance-default-paddingBottom: var(--sizes-size-3);
  --inputNumber-enhance-default-paddingLeft: var(--sizes-size-7);
  --inputNumber-enhance-default-paddingRight: var(--sizes-size-7);
  --inputNumber-enhance-default-bg-color: var(--colors-neutral-fill-11);
  --inputNumber-enhance-hover-top-border-color: var(--colors-brand-5);
  --inputNumber-enhance-hover-top-border-width: var(--borders-width-2);
  --inputNumber-enhance-hover-top-border-style: var(--borders-style-2);
  --inputNumber-enhance-hover-right-border-color: var(--colors-brand-5);
  --inputNumber-enhance-hover-right-border-width: var(--borders-width-2);
  --inputNumber-enhance-hover-right-border-style: var(--borders-style-2);
  --inputNumber-enhance-hover-bottom-border-color: var(--colors-brand-5);
  --inputNumber-enhance-hover-bottom-border-width: var(--borders-width-2);
  --inputNumber-enhance-hover-bottom-border-style: var(--borders-style-2);
  --inputNumber-enhance-hover-left-border-color: var(--colors-brand-5);
  --inputNumber-enhance-hover-left-border-width: var(--borders-width-2);
  --inputNumber-enhance-hover-left-border-style: var(--borders-style-2);
  --inputNumber-enhance-hover-top-right-border-radius: var(--borders-radius-3);
  --inputNumber-enhance-hover-top-left-border-radius: var(--borders-radius-3);
  --inputNumber-enhance-hover-bottom-right-border-radius: var(
    --borders-radius-3
  );
  --inputNumber-enhance-hover-bottom-left-border-radius: var(
    --borders-radius-3
  );
  --inputNumber-enhance-hover-paddingTop: var(--sizes-size-3);
  --inputNumber-enhance-hover-paddingBottom: var(--sizes-size-3);
  --inputNumber-enhance-hover-paddingLeft: var(--sizes-size-7);
  --inputNumber-enhance-hover-paddingRight: var(--sizes-size-7);
  --inputNumber-enhance-hover-bg-color: var(--colors-neutral-fill-11);
  --inputNumber-enhance-active-top-border-color: var(--colors-brand-5);
  --inputNumber-enhance-active-top-border-width: var(--borders-width-2);
  --inputNumber-enhance-active-top-border-style: var(--borders-style-2);
  --inputNumber-enhance-active-right-border-color: var(--colors-brand-5);
  --inputNumber-enhance-active-right-border-width: var(--borders-width-2);
  --inputNumber-enhance-active-right-border-style: var(--borders-style-2);
  --inputNumber-enhance-active-bottom-border-color: var(--colors-brand-5);
  --inputNumber-enhance-active-bottom-border-width: var(--borders-width-2);
  --inputNumber-enhance-active-bottom-border-style: var(--borders-style-2);
  --inputNumber-enhance-active-left-border-color: var(--colors-brand-5);
  --inputNumber-enhance-active-left-border-width: var(--borders-width-2);
  --inputNumber-enhance-active-left-border-style: var(--borders-style-2);
  --inputNumber-enhance-active-top-right-border-radius: var(--borders-radius-3);
  --inputNumber-enhance-active-top-left-border-radius: var(--borders-radius-3);
  --inputNumber-enhance-active-bottom-right-border-radius: var(
    --borders-radius-3
  );
  --inputNumber-enhance-active-bottom-left-border-radius: var(
    --borders-radius-3
  );
  --inputNumber-enhance-active-paddingTop: var(--sizes-size-3);
  --inputNumber-enhance-active-paddingBottom: var(--sizes-size-3);
  --inputNumber-enhance-active-paddingLeft: var(--sizes-size-7);
  --inputNumber-enhance-active-paddingRight: var(--sizes-size-7);
  --inputNumber-enhance-active-shadow: var(--shadows-shadow-none);
  --inputNumber-enhance-active-bg-color: var(--colors-neutral-fill-11);
  --inputNumber-enhance-disabled-top-border-color: var(--colors-neutral-line-8);
  --inputNumber-enhance-disabled-top-border-width: var(--borders-width-2);
  --inputNumber-enhance-disabled-top-border-style: var(--borders-style-2);
  --inputNumber-enhance-disabled-right-border-color: var(
    --colors-neutral-line-8
  );
  --inputNumber-enhance-disabled-right-border-width: var(--borders-width-2);
  --inputNumber-enhance-disabled-right-border-style: var(--borders-style-2);
  --inputNumber-enhance-disabled-bottom-border-color: var(
    --colors-neutral-line-8
  );
  --inputNumber-enhance-disabled-bottom-border-width: var(--borders-width-2);
  --inputNumber-enhance-disabled-bottom-border-style: var(--borders-style-2);
  --inputNumber-enhance-disabled-left-border-color: var(
    --colors-neutral-line-8
  );
  --inputNumber-enhance-disabled-left-border-width: var(--borders-width-2);
  --inputNumber-enhance-disabled-left-border-style: var(--borders-style-2);
  --inputNumber-enhance-disabled-top-right-border-radius: var(
    --borders-radius-3
  );
  --inputNumber-enhance-disabled-top-left-border-radius: var(
    --borders-radius-3
  );
  --inputNumber-enhance-disabled-bottom-right-border-radius: var(
    --borders-radius-3
  );
  --inputNumber-enhance-disabled-bottom-left-border-radius: var(
    --borders-radius-3
  );
  --inputNumber-enhance-disabled-paddingTop: var(--sizes-size-3);
  --inputNumber-enhance-disabled-paddingBottom: var(--sizes-size-3);
  --inputNumber-enhance-disabled-paddingLeft: var(--sizes-size-7);
  --inputNumber-enhance-disabled-paddingRight: var(--sizes-size-7);
  --inputNumber-enhance-disabled-bg-color: var(--colors-neutral-fill-10);
  --inputNumber-enhance-default-icon-fontSize: var(--fonts-size-8);
  --inputNumber-enhance-leftIcon-default-height: var(--sizes-size-7);
  --inputNumber-enhance-leftIcon-default-width: var(--sizes-size-7);
  --inputNumber-enhance-leftIcon-default-color: var(--colors-neutral-text-2);
  --inputNumber-enhance-leftIcon-hover-color: var(--colors-brand-5);
  --inputNumber-enhance-leftIcon-active-color: var(--colors-brand-5);
  --inputNumber-enhance-leftIcon-default-bg-color: var(
    --colors-neutral-fill-11
  );
  --inputNumber-enhance-leftIcon-hover-bg-color: var(--colors-neutral-fill-11);
  --inputNumber-enhance-leftIcon-active-bg-color: var(--colors-neutral-fill-11);
  --inputNumber-enhance-rightIcon-default-height: var(--sizes-size-7);
  --inputNumber-enhance-rightIcon-default-width: var(--sizes-size-7);
  --inputNumber-enhance-rightIcon-default-color: var(--colors-neutral-text-2);
  --inputNumber-enhance-rightIcon-hover-color: var(--colors-brand-5);
  --inputNumber-enhance-rightIcon-active-color: var(--colors-brand-5);
  --inputNumber-enhance-rightIcon-default-bg-color: var(
    --colors-neutral-fill-11
  );
  --inputNumber-enhance-rightIcon-hover-bg-color: var(--colors-neutral-fill-11);
  --inputNumber-enhance-rightIcon-active-bg-color: var(
    --colors-neutral-fill-11
  );
  --inputNumber-enhance-leftIcon-default-icon: '<svg viewBox="0 0 12 2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="icon icon-minus"><g id="minus" fill="currentColor" fill-rule="nonzero"><polygon id="path-1" points="0 1.6 0 0.4 12 0.4 12 1.6"></polygon></g></svg>';
  --inputNumber-enhance-rightIcon-default-icon: '<svg viewBox="0 0 12 12" xmlns="http://www.w3.org/2000/svg" class="icon  icon-plus"><path d="M6.6 6.6V12H5.4V6.6H0V5.4h5.4V0h1.2v5.4H12v1.2z" fill="currentColor" fill-rule="nonzero"></path></svg>';
  --Form-input-onHover-borderColor: var(--colors-brand-5);
  --Form-input-onFocused-borderColor: var(--colors-brand-5);

  // 勾选框
  --checkbox-checkbox-default-height: var(--sizes-size-9);
  --checkbox-checkbox-default-bg-color: var(--colors-neutral-fill-11);
  --checkbox-checkbox-default-color: var(--colors-neutral-text-2);
  --checkbox-checkbox-default-fontSize: var(--sizes-size-8);
  --checkbox-checkbox-default-top-border-color: var(--colors-neutral-line-8);
  --checkbox-checkbox-default-top-border-width: var(--borders-width-2);
  --checkbox-checkbox-default-top-border-style: var(--borders-style-2);
  --checkbox-checkbox-default-right-border-color: var(--colors-neutral-line-8);
  --checkbox-checkbox-default-right-border-width: var(--borders-width-2);
  --checkbox-checkbox-default-right-border-style: var(--borders-style-2);
  --checkbox-checkbox-default-bottom-border-color: var(--colors-neutral-line-8);
  --checkbox-checkbox-default-bottom-border-width: var(--borders-width-2);
  --checkbox-checkbox-default-bottom-border-style: var(--borders-style-2);
  --checkbox-checkbox-default-left-border-color: var(--colors-neutral-line-8);
  --checkbox-checkbox-default-left-border-width: var(--borders-width-2);
  --checkbox-checkbox-default-left-border-style: var(--borders-style-2);
  --checkbox-checkbox-default-top-right-border-radius: var(--sizes-size-2);
  --checkbox-checkbox-default-top-left-border-radius: var(--sizes-size-2);
  --checkbox-checkbox-default-bottom-right-border-radius: var(--sizes-size-2);
  --checkbox-checkbox-default-bottom-left-border-radius: var(--sizes-size-2);
  --checkbox-checkbox-hover-height: var(--sizes-size-9);
  --checkbox-checkbox-hover-bg-color: var(--colors-neutral-fill-11);
  --checkbox-checkbox-hover-color: var(--colors-neutral-text-2);
  --checkbox-checkbox-hover-fontSize: var(--sizes-size-8);
  --checkbox-checkbox-hover-top-border-color: var(--colors-brand-5);
  --checkbox-checkbox-hover-top-border-width: var(--borders-width-2);
  --checkbox-checkbox-hover-top-border-style: var(--borders-style-2);
  --checkbox-checkbox-hover-right-border-color: var(--colors-brand-5);
  --checkbox-checkbox-hover-right-border-width: var(--borders-width-2);
  --checkbox-checkbox-hover-right-border-style: var(--borders-style-2);
  --checkbox-checkbox-hover-bottom-border-color: var(--colors-brand-5);
  --checkbox-checkbox-hover-bottom-border-width: var(--borders-width-2);
  --checkbox-checkbox-hover-bottom-border-style: var(--borders-style-2);
  --checkbox-checkbox-hover-left-border-color: var(--colors-brand-5);
  --checkbox-checkbox-hover-left-border-width: var(--borders-width-2);
  --checkbox-checkbox-hover-left-border-style: var(--borders-style-2);
  --checkbox-checkbox-hover-top-right-border-radius: var(--sizes-size-2);
  --checkbox-checkbox-hover-top-left-border-radius: var(--sizes-size-2);
  --checkbox-checkbox-hover-bottom-right-border-radius: var(--sizes-size-2);
  --checkbox-checkbox-hover-bottom-left-border-radius: var(--sizes-size-2);
  --checkbox-checkbox-active-height: var(--sizes-size-9);
  --checkbox-checkbox-active-bg-color: var(--colors-neutral-fill-11);
  --checkbox-checkbox-active-color: var(--colors-neutral-text-2);
  --checkbox-checkbox-active-fontSize: var(--sizes-size-8);
  --checkbox-checkbox-active-top-border-color: var(--colors-brand-5);
  --checkbox-checkbox-active-top-border-width: var(--borders-width-2);
  --checkbox-checkbox-active-top-border-style: var(--borders-style-2);
  --checkbox-checkbox-active-right-border-color: var(--colors-brand-5);
  --checkbox-checkbox-active-right-border-width: var(--borders-width-2);
  --checkbox-checkbox-active-right-border-style: var(--borders-style-2);
  --checkbox-checkbox-active-bottom-border-color: var(--colors-brand-5);
  --checkbox-checkbox-active-bottom-border-width: var(--borders-width-2);
  --checkbox-checkbox-active-bottom-border-style: var(--borders-style-2);
  --checkbox-checkbox-active-left-border-color: var(--colors-brand-5);
  --checkbox-checkbox-active-left-border-width: var(--borders-width-2);
  --checkbox-checkbox-active-left-border-style: var(--borders-style-2);
  --checkbox-checkbox-active-top-right-border-radius: var(--sizes-size-2);
  --checkbox-checkbox-active-top-left-border-radius: var(--sizes-size-2);
  --checkbox-checkbox-active-bottom-right-border-radius: var(--sizes-size-2);
  --checkbox-checkbox-active-bottom-left-border-radius: var(--sizes-size-2);
  --checkbox-checkbox-disabled-height: var(--sizes-size-9);
  --checkbox-checkbox-disabled-bg-color: var(--colors-neutral-fill-10);
  --checkbox-checkbox-disabled-color: var(--colors-neutral-text-2);
  --checkbox-checkbox-disabled-fontSize: var(--sizes-size-8);
  --checkbox-checkbox-disabled-top-border-color: var(--colors-neutral-line-8);
  --checkbox-checkbox-disabled-top-border-width: var(--borders-width-2);
  --checkbox-checkbox-disabled-top-border-style: var(--borders-style-2);
  --checkbox-checkbox-disabled-right-border-color: var(--colors-neutral-line-8);
  --checkbox-checkbox-disabled-right-border-width: var(--borders-width-2);
  --checkbox-checkbox-disabled-right-border-style: var(--borders-style-2);
  --checkbox-checkbox-disabled-bottom-border-color: var(
    --colors-neutral-line-8
  );
  --checkbox-checkbox-disabled-bottom-border-width: var(--borders-width-2);
  --checkbox-checkbox-disabled-bottom-border-style: var(--borders-style-2);
  --checkbox-checkbox-disabled-left-border-color: var(--colors-neutral-line-8);
  --checkbox-checkbox-disabled-left-border-width: var(--borders-width-2);
  --checkbox-checkbox-disabled-left-border-style: var(--borders-style-2);
  --checkbox-checkbox-disabled-top-right-border-radius: var(--sizes-size-2);
  --checkbox-checkbox-disabled-top-left-border-radius: var(--sizes-size-2);
  --checkbox-checkbox-disabled-bottom-right-border-radius: var(--sizes-size-2);
  --checkbox-checkbox-disabled-bottom-left-border-radius: var(--sizes-size-2);
  --checkbox-checked-default-height: var(--sizes-size-9);
  --checkbox-checked-default-bg-color: var(--colors-brand-5);
  --checkbox-checked-default-color: var(--colors-neutral-text-2);
  --checkbox-checked-default-fontSize: var(--sizes-size-8);
  --checkbox-checked-default-top-border-color: var(--colors-neutral-line-8);
  --checkbox-checked-default-top-border-width: var(--borders-width-2);
  --checkbox-checked-default-top-border-style: var(--borders-style-2);
  --checkbox-checked-default-right-border-color: var(--colors-neutral-line-8);
  --checkbox-checked-default-right-border-width: var(--borders-width-2);
  --checkbox-checked-default-right-border-style: var(--borders-style-2);
  --checkbox-checked-default-bottom-border-color: var(--colors-neutral-line-8);
  --checkbox-checked-default-bottom-border-width: var(--borders-width-2);
  --checkbox-checked-default-bottom-border-style: var(--borders-style-2);
  --checkbox-checked-default-left-border-color: var(--colors-neutral-line-8);
  --checkbox-checked-default-left-border-width: var(--borders-width-2);
  --checkbox-checked-default-left-border-style: var(--borders-style-2);
  --checkbox-checked-default-top-right-border-radius: var(--sizes-size-2);
  --checkbox-checked-default-top-left-border-radius: var(--sizes-size-2);
  --checkbox-checked-default-bottom-right-border-radius: var(--sizes-size-2);
  --checkbox-checked-default-bottom-left-border-radius: var(--sizes-size-2);
  --checkbox-checked-hover-height: var(--sizes-size-9);
  --checkbox-checked-hover-bg-color: var(--colors-brand-6);
  --checkbox-checked-hover-color: var(--colors-neutral-text-2);
  --checkbox-checked-hover-fontSize: var(--sizes-size-8);
  --checkbox-checked-hover-top-border-color: var(--colors-brand-5);
  --checkbox-checked-hover-top-border-width: var(--borders-width-2);
  --checkbox-checked-hover-top-border-style: var(--borders-style-2);
  --checkbox-checked-hover-right-border-color: var(--colors-brand-5);
  --checkbox-checked-hover-right-border-width: var(--borders-width-2);
  --checkbox-checked-hover-right-border-style: var(--borders-style-2);
  --checkbox-checked-hover-bottom-border-color: var(--colors-brand-5);
  --checkbox-checked-hover-bottom-border-width: var(--borders-width-2);
  --checkbox-checked-hover-bottom-border-style: var(--borders-style-2);
  --checkbox-checked-hover-left-border-color: var(--colors-brand-5);
  --checkbox-checked-hover-left-border-width: var(--borders-width-2);
  --checkbox-checked-hover-left-border-style: var(--borders-style-2);
  --checkbox-checked-hover-top-right-border-radius: var(--sizes-size-2);
  --checkbox-checked-hover-top-left-border-radius: var(--sizes-size-2);
  --checkbox-checked-hover-bottom-right-border-radius: var(--sizes-size-2);
  --checkbox-checked-hover-bottom-left-border-radius: var(--sizes-size-2);
  --checkbox-checked-active-height: var(--sizes-size-9);
  --checkbox-checked-active-bg-color: var(--colors-brand-4);
  --checkbox-checked-active-color: var(--colors-neutral-text-2);
  --checkbox-checked-active-fontSize: var(--sizes-size-8);
  --checkbox-checked-active-top-border-color: var(--colors-brand-5);
  --checkbox-checked-active-top-border-width: var(--borders-width-2);
  --checkbox-checked-active-top-border-style: var(--borders-style-2);
  --checkbox-checked-active-right-border-color: var(--colors-brand-5);
  --checkbox-checked-active-right-border-width: var(--borders-width-2);
  --checkbox-checked-active-right-border-style: var(--borders-style-2);
  --checkbox-checked-active-bottom-border-color: var(--colors-brand-5);
  --checkbox-checked-active-bottom-border-width: var(--borders-width-2);
  --checkbox-checked-active-bottom-border-style: var(--borders-style-2);
  --checkbox-checked-active-left-border-color: var(--colors-brand-5);
  --checkbox-checked-active-left-border-width: var(--borders-width-2);
  --checkbox-checked-active-left-border-style: var(--borders-style-2);
  --checkbox-checked-active-top-right-border-radius: var(--sizes-size-2);
  --checkbox-checked-active-top-left-border-radius: var(--sizes-size-2);
  --checkbox-checked-active-bottom-right-border-radius: var(--sizes-size-2);
  --checkbox-checked-active-bottom-left-border-radius: var(--sizes-size-2);
  --checkbox-checked-disabled-height: var(--sizes-size-9);
  --checkbox-checked-disabled-bg-color: var(--colors-neutral-fill-11);
  --checkbox-checked-disabled-color: var(--colors-neutral-text-2);
  --checkbox-checked-disabled-fontSize: var(--sizes-size-8);
  --checkbox-checked-disabled-top-border-color: var(--colors-neutral-line-8);
  --checkbox-checked-disabled-top-border-width: var(--borders-width-2);
  --checkbox-checked-disabled-top-border-style: var(--borders-style-2);
  --checkbox-checked-disabled-right-border-color: var(--colors-neutral-line-8);
  --checkbox-checked-disabled-right-border-width: var(--borders-width-2);
  --checkbox-checked-disabled-right-border-style: var(--borders-style-2);
  --checkbox-checked-disabled-bottom-border-color: var(--colors-neutral-line-8);
  --checkbox-checked-disabled-bottom-border-width: var(--borders-width-2);
  --checkbox-checked-disabled-bottom-border-style: var(--borders-style-2);
  --checkbox-checked-disabled-left-border-color: var(--colors-neutral-line-8);
  --checkbox-checked-disabled-left-border-width: var(--borders-width-2);
  --checkbox-checked-disabled-left-border-style: var(--borders-style-2);
  --checkbox-checked-disabled-top-right-border-radius: var(--sizes-size-2);
  --checkbox-checked-disabled-top-left-border-radius: var(--sizes-size-2);
  --checkbox-checked-disabled-bottom-right-border-radius: var(--sizes-size-2);
  --checkbox-checked-disabled-bottom-left-border-radius: var(--sizes-size-2);
  --Checkbox-onHover-color: var(--checkbox-checked-default-bg-color);
  --button-primary-hover-bg-color: var(--checkbox-checked-hover-bg-color);
  --Checkbox-checked-onHover-bgColor: var(--checkbox-checked-active-bg-color);
  --Checkbox-onDisabled-bg: var(--colors-neutral-fill-10);
  --Checkbox-size: var(--checkbox-checkbox-default-height);
  --Checkbox--full-inner-size: var(--sizes-size-5);
  --Checkbox--sm--full-inner-size: var(--sizes-size-5);
  --Checkbox--sm-inner-size: var(--sizes-size-5);
  --Checkbox--sm-size: var(--sizes-size-8);
  --Checkbox-borderRadius: #{px2rem(2px)};
  --Checkbox-color: var(--borderColor);
  --Checkbox-gap: var(--gap-xs);
  --Checkbox-gb: #fff;
  --Checkbox-inner-size: var(--sizes-size-5);
  --Checkbox-onDisabled-color: var(--colors-neutral-text-6);
  --Checkbox-inner-onDisabled-bg: #d4d6d9;
  --Checkbox-inner-onDisabled-color: #ffffff;
  --Checkbox-disabled-unchecked-bg: #f7f7f9;
  --Checkbox-inner-disabled-checked-bg: #e8e9eb;
  --Checkbox-border-width: var(--Form-input-borderWidth);
  --Checkbox-paddingX: #{px2rem(12px)};
  --Checkbox-button-height: #{px2rem(32px)};
  --Checkbox-button-line-height: #{px2rem(28px)};
  --Checkbox-button-min-width: #{px2rem(80px)};

  // 列表
  --listSelect-base-default-top-border-color: var(--colors-neutral-line-8);
  --listSelect-base-default-top-border-width: var(--borders-width-2);
  --listSelect-base-default-top-border-style: var(--borders-style-2);
  --listSelect-base-default-right-border-color: var(--colors-neutral-line-8);
  --listSelect-base-default-right-border-width: var(--borders-width-2);
  --listSelect-base-default-right-border-style: var(--borders-style-2);
  --listSelect-base-default-bottom-border-color: var(--colors-neutral-line-8);
  --listSelect-base-default-bottom-border-width: var(--borders-width-2);
  --listSelect-base-default-bottom-border-style: var(--borders-style-2);
  --listSelect-base-default-left-border-color: var(--colors-neutral-line-8);
  --listSelect-base-default-left-border-width: var(--borders-width-2);
  --listSelect-base-default-left-border-style: var(--borders-style-2);
  --listSelect-base-default-top-right-border-radius: var(--borders-radius-3);
  --listSelect-base-default-top-left-border-radius: var(--borders-radius-3);
  --listSelect-base-default-bottom-right-border-radius: var(--borders-radius-3);
  --listSelect-base-default-bottom-left-border-radius: var(--borders-radius-3);
  --listSelect-base-default-paddingTop: var(--sizes-size-4);
  --listSelect-base-default-paddingBottom: var(--sizes-size-4);
  --listSelect-base-default-paddingLeft: var(--sizes-size-6);
  --listSelect-base-default-paddingRight: var(--sizes-size-6);
  --listSelect-base-default-color: var(--colors-neutral-text-2);
  --listSelect-base-default-bg-color: var(--colors-neutral-fill-11);
  --listSelect-base-hover-top-border-color: var(--colors-brand-5);
  --listSelect-base-hover-top-border-width: var(--borders-width-2);
  --listSelect-base-hover-top-border-style: var(--borders-style-2);
  --listSelect-base-hover-right-border-color: var(--colors-brand-5);
  --listSelect-base-hover-right-border-width: var(--borders-width-2);
  --listSelect-base-hover-right-border-style: var(--borders-style-2);
  --listSelect-base-hover-bottom-border-color: var(--colors-brand-5);
  --listSelect-base-hover-bottom-border-width: var(--borders-width-2);
  --listSelect-base-hover-bottom-border-style: var(--borders-style-2);
  --listSelect-base-hover-left-border-color: var(--colors-brand-5);
  --listSelect-base-hover-left-border-width: var(--borders-width-2);
  --listSelect-base-hover-left-border-style: var(--borders-style-2);
  --listSelect-base-hover-top-right-border-radius: var(--borders-radius-3);
  --listSelect-base-hover-top-left-border-radius: var(--borders-radius-3);
  --listSelect-base-hover-bottom-right-border-radius: var(--borders-radius-3);
  --listSelect-base-hover-bottom-left-border-radius: var(--borders-radius-3);
  --listSelect-base-hover-paddingTop: var(--sizes-size-3);
  --listSelect-base-hover-paddingBottom: var(--sizes-size-3);
  --listSelect-base-hover-paddingLeft: var(--sizes-size-7);
  --listSelect-base-hover-paddingRight: var(--sizes-size-7);
  --listSelect-base-hover-color: var(--colors-brand-5);
  --listSelect-base-hover-bg-color: var(--colors-neutral-fill-11);
  --listSelect-base-active-top-border-color: var(--colors-brand-5);
  --listSelect-base-active-top-border-width: var(--borders-width-2);
  --listSelect-base-active-top-border-style: var(--borders-style-2);
  --listSelect-base-active-right-border-color: var(--colors-brand-5);
  --listSelect-base-active-right-border-width: var(--borders-width-2);
  --listSelect-base-active-right-border-style: var(--borders-style-2);
  --listSelect-base-active-bottom-border-color: var(--colors-brand-5);
  --listSelect-base-active-bottom-border-width: var(--borders-width-2);
  --listSelect-base-active-bottom-border-style: var(--borders-style-2);
  --listSelect-base-active-left-border-color: var(--colors-brand-5);
  --listSelect-base-active-left-border-width: var(--borders-width-2);
  --listSelect-base-active-left-border-style: var(--borders-style-2);
  --listSelect-base-active-top-right-border-radius: var(--borders-radius-3);
  --listSelect-base-active-top-left-border-radius: var(--borders-radius-3);
  --listSelect-base-active-bottom-right-border-radius: var(--borders-radius-3);
  --listSelect-base-active-bottom-left-border-radius: var(--borders-radius-3);
  --listSelect-base-active-paddingTop: var(--sizes-size-3);
  --listSelect-base-active-paddingBottom: var(--sizes-size-3);
  --listSelect-base-active-paddingLeft: var(--sizes-size-7);
  --listSelect-base-active-paddingRight: var(--sizes-size-7);
  --listSelect-base-active-shadow: var(--Form-input-boxShadow);
  --listSelect-base-active-color: var(--colors-brand-5);
  --listSelect-base-active-bg-color: var(--colors-neutral-fill-11);
  --listSelect-base-disabled-top-border-color: var(--colors-neutral-line-8);
  --listSelect-base-disabled-top-border-width: var(--borders-width-2);
  --listSelect-base-disabled-top-border-style: var(--borders-style-2);
  --listSelect-base-disabled-right-border-color: var(--colors-neutral-line-8);
  --listSelect-base-disabled-right-border-width: var(--borders-width-2);
  --listSelect-base-disabled-right-border-style: var(--borders-style-2);
  --listSelect-base-disabled-bottom-border-color: var(--colors-neutral-line-8);
  --listSelect-base-disabled-bottom-border-width: var(--borders-width-2);
  --listSelect-base-disabled-bottom-border-style: var(--borders-style-2);
  --listSelect-base-disabled-left-border-color: var(--colors-neutral-line-8);
  --listSelect-base-disabled-left-border-width: var(--borders-width-2);
  --listSelect-base-disabled-left-border-style: var(--borders-style-2);
  --listSelect-base-disabled-top-right-border-radius: var(--borders-radius-3);
  --listSelect-base-disabled-top-left-border-radius: var(--borders-radius-3);
  --listSelect-base-disabled-bottom-right-border-radius: var(
    --borders-radius-3
  );
  --listSelect-base-disabled-bottom-left-border-radius: var(--borders-radius-3);
  --listSelect-base-disabled-paddingTop: var(--sizes-size-3);
  --listSelect-base-disabled-paddingBottom: var(--sizes-size-3);
  --listSelect-base-disabled-paddingLeft: var(--sizes-size-7);
  --listSelect-base-disabled-paddingRight: var(--sizes-size-7);
  --listSelect-base-disabled-color: var(--colors-neutral-text-6);
  --listSelect-base-disabled-bg-color: var(--colors-neutral-fill-10);
  --listSelect-base-image-width: var(--sizes-size-1);
  --ListControl-item-onHover-color: var(--listSelect-base-hover-color);
  --ListControl-item-onHover-borderColor: var(
    --listSelect-base-hover-top-border-color
  );
  --ListControl-item-onActive-color: var(--listSelect-base-active-color);
  --ListControl-item-onActive-onHover-bg: var(--colors-neutral-fill-11);
  --ListControl-item-onActive-color: var(
    --listSelect-base-active-top-border-color
  );
  --ListControl-item-onActive-before-bg: var(--colors-brand-4);
  --ListControl-item-color: var(--listSelect-base-default-color);
  --ListControl-item-onDisabled-color: var(--listSelect-base-disabled-color);
  --ListControl-item-paddingX: var(--listSelect-base-default-paddingLeft);
  --ListControl-item-paddingY: var(--listSelect-base-default-paddingTop);

  // 链接

  --link-onClick-color: var(--colors-link-4);
  --link-onClick-fontSize: var(--fonts-size-7);
  --link-onClick-font-style: none;
  --link-onClick-fontWeight: var(--fonts-weight-6);
  --link-onClick-text-decoration: none;
  --link-onClick-bg-color: transparent;
  --link-onHover-color: var(--colors-link-6);
  --link-onHover-fontSize: var(--fonts-size-7);
  --link-onHover-font-style: none;
  --link-onHover-fontWeight: var(--fonts-weight-6);
  --link-onHover-text-decoration: none;
  --link-onHover-bg-color: transparent;
  --link-color: var(--colors-link-5);
  --link-fontSize: var(--fonts-size-7);
  --link-font-style: none;
  --link-fontWeight: var(--fonts-weight-6);
  --link-text-decoration: none;
  --link-bg-color: transparent;
  --link-disabled-color: var(--colors-neutral-text-6);
  --link-disabled-fontSize: var(--fonts-size-7);
  --link-disabled-font-style: none;
  --link-disabled-fontWeight: var(--fonts-weight-6);
  --link-disabled-text-decoration: none;
  --link-disabled-bg-color: transparent;
  --link-icon-size: var(--sizes-size-8);
  --link-icon-margin: var(--sizes-size-3);
  --link-decoration: var(--link-text-decoration); // 原变量
  --link-onHover-decoration: var(--link-onClick-text-decoration);

  // 表单
  --Form-item-gap: var(--sizes-base-12);
  --Form-item-mobile-gap: var(--sizes-base-4);
  // --Form-item-color: var(--colors-neutral-text-5);
  --Form-item-color: var(--colors-neutral-text-14);
  --Form-item-fontColor: var(--Form-item-color);
  --Form-item-fontSize: var(--fonts-size-7);
  --Form-item-fontWeight: var(--fonts-weight-6);
  --Form-item-lineHeight: var(--fonts-lineHeight-2);
  --Form-item-star-color: var(--colors-error-5);
  --Form-item-star-size: var(--sizes-size-7);
  --Form-description-color: var(--colors-neutral-text-4);
  --Form-description-fontSize: var(--fonts-size-7);
  --Form-description-fontWeight: var(--fonts-weight-6);
  --Form-description-lineHeight: var(--fonts-lineHeight-2);
  --Form-description-gap: var(--sizes-size-3);
  --Form-item-onError-color: var(--colors-error-5);
  --Form-item-onError-borderColor: var(--colors-error-5);
  --Form-item-onError-bg: var(--colors-neutral-fill-11);
  --Form-feedBack-color: var(--colors-error-5);
  --Form-feedBack-fontSize: var(--fonts-size-8);
  --Form-feedBack-fontWeight: var(--fonts-weight-6);
  --Form-feedBack-lineHeight: var(--fonts-lineHeight-2);
  --Form-feedBack-gap: var(--sizes-size-3);
  --Form-mode-default-labelGap: var(--sizes-size-5);
  --Form-mode-default-width: 100%;
  --Form--horizontal-label-gap: var(--sizes-base-8);
  --Form--horizontal-label-widthBase: var(--sizes-base-49);
  --Form--horizontal-label-widthXs: var(--sizes-base-25);
  --Form--horizontal-label-widthSm: var(--sizes-base-35);
  --Form--horizontal-label-widthMd: 8.5rem;
  --Form--horizontal-label-widthLg: 12.5rem;
  --Form--horizontal-value-marginTop: var(--sizes-size-0);
  --Form--horizontal-value-marginBottom: var(--sizes-size-0);
  --Form--horizontal-value-marginLeft: var(--sizes-size-5);
  --Form--horizontal-value-marginRight: var(--sizes-size-0);
  --Form--horizontal-value-maxWidth: 100%;
  --Form--horizontal-value-minWidth: var(--sizes-size-0);
  --Form--horizontal-value-minWidth: var(--sizes-size-0);
  --Form-mode-inline-item-gap: var(--sizes-base-8);
  --Form-mode-inline-label-gap: var(--sizes-base-8);

  // 滑块
  --InputRange-track-bg: var(--colors-neutral-text-13);
  --InputRange-track-height: var(--sizes-size-4);
  --InputRange-track-border-radius: var(--sizes-size-3);
  --InputRange-track-onActive-bg: var(--colors-brand-5);
  --InputRange-handle-height: var(--sizes-size-9);
  --InputRange-handle-width: var(--sizes-size-9);
  --InputRange-handle-bg: var(--colors-neutral-fill-11);
  --InputRange-handle-top-border-color: var(--colors-brand-5);
  --InputRange-handle-top-border-width: 0.0625rem;
  --InputRange-handle-top-border-style: var(--borders-style-2);
  --InputRange-handle-top-right-border-radius: var(--borders-radius-7);
  --InputRange-handle-border: var(--InputRange-handle-top-border-width)
    var(--InputRange-handle-top-border-style)
    var(--InputRange-handle-top-border-color);
  --InputRange-handle-border-radius: var(
    --InputRange-handle-top-right-border-radius
  );
  --InputRange-handle-onActive-transform: scale(1.3);
  --InputRange-handle-onDrage-border-width: #{px2rem(2px)};
  --InputRange-handle-onFocus-borderRadius: var(
    --InputRange-handle-border-radius
  );
  --InputRange-padding: #{px2rem(20px)};
  --InputRange-handle-icon-width: var(--sizes-size-5);
  --InputRange-handle-icon-height: var(--sizes-size-5);
  --InputRange-handle-icon-color: var(--colors-brand-9);
  --InputRange-track-onActive-onDisabled-bg: var(--colors-neutral-fill-6);
  --InputRange-handle-onDisabled-border-color: var(--colors-neutral-fill-7);
  --InputRange-handle-onDisabled-bg: var(--colors-neutral-fill-11);
  --InputRange-handle-icon-onDisabled-color: var(--colors-neutral-fill-7);
  --InputRange-track-transition: left var(--animation-duration) ease-out,
    width var(--animation-duration) ease-out;
  --InputRange-handle-transition: transform var(--animation-duration) ease-out;
  --InputRange-track-dot-height: var(--sizes-size-4);
  --InputRange-track-dot-width: var(--sizes-size-4);
  --InputRange-track-dot-bg: var(--colors-neutral-fill-11);
  --InputRange-marks-color: var(--colors-neutral-text-2);
  --InputRange-marks-fontSize: var(--fonts-size-7);
  --InputRange-marks-fontWeight: var(--fonts-weight-6);
  --InputRange-marks-lineHeight: var(--fonts-lineHeight-2);
  --InputRange-marks-marginTop: var(--sizes-size-0);
  --InputRange-label-color: var(--colors-neutral-fill-11);
  --InputRange-label-fontSize: var(--fonts-size-7);
  --InputRange-label-font-size: var(--InputRange-label-fontSize);
  --InputRange-label-fontWeight: var(--fonts-weight-6);
  --InputRange-label-lineHeight: var(--fonts-lineHeight-2);
  --InputRange-label-paddingTop: var(--sizes-size-5);
  --InputRange-label-paddingBottom: var(--sizes-size-5);
  --InputRange-label-paddingLeft: var(--sizes-size-5);
  --InputRange-label-paddingRight: var(--sizes-size-5);
  --InputRange-label-bg: var(--colors-neutral-fill-1);
  --InputRange-label-top-right-border-radius: var(--borders-radius-3);
  --InputRange-label-padding: var(--InputRange-label-paddingTop)
    var(--InputRange-label-paddingRight) var(--InputRange-label-paddingBottom)
    var(--InputRange-label-paddingLeft);
  --InputRange-label-border-radius: var(
    --InputRange-label-top-right-border-radius
  );
  --InputRange-label-position-bottom: calc(100% + 8px);
  --InputRange-input-width: var(--sizes-base-40);
  --InputRange-input-marginTop: var(--sizes-size-0);
  --InputRange-input-marginBottom: var(--sizes-size-0);
  --InputRange-input-marginLeft: var(--sizes-size-5);
  --InputRange-input-marginRight: var(--sizes-size-5);
  --InputRange-clearIcon-height: var(--sizes-size-7);
  --InputRange-clearIcon-width: var(--sizes-size-7);
  --InputRange-clearIcon-color: var(--colors-neutral-text-4);
  --InputRange-clearIcon-hoverColor: var(--colors-brand-5);

  // 评分
  --Rating-star-margin: var(--sizes-size-5);
  --Rating-star-size: var(--sizes-base-12);
  --Rating-star-icon: '<svg class="icon" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><path fill="currentColor" d="M4.99672493,4.37617062 L1.28100904,4.93702533 C0.463702827,5.06039054 -0.102812689,5.85030962 0.0156612291,6.70135899 C0.0628630935,7.04043042 0.216147743,7.35382434 0.451761131,7.59297508 L3.14101949,10.3226082 L3.14101949,10.3226082 L2.50531937,14.1787855 C2.3655991,15.0263332 2.91216489,15.8313483 3.72610824,15.9768371 C4.05030943,16.0347866 4.38381497,15.9798039 4.67496871,15.8204054 L7.99934906,14.0003997 L7.99934906,14.0003997 L11.3254267,15.8208814 C12.0564401,16.2209912 12.9605363,15.9282748 13.3447823,15.167081 C13.4978067,14.8639388 13.5505833,14.5167196 13.4949403,14.1791872 L12.859174,10.3226082 L12.859174,10.3226082 L15.5482634,7.59400813 C16.1397301,6.99385103 16.1519752,6.00805341 15.5756136,5.39216751 C15.3460036,5.14681192 15.0450939,4.98715562 14.7195072,4.93793566 L11.0034685,4.37617062 L11.0034685,4.37617062 L9.34151455,0.868493275 C8.97611971,0.0973002947 8.07952072,-0.219434766 7.33890469,0.161045701 C7.04395753,0.312570398 6.80521255,0.561123051 6.65963735,0.868217393 L4.99672493,4.37617062 L4.99672493,4.37617062 Z"></path></svg>';
  --Rating-colors: '[{"value":"#abadb1","id":2},{"value":"#787b81","id":3},{"value":"#ffa900","id":5}]';
  --Rating-text-color: var(--colors-neutral-text-2);
  --Rating-text-fontSize: var(--fonts-size-7);
  --Rating-text-fontWeight: var(--fonts-weight-6);
  --Rating-text-lineHeight: var(--fonts-lineHeight-2);
  --Rating-inactive-color: var(--colors-neutral-text-9);
  --radio-default-default-color: var(--colors-neutral-line-8);
  --radio-default-default-text-color: var(--colors-neutral-text-1);
  --radio-default-default-bg-color: var(--colors-neutral-fill-11);
  --radio-default-default-fontSize: var(--fonts-size-7);
  --radio-default-default-fontWeight: var(--fonts-weight-6);
  --radio-default-default-lineHeight: var(--fonts-lineHeight-2);
  --radio-default-default-point-size: var(--sizes-size-9);
  --radio-default-default-point-inner-size: var(--sizes-size-5);
  --radio-default-default-marginTop: var(--sizes-size-0);
  --radio-default-default-marginBottom: var(--sizes-size-4);
  --radio-default-default-marginLeft: var(--sizes-size-0);
  --radio-default-default-marginRight: var(--sizes-size-9);
  --radio-default-default-distance: var(--sizes-size-5);
  --radio-default-hover-color: var(--colors-brand-5);
  --radio-default-hover-text-color: var(--colors-neutral-text-1);
  --radio-default-hover-bg-color: var(--colors-neutral-fill-11);
  --radio-default-active-color: var(--colors-brand-5);
  --radio-default-active-text-color: var(--colors-neutral-text-1);
  --radio-default-active-bg-color: var(--colors-neutral-fill-11);
  --radio-default-disabled-color: var(--colors-neutral-line-8);
  --radio-default-disabled-text-color: var(--colors-neutral-text-6);
  --radio-default-disabled-bg-color: var(--colors-neutral-fill-8);
  --radio-default-vertical-marginTop: var(--sizes-size-0);
  --radio-default-vertical-marginBottom: var(--sizes-size-4);
  --radio-default-vertical-marginLeft: var(--sizes-size-0);
  --radio-default-vertical-marginRight: var(--sizes-size-5);
  --Radio--sm-size: var(--Checkbox--sm-size);
  --Radio-color: var(--radio-default-default-color);
  --Radio-inner-size: var(--radio-default-default-point-inner-size);
  --Radio-onHover-color: var(--radio-default-active-color);
  --Radio-size: var(--radio-default-default-point-size);
  --Radio-onDisabled-bg: var(--radio-default-disabled-bg-color);
  --Radio-onDisabled-color: var(--radio-default-disabled-color);
  --Radio-onFocus-boxShadow: none;

  --switch-default-off-bg-color: var(--colors-neutral-fill-7);
  --switch-default-off-hover-bg-color: var(--colors-neutral-fill-6);
  --switch-default-off-slider-color: var(--colors-neutral-fill-11);
  --switch-default-on-bg-color: var(--colors-brand-5);
  --switch-default-on-hover-bg-color: var(--colors-brand-4);
  --switch-default-on-slider-color: var(--colors-neutral-fill-11);
  --switch-option-fontSize: var(--fonts-size-7);
  --switch-option-fontWeight: var(--fonts-weight-6);
  --switch-option-lineHeight: var(--fonts-lineHeight-2);
  --switch-option-color: var(--colors-neutral-text-1);
  --switch-option-marginTop: var(--sizes-size-0);
  --switch-option-marginBottom: var(--sizes-size-0);
  --switch-option-marginLeft: var(--sizes-size-5);
  --switch-option-marginRight: var(--sizes-size-0);
  --switch-text-off-fontSize: var(--fonts-size-8);
  --switch-text-off-fontWeight: var(--fonts-weight-3);
  --switch-text-off-color: var(--colors-neutral-text-11);
  --switch-text-off-marginTop: var(--sizes-size-0);
  --switch-text-off-marginBottom: var(--sizes-size-0);
  --switch-text-off-marginLeft: var(--sizes-base-12);
  --switch-text-off-marginRight: var(--sizes-size-5);
  --switch-text-on-fontSize: var(--fonts-size-8);
  --switch-text-on-fontWeight: var(--fonts-weight-3);
  --switch-text-on-color: var(--colors-neutral-text-11);
  --switch-text-on-marginTop: var(--sizes-size-0);
  --switch-text-on-marginBottom: var(--sizes-size-0);
  --switch-text-on-marginLeft: var(--sizes-base-4);
  --switch-text-on-marginRight: var(--sizes-base-12);
  --switch-size-default-height: var(--sizes-base-10);
  --switch-size-default-minWidth: var(--sizes-base-22);
  --switch-size-default-slider-width: var(--sizes-size-9);
  --switch-size-default-slider-margin: var(--sizes-size-2);
  --switch-size-default-top-right-border-radius: var(--sizes-base-15);
  --switch-size-default-top-left-border-radius: var(--sizes-base-15);
  --switch-size-default-bottom-right-border-radius: var(--sizes-base-15);
  --switch-size-default-bottom-left-border-radius: var(--sizes-base-15);
  --switch-size-sm-height: var(--sizes-size-9);
  --switch-size-sm-minWidth: var(--sizes-base-14);
  --switch-size-sm-slider-width: var(--sizes-size-7);
  --switch-size-sm-slider-margin: var(--sizes-size-2);
  --switch-size-sm-top-right-border-radius: var(--sizes-base-15);
  --switch-size-sm-top-left-border-radius: var(--sizes-base-15);
  --switch-size-sm-bottom-right-border-radius: var(--sizes-base-15);
  --switch-size-sm-bottom-left-border-radius: var(--sizes-base-15);
  --Switch-bgColor: var(--switch-default-off-bg-color);
  --Switch-borderColor: var(--colors-neutral-line-6);
  --Switch-gap: var(--switch-option-marginLeft);
  --Switch-height: var(--switch-size-default-height);
  --Switch-onActive-bgColor: var(--switch-default-on-bg-color);
  --Switch-onDisabled-bgColor: var(--colors-brand-9);
  --Switch-onDisabled-circle-BackgroundColor: var(--colors-neutral-fill-11);
  --Switch-onDisabled-color: var(--colors-neutral-text-11);
  --Switch-onHover-bgColor: var(--switch-default-off-hover-bg-color);
  --Switch-valueColor: var(--switch-text-off-color);
  --Switch-width: var(--switch-size-default-minWidth);
  --Switch-slider-margin: var(--switch-size-default-slider-margin);
  --Switch-slider-width: var(--switch-size-default-slider-width);
  --Switch-slider-transition: all 0.5s ease;
  --Switch-text-marginRight: var(--switch-text-off-marginRight);
  --Switch-text-marginLeft: var(--switch-text-off-marginLeft);
  --Switch-width--sm: var(--switch-size-sm-minWidth);
  --Switch-height--sm: var(--switch-size-sm-height);
  --Switch-slider-width--sm: var(--switch-size-sm-slider-width);
  --Switch-text-marginRight--sm: var(--switch-text-off-marginRight);
  --Switch-text-marginLeft--sm: var(--switch-text-off-marginLeft);
  --Switch-checked-bgColor: var(--switch-default-on-bg-color);
  --Switch-checked-onHover-bgColor: var(--switch-default-on-hover-bg-color);
  --Switch-checked-onActive-bgColor: var(--colors-brand-4);

  --collapse-default-top-border-color: var(--colors-neutral-line-8);
  --collapse-default-top-border-width: var(--borders-width-2);
  --collapse-default-top-border-style: var(--borders-style-2);
  --collapse-default-right-border-color: var(--colors-neutral-line-8);
  --collapse-default-right-border-width: var(--borders-width-2);
  --collapse-default-right-border-style: var(--borders-style-2);
  --collapse-default-bottom-border-color: var(--colors-neutral-line-8);
  --collapse-default-bottom-border-width: var(--borders-width-2);
  --collapse-default-bottom-border-style: var(--borders-style-2);
  --collapse-default-left-border-color: var(--colors-neutral-line-8);
  --collapse-default-left-border-width: var(--borders-width-2);
  --collapse-default-left-border-style: var(--borders-style-2);
  --collapse-default-top-right-border-radius: var(--borders-radius-3);
  --collapse-default-top-left-border-radius: var(--borders-radius-3);
  --collapse-default-bottom-right-border-radius: var(--borders-radius-3);
  --collapse-default-bottom-left-border-radius: var(--borders-radius-3);
  --collapse-default-header-paddingTop: var(--sizes-base-6);
  --collapse-default-header-paddingBottom: var(--sizes-base-6);
  --collapse-default-header-paddingLeft: var(--sizes-size-9);
  --collapse-default-header-paddingRight: var(--sizes-size-9);
  --collapse-default-header-color: var(--colors-neutral-text-2);
  --collapse-default-header-fontSize: var(--fonts-size-7);
  --collapse-default-header-fontWeight: var(--fonts-weight-6);
  --collapse-default-header-lineHeight: var(--fonts-lineHeight-2);
  --collapse-default-header-bg-color: var(--colors-neutral-text-12);
  --collapse-default-header-hover-bg-color: var(--colors-neutral-text-12);
  --collapse-default-header-hover-color: var(--colors-neutral-text-2);
  --collapse-default-disabled-header-bg-color: var(--colors-neutral-fill-10);
  --collapse-default-disabled-color: var(--colors-neutral-text-6);
  --collapse-default-content-paddingTop: var(--sizes-size-9);
  --collapse-default-content-paddingBottom: var(--sizes-size-9);
  --collapse-default-content-paddingLeft: var(--sizes-size-9);
  --collapse-default-content-paddingRight: var(--sizes-size-9);
  --collapse-default-content-color: var(--colors-neutral-text-2);
  --collapse-default-content-fontSize: var(--fonts-size-8);
  --collapse-default-content-fontWeight: var(--fonts-weight-6);
  --collapse-default-content-lineHeight: var(--fonts-lineHeight-2);
  --collapse-default-bg-color: var(--colors-neutral-fill-11);
  --collapse-icon-icon: '<svg viewBox="0 0 99 176" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g transform="translate(-0.132812, 0.304688)" fill="currentColor" fill-rule="nonzero"><path d="M95.6353697,79.4031346 C95.3433678,79.1111326 95.037575,78.8417152 94.7219891,78.5906854 L19.819373,3.29993262 C15.6492004,-0.870040234 8.88818437,-0.870040234 4.71821172,3.29993262 C0.548238867,7.47010527 0.548238867,14.2309215 4.71821172,18.4010939 L72.9329621,87.0041811 L3.25320488,156.617783 C-0.916767969,160.787956 -0.916767969,167.548772 3.25320488,171.719144 C7.42317773,175.889117 14.1841937,175.889117 18.3543662,171.719144 L94.7211895,95.4180764 C95.0369754,95.1668467 95.342968,94.8976293 95.6351697,94.6054273 C97.7329475,92.5076496 98.7744412,89.7539166 98.7615739,87.0043809 C98.7746414,84.2544453 97.7331475,81.5009123 95.6353697,79.4031346 Z" id="路径"></path></g></svg>';
  --collapse-icon-size: var(--sizes-size-6);
  --collapse-icon-color: var(--colors-neutral-text-5);
  --collapse-icon-margin: var(--sizes-size-5);
  --collapse-icon-rotate: 90deg;
  --Collapse-header-fontSize: var(--collapse-default-header-fontSize);
  --Collapse-header-fontWeight: var(--collapse-default-header-fontWeight);
  --Collapse-header-padding: var(--collapse-default-header-paddingTop)
    var(--collapse-default-header-paddingRight)
    var(--collapse-default-header-paddingBottom)
    var(--collapse-default-header-paddingLeft);
  --Collapse-header-bg: var(--collapse-default-header-bg-color);
  --Collapse-header-onHover-bg: var(--collapse-default-header-hover-bg-color);
  --Collapse-header-collapsed-borderTop: none;
  --Collapse-header-collapsed-borderBottom: none;
  --Collapse-header-wrapper-direction: row-reverse;
  --Collapse-header-bg-disabled-color: var(
    --collapse-disabled-header-hover-bg-color
  );
  --Collapse-content-padding: var(--collapse-default-content-paddingTop)
    var(--collapse-default-content-paddingRight)
    var(--collapse-default-content-paddingBottom)
    var(--collapse-default-content-paddingLeft);
  --Collapse-content-color: var(--collapse-default-content-color);
  --Collapse-content-fontSize: var(--collapse-default-content-fontSize);
  --Collapse-content-fontWeight: var(--collapse-default-content-fontWeight);

  // 选项卡
  --Tabs-bg: var(--background);
  --Tabs-borderColor: var(--colors-neutral-line-8);
  --Tabs-link-disabled-color: var(--colors-neutral-text-6);
  --Tabs--sidebar-iconColor: var(--colors-brand-5);
  --Tabs-borderRadius: var(--borderRadius);
  --Tabs-borderWidth: var(--borderWidth);
  --Tabs-borderColor: var(--colors-neutral-line-8);
  --Tabs-color: var(--text-color);
  --Tabs-content-bg: var(--background);
  --Tabs-linkFontSize: var(--fonts-size-7);
  --Tabs-linkMargin: 0 #{px2rem(3px)} 0 0;
  --Tabs-linkPadding: var(--gap-sm) var(--gap-base);
  --Tabs-onActive-bg: var(--background);
  --Tabs-onActive-borderColor: var(--borderColor);
  --Tabs-onActive-color: var(--colors-neutral-text-2);
  --Tabs-onError-color: var(--colors-error-5);
  --Tabs-onDisabled-color: var(--colors-neutral-text-7);
  --Tabs-onHover-borderColor: var(--colors-neutral-line-8);
  --Tabs-add-icon-size: #{px2rem(15px)};
  --Tabs-add-icon-padding: #{px2rem(1px)};
  --Tabs-add-icon-margin: var(--gap-xs);
  --Tabs-add-margin: var(--gap-lg);
  --Tabs-gray-color: #83868c;
  --Tabs-close-margin: #{var(--gap-xs)};
  --Tabs-close-marginTop: #{px2rem(1px)};
  --Tabs-close-size: #{px2rem(12px)};
  --Tabs-link-maxWidth: #{px2rem(160px)};
  --Tabs-icon-gap: var(--gap-sm);
  --Tabs-animation-duration: var(--animation-duration);
  // 简约
  --Tabs--simple-paddingTop: var(--sizes-size-3);
  --Tabs--simple-paddingBottom: var(--sizes-size-3);
  --Tabs--simple-paddingLeft: var(--sizes-size-9);
  --Tabs--simple-paddingRight: var(--sizes-size-9);
  --Tabs--simple-split-size: var(--Tabs--simple-paddingTop)
    var(--Tabs--simple-paddingRight) var(--Tabs--simple-paddingBottom)
    var(--Tabs--simple-paddingLeft);
  --Tabs--simple-split-width: var(--borders-width-2);
  --Tabs--simple-split-style: var(--borders-style-2);
  --Tabs--simple-split-color: var(--colors-neutral-line-8);
  --Tabs--simple-color: var(--colors-neutral-text-2);
  --Tabs--simple-fontSize: var(--fonts-size-7);
  --Tabs--simple-fontWeight: var(--fonts-weight-6);
  --Tabs--simple-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--simple-active-color: var(--colors-brand-5);
  --Tabs--simple-active-fontSize: var(--fonts-size-7);
  --Tabs--simple-active-fontWeight: var(--fonts-weight-6);
  --Tabs--simple-active-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--simple-hover-color: var(--colors-brand-5);
  --Tabs--simple-hover-fontSize: var(--fonts-size-7);
  --Tabs--simple-hover-fontWeight: var(--fonts-weight-6);
  --Tabs--simple-hover-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--simple-disabled-color: var(--colors-neutral-text-6);
  --Tabs--simple-disabled-fontSize: var(--fonts-size-7);
  --Tabs--simple-disabled-fontWeight: var(--fonts-weight-6);
  --Tabs--simple-disabled-lineHeight: var(--fonts-lineHeight-2);
  // 加强
  --Tabs--strong-paddingTop: var(--sizes-size-5);
  --Tabs--strong-paddingBottom: var(--sizes-size-5);
  --Tabs--strong-paddingLeft: var(--sizes-size-9);
  --Tabs--strong-paddingRight: var(--sizes-size-9);
  --Tabs--strong-marginTop: var(--sizes-size-0);
  --Tabs--strong-marginBottom: var(--sizes-size-0);
  --Tabs--strong-marginLeft: var(--sizes-size-0);
  --Tabs--strong-marginRight: var(--sizes-size-5);
  --Tabs--strong-bg: var(--colors-neutral-fill-11);
  --Tabs--strong-top-border-color: var(--colors-neutral-text-7);
  --Tabs--strong-top-border-width: var(--borders-width-2);
  --Tabs--strong-top-border-style: var(--borders-style-2);
  --Tabs--strong-right-border-color: var(--colors-neutral-text-7);
  --Tabs--strong-right-border-width: var(--borders-width-2);
  --Tabs--strong-right-border-style: var(--borders-style-2);
  --Tabs--strong-bottom-border-color: var(--colors-neutral-text-7);
  --Tabs--strong-bottom-border-width: var(--borders-width-2);
  --Tabs--strong-bottom-border-style: var(--borders-style-2);
  --Tabs--strong-left-border-color: var(--colors-neutral-text-7);
  --Tabs--strong-left-border-width: var(--borders-width-2);
  --Tabs--strong-left-border-style: var(--borders-style-2);
  --Tabs--strong-top-right-border-radius: var(--borders-radius-3);
  --Tabs--strong-top-left-border-radius: var(--borders-radius-3);
  --Tabs--strong-bottom-right-border-radius: var(--borders-radius-1);
  --Tabs--strong-bottom-left-border-radius: var(--borders-radius-1);
  --Tabs--strong-color: var(--colors-neutral-text-2);
  --Tabs--strong-fontSize: var(--fonts-size-7);
  --Tabs--strong-fontWeight: var(--fonts-weight-6);
  --Tabs--strong-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--strong-active-bg: var(--colors-neutral-fill-11);
  --Tabs--strong-active-top-border-color: var(--colors-neutral-text-7);
  --Tabs--strong-active-top-border-width: var(--borders-width-2);
  --Tabs--strong-active-top-border-style: var(--borders-style-2);
  --Tabs--strong-active-right-border-color: var(--colors-neutral-text-7);
  --Tabs--strong-active-right-border-width: var(--borders-width-2);
  --Tabs--strong-active-right-border-style: var(--borders-style-2);
  --Tabs--strong-active-bottom-border-color: transparent;
  --Tabs--strong-active-bottom-border-width: var(--borders-width-2);
  --Tabs--strong-active-bottom-border-style: var(--borders-style-2);
  --Tabs--strong-active-left-border-color: var(--colors-neutral-text-7);
  --Tabs--strong-active-left-border-width: var(--borders-width-2);
  --Tabs--strong-active-left-border-style: var(--borders-style-2);
  --Tabs--strong-active-color: var(--colors-brand-5);
  --Tabs--strong-active-fontSize: var(--fonts-size-7);
  --Tabs--strong-active-fontWeight: var(--fonts-weight-6);
  --Tabs--strong-active-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--strong-hover-bg: var(--colors-neutral-fill-11);
  --Tabs--strong-hover-top-border-color: var(--colors-neutral-text-7);
  --Tabs--strong-hover-top-border-width: var(--borders-width-2);
  --Tabs--strong-hover-top-border-style: var(--borders-style-2);
  --Tabs--strong-hover-right-border-color: var(--colors-neutral-text-7);
  --Tabs--strong-hover-right-border-width: var(--borders-width-2);
  --Tabs--strong-hover-right-border-style: var(--borders-style-2);
  --Tabs--strong-hover-bottom-border-color: var(--colors-neutral-text-7);
  --Tabs--strong-hover-bottom-border-width: var(--borders-width-2);
  --Tabs--strong-hover-bottom-border-style: var(--borders-style-2);
  --Tabs--strong-hover-left-border-color: var(--colors-neutral-text-7);
  --Tabs--strong-hover-left-border-width: var(--borders-width-2);
  --Tabs--strong-hover-left-border-style: var(--borders-style-2);
  --Tabs--strong-hover-color: var(--colors-brand-5);
  --Tabs--strong-hover-fontSize: var(--fonts-size-7);
  --Tabs--strong-hover-fontWeight: var(--fonts-weight-6);
  --Tabs--strong-hover-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--strong-disabled-bg: var(--colors-neutral-fill-11);
  --Tabs--strong-disabled-top-border-color: var(--colors-neutral-text-8);
  --Tabs--strong-disabled-top-border-width: var(--borders-width-2);
  --Tabs--strong-disabled-top-border-style: var(--borders-style-2);
  --Tabs--strong-disabled-right-border-color: var(--colors-neutral-text-8);
  --Tabs--strong-disabled-right-border-width: var(--borders-width-2);
  --Tabs--strong-disabled-right-border-style: var(--borders-style-2);
  --Tabs--strong-disabled-bottom-border-color: var(--colors-neutral-text-8);
  --Tabs--strong-disabled-bottom-border-width: var(--borders-width-2);
  --Tabs--strong-disabled-bottom-border-style: var(--borders-style-2);
  --Tabs--strong-disabled-left-border-color: var(--colors-neutral-text-8);
  --Tabs--strong-disabled-left-border-width: var(--borders-width-2);
  --Tabs--strong-disabled-left-border-style: var(--borders-style-2);
  --Tabs--strong-disabled-color: var(--colors-neutral-text-6);
  --Tabs--strong-disabled-fontSize: var(--fonts-size-7);
  --Tabs--strong-disabled-fontWeight: var(--fonts-weight-6);
  --Tabs--strong-disabled-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--strong-add-size: #{px2rem(32px)};
  --Tabs--strong-arrow-size: #{px2rem(24px)};
  // 线型
  --Tabs--line-padding: var(--sizes-base-16);
  --Tabs--line-border-color: var(--colors-neutral-text-3);
  --Tabs--line-border-width: var(--borders-width-2);
  --Tabs--line-border-style: var(--borders-style-2);
  --Tabs--line-color: var(--colors-neutral-text-2);
  --Tabs--line-fontSize: var(--fonts-size-7);
  --Tabs--line-fontWeight: var(--fonts-weight-6);
  --Tabs--line-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--line-active-color: var(--colors-brand-5);
  --Tabs--line-active-fontSize: var(--fonts-size-7);
  --Tabs--line-active-fontWeight: var(--fonts-weight-6);
  --Tabs--line-active-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--line-active-border-color: var(--colors-brand-5);
  --Tabs--line-active-border-width: var(--borders-width-3);
  --Tabs--line-active-border-style: var(--borders-style-2);
  --Tabs--line-onHover-borderColor: var(--Tabs--line-active-border-color);
  --Tabs--line-hover-color: var(--colors-brand-5);
  --Tabs--line-hover-fontSize: var(--fonts-size-7);
  --Tabs--line-hover-fontWeight: var(--fonts-weight-6);
  --Tabs--line-hover-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--line-disabled-color: var(--colors-neutral-text-6);
  --Tabs--line-disabled-fontSize: var(--fonts-size-7);
  --Tabs--line-disabled-fontWeight: var(--fonts-weight-6);
  --Tabs--line-disabled-lineHeight: var(--fonts-lineHeight-2);
  // 卡片
  --Tabs--card-paddingTop: var(--sizes-size-4);
  --Tabs--card-paddingBottom: var(--sizes-size-0);
  --Tabs--card-paddingLeft: var(--sizes-size-6);
  --Tabs--card-paddingRight: var(--sizes-size-6);
  --Tabs--card-padding: var(--Tabs--card-paddingTop)
    var(--Tabs--card-paddingRight) var(--Tabs--card-paddingBottom)
    var(--Tabs--card-paddingLeft);
  --Tabs--card-border-color: var(--colors-neutral-line-8);
  --Tabs--card-border-width: var(--borders-width-2);
  --Tabs--card-border-style: var(--borders-style-2);
  --Tabs--card-borderTopColor: var(--Tabs--card-border-color);
  --Tabs--card-bg: var(--colors-neutral-fill-10);
  --Tabs--card-linkBg: transparent;
  --Tabs--card-color: var(--colors-neutral-text-2);
  --Tabs--card-fontSize: var(--fonts-size-7);
  --Tabs--card-fontWeight: var(--fonts-weight-6);
  --Tabs--card-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--card-linkMargin: var(--sizes-size-6);
  --Tabs--card-linkPadding: var(--sizes-size-6);
  --Tabs--card-borderRadius: var(--borders-radius-3);
  --Tabs--card-active-color: var(--colors-neutral-text-2);
  --Tabs--card-active-fontSize: var(--fonts-size-7);
  --Tabs--card-active-fontWeight: var(--fonts-weight-6);
  --Tabs--card-active-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--card-active-linkBg: var(--colors-neutral-fill-11);
  --Tabs--card-onActive-bg: var(--Tabs--card-active-linkBg);
  --Tabs--card-hover-color: var(--colors-neutral-text-2);
  --Tabs--card-hover-fontSize: var(--fonts-size-7);
  --Tabs--card-hover-fontWeight: var(--fonts-weight-6);
  --Tabs--card-hover-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--card-hover-linkBg: var(--colors-neutral-fill-11);
  --Tabs--card-disabled-color: var(--colors-neutral-text-6);
  --Tabs--card-disabled-fontSize: var(--fonts-size-7);
  --Tabs--card-disabled-fontWeight: var(--fonts-weight-6);
  --Tabs--card-disabled-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--card-disabled-linkBg: transparent;
  --Tabs--card-add-gap: var(--gap-md);
  --Tabs--card-add-gap-top: #{px2rem(7px)};
  --Tabs--card-arrow-gap: var(--gap-sm);
  // 水平铺满
  --Tabs--tiled-top-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-top-border-width: var(--borders-width-2);
  --Tabs--tiled-top-border-style: var(--borders-style-2);
  --Tabs--tiled-right-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-right-border-width: var(--borders-width-2);
  --Tabs--tiled-right-border-style: var(--borders-style-2);
  --Tabs--tiled-bottom-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-bottom-border-width: var(--borders-width-2);
  --Tabs--tiled-bottom-border-style: var(--borders-style-2);
  --Tabs--tiled-left-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-left-border-width: var(--borders-width-2);
  --Tabs--tiled-left-border-style: var(--borders-style-2);
  --Tabs--tiled-color: var(--colors-neutral-text-2);
  --Tabs--tiled-fontSize: var(--fonts-size-7);
  --Tabs--tiled-fontWeight: var(--fonts-weight-6);
  --Tabs--tiled-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--tiled-paddingTop: var(--sizes-size-5);
  --Tabs--tiled-paddingBottom: var(--sizes-size-5);
  --Tabs--tiled-paddingLeft: var(--sizes-size-7);
  --Tabs--tiled-paddingRight: var(--sizes-size-7);
  --Tabs--tiled-active-top-border-color: var(--colors-brand-5);
  --Tabs--tiled-active-top-border-width: var(--borders-width-2);
  --Tabs--tiled-active-top-border-style: var(--borders-style-2);
  --Tabs--tiled-active-right-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-active-right-border-width: var(--borders-width-2);
  --Tabs--tiled-active-right-border-style: var(--borders-style-2);
  --Tabs--tiled-active-bottom-border-color: transparent;
  --Tabs--tiled-active-bottom-border-width: var(--borders-width-2);
  --Tabs--tiled-active-bottom-border-style: var(--borders-style-2);
  --Tabs--tiled-active-left-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-active-left-border-width: var(--borders-width-2);
  --Tabs--tiled-active-left-border-style: var(--borders-style-2);
  --Tabs--tiled-active-color: var(--colors-neutral-text-2);
  --Tabs--tiled-active-fontSize: var(--fonts-size-7);
  --Tabs--tiled-active-fontWeight: var(--fonts-weight-6);
  --Tabs--tiled-active-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--tiled-hover-top-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-hover-top-border-width: var(--borders-width-2);
  --Tabs--tiled-hover-top-border-style: var(--borders-style-2);
  --Tabs--tiled-hover-right-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-hover-right-border-width: var(--borders-width-2);
  --Tabs--tiled-hover-right-border-style: var(--borders-style-2);
  --Tabs--tiled-hover-bottom-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-hover-bottom-border-width: var(--borders-width-2);
  --Tabs--tiled-hover-bottom-border-style: var(--borders-style-2);
  --Tabs--tiled-hover-left-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-hover-left-border-width: var(--borders-width-2);
  --Tabs--tiled-hover-left-border-style: var(--borders-style-2);
  --Tabs--tiled-hover-color: var(--colors-neutral-text-2);
  --Tabs--tiled-hover-fontSize: var(--fonts-size-7);
  --Tabs--tiled-hover-fontWeight: var(--fonts-weight-6);
  --Tabs--tiled-hover-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--tiled-disabled-top-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-disabled-top-border-width: var(--borders-width-2);
  --Tabs--tiled-disabled-top-border-style: var(--borders-style-2);
  --Tabs--tiled-disabled-right-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-disabled-right-border-width: var(--borders-width-2);
  --Tabs--tiled-disabled-right-border-style: var(--borders-style-2);
  --Tabs--tiled-disabled-bottom-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-disabled-bottom-border-width: var(--borders-width-2);
  --Tabs--tiled-disabled-bottom-border-style: var(--borders-style-2);
  --Tabs--tiled-disabled-left-border-color: var(--colors-neutral-line-8);
  --Tabs--tiled-disabled-left-border-width: var(--borders-width-2);
  --Tabs--tiled-disabled-left-border-style: var(--borders-style-2);
  --Tabs--tiled-disabled-color: var(--colors-neutral-text-6);
  --Tabs--tiled-disabled-fontSize: var(--fonts-size-7);
  --Tabs--tiled-disabled-fontWeight: var(--fonts-weight-6);
  --Tabs--tiled-disabled-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--tiled-add-gap: var(--gap-base);
  // 选择器型
  --Tabs--radio-top-border-color: var(--colors-neutral-line-8);
  --Tabs--radio-top-border-width: var(--borders-width-2);
  --Tabs--radio-top-border-style: var(--borders-style-2);
  --Tabs--radio-right-border-color: var(--colors-neutral-line-8);
  --Tabs--radio-right-border-width: var(--borders-width-2);
  --Tabs--radio-right-border-style: var(--borders-style-2);
  --Tabs--radio-bottom-border-color: var(--colors-neutral-line-8);
  --Tabs--radio-bottom-border-width: var(--borders-width-2);
  --Tabs--radio-bottom-border-style: var(--borders-style-2);
  --Tabs--radio-left-border-color: var(--colors-neutral-line-8);
  --Tabs--radio-left-border-width: var(--borders-width-2);
  --Tabs--radio-left-border-style: var(--borders-style-2);
  --Tabs--radio-color: var(--colors-neutral-text-2);
  --Tabs--radio-fontSize: var(--fonts-size-7);
  --Tabs--radio-fontWeight: var(--fonts-weight-6);
  --Tabs--radio-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--radio-paddingTop: var(--sizes-size-5);
  --Tabs--radio-paddingBottom: var(--sizes-size-5);
  --Tabs--radio-paddingLeft: var(--sizes-size-7);
  --Tabs--radio-paddingRight: var(--sizes-size-7);
  --Tabs--radio-bg: var(--colors-neutral-fill-11);
  --Tabs--radio-height: var(--sizes-base-15);
  --Tabs--radio-active-top-border-color: var(--colors-brand-5);
  --Tabs--radio-active-top-border-width: var(--borders-width-2);
  --Tabs--radio-active-top-border-style: var(--borders-style-2);
  --Tabs--radio-active-right-border-color: var(--colors-brand-5);
  --Tabs--radio-active-right-border-width: var(--borders-width-2);
  --Tabs--radio-active-right-border-style: var(--borders-style-2);
  --Tabs--radio-active-bottom-border-color: var(--colors-brand-5);
  --Tabs--radio-active-bottom-border-width: var(--borders-width-2);
  --Tabs--radio-active-bottom-border-style: var(--borders-style-2);
  --Tabs--radio-active-left-border-color: var(--colors-brand-5);
  --Tabs--radio-active-left-border-width: var(--borders-width-2);
  --Tabs--radio-active-left-border-style: var(--borders-style-2);
  --Tabs--radio-active-color: var(--colors-neutral-text-11);
  --Tabs--radio-active-fontSize: var(--fonts-size-7);
  --Tabs--radio-active-fontWeight: var(--fonts-weight-6);
  --Tabs--radio-active-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--radio-active-bg: var(--colors-brand-5);
  --Tabs--radio-hover-top-border-color: var(--colors-neutral-line-8);
  --Tabs--radio-hover-top-border-width: var(--borders-width-2);
  --Tabs--radio-hover-top-border-style: var(--borders-style-2);
  --Tabs--radio-hover-right-border-color: var(--colors-neutral-line-8);
  --Tabs--radio-hover-right-border-width: var(--borders-width-2);
  --Tabs--radio-hover-right-border-style: var(--borders-style-2);
  --Tabs--radio-hover-bottom-border-color: var(--colors-neutral-line-8);
  --Tabs--radio-hover-bottom-border-width: var(--borders-width-2);
  --Tabs--radio-hover-bottom-border-style: var(--borders-style-2);
  --Tabs--radio-hover-left-border-color: var(--colors-neutral-line-8);
  --Tabs--radio-hover-left-border-width: var(--borders-width-2);
  --Tabs--radio-hover-left-border-style: var(--borders-style-2);
  --Tabs--radio-hover-color: var(--colors-neutral-text-2);
  --Tabs--radio-hover-fontSize: var(--fonts-size-7);
  --Tabs--radio-hover-fontWeight: var(--fonts-weight-6);
  --Tabs--radio-hover-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--radio-hover-bg: var(--colors-neutral-fill-11);
  --Tabs--radio-disabled-top-border-color: var(--colors-neutral-line-8);
  --Tabs--radio-disabled-top-border-width: var(--borders-width-2);
  --Tabs--radio-disabled-top-border-style: var(--borders-style-2);
  --Tabs--radio-disabled-right-border-color: var(--colors-neutral-line-8);
  --Tabs--radio-disabled-right-border-width: var(--borders-width-2);
  --Tabs--radio-disabled-right-border-style: var(--borders-style-2);
  --Tabs--radio-disabled-bottom-border-color: var(--colors-neutral-line-8);
  --Tabs--radio-disabled-bottom-border-width: var(--borders-width-2);
  --Tabs--radio-disabled-bottom-border-style: var(--borders-style-2);
  --Tabs--radio-disabled-left-border-color: var(--colors-neutral-line-8);
  --Tabs--radio-disabled-left-border-width: var(--borders-width-2);
  --Tabs--radio-disabled-left-border-style: var(--borders-style-2);
  --Tabs--radio-disabled-color: var(--colors-neutral-text-6);
  --Tabs--radio-disabled-fontSize: var(--fonts-size-7);
  --Tabs--radio-disabled-fontWeight: var(--fonts-weight-6);
  --Tabs--radio-disabled-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--radio-disabled-bg: var(--colors-neutral-fill-11);
  // 垂直
  --Tabs--vertical-color: var(--colors-neutral-text-2);
  --Tabs--vertical-fontSize: var(--fonts-size-7);
  --Tabs--vertical-fontWeight: var(--fonts-weight-6);
  --Tabs--vertical-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--vertical-paddingTop: var(--sizes-size-5);
  --Tabs--vertical-paddingBottom: var(--sizes-size-5);
  --Tabs--vertical-paddingLeft: var(--sizes-size-7);
  --Tabs--vertical-paddingRight: var(--sizes-size-7);
  --Tabs--vertical-bg: var(--colors-neutral-fill-10);
  --Tabs--vertical-width: 8.75rem;
  --Tabs--vertical-active-color: var(--colors-brand-5);
  --Tabs--vertical-active-fontSize: var(--fonts-size-7);
  --Tabs--vertical-active-fontWeight: var(--fonts-weight-6);
  --Tabs--vertical-active-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--vertical-active-border-color: var(--colors-brand-5);
  --Tabs--vertical-active-border-width: var(--borders-width-4);
  --Tabs--vertical-active-border-style: var(--borders-style-2);
  --Tabs--vertical-onActive-borderWidth: var(
    --Tabs--vertical-active-border-width
  );
  --Tabs--vertical-onActive-border: var(--Tabs--vertical-active-border-color);
  --Tabs--vertical-onActive-color: var(--Tabs--vertical-active-color);
  --Tabs--vertical-hover-color: var(--colors-brand-6);
  --Tabs--vertical-hover-fontSize: var(--fonts-size-7);
  --Tabs--vertical-hover-fontWeight: var(--fonts-weight-6);
  --Tabs--vertical-hover-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--vertical-disabled-color: var(--colors-neutral-text-6);
  --Tabs--vertical-disabled-fontSize: var(--fonts-size-7);
  --Tabs--vertical-disabled-fontWeight: var(--fonts-weight-6);
  --Tabs--vertical-disabled-lineHeight: var(--fonts-lineHeight-2);
  // 侧边栏
  --Tabs--sidebar-color: var(--colors-neutral-text-5);
  --Tabs--sidebar-fontSize: var(--fonts-size-7);
  --Tabs--sidebar-fontWeight: var(--fonts-weight-6);
  --Tabs--sidebar-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--sidebar-sideWidth: var(--sizes-base-31);
  --Tabs--sidebar-sideMargin: var(--sizes-base-11);
  --Tabs--sidebar-iconSize: #{px2rem(24px)};
  --Tabs--sidebar-iconMargin: #{px2rem(5px)};
  --Tabs--sidebar-active-color: var(--colors-brand-5);
  --Tabs--sidebar-active-fontSize: var(--fonts-size-7);
  --Tabs--sidebar-active-fontWeight: var(--fonts-weight-6);
  --Tabs--sidebar-active-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--sidebar-hover-color: var(--colors-brand-6);
  --Tabs--sidebar-hover-fontSize: var(--fonts-size-7);
  --Tabs--sidebar-hover-fontWeight: var(--fonts-weight-6);
  --Tabs--sidebar-hover-lineHeight: var(--fonts-lineHeight-2);
  --Tabs--sidebar-disabled-color: var(--colors-neutral-text-6);
  --Tabs--sidebar-disabled-fontSize: var(--fonts-size-7);
  --Tabs--sidebar-disabled-fontWeight: var(--fonts-weight-6);
  --Tabs--sidebar-disabled-lineHeight: var(--fonts-lineHeight-2);
  // chrome
  --Tabs--chrome-onHover-bg: var(--colors-neutral-fill-10);
  --Tabs--chrome-bg: var(--colors-neutral-fill-10);
  --Tabs--chrome-radius-size: #{px2rem(8px)};
  --Tabs--chrome-right-border-color: var(--colors-neutral-line-5);

  // 面板 Panel
  --Panel-bg: var(--colors-neutral-fill-11);
  --Panel-marginTop: var(--sizes-size-0);
  --Panel-marginBottom: var(--sizes-base-10);
  --Panel-marginLeft: var(--sizes-size-0);
  --Panel-marginRight: var(--sizes-size-0);
  --Panel-top-border-color: var(--colors-neutral-line-8);
  --Panel-top-border-width: var(--borders-width-2);
  --Panel-top-border-style: var(--borders-style-2);
  --Panel-right-border-color: var(--colors-neutral-line-8);
  --Panel-right-border-width: var(--borders-width-2);
  --Panel-right-border-style: var(--borders-style-2);
  --Panel-bottom-border-color: var(--colors-neutral-line-8);
  --Panel-bottom-border-width: var(--borders-width-2);
  --Panel-bottom-border-style: var(--borders-style-2);
  --Panel-left-border-color: var(--colors-neutral-line-8);
  --Panel-left-border-width: var(--borders-width-2);
  --Panel-left-border-style: var(--borders-style-2);
  --Panel-top-right-border-radius: var(--borders-radius-3);
  --Panel-top-left-border-radius: var(--borders-radius-3);
  --Panel-bottom-right-border-radius: var(--borders-radius-3);
  --Panel-bottom-left-border-radius: var(--borders-radius-3);
  --Panel-borderRadius: var(--Panel-top-left-border-radius)
    var(--Panel-top-right-border-radius) var(--Panel-bottom-right-border-radius)
    var(--Panel-bottom-left-border-radius);
  --Panel-borderWidth: var(--Panel-top-border-width)
    var(--Panel-right-border-width) var(--Panel-bottom-border-width)
    var(--Panel-left-border-width);
  --Panel-boxShadow: var(--shadows-shadow-sm);
  --Panel-heading-paddingTop: var(--sizes-size-5);
  --Panel-heading-paddingBottom: var(--sizes-size-5);
  --Panel-heading-paddingLeft: var(--sizes-size-7);
  --Panel-heading-paddingRight: var(--sizes-size-7);
  --Panel-heading-bg: var(--colors-neutral-fill-10);
  --Panel-heading-color: var(--colors-neutral-text-5);
  --Panel-heading-fontSize: var(--fonts-size-8);
  --Panel-heading-fontWeight: var(--fonts-weight-6);
  --Panel-heading-lineHeight: var(--fonts-lineHeight-2);
  --Panel-heading-top-border-color: transparent;
  --Panel-heading-top-border-width: var(--borders-width-1);
  --Panel-heading-top-border-style: var(--borders-style-2);
  --Panel-heading-right-border-color: transparent;
  --Panel-heading-right-border-width: var(--borders-width-1);
  --Panel-heading-right-border-style: var(--borders-style-2);
  --Panel-heading-bottom-border-color: var(--colors-neutral-line-8);
  --Panel-heading-bottom-border-width: var(--borders-width-2);
  --Panel-heading-bottom-border-style: var(--borders-style-2);
  --Panel-heading-left-border-color: transparent;
  --Panel-heading-left-border-width: var(--borders-width-1);
  --Panel-heading-left-border-style: var(--borders-style-2);
  --Panel-headingPadding: var(--Panel-heading-paddingTop)
    var(--Panel-heading-paddingRight) var(--Panel-heading-paddingBottom)
    var(--Panel-heading-paddingLeft);
  --Panel-headingBorderRadius: var(--Panel-top-left-border-radius)
    var(--Panel-top-right-border-radius) 0 0;
  --Panel-body-paddingTop: var(--sizes-size-7);
  --Panel-body-paddingBottom: var(--sizes-size-7);
  --Panel-body-paddingLeft: var(--sizes-size-7);
  --Panel-body-paddingRight: var(--sizes-size-7);
  --Panel-bodyPadding: var(--Panel-body-paddingTop)
    var(--Panel-body-paddingRight) var(--Panel-body-paddingBottom)
    var(--Panel-body-paddingLeft);
  --Panel-footer-paddingTop: var(--sizes-size-5);
  --Panel-footer-paddingBottom: var(--sizes-size-5);
  --Panel-footer-paddingLeft: var(--sizes-size-7);
  --Panel-footer-paddingRight: var(--sizes-size-7);
  --Panel-footer-bg: var(--colors-neutral-fill-11);
  --Panel-footer-top-border-color: var(--colors-neutral-line-8);
  --Panel-footer-top-border-width: var(--borders-width-2);
  --Panel-footer-top-border-style: var(--borders-style-2);
  --Panel-footer-right-border-color: transparent;
  --Panel-footer-right-border-width: var(--borders-width-1);
  --Panel-footer-right-border-style: var(--borders-style-2);
  --Panel-footer-bottom-border-color: transparent;
  --Panel-footer-bottom-border-width: var(--borders-width-1);
  --Panel-footer-bottom-border-style: var(--borders-style-2);
  --Panel-footer-left-border-color: transparent;
  --Panel-footer-left-border-width: var(--borders-width-1);
  --Panel-footer-left-border-style: var(--borders-style-2);
  --Panel-footerBorderRadius: 0 0 var(--Panel-bottom-right-border-radius)
    var(--Panel-bottom-left-border-radius);
  --Panel-footerBg: var(--Panel-footer-bg);
  --Panel-footerPadding: var(--Panel-footer-paddingTop)
    var(--Panel-footer-paddingRight) var(--Panel-footer-paddingBottom)
    var(--Panel-footer-paddingLeft);
  --Panel-footerBorderColor: var(--Panel-footer-top-border-color)
    var(--Panel-footer-right-border-color)
    var(--Panel-footer-bottom-border-color)
    var(--Panel-footer-left-border-color);
  --Panel-footer-buttonSpace: var(--sizes-size-5);
  --Panel-footerButtonMarginLeft: var(--Panel-footer-buttonSpace);
  --Panel-fixedBottom-borderTop: none;
  --Panel-fixedBottom-boxShadow: var(--shadows-shadow-normal);
  --Panel-btnToolbarTextAlign: right;

  // 分割线
  --Divider-style: var(--borders-style-2);
  --Divider-color: var(--colors-neutral-line-8);
  --Divider-width: var(--borders-width-2);
  --Divider-marginTop: var(--sizes-size-7);
  --Divider-marginLeft: var(--sizes-size-0);
  --Divider-marginRight: var(--sizes-size-0);
  --Divider-marginBottom: var(--sizes-size-7);
  --Divider-text-width: 5%;
  --Divider-text-fontSize: var(--fonts-size-7);
  --Divider-text-fontWeight: var(--fonts-weight-6);
  --Divider-text-color: var(--colors-neutral-text-2);
  --Divider-text-marginTop: var(--sizes-size-0);
  --Divider-text-marginLeft: var(--sizes-size-9);
  --Divider-text-marginRight: var(--sizes-size-9);
  --Divider-text-marginBottom: var(--sizes-size-0);

  --inputFile-base-des-color: var(--colors-neutral-text-2);
  --inputFile-base-des-fontSize: var(--fonts-size-7);
  --inputFile-base-des-fontWeight: var(--fonts-weight-6);
  --inputFile-base-des-margin: var(--sizes-base-5);
  --inputFile-list-marginTop: var(--sizes-base-4);
  --inputFile-list-marginBottom: var(--sizes-size-0); // 为了符合组件模版规范将marginBottom设置为0
  --inputFile-list-marginLeft: var(--sizes-size-0);
  --inputFile-list-marginRight: var(--sizes-size-0);
  --inputFile-list-paddingTop: var(--sizes-size-2);
  --inputFile-list-paddingBottom: var(--sizes-size-2);
  --inputFile-list-paddingLeft: var(--sizes-size-3);
  --inputFile-list-paddingRight: var(--sizes-size-3);
  --inputFile-list-color: var(--colors-brand-5);
  --inputFile-list-fontSize: var(--fonts-size-8);
  --inputFile-list-fontWeight: var(--fonts-weight-6);
  --inputFile-list-bg-color: transparent;
  --inputFile-list-bg-color-hover: var(--colors-neutral-fill-9);
  --inputFile-list-icon-size: var(--sizes-base-6);
  --inputFile-list-icon-color: var(--colors-neutral-text-2);
  --inputFile-list-icon-margin: var(--sizes-size-3);
  --inputFile-list-delete-icon-size: var(--sizes-base-6);
  --inputFile-list-delete-icon-color: var(--colors-neutral-text-5);
  --inputFile-list-delete-icon-color-hover: var(--colors-neutral-text-4);
  --inputFile-drag-top-border-color: var(--colors-neutral-line-8);
  --inputFile-drag-top-border-width: var(--borders-width-2);
  --inputFile-drag-top-border-style: var(--borders-style-3);
  --inputFile-drag-right-border-color: var(--colors-neutral-line-8);
  --inputFile-drag-right-border-width: var(--borders-width-2);
  --inputFile-drag-right-border-style: var(--borders-style-3);
  --inputFile-drag-bottom-border-color: var(--colors-neutral-line-8);
  --inputFile-drag-bottom-border-width: var(--borders-width-2);
  --inputFile-drag-bottom-border-style: var(--borders-style-3);
  --inputFile-drag-left-border-color: var(--colors-neutral-line-8);
  --inputFile-drag-left-border-width: var(--borders-width-2);
  --inputFile-drag-left-border-style: var(--borders-style-3);
  --inputFile-drag-top-right-border-radius: var(--borders-radius-3);
  --inputFile-drag-top-left-border-radius: var(--borders-radius-3);
  --inputFile-drag-bottom-right-border-radius: var(--borders-radius-3);
  --inputFile-drag-bottom-left-border-radius: var(--borders-radius-3);
  --inputFile-drag-hover-top-border-color: var(--colors-neutral-line-8);
  --inputFile-drag-hover-top-border-width: var(--borders-width-2);
  --inputFile-drag-hover-top-border-style: var(--borders-style-3);
  --inputFile-drag-hover-right-border-color: var(--colors-neutral-line-8);
  --inputFile-drag-hover-right-border-width: var(--borders-width-2);
  --inputFile-drag-hover-right-border-style: var(--borders-style-3);
  --inputFile-drag-hover-bottom-border-color: var(--colors-neutral-line-8);
  --inputFile-drag-hover-bottom-border-width: var(--borders-width-2);
  --inputFile-drag-hover-bottom-border-style: var(--borders-style-3);
  --inputFile-drag-hover-left-border-color: var(--colors-neutral-line-8);
  --inputFile-drag-hover-left-border-width: var(--borders-width-2);
  --inputFile-drag-hover-left-border-style: var(--borders-style-3);
  --inputFile-drag-color: var(--colors-neutral-text-2);
  --inputFile-drag-fontSize: var(--fonts-size-8);
  --inputFile-drag-fontWeight: var(--fonts-weight-6);
  --inputFile-drag-icon-size: var(--sizes-base-24);
  --inputFile-drag-icon-color: var(--colors-brand-5);
  --inputFile-drag-icon-margin: var(--sizes-size-5);
  --inputFile-drag-bg-color: var(--colors-neutral-fill-11);
  --inputFile-drag-bg-color-hover: var(--colors-neutral-text-11);
  --FileControl-danger-color: var(--colors-error-5);
  --FileControl-drag-color: var(--inputFile-drag-color);
  --FileControl-border-color: var(--inputFile-drag-top-border-color)
    var(--inputFile-drag-right-border-color)
    var(--inputFile-drag-bottom-border-color)
    var(--inputFile-drag-left-border-color);
  --FileControl-onDisabled-color: var(--colors-neutral-text-6);
  --FileControl-onDisabled-bg: var(--colors-neutral-fill-10);
  --FileControl-onHover-bg: var(--inputFile-list-bg-color-hover);
  --FileControl-icon-color: var(--inputFile-list-delete-icon-color);
  --FileControl-icon-onHover-color: var(
    --inputFile-list-delete-icon-color-hover
  );
  --FileControl-progress-borderRadius: var(--borders-radius-2);
  --inputImage-base-default-top-border-color: var(--colors-neutral-line-8);
  --inputImage-base-default-top-border-width: var(--borders-width-2);
  --inputImage-base-default-top-border-style: var(--borders-style-2);
  --inputImage-base-default-right-border-color: var(--colors-neutral-line-8);
  --inputImage-base-default-right-border-width: var(--borders-width-2);
  --inputImage-base-default-right-border-style: var(--borders-style-2);
  --inputImage-base-default-bottom-border-color: var(--colors-neutral-line-8);
  --inputImage-base-default-bottom-border-width: var(--borders-width-2);
  --inputImage-base-default-bottom-border-style: var(--borders-style-2);
  --inputImage-base-default-left-border-color: var(--colors-neutral-line-8);
  --inputImage-base-default-left-border-width: var(--borders-width-2);
  --inputImage-base-default-left-border-style: var(--borders-style-2);
  --inputImage-base-default-top-right-border-radius: var(--borders-radius-3);
  --inputImage-base-default-top-left-border-radius: var(--borders-radius-3);
  --inputImage-base-default-bottom-right-border-radius: var(--borders-radius-3);
  --inputImage-base-default-bottom-left-border-radius: var(--borders-radius-3);
  --inputImage-base-default-fontSize: var(--fonts-size-7);
  --inputImage-base-default-fontWeight: var(--fonts-weight-6);
  --inputImage-base-default-color: var(--colors-neutral-text-5);
  --inputImage-base-default-icon-size: var(--sizes-base-12);
  --inputImage-base-default-icon-color: var(--colors-neutral-text-5);
  --inputImage-base-default-icon-margin: var(--sizes-size-5);
  --inputImage-base-default-bg-color: var(--colors-neutral-fill-11);
  --inputImage-base-hover-top-border-color: var(--colors-brand-5);
  --inputImage-base-hover-top-border-width: var(--borders-width-2);
  --inputImage-base-hover-top-border-style: var(--borders-style-2);
  --inputImage-base-hover-right-border-color: var(--colors-brand-5);
  --inputImage-base-hover-right-border-width: var(--borders-width-2);
  --inputImage-base-hover-right-border-style: var(--borders-style-2);
  --inputImage-base-hover-bottom-border-color: var(--colors-brand-5);
  --inputImage-base-hover-bottom-border-width: var(--borders-width-2);
  --inputImage-base-hover-bottom-border-style: var(--borders-style-2);
  --inputImage-base-hover-left-border-color: var(--colors-brand-5);
  --inputImage-base-hover-left-border-width: var(--borders-width-2);
  --inputImage-base-hover-left-border-style: var(--borders-style-2);
  --inputImage-base-hover-color: var(--colors-neutral-text-5);
  --inputImage-base-hover-icon-color: var(--colors-neutral-text-5);
  --inputImage-base-hover-bg-color: var(--colors-neutral-fill-11);
  --inputImage-base-active-top-border-color: var(--colors-brand-5);
  --inputImage-base-active-top-border-width: var(--borders-width-2);
  --inputImage-base-active-top-border-style: var(--borders-style-2);
  --inputImage-base-active-right-border-color: var(--colors-brand-5);
  --inputImage-base-active-right-border-width: var(--borders-width-2);
  --inputImage-base-active-right-border-style: var(--borders-style-2);
  --inputImage-base-active-bottom-border-color: var(--colors-brand-5);
  --inputImage-base-active-bottom-border-width: var(--borders-width-2);
  --inputImage-base-active-bottom-border-style: var(--borders-style-2);
  --inputImage-base-active-left-border-color: var(--colors-brand-5);
  --inputImage-base-active-left-border-width: var(--borders-width-2);
  --inputImage-base-active-left-border-style: var(--borders-style-2);
  --inputImage-base-active-color: var(--colors-neutral-text-5);
  --inputImage-base-active-icon-color: var(--colors-neutral-text-5);
  --inputImage-base-active-bg-color: var(--colors-neutral-fill-11);
  --inputImage-base-disabled-top-border-color: var(--colors-neutral-line-8);
  --inputImage-base-disabled-top-border-width: var(--borders-width-2);
  --inputImage-base-disabled-top-border-style: var(--borders-style-2);
  --inputImage-base-disabled-right-border-color: var(--colors-neutral-line-8);
  --inputImage-base-disabled-right-border-width: var(--borders-width-2);
  --inputImage-base-disabled-right-border-style: var(--borders-style-2);
  --inputImage-base-disabled-bottom-border-color: var(--colors-neutral-line-8);
  --inputImage-base-disabled-bottom-border-width: var(--borders-width-2);
  --inputImage-base-disabled-bottom-border-style: var(--borders-style-2);
  --inputImage-base-disabled-left-border-color: var(--colors-neutral-line-8);
  --inputImage-base-disabled-left-border-width: var(--borders-width-2);
  --inputImage-base-disabled-left-border-style: var(--borders-style-2);
  --inputImage-base-disabled-color: var(--colors-neutral-text-6);
  --inputImage-base-disabled-icon-color: var(--colors-neutral-text-6);
  --inputImage-base-disabled-bg-color: var(--colors-neutral-fill-10);
  --ImageControl-addBtn-bg: var(--inputImage-base-default-bg-color);
  --ImageControl-addBtn-border: var(--colors-neutral-line-7);
  --ImageControl-addBtn-borderRadius: var(--borders-radius-3);
  --ImageControl-addBtn-color: var(--inputImage-base-default-color);
  --ImageControl-addBtn-onActive-bg: var(--inputImage-base-active-bg-color);
  --ImageControl-addBtn-onActive-border: var(--colors-brand-5);
  --ImageControl-addBtn-onActive-color: var(--inputImage-base-active-color);
  --ImageControl-addBtn-onDisabled-bg: var(--inputImage-base-disabled-bg-color);
  --ImageControl-addBtn-onDisabled-border: var(--colors-neutral-line-8);
  --ImageControl-addBtn-onDisabled-color: var(--inputImage-base-disabled-color);
  --ImageControl-addBtn-onHover-bg: var(--inputImage-base-hover-bg-color);
  --ImageControl-addBtn-onHover-border: var(--colors-brand-5);
  --ImageControl-addBtn-onHover-color: var(--inputImage-base-hover-color);
  --ImageControl-addBtn-upload-color: var(--inputImage-base-default-color);
  --ImageControl-progress-borderRadius: var(--borders-radius-2);

  --select-base-default-top-border-color: var(--colors-neutral-line-8);
  --select-base-default-top-border-width: var(--borders-width-2);
  --select-base-default-top-border-style: var(--borders-style-2);
  --select-base-default-right-border-color: var(--colors-neutral-line-8);
  --select-base-default-right-border-width: var(--borders-width-2);
  --select-base-default-right-border-style: var(--borders-style-2);
  --select-base-default-bottom-border-color: var(--colors-neutral-line-8);
  --select-base-default-bottom-border-width: var(--borders-width-2);
  --select-base-default-bottom-border-style: var(--borders-style-2);
  --select-base-default-left-border-color: var(--colors-neutral-line-8);
  --select-base-default-left-border-width: var(--borders-width-2);
  --select-base-default-left-border-style: var(--borders-style-2);
  --select-base-default-top-right-border-radius: var(--borders-radius-3);
  --select-base-default-top-left-border-radius: var(--borders-radius-3);
  --select-base-default-bottom-right-border-radius: var(--borders-radius-3);
  --select-base-default-bottom-left-border-radius: var(--borders-radius-3);
  --select-base-default-paddingTop: var(--sizes-size-3);
  --select-base-default-paddingBottom: var(--sizes-size-3);
  --select-base-default-paddingLeft: var(--sizes-size-6);
  --select-base-default-paddingRight: var(--sizes-size-6);
  --select-base-default-color: var(--colors-neutral-text-2);
  --select-base-default-fontSize: var(--fonts-size-7);
  --select-base-default-fontWeight: var(--fonts-weight-6);
  --select-base-default-bg-color: var(--colors-neutral-fill-11);
  --select-base-default-option-paddingTop: var(--sizes-size-0);
  --select-base-default-option-paddingBottom: var(--sizes-size-0);
  --select-base-default-option-paddingLeft: var(--sizes-size-6);
  --select-base-default-option-paddingRight: var(--sizes-size-6);
  --select-base-default-option-color: var(--colors-neutral-text-2);
  --select-base-default-option-fontSize: var(--fonts-size-7);
  --select-base-default-option-fontWeight: var(--fonts-weight-6);
  --select-base-default-option-bg-color: transparent;
  --select-base-default-option-line-height: var(--sizes-base-16);
  --select-base-hover-top-border-color: var(--colors-brand-5);
  --select-base-hover-top-border-width: var(--borders-width-2);
  --select-base-hover-top-border-style: var(--borders-style-2);
  --select-base-hover-right-border-color: var(--colors-brand-5);
  --select-base-hover-right-border-width: var(--borders-width-2);
  --select-base-hover-right-border-style: var(--borders-style-2);
  --select-base-hover-bottom-border-color: var(--colors-brand-5);
  --select-base-hover-bottom-border-width: var(--borders-width-2);
  --select-base-hover-bottom-border-style: var(--borders-style-2);
  --select-base-hover-left-border-color: var(--colors-brand-5);
  --select-base-hover-left-border-width: var(--borders-width-2);
  --select-base-hover-left-border-style: var(--borders-style-2);
  --select-base-hover-bg-color: var(--colors-neutral-fill-11);
  --select-base-hover-option-color: var(--colors-neutral-text-2);
  --select-base-hover-option-bg-color: var(--colors-neutral-fill-10);
  --select-base-active-top-border-color: var(--colors-brand-5);
  --select-base-active-top-border-width: var(--borders-width-2);
  --select-base-active-top-border-style: var(--borders-style-2);
  --select-base-active-right-border-color: var(--colors-brand-5);
  --select-base-active-right-border-width: var(--borders-width-2);
  --select-base-active-right-border-style: var(--borders-style-2);
  --select-base-active-bottom-border-color: var(--colors-brand-5);
  --select-base-active-bottom-border-width: var(--borders-width-2);
  --select-base-active-bottom-border-style: var(--borders-style-2);
  --select-base-active-left-border-color: var(--colors-brand-5);
  --select-base-active-left-border-width: var(--borders-width-2);
  --select-base-active-left-border-style: var(--borders-style-2);
  --select-base-active-shadow: var(--shadows-shadow-none);
  --select-base-active-bg-color: var(--colors-neutral-fill-11);
  --select-base-active-option-color: var(--colors-brand-5);
  --select-base-active-option-bg-color: var(--colors-neutral-fill-11);
  --select-base-disabled-top-border-color: var(--colors-neutral-line-8);
  --select-base-disabled-top-border-width: var(--borders-width-2);
  --select-base-disabled-top-border-style: var(--borders-style-2);
  --select-base-disabled-right-border-color: var(--colors-neutral-line-8);
  --select-base-disabled-right-border-width: var(--borders-width-2);
  --select-base-disabled-right-border-style: var(--borders-style-2);
  --select-base-disabled-bottom-border-color: var(--colors-neutral-line-8);
  --select-base-disabled-bottom-border-width: var(--borders-width-2);
  --select-base-disabled-bottom-border-style: var(--borders-style-2);
  --select-base-disabled-left-border-color: var(--colors-neutral-line-8);
  --select-base-disabled-left-border-width: var(--borders-width-2);
  --select-base-disabled-left-border-style: var(--borders-style-2);
  --select-base-disabled-bg-color: var(--colors-neutral-fill-10);
  --select-base-disabled-option-color: var(--colors-neutral-text-6);
  --select-base-disabled-option-bg-color: var(--colors-neutral-fill-11);
  --select-multiple-top-right-border-radius: var(--borders-radius-2);
  --select-multiple-top-left-border-radius: var(--borders-radius-2);
  --select-multiple-bottom-right-border-radius: var(--borders-radius-2);
  --select-multiple-bottom-left-border-radius: var(--borders-radius-2);
  --select-multiple-paddingTop: var(--sizes-size-0);
  --select-multiple-paddingBottom: var(--sizes-size-0);
  --select-multiple-paddingLeft: var(--sizes-size-3);
  --select-multiple-paddingRight: var(--sizes-size-3);
  --select-multiple-marginTop: var(--sizes-size-0);
  --select-multiple-marginBottom: var(--sizes-size-0);
  --select-multiple-marginLeft: var(--sizes-size-0);
  --select-multiple-marginRight: var(--sizes-size-3);
  --select-multiple-color: var(--colors-neutral-text-2);
  --select-multiple-fontSize: var(--fonts-size-8);
  --select-multiple-fontWeight: var(--fonts-weight-6);
  --select-multiple-bg-color: var(--colors-neutral-fill-10);
  --select-multiple-hover-bg-color: var(--colors-brand-10);
  --select-multiple-icon-color: var(--colors-neutral-text-6);
  --select-multiple-icon-hover-color: var(--colors-neutral-text-2);
  --select-group-color: var(--colors-neutral-text-5);
  --select-group-fontSize: var(--fonts-size-7);
  --select-group-fontWeight: var(--fonts-weight-6);
  --select-group-lineHeight: var(--fonts-lineHeight-2);
  --select-group-paddingTop: var(--sizes-size-3);
  --select-group-paddingBottom: var(--sizes-size-3);
  --select-group-paddingLeft: var(--sizes-size-7);
  --select-group-paddingRight: var(--sizes-size-7);
  --select-table-header-paddingTop: var(--sizes-size-5);
  --select-table-header-paddingBottom: var(--sizes-size-5);
  --select-table-header-paddingLeft: var(--sizes-size-7);
  --select-table-header-paddingRight: var(--sizes-base-9);
  --select-table-option-paddingTop: var(--sizes-size-4);
  --select-table-option-paddingBottom: var(--sizes-size-5);
  --select-table-option-paddingLeft: var(--sizes-size-7);
  --select-table-option-paddingRight: var(--sizes-base-9);
  --select-table-color: var(--colors-neutral-text-2);
  --select-table-fontSize: var(--fonts-size-8);
  --select-tree-color: var(--colors-neutral-text-2);
  --select-tree-fontSize: var(--fonts-size-7);
  --select-tree-hover-bg-color: var(--colors-neutral-fill-10);
  --select-tree-active-bg-color: var(--colors-brand-13);

  --Form-select-bg: var(--select-base-default-bg-color);
  --Form-select-height: var(--Form-select-outer-top);
  --Form-select-borderColor: var(--select-base-default-top-border-color)
    var(--select-base-default-right-border-color)
    var(--select-base-default-bottom-border-color)
    var(--select-base-default-left-border-color);
  --Form-select-borderRadius: var(--select-base-default-top-left-border-radius)
    var(--select-base-default-top-right-border-radius)
    var(--select-base-default-bottom-right-border-radius)
    var(--select-base-default-bottom-left-border-radius);
  --Form-select-borderWidth: var(--select-base-default-top-border-width)
    var(--select-base-default-right-border-width)
    var(--select-base-default-bottom-border-width)
    var(--select-base-default-left-border-width);
  --Form-select-caret-iconColor: var(--colors-neutral-text-6);
  --Form-select-caret-onHover-iconColor: var(--colors-neutral-text-5);
  --Form-select-caret-fontSize: var(--fonts-size-8);
  --Form-select-checkall-bottomBorder: #eceff8;
  --Form-select-color: var(--select-base-default-color);
  --Form-select-input-fontSize: var(--fontSizeSm);
  --Form-select-menu-padding: var(--sizes-base-2);
  --Form-select-menu-bg: var(--colors-neutral-fill-11);
  --Form-select-menu-color: var(--colors-neutral-text-2);
  --Form-select-menu-height: var(--sizes-base-12);
  --Form-select-menu-onActive-bg: var(--select-base-active-option-bg-color);
  --Form-select-menu-onActive-color: var(--select-base-active-option-color);
  --Form-select-menu-onDisabled-bg: var(--select-base-disabled-option-bg-color);
  --Form-select-menu-onDisabled-color: var(--select-base-disabled-option-color);
  --Form-select-menu-onHover-bg: var(--select-base-hover-option-bg-color);
  --Form-select-menu-onHover-color: var(--select-base-hover-option-color);
  --Form-select-group-color: var(--Form-select-caret-iconColor);
  --Form-select-onError-borderColor: var(--Form-input-onError-borderColor);
  --Form-select-onFocused-borderColor: var(--Form-input-onFocused-borderColor);
  --Form-select-onFocused-color: var(--Form-select-color);
  --Form-select-onHover-bg: var(--select-base-hover-bg-color);
  --Form-select-onHover-borderColor: var(--colors-brand-5);
  --Form-select-outer-borderWidth: var(--borders-width-1);
  --Form-select-outer-top: var(--sizes-base-16);
  --Form-select-outer-boxShadow: var(--shadows-shadow-normal);
  --Form-select-paddingX: var(--Form-input-paddingX);
  --Form-select-placeholderColor: var(--Form-input-placeholderColor);
  --Form-select-popoverGap: var(--borders-radius-3);
  --Form-select-icon-left: var(--sizes-size-4);
  --Form-select-search-height: var(--sizes-base-15);
  --Form-select-value-bgColor: var(--select-multiple-bg-color);
  --Form-select-value-bgColor--dark: var(--colors-neutral-fill-4);
  --Form-select-value-borderColor: var(--colors-neutral-line-9);
  --Form-select-valueIcon-color: var(--select-multiple-icon-color);
  --Form-select-valueIcon-color--dark: var(--colors-neutral-text-8);
  --Form-select-valueIcon-onHover-color: var(
    --select-multiple-icon-hover-color
  );
  --Form-select-multiple-bgColor: var(--colors-neutral-fill-10);
  --Form-selectOption-height: var(--Form-select-height);
  --Form-selectValue-bg: #{saturate(lighten($info, 40%), 2.5%)};
  --Form-selectValue-onHover-bgColor: var(--select-multiple-hover-bg-color);
  --Form-selectValue-borderColor: var(--colors-brand-7);
  --Form-selectValue-color: var(--colors-brand-5);
  --Form-selectValue-fontSize: var(--select-multiple-fontSize);
  --Form-selectValue-onDisable-bg: #{lighten(
      saturate(lighten($info, 40%), 2.5%),
      5%
    )};
  --Form-selectValue-onHover-bg: #{darken(
      saturate(lighten($info, 40%), 2.5%),
      5%
    )};
  --Form-selectValue-onDisabled-color: var(--Form-select-caret-iconColor);
  --Form-selectValue-onInvalid-color: var(--danger);
  --Form-valueLabel-maxWidth: #{px2rem(120px)};
  --Form-select-onFocus-boxShadow: none;
  --ResultBox-tag-height: #{px2rem(22px)};
  --ResultBox-tag-marginBottom: var(--select-multiple-marginBottom);
  --ResultBox-icon--onDisabled-color: #ebebeb;
  --ResultBox-icon--onHover-color: var(--select-multiple-icon-hover-color);
  --ResultBox-icon-color: var(--select-multiple-icon-color);
  --ResultBox-value--onDisabled-color: #cccccc;
  --ResultBox-value--onHover-bg: var(--select-multiple-hover-bg-color);
  --ResultBox-value--onHover-bg--dark: #b8babf;
  --ResultBox-value-bg: var(--select-multiple-bg-color);
  --ResultBox-value-color: var(--select-multiple-color);
  --ResultBox-value-clear-bg: var(--colors-neutral-fill-8);
  --ResultBox-value-clear-hover-bg: var(--colors-neutral-fill-9);
  --Tree-max-height: 300px;
  --Tree-indent: var(--gap-md);
  --Tree-icon-gap: var(--sizes-size-5);
  --Tree-icon-margin-right: #{px2rem(8px)};
  --Tree-inputHeight: calc(var(--Form-input-height) * 0.85);
  --Tree-item-onHover-bg: var(--colors-neutral-fill-10);
  --Tree-item-onHover-bg-pure: var(--select-tree-hover-bg-color);
  --Tree-itemArrowWidth: #{px2rem(16px)};
  --Tree-itemHeight: var(--sizes-base-12);
  --Tree-itemLabel--onChecked-color: var(--Form-selectValue-color);
  --TreeSelect-popover-bg: var(--colors-neutral-fill-11);
  --Tree-item-text-max-height: #{px2rem(250px)};
  --Tree-item-text-top: #{px2rem(4px)};
  --Tree-item-arrow-padding-left: #{px2rem(4px)};
  --Tree-item-arrow-color: #84868c;
  --Tree-item-onChekced-bg: var(--select-tree-active-bg-color);
  --Tree-item-onChekced-bg-borderRadius: var(--borders-radius-2);

  --inputDate-default-top-border-color: var(--colors-neutral-line-8);
  --inputDate-default-top-border-width: var(--borders-width-2);
  --inputDate-default-top-border-style: var(--borders-style-2);
  --inputDate-default-right-border-color: var(--colors-neutral-line-8);
  --inputDate-default-right-border-width: var(--borders-width-2);
  --inputDate-default-right-border-style: var(--borders-style-2);
  --inputDate-default-bottom-border-color: var(--colors-neutral-line-8);
  --inputDate-default-bottom-border-width: var(--borders-width-2);
  --inputDate-default-bottom-border-style: var(--borders-style-2);
  --inputDate-default-left-border-color: var(--colors-neutral-line-8);
  --inputDate-default-left-border-width: var(--borders-width-2);
  --inputDate-default-left-border-style: var(--borders-style-2);
  --inputDate-default-top-right-border-radius: var(--borders-radius-3);
  --inputDate-default-top-left-border-radius: var(--borders-radius-3);
  --inputDate-default-bottom-right-border-radius: var(--borders-radius-3);
  --inputDate-default-bottom-left-border-radius: var(--borders-radius-3);
  --inputDate-default-paddingTop: var(--sizes-size-3);
  --inputDate-default-paddingBottom: var(--sizes-size-3);
  --inputDate-default-paddingLeft: var(--sizes-size-6);
  --inputDate-default-paddingRight: var(--sizes-base-6);
  --inputDate-default-fontSize: var(--fonts-size-7);
  --inputDate-default-fontWeight: var(--fonts-weight-6);
  --inputDate-default-height: var(--sizes-base-16);
  --inputDate-default-color: var(--colors-neutral-text-2);
  --inputDate-default-bg-color: var(--colors-neutral-fill-11);
  --inputDate-default-icon: '<svg viewBox="0 0 13 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><g transform="translate(-1.338385, -2.000000)"><rect x="0" y="0" width="16" height="16"></rect><path d="M6,2 L6,3 L10,3 L10,2 L11,2 L11,3 L14,3 L14,14 L2,14 L2,3 L5,3 L5,2 L6,2 Z M13,7 L3,7 L3,13 L13,13 L13,7 Z M5,4 L3,4 L3,6 L13,6 L13,4 L11,4 L11,5 L10,5 L10,4 L6,4 L6,5 L5,5 L5,4 Z" fill="currentColor"></path></g></g></svg>';
  --inputDate-default-icon-color: var(--colors-neutral-text-5);
  --inputDate-default-icon-size: var(--sizes-base-7);
  --inputDate-default-title-color: var(--colors-neutral-text-2);
  --inputDate-default-title-arrow-color: var(--colors-neutral-text-5);
  --inputDate-default-option-color: var(--colors-neutral-text-2);
  --inputDate-default-option-bg-color: var(--colors-neutral-fill-11);
  --inputDate-default-option-today-border-color: var(--colors-brand-5);
  --inputDate-default-option-top-right-border-radius: var(--borders-radius-2);
  --inputDate-default-option-top-left-border-radius: var(--borders-radius-2);
  --inputDate-default-option-bottom-right-border-radius: var(
    --borders-radius-2
  );
  --inputDate-default-option-bottom-left-border-radius: var(--borders-radius-2);
  --inputDate-hover-top-border-color: var(--colors-brand-5);
  --inputDate-hover-top-border-width: var(--borders-width-2);
  --inputDate-hover-top-border-style: var(--borders-style-2);
  --inputDate-hover-right-border-color: var(--colors-brand-5);
  --inputDate-hover-right-border-width: var(--borders-width-2);
  --inputDate-hover-right-border-style: var(--borders-style-2);
  --inputDate-hover-bottom-border-color: var(--colors-brand-5);
  --inputDate-hover-bottom-border-width: var(--borders-width-2);
  --inputDate-hover-bottom-border-style: var(--borders-style-2);
  --inputDate-hover-left-border-color: var(--colors-brand-5);
  --inputDate-hover-left-border-width: var(--borders-width-2);
  --inputDate-hover-left-border-style: var(--borders-style-2);
  --inputDate-hover-color: var(--colors-neutral-text-2);
  --inputDate-hover-bg-color: var(--colors-neutral-fill-11);
  --inputDate-hover-title-color: var(--colors-brand-6);
  --inputDate-hover-title-arrow-color: var(--colors-neutral-text-2);
  --inputDate-hover-option-color: var(--colors-neutral-text-2);
  --inputDate-hover-option-bg-color: var(--colors-neutral-fill-10);
  --inputDate-active-top-border-color: var(--colors-brand-5);
  --inputDate-active-top-border-width: var(--borders-width-2);
  --inputDate-active-top-border-style: var(--borders-style-2);
  --inputDate-active-right-border-color: var(--colors-brand-5);
  --inputDate-active-right-border-width: var(--borders-width-2);
  --inputDate-active-right-border-style: var(--borders-style-2);
  --inputDate-active-bottom-border-color: var(--colors-brand-5);
  --inputDate-active-bottom-border-width: var(--borders-width-2);
  --inputDate-active-bottom-border-style: var(--borders-style-2);
  --inputDate-active-left-border-color: var(--colors-brand-5);
  --inputDate-active-left-border-width: var(--borders-width-2);
  --inputDate-active-left-border-style: var(--borders-style-2);
  --inputDate-active-shadow: var(--shadows-shadow-none);
  --inputDate-active-color: var(--colors-neutral-text-2);
  --inputDate-active-bg-color: var(--colors-neutral-fill-11);
  --inputDate-active-option-color: var(--colors-neutral-text-11);
  --inputDate-active-option-bg-color: var(--colors-brand-5);
  --inputDate-disabled-top-border-color: var(--colors-neutral-line-8);
  --inputDate-disabled-top-border-width: var(--borders-width-2);
  --inputDate-disabled-top-border-style: var(--borders-style-2);
  --inputDate-disabled-right-border-color: var(--colors-neutral-line-8);
  --inputDate-disabled-right-border-width: var(--borders-width-2);
  --inputDate-disabled-right-border-style: var(--borders-style-2);
  --inputDate-disabled-bottom-border-color: var(--colors-neutral-line-8);
  --inputDate-disabled-bottom-border-width: var(--borders-width-2);
  --inputDate-disabled-bottom-border-style: var(--borders-style-2);
  --inputDate-disabled-left-border-color: var(--colors-neutral-line-8);
  --inputDate-disabled-left-border-width: var(--borders-width-2);
  --inputDate-disabled-left-border-style: var(--borders-style-2);
  --inputDate-disabled-color: var(--colors-neutral-text-2);
  --inputDate-disabled-bg-color: var(--colors-neutral-fill-10);
  --inputDate-disabled-option-color: var(--colors-neutral-text-6);
  --inputDate-disabled-option-bg-color: var(--colors-neutral-fill-11);
  --inputDate-other-color: var(--colors-neutral-text-2);
  --inputDate-other-bg-color: var(--colors-neutral-fill-11);
  --inputDate-other-top-right-border-radius: var(--borders-radius-2);
  --inputDate-other-top-left-border-radius: var(--borders-radius-2);
  --inputDate-other-bottom-right-border-radius: var(--borders-radius-2);
  --inputDate-other-bottom-left-border-radius: var(--borders-radius-2);
  --inputDate-other-hover-color: var(--colors-neutral-text-2);
  --inputDate-other-hover-bg-color: var(--colors-neutral-fill-10);
  --inputDate-other-active-color: var(--colors-neutral-text-11);
  --inputDate-other-active-bg-color: var(--colors-brand-5);
  --inputDate-other-disabled-color: var(--colors-neutral-text-6);
  --inputDate-other-disabled-bg-color: var(--colors-neutral-fill-10);
  --inputDate-range-line-height: var(--borders-width-3);
  --inputDate-range-line-color: var(--colors-brand-4);
  --inputDate-range-separator-width: var(--sizes-size-5);
  --inputDate-range-separator-margin: var(--sizes-size-5);
  --inputDate-range-separator-color: var(--colors-neutral-fill-6);
  --inputDate-range-between-color: var(--colors-brand-10);

  --DatePicker-bg: var(--inputDate-default-bg-color);
  --DatePicker-borderColor: var(--inputDate-default-top-border-color)
    var(--inputDate-default-right-border-color)
    var(--inputDate-default-bottom-border-color)
    var(--inputDate-default-left-border-color);
  --DatePicker-borderStyle: var(--inputDate-default-top-border-style)
    var(--inputDate-default-right-border-style)
    var(--inputDate-default-bottom-border-style)
    var(--inputDate-default-left-border-style);
  --DatePicker-borderWidth: var(--inputDate-default-top-border-width)
    var(--inputDate-default-right-border-width)
    var(--inputDate-default-bottom-border-width)
    var(--inputDate-default-left-border-width);
  --DatePicker-borderRadius: var(--inputDate-default-top-left-border-radius)
    var(--inputDate-default-top-right-border-radius)
    var(--inputDate-default-bottom-right-border-radius)
    var(--inputDate-default-bottom-left-border-radius);
  --DatePicker-color: var(--inputDate-default-color);
  --DatePicker-header-onHover-color: var(--inputDate-hover-title-color);
  --DatePicker-arrow-color: var(--inputDate-default-title-arrow-color);
  --DatePicker-fontSize: var(--inputDate-default-fontSize);
  --DatePicker-header-select-borderColor: #fff;
  --DatePicker-height: var(--inputDate-default-height);
  --DatePicker-iconColor: var(--icon-color);
  --DatePicker-lineHeight: var(--Form-input-lineHeight);
  --DatePicker-onFocused-borderColor: var(--inputDate-active-top-border-color)
    var(--inputDate-active-right-border-color)
    var(--inputDate-active-bottom-border-color)
    var(--inputDate-active-left-border-color);
  --DatePicker-onHover-bg: var(--inputDate-hover-bg-color);
  --DatePicker-onHover-borderColor: var(--inputDate-hover-top-border-color)
    var(--inputDate-hover-right-border-color)
    var(--inputDate-hover-bottom-border-color)
    var(--inputDate-hover-left-border-color);
  --DatePicker-onDisabled-bg: var(--colors-neutral-text-9);
  --DatePicker-onDisabled-color: var(--colors-neutral-text-6);
  --DatePicker-onHover-iconColor: var(--colors-brand-5);
  --DatePicker-paddingX: #{px2rem(12px)};
  --DatePicker-paddingY: var(--sizes-size-3);
  --DatePicker-placeholderColor: var(--colors-neutral-text-6);
  --DatePicker-minWidth: calc(
    var(--fontSizeLg) * 5 + var(--DatePicker-paddingX) * 2 +
      var(--Form-input-clearBtn-size) * 2
  );
  --DateRangePicker-minWidth: calc(
    var(--fontSizeLg) * 8 + var(--DatePicker-paddingX) * 2 +
      var(--Form-input-clearBtn-size) * 2
  );
  --DateRangePicker-activeCursor-color: var(--inputDate-range-line-color);
  --DateRangePicker-activeCursor-height: var(--inputDate-range-line-height);
  --Calendar-btn-bg: var(--info);
  --Calendar-btn-border: var(--Calendar-btn-bg);
  --Calendar-btn-borderRadius: var(--Button-borderRadius);
  --Calendar-btn-color: var(--colors-neutral-fill-11);
  --Calendar-btn-fontSize: var(--fontSizeSm);
  --Calendar-btn-height: #{px2rem(30px)};
  --Calendar-btn-lineHeight: var(--lineHeightBase);
  --Calendar-btn-onActive-bg: var(--colors-brand-4);
  --Calendar-btn-onActive-border: var(--colors-brand-3);
  --Calendar-btn-onActive-color: var(--Calendar-btn-color);
  --Calendar-btn-onHover-bg: var(--colors-brand-4);
  --Calendar-btn-onHover-border: var(--colors-brand-3);
  --Calendar-btn-onHover-color: var(--Calendar-btn-color);
  --Calendar-btn-paddingX: #{px2rem(10px)};
  --Calendar-btn-paddingY: calc(
    (
        var(--Calendar-btn-height) - var(--Calendar-btn-lineHeight) *
          var(--Calendar-btn-fontSize)
      ) / 2
  );
  --Calendar-btnCancel-bg: var(--light);
  --Calendar-btnCancel-border: var(--colors-neutral-line-7);
  --Calendar-btnCancel-borderRadius: var(--borders-radius-3);
  --Calendar-btnCancel-color: var(--text-color);
  --Calendar-btnCancel-onActive-bg: var(--colors-neutral-fill-11);
  --Calendar-btnCancel-onActive-border: var(--colors-brand-4);
  --Calendar-btnCancel-onActive-color: var(--colors-brand-4);
  --Calendar-btnCancel-onHover-bg: var(--colors-neutral-fill-11);
  --Calendar-btnCancel-onHover-border: var(--colors-brand-6);
  --Calendar-btnCancel-onHover-color: var(--colors-brand-6);
  --Calendar-cell-bg: var(--inputDate-default-option-bg-color);
  --Calendar-cell-onActive-bg: var(--inputDate-active-option-bg-color);
  --Calendar-cell-onBetween-bg: var(--inputDate-range-between-color);
  --Calendar-cell-onDisabled-bg: var(--inputDate-other-disabled-bg-color);
  --Calendar-cell-onHover-bg: var(--inputDate-hover-option-bg-color);
  --Calendar-color: var(--inputDate-default-option-color);
  --Calendar-fontSize: var(--fontSizeSm);
  --Calendar-input-borderColor: var(--borderColor);
  --Calendar-input-borderRadius: var(--borders-radius-3);
  --Calendar-input-color: var(--info);
  --Calendar-input-fontSize: var(--fontSizeBase);
  --Calendar-input-height: #{px2rem(40px)};
  --Calendar-input-lineHeight: var(--lineHeightBase);
  --Calendar-input-onFocused-borderColor: var(--info);
  --Calendar-input-paddingX: #{px2rem(10px)};
  --Calendar-input-paddingY: calc(
    (
        var(--Calendar-input-height) - var(--Calendar-input-lineHeight) *
          var(--Calendar-input-fontSize)
      ) / 2
  );
  --Calendar-shortcut-color: #151b26;
  --Calendar-shortcut-decoration: none;
  --Calendar-shortcut-onHover-color: var(--colors-brand-6);
  --Calendar-shortcut-onHover-decoration: none;
  --Calendar-shortcuts-bg: var(--colors-neutral-text-9);
  --Calendar-shortcuts-height: var(--sizes-size-9);
  --Calendar-wLabel-color: var(--colors-neutral-text-6);
  --Calendar-icon-bottom: #{px2rem(-4px)};
  --Calendar-icon-width: var(--sizes-size-6);
  --Calendar-icon-height: var(--sizes-size-6);
  --Calendar-borderWidth: var(--borders-width-2);
  --Calendar-borderColor: var(--inputDate-default-option-today-border-color);
  --Calendar-rdt-day: var(--sizes-base-49);
  --Calendar-schedule-content-padding: 0 var(--sizes-size-3);
  --Calendar-schedule-content-height: var(--sizes-base-10);
  --Calendar-schedule-content-color: var(--colors-neutral-text-11);

  --inputTime-default-icon: '<svg viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><title>ic_时间</title><g id="ic_时间" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"><circle id="1-FL" stroke="currentColor" cx="8" cy="8" r="6.5" stroke-width="1px" fill="none" stroke-linecap="butt" stroke-linejoin="round"/><polyline id="2-FLW" stroke="currentColor" points="7.87443646 5.5 7.87443646 8.53778873 5.5 9.28133409" stroke-width="1px" fill="none" stroke-linecap="butt" stroke-linejoin="round"/></g></svg>';
  --inputTime-default-fontSize: var(--fonts-size-8);
  --inputTime-default-fontWeight: var(--fonts-weight-6);
  --inputTime-default-color: var(--colors-neutral-text-2);
  --inputTime-default-bg-color: var(--colors-neutral-fill-11);
  --inputTime-hover-color: var(--colors-neutral-text-2);
  --inputTime-hover-bg-color: var(--colors-neutral-fill-10);
  --inputTime-active-color: var(--colors-neutral-text-2);
  --inputTime-active-bg-color: var(--colors-brand-10);

  --Condition-builder-group-line-h: var(--colors-neutral-line-14);
  --Condition-builder-body-bg: var(--colors-neutral-fill-13);
  --Condition-builder-arrow-bg: var(--colors-neutral-fill-9);
  --Condition-builder-select-bg: var(--colors-neutral-fill-9);
  --Condition-builder-text-color: var(--colors-neutral-text-16);
  --Condition-builder-text-hover: var(--colors-info-5);
  --Condition-builder-bg-hover: var(--colors-info-5);
  --Condition-builder-border: var(--colors-neutral-line-15);
  --Condition-builder-text-color-op: var(--colors-neutral-text-17);
  --Condition-builder-bg-holder: var(--colors-neutral-fill-14);
  --Condition-builder-box-shadow: var(--colors-neutral-line-16);
  --Condition-builder-box-shadow-hover: var(--colors-neutral-line-17);
  --Condition-builder-item-bg: var(--colors-neutral-fill-15);
}
