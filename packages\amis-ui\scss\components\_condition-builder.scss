// 条件组件水平布局样式
.#{$ns}CBGroup {
  font-size: var(--fontSizeSm);
  position: relative;
  border: 1px solid var(--Condition-builder-group-line-h);
  border-radius: 4px;
  display: flex;
  flex-direction: column;

  &-toolbarCondition {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    padding: 0 px2rem(10px) 0 0;
    position: relative;

    &-vertical {
      flex-direction: column;
    }

    &-horizontal {
      flex-direction: row;
    }

    &[draggable='true'] {
      cursor: grab;
    }

    &-arrow {
      top: 0;
      font-size: var(--fonts-size-7);
      width: px2rem(20px);
      height: px2rem(20px);
      border-radius: 50%;
      background: var(--Condition-builder-arrow-bg);
      text-align: center;
      color: var(--Condition-builder-text-color);
      cursor: pointer;
      transition: transform 0.3s ease;

      &.is-collapse {
        transform: rotate(180deg);
      }

      &:hover {
        color: var(--Condition-builder-text-hover);
      }
    }

    .#{$ns}Words-field {
      position: relative;
      border-radius: var(--Form-select-borderRadius);
      line-height: px2rem(22px);
    }

    .#{$ns}Words-field,
    .#{$ns}Select {
      font-size: px2rem(14px);
      height: px2rem(22px);
      width: px2rem(78px);
      background: var(--Condition-builder-arrow-bg);
      border: px2rem(15px);
      color: var(--Condition-builder-text-color);
      font-weight: 400;
      margin: px2rem(24px) 0;
      text-align: center;
      min-height: px2rem(22px);

      &:hover {
        color: $white;
        background: var(--Condition-builder-bg-hover);
      }

      &-arrow {
        display: none;
      }

      &-menu {
        padding: 0;

        > .#{$ns}Select-option {
          font-size: px2rem(12px);
          font-weight: 500;
          line-height: 2rem !important;
          text-align: center;
          padding: 0 !important;
        }
      }
    }

    &::before {
      content: ' ';
      position: absolute;
      left: 50%;
      top: 50%;
      width: 100%;
      transform: translate(-50%, -50%);
      border-top: 1px dashed var(--Condition-builder-border);
    }

    &-verticalLineStyle {
      padding-right: 0;

      .#{$ns}CBGroup-toolbarCondition-arrow {
        position: absolute;
        left: 5px;
        background: #d4e5ff;

        &:hover {
          background: #2468f2;
          color: $white;
        }
      }

      .#{$ns}CBGroup-toolbarCondition-group {
        width: px2rem(28px);
        height: px2rem(28px);
        line-height: px2rem(28px);
        position: relative;
        font-size: px2rem(12px);
        text-align: center;
        color: #0832a6;
        font-weight: 500;
        background: #d4e5ff;
        border-radius: var(--Form-select-borderRadius);

        &:hover {
          color: $white;
          background: #2468f2;
        }
      }

      &::before {
        content: ' ';
        position: absolute;
        top: px2rem(5px);
        left: px2rem(14px);
        bottom: px2rem(5px);
        transform: none;
        width: 2px;
        background-color: #d4e5ff;
      }
    }
  }

  &-body {
    position: relative;
    margin: 0 px2rem(10px);
    &-wrapper {
      flex: 1;
    }

    &-collapse {
      text-align: center;
      color: var(--Condition-builder-text-color-op);
      display: flex;
      justify-content: center;
      align-items: center;

      > span {
        padding: 0 10px;
        cursor: pointer;
      }

      &::before,
      &::after {
        content: ' ';
        height: 1px;
        background: var(--Condition-builder-body-bg);
        display: block;
        flex: 1;
      }
    }

    .is-dragging {
      display: none;
    }
  }

  &-body-wrapper {
    flex: 1;
  }

  &-toolbar {
    display: flex;
    flex-direction: row;
    padding-top: px2rem(8px);

    &[draggable='true'] {
      cursor: grab;
    }

    .#{$ns}Button {
      transition: padding var(--animation-duration);
      min-width: unset;

      svg {
        width: 10px;
        height: 10px;
        top: 0;
        margin-right: 5px;
      }
    }

    .#{$ns}CBGroup-toolbarConditionAdd {
      display: flex;
      align-items: center;
      .#{$ns}ButtonGroup {
        & > .cxd-Button:not(:last-child) {
          margin-right: px2rem(24px);
        }
      }
    }
  }
  .#{$ns}ResultBox {
    padding-right: px2rem(3px);
  }

  &-field,
  &-operator {
    position: relative;
    display: inline-block;
    margin: px2rem(3px);
    vertical-align: middle;
  }

  &-fieldCaret,
  &-operatorCaret {
    transition: transform var(--animation-duration) ease-out;
    margin: 5px;
    display: flex;
    color: var(--Form-select-caret-iconColor);
    &:hover {
      color: var(--Form-select-caret-onHover-iconColor);
    }

    > svg {
      width: px2rem(10px);
      height: px2rem(10px);
      top: 0;
    }
  }

  &-fieldInput.is-active &-fieldCaret,
  &-operatorInput.is-active &-operatorCaret {
    transform: rotate(180deg);
  }

  &-placeholder {
    color: var(--text--muted-color);
    position: relative;
    padding: 10px;
    background: var(--Condition-builder-bg-holder);
    border-radius: 5px;
    &.simple {
      margin-left: 0;
    }
  }

  &-verticalLineStyle {
    flex-direction: row;
    border: none;

    .#{$ns}CBGroupOrItem-body {
      margin-top: 0;
    }

    .#{$ns}CBGroup-body {
      // group第一个条件不显示横线
      > div:first-of-type {
        .#{$ns}CBGroup-toolbarCondition:not(.#{$ns}CBGroup-toolbarCondition-verticalLineStyle)::before {
          display: none;
        }
      }
    }

    .#{$ns}CBGroup-toolbar {
      margin-left: px2rem(6px);
    }

    .#{$ns}CBGroup-toolbarCondition {
      .#{$ns}Words-field,
      .#{$ns}Select {
        margin: px2rem(6px) 0;
      }
    }

    .#{$ns}CBGroup-toolbarCondition .#{$ns}Form-static:not(.is-noPaddingY-static) {
      padding: 0;
    }

    .#{$ns}CBGroup-toolbarCondition:not(.#{$ns}CBGroup-toolbarCondition-verticalLineStyle) .#{$ns}CBGroup-toolbarCondition-arrow {
      // 竖线风格下隐藏箭头
      display: none;
    }

  }
}

// 条件样式垂直布局样式
.#{$ns}CBGroup-vertical {
  font-size: var(--fontSizeSm);
  position: relative;
  display: flex;
  &-toolbarCondition {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 px2rem(10px) 0 0;
    flex-direction: column;

    &[draggable='true'] {
      cursor: grab;
    }

    &-arrow {
      position: absolute;
      top: 0;
      font-size: var(--fonts-size-7);
      width: px2rem(20px);
      height: px2rem(20px);
      border-radius: 50%;
      background: #d4e5ff;
      text-align: center;
      color: #0832a6;
      cursor: pointer;
      transition: transform 0.3s ease;

      &.is-collapse {
        transform: rotate(180deg);
      }

      &:hover {
        background: #2468f2;
        color: $white;
      }
    }

    .#{$ns}Words-field {
      position: relative;
      border-radius: var(--Form-select-borderRadius);
      line-height: px2rem(28px);
    }

    .#{$ns}Words-field,
    .#{$ns}Select {
      font-size: px2rem(12px);
      height: px2rem(28px);
      width: px2rem(28px);
      background: #d4e5ff;
      border: none;
      color: #0832a6;
      font-weight: 500;
      padding: 0;
      padding-left: px2rem(8px);
      min-height: px2rem(28px);

      &:hover {
        color: $white;
        background: #2468f2;
      }

      &-arrow {
        display: none;
      }

      &-menu {
        padding: 0;

        > .#{$ns}Select-option {
          font-size: px2rem(12px);
          font-weight: 500;
          line-height: 2rem !important;
          text-align: center;
          padding: 0 !important;
        }
      }
    }

    &::before {
      content: ' ';
      position: absolute;
      top: px2rem(5px);
      bottom: px2rem(5px);
      width: 2px;
      background-color: #d4e5ff;
    }
  }

  &-body {
    position: relative;
    &-wrapper {
      flex: 1;
    }

    &-collapse {
      text-align: center;
      color: var(--Condition-builder-text-color-op);
      display: flex;
      justify-content: center;
      align-items: center;

      > span {
        padding: 0 10px;
        cursor: pointer;
      }

      &::before,
      &::after {
        content: ' ';
        height: 1px;
        background: var(--Condition-builder-body-bg);
        display: block;
        flex: 1;
      }
    }

    .is-dragging {
      display: none;
    }
  }

  &-body-wrapper {
    flex: 1;
  }

  &-toolbar {
    display: flex;
    flex-direction: row;
    padding-top: px2rem(8px);

    &[draggable='true'] {
      cursor: grab;
    }

    .#{$ns}Button {
      transition: padding var(--animation-duration);
      min-width: unset;

      svg {
        width: 10px;
        height: 10px;
        top: 0;
        margin-right: 5px;
      }
    }

    .#{$ns}CBGroup-toolbarConditionAdd {
      display: flex;
      align-items: center;
      .#{$ns}ButtonGroup {
        & > .cxd-Button:not(:last-child) {
          margin-right: px2rem(24px);
        }
      }
    }
  }
  .#{$ns}ResultBox {
    padding-right: px2rem(3px);
  }

  &-field,
  &-operator {
    position: relative;
    display: inline-block;
    margin: px2rem(3px);
    vertical-align: middle;
  }

  &-fieldCaret,
  &-operatorCaret {
    transition: transform var(--animation-duration) ease-out;
    margin: 5px;
    display: flex;
    color: var(--Form-select-caret-iconColor);
    &:hover {
      color: var(--Form-select-caret-onHover-iconColor);
    }

    > svg {
      width: px2rem(10px);
      height: px2rem(10px);
      top: 0;
    }
  }

  &-fieldInput.is-active &-fieldCaret,
  &-operatorInput.is-active &-operatorCaret {
    transform: rotate(180deg);
  }

  &-placeholder {
    color: var(--text--muted-color);
    position: relative;
    padding: 10px;
    background: var(--Condition-builder-bg-holder);
    border-radius: 5px;
    &.simple {
      margin-left: 0;
    }
  }
}

.#{$ns}CBGroupHeader {
  display: flex;
  justify-content: flex-end;

  &-label {
    padding: 0 5px;
  }
}

.#{$ns}CBDelete {
  margin-left: 5px;
  font-size: var(--fonts-size-6);
  color: var(--Condition-builder-text-color-op);
}

.#{$ns}CBCopy {
  margin-left: 5px;
  font-size: var(--fonts-size-6);
  color: var(--Condition-builder-text-color-op);
  cursor: pointer;
}

.#{$ns}CBItem {
  width: 100%;
}

.#{$ns}ConditionBuilderControl.is-static {
  .#{$ns}CBGroupOrItem-body-item {
    padding-left: px2rem(12px);
  }

  .#{$ns}CBItem {
    display: flex;
    width: 100%;

    .#{$ns}CBItem-RenderCondiItemBody {
      margin-left: 0;
      margin-right: 0;

      .antd-Form-col {
        flex-basis: auto;
        flex-grow: 0;
        padding: 0;
      }

      .antd-Form-row {
        margin-left: 0;
        margin-right: 0;
      }
    }

    .#{$ns}Form-item {
      padding-left: 5px;
      padding-right: 5px;
      font-size: var(--Form-fontSize)
    }
    // font-size: var(--Form-fontSize)
  }
}

.#{$ns}CBGroupOrItem {
  position: relative;
  transition: box-shadow 0.3s ease;

  &.is-hover {
    box-shadow: var(--Condition-builder-box-shadow);
  }

  & + & {
    margin-top: px2rem(10px);
  }
  &-dragbar {
    cursor: move;
    width: 20px;
    margin-left: -5px;
    opacity: 0.6;
    text-align: center;
    transition: opacity var(--animation-duration) ease-out;
    @include icon-color();
  }

  &-body {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    transition: all var(--animation-duration) ease-out;
    margin-top: px2rem(6px);
    &-group {
      width: 100%;
      flex-direction: row;
      display: flex;
      align-items: center;
      transition: all 0.2s ease;

      &.is-hover {
        cursor: grab;
        box-shadow: 0px 2px 14px 0px var(--Condition-builder-box-shadow-hover);
        border-radius: 8px;
        padding: 10px;
        margin: -10px 0px;
        background: $white;
        z-index: 1;
      }
      > .#{$ns}CBGroupOrItem-dragbar {
        left: px2rem(-5px);
        position: absolute;
      }
      > .#{$ns}CBGroup {
        margin: 0px;
      }
    }

    &-item {
      background-color: var(--Condition-builder-item-bg);
      width: 100%;
      padding: px2rem(12px);
      padding-left: px2rem(28px);
      display: flex;
      flex-direction: row;
      align-items: center;
      > .#{$ns}CBGroupOrItem-dragbar {
        left: px2rem(10px);
        position: absolute;
      }

      .antd-Form-item {
        margin-bottom: 0px;
      }
    }
  }

  // &.is-dragging {
  //   display: none;
  // }

  &.is-ghost > &-body:before {
    position: absolute;
    z-index: 2;
    content: '';
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba($info, 0.2);
  }

  .#{$ns}CBGroup {
    flex-grow: 1;
  }

  & > &-body > &-body-group > &-dragbar,
  & > &-body > &-body-item > &-dragbar,
  & > &-body > &-body-item > .#{$ns}CBDelete {
    opacity: 0;
  }

  & > &-body > &-body-group > &-dragbar,
  & > &-body > &-body-item > &-dragbar,
  & > &-body > &-body-item > .#{$ns}CBCopy {
    opacity: 0;
  }

  &:hover > &-body > &-body-item > &-dragbar,
  &:hover > &-body > &-body-item > .#{$ns}CBDelete {
    opacity: 1;
  }

  &:hover > &-body > &-body-item > &-dragbar,
  &:hover > &-body > &-body-item > .#{$ns}CBCopy {
    opacity: 1;
  }

  &-simple {
    margin-bottom: var(--gap-sm);
  }
}

.#{$ns}CBInputSwitch {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  cursor: pointer;
  > a {
    @include icon-color();
  }

  svg {
    width: px2rem(10px);
    height: px2rem(10px);
  }
}

.#{$ns}CBFunc {
  display: inline-block;
  vertical-align: middle;
  margin: px2rem(3px);

  &-select {
    display: inline-block;
    position: relative;
  }

  &-error {
    color: var(--danger);
  }

  &-args {
    display: inline-block;
    > span {
      display: inline-block;
      padding: 0 5px;
      color: var(--info);
    }

    > div {
      display: inline-block;
    }
  }
}

.#{$ns}CBValue {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin: px2rem(3px);
}

.#{$ns}CBFormula {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin: px2rem(3px);

  &-label {
    background: var(--ResultBox-value-bg);
    color: var(--text--muted-color);
    display: block;
    font-size: var(--fontSizeSm);
    align-self: center;
    margin: -5px 5px -5px -8px;
    padding: 5px;
    border-radius: 5px;
    user-select: none;
  }
}

.#{$ns}CBSeprator {
  width: 20px;
  text-align: center;
  display: inline-block;
  user-select: none;
  line-height: px2rem(32px);
}

.#{$ns}CBPicker-trigger {
  cursor: pointer;
  transition: transform var(--animation-duration) ease-out;
  display: flex;
  color: var(--Form-select-caret-iconColor);

  &:hover {
    color: var(--primary);
  }
}
