.#{$ns}Crud {
  position: relative;

  .#{$ns}Tabs {
    background-color: #ffffff;
    padding: var(--Table-contentWrap-paddingX) var(--Table-contentWrap-paddingX) 0;

    .#{$ns}Tabs-pane {
      padding: 0;

      .#{$ns}Table-contentWrap {
        padding: px2rem(16px) 0 0;
      }

      // fix: issue#538 表头固顶时样式问题
      .#{$ns}Table-fixedTop {
        .#{$ns}Table-wrapper {
          padding: px2rem(16px) 0 0;
        }

        &:after {
          width: 100%;
          margin-left: 0;
        }

        & ~ .#{$ns}Table-contentWrap {
          padding-top: 0;
        }
      }
    }
  }

  &.is-loading > &-body {
    // 弹框中，blur 效果变成白班，先去掉。
    // -webkit-filter: blur(5px);
    // filter: blur(5px);
  }

  &-body {
    .#{$ns}Table-headToolbar {
      padding: var(--Table-toolbar-marginY) var(--sizes-base-8) px2rem(1px);
    }

    .#{$ns}Table-searchHeader {
      background: var(--Table-bg);
      padding: var(--Table-contentWrap-paddingX) var(--Table-contentWrap-paddingX) 0;

      // 列搜索中配置combo的白色背景会覆盖29行的背景，combo设置成透明背景
      .#{$ns}Combo-item {
        background: transparent;
      }
    }

    .#{$ns}Table-contentWrap {
      padding: var(--Table-contentWrap-paddingX) var(--Table-contentWrap-paddingX);
    }

    // fix: issue#538 表头固顶时样式问题
    .#{$ns}Table-fixedTop {
      .#{$ns}Table-wrapper {
        padding: var(--Table-contentWrap-paddingX) var(--Table-contentWrap-paddingX) 0;
      }

      &:after {
        width: calc(100% - 2 * var(--Table-contentWrap-paddingX));
        margin-left: var(--Table-contentWrap-paddingX);
      }

      & ~ .#{$ns}Table-contentWrap {
        padding-top: 0;
      }
    }

    &.no-padding {
      .#{$ns}Table-contentWrap {
        padding: 0;

        .#{$ns}Table-fixedLeft.in {
          padding-left: 0;
        }

        .#{$ns}Table-fixedRight.in {
          padding-right: 0;
        }
      }

      // fix: issue#538 表头固顶时样式问题
      .#{$ns}Table-fixedTop {
        .#{$ns}Table-wrapper {
          padding: 0;
        }

        &:after {
          width: 100%;
          margin-left: 0;
        }

        & ~ .#{$ns}Table-contentWrap {
          padding-top: 0;
        }
      }
    }

    &.#{$ns}Cards {
      padding: var(--Table-toolbar-marginY) var(--sizes-base-8);
      background: var(--Table-bg);

      .#{$ns}Cards-toolbar-header {
        padding-bottom: var(--Table-contentWrap-paddingX);
      }

      .#{$ns}Cards-toolbar-footer .#{$ns}Crud-toolbar {
        display: inline-flex;
        align-items: center;
        justify-content: flex-end;
      }
    }

    .#{$ns}Cards-body {
      padding-bottom: var(--Table-contentWrap-paddingX)
    }

    .#{$ns}Cards-toolbar {
      margin-bottom: 0;
    }
  }

  &-selection {
    padding: var(--Table-contentWrap-paddingX) var(--Table-contentWrap-paddingX) 0;
    display: flex;

    &-list,
    &-cards {
      padding: 0 0 var(--Table-contentWrap-paddingX) 0;
    }
  }

  &-selectionLabel {
    display: inline-block;
    vertical-align: top;
    margin-top: var(--gap-xs);
  }

  &-selectionContent {
    flex: 1;
  }

  &-value {
    cursor: pointer;
    vertical-align: middle;
    user-select: none;
    line-height: calc(
      var(--Form-input-lineHeight) * var(--Form-input-fontSize) - #{px2rem(2px)}
    );
    display: inline-block;
    font-size: var(--Form-selectValue-fontSize);
    color: var(--Form-selectValue-color);
    background: var(--Table--unsaved-heading-bg);
    border-radius: 2px;
    margin-right: var(--gap-xs);
    margin-top: var(--gap-xs);

    &:hover {
      background: var(--Form-selectValue-onHover-bg);
    }

    &.is-disabled {
      pointer-events: none;
      opacity: var(--Button-onDisabled-opacity);
    }
  }

  &-valueIcon {
    cursor: pointer;
    padding: 1px 5px;

    &:hover {
      background: var(--Form-selectValue-onHover-bg);
    }
  }

  &-valueLabel {
    padding: 0 var(--gap-xs);
  }

  &-selectionClear {
    display: inline-block;
    cursor: pointer;
    user-select: none;
    margin-left: var(--gap-xs);
    margin-top: var(--gap-xs);
    vertical-align: middle;
  }

  &-toolbar-item {
    margin-top: var(--Crud-toolbar-gap);
    line-height: var(--Crud-toolbar-lineHeight);
    height: var(--Crud-toolbar-height);
    vertical-align: middle;
    display: inline-flex;
    align-items: center;

    &--left:not(:first-child) {
      margin-left: var(--Crud-toolbar-gap);
    }

    &--right:not(:last-child) {
      margin-left: var(--Crud-toolbar-gap);
    }

    &--left {
      float: left;
    }

    &--right {
      float: right;
    }
  }

  &-actions {
    > * + .#{$ns}Button,
    > * + .#{$ns}ButtonGroup,
    > * + .#{$ns}ButtonToolbar {
      margin-left: var(--Crud-toolbar-gap);
    }
  }

  &-statistics {
    line-height: var(--Crud-toolbar-height);
    vertical-align: middle;
    color: var(--colors-neutral-text-8);
    margin-right: px2rem(8px);
  }

  &-pageSwitch {
    display: flex;
    flex-flow: row wrap;
    align-items: center;

    .#{$ns}Select {
      margin-left: var(--Crud-toolbar-gap);
    }
  }

  &-pager {
    align-self: flex-start;
  }

  &-filter {
    margin-bottom: var(--gap-base);
    border: 0;
    box-shadow: var(--Crud-filter-boxShadow);
    border-radius: var(--Crud-filter-radius);

    .#{$ns}Panel-body {
      padding-bottom: 0;
    }

    .#{$ns}Panel-footerWrap {
      margin: var(--sizes-base-8) var(--sizes-base-8) 0 var(--sizes-base-8);
      border-top: var(--borders-width-2) var(--borders-style-2)
        var(--colors-brand-12);
    }

    .#{$ns}Panel-footer {
      border: none;
      padding: var(--sizes-base-8) 0;
    }

    .#{$ns}Form-group--hor {
      margin-left: 0;
      margin-right: 0;

      .#{$ns}Form-label {
        display: inline-flex;
        flex-direction: row;
        align-items: center;
        justify-content: end;

        > span {
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-all;
          white-space: nowrap;
        }
      }
    }

    .#{$ns}Form-groupColumn {
      margin-bottom: calc(var(--Form-item-gap) / 3 * 2);
    }
  }

  &-advance-search {
    .#{$ns}Form-group--hor {
      margin-bottom: 0;
    }
  }

  &-header-filter {
    margin-bottom: 0;
    border: none;
    display: block;
    box-shadow: none;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    padding: var(--Table-toolbar-marginY) var(--sizes-base-8) 1px;

    .#{$ns}Panel-body {
      padding: 0;
    }
  }

  &-top-header {
    background-color: var(--Table-bg);
    padding: var(--gap-base) 0 0 var(--gap-base);

    // 顶部工具栏，按钮间距 8px
    .#{$ns}Button {
      margin-right: var(--Panel-footerButtonMarginLeft);
    }
  }

  // 底部 crud toolbar元素 全部靠右显示
  .#{$ns}Table-footToolbar {
    .#{$ns}Crud-toolbar {
      display: inline-flex;
      align-items: center;
      justify-content: flex-end;

      &-item {
        color: var(--Pagination-light-color);
      }
    }
  }

  &.compact-mode {

    .#{$ns}Form-groupColumn {
      margin-bottom: 0;
    }

    .#{$ns}Form-item--horizontal > .#{$ns}Form-label {
      margin-right: 8px;
    }

    .#{$ns}Table-searchableForm .#{$ns}Form-group--horizontal > .#{$ns}Form-groupColumn {
      margin-bottom: 12px !important;

      &:last-child {
        margin-bottom: 0 !important;
      }
    }

    .#{$ns}Crud-top-header,
    .#{$ns}Panel-body,
    .#{$ns}Crud-body .#{$ns}Table-headToolbar {
      padding-top: 8px;
    }

    .#{$ns}Table-contentWrap {
      padding: 8px;
    }

    // fix: issue#538 表头固顶时样式问题
    .#{$ns}Table-fixedTop {
      .#{$ns}Table-wrapper {
        padding: 8px 8px 0;
      }

      &:after {
        width: calc(100% - 2 * 8px);
        margin-left: 8px;
      }

      & ~ .#{$ns}Table-contentWrap {
        padding-top: 0;
      }
    }

    .#{$ns}Panel-footerWrap {
      margin-top: 0;
    }

    .#{$ns}Table-footToolbar {
      padding: 8px 0;
    }

    .#{$ns}Panel-btnToolbar {
      padding: 8px 0;
    }

    .#{$ns}Table-table > thead > tr > th {
      padding: 7px;
    }

    .#{$ns}Table-table > tbody > tr > td {
      padding: 0 7px;
    }

    .ant-picker-range {
      width: 100%;
    }
  }

  &.compact-mode-v2 {
    .#{$ns}Crud-body {
      .#{$ns}Table-headToolbar {
        padding: var(--Table-toolbar-marginY-small) var(--sizes-base-4) px2rem(1px);
      }

      .#{$ns}Table-searchHeader {
        padding: var(--Table-contentWrap-paddingX-small) var(--Table-contentWrap-paddingX-small) 0;
      }

      .#{$ns}Table-contentWrap {
        padding: var(--Table-contentWrap-paddingX-small) var(--Table-contentWrap-paddingX-small);
      }

      // fix: issue#538 表头固顶时样式问题
      .#{$ns}Table-fixedTop {
        .#{$ns}Table-wrapper {
          padding: var(--Table-contentWrap-paddingX-small) var(--Table-contentWrap-paddingX-small) 0;
        }

        &:after {
          width: calc(100% - 2 * var(--Table-contentWrap-paddingX-small));
          margin-left: var(--Table-contentWrap-paddingX-small);
        }

        & ~ .#{$ns}Table-contentWrap {
          padding-top: 0;
        }
      }
    }


    .#{$ns}Crud-selection {
      margin-bottom: calc(var(--gap-base) / 2); // todo
    }

    .#{$ns}Crud-actions {
      > * + .#{$ns}Button,
      > * + .#{$ns}ButtonGroup,
      > * + .#{$ns}ButtonToolbar {
        margin-left: calc(var(--Crud-toolbar-gap) / 2);
      }
    }

    .#{$ns}Crud-pageSwitch {
      .#{$ns}Select {
        margin-left: calc(var(--Crud-toolbar-gap) / 2);
      }
    }

    .#{$ns}Crud-filter {
      margin-bottom: calc(var(--gap-base) / 2);

      .#{$ns}Panel-footerWrap {
        margin: var(--sizes-base-4) var(--sizes-base-4) 0 var(--sizes-base-4);
      }

      .#{$ns}Panel-footer {
        border: none;
        padding: var(--sizes-base-4) 0;
      }

      .#{$ns}Form-groupColumn {
        margin-bottom: calc(var(--Form-item-gap) / 3);
      }
    }

    .#{$ns}Crud-header-filter {
      padding: var(--Table-toolbar-marginY-small) var(--sizes-base-4) 1px;
    }

    .#{$ns}Crud-top-header {
      padding-top: calc(var(--gap-base) / 2);
      padding-left: calc(var(--gap-base) / 2);
    }
    .#{$ns}Table {
      margin-bottom: var(--gap-sm);

      // 搜索展示在表头
      &-searchHeader {
        &-item {
          margin-left: calc(var(--gap-base) / 2);
          margin-bottom: calc(var(--gap-xs) / 2);
        }

        &-item .content .#{$ns}Form-item--horizontal {
          padding: 0 calc(var(--gap-xs) / 2);
        }
      }

      .#{$ns}Form-control > & {
        margin-bottom: var(--gap-sm);
      }

      &-fixedLeft {
        &.in {
          padding-left: var(--Table-contentWrap-paddingX-small);
        }

        & > .#{$ns}Table-table {
          > thead > tr > th:last-child,
          > tbody > tr > td:last-child {
            padding-right: var(--TableCell-paddingX-small);
          }

        }
      }

      &-fixedRight {

        &.in {
          padding-right: var(--Table-contentWrap-paddingX-small);
        }

        & > .#{$ns}Table-table {
          > thead > tr > th:first-child,
          > tbody > tr > td:first-child {
            padding-left: var(--TableCell-paddingX-small);
          }
        }
      }

      &-heading {
        padding: calc(
            (
                var(--Table-heading-height) - var(--Table-fontSize) *
                  var(--lineHeightBase)
              ) / 4
          )
          var(--gap-xs);
      }

      &-searchableForm {
        &-footer {
          padding: var(--sizes-base-4) var(--sizes-size-4);
        }

        .#{$ns}Panel-body {
          padding: var(--sizes-base-4);
          padding-bottom: 0;
        }

        .#{$ns}Panel-footerWrap {
          margin: var(--sizes-base-4) var(--sizes-base-4) 0 var(--sizes-base-4);
        }

        .#{$ns}Form-group--horizontal > .#{$ns}Form-groupColumn {
          margin-bottom: calc(var(--Form-item-gap) / 3);
        }
      }

      &-header {
        padding: var(--Table-toolbar-marginY-small) var(--Table-toolbar-marginX-small);

        > * + .#{$ns}Button,
        > * + .#{$ns}ButtonGroup,
        > * + .#{$ns}ButtonToolbar {
          margin-left: calc(var(--Crud-toolbar-gap) / 2);
        }
      }

      &-toolbar {
        padding: var(--Table-toolbar-marginY-small) var(--Table-toolbar-marginX-small);

        .#{$ns}Pagination > li > a, .#{$ns}Pagination > li > span {
          height: auto;
          min-width: 1.5rem;
          line-height: 1.5rem;
          // padding: 0 8px;
        }
        .#{$ns}Pagination-perpage {
          padding: 5px 8px;
          line-height: 1.5rem;
          min-height: calc(var(--Form-selectOption-height) / 2);
        }

        .#{$ns}Pagination-inputGroup input {
          height: px2rem(26px);
          line-height: px2rem(26px);
        }
      }

      .#{$ns}PopOver {
        .#{$ns}Panel-body {
          padding: var(--TableCell-paddingY-small) var(--Table-contentWrap-paddingX-small);
        }
      }

      &-actions {
        > * {
          margin-right: calc(var(--Crud-toolbar-gap) / 2);
        }
      }

      &-table {

        &--withCombine {
          > thead > tr > th,
          > tbody > tr > td {
            &:first-child {
              padding-left: var(--TableCell-paddingX-small) !important;
            }

            &:last-child {
              padding-right: var(--TableCell-paddingX-small) !important;
            }
          }
        }

        > thead > tr {
          > th {
            padding: var(--TableCell-paddingY-small)
              var(--TableCell-paddingX-small);

            &:first-child {
              padding-left: --TableCell-paddingX-small;

              &.#{$ns}Table-checkCell {
                padding-left: calc(var(--TableCell--edge-paddingX) / 2);
              }
            }

            &:last-child {
              padding-right: --TableCell-paddingX-small;
            }
          }
        }

        > tbody > tr {
          > td,
          > th {
            padding: var(--TableCell-paddingY-small)
              var(--TableCell-paddingX-small);

            &:first-child {
              padding-left: calc(var(--TableCell--edge-paddingX) / 2);
            }

            &:last-child {
              padding-right: calc(var(--TableCell--edge-paddingX) / 2);
            }
          }
        }

        @for $i from 2 through 10 {
          tr.#{$ns}Table-tr--#{$i}th.is-expanded {
            .#{$ns}Table-expandCell:before {
              right: px2rem(14px) + px2rem(-16px) * ($i - 1);
            }
          }

          tr.#{$ns}Table-tr--#{$i}th {
            .#{$ns}Table-expandBtn {
              right: -(px2rem(16px)) * ($i - 1);
            }

            .#{$ns}Table-expandCell + td {

              &::before {
                left: px2rem(-15px) + px2rem(16px) * ($i - 2);
                height: auto;
              }

              &::after {
                top: 50%;
                left: px2rem(-15px) + px2rem(16px) * ($i - 2);
                width: px2rem(10px);
              }

              padding-left: px2rem(16px) * $i;
            }
          }
        }

        > tbody > tr > td.#{$ns}Table-expandCell {
          @for $i from 1 through 7 {
            .#{$ns}Table-divider-#{$i} {
              right: px2rem(14px) + px2rem(-16px) * ($i - 1);
            }
          }
        }

        > tbody > tr.is-expanded > td.#{$ns}Table-expandCell {
          &::before {
            right: px2rem(14px);
          }
        }

        > thead > tr > th.#{$ns}Table-checkCell,
        > tbody > tr > td.#{$ns}Table-checkCell {
          padding-right: var(--TableCell-paddingX-small);
        }
      }

      &-itemActions { // todo
        top: var(--Table-borderWidth);
        padding-left: px2rem(80px);
        padding-right: var(--TableCell-paddingX);

        a {
          padding: var(--gap-xs) var(--gap-sm);
        }
      }

      &-footTable {
        > tbody > tr > th {
          width: px2rem(120px); // todo
          padding: var(--TableCell-paddingY-small) var(--TableCell-paddingX-small);
        }

        > tbody > tr > td {
          padding: var(--TableCell-paddingY-small) var(--TableCell-paddingX-small);
        }
      }
    }
  }
}

@include media-breakpoint-up(sm) {
  .#{$ns}Crud {
    &-toolbar {
      margin-top: calc(var(--Crud-toolbar-gap) * -1);
      flex-basis: 0;
      flex-grow: 1;
      @include clearfix();
    }

    &-toolbar-item {
      line-height: var(--Crud-toolbar-lineHeight);
      height: var(--Crud-toolbar-height);
      vertical-align: middle;

      &--left:not(:first-child) {
        margin-left: var(--Crud-toolbar-gap);
      }

      &--right:not(:last-child) {
        margin-left: var(--Crud-toolbar-gap);
      }

      &--left {
        float: left;
      }

      &--right {
        float: right;
      }
    }

    &-actions {
      > * + .#{$ns}Button,
      > * + .#{$ns}Button--disabled-wrap {
        margin-left: var(--Crud-toolbar-gap);
      }
    }
  }
}
