@keyframes disappear {
  to {
    opacity: 0;
  }
}

@keyframes appear {
  from {
    opacity: 0;
  }
}

.#{$ns}ImageGallery {
  display: flex;
  flex-direction: column;
  background: transparent;
  border: none;
  border-radius: 0;
  max-width: 1010px !important;

  &-close {
    position: absolute;
    right: 0;
    top: 0;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;

    &:hover {
      color: #fff;
    }

    > svg {
      width: px2rem(16px);
      height: px2rem(16px);
    }
  }

  &-title,
  &-caption {
    height: px2rem(30px);
    vertical-align: top;
    line-height: px2rem(30px);
    font-size: px2rem(12px);
    color: var(--white);
    text-align: center;
  }

  &-caption {
    height: auto;
    text-align: left;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }

  &-main {
    background: #000;
    flex-basis: 0;
    flex-grow: 1;
    height: 0;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    user-select: none;
    overflow: hidden;

    > img {
      display: block;
      max-width: 100%;
      max-height: 100%;
      transition: transform 0.3s cubic-bezier(0, 0, 0.25, 1) 0s;
      cursor: move;
      cursor: -webkit-grab;
    }

    &.is-dragging > img {
      transition: none;
      user-select: none;
      cursor: -webkit-grabbing;
    }

    .pdfWrap {
      width: 100%;
      align-self: flex-start;
      // 处理pdf没滚动条情况垂直居中问题
      margin: auto;

      // react-pdf预览组件居中
      .react-pdf__Document {
        .react-pdf__Page {
          width: fit-content;
          margin: 0 auto;
        }

        // react-pdf预览Document组件loading居中
        .react-pdf__message--loading {
          text-align: center;
        }
      }
    }

    // 处理富文本的预览
    &.no-bg {
      background: transparent;
    }

    .#{$ns}RichTextControl--preview {
      height: 100%;

      .static-iframe {
        height: 100%;
      }
    }
  }

  &-prevBtn,
  &-nextBtn {
    > svg {
      width: px2rem(48px);
      height: px2rem(48px);
    }

    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #999;
    text-shadow: rgba(0, 0, 0, 0.3) 0px 0px 4px;

    &:hover {
      color: #fff;
      text-shadow: rgba(0, 0, 0, 0.5) 0px 0px 4px;
    }
    animation-name: disappear;
    animation-delay: var(--animation-duration);
    animation-duration: var(--animation-duration);
    animation-fill-mode: both;

    &.is-disabled {
      pointer-events: none;
    }
  }

  &-main:hover &-prevBtn,
  &-main:hover &-nextBtn {
    animation-name: appear;
    animation-delay: 0s;
    animation-duration: var(--animation-duration);
  }

  &-prevBtn {
    left: var(--gap-md);
  }

  &-nextBtn {
    right: var(--gap-md);
  }

  &-footer {
    height: px2rem(74px);
    background: #222;
    display: flex;
    flex-direction: row;
    user-select: none;
  }

  &-prevList,
  &-nextList {
    width: var(--gap-md);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    background: rgba(0, 0, 0, 0.3);
    color: #fff;

    &.is-disabled {
      background: rgba(0, 0, 0, 0.3);
      color: rgba(255, 255, 255, 0.1);
      pointer-events: none;
    }

    &:hover {
      background: rgba(0, 0, 0, 1);
      color: #fff;
    }
  }

  &-itemsWrap {
    flex-grow: 1;
    flex-basis: 0;
    width: 0;
    overflow: hidden;
    align-items: center;
    justify-content: center;
    display: flex;
  }

  &-items {
    display: inline-block;
    white-space: nowrap;
  }

  &-item {
    margin: 10px 5px;
    width: 54px;
    height: 54px;
    display: inline-flex;
    position: relative;
    border: 1px solid #666;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    > img {
      display: block;
      max-width: 100%;
      max-height: 100%;
    }

    @supports (object-fit: cover) {
      > img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    &:after {
      position: absolute;
      content: '';
      display: block;
      background: rgba(0, 0, 0, 0.5);
      z-index: 1;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }

    &:hover {
      border: 1px solid #e5e5e5;
      &:after {
        display: none;
      }
    }

    &.is-active {
      border: 1px solid var(--primary);
      &:after {
        display: none;
      }
    }
  }
}

.#{$ns}ImageGallery-toolbar {
  background-color: var(--white);
  border-radius: px2rem(4px);
  display: flex;
  align-items: flex-start;
  padding: px2rem(4px) px2rem(16px);
  position: absolute;
  bottom: px2rem(20px);
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  align-items: center;

  &-action {
    padding: px2rem(13px);
    border-radius: px2rem(4px);
    width: px2rem(40px);
    height: px2rem(40px);
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;

    &-icon {
      display: block;
      color: var(--black);

      & > svg {
        fill: var(--black);
      }
    }

    &.is-disabled {
      cursor: not-allowed;
      color: var(--icon-onDisabled-color);

      .#{$ns}ImageGallery-toolbar-action-icon {
        color: var(--icon-onDisabled-color);

        & > svg {
          color: var(--icon-onDisabled-color);
        }
      }
    }

    &:hover {
      background-color: #f2f3f5;

      .#{$ns}ImageGallery-toolbar-action-icon {
        color: var(--primary);

        & > svg {
          fill: var(--primary);
        }
      }
    }
  }
}
