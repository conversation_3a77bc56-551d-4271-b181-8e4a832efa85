.#{$ns}Table {
  position: relative;

  border-radius: var(--Table-borderRadius);
  margin-bottom: var(--gap-md);

  // 搜索展示在表头
  &-searchHeader {
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    &-wrapper {
      flex: 1;
      display: flex;
      flex-direction: row;

      > div {
        flex: 1;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
      }
    }

    &-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      background: var(--Table--unsaved-heading-bg);
      margin-right: var(--gap-xs);
      margin-bottom: var(--gap-xs);
      border: var(--Table-thead-borderWidth) solid
        var(--Table--unsaved-heading-bg);
      border-radius: var(--Table-borderRadius);
      font-size: var(--fontSizeSm);
    }

    &-item .close {
      margin: 0 var(--gap-xs);
      color: var(--Table--unsaved-heading-color);
      height: var(--ColumnToggler-lineHeight);
      line-height: var(--ColumnToggler-lineHeight);
      cursor: pointer;
    }

    &-item .content .form {
      height: var(--ColumnToggler-lineHeight);
      line-height: var(--ColumnToggler-lineHeight);
    }

    &-item .content .#{$ns}Form-item--horizontal {
      height: var(--ColumnToggler-lineHeight);
      line-height: var(--ColumnToggler-lineHeight);
      align-items: center;
      padding: 0 var(--gap-xs);

      > label,
      > div {
        font-size: var(--fontSizeSm);
        color: var(--primary);
      }

      > label {
        margin-right: var(--gap-xs);
      }
    }

    &-reset {
      display: inline-block;
      cursor: pointer;
      user-select: none;
      margin-left: var(--gap-sm);
      margin-top: var(--gap-xs);
      vertical-align: middle;

      i {
        display: inline-block;
        font: normal normal normal 14px/1 FontAwesome;
        font-size: inherit;
        text-rendering: auto;
        -webkit-font-smoothing: antialiased;
        color: var(--colors-other-6);
      }
    }
  }

  // .#{$ns}Form-control > & {
  //   margin-bottom: var(--gap-md);
  // }

  .nested-input-table-row .#{$ns}Form-control > & {
    margin-bottom: -1px; // fix: inputTable嵌套时，嵌套的table最后一行高度问题
  }

  &-fixedTop {
    position: sticky;
    top: var(--affix-offset-top);
    background: var(--Table-bg);
    display: block;
    opacity: 1;
    z-index: $zindex-sticky;

    &:after {
      content: '';
      position: absolute;
      width: 100%;
      box-shadow: var(--Table-fixedTop-boxShadow);
      z-index: 30;
      height: 30px;
      top: 100%;
      pointer-events: none;
      background-color: transparent;
      margin-top: -2px;
    }

    &.is-fakeHide {
      > .#{$ns}Table-wrapper {
        visibility: hidden;
        position: absolute;
      }

    }

    .#{$ns}Table-table {
      // fix: https://github.com/baidu/amis/pull/8332
      table-layout: fixed;
    }
  }

  &-headerContainer {
    background: var(--Table-bg);
  }

  &--headerWrap {
    display: flex;
    background: var(--Table-bg);

    .#{$ns}Table-headToolbar {
      padding-right: 0
    }

    .#{$ns}Table-searchHeader {
      line-height: 30px;
      flex:1;
      padding-left: 0;
    }
  }

  &-heading {
    background: var(--Table-heading-bg);
    padding: calc(
        (
            var(--Table-heading-height) - var(--Table-fontSize) *
              var(--lineHeightBase)
          ) / 2
      )
      var(--gap-sm);
  }

  &--unsaved &-heading {
    background: var(--Table--unsaved-heading-bg);
    color: var(--Table--unsaved-heading-color);
  }

  &-wrapper {
    overflow: hidden;
  }

  &-placeholder {
    color: var(--text--muted-color);
    text-align: center;
    height: var(--Table-placeholder-height);
    background: transparent !important;

    &:hover {
      color: var(--text--muted-color);
      background: transparent !important;
    }

    > td {
      vertical-align: middle !important;
      text-align: center;
    }

    &-empty-icon.icon {
      display: block;
      margin: 0 auto;
      width: var(--Table-empty-icon-size);
      height: var(--Table-empty-icon-size);
    }
  }

  &-searchableForm {
    background: var(--Table-searchableForm-backgroundColor);
    border-radius: var(--Table-searchableForm-borderRadius);
    margin-bottom: var(--Table-searchableForm-marginY);
    border: 0;
    box-shadow: var(--Table-searchableForm-boxShadow);

    &-footer {
      // padding: var(--Panel-footerPadding);
      padding: var(--sizes-base-8) var(--sizes-size-7);
      clear: both;
    }

    &-checkbox {
      &-inner {
        /* 消除checkbox自身的padding top */
        padding-top: 0 !important;
      }
    }

    .#{$ns}Panel-body {
      padding-bottom: 0;
    }

    .#{$ns}Panel-footerWrap {
      margin: var(--sizes-base-8) var(--sizes-base-8) 0 var(--sizes-base-8);
      border-top: var(--borders-width-2) var(--borders-style-2)
        var(--colors-brand-12);
    }

    .#{$ns}Form-item--horizontal > .#{$ns}Form-label {
      display: inline-flex;
      flex-direction: row;
      align-items: center;
      justify-content: end;

      > span {
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        white-space: nowrap;
      }
    }

    .#{$ns}Form-group--horizontal > .#{$ns}Form-groupColumn {
      margin-bottom: calc(var(--Form-item-gap) / 3 * 2);
    }

    .#{$ns}Form-group--hor {
      margin-bottom: 0;
    }
  }

  &-header {
    padding: var(--Table-toolbar-marginY) var(--Table-toolbar-marginX);

    > * + .#{$ns}Button,
    > * + .#{$ns}ButtonGroup,
    > * + .#{$ns}ButtonToolbar {
      margin-left: var(--Crud-toolbar-gap);
    }
  }

  &-toolbar {
    @include clearfix();
    display: flex;
    padding: var(--Table-toolbar-marginY) var(--Table-toolbar-marginX);
    flex-wrap: wrap;
    background: var(--Table-bg);
    // issue#129：去掉圆角
    // border-bottom-left-radius: var(--Table-borderRadius);
    // border-bottom-right-radius: var(--Table-borderRadius);

    .#{$ns}DropDown {
      &-menuItem {
        height: auto;

        .#{$ns}Checkbox {
          display: flex;
          align-items: center;
        }
      }
    }
  }

  &-header + &-toolbar {
    padding-top: 0;
  }

  &-contentWrap {
    position: relative;
    // padding-bottom: var(--Table-toolbar-marginY);
    background: var(--Table-bg);
    // issue#129：去掉圆角
    // border-top-left-radius: var(--Table-borderRadius);
    // border-top-right-radius: var(--Table-borderRadius);
  }

  &-actions {
    display: inline-block;

    > * {
      margin-right: var(--Crud-toolbar-gap);
    }
  }

  &-content {
    min-height: 0.01%;
    overflow-x: auto;
    transform: translateZ(0);

    // th {
    //   position: relative;
    // }
  }

  &-content-colDragLine {
    position: absolute;
    width: 7px;
    top: 0;
    bottom: 0;
    right: -4px;
    cursor: col-resize;
    user-select: none;
    opacity: 0.5;
    z-index: 15; // $zindex-top改成15，解决底层单元格被sticky单元格覆盖时还能拖动列宽的问题

    &--disabled {
      cursor: not-allowed;
    }

    &:hover,
    &.is-resizing {
      background: var(--primary);
    }
  }

  &-table {
    width: 100%;
    min-width: 100%;
    margin-bottom: 0;
    font-size: var(--Table-fontSize);
    color: var(--Table-color);
    background: var(--Table-bg);
    border-spacing: 0;
    border-collapse: separate;
    // border-bottom: var(--Table-borderWidth) solid var(--Table-borderColor);

    &.is-layout-fixed {
      table-layout: fixed !important;
    }

    & th,
    & td {
      text-align: left;
      border-color: transparent;
      border-bottom: var(--Table-borderWidth) solid var(--Table-borderColor);

      &.is-sticky {
        position: sticky !important;
        z-index: var(--TableCell-sticky-zIndex);
        background: inherit;
      }

      &.is-sticky-last-left:after {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        width: 30px;
        transform: translateX(100%);
        transition: box-shadow 0.3s;
        content: '';
        pointer-events: none;
      }

      &.is-sticky-first-right:after {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        width: 30px;
        transform: translateX(-100%);
        transition: box-shadow 0.3s;
        content: '';
        pointer-events: none;
      }
    }

    &.is-layout-fixed td {
      white-space: normal;
      word-break: break-all;
    }

    &.table-fixed-left .is-sticky-last-left:after {
      box-shadow: var(--Table-fixedLeft-boxShadow);
    }

    &.table-fixed-right .is-sticky-first-right:after {
      box-shadow: var(--Table-fixedRight-boxShadow);
    }

    & th {
      position: relative;
    }

    & th.text-center,
    & td.text-center,
    & th[colspan],
    & td[colspan] {
      text-align: center;
    }

    & th.text-right,
    & td.text-right {
      text-align: right;
    }

    & td .#{$ns}SwitchControl {
      padding-top: 0;
    }

    &--affixHeader {
      // fix: https://github.com/baidu/amis/issues/8708
      margin-top: calc(var(--Table-thead-height) * -1);

      // > 作用：防止嵌套子表格的表头被隐藏 https://github.com/baidu/amis/issues/7738
      > thead {
        visibility: hidden;
      }
    }

    &--withCombine {

      > thead > tr > th,
      > tbody > tr > td {
        &:first-child {
          padding-left: var(--TableCell-paddingX-middle) !important;
        }

        &:last-child {
          padding-right: var(--TableCell-paddingX-middle) !important;
        }
      }

      > thead > tr {
        border-top: var(--Table-borderWidth) solid var(--Table-borderColor) !important;
        border-left: var(--Table-borderWidth) solid var(--Table-borderColor) !important;
        border-right: var(--Table-borderWidth) solid var(--Table-borderColor) !important;
        & > th {
          border-right: var(--Table-borderWidth) solid var(--Table-borderColor) !important;
          border-bottom: var(--Table-borderWidth) solid var(--Table-borderColor) !important;
        }
      }

      // reset
      > tbody > tr {

        border-left: var(--Table-borderWidth) solid var(--Table-borderColor) !important;

        &:hover {
          background: transparent;
        }

        > td {
          vertical-align: middle;
        }

        > td {
          border-right: var(--Table-borderWidth) solid var(--Table-borderColor);
        }
      }
    }

    &--checkOnItemClick {
      > tbody > tr {
        cursor: pointer;
      }
    }

    > thead > tr {
      background: var(--Table-thead-bg);
      > th {
        background: inherit;
        padding: var(--TableCell-paddingY-middle)
          var(--TableCell-paddingX-middle);
        text-align: left;

        &:first-child {
          // padding-left: px2rem(12px);
          padding-left: var(--TableCell-paddingX-middle);

          &.#{$ns}Table-checkCell {
            padding-left: var(--TableCell--edge-paddingX);
          }
        }

        &:last-child {
          // padding-right: px2rem(12px);
          padding-right: var(--TableCell-paddingX-middle);

          .#{$ns}Table-content-colDragLine {
            // 避免出现横向滚动条
            width: 4px;
            right: 0;

            &::before {
              display: none;
            }
          }
        }

        &:not(:last-child) {
          position: relative;
          border-right: var(--Table-thead-borderWidth) solid
            var(--Table-thead-bg);

            &::before {
              content: "";
              width: 1px;
              height: var(--fontSizeXl);
              background-color: #F0F1F2;
              position: absolute;
              right: 0;
              top: 50%;
              transform: translateY(-50%)
            }
        }

        border-bottom: var(--Table-thead-borderWidth) solid
          var(--Table-thead-borderColor);

        font-size: var(--Table-thead-fontSize);
        color: var(--Table-thead-color);
        font-weight: var(--fontWeightBold);
        white-space: nowrap;

        // .#{$ns}TableCell--title {
        //   display: flex;
        //   align-items: center;
        // }
        .#{$ns}Remark {
          margin-left: var(--gap-xs);
        }

        .#{$ns}TableCell--title {
          min-width: fit-content;
          display: inline-block;
        }
      }
    }

    > thead > tr > th:first-child {
      border-top-left-radius: 4px;
    }

    > thead > tr > th:last-child {
      border-top-right-radius: 4px;
    }

    > thead > tr + tr {
      border-top: var(--Table-borderWidth) solid var(--Table-borderColor);
    }

    > thead > tr {
      border-bottom: var(--Table-borderWidth) solid var(--Table-borderColor);
    }

    > tbody > tr {
      position: relative;
      background: var(--Table-bg);

      & + tr {
        border-top: var(--Table-borderWidth) solid var(--Table-borderColor);

        > th {
          border-top: var(--Table-thead-borderWidth) solid
            var(--Table-thead-borderColor);
        }
      }

      > th {
        background: var(--Table-thead-bg);
        // font-size: var(--Table-thead-fontSize);
        color: var(--Table-thead-color);
        font-weight: var(--fontWeightBold);
        white-space: nowrap;
        border-right: var(--Table-thead-borderWidth) solid
          var(--Table-thead-borderColor);
      }

      > td,
      > th {
        padding: var(--TableCell-paddingY-middle) var(--TableCell-paddingX-middle);
        position: relative; // fix: 修复Safari中table的角标配置定位错误问题，https://github.com/baidu/amis/pull/10179

        &:first-child {
          padding-left: var(--TableCell--edge-paddingX);
        }

        &:last-child {
          padding-right: var(--TableCell--edge-paddingX);
        }
      }

      &.#{$ns}Table-tr--hasItemAction:hover {
        cursor: pointer;
      }

      &:hover,
      &.is-hovered {
        background: var(--Table-onHover-bg);
        border-color: var(--Table-onHover-borderColor);
        color: var(--Table-onHover-color);

        & + tr {
          border-color: var(--Table-onHover-borderColor);
        }
      }

      &:active {
        background: var(--Table-onHover-bg);
      }

      &:hover.#{$ns}Table-placeholder {
        color: var(--text--muted-color);
      }

      &.is-checked {
        background: var(--Table-onChecked-bg);
        border-color: var(--Table-onChecked-borderColor);
        color: var(--Table-onChecked-color);

        & + tr {
          border-color: var(--Table-onChecked-borderColor);
        }
      }

      &.is-moved,
      &.is-modified {
        background: var(--Table-onModified-bg);
        border-color: var(--Table-onModified-borderColor);
        color: var(--Table-onModified-color);

        & + tr {
          border-color: var(--Table-onModified-borderColor);
        }
      }

      &.is-summary {
        // font-weight: var(--fontWeightNormal);

        .#{$ns}Table-cell--inner {
          font-size: 0; // 总结行中内置的ckeck/expand/drag列不需要显示任何内容，只是为了填充占位
        }
      }

      &.bg-default {
        @include color-variant($light, 2%, 3%, 3%, 5%);
        color: $text-color;
      }

      &.bg-dark {
        @include color-variant($dark, 5%, 10%, 5%, 10%);
        @include font-variant($dark);
      }

      &.bg-black {
        @include color-variant($black, 5%, 10%, 5%, 10%);
        @include font-variant($black);
      }

      &.bg-primary {
        @include color-variant($primary, 5%, 10%, 5%, 10%);
        @include font-variant($primary);
      }

      &.bg-success {
        @include color-variant($success, 5%, 10%, 5%, 10%);
        @include font-variant($success);
      }

      &.bg-info {
        @include color-variant($info, 5%, 10%, 5%, 10%);
        @include font-variant($info);
      }

      &.bg-warning {
        @include color-variant($warning, 5%, 10%, 5%, 10%);
        @include font-variant($warning);
      }

      &.bg-danger {
        @include color-variant($danger, 5%, 10%, 5%, 10%);
        @include font-variant($danger);
      }

      &.is-dragging {
        opacity: var(--Table-onDragging-opacity);
      }
    }

    > tbody > tr > td > div > .#{$ns}PlainField {
      display: inline-block;
      word-break: break-all;
    }

    .#{$ns}Table-divider2 {
      content: '';
      position: absolute;
      height: px2rem(1px);
      top: 50%;
      width: px2rem(10px);
      background: var(--Table-tree-borderColor);
    }

    .#{$ns}Table-divider3 {
      position: absolute;
      width: px2rem(1px);
      top: 0;
      bottom: 0;
      height: 100%;
      background: var(--Table-tree-borderColor);
    }

    @for $i from 2 through 10 {
      tr.#{$ns}Table-tr--#{$i}th.is-expanded {
        .#{$ns}Table-expandCell:before {
          // right: px2rem(7px) + px2rem(-16px) * ($i - 1);
          left: px2rem(23px) + px2rem(18px) * ($i - 1);
        }
      }

      tr.#{$ns}Table-tr--#{$i}th {
        .#{$ns}Table-expandBtn {
          position: relative;
          // right: -(px2rem(16px)) * ($i - 1);
          left: px2rem(1px) + (px2rem(18px)) * ($i - 1);
        }

        .#{$ns}Table-divider2 {
          left: px2rem(5px) + (px2rem(18px)) * ($i - 1);
        }

        .#{$ns}Table-divider3 {
          left: px2rem(5px) + (px2rem(18px)) * ($i - 1);
        }

        .#{$ns}Table-expandCell + td {
          position: relative;
          padding-left: px2rem(16px) * $i;
        }
      }

      tr.#{$ns}Table-tr--#{$i}th.is-last .#{$ns}Table-divider3 {
        height: 50%;
      }

      tr.#{$ns}Table-tr--#{$i}th.is-last:not(.is-expanded) {
        .#{$ns}Table-expandCell + td {
          &::before {
            height: 50%;
            bottom: auto;
          }
        }
      }
    }

    > thead > tr > th.#{$ns}Table-checkCell,
    > tbody > tr > td.#{$ns}Table-checkCell {
      width: px2rem(1px);
      padding-right: var(--TableCell-paddingX);
      white-space: nowrap;

      .#{$ns}Checkbox {
        margin: 0;
      }
    }

    > tbody > tr > td.#{$ns}Table-checkCell {
      border-right: 0;
    }

    > thead > tr > th.#{$ns}Table-expandCell,
    > tbody > tr > td.#{$ns}Table-expandCell {
      border-right: 0;
      width: px2rem(1px);
      padding-right: 0;
    }

    > thead > tr > th.#{$ns}Table-dragCell,
    > tbody > tr > td.#{$ns}Table-dragCell {
      border-right: 0;
      width: px2rem(1px);
      padding-right: 0;
      cursor: move;

      > svg {
        vertical-align: middle;
        top: 0;
      }
    }

    > tbody > tr > td.#{$ns}Table-expandCell {
      position: relative;
      // 展开icon的层级要高于下一个td，不然会被覆盖
      z-index: calc(var(--TableCell-sticky-zIndex) + 1);

      @for $i from 1 through 7 {
        .#{$ns}Table-divider-#{$i} {
          position: absolute;
          width: px2rem(1px);
          top: 0;
          bottom: 0;
          height: 100%;
          background: var(--Table-tree-borderColor);
          // right: px2rem(7px) + px2rem(-16px) * ($i - 1);
          left: px2rem(23px) + px2rem(18px) * ($i - 1);
        }
      }
    }

    > tbody > tr.is-expanded > td.#{$ns}Table-expandCell {
      // position: relative;

      &::before {
        content: '';
        position: absolute;
        width: px2rem(1px);
        top: 50%;
        bottom: 0;
        // right: px2rem(7px);
        left: px2rem(23px);
        height: auto;
        background: var(--Table-tree-borderColor);
      }
    }

    > thead > tr > th .#{$ns}Form-new-star {
      vertical-align: middle;
      color: var(--Form-item-star-color);
      font-size: var(--Form-item-star-size);
      font-family: SimSun,sans-serif;
      line-height: 1;
      padding-inline-end: px2rem(4px);
    }

    > thead > tr > th.#{$ns}TableCell--sortable,
    > thead > tr > th.#{$ns}TableCell--searchable,
    > thead > tr > th.#{$ns}TableCell--filterable {
      > .#{$ns}TableCell--title {
        display: inline-block;
      }
    }

    > tbody > tr.sub-table-row:hover {
      background: transparent;
      // border-color: transparent;
    }
  }

  &Cell-sortBtn,
  &Cell-searchBtn,
  &Cell-filterBtn {
    display: inline-block;
    vertical-align: top;
    padding-left: var(--gap-sm);

    &:hover {
      color: var(--TableCell-searchBtn--onActive-color);
    }
  }

  &Cell-sortBtn {
    cursor: pointer;
    width: var(--TableCell-sortBtn-width);
    height: var(--gap-md);
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: var(--icon-color);

    &--up > svg,
    &--down > svg,
    &--default > svg {
      color: inherit;
      width: 12px;
      height: 12px;
    }

    &--up,
    &--down,
    &--default {
      display: none;
      position: absolute;
      z-index: 2;
      font-style: normal;

      &.is-active {
        display: inline-block;
      }
    }

    &--default {
      &.is-active {
        color: var(--text--muted-color);

        &:hover {
          color: var(--TableCell-searchBtn--onActive-color);
        }
      }
    }

    &--up,
    &--down {
      &.is-active {
        color: var(--TableCell-sortBtn--onActive-color);
      }
    }
  }

  &Cell-searchBtn {
    cursor: pointer;
    color: var(--text--muted-color);

    svg.icon {
      width: 12px;
      height: 12px;
    }

    &.is-active {
      color: var(--TableCell-searchBtn--onActive-color);
    }

    &.is-opened {
      color: var(--Button--default-onActive-border);
    }
  }

  &Cell-searchPopOver {
    border: none;
    min-width: px2rem(320px);
    max-width: px2rem(640px);

    .#{$ns}Panel {
      margin: 0;
    }
  }

  &Cell-filterBtn {
    cursor: pointer;
    width: var(--TableCell-filterBtn-width);
    color: var(--text--muted-color);

    svg.icon {
      width: 12px;
      height: 12px;
    }

    &.is-active {
      color: var(--TableCell-filterBtn--onActive-color);
    }

    .#{$ns}Remark {
      display: inline;
    }
  }

  &Cell-filterPopOver {
    border: none;
    width: px2rem(160px);

    .#{$ns}DropDown-menu {
      margin: 0;
      padding: 0;
      border-radius: 0;

      .#{$ns}DropDown-divider {
        height: var(--TableCell-filterPopOver-dropDownItem-height);
        line-height: var(--TableCell-filterPopOver-dropDownItem-height);
        padding: var(--TableCell-filterPopOver-dropDownItem-padding);
        background: var(--white);
        margin: 0;

        &:hover {
          background: var(--light);
          color: var(--primary);
        }

        &.is-selected {
          background: var(--light);
          color: var(--primary);
        }

        .#{$ns}Checkbox {
          width: 100%;
          margin: 0;
        }
      }
    }
  }

  &-itemActions-wrap {
    position: absolute;
    width: 100%;
    z-index: calc(var(--Table-fixed-zIndex) + 1);
    left: 0;
    top: 0;
    min-height: 30px;
    pointer-events: none;
    box-shadow: var(--Table-onHover-boxShadow);
  }

  &-itemActions {
    pointer-events: all;
    position: absolute;
    // background: var(--Table-onHover-bg);
    background: linear-gradient(
      90deg,
      rgba(var(--Table-onHover-bg-rgb), 0) 0%,
      rgba(var(--Table-onHover-bg-rgb), 1) 20%,
      rgba(var(--Table-onHover-bg-rgb), 1) 100%
    );
    top: var(--Table-borderWidth);
    bottom: 0;
    right: 0;
    padding-left: px2rem(80px);
    padding-right: var(--TableCell-paddingX);
    display: flex;
    align-items: center;

    a {
      cursor: pointer;
      padding: var(--gap-xs) var(--gap-sm);
      color: var(--link-color);
      text-decoration: var(--link-decoration);

      &:hover {
        color: var(--link-onHover-color);
        text-decoration: var(--link-onHover-decoration);
      }

      &.is-disabled {
        pointer-events: none;
        opacity: var(--Button-onDisabled-opacity);
        color: var(--text--muted-color);
      }
    }
  }

  &-dragTip {
    color: var(--text--loud-color);
    clear: both;
    margin-top: var(--gap-xs);
    width: 100%;
    color: var(--info);
  }

  &-foot {
    background: var(--Table-thead-bg);
  }

  &-footTable {
    position: relative;
    width: 100%;
    border-spacing: 0;
    border-collapse: collapse;
    margin-bottom: 0;
    background: transparent;

    > tbody > tr > th {
      width: px2rem(120px);
      text-align: right;
      padding: var(--TableCell-paddingY-middle) var(--TableCell-paddingX-middle);
    }

    > tbody > tr > td {
      word-break: break-all;
      padding: var(--TableCell-paddingY-middle) var(--TableCell-paddingX-middle);
    }

    > tbody > tr:not(:first-child) {
      border-top: var(--Table-borderWidth) solid
        var(--Table-tbody-borderTopColor);
    }
  }

  &-expandBtn {
    position: relative;
    z-index: 1;
    color: var(--Table-expandBtn-color);
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: px2rem(14px);
    line-height: 1;
    height: 16px;

    > svg {
      display: inline-block;
      text-align: center;
      cursor: pointer;
      transition: transform ease-in-out var(--animation-duration),
        top ease-in-out var(--animation-duration);
      position: relative;
      transform-origin: 50% 50%;
      width: px2rem(10px);
      height: px2rem(10px);
      top: 0;
      transform: rotate(90deg);
    }

    &.is-active > svg {
      transform: rotate(-90deg);
    }

    &:hover {
      text-decoration: none;
    }
  }

  &-dragBtn {
    margin-right: var(--gap-xs);
    display: inline-block;
    visibility: hidden;
    cursor: move;
    color: var(--icon-color);

    &:hover {
      text-decoration: none;
      color: var(--icon-onHover-color);
    }

    > svg {
      vertical-align: -2px;
    }
  }

  &-table > tbody > tr:hover .#{$ns}Table-dragBtn,
  &-table > tbody > tr.is-dragging .#{$ns}Table-dragBtn,
  &-table > tbody > tr.is-drop-allowed .#{$ns}Table-dragBtn {
    visibility: visible;
  }

  .fake-hide {
    visibility: hidden;
    position: absolute;
  }

  &-badge {
    position: absolute;
    top: 0;
    left: 0;
  }

  &--autoFillHeight {
    margin-bottom: 0;

    > .#{$ns}Table-contentWrap {
      > .#{$ns}Table-content table {
        border-top: none; // 不然会导致拖动时顶部露出内容
      }

      > .#{$ns}Table-content table thead {
        position: sticky; // 简单实现表头吸顶效果，不考虑 IE 11 不然太麻烦
        top: 0;
        z-index: 21; // 由于 badge 导致 tbody 里 tr 的 position: relative 了
      }
    }

    > .#{$ns}Table-footToolbar {
      margin-bottom: 0;
    }
  }

  &-SFToggler {
    color: var(--button-link-default-font-color);
    font-size: var(--fontSizeBase);
    margin-left: var(--gap-sm);
    display: inline-flex;
    cursor: pointer;

    &-arrow {
      width: var(--gap-md);
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 1;
      transform: rotate(90deg);

      > svg {
        transition: transform var(--animation-duration);
        display: inline-block;
        color: var(--button-link-default-font-color);
        width: 10px;
        height: 10px;
        top: 0;
      }
    }

    &.is-expanded {
      .#{$ns}Table-SFToggler-arrow > svg {
        transform: rotate(180deg);
      }
    }
  }

  // table 骨架样式
  &-emptyBlock {
    background-color: #eaebed;
    border-radius: 5px;
    line-height: 15px;
  }

  th.is-highlight,
  td.is-highlight {
    background: #F0F8FF;
    border-color: var(--Table-onHover-borderColor);
    color: #3388FF;
  }
}

.#{$ns}InputTable {
  .#{$ns}Field--quickEditable svg {
    color: var(--primary);

    &:hover {
      color: var(--primary-onHover);
    }

    &:active {
      color: var(--primary-onActive);
    }
  }
}

.#{$ns}InputTable {
  .#{$ns}Field--quickEditable svg {
    color: var(--primary);

    &:hover {
      color: var(--primary-onHover);
    }

    &:active {
      color: var(--primary-onActive);
    }
  }
}

.#{$ns}InputTable-toolbar {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;

  .#{$ns}Button {
    margin-right: var(--sizes-base-4);
  }
}

.#{$ns}InputTable-pager {
  margin-left: auto;
}

.#{$ns}OperationField {
  display: flex;
  align-items: center;
  margin: px2rem(-3px);

  > .#{$ns}Button,
  > .#{$ns}Button--disabled-wrap > .#{$ns}Button {
    margin: px2rem(3px);
  }

  > div > .#{$ns}DropDown > .#{$ns}Button--link {
    padding: 0;
    margin-right: px2rem(10px);
  }

  > .#{$ns}Button--disabled-wrap > .#{$ns}Button--link {
    padding: 0;
  }

  > .#{$ns}Button,
  > .#{$ns}DropDown > .#{$ns}Button {
    height: auto;
  }

  > .#{$ns}Button--link {
    padding: 0;
    margin-right: px2rem(10px);
    // position: relative;  // 不能加，和tooltip同时存在会引起样式问题

    &.operate-btn {
      + .#{$ns}DropDown {
        position: relative;

        &::before {
          position: absolute;
          display: block;
          left: -4px;
          top: 50%;
          content: '';
          width: 1px;
          height: 10px;
          background: $gray300;
          transform: translate(0, -50%);
        }
      }

      + .#{$ns}DropDown > .#{$ns}DropDown-menu-root {
        display: flex;
        flex-flow: row wrap;
        min-width: auto;
      }

      + .#{$ns}DropDown > .#{$ns}Button--link {
        padding: var(--button-size-default-paddingBottom);
      }
    }
  }

  .operate-btn-container {
    display: inline-block;

    // fix: crud的操作列操作按钮错位问题
    .#{$ns}Container-body {
      display: flex;
    }

    .#{$ns}Button--link {
      padding: 0;
      margin-right: px2rem(10px);
    }

    .#{$ns}Button,
    .#{$ns}Button--disabled-wrap > .#{$ns}Button {
      margin: px2rem(3px);
    }

    .#{$ns}Button--disabled-wrap > .#{$ns}Button--link {
      padding: 0;
    }

    .operate-btn-container-divider {
      position: relative;
      width: 1px;
      display: inline-block;
      vertical-align: middle;
      margin-left: px2rem(4px);

      &::before {
        position: absolute;
        display: block;
        left: -2px;
        top: 50%;
        content: '';
        width: 1px;
        height: 10px;
        background: $gray300;
        transform: translate(0, -50%);
      }
    }
  }

  .add-btns-wrap {
    display: block;
    line-height: normal;

    & > .#{$ns}Button {
      height: auto;
      margin: px2rem(3px);

      .icon {
        margin-right: 0;
      }
    }
  }

  .#{$ns}DropDown-menu-root {
    min-width: auto;
  }
}

.is-public-header {
  .sub-table-row {
    > td {
      padding: 0 !important;
    }

    .#{$ns}Table-table {
      border-bottom: none;
    }
  }

  .#{$ns}Table-table > tbody > tr.is-expanded > td.#{$ns}Table-expandCell::before {
    width: 0;
  }

  .#{$ns}Table-contentWrap {
    padding: 0;
  }
}

.sub-table-row {
  &.is-expandable {
    display: none;
  }

  &.is-expanded {
    display: table-row;
  }

  > td {
    padding-right: 0 !important;
  }

  .sub-Table {
    width: var(--SubTableCell-width-default);
    min-width: var(--SubTableCell-width-default);
    margin-left: var(--SubTableCell-marginLeft-default);

    &.sub-Table-expanded {
      display: block;
    }

    &.sub-Table-expandable {
      display: none;
    }
  }
}

.is-checkable + .sub-table-row.is-expandable .#{$ns}Table.sub-Table {
  width: var(--SubTableCell-checkable-width-default);
  min-width: var(--SubTableCell-checkable-width-default);
  margin-left: var(--SubTableCell-checkable-marginLeft-default);
}

.sub-Table .#{$ns}Table-placeholder {
  height: var(--SubTable-placeholder-height);
}
