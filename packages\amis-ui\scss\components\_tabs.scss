.#{$ns}Tabs {

  // 默认模式和加强默认默认整体背景色是白色，对齐交互规范
  &--line,
  &--strong {
    background: var(--Tabs-bg);
  }

  &-drag-tip {
    display: none;
  }

  &-linksWrapper {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    border-bottom: var(--Tabs-borderWidth) solid var(--Tabs-borderColor);
  }

  &-linksContainer-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .#{$ns}ComboTabs-addLink {
      cursor: pointer;

      & > a {
        color: var(--Tabs--card-hover-color);
      }

      &:hover > a {
        color: var(--primary);
        fill: var(--primary);
      }
    }

    // tabs溢出时外部添加按钮样式
    > .#{$ns}ComboTabs-addLink {
      margin-left: var(--Tabs-add-margin);
    }

    .#{$ns}Tabs-addable {
      display: flex;
      margin-left: var(--Tabs-add-margin);
      align-items: center;
      justify-content: flex-start;
      margin-bottom: px2rem(3px);
      padding-bottom: px2rem(8px);
      white-space: nowrap;
      cursor: pointer;
      &-reverse {
        margin-right: var(--Tabs-add-margin);
        margin-left: 0;
      }

      &-icon {
        width: var(--Tabs-add-icon-size);
        height: var(--Tabs-add-icon-size);
        padding: var(--Tabs-add-icon-padding);
        margin-right: var(--Tabs-add-icon-margin);
      }

      span {
        margin-left: 4px;
        line-height: 1;
      }

      &:hover {
        color: var(--primary);
        fill: var(--primary);
      }
    }

    .#{$ns}Tabs-linksContainer {
      height: 100%;
      flex-grow: 1;

      position: relative;
      display: flex;
      align-items: center;
      overflow: hidden;

      // &--overflow {
      //   flex: 1;

      //   .#{$ns}Tabs-links {
      //     max-width: 0;
      //   }
      // }

      &-arrow {
        width: 16px;
        height: 100%;
        display: flex;
        align-items: center;
        cursor: pointer;
        box-sizing: content-box;
        margin-bottom: px2rem(3px);
        padding-bottom: px2rem(8px);

        .iconfont {
          font-size: var(--Remark-icon-fontSize);
        }
        &:hover {
          color: var(--icon-onHover-color);
        }

        &--left {
          padding-right: 16px;
        }

        &--right {
          padding-left: 16px;
        }

        &--disabled {
          .iconfont {
            cursor: not-allowed;
            color: var(--Number-handler-onDisabled-color);
          }

          &:hover {
            color: var(--Number-handler-onDisabled-color);
          }
        }
      }

      &-main {
        position: relative;

        overflow-y: auto;
        scrollbar-width: none;
        width: 100%;
        &::-webkit-scrollbar {
          display: none;
        }

        .#{$ns}Tabs-links {
          // position: relative;
          min-width: 100%;
          max-width: 0;
          height: 100%;
          overflow-x: hidden;

          .#{$ns}Tabs-links-drag {
            position: absolute;
            height: 100%;
            top: 0;
            box-shadow: 0 0 0 1px red;
          }
        }
      }
    }
  }

  &-links {
    display: flex;
    margin: 0;
    padding: 0;
    list-style: none;
    user-select: none;
    @include clearfix();

    > .#{$ns}Tabs-link {
      position: relative;
      flex: none;
      display: flex;
      max-width: var(--Tabs-link-maxWidth);
      overflow: hidden;

      .#{$ns}Tabs-link-edit {
        max-width: var(--Tabs-link-maxWidth);
        border: none;
        outline: none;
      }

      &:hover .#{$ns}Tabs-link-close {
        width: var(--Tabs-close-size);
        margin-left: var(--Tabs-close-margin);
      }

      .#{$ns}Tabs-link-close {
        width: 0;
        margin: var(--Tabs-close-marginTop) 0 0;
        fill: var(--Tabs-gray-color);
        transition: all var(--Tabs-animation-duration) linear;
        overflow: hidden;
        cursor: pointer;

        .#{$ns}Tabs-link-close-icon {
          width: var(--Tabs-close-size);
        }
      }

      .#{$ns}Tabs-link-copy {
        margin-left: var(--gap-sm);
        color: var(--Remark-iconColor);
        cursor: pointer;

        .#{$ns}Tabs-link-copy-icon {
          width: 0.6875rem;
          height: 0.6875rem;
          margin-top: 6px;
        }
      }

      > a:first-child {
        font-size: var(--Tabs-linkFontSize);
        outline: none;
        border: var(--Tabs-borderWidth) solid transparent;
        border-top-left-radius: var(--Tabs-borderRadius);
        border-top-right-radius: var(--Tabs-borderRadius);
        color: var(--Tabs-color);
        margin: var(--Tabs-linkMargin);
        padding: var(--Tabs-linkPadding);
        text-decoration: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        max-width: 100%;
        overflow: hidden;

        .#{$ns}Tabs-link-text {
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          width: 100%;
        }

        > .#{$ns}Icon {
          margin-right: var(--Tabs-icon-gap);
          vertical-align: unset;
        }

        > img.#{$ns}Icon {
          position: relative;
          top: 0.125em;
        }
      }

      > a.#{$ns}Tabs-togglor {
        display: flex;
      }

      > .#{$ns}Combo-toolbarBtn {
        position: absolute;
        right: -10px;
        top: -10px;
        z-index: 10;
        display: none;
      }

      &:hover > .#{$ns}Combo-toolbarBtn {
        display: block;
      }

      &:hover > a:first-child,
      > a:first-child:focus {
        text-decoration: none;
      }

      &.disabled,
      &.is-disabled {
        cursor: not-allowed;

        > a:first-child {
          // color: var(--Tabs-onDisabled-color);
          color: var(--Tabs-link-disabled-color);
          background: transparent;
          border-color: transparent;
          pointer-events: none;
        }
      }

      &.active > a:first-child,
      &.is-active > a:first-child {
        color: var(--Tabs-onActive-color);
        border-color: var(--Tabs-onActive-borderColor);
        border-bottom-color: transparent;
      }

      &.has-error > a:first-child {
        color: var(--Tabs-onError-color) !important;
      }
    }

    > .#{$ns}ComboTabs-addLink {
      > a:first-child {
        &:hover {
          color: var(--primary);
          fill: var(--primary);
        }
      }
    }
  }

  .#{$ns}Tabs-togglor {
    display: flex;
    justify-content: center;
    margin: auto;
    cursor: pointer;

    &-arrow {
      width: var(--gap-md);
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 1;
      transform: rotate(90deg);

      > svg {
        transition: transform var(--animation-duration);
        display: inline-block;
        color: var(--Form-select-caret-iconColor);
        width: 10px;
        height: 10px;
        top: 0;
      }
    }

    &.is-opened {
      color: var(--Tabs-onActive-color);

      .#{$ns}Tabs-togglor-arrow > svg {
        transform: rotate(180deg);
      }
    }
  }

  &-PopOver.#{$ns}PopOver {
    border: 0;
    border-radius: 0;

    > .#{$ns}Tabs-PopOverList {
      > .#{$ns}Tabs-link > a:first-child {
        display: flex;
        .#{$ns}Tabs-link-close {
          margin-left: var(--sizes-size-5);
          .#{$ns}Tabs-link-close-icon {
            width: var(--Tabs-close-size);
          }
        }
      }
    }
  }

  &-content {
    background: var(--Tabs-content-bg);
    border-style: solid;
    border-width: 0 var(--Tabs-borderWidth) var(--Tabs-borderWidth);
    border-color: var(--Tabs-borderColor);
  }

  &-pane {
    display: none;
    padding: var(--gap-base);
    opacity: 0;
    transition: opacity var(--animation-duration) linear;

    &.is-active {
      display: block;
    }

    &.in {
      opacity: 1;
    }
  }

  &--line {
    > .#{$ns}Tabs-linksContainer-wrapper {
      position: relative;

      &::before {
        content: '';
        position: absolute;
        width: 100%;
        left: 0;
        bottom: 0;
        border-bottom: var(--Tabs--line-border-width)
          var(--Tabs--line-border-style) var(--Tabs--line-border-color);
      }

      &--toolbar {
        .#{$ns}Tabs-link {
          padding-top: 10px;
        }

        .#{$ns}Tabs-addable {
          padding: 0;
        }

        .#{$ns}Tabs-linksContainer-arrow {
          margin: 0;
        }
      }

    }

    > .#{$ns}Tabs-linksContainer > .#{$ns}Tabs-linksContainer-arrow {
      top: -4px;
    }

    > .#{$ns}Tabs-linksWrapper > .#{$ns}Tabs-links,
    > .#{$ns}Tabs-linksContainer-wrapper
      > .#{$ns}Tabs-linksContainer
      > .#{$ns}Tabs-linksContainer-main
      > .#{$ns}Tabs-links {
      > li {
        padding: 0 calc(var(--Tabs--line-padding) / 2);

        &:first-of-type {
          padding-left: 0;
        }

        &:last-of-type {
          padding-right: 0;
        }

        > a:first-child {
          border-width: 0;
          padding: 0 0 px2rem(8px);
          color: var(--Tabs--line-color);
          font-size: var(--Tabs--line-fontSize);
          font-weight: var(--Tabs--line-fontWeight);
          line-height: var(--Tabs--line-lineHeight);
        }

        &:not(.is-disabled):hover {
          a:first-child {
            color: var(--Tabs--line-hover-color);
            font-size: var(--Tabs--line-hover-fontSize);
            font-weight: var(--Tabs--line-hover-fontWeight);
            line-height: var(--Tabs--line-hover-lineHeight);
            background: transparent;
          }

          .#{$ns}Tabs-link-close {
            fill: var(--Tabs--line-hover-color);
          }
        }

        &.is-disabled {
          a:first-child {
            color: var(--Tabs--line-disabled-color);
            font-size: var(--Tabs--line-disabled-fontSize);
            font-weight: var(--Tabs--line-disabled-fontWeight);
            line-height: var(--Tabs--line-disabled-lineHeight);
          }
        }

        &.is-active {
          > a:first-child,
          > a:first-child:hover,
          > a:first-child:focus {
            border-bottom: var(--Tabs--line-active-border-width)
              var(--Tabs--line-active-border-style)
              var(--Tabs--line-onHover-borderColor);
            color: var(--Tabs--line-active-color);
            font-size: var(--Tabs--line-active-fontSize);
            font-weight: var(--Tabs--line-active-fontWeight);
            line-height: var(--Tabs--line-active-lineHeight);
            background: transparent;
            fill: var(--Tabs--line-active-color); // 作用到注册的icon
          }
        }
      }

      > .#{$ns}ComboTabs-addLink {
        padding: 0 calc(var(--Tabs--line-padding) / 2);

        > a:first-child {
          padding: 0 0 px2rem(8px);
        }
      }
    }

    > .#{$ns}Tabs-content {
      border-width: 0;
    }
  }

  &--card {
    > .#{$ns}Tabs-linksContainer-wrapper {
      background: var(--Tabs--card-bg);

      > .#{$ns}Tabs-addable {
        margin-left: 0;
        padding: 0;
        padding-top: var(--Tabs--card-add-gap-top);
        margin-right: var(--Tabs--card-add-gap);
      }

      > .#{$ns}ComboTabs-addLink {
        padding-top: px2rem(6px);
        margin: 0 px2rem(4px) 0 0;

        > a:first-child {
          padding: 0 0 px2rem(8px);
        }
      }

      > .#{$ns}Tabs-linksContainer {
        .#{$ns}Tabs-addable {
          padding: 0;
        }

        > .#{$ns}Tabs-linksContainer-arrow {
          padding: var(--Tabs--card-arrow-gap) var(--Tabs--card-add-gap) 0;
          margin-bottom: 0;
        }

        > .#{$ns}Tabs-linksContainer-main > .#{$ns}Tabs-links {
          padding: var(--Tabs--card-padding);
          border-top: var(--Tabs--card-border-width)
            var(--Tabs--card-border-style) var(--Tabs--card-borderTopColor);

          > li {
            padding: var(--Tabs--card-linkPadding);
            margin-right: var(--Tabs--card-linkMargin);
            border-top-left-radius: var(--Tabs--card-borderRadius);
            border-top-right-radius: var(--Tabs--card-borderRadius);
            background: var(--Tabs--card-linkBg);
            cursor: pointer;

            > a:first-child {
              padding: 0;
              border: none;
              color: var(--Tabs--card-color);
              font-size: var(--Tabs--card-fontSize);
              font-weight: var(--Tabs--card-fontWeight);
              line-height: var(--Tabs--card-lineHeight);
            }

            &.is-active {
              color: var(--Tabs--card-active-color);
              background: var(--Tabs--card-onActive-bg);
              > a:first-child {
                color: var(--Tabs--card-active-color);
                font-size: var(--Tabs--card-active-fontSize);
                font-weight: var(--Tabs--card-active-fontWeight);
                line-height: var(--Tabs--card-active-lineHeight);
              }
            }

            &.is-disabled {
              cursor: not-allowed;
              background: var(--Tabs--card-disabled-linkBg);
              > a:first-child {
                color: var(--Tabs--card-disabled-color);
                font-size: var(--Tabs--card-disabled-fontSize);
                font-weight: var(--Tabs--card-disabled-fontWeight);
                line-height: var(--Tabs--card-disabled-lineHeight);
              }
            }

            &:not(.is-disabled):hover,
            &:not(.is-disabled):focus {
              background: var(--Tabs--card-hover-linkBg);
              > a:first-child {
                color: var(--Tabs--card-hover-color);
                font-size: var(--Tabs--card-hover-fontSize);
                font-weight: var(--Tabs--card-hover-fontWeight);
                line-height: var(--Tabs--card-hover-lineHeight);
              }
            }
          }
        }
      }

      > .#{$ns}Tabs-toolbar {
        padding: var(--Tabs--card-padding);
        margin: 0;
      }
    }

    > .#{$ns}Tabs-content {
      border-width: 0;
    }
  }

  &--radio {
    > .#{$ns}Tabs-linksContainer-wrapper {
      margin-bottom: px2rem(10px);

      .#{$ns}Tabs-addable {
        padding: 0;
      }

      > .#{$ns}Tabs-linksContainer {
        > .#{$ns}Tabs-linksContainer-arrow {
          margin-bottom: 0;
        }

        > .#{$ns}Tabs-linksContainer-main > .#{$ns}Tabs-links {
          border: 0;

          > li {
            margin: 0;
            align-items: center;
            justify-content: center;
            text-align: center;
            margin: 0;
            min-width: 68px;
            height: var(--Tabs--radio-height);
            background: var(--Tabs--radio-bg);
            border-style: var(--Tabs--radio-top-border-style)
              var(--Tabs--radio-right-border-style)
              var(--Tabs--radio-bottom-border-style)
              var(--Tabs--radio-left-border-style);
            border-color: var(--Tabs--radio-top-border-color)
              var(--Tabs--radio-right-border-color)
              var(--Tabs--radio-bottom-border-color)
              var(--Tabs--radio-left-border-color);
            border-width: var(--Tabs--radio-top-border-width)
              var(--Tabs--radio-right-border-width)
              var(--Tabs--radio-bottom-border-width)
              var(--Tabs--radio-left-border-width);

            &:hover,
            &:focus {
              background: var(--Tabs--radio-hover-bg);
              border-style: var(--Tabs--radio-hover-top-border-style)
                var(--Tabs--radio-hover-right-border-style)
                var(--Tabs--radio-hover-bottom-border-style)
                var(--Tabs--radio-hover-left-border-style);
              border-color: var(--Tabs--radio-hover-top-border-color)
                var(--Tabs--radio-hover-right-border-color)
                var(--Tabs--radio-hover-bottom-border-color)
                var(--Tabs--radio-hover-left-border-color);
              border-width: var(--Tabs--radio-hover-top-border-width)
                var(--Tabs--radio-hover-right-border-width)
                var(--Tabs--radio-hover-bottom-border-width)
                var(--Tabs--radio-hover-left-border-width);
              > a:first-child {
                font-size: var(--Tabs--radio-hover-fontSize);
                color: var(--Tabs--radio-hover-color);
                font-weight: var(--Tabs--radio-hover-fontWeight);
                line-height: var(--Tabs--radio-hover-lineHeight);
              }
            }

            > a:first-child {
              padding: 0;
              border-radius: 0;
              font-size: var(--Tabs--radio-fontSize);
              color: var(--Tabs--radio-color);
              font-weight: var(--Tabs--radio-fontWeight);
              line-height: var(--Tabs--radio-lineHeight);
              border: none;
            }

            &.is-active {
              position: relative;
              z-index: 1;
              background: var(--Tabs--radio-active-bg);
              border-style: var(--Tabs--radio-active-top-border-style)
                var(--Tabs--radio-active-right-border-style)
                var(--Tabs--radio-active-bottom-border-style)
                var(--Tabs--radio-active-left-border-style);
              border-color: var(--Tabs--radio-active-top-border-color)
                var(--Tabs--radio-active-right-border-color)
                var(--Tabs--radio-active-bottom-border-color)
                var(--Tabs--radio-active-left-border-color);
              border-width: var(--Tabs--radio-active-top-border-width)
                var(--Tabs--radio-active-right-border-width)
                var(--Tabs--radio-active-bottom-border-width)
                var(--Tabs--radio-active-left-border-width);

              > a:first-child {
                font-size: var(--Tabs--radio-active-fontSize);
                color: var(--Tabs--radio-active-color);
                font-weight: var(--Tabs--radio-active-fontWeight);
                line-height: var(--Tabs--radio-active-lineHeight);
              }

              > .#{$ns}Tabs-link-close {
                fill: var(--Tabs--radio-active-color);
              }
            }
            &.is-disabled {
              background: var(--Tabs--radio-disabled-bg);
              border-style: var(--Tabs--radio-disabled-top-border-style)
                var(--Tabs--radio-disabled-right-border-style)
                var(--Tabs--radio-disabled-bottom-border-style)
                var(--Tabs--radio-disabled-left-border-style);
              border-color: var(--Tabs--radio-disabled-top-border-color)
                var(--Tabs--radio-disabled-right-border-color)
                var(--Tabs--radio-disabled-bottom-border-color)
                var(--Tabs--radio-disabled-left-border-color);
              border-width: var(--Tabs--radio-disabled-top-border-width)
                var(--Tabs--radio-disabled-right-border-width)
                var(--Tabs--radio-disabled-bottom-border-width)
                var(--Tabs--radio-disabled-left-border-width);

              > a:first-child {
                font-size: var(--Tabs--radio-disabled-fontSize);
                color: var(--Tabs--radio-disabled-color);
                font-weight: var(--Tabs--radio-disabled-fontWeight);
                line-height: var(--Tabs--radio-disabled-lineHeight);
              }
            }
          }

          > li + li {
            margin-left: calc(var(--Tabs--radio-left-border-width) * -1);
          }

          > .#{$ns}ComboTabs-addLink {
            padding-top: px2rem(4px);
            padding-left: px2rem(16px);

            > a:first-child {
              padding: 0 0 px2rem(8px);
            }
          }
        }
      }
    }

    > .#{$ns}Tabs-content {
      border-top: var(--Tabs-borderWidth) solid var(--Tabs-borderColor);
    }
  }

  // todo 第一个选中，有一像素的空隙待修复。
  // 不带眼镜应该看不出来。
  &--tiled {
    > .#{$ns}Tabs-linksContainer-wrapper {
      align-items: stretch;
      border-bottom: none;

      > .#{$ns}Tabs-linksContainer {
        flex: 1;
        align-items: stretch;

        > .#{$ns}Tabs-linksContainer-arrow {
          margin-bottom: 0;
          padding: 0 10px;
          height: auto;
          align-items: center;
          justify-content: center;
          border-width: var(--Tabs-borderWidth);
          border-style: solid;
          border-color: var(--Tabs-borderColor);
        }

        &.#{$ns}Tabs-linksContainer--overflow
          > .#{$ns}Tabs-linksContainer-main
          > .#{$ns}Tabs-links
          > .#{$ns}Tabs-link {
          &:first-of-type {
            border-left-width: 0;
          }
          &:last-of-type {
            border-right-width: 0;
          }
        }
      }

      .#{$ns}Tabs-addable {
        padding: 0 var(--Tabs--tiled-add-gap);
        margin-left: 0;
        white-space: nowrap;
        border-style: solid;
        border-color: var(--Tabs-borderColor);
        border-width: var(--Tabs-borderWidth);
        border-left-width: 0;
      }
    }

    > .#{$ns}Tabs-linksContainer-wrapper
      > .#{$ns}Tabs-linksContainer
      > .#{$ns}Tabs-linksContainer-main
      > .#{$ns}Tabs-links {
      width: 100%;
      display: flex;
      flex-direction: row;
      padding-left: var(--Tabs--tiled-left-border-width);
      border-bottom: 0;

      > li {
        max-width: unset;
        flex-grow: 1;
        text-align: center;
        margin-left: calc(var(--Tabs--tiled-left-border-width) * -1);
        border-style: var(--Tabs--tiled-top-border-style)
          var(--Tabs--tiled-right-border-style)
          var(--Tabs--tiled-bottom-border-style)
          var(--Tabs--tiled-left-border-style);
        border-color: var(--Tabs--tiled-top-border-color)
          var(--Tabs--tiled-right-border-color)
          var(--Tabs--tiled-bottom-border-color)
          var(--Tabs--tiled-left-border-color);
        border-width: var(--Tabs--tiled-top-border-width)
          var(--Tabs--tiled-right-border-width)
          var(--Tabs--tiled-bottom-border-width)
          var(--Tabs--tiled-left-border-width);
        padding: var(--Tabs--tiled-paddingTop) var(--Tabs--tiled-paddingRight)
          var(--Tabs--tiled-paddingBottom) var(--Tabs--tiled-paddingLeft);
        cursor: pointer;

        &:hover,
        &:focus {
          position: relative;
          z-index: 1;

          border-style: var(--Tabs--tiled-hover-top-border-style)
            var(--Tabs--tiled-hover-right-border-style)
            var(--Tabs--tiled-hover-bottom-border-style)
            var(--Tabs--tiled-hover-left-border-style);
          border-color: var(--Tabs--tiled-hover-top-border-color)
            var(--Tabs--tiled-hover-right-border-color)
            var(--Tabs--tiled-hover-bottom-border-color)
            var(--Tabs--tiled-hover-left-border-color);
          border-width: var(--Tabs--tiled-hover-top-border-width)
            var(--Tabs--tiled-hover-right-border-width)
            var(--Tabs--tiled-hover-bottom-border-width)
            var(--Tabs--tiled-hover-left-border-width);
          > a:first-child {
            color: var(--Tabs--tiled-hover-color);
            font-size: var(--Tabs--tiled-hover-fontSize);
            font-weight: var(--Tabs--tiled-hover-fontWeight);
            line-height: var(--Tabs--tiled-hover-lineHeight);
          }
        }

        > a:first-child {
          color: var(--Tabs--tiled-color);
          font-size: var(--Tabs--tiled-fontSize);
          font-weight: var(--Tabs--tiled-fontWeight);
          line-height: var(--Tabs--tiled-lineHeight);
          margin: 0;
          border-radius: 0;
          padding: 0;
          flex: 1;
          border: none;
        }

        &.is-active {
          cursor: default;

          border-style: var(--Tabs--tiled-active-top-border-style)
            var(--Tabs--tiled-active-right-border-style)
            var(--Tabs--tiled-active-bottom-border-style)
            var(--Tabs--tiled-active-left-border-style);
          border-color: var(--Tabs--tiled-active-top-border-color)
            var(--Tabs--tiled-active-right-border-color)
            var(--Tabs--tiled-active-bottom-border-color)
            var(--Tabs--tiled-active-left-border-color);
          border-width: var(--Tabs--tiled-active-top-border-width)
            var(--Tabs--tiled-active-right-border-width)
            var(--Tabs--tiled-active-bottom-border-width)
            var(--Tabs--tiled-active-left-border-width);
          > a:first-child {
            color: var(--Tabs--tiled-active-color);
            font-size: var(--Tabs--tiled-active-fontSize);
            font-weight: var(--Tabs--tiled-active-fontWeight);
            line-height: var(--Tabs--tiled-active-lineHeight);
          }
        }

        &.is-disabled {
          cursor: not-allowed;
          color: var(--Tabs--tiled-disabled-color);
          font-size: var(--Tabs--tiled-disabled-fontSize);
          font-weight: var(--Tabs--tiled-disabled-fontWeight);
          line-height: var(--Tabs--tiled-disabled-lineHeight);
          border-style: var(--Tabs--tiled-disabled-top-border-style)
            var(--Tabs--tiled-disabled-right-border-style)
            var(--Tabs--tiled-disabled-bottom-border-style)
            var(--Tabs--tiled-disabled-left-border-style);
          border-color: var(--Tabs--tiled-disabled-top-border-color)
            var(--Tabs--tiled-disabled-right-border-color)
            var(--Tabs--tiled-disabled-bottom-border-color)
            var(--Tabs--tiled-disabled-left-border-color);
          border-width: var(--Tabs--tiled-disabled-top-border-width)
            var(--Tabs--tiled-disabled-right-border-width)
            var(--Tabs--tiled-disabled-bottom-border-width)
            var(--Tabs--tiled-disabled-left-border-width);
          > a:first-child {
            color: var(--Tabs--tiled-disabled-color);
            font-size: var(--Tabs--tiled-disabled-fontSize);
            font-weight: var(--Tabs--tiled-disabled-fontWeight);
            line-height: var(--Tabs--tiled-disabled-lineHeight);
          }
        }
      }
    }
  }

  &--vertical {
    display: flex;
    min-height: px2rem(200px);
    border: 1px solid var(--colors-neutral-text-13);
    border-radius: 0;

    > .#{$ns}Tabs-linksWrapper {
      flex-direction: column;
      background: var(--Tabs--vertical-bg);
      border-bottom: 0;
    }

    > .#{$ns}Tabs-linksWrapper > .#{$ns}Tabs-links {
      width: var(--Tabs--vertical-width);
      border: none;
      flex-direction: column;

      > li {
        margin: 0 0 0 -1px;
        display: block;

        > a:first-child {
          border-radius: 0;
          border: 0;
          border-left-style: var(--Tabs--vertical-active-border-style);
          border-left-width: var(--Tabs--vertical-onActive-borderWidth);
          border-left-color: transparent;
          margin: 0;
          color: var(--Tabs--vertical-color);
          font-size: var(--Tabs--vertical-fontSize);
          font-weight: var(--Tabs--vertical-fontWeight);
          line-height: var(--Tabs--vertical-lineHeight);
          padding: var(--Tabs--vertical-paddingTop)
            var(--Tabs--vertical-paddingRight)
            var(--Tabs--vertical-paddingBottom)
            var(--Tabs--vertical-paddingLeft);

          &:hover,
          &:focus {
            color: var(--Tabs--vertical-hover-color);
            font-size: var(--Tabs--vertical-hover-fontSize);
            font-weight: var(--Tabs--vertical-hover-fontWeight);
            line-height: var(--Tabs--vertical-hover-lineHeight);
          }
        }
        &.is-disabled {
          > a:first-child {
            color: var(--Tabs--vertical-disabled-color);
            font-size: var(--Tabs--vertical-disabled-fontSize);
            font-weight: var(--Tabs--vertical-disabled-fontWeight);
            line-height: var(--Tabs--vertical-disabled-lineHeight);
          }
        }

        &.is-active {
          > a:first-child {
            color: var(--Tabs--vertical-onActive-color);
            font-size: var(--Tabs--vertical-active-fontSize);
            font-weight: var(--Tabs--vertical-active-fontWeight);
            line-height: var(--Tabs--vertical-active-lineHeight);
            border-left-style: var(--Tabs--vertical-active-border-style);
            border-left-color: var(--Tabs--vertical-onActive-border);
            border-left-width: var(--Tabs--vertical-onActive-borderWidth);
          }
        }
      }
    }

    > .#{$ns}Tabs-linksWrapper + .#{$ns}Tabs-content {
      width: calc(100% - var(--Tabs--vertical-width));
    }

    > .#{$ns}Tabs-content {
      border: none;
      flex-grow: 1;

      > .#{$ns}Tabs-pane {
        height: 100%;
      }
    }
  }

  &--sidebar {
    display: flex;
    height: 100%;

    &.sidebar--left {
      flex-direction: row;

      > .#{$ns}Tabs-content {
        border-right: none;
      }
    }
    &.sidebar--right {
      flex-direction: row-reverse;

      > .#{$ns}Tabs-content {
        border-left: none;
      }
    }

    > .#{$ns}Tabs-linksWrapper {
      flex: 0 0 var(--Tabs--sidebar-sideWidth);
      align-items: flex-start;
      border: none;
    }

    > .#{$ns}Tabs-linksWrapper > .#{$ns}Tabs-links {
      position: relative;
      margin: 0;
      padding-top: var(--Tabs--sidebar-sideMargin);
      flex-grow: 1;
      border: none;
      flex-direction: column;
      border: 0;

      > li {
        display: flex;
        margin-bottom: var(--Tabs--sidebar-sideMargin);
        padding: 0;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        border: 0;

        &.is-disabled > a:first-child {
          color: var(--Tabs--sidebar-disabled-color);
          font-size: var(--Tabs--sidebar-disabled-fontSize);
          font-weight: var(--Tabs--sidebar-disabled-fontWeight);
          line-height: var(--Tabs--sidebar-disabled-lineHeight);
        }

        > a:first-child {
          padding: 0;
          border: 0;
          margin: 0;
          display: flex;
          flex-direction: column;
          align-items: center;
          color: var(--Tabs--sidebar-color);
          font-size: var(--Tabs--sidebar-fontSize);
          font-weight: var(--Tabs--sidebar-fontWeight);
          line-height: var(--Tabs--sidebar-lineHeight);

          .#{$ns}Icon {
            font-size: var(--Tabs--sidebar-iconSize);
            height: var(--Tabs--sidebar-iconSize);
            margin-bottom: var(--Tabs--sidebar-iconMargin);
            margin-right: 0;
            top: 0;
          }
        }
        > a:first-child:hover,
        > a:first-child:focus {
          color: var(--Tabs--sidebar-hover-color);
          font-size: var(--Tabs--sidebar-hover-fontSize);
          font-weight: var(--Tabs--sidebar-hover-fontWeight);
          line-height: var(--Tabs--sidebar-hover-lineHeight);
        }
        &.is-active > a:first-child {
          color: var(--Tabs--sidebar-active-color);
          font-size: var(--Tabs--sidebar-active-fontSize);
          font-weight: var(--Tabs--sidebar-active-fontWeight);
          line-height: var(--Tabs--sidebar-active-lineHeight);
        }
      }
    }

    > .#{$ns}Tabs-content {
      flex-grow: 1;
      border-bottom: none;

      > .#{$ns}Tabs-pane {
        height: 100%;
      }
    }
  }

  &--chrome {
    > .#{$ns}Tabs-linksWrapper {
      background: var(--Tabs--chrome-bg);
      border-bottom: none;
      overflow-x: hidden;

      > .#{$ns}Tabs-addable {
        margin-left: 0;
        padding: 0;
        padding-top: 10px;
      }
    }

    > .#{$ns}Tabs-linksWrapper > .#{$ns}Tabs-links {
      border-bottom: 0;
      padding: 0 px2rem(10px);
      padding-top: px2rem(8px);
      display: flex;
      width: 100%;

      > li {
        position: relative;
        margin-bottom: 0;
        white-space: nowrap;
        min-width: 0;
        max-width: px2rem(250px);
        padding: px2rem(7px) px2rem(20px) px2rem(6px);
        cursor: pointer;
        flex: 1;
        overflow: inherit;

        > a {
          /* 最少展示一个字 */
          min-width: var(--fontSizeBase);
        }

        > a:first-child {
          background: none;
          border: none;
          position: relative;
          z-index: 5;
          overflow: hidden;
          padding: 0;
          text-overflow: ellipsis;
        }

        &.is-active {
          > a:first-child,
          > a:first-child:hover,
          > a:first-child:focus {
            background: none;
            border: none;
          }
          &:after {
            display: none;
          }
        }

        &:not(:last-child):after {
          content: '';
          width: 1px;
          height: calc(100% - var(--Tabs--chrome-radius-size) * 2);
          position: absolute;
          right: -2px;
          background: var(--Tabs--chrome-right-border-color);
          top: var(--Tabs--chrome-radius-size);
        }

        &:hover {
          .chrome-tab-background {
            z-index: 3;
            display: block;
            background-color: var(--Tabs--chrome-onHover-bg);
          }
          .chrome-tab-background > svg {
            fill: var(--Tabs--chrome-onHover-bg);
          }
          &:after {
            display: none;
          }
        }

        &.is-active {
          .chrome-tab-background {
            display: block;
            background-color: var(--Tabs-onActive-bg);
          }
          .chrome-tab-background > svg {
            fill: var(--Tabs-onActive-bg);
          }
        }

        &.is-disabled {
          cursor: not-allowed;
        }
      }
    }
    .chrome-tab-background {
      display: none;
      position: absolute;
      z-index: 4;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      border-radius: var(--Tabs--chrome-radius-size)
        var(--Tabs--chrome-radius-size) 0 0;
      > svg {
        width: var(--Tabs--chrome-radius-size);
        height: var(--Tabs--chrome-radius-size);
        bottom: 0;
        position: absolute;
      }
      > .chrome-tab-background--right {
        right: calc(var(--Tabs--chrome-radius-size) * -1);
      }
      > .chrome-tab-background--left {
        left: calc(var(--Tabs--chrome-radius-size) * -1);
      }
    }
  }

  &--simple {
    & > .#{$ns}Tabs-linksContainer-wrapper {
      border-bottom: none;

      .#{$ns}Tabs-addable {
        padding: 0;
      }

      & > .#{$ns}Tabs-linksContainer {
        > .#{$ns}Tabs-linksContainer-arrow {
          margin-bottom: 0;
        }

        > .#{$ns}Tabs-linksContainer-main > .#{$ns}Tabs-links {
          border: none;

          & > li {
            position: relative;
            border: none;
            padding: var(--Tabs--simple-split-size);
            &:not(.is-disabled) > a {
              font-size: var(--Tabs--simple-fontSize);
              color: var(--Tabs--simple-color);
              font-weight: var(--Tabs--simple-fontWeight);
              line-height: var(--Tabs--simple-lineHeight);
            }
            &:not(.is-disabled):hover {
              > a {
                font-size: var(--Tabs--simple-hover-fontSize);
                color: var(--Tabs--simple-hover-color) !important;
                font-weight: var(--Tabs--simple-hover-fontWeight);
                line-height: var(--Tabs--simple-hover-lineHeight);
              }
            }
            &.is-disabled {
              > a {
                font-size: var(--Tabs--simple-disabled-fontSize);
                color: var(--Tabs--simple-disabled-color) !important;
                font-weight: var(--Tabs--simple-disabled-fontWeight);
                line-height: var(--Tabs--simple-disabled-lineHeight);
              }
            }
            &.is-active {
              > a {
                font-size: var(--Tabs--simple-active-fontSize);
                color: var(--Tabs--simple-active-color) !important;
                font-weight: var(--Tabs--simple-active-fontWeight);
                line-height: var(--Tabs--simple-active-lineHeight);
              }
            }

            &::after {
              content: '';
              position: absolute;
              top: 50%;
              right: 0;
              height: var(--Tabs--simple-fontSize);
              transform: translateY(-50%);
              border: var(--Tabs--simple-split-width)
                var(--Tabs--simple-split-style) var(--Tabs--simple-split-color);
            }

            & > .#{$ns}Tabs-link-close {
              cursor: pointer;
            }

            &:last-of-type::after {
              content: none;
            }

            &:not(.is-disabled):hover .#{$ns}Tabs-link-close {
              fill: var(--primary);
            }

            & > a:first-child {
              padding: 0;

              &,
              &:hover,
              &:focus {
                border: none;
              }
            }
          }
        }
      }
    }

    > .#{$ns}Tabs-content {
      border: none;
    }
  }

  &--strong {
    & > .#{$ns}Tabs-linksContainer-wrapper {
      align-items: stretch;
      border-bottom: var(--Tabs-borderWidth) solid var(--Tabs-borderColor);

      .#{$ns}ComboTabs-addLink {
        display: flex;
        align-items: center;
        justify-content: center;
        width: var(--Tabs--strong-add-size);
        border: var(--Tabs-borderWidth) solid var(--Tabs-borderColor);
        border-top-left-radius: var(--Tabs-borderRadius);
        border-top-right-radius: var(--Tabs-borderRadius);

        > a {
          > svg {
            margin-right: 0;
          }

          > span {
            font-size: 0;
          }
        }
      }

      > .#{$ns}ComboTabs-addLink {
        margin-left: var(--Tabs--strong-marginRight);
        &-reverse {
          margin-right: var(--Tabs--strong-marginRight) !important;
          margin-left: 0 !important;
        }
      }

      .#{$ns}Tabs-addable {
        width: var(--Tabs--strong-add-size);
        margin-left: var(--Tabs--card-arrow-gap);
        padding: 0;
        margin-bottom: calc(var(--Tabs-borderWidth) * -1);
        align-items: center;
        justify-content: center;
        font-size: 0;
        border: var(--Tabs-borderWidth) solid var(--Tabs-borderColor);
        border-top-left-radius: var(--Tabs-borderRadius);
        border-top-right-radius: var(--Tabs-borderRadius);
        flex: none;

        &-reverse {
          margin-right: var(--Tabs--strong-marginRight);
          margin-left: 0;
        }

        > .#{$ns}Tabs-addable-icon {
          margin-right: 0;
        }

        &:hover {
          color: var(--Tabs--line-onHover-color);
        }
      }

      .#{$ns}Tabs-addable:first-child {
        margin-left: 0;
        padding-top: var(--Tabs--strong-paddingTop);
        padding-bottom: var(--Tabs--strong-paddingBottom);
      }

      > .#{$ns}Tabs-linksContainer {
        margin-bottom: calc(var(--Tabs-borderWidth) * -1);

        &.#{$ns}Tabs-linksContainer--overflow
          > .#{$ns}Tabs-linksContainer-main
          > .#{$ns}Tabs-links
          > .#{$ns}Tabs-link {
          &:first-of-type {
            border-left-width: 0;
            border-top-left-radius: 0;
          }

          &:last-of-type {
            border-right-width: 0;
            border-top-right-radius: 0;
          }
        }

        .#{$ns}Tabs-linksContainer-arrow {
          width: var(--Tabs--strong-arrow-size);
          margin-bottom: 0;
          padding: 0;
          justify-content: center;
          border: var(--Tabs-borderWidth) solid var(--Tabs-borderColor);
          border-top-right-radius: var(--Tabs-borderRadius);
          box-sizing: border-box;

          &--left {
            padding-right: 0;
            border-right-width: 0;
            border-top-left-radius: var(--Tabs-borderRadius);
          }

          &--right {
            padding-left: 0;
            border-left-width: 0;
            border-top-right-radius: var(--Tabs-borderRadius);
          }
        }

        > .#{$ns}Tabs-linksContainer-main
          > .#{$ns}Tabs-links {
            > .#{$ns}Tabs-link {
              margin: var(--Tabs--strong-marginTop) var(--Tabs--strong-marginRight)
                var(--Tabs--strong-marginBottom) var(--Tabs--strong-marginLeft);
              padding: var(--Tabs--strong-paddingTop)
                var(--Tabs--strong-paddingRight) var(--Tabs--strong-paddingBottom)
                var(--Tabs--strong-paddingLeft);
              background: var(--Tabs--strong-bg);
              border-radius: var(--Tabs--strong-top-left-border-radius)
                var(--Tabs--strong-top-right-border-radius)
                var(--Tabs--strong-bottom-right-border-radius)
                var(--Tabs--strong-bottom-left-border-radius);
              border-color: var(--Tabs--strong-top-border-color)
                var(--Tabs--strong-right-border-color)
                var(--Tabs--strong-bottom-border-color)
                var(--Tabs--strong-left-border-color);
              border-style: var(--Tabs--strong-top-border-style)
                var(--Tabs--strong-right-border-style)
                var(--Tabs--strong-bottom-border-style)
                var(--Tabs--strong-left-border-style);
              border-width: var(--Tabs--strong-top-border-width)
                var(--Tabs--strong-right-border-width)
                var(--Tabs--strong-bottom-border-width)
                var(--Tabs--strong-left-border-width);
              cursor: pointer;

              &:not(.is-disabled) > a {
                font-size: var(--Tabs--strong-fontSize);
                color: var(--Tabs--strong-color);
                font-weight: var(--Tabs--strong-fontWeight);
                line-height: var(--Tabs--strong-lineHeight);
              }
              &:not(.is-disabled):hover {
                background: var(--Tabs--strong-hover-bg);
                border-color: var(--Tabs--strong-hover-top-border-color)
                  var(--Tabs--strong-hover-right-border-color)
                  var(--Tabs--strong-hover-bottom-border-color)
                  var(--Tabs--strong-hover-left-border-color);
                border-style: var(--Tabs--strong-hover-top-border-style)
                  var(--Tabs--strong-hover-right-border-style)
                  var(--Tabs--strong-hover-bottom-border-style)
                  var(--Tabs--strong-hover-left-border-style);
                border-width: var(--Tabs--strong-hover-top-border-width)
                  var(--Tabs--strong-hover-right-border-width)
                  var(--Tabs--strong-hover-bottom-border-width)
                  var(--Tabs--strong-hover-left-border-width);
                > a {
                  font-size: var(--Tabs--strong-hover-fontSize);
                  color: var(--Tabs--strong-hover-color) !important;
                  font-weight: var(--Tabs--strong-hover-fontWeight);
                  line-height: var(--Tabs--strong-hover-lineHeight);
                }
              }
              &.is-disabled {
                cursor: not-allowed;
                background: var(--Tabs--strong-disabled-bg);
                border-color: var(--Tabs--strong-disabled-top-border-color)
                  var(--Tabs--strong-disabled-right-border-color)
                  var(--Tabs--strong-disabled-bottom-border-color)
                  var(--Tabs--strong-disabled-left-border-color);
                border-style: var(--Tabs--strong-disabled-top-border-style)
                  var(--Tabs--strong-disabled-right-border-style)
                  var(--Tabs--strong-disabled-bottom-border-style)
                  var(--Tabs--strong-disabled-left-border-style);
                border-width: var(--Tabs--strong-disabled-top-border-width)
                  var(--Tabs--strong-disabled-right-border-width)
                  var(--Tabs--strong-disabled-bottom-border-width)
                  var(--Tabs--strong-disabled-left-border-width);
                > a {
                  font-size: var(--Tabs--strong-disabled-fontSize);
                  color: var(--Tabs--strong-disabled-color) !important;
                  font-weight: var(--Tabs--strong-disabled-fontWeight);
                  line-height: var(--Tabs--strong-disabled-lineHeight);
                }
              }
              &.is-active {
                background: var(--Tabs--strong-active-bg);
                border-color: var(--Tabs--strong-active-top-border-color)
                  var(--Tabs--strong-active-right-border-color)
                  var(--Tabs--strong-active-bottom-border-color)
                  var(--Tabs--strong-active-left-border-color);
                border-style: var(--Tabs--strong-active-top-border-style)
                  var(--Tabs--strong-active-right-border-style)
                  var(--Tabs--strong-active-bottom-border-style)
                  var(--Tabs--strong-active-left-border-style);
                border-width: var(--Tabs--strong-active-top-border-width)
                  var(--Tabs--strong-active-right-border-width)
                  var(--Tabs--strong-active-bottom-border-width)
                  var(--Tabs--strong-active-left-border-width);
                > a {
                  font-size: var(--Tabs--strong-active-fontSize);
                  color: var(--Tabs--strong-active-color) !important;
                  font-weight: var(--Tabs--strong-active-fontWeight);
                  line-height: var(--Tabs--strong-active-lineHeight);
                }
              }

              &:not(.is-disabled):hover .#{$ns}Tabs-link-close {
                fill: var(--primary);
              }

              &:last-of-type {
                margin-right: 0;
              }

              & > a:first-child {
                padding: 0;
                margin: 0;
                border: none;
              }
            }

            > .#{$ns}ComboTabs-addLink {
              margin-left: var(--Tabs--strong-marginRight);
              padding-left: 0;
              padding-right: 0;
              &-reverse {
                margin-right: var(--Tabs--strong-marginRight) !important;
                margin-left: 0 !important;
              }
            }

            > .#{$ns}Tabs-link:first-child {
              margin-left: 0;
            }
          }
      }
    }
  }

  &--custom {
    > .#{$ns}Tabs-linksContainer-wrapper {
      position: relative;
      // overflow: initial;

      // navigation bar 底部横线
      &::before {
        content: '';
        position: absolute;
        width: 100%;
        left: 0;
        bottom: 0;
        border-bottom: var(--Tabs--line-border-width)
          var(--Tabs--line-border-style) var(--Tabs--line-border-color);
      }

      // 与navigation bar 同一行
      &--toolbar {
        .#{$ns}Tabs-link {
          padding-top: 10px;
        }

        .#{$ns}Tabs-addable {
          padding: 0;
        }

        .#{$ns}Tabs-linksContainer-arrow {
          margin: 0;
        }
      }
    }

    // > .#{$ns}Tabs-linksContainer-wrapper > .#{$ns}Tabs-linksContainer {
    //   overflow: initial;
    //   > .#{$ns}Tabs-linksContainer-main {
    //     overflow: initial;
    //   }
    // }

    > .#{$ns}Tabs-linksContainer > .#{$ns}Tabs-linksContainer-arrow {
      top: -4px;
    }

    > .#{$ns}Tabs-linksContainer-wrapper
      > .#{$ns}Tabs-linksContainer
      > .#{$ns}Tabs-linksContainer-main
      > .#{$ns}Tabs-links {
      align-items: center;
      // overflow: initial;

      .#{$ns}Tabs-link {
        max-width: none;
        // overflow: initial;

        .#{$ns}Tabs-link-close {
          height: 100%;
        }
      }

      > li {
        padding: 0 calc(var(--Tabs--line-padding) / 2);

        &:first-of-type {
          padding-left: 0;
        }

        &:last-of-type {
          padding-right: 0;
        }

        > .#{$ns}Tabs-custom-nav {
          cursor: pointer;
          border-width: 0;
          padding: 0 0 px2rem(8px);
          color: var(--Tabs--line-color);
          font-size: var(--Tabs--line-fontSize);
          font-weight: var(--Tabs--line-fontWeight);
          line-height: var(--Tabs--line-lineHeight);
        }

        // 鼠标移入到非禁用的nav上
        &:not(.is-disabled):hover {
          .#{$ns}Tabs-custom-nav {
            // color: var(--Tabs--line-hover-color);
            font-size: var(--Tabs--line-hover-fontSize);
            font-weight: var(--Tabs--line-hover-fontWeight);
            line-height: var(--Tabs--line-hover-lineHeight);
            background: transparent;
          }

          .#{$ns}Tabs-link-close {
            fill: var(--Tabs--line-hover-color);
          }
        }

        &.is-disabled {
          .#{$ns}Tabs-custom-nav {
            cursor: not-allowed;
            // color: var(--Tabs--line-disabled-color);
            font-size: var(--Tabs--line-disabled-fontSize);
            font-weight: var(--Tabs--line-disabled-fontWeight);
            line-height: var(--Tabs--line-disabled-lineHeight);
          }
        }

        &.is-active {
          > .#{$ns}Tabs-custom-nav,
          > .#{$ns}Tabs-custom-nav:hover,
          > .#{$ns}Tabs-custom-nav:focus {
            border-bottom: var(--Tabs--line-active-border-width)
              var(--Tabs--line-active-border-style)
              var(--Tabs--line-onHover-borderColor);
            // color: var(--Tabs--line-active-color);
            font-size: var(--Tabs--line-active-fontSize);
            font-weight: var(--Tabs--line-active-fontWeight);
            line-height: var(--Tabs--line-active-lineHeight);
            background: transparent;
          }
        }
      }
    }

    > .#{$ns}Tabs-content {
      border-width: 0;
    }
  }

  &-toolbar {
    // display: inline-block;
    // float: right;
    // padding-top: var(--gap-xs);
    flex: 1 1 auto; // 撑开剩余空间
    margin-left: var(--gap-base);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end; // 默认居右展示
  }

  .#{$ns}Combo-tab-toolbar {
    display: flex;
    .#{$ns}Combo-tab-copyBtn {
      margin-left: var(--gap-sm);
      color: var(--Remark-iconColor);
      svg {
        width: 0.6875rem;
        height: 0.6875rem;
        margin-bottom: 1px;
      }
    }
    .#{$ns}Combo-tab-delBtn {
      display: none;
      svg {
        margin-bottom: 1px;
      }
    }
  }
  .#{$ns}Tabs-link:hover {
    .#{$ns}Combo-tab-toolbar {
      .#{$ns}Combo-tab-delBtn {
        display: block;
      }
    }
  }
}
