.#{$ns}DateRangePicker {
  position: relative;
  display: inline-flex;
  flex-wrap: nowrap;
  border-width: var(--DatePicker-borderWidth);
  border-style: var(--DatePicker-borderStyle);
  border-color: var(--DatePicker-borderColor);
  font-size: var(--DatePicker-fontSize);
  padding: 0 var(--inputDate-default-paddingRight) 0
    var(--inputDate-default-paddingLeft);
  height: var(--DatePicker-height);
  outline: none;
  border-radius: var(--DatePicker-borderRadius);
  color: var(--DatePicker-color);
  background: var(--DatePicker-bg);
  @include input-border();

  .#{$ns}DateRangePicker-clear {
    display: none;
  }

  &:not(.is-disabled) {
    &:hover {
      background: var(--DatePicker-onHover-bg);
      border-color: var(--DatePicker-onHover-borderColor);
      border-style: var(--inputDate-hover-top-border-style)
        var(--inputDate-hover-right-border-style)
        var(--inputDate-hover-bottom-border-style)
        var(--inputDate-hover-left-border-style);
      border-width: var(--inputDate-hover-top-border-width)
        var(--inputDate-hover-right-border-width)
        var(--inputDate-hover-bottom-border-width)
        var(--inputDate-hover-left-border-width);

      .#{$ns}DateRangePicker-input {
        color: var(--inputDate-hover-color);
      }

      .#{$ns}DateRangePicker-clear {
        display: flex;
      }

      .#{$ns}DateRangePicker-clear + .#{$ns}DateRangePicker-toggler {
        // display: none;
      }
    }

    &.is-focused {
      background: var(--inputDate-active-bg-color);
      border-color: var(--DatePicker-onFocused-borderColor);
      box-shadow: var(--inputDate-active-shadow);
      border-style: var(--inputDate-active-top-border-style)
        var(--inputDate-active-right-border-style)
        var(--inputDate-active-bottom-border-style)
        var(--inputDate-active-left-border-style);
      border-width: var(--inputDate-active-top-border-width)
        var(--inputDate-active-right-border-width)
        var(--inputDate-active-bottom-border-width)
        var(--inputDate-active-left-border-width);

      .#{$ns}DateRangePicker-input {
        color: var(--inputDate-active-color);
      }
    }
  }

  .#{$ns}DateRangePicker-input {
    border: none;
    border-bottom: var(--DateRangePicker-activeCursor-height) solid transparent;
    outline: none;
    padding: 0;
    background: 0;
    flex: 1;
    width: 50%;
    line-height: px2rem(30px);
    font-size: var(--DatePicker-fontSize);
    font-weight: var(--inputDate-default-fontWeight);
    color: var(--inputDate-default-color);

    &::placeholder {
      color: var(--colors-neutral-text-6);
    }
  }

  .#{$ns}DateRangePicker-input.isActive {
    border-bottom: var(--DateRangePicker-activeCursor-height) solid
      var(--DateRangePicker-activeCursor-color);
  }

  .#{$ns}DateRangePicker-input-separator {
    margin: 0 var(--inputDate-range-separator-margin);
    display: flex;
    align-items: center;

    &-line {
      width: var(--inputDate-range-separator-width);
      height: 1px;
      background: var(--inputDate-range-separator-color);
    }
  }

  &.is-disabled {
    cursor: not-allowed;
    background: var(--inputDate-disabled-bg-color);
    border-width: var(--inputDate-disabled-top-border-width)
      var(--inputDate-disabled-right-border-width)
      var(--inputDate-disabled-bottom-border-width)
      var(--inputDate-disabled-left-border-width);
    border-style: var(--inputDate-disabled-top-border-style)
      var(--inputDate-disabled-right-border-style)
      var(--inputDate-disabled-bottom-border-style)
      var(--inputDate-disabled-left-border-style);
    border-color: var(--inputDate-disabled-top-border-color)
      var(--inputDate-disabled-right-border-color)
      var(--inputDate-disabled-bottom-border-color)
      var(--inputDate-disabled-left-border-color);

    > .#{$ns}DateRangePicker-input {
      cursor: not-allowed;
      color: var(--inputDate-disabled-color);
    }
  }

  &-placeholder {
    color: var(--DatePicker-placeholderColor);
    user-select: none;
    margin-right: var(--gap-base);
    flex-basis: 0;
    flex-grow: 1;
  }

  &-value {
    margin-right: var(--gap-base);
    flex-basis: 0;
    flex-grow: 1;
  }

  &-toggler {
    @include input-clear();
    line-height: 1;

    svg {
      color: var(--inputDate-default-icon-color);
      width: var(--inputDate-default-icon-size);
      height: var(--inputDate-default-icon-size);
    }
  }

  .DatePicker-toggler-clock {
    content: var(--inputTime-default-icon);
  }

  .DatePicker-toggler-date {
    content: var(--inputDate-default-icon);
  }

  &-clear {
    @include input-clear();
    line-height: 1;
    svg {
      top: 0;
      width: var(--inputDate-default-icon-size);
      height: var(--inputDate-default-icon-size);
    }
  }

  &-activeCursor {
    bottom: 0;
    height: var(--DateRangePicker-activeCursor-height);
    background: var(--DateRangePicker-activeCursor-color);
    opacity: 0;
    transition: all var(--animation-duration) ease-in-out;
    pointer-events: none;

    &.isFocused {
      opacity: 1;
    }
  }
}

.#{$ns}DateRangePicker-wrap {
  width: auto;
  // padding-bottom: var(--gap-sm);
  padding: 0;

  &.is-mobile {
    min-width: 0;
    min-height: 0;
    flex: 1;

    .#{$ns}DateRangePicker-end {
      margin-top: 0;
    }
  }
}

.#{$ns}DateRangePicker-picker-wrap {
  display: flex;
}

.#{$ns}DateRangePicker-start,
.#{$ns}DateRangePicker-end {
  display: flex;
  flex: 1;

  .rdtPicker {
    padding: 0;
    box-shadow: none;
    border: none;
  }
}

.#{$ns}DateRangePicker-end {
  margin-top: 20px;
}

.#{$ns}DateRangePicker-rangers {
  // margin: 0 0 var(--gap-sm);
  margin: 0;
  padding-left: px2rem(10px);
  list-style: none;
  background: #f7f7f9;
  line-height: px2rem(32px);
}

.#{$ns}DateRangePicker-ranger {
  display: inline-block;
  margin-right: px2rem(32px);
  font-size: var(--fontSizeSm);

  a {
    cursor: pointer;
    color: var(--DatePicker-color);

    &:hover {
      color: var(--DatePicker-header-onHover-color);
    }
  }
}

.#{$ns}DateRangePicker-actions {
  text-align: right;
  // margin-top: var(--gap-sm);
  padding: var(--gap-sm);
  border-top: 1px solid var(--Calendar-input-borderColor);
}

.#{$ns}DateRangeControl:not(.is-inline) > .#{$ns}DateRangePicker {
  display: flex;
}

.#{$ns}DateRangeControl {
  @include input-date();
}

.#{$ns}DateRangePicker-popover {
  margin: px2rem(2px) 0 0;

  &.#{$ns}PopOver--leftTopLeftBottom,
  &.#{$ns}PopOver--rightTopRightBottom {
    margin: px2rem(-2px) 0 0;
  }
}

.#{$ns}DateRangePicker-popup {
  height: px2rem(400px);
}

@include media-breakpoint-up(sm) {
  .#{$ns}DateRangePicker-wrap {
    white-space: nowrap;
  }

  .#{$ns}DateRangePicker-end {
    margin-top: 0;
    // margin-left: var(--gap-sm);
    border-left: 1px solid var(--Calendar-input-borderColor);
  }
}

.#{$ns}DateRangeCalendar {
  display: inline-block;
  border-width: var(--DatePicker-borderWidth);
  border-style: var(--DatePicker-borderStyle);
  border-color: var(--DatePicker-borderColor);
  background: var(--DatePicker-bg);
  border-radius: var(--DatePicker-borderRadius);
}

// 移动端输入框样式
.#{$ns}DateRangePicker.is-mobile {
  border: 0;
  justify-content: flex-end;

  &.is-focused,
  &:hover {
    border: none;
  }

  span,
  a {
    &:focus {
      outline: unset;
    }
  }

  .#{$ns}DateRangePicker-value,
  .#{$ns}DateRangePicker-clear {
    display: inline-flex;
    justify-content: flex-end;
    padding: 0 0;
  }

  .#{$ns}DateRangePicker-value {
    margin-right: var(--gap-xs);
  }

  .#{$ns}DateRangePicker-placeholder {
    flex-grow: unset;
    flex-basis: unset;
  }

  .#{$ns}DateRangePicker-toggler {
    margin-top: 2px;
  }
}
