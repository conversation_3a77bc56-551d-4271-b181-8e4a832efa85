.#{$ns}DimensionTable {
  .#{$ns}Table {
    &.is-selecting {
      td {
        user-select: none;
      }
      .#{$ns}Table-table {
        tr {
          background: none;
        }
      }
    }
    &-content {
      display: inline-block;
      max-width: 100%;
      max-height: 500px;
      overflow: auto;
    }
    &-table {
      border-collapse: collapse;
      width: auto;
      min-width: unset;
      tr {
        border: none;
        &:hover {
          background: var(--Table-onHover-bg);
        }

        &:first-child {
          // 列头第1行
          td.is-columnHeader {
            // 左/右/上 边框
            box-shadow: inset 0px -1px var(--Table-borderColor),
                        inset -1px 0px var(--Table-borderColor),
                        inset 0px 1px var(--Table-borderColor);
          }
        }

        td {
          min-width: 120px;
          position: relative;
          border: none;
          // 单元格在 sticky 的时候，正常的border属性 会丢失，因此使用  box-shadow 来实现border效果
          // 右/下 边框
          box-shadow: inset 0px -1px var(--Table-borderColor),
                      inset -1px 0px var(--Table-borderColor);

          &.is-header {
            background-color: var(--Table-thead-bg);
          }

          &.is-title {
            position: sticky;
            top: 0;
            left: 0;
            z-index: 3;
            // 上/下/左/右 边框
            box-shadow: inset 0px -1px var(--Table-borderColor),
                        inset -1px 0px var(--Table-borderColor),
                        inset 1px 0px var(--Table-borderColor),
                        inset 0px 1px var(--Table-borderColor);
          }

          &.is-columnHeader {
            position: sticky;
            top: 0;
            z-index: 2;
          }

          &.is-rowHeader {
            position: sticky;
            left: 0;
            z-index: 2;
            // 行头第1列
            &--firstTd {
              // 左/右/下 边框
              box-shadow: inset 0px -1px var(--Table-borderColor),
                          inset -1px 0px var(--Table-borderColor),
                          inset 1px 0px var(--Table-borderColor);
            }
          }

          &.is-selected {
            background: var(--Table-onChecked-bg);
          }

          &.hover-column {
            background-color: var(--Table-onHover-bg);
          }
        }
      }

      td {
        // 去掉最后一个表单项的 下边距
        & > .#{$ns}Form-item {
          margin-bottom: 0;
        }
      }
    }
  }

  &-toolbarIcon {
    .is-selecting & {
      display: none;
    }

    position: absolute;
    right: 4px;
    top: 4px;
  }

  &-SelectionMask {
    position: fixed;
    background: #409eff;
    opacity: 0.4;
    z-index: 10000;
  }
}
