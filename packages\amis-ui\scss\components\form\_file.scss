.#{$ns}FileControl {
  &-templateInfo {
    display: block;
    margin-bottom: var(--gap-base);
    padding: var(--Button-paddingY) var(--Button-paddingX);
    cursor: pointer;

    > svg {
      margin-right: var(--gap-xs);
    }
  }

  &-dropzone {
    outline: none;
  }

  &-selectBtn {
    display: inline-flex;
    margin-right: var(--inputFile-base-des-margin);

    > svg {
      margin-right: 4px !important;
      width: px2rem(16px);
      height: px2rem(16px);
      top: 0;
    }

    > span {
      font-size: var(--fontSizeSm);
      line-height: 20px;
    }
  }

  &-extraActions {
    display: inline-flex;
    gap: 8px;
    margin-left: var(--gap-sm);
    vertical-align: middle
  }

  // &-dropzone:focus {
  //     .#{$ns}FileControl-selectBtn {
  //         background: var(--Button--default-onHover-bg);
  //         border-color: var(--Button--default-onHover-border);
  //         color: var(--Button--default-onHover-color);
  //     }

  //     &:after {
  //         content: '当前状态接受从剪切板中粘贴文件。';
  //         color: var(--text--muted-color);
  //         font-size: 11px;
  //         margin-top: 10px;
  //     }
  // }

  &-description {
    // inputFile需要遵循通用form组件的提示保持一致
    // color: var(--inputFile-base-des-color);
    // font-size: var(--inputFile-base-des-fontSize);
    // font-weight: var(--inputFile-base-des-fontWeight);
    margin-top: 4px;
    line-height: var(--Form-description-lineHeight);
    font-size: var(--Form-description-fontSize);
    color: var(--Form-description-color);
  }

  &-sizeTip {
    // inputFile的maxSize提示文案需要和form组件的提示保持一致
    margin-top: 4px;
    //line-height: 20px;
    //font-size: var(--fontSizeSm);
    //color: var(--FileControl-icon-color);
    line-height: var(--Form-description-lineHeight);
    font-size: var(--Form-description-fontSize);
    color: var(--Form-description-color);
  }

  &-list {
    list-style: none;
    margin: var(--inputFile-list-marginTop) var(--inputFile-list-marginRight)
      var(--inputFile-list-marginBottom) var(--inputFile-list-marginLeft);
    padding: 0;
    max-height: 144px;
    overflow-x: hidden;
    overflow-y: auto;

    > li {
      // margin-bottom: 4px;
      font-size: 12px;
      background: var(--inputFile-list-bg-color);

      &:hover {
        border-radius: 2px;
        background: var(--FileControl-onHover-bg);
      }
    }

    &-tooltip span {
      font-size: var(--fontSizeSm);
      color: var(--FileControl-danger-color);
    }
  }

  &-previewList {
    // 预览模式下使用弹性布局控制换行，去除掉下面image的margin Bottom
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin: var(--inputFile-list-marginTop) var(--inputFile-list-marginRight)
      var(--inputFile-list-marginBottom) var(--inputFile-list-marginLeft);

    .#{$ns}ImageControl-item {
      margin-bottom: 0;
      margin-right: 0;
    }

    .is-invalid {
      &-thumb {
        border-color: var(--Tag-error-color);
        border-width: 1px;
        border-style: solid;
        border-radius: var(--inputImage-base-default-top-left-border-radius) var(--inputImage-base-default-top-right-border-radius) var(--inputImage-base-default-bottom-right-border-radius) var(--inputImage-base-default-bottom-left-border-radius);
      }

      &-caption {
        color: var(--Tag-error-color);
      }
    }
  }

  &-itemInfo {
    padding: var(--inputFile-list-paddingTop) var(--inputFile-list-paddingRight)
      var(--inputFile-list-paddingBottom) var(--inputFile-list-paddingLeft);
    display: flex;
    line-height: 24px;
    height: auto;

    span {
      word-break: break-all;
    }

    &.is-invalid {
      color: var(--FileControl-danger-color);

      .#{$ns}FileControl-itemInfoIcon,
      .#{$ns}FileControl-itemInfoText {
        color: var(--FileControl-danger-color);
      }
    }

    > svg:not(:first-child) {
      margin-left: 10px;
      width: px2rem(16px);
      height: px2rem(16px);
      top: var(--gap-xs);
    }
  }

  &-itemInfoIcon {
    margin-right: var(--inputFile-list-icon-margin);
    font-size: var(--inputFile-list-icon-size);
    color: var(--inputFile-list-icon-color);
  }

  &-itemInfoText {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    vertical-align: middle;
    color: var(--inputFile-list-color);
    font-size: var(--inputFile-list-fontSize);
    font-weight: var(--inputFile-list-fontWeight);
    line-height: px2rem(24px);

    &:hover {
      color: var(--inputFile-list-color);
      font-size: var(--inputFile-list-fontSize);
      font-weight: var(--inputFile-list-fontWeight);
      line-height: px2rem(24px);
    }
  }

  &-clear {
    color: var(--FileControl-icon-color);
    display: none;
    cursor: pointer;
    margin-left: auto;
    margin-right: var(--gap-xs);
    font-size: var(--inputFile-list-delete-icon-size);
    line-height: px2rem(24px);
    &:hover {
      font-size: var(--inputFile-list-delete-icon-size);
      line-height: px2rem(24px);
      color: var(--FileControl-icon-onHover-color);
    }
  }

  &-list:empty {
    display: none;
  }

  &-list > li:hover &-clear {
    display: block;
  }

  &-progressInfo {
    display: inline-flex;
    height: 20px;
    padding: 0 6px;
    transform: translateY(-3px);
    width: 100%;
    align-items: center;
    white-space: nowrap;
    text-overflow: ellipsis;

    > span {
      display: inline-block;
      padding: 0 4px 0 10px;
      font-size: 12px;
    }

    > svg {
      display: inline-block;
      margin: 0 4px 0 10px;
      width: 14px;
      height: 14px;
      top: 0;
    }
  }

  &-progress {
    height: 5px;
    flex: 1;
    background: #ebebeb;

    > span {
      display: block;
      background: var(--info);
      border-radius: var(--FileControl-progress-borderRadius);
      height: 100%;
      transition: ease-out width var(--animation-duration);
    }
  }

  &-dropzone.disabled > &-acceptTip {
    filter: grayscale(100%);
    box-shadow: none;
    cursor: not-allowed;
    color: var(--FileControl-onDisabled-color);
    pointer-events: auto;
    background: var(--FileControl-onDisabled-bg);

    &:hover {
      border-color: var(--FileControl-border-color);
    }

    > div,
    > span {
      color: var(--FileControl-onDisabled-color);
    }
  }

  &-acceptTip {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-flow: column;
    height: 180px;
    text-align: center;
    border-style: var(--inputFile-drag-top-border-style)
      var(--inputFile-drag-right-border-style)
      var(--inputFile-drag-bottom-border-style)
      var(--inputFile-drag-left-border-style);
    border-color: var(--FileControl-border-color);
    border-width: var(--inputFile-drag-top-border-width)
      var(--inputFile-drag-right-border-width)
      var(--inputFile-drag-bottom-border-width)
      var(--inputFile-drag-left-border-width);
    border-radius: var(--inputFile-drag-top-left-border-radius)
      var(--inputFile-drag-top-right-border-radius)
      var(--inputFile-drag-bottom-right-border-radius)
      var(--inputFile-drag-bottom-left-border-radius);
    background: var(--inputFile-drag-bg-color);
    cursor: pointer;

    &-click {
      color: var(--Spinner-color);
    }

    &-help {
      a {
        color: var(--FileControl-icon-color);
        font-size: var(--fontSizeSm);
      }
    }

    &:hover {
      border-style: var(--inputFile-drag-hover-top-border-style)
        var(--inputFile-drag-hover-right-border-style)
        var(--inputFile-drag-hover-bottom-border-style)
        var(--inputFile-drag-hover-left-border-style);
      border-color: var(--inputFile-drag-hover-top-border-color)
        var(--inputFile-drag-hover-right-border-color)
        var(--inputFile-drag-hover-bottom-border-color)
        var(--inputFile-drag-hover-left-border-color);
      border-width: var(--inputFile-drag-hover-top-border-width)
        var(--inputFile-drag-hover-right-border-width)
        var(--inputFile-drag-hover-bottom-border-width)
        var(--inputFile-drag-hover-left-border-width);
      background: var(--inputFile-drag-bg-color-hover);
    }

    > span {
      margin-top: var(--inputFile-drag-icon-margin);
      line-height: 20px;
      font-size: var(--inputFile-drag-fontSize);
      font-weight: var(--inputFile-drag-fontWeight);
      color: var(--FileControl-drag-color);
    }

    > svg {
      top: 0;
      font-size: var(--inputFile-drag-icon-size);
      color: var(--inputFile-drag-icon-color);
    }
  }

  &-dropzone.is-active &-acceptTip {
    border-color: var(--info);
  }

  &-sum {
    font-size: var(--fontSizeSm);

    > a {
      cursor: pointer;
    }
  }

  // 仅在静态显示（Form-static）下：去掉文件列表首行与容器顶部之间的 12px 间距
  &.#{$ns}Form-static {
    .#{$ns}FileControl-list,
    .#{$ns}FileControl-previewList {
      margin-top: 0;
    }
  }
}
