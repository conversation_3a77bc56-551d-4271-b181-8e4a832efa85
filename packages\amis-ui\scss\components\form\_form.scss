@use 'sass:math';

.#{$ns}Form {
  font-size: var(--Form-fontSize);
  position: relative;

  // 一般是快速编辑里的最后一个输入框
  &--quickEdit > div:last-of-type {
    margin-bottom: 0;
  }

  &--inline {
    > .#{$ns}Button {
      margin-bottom: calc(var(--Form-item-gap) / 2);
      margin-right: calc(var(--Form-item-gap) / 2);
    }

    > .#{$ns}PlainField {
      display: inline-block;
      padding-top: var(--Form-input-paddingY);
      // padding-bottom: var(--Form-input-paddingY);
    }
  }
}

.#{$ns}Form-static {
  min-height: var(--Form-input-height);
  font-size: var(--Form-static-fontSize);
  &:not(.is-noPaddingY-static) {
    padding-top: var(--Form-label-paddingTop);
    padding-bottom: var(--Form-label-paddingTop);
  }
  margin-bottom: 0;
  word-break: break-word;

  .#{$ns}Form-item--inline > .#{$ns}Form-value > & {
    display: inline-block;
    vertical-align: top;
  }

  &--borderFull {
    border: var(--Form-input-borderWidth) solid var(--Form-input-borderColor);
    border-radius: var(--Form-input-borderRadius);
    padding: var(--Form-input-paddingY) var(--Form-input-paddingX);
  }

  &--borderHalf {
    border-bottom: var(--Form-input-borderWidth) solid
      var(--Form-input-borderColor);
    border-radius: var(--Form-input-borderRadius);
    padding: var(--Form-input-paddingY) var(--Form-input-paddingX);
  }
}

.#{$ns}Form--itemNoMarginBottom {
  .#{$ns}Form-item {
    margin-bottom: 0;
  }
}

.#{$ns}Form-label {
  display: inline-flex; // issue#469，避免label的冒号和label换行
  font-weight: var(--Form-item-fontWeight);
  line-height: var(--Form-item-lineHeight);
  margin-bottom: var(--Form-mode-default-labelGap);
  position: relative;
  font-size: var(--Form-item-fontSize);
  color: var(--Form-item-fontColor);

  > span {
    position: relative;
    display: inline-flex; // issue#469，避免label的冒号和label换行
    max-width: 100%;
  }

  &--hasColon {
    &::after {
      content: ':';
      position: relative;
      display: inline-flex;
    }
  }
}

.is-mobile {
  .#{$ns}Form-label {
    &--hasColon {
      &::after {
        line-height: px2rem(32px);
      }
    }
  }
}

.#{$ns}Form-star {
  color: var(--Form-item-star-color);
  font-size: var(--Form-item-star-size);
  font-family: SimSun,sans-serif;
  line-height: 1;
  padding-inline-end: px2rem(4px);
}

// FIX: issue#667 group中的select样式问题
.#{$ns}Form-item.is-required .#{$ns}Form-label .#{$ns}TplField {
  padding-inline-start: px2rem(4px);
}

// 表单的label中部分场景下不需要这个padding
// .#{$ns}Form-item.is-required .#{$ns}Typography .#{$ns}TplField,
// .#{$ns}Form-item.is-required .#{$ns}Typography--hidden .#{$ns}TplField,
.#{$ns}Form-item.is-required .#{$ns}Alert-content .#{$ns}TplField {
  padding-inline-start: 0;
}

.#{$ns}Form-feedback {
  color: var(--Form-feedBack-color);
  list-style-type: none;
  margin: 0;
  margin-top: var(--Form-feedBack-gap);
  padding: 0;
  font-size: var(--Form-feedBack-fontSize);
}

.#{$ns}Form-fielderror {
  color: var(--Form-feedBack-color);
  list-style-type: none;
  margin: 0;
  margin-top: var(--Form-feedBack-gap);
  font-size: var(--Form-feedBack-fontSize);
  max-height: px2rem(400px);
  overflow-y: scroll;
  padding: px2rem(6px);
  border: 1px solid var(--colors-neutral-line-8);

  > li {
    cursor: pointer;
  }
}

.#{$ns}Form-description {
  display: block;
  word-break: break-word;
  color: var(--Form-description-color);
  font-weight: var(--Form-description-fontWeight);
  line-height: var(--Form-description-lineHeight);
  margin-top: var(--Form-description-gap);
  font-size: var(--Form-description-fontSize);
}

.#{$ns}Form-hint {
  display: inline-block;
  margin-left: var(--gap-sm);
  padding-top: var(--Form-input-paddingY);
  vertical-align: top;
}

.#{$ns}Form-item {
  margin-bottom: var(--Form-item-gap);

  &:last-child {
    margin-bottom: 0;
    .#{$ns}Form-feedback {
      position: relative;
    }
  }

  .#{$ns}Grid-form > &:last-child {
    margin-bottom: 0;
  }

  .#{$ns}Form--inline > &--inline {
    &:last-child {
      margin-right: 0;
      margin-bottom: 0;
    }
  }

  .#{$ns}Form-remark {
    padding-top: var(--Form-label-paddingTop);
    vertical-align: top;
  }

  &--inline {
    margin-right: var(--Form-mode-inline-item-gap);
  }

  &--horizontal {
    > .#{$ns}Form-label {
      display: inline-flex; // issue#469，避免label的冒号和label换行
      text-align: var(--Form--horizontal-label-align);
      justify-content: right;
      white-space: var(--Form--horizontal-label-whiteSpace);

      &--left {
        text-align: left;
        justify-content: left;
      }
    }

    &.is-mobile {
      .#{$ns}Form-star {
        position: absolute;
        left: px2rem(-6px);
        top: px2rem(6px);
      }
    }
  }

  // label使用 Typography 组件包裹后样式兼容
  &--row {
    .#{$ns}Form-label {
      display: inline-flex; // issue#469，避免label的冒号和label换行
      text-align: var(--Form--horizontal-label-align);
      justify-content: right;

      &--left {
        text-align: left;
        justify-content: left;
      }
    }
  }

  // label使用 Typography 组件包裹后样式兼容
  &--inline {
    .#{$ns}Form-label {
      display: inline-flex; // issue#469，避免label的冒号和label换行
      text-align: var(--Form--horizontal-label-align);
      justify-content: right;

      &--left {
        text-align: left;
        justify-content: left;
      }
    }
  }

  &--horizontal-justify {
    justify-content: space-between;

    > .#{$ns}Form-label {
      text-align: var(--Form--horizontal-justify-label-align);
      white-space: var(--Form--horizontal-justify-label-whiteSpace);
    }
    > .#{$ns}Form-value {
      text-align: var(--Form--horizontal-justify-value-align);

      > * {
        text-align: initial;
      }
    }
  }

  &--normal {
    > .#{$ns}Form-label {
      display: block;
      width: var(--Form-mode-default-width);
      .#{$ns}Form-star {
        position: absolute;
        left: px2rem(-6px);
        top: px2rem(5px);

      }
    }

    // 移动端样式同步
    &.is-mobile  {
      .#{$ns}Form-label {
        display: inline-flex;

        span {
          line-height: px2rem(32px);
        }
        // align-items: center;

        .#{$ns}Form-star {
          top: px2rem(8px);
          line-height: px2rem(16px);
        }

        &::after {
          line-height: px2rem(32px);
        }

        .#{$ns}Typography {
          text-overflow: clip;
          white-space: wrap;
          line-height: px2rem(32px);
        }
      }
    }
  }

  &--row {
    .#{$ns}Form-rowInner {
      min-width: 0;
      min-height: 0;
      flex: 1;
    }

    .#{$ns}Form-star {
      position: absolute;
      left: px2rem(-6px);
      top: px2rem(3px);
    }

    &.is-mobile {
      .#{$ns}Form-star {
        top: px2rem(5px);
      }
    }
  }

  // &.is-error > .#{$ns}Form-label {
  //   color: var(--Form-item-onError-color);
  // }

  .#{$ns}Form-control--withSize {
    > .#{$ns}ResultBox {
      // 兼容 @media (min-width: 576px) .cxd-Form-control--sizeLg
      display: flex;
    }
  }
}

.is-mobile {
  .#{$ns}Form-label {
    .#{$ns}Typography {
      text-overflow: clip;
      white-space: wrap;
      line-height: px2rem(32px);
    }
  }
}

.#{$ns}Form-placeholder {
  color: var(--Form-input-placeholderColor);
}

.#{$ns}Form-caption {
  display: inline-block;
  line-height: var(--Form-input-height);
  height: var(--Form-input-height);
  margin-left: px2rem(10px);
}

.#{$ns}Form {
  & &-item {
    &--row {
      &.is-mobile {
        .#{$ns}Form-star {
          padding-inline-end: none;
        }

        .#{$ns}Form-control {
          padding-left: px2rem(4px);

          & > .#{$ns}Form-control {
            padding-left: 0;
          }
        }
      }
    }
  }
}

@include media-breakpoint-up(sm) {
  .#{$ns}Form-label {
    display: inline-flex; // issue#469，避免label的冒号和label换行
    max-width: 100%;
  }

  .#{$ns}Form-control--sizeXs {
    // min-width: var(--Form-control-widthXs);
    // width: auto;
    width: var(--Form-control-widthXs);
    max-width: 100%;
    display: inline-block;
    vertical-align: top;

    // 非常难受，number 类型的 input 不能设置size
    &.#{$ns}NumberControl {
      width: var(--Form-control-widthXs);
    }
  }

  .#{$ns}Form-control--sizeSm {
    width: var(--Form-control-widthSm);
    max-width: 100%;
    display: inline-block;
    vertical-align: top;

    // 非常难受，number 类型的 input 不能设置size
    &.#{$ns}NumberControl {
      width: var(--Form-control-widthSm);
    }
  }

  .#{$ns}Form-control--sizeMd {
    width: var(--Form-control-widthMd);
    max-width: 100%;
    display: inline-block;
    vertical-align: top;
  }

  .#{$ns}Form-control--sizeLg {
    width: var(--Form-control-widthLg);
    max-width: 100%;
    display: inline-block;
    vertical-align: top;

    // 非常难受，number 类型的 input 不能设置size
    &.#{$ns}NumberControl {
      width: var(--Form-control-widthLg);
    }
  }

  .#{$ns}Form-item {
    &--horizontal {
      display: flex;
      flex-wrap: nowrap;

      > .#{$ns}Form-value {
        flex-basis: 0;
        flex-grow: 1;
        max-width: var(--Form--horizontal-value-maxWidth);
        min-width: var(--Form--horizontal-value-minWidth);
      }

      .#{$ns}Form-itemColumn--xs,
      .#{$ns}Form-itemColumn--sm,
      .#{$ns}Form-itemColumn--normal,
      .#{$ns}Form-itemColumn--auto,
      .#{$ns}Form-itemColumn--md,
      .#{$ns}Form-itemColumn--lg {
        flex-grow: unset;
        flex-basis: unset;
      }

      > .#{$ns}Form-label {
        line-height: px2rem(32px);
        margin: 0;
        margin-right: var(--Form--horizontal-label-gap);
        flex-shrink: 0;

        .#{$ns}Form-star {
          position: absolute;
          left: px2rem(-6px);
          top: px2rem(10px);
        }
      }

      .#{$ns}Form-itemColumn--xs {
        width: var(--Form--horizontal-label-widthXs);
      }

      .#{$ns}Form-itemColumn--sm {
        width: var(--Form--horizontal-label-widthSm);
      }

      .#{$ns}Form-itemColumn--normal {
        width: var(--Form--horizontal-label-widthBase);
      }

      .#{$ns}Form-itemColumn--md {
        width: var(--Form--horizontal-label-widthMd);
      }

      .#{$ns}Form-itemColumn--lg {
        width: var(--Form--horizontal-label-widthLg);
      }

      .#{$ns}Form-itemColumn--auto {
        width: auto;
      }

      @for $i from (1) through $Form--horizontal-columns {
        .#{$ns}Form-itemColumn--#{$i} {
          flex: 0 0 percentage(math.div($i, $Form--horizontal-columns));
          max-width: percentage(math.div($i, $Form--horizontal-columns));
          min-height: 1px;
        }
      }
      .#{$ns}Form-itemColumn--align-left {
        text-align: left;
      }
      .#{$ns}Form-itemColumn--align-right {
        text-align: right;
      }
    }

    &--inline {
      display: inline-block;
      vertical-align: top;

      > .#{$ns}Form-label {
        height: px2rem(32px);
        line-height: px2rem(32px);
        margin: 0;
        margin-right: var(--Form-mode-inline-label-gap);

        .#{$ns}Form-star {
          position: absolute;
          left: px2rem(-6px);
          top: px2rem(10px);
        }
      }

      > .#{$ns}Form-value {
        display: inline-block;
        vertical-align: top;

        > .#{$ns}Button--link {
          padding-top: var(--Form-label-paddingTop);
        }

        > .#{$ns}Form-control {
          vertical-align: top;
          display: inline-block;
        }

        > .#{$ns}Form-control.#{$ns}InputGroup {
          display: inline-flex;
        }

        > .#{$ns}TextControl--withAddOn {
          display: inline-flex;
        }
      }
    }
  }

  .#{$ns}Form-row {
    display: flex;
    flex-wrap: wrap;

    margin-left: calc(var(--Form-row-gutterWidth) / -2);
    margin-right: calc(var(--Form-row-gutterWidth) / -2);
    align-items: flex-start;

    > * {
      padding-left: calc(var(--Form-row-gutterWidth) / 2);
      padding-right: calc(var(--Form-row-gutterWidth) / 2);
    }
  }

  .#{$ns}Form-col {
    flex-basis: 0;
    flex-grow: 1;
    flex-shrink: 1;
  }

  .#{$ns}Form-rowInner {
    display: flex;
    flex-wrap: nowrap;

    > .#{$ns}Form-label {
      display: inline-flex; // issue#469，避免label的冒号和label换行
      vertical-align: top;
      padding-top: var(--Form-label-paddingTop);
      padding-right: var(--Form-row-gutterWidth);
    }

    > .#{$ns}Form-control {
      flex-basis: 0;
      flex-grow: 1;

      // &:not(.#{$ns}Form-control--withSize) {
      //   width: var(--Form-control-widthBase);
      // }
    }
  }
}

.#{$ns}Form--debug {
  padding: var(--gap-sm);
  margin-bottom: var(--gap-sm);
  background-color: rgb(250, 250, 250);
  border-radius: var(--borderRadius);
  position: relative;
}

// .#{$ns}Form--quickEdit {
//   min-width: var(--Form-control-widthSm);
// }

.#{$ns}Form--column {
  display: flex;
  flex-wrap: wrap;
  margin-left: calc(var(--Form-group-gutterWidth) / -2);
  margin-right: calc(var(--Form-group-gutterWidth) / -2);
  > .#{$ns}Form-item {
    flex-shrink: 0;
    flex-grow: 1;
    padding-left: calc(var(--Form-group-gutterWidth) / 2);
    padding-right: calc(var(--Form-group-gutterWidth) / 2);
  }
}

.#{$ns}Form--column-2 > .#{$ns}Form-item {
  width: 50%;
}

.#{$ns}Form--column-3 > .#{$ns}Form-item {
  width: 33%;
}

.#{$ns}Form--column-4 > .#{$ns}Form-item {
  width: 25%;
}

.#{$ns}Form--column-5 > .#{$ns}Form-item {
  width: 20%;
}

.#{$ns}Form--column-6 > .#{$ns}Form-item {
  width: 16.6%;
}

.#{$ns}Form--column-7 > .#{$ns}Form-item {
  width: 14.2%;
}

.#{$ns}Form--column-8 > .#{$ns}Form-item {
  width: 12.5%;
}

.#{$ns}Form-column-9 > .#{$ns}Form-item {
  width: 11.1%;
}

.#{$ns}Form-column-10 > .#{$ns}Form-item {
  width: 10%;
}

/* 移动端样式调整 */
@include media-breakpoint-down(sm) {
  .#{$ns}Form {
    // &::before {
    //   position: absolute;
    //   box-sizing: border-box;
    //   content: ' ';
    //   pointer-events: none;
    //   right: 0;
    //   top: 0;
    //   left: 0;
    //   border-bottom: var(--Form-input-borderWidth) solid var(--borderColor);
    // }

    // 移动端样式，处理inputGroup下表单存在两条边框问题
    .#{$ns}Form-group {
      .#{$ns}Form-item.is-mobile {
        &::after {
          border-bottom: none;
        }
      }
    }

    .#{$ns}Form-item {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 0;
      padding: var(--Form-item-mobile-gap) 0;
      position: relative;

      &::after {
        position: absolute;
        box-sizing: border-box;
        content: ' ';
        pointer-events: none;
        right: 0;
        bottom: 0;
        left: 0;
        border-bottom: var(--Form-input-borderWidth) solid var(--borderColor);
      }

      &.is-mobile {
        &.hidden-bottom-line {
          &::after {
            border-bottom: none;
          }
        }

        &::after {
          border-bottom: var(--Form-input-borderWidth) solid #e8e9eb;
        }

        .#{$ns}Form-label {
          .#{$ns}Typography {
            line-height: px2rem(32px);
          }
        }
      }

      .#{$ns}InputGroup-addOn,
      .#{$ns}TextControl-addOn {
        border: none;
      }

      > .#{$ns}Form-label {
        flex: 0 0 28%;
        max-width: 28%;
        min-height: 1px;
        text-align: left;
        padding-right: calc(var(--Form--horizontal-gutterWidth) / 2);
        overflow-wrap: break-word;
        margin-right: 0;
        margin-bottom: 0;
        font-size: var(--fontSizeMd);
      }

      .#{$ns}Form-description {
        font-size: var(--fontSizeBase);
      }

      .#{$ns}TextControl-input {
        font-size: var(--fontSizeMd);

        input {
          height: calc(var(--Form-input-lineHeight) * var(--fontSizeLg));
        }
      }

      .#{$ns}Form-value,
      .#{$ns}Form-control {
        flex: 1;
        flex-wrap: wrap;
        font-size: var(--fontSizeMd);

        &.is-disabled > .#{$ns}TextControl-input {
          background: transparent;
        }
      }
      .#{$ns}SelectControl.#{$ns}Form-control {
        overflow: hidden;
      }

      .#{$ns}Form-hint,
      .#{$ns}Form-remark,
      .#{$ns}Form-static,
      .#{$ns}Form-group--hor .#{$ns}Form-item,
      .#{$ns}SwitchControl,
      .#{$ns}CheckboxControl,
      .#{$ns}RadiosControl,
      .#{$ns}CheckboxesControl {
        padding-top: 0;
        padding-bottom: 0;
      }

      .#{$ns}Form-group--horizontal .#{$ns}TextControl-input input {
        height: var(--Form-input-height);
      }

      .#{$ns}Form-hint {
        font-size: var(--fontSizeBase);
        margin-left: 0;
        color: var(--text--muted-color);
      }

      .#{$ns}TextControl-placeholder {
        top: 0;
      }

      .#{$ns}Form-static {
        padding-top: px2rem(4px);
        min-height: 0;
      }

      .#{$ns}Form-description,
      .#{$ns}Form-feedback {
        font-size: var(--Form-feedBack-fontSize);
      }

      .#{$ns}InputGroup {
        .#{$ns}Select,
        .#{$ns}InputGroup-btn .#{$ns}Button {
          border: none;
        }

        > .#{$ns}TextControl-input input {
          height: var(--Form-input-height);
        }
      }

      .#{$ns}ColorPicker {
        padding: 0;
        border: none;

        .#{$ns}ColorPicker-arrow {
          display: none;
        }
      }

      .#{$ns}Form-group--hor .#{$ns}Form-item .#{$ns}Button {
        margin-bottom: var(--gap-xs);
      }

      .#{$ns}TextareaControl > textarea,
      .#{$ns}Form-control > .#{$ns}TextControl-input,
      .#{$ns}Number-input,
      .#{$ns}ResultBox,
      .#{$ns}DateControl > .#{$ns}DatePicker,
      .#{$ns}TextControl.is-focused > .#{$ns}TextControl-input {
        border: none;
        padding: 0 var(--Form-input-paddingX) 0 0;
        box-shadow: none;
        border-radius: 0;

        &:hover,
        &:focus,
        &.active {
          border: none;
          outline: none;
          outline-style: none;
        }
      }

      .#{$ns}TextareaControl > textarea {
        padding-top: px2rem(6px);
        font-size: var(--fontSizeMd);
      }

      .#{$ns}Form-control > .#{$ns}TextControl-input--multiple {
        padding: 0;
        min-height: 0;
      }

      // 移动端 row 布局模式使校验报错信息展示在下面
      &--row {
        flex-direction: column;
      }
    }

    .#{$ns}Form-groupColumn {
      margin-bottom: 0;
    }

    > .#{$ns}Form-item.is-placeholder::after {
      display: none;
    }

    .#{$ns}Divider {
      display: none;
    }

    .#{$ns}Tabs-pane {
      padding: 0;
    }

    .#{$ns}Form-item .#{$ns}Form-groupColumn > .#{$ns}Form-item {
      padding-bottom: var(--Form-input-paddingX);
    }
  }
}
