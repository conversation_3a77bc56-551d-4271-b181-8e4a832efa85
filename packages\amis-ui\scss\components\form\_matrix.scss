.#{$ns}MatrixControl {
  &-error {
    margin-bottom: 0;
  }

  &-mask {
    position: fixed;
    background: #409eff;
    opacity: 0.4;
    z-index: 10000;
  }

  .no-wrap {
    white-space: nowrap;
  }

  .text-center {
    .#{$ns}Checkbox {
      margin-right: 0;
    }

    &::before {
      display: none;
    }
  }

  table.is-compact {
    th, td {
      padding: var(--TableCell-paddingY-small) var(--TableCell-paddingX-small);
    }

    & > tbody > tr > td:last-child {
      padding-right: var(--TableCell-paddingX-small);
    }
  }

  .bg-cell-table {
    .#{$ns}Table-table th,
    .#{$ns}Table-table td{
      border-right: var(--Table-borderWidth) solid var(--Table-borderColor);
    }

    .#{$ns}Table-table th:first-of-type,
    .#{$ns}Table-table td:first-of-type {
      border-left: var(--Table-borderWidth) solid var(--Table-borderColor);
    }

    td.is-checked {
      background-color: var(--Checkbox-onHover-color);
    }

    tr.is-sticky {
      position: sticky;
      top: 0;
      z-index: 100;
    }

    th.is-sticky,
    td.is-sticky {
      position: sticky;
      left: 0;
      z-index: 20;
    }

    .fixed-left-table td.is-sticky,
    .fixed-left-table th.is-sticky {
      &::after {
        position: absolute;
        top: 0;
        right: 0;
        bottom: -1px;
        width: 30px;
        transform: translateX(100%);
        transition: box-shadow .3s;
        content: "";
        pointer-events: none;

        box-shadow: var(--Table-fixedLeft-boxShadow);
      }
    }
  }

  .clear-btn {
    margin-top: 8px;
  }
}
