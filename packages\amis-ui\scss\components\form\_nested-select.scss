.#{$ns}NestedSelectControl {
  position: relative;
}

.#{$ns}NestedSelect {
  position: relative;
  @include input-border();

  &-optionArrowRight {
    display: inline-block;
    padding-left: var(--Form-select-icon-left);

    svg {
      width: px2rem(12px);
      height: px2rem(12px);
      fill: var(--Form-input-iconColor);
      color: var(--Form-select-caret-iconColor);
    }
  }

  &-optionLoading {
    display: inline-flex;
    align-items: center;
    padding-left: var(--Form-select-icon-left);
  }

  &-menuOuter {
    display: flex;
  }

  &-noResult {
    width: px2rem(160px);
    padding: 0 var(--gap-xs);
    color: var(--Form-select-placeholderColor);
    line-height: var(--Form-input-lineHeight);
    font-size: var(--Form-select-input-fontSize);
    user-select: none;
    padding: calc(
        (
            var(--Form-select-menu-height) - var(--Form-input-lineHeight) *
              var(--Form-input-fontSize)
          ) / 2
      )
      var(--Form-select-paddingX);
  }

  &-menu {
    min-height: px2rem(32px);
    max-height: px2rem(175px);
    padding: var(--Form-select-menu-padding);
    background: var(--Form-select-menu-bg);
    color: var(--Form-select-menu-color);
    overflow-y: auto;
    overflow-x: hidden;

    &:not(:first-child) {
      border-left: 1px solid var(--borderColorLight);
    }

    .#{$ns}NestedSelect-title {
      padding-left: var(--gap-md);
      line-height: var(--select-base-default-option-line-height);
      font-size: var(--select-base-default-option-fontSize);
      font-weight: bold;
      color: var(--select-base-default-option-color);
      background: var(--select-base-default-option-bg-color);
      max-width: 10em;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .#{$ns}NestedSelect-option {
      position: relative;
      padding: 0 var(--gap-md);
      min-height: var(--select-base-default-option-line-height);
      line-height: var(--select-base-default-option-line-height);
      cursor: pointer;
      display: flex;
      font-size: var(--select-base-default-option-fontSize);
      font-weight: var(--select-base-default-option-fontWeight);
      color: var(--select-base-default-option-color);
      background: var(--select-base-default-option-bg-color);

      > .#{$ns}NestedSelect-optionLabel {
        flex: 1;
        height: px2rem(32px);
        width: max-content;
        overflow: hidden;
        text-overflow: ellipsis;
        &.is-disabled {
          cursor: not-allowed;
          color: var(--text--muted-color);
        }
      }
      .#{$ns}NestedSelect-optionLabel-highlight {
        color: var(--Form-select-menu-onActive-color);
      }

      &.is-active {
        color: var(--Form-select-menu-onActive-color) !important;
        background: var(--Form-select-menu-onActive-bg);
      }

      &.#{$ns}NestedSelect-option-hover,
      &:hover {
        color: var(--Form-select-menu-onHover-color);
        background: var(--Form-select-menu-onHover-bg);
      }

      &:hover > .#{$ns}NestedSelect-childrenOuter {
        display: block;
      }

      &.no-result {
        justify-content: center;
        cursor: default;
        color: var(--Form-select-placeholderColor);
        &:hover {
          color: unset;
          background: unset;
        }
      }

      &.is-disabled {
        cursor: not-allowed;

        &:hover {
          color: var(--text--muted-color);
          background: var(--select-base-default-option-bg-color);
        }
      }
    }
  }

  &-popover {
    border: none;
  }

  &-popup {
    height: px2rem(460px);
  }
}
