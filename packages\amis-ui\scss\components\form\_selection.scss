.#{$ns}Selection {
  > .#{$ns}Checkbox {
    display: block;
    // height: var(--Form-input-height);
    line-height: var(--Form-input-lineHeight);
    font-size: var(--Form-input-fontSize);
    padding: calc(
        (
            var(--Form-input-height) - var(--Form-input-lineHeight) *
              var(--Form-input-fontSize)
          ) / 2
      )
      var(--gap-sm)
      calc(
        (
            var(--Form-input-height) - var(--Form-input-lineHeight) *
              var(--Form-input-fontSize)
          ) / 2
      )
      calc(var(--gap-sm) + var(--Checkbox-size));
  }

  &--inline > .#{$ns}Checkbox {
    display: inline-block;
  }

  &-addBtn {
    display: block;
    cursor: pointer;

    &:hover {
      text-decoration: none;
    }

    > svg {
      width: px2rem(14px);
      height: px2rem(14px);
      margin-right: var(--Checkbox-gap);
    }
  }
}

.#{$ns}GroupedSelection {
  max-height: px2rem(300px);
  height: 100%;
  overflow: auto;
  user-select: none;
  padding: var(--gap-xs) 0;

  &-group > &-itemLabel {
    padding: var(--select-group-paddingTop) var(--select-group-paddingRight)
      var(--select-group-paddingBottom) var(--select-group-paddingLeft);
    color: var(--select-group-color);
    font-size: var(--select-group-fontSize);
    font-weight: var(--select-group-fontWeight);
    line-height: var(--select-group-lineHeight);
  }

  &-item {
    display: flex;
    color: var(--select-base-default-option-color);
    background: var(--select-base-default-option-bg-color);
    line-height: var(--select-base-default-option-line-height);
    font-size: var(--select-base-default-option-fontSize);
    font-weight: var(--select-base-default-option-fontWeight);
    padding: var(--select-base-default-option-paddingTop)
      var(--select-base-default-option-paddingRight)
      var(--select-base-default-option-paddingBottom)
      var(--select-base-default-option-paddingLeft);
    flex-direction: row;

    > .#{$ns}Checkbox {
      margin-right: px2rem(10px);
    }
    cursor: pointer;
    user-select: none;

    &:hover {
      color: var(--Form-select-menu-onHover-color);
      background: var(--Form-select-menu-onHover-bg);

      i {
        border: 1px solid var(--menu-active-color);
      }
    }

    &.is-active,
    &.is-focused,
    &.is-opened {
      color: var(--Form-select-menu-onActive-color);
      background: var(--Form-select-menu-onActive-bg);
    }

    &.is-disabled {
      pointer-events: none;
      color: var(--Form-select-menu-onDisabled-color);
      background: var(--Form-select-menu-onDisabled-bg);
    }
  }

  &-group > &-items > &-item {
    padding-left: calc(var(--gap-base) * 2);
  }

  &-itemLabel {
    flex-grow: 1;

    span {
      vertical-align: middle;
      white-space: nowrap; // 避免在条件组合中折行

      &.is-invalid {
        color: var(--Form-selectValue-onInvalid-color);
      }
    }
  }

  &-placeholder {
    @include checkboxes-placeholder();
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.#{$ns}TableSelection {
  position: relative;
  height: 100%;

  .#{$ns}Table-content {
    border-top: var(--Table-borderWidth) solid var(--Table-borderColor);

    &.is-virtual {
      height: 100%;
      display: flex;
      flex-direction: column;

      .#{$ns}Table-content-virtual {
        flex: 1;
        position: relative;
      }
    }
  }

  .#{$ns}Table-table > tbody > tr {
    cursor: pointer;
  }

  .#{$ns}Table-table > thead > tr > th,
  .#{$ns}Table-table > tbody > tr > td {
    color: var(--select-table-color);
    font-size: var(--select-table-fontSize);
    vertical-align: middle;
  }

  .#{$ns}Table-table > thead > tr > th {
    padding: var(--select-table-header-paddingTop)
      var(--select-table-header-paddingLeft)
      var(--select-table-header-paddingBottom)
      var(--select-table-header-paddingLeft);
  }

  .#{$ns}Table-table > tbody > tr > td {
    padding: var(--select-table-option-paddingTop)
      var(--select-table-option-paddingLeft)
      var(--select-table-option-paddingBottom)
      var(--select-table-option-paddingLeft);
  }

  .#{$ns}Table-table > thead > tr > th:last-child {
    padding-right: var(--select-table-header-paddingRight);
  }

  .#{$ns}Table-table > tbody > tr > td:last-child {
    padding-right: var(--select-table-option-paddingRight);
  }

  .#{$ns}Table-table > tbody > tr.is-active > td {
    color: var(--Form-select-menu-onActive-color);
    background: var(--Form-select-menu-onActive-bg);
  }

  .#{$ns}Table-table > tbody > tr.is-disabled > td {
    pointer-events: none;
    color: var(--Form-select-menu-onDisabled-color);
    background: var(--Form-select-menu-onDisabled-bg);
  }

  .#{$ns}Table-table > tbody > tr:hover > td,
  .#{$ns}Table-table > tbody > tr.is-hovered > td {
    color: var(--Form-select-menu-onHover-color);
    background: var(--Form-select-menu-onHover-bg);
  }
}

.#{$ns}TreeSelection {
  .#{$ns}Table-expandBtn {
    color: var(--icon-color);
    margin-right: var(--gap-xs);
  }

  &-sublist {
    position: relative;
    margin: 0 0 0 px2rem(35px);
    display: none;
  }

  &-item {
    position: relative;
  }
  &-item.is-expanded > &-sublist {
    display: block;
  }

  &-item:not(:last-child) > &-sublist:before {
    bottom: 0;
  }

  &-item.is-disabled > .#{$ns}TreeSelection-itemInner {
    cursor: not-allowed;
    color: var(--text--muted-color);
  }

  &-item .#{$ns}TreeSelection-itemLabel {
    white-space: nowrap;
  }

  &-itemInner {
    display: flex;
    align-items: center;
    line-height: var(--Tree-itemHeight);
    position: relative;
    font-size: var(--Form-input-fontSize);
    padding: 0 var(--gap-sm);
    flex-direction: row;

    > .#{$ns}Checkbox {
      margin-right: var(--gap-sm);
    }
    cursor: pointer;
    user-select: none;

    &:hover {
      background: var(--Tree-item-onHover-bg);
    }

    &.is-active {
      color: var(--Form-select-menu-onActive-color);
    }

    &.is-disabled {
      pointer-events: none;
      color: var(--text--muted-color);
    }

    .#{$ns}TreeSelection-itemLabel {
      margin-left: var(--gap-xs);
    }
  }

  &-itemLabel {
    flex-grow: 1;

    span {
      vertical-align: middle;
    }
  }

  &-placeholder {
    @include checkboxes-placeholder();
  }
}

.#{$ns}ChainedSelection {
  display: flex;
  flex-direction: row;
  min-height: 100%;
  flex-wrap: wrap;

  &-col {
    position: relative;
    flex-grow: 1;
    min-width: 150px;
    width: 0;
  }

  &-col:not(:last-child) {
    border-right: 1px solid var(--borderColor);
  }

  &-subTitle {
    font-size: var(--fontSizeSm);
    padding: var(--gap-xs) var(--gap-xs);
    color: var(--text--muted-color);
  }

  &-checkAll {
    width: 100%;
  }

  &-item {
    display: flex;
    // height: var(--Form-input-height);
    font-size: var(--select-base-default-option-fontSize);
    font-weight: var(--select-base-default-option-fontWeight);
    color: var(--select-base-default-option-color);
    background: var(--select-base-default-option-bg-color);
    line-height: var(--select-base-default-option-line-height);
    padding: var(--select-base-default-option-paddingTop)
      var(--select-base-default-option-paddingRight)
      var(--select-base-default-option-paddingBottom)
      var(--select-base-default-option-paddingLeft);
    flex-direction: row;

    > .#{$ns}Checkbox {
      margin-right: 0;
    }
    cursor: pointer;
    user-select: none;

    &.is-active {
      color: var(--Form-select-menu-onActive-color);
      background: var(--Form-select-menu-onActive-bg);
    }

    &:hover {
      color: var(--Form-select-menu-onHover-color);
      background: var(--Form-select-menu-onHover-bg);
    }

    &.is-disabled {
      pointer-events: none;
      color: var(--Form-select-menu-onDisabled-color);
      background: var(--Form-select-menu-onDisabled-bg);
    }
  }

  &-itemLabel {
    flex-grow: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    span {
      margin-left: px2rem(10px);
      vertical-align: middle;

      &.is-invalid {
        color: var(--Form-selectValue-onInvalid-color);
      }
    }
  }

  &-placeholder {
    @include checkboxes-placeholder();
  }
}

.#{$ns}AssociatedSelection {
  display: flex;
  flex-direction: row;

  &-left,
  &-right {
    position: relative;
    flex-grow: 1;
    width: 0;
    min-height: px2rem(200px);
    max-height: px2rem(400px);
    overflow: auto;

    > .#{$ns}GroupedSelection {
      padding: 0;
      max-height: 100%;
    }
  }

  &-left {
    border-right: 1px solid var(--borderColor);
  }

  &-reload {
    text-align: center;
    color: var(--info);
    margin: 20px 0 0;

    &.is-clickable {
      cursor: pointer;
    }
  }

  &-box {
    line-height: var(--Form-input-lineHeight);
    font-size: var(--fontSizeSm);
    color: var(--text--muted-color);
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;

    > p {
      text-align: center;
      margin: 10px 0 20px;
      color: var(--text--muted-color);
    }
  }
}

.#{$ns}ResultTreeList {
  height: 100%;
  width: 100%;
}

.#{$ns}ResultTableList {
  height: 100%;
  width: 100%;

  &-close-btn {
    float: right;
    cursor: pointer;
  }
}
