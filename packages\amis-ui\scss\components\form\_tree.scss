.#{$ns}TreeControl {
  border: 1px solid var(--Form-input-borderColor);
  padding: 6px 12px;
  border-radius: 2px;
  max-height: var(--Tree-max-height);
  overflow: auto;

  &.h-full {
    max-height: none;
    overflow: visible;
  }

  &.no-border {
    border: 0;
  }

  &.is-sticky {
    max-height: unset;
    overflow: unset;
  }

  &-searchbox {
    margin-top: var(--gap-sm);
    margin-bottom: var(--gap-md);

    &.is-active {
      width: 100%;
    }
  }

  & > .#{$ns}Tree {
    max-height: var(--Tree-max-height);
    overflow: auto;
  }

  & > .#{$ns}Tree--auto-fill-height.is-searchable {
    height: calc(100% - var(--gap-sm) - var(--gap-md) - 30px);
    max-height: calc(100% - var(--gap-sm) - var(--gap-md) - 30px);
  }

  & > .#{$ns}Tree--auto-fill-height {
    height: 100%;
    max-height: 100%;

    .#{$ns}Tree-list {
      height: 100%;
    }
  }
}

.#{$ns}Tree {
  &-list,
  &-sublist {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  &-sublist {
    margin-left: var(--Tree-indent);
  }

  &-sublist.is-folded {
    display: none;
  }

  &-item {
    display: flex;
    line-height: var(--Tree-itemHeight);
    position: relative;

    > div {
      &:hover {
        text-decoration: none;

        > .#{$ns}Tree-item-icons {
          visibility: visible;
        }
      }

      > span > svg {
        display: inline-block;
        cursor: pointer;
        position: relative;
        top: 2px;
        width: px2rem(16px);
        height: px2rem(16px);
        margin-left: var(--Tree-icon-gap);
      }
    }
  }

  &.is-disabled {
    // pointer-events: none; // 禁用后hover效果不生效

    .#{$ns}Tree-itemArrow {
      pointer-events: auto;
    }
  }

  &.is-draggable {
    position: relative;
  }

  &--outline &-item--isLeaf.is-child {
    &:before {
      position: absolute;
      top: -8px;
      left: calc(
        (
            var(--Tree-icon-gap) + var(--Tree-itemArrowWidth) +
              var(--Tree-icon-margin-right)
          ) / 2 - var(--Tree-indent)
      );
      bottom: 0px;
      border-left: 1px solid var(--borderColor);
      content: '';
    }
  }

  &-rootItem {
    line-height: var(--Tree-itemHeight);
  }

  &-item > div:hover > .#{$ns}Tree-item-icons,
  &-rootItem > div:hover > .#{$ns}Tree-item-icons {
    visibility: visible;
  }

  &-itemLabel {
    display: flex;
    flex-direction: row;
    align-items: center;
    position: relative;
    user-select: none;
    border-bottom: px2rem(4px) solid transparent;
    padding-left: var(--Tree-icon-gap);
    & > * {
      position: relative;
      // z-index: 2; // 去除层叠上下文，解决inputTree悬浮按钮被下一个元素覆盖问题
    }
    > .#{$ns}Checkbox {
      display: inline-flex;
      align-self: center;
    }
    &-item {
      padding-left: var(--Tree-item-arrow-padding-left);
      display: inline-flex;
      align-items: center;
      // overflow: hidden;
      min-height: px2rem(32px); // fix: issue#719
    }

    .is-block-node &-item {
      flex: auto;
    }

    &:hover {
      .#{$ns}Tree {
        &-item-icons {
          visibility: visible;
        }
      }
    }
  }

  .is-block-node &-itemLabel {
    flex: auto;
  }

  &-item {
    .#{$ns}Tree {
      &-itemLabel:hover {
        background-color: var(--Tree-item-onHover-bg);
      }
      &-itemLabel.is-checked {
        background-color: var(--Tree-item-onChekced-bg);
      }
    }

    .is-checked {
      border-radius: var(--Tree-item-onChekced-bg-borderRadius);
      .#{$ns}Tree {
        &-item-icons {
          visibility: visible;
        }
      }
    }
    .is-disabled {
      color: var(--text--muted-color);
      background: none;
      &:hover {
        background: none;
      }
    }
  }

  &.is-draggable &-itemLabel:hover::after {
    display: none;
  }

  &-item-icons {
    visibility: hidden;
    transition: visibility var(--animation-duration) ease;
    display: inline-block;
    vertical-align: top;
    height: var(--Tree-itemHeight);
    line-height: var(--Tree-itemHeight);
    padding-right: var(--Tree-icon-gap);
    flex-shrink: 0;

    &.always-show {
      visibility: visible;
    }

    > a {
      display: inline-block;
      vertical-align: middle;
      margin-left: var(--Tree-icon-gap);
      cursor: pointer;
      > svg {
        $svgSize: px2rem(12px);
        width: $svgSize;
        height: $svgSize;
        top: 0;
      }
    }
  }

  &-itemInput {
    padding-left: var(--Tree-itemArrowWidth);
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;

    > a {
      display: inline-block;
      cursor: pointer;
      margin-left: var(--Tree-icon-gap);
      color: var(--icon-color);

      &:hover {
        color: var(--icon-onHover-color);
        text-decoration: none;
      }
    }

    > input {
      flex-grow: 1;
      width: 100%;
      outline: none;
      background: var(--Form-input-bg);
      border: var(--Form-input-borderWidth) solid var(--Form-input-borderColor);
      border-radius: var(--Form-input-borderRadius);
      line-height: var(--Form-input-lineHeight);
      padding: calc(
          (
              var(--Tree-inputHeight) - var(--Form-input-lineHeight) *
                var(--Form-input-fontSize) - #{px2rem(2px)}
            ) / 2
        )
        var(--Form-input-paddingX);
      font-size: var(--Form-input-fontSize);

      &::placeholder {
        color: var(--Form-input-placeholderColor);
        user-select: none;
      }

      &:focus {
        border-color: var(--Form-input-onFocused-borderColor);
        box-shadow: var(--Form-input-boxShadow);
        background: var(--Form-input-onFocused-bg);
      }
    }
  }

  &-addTopBtn {
    cursor: pointer;
    height: var(--Tree-itemHeight);
    line-height: var(--Tree-itemHeight);
    display: block;

    &:hover {
      text-decoration: none;
    }

    &.is-disabled {
      pointer-events: none;
      color: var(--text--muted-color);
    }

    > svg {
      $svgSize: px2rem(14px);
      width: $svgSize;
      height: $svgSize;
      top: $svgSize * 0.125;
      margin-right: var(--Tree-icon-gap);
    }
  }

  &-itemArrow {
    cursor: pointer;
    width: var(--Tree-itemArrowWidth);
    display: inline-flex;
    margin-right: var(--Tree-icon-margin-right);

    > * {
      width: var(--Tree-itemArrowWidth);
      height: var(--Tree-itemArrowWidth);
      display: block;
      transition: transform var(--animation-duration);
      top: 0;
      color: var(--Tree-item-arrow-color);
    }

    &.is-folded > * {
      transform: rotate(-90deg);
    }
  }

  &-itemArrowPlaceholder {
    display: inline-block;
    width: calc(var(--Tree-itemArrowWidth) + var(--Tree-icon-gap));
  }

  &-itemDrager {
    cursor: move;
    color: var(--icon-color);
  }

  &-spinner {
    margin-right: var(--Tree-icon-gap);
  }

  &-itemIcon {
    display: inline-flex;
    margin-right: var(--Tree-icon-margin-right);
  }

  &-rootIcon,
  &-folderIcon,
  &-leafIcon {
    > svg {
      top: 0;
      width: px2rem(14px);
      height: px2rem(14px);
    }
  }

  &-itemText {
    cursor: pointer;
    display: inline-block;
    color: var(--select-tree-color);
    font-size: var(--select-tree-fontSize);
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .is-disabled &-itemText {
    color: var(--text--muted-color);
  }
  .is-block-node &-itemText {
    flex: auto;
  }

  &-placeholder {
    color: var(--text--muted-color);
  }

  &-dropIndicator {
    position: absolute;
    height: px2rem(2px);
    background-color: var(--Tree-itemLabel--onChecked-color);
    border-radius: px2rem(1px);
    z-index: 10;

    &::after {
      position: absolute;
      top: px2rem(-3px);
      left: px2rem(-6px);
      width: px2rem(8px);
      height: px2rem(8px);
      background-color: transparent;
      border: px2rem(2px) solid var(--Tree-itemLabel--onChecked-color);
      border-radius: 50%;
      content: '';
    }
  }
}
