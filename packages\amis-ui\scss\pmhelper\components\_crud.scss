.pm-crud-advanced-search-text {
  color: #6c757d;
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}

.pm-crud-left-right-layout {
  height: 100%;
  flex: 1 1 0;
  overflow: auto;
}

// headerToolbar在最上面
.pm-no-padding-headerToolbar {
  .#{$ns}Table-contentWrap {
    padding: 0;
    padding-top: 16px;

    .#{$ns}Table-fixedLeft.in {
      padding-left: 0;
    }

    .#{$ns}Table-fixedRight.in {
      padding-right: 0;
    }
  }

  // fix: issue#538 表头固顶时样式问题
  .#{$ns}Table-fixedTop {
    .#{$ns}Table-wrapper {
      padding: 16px 0 0;
    }

    &:after {
      width: 100%;
      margin-left: 0;
    }
  }

  .#{$ns}Table-heading {
    padding: 0;
  }

  .#{$ns}Table-headToolbar {
    padding: 0;
    padding-bottom: 1px;
  }

  .#{$ns}Crud-toolbar {
    margin-top: 0;
  }

  .#{$ns}Crud-selection {
    padding: 16px 0 0 0;
  }

  .#{$ns}Crud-toolbar-item {
    margin-top: 0;
  }
}


.pm-no-padding-title {
  .#{$ns}Table-contentWrap {
    padding: 0;
    padding-top: 16px;

    .#{$ns}Table-fixedLeft.in {
      padding-left: 0;
    }

    .#{$ns}Table-fixedRight.in {
      padding-right: 0;
    }
  }

  .#{$ns}Table-fixedTop {
    .#{$ns}Table-wrapper {
      padding: 16px 0 0;
    }

    &:after {
      width: 100%;
      margin-left: 0;
    }
  }

  .#{$ns}Table-footToolbar {
    padding-bottom: 0;
  }

  .#{$ns}Table-heading {
    padding: 0;
  }

  .#{$ns}Crud-selection {
    padding: 16px 0 0 0;
  }
}


.pm-no-padding {
  .#{$ns}Table-contentWrap {
    padding: 0;

    .#{$ns}Table-fixedLeft.in {
      padding-left: 0;
    }

    .#{$ns}Table-fixedRight.in {
      padding-right: 0;
    }
  }

  .#{$ns}Table-footToolbar {
    padding-bottom: 0;
  }
}

// 去除所有的左右padding 上方有东西
.pm-no-padding-but-top {
  .#{$ns}Table-contentWrap {
    padding: 0;
    padding-top: 16px;

    .#{$ns}Table-fixedLeft.in {
      padding-left: 0;
    }

    .#{$ns}Table-fixedRight.in {
      padding-right: 0;
    }
  }

  // fix: issue#538 表头固顶时样式问题
  .#{$ns}Table-fixedTop {
    .#{$ns}Table-wrapper {
      padding: 16px 0 0;
    }

    &:after {
      width: 100%;
      margin-left: 0;
    }
  }

  .#{$ns}Table-headToolbar {
    padding-left: 0;
    padding-right: 0;
  }

  .#{$ns}Crud-selection {
    padding: 16px 0 0 0;
  }

  .#{$ns}Table-heading {
    padding-left: 0;
    padding-right: 0;
    padding-bottom: 0;
  }

  .#{$ns}Table-footToolbar {
    padding-bottom: 0;
  }
}
// 去除crud 嵌套subtable的padding
.pm-curdTable-model {
  .sub-Table{
    .#{$ns}Table-contentWrap {
      padding: 0;
    }
  }

}
// .#{$ns}Crud-top-header {
//   .pm-buttonGap-lg{
//     margin-right: 24px;
//   }
//   .pm-buttonGap-md{
//     margin-right: 16px;
//   }
//   .pm-buttonGap-sm{
//     margin-right: 8px;
//   }
// }

.pm-button-group-container{
  // one
  .pm-buttonGap-one-lg{
    margin-right: 35px;
    .#{$ns}Button{
      margin-right: 0;
    }
  }
  .pm-buttonGap-one-md{
    margin-right: 27px;
    .#{$ns}Button{
      margin-right: 0;
    }
  }
  .pm-buttonGap-one-sm{
    margin-right: 19px;
    .#{$ns}Button{
      margin-right: 0;
    }
  }
  // two
  .pm-buttonGap-two-lg{
    margin-right: 38px;
    .#{$ns}Button{
      margin-right: 0;
    }
  }
  .pm-buttonGap-two-md{
    margin-right: 30px;
    .#{$ns}Button{
      margin-right: 0;
    }
  }
  .pm-buttonGap-two-sm{
    margin-right: 22px;
    .#{$ns}Button{
      margin-right: 0;
    }
  }
  // three
  .pm-buttonGap-three-lg{
    margin-right: 42px;
    .#{$ns}Button{
      margin-right: 0;
    }
  }
  .pm-buttonGap-three-md{
    margin-right: 34px;
    .#{$ns}Button{
      margin-right: 0;
    }
  }
  .pm-buttonGap-three-sm{
    margin-right: 26px;
    .#{$ns}Button{
      margin-right: 0;
    }
  }
}

.pm-filter-right {
  // 清除浮动
  .#{$ns}Table-headerContainer::after {
    content: '';
    display: block;
    clear: both;
  }

  .#{$ns}Crud-header-filter {
    text-align: right;
    float: right;
    width: 50%;
  }
}

