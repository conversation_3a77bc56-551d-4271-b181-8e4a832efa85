.standard-Form .antd-Button,
.standard-Button {
  // 当为button组件配置为["link", "text"]模式时，设置高度为auto且处理换行问题
  &.antd-Button--link,
  &.antd-Button--text {
    text-align: left;
    height: auto;

    &.antd-Button--link-noPadding {
      padding: 0;
    }
  }

  // 当为button组件配置为"text"模式时，去除背景色
  &.antd-Button--text {
    background: transparent;
    border-width: 0;
    box-shadow: none;

    &:hover {
      background: transparent !important;
    }
  }
}

// 在DropDown内使用button时，需要添加边距
.standard-Form .antd-DropDown-menu .antd-Button {
  &.antd-Button--link,
  &.antd-Button--text {
    padding: 0 var(--button-size-default-paddingRight) 0
      var(--button-size-default-paddingLeft);
    line-height: var(--DropDown-menu-height);
  }
}
.antd-DropDown-menu .standard-Button,
.antd-DropDown-menu .standard-Link {
    padding: 0 var(--button-size-default-paddingRight) 0
    var(--button-size-default-paddingLeft) !important;
    line-height: var(--DropDown-menu-height);
  }

.antd-DropDown-menu .antd-DropDown-button:has(> .standard-Button),
.antd-DropDown-menu .antd-DropDown-button:has(> .standard-Link) {
  padding: 0;
}
