.standard-Form .antd-Button,
.standard-Button {
  // 当为button组件配置为["link", "text"]模式时，设置高度为auto且处理换行问题
  &.antd-Button--link,
  &.antd-Button--text {
    // white-space: break-spaces;
    text-align: left;
    height: auto;

    &.antd-Button--link-noPadding {
      padding: 0;
    }
  }

  // 当为button组件配置为"text"模式时，去除背景色
  &.antd-Button--text {
    background: transparent;
    border-width: 0;
    box-shadow: none;

    &:hover {
      background: transparent !important;
    }
  }
}

// 操作列配置button按钮时，不换行
// .standard-Crud .antd-Table-content .antd-OperationField .antd-Button,
// .standard-Form  .antd-InputTable .antd-Button {
//   &.antd-Button--link,
//   &.antd-Button--text {
//     white-space: nowrap;
//   }
// }

// 在DropDown内使用button时，需要添加边距
.antd-DropDown-menu .standard-Button,
.antd-DropDown-menu .standard-Link {
  padding: var(--button-size-default-paddingTop)
    var(--button-size-default-paddingRight)
    var(--button-size-default-paddingBottom)
    var(--button-size-default-paddingLeft);

  &.antd-Button--link,
  &.antd-Button--text {
    padding: var(--button-size-default-paddingTop)
      var(--button-size-default-paddingRight)
      var(--button-size-default-paddingBottom)
      var(--button-size-default-paddingLeft);
  }
}

.antd-DropDown-menu .antd-DropDown-button:has(> .standard-Button),
.antd-DropDown-menu .antd-DropDown-button:has(> .standard-Link) {
  padding: 0;
}
