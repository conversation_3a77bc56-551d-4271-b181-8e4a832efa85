// 卡片组件的默认的规范样式，修改标题文字大小
.standard-Card,
.standard-Cards .antd-Card,
.standard-Crud-body .antd-Card {
  .standard-Card-header {
    border-radius: var(--Card-borderRadius) var(--Card-borderRadius) 0 0;

    .antd-Card-avtar {
      &.avatar--square img {
        border-radius: 0;
      }
      &.avatar--rounded img {
        border-radius: 10%;
      }
    }

    .antd-Card-avtarText {
      width: 64px;
      height: 64px;
      line-height: 64px;

      &.avatar--square {
        border-radius: 0;
      }
      &.avatar--rounded {
        border-radius: 10%;
      }
    }
  }

  .antd-Card-heading + .antd-Card-body {
    padding-top: 16px;
  }

  // 只有标题区域和底部区域，无内容时，需要去除底部上边框
  .antd-Card-heading + .antd-Card-footer-wrapper .antd-Card-actions {
    border-top: none;
  }
  // 在card组件外层使用的是flex布局，如果左侧内容过多，回挤压thumb-md（头像）区域内容，导致每个头像的大小不一致，因此设置不缩放
  .thumb-md {
    flex-shrink: 0;
  }

  // 配置了多媒体的场景，需要去除头部下边框及处理边距
  &:has(.antd-Card-multiMedia--left) {
    .antd-Card-heading {
      border-bottom: none;
      padding: 1rem;

      &:has(+ .antd-Card-body) {
        padding-bottom: 0;
      }
    }

    .antd-Card-actions-wrapper .antd-Card-actions .antd-Card-action {
      margin-right: 8px;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}

// 1. crud的卡片模式下；2. cards组件；默认一行一个时，无边框
.standard-Card,
.standard-Cards.single-columns .antd-Card,
.standard-Crud-body.single-columns .antd-Card {
  border: none;
}

// 1. crud的卡片模式下；2. cards组件；如果标题区域使用Title容器配置，需要去除Title容器默认的边距
.standard-Crud-body,
.standard-Cards .antd-Card {
  .standard-Title {
    padding: 0;
    margin: 0;

    .antd-Panel-body {
      padding: 0;

      .antd-TplField.font-bold {
        margin-left: 0;
      }
    }
  }
  .standard-Form .standard-Form-body {
    padding: 0;
  }
}

// 卡片组下最后一个卡片需要去除下边距
.standard-Cards .antd-Cards-body,
.standard-Crud-body .antd-Cards-body {
  row-gap: 16px;
  .antd-Card {
    margin-bottom: 0;

    .antd-Card-heading {
      flex-grow: 0;
    }
  }
}

// 分组容器内嵌套小分组时，card标题文字大小修改
.standard-GroupContainer .antd-Card:not(.antd-Card--link) {
  margin-bottom: 0;
  border: none;

  .standard-Card-header {
    padding: 16px;
    border-bottom: none;
  }
}

// card组件内部嵌套tabs组件时，去除边框
.standard-Card:has(.standard-Tabs) {
  border: none;

  .standard-Card-header {
    border-bottom: none;
  }

  .standard-Card-body {
    padding: 0;
  }
}

// 白底背景容器下，去除card的边框
.antd-Panel-body,
.antd-Tabs,
.antd-Wizard,
.antd-Modal,
.antd-Drawer,
.standard-Wrapper--white {
  .standard-Card {
    border: none;
  }

  .standard-Cards:not(:has(.single-columns)) .antd-Card {
    border: 1px solid rgb(217, 217, 217);
  }
}

// crud的卡片模式，一行展示多个卡片时，添加边框
.antd-Cards.standard-Crud-body:not(.single-columns) .antd-Card {
  border: 1px solid rgb(217, 217, 217);
}
