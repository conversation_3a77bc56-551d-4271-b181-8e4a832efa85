.standard-Crud {
  .antd-Table-headerContainer {
    // 当crud设置title属性时，去除边距
    .antd-Table-heading {
      padding: 0;
    }

    // 当crud在headerFilter中设置ButtonGroupSelect时，需要设置ButtonGroupSelect所在的表单项右边距为0
    .antd-Crud-header-filter {
      .antd-Form-item:has(.antd-ButtonGroup) {
        margin-right: 0;
      }
    }
  }

  // 默认是15px，在白底背景下底部有部分阴影被遮住了，设置下
  & > .antd-Crud-filter,
  & > .standard-Crud-body > .antd-Table-searchableForm {
    position: relative;
    z-index: 1;
  }

  // 去掉 搜索表单，底部 marginTop，保持 搜索表单 上/下 margin 一致
  & > .antd-Crud-filter,
  &-body > .antd-Table-searchableForm {
    .antd-Panel-footerWrap {
      margin-top: 0;
    }
  }

  // crud的卡片模式下，标题区域使用Title容器，需要去除Title容器默认的边距
  .standard-Crud-body {
    .antd-Cards-body:has(.standard-Form-body) {
      .standard-Title {
        padding: 0;
        margin: 0;
        .antd-Panel-body {
          padding: 0;
        }
      }
    }

    .antd-Table-table {
      // crud的列标题配置remark时，设置水平间距为8
      thead .antd-TableCell--title .antd-Remark {
        margin-left: var(--gap-sm);
        margin-right: 0;
      }
      // crud的列内容配置remark时，设置Remark自身边距为0
      tbody .standard-Flex--row-gap .antd-Remark {
        margin-right: 0;
        margin-left: 0;
      }
    }

    &.antd-Table {
      > .antd-Table-footToolbar {
        padding-left: 16px;
        padding-right: 16px;
      }
    }

    .antd-Table-fixedTop {
      .antd-Table-wrapper {
        width: 100%;
      }
    }
  }
  // 嵌套子表格带时候，表格和单元格的padding重叠，出现两个间距
  .sub-table-row .sub-Table {
    margin-bottom: 0;

    .antd-Crud-body {
      margin-bottom: 0;
    }

    .antd-Table-toolbar {
      padding-bottom: 0;
    }
  }

  // crud的卡片模式，style设置了
  .antd-Cards.standard-Crud-body {
    > div:has(.antd-Cards-toolbar-header) {
      position: unset !important;
      z-index: unset !important;
    }
  }

  // 卡片模式无数据时，底部边距需要设置间距
  .antd-Cards-toolbar-footer {
    padding: var(--Table-toolbar-marginY) var(--Table-toolbar-marginX);
  }
}

// 下面这些容器嵌套crud时，因为容器本身有内边距16px，需去掉Crud本身的左右边距、底边距
.antd-Form,
.antd-Wizard,
.antd-Tabs,
.antd-Modal,
.antd-Drawer,
.standard-Wrapper--white,
.standard-GroupContainer .standard-GroupContainer-item,
.standard-LeftRightContainer-left .standard-GroupContainer {
  .standard-Crud {
    // 解决 headerToolbar 场景下，向上间距和容器本身的间距累加的场景
    // 由于 子元素 antd-Table-headToolbar 设置高度，不能通过设置paddingTop为0的方案
    .antd-Table-headerContainer:not(:has(.antd-Table-heading)) {
      margin-top: calc(var(--Table-toolbar-marginY) * -1);
    }
    // 去除辅助内容操作区域的上边距
    .antd-Table-headerContainer .antd-Table-toolbar,
    .antd-Table-headerContainer .antd-Crud-header-filter,
    .antd-Table-headerContainer .antd-Crud-selection {
      padding-left: 0;
      padding-right: 0;
    }

    // 去除内容区域左右边距，因为tab内容区域有统一的内边距16
    .antd-Table-contentWrap {
      padding: 1rem 0;
    }

    // 去除底部外边距
    .standard-Crud-body {
      margin-bottom: 0;
    }

    .antd-Table-footToolbar {
      padding-bottom: 3px;
    }

    // 当在以上容器内嵌套Crud、且Crud底部footToolbar不展示时，需要去除Crud内容区域的下边距，因为外层容器有底部内边距
    &:last-of-type:not(:has(.antd-Table-footToolbar)) {
      // 去除内容区域左右边距，因为tab内容区域有统一的内边距16
      .antd-Table-contentWrap {
        padding-bottom: 0;
      }
    }

    // crud的卡片模式，在已有内边距的容器内需要去除边距
    .standard-Crud-body.antd-Cards {
      padding: 0;
    }
  }
}

// crud的查询区域使用between组件，需要去除between组件里内置的group下groupColumn的下边距
.antd-Table-searchableForm
  .antd-Form-group--horizontal
  > .antd-Form-groupColumn:has(.standard-Between),
.antd-Crud-filter .antd-Form-groupColumn:has(.standard-Between) {
  .standard-Between .antd-Form-groupColumn {
    margin: 0;
  }
}

// crud的表单查询区域整个group容器，左右边距在组件库之前规范被重写为0，出现的问题
// 1. 与antd-Table-searchableForm内的表单内容宽度对不上
// 2. 表单内使用group时，value区域表单项对不齐
// 因此这里重置为百度amis原来的样式，左右边距设置负值，因为内部的每个groupColumn有自己的内边距
.antd-Crud.standard-Crud .antd-Crud-filter .antd-Form-group--hor {
  margin-left: calc(var(--Form-group-gutterWidth) / -2);
  margin-right: calc(var(--Form-group-gutterWidth) / -2);
}
