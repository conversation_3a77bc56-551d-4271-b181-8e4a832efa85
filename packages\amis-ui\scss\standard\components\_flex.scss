.standard-Flex{
  // 实现space的效果
  // 横向使用flex 8px间隔
  &--row-gap {
    gap: 8px;
    & > .antd-Form-item {
      margin-bottom: 0;
    }

    // &.standard-Flex-gap-sm{
    //   gap: 8px;
    // }
    // &.standard-Flex-gap-md{
    //   gap: 16px;
    // }
    // &.standard-Flex-gap-lg{
    //   gap: 24px;
    // }
    // 内部去掉formItem多余的marginBottom


  }
  // 纵向 16px
  &--column-gap{
    gap: 16px;
    // &.standard-Flex-gap-sm{
    //   gap: 8px;
    // }
    // &.standard-Flex-gap-md{
    //   gap: 16px;
    // }
    // &.standard-Flex-gap-lg{
    //   gap: 24px;
    // }
    & > .standard-Flex-space-item {
      margin-top: 0;
      margin-bottom: 0;
      // 去掉纵向容器本身的上下margin
      & > * {
        margin-top: 0;
        margin-bottom: 0;
      }
    }
  }

  // .antd-Tabs-pane{
  //   padding-left: 0;
  //   padding-right: 0;
  //   padding-bottom: 0;
  // }

}
