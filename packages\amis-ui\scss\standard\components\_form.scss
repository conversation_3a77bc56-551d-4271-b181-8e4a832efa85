// 被 Tabs 嵌套时
.antd-Tabs {
  .standard-Form--basic {
    // footer 带边框
    & > .antd-Panel-body {
      padding: 0 0 16px 0;
    }
    & > .antd-Panel-footerWrap {
      background-color: #fff;
      border-top: 1px solid var(--borderColor);
    }
  }
}

.standard-LeftRightContainer-left {
  .antd-Panel-heading {
    padding-top: 0;
  }
}

.standard-Form {
  .antd-Panel-heading {
    background-color: #fff;
  }
  .standard-ButtonList {
    margin-bottom: 16px;
  }

  // 表单项组合多个组件垂直布局时，下边距设置为16
  .antd-Form-group--ver {
    .antd-Form-item:not(:last-child) {
      margin-bottom: 16px;
    }
  }

  // input-group 静态模式下，设置flex-grow: 0，并设置超出宽度时使用省略
  .antd-InputGroup {
    .antd-Form-static,
    .antd-Form-static .antd-Words-field.antd-Form-control {
      max-width: 100%;
      min-width: 20%;
      overflow-x: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      flex-grow: 0;
    }

    // 静态模式，且后接静态表单组件，则设置右间距
    .antd-Form-static:has(+ .antd-Form-static) {
      margin-right: 8px;
    }

    // 如果前后都是静态前后缀按钮，则取消右侧间距，并且设置最小宽度为0
    &:has(.antd-InputGroup-static-btn + .antd-Form-static),
    &:has(.antd-Form-static + .antd-InputGroup-static-btn) {
      .antd-Form-static {
        margin-right: 0;
        min-width: 0;
      }
    }

    // inputgroup内两个表单项都展示-时，去除后面的-，仅展示一个
    .antd-Form-static:has(.text-muted) + .antd-Form-static:has(.text-muted) {
      &:last-of-type {
        .text-muted {
          display: none;
        }
      }
    }
  }

  // form的label默认设置了line-height为32px，目的是为了跟右侧的表单组件垂直居中
  // 但在静态模式下，右侧为文字时，需要设置line-height统一为21px，否则左侧label与右侧文字对不齐的问题
  &.antd-Form--isStatic .antd-Form-item--horizontal,
  .antd-Form-item--horizontal:has(.antd-Form-static) {
    .antd-Form-static:not(.is-noPaddingY-static) {
      padding-top: 5px;
      padding-bottom: unset;
    }

    .antd-InputGroup-static-btn {
      height: 100%;
      line-height: 21px;
      padding-top: 5px;
    }

    .antd-Form-static {
      > .antd-FormulaPicker.is-static {
        vertical-align: unset;
      }
    }
  }
}
