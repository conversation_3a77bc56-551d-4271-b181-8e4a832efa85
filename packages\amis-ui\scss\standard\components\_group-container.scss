// 分组容器在form内的时候，单独处理下form的样式
.standard-Form:has(.standard-GroupContainer) {
  .standard-Form-body {
    padding: 0;
    background: transparent;
  }

  > .standard-Form-footer {
    border-top: 0;
  }
}

// 分组容器组件默认样式处理, 设置边框、边距
.standard-GroupContainer {
  > .#{$ns}Collapse {
    padding: 0 16px;
    margin-bottom: 16px;
    border: none;
    border-radius: var(--borderRadiusLg) var(--borderRadiusLg)
      var(--borderRadiusLg) var(--borderRadiusLg);

    // 默认的样式会给第一个分组仅添加左上角和右上角的border-radius，在灰底背景下左下角和右下角展示有问题，覆盖下
    &:first-child {
      border-radius: var(--borderRadiusLg) var(--borderRadiusLg)
        var(--borderRadiusLg) var(--borderRadiusLg);
    }

    &:last-of-type {
      margin-bottom: 0;
    }

    // 默认折叠分组样式
    > .#{$ns}Collapse-header {
      position: relative;
      display: flex;
      align-items: center;
      width: 100%;
      border: none;
      padding: 8px 0;
      line-height: 2;
      background-color: var(--white);

      // 标题区域除了左侧的默认的折叠器icon外，右侧标题、子标题、辅助内容、操作按钮区域使用标题组件
      // 去除标题组件的内边距和外边距
      .standard-Title {
        flex: 1;
        padding: 0;
        margin: 0;

        .antd-Panel-body {
          padding: 0;
        }
      }

      .#{$ns}Collapse-arrow-wrap {
        height: 2rem;
      }
    }

    > .#{$ns}Collapse-contentWrapper {
      padding: 0;
      > .antd-Collapse-body > .#{$ns}Collapse-content {
        font-size: 14px;
        padding: 0;
      }
    }

    // 分组展开的样式
    &.is-active {
      > .#{$ns}Collapse-header {
        position: relative;

        &::before {
          content: '';
          width: calc(100%);
          height: 1px;
          background-color: #f3f4f6;
          position: absolute;
          bottom: 0;
        }
      }

      > .#{$ns}Collapse-contentWrapper {
        padding: 16px 0;
      }
    }
  }

  > .#{$ns}Panel {
    margin-bottom: 16px;
    box-shadow: none;
    border-width: 0px;
    background-color: #fff;
    color: #58666e;

    &:last-of-type {
      margin-bottom: 0;
    }

    > .#{$ns}Panel-heading {
      display: flex;
      background-color: #fff;
      border-color: #f3f4f6;

      .standard-Title {
        flex: 1;
        padding: 0;
        margin: 0;

        .antd-Panel-body {
          padding: 0;
        }
      }
    }

    > .#{$ns}Panel-body {
      padding: 16px;
    }

    // 移动端样式同步
    &.is-mobile > .#{$ns}Panel-body {
      padding: px2rem(12px);
    }
  }

  & .antd-Table:not(:has(+ .antd-InputTable-toolbar)) {
    margin-bottom: 0;
  }

  &-Divider {
    display: none;
  }

  // 分组里表单项后跟小分组时，需要去除表单项下边距
  .standard-GroupContainer-item .antd-Form-group:has(+.standard-SmallGroupContainer),
  .standard-GroupContainer-item .antd-Form-item:has(+.standard-SmallGroupContainer) {
    margin-bottom: 0;
  }
}
// 小分组里标题和内容区域同时存在时，需要去除标题和内容区域间的间距，以及卡片底部的间距
.standard-Card.standard-SmallGroupContainer {
  .antd-Card-heading + .antd-Card-body {
    padding-top: 0;
    padding-bottom: 0;
  }
}

// Tabs、Wizard、Modal、Drawer、左右布局等容器嵌套分组容器时，处理分组容器的样
.antd-Tooltip,
.antd-Tabs,
.antd-Wizard,
.antd-Modal,
.antd-Drawer,
.standard-Wrapper--white,
.standard-LeftRightContainer-left {
  .standard-GroupContainer {
    > .#{$ns}Collapse,
    > .#{$ns}Panel {
      padding: 0;
      /**
       * 原有分组间的分割线是由divider实现，本次移除divider，改为修改分组内的panel样式实现
       * 设置内边距、外边距，还原divider上下外边距的样式
       * 只对panel设置下border
       */
      margin-bottom: 16px;
      padding-bottom: 15px;
      border-bottom: var(--Divider-width) var(--Divider-style) var(--Divider-color);
      border-radius: 0;

      &:last-child {
        // 最后一项panel移除下border、内外边距
        border-bottom: none;
        padding-bottom: 0;
        margin-bottom: 0;
      }

      > .#{$ns}Collapse-header {
        padding: 0;
      }

      > .#{$ns}Collapse-contentWrapper {
        padding: 0;
      }

      // 分组展开的样式
      &.is-active {
        > .#{$ns}Collapse-header {
          &::before {
            display: none;
          }
        }

        > .#{$ns}Collapse-contentWrapper {
          padding-top: 16px;
        }
      }
    }

    // 以下样式应用需要排除掉的场景：tabs > 无折叠分组 > crud带查询区域
    .#{$ns}Panel:not(.antd-Table-searchableForm):not(.antd-Crud-filter) {
      > .#{$ns}Panel-heading {
        padding: 0;
        border: none;
      }

      > .#{$ns}Panel-body {
        padding: 0;
      }

      // 白底背景的情况下，如果有标题区域，需要给内容区域加上边距，如果没有标题，不需要加的原因是分割线包含有边距
      &:has(.antd-Panel-heading) {
        > .#{$ns}Panel-body {
          padding-top: 16px;
        }
      }

      &:last-of-type {
        margin-bottom: 0;
      }
    }

    // 分组容器下快速编辑弹框、curd列搜索弹框，需要有内边距
    & .#{$ns}QuickEdit-popover,
    .#{$ns}TableCell-searchPopOver {
      .#{$ns}Panel-body {
        padding: var(--Panel-bodyPadding) !important;
      }
    }

    &-Divider {
      display: block;
    }
  }

  /**
   * 此处改动需验证分组容器-版本对比前后值同时分模块展示的容器场景，检测下边距是否正常
   * 本身有内边距的容器内使用分组容器的无折叠分组时，需要去除panel的底部外边距，但需要排除掉crud的查询区域
   */
  .antd-Panel:not(.antd-Crud-filter):not(.antd-Panel--form):not(
      .antd-Table-searchableForm
    ) {
    // 只对最后一项panel设置该样式，不影响上述分组里的panel场景
    &:last-of-type {
      margin-bottom: 0;
    }
  }
}
