// 当在左右布局左侧容器使用input-tree时，需要去除input-tree的边框及内边距
// 因为左右布局左侧容器默认有16px边距，且会在父级容器白底的情况下添加边框
.standard-LeftRightContainer-left .standard-InputTree {
  height: 100%;

  .standard-InputTree-treeContainer {
    padding: 0;
    // issue#1116 这个样式会影响到inputTree的高度不正常，注释掉
    // height: 100%;
    border: none;
  }
}

// inputTree配置extraActions属性时，icon大小和高度与默认的icon保持统一
.standard-InputTree {
  .standard-InputTree-treeContainer {
    .antd-Button.antd-Button--link {
      display: inline-block;
      height: var(--Tree-itemHeight);
      line-height: var(--Tree-itemHeight);
      margin-left: 8px;

      > svg {
        width: 0.75rem;
        height: 0.75rem;
      }
    }
  }
}
