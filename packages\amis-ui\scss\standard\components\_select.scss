// 为什么没有放到 standard-Select 下
// 原因：如果设置 popOverContainerSelector 后，下拉菜单是没有渲染到选择框根元素下面
// standard-Select-Option 为专门设置给option的类目，不受 popOverContainerSelector 影响
.standard-Select-Option {

  // 以下样式是为解决多行选项的样式
  .standard-Flex {
    &>.antd-TplField,.standard-Each>.antd-TplField>span {
      display: block;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
    }

    &>.antd-TplField:not(:first-of-type),.standard-Each>.antd-TplField  {
      color: #6c757d;
      line-height: 1rem;
      font-size: var(--fontSizeXs);
    }
    .standard-Each>.antd-TplField {
      line-height: 1.5rem;
    }
  }

  // issue980 弹窗闪一次的问题
  &:not(.standard-Select-Multiple-Option) {
    .standard-Flex {
      &>*:last-child {
        margin-bottom: 0.5rem;
      }
    }
  }
}

// 以下样式是为了处理多选情况下，点选项的任意位置都可以选中该选项 （参考之前的辅助函数）
.antd-Select-menu {

  // 由于无法给整个下拉菜单设置类名，只能给单个设置，这里通过单个option去判断当前元素是否为需要设置样式的元素
  label:has(.standard-Select-Multiple-Option) {
    width: 100%;

    &>i+span {
      width: calc(100% - 8px);
    }
  }
}
// 以下样式是为了解决 tooltip 提示框里面多行的场景 行之间的间距和字体样式
.standard-Select-option-tooltip {
  .standard-Flex {
    &>.antd-TplField:not(:first-of-type),.standard-Each>.antd-TplField {
      color: #6c757d;
      font-size: 12px;
      margin-top: 4px;
    }
  }
}
