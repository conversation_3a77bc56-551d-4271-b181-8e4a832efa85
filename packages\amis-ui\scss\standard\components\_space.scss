.standard-Space {
  // 横向使用flex 8px间隔，且自动水平居中
  &--horizontal {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    gap: 8px;
    // 清空 items 原有margin, 统一使用 gap 控制间隙
    & > * {
      // 每一个项目，使用自身的宽高占位，不进行伸缩
      flex: 0 0 auto;
      margin-left: 0;
      margin-right: 0;
    }
    // 内部去掉formItem多余的marginBottom
    & > .antd-Form-item {
      margin-bottom: 0;
    }
  }

  // 纵向 16px
  &--vertical {
    display: flex;
    flex-wrap: nowrap;
    align-items: stretch;
    justify-content: flex-start;
    flex-direction: column;
    gap: 16px;
    & > .standard-Space-item {
      // 去掉纵向容器本身的上下margin
      & > * {
        margin-top: 0;
        margin-bottom: 0;
      }
    }
  }
}
