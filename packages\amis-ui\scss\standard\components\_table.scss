// 被 Tabs 嵌套时
.standard-Table {
  .antd-Table-headerContainer {
    // 当title是第一个元素的时候，padding-top是16px
    > .antd-Table-heading {
      padding: 16px 8px;
    }
    // 既有headtoolbar 又有title的场景时，padding-top是0，用antd-Table-headToolbar的padding-bottom就行
    .antd-Table-headToolbar ~ .antd-Table-heading {
      padding: 0 8px 16px 8px !important;
    }
  }

  // 表格配置copyable时，间距改为8
  .antd-Table-table tbody .antd-Field-copyBtn {
    margin-left: var(--gap-sm);
  }
}
