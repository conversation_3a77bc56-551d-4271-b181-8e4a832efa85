.standard-Tabs {
  // line模式 默认增加 padding
  &.antd-Tabs--line {
    padding: 16px;

    // 去掉line模式，tabsPane 的底部padding(使用 tabs 容器的 padding)
    & > .antd-Tabs-content > .antd-Tabs-pane {
      padding-bottom: 0;
    }

    // 多层 line模式嵌套（2层及其下层）, 去掉子级 tab 外层 padding
    .antd-Tabs-pane > .antd-Tabs--line {
      padding: 0;
    }

    // 多级 tab-line模式，第二级开始后边的 tabsPane paddingBottom 设置为0
    .antd-Tabs-pane {
      .antd-Tabs--line .antd-Tabs-pane {
        padding-bottom: 0;
      }
    }
  }

  &.antd-Tabs--strong .antd-Tabs-pane {
    padding: 16px;
  }
}


// 有内边距的容器内嵌套tabs时，去除tabs内边距和底部边距
.antd-Form,
.antd-Tooltip,
.antd-Wizard,
.antd-Modal,
.antd-Drawer,
.antd-Wrapper--md,
.standard-LeftRightContainer-left,
.standard-GroupContainer {
  .antd-Tabs.standard-Tabs:not(.antd-Tabs--strong) {
    padding: 0;
  }
}

// tabs内嵌套crud的场景下，需要去除左右边距，此处需要注意tabs设置二级tab和三级tab的场景自测
.standard-Tabs:not(.antd-Tabs--strong) {
  // 此处需要注意tabs设置二级tab和三级tab的场景自测
  > .antd-Tabs-content {
    // 1. tabs下嵌套 分组 > crud，二三级tabs嵌套 分组 > crud
    // 2. 左侧tabs+tree，右侧tabs+crud带全局操作按钮  tab.pane与form场景存在时，去掉左右的padding
    // 3. tabs内直接嵌套分组容器时，去除左、右边距
    // 4. Tabs > GroupContainer > Tabs 的场景，需要排除掉 Tabs >  Tabs > GroupContainer > Tabs
    // :not(:has(>.antd-Tabs)) 是为了处理在二级tabs下嵌套这几种容器时，第一层tabs仍然保留内边距
    > .antd-Tabs-pane:has(.antd-Crud):not(:has(.antd-Tabs)),
    > .antd-Tabs-pane:has(.standard-Form):not(:has(.antd-Tabs)),
    > .antd-Tabs-pane:has(.standard-GroupContainer):not(:has(.antd-Tabs)),
    > .antd-Tabs-pane:has(.standard-GroupContainer .antd-Tabs):not(:has( .antd-Tabs .standard-GroupContainer .antd-Tabs)) {
      padding: 1rem 0 0;
    }
  }
}
