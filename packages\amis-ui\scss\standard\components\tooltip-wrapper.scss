.standard-TooltipWrapper {
  
}

// tooltip模拟popOver功能
.standard-TooltipWrapper-popover:has(.standard-GroupContainer) {
  &.pm-tooltip-popover-sm {
    width: var(--Modal-widthSm);
    max-width: var(--Modal-widthSm);
  }
  &.pm-tooltip-popover-xs {
    width: var(--Modal-widthBase);
    max-width: var(--Modal-widthBase);
  }
  &.pm-tooltip-popover-md {
    max-width: var(--Modal-widthMd);
    width: var(--Modal-widthMd);
  }
  &.pm-tooltip-popover-lg {
    max-width: var(--Modal-widthLg);
    width: var(--Modal-widthLg);
  }

  .antd-Tooltip-title {
    font-size: var(--Modal-title-fontSize);
    color: var(--Modal-title-color);
    font-weight: var(--Modal-title-fontWeight);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, .06);
  }
  .antd-Tooltip-body {
    padding: 0;
  }
  .antd-Tooltip-body-content {
    height: auto;
    overflow-y: auto;
    padding: 1.5rem;
  }

  .antd-Tooltip-body-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: var(--Modal-footer-padding) 1.5rem;
    border-top: 1px solid rgba(0, 0, 0, .06);
    .antd-Button {
      display: inline-block;
      font-size: var(--button-size-md-fontSize);
      font-weight: var(--button-size-md-fontWeight);
      line-height: var(--button-size-md-lineHeight);
      border-radius: var(--button-size-md-top-left-border-radius) var(--button-size-md-top-right-border-radius) var(--button-size-md-bottom-right-border-radius) var(--button-size-md-bottom-left-border-radius);
      height: var(--button-size-md-height);
      padding: var(--button-size-md-paddingTop) var(--button-size-md-paddingRight) var(--button-size-md-paddingBottom) var(--button-size-md-paddingLeft);
      margin: var(--button-size-md-marginTop) var(--button-size-md-marginRight) var(--button-size-md-marginBottom) var(--button-size-md-marginLeft);
      margin-left: var(--gap-sm);
    }
  }
}