$ns: 'antd-';

:root {
  --colors-neutral-fill-none: translate;
  // --colors-error-main: #ff4d4f;
  // --colors-error-1: #660a18;
  // --colors-error-2: #8c1523;
  // --colors-error-3: #b32430;
  --colors-error-4: #d9363e;
  --colors-error-5: #ff4d4f;
  --colors-error-6: #ff7070;
  // --colors-error-7: #ff9694;
  // --colors-error-8: #ffbab8;
  // --colors-error-9: #ffdddb;
  --colors-error-10: #fff2f0;
  --colors-error-11: #ffccc7;
  // --colors-warning-main: #faad14;
  // --colors-warning-1: #613400;
  // --colors-warning-2: #874d00;
  // --colors-warning-3: #ad6800;
  --colors-warning-4: #d48806;
  --colors-warning-5: #faad14;
  --colors-warning-6: #ffc443;
  // --colors-warning-7: #ffd572;
  // --colors-warning-8: #ffe4a1;
  // --colors-warning-9: #fff2d0;
  --colors-warning-10: #fff9e6;
  --colors-warning-11: #ffe58f;
  // --colors-success-main: #389e0d;
  // --colors-success-1: #010500;
  // --colors-success-2: #092b00;
  // --colors-success-3: #135200;
  --colors-success-4: #237804;
  --colors-success-5: #52c41a;
  --colors-success-6: #55ab2d;
  // --colors-success-7: #74b852;
  // --colors-success-8: #95c47c;
  // --colors-success-9: #b9d1ab;
  --colors-success-10: #f6ffed;
  --colors-success-11: #b7eb8f;
  // --colors-link-1: #002766;
  // --colors-link-2: #003a8c;
  // --colors-link-3: #0050b3;
  --colors-link-4: #096dd9;
  --colors-link-5: #1677ff;
  --colors-link-6: #4096ff;
  // --colors-link-7: #74c0ff;
  // --colors-link-8: #a2d7ff;
  // --colors-link-9: #d1ecff;
  // --colors-link-10: #e6f5ff;
  // --colors-info-1: #002766;
  // --colors-info-2: #003a8c;
  // --colors-info-3: #0050b3;
  --colors-info-4: #096dd9;
  --colors-info-5: #1677ff;
  --colors-info-6: #4096ff;
  // --colors-info-7: #74c0ff;
  // --colors-info-8: #a2d7ff;
  // --colors-info-9: #d1ecff;
  --colors-info-10: #e6f4ff;
  --colors-info-11: #91caff;
  // --colors-other-main: #2468f2;
  // --colors-other-1: #001259;
  // --colors-other-2: #001e80;
  // --colors-other-3: #0832a6;
  // --colors-other-4: #144bcc;
  // --colors-other-5: #2468f2;
  // --colors-other-6: #528eff;
  // --colors-other-7: #7dadff;
  // --colors-other-8: #a8caff;
  // --colors-other-9: #d4e5ff;
  // --colors-other-10: #e6f0ff;
  --colors-brand-1: #000000;
  --colors-brand-2: #fafafa;
  --colors-brand-3: #4096ff;
  --colors-brand-4: #096dd9;
  --colors-brand-5: #1677ff;
  --colors-brand-6: #4096ff;
  --colors-brand-7: #74c0ff;
  --colors-brand-8: #a2d7ff;
  --colors-brand-9: rgba(22, 119, 255, 0.65);
  --colors-brand-10: #ffffff;
  --colors-brand-11: #e5e6e9;
  --colors-brand-12: #f0f1f2;
  --colors-brand-13: #e6f4ff;
  --colors-neutral-text-1: rgba(0, 0, 0, 0.88);
  --colors-neutral-text-2: #151b26;
  --colors-neutral-text-3: #d4d6d9;
  --colors-neutral-text-4: rgba(0, 0, 0, 0.45);
  --colors-neutral-text-5: rgba(0, 0, 0, 0.88);
  --colors-neutral-text-6: rgba(0, 0, 0, 0.25);
  --colors-neutral-text-7: rgba(0, 0, 0, 0.25);
  --colors-neutral-text-8: rgba(0, 0, 0, 0.85);
  --colors-neutral-text-9: #f5f5f5;
  --colors-neutral-text-10: rgba(0, 0, 0, 0.04);
  --colors-neutral-text-11: #ffffff;
  --colors-neutral-text-12: rgba(0, 0, 0, 0.02);
  --colors-neutral-text-13: rgba(0, 0, 0, 0.1);
  --colors-neutral-text-14: rgba(0, 0, 0, 0.55);
  --colors-neutral-text-15: rgb(166, 166, 166);
  --colors-neutral-text-16: #333;
  --colors-neutral-text-17: #84868c;
  // --colors-neutral-fill-none: transparent;
  --colors-neutral-fill-1: rgba(0, 0, 0, 0.88);
  --colors-neutral-fill-2: #001529;
  --colors-neutral-fill-3: rgba(0, 0, 0, 0.85); // #000000d9
  --colors-neutral-fill-4: #5c6166;
  --colors-neutral-fill-5: rgba(0, 0, 0, 0.88);
  --colors-neutral-fill-6: rgba(0, 0, 0, 0.25);
  --colors-neutral-fill-7: rgba(0, 0, 0, 0.25); // #00000040
  --colors-neutral-fill-8: rgba(0, 0, 0, 0.04);
  --colors-neutral-fill-9: #f5f5f5;
  --colors-neutral-fill-10: rgba(0, 0, 0, 0.04);
  --colors-neutral-fill-11: #ffffff;
  --colors-neutral-fill-12: #3388ff;
  --colors-neutral-fill-13: #e8e9eb;
  --colors-neutral-fill-14: rgba(0, 0, 0, 0.03);
  --colors-neutral-fill-15: #f7f7f9;
  --colors-neutral-line-1: rgba(0, 0, 0, 0.88);
  // --colors-neutral-line-2: #151e26;
  --colors-neutral-line-3: rgba(0, 0, 0, 0.85); // #000000d9
  --colors-neutral-line-4: #5c6166;
  --colors-neutral-line-5: rgba(0, 0, 0, 0.88);
  --colors-neutral-line-6: rgba(0, 0, 0, 0.25);
  --colors-neutral-line-7: rgba(0, 0, 0, 0.25); // #00000040
  --colors-neutral-line-8: rgb(217, 217, 217);
  --colors-neutral-line-9: #f5f5f5;
  --colors-neutral-line-10: rgba(0, 0, 0, 0.04);
  --colors-neutral-line-11: #ffffff;
  --colors-neutral-line-13: rgba(5, 5, 5, 0.06);
  --colors-neutral-line-14: #e8e9eb;
  --colors-neutral-line-15: #e5e5e5;
  --colors-neutral-line-16: #e8ebee;
  --colors-neutral-line-17: rgba(0, 0, 0, 0.16);
  --colors-neutral-line-18: #f0f0f0;
  --fonts-base-family: -apple-system, BlinkMacSystemFont, SF Pro SC, SF Pro Text,
    Helvetica Neue, Helvetica, PingFang SC, Segoe UI, Roboto, Hiragino Sans GB,
    Arial, microsoft yahei ui, Microsoft YaHei, SimSun, sans-serif;
  --fonts-size-1: 48px;
  --fonts-size-2: 40px;
  --fonts-size-3: 32px;
  --fonts-size-4: 24px;
  --fonts-size-5: 18px;
  --fonts-size-6: 16px;
  --fonts-size-7: 14px;
  --fonts-size-8: 12px;
  --fonts-size-9: 12px;
  --fonts-weight-1: 900;
  --fonts-weight-2: 800;
  --fonts-weight-3: 700;
  --fonts-weight-4: 600;
  --fonts-weight-5: 500;
  --fonts-weight-6: 400;
  --fonts-weight-7: 300;
  --fonts-weight-8: 200;
  --fonts-weight-9: 100;
  --fonts-lineHeight-1: 1.3;
  --fonts-lineHeight-2: 1.5;
  --fonts-lineHeight-3: 1.7;
  --borders-style-1: none;
  --borders-style-2: solid;
  --borders-style-3: dashed;
  --borders-style-4: dotted;
  --borders-width-1: 0px;
  --borders-width-2: 1px;
  --borders-width-3: 2px;
  --borders-width-4: 4px;
  --borders-radius-1: 0px;
  --borders-radius-2: 1px;
  --borders-radius-3: 6px;
  --borders-radius-4: 6px;
  --borders-radius-5: 8px;
  --borders-radius-6: 10px;
  --borders-radius-7: 50%;
  --sizes-size-0: 0rem;
  --sizes-size-1: auto;
  --sizes-size-2: 0.125rem;
  --sizes-size-3: 4px;
  --sizes-size-4: 0.375rem;
  --sizes-size-5: 0.5rem;
  --sizes-size-6: 0.625rem;
  --sizes-size-7: 15px;
  --sizes-size-8: 0.875rem;
  --sizes-size-9: 1rem;
  --sizes-base-1: 0.125rem;
  --sizes-base-2: 0.25rem;
  --sizes-base-3: 0.375rem;
  --sizes-base-4: 0.5rem;
  --sizes-base-5: 0.625rem;
  --sizes-base-6: 0.75rem;
  --sizes-base-7: 0.875rem;
  --sizes-base-8: 1rem;
  --sizes-base-9: 1.125rem;
  --sizes-base-10: 1.25rem;
  --sizes-base-11: 1.375rem;
  --sizes-base-12: 1.5rem;
  --sizes-base-13: 1.625rem;
  --sizes-base-14: 1.75rem;
  --sizes-base-15: 1.875rem;
  --sizes-base-16: 2rem;
  --sizes-base-17: 2.125rem;
  --sizes-base-18: 2.25rem;
  --sizes-base-19: 2.375rem;
  --sizes-base-20: 2.5rem;
  --sizes-base-21: 2.625rem;
  --sizes-base-22: 2.75rem;
  --sizes-base-23: 2.875rem;
  --sizes-base-24: 3rem;
  --sizes-base-25: 3.125rem;
  --sizes-base-26: 3.25rem;
  --sizes-base-27: 3.375rem;
  --sizes-base-28: 3.5rem;
  --sizes-base-29: 3.625rem;
  --sizes-base-30: 3.75rem;
  --sizes-base-31: 3.875rem;
  --sizes-base-32: 4rem;
  --sizes-base-33: 4.125rem;
  --sizes-base-34: 4.25rem;
  --sizes-base-35: 4.375rem;
  --sizes-base-36: 4.5rem;
  --sizes-base-37: 4.625rem;
  --sizes-base-38: 4.75rem;
  --sizes-base-39: 4.875rem;
  --sizes-base-40: 5rem;
  --sizes-base-41: 5.125rem;
  --sizes-base-42: 5.25rem;
  --sizes-base-43: 5.375rem;
  --sizes-base-44: 5.5rem;
  --sizes-base-45: 5.625rem;
  --sizes-base-46: 5.75rem;
  --sizes-base-47: 5.875rem;
  --sizes-base-48: 6rem;
  --sizes-base-49: 6.125rem;
  --sizes-base-50: 6.25rem;
  --shadows-shadow-none: 0px 0px 0px 0px transparent;
  --shadows-shadow-sm: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
  --shadows-shadow-normal: 0px 1px 3px 0px rgba(0, 0, 0, 0.1),
    0px 1px 2px 0px rgba(0, 0, 0, 0.06);
  --shadows-shadow-md: 0px 4px -1px 0px rgba(0, 0, 0, 0.1),
    0px 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadows-shadow-lg: 0px 10px 15px -3px rgba(0, 0, 0, 0.1),
    0px 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadows-shadow-xl: 0px 20px 25px -5px rgba(0, 0, 0, 0.1),
    0px 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadows-shadow-2xl: 0px 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadows-shadow-1: 0px 1px 8px 0px rgba(0, 0, 0, 0.06);

  // input 激活时的 shadow
  --input-default-active-shadow: 0 0 0 2px rgb(5 145 255 / 10%);

  // 左侧导航样式，基于暗色模式
  --Layout-aside-onAcitve-bg: rgb(24, 144, 255);
  --Layout-asideLink-onActive-color: #fff;
  --Layout-asideLink-color: hsla(0, 0%, 100%, 0.75);
  --Layout-aside-subList-bg: #000c17;

  // 表格
  --Table-thead-bg: #f8f9fb;
  --Table-onChecked-bg: rgb(220, 244, 255);
  --Table-onChecked-color: rgba(0, 0, 0, 0.85);
}
