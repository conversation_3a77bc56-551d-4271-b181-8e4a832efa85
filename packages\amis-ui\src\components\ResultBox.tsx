import {ThemeProps, themeable} from 'amis-core';
import React from 'react';
import omit from 'lodash/omit';
import isInteger from 'lodash/isInteger';
import {InputBoxProps} from './InputBox';
import {uncontrollable} from 'amis-core';
import {Icon} from './icons';
import Input from './Input';
import {autobind, isMobile, ucFirst} from 'amis-core';
import {LocaleProps, localeable} from 'amis-core';
import Tag from './Tag';
import isPlainObject from 'lodash/isPlainObject';
import TooltipWrapper, {TooltipObject} from './TooltipWrapper';

export const CLEAR_TYPE = {
  all: 'all', // input的清除按钮
  item: 'item', // 删除某一个item
  keyDown: 'keyDown', // input删除键操作
  other: 'other' // 其它
}

export interface ResultBoxProps
  extends ThemeProps,
    LocaleProps,
    Omit<InputBoxProps, 'result' | 'prefix' | 'onChange' | 'translate'> {
  onChange?: (value: string) => void;
  onResultClick?: (e: React.MouseEvent<HTMLElement>) => void;
  result?: Array<any> | any;
  itemRender: (value: any) => JSX.Element | string;
  onResultChange?: (value: Array<any>, type?: string, item?: object) => void;
  onClear?: (e: React.MouseEvent<HTMLElement>) => void;
  allowInput?: boolean;
  inputPlaceholder: string;
  useMobileUI?: boolean;
  hasDropDownArrow?: boolean;
  maxTagCount?: number;
  overflowTagPopover?: TooltipObject;
  actions?: JSX.Element | JSX.Element[];
  showInvalidMatch?: boolean;
  citer?: 'treeselect' | undefined; // 标识组合当前组件的组件，目前只有treeselect和undefined
}

export class ResultBox extends React.Component<ResultBoxProps> {
  static defaultProps: Pick<
    ResultBoxProps,
    'clearable' | 'placeholder' | 'itemRender' | 'inputPlaceholder'
  > = {
    clearable: false,
    placeholder: 'placeholder.noData',
    inputPlaceholder: 'placeholder.enter',
    itemRender: (option: any) => (
      <span>{`${option.scopeLabel || ''}${option.label}`}</span>
    )
  };

  state = {
    isFocused: false
  };

  inputRef: React.RefObject<any> = React.createRef();

  focus() {
    this.inputRef.current?.focus();
  }

  blur() {
    this.inputRef.current?.blur();
  }

  @autobind
  clearValue(e: React.MouseEvent<any>) {
    e.preventDefault();
    e.stopPropagation();
    this.props.onClear && this.props.onClear(e);
    this.props.onResultChange && this.props.onResultChange([], CLEAR_TYPE.all);
  }

  @autobind
  handleFocus(e: any) {
    const onFocus = this.props.onFocus;
    onFocus && onFocus(e);
    this.setState({
      isFocused: true
    });
  }

  @autobind
  handleBlur(e: any) {
    const onBlur = this.props.onBlur;
    onBlur && onBlur(e);
    this.setState({
      isFocused: false
    });
  }

  @autobind
  removeItem(e: React.MouseEvent<HTMLElement>, idx?: number) {
    e.stopPropagation();
    e.preventDefault();

    const {result, onResultChange} = this.props;
    const index = idx !== undefined ? idx : parseInt(e.currentTarget.getAttribute('data-index')!, 10);
    const newResult = Array.isArray(result) ? result.concat() : [];
    // 删除的元素，触发删除监听的时候需要把删除的元素当成参数进行传递
    const delItem =  newResult.splice(index, 1);
    onResultChange && onResultChange(newResult, CLEAR_TYPE.item, delItem[0]);
  }

  @autobind
  handleChange(e: React.ChangeEvent<HTMLInputElement>) {
    const {onChange} = this.props;

    onChange?.(e.currentTarget.value);
  }

  renderMultipeTags(tags: any[]) {
    const {
      maxTagCount,
      overflowTagPopover,
      itemRender,
      classnames: cx,
      showInvalidMatch
    } = this.props;

    if (
      maxTagCount != null &&
      isInteger(Math.floor(maxTagCount)) &&
      Math.floor(maxTagCount) >= 0 &&
      Math.floor(maxTagCount) < tags.length
    ) {
      const maxVisibleCount = Math.floor(maxTagCount);
      const tooltipProps: TooltipObject = {
        placement: 'top',
        trigger: 'hover',
        showArrow: false,
        offset: [0, -10],
        tooltipClassName: cx(
          'ResultBox-overflow',
          overflowTagPopover?.tooltipClassName
        ),
        ...omit(overflowTagPopover, ['children', 'content', 'tooltipClassName'])
      };

      return [
        ...tags.slice(0, maxVisibleCount),
        {label: `+ ${tags.length - maxVisibleCount} ...`}
      ].map((item, index) => {
        const isShowInvalid = showInvalidMatch && item?.__unmatched;
        return index === maxVisibleCount ? (
          <TooltipWrapper
            key={tags.length}
            tooltip={{
              ...tooltipProps,
              children: () => (
                <div className={cx('ResultBox-overflow-wrapper')}>
                  {tags
                    .slice(maxVisibleCount, tags.length)
                    .map((item, index) => {
                      const itemIndex = index + maxVisibleCount;
                      return this.renderCustomTag(item, itemIndex);
                    })}
                </div>
              )
            }}
          >
            <div
              className={cx('ResultBox-value', {
                'is-invalid': isShowInvalid
              })}
              key={index}
            >
              <span className={cx('ResultBox-valueLabel')}>{item.label}</span>
            </div>
          </TooltipWrapper>
        ) : this.renderCustomTag(item, index);
      });
    }

    return tags.map((item, index) => this.renderCustomTag(item, index));
  }

  // feat: 基于Tag组件达到渲染多样式tag的效果, 默认使用原来样式
  renderCustomTag(item: any, idx: number, options?: any) {
    const {
      classnames: cx,
      showInvalidMatch,
      itemRender,
      citer,
      disabled
    } = this.props;

    const disabledType = citer === 'treeselect';

    const {customTag = {}, __unmatched = false} = item || {};
    const handleRemoveItem = (e: React.MouseEvent<HTMLElement>) => disabledType && item?.disabled ? null : this.removeItem(e, idx);
    const isShowInvalid = showInvalidMatch && __unmatched;

    if (item?.customTag) {
      // issue: 自定义tag 目前针对未匹配的选项不支持label标红
      return (
        <div
          className={cx('ResultBox-value')}
          style={{padding: 0, background: 'none'}}
          key={idx}
        >
          <Tag
            className={customTag.className}
            displayMode={customTag.displayMode}
            color={customTag.color}
            icon={customTag.icon}
            style={customTag.style || {}}
            closable={disabledType ? !item.disabled : !disabled}
            closeIcon={customTag.closeIcon}
            onClose={handleRemoveItem}
          >
            {itemRender(item)}
          </Tag>
        </div>
      );
    }

    return (
      <div
        className={cx('ResultBox-value', {
          'is-invalid': isShowInvalid
        })}
        key={idx}
      >
        <span className={cx('ResultBox-valueLabel')}>{itemRender(item)}</span>
        {
          // FIX： issue819 禁用状态下不展示 x
          (disabledType && item?.disabled) || disabled ? null : <a data-index={idx} onClick={handleRemoveItem}>
            <Icon icon="close" className="icon" />
          </a>
        }
      </div>
    );
  }

  render() {
    /** 不需要透传给Input的属性要解构出来 */
    const {
      className,
      classnames: cx,
      classPrefix,
      clearable,
      disabled,
      hasError,
      result,
      value,
      placeholder,
      children,
      itemRender,
      allowInput,
      inputPlaceholder,
      onResultChange,
      onChange,
      onResultClick,
      translate: __,
      locale,
      onKeyPress,
      onFocus,
      onBlur,
      borderMode,
      useMobileUI,
      hasDropDownArrow,
      actions,
      onClear,
      maxTagCount,
      overflowTagPopover,
      ...rest
    } = this.props;
    const isFocused = this.state.isFocused;
    const mobileUI = useMobileUI && isMobile();

    return (
      <div
        className={cx('ResultBox', className, {
          'is-focused': isFocused,
          'is-disabled': disabled,
          'is-error': hasError,
          'is-clickable': onResultClick,
          'is-clearable': clearable,
          'is-mobile': mobileUI,
          'is-group': Array.isArray(result),
          [`ResultBox--border${ucFirst(borderMode)}`]: borderMode
        })}
        onClick={disabled ? undefined : onResultClick}
        tabIndex={!allowInput && !disabled && onFocus ? 0 : -1}
        onKeyPress={allowInput ? undefined : onKeyPress}
        onFocus={allowInput ? undefined : onFocus}
        onBlur={allowInput ? undefined : onBlur}
      >
        <div className={cx('ResultBox-value-wrap')}>
          {Array.isArray(result) && result.length ? (
            this.renderMultipeTags(result)
          ) : result && !Array.isArray(result) ? (
            <span className={cx('ResultBox-singleValue', { 'is-focused': isFocused })}>
              {isPlainObject(result) ? itemRender(result) : result}
            </span>
          ) : allowInput && !disabled ? null : (
            <span className={cx('ResultBox-placeholder')}>
              {__(placeholder || 'placeholder.noData')}
            </span>
          )}

          {allowInput && !disabled ? (
            <Input
              {...rest}
              className={cx('ResultBox-value-input')}
              onKeyPress={onKeyPress}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault(); // #1293 阻止表单提交
                }
              }}
              ref={this.inputRef}
              value={value || ''}
              onChange={this.handleChange}
              placeholder={__(
                /** 数组模式下输入内容后将不再展示placeholder */
                Array.isArray(result)
                  ? result.length > 0
                    ? inputPlaceholder
                    : placeholder
                  : result
                  ? ''
                  : placeholder
              )}
              onFocus={this.handleFocus}
              onBlur={this.handleBlur}
            />
          ) : null}

          {children}
        </div>

        <div className={cx('ResultBox-actions')}>
          {clearable &&
          !disabled &&
          (Array.isArray(result) ? result.length : result) ? (
            <a
              onClick={this.clearValue}
              className={cx('ResultBox-clear', {
                'ResultBox-clear-with-arrow': hasDropDownArrow
              })}
            >
              <div className={cx('ResultBox-clear-wrap')}>
                <Icon icon="input-clear" className="icon" />
              </div>
            </a>
          ) : null}

          {actions}

          {hasDropDownArrow && !mobileUI && (
            <span className={cx('ResultBox-pc-arrow')}>
              <Icon icon="right-arrow-bold" className="icon" />
            </span>
          )}
          {!allowInput && mobileUI ? (
            <span className={cx('ResultBox-arrow')}>
              <Icon icon="caret" className="icon" />
            </span>
          ) : null}
        </div>
      </div>
    );
  }
}

export default themeable(
  localeable(
    uncontrollable(ResultBox, {
      value: 'onChange',
      result: 'onResultChange'
    })
  )
);
