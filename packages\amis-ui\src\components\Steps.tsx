import React, {useCallback, useMemo} from 'react';
import {ClassName, themeable, ThemeProps} from 'amis-core';
import {Icon} from './icons';
import TooltipWrapper from './TooltipWrapper';
import {isMobile} from 'amis-core';

export enum StepStatus {
  wait = 'wait',
  process = 'process',
  finish = 'finish',
  error = 'error',
}

const FINISH_ICON = 'check';
const ERROR_ICON = 'close';

export type StepObject = {
  className?: ClassName;
  /**
   * 标题
   */
  title?: string | JSX.Element;

  /**
   * 子标题
   */
  subTitle?: string | JSX.Element;

  /**
   * 图标
   */
  icon?: string;

  value?: string | number;

  /**
   * 描述
   */
  description?: string | JSX.Element;

  status?: StepStatus;

  iconClassName?: string;

  /** 当前节点是否可点击 */
  clickable?: boolean;

  /** 是否显示tooltip */
  tooltipVisible?: boolean
};

export interface StepsObject {
  /**
   * 指定为 Steps 步骤条渲染器
   */
  type: 'steps';

  /**
   * 步骤
   */
  steps?: Array<StepObject>;

  /**
   * API 或 数据映射
   */
  source?: string;

  /**
   * 指定当前步骤
   */
  value?: number | string;

  /**
   * 变量映射
   */
  name?: string;

  status: StepStatus;

  /**
   * 展示模式
   */
  mode?: 'horizontal' | 'vertical';

  /**
   * 标签放置位置
   */
  labelPlacement?: 'horizontal' | 'vertical';
}

export type ClickStepEvent = {
  step: StepObject;
  /** 当前被点击步骤的序号 */
  index: number;
};

const Mode = {
  Vertical: 'vertical',
  Horizontal: 'horizontal',
} as const;

type ModeType = (typeof Mode)[keyof typeof Mode];

const LabelPlacement = {
  Vertical: 'vertical',
  Horizontal: 'horizontal',
} as const;

type LabelPlacementType = (typeof LabelPlacement)[keyof typeof LabelPlacement];

const Delimiter = {
  Line: 'line',
  Arrow: 'arrow',
} as const;

type DelimiterType = (typeof Delimiter)[keyof typeof Delimiter];

export interface StepsProps extends ThemeProps {
  steps: StepObject[];
  className: string;
  current: number;
  status?:
    | StepStatus
    | {
        [propName: string]: StepStatus;
      };
  mode?: ModeType;
  labelPlacement?: LabelPlacementType;
  progressDot?: boolean;
  useMobileUI?: boolean;
  onClick?: (event: ClickStepEvent) => void;
  /** 步骤间的连接器 */
  delimiter?: DelimiterType;
  tooltipVisible?: boolean
}

/** 生成 steps 的 className */
const useStepsClassName = ({
  classnames,
  mode,
  progressDot,
  labelPlacement,
  useMobileUI,
  className,
}: Pick<
  StepsProps,
  'classnames' | 'progressDot' | 'useMobileUI' | 'className'
> & {
  mode: ModeType;
  labelPlacement: LabelPlacementType;
}) => {
  return useMemo(
    () =>
      classnames(
        'Steps',
        `Steps--${mode}`,
        // 纵向步骤条暂时不支持labelPlacement属性
        progressDot ||
          (labelPlacement === LabelPlacement.Vertical && mode !== Mode.Vertical)
          ? 'Steps--Placement-vertical'
          : '',
        progressDot ? 'Steps--ProgressDot' : '',
        useMobileUI && isMobile() ? 'Steps-mobile' : '',
        className,
      ),
    [classnames, mode, progressDot, labelPlacement, useMobileUI, className],
  );
};

/** 生成步骤项 vDom 数组 */
const useStepItems = ({
  current,
  status,
  steps,
  onClick,
  progressDot,
  classnames,
  delimiter,
  mode,
  labelPlacement,
  tooltipVisible,
}: Pick<
  StepsProps,
  'current' | 'status' | 'steps' | 'onClick' | 'progressDot' | 'classnames' | 'tooltipVisible'
> & {
  delimiter: DelimiterType;
  mode: ModeType;
  labelPlacement: LabelPlacementType;
}) => {
  const getStepStatus = useCallback(
    (
      step: StepObject,
      i: number,
    ): {
      stepStatus: StepStatus;
      icon?: string;
    } => {
      let stepStatus: StepStatus = StepStatus.wait;
      let icon = step.icon;

      if (i < current) {
        stepStatus = StepStatus.finish;
        !icon && (icon = FINISH_ICON);
      } else if (i === current) {
        stepStatus = StepStatus.process;
      }

      if (typeof status === 'string') {
        if (i === current) {
          stepStatus = step.status || status || StepStatus.process;
          stepStatus === StepStatus.error && !icon && (icon = ERROR_ICON);
        }
      } else if (typeof status === 'object') {
        const key = step.value;
        (null !== key && void 0 !== key) && status[key] && (stepStatus = status[key]);
      }

      return {
        stepStatus,
        icon,
      };
    },
    [current, status],
  );

  return useMemo(
    () =>
      steps.map((step, i) => {
        const {stepStatus, icon} = getStepStatus(step, i);
        const clickHandler = onClick && (() => onClick({step, index: i}));

        const isTooltipVisible = typeof step.tooltipVisible === 'boolean' ?
          step.tooltipVisible : tooltipVisible

        return (
          <StepItem
            key={i}
            step={step}
            index={i}
            status={stepStatus}
            isSuccess={i < current}
            icon={icon}
            onClick={clickHandler}
            progressDot={progressDot}
            classnames={classnames}
            delimiter={delimiter}
            mode={mode}
            labelPlacement={labelPlacement}
            tooltipVisible={isTooltipVisible}
          />
        );
      }),
    [
      steps,
      getStepStatus,
      onClick,
      current,
      progressDot,
      classnames,
      delimiter,
      mode,
      labelPlacement,
      tooltipVisible,
    ],
  );
};

/** 解析步骤连接器 */
const useDelimiter = (delimiter: StepsProps['delimiter']) => {
  return useMemo(
    () =>
      delimiter && Object.values(Delimiter).includes(delimiter)
        ? delimiter
        : Delimiter.Line,
    [delimiter],
  );
};

const useMode = (mode: StepsProps['mode']) => {
  return useMemo(
    () => (mode && Object.values(Mode).includes(mode) ? mode : Mode.Horizontal),
    [mode],
  );
};

const useLabelPlacement = (labelPlacement: StepsProps['labelPlacement']) => {
  return useMemo(
    () =>
      labelPlacement && Object.values(LabelPlacement).includes(labelPlacement)
        ? labelPlacement
        : LabelPlacement.Horizontal,
    [labelPlacement],
  );
};

export function Steps(props: StepsProps) {
  const {
    steps,
    classnames,
    className,
    style,
    current,
    status,
    mode: modeProp,
    labelPlacement: labelPlacementProp,
    progressDot = false,
    useMobileUI,
    onClick,
    tooltipVisible = true,
    delimiter: delimiterProp,
  } = props;
  const mode = useMode(modeProp);
  const labelPlacement = useLabelPlacement(labelPlacementProp);
  const stepsClassName = useStepsClassName({
    classnames,
    mode,
    progressDot,
    labelPlacement,
    useMobileUI,
    className,
  });
  const delimiter = useDelimiter(delimiterProp);
  const stepItems = useStepItems({
    current,
    status,
    steps,
    onClick,
    progressDot,
    classnames,
    delimiter,
    mode,
    labelPlacement,
    tooltipVisible
  });

  return (
    <ul className={stepsClassName} style={style}>
      {stepItems}
    </ul>
  );
}

type StepItemProps = {
  step: StepObject;
  /** step 的索引 */
  index: number;
  status: StepStatus;
  /** 本 step 是否小于 steps 的当前 step */
  isSuccess: boolean;
  icon?: string;
  onClick?: () => void;
  delimiter: DelimiterType;
  mode: ModeType;
  labelPlacement: LabelPlacementType;
  tooltipVisible?: boolean
} & Pick<StepsProps, 'progressDot' | 'classnames'>;

const StepItem = ({
  step,
  index,
  status,
  isSuccess,
  icon,
  progressDot,
  onClick,
  classnames: cx,
  delimiter,
  mode,
  labelPlacement,
  tooltipVisible = true,
}: StepItemProps) => {
  const stepClassName = useMemo(
    () =>
      cx(
        'StepItem',
        progressDot ? 'StepItem-ProgressDot' : '',
        `is-${status}`,
        isSuccess ? 'is-success' : '',
        `delimiter-${delimiter}`,
        `mode-${mode}`,
        `labelPlacement-${labelPlacement}`,
        step.className,
      ),
    [cx, progressDot, status, onClick, step.className],
  );

  const clickable = onClick && step.clickable !== false;

  /*
  使用TooltipWrapper代替原声的提示
  */
  return (
    <li className={stepClassName}>
      <div
        className={cx('StepItem-container', clickable ? 'clickable' : '')}
        onClick={clickable ? onClick : undefined}
      >
        <div className={cx('StepItem-containerTail')}></div>

        {progressDot ? (
          <div className={cx('StepItem-containerProgressDot')}></div>
        ) : (
          <div className={cx('StepItem-containerIcon')}>
            <span className={cx('StepItem-icon', step.iconClassName)}>
              {icon ? <Icon icon={icon} className="icon" /> : index + 1}
            </span>
          </div>
        )}

        <div className={cx('StepItem-containerWrapper')}>
          <div className={cx('StepItem-body')}>
            <div
              className={cx(
                'StepItem-title',
                progressDot ? 'StepItem-vertical-ProgressDot' : '',
              )}
            >
              {tooltipVisible ? (
                <TooltipWrapper
                  tooltip={{
                    content: step.title as string,
                  }}
                  tooltipClassName={cx('Select-tooltip')}
                  trigger={'hover'}
                  tooltipTheme={'dark'}
                >
                  <span
                    className={cx('StepItem-ellText')}
                  >
                    {step.title}
                  </span>
                </TooltipWrapper>
              ) : (
                <span className={cx('StepItem-ellText')}>
                  {step.title}
                </span>
              )}
              {step.subTitle && (
                tooltipVisible ? (
                  <TooltipWrapper
                    tooltip={{
                      content: step.subTitle as string,
                    }}
                    tooltipClassName={cx('Select-tooltip')}
                    trigger={'hover'}
                    tooltipTheme={'dark'}
                  >
                    <span
                      className={cx('StepItem-subTitle', 'StepItem-ellText')}
                    >
                      {step.subTitle}
                    </span>
                  </TooltipWrapper>
                ) : (
                  <span className={cx('StepItem-subTitle', 'StepItem-ellText')}>
                    {step.subTitle}
                  </span>
                )
              )}
            </div>
            {tooltipVisible ? (
              <TooltipWrapper
                tooltip={{
                  content: step.description as string,
                }}
                tooltipClassName={cx('Select-tooltip')}
                trigger={'hover'}
                tooltipTheme={'dark'}
              >
                <div
                  className={cx('StepItem-description', 'StepItem-ellText')}
                >
                  <span>{step.description}</span>
                </div>
              </TooltipWrapper>
            ) : (
              <div className={cx('StepItem-description', 'StepItem-ellText')}>
                <span>{step.description}</span>
              </div>
            )}
          </div>
        </div>
      </div>
      {delimiter === Delimiter.Arrow && (
        <Icon icon="right-arrow" className="delimiter-arrow-icon" />
      )}
    </li>
  );
};

export default themeable(Steps);
