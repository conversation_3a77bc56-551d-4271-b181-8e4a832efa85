/**
 * @file Tooltip
 * @description
 * <AUTHOR>
 */

import React from 'react';
import cx from 'classnames';
import {ClassNamesFn, themeable, autobind} from 'amis-core';

interface TooltipProps extends React.HTMLProps<HTMLDivElement> {
  title?: string;
  classPrefix: string;
  classnames: ClassNamesFn;
  theme?: string;
  className?: string;
  style?: any;
  arrowProps?: any;
  placement?: string;
  showArrow?: boolean;
  tooltipTheme?: string;
  [propName: string]: any;
}

export class Tooltip extends React.Component<TooltipProps> {
  static defaultProps = {
    className: '',
    tooltipTheme: 'light',
    showArrow: true
  };

  // issue#1098 双击tooltip内的内容触发了crud行的双击事件
  @autobind
  handleDbClick(e: any) {
    e.stopPropagation();
  }

  render() {
    const {
      classPrefix: ns,
      className,
      tooltipTheme,
      title,
      children,
      arrowProps,
      style,
      placement,
      arrowOffsetLeft,
      arrowOffsetTop,
      positionLeft,
      positionTop,
      classnames: cx,
      activePlacement,
      showArrow,
      onMouseEnter,
      onMouseLeave,
      ...rest
    } = this.props;

    return (
      <div
        {...rest}
        className={cx(
          `Tooltip`,
          activePlacement ? `Tooltip--${activePlacement}` : '',
          className,
          `Tooltip--${tooltipTheme === 'dark' ? 'dark' : 'light'}`
        )}
        style={style}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        onDoubleClick={this.handleDbClick}
        role="tooltip"
      >
        {showArrow ? (
          <div className={cx(`Tooltip-arrow`)} {...arrowProps} />
        ) : null}
        {title ? <div className={cx('Tooltip-title')}>{title}</div> : null}
        <div className={cx('Tooltip-body')}>{children}</div>
      </div>
    );
  }
}

export default themeable(Tooltip);
