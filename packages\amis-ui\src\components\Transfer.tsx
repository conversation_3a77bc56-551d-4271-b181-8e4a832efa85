import React from 'react';
import {
  intersectionWith,
  differenceWith,
  includes,
  debounce,
  result,
  isEqual,
  unionWith
} from 'lodash';

import {ThemeProps, themeable, findTree} from 'amis-core';
import {BaseSelectionProps, BaseSelection, ItemRenderStates} from './Selection';
import {Options, Option} from './Select';
import {uncontrollable} from 'amis-core';
import ResultList from './ResultList';
import TableSelection from './TableSelection';
import {autobind, flattenTree} from 'amis-core';
import InputBox from './InputBox';
import Checkbox from './Checkbox';
import Tree from './Tree';
import {Icon} from './icons';
import AssociatedSelection from './AssociatedSelection';
import {LocaleProps, localeable} from 'amis-core';
import GroupedSelection from './GroupedSelection';
import ChainedSelection from './ChainedSelection';
import {ItemRenderStates as ResultItemRenderStates} from './ResultList';
import ResultTableList from './ResultTableList';
import ResultTreeList from './ResultTreeList';
import {SpinnerExtraProps} from './Spinner';
import Pagination from './Pagination';
import type { Payload } from 'amis-core';

export type SelectMode =
  | 'table'
  | 'group'
  | 'list'
  | 'tree'
  | 'chained'
  | 'associated';

export interface TransferProps
  extends ThemeProps,
    LocaleProps,
    SpinnerExtraProps,
    Omit<BaseSelectionProps, 'itemRender'> {
  inline?: boolean;
  statistics?: boolean;
  showArrow?: boolean;
  multiple?: boolean;

  selectTitle?: string;
  selectMode?: SelectMode;
  columns?: Array<{
    name: string;
    label: string;
    [propName: string]: any;
  }>;
  cellRender?: (
    column: {
      name: string;
      label: string;
      [propName: string]: any;
    },
    option: Option,
    colIndex: number,
    rowIndex: number
  ) => JSX.Element;
  leftOptions?: Array<Option>;
  leftMode?: 'tree' | 'list' | 'group';
  leftDefaultValue?: any;
  rightMode?: 'table' | 'list' | 'group' | 'tree' | 'chained';

  // search 相关
  searchResultMode?: 'table' | 'list' | 'group' | 'tree' | 'chained';
  searchResultColumns?: Array<{
    name: string;
    label: string;
    [propName: string]: any;
  }>;
  searchPlaceholder?: string;
  noResultsText?: string;
  onChange?: (value: Array<Option>, optionModified?: boolean) => void;
  onSearch?: (
    term: string,
    setCancel: (cancel: () => void) => void
  ) => Promise<Options | void>;

  // 自定义选择框相关
  selectRender?: (
    props: Omit<TransferProps, 'onSearch'> & {
      onSearch: (text: string) => void;
      onSearchCancel: () => void;
      searchResult: Options | null;
    }
  ) => JSX.Element;

  resultTitle?: string;
  // 结果提示语
  resultListModeFollowSelect?: boolean;
  resultSearchPlaceholder?: string;
  optionItemRender?: (option: Option, states: ItemRenderStates) => JSX.Element;
  resultItemRender?: (
    option: Option,
    states: ResultItemRenderStates
  ) => JSX.Element;
  resultSearchable?: boolean;
  onResultSearch?: (text: string, item: Option) => boolean;
  sortable?: boolean;
  onRef?: (ref: Transfer) => void;
  onSelectAll?: (options: Options) => void;
  itemHeight?: number; // 每个选项的高度，主要用于虚拟渲染
  virtualThreshold?: number; // 数据量多大的时候开启虚拟渲染`
  virtualListHeight?: number; // 虚拟渲染时，列表高度
  showInvalidMatch?: boolean;
  checkAll?: boolean;
  checkAllLabel?: string;
  /** 树形模式下，给 tree 的属性 */
  onlyChildren?: boolean;
  /** 分页模式下累积的选项值，用于右侧回显 */
  accumulatedOptions?: Option[];
  /** 分页配置 */
  pagination?: {
    /** 是否开启分页 */
    enable: boolean;
    /** 分页组件CSS类名 */
    className?: string;
    /**
     * 通过控制layout属性的顺序，调整分页结构 total,perPage,pager,go
     * @default 'pager'
     */
    layout?: string | Array<string>;

    /**
     * 指定每页可以显示多少条
     * @default [10, 20, 50, 100]
     */
    perPageAvailable?: Array<number>;

    /**
     * 最多显示多少个分页按钮。
     *
     * @default 5
     */
    maxButtons?: number;
    page?: number;
    perPage?: number;
    total?: number;
    popOverContainer?: any;
    popOverContainerSelector?: string;
  };
  /** 切换分页事件 */
  onPageChange?: (
    page: number,
    perPage?: number,
    direction?: 'forward' | 'backward'
  ) => void;
}

export interface TransferState {
  inputValue: string;
  searchResult: Options | null | {leftOptions: Options; options: Options};
  isTreeDeferLoad: boolean;
  resultSelectMode: 'list' | 'tree' | 'table';
}

export class Transfer<
  T extends TransferProps = TransferProps
> extends React.Component<T, TransferState> {
  static defaultProps: Pick<
    TransferProps,
    | 'multiple'
    | 'resultListModeFollowSelect'
    | 'selectMode'
    | 'statistics'
    | 'virtualThreshold'
    | 'checkAllLabel'
  > = {
    multiple: true,
    resultListModeFollowSelect: false,
    selectMode: 'list',
    statistics: true,
    virtualThreshold: 100,
    checkAllLabel: 'Select.checkAll'
  };

  state: TransferState = {
    inputValue: '',
    searchResult: null,
    isTreeDeferLoad: false,
    resultSelectMode: 'list'
  };

  valueArray: Options;
  availableOptions: Options;
  unmounted = false;
  cancelSearch?: () => void;
  treeRef: any;

  constructor(props) {
    super(props);
    this.handleDeferLoadForSearchCritia = this.handleDeferLoadForSearchCritia.bind(this);
  }


  componentDidMount() {
    this.props?.onRef?.(this);
  }

  static getDerivedStateFromProps(props: TransferProps) {
    // 计算是否是懒加载模式
    const isTreeDeferLoad =
      props.selectMode === 'tree' &&
      !!findTree(
        props.options,
        (option: Option) => option.deferApi || option.defer
      );

    // 计算结果的selectMode
    let resultSelectMode = 'list';
    if (
      props.selectMode === 'tree' &&
      props.resultListModeFollowSelect &&
      !isTreeDeferLoad
    ) {
      resultSelectMode = 'tree';
    }

    if (props.selectMode === 'table' && props.resultListModeFollowSelect) {
      resultSelectMode = 'table';
    }

    return {
      isTreeDeferLoad,
      resultSelectMode
    };
  }

  componentWillUnmount() {
    this.lazySearch.cancel();
    this.unmounted = true;
  }

  @autobind
  domRef(ref: any) {
    this.treeRef = ref;
  }

  @autobind
  toggleAll() {
    const {options, option2value, onChange, value, onSelectAll} = this.props;
    let valueArray = BaseSelection.value2array(value, options, option2value);
    const availableOptions = this.availableOptions;

    // availableOptions 中选项是否都被选中了
    const isAvailableOptionsAllSelected =
      intersectionWith(availableOptions, valueArray, isEqual).length ===
      availableOptions.length;
    // 全不选
    if (isAvailableOptionsAllSelected) {
      valueArray = differenceWith(valueArray, availableOptions, isEqual);
    }
    // 全选
    else {
      valueArray = unionWith(valueArray, availableOptions, isEqual);
    }

    let newValue: string | Options = option2value
      ? valueArray.map(item => option2value(item))
      : valueArray;

    isAvailableOptionsAllSelected || onSelectAll?.(newValue);

    onChange?.(newValue);
  }

  // 全选，给予动作全选使用
  selectAll() {
    const {options, option2value, onChange} = this.props;
    const availableOptions = flattenTree(options).filter(
      (option, index, list) =>
        !option.disabled &&
        option.value !== void 0 &&
        list.indexOf(option) === index
    );
    let newValue: string | Options = option2value
      ? availableOptions.map(item => option2value(item))
      : availableOptions;
    onChange?.(newValue);
  }

  @autobind
  clearAll() {
    const {onChange} = this.props;
    onChange && onChange([]);
  }

  @autobind
  handleSearchKeyDown(e: React.KeyboardEvent<any>) {
    if (e.key === 'Enter') {
      e.preventDefault();
    }
  }

  @autobind
  handleSearch(inputValue: string) {
    // text 有值的时候，走搜索否则直接走 handleSeachCancel ，等同于右侧的 clear 按钮
    this.setState({inputValue}, () => {
      if (inputValue) {
        // 如果有取消搜索，先取消掉。
        this.cancelSearch && this.cancelSearch();
        this.lazySearch();
      } else {
        this.handleSeachCancel();
      }
    });
  }

  @autobind
  handleSeachCancel() {
    this.setState({
      inputValue: '',
      searchResult: null
    });
  }

  lazySearch = debounce(
    async () => {
      const {inputValue} = this.state;
      if (!inputValue) {
        return;
      }
      const onSearch = this.props.onSearch!;
      let result = await onSearch(
        inputValue,
        (cancelExecutor: () => void) => (this.cancelSearch = cancelExecutor)
      );

      if (this.unmounted) {
        return;
      }

      // 判断返回结果的合法性，增加了对Associated模式数据格式的兼容判断
      if (Array.isArray(result) || result?.leftOptions) {
        this.setState({
          searchResult: result
        });
      } else {
        throw new Error('onSearch 需要返回数组');
      }
    },
    250,
    {trailing: true, leading: false}
  );

  getFlattenArr(options: Array<Option>) {
    return flattenTree(options).filter(
      (option, index, list) =>
        !option.disabled &&
        option.value !== void 0 &&
        list.indexOf(option) === index
    );
  }

  // 树搜索处理
  @autobind
  handleSearchTreeChange(values: Array<Option>, searchOptions: Array<Option>) {
    const {onChange, value, multiple} = this.props;
    const searchAvailableOptions = this.getFlattenArr(searchOptions);
    values = Array.isArray(values) ? values : values ? [values] : [];

    const useArr = intersectionWith(
      searchAvailableOptions,
      values,
      (a, b) => a.value === b.value
    );
    const unuseArr = differenceWith(
      searchAvailableOptions,
      values,
      (a, b) => a.value === b.value
    );

    const newArr: Array<Option> = [];
    if (multiple) {
      Array.isArray(value) &&
      value.forEach((item: Option) => {
        if (!unuseArr.find(v => v.value === item.value)) {
          newArr.push(item);
        }
      });
    }

    useArr.forEach(item => {
      if (!newArr.find(v => v.value === item.value)) {
        newArr.push(item);
      }
    });

    onChange && onChange(newArr);
  }

  renderSelect(
    props: TransferProps & {
      onToggleAll?: () => void;
    }
  ) {
    const {
      selectRender,
      selectMode,
      classnames: cx,
      selectTitle,
      onSearch,
      disabled,
      options,
      statistics,
      translate: __,
      searchPlaceholder = __('Transfer.searchKeyword')
    } = props;

    if (selectRender) {
      return selectRender({
        ...props,
        onSearch: this.handleSearch,
        onSearchCancel: this.handleSeachCancel,
        searchResult: this.state.searchResult,
        handleDeferLoadForSearchCritia: this.handleDeferLoadForSearchCritia
      });
    }

    let checkedPartial = false;
    let checkedAll = false;

    checkedAll = this.availableOptions.every(
      option => this.valueArray.indexOf(option) > -1
    );
    checkedPartial = this.availableOptions.some(
      option => this.valueArray.indexOf(option) > -1
    );

    // 不在当前 availableOptions 中的已选项 数量
    const selectedNotInAvailableOptions = differenceWith(
      this.valueArray,
      this.availableOptions,
      isEqual
    ).length;

    return (
      <>
        <div
          className={cx(
            'Transfer-title',
            selectMode === 'table' ? 'Transfer-title--light' : ''
          )}
        >
          <span>
            {includes(['list', 'tree'], selectMode) ? (
              <Checkbox
                checked={checkedPartial}
                partial={checkedPartial && !checkedAll}
                disabled={disabled}
                onChange={props.onToggleAll || this.toggleAll}
                size="sm"
              />
            ) : null}
            {__(selectTitle || 'Transfer.available')}
            {statistics !== false ? (
              <span>
                （
                {this.availableOptions.length -
                  this.valueArray.length +
                  selectedNotInAvailableOptions}
                /{this.availableOptions.length}）
              </span>
            ) : null}
          </span>
          {includes(['chained', 'associated'], selectMode) ? (
            <a
              onClick={props.onToggleAll || this.toggleAll}
              className={cx(
                'Transfer-checkAll',
                disabled || !options.length ? 'is-disabled' : ''
              )}
            >
              {__('Select.checkAll')}
            </a>
          ) : null}
        </div>

        {onSearch ? (
          <div className={cx('Transfer-search')}>
            <InputBox
              value={this.state.inputValue}
              onChange={this.handleSearch}
              clearable={false}
              onKeyDown={this.handleSearchKeyDown}
              placeholder={searchPlaceholder}
            >
              {this.state.searchResult !== null ? (
                <a onClick={this.handleSeachCancel}>
                  <Icon icon="close" className="icon" />
                </a>
              ) : (
                <Icon icon="search" className="icon" />
              )}
            </InputBox>
          </div>
        ) : null}

        {this.state.searchResult !== null
          ? this.renderSearchResult(props)
          : this.renderOptions(props)}

        {this.renderFooter()}
      </>
    );
  }

  renderFooter() {
    const {classnames: cx, pagination, onPageChange} = this.props;

    return pagination?.enable ? (
      <div className={cx('Transfer-footer')}>
        <Pagination
          className={cx('Transfer-footer-pagination', pagination.className)}
          activePage={pagination.page}
          perPage={pagination.perPage}
          total={pagination.total}
          layout={pagination.layout}
          maxButtons={pagination.maxButtons}
          perPageAvailable={pagination.perPageAvailable}
          popOverContainer={pagination.popOverContainer}
          popOverContainerSelector={pagination.popOverContainerSelector}
          onPageChange={onPageChange}
        />
      </div>
    ) : null;
  }

  // 处理查找场景下的延迟加载
  async handleDeferLoadForSearchCritia(node: Option): Promise<Payload | null> {
    if (this.props.onDeferLoad) {
      // 将节点改为loading状态，并强制刷新。
      // 虽然onDeferLoad回调函数里面也会更改porps.options里节点的状态，但是不会更改state.searchResult，所以需要手动刷新。
      node.loading = true;
      this.forceUpdate();
      const json = await this.props.onDeferLoad(node);

      //根据加载结果更新节点。
      if (json) {
        node.children = json.data?.options || json.data.items || json.data.rows;
        node.loading = false;
        node.loaded = true;
      } else {
        node.loading = false;
        node.error = true;
      }
      // 强制刷新组件。
      this.forceUpdate();

      return json;
    }
    return null;
  }

  renderSearchResult(props: TransferProps) {
    const {
      searchResultMode,
      selectMode,
      noResultsText,
      searchResultColumns,
      columns,
      classnames: cx,
      value,
      disabled,
      onChange,
      option2value,
      optionItemRender,
      cellRender,
      multiple,
      labelField,
      virtualThreshold,
      itemHeight,
      virtualListHeight,
      checkAll,
      checkAllLabel,
      onlyChildren
    } = props;
    const {isTreeDeferLoad, searchResult} = this.state;
    const options = searchResult ?? [];
    const mode = searchResultMode || selectMode;
    const resultColumns = searchResultColumns || columns;

    return mode === 'table' ? (
      <TableSelection
        placeholder={noResultsText}
        className={cx('Transfer-selection')}
        columns={resultColumns!}
        options={options}
        value={value}
        disabled={disabled}
        onChange={onChange}
        option2value={option2value}
        cellRender={cellRender}
        itemRender={optionItemRender}
        multiple={multiple}
        virtualThreshold={virtualThreshold}
        itemHeight={itemHeight}
        virtualListHeight={virtualListHeight}
      />
    ) : mode === 'tree' ? (
      <Tree
        onRef={this.domRef}
        placeholder={noResultsText}
        className={cx('Transfer-selection')}
        options={options}
        value={value}
        disabled={disabled}
        onDeferLoad={this.handleDeferLoadForSearchCritia}
        onChange={(value: Array<any>) =>
          this.handleSearchTreeChange(value, options)
        }
        joinValues={false}
        showIcon={false}
        multiple={multiple}
        cascade={true}
        onlyChildren={onlyChildren ?? !isTreeDeferLoad}
        itemRender={optionItemRender}
        labelField={labelField}
        virtualThreshold={virtualThreshold}
        itemHeight={itemHeight}
        checkAllLabel={checkAllLabel}
        checkAll={checkAll}
        // placeholder="暂无选项"
      />
    ) : mode === 'chained' ? (
      <ChainedSelection
        placeholder={noResultsText}
        className={cx('Transfer-selection')}
        onDeferLoad={this.handleDeferLoadForSearchCritia}
        options={options}
        value={value}
        disabled={disabled}
        onChange={onChange}
        option2value={option2value}
        itemRender={optionItemRender}
        multiple={multiple}
        labelField={labelField}
        virtualThreshold={virtualThreshold}
        itemHeight={itemHeight}
        virtualListHeight={virtualListHeight}
        checkAllLabel={checkAllLabel}
        checkAll={checkAll}
      />
    ) : mode === 'associated' ? (
      <AssociatedSelection
        className={cx('Transfer-selection')}
        options={searchResult!.options || []}
        value={value}
        disabled={disabled}
        onChange={onChange}
        option2value={option2value}
        onDeferLoad={this.handleDeferLoadForSearchCritia}
        columns={columns}
        leftOptions={searchResult!.leftOptions || []}
        leftMode={this.props.leftMode}
        rightMode={this.props.rightMode}
        leftDefaultValue={this.props.leftDefaultValue}
        itemRender={optionItemRender}
        multiple={multiple}
        labelField={labelField}
        virtualThreshold={virtualThreshold}
        itemHeight={itemHeight}
        virtualListHeight={virtualListHeight}
        loadingConfig={this.props.loadingConfig}
        checkAllLabel={checkAllLabel}
        checkAll={checkAll}
        leftPlaceholder="暂无选项"
      />
    ) : (
     <GroupedSelection
        placeholder={noResultsText}
        className={cx('Transfer-selection')}
        options={options}
        value={value}
        disabled={disabled}
        onChange={onChange}
        option2value={option2value}
        itemRender={optionItemRender}
        multiple={multiple}
        labelField={labelField}
        virtualThreshold={virtualThreshold}
        itemHeight={itemHeight}
        virtualListHeight={virtualListHeight}
        checkAllLabel={checkAllLabel}
        checkAll={checkAll}
      />
    );
  }

  renderOptions(props: TransferProps) {
    const {
      selectMode,
      columns,
      options,
      value,
      disabled,
      onChange,
      option2value,
      classnames: cx,
      onDeferLoad,
      leftOptions,
      leftMode,
      rightMode,
      cellRender,
      leftDefaultValue,
      optionItemRender,
      multiple,
      noResultsText,
      labelField,
      virtualThreshold,
      itemHeight,
      virtualListHeight,
      loadingConfig,
      checkAll,
      checkAllLabel,
      onlyChildren
    } = props;

    return selectMode === 'table' ? (
      <TableSelection
        className={cx('Transfer-selection')}
        columns={columns!}
        options={options || []}
        value={value}
        disabled={disabled}
        onChange={onChange}
        option2value={option2value}
        onDeferLoad={onDeferLoad}
        cellRender={cellRender}
        multiple={multiple}
        virtualThreshold={virtualThreshold}
        itemHeight={itemHeight}
        virtualListHeight={virtualListHeight}
        checkAllLabel={checkAllLabel}
        checkAll={checkAll}
      />
    ) : selectMode === 'tree' ? (
      <Tree
        onRef={this.domRef}
        placeholder={noResultsText}
        className={cx('Transfer-selection')}
        options={options}
        value={value}
        disabled={disabled}
        onChange={onChange!}
        onlyChildren={onlyChildren ?? !this.state.isTreeDeferLoad}
        itemRender={optionItemRender}
        onDeferLoad={onDeferLoad}
        joinValues={false}
        showIcon={false}
        multiple={multiple}
        cascade={true}
        labelField={labelField}
        virtualThreshold={virtualThreshold}
        itemHeight={itemHeight}
        loadingConfig={loadingConfig}
        checkAllLabel={checkAllLabel}
        checkAll={checkAll}
      />
    ) : selectMode === 'chained' ? (
      <ChainedSelection
        className={cx('Transfer-selection')}
        options={options || []}
        value={value}
        disabled={disabled}
        onChange={onChange}
        option2value={option2value}
        onDeferLoad={onDeferLoad}
        itemRender={optionItemRender}
        multiple={multiple}
        labelField={labelField}
        virtualThreshold={virtualThreshold}
        itemHeight={itemHeight}
        virtualListHeight={virtualListHeight}
        loadingConfig={loadingConfig}
        checkAllLabel={checkAllLabel}
        checkAll={checkAll}
      />
    ) : selectMode === 'associated' ? (
      <AssociatedSelection
        className={cx('Transfer-selection')}
        options={options || []}
        value={value}
        disabled={disabled}
        onChange={onChange}
        option2value={option2value}
        onDeferLoad={onDeferLoad}
        columns={columns}
        leftOptions={leftOptions || []}
        leftMode={leftMode}
        rightMode={rightMode}
        leftDefaultValue={leftDefaultValue}
        itemRender={optionItemRender}
        multiple={multiple}
        labelField={labelField}
        virtualThreshold={virtualThreshold}
        itemHeight={itemHeight}
        virtualListHeight={virtualListHeight}
        loadingConfig={loadingConfig}
        checkAllLabel={checkAllLabel}
        checkAll={checkAll}
      />
    ) : (
      <GroupedSelection
        className={cx('Transfer-selection')}
        options={options || []}
        value={value}
        disabled={disabled}
        onChange={onChange}
        option2value={option2value}
        onDeferLoad={onDeferLoad}
        itemRender={optionItemRender}
        multiple={multiple}
        labelField={labelField}
        virtualThreshold={virtualThreshold}
        itemHeight={itemHeight}
        virtualListHeight={virtualListHeight}
        checkAllLabel={checkAllLabel}
        checkAll={checkAll}
      />
    );
  }

  renderResult() {
    const {
      columns,
      options,
      disabled,
      option2value,
      classnames: cx,
      cellRender,
      onChange,
      value,
      resultItemRender,
      resultSearchable,
      resultSearchPlaceholder,
      onResultSearch,
      sortable,
      labelField,
      translate: __,
      placeholder = __('Transfer.selectFromLeft'),
      virtualThreshold,
      itemHeight,
      loadingConfig,
      showInvalidMatch,
      pagination,
      accumulatedOptions
    } = this.props;

    const {resultSelectMode, isTreeDeferLoad} = this.state;
    const searchable = !isTreeDeferLoad && resultSearchable;

    switch (resultSelectMode) {
      case 'table':
        return (
          <ResultTableList
            classnames={cx}
            columns={columns!}
            options={(pagination?.enable ? accumulatedOptions : options) || []}
            value={value}
            disabled={disabled}
            option2value={option2value}
            cellRender={cellRender}
            onChange={onChange}
            multiple={false}
            searchable={searchable}
            placeholder={placeholder}
            searchPlaceholder={resultSearchPlaceholder}
            onSearch={onResultSearch}
            virtualThreshold={virtualThreshold}
            itemHeight={itemHeight}
          />
        );
      case 'tree':
        return (
          <ResultTreeList
            loadingConfig={loadingConfig}
            classnames={cx}
            className={cx('Transfer-value')}
            options={(pagination?.enable ? accumulatedOptions : options) || []}
            valueField={'value'}
            value={value || []}
            disabled={disabled}
            onChange={onChange!}
            itemRender={resultItemRender}
            searchable={searchable}
            placeholder={placeholder}
            searchPlaceholder={resultSearchPlaceholder}
            onSearch={onResultSearch}
            labelField={labelField}
            virtualThreshold={virtualThreshold}
            itemHeight={itemHeight}
          />
        );
      default:
        return (
          <ResultList
            className={cx('Transfer-value')}
            sortable={sortable}
            disabled={disabled}
            value={value}
            onChange={onChange}
            placeholder={placeholder}
            searchPlaceholder={resultSearchPlaceholder}
            itemRender={resultItemRender}
            searchable={searchable}
            onSearch={onResultSearch}
            labelField={labelField}
            virtualThreshold={virtualThreshold}
            itemHeight={itemHeight}
            showInvalidMatch={showInvalidMatch}
          />
        );
    }
  }

  render() {
    const {
      inline,
      classnames: cx,
      className,
      value,
      resultTitle,
      options,
      option2value,
      disabled,
      statistics,
      showArrow,
      resultListModeFollowSelect,
      selectMode = 'list',
      translate: __,
      pagination
    } = this.props;
    const {searchResult} = this.state;

    this.valueArray = BaseSelection.value2array(value, options, option2value);

    this.availableOptions = flattenTree(searchResult?.options || searchResult || options).filter(
      (option, index, list) =>
        !option.disabled &&
        option.value !== void 0 &&
        list.indexOf(option) === index
    );

    const tableType = resultListModeFollowSelect && selectMode === 'table';

    return (
      <div
        className={cx('Transfer', className, inline ? 'Transfer--inline' : '')}
      >
        <div className={cx('Transfer-select', {
          'Transfer-select--pagination': !!pagination?.enable
        })}>
          {this.renderSelect(this.props)}
        </div>
        <div className={cx('Transfer-mid')}>
          {showArrow /*todo 需要改成确认模式，即：点了按钮才到右边 */ ? (
            <div className={cx('Transfer-arrow')}>
              <Icon icon="right-arrow" className="icon" />
            </div>
          ) : null}
        </div>
        <div className={cx('Transfer-result', {
          'Transfer-select--pagination': !!pagination?.enable
        })}>
          <div
            className={cx(
              'Transfer-title',
              tableType ? 'Transfer-table-title' : '',
              selectMode === 'table' ? 'Transfer-title--light' : ''
            )}
          >
            <span>
              {__(resultTitle || 'Transfer.selectd')}
              {statistics !== false ? (
                <span>（{this.valueArray.length}）</span>
              ) : null}
            </span>
            <a
              onClick={this.clearAll}
              className={cx(
                'Transfer-clearAll',
                disabled || !this.valueArray.length ? 'is-disabled' : ''
              )}
            >
              {__('clear')}
            </a>
          </div>
          {this.renderResult()}
        </div>
      </div>
    );
  }
}

export default themeable(
  localeable(
    uncontrollable(class extends Transfer {}, {
      value: 'onChange'
    })
  )
);
