/**
 * @file Tree
 * @description 树形组件
 *
 * 情况列举：
 * 1. 选中父节点时，连带选中子节点 : autoChildren = true 前提条件
 *    1.1 交互
 *        1.1.1 子节点不可以取消勾选 cascade = false,
 *        1.1.2 子节点可以取消勾选 cascade = true, withChildren 失效
 *    1.2 数据（state.value）
 *        1.2.1 只提交父节点数据 cascade = false
 *        1.2.2 只提交子节点的数据  onlyChildren = true
 *        1.2.3 全部数据提交 withChildren = true || cascade = true
 *
 * 2. 选中节点时，只选中当前节点，没有联动效果
 *
 * <AUTHOR>
 */

import React from 'react';
import {
  filter,
  eachTree,
  isVisible,
  autobind,
  findTreeIndex,
  hasAbility,
  getTreeParent,
  getTreeAncestors,
  flattenTree,
  flattenTreeWithLeafNodes,
  isExpression,
  evalExpression,
} from 'amis-core';
import {Option, Options, value2array} from './Select';
import {themeable, ThemeProps, highlight, createObject} from 'amis-core';
import {Icon} from './icons';
import Checkbox from './Checkbox';
import {LocaleProps, localeable} from 'amis-core';
import Spinner, {SpinnerExtraProps} from './Spinner';
import {ItemRenderStates} from './Selection';
import VirtualList from './virtual-list';
import {cloneDeep, isEqual} from 'lodash';
import type { SchemaNode } from 'amis-core';
import type { Payload } from 'amis-core';

interface IDropIndicator {
  left: number;
  top: number;
  width: number;
}

export const DropPosition = {
  Slef: 'self',
  Top: 'top',
  BottomLeft: 'bottom-left',
  BottomRight: 'bottom-right',
} as const;

type IDropPosition = typeof DropPosition;

export interface IDropInfo {
  /** 拖拽节点 */
  dragNode: Option;
  /** 放置节点 */
  node: Option;
  position: IDropPosition[keyof IDropPosition];
  indicator: IDropIndicator | null;
}

interface TreeSelectorProps extends ThemeProps, LocaleProps, SpinnerExtraProps {
  highlightTxt?: string;

  onRef?: any;

  showIcon?: boolean;
  // 是否默认都展开
  initiallyOpen?: boolean;
  // 默认展开的级数，从1开始，只有initiallyOpen不是true时生效
  unfoldedLevel?: number;
  // 单选时，是否展示radio
  showRadio?: boolean;
  multiple?: boolean;
  // 是否都不可用
  disabled?: boolean;
  // 多选时，选中父节点时，是否将其所有子节点也融合到取值中，默认是不融合
  withChildren?: boolean;
  // 多选时，选中父节点时，是否只将起子节点加入到值中。
  onlyChildren?: boolean;
  // 单选时，只运行选择子节点
  onlyLeaf?: boolean;
  // 名称、取值等字段名映射
  labelField: string;
  valueField: string;
  iconField: string;
  unfoldedField: string;
  foldedField: string;
  disabledField: string;

  // 是否显示 outline 辅助线
  showOutline?: boolean;

  className?: string;
  itemClassName?: string;
  itemClassNameExpr?: string; // 配置行的表达式类名
  joinValues?: boolean;
  extractValue?: boolean;
  delimiter?: string;
  options: Options;
  value: any;
  onChange: Function;
  placeholder?: string;
  hideRoot?: boolean;
  rootLabel?: string;
  rootValue?: any;
  // 是否开启节点路径记录
  enableNodePath?: boolean;
  // 路径节点的分隔符
  pathSeparator?: string;
  // 已选择节点路径
  nodePath: any[];
  // ui级联关系，true代表级联选中，false代表不级联，默认为true
  autoCheckChildren: boolean;

  /*
   * 该属性代表数据级联关系，autoCheckChildren为true时生效，默认为false，具体数据级联关系如下：
   * 1.cascade 为false，ui行为为级联选中子节点，子节点禁用；值只包含父节点的值
   * 2.cascade为false，withChildren为true，ui行为为级联选中子节点，子节点禁用；值包含父子节点的值
   * 3.cascade为true，ui行为级联选中子节点，子节点可反选，值包含父子节点的值，此时withChildren属性失效
   * 4.cascade不论为true还是false，onlyChildren为true，ui行为级联选中子节点，子节点可反选，值只包含子节点的值
   */
  cascade?: boolean;

  /**
   * 是否使用 disable 字段
   */
  selfDisabledAffectChildren?: boolean;
  minLength?: number;
  maxLength?: number;
  // 是否为内建 增、改、删。当有复杂表单的时候直接抛出去让外层能统一处理
  bultinCUD?: boolean;
  rootCreatable?: boolean;
  rootCreateTip?: string;
  creatable?: boolean;
  createTip?: string;
  // 是否开启虚拟滚动
  virtualThreshold?: number;
  itemHeight?: number;
  onAdd?: (
    idx?: number | Array<number>,
    value?: any,
    skipForm?: boolean,
  ) => void;
  editable?: boolean;
  editTip?: string;
  onEdit?: (value: Option, origin?: Option, skipForm?: boolean) => void;
  removable?: boolean;
  removeTip?: string;
  onDelete?: (value: Option) => void;
  onDeferLoad?: (option: Option) => Promise<Payload | null>;
  onExpandTree?: (nodePathArr: any[]) => void;
  draggable?: boolean;
  onDragStart?: (option: Option) => void;
  onDragEnd?: (option: Option) => void;
  onDragOver?: (dropInfo: IDropInfo) => void;
  onDragLeave?: (dropInfo: IDropInfo) => void;
  onMove?: (dropInfo: IDropInfo) => void;
  itemRender?: (option: Option, states: ItemRenderStates) => JSX.Element;
  // 是否允许全选
  checkAll?: boolean;
  // 全选按钮文案
  checkAllLabel?: string;
  enableDefaultIcon?: boolean;
  // 展开/折叠图标
  switcherIcon?: JSX.Element;
  /**
   * 节点是否占据一行
   * @deprecated 1.70.0版本废弃，只支持整行，业务项目没搜到有用，猜测只是为了同步antd的tree功能
   */
  blockNode?: boolean;
  extraActions?: SchemaNode[];
  renderSchema?: (path: string, schema: any[], data: any) => React.ReactNode
  onExpand?: (option: Option, expanded: boolean) => void;
  onRightClick?: (option: Option) => void;
  data: any;
  autoFillHeight?: boolean;
  showActionsOnDisabled?: boolean; // 禁用状态下是否展示按钮
  itemDisabledOn?: string; // 控制单个选项的表达式模式
}

interface TreeSelectorState {
  value: Array<any>;
  emitedValue: any;
  inputValue: string;
  addingParent: Option | null;
  isAdding: boolean;
  isEditing: boolean;
  editingItem: Option | null;
  // 拍平的 Option list
  flattenedOptions: Option[];
  // 拖拽指示器
  dropIndicator: IDropIndicator | null;
  rootHeight: number;
}

export class TreeSelector extends React.Component<
  TreeSelectorProps,
  TreeSelectorState
> {
  static defaultProps = {
    showIcon: true,
    showOutline: false,
    initiallyOpen: true,
    unfoldedLevel: 1,
    showRadio: false,
    multiple: false,
    disabled: false,
    withChildren: false,
    onlyChildren: false,
    labelField: 'label',
    valueField: 'value',
    iconField: 'icon',
    unfoldedField: 'unfolded',
    foldedField: 'folded',
    disabledField: 'disabled',
    joinValues: true,
    extractValue: false,
    delimiter: ',',
    hideRoot: true,
    rootLabel: 'Tree.root',
    rootValue: 0,
    autoCheckChildren: true,
    cascade: false,
    selfDisabledAffectChildren: true,
    rootCreateTip: 'Tree.addRoot',
    createTip: 'Tree.addChild',
    editTip: 'Tree.editNode',
    removeTip: 'Tree.removeNode',
    enableNodePath: false,
    pathSeparator: '/',
    nodePath: [],
    virtualThreshold: 100,
    itemHeight: 32,
    enableDefaultIcon: true,
    blockNode: true,
    extraActions: [],
    onlyLeafShowCheckbox: false, // 这个属性是给 TabsTransfer 的级联模式使用的，搜索状态展示树并且只有叶子结点展示checkbox
  };
  // 展开的节点
  unfolded: WeakMap<Object, boolean> = new Map();
  // key: child option, value: parent option;
  relations: WeakMap<Option, Option> = new Map();

  dragNode: Option | null;
  dropInfo: IDropInfo | null;

  /** Tree 组件的根元素 */
  root = React.createRef<HTMLDivElement>();

  constructor(props: TreeSelectorProps) {
    super(props);
    this.state = {
      value: value2array(
        props.value,
        {
          multiple: props.multiple,
          delimiter: props.delimiter,
          valueField: props.valueField,
          labelField: props.labelField,
          options: props.options,
          pathSeparator: props.pathSeparator,
        },
        props.enableNodePath,
      ),
      emitedValue: props.value,
      flattenedOptions: [],
      inputValue: '',
      addingParent: null,
      isAdding: false,
      isEditing: false,
      editingItem: null,
      dropIndicator: null,
      rootHeight: 266,
    };

    this.syncUnFolded(props);
    this.flattenOptions(props, true);
  }

  componentDidMount() {
    const {enableNodePath, autoFillHeight} = this.props;

    // onRef只有渲染器的情况才会使用
    this.props.onRef?.(this);
    enableNodePath && this.expandLazyLoadNodes();
     // 修复issue511，设置虚拟滚动后虚拟列表展示的元素不正确的问题，当组件设置了autoFillHeight，或者当前容器的高度
    if (this.root?.current && autoFillHeight) {
      this.setState({
        rootHeight: this.root.current.clientHeight
      });
    }
  }

  componentDidUpdate(prevProps: TreeSelectorProps) {
    const props = this.props;

    if (prevProps.options !== props.options) {
      this.syncUnFolded(props);
      this.flattenOptions(props);
    }

    if (
      !isEqual(this.state.emitedValue, props.value) ||
      prevProps.options !== props.options
    ) {
      this.setState({
        emitedValue: props.value,
      });
      this.setState({
        value: value2array(
          props.value,
          {
            multiple: props.multiple,
            delimiter: props.delimiter,
            valueField: props.valueField,
            pathSeparator: props.pathSeparator,
            options: props.options,
            labelField: props.labelField,
          },
          props.enableNodePath,
        ),
      });
    }
  }

  /**
   * 展开懒加载节点的父节点
   */
  expandLazyLoadNodes() {
    const {pathSeparator, onExpandTree, nodePath = []} = this.props;
    const nodePathArr = nodePath.map(path =>
      path ? path.toString().split(pathSeparator) : [],
    );
    onExpandTree?.(nodePathArr);
  }

  syncUnFolded(props: TreeSelectorProps, unfoldedLevel?: number, openValues?: any[]) {
    // 传入默认展开层级需要重新初始化unfolded
    let initFoldedLevel = typeof unfoldedLevel !== 'undefined';
    const expandLevel =
      Number(initFoldedLevel ? unfoldedLevel : props.unfoldedLevel) - 1;

    // 初始化树节点的展开状态
    let unfolded = this.unfolded;
    const {foldedField, unfoldedField, valueField } = this.props;

    eachTree(props.options, (node: Option, index, level, path) => {
      // 同时满足以下条件，则不继续展开判断：1. 存在子节点 2. 未指定展开层级 3. 未指定展开值
      if (unfolded.has(node[valueField] || node) && !initFoldedLevel && !openValues) {
        return;
      }

      if (node.children && node.children.length) {
        let ret: any = true;

        if (
          node.defer &&
          node.loaded &&
          !initFoldedLevel &&
          unfoldedField &&
          node[unfoldedField] !== false
        ) {
          ret = true;
        } else if (
          unfoldedField &&
          typeof node[unfoldedField] !== 'undefined'
        ) {
          ret = !!node[unfoldedField];
        } else if (foldedField && typeof node[foldedField] !== 'undefined') {
          ret = !node[foldedField];
        } else {
          ret = !!props.initiallyOpen && !initFoldedLevel;

          // expandLevel和openValues不能同时存在，同时存在以openValues为主
          if (!ret && level <= expandLevel && !openValues) {
            ret = true;
          }
        }

        unfolded.set(node[valueField] || node, ret);

        // 值在 openValues 中的选项及其祖先选项要展开
        if (openValues && openValues.includes(node[valueField])) {
          path.forEach(opt => {
            unfolded.set(opt[valueField] || opt, true);
            return;
          });
        }
      }
    });

    initFoldedLevel && this.forceUpdate();

    return unfolded;
  }

  @autobind
  async toggleUnfolded(node: any) {
    const unfolded = this.unfolded;
    const {onExpand, onDeferLoad, unfoldedField, valueField} = this.props;
    onExpand?.(node, !unfolded.get(node[valueField] || node));

    // ！ hack: 在node上直接添加属性，options 在更新的时候旧的字段会保留
    if (node.defer && node.loaded) {
      node[unfoldedField] = !unfolded.get(node[valueField] || node);
    }

    unfolded.set(node[valueField] || node, !unfolded.get(node[valueField] || node));

    if (node.defer && !node.loaded) {
      await onDeferLoad?.(node);
    }
    this.flattenOptions();
    this.forceUpdate();
  }

  // 判断节点是否展开，供后续过滤数据使用
  isUnfolded(node: any): boolean {
    const { valueField } = this.props;
    const unfolded = this.unfolded;
    const parent = this.relations.get(node[valueField] || node);
    if (parent) {
      // 节点展开条件：自身节点展开，同时父级节点也展开
      return !!unfolded.get(node[valueField] || node) && this.isUnfolded(parent);
    }
    return !!unfolded.get(node[valueField] || node);
  }

  @autobind
  clearSelect() {
    const {joinValues, rootValue, onChange} = this.props;
    const emitedValue = joinValues? rootValue : [];
    this.setState({
      emitedValue
    });
    this.setState(
      {
        value: [],
      },
      () => {

        onChange(emitedValue);
      },
    );
  }

  /**
   * enableNodePath为true时，将label和value转换成node path格式
   */
  transform2NodePath(value: any) {
    const {
      multiple,
      options,
      valueField,
      labelField,
      joinValues,
      extractValue,
      pathSeparator,
      delimiter,
    } = this.props;

    const nodesValuePath: string[] = [];
    const selectedNodes = Array.isArray(value) ? value.concat() : [value];
    const selectedNodesPath = selectedNodes.map(node => {
      const nodePath = getTreeAncestors(options, node, true)?.reduce(
        (acc, node) => {
          acc[labelField as string].push(node[labelField as string]);
          acc[valueField as string].push(node[valueField as string]);
          return acc;
        },
        {[labelField as string]: [], [valueField as string]: []},
      );
      const nodeValuePath = nodePath[valueField as string].join(pathSeparator);

      nodesValuePath.push(nodeValuePath);
      return {
        ...node,
        [labelField]: nodePath[labelField as string].join(pathSeparator),
        [valueField]: nodeValuePath,
      };
    });

    if (multiple) {
      return joinValues
        ? nodesValuePath.join(delimiter)
        : extractValue
        ? nodesValuePath
        : selectedNodesPath;
    } else {
      return joinValues || extractValue
        ? selectedNodesPath[0][valueField]
        : selectedNodesPath[0];
    }
  }

  @autobind
  handleSelect(node: any, value?: any) {
    const {joinValues, valueField, onChange, enableNodePath, onlyLeaf} =
      this.props;

    if (node[valueField as string] === undefined) {
      if (node.defer && !node.loaded) {
        this.toggleUnfolded(node);
      }
      return;
    }

    if (onlyLeaf && node.children) {
      return;
    }

    const emitedValue = enableNodePath
      ? this.transform2NodePath(node)
      : joinValues
      ? node[valueField as string]
      : cloneDeep(node);
    this.setState({
      emitedValue,
    });

    this.setState(
      {
        value: [node],
      },
      () => {
        onChange(emitedValue);
      },
    );
  }

  @autobind
  handleCheck(item: any, checked: boolean) {
    // TODO: 重新梳理这里的逻辑
    const props = this.props;
    const value = this.state.value.concat();
    const idx = value.indexOf(item);
    const {onlyChildren, withChildren, cascade, autoCheckChildren} = props;
    if (checked) {
      ~idx || value.push(item);
      // cascade 为 true 表示父节点跟子节点没有级联关系。
      if (autoCheckChildren) {
        const children = item.children ? item.children.concat([]) : [];
        const hasDisabled = flattenTree(children).some(item => item?.disabled);

        if (onlyChildren) {
          // 父级选中的时候，子节点也都选中，但是自己不选中
          !~idx && children.length && value.pop();

          // 这个 isAllChecked 主要是判断如果有disabled的item项，这时父节点还是选中的话，针对性的处理逻辑
          const isAllChecked = flattenTreeWithLeafNodes(children)
            .filter(item => !item?.disabled)
            .every(v => ~value.indexOf(v));

          while (children.length) {
            let child = children.shift();
            let index = value.indexOf(child);

            if (child.children && child.children.length) {
              children.push.apply(children, child.children);
              continue;
            }

            if (hasDisabled && isAllChecked) {
              if (
                ~index &&
                children.value !== 'undefined' &&
                !child?.disabled
              ) {
                value.splice(index, 1);
              }
              continue;
            }

            if (!~index && child.value !== 'undefined' && !child?.disabled) {
              value.push(child);
            }
          }
        } else {
          // 这个 isAllChecked 主要是判断如果有disabled的item项，这时父节点还是选中的话，针对性的处理逻辑
          const isAllChecked = flattenTree(children)
            .filter(item => !item?.disabled)
            .every(v => ~value.indexOf(v));

          // 只要父节点选择了,子节点就不需要了,全部去掉勾选.  withChildren时相反
          while (children.length) {
            let child = children.shift();
            let index = value.indexOf(child);

            if (!child?.disabled) {
              // 判断下下面是否有禁用项
              if (!hasDisabled) {
                if (~index) {
                  value.splice(index, 1);
                }

                if (withChildren || cascade) {
                  value.push(child);
                }
              } else {
                isAllChecked ? value.splice(index, 1) : value.push(child);
              }
            }

            if (child.children && child.children.length) {
              children.push.apply(children, child.children);
            }
          }

          let toCheck = item;

          while (true) {
            const parent = getTreeParent(props.options, toCheck);
            // 判断 parent 节点是否已经勾选，避免重复值
            if (parent?.value && !~value.indexOf(parent)) {
              // 如果所有孩子节点都勾选了，应该自动勾选父级。

              if (
                parent.children.every((child: any) => ~value.indexOf(child))
              ) {
                if (!cascade && !withChildren) {
                  parent.children.forEach((child: any) => {
                    const index = value.indexOf(child);
                    if (~index) {
                      value.splice(index, 1);
                    }
                  });
                }
                value.push(parent);
                toCheck = parent;
                continue;
              }
            }
            break;
          }
        }
      }
    } else {
      ~idx && value.splice(idx, 1);
      if (autoCheckChildren) {
        if (cascade || withChildren || onlyChildren) {
          const children = item.children ? item.children.concat([]) : [];
          while (children.length) {
            let child = children.shift();
            let index = value.indexOf(child);
            if (~index && !child?.disabled) {
              value.splice(index, 1);
            }
            if (child.children && child.children.length) {
              children.push.apply(children, child.children);
            }
          }
        }
      }
    }

    this.setState(
      {
        value,
      },
      () => this.fireChange(value),
    );
  }

  handleRightClick(item: Option, e: React.MouseEvent) {
    if (e.button === 2) {
      this.props.onRightClick?.(item);
    }
  }

  fireChange(value: Option[]) {
    const {
      joinValues,
      extractValue,
      valueField,
      delimiter,
      onChange,
      enableNodePath,
    } = this.props;

    const emitedValue = enableNodePath
      ? this.transform2NodePath(value)
      : joinValues
      ? value.map(item => item[valueField as string]).join(delimiter)
      : extractValue
      ? value.map(item => item[valueField as string])
      : value;

    this.setState({
      emitedValue,
    });
    onChange(emitedValue);
  }

  @autobind
  handleAdd(parent: Option | null = null) {
    const {bultinCUD, onAdd, options, valueField} = this.props;

    if (!bultinCUD) {
      const idxes = findTreeIndex(options, item => item === parent) || [];
      return onAdd && onAdd(idxes.concat(0));
    } else {
      this.setState(
        {
          isEditing: false,
          isAdding: true,
          addingParent: parent,
        },
        () => {
          if (!parent) {
            return;
          }

          const result = [] as Option[];

          for (let option of this.state.flattenedOptions) {
            result.push(option);

            if (option === parent) {
              result.push({
                [valueField]: Date.now(),
                isAdding: true,
              });
            }
          }

          this.setState({flattenedOptions: result});
        },
      );
    }
  }

  @autobind
  handleEdit(item: Option) {
    const {bultinCUD, onEdit, labelField, options} = this.props;

    if (!bultinCUD) {
      onEdit?.(item);
    } else {
      this.setState({
        isEditing: true,
        isAdding: false,
        editingItem: item,
        inputValue: item[labelField],
      });
    }
  }

  @autobind
  handleRemove(item: Option) {
    const {onDelete} = this.props;

    onDelete && onDelete(item);
  }

  @autobind
  handleInputChange(e: React.ChangeEvent<HTMLInputElement>) {
    this.setState({
      inputValue: e.currentTarget.value,
    });
  }

  /** 确认新增 */
  @autobind
  handleConfirm() {
    const {
      inputValue: value,
      isAdding,
      addingParent,
      editingItem,
      isEditing,
    } = this.state;

    if (!value) {
      return;
    }

    const {labelField, onAdd, options, onEdit} = this.props;
    this.setState(
      {
        inputValue: '',
        isAdding: false,
        isEditing: false,
      },
      () => {
        if (isAdding && onAdd) {
          const idxes =
            (addingParent &&
              findTreeIndex(options, item => item === addingParent)) ||
            [];
          onAdd(idxes.concat(0), {[labelField]: value}, true);
        } else if (isEditing && onEdit) {
          onEdit(
            {
              ...editingItem,
              [labelField]: value,
            },
            editingItem!,
            true,
          );
        }
      },
    );
  }

  /** 取消新增 */
  @autobind
  handleCancel() {
    const {flattenedOptions} = this.state;
    const flattenedOptionsWithoutAdding = flattenedOptions.filter(
      item => !item.isAdding,
    );
    this.setState({
      inputValue: '',
      isAdding: false,
      isEditing: false,
      flattenedOptions: flattenedOptionsWithoutAdding,
    });
  }

  renderInput(prfix: JSX.Element | null = null, itemClass?: string) {
    const {classnames: cx, translate: __} = this.props;
    const {inputValue} = this.state;

    return (
      <div className={cx('Tree-itemLabel', itemClass)}>
        <div className={cx('Tree-itemInput')}>
          {prfix}
          <input
            onChange={this.handleInputChange}
            value={inputValue}
            placeholder={__('placeholder.enter')}
          />
          <a data-tooltip={__('cancel')} onClick={this.handleCancel}>
            <Icon icon="close" className="icon" />
          </a>
          <a data-tooltip={__('confirm')} onClick={this.handleConfirm}>
            <Icon icon="check" className="icon" />
          </a>
        </div>
      </div>
    );
  }

  getOffsetPosition(element: HTMLElement) {
    let left = 0;
    let top = 0;

    while (element.offsetParent) {
      left += element.offsetLeft;
      top += element.offsetTop;
      element = element.offsetParent as HTMLElement;
    }
    return {left, top};
  }

  /** 放置节点和拖拽节点为同一节点时 indicator 为 null */
  @autobind
  getDropInfo(e: React.DragEvent<Element>, node: Option): IDropInfo {
    const dragNode = this.dragNode!;

    if (node === dragNode) {
      return {
        node,
        dragNode,
        position: DropPosition.Slef,
        indicator: null,
      };
    }

    // 计算放置节点相对与树组件根节点的位置
    const rootOffset = this.getOffsetPosition(this.root.current!);
    const nodeOffset = this.getOffsetPosition(e.currentTarget as HTMLElement);
    const left = nodeOffset.left - rootOffset.left;
    const top = nodeOffset.top - rootOffset.top;

    // indicator 相关的一些位置偏移量
    // 基础偏横向移量，为了与拖拽 icon 对齐
    const basicOffsetX = 17;
    // 触发将拖拽节点识别为子节点的偏移量，其值为选项 icon 左边缘到选项左边缘距离
    const subOptionTriggerOffsetX = 50;
    // 子节点缩进偏移量
    const subOptionIndentOffsetX = 16;

    // 放置节点的尺寸位置信息
    const rect = e.currentTarget.getBoundingClientRect();
    const {clientX, clientY} = e;
    let position: IDropInfo['position'] = DropPosition.Top;
    let indicator: IDropIndicator = {
      top,
      left: left + basicOffsetX,
      width: rect.width - basicOffsetX,
    };

    if (clientY >= rect.top + rect.height / 2) {
      indicator.top += rect.height;

      if (clientX < rect.left + subOptionTriggerOffsetX) {
        position = DropPosition.BottomLeft;
      } else {
        position = DropPosition.BottomRight;
        indicator.left += subOptionIndentOffsetX;
        indicator.width -= subOptionIndentOffsetX;
      }
    }

    return {
      node,
      dragNode,
      position,
      indicator,
    };
  }

  @autobind
  onDragStart(node: Option) {
    return (e: React.DragEvent<Element>) => {
      e.stopPropagation();
      e.dataTransfer.effectAllowed = 'copyMove';

      this.dragNode = node;
      this.dropInfo = null;

      if (node?.children?.length) {
        this.unfolded.set(node[this.props.valueField] || node, false);
        this.flattenOptions();
        this.forceUpdate();
      }

      this.props.onDragStart?.(node);
    };
  }

  @autobind
  onDragOver(node: Option) {
    return (e: React.DragEvent<Element>) => {
      e.preventDefault();
      this.dropInfo = this.getDropInfo(e, node);
      this.setState({
        dropIndicator: this.dropInfo.indicator,
      });
      this.props.onDragOver?.(this.dropInfo);
    };
  }

  onDragLeave = (node: Option) => {
    return (e: React.DragEvent<Element>) => {
      this.setState({dropIndicator: null});
      this.props.onDragLeave?.(this.getDropInfo(e, node));
    };
  };

  onDragEnd = () => {
    this.setState({dropIndicator: null});
    const dragNode = this.dragNode!;
    this.dragNode = null;
    setTimeout(() => {
      // 延迟清除，因为 onDrop 中还要用
      this.dropInfo = null;
    });

    this.props.onDragEnd?.(dragNode);
  };

  onDrop = (e: React.DragEvent) => {
    e.preventDefault();
    this.props.onMove?.(this.dropInfo!);
  };

  /**
   * 将树形接口转换为平铺结构，以支持虚拟列表
   * TODO: this.unfolded => reaction 更加合理
   */
  flattenOptions(
    props?: TreeSelectorProps,
    initial?: boolean,
  ): void | Option[] {
    let flattenedOptions: Option[] = [];

    eachTree(
      props?.options || this.props.options,
      (item, _i, level, path: Option[]) => {
        item.level = level;

        if (!isVisible(item)) {
          return;
        }

        if (path.length === 1) {
          // 父节点
          flattenedOptions.push(item);
          return;
        }

        const parent = path[path.length - 2];

        if (this.isUnfolded(parent)) {
          this.relations.set(item[this.props.valueField] || item, parent);
          // 父节点是展开的状态
          flattenedOptions.push(item);
        }
      },
    );

    if (initial) {
      // 初始化
      this.state = {...this.state, flattenedOptions};
    } else {
      this.setState({flattenedOptions});
    }
  }

  /**
   * 判断父元素是否勾选
   * TODO: 递归可能需要优化
   */
  isParentChecked(item?: Option): boolean {
    if (!item || !this.relations.get(item[this.props.valueField] || item)) {
      return false;
    }
    const itemParent = this.relations.get(item[this.props.valueField] || item);
    const {value} = this.state;
    const checked = !!~value.indexOf(itemParent);

    return checked || this.isParentChecked(itemParent);
  }

  /**
   * 判断 子元素 是否全部选中
   */
  isItemChildrenChecked(item: Option) {
    if (!item || !item.children) {
      return true;
    }
    return !item.children.some(child => !this.isItemChecked(child));
  }

  /**
   * 判断子元素 部分勾选
   */
  isItemChildrenPartialChecked(item: Option, checked: boolean): boolean {
    if (!item || !item.children || checked) {
      return false;
    }
    let checkedLength = 0;
    let partialChildrenLength = 0;
    for (const child of item.children) {
      if (this.isItemChecked(child)) {
        checkedLength++;
      } else if (this.isItemChildrenPartialChecked(child, false)) {
        partialChildrenLength++;
      }
    }

    return checkedLength !== 0 || partialChildrenLength !== 0;
  }

  /**
   * 判断元素是否选中：checked
   */
  isItemChecked(item?: Option): boolean {
    if (!item) {
      return false;
    }

    const {autoCheckChildren, onlyChildren, multiple, withChildren, cascade} =
      this.props;
    const {value} = this.state;
    const checked = !!~value.indexOf(item);

    if (checked) {
      return true;
    }

    if (item.children?.length) {
      if (
        onlyChildren &&
        autoCheckChildren &&
        this.isItemChildrenChecked(item) // TODO: 优化这个逻辑
      ) {
        // 当前元素没有在 value 中，但是子组件全部勾选了
        return true;
      }
    }
    const itemParent = this.relations.get(item[this.props.valueField] || item);
    if (itemParent && multiple && autoCheckChildren) {
      // 当前节点为子节点
      if (withChildren) {
        return false;
      }
      if (cascade) {
        return false;
      }
      return this.isParentChecked(item);
    }

    // 判断父组件是否勾选
    return false;
  }

  /**
   * item 是否 disabled 状态
   * props.disabled === true return;
   *
   */
  isItemDisabled(item: Option, checked: boolean) {
    const {
      disabledField,
      disabled,
      autoCheckChildren,
      valueField,
      multiple,
      maxLength,
      minLength,
      cascade,
      onlyChildren,
      itemDisabledOn,
      data,
    } = this.props;
    const {value} = this.state;
    const selfDisabled = item[disabledField];
    const itemDisabled = isExpression(itemDisabledOn) ? evalExpression(itemDisabledOn as string, createObject(data, item)) : false;
    const nodeDisabled =
      !!disabled ||
      selfDisabled ||
      itemDisabled ||
      (multiple && !autoCheckChildren && !item[valueField]);

    if (nodeDisabled) {
      return true;
    }

    if (
      (maxLength && !checked && value.length >= maxLength) ||
      (minLength && checked && value.length <= minLength)
    ) {
      return true;
    }

    const itemParent = this.relations.get(item[valueField] || item);

    if (
      autoCheckChildren &&
      multiple &&
      checked &&
      itemParent &&
      this.isItemChecked(itemParent)
    ) {
      // 子节点
      if (onlyChildren) {
        return false;
      }
      return !cascade;
    }

    return false;
  }

  @autobind
  renderItem({index, style}: {index: number; style?: Record<string, any>}) {
    const {
      itemClassName,
      itemClassNameExpr,
      showIcon,
      showRadio,
      multiple,
      labelField,
      iconField,
      cascade,
      classnames: cx,
      highlightTxt,
      creatable,
      editable,
      removable,
      createTip,
      editTip,
      removeTip,
      translate: __,
      itemRender,
      draggable,
      loadingConfig,
      enableDefaultIcon,
      valueField,
      switcherIcon,
      extraActions,
      renderSchema,
      data,
      showActionsOnDisabled,
      options,
      onlyLeafShowCheckbox,
      alwaysShowActions,
    } = this.props;

    const item = this.state.flattenedOptions[index];

    if (!item) {
      return null;
    }
    let extraActionList: any[] = extraActions as any;
    if (!Array.isArray(extraActions)) {
      extraActionList = [extraActions]
    }

    const allData = createObject(data, item);
    let extraActionDoms = null;
    if (typeof renderSchema === 'function') {
      extraActionDoms = renderSchema('/input-tree/extraactions', extraActionList, {data: allData});
    }

    const {isAdding, editingItem, isEditing} = this.state;

    const checked = this.isItemChecked(item);
    const disabled = this.isItemDisabled(item, checked);
    const partial = this.isItemChildrenPartialChecked(item, checked);
    const checkedInValue = !!~this.state.value.indexOf(item);

    const checkbox: JSX.Element | null = multiple ? (
      <Checkbox
        size="sm"
        disabled={disabled}
        checked={checked || partial}
        partial={partial}
        onChange={this.handleCheck.bind(this, item, !checked)}
      />
    ) : showRadio ? (
      <Checkbox
        size="sm"
        disabled={disabled}
        checked={checked}
        onChange={this.handleSelect.bind(this, item)}
      />
    ) : null;

    const isLeaf =
      (!item.children || !item.children.length) && !item.placeholder;
    const iconValue =
      item[iconField] ||
      (enableDefaultIcon !== false
        ? item.children
          ? 'folder'
          : 'file'
        : false);
    const level = item.level ? item.level - 1 : 0;
    const isUnfolded = this.isUnfolded(item);

    // 获取当前行数据的类名
    const itemClass = itemClassNameExpr
      ? filter(itemClassNameExpr, createObject(data,
        {
          ...item,
          __options: options, // 树全量数据
          __item: item, // 行数据
          __folded: !isUnfolded, // 展开/收起状态
          __checked: checked, // 选中状态
        }))
      : itemClassName

    let body = null;

    if (isEditing && editingItem === item) {
      body = this.renderInput(checkbox, itemClass);
    } else if (item.isAdding) {
      body = this.renderInput(checkbox, itemClass);
    } else {
      const dragHanlers = draggable
        ? {
            onDragStart: this.onDragStart(item),
            onDragOver: this.onDragOver(item),
            onDragLeave: this.onDragLeave(item),
            onDragEnd: this.onDragEnd,
            onDrop: this.onDrop,
          }
        : {};

      body = (
        <div
          className={cx('Tree-itemLabel', itemClass, {
            'is-children-checked':
              multiple &&
              !cascade &&
              this.isItemChildrenChecked(item) &&
              !disabled,
            'is-checked': checkedInValue,
            'is-disabled': disabled,
          })}
          draggable={draggable}
          {...dragHanlers}
        >
          {draggable && (
            <a className={cx('Tree-itemDrager drag-bar')}>
              <Icon icon="drag-bar" className="icon" />
            </a>
          )}

          {item.loading ? (
            <Spinner
              size="sm"
              show
              icon="reload"
              spinnerClassName={cx('Tree-spinner')}
              loadingConfig={loadingConfig}
            />
          ) : !isLeaf || (item.defer && !item.loaded) ? (
            <div
              onClick={() => this.toggleUnfolded(item)}
              className={cx('Tree-itemArrow', {
                'is-folded': !isUnfolded,
              })}
            >
              {switcherIcon ? (
                switcherIcon
              ) : (
                <Icon icon="down-arrow-bold" className="icon" />
              )}
            </div>
          ) : (
            <span className={cx('Tree-itemArrowPlaceholder')} />
          )}

          {onlyLeafShowCheckbox ? isLeaf ? checkbox : null : checkbox}

          <div className={cx('Tree-itemLabel-item')}>
            {showIcon ? (
              <i
                className={cx(
                  `Tree-itemIcon ${
                    item.children ? 'Tree-folderIcon' : 'Tree-leafIcon'
                  }`,
                )}
                onClick={() =>
                  !disabled &&
                  (multiple
                    ? this.handleCheck(item, !checked)
                    : this.handleSelect(item))
                }
              >
                {iconValue ? <Icon icon={iconValue} className="icon" /> : null}
              </i>
            ) : null}

            <span
              className={cx('Tree-itemText')}
              onClick={() =>
                !disabled &&
                (multiple
                  ? this.handleCheck(item, !checked)
                  : this.handleSelect(item))
              }
              title={item[labelField]}
            >
              {itemRender
                ? itemRender(item, {
                    index,
                    multiple: multiple,
                    checked: checked,
                    onChange: () => this.handleCheck(item, !checked),
                    disabled: disabled || item.disabled,
                  })
                : highlightTxt
                ? highlight(`${item[labelField]}`, highlightTxt)
                : `${item[labelField]}`}
            </span>

            {/* issue657: 删除defer节点限制按钮未loaded时悬浮不展示 */}
            {!isAdding &&
            !isEditing ? (
              <div className={cx('Tree-item-icons', {
                'always-show': alwaysShowActions,
              })}>
                {!disabled && creatable && hasAbility(item, 'creatable') ? (
                  <a
                    onClick={this.handleAdd.bind(this, item)}
                    data-tooltip={__(createTip)}
                    data-position="left"
                  >
                    <Icon icon="plus" className="icon" />
                  </a>
                ) : null}

                {!disabled && removable && hasAbility(item, 'removable') ? (
                  <a
                    onClick={this.handleRemove.bind(this, item)}
                    data-tooltip={__(removeTip)}
                    data-position="left"
                  >
                    <Icon icon="minus" className="icon" />
                  </a>
                ) : null}

                {!disabled && editable && hasAbility(item, 'editable') ? (
                  <a
                    onClick={this.handleEdit.bind(this, item)}
                    data-tooltip={__(editTip)}
                    data-position="left"
                  >
                    <Icon icon="new-edit" className="icon" />
                  </a>
                ) : null}

                {
                  (!disabled || disabled && showActionsOnDisabled) && extraActions && hasAbility(item, 'additionable') ? extraActionDoms : null
                }
              </div>
            ) : null}
          </div>
        </div>
      );
    }

    // 配置itemClassNameExpr表达式动态设置类名
    const finalItemClassName = cx(
      'Tree-item',
      {
        'Tree-item--isLeaf': isLeaf,
        'is-child': this.relations.get(item[valueField] || item),
        'is-checked': checkedInValue,
      }
    )

    return (
      <li
        key={item[valueField]}
        className={finalItemClassName}
        style={{
          ...style,
          left: `calc(${level} * var(--Tree-indent))`,
          width: `calc(100% - ${level} * var(--Tree-indent))`,
        }}
        onContextMenu={this.handleRightClick.bind(this, item)}
      >
        {body}
      </li>
    );
  }

  isEmptyOrNotExist(obj: any) {
    return obj === '' || obj === undefined || obj === null;
  }

  getAvailableOptions() {
    const {options, onlyChildren, valueField} = this.props;
    const flattendOptions = flattenTree(options, item =>
      onlyChildren
        ? item.children
          ? null
          : item
        : this.isEmptyOrNotExist(item[valueField || 'value'])
        ? null
        : item,
    ).filter(a => a && !a.disabled);

    return flattendOptions as Option[];
  }

  @autobind
  handleCheckAll(availableOptions: Option[], checkedAll: boolean) {
    this.setState(
      {
        value: checkedAll ? [] : availableOptions,
      },
      () => this.fireChange(checkedAll ? [] : availableOptions),
    );
  }

  renderCheckAll() {
    const {
      multiple,
      checkAll,
      checkAllLabel,
      classnames: cx,
      translate: __,
      disabled,
    } = this.props;

    if (!multiple || !checkAll) {
      return null;
    }

    const availableOptions = this.getAvailableOptions();

    const checkedAll = availableOptions.every(option =>
      this.isItemChecked(option),
    );
    const checkedPartial = availableOptions.some(option =>
      this.isItemChecked(option),
    );

    return (
      <div
        className={cx('Tree-itemLabel')}
        onClick={() => this.handleCheckAll(availableOptions, checkedAll)}
      >
        <Checkbox
          size="sm"
          disabled={disabled}
          checked={checkedPartial}
          partial={checkedPartial && !checkedAll}
        />

        <div className={cx('Tree-itemLabel-item')}>
          <span className={cx('Tree-itemText')}>{__(checkAllLabel)}</span>
        </div>
      </div>
    );
  }

  @autobind
  renderList(list: Options, value: any[]) {
    const {virtualThreshold, itemHeight = 32} = this.props;
    // 修复issue511，设置虚拟滚动后虚拟列表展示的元素不正确的问题
    const { rootHeight } = this.state;
    if (virtualThreshold && list.length > virtualThreshold) {
      return (
        <VirtualList
          height={list.length > 8 ? rootHeight : list.length * itemHeight}
          itemCount={list.length}
          prefix={this.renderCheckAll()}
          itemSize={itemHeight}
          //! hack: 让 VirtualList 重新渲染
          renderItem={this.renderItem.bind(this)}
        />
      );
    }

    return (
      <>
        {this.renderCheckAll()}
        {list.map((item, index) => this.renderItem({index}))}
      </>
    );
  }

  render() {
    const {
      className,
      placeholder,
      hideRoot,
      rootLabel,
      showOutline,
      showIcon,
      classnames: cx,
      creatable,
      rootCreatable,
      rootCreateTip,
      disabled,
      draggable,
      blockNode,
      translate: __,
      autoFillHeight,
    } = this.props;
    const {
      value,
      isAdding,
      addingParent,
      isEditing,
      dropIndicator,
      flattenedOptions,
    } = this.state;

    let addBtn = null;

    if (creatable && rootCreatable !== false && hideRoot) {
      addBtn = (
        <a
          className={cx('Tree-addTopBtn', {
            'is-disabled': isAdding || isEditing,
          })}
          onClick={this.handleAdd.bind(this, null)}
        >
          <Icon icon="plus" className="icon" />
          <span>{__(rootCreateTip)}</span>
        </a>
      );
    }

    return (
      <div
        className={cx(`Tree ${className || ''}`, {
          'Tree--outline': showOutline,
          'is-disabled': disabled,
          'is-draggable': draggable,
          'is-block-node': blockNode,
          'Tree--auto-fill-height': autoFillHeight
        })}
        ref={this.root}
      >
        {(flattenedOptions && flattenedOptions.length) ||
        addBtn ||
        hideRoot === false ? (
          <ul className={cx('Tree-list')}>
            {hideRoot ? (
              <>
                {addBtn}
                {isAdding && !addingParent ? (
                  <li className={cx('Tree-item')}>{this.renderInput()}</li>
                ) : null}

                {this.renderList(flattenedOptions, value)}
              </>
            ) : (
              <li
                className={cx('Tree-rootItem', {
                  'is-checked': !value || !value.length,
                })}
              >
                <div className={cx('Tree-itemLabel')}>
                  <span
                    className={cx('Tree-itemText')}
                    onClick={this.clearSelect}
                  >
                    {showIcon ? (
                      <i className={cx('Tree-itemIcon Tree-rootIcon')}>
                        <Icon icon="home" className="icon" />
                      </i>
                    ) : null}
                    {rootLabel}
                  </span>
                  {!disabled &&
                  creatable &&
                  rootCreatable !== false &&
                  !isAdding &&
                  !isEditing ? (
                    <div className={cx('Tree-item-icons')}>
                      {creatable ? (
                        <a
                          onClick={this.handleAdd.bind(this, null)}
                          data-tooltip={rootCreateTip}
                          data-position="left"
                        >
                          <Icon icon="plus" className="icon" />
                        </a>
                      ) : null}
                    </div>
                  ) : null}
                </div>
                <ul className={cx('Tree-sublist')}>
                  {isAdding && !addingParent ? (
                    <li className={cx('Tree-item')}>{this.renderInput()}</li>
                  ) : null}
                  {this.renderList(flattenedOptions, value)}
                </ul>
              </li>
            )}
          </ul>
        ) : (
          <div className={cx('Tree-placeholder')}>{placeholder}</div>
        )}

        {dropIndicator && (
          <div className={cx('Tree-dropIndicator')} style={dropIndicator} />
        )}
      </div>
    );
  }
}

export default themeable(localeable(TreeSelector));
