import {localeable, LocaleProps, themeable, ThemeProps} from 'amis-core';
import React, {Component, createRef} from 'react';
import debouce from 'lodash/debounce';
import type {TooltipObject} from './TooltipWrapper';
import TooltipWrapper from './TooltipWrapper';

enum TooltipMode {
  auto = 'auto',
  always = 'always',
  none = 'none',
}

export interface TypographyEllipsisObject {
  rows?: number;
  width?: number;
  tooltipMode?: TooltipMode;
  tooltip?: string | TooltipObject;
}

interface TypographyProps
  extends ThemeProps,
    LocaleProps,
    React.DOMAttributes<HTMLElement> {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  ellipsis?: TypographyEllipsisObject;
}

interface TypographyState {
  isExceed: boolean;
}

class Typography extends Component<TypographyProps, TypographyState> {
  static defaultProps = {
    ellipsis: {
      tooltipMode: TooltipMode.auto,
    },
  };

  textRef = createRef<HTMLDivElement>();
  hideRef = createRef<HTMLDivElement>()

  lazyCheckExceed = debouce(this.checkExceed.bind(this), 250);

  constructor(props: TypographyProps) {
    super(props);
    this.state = {
      isExceed: false,
    };
  }

  componentDidMount() {
    this.checkExceed();
    window.addEventListener('resize', this.lazyCheckExceed);
  }

  componentWillUnmount() {
    window.removeEventListener('resize', this.lazyCheckExceed);
  }

  // 检测内容是否溢出
  checkExceed () {
    const textElement = this.textRef.current;
    if (!textElement) return;

    const childDiv = document.createElement('em');
    textElement.appendChild(childDiv);

    const rect = textElement.getBoundingClientRect();
    const childRect = childDiv.getBoundingClientRect();

    // Reset
    textElement.removeChild(childDiv);
    const isEllipsis = rect.left > childRect.left ||
      childRect.right > rect.right ||
      // Vertical out of range
      rect.top > childRect.top ||
      childRect.bottom > rect.bottom;

    if (isEllipsis !== this.state.isExceed) {
      this.setState({isExceed: isEllipsis});
    }
  };

  renderContent() {
    const {children, classnames: cx, className, style, ellipsis, onClick, onMouseEnter,onMouseLeave,} = this.props;
    const { rows, width } = ellipsis || {};

    const measureStyle: React.CSSProperties = {
      width,
      margin: 0,
      padding: 0,
    };

    const content = (
      <span
        ref={this.textRef}
        onClick={onClick}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        style={{...measureStyle, ...style, WebkitLineClamp: rows}}
        className={cx(
          'Typography',
          'Typography-common',
          'Typography-content',
          rows ? 'Typography-ellipsis-multiline' : 'Typography-ellipsis-singleline',
          className,
        )}
      >
        {children}
      </span>
    );

    return content;
  }

  render() {
    const {children, ellipsis, classnames: cx, className} = this.props;
    const {isExceed} = this.state;
    const {tooltip, tooltipMode, rows} = ellipsis || {};

    const content = typeof tooltip === 'string' ? tooltip : tooltip?.content;

    /**
     * 判断是否显示tooltip，采用分层判断逻辑：
     *
     * 第一层：判断content
     * - content为空字符串('')时，不展示tooltip（直接返回false）
     *
     * 第二层：根据tooltipMode判断
     * - tooltipMode为'none'时，永远不展示tooltip
     * - tooltipMode为'always'时，永远展示tooltip
     * - tooltipMode为'auto'时，根据内容是否超出(isExceed)决定是否展示
     */
    const shouldShowTooltip = () => {
      // 1. 如果content为空字符串，则不展示tooltip
      if (content === '') {
        return false;
      }

      // 2. 根据tooltipMode判断是否展示
      switch (tooltipMode) {
        case 'none':
          // none模式永远不展示
          return false;
        case 'always':
          // always模式永远展示
          return true;
        case 'auto':
        default:
          // auto模式下内容超出才展示
          return isExceed;
      }
    };

    // 调用函数确定是否展示tooltip
    const showTooltip = shouldShowTooltip();

    // tooltip配置的提示文本优先级最高
    const tooltipContent =
      content !== undefined
        ? content
        : children;

    // 优化点：只有需要显示tooltip时才创建TooltipWrapper
    if (showTooltip) {
      return (
        <TooltipWrapper
          tooltipClassName={cx('Typography-tooltip')}
          tooltip={{
            tooltipTheme: 'dark',
            disabled: !showTooltip,
            content: tooltipContent as string,
            ...(tooltip && typeof tooltip === 'object' ? tooltip : {}),
          }}
        >
          {this.renderContent()}
        </TooltipWrapper>
      );
    }

    return this.renderContent();
  }
}

export default themeable(localeable(Typography));
