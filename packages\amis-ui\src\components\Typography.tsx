import {localeable, LocaleProps, themeable, ThemeProps} from 'amis-core';
import React, {Component, createRef} from 'react';
import debouce from 'lodash/debounce';
import type {TooltipObject} from './TooltipWrapper';
import TooltipWrapper from './TooltipWrapper';

enum TooltipMode {
  auto = 'auto',
  always = 'always',
  none = 'none',
}

export interface TypographyEllipsisObject {
  rows?: number;
  width?: number;
  tooltipMode?: TooltipMode;
  tooltip?: string | TooltipObject;
}

interface TypographyProps
  extends ThemeProps,
    LocaleProps,
    React.DOMAttributes<HTMLElement> {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  ellipsis?: TypographyEllipsisObject;
}

interface TypographyState {
  isExceed: boolean;
}

class Typography extends Component<TypographyProps, TypographyState> {
  static defaultProps = {
    ellipsis: {
      tooltipMode: TooltipMode.auto,
    },
  };

  textRef = createRef<HTMLDivElement>();
  hideRef = createRef<HTMLDivElement>()

  lazyCheckExceed = debouce(this.checkExceed.bind(this), 250);

  constructor(props: TypographyProps) {
    super(props);
    this.state = {
      isExceed: false,
    };
  }

  componentDidMount() {
    this.checkExceed();
    window.addEventListener('resize', this.lazyCheckExceed);
  }

  componentWillUnmount() {
    window.removeEventListener('resize', this.lazyCheckExceed);
  }

  // 检测内容是否溢出
  checkExceed () {
    const { classnames: cx, className, ellipsis } = this.props;
    const { rows } = ellipsis || {};
    // 创建元素
    const element = this.textRef.current;
    
    if (element) {
      const hideElement = document.createElement('span');
      // 设置元素属性
      const classList = cx(
        'Typography',
        'Typography--hidden',
        'Typography-common',
        rows ? 'Typography-hidden-multiline' : 'Typography-hidden-singleline',
        className,
      ).split(' ');
      hideElement.innerHTML = element.innerHTML;
      hideElement.classList.add(...classList);
      element?.parentElement?.appendChild(hideElement)

      // 多行文本时，处理下有 padding 时等的情况下，宽度需要与原来的相等再进行计算
      if (this.props.ellipsis?.rows) {
        hideElement.style.width = element.offsetWidth + 'px';
      }

      const hideElementBound = hideElement.getBoundingClientRect();
      const elementBound = element.getBoundingClientRect();
      // const isExceed =
      // element.scrollHeight > element.clientHeight ||
      //   element.scrollWidth > element.clientWidth;
      const isExceed =
        +hideElementBound.height.toFixed(2) > +elementBound.height.toFixed(2) ||
        +hideElementBound.width.toFixed(2) > +elementBound.width.toFixed(2);

      this.setState({isExceed});
      // 计算结束位置之后移除元素
      element?.parentElement?.removeChild(hideElement);
    }
  };

  renderContent() {
    const {children, classnames: cx, className, style, ellipsis, onClick, onMouseEnter,onMouseLeave,} = this.props;
    const { rows, width } = ellipsis || {};

    const measureStyle: React.CSSProperties = {
      width,
      margin: 0,
      padding: 0,
    };

    const content = (
      <span
        ref={this.textRef}
        onClick={onClick}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        style={{...measureStyle, ...style, WebkitLineClamp: rows}}
        className={cx(
          'Typography',
          'Typography-common',
          'Typography-content',
          rows ? 'Typography-ellipsis-multiline' : 'Typography-ellipsis-singleline',
          className,
        )}
      >
        {children}
      </span>
    );

    return content;
  }

  render() {
    const {children, ellipsis, classnames: cx, className} = this.props;
    const {isExceed} = this.state;
    const {tooltip, tooltipMode, rows} = ellipsis || {};

    const content = typeof tooltip === 'string' ? tooltip : tooltip?.content;

    /**
     * 1. 自动模式下，如果内容溢出，则显示tooltip
     * 2. tooltip配置内容时始终显示tooltip
     */
    const showTooltip =
      tooltipMode === 'always' ||
      content !== undefined ||
      (tooltipMode !== 'none' && isExceed);

    // tooltip配置的提示文本优先级最高
    const tooltipContent =
      content !== undefined
        ? content
        : children;

    // 优化点：只有需要显示tooltip时才创建TooltipWrapper
    if (showTooltip) {
      return (
        <TooltipWrapper
          tooltipClassName={cx('Typography-tooltip')}
          tooltip={{
            tooltipTheme: 'dark',
            disabled: !showTooltip,
            content: tooltipContent as string,
            ...(tooltip && typeof tooltip === 'object' ? tooltip : {}),
          }}
        >
          {this.renderContent()}
        </TooltipWrapper>
      );
    }

    return this.renderContent();
  }
}

export default themeable(localeable(Typography));
