import React from 'react';
import {
  ConditionBuilderFields,
  ConditionBuilderFuncs,
  ICondiOpt,
} from './types';
import {
  ThemeProps,
  themeable,
  autobind,
  localeable,
  LocaleProps,
  guid,
  ConditionGroupValue,
  eachTree,
  createObject,
} from 'amis-core';
import Button from '../Button';
import GroupOrItem from './GroupOrItem';
import {changeType, ConditionBuilderConfig, ExtraChangeInfo} from './config';
import {FormulaPickerProps} from '../formula/Picker';
import Select from '../Select';
import GroupHeader from './GroupHeader';

import {DownArrowBoldIcon} from '../icons';
import { cloneDeep } from 'lodash';

interface ConditionGroupState {
  isCollapsed: boolean;
}

export interface ConditionGroupProps extends ThemeProps, LocaleProps {
  builderMode?: 'simple' | 'full';
  config: ConditionBuilderConfig;
  value?: ConditionGroupValue;
  fields: ConditionBuilderFields;
  funcs?: ConditionBuilderFuncs;
  showNot?: boolean;
  showANDOR?: boolean;
  showHeader?: boolean;
  data?: any;
  disabled?: boolean;
  searchable?: boolean;
  onChange: (
    value: ConditionGroupValue,
    extraChangeInfo?: ExtraChangeInfo,
  ) => void;
  removeable?: boolean;
  copyable?: boolean; // 控制拷贝组按钮，顶层组不需要拷贝组和删除组功能
  // hasConditionValidError: () => boolean;
  onRemove?: (e: React.MouseEvent) => void;
  onCopy?: (e: React.MouseEvent) => void;
  draggable?: boolean;
  isRoot?: boolean;
  onDragStart?: (e: React.MouseEvent) => void;
  fieldClassName?: string;
  formula?: FormulaPickerProps;
  popOverContainer?: any;
  renderEtrValue?: any;
  selectMode?: 'list' | 'tree';
  isCollapsed?: boolean; // 是否折叠
  rootCondiOptions?: Array<ICondiOpt>;
  leafCondiOptions?: Array<ICondiOpt>;
  conditionItemBody?: any;
  deepth?: number;
  maxLevel?: number;
  minLevel?: number;
  toolbarMode?: 'vertical' | 'horizontal';
  currLevel?: number; // 当前group所在的层级
  index?: number;
  itemAddable?: boolean;
  addable?: boolean;
  itemRemoveable?: boolean;
  itemCopyable?: boolean;
  resolveExpression?: (expression: string, data: any) => boolean;
  static?: boolean;
  verticalLineStyle?: boolean; // 横版使用竖版样式风格
}

export class ConditionGroup extends React.Component<
  ConditionGroupProps,
  ConditionGroupState
> {
  constructor(props: ConditionGroupProps) {
    super(props);

    this.state = {
      isCollapsed: false,
    };
  }

  componentDidUpdate(prevProps: Readonly<ConditionGroupProps>): void {
    // 上层折叠的时候，内层也折叠，主要是为了处理，子节点中，第一项也是Group的折叠场景
    if (prevProps.isCollapsed !== this.props.isCollapsed) {
      this.setState({
        isCollapsed: this.props.isCollapsed || false,
      });
    }
  }

  getValue() {
    const { toolbarMode } = this.props;
    if (toolbarMode === 'vertical') {
      return {
        id: guid(),
        conjunction: this.getDefaultCondi(),
        ...this.props.value
      } as ConditionGroupValue;
    }
    return {
      id: guid(),
      isCollapsed: false,
      ...this.props.value,
    } as ConditionGroupValue;
  }

  getDefaultCondi() {
    const {rootCondiOptions, leafCondiOptions, value, isRoot} = this.props;
    if (!isRoot) {
      return leafCondiOptions?.length ? leafCondiOptions[0].value : '';
    }
    // if (value?.conjunction !== undefined) {
    //   return leafCondiOptions?.length ? leafCondiOptions[0].value : '';
    // }
    // if (condiType === CondiTypeConfig.leaf) {
    //   return leafCondiOptions?.length ? leafCondiOptions[0].value : ''
    // }
    return rootCondiOptions?.length ? rootCondiOptions[0].value : '';
  }

  @autobind
  handleNotClick() {
    const onChange = this.props.onChange;
    let value = this.getValue();
    value.not = !value.not;

    onChange(value);
  }

  @autobind
  handleConjunctionChange(val: ICondiOpt, index: number) {
    const { toolbarMode = 'horizontal' } = this.props;
    if (toolbarMode === 'vertical') {
      return this.amisHandleConjunctionChange(val as any);
    }
    const onChange = this.props.onChange;
    let value = this.getValue();
    value.children = Array.isArray(value.children)
      ? value.children.concat()
      : [];
    const changedItem: ConditionGroupValue = value.children[index];
    changedItem.id = guid();
    changedItem.conjunction = val.value || this.getDefaultCondi();
    value.children.splice(index, 1, changedItem);
    onChange(value, {
      type: changeType.ChangeConjunction,
      changedItem,
      conjunction: val,
    });
  }

  @autobind
  amisHandleConjunctionChange(val: ICondiOpt) {
    const onChange = this.props.onChange;
    let value = this.getValue();
    value.conjunction = val.value || this.getDefaultCondi();
    onChange(value);
  }

  @autobind
  handleAdd() {
    // if (this.props?.conditionItemBody && this.hasConditionValidError()) {
    //   return;
    // }
    const onChange = this.props.onChange;
    let value = this.getValue();

    value.children = Array.isArray(value.children)
      ? value.children.concat()
      : [];

    const { toolbarMode } = this.props;
    let newItem = {
      id: guid(),
      conjunction: this.getDefaultCondi(),
    };

    if (toolbarMode === 'vertical') {
      newItem = {
        id: guid()
      } as any
    }


    value.children.push(newItem);
    onChange(value, {
      type: changeType.Add,
      addedItem: newItem,
    });
  }

  // 去掉校验
  // @autobind
  // hasConditionValidError() {
  //   const { hasConditionValidError } = this.props;
  //   if (typeof hasConditionValidError === "function") {
  //     return hasConditionValidError();
  //   }
  //   /*
  //     初始化场景，value是undefined
  //   */
  //   const value = this.props.value;
  //   if (value === undefined) {
  //     return false;
  //   }
  //   /*
  //     需要过滤掉组数据，所有的组都有children字段，只有具体条件才会被校验
  //   */
  //   const allNodes = flattenTree([value])?.filter((item) => !item.children);
  //   if (allNodes.some((condition) => condition.valid === false)) {
  //     return true
  //   }

  //   return false;
  // }

  @autobind
  handleAddGroup() {
    // if (this.props?.conditionItemBody && this.hasConditionValidError()) {
    //   return;
    // }
    const onChange = this.props.onChange;
    let value = this.getValue();

    value.children = Array.isArray(value.children)
      ? value.children.concat()
      : [];
    const { toolbarMode, leafCondiOptions } = this.props;
    const len = value.children.length;
    let newGroup = {
      id: guid(),
      conjunction: len === 0 ? '' : this.getDefaultCondi(),
      isCollapsed: false,
      children: [
        {
          id: guid(),
          conjunction: '',
        },
      ],
    };

    if (toolbarMode === 'vertical') {
      newGroup = {
        id: guid(),
        conjunction: leafCondiOptions?.length ? leafCondiOptions[0].value : '',
        children: [
          {
            id: guid()
          } as any
        ]
      } as any
    }
    value.children.push(newGroup);
    onChange(value, {
      type: changeType.Add,
      addedItem: newGroup,
    });
  }

  @autobind
  handleGroupHeaderChange(val: ConditionGroupValue) {
    const onChange = this.props.onChange;
    let value = this.getValue();
    onChange({
      ...value,
      ...val,
    });
  }

  @autobind
  handleItemChange(
    item: any,
    index: number,
    extraChangeInfo?: ExtraChangeInfo,
  ) {
    const onChange = this.props.onChange;
    let value = this.getValue();

    value.children = Array.isArray(value.children)
      ? value.children.concat()
      : [];

    value.children.splice(index!, 1, item);
    onChange(value, extraChangeInfo);
  }

  @autobind
  handleItemRemove(index: number) {
    const onChange = this.props.onChange;
    let value = this.getValue();
    value.children = Array.isArray(value.children)
      ? value.children.concat()
      : [];

    value.children.splice(index, 1);
    onChange(value);
  }

  @autobind
  handleItemCopy(index: number) {
    // if (this.props?.conditionItemBody && this.hasConditionValidError()) {
    //   return;
    // }
    const onChange = this.props.onChange;
    let value = this.getValue();
    value.children = Array.isArray(value.children)
      ? value.children.concat()
      : [];

    // 深拷贝，解决id重复导致数据乱掉问题
    const insertItem = cloneDeep(value.children[index]);
    eachTree([insertItem], (item, index) => {
      item.id = guid();
    })
    value.children.splice(index, 0, insertItem);
    onChange(value);
  }

  @autobind
  toggleCollapse(item?: ConditionGroupValue, index?: number) {
    const { toolbarMode, verticalLineStyle } = this.props;
    if (toolbarMode === 'vertical' || verticalLineStyle) {
      return this.amisToggleCollapse()
    }
    if (item === undefined || index === undefined) {
      return;
    }
    let value = this.getValue();
    const onChange = this.props.onChange;
    value.children = Array.isArray(value.children)
      ? value.children.concat()
      : [];
    item.id = guid();
    item.isCollapsed = !item.isCollapsed;
    value.children.splice(index, 1, item);
    onChange(value);
  }

  @autobind
  amisToggleCollapse() {
    this.setState(state => {
      return {
        isCollapsed: !state.isCollapsed
      }
    })
  }

  // 横版模式下的竖线风格
  getVerticalStyleConditionToolbar(item: ConditionGroupValue, index: number, rootClass: string) {
    const {
      classnames: cx,
      onDragStart,
      translate: __,
      draggable,
      isRoot,
    } = this.props;
    const {isCollapsed } = item || {};
    return (
      <div
        className={cx(`${rootClass}-toolbarCondition`, `${rootClass}-toolbarCondition-verticalLineStyle`)}
        draggable={!isRoot && draggable} // 第一层条件组，不支持拖拽
        onDragStart={onDragStart}
      >
        {Array.isArray(item?.children) && item!.children.length > 1 ? (
          <div
            className={cx(`${rootClass}-toolbarCondition-arrow`, {
              'is-collapse': isCollapsed,
            })}
            onClick={() => this.toggleCollapse(item, index)}
          >
            <DownArrowBoldIcon />
          </div>
        ) : null}
        <div className={cx(`${rootClass}-toolbarCondition-group`)}>
          <span>{__('Condition.group')}</span>
        </div>
      </div>
    );
  }

  getConditionToolbar(item: ConditionGroupValue, index: number, rootClass: string) {
    const {
      classnames: cx,
      value,
      onDragStart,
      translate: __,
      draggable,
      isRoot,
      disabled,
      showNot,
      rootCondiOptions = [],
      leafCondiOptions = [],
      static: isStatic,
      renderEtrValue,
    } = this.props;
    const {isCollapsed} = item || {};
    return (
      <div
        className={cx(`${rootClass}-toolbarCondition`)}
        draggable={!isRoot && draggable} // 第一层条件组，不支持拖拽
        onDragStart={onDragStart}
      >
        {Array.isArray(item?.children) && item!.children.length > 1 ? (
          <div
            className={cx(`${rootClass}-toolbarCondition-arrow`, {
              'is-collapse': isCollapsed,
            })}
            onClick={() => this.toggleCollapse(item, index)}
          >
            <DownArrowBoldIcon />
          </div>
        ) : null}
        {showNot ? (
          <Button
            onClick={this.handleNotClick}
            className="m-b-sm z-10"
            size="xs"
            active={value?.not}
            disabled={disabled}
          >
            {__('Condition.not')}
          </Button>
        ) : null}
        {index > 0 ? (
          isStatic ?
            (<>{renderEtrValue({
              type: 'select',
              static: true,
              name: 'conjunction',
              options: isRoot
                ? rootCondiOptions
                : leafCondiOptions,
              value: item?.conjunction || this.getDefaultCondi(),
            }, {})}</>)
            : (
            <Select
              options={
                isRoot
                  ? rootCondiOptions
                  : leafCondiOptions
              }
              value={item?.conjunction || this.getDefaultCondi()}
              disabled={disabled}
              onChange={(val: ICondiOpt) =>
                this.handleConjunctionChange(val, index)
              }
              clearable={false}
            />)
        ) : null}
      </div>
    );
  }

  render() {
    const {
      builderMode,
      classnames: cx,
      fieldClassName,
      value,
      data,
      fields,
      funcs,
      config,
      removeable = true,
      copyable = true,
      onRemove,
      onCopy,
      onDragStart,
      showNot,
      showANDOR = false,
      showHeader = false,
      rootCondiOptions,
      leafCondiOptions,
      disabled,
      searchable,
      translate: __,
      formula,
      popOverContainer,
      selectMode,
      renderEtrValue,
      draggable,
      isRoot, // 不能透传给子条件组
      deepth,
      minLevel,
      maxLevel,
      toolbarMode = 'horizontal',
      currLevel,
      itemAddable = true,
      addable = true,
      resolveExpression,
      ...rest
    } = this.props;
    const childLen = value?.children?.length || 0;
    const { isCollapsed } = this.state;

    const outConditionToolbar = toolbarMode === 'vertical';
    const rootClass = outConditionToolbar ? 'CBGroup-vertical' : 'CBGroup';
    let body =
      Array.isArray(value?.children) && value!.children.length
        ? value?.isCollapsed
          ? value!.children.slice(0, 1)
          : value!.children
        : null;
    if (outConditionToolbar || rest.verticalLineStyle) {
      body =
        Array.isArray(value?.children) && value!.children.length
          ? isCollapsed
            ? value!.children.slice(0, 1)
            : value!.children
          : null;
    }

    const expressionData = createObject(data, { ...value, currLevel, index: rest.index });
    const itemAddableBtn = resolveExpression?.('itemAddable', expressionData) ?? itemAddable;
    const addableBtn = resolveExpression?.('addable', expressionData) ?? addable;
    const removeableBtn = resolveExpression?.('removeable', expressionData) ?? removeable;
    const copyableBtn = resolveExpression?.('copyable', expressionData) ?? copyable;

    return (
      <div className={cx(rootClass, { 'CBGroup-verticalLineStyle': rest.verticalLineStyle })} data-group-id={value?.id}>
        {/* 竖版模式 */}
        {
          builderMode === 'simple' && showANDOR === false ? null : outConditionToolbar ? this.getConditionToolbar(value as any, 1, rootClass) : null
        }
        {/* 横版模式下，竖线风格的样式 */}
        {
          builderMode === 'simple' && showANDOR === false ? null : rest.verticalLineStyle ? this.getVerticalStyleConditionToolbar(value as any, 1, rootClass) : null
        }
        {!outConditionToolbar && showHeader && (
          <GroupHeader value={value} onChange={this.handleGroupHeaderChange} />
        )}
        <div className={cx(`${rootClass}-body-wrapper`)}>
          <div className={cx(`${rootClass}-body`)}>
            {body ? (
              body.map((item, index) => (
                <div
                  key={item.id}
                  data-id={item.id}
                >
                  {/* 横版模式 */}
                  {builderMode === 'simple' && showANDOR === false
                    ? null
                    : outConditionToolbar ? null : this.getConditionToolbar(item, index, rootClass)}
                  <GroupOrItem
                    {...rest}
                    removeable={removeable}
                    copyable={copyable}
                    itemAddable={itemAddable}
                    addable={addable}
                    draggable={draggable && value!.children!.length > 1}
                    onDragStart={onDragStart}
                    config={config}
                    fields={fields}
                    fieldClassName={fieldClassName}
                    value={item as ConditionGroupValue}
                    levelSize={body.length}
                    index={index}
                    onChange={this.handleItemChange}
                    funcs={funcs}
                    onRemove={this.handleItemRemove}
                    onCopy={this.handleItemCopy}
                    data={data}
                    disabled={disabled}
                    searchable={searchable}
                    showHeader={showHeader}
                    rootCondiOptions={rootCondiOptions}
                    leafCondiOptions={leafCondiOptions}
                    minLevel={minLevel}
                    maxLevel={maxLevel}
                    showNot={showNot}
                    builderMode={builderMode}
                    formula={formula}
                    popOverContainer={popOverContainer}
                    renderEtrValue={renderEtrValue}
                    selectMode={selectMode}
                    isCollapsed={isCollapsed}
                    // hasConditionValidError={this.hasConditionValidError}
                    toolbarMode={toolbarMode}
                    currLevel={currLevel}
                    resolveExpression={resolveExpression}
                  />
                </div>
              ))
            ) : (
              <div
                className={cx(
                  `CBGroup-placeholder ${
                    builderMode === 'simple' ? 'simple' : ''
                  }`,
                )}
              >
                {__('Condition.blank')}
              </div>
            )}
            {/* {isCollapsed ? (
              <div className={cx('CBGroup-body-collapse')}>
                <span onClick={() => this.toggleCollapse()}>
                  {__('Condition.collapse')} <DownArrowBoldIcon />
                </span>
              </div>
            ) : null} */}
          </div>

          {rest.static || isCollapsed || disabled ? null : (
            <div
              className={cx('CBGroup-toolbar')}
              draggable={draggable}
              onDragStart={onDragStart}
            >
              <div
                className={cx(
                  `CBGroup-toolbarConditionAdd${
                    builderMode === 'simple' ? '-simple' : ''
                  }`,
                )}
              >
                <div className={cx('ButtonGroup')}>
                  {itemAddableBtn && (!maxLevel || childLen < maxLevel) ? (
                    <Button
                      level="link"
                      onClick={this.handleAdd}
                      size="xs"
                      disabled={disabled}
                    >
                      {__('Condition.add_cond')}
                    </Button>
                  ) : null}
                  {builderMode !== 'simple' && addableBtn &&
                  (!maxLevel || childLen < maxLevel) ? (
                    <Button
                      onClick={this.handleAddGroup}
                      size="xs"
                      disabled={disabled}
                      level="link"
                    >
                      {__('Condition.add_cond_group')}
                    </Button>
                  ) : null}
                  {(currLevel as number) > 1 && removeableBtn && (!maxLevel || childLen < maxLevel) ? (
                    <Button
                      onClick={onRemove}
                      size="xs"
                      disabled={disabled}
                      level="link"
                    >
                      {__('Condition.delete_cond_group')}
                    </Button>
                  ) : null}
                  {(currLevel as number) > 1 && copyableBtn && (!maxLevel || childLen < maxLevel) ? (
                    <Button
                      onClick={onCopy}
                      size="xs"
                      disabled={disabled}
                      level="link"
                    >
                      {__('Condition.copy_cond_group')}
                    </Button>
                  ) : null}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }
}

export default themeable(localeable(ConditionGroup));
