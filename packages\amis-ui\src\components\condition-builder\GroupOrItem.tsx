import {ConditionBuilderConfig, ExtraChangeInfo} from './config';
import {
  ConditionBuilderFields,
  ConditionBuilderFuncs,
  ICondiOpt,
} from './types';
import {ThemeProps, themeable, localeable, autobind, createObject} from 'amis-core';
import React from 'react';
import {Icon} from '../icons';
import ConditionGroup from './Group';
import ConditionItem from './Item';
import {FormulaPickerProps} from '../formula/Picker';
import Button from '../Button';
import type {ConditionGroupValue, ConditionValue, LocaleProps} from 'amis-core';

export interface CBGroupOrItemProps extends ThemeProps, LocaleProps {
  builderMode?: 'simple' | 'full';
  config: ConditionBuilderConfig;
  value?: ConditionGroupValue;
  fields: ConditionBuilderFields;
  funcs?: ConditionBuilderFuncs;
  index: number;
  data?: any;
  draggable?: boolean;
  disabled?: boolean;
  searchable?: boolean;
  showHeader?: boolean;
  showNot?: boolean;
  onChange: (
    value: ConditionGroupValue,
    index: number,
    extraChangeInfo?: ExtraChangeInfo,
  ) => void;
  removeable?: boolean;
  copyable?: boolean;
  onDragStart?: (e: React.MouseEvent) => void;
  onRemove?: (index: number) => void;
  onCopy?: (index: number) => void;
  // hasConditionValidError: () => boolean;
  fieldClassName?: string;
  formula?: FormulaPickerProps;
  popOverContainer?: any;
  renderEtrValue?: any;
  selectMode?: 'list' | 'tree';
  isCollapsed?: boolean;
  rootCondiOptions?: Array<ICondiOpt>;
  leafCondiOptions?: Array<ICondiOpt>;
  conditionItemBody: any;
  deepth?: number;
  maxLevel?: number;
  minLevel?: number;
  levelSize?: number;
  toolbarMode?: 'vertical' | 'horizontal';
  currLevel?: number;
  itemAddable?: boolean;
  addable?: boolean;
  itemRemoveable?: boolean;
  itemCopyable?: boolean;
  resolveExpression?: (expression: string, data: any) => boolean;
  static?: boolean;
}

export class CBGroupOrItem extends React.Component<CBGroupOrItemProps> {
  state = {
    hover: false,
  };
  @autobind
  handleItemChange(value: any) {
    this.props.onChange(value, this.props.index);
  }

  handleGroupItemChange = (
    value: ConditionGroupValue,
    extraChangeInfo?: ExtraChangeInfo,
  ) => {
    this.props.onChange(value, this.props.index, extraChangeInfo);
  };

  @autobind
  handleItemRemove() {
    this.props.onRemove?.(this.props.index);
  }

  @autobind
  handleItemCopy() {
    // const { hasConditionValidError } = this.props;
    // if (this.props?.conditionItemBody && hasConditionValidError()) {
    //   return;
    // }
    this.props.onCopy?.(this.props.index);
  }

  @autobind
  handlerHoverIn(e: any) {
    e.stopPropagation();
    this.setState({
      hover: true,
    });
  }

  @autobind
  handlerHoverOut(e: any) {
    this.setState({
      hover: false,
    });
  }

  render() {
    const {
      builderMode,
      classnames: cx,
      fieldClassName,
      value,
      config,
      fields,
      funcs,
      draggable,
      data,
      disabled,
      searchable,
      showHeader = false,
      onDragStart,
      formula,
      popOverContainer,
      selectMode,
      renderEtrValue,
      isCollapsed,
      rootCondiOptions,
      leafCondiOptions,
      deepth,
      maxLevel,
      minLevel,
      levelSize = 0,
      removeable,
      toolbarMode = 'horizontal',
      showNot,
      currLevel,
      itemRemoveable = true,
      itemCopyable = true,
      resolveExpression,
      static: isStatic,
      // hasConditionValidError,
      ...rest
    } = this.props;

    const expressionData = createObject(data, { ...value, currLevel, index: rest.index });
    const itemRemoveableBtn = resolveExpression?.('itemRemoveable', expressionData) ?? itemRemoveable;
    const itemCopyableBtn = resolveExpression?.('itemCopyable', expressionData) ?? itemCopyable;

    return (
      <div
        className={cx(
          `CBGroupOrItem${builderMode === 'simple' ? '-simple' : ''}`,
        )}
      >
        <div className={cx('CBGroupOrItem-body')}>
          {value?.children ? (
            <div
              className={cx('CBGroupOrItem-body-group', {
                'is-hover': isStatic ? false : this.state.hover, // static 模式下没有hover效果
              })}
              onMouseOver={this.handlerHoverIn}
              onMouseOut={this.handlerHoverOut}
            >
              {!isStatic && draggable && !disabled ? (
                <a
                  draggable
                  onDragStart={onDragStart}
                  className={cx('CBGroupOrItem-dragbar')}
                >
                  <Icon icon="drag-bar" className="icon" />
                </a>
              ) : null}
              <ConditionGroup
                {...rest}
                itemRemoveable={itemRemoveable}
                itemCopyable={itemCopyable}
                isCollapsed={isCollapsed}
                draggable={draggable}
                disabled={disabled}
                searchable={searchable}
                showHeader={showHeader}
                rootCondiOptions={rootCondiOptions}
                leafCondiOptions={leafCondiOptions}
                minLevel={minLevel}
                maxLevel={maxLevel}
                onDragStart={onDragStart}
                config={config}
                fields={fields}
                value={value as ConditionGroupValue}
                onChange={this.handleGroupItemChange}
                fieldClassName={fieldClassName}
                funcs={funcs}
                removeable={removeable}
                onRemove={this.handleItemRemove}
                onCopy={this.handleItemCopy}
                data={data}
                renderEtrValue={renderEtrValue}
                toolbarMode={toolbarMode}
                currLevel={(currLevel as number) + 1}
                resolveExpression={resolveExpression}
                static={isStatic}
                // hasConditionValidError={hasConditionValidError}
              />
            </div>
          ) : (
            <div className={cx('CBGroupOrItem-body-item')}>
              {!isStatic && draggable && !disabled ? (
                <a
                  draggable
                  onDragStart={onDragStart}
                  className={cx('CBGroupOrItem-dragbar')}
                >
                  <Icon icon="drag-bar" className="icon" />
                </a>
              ) : null}
              <ConditionItem
                {...rest}
                disabled={disabled}
                searchable={searchable}
                config={config}
                fields={fields}
                value={value as ConditionValue}
                onChange={this.handleItemChange}
                fieldClassName={fieldClassName}
                funcs={funcs}
                data={data}
                formula={formula}
                popOverContainer={popOverContainer}
                renderEtrValue={renderEtrValue}
                selectMode={selectMode}
                currLevel={currLevel as number}
                resolveExpression={resolveExpression}
                static={isStatic}
              />

              {/* 添加静态模式，不展示按钮 */}
              {!isStatic && !disabled && itemRemoveableBtn &&
                (!minLevel ||
                  (minLevel &&
                    !Number.isNaN(minLevel) &&
                    levelSize > minLevel)) && (
                  <Button
                    className={cx('CBDelete')}
                    onClick={this.handleItemRemove}
                    disabled={disabled}
                    level="link"
                  >
                    <Icon icon="remove" className="icon" />
                  </Button>
                )}
              {!isStatic && !disabled && itemCopyableBtn &&
                (!maxLevel ||
                  (maxLevel &&
                    !Number.isNaN(maxLevel) &&
                    levelSize < maxLevel)) && (
                  <Button
                    className={cx('CBDelete')}
                    onClick={this.handleItemCopy}
                    disabled={disabled}
                    level="link"
                  >
                    <Icon icon="copy" className="icon" />
                  </Button>
                )}
            </div>
          )}
        </div>
      </div>
    );
  }
}

export default themeable(localeable(CBGroupOrItem));
