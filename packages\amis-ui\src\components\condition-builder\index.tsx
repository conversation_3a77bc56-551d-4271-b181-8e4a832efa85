import React from 'react';
import {
  ThemeProps,
  themeable,
  LocaleProps,
  localeable,
  autobind,
  animation,
  findTreeIndex,
  getTree,
  spliceTree,
  mapTree,
  guid,
  noop,
} from 'amis-core';
import {uncontrollable} from 'amis-core';
import {
  ConditionBuilderFields,
  ConditionBuilderFuncs,
  ICondiOpt,
} from './types';
import ConditionGroup from './Group';
import defaultConfig, {
  ConditionBuilderConfig,
  ExtraChangeInfo,
  changeType,
} from './config';
import {FormulaPickerProps} from '../formula/Picker';
import PickerContainer from '../PickerContainer';
import ResultBox from '../ResultBox';
import type { ConditionGroupValue, ConditionRule } from 'amis-core';
import { isEqual } from 'lodash';

export interface ConditionBuilderProps extends ThemeProps, LocaleProps {
  builderMode?: 'simple' | 'full'; // 简单模式｜完整模式
  embed?: boolean;
  pickerIcon?: JSX.Element;
  placeholder?: string;
  title?: string;
  fields: ConditionBuilderFields;
  funcs?: ConditionBuilderFuncs;
  showNot?: boolean;
  showANDOR?: boolean;
  showHeader?: boolean;
  value?: ConditionGroupValue;
  data?: any;
  onChange: (value?: ConditionGroupValue) => void;
  onConjunctionChange?: (
    conjunction: ICondiOpt,
    changedItem: ConditionRule | ConditionGroupValue,
    value: ConditionGroupValue,
  ) => void;
  onAdd?: (
    addedItem: ConditionRule | ConditionGroupValue,
    value: ConditionGroupValue,
  ) => void;
  config?: ConditionBuilderConfig;
  disabled?: boolean;
  draggable?: boolean;
  searchable?: boolean;
  fieldClassName?: string;
  formula?: FormulaPickerProps;
  popOverContainer?: any;
  renderEtrValue?: any;
  selectMode?: 'list' | 'tree';
  rootCondiOptions?: Array<ICondiOpt>;
  leafCondiOptions?: Array<ICondiOpt>;
  conditionItemBody?: any;
  deepth?: number;
  maxLevel?: number;
  minLevel?: number;
  toolbarMode?: 'vertical' | 'horizontal',
  resolveExpression?: (expression: string, data: any) => boolean;
  static?: boolean;
}

export interface ConditionBuilderState {
  normalizedValue: ConditionGroupValue;
}

export class QueryBuilder extends React.Component<
  ConditionBuilderProps,
  ConditionBuilderState
> {
  config = {...defaultConfig, ...this.props.config};

  dragTarget?: HTMLElement;
  // dragNextSibling: Element | null;
  ghost?: HTMLElement;
  host: HTMLElement;
  lastX: number;
  lastY: number;
  lastMoveAt: number = 0;

  state: ConditionBuilderState = {
    normalizedValue: Array.isArray(this.props?.value?.children)
      ? {
          ...this.props?.value,
          children: mapTree(this.props?.value!.children, (value: any) => {
            if (value.id) {
              return value;
            }

            return {
              ...value,
              id: guid(),
            };
          }),
        }
      : this.props?.value as ConditionGroupValue
  }

  UNSAFE_componentWillReceiveProps(nextProps: Readonly<ConditionBuilderProps>) {
    const { value } = nextProps;
    const prevValue = this.props.value;

    // #1172 避免编辑时因为没有id导致每次渲染都重新生成id，导致组件销毁重建
    if (!isEqual(value, prevValue) || !this.state.normalizedValue) {
      const normalizedValue = Array.isArray(value?.children)
        ? {
            ...value,
            children: mapTree(value!.children, (value: any) => {
              if (value.id) {
                return value;
              }

              return {
                ...value,
                id: guid(),
              };
            }),
        }
        : value as ConditionGroupValue;
      this.setState({ normalizedValue })
    }
  }

  @autobind
  handleDragStart(e: React.DragEvent) {
    const { draggable, static: isStatic } = this.props;
    const canDrag = draggable && !isStatic

    if(!canDrag) return;

    const target = e.currentTarget;
    const item = target.closest('[data-id]') as HTMLElement;
    this.dragTarget = item;
    // this.dragNextSibling = item.nextElementSibling;
    this.host = item.closest('[data-group-id]') as HTMLElement;

    const ghost = item.cloneNode(true) as HTMLElement;
    ghost.classList.add('is-ghost');
    this.ghost = ghost;

    e.dataTransfer.setDragImage(item as HTMLElement, 0, 0);

    target.addEventListener('dragend', this.handleDragEnd);
    document.body.addEventListener('dragover', this.handleDragOver);
    document.body.addEventListener('drop', this.handleDragDrop);
    this.lastX = e.clientX;
    this.lastY = e.clientY;

    // 应该是 chrome 的一个bug，如果你马上修改，会马上执行 dragend
    setTimeout(() => {
      item.classList.add('is-dragging');
      // item.parentElement!.insertBefore(
      //   item,
      //   item.parentElement!.firstElementChild
      // ); // 挪到第一个，主要是因为样式问题。
    }, 5);
  }

  @autobind
  handleDragOver(e: DragEvent) {
    e.preventDefault();
    const item = (e.target as HTMLElement).closest('[data-id]') as HTMLElement;

    const dx = e.clientX - this.lastX;
    const dy = e.clientY - this.lastY;
    const d = Math.max(Math.abs(dx), Math.abs(dy));
    const now = Date.now();

    // 没移动还是不要处理，免得晃动个不停。
    if (d < 5) {
      if (this.lastMoveAt === 0) {
      } else if (now - this.lastMoveAt > 500) {
        const host = (e.target as HTMLElement).closest(
          '[data-group-id]',
        ) as HTMLElement;

        if (host) {
          this.host = host;
          this.lastMoveAt = now;
          this.lastX = 0;
          this.lastY = 0;
          this.handleDragOver(e);
          return;
        }
      }
      return;
    }

    this.lastMoveAt = now;
    this.lastX = e.clientX;
    this.lastY = e.clientY;

    if (
      !item ||
      item.classList.contains('is-ghost') ||
      item.closest('[data-group-id]') !== this.host
    ) {
      return;
    }

    const container = item.parentElement!;
    const children = [].slice.apply(container!.children);

    const idx = children.indexOf(item);

    if (this.ghost!.parentElement !== container) {
      container.appendChild(this.ghost!);
    }

    const rect = item.getBoundingClientRect();
    const isAfter = dy > 0 && e.clientY > rect.top + rect.height / 2;
    const gIdx = isAfter ? idx : idx - 1;
    const cgIdx = children.indexOf(this.ghost);

    if (gIdx !== cgIdx) {
      animation.capture(container);

      if (gIdx === children.length - 1) {
        container.appendChild(this.ghost!);
      } else {
        container.insertBefore(this.ghost!, children[gIdx + 1]);
      }

      animation.animateAll();
    }
  }

  @autobind
  handleDragDrop() {
    const onChange = this.props.onChange;
    const fromId = this.dragTarget!.getAttribute('data-id')!;
    const toId = this.host.getAttribute('data-group-id')!;
    const children = [].slice.call(this.ghost!.parentElement!.children);
    const idx = children.indexOf(this.dragTarget);

    if (~idx) {
      children.splice(idx, 1);
    }

    // 获取目标位置索引
    const toIndex = children.indexOf(this.ghost);
    let value = this.props.value!;

    // 查找原始数据位置
    const indexes = findTreeIndex([value], item => item.id === fromId);

    if (indexes) {
      const origin = getTree([value], indexes.concat())!;
      // 先删除原始数据
      [value] = spliceTree([value]!, indexes, 1);

      // 根据 groupId 查找目标位置所在组
      const indexes2 = findTreeIndex([value], item => item.id === toId);

      if (indexes2) {
        // 将原始数据插入到目标位置
        [value] = spliceTree([value]!, indexes2.concat(toIndex), 0, origin);
        onChange(value);
      }
    }
  }

  @autobind
  handleDragEnd(e: Event) {
    const target = e.target as HTMLElement;

    target.removeEventListener('dragend', this.handleDragEnd);
    document.body.removeEventListener('dragover', this.handleDragOver);
    document.body.removeEventListener('drop', this.handleDragDrop);

    this.dragTarget!.classList.remove('is-dragging');
    // if (this.dragNextSibling) {
    //   this.dragTarget.parentElement!.insertBefore(
    //     this.dragTarget,
    //     this.dragNextSibling
    //   );
    // } else {
    //   this.dragTarget.parentElement!.appendChild(this.dragTarget);
    // }
    delete this.dragTarget;
    // delete this.dragNextSibling;
    this.ghost!.parentElement?.removeChild(this.ghost!);
    delete this.ghost;
  }

  @autobind
  handleClear() {
    this.props.onChange();
  }

  @autobind
  highlightValue(value: ConditionGroupValue) {
    const {classnames: cx, translate: __} = this.props;
    const html = {
      __html: `<span class="label label-info">${__(
        'Condition.configured',
      )}</span>`,
    };

    return (
      <div className={cx('CPGroup-result')} dangerouslySetInnerHTML={html} />
    );
  }

  getDefaultCondiOpt() {
    const {translate: __} = this.props;
    const defaultCondiOpt = [
      {
        label: __('Condition.and'),
        value: 'and',
      },
      {
        label: __('Condition.or'),
        value: 'or',
      },
    ];
    return defaultCondiOpt;
  }

  renderBody(
    onChange: (value: ConditionGroupValue) => void,
    value?: ConditionGroupValue,
    popOverContainer?: any,
  ) {
    const {
      classnames: cx,
      fieldClassName,
      fields,
      funcs,
      showNot,
      showANDOR,
      showHeader,
      data,
      disabled,
      searchable,
      builderMode,
      formula,
      renderEtrValue,
      selectMode,
      draggable,
      rootCondiOptions = this.getDefaultCondiOpt(),
      leafCondiOptions = this.getDefaultCondiOpt(),
      onConjunctionChange,
      onAdd,
      toolbarMode = 'horizontal',
      ...rest
    } = this.props;

    const onFinalChange = (
      value: ConditionGroupValue,
      extraChangeInfo?: ExtraChangeInfo,
    ) => {
      onChange(value);

      if (
        extraChangeInfo?.type === changeType.ChangeConjunction &&
        onConjunctionChange
      ) {
        const {changedItem, conjunction} = extraChangeInfo;
        onConjunctionChange(conjunction, changedItem, value);
      } else if (extraChangeInfo?.type === changeType.Add && onAdd) {
        onAdd(extraChangeInfo.addedItem, value);
      }
    };

    const { normalizedValue } = this.state;

    return (
      <React.Fragment>
        <ConditionGroup
          {...rest}
          builderMode={builderMode}
          config={this.config}
          funcs={funcs || this.config.funcs}
          fields={fields || this.config.fields}
          value={normalizedValue as any}
          draggable={draggable && !rest.static}
          onChange={onFinalChange}
          classnames={cx}
          isRoot={true}
          fieldClassName={fieldClassName}
          // removeable={false} // 顶层组不需要删除组功能
          // copyable={false} // 顶层组不需要拷贝组功能
          onDragStart={this.handleDragStart}
          showANDOR={showANDOR}
          showHeader={showHeader}
          rootCondiOptions={rootCondiOptions}
          leafCondiOptions={leafCondiOptions}
          showNot={showNot}
          data={data}
          disabled={disabled}
          searchable={searchable}
          formula={formula}
          renderEtrValue={renderEtrValue}
          popOverContainer={popOverContainer}
          selectMode={selectMode}
          toolbarMode={toolbarMode}
          currLevel={1}
          index={0}
        />
      </React.Fragment>
    );
  }

  render() {
    const {
      classnames: cx,
      placeholder,
      embed = true,
      pickerIcon,
      locale,
      translate,
      classPrefix,
      onChange: onFinalChange,
      value,
      title,
      disabled,
      popOverContainer,
    } = this.props;

    if (embed) {
      return this.renderBody(onFinalChange, value, popOverContainer);
    }

    return (
      <PickerContainer
        classnames={cx}
        classPrefix={classPrefix}
        translate={translate}
        locale={locale}
        onConfirm={onFinalChange}
        value={value}
        size={'md'}
        popOverContainer={popOverContainer}
        bodyRender={(params: {
          value: ConditionGroupValue;
          onChange: (value: ConditionGroupValue) => void;
        }) => this.renderBody(params.onChange, params.value)}
        title={title}
      >
        {({onClick, isOpened}) => (
          <ResultBox
            classnames={cx}
            classPrefix={classPrefix}
            translate={translate}
            locale={locale}
            className={cx('CBGroup-result', {'is-active': isOpened})}
            allowInput={false}
            clearable={true}
            result={value}
            itemRender={this.highlightValue}
            onResultChange={noop}
            onClear={this.handleClear}
            disabled={disabled}
            borderMode={'full'}
            placeholder={placeholder}
            actions={
              pickerIcon && (
                <span className={cx('CBPicker-trigger')} onClick={onClick}>
                  {pickerIcon}
                </span>
              )
            }
            onResultClick={pickerIcon ? undefined : onClick}
          ></ResultBox>
        )}
      </PickerContainer>
    );
  }
}

export default themeable(
  localeable(
    uncontrollable(QueryBuilder, {
      value: 'onChange',
    }),
  ),
);
