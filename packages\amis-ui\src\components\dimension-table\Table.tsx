import React, { useRef, useState, useEffect } from "react";
import { useSetState } from "ahooks";
import TableCell, { DimensionRow, DimensionSelectionArea, DimensionTd, TwoDimensionCellProps } from "./TableCell";

type TableProps = Omit<
  TwoDimensionCellProps,
  'td' | 'rowIndex' | 'columnIndex' | 'selectionAreaRef' |  'onSelectionAction' | 'tdReloadKey'
  > & {
    hoverMode?: 'row' | 'cross'
  }

const Table = (props: TableProps) => {
  const {table, cx, prefixCx, mergeable} = props;

  const [selection, setSelection] = useSetState({
    endX: 0,
    endY: 0,
    selectionKey: 0
  })
  const updateSelectionKey = () => {
    if (!selectionAreaRef.current.isSelecting) {
      setSelection({
        selectionKey: selection.selectionKey + 1
      })
    }
  }

  const tableRef = useRef<HTMLTableElement>(null)
  // 框选格子区域
  const selectionAreaRef = useRef<DimensionSelectionArea>({
    startX: 0,
    startY: 0,
    selectable: true,
    isSelecting: false, // 同步 isSelecting state (用于实时获取)
    sourceTd: undefined, // 选择时动态设置的 开始格子
    targetTd: undefined, // 选择时动态设置的 结束格子
  });

  const [isSelecting, _setSelecting] = useState(false)
  const setSelecting = (toggle: boolean) => {
    selectionAreaRef.current.isSelecting = toggle
    _setSelecting(toggle)
  }

  // 判断单元格区域是不是能被合并
  const checkMergeable = (td?: DimensionTd) => {
    if (!td) {
      return false
    }

    const result = td.isColumnHeader && mergeable.columnHeader ||
      td.isRowHeader && mergeable.rowHeader ||
      td.isCell && mergeable.cell

    return result
  }

  // 选项操作
  const handleSelectionAction = (options: {
    type: 'selection' | 'clear' | 'mergeSelection'
    needUpdate?: boolean
    lastTargetTd?: DimensionTd // 鼠标松开时的表格
  }) => {
    const { type, needUpdate = true } = options

    switch (type) {
      // 清空选项
      case 'clear': {
        table.current.forEachCell((td: DimensionTd) => {
          table.current.setTdSelected(td, false)
        });
        break;
      }

      // 选中指定区域
      case 'selection': {
        let { sourceTd, targetTd } = selectionAreaRef.current
        if (!targetTd || !sourceTd) {
          return
        }

        const validSelectedTds: DimensionTd[] = []

        // 开始结束都是相同的格子
        if (sourceTd === targetTd && !targetTd.isTitle) {
          table.current.setTdSelected(targetTd, true)
          if (checkMergeable(targetTd)) {
            validSelectedTds.push(targetTd)
          }
        }
        // 开始结束格子不相同
        if (sourceTd !== targetTd) {
          const tdRange = table.current.findAreaTd(
            sourceTd,
            targetTd
          )
          tdRange.forEach((td: DimensionTd) => {
            if (!td.isTitle && checkMergeable(targetTd)) {
              table.current.setTdSelected(td, true)
              if (td.isSelected) {
                validSelectedTds.push(td)
              }
            }
          })
        }

        /**
         * 对选中的格子进行处理，仅对相同区域的表格进行选中
         */
        if (validSelectedTds.length > 1) {
          let lastTargetTd = options.lastTargetTd || targetTd
          lastTargetTd = lastTargetTd?.isTitle
            ? validSelectedTds[0]
            : lastTargetTd
          validSelectedTds.forEach((td) => {
            if (!table.current.isSameTdType(td, lastTargetTd)) {
              table.current.setTdSelected(td, false)
            }
          })
        }

        break
      }

      // 多个选中区域进行合并选中
      case 'mergeSelection': {
        const { targetTd, sourceTd } = selectionAreaRef.current
        if (!targetTd || !sourceTd) {
          return
        }
        // 记录本次 鼠标停留位置
        const lastTargetTd = targetTd

        // 如果 开始/结束 都是相同格子，将当前格子设置为选中
        if (targetTd && sourceTd === targetTd) {
          table.current.setTdSelected(targetTd, true)
        }

        // 找到所有选择的格子
        const selectedTds: DimensionTd[] = []
        table.current.forEachCell((td: DimensionTd) => {
          if (td.isSelected) {
            selectedTds.push(td)
          }
        })

        // 存在多个选中的格子, 取相同区域的 第1个与最后一个 合并
        const sameTypeTds = selectedTds.filter((td) => {
          return table.current.isSameTdType(td, lastTargetTd)
        })
        // 更新计算后相同区域的 开始/结束 格子，用于被选中
        if (sameTypeTds.length > 1) {
          selectionAreaRef.current.sourceTd = sameTypeTds[0]
          selectionAreaRef.current.targetTd = sameTypeTds.slice(-1)[0]
        }

        // 先清空所有选中的格子
        handleSelectionAction({type: 'clear', needUpdate: false})
        // 再将更新选中内容
        handleSelectionAction({
          type: 'selection',
          lastTargetTd
        })

        // 将最后鼠标停留的格子，设置为下一次开始选择时的初始格子
        Object.assign(selectionAreaRef.current, {
          sourceTd: lastTargetTd,
          targetTd: lastTargetTd,
        })

        break
      }
    }

    needUpdate && updateSelectionKey()
  };

  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    /**
     * 1. 非鼠标左键 不处理
     * 2. 当前格子不允许合并 不处理
     */
    const isMergeable = checkMergeable(selectionAreaRef.current.sourceTd)
    selectionAreaRef.current.selectable = isMergeable
    if (
      e.button !== 0 ||
      !isMergeable
     ) {
      return
    }

    e.preventDefault();
    selectionAreaRef.current.startX = e.clientX
    selectionAreaRef.current.startY = e.clientY
    // @ts-ignore
    document.body.addEventListener("mousemove", handleMouseMove);
    // @ts-ignore
    document.body.addEventListener("mouseup", handleMouseUp);
  }

  const handleMouseMove = (e: React.MouseEvent<HTMLElement>) => {
    if (!selectionAreaRef.current.selectable) {
      return
    }

    const { isSelecting, startX, startY } = selectionAreaRef.current
    if (isSelecting) {
      setSelection({
        endX: e.clientX,
        endY: e.clientY,
      })
      return
    }

    // 超过一个最小移动距离只有，才能开始框选
    if ( Math.abs(e.clientX - startX) > 5 ||
       Math.abs(e.clientY - startY) > 5) {
      setSelecting(true)
    }
  }

  const handleMouseUp = (e: React.MouseEvent<HTMLElement>) => {
    if (!selectionAreaRef.current.selectable) {
      handleSelectionAction({type: 'clear'})
      return
    }

    e.preventDefault();
    // @ts-ignore
    document.body.removeEventListener("mousemove", handleMouseMove);
    // @ts-ignore
    document.body.removeEventListener("mouseup", handleMouseUp);

    if (!selectionAreaRef.current.isSelecting) {
      handleSelectionAction({type: 'clear'})
    } else  {
      handleSelectionAction({type: 'mergeSelection'})
    }

    setSelecting(false)
  }

  useEffect(() => {
    // hover 非十字交叉模式直接返回
    if (props.hoverMode !== 'cross') return;

    let table = tableRef.current as HTMLTableElement;
    let grid: Array<Array<any>> = []; // 用于存储表格网格（二维数组），每个位置存放对应的单元格

    if (!table) return;

    // 初始化grid数组：每行对应一个空数组
    for (let i = 0; i < table.rows.length; i++) {
      grid[i] = [];
    }

    // 遍历每一行，构建网格信息
    for (let i = 0; i < table.rows.length; i++) {
      let cells = table.rows[i].cells;
      let col = 0; // 当前行的列计数器
      for (let j = 0; j < cells.length; j++) {
        // 如果当前位置已有单元格占据（例如前面的 rowspan），则跳过
        while (grid[i][col]) {
          col++;
        }
        let cell = cells[j];
        // 获取 rowspan 和 colspan，默认为1
        let rowspan = cell.rowSpan || 1;
        let colspan = cell.colSpan || 1;
        // 将当前单元格的起始列号及跨列数保存到自定义属性上
        cell.dataset.colIndex = col;
        cell.dataset.colSpan = colspan;
        // 在grid数组中填入当前单元格的引用
        for (let r = i; r < i + rowspan; r++) {
          for (let c = col; c < col + colspan; c++) {
            grid[r][c] = cell;
          }
        }
        col += colspan;
      }
    }

    function handleOver(e: React.MouseEvent<HTMLTableElement>) {
      let td = (e.target as HTMLElement).closest("td") as HTMLElement;
      if (!td || td?.classList.contains('is-header')) return;
      // 清除之前的高亮效果
      removeHighlights();

      // 获取该单元格在视觉上的起始列及跨几列（对于大部分情况跨列为1）
      let colIndex = parseInt(td.dataset.colIndex);
      let colspan = parseInt(td.dataset.colSpan);

      // 遍历每一行，根据grid数组获取该列的单元格，并添加高亮样式
      for (let r = 0; r < grid.length; r++) {
        for (let c = colIndex; c < colIndex + colspan; c++) {
          let cell = grid[r][c];
          const isHeader = cell?.classList.contains('is-header');
          if (cell && !isHeader) {
            cell.classList.add("hover-column");
          }
        }
      }
    }

    function handleOut() {
      removeHighlights();
    }

    // 绑定鼠标进入和离开事件
    table.addEventListener("mouseover", handleOver);

    // 当鼠标离开单元格时，移除所有高亮
    table.addEventListener("mouseout", handleOut)

    // 移除table内所有的高亮样式
    function removeHighlights() {
      let highlighted = table.querySelectorAll(".hover-column");
      highlighted.forEach(function(cell) {
        cell.classList.remove("hover-column");
      });
    }

    return () => {
      table.removeEventListener("mouseover", handleOver);
      table.removeEventListener("mouseout", handleOut);
    }
  })

  return (
    <div
      className={cx('Table m-b-none', {
        'is-selecting': isSelecting
      })}
      onMouseDown={handleMouseDown}
    >
      <div
        className={prefixCx('SelectionMask')}
        style={{
          display: isSelecting ? 'block' : 'none',
          left: Math.min(selectionAreaRef.current.startX, selection.endX),
          top: Math.min(selectionAreaRef.current.startY, selection.endY),
          width: Math.abs(selection.endX - selectionAreaRef.current.startX),
          height: Math.abs(selection.endY - selectionAreaRef.current.startY),
        }}
      />
      <div className={cx('Table-content')}>
        <table
          ref={tableRef}
          className={cx('Table-table')}
        >
          <tbody>
            {table.current.forMapRow(({id, tds}: DimensionRow, rowIndex: number) => {
              return (
                <tr key={id}>
                  {tds.map((td: any, columnIndex: number) => {
                    return (
                      <TableCell
                        {...props}
                        selectionAreaRef={selectionAreaRef}
                        onSelectionAction={handleSelectionAction}
                        key={td.id}
                        tdReloadKey={td.reloadKey}
                        rowIndex={rowIndex}
                        columnIndex={columnIndex}
                        td={td}
                      />
                    );
                  })}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Table
