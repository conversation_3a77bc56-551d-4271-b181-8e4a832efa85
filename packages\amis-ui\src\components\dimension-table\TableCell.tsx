import React, {  useState, memo, useMemo, useEffect, useRef, MutableRefObject } from 'react'
import { anyChanged, shallowEqual } from 'amis-core'
import { getToolbarActions } from './getToolbarActions'

export type DimensionTableMode = 'basic' | 'standard' | 'merge'

export type DimensionTd = {
  id: string
  parentId?: string // 不存在parentId时，表示根节点
  isTitle: boolean // 标题
  isRowHeader: boolean // 行头
  isColumnHeader: boolean // 列头
  isCell: boolean // 单元格
  rowspan: number // 行合并
  colspan: number // 列合并
  data: any // 数据
  // 仅前端UI使用
  reloadKey?: number // 单元格刷新key,当key变化则会更新单元格
  isSelected?: boolean // 是否被选中（用于合并单元格）
  errMsg?: string // 错误文案（用于校验）
  width?: number // 单元格宽度（用于表头吸顶）
  height?: number // 单元格高度（用于表头吸顶）
}

export type DimensionRow = {
  id: string // 行id
  tds: DimensionTd[] // 单元格
}

export type DimensionSelectionArea = {
  startX: number
  startY: number
  selectable: boolean // 格子是否可被选中
  isSelecting: boolean // 同步
  sourceTd?: DimensionTd, // 选择时，
  targetTd?: DimensionTd// 合并结束格子
}

export type TwoDimensionCellProps = {
  td: DimensionTd
  table?: any
  rowIndex: number
  columnIndex: number
  prefixCx: (...args: any[]) => string
  cx: (...args: any[]) => string
  selectionAreaRef: MutableRefObject<DimensionSelectionArea>
  onSelectionAction:  (options: {type: 'selection' | 'clear'}) => void
  onChangeTableData: any
  renderTableTd: any
  renderTableTdToolbar: any
  tableReloadKey: number
  tdReloadKey: number
  readOnly?: boolean
  tableMode: DimensionTableMode
  mergeable: {
    rowHeader: boolean
    columnHeader: boolean
    cell: boolean
  }
}

// 仅当指定属性变化时，才重渲染TD
const arePropsEqual = (oldProps: TwoDimensionCellProps, newProps: TwoDimensionCellProps) => {
  const checkPropKeys = ['tableReloadKey', 'tdReloadKey', 'readOnly']
  const simpleValueSame = !anyChanged(checkPropKeys, oldProps, newProps)
  const mergeableSame = shallowEqual(oldProps.mergeable, newProps.mergeable)

  // 列头/行头 索引是否发生变化
  const headerIndexSame = newProps.td.isRowHeader || newProps.td.isColumnHeader
    ? !anyChanged(['rowIndex', 'columnIndex'], oldProps, newProps)
    : true

  const isEqual = simpleValueSame && mergeableSame && headerIndexSame

  return isEqual
}

// 单元格组件
const TableCell = memo((props: TwoDimensionCellProps) => {
  const {
    cx,
    prefixCx,
    td,
    table,
    rowIndex,
    columnIndex,
    tableMode,
    selectionAreaRef,
    onSelectionAction,
    onChangeTableData,
    readOnly,
    tableReloadKey,
    tdReloadKey
  } = props

  const tdRef = useRef<HTMLTableCellElement>(null)
  const cacheData = useRef({
    isRowHeaderFirstTd: false, // 是否是行头第1个格子（用于处理行头左边框问题）
    showToolbar: false
  })
  const [showToolbar, _setShowToolbar] = useState(false)
  const setShowToolbar = (toggle: boolean) => {
    cacheData.current.showToolbar = toggle
    _setShowToolbar(toggle)
  }
  const [tdPosition, setTdPosition] = useState<{left?: number, top?: number}>({
    left: 0,
    top: 0
  })

  const handleToolbarAction = (
    options?: {
      // 需要更新 table数据
      needChangeTableData?: boolean
    }
  ) => {
    const { needChangeTableData } = options || {}
    onSelectionAction({type: 'clear'})
    if (tdRef.current) {
      setShowToolbar(false)
    }
    if (needChangeTableData !== false) {
      onChangeTableData()
    }
  }

  const handleMouseOver = () => {
    /**
     * 如果 工具图标已经显示，则不处理
     */
    if (cacheData.current.showToolbar) {
      return
    }

    setShowToolbar(true)

    // 正在进行选择时，将 结束格子 设置为当前格子
    if (selectionAreaRef.current.isSelecting) {
      selectionAreaRef.current.targetTd = td
      // 当前格子未选中时，才需要选中
      if (!td.isSelected) {
        onSelectionAction({type: 'selection'})
      }
      return
    }

    // 未进行选择时，将 开始格子 设置为当前格子
    selectionAreaRef.current.sourceTd = td
    // 兼容只选择1个的情况
    selectionAreaRef.current.targetTd = td
  }

  const handleMouseLeave = () => {
    tdRef.current?.dataset
    setShowToolbar(false)
  }

  // 设置单元格表单数据（用于编辑弹窗确定按钮）
  const handleTdDataChange = (options: {values: any}) => {
    const { values } = options
    table.current.updateTd(td, {
      data: values,
      errMsg: ''
    })
    handleToolbarAction()
  }

  // 计算表格的宽高（用于表头吸顶）
  useEffect(() => {
    cacheData.current.isRowHeaderFirstTd = false

    // 单元格无限计算
    if (td.isCell || !tdRef.current) {
      return
    }

    const { width, height } = tdRef.current.getBoundingClientRect()
    td.width = width
    td.height = height

    let top = 0
    let left = 0

    let preTd = td
    // 列头 将向上所有格子的height 的和，当作top
    if (td.isColumnHeader) {
      while (preTd) {
        preTd = table.current.findTheTdAbove(preTd)
        top += (preTd?.height || 0)
      }
      setTdPosition({ top })
    }

    // 行头 将向左将所有格子的with的和，当作left
    if (td.isRowHeader) {
      const [_, matCol] = table.current.getTdMatrix(td)
      cacheData.current.isRowHeaderFirstTd = matCol === 0
      while (preTd) {
        preTd = table.current.findTheTdPrev(preTd)
        left += (preTd?.width || 0)
      }
      setTdPosition({ left })
    }

  }, [tdReloadKey, columnIndex, rowIndex])

  // 渲染表格内容
  const TdComponent = useMemo(() => {
    return props.renderTableTd({
      rowIndex,
      columnIndex,
      td,
    })
  }, [tableReloadKey, tdReloadKey, readOnly])

  // 渲染表格操作按钮
  const isShowToolbar = showToolbar && !readOnly
  const TdToolbarComponent = useMemo(() => {
    if (!isShowToolbar) {
      return null
    }

    const _getToolbarActions = (
      options: {
        enableControl: any
      }
    ) => getToolbarActions({
      td,
      table,
      tableMode,
      onToolbarAction: handleToolbarAction,
      ...options
    })

    return props.renderTableTdToolbar({
      td,
      rowIndex,
      columnIndex,
      getToolbarActions: _getToolbarActions,
      onTdDataChange: handleTdDataChange,
    })
  }, [tdReloadKey, isShowToolbar])

  const mouseEvents = readOnly
    ? {}
    : {
      onMouseOver: handleMouseOver,
      onMouseLeave: handleMouseLeave,
    }

  return (
    <td
      ref={tdRef}
      className={cx({
        'is-selected': td.isSelected,
        'is-header': !td.isCell,
        'is-title': td.isTitle,
        'is-rowHeader': td.isRowHeader,
        'is-rowHeader--firstTd': cacheData.current.isRowHeaderFirstTd,
        'is-columnHeader': td.isColumnHeader
      })}
      style={tdPosition}
      data-row={rowIndex}
      data-col={columnIndex}
      colSpan={td.colspan}
      rowSpan={td.rowspan}
      {...mouseEvents}
    >
      { TdComponent }
      <div
        className={prefixCx('toolbarIcon')}
        onMouseDown={(e) => {e.stopPropagation()}}
      >
        { TdToolbarComponent }
      </div>
    </td>
  )
}, arePropsEqual)

export default TableCell
