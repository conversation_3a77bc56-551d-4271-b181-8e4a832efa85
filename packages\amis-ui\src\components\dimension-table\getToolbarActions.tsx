import {DimensionTd} from './TableCell';

type BaseAction = {
  label: string
  isVisible: () => boolean | undefined
  onAction: () => void
  needChangeTableData?: boolean // 是否需要更新tableData, 默认 true
}

type BaseActionConfig = {
  [key: string]: BaseAction
}

/**
 * 统一整理 单元格子类型，保证 isTitle，isColumnHeader， isRowHeader， isCell 标记互斥
 * @param newTd 单元格
 * @param x 行索引
 * @param y 列索引
 * @returns 处理好的单元格
 */
const formatTdType = (newTd: Partial<DimensionTd>, rowIndex: number, colIndex: number) => {
  const {isColumnHeader, isRowHeader, isTitle} = newTd;
  delete newTd.isTitle;
  delete newTd.isColumnHeader;
  delete newTd.isRowHeader;
  delete newTd.isCell;
  // 第1行第1个 或者 已有isTitle 标记，设置为 isTitle
  if ((rowIndex === 0 && colIndex === 0) || isTitle) {
    newTd.isTitle = true;
  } else if (isColumnHeader) {
    newTd.isColumnHeader = true;
  } else if (isRowHeader) {
    newTd.isRowHeader = true;
  } else {
    newTd.isCell = true;
  }

  return newTd;
};

// 自动将 左上角 表头行/表头列 合并为一个格子
const autoMergeTitleTds = (table: any) => {
  const titleTds: DimensionTd[] = [];
  table.current.forEachCell((td: DimensionTd) => {
    if (td.isTitle) {
      titleTds.push(td);
    }
  });
  if (titleTds.length > 1) {
    table.current.merge(titleTds[0], titleTds.slice(-1)[0]);
  }
};

// 获取单元格操作按钮
export const getToolbarActions = (options: {
  td: DimensionTd;
  table: any;
  tableMode: string;
  onToolbarAction: (opts: { needChangeTableData?: boolean }) => void;
  enableControl: {
    copyable: boolean
    addable: boolean
    editable: boolean
    removable: boolean
    mergeable: boolean
  }
}) => {
  const {
    tableMode,
    td,
    table,
    onToolbarAction,
    enableControl
  } = options;

  const isBasicMode = tableMode === 'basic'
  const isStandardMode = tableMode === 'standard'
  const isMergeMode = tableMode === 'merge'
  const [rowIndex = -1, columnIndex = -1] = table.current?.getTdIndex(td) || []
  const [tdMatRow = 0, tdMatCol = 0] = table.current?.getTdMatrix(td) || []

  // 插入行
  const handleInsertRow = (options: {
    insertTop?: boolean;
    insertBottom?: boolean;
    isCopy?: boolean;
  }) => {
    const {isCopy, insertTop} = options || {};

    const newRowItems: DimensionTd[] = []
    const updateNewTd = (newTd: DimensionTd, x: number, y: number) => {
      let sourceTdX = insertTop ? x : x - 1;
      const sourceTD: DimensionTd = table.current.getTdByMatrix(
        sourceTdX,
        y,
      );

      if (sourceTD) {
        newTd.isColumnHeader = sourceTD.isColumnHeader;
        newTd.isRowHeader = sourceTD.isRowHeader;
        newTd.isTitle = sourceTD.isTitle;
        if (isCopy) {
          newTd.data = sourceTD.data;
        }
      }

      const newTdItem = formatTdType(newTd, x, y) as DimensionTd
      newRowItems.push(newTdItem)

      return newTdItem;
    };

    if (insertTop) {
      table.current.insertRow(td, updateNewTd);
    } else {
      // 找下一行
      const nextRowTd = table.current.findTheTdUnder(td);
      // 非最后1行
      if (nextRowTd) {
        table.current.insertRow(nextRowTd, updateNewTd);
      } else {
        // 最后1行
        table.current.addNewRow(updateNewTd);
      }
    }

    // 自动合并 标题单元格
    if (td.isColumnHeader || td.isRowHeader) {
      autoMergeTitleTds(table);
    }

    if (isStandardMode && td.isColumnHeader) {
      /**
       * 标准模式下：处理 新行列合并与上一行一致，新行的 td.parentId 与上一行 td.id 一致
       */
      const newMatRow = insertTop ? tdMatRow : tdMatRow + td.rowspan
      const titleTd = table.current.getTdByIndex(0, 0)
      let currMatCol = titleTd.colspan
      let currTd = table.current.getTdByMatrix(newMatRow, currMatCol)
      while(currTd) {
        const aboveTd = table.current.findTheTdAbove(currTd)
        if (!aboveTd) {
          break
        }
        // 如果新行与上一行都是 列头，则将 td.parentId 与上一行 td.id 一致
        if (currTd.isColumnHeader && table.current.isSameTdType(currTd, aboveTd)) {
          currTd.parentId = aboveTd.id
        }
        currMatCol += aboveTd.colspan
        const nextTd = table.current.getTdByMatrix(newMatRow, currMatCol)
        // 如果上一行存在合并，则将新行的格子进行合并
        if (aboveTd.isColumnHeader && aboveTd.colspan > 1) {
          const targetTd = table.current.getTdByMatrix(newMatRow, currMatCol - 1)
          table.current.merge(currTd, targetTd)
        }
        currTd = nextTd
      }

      /**
       * 列头 新行的下一行的 parentId
       */
      currTd = table.current.getTdByMatrix(newMatRow + 1, titleTd.colspan)
      while(currTd) {
        // 列头 新行的下一行的 td.parentId 与新行 td.id 一致
        const aboveTd = table.current.findTheTdAbove(currTd)
        if (aboveTd && currTd.isColumnHeader && table.current.isSameTdType(currTd, aboveTd)) {
          currTd.parentId = aboveTd.id
        }
        currTd =  table.current.findTheTdNext(currTd)
      }
    }

    // 标准模式下：行头 添加同级行
    if (isStandardMode && (td.isRowHeader || td.isCell)) {
      let preTd: DimensionTd = table.current.findTheTdPrev(td)
      while (preTd) {
        // 如果当前格子不是 行头，直接向左找到第一个行头为止
        if (!preTd.isRowHeader) {
          preTd = table.current.findTheTdPrev(preTd)
          break
        }

        const [matRow, matCol] = table.current.getTdMatrix(preTd)
        const targetMatrixRow = insertTop ? matRow - 1 : matRow + preTd.rowspan
        const targetTd = table.current.getTdByMatrix(targetMatrixRow, matCol + preTd.colspan - 1)

        let nextPreTd = null
        if (preTd !== targetTd && newRowItems.includes(targetTd)) {
          if (insertTop) {
            nextPreTd = table.current.findTheTdPrev(preTd)
            targetTd.data = preTd.data
            table.current.merge(targetTd, preTd)
          } else {
            table.current.merge(preTd, targetTd)
          }
        }

        preTd = insertTop
          ? nextPreTd
          : table.current.findTheTdPrev(preTd)
      }

      /**
       * 列头 每一行的 parentId 与都是上一行的一致
       */
      const newMatRow = insertTop ? tdMatRow : tdMatRow + td.rowspan
      let currMatCol = 1
      let currTd = table.current.getTdByMatrix(newMatRow, currMatCol)
      while(currTd) {
        // 行头 每一列的 parentId 与都是左侧列的一致
        const leftTd = table.current.findTheTdPrev(currTd)
        if (!currTd.isRowHeader) {
          break
        }
        // 左侧仅对应单个格子时
        if (leftTd.rowspan === 1) {
          currTd.parentId = leftTd.id
        } else {
          // 左侧对应多个格子时
          for (let i = 0; i < leftTd.rowspan; i++) {
            const [leftTdMatRow, leftTdMatCol] = table.current.getTdMatrix(leftTd)
            const childTd = table.current.getTdByMatrix(leftTdMatRow + i, currMatCol)
            childTd.parentId = leftTd.id
          }
        }
        currMatCol += 1
        currTd = table.current.getTdByMatrix(newMatRow, currMatCol)
      }
    }

  };

  // 插入列
  const handleInsertColumn = (options: {
    insertLeft?: boolean;
    insertRight?: boolean;
    isCopy?: boolean;
  }) => {
    const {isCopy, insertLeft} = options || {};

    const newColItems: DimensionTd[] = []
    const updateNewTd = (newTd: DimensionTd, x: number, y: number) => {
      let sourceTdY = insertLeft ? y : y - 1;
      const sourceTD: DimensionTd = table.current.getTdByMatrix(
        x,
        sourceTdY,
      );

      if (sourceTD) {
        newTd.isRowHeader = sourceTD.isRowHeader;
        newTd.isColumnHeader = sourceTD.isColumnHeader;
        newTd.isTitle = sourceTD.isTitle;
        if (isCopy) {
          newTd.data = sourceTD.data;
        }
      }

      const newTdItem = formatTdType(newTd, x, y) as DimensionTd
      newColItems.push(newTdItem)

      return newTdItem;
    };

    if (insertLeft) {
      table.current.insertCell(td, updateNewTd);
    } else {
      // 查找最后一个td
      const nextTd = table.current.findTheTdNext(td);
      // 非最后1列
      if (nextTd) {
        table.current.insertCell(nextTd, updateNewTd);
      } else {
        // 最后1列
        table.current.addCellToAllRow(updateNewTd);
      }
    }

    // 标题格子 自动合并
    if (td.isColumnHeader || td.isRowHeader) {
      autoMergeTitleTds(table);
    }

    if (isStandardMode && td.isRowHeader) {
      /**
       * 行头 新列的行合并与左一列一致，新列的 td.parentId 与左列 td.id 一致
       */
      const newMatCol = insertLeft ? tdMatCol : tdMatCol + td.colspan
      const titleTd = table.current.getTdByIndex(0, 0)
      let currMatRow = titleTd.rowspan
      let currTd = table.current.getTdByMatrix(currMatRow, newMatCol)
      while(currTd) {
        const leftTd = table.current.findTheTdPrev(currTd)
        if (!leftTd) {
          break
        }
        // 如果新列与左侧列都是 行头，则将 td.parentId 与左侧列 td.id 一致
        if (currTd.isRowHeader && table.current.isSameTdType(currTd, leftTd)) {
          currTd.parentId = leftTd.id
        }
        currMatRow += leftTd.rowspan
        const nextTd = table.current.getTdByMatrix(currMatRow, newMatCol)
         // 如果左侧列存在合并，则将新列的格子进行合并
        if (leftTd.isRowHeader && leftTd.rowspan > 1) {
          const targetTd = table.current.getTdByMatrix(currMatRow - 1, newMatCol)
          table.current.merge(currTd, targetTd)
        }
        currTd = nextTd
      }

      /**
       * 行头 新列的右一列的 parentId 与新列一致
       */
      currTd = table.current.getTdByMatrix(titleTd.rowspan, newMatCol + 1)
      while(currTd) {
        // 行头 新列的右一列的 parentId 与新列一致
        const leftTd = table.current.findTheTdPrev(currTd)
        if (leftTd && currTd.isRowHeader && table.current.isSameTdType(currTd, leftTd)) {
          currTd.parentId = leftTd.id
        }
        currTd = table.current.findTheTdUnder(currTd)
      }
    }

    // 标准模式下：列标题 添加同级列 的自动合并
    if (isStandardMode && (td.isColumnHeader || td.isCell)) {
      let aboveTd: DimensionTd = table.current.findTheTdAbove(td)
      while (aboveTd) {
        // 如果当前格子不是 列头，直接向上找到第一个列头为止
        if (!aboveTd.isColumnHeader) {
          aboveTd = table.current.findTheTdAbove(aboveTd)
          break
        }

        const [matRow, matCol] = table.current.getTdMatrix(aboveTd)
        const targetMatrixCol = insertLeft ? matCol-1 : matCol + aboveTd.colspan
        const targetTd = table.current.getTdByMatrix(matRow + aboveTd.rowspan - 1, targetMatrixCol)

        let nextPreTd = null
        if (aboveTd !== targetTd && newColItems.includes(targetTd)) {
          if (insertLeft) {
            nextPreTd = table.current.findTheTdAbove(aboveTd)
            targetTd.data = aboveTd.data
            table.current.merge(targetTd, aboveTd)
          } else {
           table.current.merge(aboveTd, targetTd)
          }
        }

        aboveTd = insertLeft
          ? nextPreTd
          : table.current.findTheTdAbove(aboveTd)
      }

      /**
       * 列头 每一行的 td.parentId 与都是上一行 td.id 的一致
       */
      const newMatCol = insertLeft ? tdMatCol : tdMatCol + td.colspan
      let currMatRow = 1
      let currTd = table.current.getTdByMatrix(currMatRow, newMatCol)
      while(currTd) {
        // 列头 每一行的 td.parentId 与都是上一行 td.id 的一致
        const aboveTd = table.current.findTheTdAbove(currTd)
        if (!currTd.isColumnHeader) {
          break
        }
        // 上层仅对应单个格子时
        if (aboveTd.colspan === 1) {
          currTd.parentId = aboveTd.id
        } else {
          // 上层对应多个格子时，改变每一个格子parentId
          for (let i = 0; i < aboveTd.colspan; i++) {
            const [_, aboveTdMatCol] = table.current.getTdMatrix(aboveTd)
            const childTd = table.current.getTdByMatrix(currMatRow, aboveTdMatCol + i)
            childTd.parentId = aboveTd.id
          }
        }
        currMatRow += 1
        currTd = table.current.getTdByMatrix(currMatRow, newMatCol)
      }
    }
  };

  // 合并CELL
  const handleMergeCell = () => {
    const selectedTds: DimensionTd[] = [];
    table.current.forEachCell((td: DimensionTd) => {
      if (td.isSelected) {
        selectedTds.push(td);
      }
    });
    // 存在多个选中的格子，取第1个与最后一个 合并
    if (selectedTds.length > 1) {
      const mergeSourceTd = selectedTds[0];
      const mergeTargetTd = selectedTds.slice(-1)[0];
      table.current.merge(mergeSourceTd, mergeTargetTd);
    }
  };

  // 拆分CELL
  const handleSplitCell = () => {
    table.current.split(td, (newTd: DimensionTd, x: number, y: number) => {
      newTd.isColumnHeader = td.isColumnHeader;
      newTd.isRowHeader = td.isRowHeader;
      newTd.isTitle = td.isTitle;
      return formatTdType(newTd, x, y);
    });
  };

  // 删除行
  const handleDelRow = () => {
    const titleTd = table.current.getTdByIndex(0, 0)
    // 先记录删除行，下一行第一个td
    let currTd = table.current.getTdByMatrix(tdMatRow + td.rowspan, titleTd.colspan)

    // 删除指定行
    table.current.deleteRow(td);

    // 删除列头行时，将删除行的下一行的 td.parentId 与 删除行上一行的 td.id 一致
    if (isStandardMode && td.isColumnHeader) {
      while(currTd) {
        // 列头 新行的下一行的 td.parentId 上一行的 td.id 一致
        const aboveTd = table.current.findTheTdAbove(currTd)
        if (!aboveTd) {
          currTd.parentId = ''
        } else if (currTd.isColumnHeader && table.current.isSameTdType(currTd, aboveTd)) {
          currTd.parentId = aboveTd.id
        }
        currTd = table.current.findTheTdNext(currTd)
      }
    }
  };

  // 删除列
  const handleDelColumn = () => {
    const titleTd = table.current.getTdByIndex(0, 0)
    // 先记录删除列，右侧第一个td
    let currTd = table.current.getTdByMatrix(titleTd.rowspan, tdMatCol + td.colspan)

    // 删除指定列
    table.current.deleteColumn(td);

    // 删除行头列时，将删除行的右侧列的 td.parentId 与 删除行左侧列的 td.id 一致
    if (isStandardMode && td.isRowHeader) {
      while(currTd) {
        // 行头 新列的右一列的 parentId 与新列一致
        const leftTd = table.current.findTheTdPrev(currTd)
        if (!leftTd) {
          currTd.parentId = ''
        } else if (currTd.isRowHeader && table.current.isSameTdType(currTd, leftTd)) {
          currTd.parentId = leftTd.id
        }
        currTd = table.current.findTheTdUnder(currTd)
      }
    }
  };

  // 复制单元格数据
  const handleCopyTdData = () => {
    table.current.setCopySourceTd(td)
  }

  // 粘贴单元格数据
  const handlePasteTdData = () => {
    table.current.pasteCopyTd(td)
  }

  const getActions = () => {
    const cacheMap = {
      rowIndex: {} as {[key: string]: boolean}, // 与当前格子类型相同的格子 的行索引
      colIndex: {} as {[key: string]: boolean}, // 与当前格子类型相同的格子 的列索引
      selectedCount: 0 // 选中格子的数量
    };
    table.current.forEachCell((item: DimensionTd) => {
      if (item.isSelected) {
        cacheMap.selectedCount += 1
      }
      if (table.current.isSameTdType(td, item)) {
        const [x, y] = table.current.getTdMatrix(item) || [];
        cacheMap.rowIndex[x] = true;
        cacheMap.colIndex[y] = true;
      }
    });

    // 当前格子被选中，且有2个以上选中。当前格子只有 “合并单元格”操作
    const isReadyMerge = td.isSelected && cacheMap.selectedCount > 1

    // 所有操作配置。具体显/隐 通过 isVisible 进行控制
    const baseActionConfig: BaseActionConfig = {
      edit: {
        label: '编辑',
        needChangeTableData: false,
        onAction: () => {}, // 具体的编辑操作，会在 amis 层重写
        isVisible: () => {
          const result = enableControl.editable &&
            !isReadyMerge // 当前格子不可合并

          return result
        }
      },
      merge: {
        label: '合并单元格',
        onAction: handleMergeCell,
        isVisible: () => {
          const result = enableControl.mergeable &&
            !td.isTitle && // 非标题格子
            isReadyMerge && // 当前格子可合并
            isMergeMode // 仅合并模式

          return result
        }
      },
      split: {
        label: '拆分单元格',
        onAction: handleSplitCell,
        isVisible: () => {
          const result = enableControl.mergeable &&
            !td.isTitle && // 非标题格子
            !isReadyMerge && // 当前格子不可合并
            (isMergeMode && (td.rowspan > 1 || td.colspan > 1)) // 合并模式下，存在合并时

          return result
        }
      },
      copyCell: {
        label: '复制单元格',
        needChangeTableData: false,
        onAction: handleCopyTdData,
        isVisible: () => {
          const result = enableControl.copyable &&
            !td.isTitle && // 非标题格子
            !isReadyMerge // 当前格子不可合并

          return result
        }
      },
      pasteCell: {
        label: '粘贴单元格',
        onAction: handlePasteTdData,
        isVisible: () => {
          const result = enableControl.copyable &&
            !td.isTitle && // 非标题格子
            !isReadyMerge && // 当前格子不可合并
            table.current.isSameTdType(table.current.copySourceTd, td) && // 仅相同类型的Td可以被粘贴
            table.current.copySourceTd !== td // 当前Td 不是被复制的Td

          return result
        }
      },
      copyRow: {
        label: '复制行',
        onAction: () => handleInsertRow({isCopy: true}),
        isVisible: () => {
          const result = enableControl.copyable &&
            !td.isTitle && // 非标题格子
            !isReadyMerge && // 当前格子不可合并
            (isBasicMode ? !td.isColumnHeader : true) // 基础模式下，列头不可复制行

          return result
        }
      },
      copyColumn: {
        label: '复制列',
        onAction: () => handleInsertColumn({isCopy: true}),
        isVisible: () => {
          const result = enableControl.copyable &&
            !td.isTitle &&  // 非标题格子
            !isReadyMerge && // 当前格子不可合并
            (isBasicMode ? !td.isRowHeader : true) // 基础模式下，行头不可复制行

          return result
        }
      },
      insertTop: {
        label: td.isColumnHeader
          ? '上方插入表头行'
          : (isStandardMode && td.isRowHeader)
            ? '上方插入同级行'
            : '上方插入行',
        onAction: () => handleInsertRow({insertTop: true}),
        isVisible: () => {
          const result = enableControl.addable &&
            !td.isTitle && // 非标题格子
            !isReadyMerge && // 当前格子不可合并
            (td.isColumnHeader ? rowIndex > 0 : true) && // 列头只能第2行开始，可向上插入行
            (isBasicMode ? !td.isColumnHeader : true) // 基础模式下，列头不可插入行

          return result
        }
      },
      insertBottom: {
        label: td.isColumnHeader
          ? '下方插入表头行'
          : (isStandardMode && td.isRowHeader)
            ? '下方插入同级行'
            : '下方插入行',
        onAction: () => handleInsertRow({insertBottom: true}),
        isVisible: () => {
          const result = enableControl.addable &&
            !td.isTitle &&  // 非标题格子
            !isReadyMerge &&  // 当前格子不可合并
            (isBasicMode ? !td.isColumnHeader : true) // 基础模式下，列头不可插入行

          return result
        }
      },
      insertLeft: {
        label: td.isRowHeader
          ? '左侧插入表头列'
          : (isStandardMode && td.isColumnHeader)
            ? '左侧插入同级列'
            : '左侧插入列',
        onAction: () => handleInsertColumn({insertLeft: true}),
        isVisible: () => {
          const result = enableControl.addable &&
            !td.isTitle && // 非标题格子
            !isReadyMerge && // 当前格子不可合并
            (td.isRowHeader ? columnIndex > 0 : true) && // 行头只能第2列开始，可向左插入列
            (isBasicMode ? !td.isRowHeader : true) // 基础模式下，行头不可插入列

          return result
        }
      },
      insertRight: {
        label: td.isRowHeader
          ? '右侧插入表头列'
          : (isStandardMode && td.isColumnHeader)
            ? '右侧插入同级列'
            : '右侧插入列',
        onAction: () => handleInsertColumn({insertRight: true}),
        isVisible: () => {
          const result = enableControl.addable &&
            !td.isTitle && // 非标题格子
            !isReadyMerge && // 当前格子不可合并
            (isBasicMode ? !td.isRowHeader : true) // 基础模式下，行头不可插入列

          return result
        }
      },
      /**
       * 相同区域的 行/列 最少1条。（只有超过1条的才能被删除）
       */
      deleteRow: {
        label: '删除行',
        onAction: handleDelRow,
        isVisible: () => {
          const sameTdTypeRowCount = Object.keys(cacheMap.rowIndex).length;
          const result = enableControl.removable &&
            !td.isTitle && // 非标题格子
            !isReadyMerge && // 当前格子不可合并
            (td.rowspan < sameTdTypeRowCount) // 只有超过1行的才能删除行

          return result
        }
      },
      deleteColumn: {
        label: '删除列',
        onAction: handleDelColumn,
        isVisible: () => {
          const sameTdTypeColCount = Object.keys(cacheMap.colIndex).length;
          const result = enableControl.addable &&
            !td.isTitle &&  // 非标题格子
            !td.isSelected &&  // 当前格子不可合并
            (td.colspan < sameTdTypeColCount) // 只有超过1列的才能删除列

          return result
        }
      },
    };

    // 对 onAction 进行拦截处理
    const onActionProxy = (
      action: BaseAction & { actionKey: string }
    ) => {
      const newOnAction = () => {
        /**
         * 非“粘贴”操作，先将之前 存的copySourceTd 清除
         * 即：
         * “复制”之后下一个操作粘贴才有效。如果进行其他操作，就清除掉之前复制的内容。
         * 不然多次操作，可能会忘记之前复制的是哪个格子了。
         */
        if (action.actionKey !== 'pasteCell') {
          table.current.setCopySourceTd()
        }

        // 执行具体的操作
        action.onAction?.()

        // 回调上层
        onToolbarAction({
          needChangeTableData: action.needChangeTableData
        })
      }

      return newOnAction
    }

    /**
     * 1. 删除掉 isVisible() 返回false 的操作
     * 2. 对 onAction 进行拦截处理，处理一些公共逻辑
     */
    const actions = Object.keys(baseActionConfig).flatMap((actionKey) => {
      const action = baseActionConfig[actionKey]
      const { isVisible, ...restAction } = action

      if (!isVisible()) {
        return []
      }

      const newAction = {
        ...restAction,
        actionKey,
        onAction: onActionProxy({
          ...action,
          actionKey
        })
      }

      return [newAction]
    })

    return actions
  };

  const toolbarActions = getActions();

  return toolbarActions;
};
