import useDebounceFn from "ahooks/lib/useDebounceFn";
import { uuidv4, mapTree } from 'amis-core'
import { useEffect, useRef, useState } from "react";
import isEmpty from "lodash/isEmpty";
import cloneDeep from "lodash/cloneDeep";
import { convertToColumnHeader, convertToRowHeader, arrayToTree } from './tableControl/tree'
import { DimensionTableMode, DimensionTd } from "./TableCell";

type TableDataRow = {
  id: string
  tds: Array<Partial<DimensionTd>>
}

// 表格数据（merge模式，table实例 使用）
type TableData = Array<TableDataRow>

// 结构化的单元格数据（树状结构）
type StructTableItem = {
  data: any
  props: Omit<DimensionTd, 'data'> & {rowId?: string}
  children?: StructTableItem[]
}
// 结构化的表格数据（basic/standard 模式使用）
type StructTableData = {
  // 标题数据格式
  title: StructTableItem
  // 列头数据格式（树状结构）
  columnHeader: Array<StructTableItem>
  // 行头数据格式（树状结构）
  rowHeader:  Array<StructTableItem>
  // 单元格 2维数组
  cell: Array<Omit<StructTableItem, 'children'>[]>
}

// 更新key，用于刷新组件
export const useReloadKey = (wait: number = 200) => {
  const [reloadKey, _setReloadKey] = useState(0);

  const setReloadKey = (onReload?: () => void) => {
    onReload?.()
    _setReloadKey(prev => prev + 1)
  }

  const { run: lazyUpdateReloadKey } = useDebounceFn(setReloadKey, {wait});
  const updateReloadKey = (lazy = false, onReload?: () => void) => {
    if (lazy) {
      lazyUpdateReloadKey(onReload)
      return
    }
    setReloadKey(onReload)
  };

  return {
    reloadKey,
    updateReloadKey,
  }
}

// table 实例 的默认数据
const getDefaultTableData = () => {
  const defaultTd: Partial<DimensionTd> = {
    colspan: 1,
    rowspan: 1,
    data: {}
  }

  const tableData: TableData = [
    {
      id: uuidv4(),
      tds: [{
        id: uuidv4(),
        isTitle: true,
        ...defaultTd
      }, {
        id: uuidv4(),
        isColumnHeader: true,
        ...defaultTd
      }]
    },
    {
      id: uuidv4(),
      tds: [{
        id: uuidv4(),
        isRowHeader: true,
        ...defaultTd
      }, {
        id: uuidv4(),
        isCell: true,
        ...defaultTd
      }]
    }
  ]

  return tableData
}

// 将propValue解析为 渲染需要的数据
const resolvePropValue = (
  options: {
    value: any;
    tableMode: DimensionTableMode
  }
) => {
  const { value, tableMode } = options

  const isMergeMode = tableMode === 'merge'
  /**
   * 填充默认数据
   * 1. 无数据时
   * 2. 自由合并模式下，value的值不是数组
   */
  if (
    !value || isEmpty(value) ||
    (isMergeMode && !Array.isArray(value))
  ) {
    return getDefaultTableData()
  }

  // tableMode 为 merge（合并模式） 不需要转数据
  if (isMergeMode) {
    return value.concat()
  }

  // tableMode为 basic/standard(非合并模式时) 转换数据
  const { title = {}, rowHeader = [], columnHeader = [], cell = [] } = value

  // 列头
  const {rows: columnHeaders = []} = convertToColumnHeader(cloneDeep(columnHeader))
  // 行头
  const {columns: rowHeaders = []} = convertToRowHeader(cloneDeep(rowHeader))

  const getTd = (item:
    StructTableItem & {
      colspan?: number
      rowspan?: number
    }
  ): DimensionTd & { rowId?: string } => {
    const { props, data, colspan, rowspan } = item
    const td = {
      // @ts-ignore
      id: uuidv4(),
      ...props,
      colspan: colspan || props.colspan,
      rowspan: rowspan || props.rowspan,
      data
    }
    return td
  }

  let tableData: TableData = []

  // 构造 标题 + 列头
  columnHeaders.forEach((col: StructTableItem[], colIndex: number) => {
    // 列头
    const tds = col.map((item) => {
      return getTd(item)
    })
    tableData[colIndex] = {
      id: tds[0]?.rowId || uuidv4(),
      tds
    }

    // 插入标题
    if (colIndex === 0) {
      // 行头 列数
      const rowHeaderColCount = Math.max(...rowHeaders.map((item: StructTableItem[]) => item.length))
      // 列头 行数
      const columnHeaderRowCount  = columnHeaders.length
      tableData[0].tds.unshift(getTd({
        data: title.data,
        props: {
          ...title.props,
          colspan: rowHeaderColCount,
          rowspan: columnHeaderRowCount
        }
      }))
    }
  })

  // 构造 行头 + cell
  cell.forEach((row: StructTableItem[], rowIndex: number) => {

    const rowItems = (rowHeaders[rowIndex] || []).concat(row).map((item: StructTableItem) => {
      return getTd(item)
    }) || []

    const tableRow = {
      id: rowItems[0]?.rowId || uuidv4(),
      tds: rowItems
    }

    tableData.push(tableRow)
  })

  return tableData
}

// 将渲染数据处理为 propValue
const parsePropValue = (
  options: {
    tableData: any
    tableMode: string
  }
) => {
  const { tableData, tableMode } = options

  const getItem = (td: any, parse: boolean = true) => {
    const {
      // 将仅前端使用的数据，过滤出来。将 restTd 传给 formItem
      reloadKey,
      isSelected,
      errMsg,
      width,
      height,
      ...restTd
    } = td

    let item = restTd

    if (parse) {
      const { data, children, ...props } = item
      item = {
        data,
        props,
      }
      if (children) {
        item.children = children
      }
    }

    return item
  }

  const isMergeMode = tableMode === 'merge'

  // 合并模式只需要最基本的转换
  if (isMergeMode) {
    const result: TableData = tableData.map((tr: TableDataRow) => {
      return {
        ...tr,
        tds: tr.tds.map((td: DimensionTd) => getItem(td, false))
      }
    })
    return result
  }

  // 非合并模式，需要转为需要的格式
  let title = {} as StructTableItem
  let rowHeader: any[] = []
  let columnHeader: any[] = []
  let cell: StructTableItem[][] = []

  tableData.forEach((row: TableDataRow) => {
    const cellRow: any[] = []
    let rowId = row.id
    row.tds.forEach((td) => {
      if (td.isTitle) {
        title = getItem(td)
      } else if (td.isColumnHeader) {
        columnHeader.push({
          ...td,
          rowId
        })
      } else if (td.isRowHeader) {
        rowHeader.push({
          ...td,
          rowId
        })
      } else if (td.isCell) {
        cellRow.push(getItem(td))
      }
    })
    if (cellRow.length) {
      cell.push(cellRow)
    }
  })

  rowHeader = mapTree(arrayToTree(rowHeader, ''), (td) => getItem(td)) as StructTableItem[]
  columnHeader = mapTree(arrayToTree(columnHeader, ''), (td) => getItem(td)) as StructTableItem[]

  const result: StructTableData = {
    title,
    rowHeader: rowHeader || [],
    columnHeader: columnHeader || [] ,
    cell,
  }

  return result
}

// 设置数据，处理 value 的转换逻辑
export const useTableData = (options: {
  value: any
  tableMode: DimensionTableMode
  onChange: any
}) => {
  const tableData = useRef<TableData>()
  // 缓存onChange 回调的数据
  const cacheValue = useRef<any>()

  const { value, onChange, tableMode } = options

  useEffect(() => {
    /**
     * 当前 value 的变更是由 onChange 回调引起的时，无需转换再次转换
     */
    const isLastUpdate = !!cacheValue.current && cacheValue.current === value
    if (!isLastUpdate) {
      // 将 value 数据 转为 table 实例所需要的数据
      const resolvedValue = resolvePropValue({
        value,
        tableMode
      })
      tableData.current = resolvedValue
    }
  }, [value])

  // table实例 数据变时时的回调
  const handleTableDataChange = () => {
    // 将 table 实例数据，转为 value 数据
    const parsedValue = parsePropValue({
      tableData: tableData.current,
      tableMode
    })
    cacheValue.current = parsedValue
    onChange(parsedValue)
  }

  return {
    tableData,
    handleTableDataChange
  }
}
