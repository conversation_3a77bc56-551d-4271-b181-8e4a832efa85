import {themeable, ThemeProps} from 'amis-core';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
} from 'react';
import TableControl from './tableControl';
import { DimensionTableMode, DimensionTd } from './TableCell';
import { useReloadKey, useTableData } from './hooks';
import Table from './Table';
export interface DimensionTableProps extends ThemeProps {
  // 表格数据
  value: any
  // 数据变化回调
  onChange: any
  // 跟踪数据（全量更新表格）
  trackData: any
  // 表格模式
  tableMode: DimensionTableMode;
  // 仅可读性
  readOnly?: boolean
  // 是否可合并
  mergeable: {
    // 行头区域是否可合并
    rowHeader: boolean
    // 列头区域是否可合并
    columnHeader: boolean
    // 数据格区域是否可合并
    cell: boolean
  },
  // 渲染单元格
  renderTableTd: (options: any) => JSX.Element
  // 渲染单元格工具操作
  renderTableTdToolbar: (options: any) => JSX.Element | null
  // 校验单元格
  onValidateTd: (
    options: {
      td: DimensionTd,
      rowIndex: number,
      columnIndex: number,
      value: any
    }) => string
  // hover 模式
  hoverMode?: 'row' | 'cross';
}

const DimensionTable = forwardRef((props: DimensionTableProps, ref) => {
  const {
    classnames: cx,
    readOnly,
    trackData,
    mergeable,
    tableMode,
    hoverMode,
  } = props;

  // table实例
  const table = useRef<any>(null);
  const {reloadKey, updateReloadKey} = useReloadKey()
  const {tableData, handleTableDataChange} = useTableData({
    tableMode,
    value: props.value,
    onChange: (value: any) => {
      validate(value) // 变更值之前先校验
      props.onChange(value)
    }
  })

  useImperativeHandle(
    ref,
    () => {
      return {
        validate
      };
    },
    [],
  );

  // tableData 变化，同步最新数据，并全量更新一次Table单元格
  useEffect(() => {
    if (!table.current) {
      table.current = new TableControl();
      // 用于测试
      // window.myTable = table.current;
    }
    if (tableData.current) {
      table.current.init(tableData.current);
      updateReloadKey()
    }
  }, [tableData.current]);

  // 当跟踪数据发生变化，使用lazy方式，更新全量Table单元格
  useEffect(() => {
    updateReloadKey(true)
  }, [trackData])

  const prefixCx = (clsStr: string = '', ...args: any[]) =>
    cx(`DimensionTable${clsStr ? `-${clsStr}` : ''}`, ...args);

  const validate = (value: any): boolean => {
    const { onValidateTd } = props
    let validateResult = true // 错误标记

    table.current.forEachCell((td: DimensionTd, rowIndex: number, columnIndex: number) => {
      // 如果后续存在复杂的联动校验，可考虑将 table 实例，传入 onValidateTd，可利用tableCtrl的遍历/查找方法
      const errMsg = onValidateTd({
        td,
        rowIndex,
        columnIndex,
        value
      })

      // 所有表头格子均更新（当出现msg文案时，表头高度会变，用于重新计算吸顶高度）
      if (!td.isCell) {
        table.current.updateTd(td)
      }
      // 更新对应格子的错误信息
      if (td.errMsg !== errMsg) {
        table.current.updateTd(td, {errMsg})
      }
      // 更改校验错误标记
      if (errMsg) {
        validateResult = false
      }
    })

    return validateResult
  }

  const TableComponent = useMemo(
    () => table.current && (
      <Table
        cx={cx}
        prefixCx={prefixCx}
        table={table}
        readOnly={readOnly}
        mergeable={mergeable}
        tableReloadKey={reloadKey}
        onChangeTableData={handleTableDataChange}
        tableMode={props.tableMode}
        renderTableTd={props.renderTableTd}
        renderTableTdToolbar={props.renderTableTdToolbar}
        hoverMode={hoverMode}
      />
    ),
    [reloadKey, readOnly, mergeable],
  );

  return (
    <div className={prefixCx()}>
      {TableComponent}
    </div>
  );
});

export default themeable(DimensionTable);
