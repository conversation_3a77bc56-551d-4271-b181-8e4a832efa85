// @ts-nocheck

import {uuidv4, anyChanged} from 'amis-core'

export default {
  deleteObjectInArray(arr, obj) {
    const index = arr.indexOf(obj)
    if (index > -1) {
      arr.splice(index, 1)
    }
  },
  newTd: () => { return {id: uuidv4(), colspan: 1, rowspan: 1, data: {}, reloadKey: 0 }},
  delTd(row, td) {
    this.deleteObjectInArray(row.tds, td)
  },
  newTr: () => ({id: uuidv4(), tds: []}),
  delTr(object, row) {
    this.deleteObjectInArray(object, row)
  },
  updateTd: (td, newProps = {}) => {
    if (!td) {
      return {}
    }
    Object.assign(td, {
      ...newProps,
      reloadKey: (td?.reloadKey || 0) + 1,
    })
    return td
  },
  setTdSelected(td, toggle) {
    if (!td) {
      return {}
    }
    if (td.isSelected !== toggle) {
      return this.updateTd(td, {
        isSelected: toggle
      })
    }
    return td
  },
  isSameTdType: (td, otherTd) => {
    if (!td || !otherTd) {
      return false
    }

    return !anyChanged(['isTitle', 'isColumnHeader', 'isRowHeader', 'isCell'], td, otherTd)
  },
  getTdRowSpan: (obj) => obj.rowspan,
  getTdColSpan: (obj) => obj.colspan,
  getRowByIndex: (object, index) => object[index],
  getRowByTd(object, td){
    let ret = null
    this.eachRow(object, (row) => {
      const index = this.getTdsInRow(row).indexOf(td)
      if (index > -1) {
        ret = row
      }
    })
    return ret
  },
  getTdsInRow: (row) => row.tds || [],
  isTdInRow: function(row, td) {
    return this.getTdsInRow(row).indexOf(td) > -1
  },
  getTdInRow: (row, index) => row.tds[index],
  getRowSize: (object) => object.length,
  eachRow:(object, fn) => {
    if (object?.length > 0) {
      object.forEach(fn)
    }
  },
  mapRow:(object, fn) => {
    if (object?.length > 0) {
      return object.map(fn)
    }
  },
  // 在索引index前插入行
  insertEmptyRow(object, index) {
    object.splice(index, 0, this.newTr())
    const newRow = object[index]
    return newRow
  },
  insertRowBefore(object, row, newRowContent) {
    const index = object.indexOf(row)
    object.splice(index, 0, newRowContent)
    const newRow = object[index]
    return newRow
  },
  insertBeforeTd(row, td, newEl) {
    row.tds.splice(this.getTdsInRow(row).indexOf(td), 0, newEl)
  },
  insertAfterTd(row, td, newEl) {
    row.tds.splice(this.getTdsInRow(row).indexOf(td) + 1, 0, newEl)
  },
  appendTd: (row, newEl) => row.tds.push(newEl),
  /**
   * @param {Element} td 			给哪个TD进行操作
   * @param {Number} rowSpan 		td rowSpan设置值，当offset=true时，rowSpan则为相对变动的值
   * @param {Number} colSpan 		td colSpan设置值，当offset=true时，colSpan则为相对变动的值
   * @param {Boolean} offset 		指明是否把前两值作为相对变动的值
   */
  setTdSpan(td, rowSpan, colSpan, offset = false){
    if (rowSpan !== null) {
      if (offset) {
        td.rowspan += rowSpan
      } else {
        td.rowspan = rowSpan
      }
    }
    if (colSpan !== null) {
      if (offset) {
        td.colspan += colSpan
      } else {
        td.colspan = colSpan
      }
    }
    return this.updateTd(td)
  },

  /**
   * 复制 td 数据
   */
  copySourceTd: null,
  setCopySourceTd: function(td) {
    this.copySourceTd = td || null
  },
  pasteCopyTd: function(targetTd) {
    // 仅相同区域的单元格数据才可复制
    if (this.copySourceTd && targetTd && this.isSameTdType(this.copySourceTd, targetTd)) {
      this.updateTd(targetTd, {
        data: this.copySourceTd.data || {}
      })
    }
  },
}
