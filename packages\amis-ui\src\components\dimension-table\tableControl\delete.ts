
// @ts-nocheck

export default {
    split(td, decorater) {
        if (!td) return;
        if (!decorater) decorater = (td) => td;
        let [spanRow, spanCol] = this.getSpan(td);
        if (spanRow > 1 || spanCol > 1) {
            this.setTdSpan(td, 1, 1);
            // row 1
            let spanIndexCol = spanCol - 1;
            while (spanIndexCol > 0) {
                const [rowIndex] = this.getTdIndex(td);
                const row = this.getRowByIndex(this.dom, rowIndex)
                this.insertAfterTd(
                    row,
                    td,
                    decorater(this.newTd(), rowIndex, spanIndexCol - 1, this.dom, row)
                );
                spanIndexCol--;
            }
            // other row
            let spanIndexRow = spanRow - 1 - 1;
            const arrTdIndex = this.getTdIndex(td);
            const arrTdMatrix = this.getTdMatrix(td);
            while (spanIndexRow >= 0) {
                spanIndexCol = spanCol - 1;
                const tr = this.getRowByIndex(this.dom, arrTdIndex[0] + (spanRow - 1 - spanIndexRow));
                const tds = this.getTdsInRow(tr);
                const firstTdMatrix = tds.length > 0 ? this.getTdMatrix(tds[0]) : Number.MAX_VALUE;
                /*
                    1. The td ready to merge was on the first cell index.
                    2. The row was empty.
                    3. The first td in the row must be after the td which ready to insert.
                 */
                if (arrTdMatrix[0] == 0 && tds.length == 0 && firstTdMatrix[1] <= arrTdMatrix[1]) {
                    while (spanIndexCol >= 0) {
                        this.appendTd(
                            tr,
                            decorater(
                                this.newTd(),
                                spanRow - 1 - spanIndexRow,
                                spanCol - 1 - spanIndexCol,
                                this.dom,
                                tr
                            )
                        );
                        spanIndexCol--;
                    }
                } else {
                    for (let index = 0; index < tds.length; index++) {
                        const tdFound = tds[index];
                        const tdFoundMatrix = this.getTdMatrix(tdFound);
                        if (tdFoundMatrix[1] > arrTdMatrix[1]) {
                            while (spanIndexCol >= 0) {
                                this.insertBeforeTd(
                                    tr,
                                    tdFound,
                                    decorater(
                                        this.newTd(),
                                        spanRow - 1 - spanIndexRow,
                                        spanCol - 1 - spanIndexCol,
                                        this.dom,
                                        tr
                                    )
                                );
                                spanIndexCol--;
                            }
                            break;
                        } else if (index + 1 == tds.length) {   // last position
                            while (spanIndexCol >= 0) {
                                this.insertAfterTd(
                                    tr,
                                    tdFound,
                                    decorater(
                                        this.newTd(),
                                        spanRow - 1 - spanIndexRow,
                                        spanIndexCol - 1,
                                        this.dom,
                                        tr
                                    )
                                );
                                spanIndexCol--;
                            }
                        }
                    }
                }
                spanIndexRow--;
            }
        }
        this.refresh();
    },

    deleteColumn(td) {
        if (!td) return;
        // const size = td.colSpan;
        const [tdRowMatrix, tdColMatrix] = this.getTdMatrix(td);
        const tdColSpan = this.getTdColSpan(td);
        let tdColCount = 0;

        while (tdColCount < tdColSpan) {
            this.deleteColumnHandler(tdColMatrix);
            this.refresh();
            tdColCount++;
        }
    },

    deleteColumnHandler(tdColMatrix) {
        const tdDone = [];
        const tbSize = this.getSize();
        let rowIndex = 0;

        while (rowIndex < tbSize[0]) {
            const tdFound = this.getTdByMatrix(rowIndex, tdColMatrix);
            if (tdFound && tdDone.indexOf(tdFound) == -1) {
                let span = this.getTdColSpan(tdFound);
                if (span > 1) {
                    this.setTdSpan(tdFound, null, span - 1);
                } else {
                    this.delTd(this.getRowByIndex(this.dom, rowIndex), tdFound);
                }
                tdDone.push(tdFound);
                // fix some td rowSpan greater than 1, and delete wrong TDs in the next row.
                const rowSpan = this.getTdRowSpan(tdFound);
                if (rowSpan > 1) {
                    rowIndex += rowSpan;
                    continue;
                }
            }
            rowIndex++;
        }
    },

    deleteRow(td) {
        if (!td) return;
        const [tdRowMatrix, tdColMatrix] = this.getTdMatrix(td);

        const tdRowSpan = this.getTdRowSpan(td);
        let tdRowCount = 0;
        while (tdRowCount < tdRowSpan) {
            this.deleteRowHandler(tdRowMatrix);
            this.refresh();
            tdRowCount++;
        }
    },

    deleteRowHandler(tdRowMatrix) {
      const tr = this.getRowByIndex(this.dom, tdRowMatrix);
      const nextTr = this.getRowByIndex(this.dom, tdRowMatrix + 1);
      const tbSize = this.getSize();
      let colIndex = 0;
      const tdMarkShort = [];
      while (colIndex < tbSize[1]) {
          const cellType = this.cellType(tdRowMatrix, colIndex);

          //
          if (cellType == this.CELL_BIG_HEAD) {
              const tdFound = this.getTdByMatrix(tdRowMatrix, colIndex);
              const tdRowSpan = this.getTdRowSpan(tdFound);
              const tdColSpan = this.getTdColSpan(tdFound);
              // move tdFound to next tr
              if (tdRowSpan > 1 && tbSize[0] != tdRowMatrix) {
                  let nextTdColMatrix = colIndex + tdColSpan;
                  while (nextTdColMatrix < tbSize[1]) {
                    let targetColMatrix = nextTdColMatrix
                    let nextRowTd // 当前格子下一行对应的第一个格子
                    do {
                        nextRowTd = this.getTdByMatrix(tdRowMatrix + 1, targetColMatrix++);
                    } while(nextRowTd && !this.isTdInRow(nextTr, nextRowTd))
                    // 将上一行带有行合并的td, 插入到下一行
                    const targetRow = this.getRowByIndex(this.dom, tdRowMatrix + 1);
                    if (nextRowTd && targetRow == nextTr) {
                        /**
                         * 如果当前行找到了下一行的td, 表示这个 td 是由上一行的td插入进来的，要将后续td插入到后边
                         * 否则，直接插入到前边
                         */
                        if (this.isTdInRow(tr, nextRowTd)) {
                          this.insertAfterTd(targetRow, nextRowTd, tdFound);
                        } else {
                          this.insertBeforeTd(targetRow, nextRowTd, tdFound);
                        }
                        break;
                    }
                    nextTdColMatrix++;
                  }
                  // const tdFoundInRow = this.getRowByTd(tdFound);
                  // if (tdFoundInRow != nextTr && nextTr) {
                  //     this.appendTd(nextTr, tdFound);
                  // }
                  // make sure short it.
                  this.setTdSpan(tdFound, -1, null, true);
                  this.refresh();
              }

              colIndex += tdColSpan;
          } else if(cellType == this.CELL_BIG) {
              const tdFound = this.getTdByMatrix(tdRowMatrix, colIndex);
              tdMarkShort.push(tdFound);
              this.refresh();
              const tdColSpan = this.getTdColSpan(tdFound);
              colIndex += tdColSpan;
          } else {
              colIndex++;
          }
      }
      tdMarkShort.forEach((td) => this.setTdSpan(td, -1, null, true));
      this.delTr(this.dom, tr);
  }
};
