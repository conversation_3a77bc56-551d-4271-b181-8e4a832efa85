// @ts-nocheck

/**
 * 参考开源项目：tableButcher
 * 地址：https://github.com/counterm/tableButcher
 */

import deleteCell from './delete';
import merge from './merge';
import matrix from './matrix';
import insert from './insert';
import query from './query';
import cell from './cell'

const TableControl: any = function() {
  this.init = function(object) {
      this.initObj(object);
      this.buildMatrix();
  };
};

TableControl.prototype = {
  dom: null,
  ...cell,
  ...deleteCell,
  ...merge,
  ...matrix,
  ...insert,
  ...query
};

export default TableControl
