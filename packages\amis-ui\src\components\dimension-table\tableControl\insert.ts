// @ts-nocheck

// append column or row
export default {
    addCellToAllRow(fn){
        let [sizeRow, sizeCell] = this.getSize();
        this.forEachRow((row, rowIndex)=>{
            let td = this.newTd();
            if (fn) {
                const retFn = fn(td, rowIndex, sizeCell, this.dom, row);
                retFn && td == retFn;
            }
            this.appendTd(row, td);
        });
        this.refresh();
        return this;
    },
    // Method can recognize each big cell, and expand them by increase their colspan.
    insertRow(beforeTd, fn){
        const [tdRowIndex, tdColIndex] = this.getTdIndex(beforeTd);
        let [sizeRow, sizeCell] = this.getSize();
        const trHolder = this.newTr();

        const fnAddTd = (cellIndex) => {
            let td = this.newTd();
            if (fn) {
                const retFn = fn(td, tdRowIndex, cellIndex, this.dom, trHolder);
                retFn && td == retFn;
            }
            this.appendTd(trHolder, td);
        };

        // first row.
        if (tdRowIndex == 0) {
            let cellIndex = 0;
            while (cellIndex < sizeCell) {
                fnAddTd(cellIndex);
                cellIndex++;
            }
        } else {
            // const tdBase = [];          // create row upon which TD.
            let sizeIndex = 0;

            while (sizeIndex < sizeCell) {
                const tdFound = this.getTdByMatrix(tdRowIndex, sizeIndex);
                const cellType = this.cellType(tdRowIndex, sizeIndex);
                const colSpan = this.getTdColSpan(tdFound);
                if (cellType == this.CELL_NORMAL) {
                    fnAddTd(sizeIndex);
                    sizeIndex++;
                } else if (cellType == this.CELL_BIG_HEAD) {
                    let size = colSpan;
                    while (size > 0) {
                        fnAddTd(sizeIndex);
                        size--;
                    }
                    sizeIndex += colSpan;
                } else if (cellType == this.CELL_BIG_HEAD || cellType == this.CELL_BIG) {
                    this.setTdSpan(tdFound, 1, null, true);
                    sizeIndex += colSpan;
                }
            }
        }
        const tdInRow = this.getRowByTd(this.dom, beforeTd);
        const newRow = this.insertRowBefore(this.dom, tdInRow, trHolder);
        this.refresh();
        return newRow;
    },
    addNewRow(fn){
        let [rowCount, colCount] = this.getSize();
        const newTr = this.insertEmptyRow(this.dom, rowCount);
        let index = 0;
        while(colCount>0){
            let newTd = this.newTd();
            if (fn) {
                const retFn = fn(newTd, rowCount, index, this.dom, newTr);
                retFn && newTd == retFn;
            }
            this.appendTd(newTr, newTd);
            colCount--;
            index++;
        }
        this.refresh();
        return newTr;
    },
    insertCell(beforeThisTd, fn){
        let [tdRowIndex, tdColIndex] = this.getTdMatrix(beforeThisTd);

        let newInsertTd = (rowIndex, cellIndex, row) => {
            let newTd = this.newTd();
            if (fn) {
                const retFn = fn(newTd, rowIndex, cellIndex, this.dom, row);
                retFn && newTd == retFn;
            }
            return newTd;
        };

        // if selected td was the first column
        if (tdColIndex == 0) {
            this.forEachRow((row, rowIndex) => {
                this.insertBeforeTd(row, this.getTdInRow(row, 0), newInsertTd(rowIndex, tdColIndex, row));
            });
            return;
        }

        //
        const matrix = this.getTdMatrix(beforeThisTd);
        const colMatrix = matrix[1];
        const size = this.getSize();

        let index = -1;
        /*
         0 0 x 0 0 0 0
         0 0 x 0 0 0 0
         0 0 x 0 0 0 0
         0 0 x 0 0 0 0
         Scan one column, will meet normal td size, or big td.
         Big td has 4 placements.
         For example, it is now scanning to position (1, 2)

         1.In big td's head.
         0 0 0 0 0 0 0
         0 0 -1 1 1 0 0
         0 0 1 1 1 0 0

         2.In big td's left side.
         0 0 -1 1 1 0 0
         0 0 1 1 1 0 0
         0 0 0 0 0 0 0

         3.In big td's upper middle position.
         0 0 0 0 0 0 0
         0 -1 1 1 0 0 0
         0 1 1 1 0 0 0
         0 0 0 0 0 0 0

         4.In big td's middle or lower middle position.
         0 -1 1 1 0 0 0
         0 1 1 1 0 0 0
         0 0 0 0 0 0 0
         0 0 0 0 0 0 0
         */
        const operatorTd = [
            [], // insertBefore
            []  // increase colspan
        ]
        while (index < size[0] - 1) {
            index++;

            const insertBeforeThisTd = this.getTdByMatrix(index, colMatrix);
            const tdIsBig = this.cellType(insertBeforeThisTd) != this.CELL_NORMAL;

            if (!tdIsBig) {
                operatorTd[0].push(insertBeforeThisTd);
            } else {
                const firstMatrix = this.findFirstNumberPosition(index, colMatrix);
                // 1
                if (firstMatrix[0] == index && firstMatrix[1] == colMatrix) {
                    operatorTd[0].push(insertBeforeThisTd);
                } else if (firstMatrix[0] != index && firstMatrix[1] == colMatrix) {
                    // 2
                    const colSpan = this.getTdColSpan(insertBeforeThisTd);
                    const tdAfter = this.getTdByMatrix(index, colMatrix + colSpan);
                    operatorTd[0].push(tdAfter);
                } else if (firstMatrix[0] == index && firstMatrix[1] < colMatrix) {
                    // 3
                    operatorTd[1].push(insertBeforeThisTd);
                } else if (firstMatrix[0] < index && firstMatrix[1] < colMatrix) {
                    // 4
                    continue;
                } else {
                    console.error('Cases not treated.');
                }
            }
        }

        operatorTd[0].forEach((td) => {
            const tr = this.getRowByTd(this.dom, td);
            const trIndex = this.dom?.indexOf(tr)
            this.insertBeforeTd(tr, td, newInsertTd(trIndex, tdColIndex, tr));
        });

        operatorTd[1].forEach(td => {
            this.setTdSpan(td, null, 1, true);
        })

        this.refresh();
        return this;
    }
};
