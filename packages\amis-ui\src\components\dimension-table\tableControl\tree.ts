// @ts-nocheck

// 参考开源项目 https://github.com/zhjing1019/moreTreeTable/blob/master/src/components/MoreTreeTable.vue

const getAllColumns = (columns) => {
  const result = [];
  columns.forEach(column => {
    if (column.children) {
      result.push(column);
      result.push.apply(result, getAllColumns(column.children));
    } else {
      result.push(column);
    }
  });
  return result;
}

/**
 * 获得竖向表头的colspan和rowspan
 * 并且拿到竖向表头的数组
 */
export const convertToRowHeader = (originColumns)  => {
  let maxLevel = 1;
  const traverse = (column, parent) => {
    if (parent) {
      column.level = parent.level + 1;
      if (maxLevel < column.level) {
        maxLevel = column.level;
      }
    }
    if (column.children) {
      let rowspan = 0;
      column.children.forEach(subColumn => {
        traverse(subColumn, column);
        rowspan += subColumn.rowspan;
      });
      column.rowspan = rowspan;
    } else {
      column.rowspan = 1;
    }
  };

  originColumns.forEach(column => {
    column.level = 1;
    traverse(column);
  });
  const rows = [];
  for (let i = 0; i < maxLevel; i++) {
    rows.push([]);
  }
  const allColumns = getAllColumns(originColumns);

  allColumns.forEach(column => {
    if (!column.children) {
      column.colspan = maxLevel - column.level + 1;
    } else {
      column.colspan = 1;
    }
    rows[column.level - 1].push(column);
  });

  let count = 0;
  let colRow = [];
  let lastData = [];
  let cell = [];
  for (let i = 0; i < maxLevel; i++) {
    cell.push(null);
  }
  allColumns.forEach(column => {
    let newCell = JSON.parse(JSON.stringify(cell));
    if (column.level === maxLevel || !column.children) {
      colRow[count] = newCell;
      count++;
    }
  });

  let colRowIndex = 0;
  allColumns.forEach(column => {
    if (column.level === maxLevel || !column.children) {
      colRow[colRowIndex][column.level - 1] = column;
      lastData.push(column);
      colRowIndex++;
    } else {
      colRow[colRowIndex][column.level - 1] = column;
    }
  });

  const newColRow = colRow.map((col) => {
    return col.filter((item) => !!item)
  })

  return { columns: newColRow, lastData: lastData };
}

//计算单元格的colSpan
/**
 * 计算单元格的colsapn，用于合并列
 * 计算单元格所属的层级
 * @param {*} originColumns，是指第一层级的元素
 */
export const convertToColumnHeader = (originColumns) => {
  let maxLevel = 1;
  const traverse = (column, parent) => {
    if (parent) {
      //计算当前元素属于第几个层级
      column.level = parent.level + 1;
      if (maxLevel < column.level) {
        //计算最大层级
        maxLevel = column.level;
      }
    }
    if (column.children) {
      let colspan = 0;
      column.children.forEach(subColumn => {
        //进行递归
        traverse(subColumn, column);
        colspan += subColumn.colspan;
      });
      column.colspan = colspan;
    } else {
      column.colspan = 1;
    }
  };

  originColumns.forEach(column => {
    column.level = 1;
    traverse(column);
  });
  const rows = [];
  let lastData = [];
  for (let i = 0; i < maxLevel; i++) {
    rows.push([]);
  }
  const allColumns = getAllColumns(originColumns);
  allColumns.forEach(column => {
    if (!column.children) {
      column.rowspan = maxLevel - column.level + 1;
      lastData.push(column);
    } else {
      column.rowspan = 1;
    }
    rows[column.level - 1].push(column);
  });
  return { rows: rows, lastData: lastData };
}

/**
 * 数组转树形结构
 * @param {array} arr 被转换的数组
 * @param {number|string} root 根节点（最外层节点）的 id
 * @return array
 */
export const arrayToTree = (arr, root) => {
  const result = [] // 用于存放结果
  const map = {} // 用于存放 list 下的节点

  // 1. 遍历 arr，将 arr 下的所有节点使用 id 作为索引存入到 map
  for (const item of arr) {
    map[item.id] = item // 浅拷贝（存储对 item 的引用）
  }

  // 2. 再次遍历，将根节点放入最外层，子节点放入父节点
  for (const item of arr) {
    // 3. 获取节点的 id 和 父 id
    const { id, parentId = '' } = item // ES6 解构赋值
    // 4. 如果是根节点，存入 result
    if (parentId === root) {
      result.push(map[id])
    } else if (map[parentId]) {
      // 5. 反之，存入到父节点
      map[parentId].children ? map[parentId].children.push(map[id]) : (map[parentId].children = [map[id]])
    }
  }

  // 将结果返回
  return result
}
