/**
 * @file Icon
 * @description
 * <AUTHOR>
 */
import cxClass from 'classnames';
import isObject from 'lodash/isObject';
import React,{ createElement } from 'react';
import ArrowsRotateIcon from '../icons/arrows-rotate-solid.svg';
import BackIcon from '../icons/back.svg';
import CalendarIcon from '../icons/calendar.svg';
import CheckIcon from '../icons/check.svg';
import ClockIcon from '../icons/clock.svg';
import CloseSmallIcon from '../icons/close-small.svg';
import CloseTransParentIcon from '../icons/close-transparent.svg';
import CloseIcon from '../icons/close.svg';
import CloudUploadIcon from '../icons/cloud-upload.svg';
import ColmunsIcon from '../icons/columns.svg';
import DeskEmptyIcon from '../icons/desk-empty.svg';
import DownloadIcon from '../icons/download.svg';
import Download2Icon from '../icons/download2.svg';
import DragBarIcon from '../icons/drag-bar.svg';
import DragClose from '../icons/drag-close.svg';
import EditIcon from '../icons/edit.svg';
import EnterIcon from '../icons/enter.svg';
import ExchangeIcon from '../icons/exchange.svg';
import FailIcon from '../icons/fail.svg';
import FileRegularIcon from '../icons/file-regular.svg';
import FileIcon from '../icons/file.svg';
import PictureIcon from '../icons/image-regular.svg';
import ImageIcon from '../icons/image.svg';
import InfoIcon from '../icons/info.svg';
import LeftArrowIcon from '../icons/left-arrow.svg';
import LocationIcon from '../icons/location.svg';
import MinusIcon from '../icons/minus.svg';
import MoveIcon from '../icons/move.svg';
import MuteIcon from '../icons/mute.svg';
import PauseIcon from '../icons/pause.svg';
import PencilIcon from '../icons/pencil.svg';
import PlayIcon from '../icons/play.svg';
import PlusIcon from '../icons/plus.svg';
import ReDoIcon from '../icons/redo.svg';
import RefreshIcon from '../icons/refresh.svg';
import ReloadIcon from '../icons/reload.svg';
import RemoveIcon from '../icons/remove.svg';
import RetryIcon from '../icons/retry.svg';
import RightArrowIcon from '../icons/right-arrow.svg';
import Exchangev2Icon from '../icons/right-left-solid.svg';
import SearchIcon from '../icons/search.svg';
import StatusCloseIcon from '../icons/status-close.svg';
import SubPlusIcon from '../icons/sub-plus.svg';
import SuccessIcon from '../icons/success.svg';
import TreeDownIcon from '../icons/tree-down.svg';
import UnDoIcon from '../icons/undo.svg';
import UploadIcon from '../icons/upload.svg';
import ViewIcon from '../icons/view.svg';
import VolumeIcon from '../icons/volume.svg';

import AlertDanger from '../icons/alert-danger.svg';
import AlertInfo from '../icons/alert-info.svg';
import AlertSuccess from '../icons/alert-success.svg';
import AlertWarning from '../icons/alert-warning.svg';
import CaretIcon from '../icons/caret.svg';
import ColumnFilterIcon from '../icons/column-filter.svg';
import CompressAltIcon from '../icons/compress-alt.svg';
import CopyIcon from '../icons/copy.svg';
import DateIcon from '../icons/date.svg';
import Department from '../icons/department.svg';
import Detail from '../icons/detail.svg';
import Disabled from '../icons/disabled.svg';
import Disabledv2 from '../icons/disabledv2.svg';
import DotIcon from '../icons/dot.svg';
import DownArrowBoldIcon from '../icons/down-arrow-bold.svg';
import DownIcon from '../icons/down.svg';
import EllipsisVIcon from '../icons/ellipsis-v.svg';
import Enable from '../icons/enable.svg';
import Enablev2 from '../icons/enablev2.svg';
import ExpandAltIcon from '../icons/expand-alt.svg';
import Export from '../icons/export.svg';
import SettingIcon2 from '../icons/extra-configuration.svg';
import ExtraConfiguration from '../icons/extra-configurationv2.svg';
import FilterIcon from '../icons/filter.svg';
import FolderIcon from '../icons/folder.svg';
import FunctionIcon from '../icons/function.svg';
import HomeIcon from '../icons/home.svg';
import InfoCircleIcon from '../icons/info-circle.svg';
import InputClearIcon from '../icons/input-clear.svg';
import { default as Invisible,default as InvisibleIcon } from '../icons/invisible.svg';
import LoadingOutline from '../icons/loading-outline.svg';
import MenuIcon from '../icons/menu.svg';
import NewEdit from '../icons/new-edit.svg';
import PlusCicleIcon from '../icons/plus-cicle.svg';
import PlusFineIcon from '../icons/plus-fine.svg';
import Post from '../icons/post.svg';
import QuestionMarkIcon from '../icons/question-mark.svg';
import QuestionIcon from '../icons/question.svg';
import RightArrowBoldIcon from '../icons/right-arrow-bold.svg';
import RightDoubleArrowIcon from '../icons/right-double-arrow.svg';
import Role from '../icons/role.svg';
import RotateLeft from '../icons/rotate-left.svg';
import RotateRight from '../icons/rotate-right.svg';
import ScaleOrigin from '../icons/scale-origin.svg';
import ScheduleIcon from '../icons/schedule.svg';
import SettingIcon from '../icons/setting.svg';
import SliderHandleIcon from '../icons/slider-handle-icon.svg';
import SortAscIcon from '../icons/sort-asc.svg';
import SortDefaultIcon from '../icons/sort-default.svg';
import SortDescIcon from '../icons/sort-desc.svg';
import StarRegular from '../icons/star-regular.svg';
import StarSolid from '../icons/star-solid.svg';
import Star from '../icons/star.svg';
import StepsFinsh from '../icons/steps-finsh.svg';
import TransparentIcon from '../icons/transparent.svg';
import TrashIcon from '../icons/trash.svg';
import UserRemove from '../icons/user-remove.svg';
import WarningMarkIcon from '../icons/warning-mark.svg';
import WarningIcon from '../icons/warning.svg';
import WindowRestoreIcon from '../icons/window-restore.svg';
import ZoomInIcon from '../icons/zoom-in.svg';
import ZoomOutIcon from '../icons/zoom-out.svg';

import Authorization from '../icons/authorization.svg';
import RemarkIcon from '../icons/remark.svg';
import TableEmptyIcon from '../icons/table-empty.svg';
import ThumbsUpRegular from '../icons/thumbs-up-regular.svg';
import ThumbsUpSolid from '../icons/thumbs-up-solid.svg';
import WorkOrder from '../icons/work-order.svg';

import CircleMessageIcon from '../icons/circle-message.svg';
import CirclePhoneDownIcon from '../icons/circle-phone-down.svg';
import CirclePhoneIcon from '../icons/circle-phone.svg';
import RunningPhoneIcon from '../icons/running-phone.svg';
import SetBottomIcon from '../icons/set-bottom.svg';
import SetTopIcon from '../icons/set-top.svg';
import HotIcon from '../icons/hot.svg';
import PmEdit from '../icons/pm-edit.svg';

// 兼容原来的用法，后续不直接试用。

export const closeIcon = <CloseIcon />;
export const unDoIcon = <UnDoIcon />;
export const reDoIcon = <ReDoIcon />;
export const enterIcon = <EnterIcon />;
export const volumeIcon = <VolumeIcon />;
export const muteIcon = <MuteIcon />;
export const playIcon = <PlayIcon />;
export const pauseIcon = <PauseIcon />;
export const leftArrowIcon = <LeftArrowIcon />;
export const rightArrowIcon = <RightArrowIcon />;
const iconFactory: {
  [propName: string]: React.ElementType<{}>;
} = {};

export function getIcon(key: string) {
  return iconFactory[key];
}

export function hasIcon(iconName: string) {
  return !!getIcon(iconName);
}

export function registerIcon(key: string, component: React.ElementType<{}>) {
  iconFactory[key] = component;
}

registerIcon('pm-edit', PmEdit);
registerIcon('enable', Enable);
registerIcon('disabled', Disabled);
/** 启用/禁用：默认色系 */
registerIcon('enabled', Enablev2);
registerIcon('unenabled', Disabledv2);

registerIcon('detail', Detail);
registerIcon('export', Export);
registerIcon('drag-close', DragClose);
registerIcon('close', CloseIcon);
registerIcon('close-transparent', CloseTransParentIcon);
registerIcon('close-small', CloseSmallIcon);
registerIcon('status-close', StatusCloseIcon);
registerIcon('undo', UnDoIcon);
registerIcon('redo', ReDoIcon);
registerIcon('enter', EnterIcon);
registerIcon('volume', VolumeIcon);
registerIcon('mute', MuteIcon);
registerIcon('play', PlayIcon);
registerIcon('pause', PauseIcon);
registerIcon('left-arrow', LeftArrowIcon);
registerIcon('right-arrow', RightArrowIcon);
registerIcon('prev', LeftArrowIcon);
registerIcon('next', RightArrowIcon);
registerIcon('check', CheckIcon);
registerIcon('plus', PlusIcon);
registerIcon('sub-plus', SubPlusIcon);
registerIcon('add', PlusIcon);
registerIcon('minus', MinusIcon);
registerIcon('pencil', PencilIcon);
registerIcon('view', ViewIcon);
registerIcon('remove', RemoveIcon);
registerIcon('retry', RetryIcon);
registerIcon('upload', UploadIcon);
registerIcon('download', DownloadIcon);
registerIcon('download2', Download2Icon);
registerIcon('file', FileIcon);
registerIcon('file-regular', FileRegularIcon);
registerIcon('success', SuccessIcon);
registerIcon('fail', FailIcon);
registerIcon('warning', WarningIcon);
registerIcon('warning-mark', WarningMarkIcon);
registerIcon('search', SearchIcon);
registerIcon('back', BackIcon);
registerIcon('move', MoveIcon);
registerIcon('info', InfoIcon);
registerIcon('info-circle', InfoCircleIcon);
registerIcon('location', LocationIcon);
registerIcon('drag-bar', DragBarIcon);
registerIcon('reload', ReloadIcon);
registerIcon('exchange', ExchangeIcon);
registerIcon('exchange-move', Exchangev2Icon);
registerIcon('columns', ColmunsIcon);
registerIcon('calendar', CalendarIcon);
registerIcon('clock', ClockIcon);
registerIcon('copy', CopyIcon);
registerIcon('filter', FilterIcon);
registerIcon('column-filter', ColumnFilterIcon);
registerIcon('caret', CaretIcon);
registerIcon('right-arrow-bold', RightArrowBoldIcon);
registerIcon('down-arrow-bold', DownArrowBoldIcon);
registerIcon('zoom-in', ZoomInIcon);
registerIcon('zoom-out', ZoomOutIcon);
registerIcon('question', QuestionIcon);
registerIcon('question-mark', QuestionMarkIcon);
registerIcon('window-restore', WindowRestoreIcon);
registerIcon('schedule', ScheduleIcon);
registerIcon('home', HomeIcon);
registerIcon('folder', FolderIcon);
registerIcon('sort-default', SortDefaultIcon);
registerIcon('sort-asc', SortAscIcon);
registerIcon('sort-desc', SortDescIcon);
registerIcon('setting', SettingIcon);
registerIcon('extra-configuration', SettingIcon2);
registerIcon('configuration', ExtraConfiguration);
registerIcon('plus-cicle', PlusCicleIcon);
registerIcon('ellipsis-v', EllipsisVIcon);
registerIcon('expand-alt', ExpandAltIcon);
registerIcon('compress-alt', CompressAltIcon);
registerIcon('transparent', TransparentIcon);
registerIcon('loading-outline', LoadingOutline);
registerIcon('star', Star);
registerIcon('star-regular', StarRegular);
registerIcon('star-solid', StarSolid);
registerIcon('alert-success', AlertSuccess);
registerIcon('alert-info', AlertInfo);
registerIcon('alert-warning', AlertWarning);
registerIcon('alert-danger', AlertDanger);
registerIcon('alert-fail', AlertDanger);
registerIcon('tree-down', TreeDownIcon);
registerIcon('function', FunctionIcon);
registerIcon('input-clear', InputClearIcon);
registerIcon('slider-handle', SliderHandleIcon);
registerIcon('cloud-upload', CloudUploadIcon);
registerIcon('image', ImageIcon);
registerIcon('picture', PictureIcon);
registerIcon('refresh', RefreshIcon);
registerIcon('trash', TrashIcon);
registerIcon('menu', MenuIcon);
registerIcon('user-remove', UserRemove);
registerIcon('role', Role);
registerIcon('department', Department);
registerIcon('post', Post);
registerIcon('dot', DotIcon);
registerIcon('drag', DragBarIcon);
registerIcon('edit', EditIcon);
registerIcon('desk-empty', DeskEmptyIcon);
registerIcon('invisible', Invisible);
registerIcon('plus-fine', PlusFineIcon);
registerIcon('steps-finsh', StepsFinsh);
registerIcon('date', DateIcon);
registerIcon('remove', RemoveIcon);
registerIcon('invisible', InvisibleIcon);
registerIcon('down', DownIcon);
registerIcon('right-double-arrow', RightDoubleArrowIcon);
registerIcon('new-edit', NewEdit);
registerIcon('rotate-left', RotateLeft);
registerIcon('rotate-right', RotateRight);
registerIcon('scale-origin', ScaleOrigin);
registerIcon('table-empty', TableEmptyIcon);
registerIcon('authorization', Authorization);
registerIcon('work-order', WorkOrder);
registerIcon('remark', RemarkIcon);
registerIcon('thumbs-up', ThumbsUpRegular);
registerIcon('thumbs-up-solid', ThumbsUpSolid);
registerIcon('arrow-rotate', ArrowsRotateIcon);

registerIcon('circle-phone', CirclePhoneIcon);
registerIcon('circle-phone-down', CirclePhoneDownIcon);
registerIcon('circle-message', CircleMessageIcon);
registerIcon('set-top', SetTopIcon);
registerIcon('set-bottom', SetBottomIcon);
registerIcon('running-phone', RunningPhoneIcon);
registerIcon('hot', HotIcon);
export interface IconCheckedSchema {
  id: string;
  name?: string;
  svg?: string;
}

export interface IconCheckedSchemaNew {
  type: 'icon';
  icon: IconCheckedSchema;
}

export function Icon({
  icon,
  className,
  classPrefix = '',
  classNameProp,
  iconContent,
  vendor,
  cx: iconCx,
  onClick,
  onMouseEnter,
  onMouseLeave,
  onMouseOver,
  onMouseOut,
  onMouseDown,
  onMouseUp,
  onMouseMove,
  onBlur,
  onFocus,
  onTouchStart,
  onTouchMove,
  onTouchEnd,
  onTouchCancel,
  style,
}: {
  icon: string;
  iconContent?: string;
} & React.ComponentProps<any>) {
  let cx = iconCx || cxClass;

  if (typeof jest !== 'undefined' && icon) {
    iconContent = '';
  }

  if (!icon) {
    return null;
  }

  // 支持的事件
  let events: any = {
    onClick,
    onMouseEnter,
    onMouseLeave,
    onMouseOver,
    onMouseOut,
    onMouseDown,
    onMouseUp,
    onMouseMove,
    onBlur,
    onFocus,
    onTouchStart,
    onTouchMove,
    onTouchEnd,
    onTouchCancel
  };

  // 直接的icon dom
  if (React.isValidElement(icon)) {
    return React.cloneElement(icon, {
      ...events,
      ...((icon.props as any) || {}),
      className: cxClass(
        cx(className, classNameProp),
        (icon.props as any).className
      ),
      style
    });
  }

  if (iconContent) {
    // 从css变量中获取icon
    const refFn = function (dom: any) {
      if (dom) {
        const domStyle = getComputedStyle(dom);
        const svgStr = domStyle.getPropertyValue('content');
        const svg = /(<svg.*<\/svg>)/.exec(svgStr);

        if (svg) {
          const svgHTML = svg[0].replace(/\\"/g, '"');
          if (dom.svgHTMLClone !== svgHTML) {
            dom.innerHTML = svgHTML;
            // 存储svg，不直接用innerHTML是防止<circle />渲染后变成<circle></circle>的情况
            dom.svgHTMLClone = svgHTML;
            dom.style.display = '';
          }
        }
      }
    };

    return (
      <div
        {...events}
        className={cx(iconContent, className, classNameProp)}
        ref={refFn}
        style={style}
      ></div>
    );
  }

  // 获取注册的icon
  const Component = getIcon(icon);
  if (Component) {
    return (
      <Component
        {...events}
        className={cx(className, `icon-${icon}`, classNameProp)}
        // @ts-ignore
        icon={icon}
        style={style}
      />
    );
  }

  // 符合schema的icon
  if (
    isObject(icon) &&
    (icon as IconCheckedSchemaNew).type === 'icon' &&
    (icon as IconCheckedSchemaNew).icon
  ) {
    icon = (icon as IconCheckedSchemaNew).icon;
  }

  // icon是引用svg的情况
  if (
    isObject(icon) &&
    typeof (icon as IconCheckedSchema).id === 'string' &&
    (icon as IconCheckedSchema).id.startsWith('svg-')
  ) {
    const svg = icon as IconCheckedSchema;
    const id = `${svg.id.replace(/^svg-/, '')}`;
    if (!document.getElementById(id)) {
      // 如果svg symbol不存在，则尝试将svg字符串赋值给icon，走svg字符串的逻辑
      icon = svg.svg?.replace(/'/g, '');
    } else {
      return (
        <svg
          {...events}
          className={cx('icon', 'icon-object', className, classNameProp)}
          style={style}
        >
          <use xlinkHref={'#' + id}></use>
        </svg>
      );
    }
  }

  // 直接传入svg字符串
  if (typeof icon === 'string' && icon.startsWith('<svg')) {
    const svgStr = /<svg .*?>(.*?)<\/svg>/.exec(icon);
    const viewBox = /viewBox="(.*?)"/.exec(icon);
    const svgHTML = createElement('svg', {
      ...events,
      className: cx('icon', className, classNameProp),
      style,
      dangerouslySetInnerHTML: {__html: svgStr ? svgStr[1] : ''},
      viewBox: viewBox?.[1] || '0 0 16 16'
    });
    return svgHTML;
  }

  // icon是链接
  const isURLIcon = typeof icon === 'string' && icon?.indexOf('.') !== -1;
  if (isURLIcon) {
    return (
      <img
        {...events}
        className={cx(`${classPrefix}Icon`, className, classNameProp)}
        src={icon}
        style={style}
      />
    );
  }

  // icon是普通字符串
  const isIconfont = typeof icon === 'string';

  let iconPrefix = '';
  if (vendor === 'iconfont') {
    iconPrefix = `iconfont icon-${icon}`;
  } else if (vendor === 'fa') {
    //默认是fontawesome v4，兼容之前配置
    iconPrefix = `${vendor} ${vendor}-${icon}`;
  } else {
    // 如果vendor为空，则不设置前缀,这样可以支持fontawesome v5、fontawesome v6或者其他框架
    iconPrefix = icon;
  }

  if (isIconfont) {
    return (
      <i
        {...events}
        className={cx(icon, className, classNameProp, iconPrefix)}
        style={style}
      />
    );
  }

  // 没有合适的图标
  return <span className="text-danger">没有 icon {icon}</span>;
}

export {
InputClearIcon,
CloseIcon,
UnDoIcon,
ReDoIcon,
EnterIcon,
VolumeIcon,
MuteIcon,
PlayIcon,
PauseIcon,
ReloadIcon,
LeftArrowIcon,
RightArrowIcon,
CheckIcon,
PlusIcon,
SubPlusIcon,
MinusIcon,
PencilIcon,
FunctionIcon,
MenuIcon,
UserRemove,
Role,
Department,
Post,
RightDoubleArrowIcon,
DownArrowBoldIcon
};
