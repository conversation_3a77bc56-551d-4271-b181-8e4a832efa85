import {registerLocale as register} from 'amis-core';

register('en-US', {
  'Action.countDown': 'Wait for ${timeLeft}s',
  'Alert.info': 'System Info',
  'App.home': 'Home',
  'App.navigation': 'Navigation',
  'asc': 'Asc',
  'Calendar.datepicker': 'Date Picker',
  'Calendar.yearmonth': 'YYYY MM',
  'Calendar.year': 'YYYY',
  'Calendar.begin': 'begin',
  'Calendar.end': 'end',
  'Calendar.beginAndEnd': 'b/e',
  'Calendar.toast': 'Out of date range',
  'Calendar.startPick': 'Select start time',
  'Calendar.endPick': 'Select end time',
  'cancel': 'Cancel',
  'Card.dragTip': 'Drag top button to sort',
  'Card.toggleDrag': 'Toggle drag to sort',
  'City.street': 'Enter street info',
  'clear': 'Clear',
  'more': 'More',
  'ColorPicker.placeholder': 'Select color',
  'SubForm.add': 'New',
  'add': 'New',
  'Combo.dragDropSort': 'Drag to sort',
  'Combo.invalidData': 'invalid data, please remove',
  'Combo.maxLength': 'Maximum item ia {{MaxLength}}}. Please delete some',
  'Combo.minLength': 'A least {{minLength}} item. Please add more',
  'Combo.type': 'Type',
  'copy': 'Copy',
  'confirm': 'Confirm',
  'Copyable.tip': 'Copy',
  'CRUD.reset': 'Reset',
  'CRUD.search': 'Search',
  'CRUD.exportCSV': 'Export CSV',
  'CRUD.exportExcel': 'Export Excel',
  'CRUD.fetchFailed': 'Fetch failed',
  'CRUD.filter': 'Filter',
  'CRUD.selected': 'selected {{total}} items: ',
  'CRUD.invalidArray': 'data.items must be an array',
  'CRUD.invalidData': 'data is empty',
  'CRUD.loadMore': 'Load more',
  'CRUD.loadMoreDisableTip': 'No data or last page',
  'CRUD.perPage': 'Per page',
  'CRUD.stat': '{{page}} of {{lastPage}} total: {{total}}.',
  'CRUD.paginationGoText': 'Go to',
  'CRUD.paginationPageText': 'page',
  'PaginationWrapper.placeholder': 'please config body',
  'Pagination.select': '{{count}} items/page',
  'Pagination.goto': 'goto',
  'Pagination.go': 'GO',
  'Pagination.totalPage': 'total {{lastPage}} pages',
  'Pagination.totalCount': 'total {{total}} items',
  'Date.titleYear': 'select year',
  'Date.titleMonth': 'select month and year',
  'Date.titleDate': 'select month, year and day',
  'Date.titleTime': 'select time',
  'Date.daysago': '{{days}} day(s) ago',
  'Date.dayslater': '{{days}} day(s) later',
  'Date.endOfMonth': 'last day of the month',
  'Date.endOfLastMonth': 'last day of last month',
  'Date.endOfWeek': 'Saturday',
  'Date.hoursago': '{{hours}} hour(s) ago',
  'Date.hourslater': '{{hours}} hour(s) later',
  'Date.invalid': 'Invalid date',
  'Number.invalid': 'Invalid number',
  'Date.monday': 'Monday',
  'Date.monthsago': '{{months}} month(s) ago',
  'Date.monthslater': '{{months}} month(s) later',
  'Date.now': 'Now',
  'Date.placeholder': 'Select Date',
  'Date.quartersago': '{{quarters}} quarter(s) ago',
  'Date.quarterslater': '{{quarters}} quarter(s) later',
  'Date.startOfLastMonth': 'First day of the last month',
  'Date.startOfLastQuarter': 'First day of the last quarter',
  'Date.startOfMonth': 'First day of the month',
  'Date.startOfQuarter': 'First day of the quarter',
  'Date.today': 'Today',
  'Date.tomorrow': 'Tomorrow',
  'Date.weeksago': '{{weeks}} week(s) ago',
  'Date.weekslater': '{{weeks}} week(s) later',
  'Date.yesterday': 'Yesterday',
  'dateformat.year': 'YYYY',
  'DateRange.daysago': 'Last {{days}} day(s)',
  'DateRange.dayslater': 'Within {{days}} day(s)',
  'DateRange.weeksago': 'Last {{weeks}} week(s)',
  'DateRange.weekslater': 'Within {{weeks}} week(s)',
  'DateRange.monthsago': 'Last {{months}} month(s)',
  'DateRange.monthslater': 'Within {{months}} month(s)',
  'DateRange.quartersago': 'Last {{quarters}} quarter(s)',
  'DateRange.quarterslater': 'Within {{quarters}} quarter(s)',
  'DateRange.yearsago': 'Last {{years}} year(s)',
  'DateRange.yearslater': 'Within {{years}} year(s)',
  'DateRange.hoursago': 'Last {{hours}} hour(s)',
  'DateRange.hourslater': 'Within {{hours}} hour(s)',
  'DateRange.1dayago': '1 day ago',
  'DateRange.1daysago': '1 day ago',
  'DateRange.7daysago': '7 days ago',
  'DateRange.30daysago': '30 days ago',
  'DateRange.90daysago': '90 days ago',
  'DateRange.lastMonth': 'Last month',
  'DateRange.lastWeek': 'Last week',
  'DateRange.lastQuarter': 'Last quarter',
  'DateRange.placeholder': 'Select a Date range',
  'YearRange.placeholder': 'Select a Year range',
  'DateRange.thisWeek': 'This Week',
  'DateRange.thisMonth': 'This month',
  'DateRange.thisQuarter': 'This quarter',
  'DateRange.thisYear': 'This year',
  'DateRange.lastYear': 'Last year',
  'DateRange.valueConcat': ' to ',
  'DateTime.placeholder': 'Select Datetime',
  'delete': 'Delete',
  'deleteConfirm': 'Are your sure to delete?',
  'deleteFailed': 'Delete failed',
  'desc': 'Desc',
  'Dialog.close': 'Close',
  'Dialog.title': 'Title',
  'Embed.invalidRoot': 'Invalid root selector',
  'Embed.downloading': 'Start downloading',
  'Embed.downloadError': 'Download error',
  'Excel.placeholder': `Drag 'n' drop excel here, or click to select`,
  'Excel.parsed': '{{filename}} Parsed',
  'fetchFailed': 'Fetch api failed',
  'File.continueAdd': 'Continue add',
  'File.downloadTpl': 'Download template',
  'File.download': 'Download file',
  'File.dragDrop': `Drag 'n' drop some files here or`,
  'File.clickUpload': `click here to upload`,
  'File.helpText': 'Help documentation',
  'File.errorRetry': 'File upload failed, please try again',
  'File.failed': 'Failed files.',
  'File.invalidType': '{{files}} does not match type `{{accept}}`',
  'File.maxLength': 'The maximum limit is {{maxLength}}',
  'File.maxSize':
    '{{filename}} you selected exceeds the maximum limit of {{maxSize}}',
  'File.pause': 'Pause uplaod',
  'File.repick': 'Repick',
  'File.result':
    'Successfully uploaded {{uploaded}} files, failed to upload {{failed}} files',
  'File.retry': 'Retry',
  'File.sizeLimit': 'The maximum file size is {{maxSize}}',
  'File.start': 'Start upload',
  'File.upload': 'Upload',
  'Image.upload': 'Upload image',
  'File.uploadFailed': 'return data of udpload api is empty',
  'File.uploading': 'Uploading',
  'FormItem.autoFillLoadFailed': 'return data of autoUpdate api is error',
  'FormItem.autoFillSuggest': 'Reference data entry',
  'Form.loadOptionsFailed': 'Failed to load options because: {{reason}}',
  'Form.submit': 'Submit',
  'Form.title': 'Form',
  'Form.unique': 'Current value is not unique',
  'Form.validateFailed': 'Form input validation failed',
  'Form.nestedError': 'Form cannot appear as a descendant of form',
  'Iframe.invalid': 'Invalid iframe url',
  'Iframe.invalidProtocol': 'Can not use http url iframe in https',
  'Image.configError': 'Can only set one of crop or multiple',
  'Image.crop': 'Crop image',
  'Image.dragDrop': `Drag 'n' drop some photos here`,
  'Image.height': 'height: {{height}}px',
  'Image.limitMax': 'Minimum image size is {{info}}',
  'Image.limitMin': 'Maximum image size is {{info}}',
  'Image.limitRatio': 'Please upload image with the size ratio of {{ration}}',
  'Image.pasteTip': 'You can paste image from the clipboard',
  'Image.placeholder': 'Click to select image or drag into this area',
  'Image.size': 'size: ({{width}}px x {{height}}px)',
  'Image.sizeNotEqual':
    'The image you selected does not meet the size requirements {{info}}',
  'Image.width': 'width: {{width}}px',
  'Image.zoomIn': 'Zoom In',
  'Pdf.viewFullText': 'view full text',
  'Pdf.view': 'view',
  'Log.mustHaveSource': 'Must have source in config',
  'Log.showLineNumber': 'Display line number',
  'Log.notShowLineNumber': 'Hide line number',
  'Log.expand': 'Expand Operation',
  'Log.collapse': 'Collapse Operation',
  'link': 'Link',
  'loading': 'Loading',
  'LocationPicker.placeholder': 'Pick location',
  'Month.placeholder': 'Select a month',
  'Nav.sourceError': 'Fetch link error',
  'networkError': 'Network error or missing CORS configuration',
  'noResult': 'No Result',
  'NumberInput.placeholder': 'Please enter a number',
  'Options.addPlaceholder': 'Please enter a name',
  'Options.deleteAPI': 'Must have deleteAPI',
  'Options.editLabel': 'Edit {{label}}',
  'Options.label': 'option',
  'Options.createFailed': 'create failed, please check',
  'placeholder.empty': '<Empty>',
  'placeholder.enter': 'Enter',
  'placeholder.noData': 'No data',
  'placeholder.noOption': 'No option',
  'placeholder.selectData': 'Select data',
  'Quarter.placeholder': 'Select a quarter',
  'Repeat.pre': 'Per',
  'reset': 'Reset',
  'save': 'Save',
  'saveFailed': 'Save failed',
  'saveSuccess': 'Saved successfully',
  'search': 'Search',
  'searchHistory': 'Search History',
  'searchResult': 'Search result',
  'Checkboxes.selectAll': 'Select/Deselect All',
  'Select.checkAll': 'Check all',
  'Select.clear': 'Clear',
  'Select.edit': 'Edit',
  'Select.upload': 'Re upload',
  'Select.clearAll': 'Clear all',
  'Select.createLabel': 'New option',
  'Select.placeholder': 'Select',
  'Select.searchPromptText': 'Input to search',
  'sort': 'Sort',
  'stop': 'Stop',
  'SubForm.button': 'Config',
  'SubForm.editDetail': 'Edit Detail',
  'SubForm.Detail': 'Detail',
  'System.error': 'System error',
  'System.notify': 'System notify',
  'System.copy': 'Content copied',
  'System.requestError': 'Request error: ',
  'System.requestErrorStatus': 'Request error, status code: ',
  'Table.addRow': 'Add row',
  'Table.subAddRow': 'Add sub row',
  'Table.copyRow': 'Copy row',
  'Table.columnsVisibility': 'Click to control columns visibility',
  'Table.deleteRow': 'Delete current row',
  'Table.discard': 'Discard',
  'Table.dragTip': 'Drag the button on the left to sort',
  'Table.editing': 'You should finished editing',
  'Table.editRow': 'Edit current row',
  'Table.modified':
    'There are {{modified}} records have been modified, you can:',
  'Table.moved': 'There are {{moved}} records changed the order, you can:',
  'Table.operation': 'Operation',
  'Table.playload': 'Must have playload',
  'Table.startSort': 'Click to start sorting',
  'Table.valueField': 'Must have valueField',
  'Table.index': 'Index',
  'Table.add': 'Add',
  'Table.addButtonDisabledTip':
    'In content editing, please submit first and then create a new option',
  'Table.toggleColumn': 'Display columns',
  'Table.searchFields': 'Set query fields',
  'Table.searchHeader': 'Filter condition：',
  'Tag.placeholder': 'No tag yet',
  'Tag.tip': 'Recently used tag',
  'Text.add': 'New {{label}}',
  'Time.placeholder': 'Select Time',
  'Transfer.configError': 'Config error',
  'Transfer.refreshIcon': 'Click to refresh',
  'Transfer.searchKeyword': 'Enter keywords',
  'Transfer.available': 'Available',
  'Transfer.selectd': 'Selected',
  'Transfer.selectFromLeft': 'Select from the left',
  'Tree.addChild': 'Add child',
  'Tree.addRoot': 'Add root node',
  'Tree.editNode': 'Edit this node',
  'Tree.removeNode': 'Remove this node',
  'Tree.root': 'Root',
  'validate.equals': 'value must be $1',
  'validate.equalsField': 'value must be $1',
  'validate.gt': 'Please enter a value greater than $1',
  'validate.isAlpha': 'Please enter letters',
  'validate.isAlphanumeric': 'Please enter letters or numbers',
  'validate.isEmail': 'Email format is incorrect',
  'validate.isFloat': 'Please enter a floating point value',
  'validate.isId': 'invalid ID Card number',
  'validate.isInt': 'Please enter an integer number',
  'validate.isJson': 'invalid JSON format.',
  'validate.isLength': 'Please make sure the length of contents is $1',
  'validate.isNumeric': 'Please enter a number',
  'validate.isPhoneNumber': 'invalid phone number',
  'validate.isRequired': 'This is required',
  'validate.isTelNumber': 'invalid telephone number',
  'validate.isUrl': 'Incorrect URL format',
  'validate.isUrlPath': 'You can only enter letters, numbers, `-` and`_` .',
  'validate.isWords': 'Please enter word',
  'validate.isZipcode': 'invalid postal address',
  'validate.lt': 'Please enter a value less than $1',
  'validate.matchRegexp':
    'The format is not correct. Please enter the content with the rule `${1| raw}`.',
  'validate.maximum': 'The input value exceeds the maximum value of $1',
  'Combo.arryMaxLength': 'Maximum item ia $1. Please delete some',
  'Combo.arrayMinLength': 'A least $0 item. Please add more',
  'validate.maxLength':
    'Please control the content length, do not enter more than $1 letters',
  'validate.minimum': 'The input value is lower than the minimum value of $1',
  'validate.minLength': 'Please enter more, at least $1 characters.',
  'validate.array.minLength': 'Please add more members, at least $1 members',
  'validate.array.maxLength':
    'Please control the number of members, which cannot exceed $1',
  'validate.isVariableName': 'Please enter a valid variable name',
  'validate.notEmptyString': 'Please do not enter all blank characters',
  'validate.isDateTimeSame':
    'The current date value is invalid, please enter the same date value as $1',
  'validate.isDateTimeBefore':
    'The current date value is invalid, please enter a date value before $1',
  'validate.isDateTimeAfter':
    'The current date value is invalid, please enter a date value after $1',
  'validate.isDateTimeSameOrBefore':
    'The current date value is invalid, please enter a date value that is the same as or before $1',
  'validate.isDateTimeSameOrAfter':
    'The current date value is invalid, please enter a date value that is the same as or after $1',
  'validate.isDateTimeBetween':
    'The current date value is invalid, please enter a date value between $1 and $2',
  'validate.isTimeSame':
    'The current time value is invalid, please enter the same time value as $1',
  'validate.isTimeBefore':
    'The current time value is invalid, please enter a time value before $1',
  'validate.isTimeAfter':
    'The current time value is invalid, please enter a time value after $1',
  'validate.isTimeSameOrBefore':
    'The current time value is invalid, please enter a time value that is the same as or before $1',
  'validate.isTimeSameOrAfter':
    'The current time value is invalid, please enter a time value that is the same as or after $1',
  'validate.isTimeBetween':
    'The current time value is invalid, please enter a time value between $1 and $2',
  'validateFailed': 'Validate failed',
  'Wizard.configError': 'Config error',
  'Wizard.finish': 'Finish',
  'Wizard.next': 'Next',
  'Wizard.prev': 'Prev',
  'Wizard.saveAndNext': 'Save & Next',
  'year-to-year': '{{from}} - {{to}}',
  'Year.placeholder': 'Select a Year',
  'reload': 'Reload',
  'rotate': 'Rotate',
  'rotate.left': 'Rotate left',
  'rotate.right': 'Rotate right',
  'zoomIn': 'Zoom in',
  'zoomOut': 'Zoom out',
  'scale.origin': 'Original scale',
  'Editor.fullscreen': 'full screen',
  'Editor.exitFullscreen': 'exit fullscreen mode',
  'Condition.not': 'not',
  'Condition.and': 'and',
  'Condition.or': 'or',
  'Condition.collapse': 'unfold',
  'Condition.add_cond': 'add condition',
  'Condition.add_cond_group': 'add condition group',
  'Condition.delete_cond_group': 'delete condition group',
  'Condition.equal': 'equal',
  'Condition.not_equal': 'not equal',
  'Condition.less': 'less',
  'Condition.less_or_equal': 'less or equal',
  'Condition.greater': 'greater',
  'Condition.greater_or_equal': 'greater or equal',
  'Condition.between': 'between',
  'Condition.not_between': 'not between',
  'Condition.is_empty': 'empty',
  'Condition.is_not_empty': 'not empty',
  'Condition.like': 'contains',
  'Condition.not_like': 'not contains',
  'Condition.starts_with': 'starts with',
  'Condition.ends_with': 'ends with',
  'Condition.select_equals': 'equals',
  'Condition.select_not_equals': 'not equal',
  'Condition.select_any_in': 'contains',
  'Condition.select_not_any_in': 'not contains',
  'Condition.placeholder': 'Please enter text',
  'Condition.cond_placeholder': 'select condition',
  'Condition.field_placeholder': 'select field',
  'Condition.blank': 'blank',
  'Condition.expression': 'expression',
  'Condition.formula_placeholder': 'Please enter a formula',
  'Condition.fun_error': 'Function is undefined',
  'Condition.configured': 'Configured',
  'InputTable.uniqueError': 'Column `{{label}}` unique validate failed',
  'Timeline.collapseText': 'Unfold',
  'Timeline.expandText': 'Fold',
  'collapse': 'Collapse',
  'expand': 'Expand',
  'FormulaEditor.btnLabel': 'Formula Edit',
  'FormulaEditor.title': 'Formula Editor',
  'FormulaEditor.run': 'Run',
  'FormulaEditor.sourceMode': 'Source Mode',
  'FormulaEditor.runContext': 'Run Context',
  'FormulaEditor.runResult': 'Run Result',
  'FormulaEditor.toggleAll': 'Expand All',
  'FormulaEditor.variable': 'Variable',
  'FormulaEditor.function': 'Function',
  'FormulaEditor.invalidData': 'invalid data, position or reason is {{err}}',
  'pullRefresh.pullingText': 'Pull down to refresh...',
  'pullRefresh.loosingText': 'Release to refresh...',
  'pullRefresh.loadingText': 'Loading...',
  'pullRefresh.successText': 'Loading success',
  'Picker.placeholder': 'Click icon on the right',
  'UserSelect.edit': 'edit',
  'UserSelect.save': 'preservation',
  'UserSelect.resultSort': 'Select result sort',
  'UserSelect.selected': 'Selected',
  'UserSelect.clear': 'empty',
  'UserSelect.sure': 'submit',
  'SchemaType.string': 'String',
  'SchemaType.number': 'Number',
  'SchemaType.integer': 'integer',
  'SchemaType.object': 'Object',
  'SchemaType.array': 'Array',
  'SchemaType.boolean': 'Boolean',
  'SchemaType.any': 'Any',
  'SchemaType.null': 'Null',
  'JSONSchema.title': 'Title',
  'JSONSchema.default': 'Default',
  'JSONSchema.description': 'Description',
  'JSONSchema.key': 'Key',
  'JSONSchema.array_items': 'Items',
  'JSONSchema.members': 'Members',
  'JSONSchema.key_duplicated': 'Key already exists',
  'JSONSchema.key_invalid': 'Key invalid',
  'TimeNow': 'Now',
  'IconSelect.all': 'All',
  'IconSelect.choice': 'Icon selection'
});
