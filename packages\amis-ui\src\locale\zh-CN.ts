import { registerLocale as register } from 'amis-core';

register('zh-CN', {
  'Action.countDown': '请等待 ${timeLeft} 秒',
  'Alert.info': '系统消息',
  'App.home': '首页',
  'App.navigation': '导航',
  'asc': '正序',
  'Calendar.datepicker': '日期选择',
  'Calendar.yearmonth': 'YYYY年MM月',
  'Calendar.year': 'YYYY年',
  'Calendar.begin': '开始',
  'Calendar.end': '结束',
  'Calendar.beginAndEnd': '始/终',
  'Calendar.toast': '超出日期范围',
  'Calendar.startPick': '开始时间',
  'Calendar.endPick': '结束时间',
  'Schedule': '日程',
  'Time': '时间',
  'Content': '内容',
  'cancel': '取消',
  'more': '更多',
  'Card.dragTip': '请拖动顶部的按钮进行排序',
  'Card.toggleDrag': '对卡片进行排序操作',
  'City.street': '请输入街道信息',
  'clear': '清空',
  'ColorPicker.placeholder': '请选择颜色',
  'SubForm.add': '新增一项',
  'add': '新增',
  'Combo.dragDropSort': '拖拽排序',
  'Combo.invalidData': '数值格式不正确',
  'Combo.maxLength': '组合表单超出{{maxLength}}个，请删除',
  'Combo.minLength': '组合表单数量不足{{minLength}}个，请添加更多',
  'Combo.type': '类型',
  'copy': '复制',
  'confirm': '确认',
  'Copyable.tip': '点击复制',
  'CRUD.reset': '重 置',
  'CRUD.search': '查 询',
  'CRUD.exportCSV': '导出 CSV',
  'CRUD.exportExcel': '导出 Excel',
  'CRUD.fetchFailed': '获取失败',
  'CRUD.filter': '筛选',
  'CRUD.selected': '已选{{total}}条：',
  'CRUD.invalidArray': 'data.items 必须是数组',
  'CRUD.invalidData': '返回数据格式不正确，data 没有数据',
  'CRUD.loadMore': '加载更多',
  'CRUD.loadMoreDisableTip': '无数据或最后一页',
  'CRUD.perPage': '每页显示',
  // 'CRUD.stat': '{{page}}/{{lastPage}} 总共：{{total}} 项',
  'CRUD.stat': '共 {{total}} 条',
  'CRUD.paginationGoText': '前往',
  'CRUD.paginationPageText': '页',
  'PaginationWrapper.placeholder': '请配置内容',
  'Pagination.select': '{{count}} 条/页',
  'Pagination.goto': '跳至',
  'Pagination.go': 'GO',
  'Pagination.totalPage': '共 {{lastPage}} 页',
  'Pagination.totalCount': '共 {{total}} 条',
  'Date.titleYear': '选择年',
  'Date.titleMonth': '选择年月',
  'Date.titleDate': '选择年月日',
  'Date.titleTime': '选择时间',
  'Date.daysago': '{{days}}天前',
  'Date.dayslater': '{{days}}天后',
  'Date.endOfMonth': '本月最后一天',
  'Date.endOfWeek': '周日',
  'Date.endOfLastMonth': '上月最后一天',
  'Date.hoursago': '{{hours}}小时前',
  'Date.hourslater': '{{hours}}小时后',
  'Date.invalid': '日期无效',
  'Number.invalid': '数字无效',
  'Date.monday': '本周一',
  'Date.monthsago': '{{months}}月前',
  'Date.monthslater': '{{months}}月后',
  'Date.now': '现在',
  'Date.placeholder': '请选择日期',
  'Date.quartersago': '{{quarters}}季度前',
  'Date.quarterslater': '{{quarters}}季度后',
  'Date.startOfLastMonth': '上个月第一天',
  'Date.startOfLastQuarter': '上个季度第一天',
  'Date.startOfMonth': '本月第一天',
  'Date.startOfQuarter': '本季度第一天',
  'Date.today': '今天',
  'Date.tomorrow': '明天',
  'Date.weeksago': '{{weeks}}周前',
  'Date.weekslater': '{{weeks}}周后',
  'Date.yesterday': '昨天',
  'dateformat.year': 'YYYY年',
  'DateRange.daysago': '最近{{days}}天',
  'DateRange.dayslater': '{{days}}天以内',
  'DateRange.weeksago': '最近{{weeks}}周',
  'DateRange.weekslater': '{{weeks}}周以内',
  'DateRange.monthsago': '最近{{months}}月',
  'DateRange.monthslater': '{{months}}月以内',
  'DateRange.quartersago': '最近{{quarters}}季度',
  'DateRange.quarterslater': '{{quarters}}季度以内',
  'DateRange.yearsago': '最近{{years}}年',
  'DateRange.yearslater': '{{years}}年以内',
  'DateRange.hoursago': '最近{{hours}}小时',
  'DateRange.hourslater': '{{hours}}小时以内',
  'DateRange.1daysago': '最近1天',
  'DateRange.1dayago': '最近1天',
  'DateRange.7daysago': '最近7天',
  'DateRange.30daysago': '最近30天',
  'DateRange.90daysago': '最近90天',
  'DateRange.lastWeek': '上周',
  'DateRange.lastMonth': '上个月',
  'DateRange.lastQuarter': '上个季度',
  'DateRange.placeholder': '请选择日期范围',
  'DateRange.thisWeek': '这个周',
  'DateRange.thisMonth': '这个月',
  'DateRange.thisQuarter': '这个季度',
  'DateRange.valueConcat': ' 至 ',
  'DateTime.placeholder': '请选择日期以及时间',
  'MonthRange.placeholder': '请选择月份范围',
  'QuarterRange.placeholder': '请选择季度范围',
  'YearRange.placeholder': '请选择年份范围',
  'DateRange.thisYear': '今年',
  'DateRange.lastYear': '去年',
  'delete': '删除',
  'deleteConfirm': '确认要删除？',
  'deleteFailed': '删除失败',
  'desc': '降序',
  'Dialog.close': '关闭',
  'Dialog.title': '弹框',
  'Embed.invalidRoot': '选择器不对，页面上没有此元素',
  'Embed.downloading': '文件开始下载',
  'Embed.downloadError': '文件下载失败',
  'Excel.placeholder': '拖拽 Excel 到这，或点击上传',
  'Excel.parsed': '已解析 {{filename}}',
  'fetchFailed': '初始化失败',
  'File.continueAdd': '继续添加',
  'File.downloadTpl': '下载模板',
  'File.download': '下载文件',
  'File.dragDrop': '将文件拖到此处，或',
  'File.clickUpload': '点击上传',
  'File.helpText': '帮助文档',
  'File.errorRetry': '文件上传失败请重试',
  'File.failed': '失败文件',
  'File.invalidType': '{{files}} 不符合类型的 {{accept}} 的设定，请仔细检查',
  'File.maxLength': '最多上传 {{maxLength}} 个文件',
  'File.maxSize':
    '{{filename}} 大小为 {{actualSize}} 超出了最大为 {{maxSize}} 的限制',
  'File.pause': '暂停上传',
  'File.repick': '重新选择',
  'File.result': '已成功上传 {{uploaded}} 个文件，{{failed}} 个文件上传失败，',
  'File.retry': '重试上传',
  'File.sizeLimit': '文件大小不超过 {{maxSize}}',
  'File.start': '开始上传',
  'File.upload': '文件上传',
  'Image.upload': '图片上传',
  'File.uploadFailed': '接口返回错误，请仔细检查',
  'File.uploading': '上传中...',
  'FormItem.autoFillLoadFailed': '接口返回错误，请仔细检查',
  'FormItem.autoFillSuggest': '参照录入数据',
  'Form.loadOptionsFailed': '加载选项失败，原因：{{reason}}',
  'Form.submit': '提交',
  'Form.title': '表单',
  'Form.unique': '当前值不唯一',
  'Form.validateFailed': '依赖的部分字段没有通过验证',
  'Form.nestedError': '表单不要直接嵌套在表单下面',
  'Iframe.invalid': 'iframe 地址不合法',
  'Iframe.invalidProtocol': '无法加载 http 协议的 iframe',
  'Image.configError': '图片多选配置和裁剪配置只能设置一个',
  'Image.crop': '裁剪图片',
  'Image.dragDrop': '将图片拖拽到此处',
  'Image.height': '高度 {{height}}px',
  'Image.limitRatio': '请上传尺寸比率为 {{ratio}} 的图片',
  'Image.pasteTip': '可以粘贴剪切板中的图片',
  'Image.placeholder': '点击选择图片或拖拽图片到这里',
  'Image.size': '尺寸（{{width}} x {{height}}）',
  'Image.sizeMax': '请上传不要大于{{info}}的图片',
  'Image.sizeMin': '请上传不要小于{{info}}的图片',
  'Image.sizeNotEqual': '请上传{{info}}的图片',
  'Image.width': '宽度 {{width}}px',
  'Image.zoomIn': '查看大图',
  'Image.limitMax': '上传图片大于{{info}},请检查图片尺寸',
  'Image.limitMin': '上传图片小于{{info}},请检查图片尺寸',
  'Pdf.viewFullText': '查看全文',
  'Pdf.view': '查看',
  'Log.mustHaveSource': '需要配置 Source',
  'Log.showLineNumber': '显示行数',
  'Log.notShowLineNumber': '不现实行数',
  'Log.expand': '展开工具栏',
  'Log.collapse': '收起工具栏',
  'link': '链接',
  'loading': '加载中',
  'LocationPicker.placeholder': '请选择位置',
  'Month.placeholder': '请选择月份',
  'Nav.sourceError': '获取链接错误',
  'networkError': '网络错误，可能是未配置跨域 CORS',
  'noResult': '未找到任何结果',
  'NumberInput.placeholder': '请输入数字',
  'Options.addPlaceholder': '请输入名称',
  'Options.deleteAPI': '必须设置 deleteAPI',
  'Options.editLabel': '编辑{{label}}',
  'Options.label': '选项',
  'Options.createFailed': '新增失败，请仔细检查',
  'placeholder.empty': '<空>',
  'placeholder.enter': '请输入',
  'placeholder.noData': '暂无数据',
  'placeholder.noOption': '暂无选项',
  'placeholder.selectData': '请先选择数据',
  'Quarter.placeholder': '请选择季度',
  'Repeat.pre': '每',
  'reset': '重置',
  'save': '保存',
  'saveFailed': '保存失败',
  'saveSuccess': '保存成功',
  'search': '搜索',
  'searchHistory': '搜索历史',
  'searchResult': '搜索结果',
  'stop': '停止',
  'Checkboxes.selectAll': '全选/不选',
  'Select.checkAll': '全选',
  'Select.clear': '移除',
  'Select.edit': '编辑',
  'Select.upload': '重新上传',
  'Select.clearAll': '移除所有',
  'Select.createLabel': '新增选项',
  'Select.placeholder': '请选择',
  'Select.searchPromptText': '搜索',
  'sort': '排序',
  'SubForm.button': '设置',
  'SubForm.editDetail': '编辑详情',
  'SubForm.Detail': '查看详情',
  'System.error': '系统错误',
  'System.notify': '系统消息',
  'System.copy': '内容已复制到粘贴板',
  'System.requestError': '接口报错：',
  'System.requestErrorStatus': '接口出错，状态码是：',
  'Table.addRow': '新增一行',
  'Table.subAddRow': '新增子级',
  'Table.copyRow': '复制一行',
  'Table.columnsVisibility': '点击选择显示列',
  'Table.deleteRow': '删除当前行',
  'Table.discard': '放弃',
  'Table.dragTip': '请拖动左边的按钮进行排序',
  'Table.editing': '请先处理表格编辑项',
  'Table.editRow': '编辑当前行',
  'Table.modified': '当前有 {{modified}} 条记录修改但没有提交，你可以：',
  'Table.moved': '当前有 {{moved}} 条记录修改了顺序但没有提交，你可以：',
  'Table.operation': '操作',
  'Table.playload': 'action 上请配置 payload, 否则不清楚要删除哪个',
  'Table.startSort': '点击开始排序',
  'Table.valueField': '请配置 valueField',
  'Table.index': '序号',
  'Table.add': '新增',
  'Table.addButtonDisabledTip': '内容编辑中，请先提交后新建选项',
  'Table.toggleColumn': '显示列',
  'Table.searchFields': '设置查询字段',
  'Table.collapse': '高级搜索',
  'Table.searchHeader': '筛选条件：',
  'Tag.placeholder': '请输入/选择标签',
  'Tag.tip': '最近使用的标签',
  'Text.add': '新增：{{label}}',
  'Time.placeholder': '请选择时间',
  'Transfer.configError': '配置错误，选项无法与左侧选项对应',
  'Transfer.refreshIcon': '点击刷新重新加载',
  'Transfer.searchKeyword': '请输入关键字',
  'Transfer.available': '可选项',
  'Transfer.selectd': '已选项',
  'Transfer.selectFromLeft': '请选择左侧数据',
  'Tree.addChild': '添加子节点',
  'Tree.addRoot': '添加一级节点',
  'Tree.editNode': '编辑该节点',
  'Tree.removeNode': '移除该节点',
  'Tree.root': '顶级',
  'validate.equals': '输入的数据与 $1 不一致',
  'validate.equalsField': '输入的数据与 $1 值不一致',
  'validate.gt': '请输入大于 $1 的值',
  'validate.isAlpha': '请输入字母',
  'validate.isAlphanumeric': '请输入字母或者数字',
  'validate.isEmail': 'Email 格式不正确',
  'validate.isFloat': '请输入浮点型数值',
  'validate.isId': '请输入合法的身份证号',
  'validate.isInt': '请输入整型数字',
  'validate.isJson': 'JSON 格式不正确',
  'validate.isLength': '请输入长度为 $1 的内容',
  'validate.isNumeric': '请输入数字',
  'validate.isPhoneNumber': '请输入合法的手机号码',
  'validate.isRequired': '这是必填项',
  'validate.isTelNumber': '请输入合法的电话号码',
  'validate.isUrl': 'URL 格式不正确',
  'validate.isUrlPath': '只能输入字母、数字、`-` 和 `_`.',
  'validate.isWords': '请输入单词',
  'validate.isZipcode': '请输入合法的邮编地址',
  'validate.lt': '请输入小于 $1 的值',
  'validate.matchRegexp': '格式不正确, 请输入符合规则为 `${1|raw}` 的内容。',
  'validate.maximum': '当前输入值超出最大值 $1',
  'validate.maxLength': '请控制内容长度, 不要输入 $1 个以上字符',
  'validate.array.maxLength': '请控制成员个数, 不能超过 $1 个',
  'validate.minimum': '当前输入值低于最小值 $1',
  'validate.minLength': '请输入更多的内容，至少输入 $1 个字符。',
  'validate.array.minLength': '请添加更多的成员，成员数至少 $1 个。',
  'validate.notEmptyString': '请不要全输入空白字符',
  'validate.isDateTimeSame': '当前日期值不合法，请输入和 $1 相同的日期值',
  'validate.isDateTimeBefore': '当前日期值不合法，请输入 $1 之前的日期值',
  'validate.isDateTimeAfter': '当前日期值不合法，请输入 $1 之后的日期值',
  'validate.isDateTimeSameOrBefore':
    '当前日期值不合法，请输入和 $1 相同或之前的日期值',
  'validate.isDateTimeSameOrAfter':
    '当前日期值不合法，请输入和 $1 相同或之后的日期值',
  'validate.isDateTimeBetween':
    '当前日期值不合法，请输入 $1 和 $2 之间的日期值',
  'validate.isTimeSame': '当前时间值不合法，请输入和 $1 相同的时间值',
  'validate.isTimeBefore': '当前时间值不合法，请输入 $1 之前的时间值',
  'validate.isTimeAfter': '当前时间值不合法，请输入 $1 之后的时间值',
  'validate.isTimeSameOrBefore':
    '当前时间值不合法，请输入和 $1 相同或之前的时间值',
  'validate.isTimeSameOrAfter':
    '当前时间值不合法，请输入和 $1 相同或之后的时间值',
  'validate.isTimeBetween': '当前时间值不合法，请输入 $1 和 $2 之间的时间值',
  'validate.isVariableName': '请输入合法的变量名',
  'validateFailed': '表单验证失败',
  'Wizard.configError': '配置错误',
  'Wizard.finish': '完成',
  'Wizard.next': '下一步',
  'Wizard.prev': '上一步',
  'Wizard.saveAndNext': '保存并下一步',
  'year-to-year': '{{from}} 年 - {{to}} 年',
  'Year.placeholder': '请选择年',
  'reload': '刷新',
  'rotate': '旋转',
  'rotate.left': '向左旋转',
  'rotate.right': '向右旋转',
  'zoomIn': '放大',
  'zoomOut': '缩小',
  'scale.origin': '原始尺寸',
  'Editor.fullscreen': '全屏',
  'Editor.exitFullscreen': '退出全屏',
  'Condition.ralation': '条件组间关系',
  'Condition.ralationWithItem': '条件间关系',
  'Condition.isEnclosed': '是否被括号包裹',
  'Condition.layoutType': '布局方式',
  'Condition.yes': '是',
  'Condition.no': '否',
  'Condition.vertical': '纵向',
  'Condition.horizontal': '横向',
  'Condition.not': '非',
  'Condition.and': '且',
  'Condition.or': '或',
  'Condition.collapse': '展开全部',
  'Condition.add_cond': '添加条件',
  'Condition.add_cond_group': '添加条件组',
  'Condition.delete_cond_group': '删除组',
  'Condition.copy_cond_group': '复制组',
  'Condition.group': '组',
  'Condition.equal': '等于',
  'Condition.not_equal': '不等于',
  'Condition.less': '小于',
  'Condition.less_or_equal': '小于或等于',
  'Condition.greater': '大于',
  'Condition.greater_or_equal': '大于或等于',
  'Condition.between': '属于范围',
  'Condition.not_between': '不属于范围',
  'Condition.is_empty': '为空',
  'Condition.is_not_empty': '不为空',
  'Condition.like': '模糊匹配',
  'Condition.not_like': '不匹配',
  'Condition.starts_with': '匹配开头',
  'Condition.ends_with': '匹配结尾',
  'Condition.select_equals': '等于',
  'Condition.select_not_equals': '不等于',
  'Condition.select_any_in': '包含',
  'Condition.select_not_any_in': '不包含',
  'Condition.placeholder': '请输入文本',
  'Condition.cond_placeholder': '请选择操作',
  'Condition.field_placeholder': '请选择字段',
  'Condition.blank': '空',
  'Condition.expression': '表达式',
  'Condition.formula_placeholder': '请输入公式',
  'Condition.fun_error': '方法未定义',
  'Condition.configured': '已配置',
  'InputTable.uniqueError': '列`{{label}}`没有通过唯一验证',
  'Timeline.collapseText': '收起',
  'Timeline.expandText': '展开',
  'collapse': '收起',
  'expand': '展开',
  'FormulaEditor.btnLabel': '公式编辑',
  'FormulaEditor.title': '公式编辑器',
  'FormulaEditor.run': '运行',
  'FormulaEditor.sourceMode': '源码模式',
  'FormulaEditor.runContext': '上下文数据',
  'FormulaEditor.runResult': '运行结果',
  'FormulaEditor.toggleAll': '展开全部',
  'FormulaEditor.variable': '变量',
  'FormulaEditor.function': '函数',
  'FormulaEditor.invalidData': '公式值校验错误，错误的位置/原因是 {{err}}',
  'pullRefresh.pullingText': '下拉即可刷新...',
  'pullRefresh.loosingText': '释放即可刷新...',
  'pullRefresh.loadingText': '加载中...',
  'pullRefresh.successText': '加载成功',
  'Picker.placeholder': '请点击右侧的图标',
  'UserSelect.edit': '编辑',
  'UserSelect.save': '保存',
  'UserSelect.resultSort': '选择结果排序',
  'UserSelect.selected': '已选',
  'UserSelect.clear': '清空',
  'UserSelect.sure': '确定',
  'SchemaType.string': '文本',
  'SchemaType.number': '数字',
  'SchemaType.integer': '整数',
  'SchemaType.object': '对象',
  'SchemaType.array': '数组',
  'SchemaType.boolean': '布尔',
  'SchemaType.null': 'Null',
  'SchemaType.any': '任意',
  'JSONSchema.title': '名称',
  'JSONSchema.default': '默认值',
  'JSONSchema.key': '字段名',
  'JSONSchema.description': '描述',
  'JSONSchema.add_prop': '添加属性',
  'JSONSchema.add_item': '添加项',
  'JSONSchema.array_items': '成员类型',
  'JSONSchema.members': '成员',
  'JSONSchema.key_duplicated': '字段名已经存在',
  'JSONSchema.key_invalid': '字段名格式错误',
  'Required': '必填',
  'TimeNow': '此刻',
  'IconSelect.all': '全部',
  'IconSelect.choice': '图标选择'
});
