{"name": "amis-utils", "version": "1.90.5", "description": "amis 相关工具模块", "main": "lib/index.js", "module": "esm/index.js", "types": "lib/index.d.ts", "scripts": {"build": "npm run clean-dist && cross-env NODE_ENV=production rollup -c", "lib": "npm run clean-dist && cross-env NODE_ENV=production IS_LIB=1 rollup -c", "clean-dist": "rimraf lib/** esm/** tsconfig.tsbuildinfo .rollup.cache/**", "declaration": "tsc --project tsconfig-for-declaration.json --allowJs --declaration --emitDeclarationOnly --declarationDir ./lib --rootDir ./src"}, "exports": {".": {"require": "./lib/index.js", "import": "./esm/index.js"}, "./lib/*": {"require": "./lib/*.js", "import": "./esm/*.js"}}, "keywords": ["amis", "utils", "env"], "files": ["lib", "esm"], "license": "MIT", "dependencies": {"amis-core": "^1.90.5", "axios": "*", "copy-to-clipboard": "^3.3.3", "lodash": "4.17.21", "resolve-pathname": "^3.0.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^22.0.2", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^14.1.0", "@rollup/plugin-typescript": "^8.3.4", "cross-env": "^7.0.3", "rimraf": "^3.0.2", "rollup": "^2.60.2", "rollup-plugin-license": "^2.6.0", "rollup-plugin-terser": "^7.0.2", "ts-loader": "^9.2.3", "ts-node": "^10.4.0", "typescript": "^4.3.5"}, "peerDependencies": {"amis-core": "*", "axios": "*"}, "publishConfig": {"access": "public", "registry": "http://registry.caijj.net/repository/npm-caijiajia/"}, "author": "<EMAIL>", "authors": ["<EMAIL>"]}