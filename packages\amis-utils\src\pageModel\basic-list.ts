import { CRUD_BASIC_CONFIG, CRUD_BASIC_CONFIG_V2, FOOTER_TOOLBAR } from './constants';
import { generateGroupForm, generateCollapseGroup } from './form';
import { generateNoPaddingWrapper } from './common'
/**
 * 基本列表页
 * 全局操作按钮最多展示5个，大于5个时，展示4个，其他进行折叠
 * TODO：按钮进行了visible控制时，要过滤visible: false 的按钮。
 * @param schema: SchemaNode
 * @returns
 */
export const getBasicListSchema = (schema: any) => {
  const { type, className = "", ...rest } = schema || {};

  const resultSchema = {
    type: "crud",
    className: `${className} pm-crud`,
    ...CRUD_BASIC_CONFIG,
    ...rest,
  };

  return resultSchema;
}

export const getBasicListSchemaV2 = (schema: any) => {
  const { type, className = "", ...rest } = schema || {};
const {subTable}=schema;
  const resultSchema = {
    type: "crud",
    className: `${subTable?'pm-curdTable-model':""} ${className} pm-crud`,
    ...CRUD_BASIC_CONFIG_V2,
    ...rest,
  };

  return resultSchema;
}

export const getBasicListSchemaV3 = (schema: any) => {
  const { type, topToolbar, className = "", ...rest } = schema || {};

  let renderBtns = [];
  let dropdownButton = [];
  if (topToolbar && Array.isArray(topToolbar)) {
    if (topToolbar.length > 5) {
      renderBtns = topToolbar.slice(0, 4);
      dropdownButton = topToolbar.slice(4);
    } else {
      renderBtns = topToolbar;
    }
  } else {
    renderBtns = [topToolbar]
  }

  const frontBtns = renderBtns.map((itm: any, index: number) => {
    if (index === 0 && itm.level === 'link') {
      return ({ ...itm, className: `pl-0 ${itm.className}` })
    }
    return itm
  });

  const resultSchema = {
    type: "crud",
    className: `${className} pm-crud`,
    ...CRUD_BASIC_CONFIG_V2,
    ...rest,
    topToolbar: [
      ...frontBtns,
      dropdownButton.length > 0 ? {
        type: "dropdown-button",
        label: "更多",
        buttons: dropdownButton
      } : null,

    ],
  };

  return resultSchema;
}

/**
 * 基础列表某列中存在多个标签，每个标签占一行
 *
 */
export const toolTipWrapperSchema = (schema: any) => {
   const {body} = schema
   return {
     ...schema,
     className: "pm-mb-1 pm-min-w-full",
     body:{
      ...body,
      className: "pm-min-w-full",
     }
   }
}

// 紧凑模式
export const getCompactModeListSchema = (schema: any) => {
  const { type, className = "", ...rest } = schema || {};

  const resultSchema = {
    type: "crud",
    className: `${className} compact-mode pm-crud`,
    ...CRUD_BASIC_CONFIG_V2,
    ...rest,
  };

  return resultSchema;
}
// 紧凑模式-V2
export const getCompactModeListSchemaV2 = (schema: any) => {
  const { type, className = "", ...rest } = schema || {};

  const resultSchema = {
    type: "crud",
    className: `${className} compact-mode-v2 pm-crud`,
    ...CRUD_BASIC_CONFIG_V2,
    ...rest,
  };

  return resultSchema;
}

/**
 * 基本列表页 - 无边距(内、外)
 */
export const getWithoutMarginsCRUDSchema = (schema: any, noPadding = true, noMargin = false) => {
  const { type, className = "", bodyClassName = "", ...rest } = schema || {};

  const resultSchema = {
    type: "crud",
    className: `${className} pm-crud`,
    ...CRUD_BASIC_CONFIG_V2,
    bodyClassName: `${noPadding ? 'no-padding ' : ''}${noMargin ? 'mb-0 ' : ''}${bodyClassName}`,
    ...rest,
  };

  return resultSchema;
}

/**
 * 基本列表页 - 无边距(内、外) - V2
 * */
export const getWithoutMarginsCRUDSchemaV2 = (schema: any, noPadding = true, noMargin = false) => {
  const { type, className = "", bodyClassName = "", ...rest } = schema || {};

  const resultSchema = {
    type: 'crud',
    ...CRUD_BASIC_CONFIG_V2,
    ...rest
  };

  /**
   * 配置noPadding时，首先判断是否有查询区域
   * 如果有查询区域 认为设置noPadding无效
   * */
  if (resultSchema.filter || (resultSchema.autoGenerateFilter && resultSchema.columns.find((c: any) => c.searchable))) {
    return {
      ...resultSchema,
      className: `${className} pm-crud`,
      noPadding: false,
      bodyClassName: `${noMargin ? 'mb-0 ' : ''}${bodyClassName}`,
    }
  }

  /**
   * topToolbar是最上面的元素
   * 去除topToolbar的上padding（左右的会根据noPadding默认支持）
   * 当存在autoGenerateFilter的时候 哪怕没有searchable 也会无法显示topToolbar 所以增加条件区域的判断
   * */
  if (resultSchema.topToolbar && !resultSchema.autoGenerateFilter && !resultSchema.filter) {
    return {
      ...resultSchema,
      className: `${className} pm-crud`,
      noPadding, // 用于去除topToolbar headerFilter headerToolbar 的左右padding
      topToolbarClassName: `${noPadding ? 'pm-pt-0 pm-pl-0' : ''}`,
      bodyClassName: `${noPadding ? 'pm-no-padding-but-top ' : ''}${noMargin ? 'mb-0 ' : ''}${bodyClassName}`,
    }
  }

  /**
   * headerFilter是最上面的元素
   * 去除headerFilter的上padding（左右的会根据noPadding默认支持）
   * */
  if (resultSchema.headerFilter) {
    return {
      ...resultSchema,
      className: `${className} pm-crud`,
      noPadding, // 用于去除headerFilter headerToolbar 的左右padding
      headerFilter: {
        ...schema.headerFilter,
        panelClassName: 'pm-pt-0' // 去除上 左右padding
      },
      bodyClassName: `${noPadding ? 'pm-no-padding-but-top ' : ''}${noMargin ? 'mb-0 ' : ''}${bodyClassName}`,
    }
  }

  /**
   * 配置了headerToolbar是最上面的元素
   * 去除headerToolbar的上padding（左右的会根据noPadding默认支持）
   * */
  if (resultSchema.headerToolbar || resultSchema.toolbar ||
    resultSchema.columnsTogglable === true ||
    (resultSchema.columnsTogglable === 'auto' && resultSchema.columns.length >= 5)
  ) {
    return {
      ...resultSchema,
      className: `${className} pm-crud`,
      noPadding, // 用于去除headerFilter headerToolbar 的左右padding
      bodyClassName: `${noPadding ? 'pm-no-padding-headerToolbar ' : ''}${noMargin ? 'mb-0 ' : ''}${bodyClassName}`,
    }
  }

  if (resultSchema.title) {
    return {
      ...resultSchema,
      className: `${className} pm-crud`,
      noPadding, // 用于去除headerFilter headerToolbar 的左右padding
      bodyClassName: `${noPadding ? 'pm-no-padding-title ' : ''}${noMargin ? 'mb-0 ' : ''}${bodyClassName}`,
    }
  }

  return {
    ...resultSchema,
    noPadding,
    className: `${className} pm-crud`,
    bodyClassName: `${noPadding ? 'no-padding ' : ''}${noMargin ? 'mb-0 ' : ''}${bodyClassName}`,
  };
};

/**
 * 带全局操作按钮的列表页
 * 全局操作按钮最多展示5个，大于5个时，展示4个，其他进行折叠
 * TODO：按钮进行了visible控制时，要过滤visible: false 的按钮。
 * @param schema: SchemaNode
 * @returns
 */
export const getGlobalActionButtonListSchema = (schema: any) => {
  const { type, className = "", topToolbar, ...rest } = schema || {};

  let renderBtns = [];
  let dropdownButton = [];
  if (topToolbar && Array.isArray(topToolbar)) {
    if (topToolbar.length > 5) {
      renderBtns = topToolbar.slice(0, 4);
      dropdownButton = topToolbar.slice(4);
    } else {
      renderBtns = topToolbar;
    }
  } else {
    renderBtns = [topToolbar]
  }

  const frontBtns = renderBtns.map((itm: any, index: number) => {
    if (index === 0 && itm.level === 'link') {
      return ({ ...itm, className: `pl-0 ${itm.className}` })
    }
    return itm
  });

  const resultSchema = {
    type: "crud",
    className: `${className} pm-crud`,
    ...CRUD_BASIC_CONFIG,
    ...rest,
    topToolbar: [
      ...frontBtns,
      dropdownButton.length > 0 ? {
        type: "dropdown-button",
        label: "更多",
        buttons: dropdownButton
      } : null
    ],
  };

  return resultSchema;
}


export const getGlobalActionButtonListSchemaV2 = (schema: any) => {
  const { type, className = "", topToolbar, ...rest } = schema || {};

  let renderBtns = [];
  let dropdownButton = [];
  if (topToolbar && Array.isArray(topToolbar)) {
    if (topToolbar.length > 5) {
      renderBtns = topToolbar.slice(0, 4);
      dropdownButton = topToolbar.slice(4);
    } else {
      renderBtns = topToolbar;
    }
  } else {
    renderBtns = [topToolbar]
  }

  const frontBtns = renderBtns.map((itm: any, index: number) => {
    if (index === 0 && itm.level === 'link') {
      return ({ ...itm, className: `pl-0 ${itm.className}` })
    }
    return itm
  });

  const resultSchema = {
    type: "crud",
    className: `${className} pm-crud`,
    ...CRUD_BASIC_CONFIG_V2,
    ...rest,
    topToolbar: [
      ...frontBtns,
      dropdownButton.length > 0 ? {
        type: "dropdown-button",
        label: "更多",
        buttons: dropdownButton
      } : null
    ],
  };

  return resultSchema;
}

// export const getButtonGroupWithBadgeSchema = (schema:any)=>{
//   const budgeOffset = {
//     'sm': [-8, 0],
//     'md': [-16, 0],
//     'lg': [-24,0]
//   }
//   if(schema && Array.isArray(schema)){
//     schema = schema?.map(config=>{
//       if(config.type === "button" ){
//         /**
//          * 配置了角标
//          */
//         if(config?.badge && Object.prototype.toString.call(config.badge) === '[object Object]'){
//           // 根据gapSize 设置偏移量
//           config.badge.offset = [-8,0]; // budgeOffset?.[config.gapSize || 'sm']
//           const {text} =config.badge
//           // 封顶值固定为99
//           if( (typeof text === 'number' || (text && !isNaN(Number(text))))){
//             config.badge.overflowCount = 99
//           }else {
//             config.badge.text = text.slice(0,3)
//           }
//         }
//         return {...config , className: `${config.className||''} pm-buttonGap-${config?.gapSize ||'sm'}`}
//       }else{
//         return config
//       }

//     })
//   }

//   return {
//     type:"container",
//     className:"pm-button-group-container",
//     body:schema
//   }

// }



export const getButtonGroupWithBadgeSchema = (schema: any) => {
  const lengthToWord = {
    1: "one", // 22
    2: "two", // 28
    3: "three" // 36
  }
  if (schema && Array.isArray(schema)) {
    schema = schema?.map(config => {
      if (config.type === "button") {
        /**
         * 配置了角标
         */
        if (config?.badge && Object.prototype.toString.call(config.badge) === '[object Object]') {
          const { text } = config.badge
          // 封顶值固定为99
          if ((typeof text === 'number' || (text && !isNaN(Number(text))))) {
            config.badge.overflowCount = 99
          } else {
            config.badge.text = text.slice(0, 3)
          }
          let len = (config.badge.text + '').length
          len = len >= 3 ? 3 : len
          config.badge.className = `${config.badge.className || ''} pm-buttonGap-${lengthToWord[len]}-${config?.gapSize || 'sm'}`
        }
        return { ...config }
      } else {
        return config
      }

    })
  }

  return {
    type: "container",
    className: "pm-button-group-container",
    body: schema
  }
}



/**
 * 带批量操作的列表页
 * @param schema: SchemaNode
 * @returns
 */
export const getBatchOperateListSchema = (schema: any) => {
  const { type, className = "", headerToolbar = [], ...rest } = schema || {};

  const resultSchema = {
    type: "crud",
    className: `${className} pm-crud`,
    ...CRUD_BASIC_CONFIG,
    headerToolbar: [
      "bulkActions",
      ...headerToolbar,
    ],
    ...rest,
  };

  return resultSchema;
}

export const getBatchOperateListSchemaV2 = (schema: any) => {
  const { type, className = "", headerToolbar = [], ...rest } = schema || {};

  const resultSchema = {
    type: "crud",
    className: `${className} pm-crud`,
    ...CRUD_BASIC_CONFIG_V2,
    headerToolbar: [
      "bulkActions",
      ...headerToolbar,
    ],
    ...rest,
  };

  return resultSchema;
}

export const getButtonToolBarSchema = (buttons: any, toolBarConfig: any = {}) => {
  let renderBtns = buttons || [];
  let dropdownButton = [];
  if (buttons?.length > 5) {
    renderBtns = buttons.slice(0, 4);
    dropdownButton = buttons.slice(4);
  }

  const frontBtns = renderBtns.map((itm: any, index: number) => {
    if (index === 0 && itm.level === 'link') {
      return ({ ...itm, className: `mr-1 pl-0 ${itm.className}` })
    }
    return ({ ...itm, className: `mr-1 ${itm.className}` })
  });

  let moreBtn: any[] = []
  // 是否需要更多按钮
  if (dropdownButton.length > 0) {
    moreBtn = [{
      "type": "dropdown-button",
      "label": "更多",
      "className": 'mt-1 ml-1',
      "buttons": dropdownButton
    }]
  }

  return frontBtns.length > 0 ? (
    {
      type: "button-toolbar",
      label: false,
      className: `mb-4 ${toolBarConfig?.className || ''}`,
      buttons: [
        ...frontBtns,
        ...moreBtn
      ],
      ...toolBarConfig,
    }
  ) : null
};

/**
 * byTab的 CRUD 列表页
 * @param schema: SchemaNode
 * @returns
 */
export const getTabCrudListSchema = (schema: any) => {

  return {
    type: "wrapper",
    className: "pm-bg-white",
    body: schema,
  };
}

/**
 * byTab的列表页
 * @param schema: SchemaNode
 * @returns
 */
export const getTabTableListSchema = (schema: any) => {
  const { type, api, columns, tabs, ...rest } = schema || {};

  const formatTabs = () => tabs.map((tabItem) => {
    const { title, tab } = tabItem;
    return ({
      title,
      tab: {
        type: "crud",
        className: `${tab.className || ''} pm-crud`,
        ...CRUD_BASIC_CONFIG,
        perPage: 10,
        ...tab,
      }
    })
  })

  const resultSchema = {
    type: "crud",
    syncLocation: false,
    columnsTogglable: false,
    mode: "tabs",
    api,
    tabs: formatTabs(),
    ...rest
  }

  return resultSchema;
}

export const getTabTableListSchemaV2 = (schema: any) => {
  const { type, api, columns, tabs, ...rest } = schema || {};

  const formatTabs = () => tabs.map((tabItem) => {
    const { title, tab } = tabItem;
    return ({
      title,
      tab: {
        type: "crud",
        className: `${tab.className || ''} pm-crud`,
        ...CRUD_BASIC_CONFIG_V2,
        perPage: 10,
        ...tab,
      }
    })
  })

  const resultSchema = {
    type: "crud",
    syncLocation: false,
    columnsTogglable: false,
    mode: "tabs",
    api,
    tabs: formatTabs(),
    ...rest
  }

  return resultSchema;
}

/**
 * 左右布局 - 左侧树
 */
export const getLeftTreeForTreeListSchema = (schema: any) => {
  const { type, className = "", treeContainerClassName = "", ...rest } = schema || {};
  const noBorderForTreeContainer = treeContainerClassName.indexOf('border') < 0;
  return {
    "type": "input-tree",
    "label": "",
    "searchable": true,
    "className": `${className} pm-bg-white w-72 min-h-screen mr-4`,
    "treeContainerClassName": `${treeContainerClassName}${noBorderForTreeContainer ? ' border-none' : ''}`,
    ...rest
  }
}

/**
 * 左右布局-左侧树-V2
 */
export const getLeftTreeForTreeListSchemaV2 = (schema: any) => {
  const { type, scrollClassName = "", className = "", treeContainerClassName = "", ...rest } = schema || {};
  const noBorderForTreeContainer = treeContainerClassName.indexOf('border') < 0;
  return {
    "type": "input-tree",
    "label": "",
    "searchable": true,
    "autoFillHeight": true,
    "className": `${className} pm-bg-white ${scrollClassName ? scrollClassName : 'w-72'}`,
    "treeContainerClassName": `max-h-none ${treeContainerClassName}${noBorderForTreeContainer ? ' border-none' : ''}`,
    ...rest
  }
}

/**
 * 左右布局-左侧-无边框
 */
export const getLeftAndRightLayoutSchema = (schema: any) => {
  const [leftContent, ...rest] = schema;
  return {
    type: "flex",
    alignItems: "flex-start",
    className: 'h-full',
    items: [
      {
        type: "wrapper",
        size: "none",
        className: "h-full pm-bg-white mr-4 max-h-screen overflow-y-auto",
        body: leftContent,
      },
      ...rest,
    ]
  }
}

/**
 * 左右布局-左侧-有边框
 */
export const getBorderedLeftAndRightLayoutSchema = (schema: any) => {
  const [leftContent, ...rest] = schema;
  return {
    type: "flex",
    alignItems: "flex-start",
    items: [
      {
        type: "wrapper",
        size: "none",
        className: "h-full pm-bg-white border border-gray-200 border-solid mr-4 max-h-screen overflow-y-auto",
        body: leftContent,
      },
      ...rest,
    ]
  }
}

/**
 * 左右布局 - 右侧CRUD
 */
export const getRightCRUDForTreeListSchema = (schema: any) => {
  const { className, ...rest } = schema || {};
  return getBasicListSchema({
    "className": `${className} max-h-screen overflow-y-auto flex-1`,
    ...rest
  })
}

/**
 * 卡片模式
 */
export const getCardListSchema = (schema: any) => {
  const { mode, type, className, bodyClassName,topToolbar, ...rest } = schema || {};

  let renderBtns = [];
  let dropdownButton = [];
  if (topToolbar && Array.isArray(topToolbar)) {
    if (topToolbar.length > 5) {
      renderBtns = topToolbar.slice(0, 4);
      dropdownButton = topToolbar.slice(4);
    } else {
      renderBtns = topToolbar;
    }
  } else {
    renderBtns = [topToolbar]
  }

  const frontBtns = renderBtns.map((itm: any, index: number) => {
    if (index === 0 && itm?.level === 'link') {
      return ({ ...itm, className: `pl-0 ${itm?.className}` })
      // } else if (index === 0) {
      //   return ({...itm, className: `ml-3 ${itm?.className}`})
    }
    return itm
  });

  return {
    type: "crud",
    mode: 'cards',
    className: `${className || ''} pm-crud`,
    bodyClassName: `${bodyClassName || ''}`,
    syncLocation: false,
    columnsTogglable: false,
    footerToolbar: FOOTER_TOOLBAR,
    topToolbar: [
      ...(frontBtns || {}),
      dropdownButton.length > 0 ? {
        type: "dropdown-button",
        label: "更多",
        buttons: dropdownButton
      } : null
    ],
    ...rest,
  };
}

/**
 * 卡片模式- 收敛className
 */
export const cardListWrapSchema =(schema: any)=>{
  const { className,bodyClassName, ...ret } = schema || {};
  return ({
    className: `${className} pm-left-search`,
    bodyClassName: `${bodyClassName} pm-left-search-custom-bg-color no-padding`,
    ...ret,
  })
}

/**
 * 卡片模式带搜索
 * @param schema
 * @returns
 */
export const getCardListWithSearchSchema = (schema: any) => {
  const { mode, type, className, leftToolCol = [], rightToolCol = [], ...rest } = schema || {};

  let renderBtns = [];
  let dropdownButton = [];
  if (leftToolCol && Array.isArray(leftToolCol)) {
    if (leftToolCol.length > 5) {
      renderBtns = leftToolCol.slice(0, 4);
      dropdownButton = leftToolCol.slice(4);
    } else {
      renderBtns = leftToolCol;
    }
  } else {
    renderBtns = [leftToolCol]
  }

  const frontBtns = renderBtns.map((itm: any, index: number) => {
    if (index === 0 && itm?.level === 'link') {
      return ({ ...itm, className: `pl-0 ${itm?.className}` })
    }
    return itm
  });


  return {
    type: "crud",
    mode: 'cards',
    className: `${className || ''} pm-crud`,
    syncLocation: false,
    columnsTogglable: false,
    footerToolbar: FOOTER_TOOLBAR,
    headerToolbar: [
      ...(frontBtns || {}),
      dropdownButton.length > 0 ? {
        type: "dropdown-button",
        label: "更多",
        buttons: dropdownButton
      } : null,
      ...rightToolCol
    ],
    ...rest,
  };
}


// 卡片列表切换
export const getCardSwitchListSchema = (schema: any) => {
  const { type, className, headerToolbar = [], ...rest } = schema || {};

  const newheaderToolbar = Array.isArray(headerToolbar) ? headerToolbar : [headerToolbar];

  const resultSchema = {
    type: "crud",
    className: `${className || ''} pm-crud`,
    ...CRUD_BASIC_CONFIG,
    headerToolbar: [
      ...newheaderToolbar,
      {
        type: "button-group-select",
        name: "type",
        value: "${mode}",
        align: "right",
        visibleDivider: true,
        btnLevel: "text",
        btnActiveLevel: "link",
        options: [
          {
            label: "列表模式",
            value: "table"
          },
          {
            label: "全图模式",
            value: "cards"
          }
        ],
        onEvent: {
          change: {
            actions: [
              {
                actionType: "setValue",
                componentId: "switch-mode-crud",
                args: {
                  value: {
                    mode: "${event.data.value}"
                  }
                },
              }
            ]
          }
        }
      }
    ],
    ...rest,
  };

  return resultSchema;
}


export const getCardSwitchListSchemaV2 = (schema: any) => {
  const { type, className, headerToolbar = [], ...rest } = schema || {};

  const newheaderToolbar = Array.isArray(headerToolbar) ? headerToolbar : [headerToolbar];

  const resultSchema = {
    type: "crud",
    className: `${className || ''} pm-crud`,
    ...CRUD_BASIC_CONFIG_V2,
    headerToolbar: [
      ...newheaderToolbar,
      {
        type: "button-group-select",
        name: "type",
        value: "${mode}",
        align: "right",
        visibleDivider: true,
        btnLevel: "text",
        btnActiveLevel: "link",
        options: [
          {
            label: "列表模式",
            value: "table"
          },
          {
            label: "全图模式",
            value: "cards"
          }
        ],
        onEvent: {
          change: {
            actions: [
              {
                actionType: "setValue",
                componentId: "switch-mode-crud",
                args: {
                  value: {
                    mode: "${event.data.value}"
                  }
                },
              }
            ]
          }
        }
      }
    ],
    ...rest,
  };

  return resultSchema;
}


/**
 * 左右布局 - 左侧内容区可拖拽改变宽度，右侧自适应宽度
 */
export const getResizePageSchema = (schema: any) => {
  const { className = '', asideClassName = '', bodyClassName = '', ...rest } = schema || {};
  return {
    "type": "page",
    "asideResizor": true,
    "asideMinWidth": 32,
    "asideMaxWidth": 500,
    ...rest,
    "className": `bg-light ${className}`,
    "asideClassName": `w-xl page-aside-region pm-bg-white mr-4 ${asideClassName}`,
    "bodyClassName": `p-0 max-h-screen overflow-y-auto ${bodyClassName}`,
  }
}

/**
 *
 * @param schema
 * @returns
 */

export const getResizePageWithSearchSchema = (schema: any) => {
  const { asideClassName, ...ret } = schema || {};
  return ({
    asideClassName: `${asideClassName} h-full antd-aside-tree-info`,
    ...ret,
  })
}


// CRUD左右布局
export const getLeftSearchPageSchema = (schema: any) => {
  const { asideClassName = '', ...rest } = schema || {};
  return getResizePageSchema({
    "asideClassName": `pm-left-search-aside ${asideClassName}`,
    ...rest,
  })
}

// CRUD左右布局-查询表单
export const getAsideFormSchema = (schema: any, customBody) => {
  const { panelClassName = '', body, ...rest } = schema || {};
  return generateGroupForm({
    "type": "form",
    "panelClassName": `pm-left-search-form pm-leftRightLayout-compact-group-form ${panelClassName}`,
    "title": "过滤器：",
    "labelWidth": 60,
    "actions": [
      {
        "type": "button",
        "label": "重置"
      },
      {
        "type": "button",
        "level": "primary",
        "className": "mr-4",
        "label": "查询"
      }
    ],
    body: customBody ? body : generateCollapseGroup(body),
    ...rest
  })
}

// CRUD左右布局-查询表单折叠器
export const getCollapseGroupForLeftSearchSchema = (schemaList: any) => {
  return schemaList?.map((item: any, index: number) => {
    const { className, headingClassName, ...rest } = item;
    return ({
      key: JSON.stringify(index),
      className: `mb-0 pm-left-search-collapse ${className}`,
      headingClassName: `pm-left-search-collapse-header ${headingClassName}`,
      collapsed: false,
      ...rest,
    })
  });
}

// CRUD左右布局-右边card列表
export const getCardListForLeftSearchSchema = (schema: any) => {
  const { className = "", bodyClassName = "", card = {}, ...rest } = schema || {};
  const { className: cardClassName = "", ...cardRest } = card;
  return getCardListSchema({
    "columnsCount": 1,
    "headerToolbar": [
      {
        "type": "search-box",
        "name": "keywords",
        "style": {
          "width": "100%"
        },
        "placeholder": "请输入"
      }
    ],
    "className": `pm-left-search ${className}`,
    "bodyClassName": `pm-left-search-custom-bg-color no-padding ${bodyClassName}`,
    "card": {
      "className": `border-none ${cardClassName}`,
      ...cardRest,
    },
    ...rest,
  });
}

/**
 * 多模式切换（通用）
 * @param schema
 * @returns
 */
export const getMultiModeSwitchSchema = (schema: any) => {
  const { modeOptions, body, ...rest } = schema || {};

  const newBody = Array.isArray(body) ? body : [body];

  const resultSchema = generateNoPaddingWrapper({
    className: 'pm-h-full',
    noPadding: {
      top: false,
      left: false,
      right: false,
    },
    body: [
      {
        type: 'flex',
        justify: "flex-end",
        className: 'pm-mb-4',
        items: [
          {
            type: "button-group-select",
            name: "type",
            value: "${mode}",
            align: "right",
            visibleDivider: true,
            btnLevel: "text",
            btnActiveLevel: "link",
            options: modeOptions || [],
            onEvent: {
              change: {
                actions: [
                  {
                    actionType: "setValue",
                    componentId: "switch-mode-container",
                    args: {
                      value: {
                        mode: "${event.data.value}"
                      }
                    },
                  }
                ]
              }
            }
          }
        ]
      },
      ...newBody
    ],
    ...rest,
  })

  return resultSchema;
}


/**
 * 扩展input-table 组件增加顶部按钮区域
 * @param schema
 * @returns
 */

export const getGlobalActionButtonInputTableSchema = (schema: any) => {
  const { label, topToolbar = {}, footerToolbar = {}, ...rest } = schema || {};
  // 接受button list, 给每个按钮加一个左间距
  const getButtonList = (buttons: any) => {
    return buttons.map((button: any) => {
      return ({
        ...button,
        className: `${button.className || ''} ${button.level === 'link' ? 'pl-2' : 'ml-2'}`
      })
    });
  }
  // 顶部按钮
  const { leftItems = [], rightItems = [] } = topToolbar;
  const isExitHeader = leftItems && leftItems.length > 0 || rightItems && rightItems.length > 0;
  const getRightButtonList = (buttons: any) => {
    return buttons.map((button: any) => {
      return ({
        ...button,
        className: `${button.className || ''} ${button.level === 'link' ? 'pr-2' : 'mr-2'}`
      })
    });
  }
  // 底部区域 包括 文字和按钮
  const { bottomText, buttons = [] } = footerToolbar;
  const getBottomButtonList = (buttons: any) => {
    return buttons.map((button: any) => {
      return ({
        ...button,
        className: `${button.className || ''} ${button.level === 'link' ? 'pr-2' : 'mr-2'} ${bottomText ? 'pm-button-mt' : ''}`
      })
    });
  }
  //判断用户是否配置topToolbar
  const isEmpty = Object.keys(topToolbar).length !== 0;
  // 组装schema
  // isExitHeader 判断topToolbar内leftItems,rightItems是否都为空
  const newSchema = {
    label: isExitHeader ? label : false,
    type: "group",
    direction: "vertical",
    body: [
      {
        type: 'flex',
        justify: 'space-between',
        className: `${isEmpty && isExitHeader ? 'pm-button-mb' : ""}`,
        items: [
          {
            type: "flex",
            items: getButtonList(leftItems)
          },
          {
            type: "flex",
            items: getRightButtonList(rightItems)
          }
        ]
      },
      {
        ...rest,
        label: isExitHeader ? false : label, // 当没有toptool的时候 使用input-table自己的label
        footerToolbarClassName: "flex-1 pr-4",
        showFooterAddBtn: false, // 隐藏自带的bottom 是为了显示文字
        extraFooterConfig: {
          type: "flex",
          direction: "column",
          alignItems: "start",
          items: [
            {
              type: 'tpl',
              className: "content-center",
              tpl: bottomText,
              visible: bottomText
            },
            {
              type: "flex",
              items: getBottomButtonList(buttons)
            },
          ]
        },
      }
    ]
  };
  return newSchema;
}

/**
 * 在crud中 会同时传入headerToolBar headerFilter等
 * 目前headerFilter默认在左侧
 * 因此新增了辅助函数用于处理多个头之间的规则
 * */
export const getCRUDHeader = (schema: any) => {
  const {
    headerFilter, // 分段器
    headerToolbar, // 工具栏
    className,
    ...rest
  } = schema;

  /**
   * 当没有bulkActions或headerToolbar时
   * headerFilter正常在左侧
   * 哪怕有列配置和列排序也是在左侧(参考批量操作按钮)
   * */
  if (!headerToolbar || headerToolbar.length === 0) {
    return {
      ...schema
    }
  }

  return {
    ...schema,
    className: className ? `${className} pm-filter-right` : 'pm-filter-right'
  }
};
//固定宽度左右布局
export const generateFixedWidthSchema = (schema: any) => {
  const { left, right } = schema
  /**
   * 左侧宽度默认320【左侧宽度和右侧宽度都未设置时生效】
   * 设置左侧宽度时左侧内容以设置宽度为准，右侧内容自动撑开
   * 不设置左侧宽度，设置右侧宽度时，左侧内容自动撑开
   * 左侧宽度右侧宽度都设置时，左侧宽度生效，右侧内容自动撑开
   */
  let leftWrapperStyle = right.customWidth&&!left.customWidth?'bg-white h-full flex-grow':`bg-white h-full ${left.customWidth||'w-80'}`
  let rightWrapperStyle = (!right.customWidth && !left.customWidth) || left.customWidth ? "flex-grow bg-light h-full ml-4 " : `bg-light h-full ml-4 flex-shrink ${right.customWidth}`
  return (
    {
      type: 'flex',
      alignItems: 'flex-start',
      justify: 'start',
      className: "h-full", // 整体布局高度为100vh
      items: [
        {
          // 左侧组件
          type: 'wrapper',
          size: "none",
          className: leftWrapperStyle,
          body: left
        },
        {
          //右侧组件
          type: "wrapper",
          size: "none",
          className: rightWrapperStyle,
          body: {
            ...right,
            className: "p-none",
          }
        }
      ],
    }
  )
}

/**
 * 列表页 column 中typography 应用超长文本省略
 */
export const getContentTypographySchema = (schema: any) => {
  const {className} = schema
  return {
    ...schema,
    className: `pm-typography-model ${className}`,
  }
}
