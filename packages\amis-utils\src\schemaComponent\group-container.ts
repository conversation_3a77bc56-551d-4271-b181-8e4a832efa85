import { extendsSchemaComponent } from 'amis-core'

/**
 * 小分组
 * @param config
 * @returns
 */
const getSmallGroupContainer = (config: any) => {
  const { items: bodyConfig } = config;

  let resultBody: any[] = []
  if (!Array.isArray(bodyConfig)) {
    resultBody = bodyConfig
    return resultBody
  }

  // body遍历
  bodyConfig.forEach((itemConfig, index) => {
    const { className = '', headerClassName = "", bodyClassName = "", header, body } = itemConfig
    const isLatest = index === bodyConfig.length - 1
    const title = header?.title ?? ""

    // 每个分组都是一个card组件
    // 规范约束小分组只能配置标题，因此这里不支持配置header的其他属性，也不支持配置card的其他属性
    resultBody.push({
      standardMode: true,
      type: "card",
      useCardLabel: false,
      className: `standard-SmallGroupContainer ${className}`,
      headerClassName,
      bodyClassName: `flex flex-col gap-4 ${isLatest ? "pb-0" : ""} ${bodyClassName}`,
      ...title && {
        header: {
          title: header?.title,
        }
      },
      body,
    });
  })

  return resultBody
}

/**
 * 分组容器
 * @param config
 * @returns
 */
const groupContainer = (config: any) => {
  const { className, collapsible, items: bodyConfig, isSmallGroup, ...restConfig } = config;

  let resultBody: any[] = []
  if (!Array.isArray(bodyConfig)) {
    resultBody = bodyConfig
    return resultBody
  }

  // 如果是小分组，调用小分组函数，返回schema
  if (isSmallGroup) {
    return getSmallGroupContainer(config)
  }

  // header转换，折叠使用collapse，不可折叠使用panel
  bodyConfig.forEach((itemConfig) => {
    const { type, className = '', header = {}, title: abandonTitle, ...itemRest } = itemConfig
    const { title = "", subTitle = "", assistContent = [], actions = [], } = header
    const hasHeader = title
    let newItem = {}

    const innerHeader = [
      {
        type: "title",
        title: title,
        subTitle,
        assistContent,
        actions,
      },
    ]

    if (collapsible) {
      // 如果需要折叠功能，则使用collapse组件
      newItem = {
        collapsable: true,
        ...itemRest,
        ...hasHeader && { header: innerHeader },
        className: `pm-bg-white standard-GroupContainer-item ${className ?? ""}`,
        type: "collapse",
      }
    } else {
      // 如果无需折叠功能，则使用panel组件
      newItem = {
        ...itemRest,
        ...hasHeader && { header: innerHeader, },
        type: "panel",
        className: `standard-GroupContainer-item ${className ?? ""}`,
      }
    }

    resultBody.push(newItem);


  })

  // 可折叠分组容器
  if (collapsible) {
    return {
      ...restConfig,
      type: "collapse-group",
      body: resultBody,
      className: `standard-GroupContainer ${className ?? ""}`
    }
  }

  // 不可折叠分组容器
  return {
    type: "wrapper",
    body: resultBody,
    size: "none",
    className: `standard-GroupContainer ${className ?? ""}`
  }
}

extendsSchemaComponent({ groupContainer })


