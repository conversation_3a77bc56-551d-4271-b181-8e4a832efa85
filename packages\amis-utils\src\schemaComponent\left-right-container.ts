import  { extendsSchemaComponent } from 'amis-core'

/**
 * 左右容器
 * @param config
 */
const leftRightContainer = (config: any) => {
  const {
    className = '',
    left,
    right,
    defaultWidth,
    leftWidth,
    rightWidth,
    minWidth = 50,
    maxWidth = 500,
    draggable = true,
    leftTitle,
    rightTitle,
    leftProps,
  } = config

  /**
   * 左侧可拖拽，或者左侧固定宽度时
   */
  const isLeftFixed = draggable || !rightWidth

  const { wrapperClassName: leftClassName = '' } = left || {}
  const { wrapperClassName: rightClassName = '' } = right ||  {}

  /**
   * 设定宽度 或者 可拖拽时
   */
  const fixedProps = draggable ? {
    type: 'resize-container',
    bodyClassName: 'p-4 h-full overflow-y-auto',
    defaultSize: {
      // height: '100%',
      width: defaultWidth
    },
    minWidth,
    maxWidth,
  } : {
    type: 'wrapper',
    size: 'none',
    className: 'p-4 h-full overflow-y-auto',
    style: {
      width: leftWidth || rightWidth
    }
  }

  /**
   * 伸缩适应宽度
   */
  const flexProps = {
    type: 'wrapper',
    size: 'none',
    className: 'flex-1 max-h-full overflow-y-auto',
  }

  const leftContainer: any = {
    body: Array.isArray(left) ? left : [left],
    ...(isLeftFixed ? fixedProps : flexProps),
    ...leftProps,
  }

  const rightContainer: any = {
    body: Array.isArray(right) ? right : [right],
    ...(isLeftFixed ? flexProps : fixedProps)
  }

  // 左侧配置 leftTitle 属性时，左侧 title 添加下边框
  if (leftTitle) {
    leftContainer.body = [
      {
        "type": "title",
        "title": leftTitle,
        className: '-m-4 border-t-0 border-l-0 border-r-0 border-b border-solid border-gray-100'
      },
      ...leftContainer.body,
    ]
  }

  // 右侧配置 rightTitle 属性时，在白底背景下，右侧 title 添加下边框
  if (rightTitle) {
    rightContainer.body = [
      {
        "type": "title",
        "title": rightTitle
      },
      ...rightContainer.body,
    ]
  }

  leftContainer.className += ` standard-LeftRightContainer-left mr-4 bg-white max-h-full ${leftClassName}`
  rightContainer.className += ` standard-LeftRightContainer-right max-h-full ${rightClassName}`

  const wrapperContainer = {
    className: `standard-LeftRightContainer ${className} ${draggable ? 'max-h-full' : 'h-full'}`,
    type: 'flex',
    justify: 'start',
    alignItems: 'stretch',
  }

  return {
    ...wrapperContainer,
    items: [
      leftContainer,
      rightContainer
    ]
  }
}

extendsSchemaComponent({leftRightContainer})
