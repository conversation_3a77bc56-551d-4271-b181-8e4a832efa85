import { extendsSchemaComponent } from 'amis-core'

const space = (schema: any) => {
  const { direction = 'horizontal', className = '', items } = schema

  if (!Array.isArray(items)) {
    return schema
  }

  const isVertical = direction === 'vertical'

  return {
    type: 'wrapper',
    size: 'none',
    className: `standard-Space standard-Space--${direction} ${className}`,
    body: !isVertical
      ? items
      : items.map((item) => ({ // 垂直时未使用flex布局，给每一个item包一个 wrapper
        type: 'wrapper',
        size: 'none',
        className: 'standard-Space-item',
        body: item
      })),
  }
}


extendsSchemaComponent({space})
