import { extendsSchemaComponent } from 'amis-core'

const title = (titleConfig: any) => {
  const {
    title,
    subTitle,
    className = '',
    iconConfig,
    affixHeader,
    assistContent,
    actions,
    noPadding = false,
  } = titleConfig;
  const { onEvent, icon } = iconConfig || {};
  // 默认事件是返回上一页
  let iconEvent;

  if (iconConfig) {
    // 如果有自定义事件 使用自定义事件
    if (onEvent) {
      iconEvent = onEvent;
    } else {
      iconEvent = {
        click: {
          actions: [
            {
              actionType: "goBack",
            }
          ]
        }
      };
    }
  }

  let titleContainerClassName = "flex-1 w-full mr-2 pm-place-content-start";

  //  当有多个按钮时，间距需要添加 "mr-2", 否则 多个按钮就紧挨着了
  const newAction: any[] = [];
  if (actions && actions.length > 0) {
    titleContainerClassName = "flex-1 w-3/4 mr-2 pm-place-content-start";
    const count = actions.length;
    actions.forEach((actionEle: any, index: number) => {
      if ((index + 1) === count) {
        newAction.push(actionEle);
      } else {
        const newEle = {
          ...actionEle,
          className: "mr-2",
        }
        newAction.push(newEle);
      }
    });
  }

  // 小标题 样式
  let subTitleClassName = "";
  if (subTitle) {
    subTitleClassName = "pm-subtitle-color text-ellipsis pm-subtitle-min-width pm-button-ml";
  }

  const titleContainer = {
    type: "flex",
    justify: "space-between",
    items: [
      {
        type: "flex",
        className: titleContainerClassName,
        items: [
          {
            type: "icon",
            icon: icon || "chevron-left",
            className: "pr-2",
            onEvent: iconEvent,
            style: {
              cursor: "pointer"
            },
            visible: !!iconConfig,
          },
          {
            type: "tpl",
            tpl: title,
            className: 'text-lg text-black font-bold text-ellipsis',
          },
          {
            type: "tpl",
            className: subTitleClassName,
            tpl: subTitle || '',
          },
          assistContent && {
            type: 'wrapper',
            size: 'none',
            className: 'ml-2',
            body: assistContent
          }
        ].filter(Boolean)
      },
      {
        type: "flex",
        className: 'ml-2',
        items: [
          newAction
        ]
      }
    ]
  }


  // 组装json
  const newSchema = {
    type: "wrapper",
    className: `standard-Title ${className} pm-bg-white ${noPadding ? 'p-0' : 'p-2 pl-none pr-none mb-4'}`,
    style: affixHeader ?
      {
        position: "sticky",
        top: "0px",
        background: "#fff",
      } :
      undefined,
    body: {
      ...titleContainer,
      className: `standard-Title-body antd-Panel-body ${noPadding ? 'p-0' : 'pt-0 pb-0'}`,
    }
  };

  return newSchema;
}

extendsSchemaComponent({title})
