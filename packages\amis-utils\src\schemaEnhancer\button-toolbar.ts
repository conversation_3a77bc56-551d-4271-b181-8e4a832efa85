/**
 * 更多操作操作按钮，超过固定个数时，就显示 “更多”
 * 默认超出4个时，（第五个按钮变为“更多”按钮），支持角标展示
 * TODO:
 *  1. 超出个数button 使用 VisibleOn 显隐时存在问题， 考虑结合 show-more 组件，进行优化
 */


import { addSchemaEnhancer } from 'amis-core'

// 按钮 带 角标
const transformWithBadge = (buttons: any[]) => {
  const newButtons = buttons.map(btnSchema => {
    if (Object.prototype.toString.call(btnSchema?.badge) !== '[object Object]') {
      return btnSchema
    }

    // 对角标配置进行改写
    const { text, className = '' } = btnSchema.badge
    const badgeSchema: any = {
      ...btnSchema.badge
    }
    // 整数时 封顶99
    if (/[0-9]*$/.test(text)) {
      badgeSchema.overflowCount = 99
    } else {
      // 其他情况截取前三个字符
      badgeSchema.text = text.slice(0, 3)
    }

    const lengthToWord: any = {
      1: "one", // 22
      2: "two", // 28
      3: "three" // 36
    }
    const textLen = Math.min((badgeSchema.text + '').length, 3)

    badgeSchema.className = `${className || ''} pm-buttonGap-${lengthToWord[textLen]}-${btnSchema.gapSize || 'sm'}`

    return {
      ...btnSchema,
      badge: badgeSchema
    }
  })

  return newButtons
}

addSchemaEnhancer({
  type: 'button-toolbar',
  transformFn: (schema: any = {}) => {
    const {
      maxCount: count = 4,
      dropdownConfig,
      className = '',
      buttons,
      ...rest
    } = schema

    // buttons 不是数组时不处理
    if (!Array.isArray(buttons)) {
      return schema
    }

    const maxCunt = Math.max(2, count) // 最小为2

    let displayButtons = buttons.slice(0, maxCunt).map((item, index) => {
      // 对第一个 link 类型 按钮， 去掉左那边距
      if (index === 0 && item.level === 'link') {
        return {
          ...item,
          className: `pl-0 ${item.className || ''}`
        }
      } else if (['button', 'action'].includes(item.type)) {
        return {
          ...item,
          className: `pm-button-mr ${item.className || ''}`
        }
      }

      return item
    })

    const withBadge = !!buttons.find((item) => !!item.badge)

    // 按钮包裹角标时，要经过角标规则转换
    if (withBadge) {
      displayButtons = transformWithBadge(displayButtons)
    }

    // 超出指定数量按钮，显示 “更多” dropdown按钮
    const dropdownButton = buttons.slice(maxCunt);
    if (dropdownButton.length) {
      displayButtons = displayButtons.concat({
        label: "更多",
        ...dropdownConfig,
        type: "dropdown-button",
        buttons: dropdownButton
      })
    }

    return {
      label: false,
      ...rest,
      buttons: displayButtons,
      className: ` ${
        // 包裹了角标 需要增加特定的 className
        withBadge ? 'pm-button-group-container' : ''} ${className}`
    }
  }
})
