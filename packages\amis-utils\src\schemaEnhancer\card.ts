import {addSchemaEnhancer} from 'amis-core';


// .thumb: 50px
// .thumb-xxs: 30px
// .thumb-xs: 34px
// .thumb-sm: 40px
// .thumb-md: 64px (默认)
// .thumb-lg: 96px
// .thumb-btn: 200px
// .thumb-xl: 128px

addSchemaEnhancer({
  type: 'card',
  // 新增 图片比例属性，提供查询时样式
  // enhanceProps: ['imageRatio'],
  transformFn: (schema: any = {}) => {
    const {
      className = '',
      headerClassName = '',
      header,
      media,
      mode = 'normal',
      ...rest
    } = schema || {};
    const headerBorderCls =
      'border-gray-100 border-solid border border-t-0 border-l-0 border-r-0';

    let headerBgColor = header?.bgColor || '';
    let headerTitleClassName = header?.titleClassName || '';
    let bodyClassName = rest?.bodyClassName || ""

    // 如果配置了看板模式
    if (mode === 'board') {
      const headerBgColorConfig: any = {
        blue: 'bg-blue-50',
      };

      // 设置头部的背景颜色
      headerBgColor = headerBgColorConfig[header?.bgColor] || '';
      // 设置头部字体颜色
      headerTitleClassName = `${headerTitleClassName} text-center`
      // 设置内容区域的字体颜色
      bodyClassName = `${bodyClassName} text-center`
    }

    // 是否设置了图片比例
    const hasImageRatio = media?.imageRatio;

    // 默认图片比例1:1
    let mediaClassName = 'pm-w-24 pm-h-24';
    let subTitleClassName = `pm-overflow-ellipsis-3 pm-max-h-24 ${
      header?.subTitleClassName ?? ''
    }`;

    if (hasImageRatio) {
      let _imageRatio = media?.imageRatio.replace(/\s/g, '');

      if (_imageRatio === '3:4') {
        mediaClassName = 'pm-w-24 pm-h-32';
        subTitleClassName = 'pm-overflow-ellipsis-5 pm-max-h-32';
      } else if (_imageRatio === '4:3') {
        mediaClassName = 'pm-w-24 pm-h-18';
        subTitleClassName = 'pm-overflow-ellipsis-2 pm-max-h-18';
      }
    }

    const avatarShapeClsMap = {
      circle: 'avatar', // 圆形
      square: 'avatar--square', // 方型
      rounded: 'avatar--rounded', // 圆角
    };
    const avatarShape: keyof typeof avatarShapeClsMap =
      header?.avatarShape || 'circle';
    const avatarShapeCls = avatarShapeClsMap[avatarShape];
    const avatarClassName = `thumb-md ${avatarShapeCls}`

    return {
      ...rest,
      className: `standard-Card ${className} ${
        hasImageRatio ? 'antd-Card-multiMedia--container' : ''
      }`,
      headerClassName: `standard-Card-header pt-2 pb-2  ${headerBorderCls} ${headerClassName} ${headerBgColor}`,
      bodyClassName: `standard-Card-body ${bodyClassName}`,
      ...(header && {
        header: {
          ...header,
          ...(header?.avatar && {
            avatarClassName: `pull-left ${header?.avatarClassName ?? ''} ${avatarClassName}`,
          }),
          ...(header?.avatarText && {
            avatarTextClassName: `${header?.avatarTextClassName ?? ''} ${avatarClassName}`,
          }),
          titleClassName: `${headerTitleClassName}`,
          subTitleClassName: `${subTitleClassName}`,
        },
      }),
      ...(media && {
        media: {
          ...(media || {}),
          // 保留media className 可配置
          className: `${media?.className ?? ''} ${mediaClassName}`,
        },
      }),
    };
  },
});
