import { addSchemaEnhancer } from 'amis-core'

/**
 * 转换 Crud topToolbar
 */
const transformTopToolbar = (schema: any) => {
  const { topToolbar } = schema
  if (!topToolbar) {
    return undefined
  }

  /**
   * 当配置的是按钮数组时，默认包裹 一层 button-toolbar
   * 超出4个按钮时，第5个按钮转为“更多”下拉框
   */
  if (
    Array.isArray(topToolbar) &&
    topToolbar.every((item) => ['action', 'button'].includes(item?.type))
  ) {
    return {
      type: "button-toolbar",
      standardMode: true,
      buttons: topToolbar
    }
  }

  return topToolbar
}

/**
 * 转换 Crud headToolbar
 * 1. 存在 bulkActions 时，自动将 批量操作放在第一个
 */
const transformHeadToolbar = (schema: any) => {
  let { bulkActions, headerToolbar = [] } = schema

  // 存在 bulkActions 时， 且 headerToolbar未配置 bulkActions 时，自动将 批量操作放在第一个
  if (Array.isArray(bulkActions) && !headerToolbar.includes('bulkActions')) {
    headerToolbar = ['bulkActions'].concat(headerToolbar)
  }

  return headerToolbar
}

/**
 * 转换 filter 部分
 * 1. filterFormAdvanceSearchAble 不为 true 时，默认 包裹一层 group.horizon
 * TODO：可做如下优化
 *   1. 当 filter 表单不再columns中存在的时候（即 column.searchable 无法使用时），
 *      可以将 columns 的 searchable 拼接到，filter 中（配合 sort 排序）
 *
 *   2. 给 searchable 定义顺序(配置 sort 字段时，将searchable 抽离出来放到 filter 中)
 */
const transformFilter = (schema: any) => {
  const { filter, filterFormAdvanceSearchAble } = schema
  // 不配置 filter 或者 不是 object 类型，不处理。
  if (!filter) {
    return
  }

  const { type, title = '', actions, ...rest } = filter
  let { body } = filter

  //  filterFormAdvanceSearchAble 不为 true 时，默认 包裹一层 group.horizontal
  if (filterFormAdvanceSearchAble !== true && Array.isArray(body)) {
    const firstType = body?.[0]?.type
    if (firstType && firstType !== 'group') {
      body = [{
        "type": "group",
        "mode": "horizontal",
        body
      }]
    }
  }

  return {
    ...rest,
    title,
    body,
    actions: actions || [
      {
        "type": "button",
        "actionType": "reset-and-submit",
        "label": "重 置"
      },
      {
        "type": "submit",
        "level": "primary",
        "label": "查 询"
      }
    ]
  }
}

/**
 * 基础 Crud 转换
 */

addSchemaEnhancer({
  type: 'crud',
  // 新增 compactMode 属性，支持紧凑模式
  enhanceProps: ['compactMode'],
  transformFn: (schema: any = {}) => {
    // 解析出来的属性，均完全使用转换后的（如果需要json配置，可覆盖的不要再此处解析，使用 schema.xxx 取值）
    const {
      className = '',
      bodyClassName = '',
      headerToolbar,
      topToolbar,
      filter,
      // 新增属性
      compactMode, // 行紧凑模式
      // 其余属性
      ...originConfig
    } = schema

    // 默认配置，当 mode 为table时，默认配置 autoGenerateFilter
    const isDefConfigAutoGenerateFilter = !originConfig?.mode || originConfig?.mode === "table"

    const defConfig = {
      className: `pm-crud ${className} ${
        // 紧凑模式
        compactMode ? 'compact-mode-v2' : ''
        } ${
        // 存在 subTable 时，增加特殊处理
        schema.subTable ? 'pm-curdTable-model' : ''
        }`,
      bodyClassName: `standard-Crud-body ${bodyClassName}`,
      topToolbar: transformTopToolbar(schema),
      headerToolbar: transformHeadToolbar(schema),
      filter: transformFilter(schema),
      syncLocation: false,
      columnsTogglable: false,
      footerToolbar: [{
        type: 'tpl',
        // 默认取  count||total 中数据，最好能支持 自定义取字段
        tpl: '共${count||total||0}条',
        className: 'pr-2',
      }, {
        type: 'pagination',
        layout: 'pager,perPage,go',
        }],
      // 仅在table模式下配置，否则会出现api不请求的问题
      ...isDefConfigAutoGenerateFilter && {
        autoGenerateFilter: {
          showBtnToolbar: false,
          defaultExpanded: false,
        },
      }
    }

    // 存在 headerToolbar 时， headerFilter 自动移动到右侧（headerFilter 默认在左侧）
    if (defConfig.headerToolbar?.length) {
      defConfig.className += ` pm-filter-right`
    }

    // 基础 Crud 配置
    const baseCrudConfig = {
      ...defConfig,
      ...originConfig
    }

    return baseCrudConfig
  }
})

/**
 * Crud Cards模式, card 独占一行 转换（一行多个 cards 时无法使用）
 *
 * TODO：
 * 1. 考虑将所有 Card 的样式，放入 Card 组件中
 */
addSchemaEnhancer({
  type: 'crud',
  transformFn: (schema: any = {}) => {
    const {
      card,
      columns,
      columnsCount = 1,
      className = '',
      bodyClassName = '',
    } = schema || {};

    // 非 cards 模式, 或者 非单行 card 不处理
    if (columns?.length || !card || columnsCount !== 1) {
      return schema
    }

    const {
      className: cardClassName = '',
      title = '',
      header = {},
      ...cardRest
    } = card;

    const overwriteConfig = {
      mode: 'cards',
      columnsCount: 1,
      className: `${className}`,
      bodyClassName: `single-columns pm-left-search-custom-bg-color no-padding ${bodyClassName}`,
      card: {
        className: `border-none ${cardClassName}`,
        headerClassName: `pt-2 pb-2 items-center border-gray-100 border-solid border border-t-0 border-l-0 border-r-0 ${cardRest.headerClassName || ''}`,
        bodyClassName: `pm-card-body ${cardRest.bodyClassName || ''}`,
        header: {
          title,
          titleClassName: `font-normal ${header?.titleClassName || ''}`,
          ...header,
        },
        ...cardRest,
      }
    }

    const cardsCrudConfig = {
      ...schema,
      ...overwriteConfig
    }

    return cardsCrudConfig
  }
})

