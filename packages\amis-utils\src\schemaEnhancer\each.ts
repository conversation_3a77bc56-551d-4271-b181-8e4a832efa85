import { addSchemaEnhancer } from 'amis-core'

const xLayoutConfig: Record<string, string> = {
  start: "justify-start",
  center: "justify-center",
  end: "justify-end"
}

const yLayoutConfig: Record<string, string> = {
  start: "items-start",
  center: "items-center",
  end: "items-end"
}

addSchemaEnhancer({
  type: 'each',
  transformFn: (schema: any = {}) => {
    // 为each组件拓展mode、gap、justify、alignItems布局类属性
    const {className = '',  mode = "normal", gap, justify = "start", alignItems = "start", ...rest} = schema
    const isHorizontal = mode === 'horizontal'
    const isVertical = mode === 'vertical'
    const horizontalCls = isHorizontal ? `flex` : ""
    const verticalCls = isVertical ? `flex flex-col` : ""
    const gapCls = `gap-${isVertical ? 4 : 2}`
    const justifyCls = xLayoutConfig[justify] || ""
    const alignItemsCls = yLayoutConfig[alignItems] || ""


    return {
      ...rest,
      className: `${className} ${horizontalCls} ${verticalCls} ${gapCls} ${justifyCls} ${alignItemsCls}`
    }
  }
})
