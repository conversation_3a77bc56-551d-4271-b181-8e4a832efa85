import { addSchemaEnhancer } from 'amis-core'

addSchemaEnhancer({
  type: 'tooltip-wrapper',
  transformFn: (schema: any = {}) => {
    let { className = "", tooltipClassName = "", size = 'xs', maxHeight = 400, content, ...ret } = schema || {};
    const sizeMap: any = {
      sm: 'pm-tooltip-popover-sm',
      xs: 'pm-tooltip-popover-xs',
      md: 'pm-tooltip-popover-md',
      lg: 'pm-tooltip-popover-lg',
    }

    return ({
      ...ret,
      className: `standard-TooltipWrapper ${className}`,
      tooltipClassName: `standard-TooltipWrapper-popover ${sizeMap?.[size] || ''} ${tooltipClassName}`,
      content: {
        type: 'wrapper',
        size: 'none',
        style: {
          maxHeight,
        },
        body: content,
      }
    })
  }
})
