// 专门用于Mock的setup文件，在模块加载之前执行
// 解决React Transition Group在Jest/JSDOM环境下的兼容性问题
// 参考：https://testing-library.com/docs/example-react-transition-group/

// Mock react-transition-group/Transition 子模块路径
jest.mock('react-transition-group/Transition', () => {
  const React = require('react');

  // Mock Transition组件 - 正确处理mountOnEnter和unmountOnExit逻辑
  const MockTransition = ({ children, in: inProp, unmountOnExit, mountOnEnter, ...props }) => {
    // React Transition Group的正确逻辑：
    // 1. mountOnEnter=true: 组件只有在第一次inProp=true时才挂载
    // 2. unmountOnExit=true: 当inProp=false时，组件卸载
    // 3. unmountOnExit=false: 即使inProp=false，组件也保持挂载状态

    // 如果unmountOnExit为true且组件应该被隐藏，则不渲染
    if (unmountOnExit && !inProp) {
      return null;
    }

    // 对于unmountOnExit=false的情况，即使inProp=false也应该继续渲染
    // mountOnEnter只影响首次挂载，不影响已挂载组件的显示

    // 对于函数children，始终传递'entered'状态
    if (typeof children === 'function') {
      return children('entered');
    }

    return children;
  };

  // 设置状态常量
  MockTransition.ENTERING = 'entering';
  MockTransition.ENTERED = 'entered';
  MockTransition.EXITING = 'exiting';
  MockTransition.EXITED = 'exited';

  // 默认导出Transition组件
  MockTransition.default = MockTransition;

  return MockTransition;
});

// Mock react-transition-group主模块
jest.mock('react-transition-group', () => {
  const React = require('react');

  const MockTransition = ({ children, in: inProp, unmountOnExit, mountOnEnter, ...props }) => {
    // 简化的主模块Mock，遵循相同逻辑
    if (unmountOnExit && !inProp) {
      return null;
    }
    if (typeof children === 'function') {
      return children('entered');
    }
    return children;
  };

  const MockCSSTransition = ({ children, in: inProp, unmountOnExit, mountOnEnter, ...props }) => {
    return React.createElement(MockTransition, { children, in: inProp, unmountOnExit, mountOnEnter, ...props });
  };

  const MockTransitionGroup = ({ children }) => children;

  return {
    Transition: MockTransition,
    CSSTransition: MockCSSTransition,
    TransitionGroup: MockTransitionGroup,
    ENTERING: 'entering',
    ENTERED: 'entered',
    EXITING: 'exiting',
    EXITED: 'exited'
  };
});
