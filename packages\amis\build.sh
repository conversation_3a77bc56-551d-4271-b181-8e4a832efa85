#!/bin/bash
set -e

export NODE_ENV=production

rm -rf esm
rm -rf lib
rm -rf output

echo "===rollup build==="
ulimit -n 65535 && NODE_ENV=production node --max-old-space-size=8192 ../../node_modules/.bin/rollup -c

# 从 amis-ui 中复制 css
mkdir -p lib/themes
# cp ../../node_modules/amis-ui/lib/themes/ang.css lib/themes/ang.css
# cp ../../node_modules/amis-ui/lib/themes/dark.css lib/themes/dark.css
cp ../../node_modules/amis-ui/lib/themes/antd.css lib/themes/antd.css
# cp ../../node_modules/amis-ui/lib/themes/cxd.css lib/themes/cxd.css
cp ../../node_modules/amis-ui/lib/themes/default.css lib/themes/default.csss
cp ../../node_modules/amis-ui/lib/helper.css lib/helper.css

# 从 dataseed-ui中复制 css 追加到 lib/themes/antd.css 文件末尾
cat ../../node_modules/dataseed-ui/lib/themes/antd.css >> lib/themes/antd.css

# 生成 sdk
echo "===sdk==="
rm -rf sdk && mkdir sdk

cp ./lib/helper.css sdk/helper.css
cp ../../examples/static/iconfont.* sdk/

echo "===postcss ie11==="
# cat lib/themes/ang.css | ../../node_modules/.bin/postcss >lib/themes/ang-ie11.css
# cat lib/themes/dark.css | ../../node_modules/.bin/postcss >lib/themes/dark-ie11.css
cat lib/themes/antd.css | ../../node_modules/.bin/postcss >lib/themes/antd-ie11.css
# cat lib/themes/cxd.css | ../../node_modules/.bin/postcss >lib/themes/cxd-ie11.css
# cp lib/themes/cxd-ie11.css lib/themes/default-ie11.css

echo "===build-schemas==="
npm run build-schemas
