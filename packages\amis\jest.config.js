module.exports = {
  testEnvironment: "jsdom",
  collectCoverageFrom: [
    "src/**/*"
  ],
  moduleFileExtensions: [
    "ts",
    "tsx",
    "js"
  ],
  transform: {
    "\\.(ts|tsx)$": [
      "ts-jest",
      {
        "diagnostics": false
      }
    ]
  },
  setupFiles: [
    "jest-canvas-mock",
    "<rootDir>/__tests__/setup-mocks.js"
  ],
  testRegex: "/.*\\.test\\.(ts|tsx|js)$",
  moduleNameMapper: {
    "\\.(css|less|sass|scss)$": "<rootDir>/../../__mocks__/styleMock.js",
    "\\.(svg)$": "<rootDir>/../../__mocks__/svgMock.js",
    "\\.svg\\.js$": "<rootDir>/../../__mocks__/svgJsMock.js",
    "^dataseed\\-ui$": "<rootDir>/../dataseed-ui/src/index.tsx",
    "^amis\\-ui$": "<rootDir>/../amis-ui/src/index.tsx",
    "^amis\\-ui/lib/(.*)": "<rootDir>/../amis-ui/src/$1",
    "^amis\\-core$": "<rootDir>/../amis-core/src/index.tsx",
    "^amis\\-core/lib/(.*)": "<rootDir>/../amis-core/src/$1",
    "^amis\\-formula$": "<rootDir>/../amis-formula/src/index.ts",
    "^amis\\-formula/lib/(.*)": "<rootDir>/../amis-formula/src/$1"
  },
  setupFilesAfterEnv: [
    "<rootDir>/../amis-core/__tests__/jest.setup.js"
  ],
  testPathIgnorePatterns: [
    "/node_modules/",
    "/.rollup.cache/"
  ],
  snapshotFormat: {
    escapeString: false,
    printBasicPrototype: false
  }
};
