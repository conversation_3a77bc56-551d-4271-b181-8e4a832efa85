{"name": "amis", "version": "1.90.5", "description": "一种MIS页面生成工具", "main": "lib/index.js", "module": "esm/index.js", "types": "lib/index.d.ts", "scripts": {"test": "jest", "update-snapshot": "jest --updateSnapshot", "coverage": "jest --coverage", "publish-to-internal": "sh build.sh && sh publish.sh", "build": "npm run clean-dist && sh build.sh", "prettier": "prettier --write '{src,scss,examples}/**/**/*.{js,jsx,ts,tsx,scss,json}'", "build-schemas": "ts-node -O '{\"target\":\"es6\"}' ../../scripts/build-schemas.ts", "clean-dist": "rimraf lib/** esm/** tsconfig.tsbuildinfo .rollup.cache/**"}, "keywords": ["react", "amis", "mis", "renderer", "json", "schema"], "author": "baidu", "license": "Apache-2.0", "licenses": [{"type": "Apache-2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}], "lint-staged": {"{src,examples}/**/**/*.{tsx,jsx,ts}": ["prettier --write"]}, "dependencies": {"@dataseed/cdn-cloud": "^0.0.15", "amis-core": "^1.90.5", "amis-ui": "^1.90.5", "attr-accept": "2.2.2", "blueimp-canvastoblob": "2.1.0", "classnames": "2.3.1", "dataseed-ui": "^1.90.5", "downshift": "6.1.12", "echarts": "5.4.0", "echarts-stat": "^1.2.0", "exceljs": "^4.3.0", "file-saver": "^2.0.2", "hls.js": "1.1.3", "hoist-non-react-statics": "^3.3.2", "hotkeys-js": "^3.8.7", "immutability-helper": "^3.1.1", "jsbarcode": "^3.11.5", "keycode": "^2.2.1", "lodash": "^4.17.15", "match-sorter": "^6.3.1", "memoize-one": "^6.0.0", "mobx": "^4.5.0", "mobx-react": "^6.3.1", "mobx-state-tree": "^3.17.3", "moment": "^2.19.4", "mpegts.js": "1.7.3", "ooxml-viewer": "0.1.1", "prop-types": "^15.6.1", "qrcode.react": "^3.1.0", "rc-overflow": "^1.2.4", "react-cropper": "^2.1.8", "react-dropzone": "^11.4.2", "react-json-view": "1.21.3", "react-transition-group": "4.4.2", "sortablejs": "1.15.0", "tslib": "^2.3.1", "video-react": "0.15.0"}, "devDependencies": {"@fortawesome/fontawesome-free": "^6.1.1", "@rollup/plugin-commonjs": "^22.0.2", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^14.1.0", "@rollup/plugin-typescript": "^8.3.4", "@svgr/rollup": "^6.2.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.4.0", "@types/async": "^2.0.45", "@types/codemirror": "^5.60.3", "@types/echarts": "^4.9.2", "@types/file-saver": "^2.0.1", "@types/history": "^4.6.0", "@types/hoist-non-react-statics": "^3.3.1", "@types/jest": "^28.1.0", "@types/json-schema": "^7.0.11", "@types/lodash": "^4.14.175", "@types/markdown-it": "^12.2.1", "@types/mkdirp": "^1.0.1", "@types/node": "^12.7.1", "@types/papaparse": "^5.2.2", "@types/prop-types": "^15.5.2", "@types/qs": "^6.5.1", "@types/react": "^18.0.24", "@types/react-color": "^3.0.5", "@types/react-dom": "^18.0.8", "@types/react-onclickoutside": "^6.0.2", "@types/react-router-dom": "^5.3.3", "@types/react-test-renderer": "^17.0.1", "@types/react-transition-group": "4.4.3", "@types/sortablejs": "^1.3.32", "@types/tinymce": "^4.5.24", "axios": "0.25.0", "bce-sdk-js": "^0.2.9", "concurrently": "^7.3.0", "copy-to-clipboard": "3.3.1", "core-js": "^3.21.0", "css": "3.0.0", "fs-walk": "0.0.2", "glob": "^7.2.0", "history": "^4.7.2", "husky": "^7.0.4", "jest": "^29.0.3", "jest-canvas-mock": "^2.3.0", "jest-environment-jsdom": "^29.0.3", "js-yaml": "^4.1.0", "json5": "^2.2.1", "lint-staged": "^12.3.3", "marked": ">=4.0.12", "mkdirp": "^1.0.4", "moment-timezone": "^0.5.34", "path-to-regexp": "^6.2.0", "postcss": "^8.4.6", "postcss-cli": "^9.1.0", "postcss-custom-properties": "^12.1.5", "prettier": "^2.6.1", "pretty-quick": "^3.1.1", "prismjs": "^1.25.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router": "5.2.1", "react-router-dom": "5.3.0", "react-test-renderer": "^18.0.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-auto-external": "^2.0.0", "rollup-plugin-license": "^2.7.0", "ts-jest": "^29.0.2", "ts-json-schema-generator": "0.96.0", "ts-node": "^10.5.0", "typescript": "^4.6.4"}, "files": ["lib", "esm", "sdk", "schema.json", "revision.json"], "exports": {".": {"require": "./lib/index.js", "import": "./esm/index.js"}, "./lib/helper.css": {"style": "./lib/helper.css", "require": "./lib/helper.css", "import": "./lib/helper.css"}, "./lib/themes/*": {"style": "./lib/themes/*", "require": "./lib/themes/*", "import": "./lib/themes/*"}, "./lib/*": {"require": "./lib/*.js", "import": "./esm/*.js"}, "./esm/*": {"require": "./lib/*.js", "import": "./esm/*.js"}, "./sdk/*": {"style": ["./sdk/*"], "require": ["./sdk/*"], "import": ["./sdk/*"]}, "./schema.json": {"require": ["./schema.json"], "import": ["./schema.json"]}, "./src": "./src/index.tsx", "./*": "./src/*"}, "peerDependencies": {"react": ">=16.8.6", "react-dom": ">=16.8.6"}, "publishConfig": {"access": "public", "registry": "http://registry.caijj.net/repository/npm-caijiajia/"}, "authors": ["<EMAIL>"], "gitHead": "b5ccc65ca8fc5be8ec77dab9883a70915f24ec76"}