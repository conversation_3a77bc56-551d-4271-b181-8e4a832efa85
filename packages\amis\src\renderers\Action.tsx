import React from 'react';
import hotkeys from 'hotkeys-js';
import {
  ActionObject,
  extendObject,
  insertCustomStyle,
IScopedContext,
  isObject,
  Renderer,
  RendererProps,
  ScopedContext,
  uuid,
  attachmentAdpator,
  resolveVariableAndFilter,
  ApiObject,
  str2function,
  qsstringify
} from 'amis-core';
import {filter} from 'amis-core';
import {BadgeObject, Button, Html, SpinnerExtraProps} from 'amis-ui';
import pick from 'lodash/pick';
import omit from 'lodash/omit';

export interface ButtonSchema extends BaseSchema {
  /**
   * 主要用于用户行为跟踪里区分是哪个按钮
   */
  id?: string;

  /**
   * 是否为块状展示，默认为内联。
   */
  block?: boolean;

  /**
   * 禁用时的文案提示。
   */
  disabledTip?: string;

  /**
   * 按钮图标， iconfont 的类名
   */
  icon?: SchemaIcon;

  /**
   * icon 上的css 类名
   */
  iconClassName?: SchemaClassName;

  /**
   * 右侧按钮图标， iconfont 的类名
   */
  rightIcon?: SchemaIcon;

  /**
   * 右侧 icon 上的 css 类名
   */
  rightIconClassName?: SchemaClassName;
  /**
   * loading 上的css 类名
   */
  loadingClassName?: SchemaClassName;

  /**
   * 按钮文字
   */
  label?: string;

  /**
   * 按钮样式
   */
  level?:
    | 'text'
    | 'info'
    | 'success'
    | 'warning'
    | 'danger'
    | 'link'
    | 'primary'
    | 'dark'
    | 'light'
    | 'secondary';

  /**
   * @deprecated 通过 level 来配置
   */
  primary?: boolean;

  /**
   * 按钮大小
   */
  size?: 'xs' | 'sm' | 'md' | 'lg';

  tooltip?: SchemaTooltip;
  tooltipPlacement?: 'top' | 'right' | 'bottom' | 'left';
  iconTooltip?: SchemaTooltip;
  disabledIconTip?: SchemaTooltip;
  rightIconTooltip?: SchemaTooltip;
  disabledRightIconTip?: SchemaTooltip;

  /**
   * 指定按钮类型，支持 button、submit或者reset三种类型。
   */
  type: 'button' | 'submit' | 'reset';

  /**
   * 提示文字，配置了操作前会要求用户确认。
   */
  confirmText?: string;

  /**
   * 确认弹出框标题
   */
  confirmTitle?: string;

  /**
   * 如果按钮在form中，配置此属性会要求用户把指定的字段通过验证后才会触发行为。
   */
  required?: Array<string>;

  /**
   * 激活状态时的样式
   */
  activeLevel?: string;

  /**
   * 激活状态时的类名
   */
  activeClassName?: string;

  /**
   * 如果按钮在弹框中，可以配置这个动作完成后是否关闭弹窗，或者指定关闭目标弹框。
   */
  close?: boolean | string;

  /**
   * 当按钮时批量操作按钮时，默认必须有勾选元素才能可点击，如果此属性配置成 false，则没有点选成员也能点击。
   */
  requireSelected?: boolean;

  /**
   * 是否将弹框中数据 merge 到父级作用域。
   */
  mergeData?: boolean;

  /**
   * 可以指定让谁来触发这个动作。
   */
  target?: string;

  /**
   * 点击后的禁止倒计时（秒）
   */
  countDown?: number;

  /**
   * 倒计时功能配置。仅在 countDown 配置后生效
   */
  countDownConfig?: {
    /**
     * 自动开启倒计时， 默认 false
     */
    autoStartCount?: boolean
    /**
     * 仅倒计时1次，后续无倒计时功能， 默认 false
     */
    onlyCountDownOnce?: boolean
    /**
     * 倒计时结束，触发点击， 默认 false
     */
    clickOnCountEnd?: boolean
    /**
     * 倒计时中，仅用按钮， 默认 false
     */
    enabledOnCounting?: boolean
    /**
     * 点击时去除倒计时(仅在 enabledOnCounting:true 时生效)，默认 false
     * !!文档暂不开放
     */
     continueCountingOnClick?: boolean
  }

  /**
   * 倒计时文字自定义
   */
  countDownTpl?: string;

  /**
   * 角标
   */
  badge?: BadgeObject;

  /**
   * 键盘快捷键
   */
  hotKey?: string;
  /**
   * 是否显示loading效果
   */
  loadingOn?: string;

  /**
   * 自定义事件处理函数
   */
  onClick?: string | any;

  /**
   * 子内容
   */
  body?: SchemaCollection;

  // 去掉 link 类型按钮内边距
  linkWithoutPadding?: boolean;
}

export interface AjaxActionSchema extends ButtonSchema {
  /**
   * 指定为发送 ajax 的行为。
   */
  actionType: 'ajax';

  /**
   * 配置 ajax 发送地址
   */
  api: SchemaApi;

  feedback?: FeedbackDialog;

  reload?: SchemaReload;
  redirect?: string;
  ignoreConfirm?: boolean;

  /**
   * 是否开启请求隔离, 主要用于隔离联动CRUD, Service的请求
   */
  isolateScope?: boolean;
}

export interface DownloadActionSchema
  extends Omit<AjaxActionSchema, 'actionType'> {
  /**
   * 指定为下载行为
   */
  actionType: 'download';
}

export interface SaveAsActionSchema
  extends Omit<AjaxActionSchema, 'actionType'> {
  /**
   * 指定为保存到本地
   */
  actionType: 'saveAs';
}

export interface UrlActionSchema extends ButtonSchema {
  /**
   * 指定为打开链接
   */
  actionType: 'url';

  /**
   * 是否新窗口打开
   */
  blank?: boolean;

  /**
   * 打开的目标地址
   */
  url: string;
}

export interface DialogActionSchema extends ButtonSchema {
  /**
   * 指定为打开弹窗
   */
  actionType: 'dialog';

  /**
   * 弹框详情
   * 文档：https://baidu.gitee.io/amis/docs/components/dialog
   */
  dialog: DialogSchemaBase;

  /**
   * 是否有下一个的表达式，正常可以不用配置，如果想要刷掉某些数据可以配置这个。
   */
  nextCondition?: SchemaExpression;
  reload?: SchemaReload;
  redirect?: string;
}

export interface DrawerActionSchema extends ButtonSchema {
  /**
   * 指定为打开弹窗，抽出式弹窗
   */
  actionType: 'drawer';

  /**
   * 抽出式弹框详情
   * 文档：https://baidu.gitee.io/amis/docs/components/drawer
   */
  drawer: DrawerSchemaBase;

  /**
   * 是否有下一个的表达式，正常可以不用配置，如果想要刷掉某些数据可以配置这个。
   */
  nextCondition?: SchemaExpression;
  reload?: SchemaReload;
  redirect?: string;
}

export interface ToastActionSchema extends ButtonSchema {
  /**
   * 指定为打开弹窗，抽出式弹窗
   */
  actionType: 'toast';

  /**
   * 轻提示详情
   * 文档：https://baidu.gitee.io/amis/docs/components/toast
   */
  toast: ToastSchemaBase;
}

export interface CopyActionSchema extends ButtonSchema {
  /**
   * 指定为复制内容行为
   */
  actionType: 'copy';

  /**
   * 复制啥内容由此配置，支持模板语法。
   */
  copy: SchemaTpl;
}

export interface LinkActionSchema extends ButtonSchema {
  /**
   * 指定为打开链接行为，跟 url 不同的时这个行为为单页模式。
   */
  actionType: 'link';

  /**
   * 跳转到哪？支持配置相对路径。
   */
  link: string;
}

export interface ReloadActionSchema extends ButtonSchema {
  /**
   * 指定为刷新目标组件。
   */
  actionType: 'reload';

  /**
   * 指定目标组件。
   */
  target?: SchemaReload;
}

export interface EmailActionSchema extends ButtonSchema {
  /**
   * 指定为打开邮箱行为
   */
  actionType: 'email';

  /**
   * 收件人邮箱
   */
  to: string;

  /**
   * 抄送邮箱
   */
  cc?: string;

  /**
   * 匿名抄送邮箱
   */
  bcc?: string;

  /**
   * 邮件主题
   */
  subject?: string;

  /**
   * 邮件正文
   */
  body?: string;
}

export interface UpdateLocation extends ButtonSchema {
  /**
   * 动作名称
   */
  actionType: 'updateLocation';

  /**
   * 更新的query数据
   */
  query: object;
}

export interface OtherActionSchema extends ButtonSchema {
  actionType:
    | 'prev'
    | 'next'
    | 'cancel'
    | 'close'
    | 'submit'
    | 'confirm'
    | 'add'
    | 'reset'
    | 'reset-and-submit';
  [propName: string]: any;
}

export interface VanillaAction extends ButtonSchema {
  actionType?: string;
}

/**
 * 按钮动作渲染器。
 * 文档：https://baidu.gitee.io/amis/docs/components/action
 */
export type ActionSchema =
  | AjaxActionSchema
  | UrlActionSchema
  | LinkActionSchema
  | DialogActionSchema
  | DrawerActionSchema
  | ToastActionSchema
  | CopyActionSchema
  | ReloadActionSchema
  | EmailActionSchema
  | OtherActionSchema
  | VanillaAction
  | UpdateLocation;

const ActionProps = [
  'id',
  'dialog',
  'drawer',
  'toast',
  'url',
  'link',
  'confirmText',
  'confirmTitle',
  'tooltip',
  'disabledTip',
  'iconTooltip',
  'disabledIconTip',
  'rightIconTooltip',
  'disabledRightIconTip',
  'className',
  'asyncApi',
  'redirect',
  'size',
  'level',
  'primary',
  'feedback',
  'api',
  'blank',
  'tooltipPlacement',
  'to',
  'cc',
  'bcc',
  'subject',
  'body',
  'content',
  'required',
  'type',
  'actionType',
  'label',
  'icon',
  'rightIcon',
  'reload',
  'target',
  'close',
  'messages',
  'mergeData',
  'index',
  'copy',
  'copyFormat',
  'payload',
  'requireSelected',
  'countDown',
  'countDownConfig',
  'fileName',
  'isolateScope',
  'linkWithoutPadding',
  'needDispatch',
  'query'
];
import {filterContents} from './Remark';
import {ClassNamesFn, themeable, ThemeProps} from 'amis-core';
import {autobind, createObject} from 'amis-core';
import {
  BaseSchema,
  FeedbackDialog,
  SchemaApi,
  SchemaClassName,
  SchemaCollection,
  SchemaExpression,
  SchemaIcon,
  SchemaReload,
  SchemaTooltip,
  SchemaTpl
} from '../Schema';
import {DialogSchema, DialogSchemaBase} from './Dialog';
import {DrawerSchema, DrawerSchemaBase} from './Drawer';
import {ToastSchemaBase} from '../Schema';
import {withBadge, Icon} from 'amis-ui';
import {normalizeApi, str2AsyncFunction} from 'amis-core';
import {TooltipWrapper} from 'amis-ui';
import {ICmptAction} from 'amis-core/lib/actions/CmptAction';

// 构造一个假的 React 事件避免可能的报错，主要用于快捷键功能
// 来自 https://stackoverflow.com/questions/27062455/reactjs-can-i-create-my-own-syntheticevent
export const createSyntheticEvent = <T extends Element, E extends Event>(
  event: E
): React.SyntheticEvent<T, E> => {
  let isDefaultPrevented = false;
  let isPropagationStopped = false;
  const preventDefault = () => {
    isDefaultPrevented = true;
    event.preventDefault();
  };
  const stopPropagation = () => {
    isPropagationStopped = true;
    event.stopPropagation();
  };
  return {
    nativeEvent: event,
    currentTarget: event.currentTarget as EventTarget & T,
    target: event.target as EventTarget & T,
    bubbles: event.bubbles,
    cancelable: event.cancelable,
    defaultPrevented: event.defaultPrevented,
    eventPhase: event.eventPhase,
    isTrusted: event.isTrusted,
    preventDefault,
    isDefaultPrevented: () => isDefaultPrevented,
    stopPropagation,
    isPropagationStopped: () => isPropagationStopped,
    persist: () => {},
    timeStamp: event.timeStamp,
    type: event.type
  };
};

export interface ActionProps
  extends Omit<
      ButtonSchema,
      'className' | 'iconClassName' | 'rightIconClassName' | 'loadingClassName'
    >,
    ThemeProps,
    Omit<
      AjaxActionSchema,
      | 'type'
      | 'className'
      | 'iconClassName'
      | 'rightIconClassName'
      | 'loadingClassName'
    >,
    Omit<
      UrlActionSchema,
      | 'type'
      | 'className'
      | 'iconClassName'
      | 'rightIconClassName'
      | 'loadingClassName'
    >,
    Omit<
      LinkActionSchema,
      | 'type'
      | 'className'
      | 'iconClassName'
      | 'rightIconClassName'
      | 'loadingClassName'
    >,
    Omit<
      DialogActionSchema,
      | 'type'
      | 'className'
      | 'iconClassName'
      | 'rightIconClassName'
      | 'loadingClassName'
    >,
    Omit<
      DrawerActionSchema,
      | 'type'
      | 'className'
      | 'iconClassName'
      | 'rightIconClassName'
      | 'loadingClassName'
    >,
    Omit<
      ToastSchemaBase,
      | 'type'
      | 'className'
      | 'iconClassName'
      | 'rightIconClassName'
      | 'loadingClassName'
    >,
    Omit<
      CopyActionSchema,
      | 'type'
      | 'className'
      | 'iconClassName'
      | 'rightIconClassName'
      | 'loadingClassName'
    >,
    Omit<
      ReloadActionSchema,
      | 'type'
      | 'className'
      | 'iconClassName'
      | 'rightIconClassName'
      | 'loadingClassName'
    >,
    Omit<
      EmailActionSchema,
      | 'type'
      | 'className'
      | 'iconClassName'
      | 'rightIconClassName'
      | 'loadingClassName'
      | 'body'
    >,
    Omit<
      UpdateLocation,
      | 'className'
      | 'iconClassName'
      | 'rightIconClassName'
      | 'loadingClassName'
      | 'body'
    >,
    Omit<
      OtherActionSchema,
      | 'type'
      | 'className'
      | 'iconClassName'
      | 'rightIconClassName'
      | 'loadingClassName'
    >,
    SpinnerExtraProps {
  actionType: any;
  onAction?: (
    e: React.MouseEvent<any> | void | null,
    action: ActionSchema
  ) => void;
  // 可以用来监控这个动作的执行结果，包括成功与失败。
  onActionSensor?: (promise?: Promise<any>) => void;
  isCurrentUrl?: (link: string) => boolean;
  onClick?:
    | ((e: React.MouseEvent<any>, props: any) => void)
    | string
    | Function
    | null;
  componentClass: React.ElementType;
  tooltipContainer?: any;
  data?: any;
  isMenuItem?: boolean;
  active?: boolean;
}

const allowedType = ['button', 'submit', 'reset'];

interface ActionState {
  inCountDown: boolean; // 是否在倒计时
  countDownEnd: number; // 倒计时结束的精确时间
  timeLeft: number; // 倒计时剩余时间
}

export class Action extends React.Component<ActionProps, ActionState> {
  static defaultProps = {
    type: 'button' as 'button',
    componentClass: 'button' as React.ElementType,
    tooltipPlacement: 'bottom' as 'bottom',
    activeClassName: 'is-active',
    countDownTpl: 'Action.countDown',
    countDown: 0,
  };

  state: ActionState = {
    inCountDown: false,
    countDownEnd: 0,
    timeLeft: 0
  };

  localStorageKey?: string;

  // 倒计时定时器
  countdownTimer: any = 0;
  isCountDownOnce: boolean = false //

  dom: any;

  constructor(props: ActionProps) {
    super(props);

    if (this.props?.$schema?.id) {
      this.localStorageKey =
        'amis-countdownend-' +
        (this.props.name || '') +
        this.props?.$schema?.id;
    }

    const countDownEnd = this.localStorageKey
      ? parseInt(localStorage.getItem(this.localStorageKey) || '0')
      : 0;

    if (countDownEnd && this.props.countDown) {
      if (Date.now() < countDownEnd) {
        this.state = {
          inCountDown: true,
          countDownEnd,
          timeLeft: Math.floor((countDownEnd - Date.now()) / 1000)
        };
        this.handleCountDown();
      }
    }
  }

  @autobind
  async handleAction(
    e: React.MouseEvent<any>,
    options?: {
      firedByCountDownEnd?: boolean
    }
  ) {
    const {onAction, onActionSensor, disabled, countDown, countDownConfig, env, translate: __} = this.props;
    // https://reactjs.org/docs/legacy-event-pooling.html
    e.persist?.(); // 等 react 17之后去掉 event pooling 了，这个应该就没用了
    let onClick = this.props.onClick;

    if (typeof onClick === 'string') {
      onClick = str2AsyncFunction(onClick, 'event', 'props');
    }
    const result: any = onClick && (await onClick(e, this.props));

    if (
      disabled ||
      e.isDefaultPrevented?.() ||
      result === false ||
      !onAction ||
      /**
       * 非自动倒计时的情况下：处于倒计时中，直接不处理点击事件
       */
      (this.state.inCountDown && !countDownConfig?.enabledOnCounting)
    ) {
      return;
    }

    e.preventDefault?.();
    const action = pick(this.props, ActionProps) as ActionSchema;
    const actionType = action.actionType;

    // ajax 会在 wrapFetcher 里记录，这里再处理就重复了，因此去掉
    // add 一般是 input-table 之类的，会触发 formItemChange，为了避免重复也去掉
    if (
      actionType !== 'ajax' &&
      actionType !== 'download' &&
      actionType !== 'downloadFile' &&
      actionType !== 'add'
    ) {
      env?.tracker(
        {
          eventType: actionType || this.props.type || 'click',
          eventData: omit(action, ['type', 'actionType', 'tooltipPlacement'])
        },
        this.props
      );
    }

    // 处理下载
    if (['download', 'downloadFile'].includes(actionType || '')) {
      // download/downloadFile 是一种 ajax 的简写
      action.actionType = 'ajax';

      const apiObject = normalizeApi((action as AjaxActionSchema).api);
      apiObject.responseType = 'blob';

      // downloadFile 文件，默认添加 attachmentAdpator 适配器
      if (actionType === 'downloadFile') {
        const originAdaptor = typeof apiObject.adaptor === 'string'
          ? str2function(apiObject.adaptor,
              'payload',
              'response',
              'api'
            ) as any
          : apiObject.adaptor

        apiObject.adaptor = async (payload: object, response: any, api: ApiObject) => {
          let apiPayload: any = payload
          if (originAdaptor) {
            apiPayload = originAdaptor(payload, response, api)
          }

          if (!response.headers?.['content-disposition'] || action.fileName) {
            const fileName = typeof action.fileName === 'string'
              ? resolveVariableAndFilter(action.fileName, this.props.data || {}, '| raw')
              : ''
            if (!response.headers) {
              response.headers = {}
            }
            response.headers['content-disposition'] = `attachment; filename=${fileName || '未命名文件'}`
          }

          const isSuccess = response.status === 200 && ([0, undefined].includes(apiPayload.status))

          // 请求成功时 调用下载方法
          if (isSuccess) {
            attachmentAdpator({
              ...response,
              data: apiPayload.data || apiPayload
            }, __);
          }
          if (!isSuccess && Object.prototype.toString.call(apiPayload) === '[object Blob]') {
            let blobText = ''
            try {
              blobText = await apiPayload.text();
              apiPayload = JSON.parse(blobText);
            } catch (error) {
            }
          }
          return {
            status: apiPayload.status || 0,
            msg: apiPayload.msg || apiPayload.message || (
              isSuccess
                ? (__?.('Embed.downloading') || '文件开始下载')
                : (__?.('Embed.downloadError') || '文件下载失败')
            )
          }
        }
      }

      (action as AjaxActionSchema).api = apiObject;
    }

    const sensor: any = onAction(e, action);
    if (sensor?.then) {
      onActionSensor?.(sensor);
      await sensor;
    }

    // 倒计时场景, 被用户鼠标点击时
    if (countDown && !options?.firedByCountDownEnd) {
      if (this.state.inCountDown && !countDownConfig?.continueCountingOnClick) {
        // 处于倒计时中， 点击清除倒计时
        this.handleCountDownEnd()
      } else if (
        !this.state.inCountDown &&
        !(countDownConfig?.onlyCountDownOnce && this.isCountDownOnce)
      ) {
        // 不处于倒计时中且可点击多次时，重新开启倒计时
        this.handleCountDownStart()
      }
    }
  }

  @autobind
  handleCountDown() {
    // setTimeout 一般会晚于 1s，经过几十次后就不准了，所以使用真实时间进行 diff
    const timeLeft = Math.floor((this.state.countDownEnd - Date.now()) / 1000) + 1;

    if (timeLeft <= 0) {
      this.handleCountDownEnd({
        isCountDownEnd: true
      })
    } else {
      this.setState({
        timeLeft: timeLeft
      });
      if (this.countdownTimer) {
        clearTimeout(this.countdownTimer)
      }
      this.countdownTimer = setTimeout(() => {
        this.handleCountDown();
      }, 1000);
    }
  }

  @autobind
  handleCountDownStart() {
    const { countDown, countDownConfig } = this.props;
    // 不存在 倒计时 配置，不处理后续逻辑
    if (!countDown) {
      return
    }

    const countDownEnd = Date.now() + countDown * 1000;
    this.setState({
      countDownEnd: countDownEnd,
      inCountDown: true,
      timeLeft: countDown
    });

    if (this.localStorageKey) {
      localStorage.setItem(this.localStorageKey, String(countDownEnd));
    }

    if (this.countdownTimer) {
      clearTimeout(this.countdownTimer)
    }
    this.countdownTimer = setTimeout(() => {
      this.handleCountDown();
    }, 1000);
  }

  @autobind
  async handleCountDownEnd(options?: { isCountDownEnd?: boolean }) {
    const { countDownConfig, dispatchEvent, data } = this.props

    const { isCountDownEnd = false } = options || {}

    if (this.countdownTimer) {
      clearTimeout(this.countdownTimer)
      this.countdownTimer = null
    }

    this.localStorageKey && localStorage.removeItem(this.localStorageKey);

    this.setState({
      inCountDown: false,
      timeLeft: 0
    });

    this.isCountDownOnce = true

    // 结束倒计时
    if (isCountDownEnd) {
      // 自动触发点击事件
      if(countDownConfig?.clickOnCountEnd) {
        const clickEvent = new Event("click")
        this.handleAction(clickEvent as any, {firedByCountDownEnd: true})
      }

      // 触发 "countDownEnd" 动作
      await dispatchEvent('countDownEnd', data);
    }
  }

  @autobind
  getIconElement(direction: 'left' | 'right' = 'left') {
    const {
      icon,
      rightIcon,
      iconClassName,
      rightIconClassName,
      disabled,
      iconTooltip,
      disabledIconTip,
      rightIconTooltip,
      disabledRightIconTip,
      classnames: cx,
      tooltipContainer,
      tooltipTrigger,
      tooltipRootClose,
    } = this.props;

    const finalIcon = direction === 'left' ? icon : rightIcon;
    const finalIconClassName = direction === 'left' ? iconClassName : rightIconClassName;
    const tooltip = direction === 'left'
      ? (disabled ? disabledIconTip : iconTooltip)
      : (disabled ? disabledRightIconTip : rightIconTooltip);

    let iconElement = (
      <Icon
        cx={cx}
        icon={finalIcon}
        className={cx('Button-icon','icon')}
        classNameProp={finalIconClassName}
      />
    );

    // 防止将空值放到Tooltip子组件中
    if (!React.isValidElement(iconElement)) {
      return null;
    }

    return (
      <TooltipWrapper
        tooltip={tooltip}
        container={tooltipContainer}
        trigger={tooltipTrigger}
        rootClose={tooltipRootClose}
      >
        {iconElement}
      </TooltipWrapper>
    )
  }

  @autobind
  componentDidMount() {
    const {hotKey, countDown, countDownConfig, selfLoading} = this.props;

    /**
     * 自动开启倒计时
     *
     * selfLoading 的场景下，会包一层 service 再渲染 button。会渲染执行2次 componentDidMount。
     * 因此需要 排除 selfLoading 的情况。
     */
    if (!selfLoading && countDown && countDownConfig?.autoStartCount) {
      this.handleCountDownStart()
    }

    if (hotKey) {
      hotkeys(hotKey, event => {
        event.preventDefault();
        const click = new MouseEvent('click', {
          bubbles: true,
          cancelable: true
        });
        this.handleAction(createSyntheticEvent(click) as any);
      });
    }
  }

  @autobind
  componentWillUnmount() {
    const {hotKey} = this.props;

    if (hotKey) {
      hotkeys.unbind(hotKey);
    }

    // 移除未执行的倒计时
    if (this.countdownTimer) {
      clearTimeout(this.countdownTimer)
      this.countdownTimer = null
    }
  }

  render() {
    const {
      type,
      icon,
      loadingClassName,
      primary,
      size,
      level,
      countDownTpl,
      block,
      className,
      style,
      componentClass,
      tooltip,
      disabledTip,
      tooltipPlacement,
      actionType,
      link,
      data,
      translate: __,
      activeClassName,
      isCurrentUrl,
      isMenuItem,
      active,
      activeLevel,
      tooltipTrigger,
      tooltipContainer,
      tooltipRootClose,
      loading,
      body,
      render,
      onMouseEnter,
      onMouseLeave,
      classnames: cx,
      classPrefix: ns,
      loadingConfig,
      css,
      id,
      linkWithoutPadding,
      selfLoading,
      store,
      countDownConfig,
      _showLoading,
      ghost,
    } = this.props;
    insertCustomStyle(
      css,
      [
        {
          key: 'className',
          value: className,
          weights: {
            hover: {
              suf: ':not(:disabled):not(.is-disabled)'
            },
            active: {suf: ':not(:disabled):not(.is-disabled)'}
          }
        }
      ],
      id
    );

    if (actionType !== 'email' && body) {
      return (
        <TooltipWrapper
          classPrefix={ns}
          classnames={cx}
          placement={tooltipPlacement}
          tooltip={filterContents(tooltip, data, render)}
          container={tooltipContainer}
          trigger={tooltipTrigger}
          rootClose={tooltipRootClose}
        >
          <div
            className={cx('Action', className)}
            style={style}
            onClick={this.handleAction}
            onMouseEnter={onMouseEnter}
            onMouseLeave={onMouseLeave}
          >
            {render('body', body) as JSX.Element}
          </div>
        </TooltipWrapper>
      );
    }


    let label: JSX.Element | string | undefined  = this.props.label;
    let disabled = this.props.disabled;
    let isActive = !!active;

    if(selfLoading) {
      return render('body', {
        type: 'service',
        loadingConfig: {
          show: false,
        },
        renderRoot: false,
        // className: 'border-none inline-block',
        api: {
          url: '/',
          sendOn: '${false}'
        },
        body: {
          disabled, // issue#711, 解决crud批量操作按钮禁用状态失效问题
          ...this.props.$schema,
          selfLoading: false, // 避免死循环
          _showLoading: true, // 给实际渲染的元素打一个标记，有这个标记使用 store.loading
        }
      }) as JSX.Element;
    }

    if (actionType === 'link' && !isActive && link && isCurrentUrl) {
      isActive = isCurrentUrl(link);
    }

    // 倒计时
    if (this.state.inCountDown) {
      label = filterContents(__(countDownTpl), {
        ...data,
        timeLeft: this.state.timeLeft
      }, render) as string;
      // countDownTpl 支持 html 字符串
      if (!!label && typeof label === 'string') {
        label = <Html html={label} />
      }
      disabled = !countDownConfig?.enabledOnCounting;
    }

    const iconElement = this.getIconElement();
    const rightIconElement = this.getIconElement('right');

    return (
      <Button
        loadingConfig={loadingConfig}
        className={cx(className, {
          [activeClassName || 'is-active']: isActive,
          'is-ghost': ghost,
        })}
        style={style}
        size={size}
        level={
          activeLevel && isActive
            ? activeLevel
            : level || (primary ? 'primary' : undefined)
        }
        loadingClassName={loadingClassName}
        loading={_showLoading ? (store.loading || loading) : loading}
        onClick={this.handleAction}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        type={type && ~allowedType.indexOf(type) ? type : 'button'}
        disabled={disabled}
        componentClass={isMenuItem ? 'a' : componentClass}
        overrideClassName={isMenuItem}
        tooltip={filterContents(tooltip, data, render)}
        disabledTip={filterContents(disabledTip, data, render)}
        tooltipPlacement={tooltipPlacement}
        tooltipContainer={tooltipContainer}
        tooltipTrigger={tooltipTrigger}
        tooltipRootClose={tooltipRootClose}
        block={block}
        iconOnly={!!(icon && !label && level !== 'link')}
        linkWithoutPadding={linkWithoutPadding}
      >
        {!loading ? iconElement : ''}
        {label
          ? typeof label === 'string'
            ? <span>{filter(String(label), data)}</span>
            : label
          : null}
        {rightIconElement}
      </Button>
    );
  }
}

export default themeable(Action);

export type ActionRendererProps = RendererProps &
  Omit<ActionProps, 'onAction' | 'isCurrentUrl' | 'tooltipContainer'> & {
    onAction: (
      e: React.MouseEvent<any> | string | void | null,
      action: object,
      data: any
    ) => void;
    btnDisabled?: boolean;
  };

@Renderer({
  type: 'action'
})
// @ts-ignore 类型没搞定
@withBadge
export class ActionRenderer extends React.Component<ActionRendererProps> {
  static contextType = ScopedContext;

  state = {
    loading: false,
  }

  constructor(props: ActionRendererProps, scoped: IScopedContext) {
    super(props);

    scoped.registerComponent(this);
  }

  componentWillUnmount() {
    const scoped = this.context as IScopedContext;
    scoped.unRegisterComponent(this);
  }

  /**
   * 动作处理
   */
  doAction(
    action: ActionObject,
    args: {
      value?: string | {[key: string]: string};
    }
  ) {
    const actionType = action?.actionType as any;

    if (actionType === 'click') {
      this.handleAction(actionType, action);
    }
  }

  @autobind
  async handleAction(
    e: React.MouseEvent<any> | string | void | null,
    action: any
  ) {
    const {env, onAction, data, ignoreConfirm, dispatchEvent, $schema, _showLoading} =
      this.props;
    let mergedData = data;

    if (action?.actionType === 'click' && isObject(action?.args)) {
      mergedData = createObject(data, {
        ...action.args,
        nativeEvent: e
      });
    }

    const hasOnEvent = $schema.onEvent && Object.keys($schema.onEvent).length;
    let confirmText: string = '';
    // 有些组件虽然要求这里忽略二次确认，但是如果配了事件动作还是需要在这里等待二次确认提交才可以
    if (
      (!ignoreConfirm || hasOnEvent) &&
      action.confirmText &&
      env.confirm &&
      (confirmText = filter(action.confirmText, mergedData))
    ) {
      let confirmed = await env.confirm(confirmText, filter(action.confirmTitle, mergedData));
      if (confirmed) {
        // 触发渲染器事件
        const rendererEvent = await dispatchEvent(
          e as React.MouseEvent<any> | string,
          mergedData
        );

        // 阻止原有动作执行
        if (rendererEvent?.prevented) {
          throw new Error('prevented');
        }

        // 因为crud里面也会处理二次确认，所以如果按钮处理过了就跳过crud的二次确认
        onAction(e, {...action, ignoreConfirm: !!hasOnEvent}, mergedData);
      } else if (action.countDown) {
        throw new Error('cancel');
      }
    } else {
      // _showLoading 为true表示是配置了selfLoading被service包裹的按钮，这时候去设置loading
      _showLoading && this.setState({ loading: true })
      // 触发渲染器事件
      const rendererEvent = await dispatchEvent(
        e as React.MouseEvent<any> | string,
        mergedData
      );
      _showLoading && this.setState({ loading: false })

      // 阻止原有动作执行
      if (rendererEvent?.prevented) {
        throw new Error('prevented');
      }

      onAction(e, action, mergedData);
    }
  }

  @autobind
  handleMouseEnter(e: React.MouseEvent<any>) {
    const {dispatchEvent, data} = this.props;
    dispatchEvent(
      e,
      createObject(data, {
        nativeEvent: e
      })
    );
  }

  @autobind
  handleMouseLeave(e: React.MouseEvent<any>) {
    const {dispatchEvent, data} = this.props;
    dispatchEvent(
      e,
      createObject(data, {
        nativeEvent: e
      })
    );
  }

  @autobind
  isCurrentAction(link: string) {
    const {env, data} = this.props;
    return env.isCurrentUrl(filter(link, data));
  }

  render() {
    const {env, disabled, btnDisabled, loading, ...rest} = this.props;

    return (
      <Action
        {...(rest as any)}
        env={env}
        disabled={disabled || btnDisabled}
        onAction={this.handleAction}
        onMouseEnter={this.handleMouseEnter}
        onMouseLeave={this.handleMouseLeave}
        loading={loading || this.state.loading}
        isCurrentUrl={this.isCurrentAction}
        tooltipContainer={
          env.getModalContainer ? env.getModalContainer : undefined
        }
      />
    );
  }
}

@Renderer({
  type: 'button'
})
export class ButtonRenderer extends ActionRenderer {}

@Renderer({
  type: 'submit'
})
export class SubmitRenderer extends ActionRenderer {}

@Renderer({
  type: 'reset'
})
export class ResetRenderer extends ActionRenderer {}
