import {
  isObject,
  Renderer,
  RendererProps,
  resolveVariableAndFilter,
} from 'amis-core';
import { dayjs } from 'dataseed-ui';
import mapValues from 'lodash/mapValues';
import React from 'react';
import { BaseSchema } from '../../Schema';

// @ts-ignore
export interface DataResourceAuthorizedSchema extends BaseSchema {
  type: 'data-resource-authorized';

  /**
   * 数据资源类型下拉选项
   */
  dataResourceTypeList?: string;
}

// @ts-ignore
export interface DataResourceAuthorizedProps
  extends RendererProps,
  Omit<DataResourceAuthorizedSchema, 'type'> { }

const SUBJECT_TYPE_MAP = {
  USER: 'USER',
  ORG: 'ORG',
};

const subjectType = {
  [SUBJECT_TYPE_MAP.USER]: '授权给账号',
  [SUBJECT_TYPE_MAP.ORG]: '授权给组织',
};

const EXPIRED_TIME_TYPE_MAP = {
  FIXEDDATE: 'fixedDate',
  PERMANENT: 'permanent',
};

const expiredTimeType = {
  [EXPIRED_TIME_TYPE_MAP.FIXEDDATE]: '固定日期',
  [EXPIRED_TIME_TYPE_MAP.PERMANENT]: '永久',
};

const MODAL_TYPE_MAP = {
  AUTHORIZED: 'AUTHORIZED',
  APPLIED: 'APPLIED',
};

const handleData = (data, otherData) => {
  return {
    dataResourceOuterName: data?.dataResourceOuterName,
    dataResourceOuterCode: data?.dataResourceOuterCode,
    dataResourceTypeCode: data?.dataResourceTypeCode,
    subjectType: data?.subjectType,
    subjectCode:
      data?.subjectType === SUBJECT_TYPE_MAP.USER
        ? data?.userValue
        : data?.orgValue,
    resourceOperatingMetaCode: data?.resourceOperatingMetaCode,
    expiredTime:
      data.expiredTimeType === EXPIRED_TIME_TYPE_MAP.FIXEDDATE
        ? data.expiredTimeValue + ' 23:59:59'
        : undefined,
    description: data?.description,
    sensitive: otherData.sensitive,
  };
};

export class DataResourceAuthorized extends React.Component<DataResourceAuthorizedProps> {
  static defaultProps = {
    wrapperDialogId: 'dialogId',
    customDataResouceType: {
      label: '资源类型',
      disabled: false,
    },
    customDataResouceName: {
      label: '资源名称',
    },
    customOperationType: {
      label: '操作类型',
    },
    customExpiredTimeType: {
      label: '授权到期日',
    },
    customExpiredTimeValue: {},
    customDescription: {
      label: '申请备注',
    },
    customSubjectType: {
      label: '授权类型',
    },
    customUserValue: {
      label: '授权给账号',
    },
    customDataResourceOwner: {
      label: '拥有者',
    },
    customOrgValue: {
      label: '授权给组织',
    },
    customTableHeader: {
      dataResouceTypeLabel: '资源类型',
      operationTypeLabel: '授权操作',
    },
    // 申请权限
    appliedApi: {
      url: '/idaasopr/data-authorization/opt/apply',
      method: 'post',
    },
    // 申请权限-前置校验
    appliedCheckApi: {
      url: '/idaasopr/data-authorization/opt/apply/check',
      method: 'post',
    },
    // 授权列表
    authorizationListApi: {
      url: '/idaasopr/data-authorization/opt/query',
      method: 'post',
    },
    // 获取用户列表
    userApi: {
      url: '/idaasopr/v2/users',
    },
    // 获取组织列表
    orgApi: {
      url: '/idaasopr/orgs/tree',
    },
    // 移除权限
    deleteAuthApi: {
      url: '/idaasopr/data-authorization/${dataAuthorizationId}',
      method: 'delete',
    },
    // 授权
    addAuthApi: {
      url: '/idaasopr/data-authorization',
      method: 'post',
    },
    // 授权-前置校验
    addAuthCheckApi: {
      url: '/idaasopr/data-authorization/check',
      method: 'post',
    },
    // 查询数据资源类型详情
    authDetailApi: {
      url: '/idaasopr/data-resource-type/${dataResourceTypeCode}',
    },
    // 修改权限
    updateAuthApi: {
      url: '/idaasopr/data-authorization/${dataAuthorizationId}',
      method: 'put',
    },
    sensitive: '对内公开',
  };

  constructor(props: DataResourceAuthorizedProps) {
    super(props);

    this.buildSchema = this.buildSchema.bind(this);
    this.buildAppliedSchema = this.buildAppliedSchema.bind(this);
    this.resolveData = this.resolveData.bind(this);
    this.buildAuthorizedSchema = this.buildAuthorizedSchema.bind(this);
    this.buildCommonSchema = this.buildCommonSchema.bind(this);
  }

  /**
   * 解析表达式数据
   * @param field 需要解析的数据
   * @returns
   */
  resolveData = (field: any) => {
    const { data } = this.props;

    if (typeof field === 'string') {
      return resolveVariableAndFilter(field, data, '| raw');
    }

    if (isObject(field)) {
      return mapValues(field, val => {
        if (typeof val !== 'string') {
          return val;
        }
        return resolveVariableAndFilter(val, data, '| raw');
      });
    }

    return field;
  };

  // 渲染两种模式公共的schema
  buildCommonSchema() {
    const {
      dataResourceTypeList,
      customDataResouceType,
      authDetailApi,
      customDataResouceName,
      customOperationType,
      customDataResourceOwner,
      data,
    } = this.props;

    const dataResouceTypeOptions: any = this.resolveData(dataResourceTypeList);
    let _operations: any[] = [];
    // 回填默认值的逻辑
    if (data.dataResourceTypeCode) {
      _operations =
        dataResouceTypeOptions?.find(
          (item: any) =>
            item.dataResourceTypeCode === data.dataResourceTypeCode,
        )?.dataResourceOperatingList || [];
    }

    const commonSchema = [
      {
        type: 'select',
        options: dataResouceTypeOptions,
        valueField: 'dataResourceTypeCode',
        labelField: 'dataResourceTypeName',
        required: true,
        autoFill: {
          dataResourceOuterName: '${dataResourceOuterName}',
          dataResourceOuterCode: '${dataResourceOuterCode}',
        },
        ...customDataResouceType,
        name: 'dataResourceTypeCode',
        onEvent: {
          // selectItems中如果有操作类型列表用操作类型列表，没有的话发送请求获取操作类型列表
          change: {
            actions: [
              // 发送接口
              {
                actionType: 'ajax',
                expression:
                  '!event.data.selectedItems?.dataResourceOperatingList?.length',
                args: {
                  api: {
                    adaptor: (payload, res) => {
                      return {
                        ...res,
                        status: res.status === 200 ? 0 : res.status,
                        data: payload,
                      };
                    },
                    ...authDetailApi,
                  },
                },
              },
              // 获取接口中的操作类型列表
              {
                actionType: 'setValue',
                componentId: '__form',
                expression:
                  '!event.data.selectedItems?.dataResourceOperatingList?.length && event.data?.responseResult?.responseStatus === 0',
                args: {
                  value: {
                    _operations: '${event.data.dataResourceOperatingList}',
                    resourceOperatingMetaCode:
                      '${GET(${event.data.dataResourceOperatingList|first}, "code")}',
                  },
                },
              },
              // selectItems中存在操作类型列表，直接使用
              {
                actionType: 'setValue',
                componentId: '__form',
                expression:
                  'event.data.selectedItems?.dataResourceOperatingList',
                args: {
                  value: {
                    _operations:
                      '${event.data.selectedItems.dataResourceOperatingList}',
                    resourceOperatingMetaCode:
                      '${GET(${event.data.selectedItems.dataResourceOperatingList|first}, "code")}',
                  },
                },
              },
            ],
          },
        },
      },
      {
        type: 'hidden',
        name: 'dataResourceOuterCode',
      },
      {
        type: 'input-text',
        label: customDataResouceName?.label,
        static: true,
        name: 'dataResourceOuterName',
      },
      {
        type: 'input-text',
        label: customDataResourceOwner?.label,
        static: true,
        name: 'dataResourceOwner',
      },
      {
        type: 'hidden',
        id: '_operations',
        name: '_operations',
        value: _operations,
      },
      {
        type: 'select',
        id: '_resourceOperatingMetaCode',
        source: '${_operations}',
        valueField: 'code',
        labelField: 'name',
        required: true,
        ...customOperationType,
        name: 'resourceOperatingMetaCode',
      },
    ];

    return commonSchema;
  }

  // 授权的schema
  buildAuthorizedSchema() {
    const {
      dataResourceTypeList,
      customSubjectType,
      customUserValue,
      customOrgValue,
      customExpiredTimeType,
      customExpiredTimeValue,
      customTableHeader,
      authorizationListApi,
      userApi,
      orgApi,
      deleteAuthApi,
      updateAuthApi,
    } = this.props;

    const dataResouceTypeOptions: any = this.resolveData(dataResourceTypeList);
    const common = this.buildCommonSchema();

    const authorizedSchema = [
      ...common,
      {
        type: 'radios',
        required: true,
        value: SUBJECT_TYPE_MAP.USER,
        options: subjectType,
        ...customSubjectType,
        name: 'subjectType',
      },
      {
        type: 'select',
        visibleOn: `\${subjectType === "${SUBJECT_TYPE_MAP.USER}"}`,
        required: true,
        autoComplete: {
          data: {
            pageNo: 1,
            pageSize: 50,
            name: '${term}',
          },
          adaptor: (payload: any) => {
            return (
              payload?.data?.map((item: any) => ({
                label: item?.name,
                value: item?.userId,
              })) || []
            );
          },
          ...userApi,
        },
        ...customUserValue,
        name: 'userValue',
      },
      {
        type: 'tree-select',
        visibleOn: `\${subjectType === "${SUBJECT_TYPE_MAP.ORG}"}`,
        required: true,
        searchable: true,
        valueField: 'corporationIdOrOrgId',
        labelField: 'name',
        source: {
          ...orgApi,
        },
        ...customOrgValue,
        name: 'orgValue',
      },
      {
        type: 'radios',
        value: EXPIRED_TIME_TYPE_MAP.FIXEDDATE,
        autoFill: {
          expiredTimeValue: dayjs().add(2, 'y').format('YYYY-MM-DD'),
        },
        options: expiredTimeType,
        required: true,
        ...customExpiredTimeType,
        name: 'expiredTimeType',
      },
      {
        type: 'ds-date-picker',
        value: dayjs().add(2, 'y').format('YYYY-MM-DD'),
        visibleOn: `\${expiredTimeType === "${EXPIRED_TIME_TYPE_MAP.FIXEDDATE}"}`,
        required: true,
        ...customExpiredTimeValue,
        name: 'expiredTimeValue',
      },
      {
        type: 'flex',
        justify: 'end',
        items: [
          {
            type: 'submit',
            label: '添加',
            primary: true,
            close: false,
          },
        ],
      },
      {
        type: 'crud',
        name: '_crud',
        id: '_crud',
        columnsTogglable: false,
        perPage: 5,
        data: {
          initFlag: dataResouceTypeOptions,
        },
        footToolbar: [
          {
            type: 'pagination',
            layout: 'total,pager,perPage,go',
            mode: 'normal',
            showPageInput: true,
            disabled: false,
          },
        ],
        api: {
          data: {
            pageNo: '${page}',
            pageSize: '${perPage}',
            typeAndResource: dataResouceTypeOptions?.map((item: any) => {
              const { dataResourceTypeCode, dataResourceOuterCode } = item || {};

              return {
                dataResourceTypeCode,
                dataResourceOuterCode,
              };
            }),
          },
          sendOn: '${initFlag}',
          trackExpression: '${initFlag}',
          adaptor: (payload: any, res, api: any) => {
            const { pageNo, pageSize } = api?.data || {};
            // 保证非第一页的序号不是从0开始
            return {
              list: payload.data?.map?.((item, index: number) => ({
                ...item,
                index: pageNo * pageSize - pageSize + index + 1,
              })),
              count: payload.total,
            };
          },
          ...authorizationListApi,
        },
        columns: [
          {
            label: '序号',
            name: 'index',
          },
          {
            label: customTableHeader.dataResouceTypeLabel || '资源类型',
            name: 'resourceTypeMetaName',
          },
          {
            label: customTableHeader.customDataResouceName || '资源名称',
            name: 'resourceOuterName',
          },
          {
            label: '授权类型',
            name: 'subjectType',
            type: 'mapping',
            map: subjectType,
            width: 120,
          },
          {
            label: '授权对象',
            name: 'subjectName',
          },
          {
            label: '授权到期日',
            name: 'expiredTime',
            width: 150,
          },
          {
            label: customTableHeader.operationTypeLabel || '授权操作',
            name: 'resourceOperatingMetaName',
          },
          {
            label: '授权时间',
            name: 'createdAt',
            width: 150,
          },
          {
            label: '操作',
            name: 'actions',
            type: 'operation',
            fixed: 'right',
            buttons: [
              {
                type: 'button',
                level: 'link',
                label: '移除',
                actionType: 'dialog',
                dialog: {
                  title: '确认移除',
                  body: '确认移除该授权吗？',
                  actions: [
                    {
                      type: 'button',
                      label: '取消',
                      actionType: 'cancel',
                    },
                    {
                      type: 'button',
                      label: '确认',
                      level: 'primary',
                      actionType: 'confirm',
                      onEvent: {
                        click: {
                          actions: [
                            {
                              actionType: 'ajax',
                              args: {
                                api: {
                                  data: {},
                                  adaptor: (_, res) => {
                                    return {
                                      ...res,
                                      msg: res.status === 200 ? '操作成功' : '操作失败',
                                      data: {},
                                      status: res.status === 200 ? 0 : res.status,
                                    };
                                  },
                                  ...deleteAuthApi,
                                },
                              }
                            },
                            {
                              actionType: 'reload',
                              componentId: '_crud',
                              expression:
                              '${event.data.responseResult.responseStatus === 0}',
                            },
                          ]
                        }
                      },
                    }
                  ]
                }
              },
              {
                type: 'button',
                level: 'link',
                label: '更改授权到期日',
                actionType: 'dialog',
                dialog: {
                  title: '',
                  body: {
                    type: 'form',
                    data: {
                      expiredTimeType: `\${IF(expiredTime, "${EXPIRED_TIME_TYPE_MAP.FIXEDDATE}", "${EXPIRED_TIME_TYPE_MAP.PERMANENT}")}`,
                      expiredTimeValue: '${LEFT(expiredTime, 10)}',
                    },
                    api: {
                      requestAdaptor: (api: any) => {
                        const { data } = api;

                        return {
                          ...api,
                          data: {
                            expiredTime:
                              data.expiredTimeType ===
                                EXPIRED_TIME_TYPE_MAP.FIXEDDATE
                                ? data.expiredTimeValue
                                : undefined,
                          },
                        };
                      },
                      adaptor: (_, res) => {
                        return {
                          ...res,
                          msg: res.status === 200 ? '操作成功' : '操作失败',
                          data: {},
                          status: res.status === 200 ? 0 : res.status,
                        };
                      },
                      ...updateAuthApi,
                    },
                    body: [
                      {
                        type: 'radios',
                        value: EXPIRED_TIME_TYPE_MAP.FIXEDDATE,
                        autoFill: {
                          expiredTimeValue: dayjs()
                            .add(2, 'y')
                            .format('YYYY-MM-DD'),
                        },
                        options: expiredTimeType,
                        required: true,
                        ...customExpiredTimeType,
                        name: 'expiredTimeType',
                      },
                      {
                        type: 'ds-date-picker',
                        value: dayjs().add(2, 'y').format('YYYY-MM-DD'),
                        visibleOn: `\${expiredTimeType === "${EXPIRED_TIME_TYPE_MAP.FIXEDDATE}"}`,
                        required: true,
                        ...customExpiredTimeValue,
                        name: 'expiredTimeValue',
                      },
                    ],
                  },
                },
              },
            ],
          },
        ],
      },
    ];

    return authorizedSchema;
  }

  // 权限申请的schema
  buildAppliedSchema() {
    const { customExpiredTimeType, customExpiredTimeValue, customDescription } =
      this.props;

    const common = this.buildCommonSchema();

    const applisedSchema = [
      ...common,
      {
        type: 'radios',
        value: EXPIRED_TIME_TYPE_MAP.FIXEDDATE,
        autoFill: {
          expiredTimeValue: dayjs().add(2, 'y').format('YYYY-MM-DD'),
        },
        options: expiredTimeType,
        required: true,
        ...customExpiredTimeType,
        name: 'expiredTimeType',
      },
      {
        type: 'ds-date-picker',
        value: dayjs().add(2, 'y').format('YYYY-MM-DD'),
        visibleOn: `\${expiredTimeType === "${EXPIRED_TIME_TYPE_MAP.FIXEDDATE}"}`,
        required: true,
        ...customExpiredTimeValue,
        name: 'expiredTimeValue',
      },
      {
        type: 'textarea',
        validations: {
          maxLength: 100,
        },
        required: true,
        ...customDescription,
        name: 'description',
      },
    ];

    return applisedSchema;
  }

  buildSchema() {
    const {
      appliedApi,
      sensitive,
      modalType,
      addAuthApi,
      addAuthCheckApi,
      appliedCheckApi,
      wrapperDialogId,
      authorizationListApi,
      dataResourceTypeList,
    } = this.props;

    const submitApi = {
      api: {
        ...(modalType === MODAL_TYPE_MAP.AUTHORIZED ? addAuthApi : appliedApi),
        method: 'post',
        requestAdaptor: api => {
          const { data } = api;
          return {
            ...api,
            data: handleData(data, { sensitive }),
          };
        },
        adaptor: (_, res) => {
          return {
            ...res,
            msg: res.status === 200 ? '操作成功' : '操作失败',
            status: res.status === 200 ? 0 : res.status,
          };
        },
      },
    };

    const dataResouceTypeOptions: any = this.resolveData(dataResourceTypeList);

    const schema = {
      type: 'form',
      id: '__form',
      closeDialogOnSubmit: false,
      initApi: {
        data: {
          pageNo: 1,
          pageSize: 500,
          dataResourceOperatingCodeList: ['dot_owner'],
          filterEffective: true,
          typeAndResource: dataResouceTypeOptions?.map((item: any) => {
            const { dataResourceTypeCode, dataResourceOuterCode } = item || {};
            return {
              dataResourceTypeCode,
              dataResourceOuterCode,
            };
          }),
        },
        adaptor: (payload: any, res: any) => {
          const ownerData = payload?.data?.map(item => item.subjectName)?.join(',');
          return {
            data: {
              dataResourceOwner: ownerData,
            },
            msg: res.status === 200 ? '' : '查询授权记录失败',
            status: res.status === 200 ? 0 : res.status,
          };
        },
        ...authorizationListApi,
      },
      api: {
        // 处理授权和权限申请的参数，去除一些多余字段
        requestAdaptor: (api: any) => {
          const { data } = api;
          return {
            ...api,
            data: {
              dataResourceOuterName: data?.dataResourceOuterName,
              dataResourceOuterCode: data?.dataResourceOuterCode,
              dataResourceTypeCode: data?.dataResourceTypeCode,
              subjectType: data?.subjectType,
              subjectCode:
                data?.subjectType === SUBJECT_TYPE_MAP.USER
                  ? data?.userValue
                  : data?.orgValue,
              resourceOperatingMetaCode: data?.resourceOperatingMetaCode,
              expiredTime:
                data.expiredTimeType === EXPIRED_TIME_TYPE_MAP.FIXEDDATE
                  ? `${data.expiredTimeValue} 23:59:59`
                  : undefined,
              description: data?.description,
              sensitive,
            },
          };
        },
        adaptor: (payload, res) => {
          return {
            // msg: res.status === 200 ? '操作成功' : '操作失败',
            data: payload,
            status: res.status === 200 ? 0 : res.status,
          };
        },

        ...(modalType === MODAL_TYPE_MAP.AUTHORIZED
          ? addAuthCheckApi
          : appliedCheckApi),
        // ...(modalType === MODAL_TYPE_MAP.AUTHORIZED ? addAuthApi : appliedApi),
      },
      // 授权成功后刷新授权列表
      onEvent: {
        submitSucc: {
          actions: [
            {
              actionType: 'custom',
              script: (ctx, doAction, event) => {
                console.log(event);
                const result = event.data?.result?.data?.result;
                if (result) {
                  doAction([
                    {
                      actionType: 'ajax',
                      args: {
                        api: {
                          ...(modalType === MODAL_TYPE_MAP.AUTHORIZED
                            ? addAuthApi
                            : appliedApi),
                          method: 'post',
                          requestAdaptor: api => {
                            const { data } = api;
                            return {
                              ...api,
                              data: handleData(data, { sensitive }),
                            };
                          },
                          adaptor: (payload: any, res: any) => {
                            if (modalType === MODAL_TYPE_MAP.AUTHORIZED) {
                              doAction({
                                actionType: 'reload',
                                componentId: '_crud',
                              });
                            } else {
                              doAction({
                                actionType: 'cancel',
                                componentId: wrapperDialogId,
                              });
                            }
                            return {
                              msg:
                                res.status === 200
                                  ? modalType === MODAL_TYPE_MAP.APPLIED ? '申请成功, BPM流程中' : '授权成功'
                                  : res?.message || '操作失败',
                              data: {},
                              status: res.status === 200 ? 0 : res.status,
                            };
                          },
                        },
                      },
                    },
                    modalType === MODAL_TYPE_MAP.AUTHORIZED
                      ? {}
                      : {
                        // 由于设置了 closeDialogOnSubmit：false，提交后不会关闭弹窗。 所以需要主动关闭
                        actionType: 'close',
                        componentId: '__form',
                      },
                  ]);
                } else {
                  doAction({
                    actionType: 'dialog',
                    args: {
                      dialog: {
                        type: 'dialog',
                        id: 'tipDialogId',
                        title: '系统提示',
                        showCloseButton: false,
                        body: [
                          {
                            type: 'tpl',
                            tpl: '您选择的权限比原有权限更小，确认继续？',
                          },
                        ],
                        onEvent: {
                          confirm: {
                            actions: [
                              {
                                actionType: 'ajax',
                                args: submitApi,
                              },
                              {
                                actionType: 'cancel',
                                componentId: 'tipDialogId',
                                expression:
                                  '${event.data.responseResult.responseStatus === 0}',
                              },
                              ...(modalType === MODAL_TYPE_MAP.AUTHORIZED
                                ? [
                                  {
                                    actionType: 'reload',
                                    componentId: '_crud',
                                    expression:
                                      '${event.data.responseResult.responseStatus === 0}',
                                  },
                                ]
                                : [
                                  {
                                    actionType: 'cancel',
                                    componentId: wrapperDialogId,
                                  },
                                  {
                                    // 由于设置了 closeDialogOnSubmit：false，提交后不会关闭弹窗。 所以需要主动关闭
                                    actionType: 'close',
                                    componentId: '__form',
                                  },
                                ]),
                            ],
                          },
                        },
                      },
                    },
                  });
                }
              },
            },
            {
              actionType: 'reload',
              componentId: '__form',
            },
          ],
        },
      },
      title: '',
      mode: 'horizontal',
      wrapWithPanel: false,
      actions: [],
      body:
        modalType === MODAL_TYPE_MAP.AUTHORIZED
          ? this.buildAuthorizedSchema()
          : this.buildAppliedSchema(),
    };

    return schema;
  }

  render() {
    const { render } = this.props;
    const schema = this.buildSchema();

    return render('body', schema);
  }
}

@Renderer({
  type: 'data-resource-authorized',
})
export class DataResourceAuthorizedRenderer extends DataResourceAuthorized { }
