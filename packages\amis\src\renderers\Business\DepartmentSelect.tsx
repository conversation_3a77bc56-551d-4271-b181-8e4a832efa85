import { ApiObject,build<PERSON><PERSON>,<PERSON><PERSON><PERSON>,RendererProps } from 'amis-core';
import React from 'react';

import { TreeSelectControlSchema } from '../Form/TreeSelect';


export interface DepartmentSelectSchema extends Omit<TreeSelectControlSchema, 'type'> {
  type: 'department-select';
}

export interface DepartmentSelectProps
  extends RendererProps,
    Omit<DepartmentSelectSchema, 'type' | 'className'> {}

export interface DepartmentSelectState {}

export class DepartmentSelect extends React.Component<
  DepartmentSelectProps,
  DepartmentSelectState
> {
  static defaultProps: Partial<DepartmentSelectProps> = {};

  constructor(props: DepartmentSelectProps) {
    super(props);
  }

  render() {
    const {render, $schema} = this.props;

    const {source, ...restSchema} = $schema || ({} as DepartmentSelectSchema);

    // 来自调用方 设置的 source
    const newSource = source ? buildApi(source) : ({} as ApiObject);

    // 生产模式下，接口格式是：'https://3xsw4ap8wah59.cfc-execute.bj.baidubce.com/api/amis-mock/mock2/business/department-select'
    const isMock =
      newSource.url?.indexOf('/api/') === 0 ||
      newSource.url?.includes('/api/amis-mock/mock2/');

    const selectSchemaNode = {
      multiple: true,
      searchable: true,
      initiallyOpen: false,
      maxTagCount: 2,
      labelField: 'name',
      valueField: 'corporationIdOrOrgId',
      ...restSchema,
      type: 'tree-select',
      source: {
        ...newSource,
        method: isMock && newSource.method ? newSource.method : 'get',
        url: isMock ? newSource.url : '/idaas/orgs/tree',
        adaptor: (payload: object, response: any, api: ApiObject) => {
          let newRes = {} as any;
          if (newSource?.adaptor) {
            newRes = newSource?.adaptor(payload, response, api);
          }
          const resData=  Array.isArray(payload || response.data) ? payload || response.data: [];
          return {
            status: response.status === 200 ? 0 : response.status,
            msg: response.statusText,
            data: resData,
            ...newRes,
          };
        },
        requestAdaptor: (api: ApiObject) => {
          let newApiObj = {} as ApiObject;
          if (newSource?.requestAdaptor) {
            newApiObj = newSource?.requestAdaptor(api);
          }
          return {...api, ...newApiObj};
        },
      },
    };

    return render('body', selectSchemaNode, {});
  }
}

@Renderer({
  type: 'department-select',
})
export class DepartmentSelectRenderer extends DepartmentSelect {}
