import { ApiObject,autobind,build<PERSON><PERSON>,<PERSON><PERSON><PERSON>,RendererProps } from 'amis-core';
import React from 'react';
import { SelectControlSchema } from '../Form/Select';


export interface UserSelectSchema extends Omit<SelectControlSchema, 'type'> {
  type: 'user-select';
}

export interface UserSelectProps
  extends RendererProps,
    Omit<UserSelectSchema, 'type' | 'className'> {}

export interface UserSelectState {}

export class UserSelect extends React.Component<
  UserSelectProps,
  UserSelectState
> {
  static defaultProps: Partial<UserSelectProps> = {};

  constructor(props: UserSelectProps) {
    super(props);
  }

  @autobind
  formatData(responseData: any) {
    if (!Array.isArray(responseData)) return [];

    const optionList: any = [];
    for (let i = 0, l = responseData.length; i < l; i++) {
      const node = typeof responseData[i] === 'object' ? responseData[i] : {};
      const {userId, name, nickname} = node;
      if (!userId) continue;

      optionList.push({
        label: `${name || ''}${nickname ? `（${nickname}）` : ''}`,
        value: userId,
        origin: node,
      });
    }

    return optionList;
  }

  render() {
    const {render, $schema} = this.props;

    const {source, ...restSchema} = $schema || ({} as UserSelectSchema);

    // 来自调用方 设置的 source
    const newSource = source ? buildApi(source) : ({} as ApiObject);

    const isMock =
      newSource.url?.indexOf('/api/') === 0 ||
      newSource.url?.includes('/api/amis-mock/mock2/');

    const selectSchemaNode = {
      multiple: true,
      searchable: true,
      maxTagCount: 2,
      ...restSchema,
      type: 'select',
      autoComplete: {
        ...newSource,
        data: {
          pageNo: 1,
          pageSize: 100,
          name: '${term}',
        },
        method: isMock && newSource.method ? newSource.method : 'get',
        url: isMock ? newSource.url : '/idaas/v2/users',
        adaptor: (payload: object, response: any, api: ApiObject) => {
          let newRes = {} as any;
          if (newSource?.adaptor) {
            newRes = newSource?.adaptor(payload, response, api);
          }
          const payloadData =
            typeof payload === 'object' && 'data' in payload
              ? payload?.data
              : void 0;
          return {
            status: response.status === 200 ? 0 : response.status,
            msg: response.statusText,
            data: this.formatData(payloadData || response.data),
            ...newRes,
          };
        },
        requestAdaptor: (api: ApiObject) => {
          let newApiObj = {} as ApiObject;
          if (newSource?.requestAdaptor) {
            newApiObj = newSource?.requestAdaptor(api);
          }
          return {...api, ...newApiObj};
        },
      },
    };

    return render('body', selectSchemaNode, {});
  }
}

@Renderer({
  type: 'user-select',
})
export class UserSelectRenderer extends UserSelect {}
