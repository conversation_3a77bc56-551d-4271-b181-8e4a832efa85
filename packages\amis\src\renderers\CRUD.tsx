import {
ActionObject,anyChanged,autoGetRenderColumnsNum,createObject,CRUDStore,evalExpression,extendObject,filter,getExprProperties,getPropValue,
getVariable,ICRUDStore,isApiOutdated,isArrayChildrenModified,IScopedContext,isEffectiveApi,isObjectShallowModified,isPureVariable,isVisible,padArr,parseQuery,PlainObject,qsparse,qsstringify,Renderer,RendererProps,resolveVariableAndFilter,Schema,SchemaNode,ScopedContext,str2function
} from 'amis-core';
import { Button,Html,Icon,Select,SpinnerExtraProps } from 'amis-ui';
import debounce from 'lodash/debounce';
import find from 'lodash/find';
import findIndex from 'lodash/findIndex';
import isEqual from 'lodash/isEqual';
import omit from 'lodash/omit';
import pick from 'lodash/pick';
import pickBy from 'lodash/pickBy';
import memoize from 'lodash/memoize';
import omitBy from 'lodash/omitBy';
import isEmpty from 'lodash/isEmpty';
import React from 'react';
import { findDOMNode } from 'react-dom';
import {
  BaseSchema,
  SchemaApi,
  SchemaClassName,
  SchemaExpression,
  SchemaMessage,
  SchemaName,
  SchemaObject,
  SchemaTokenizeableString,
  SchemaTpl
} from '../Schema';
import {isAlive} from 'mobx-state-tree';
import { ActionSchema } from './Action';
import { CardsSchema } from './Cards';
import { ListSchema } from './List';
import { TableSchema } from './Table';
import type {TableRendererEvent} from './Table';
import type {CardsRendererEvent} from './Cards';

import type { PaginationProps } from './Pagination';

export type CRUDBultinToolbarType =
  | 'columns-toggler'
  | 'drag-toggler'
  | 'pagination'
  | 'bulkActions'
  | 'bulk-actions'
  | 'statistics'
  | 'switch-per-page'
  | 'load-more'
  | 'filter-toggler'
  | 'export-csv'
  | 'export-excel';

export interface CRUDBultinToolbar extends Omit<BaseSchema, 'type'> {
  type: CRUDBultinToolbarType;
}

export type CRUDToolbarChild = SchemaObject | CRUDBultinToolbar;

export type CRUDToolbarObject = {
  /**
   * 对齐方式
   */
  align?: 'left' | 'right';
};

type AutoGenerateFilterObject = {
  /* 是否显示设置查询字段 */
  showBtnToolbar?: boolean;

  /* 是否默认展开 */
  defaultExpanded?: boolean;
};

export interface CRUDCommonSchema extends BaseSchema, SpinnerExtraProps {
  /**
   *  指定为 CRUD 渲染器。
   */
  type: 'crud';

  /**
   * 指定内容区的展示模式。
   */
  mode?: 'table' | 'grid' | 'cards' | /* grid 的别名*/ 'list';

  /**
   * 初始化数据 API
   */
  api?: SchemaApi;

  /**
   * 批量操作
   */
  bulkActions?: Array<ActionSchema>;

  /**
   * 单条操作
   */
  itemActions?: Array<ActionSchema>;

  /**
   * 每页个数，默认为 10，如果不是请设置。
   *
   * @default 10
   */
  perPage?: number;

  /**
   * 默认排序字段
   */
  orderBy?: string;

  /**
   * 默认排序方向
   */
  orderDir?: 'asc' | 'desc';

  /**
   * 可以默认给定初始参数如： {\"perPage\": 24}
   */
  defaultParams?: PlainObject;

  /**
   * 是否可通过拖拽排序
   */
  draggable?: boolean | SchemaExpression;

  /**
   * 是否可通过拖拽排序，通过表达式来配置
   */
  draggableOn?: SchemaExpression;

  name?: SchemaName;

  /**
   * 过滤器表单
   */
  filter?: any; // todo

  /**
   * 初始是否拉取
   * @deprecated 建议用 api 的 sendOn 代替。
   */
  initFetch?: boolean;

  /**
   * 初始是否拉取，用表达式来配置。
   * @deprecated 建议用 api 的 sendOn 代替。
   */
  initFetchOn?: SchemaExpression;

  /**
   * 配置内部 DOM 的 className
   */
  innerClassName?: SchemaClassName;

  /**
   * 设置自动刷新时间
   */
  interval?: number;

  /**
   * 设置用来确定位置的字段名，设置后新的顺序将被赋值到该字段中。
   */
  orderField?: string;

  /**
   * 设置分页页码字段名。
   * @default page
   */
  pageField?: string;

  /**
   * 设置分页一页显示的多少条数据的字段名。
   * @default perPage
   */
  perPageField?: string;

  /**
   * 快速编辑后用来批量保存的 API
   */
  quickSaveApi?: SchemaApi;

  /**
   * 快速编辑配置成及时保存时使用的 API
   */
  quickSaveItemApi?: SchemaApi;

  /**
   * 保存排序的 api
   */
  saveOrderApi?: SchemaApi;

  /**
   * 是否将过滤条件的参数同步到地址栏,默认为true
   * @default true
   */
  syncLocation?: boolean;

  /**
   * 顶部工具栏
   */
  headerToolbar?: Array<
    (CRUDToolbarChild & CRUDToolbarObject) | CRUDBultinToolbarType
  >;

  /**
   * 底部工具栏
   */
  footerToolbar?: Array<
    (CRUDToolbarChild & CRUDToolbarObject) | CRUDBultinToolbarType
  >;

  /**
   * 每页显示多少个空间成员的配置如： [10, 20, 50, 100]。
   */
  perPageAvailable?: Array<number>;

  messages?: SchemaMessage;

  /**
   * 是否隐藏快速编辑的按钮。
   */
  hideQuickSaveBtn?: boolean;

  /**
   * 是否自动跳顶部，当切分页的时候。
   */
  autoJumpToTopOnPagerChange?: boolean;

  /**
   * 静默拉取
   */
  silentPolling?: boolean;
  stopAutoRefreshWhen?: SchemaExpression;

  stopAutoRefreshWhenModalIsOpen?: boolean;
  filterTogglable?: boolean;
  filterDefaultVisible?: boolean;

  /**
   * 是否将接口返回的内容自动同步到地址栏，前提是开启了同步地址栏。
   */
  syncResponse2Query?: boolean;

  /**
   * 搜索、重置、分页的时候是否保留用户选择。
   */
  keepItemSelectionOnPageChange?: boolean;

  /**
   * 当配置 keepItemSelectionOnPageChange 时有用，用来配置已勾选项的文案。
   */
  labelTpl?: SchemaTpl;

  /**
   * 是否为前端单次加载模式，可以用来实现前端分页。
   */
  loadDataOnce?: boolean;

  /**
   * 在开启loadDataOnce时，filter时是否去重新请求api
   */
  loadDataOnceFetchOnFilter?: boolean;

  /**
   * 也可以直接从环境变量中读取，但是不太推荐。
   */
  source?: SchemaTokenizeableString;

  /**
   * 如果时内嵌模式，可以通过这个来配置默认的展开选项。
   */
  expandConfig?: {
    /**
     * 默认是展开第一个、所有、还是都不展开。
     */
    expand?: 'first' | 'all' | 'none';

    /**
     * 是否显示全部切换按钮
     */
    expandAll?: boolean;

    /**
     * 是否为手风琴模式
     */
    accordion?: boolean;

    /**
     * 在数据更新时，折叠所有展开项，默认 false
     */
    keepExpanded?: boolean;
  };

  /**
   * 默认只有当分页数大于 1 是才显示，如果总是想显示请配置。
   */
  alwaysShowPagination?: boolean;

  /**
   * 开启查询区域，会根据列元素的searchable属性值，自动生成查询条件表单
   */
  autoGenerateFilter?: AutoGenerateFilterObject | boolean;

  /**
   * 内容区域占满屏幕剩余空间
   */
  autoFillHeight?: TableSchema['autoFillHeight'];

  /** 顶层操作栏，在搜索区域上方 */
  topToolbar?: SchemaNode;

  /**
   * loadDataOnceFetchOnFilter 设为false时，本地搜索是否支持过滤 ”“ 场景
   */
  supportEmptyStringOnFilter?: boolean;
  /**
   * 设置Form依赖的Spinner的size
   */
  spinnerSize?: 'sm' | 'lg' | '',

  /**
   * 设置非追随过滤条件
   */
  headerFilter?: SchemaNode,

  /**
   * 重置、清空过滤条件时，仍需保持不变的参数
   */
  unsetQueryParams?: string[],

  /**
   * 是否支持 filter 模式的高级搜索
   */
  filterFormAdvanceSearchAble?: boolean,

  isInlineHeader?: boolean,
  /**
   * 是否启用嵌套表格的级连选择功能
   */
  cascadeSelection?: boolean;

  /**
   * 是否更新Table所有的行
   */
  updateAllRows?: boolean;
  /**
   * 是否去除crud整体内边距
   */
  noPadding?: boolean;

  /**
   * 是否去拍平列过滤的参数，默认 true
   */
  flatHeadSearchable?: boolean;

  /**
   * 开启勾选功能后，初始化时默认选中数据
   */
  defaultSelectedItems: any[]

  /**
   * filter表单中展示的行数，默认为2
   */
  filterRowNum?: number;
}

export type CRUDCardsSchema = CRUDCommonSchema & {
  mode: 'cards';
} & Omit<CardsSchema, 'type'>;

export type CRUDListSchema = CRUDCommonSchema & {
  mode: 'list';
} & Omit<ListSchema, 'type'>;

export type CRUDTableSchema = CRUDCommonSchema & {
  mode?: 'table';
} & Omit<TableSchema, 'type'>;

/**
 * CRUD 增删改查渲染器。
 * 文档：https://baidu.gitee.io/amis/docs/components/crud
 */
export type CRUDSchema = CRUDCardsSchema | CRUDListSchema | CRUDTableSchema;

export interface CRUDProps
  extends RendererProps,
    Omit<CRUDCommonSchema, 'type' | 'className'>,
    SpinnerExtraProps {
  store: ICRUDStore;
  pickerMode?: boolean; // 选择模式，用做表单中的选择操作
}

export type CRUDRendererEvent = TableRendererEvent | CardsRendererEvent;

const INNER_EVENTS: Array<CRUDRendererEvent> = [
  'rowClick',
  'rowDbClick',
];

export default class CRUD extends React.Component<CRUDProps, any> {
  static propsList: Array<keyof CRUDProps> = [
    'bulkActions',
    'itemActions',
    'mode',
    'orderField',
    'syncLocation',
    'toolbar',
    'toolbarInline',
    'messages',
    'value',
    'options',
    'multiple',
    'valueField',
    'defaultParams',
    'bodyClassName',
    'perPageAvailable',
    'pageField',
    'perPageField',
    'hideQuickSaveBtn',
    'autoJumpToTopOnPagerChange',
    'interval',
    'silentPolling',
    'stopAutoRefreshWhen',
    'stopAutoRefreshWhenModalIsOpen',
    'api',
    'affixHeader',
    'columnsTogglable',
    'placeholder',
    'tableClassName',
    'headerClassName',
    'footerClassName',
    // 'toolbarClassName',
    'headerToolbar',
    'footerToolbar',
    'filterTogglable',
    'filterDefaultVisible',
    'autoGenerateFilter',
    'syncResponse2Query',
    'keepItemSelectionOnPageChange',
    'labelTpl',
    'labelField',
    'loadDataOnce',
    'loadDataOnceFetchOnFilter',
    'source',
    'header',
    'columns',
    'size',
    'onChange',
    'onInit',
    'onSaved',
    'onSave',
    'onQuery',
    'formStore',
    'autoFillHeight',
    'unsetQueryParams',
    'filterFormAdvanceSearchAble',
    'isInlineHeader',
    'cascadeSelection',
    'noPadding'
  ];
  static defaultProps = {
    toolbarInline: true,
    headerToolbar: ['bulkActions'],
    footerToolbar: ['statistics', 'pagination'],
    primaryField: 'id',
    syncLocation: true,
    pageField: 'page',
    perPageField: 'perPage',
    hideQuickSaveBtn: false,
    autoJumpToTopOnPagerChange: false,
    silentPolling: false,
    filterTogglable: false,
    filterDefaultVisible: true,
    loadDataOnce: false,
    loadDataOnceFetchOnFilter: true,
    autoFillHeight: false,
    alwaysShowPagination: true,
    spinnerSize: 'lg',
    unsetQueryParams: [],
    filterFormAdvanceSearchAble: false,
    cascadeSelection: false,
    updateAllRows: false,
    noPadding: false,
    flatHeadSearchable: true,
    filterRowNum: 2,
    showSelection: true,
  };

  control: any;
  lastQuery: any;
  lastData: any;

  timer: ReturnType<typeof setTimeout>;
  mounted: boolean;
  /** 父容器, 主要用于定位CRUD内部popover的挂载点 */
  parentContainer: Element | null;
  needSearch?: boolean; // 用与clear 和 reset动作中判断 headerFilter 是否需要进行搜索

  // 用于解决表单初始化时机问题
  totalFilterForms: number;
  filterInitCount: number;

  filterOnEvent = memoize(onEvent => {
    const omitedOnEvent = omitBy(onEvent, (event, key: any) => !INNER_EVENTS.includes(key));
    // #870 omitObj空对象时一定要传undefined，否则会导致schema上的onEvent被覆盖
    return isEmpty(omitedOnEvent) ? undefined : omitedOnEvent;
  });

  constructor(props: CRUDProps) {
    super(props);

    this.controlRef = this.controlRef.bind(this);
    this.handleFilterReset = this.handleFilterReset.bind(this);
    this.handleFilterSubmit = this.handleFilterSubmit.bind(this);
    this.handleFilterInit = this.handleFilterInit.bind(this);
    this.handleAction = this.handleAction.bind(this);
    this.handleBulkAction = this.handleBulkAction.bind(this);
    this.handleChangePage = debounce(this.handleChangePage, 200, {
      leading: true,
      trailing: false,
    }).bind(this);
    this.handleBulkGo = this.handleBulkGo.bind(this);
    this.handleDialogConfirm = this.handleDialogConfirm.bind(this);
    this.handleDialogClose = this.handleDialogClose.bind(this);
    this.handleSave = this.handleSave.bind(this);
    this.handleSaveOrder = this.handleSaveOrder.bind(this);
    this.handleSelectChange = this.handleSelectChange.bind(this);
    this.handleSelect = this.handleSelect.bind(this);
    this.handleChildPopOverOpen = this.handleChildPopOverOpen.bind(this);
    this.handleChildPopOverClose = this.handleChildPopOverClose.bind(this);
    this.search = this.search.bind(this);
    this.silentSearch = this.silentSearch.bind(this);
    this.handleQuery = this.handleQuery.bind(this);
    this.renderHeaderToolbar = this.renderHeaderToolbar.bind(this);
    this.renderHeaderFilter = this.renderHeaderFilter.bind(this);
    this.renderFooterToolbar = this.renderFooterToolbar.bind(this);
    this.clearSelection = this.clearSelection.bind(this);
    this.syncQuery = this.syncQuery.bind(this);
    this.renderSelection = this.renderSelection.bind(this);
    this.clearAllParams = this.clearAllParams.bind(this);

    const {
      location,
      store,
      pageField,
      perPageField,
      syncLocation,
      loadDataOnce,
    } = props;

    this.mounted = true;

    if (syncLocation && location && (location.query || location.search)) {
      store.updateQuery(
        parseQuery(location),
        undefined,
        pageField,
        perPageField,
      );
    } else if (syncLocation && !location && window.location.search) {
      store.updateQuery(
        parseQuery(window.location),
        undefined,
        pageField,
        perPageField,
      );
    }

    this.props.store.setFilterTogglable(
      !!this.props.filterTogglable,
      this.props.filterDefaultVisible,
    );

    // 如果有 api，data 里面先写个 空数组，面得继承外层的 items
    // 比如 crud 打开一个弹框，里面也是个 crud，默认一开始其实显示
    // 的是外层 crud 的数据，等接口回来后就会变成新的。
    // 加上这个就是为了解决这种情况
    if (this.props.api) {
      this.props.store.updateData({
        items: [],
      });
    }

    // 初始化表单计数器
    this.totalFilterForms = Number(!!(this.props.filter || this.props.autoGenerateFilter)) + Number(!!this.props.headerFilter);
    this.filterInitCount = 0;
  }

  componentDidMount() {
    const {
      store,
      autoGenerateFilter,
      defaultSelectedItems,
      pickerMode,
      keepItemSelectionOnPageChange
    } = this.props;

    if (this.props.perPage) {
      store.changePage(store.page, this.props.perPage);
    }

    // 没有 filter 或者 没有展示 filter 时应该默认初始化一次，
    // 否则就应该等待 filter 里面的表单初始化的时候才初始化
    // 另外autoGenerateFilter时，table 里面会单独处理这块逻辑
    // 所以这里应该忽略 autoGenerateFilter 情况
    if (
      (!this.props.filter && !autoGenerateFilter) ||
      (store.filterTogggable && !store.filterVisible)
    ) {
      this.handleFilterInit({});
    }

    let val: any;
    if (this.props.pickerMode && (val = getPropValue(this.props))) {
      store.setSelectedItems(val);
    } else if (
      // 开启勾选项功能
      pickerMode ||
      keepItemSelectionOnPageChange ||
      !!(this.hasBulkActionsToolbar() && this.hasBulkActions())
    ) {
      // 设置默认选中数据
      const defSelected = typeof defaultSelectedItems === 'string'
        ? resolveVariableAndFilter(defaultSelectedItems, this.props.data, '| raw')
        : defaultSelectedItems
      if (Array.isArray(defSelected)) {
        store.setSelectedItems(defSelected);
      }
    }

    this.parentContainer = this.getClosestParentContainer();
  }

  componentDidUpdate(prevProps: CRUDProps) {
    const props = this.props;
    const store = prevProps.store;

    if (
      anyChanged(
        [
          'headerFilter',
          'toolbar',
          'headerToolbar',
          'footerToolbar',
          'bulkActions',
        ],
        prevProps,
        props,
      )
    ) {
      // 来点参数变化。
      this.renderHeaderToolbar = this.renderHeaderToolbar.bind(this);
      this.renderHeaderFilter = this.renderHeaderFilter.bind(this);
      this.renderFooterToolbar = this.renderFooterToolbar.bind(this);
    }

    let val: any;

    if (
      this.props.pickerMode &&
      isArrayChildrenModified(
        (val = getPropValue(this.props)),
        getPropValue(prevProps),
      ) &&
      !isEqual(val, store.selectedItems.concat())
    ) {
      /**
       * 更新链：Table -> CRUD -> Picker -> Form
       * 对于Picker模式来说，执行到这里的时候store.selectedItems已经更新过了，所以需要额外判断一下
       */
      store.setSelectedItems(val);
    }

    if (this.props.filterTogglable !== prevProps.filterTogglable) {
      store.setFilterTogglable(
        !!props.filterTogglable,
        props.filterDefaultVisible,
      );
    }

    let dataInvalid = false;

    if (
      prevProps.syncLocation &&
      prevProps.location &&
      prevProps.location.search !== props.location.search
    ) {
      // 同步地址栏，那么直接检测 query 是否变了，变了就重新拉数据
      store.updateQuery(
        parseQuery(props.location),
        undefined,
        props.pageField,
        props.perPageField,
      );
      dataInvalid = !!(
        props.api && isObjectShallowModified(store.query, this.lastQuery, false)
      );
    }

    if (dataInvalid) {
      // 要同步数据
    } else if (
      prevProps.api &&
      props.api &&
      isApiOutdated(
        prevProps.api,
        props.api,
        store.fetchCtxOf(prevProps.data, {
          pageField: prevProps.pageField,
          perPageField: prevProps.perPageField,
        }),
        store.fetchCtxOf(props.data, {
          pageField: props.pageField,
          perPageField: props.perPageField,
        }),
      )
    ) {
      dataInvalid = true;
    } else if (!props.api && isPureVariable(props.source) && props.data !== prevProps.data) {
      const next = resolveVariableAndFilter(props.source, props.data, '| raw');

      if (!this.lastData || this.lastData !== next) {
        store.initFromScope(props.data, props.source, {
          columns: store.columns ?? props.columns
        });
        this.lastData = next;

        // crud table 模式本地分页
        const mode = isPureVariable(props.mode)
          ? resolveVariableAndFilter(props.mode, props.data, '| raw')
          : props.mode;
        if (!mode || mode === 'table') {
          store.updateTotal((Array.isArray(next) ? next : []).length);
        }
      }
    }

    if (dataInvalid) {
      this.search();
    }
  }

  componentWillUnmount() {
    this.mounted = false;
    clearTimeout(this.timer);
  }

  /** 查找CRUD最近层级的父窗口 */
  getClosestParentContainer() {
    const dom = findDOMNode(this) as HTMLElement;
    const overlay = dom?.closest('[role=dialog]');

    return overlay;
  }

  controlRef(control: any) {
    // 因为 control 有可能被 n 层 hoc 包裹。
    while (control && control.getWrappedInstance) {
      control = control.getWrappedInstance();
    }

    this.control = control;
  }

  // 根据列里配置的name 将列滚动到视图内
  scrollIntoViewByColumnName(name: string) {
    if(this.control) {
      this.control?.scrollIntoViewByColumnName(name);
    }
  }

  // 高亮某一列
  setColumnIsHighLightByColumnName(name: string) {
    if(this.control) {
      this.control?.setColumnIsHighLightByColumnName(name);
    }
  }

  // 取消高亮
  removeColumnIsHighLight() {
    if(this.control) {
      this.control?.removeColumnIsHighLight();
    }
  }

  // issue#400 清空查询参数动作
  clearAllParams(args: any = {}, actionType?: string) {
    if(this.control) {
      const { store, syncLocation, env, pageField, perPageField } = this.props;

      const isClear = actionType === 'clear';

      /**
       * args参数设计
       * 1. needSearch: 清空查询条件后是否需要触发查询，默认查询
       */
      const { needSearch = true } = args;
      // console.log('this.control', this.control);

      /**
       * reset 参数说明，这里主要用于清空列查询数据
       * 1. 需要清除的字段集合，此处清空全部不传
       * 2. 是否是动作触发
       * 3. 是否触发查询，此处只用于清空 table层的searchFormData，不发送请求
       */
      this.control?.headSearch?.reset(undefined, true, false);

      // 重置查询条件
      store.updateQuery(
        isClear ? {} : {
          ...store.pristineQuery
        },
        syncLocation && env && env.updateLocation
        ? (location: any) => env.updateLocation(location)
        : undefined,
        pageField,
        perPageField,
        true
      )

      isClear &&  (this.needSearch = needSearch);
      needSearch && this.search();
    }
  }

  handleAction(
    e: React.UIEvent<any> | undefined,
    action: ActionObject,
    ctx: object,
    throwErrors: boolean = false,
    delegate?: IScopedContext,
  ): any {
    const {
      onAction,
      store,
      messages,
      pickerMode,
      env,
      pageField,
      stopAutoRefreshWhenModalIsOpen,
      data,
    } = this.props;

    if (
      action.actionType &&
      ['toggleExpanded','toggleExpandedAll'].includes(action.actionType)
    ) {
      // 直接调用table组件的动作
      return this.control?.doAction(action, action.args, throwErrors);
    } else if (action.actionType === 'dialog') {
      store.setCurrentAction(action);
      const idx: number = (ctx as any).index;
      const length = store.items.length;
      stopAutoRefreshWhenModalIsOpen && clearTimeout(this.timer);
      store.openDialog(ctx, {
        hasNext: idx < length - 1,
        nextIndex: idx + 1,
        hasPrev: idx > 0,
        prevIndex: idx - 1,
        index: idx,
      });
    } else if (action.actionType === 'ajax') {
      store.setCurrentAction(action);
      const data = ctx;

      // 由于 ajax 一段时间后再弹出，肯定被浏览器给阻止掉的，所以提前弹。
      const redirect = action.redirect && filter(action.redirect, data);
      redirect && action.blank && env.jumpTo(redirect, action);

      return store
        .saveRemote(action.api!, data, {
          successMessage:
            (action.messages && action.messages.success) ||
            (messages && messages.saveSuccess),
          errorMessage:
            (action.messages && action.messages.failed) ||
            (messages && messages.saveFailed),
        })
        .then(async (payload: object) => {
          const data = createObject(ctx, payload);

          if (action.feedback && isVisible(action.feedback, data)) {
            await this.openFeedback(action.feedback, data);
            stopAutoRefreshWhenModalIsOpen && clearTimeout(this.timer);
          }

          const redirect = action.redirect && filter(action.redirect, data);
          redirect && !action.blank && env.jumpTo(redirect, action);
          action.reload
            ? this.reloadTarget(filter(action.reload, data), data)
            : redirect
            ? null
            : this.search(undefined, undefined, true, true);
          action.close && this.closeTarget(action.close);
          // 清空列选择
          this.clearSelection();
        })
        .catch(e => {
          if (throwErrors || action.countDown) {
            throw e;
          }
        });
    } else if (action.actionType === 'reload' && !action.target) {
      this.reload();
    } else if (
      pickerMode &&
      (action.actionType === 'confirm' || action.actionType === 'submit')
    ) {
      store.setCurrentAction(action);
      return Promise.resolve({
        items: store.selectedItems.concat(),
      });
    } else if (action.onClick) {
      store.setCurrentAction(action);
      let onClick = action.onClick;
      if (typeof onClick === 'string') {
        onClick = str2function(onClick, 'event', 'props', 'data');
      }
      onClick && onClick(e, this.props, ctx);
    } else if (action.actionType === 'changePage') {
      const {page, perPage} = action.args || {};
      this.handleChangePage(page, perPage);
    } else if (action.actionType === 'query') {
      const {queryParams, setPristineQuery} = action.args;
      if (setPristineQuery) {
        store.setPristineQuery({...store.pristineQuery, ...queryParams});
      }
      this.handleFilterSubmit(queryParams);
    } else if (action.actionType === 'select') {
      const args = action.args || {};
      const dataSource = store.getData(data);
      const selected: Array<any> = [];
      const unSelected: Array<any> = [];
      dataSource?.items?.forEach((item: any, rowIndex: number) => {
        const flag = evalExpression(args?.selected, {record: item, rowIndex});
        if (flag) {
          selected.push(item);
        } else {
          unSelected.push(item);
        }
      });
      store.setSelectedItems(selected);
      store.setUnSelectedItems(unSelected);
    } else if (action.actionType === 'clearAll') {
      const dataSource = store.getData(data);
      store.setSelectedItems([]);
      store.setUnSelectedItems(dataSource?.items || []);
    } else if(action.actionType === 'scrollIntoView') {
      this.scrollIntoViewByColumnName(action.args?.columnName);
    } else if(action.actionType === 'setHighLightColumn') {
      this.setColumnIsHighLightByColumnName(action.args?.columnName);
    } else if(action.actionType === 'removeHighLightColumn') {
      this.removeColumnIsHighLight();
    } else if(action.actionType === 'clear' || action.actionType === 'reset') {
      this.clearAllParams(action.args, action.actionType);
    } else {
      onAction(e, action, ctx, throwErrors, delegate || this.context);
    }
  }

  handleBulkAction(
    selectedItems: Array<any>,
    unSelectedItems: Array<any>,
    e: React.UIEvent<any>,
    action: ActionObject,
  ) {
    const {
      store,
      primaryField,
      onAction,
      messages,
      pageField,
      stopAutoRefreshWhenModalIsOpen,
      env,
    } = this.props;

    if (!selectedItems.length && action.requireSelected !== false) {
      return;
    }

    let ids = selectedItems
      .map(item =>
        item.hasOwnProperty(primaryField) ? item[primaryField as string] : null,
      )
      .filter(item => item)
      .join(',');

    const ctx = createObject(store.mergedData, {
      ...selectedItems[0],
      currentPageData: store.mergedData.items.concat(),
      rows: selectedItems,
      items: selectedItems,
      selectedItems,
      unSelectedItems: unSelectedItems,
      ids,
    });

    let fn = () => {
      if (action.actionType === 'dialog') {
        return this.handleAction(
          e,
          {
            ...action,
            __from: 'bulkAction',
          },
          ctx,
        );
      } else if (action.actionType === 'ajax') {
        isEffectiveApi(action.api, ctx) &&
          store
            .saveRemote(action.api as string, ctx, {
              successMessage:
                (action.messages && action.messages.success) ||
                (messages && messages.saveSuccess),
              errorMessage:
                (action.messages && action.messages.failed) ||
                (messages && messages.saveFailed),
            })
            .then(async (payload: object) => {
              const data = createObject(ctx, payload);
              if (action.feedback && isVisible(action.feedback, data)) {
                await this.openFeedback(action.feedback, data);
                stopAutoRefreshWhenModalIsOpen && clearTimeout(this.timer);
              }

              action.reload
                ? this.reloadTarget(filter(action.reload, data), data)
                : this.search(
                    {[pageField || 'page']: 1},
                    undefined,
                    true,
                    true,
                  );
              action.close && this.closeTarget(action.close);

              const redirect = action.redirect && filter(action.redirect, data);
              redirect && env.jumpTo(redirect, action);

              // 清空列选择
              this.clearSelection();
            })
            .catch(() => null);
      } else if (onAction) {
        onAction(e, action, ctx, false, this.context);
      }
    };

    // Action如果配了事件动作也会处理二次确认，这里需要处理一下忽略
    let confirmText: string = '';
    if (
      !action.ignoreConfirm &&
      action.confirmText &&
      env.confirm &&
      (confirmText = filter(action.confirmText, ctx))
    ) {
      env.confirm(confirmText).then((confirmed: boolean) => confirmed && fn());
    } else {
      fn();
    }
  }

  handleItemAction(action: ActionObject, ctx: any) {
    this.doAction(action, ctx);
  }

  handleFilterInit(values: object, props: any, isHeadFilter?: boolean) {
    const {defaultParams, data, store, orderBy, orderDir, headerFilter} =
      this.props;
    const params = {...defaultParams};

    if (orderBy) {
      params['orderBy'] = orderBy;
      params['orderDir'] = orderDir || 'asc';
    }

    // 更新计数器
    this.filterInitCount++;

    // 只有当所有表单都初始化完成后才发起请求
    const shouldSearch = this.props.initFetch !== false && this.filterInitCount >= this.totalFilterForms;

    this.handleFilterSubmit(
      {
        ...params,
        ...values,
        ...store.query,
      },
      false,
      true,
      shouldSearch,
    );

    store.setPristineQuery();

    const {pickerMode, options} = this.props;

    pickerMode &&
      store.updateData({
        items: options || [],
      });
  }

  handleFilterReset(values: object, action: any, resetData = {}) {
    const {
      store,
      syncLocation,
      env,
      pageField,
      perPageField,
      keepItemSelectionOnPageChange,
      unsetQueryParams,
    } = this.props;

    if (store.loading) {
      return;
    }

    /**
     * 根据筛选区域和列筛选的逻辑
     * 筛选区域重置不影响列排序
     * */
    // this.control?.updateSortList?.([]);

    const isClear = action?.actionType === 'clear';

    /**
     * 无论是重置还是清除，都不涉及列数据
     * values仅为查询区域搜集的values
     * store.columnQueryKeys会在crud render table的constructor中初始化
     * 判断规则为 headSearchable 或 !autoGenerateFilter && searchable
     * */
    // const tmpValues: any = {};
    // Object.keys(values)
    //   .filter(key => !store.columnQueryKeys.some(item => item === key))
    //   .forEach(index => {
    //     // @ts-ignore
    //     tmpValues[index] = values[index];
    //   });
    // const tmpPristinQuery: any = {};
    // Object.keys(values)
    //   .filter(key => !store.columnQueryKeys.some(item => item === key))
    //   .forEach(index => {
    //     tmpPristinQuery[index] = store.pristineQuery[index];
    //   });

    /**
     * 过滤完列数据后
     * 如果是清除逻辑，拿到当前的values
     * 如果是重置逻辑，拿到当前的store.pristineQuery数据
     * setPristineQuery目前仅在handleFilterInit和query中传入了setPristineQuery时触发
     * */
    let query: {[propName: string]: any} = {};

    Object.keys(resetData).forEach(key => {
      query[key] = isClear ? undefined : store.pristineQuery[key]
    })

    /**
     * 仍需保持不变的参数
     * */
    unsetQueryParams?.forEach((key: string) => {
      query = {
        ...query,
        [key]: store.query[key],
      };
    });

    // issue#914 重置不会回到第一页
    query[pageField || 'page'] = store.pristineQuery[pageField || 'page'];

    /**
     * FIX: issue#860 解决点击重置的时候会导致没有选择的字段从没有值变成值为undefined从而导致多走一次withStore的问题
     * 如果这次的query和上次的query都是undefined，表示没有被操作过，删除这个字段防止进入withStore的判断逻辑中
     */
    Object.keys(query).forEach(key => {
      if(query[key] === undefined && store.query[key] === undefined) {
        delete query[key]
      }
    })

    store.updateQuery(
      query,
      syncLocation && env && env.updateLocation
        ? (location: any) => env.updateLocation(location)
        : undefined,
      pageField,
      perPageField,
      false,
    );
    this.lastQuery = store.query;

    // 对于带 submit 的 reset(包括 actionType 为 reset-and-submit clear-and-submit 和 form 的 resetAfterSubmit 属性)
    // 不执行 search，否则会多次触发接口请求
    if (
      action?.actionType &&
      ['reset-and-submit', 'clear-and-submit', 'submit'].includes(
        action.actionType,
      )
    ) {
      return;
    }

    this.search();
    // 切换分页时若不保留选项的话 需要清除store中已选择的项
    if (!keepItemSelectionOnPageChange) {
      this.clearSelection();
    }
  }

  handleFilterSubmit(
    values: Record<string, any>,
    jumpToFirstPage: boolean = true,
    replaceLocation: boolean = false,
    search: boolean = true,
  ) {
    const {
      store,
      syncLocation,
      env,
      pageField,
      perPageField,
      loadDataOnceFetchOnFilter,
      keepItemSelectionOnPageChange,
    } = this.props;

    if (store.loading) {
      return;
    }

    /** 找出clearValueOnHidden的字段, 保证updateQuery时不会使用上次的保留值 */
    values = {
      ...values,
      ...pickBy(values?.__super?.diff ?? {}, value => value === undefined),
    };
    values = syncLocation
      ? qsparse(qsstringify(values, undefined, true))
      : values;

    store.updateQuery(
      {
        ...values,
        [pageField || 'page']: jumpToFirstPage ? 1 : store.page,
      },
      syncLocation && env && env.updateLocation
        ? (location: any) => env.updateLocation(location, replaceLocation)
        : undefined,
      pageField,
      perPageField,
    );
    this.lastQuery = store.query;
    search &&
      this.search(undefined, undefined, undefined, loadDataOnceFetchOnFilter);

    // 切换分页时若不保留选项的话 需要清除store中已选择的项
    if (!keepItemSelectionOnPageChange) {
      this.clearSelection();
    }
  }

  handleBulkGo(
    selectedItems: Array<any>,
    unSelectedItems: Array<any>,
    e: React.MouseEvent<any>,
  ) {
    const action = this.props.store.selectedAction;
    const env = this.props.env;
    let confirmText: string = '';

    if (
      action.confirmText &&
      (confirmText = filter(action.confirmText, this.props.store.mergedData))
    ) {
      return env
        .confirm(confirmText)
        .then(
          (confirmed: boolean) =>
            confirmed &&
            this.handleBulkAction(
              selectedItems,
              unSelectedItems,
              e as any,
              action,
            ),
        );
    }

    return this.handleBulkAction(
      selectedItems,
      unSelectedItems,
      e as any,
      action,
    );
  }

  handleDialogConfirm(
    values: object[],
    action: ActionObject,
    ctx: any,
    components: Array<any>,
  ) {
    const {
      store,
      pageField,
      stopAutoRefreshWhenModalIsOpen,
      interval,
      silentPolling,
      env,
    } = this.props;

    store.closeDialog(true);
    const dialogAction = store.action as ActionObject;

    if (stopAutoRefreshWhenModalIsOpen && interval) {
      this.timer = setTimeout(
        silentPolling ? this.silentSearch : this.search,
        Math.max(interval, 1000),
      );
    }

    if (
      action.actionType === 'next' &&
      typeof ctx.nextIndex === 'number' &&
      store.data.items[ctx.nextIndex]
    ) {
      return this.handleAction(
        undefined,
        {
          ...dialogAction,
        },
        createObject(
          createObject(store.data, {
            index: ctx.nextIndex,
          }),
          store.data.items[ctx.nextIndex],
        ),
      );
    } else if (
      action.actionType === 'prev' &&
      typeof ctx.prevIndex === 'number' &&
      store.data.items[ctx.prevIndex]
    ) {
      return this.handleAction(
        undefined,
        {
          ...dialogAction,
        },
        createObject(
          createObject(store.data, {
            index: ctx.prevIndex,
          }),
          store.data.items[ctx.prevIndex],
        ),
      );
    } else if (values.length) {
      const value = values[0];
      ctx = createObject(ctx, value);
      const component = components[0];

      // 提交来自 form
      if (component && component.props.type === 'form') {
        // 数据保存了，说明列表数据已经无效了，重新刷新。
        if (value && (value as any).__saved) {
          const reload = action.reload ?? dialogAction.reload;
          // 配置了 reload 则跳过自动更新。
          reload ||
            this.search(
              dialogAction.__from ? {[pageField || 'page']: 1} : undefined,
              undefined,
              true,
              true,
            );
        } else if (
          value &&
          ((value.hasOwnProperty('items') && (value as any).items) ||
            value.hasOwnProperty('ids')) &&
          this.control.bulkUpdate
        ) {
          this.control.bulkUpdate(value, (value as any).items);
        }
      }
    }

    const reload = action.reload ?? dialogAction.reload;
    if (reload) {
      this.reloadTarget(filter(reload, ctx), ctx);
    }

    let redirect = action.redirect ?? dialogAction.redirect;
    redirect = redirect && filter(redirect, ctx);
    redirect && env.jumpTo(redirect, dialogAction);
  }

  handleDialogClose(confirmed = false) {
    const {store, stopAutoRefreshWhenModalIsOpen, silentPolling, interval} =
      this.props;
    store.closeDialog(confirmed);

    if (stopAutoRefreshWhenModalIsOpen && interval) {
      this.timer = setTimeout(
        silentPolling ? this.silentSearch : this.search,
        Math.max(interval, 1000),
      );
    }
  }

  openFeedback(dialog: any, ctx: any) {
    return new Promise(resolve => {
      const {store} = this.props;
      store.setCurrentAction({
        type: 'button',
        actionType: 'dialog',
        dialog: dialog,
      });
      store.openDialog(ctx, undefined, confirmed => {
        resolve(confirmed);
      });
    });
  }

  search(
    values?: any,
    silent?: boolean,
    clearSelection?: boolean,
    forceReload = false,
  ) {
    const {
      store,
      api,
      messages,
      pageField,
      perPageField,
      interval,
      stopAutoRefreshWhen,
      stopAutoRefreshWhenModalIsOpen,
      silentPolling,
      syncLocation,
      syncResponse2Query,
      keepItemSelectionOnPageChange,
      pickerMode,
      env,
      loadDataOnce,
      loadDataOnceFetchOnFilter,
      supportEmptyStringOnFilter,
      source,
      columns,
      filter,
      expandConfig,
      dispatchEvent,
    } = this.props;

    // reload 需要清空用户选择。
    if (keepItemSelectionOnPageChange && clearSelection && !pickerMode) {
      store.setSelectedItems([]);
      store.setUnSelectedItems([]);
    }

    // reload 情况下，table 模式嵌套子表格
    // 未配置 keepExpanded 时，收起所有展开项目
    // 检查 store 是否仍然有效，避免在模式切换时访问已销毁的 store
    const controlStore = this.control?.props?.store;
    if (
      !expandConfig?.keepExpanded &&
      controlStore &&
      isAlive(controlStore)
    ) {
      controlStore.toggleExpandAll?.(false);
    }

    let loadDataMode = '';
    if (values && typeof values.loadDataMode === 'string') {
      loadDataMode = 'load-more';
      delete values.loadDataMode;
    }

    clearTimeout(this.timer);
    values &&
      store.updateQuery(
        values,
        !loadDataMode && syncLocation && env && env.updateLocation
          ? env.updateLocation
          : undefined,
        pageField,
        perPageField,
      );
    this.lastQuery = store.query;
    const data = createObject(store.data, store.query);
    const matchFunc =
    this.props?.matchFunc && typeof this.props.matchFunc === 'string'
      ? (str2function(
          this.props.matchFunc,
          'items',
          'itemsRaw',
          'options'
        ) as any)
      : this.props?.matchFunc;

    isEffectiveApi(api, data)
      ? store
          .fetchInitData(api, data, {
            successMessage: messages && messages.fetchSuccess,
            errorMessage: messages && messages.fetchFailed,
            autoAppend: true,
            forceReload,
            loadDataOnce,
            loadDataOnceFetchOnFilter,
            supportEmptyStringOnFilter,
            filter,
            source,
            silent,
            pageField,
            perPageField,
            loadDataMode,
            syncResponse2Query,
            columns: store.columns ?? columns,
            matchFunc,
          })
          .then(async value => {
            const {page, lastPage} = store;
            // 空列表 且 页数已经非法超出，则跳转到最后的合法页数
            if (
              !store.data.items.length &&
              !interval &&
              page > 1 &&
              lastPage < page
            ) {
              this.search(
                {
                  ...store.query,
                  [pageField || 'page']: lastPage,
                },
                false,
                undefined,
              );
            }

            interval &&
              this.mounted &&
              (!stopAutoRefreshWhen ||
                !(
                  (stopAutoRefreshWhenModalIsOpen && store.hasModalOpened) ||
                  evalExpression(
                    stopAutoRefreshWhen,
                    createObject(store.data, store.query),
                  )
                )) &&
              (this.timer = setTimeout(
                silentPolling
                  ? this.silentSearch.bind(this, undefined, undefined, true)
                  : this.search.bind(
                      this,
                      undefined,
                      undefined,
                      undefined,
                      true,
                    ),
                Math.max(interval, 1000),
              ));


            // 这里的value就是返回体，data属性是接口实际返回的数据
            const rendererEvent = await dispatchEvent('fetchInited', createObject(data, value));

            if (rendererEvent?.prevented) {
              return;
            }

            return value;
          })
      : source && store.initFromScope(data, source, {
        columns: store.columns ?? columns,
        matchFunc
      });
  }

  silentSearch(values?: object, clearSelection?: boolean, forceReload = false) {
    return this.search(values, true, clearSelection, forceReload);
  }

  handleChangePage(page: number, perPage?: number) {
    const {
      store,
      syncLocation,
      env,
      pageField,
      perPageField,
      autoJumpToTopOnPagerChange,
      affixOffsetTop,
      keepItemSelectionOnPageChange,
    } = this.props;

    let query: any = {
      [pageField || 'page']: page,
    };

    if (perPage) {
      query[perPageField || 'perPage'] = perPage;
    }

    store.updateQuery(
      query,
      syncLocation && env?.updateLocation ? env.updateLocation : undefined,
      pageField,
      perPageField,
    );

    // 切换分页时若不保留选项的话 需要清除store中已选择的项
    if (!keepItemSelectionOnPageChange) {
      this.clearSelection();
    }

    this.search(undefined, undefined, undefined);

    if (autoJumpToTopOnPagerChange && this.control) {
      (findDOMNode(this.control) as HTMLElement).scrollIntoView();
      const scrolledY = window.scrollY;
      const offsetTop = affixOffsetTop ?? env?.affixOffsetTop ?? 0;
      scrolledY && window.scroll(0, scrolledY - offsetTop);
    }
  }

  handleSave(
    rows: Array<object> | object,
    diff: Array<object> | object,
    indexes: Array<string>,
    unModifiedItems?: Array<any>,
    rowsOrigin?: Array<object> | object,
    options?: {
      resetOnFailed?: boolean;
      reload?: string;
    },
  ) {
    const {
      store,
      quickSaveApi,
      quickSaveItemApi,
      primaryField,
      env,
      messages,
      reload,
    } = this.props;

    if (Array.isArray(rows)) {
      if (!isEffectiveApi(quickSaveApi)) {
        env && env.alert('CRUD quickSaveApi is required');
        return;
      }

      const data: any = createObject(store.data, {
        rows,
        rowsDiff: diff,
        indexes: indexes,
        rowsOrigin,
      });

      if (rows.length && rows[0].hasOwnProperty(primaryField || 'id')) {
        data.ids = rows
          .map(item => (item as any)[primaryField || 'id'])
          .join(',');
      }

      if (unModifiedItems) {
        data.unModifiedItems = unModifiedItems;
      }

      store
        .saveRemote(quickSaveApi, data, {
          successMessage: messages && messages.saveFailed,
          errorMessage: messages && messages.saveSuccess,
        })
        .then(() => {
          const finalReload = options?.reload ?? reload;
          finalReload
            ? this.reloadTarget(filter(finalReload, data), data)
            : this.search(undefined, undefined, true, true);
        })
        .catch(() => {});
    } else {
      if (!isEffectiveApi(quickSaveItemApi)) {
        env && env.alert('CRUD quickSaveItemApi is required!');
        return;
      }

      const data = createObject(store.data, {
        item: rows,
        modified: diff,
        origin: rowsOrigin,
      });

      const sendData = createObject(data, rows);
      store
        .saveRemote(quickSaveItemApi, sendData)
        .then(() => {
          const finalReload = options?.reload ?? reload;
          finalReload
            ? this.reloadTarget(filter(finalReload, data), data)
            : this.search(undefined, undefined, true, true);
        })
        .catch(() => {
          options?.resetOnFailed && this.control.reset();
        });
    }
  }

  handleSaveOrder(moved: Array<object>, rows: Array<object>) {
    const {
      store,
      saveOrderApi,
      orderField,
      primaryField,
      env,
      reload,
      pageField = 'page',
      perPageField = 'perPage',
    } = this.props;

    if (!saveOrderApi) {
      env && env.alert('CRUD saveOrderApi is required!');
      return;
    }

    const model: {
      insertAfter?: any;
      insertBefore?: any;
      idMap?: any;
      rows?: any;
      ids?: any;
      order?: any;
      [propName: string]: any;
    } = createObject(store.data);

    let insertAfter: any;
    let insertBefore: any;
    const holding: Array<object> = [];
    const hasIdField =
      primaryField &&
      rows[0] &&
      (rows[0] as object).hasOwnProperty(primaryField);

    hasIdField || (model.idMap = {});

    model.insertAfter = {};
    rows.forEach((item: any) => {
      if (~moved.indexOf(item)) {
        if (insertAfter) {
          let insertAfterId = hasIdField
            ? (insertAfter as any)[primaryField as string]
            : rows.indexOf(insertAfter);
          model.insertAfter[insertAfterId] =
            (model as any).insertAfter[insertAfterId] || [];

          hasIdField || (model.idMap[insertAfterId] = insertAfter);
          model.insertAfter[insertAfterId].push(
            hasIdField ? item[primaryField as string] : item,
          );
        } else {
          holding.push(item);
        }
      } else {
        insertAfter = item;
        insertBefore = insertBefore || item;
      }
    });

    if (insertBefore && holding.length) {
      let insertBeforeId = hasIdField
        ? insertBefore[primaryField as string]
        : rows.indexOf(insertBefore);
      hasIdField || (model.idMap[insertBeforeId] = insertBefore);
      model.insertBefore = {};
      model.insertBefore[insertBeforeId] = holding.map((item: any) =>
        hasIdField ? item[primaryField as string] : item,
      );
    } else if (holding.length) {
      const first: any = holding[0];
      const firstId = hasIdField
        ? first[primaryField as string]
        : rows.indexOf(first);

      hasIdField || (model.idMap[firstId] = first);
      model.insertAfter[firstId] = holding
        .slice(1)
        .map((item: any) => (hasIdField ? item[primaryField as string] : item));
    }

    if (orderField) {
      const start = (store.page - 1) * store.perPage || 0;
      rows = rows.map((item, key) =>
        extendObject(item, {
          [orderField]: start + key + 1,
        }),
      );
    }

    model.rows = rows.concat();
    hasIdField &&
      (model.ids = rows
        .map((item: any) => item[primaryField as string])
        .join(','));
    hasIdField &&
      orderField &&
      (model.order = rows.map(item =>
        pick(item, [primaryField as string, orderField]),
      ));

    model[pageField] = store.page;
    model[perPageField] = store.perPage;

    isEffectiveApi(saveOrderApi, model) &&
      store
        .saveRemote(saveOrderApi, model)
        .then(() => {
          reload && this.reloadTarget(filter(reload, model), model);
          this.search(undefined, undefined, true, true);
        })
        .catch(() => {});
  }

  /**
   * FIX issue#493 抽离同步selected逻辑和和冒泡selectedChange事件逻辑
   * @param items
   * @param unSelectedItems
   * @returns
   */
  computedSelectedRows(items: Array<any>, unSelectedItems: Array<any>) {
    const {
      store,
      data,
      keepItemSelectionOnPageChange,
      primaryField,
      multiple,
      pickerMode,
      onSelect,
      dispatchEvent,
    } = this.props;
    let newItems = items;
    let newUnSelectedItems = unSelectedItems;

    if (keepItemSelectionOnPageChange && store.selectedItems.length) {
      const oldItems = store.selectedItems.concat();
      const oldUnselectedItems = store.unSelectedItems.concat();

      items.forEach(item => {
        const idx = findIndex(
          oldItems,
          a =>
            a === item ||
            (a[primaryField || 'id'] &&
              a[primaryField || 'id'] == item[primaryField || 'id']),
        );

        if (~idx) {
          oldItems[idx] = item;
        } else {
          oldItems.push(item);
        }

        const idx2 = findIndex(
          oldUnselectedItems,
          a =>
            a === item ||
            (a[primaryField || 'id'] &&
              a[primaryField || 'id'] == item[primaryField || 'id']),
        );

        if (~idx2) {
          oldUnselectedItems.splice(idx2, 1);
        }
      });

      unSelectedItems.forEach(item => {
        const idx = findIndex(
          oldUnselectedItems,
          a =>
            a === item ||
            (a[primaryField || 'id'] &&
              a[primaryField || 'id'] == item[primaryField || 'id']),
        );

        const idx2 = findIndex(
          oldItems,
          a =>
            a === item ||
            (a[primaryField || 'id'] &&
              a[primaryField || 'id'] == item[primaryField || 'id']),
        );

        if (~idx) {
          oldUnselectedItems[idx] = item;
        } else {
          oldUnselectedItems.push(item);
        }
        !~idx && ~idx2 && oldItems.splice(idx2, 1);
      });

      newItems = oldItems;
      newUnSelectedItems = oldUnselectedItems;

      // const thisBatch = items.concat(unSelectedItems);
      // let notInThisBatch = (item: any) =>
      //   !find(
      //     thisBatch,
      //     a => a[primaryField || 'id'] == item[primaryField || 'id']
      //   );

      // newItems = store.selectedItems.filter(notInThisBatch);
      // newUnSelectedItems = store.unSelectedItems.filter(notInThisBatch);

      // newItems.push(...items);
      // newUnSelectedItems.push(...unSelectedItems);
    }

    if (pickerMode && multiple === false && newItems.length > 1) {
      newUnSelectedItems.push.apply(
        newUnSelectedItems,
        newItems.splice(0, newItems.length - 1),
      );
    }

    store.setSelectedItems(newItems);
    store.setUnSelectedItems(newUnSelectedItems);

    return {
      newItems,
      newUnSelectedItems,
      selectedItems: newItems,
      unSelectedItems: newUnSelectedItems
    }
  }

  /**
   * FIX issue#493 抽离同步selected逻辑和和冒泡selectedChange事件逻辑
   * @param items
   * @param unSelectedItems
   * @returns
   */
  async handleSelect(items: Array<any>, unSelectedItems: Array<any>) {
    const { onSelect } = this.props;
    const { newItems, newUnSelectedItems } = this.computedSelectedRows(items, unSelectedItems);

    onSelect && onSelect(newItems, newUnSelectedItems);
  }

  /**
   * FIX issue#493 抽离同步selected逻辑和和冒泡selectedChange事件逻辑
   * @param items
   * @param unSelectedItems
   * @returns
   */
  async handleSelectChange(items: Array<any>, unSelectedItems: Array<any>) {
    const { data, dispatchEvent, onSelect } = this.props;
    const { newItems, newUnSelectedItems } = this.computedSelectedRows(items, unSelectedItems);

    const rendererEvent = await dispatchEvent(
      'selectedChange',
      createObject(data, {
        newItems,
        newUnSelectedItems,
        selectedItems: newItems,
        unSelectedItems: newUnSelectedItems,
      }),
    );

    if (rendererEvent?.prevented) {
      return;
    }

    onSelect && onSelect(newItems, newUnSelectedItems);
  }

  handleChildPopOverOpen(popOver: any) {
    if (
      this.props.interval &&
      popOver &&
      ~['dialog', 'drawer'].indexOf(popOver.mode)
    ) {
      this.props.stopAutoRefreshWhenModalIsOpen && clearTimeout(this.timer);
      this.props.store.setInnerModalOpened(true);
    }
  }

  handleChildPopOverClose(popOver: any) {
    const {stopAutoRefreshWhenModalIsOpen, silentPolling, interval} =
      this.props;

    if (popOver && ~['dialog', 'drawer'].indexOf(popOver.mode)) {
      this.props.store.setInnerModalOpened(false);

      if (stopAutoRefreshWhenModalIsOpen && interval) {
        this.timer = setTimeout(
          silentPolling ? this.silentSearch : this.search,
          Math.max(interval, 1000),
        );
      }
    }
  }

  syncQuery(values: object, bRequest: boolean = false) {
    const {store, syncLocation, env, pageField, perPageField} = this.props;
    const originQuery = JSON.parse(JSON.stringify(store.query));
    store.updateQuery(
      values,
      syncLocation && env && env.updateLocation
        ? env.updateLocation
        : undefined,
      pageField,
      perPageField,
      false,
    );
    if (bRequest) {
      const result = Object.keys(values).every((key) => {
        /**
         * 覆盖初始化场景：
         *  query = {page: 1}
         *  values = {
         *    wfh: undefined,
         *    age: undefined
         *  }
         * 比较的是values[key]和originQuery[key]，故能覆盖初始化场景
         * */
        // @ts-ignore
        if (isEqual(values[key], originQuery[key])) {
          return true;
        }

        return false;
      });

      if (!result) {
      /*
        如果所有的values都存在store.query中，可以不用请求新数据
      */
        this.search();
      }
    }
  }

  handleQuery(
    values: object,
    forceReload: boolean = false,
    replace?: boolean,
    resetPage?: boolean,
  ) {
    const {
      store,
      syncLocation,
      env,
      pageField,
      perPageField,
      keepItemSelectionOnPageChange,
    } = this.props;
    store.updateQuery(
      resetPage
        ? {
            // 有些交互场景完全不想重置
            ...values,
            [pageField || 'page']: 1,
          }
        : values,
      syncLocation && env && env.updateLocation
        ? env.updateLocation
        : undefined,
      pageField,
      perPageField,
      replace,
    );
    this.search(undefined, undefined, undefined, forceReload);
    // 切换分页时若不保留选项的话 需要清除store中已选择的项
    if (!keepItemSelectionOnPageChange) {
      this.clearSelection();
    }
  }

  reload(
    subpath?: string,
    query?: any,
    replace?: boolean,
    resetPage?: boolean,
  ) {
    if (query) {
      return this.receive(query, undefined, replace, resetPage);
    } else {
      this.search(undefined, undefined, true, true);
    }
  }

  receive(
    values: object,
    subPath?: string,
    replace?: boolean,
    resetPage?: boolean,
  ) {
    this.handleQuery(values, true, replace, resetPage);
  }

  reloadTarget(target: string, data: any) {
    // implement this.
  }

  closeTarget(target: string) {
    // implement this.
  }

  dispatchSortEvent = async (sortObj: any) => {
    const {data, dispatchEvent} = this.props;
    const rendererEvent = await dispatchEvent(
      'columnSort',
      createObject(data, sortObj),
    );

    if (rendererEvent?.prevented) {
      return;
    }
  };

  doAction(action: ActionObject, data: object, throwErrors: boolean = false) {
    return this.handleAction(undefined, action, data, throwErrors);
  }

  async unSelectItem(item: any, index: number) {
    const {store, valueField, primaryField, dispatchEvent, data, onSelect} =
      this.props;
    const key = valueField || primaryField || 'id';
    const selected = store.selectedItems.concat();
    const unSelected = store.unSelectedItems.concat();
    const currentItems = store.items;
    const deletedItemOnCurrent = currentItems.some(
      curr => curr[key] === item[key],
    );

    const idx = selected.indexOf(item);
    ~idx && unSelected.push.apply(unSelected, selected.splice(idx, 1));

    store.setSelectedItems(selected);
    store.setUnSelectedItems(unSelected);

    if (!deletedItemOnCurrent) {
      const rendererEvent = await dispatchEvent(
        'selectedChange',
        createObject(data, {
          newItems: selected,
          newUnSelectedItems: unSelected,
          selectedItems: selected,
          unSelectedItems: unSelected,
        }),
      );
      if (rendererEvent?.prevented) {
        return;
      }

      onSelect && onSelect(selected, unSelected);
    }
  }

  async clearSelection() {
    const {
      store,
      dispatchEvent,
      data,
      onSelect,
    } = this.props;
    const selected = store.selectedItems.concat();
    const unSelected = store.unSelectedItems.concat(selected);

    store.setSelectedItems([]);
    store.setUnSelectedItems(unSelected);

    // 触发 删除选项事件
    dispatchEvent('clearSelection', data);

    /**
     * FIX issue#493
     * 修复翻页时，选项被清空 selectedChange事件 没触发的问题
     */
    if(
      selected.length !== store.selectedItems.length
    ) {
      const rendererEvent = await dispatchEvent(
        'selectedChange',
        createObject(data, {
          newItems: [],
          newUnSelectedItems: unSelected,
          selectedItems: [],
          unSelectedItems: unSelected,
        }),
      );
      if (rendererEvent?.prevented) {
        return;
      }

      onSelect && onSelect(selected, unSelected);
    }
  }

  hasBulkActionsToolbar() {
    const {headerToolbar, footerToolbar} = this.props;

    const isBulkActions = (item: any) =>
      ~['bulkActions', 'bulk-actions'].indexOf(item.type || item);
    return (
      (Array.isArray(headerToolbar) && find(headerToolbar, isBulkActions)) ||
      (Array.isArray(footerToolbar) && find(footerToolbar, isBulkActions))
    );
  }

  hasBulkActions() {
    const {bulkActions, itemActions, store} = this.props;

    if (!bulkActions || !bulkActions.length) {
      return false;
    }

    let bulkBtns: Array<ActionSchema> = [];
    const ctx = store.mergedData;

    if (bulkActions && bulkActions.length) {
      bulkBtns = bulkActions
        .map(item => ({
          ...item,
          ...getExprProperties(item as Schema, ctx),
        }))
        .filter(item => !item.hidden && item.visible !== false);
    }

    return bulkBtns.length;
  }

  renderBulkActions(childProps: any) {
    let {
      bulkActions,
      itemActions,
      store,
      render,
      classnames: cx,
      primaryField,
    } = this.props;

    if (!bulkActions || !bulkActions.length) {
      return null;
    }

    const selectedItems = store.selectedItems;
    const unSelectedItems = store.unSelectedItems;

    let bulkBtns: Array<ActionSchema> = [];
    let itemBtns: Array<ActionSchema> = [];

    const ctx = createObject(store.mergedData, {
      currentPageData: store.mergedData.items.concat(),
      rows: selectedItems.concat(),
      items: selectedItems.concat(),
      selectedItems: selectedItems.concat(),
      unSelectedItems: unSelectedItems.concat(),
      ids: selectedItems
        .map(item =>
          item.hasOwnProperty(primaryField)
            ? item[primaryField as string]
            : null,
        )
        .filter(item => item)
        .join(','),
    });

    // const ctx = createObject(store.data, {
    //     ...store.query,
    //     items: childProps.items,
    //     selectedItems: childProps.selectedItems,
    //     unSelectedItems: childProps.unSelectedItems
    // });

    if (
      bulkActions &&
      bulkActions.length &&
      (!itemActions || !itemActions.length || selectedItems.length > 1)
    ) {
      bulkBtns = bulkActions
        .map(item => ({
          ...item,
          ...getExprProperties(item as Schema, ctx),
        }))
        .filter(item => !item.hidden && item.visible !== false);
    }

    const itemData = createObject(
      store.data,
      selectedItems.length ? selectedItems[0] : {},
    );

    if (itemActions && selectedItems.length <= 1) {
      itemBtns = itemActions
        .map(item => ({
          ...item,
          ...getExprProperties(item as Schema, itemData),
        }))
        .filter(item => !item.hidden && item.visible !== false);
    }

    return bulkBtns.length || itemBtns.length ? (
      <div className={cx('Crud-actions')}>
        {bulkBtns.map((btn, index) => {
          return render(
            `bulk-action/${index}`,
            {
              ...omit(btn, ['visibleOn', 'hiddenOn', 'disabledOn']),
              type: btn.type || 'button',
              ignoreConfirm: true,
            },
            {
              key: `bulk-${index}`,
              data: ctx,
              disabled:
                btn.disabled ||
                (btn.requireSelected !== false ? !selectedItems.length : false),
              onAction: this.handleBulkAction.bind(
                this,
                selectedItems.concat(),
                unSelectedItems.concat(),
              ),
            },
          );
        })}

        {itemBtns.map((btn, index) =>
          render(
            `bulk-action/${index}`,
            {
              ...omit(btn, ['visibleOn', 'hiddenOn', 'disabledOn']),
              type: 'button',
            },
            {
              key: `item-${index}`,
              data: itemData,
              disabled: btn.disabled || selectedItems.length !== 1,
              onAction: this.handleItemAction.bind(this, btn, itemData),
            },
          ),
        )}
      </div>
    ) : null;
  }

  renderPagination(toolbar: SchemaNode) {
    const {store, render, classnames: cx, alwaysShowPagination} = this.props;
    const {page, lastPage} = store;

    if (
      store.mode !== 'simple' &&
      store.lastPage < 2 &&
      !alwaysShowPagination
    ) {
      return null;
    }

    const extraProps: Pick<
      PaginationProps,
      | 'showPageInput'
      | 'maxButtons'
      | 'layout'
      | 'popOverContainerSelector'
      | 'perPageAvailable'
    > = {};

    /** 优先级：showPageInput显性配置 > (lastPage > 9) */
    if (typeof toolbar !== 'string') {
      const showPageInput = (toolbar as Schema).showPageInput;

      extraProps.showPageInput =
        showPageInput === true || (lastPage > 9 && showPageInput == null);
      extraProps.maxButtons = (toolbar as Schema).maxButtons;
      extraProps.layout = (toolbar as Schema).layout;
      extraProps.popOverContainerSelector = (
        toolbar as Schema
      ).popOverContainerSelector;
      extraProps.perPageAvailable = (toolbar as Schema).perPageAvailable;
    } else {
      extraProps.showPageInput = lastPage > 9;
    }

    return (
      <div className={cx('Crud-pager')}>
        {render(
          'pagination',
          {
            type: 'pagination',
          },
          {
            ...extraProps,
            activePage: page,
            lastPage: lastPage,
            hasNext: store.hasNext,
            mode: store.mode,
            perPage: store.perPage,
            total: store.total,
            popOverContainer: this.parentContainer,
            onPageChange: this.handleChangePage,
          },
        )}
      </div>
    );
  }

  renderStatistics() {
    const {
      store,
      classnames: cx,
      translate: __,
      alwaysShowPagination,
    } = this.props;

    if (store.lastPage <= 1 && !alwaysShowPagination) {
      return null;
    }

    return (
      <div className={cx('Crud-statistics')}>
        {__('CRUD.stat', {
          page: store.page,
          lastPage: store.lastPage,
          total: store.total,
        })}
      </div>
    );
  }

  renderSwitchPerPage(childProps: any) {
    const {
      store,
      perPageAvailable,
      classnames: cx,
      classPrefix: ns,
      translate: __,
    } = this.props;

    const items = childProps.items;

    if (!items.length) {
      return null;
    }

    const perPages = (perPageAvailable || [5, 10, 20, 50, 100]).map(
      (item: any) => ({
        label: item,
        value: item + '',
      }),
    );

    return (
      <div className={cx('Crud-pageSwitch')}>
        <span>{__('CRUD.perPage')}</span>
        <Select
          classPrefix={ns}
          searchable={false}
          placeholder={__('Select.placeholder')}
          options={perPages}
          value={store.perPage + ''}
          onChange={(value: any) => this.handleChangePage(1, value.value)}
          clearable={false}
          popOverContainer={this.parentContainer}
        />
      </div>
    );
  }

  renderLoadMore() {
    const {store, classPrefix: ns, classnames: cx, translate: __} = this.props;
    const {page, lastPage} = store;

    return (
      <div className={cx('Crud-loadMore')}>
        <Button
          disabled={page >= lastPage}
          disabledTip={__('CRUD.loadMoreDisableTip')}
          classPrefix={ns}
          onClick={() =>
            this.search({page: page + 1, loadDataMode: 'load-more'})
          }
          size="sm"
        >
          {__('CRUD.loadMore')}
        </Button>
      </div>
    );
  }

  renderFilterToggler() {
    const {store, classnames: cx, translate: __} = this.props;

    if (!store.filterTogggable) {
      return null;
    }

    return (
      <button
        onClick={() => store.setFilterVisible(!store.filterVisible)}
        className={cx('Button Button--size-default Button--default', {
          'is-active': store.filterVisible,
        })}
      >
        <Icon icon="filter" className="icon m-r-xs" />
        {__('CRUD.filter')}
      </button>
    );
  }

  renderExportCSV(toolbar: Schema) {
    const {store, classPrefix: ns, translate: __, loadDataOnce} = this.props;
    const api = (toolbar as Schema).api;

    return (
      <Button
        classPrefix={ns}
        onClick={() =>
          store.exportAsCSV({
            loadDataOnce,
            api,
            data: store.filterData /* 因为filter区域可能设置了过滤字段值，所以query信息也要写入数据域 */,
          })
        }
      >
        {toolbar.label || __('CRUD.exportCSV')}
      </Button>
    );
  }

  renderToolbar(
    toolbar?: SchemaNode,
    index: number = 0,
    childProps: any = {},
    toolbarRenderer?: (toolbar: SchemaNode, index: number) => React.ReactNode,
  ) {
    if (!toolbar) {
      return null;
    }

    const {render, store, translate: __, noPadding = false} = this.props;
    const type = (toolbar as Schema).type || toolbar;

    if (type === 'bulkActions' || type === 'bulk-actions') {
      return this.renderBulkActions(childProps);
    } else if (type === 'pagination') {
      return this.renderPagination(toolbar);
    } else if (type === 'statistics') {
      return this.renderStatistics();
    } else if (type === 'switch-per-page') {
      return this.renderSwitchPerPage(childProps);
    } else if (type === 'load-more') {
      return this.renderLoadMore();
    } else if (type === 'filter-toggler') {
      return this.renderFilterToggler();
    } else if (type === 'export-csv') {
      return this.renderExportCSV(toolbar as Schema);
    } else if (type === 'reload') {
      let reloadButton = {
        label: '',
        icon: 'fa fa-sync',
        tooltip: __('reload'),
        tooltipPlacement: 'top',
        type: 'button',
      };
      if (typeof toolbar === 'object') {
        reloadButton = {...reloadButton, ...omit(toolbar, ['type', 'align'])};
      }
      return render(`toolbar/${index}`, reloadButton, {
        onAction: () => {
          this.reload();
        },
      });
    } else if (Array.isArray(toolbar)) {
      const children: Array<any> = toolbar
        .filter((toolbar: any) => isVisible(toolbar, store.filterData))
        .map((toolbar, index) => ({
          dom: this.renderToolbar(toolbar, index, childProps, toolbarRenderer),
          toolbar,
        }))
        .filter(item => item.dom);
      const len = children.length;
      const cx = this.props.classnames;
      if (len) {
        return (
          <div className={cx('Crud-toolbar')} key={index}>
            {children.map(({toolbar, dom: child}, index) => {
              const type = (toolbar as Schema).type || toolbar;
              let align =
                toolbar.align || (type === 'pagination' ? 'right' : 'left');
              return (
                <div
                  key={index}
                  className={cx(
                    'Crud-toolbar-item',
                    align ? `Crud-toolbar-item--${align}` : '',
                    // toolbar.className
                    toolbar.toolbarClassName,
                  )}
                >
                  {child}
                </div>
              );
            })}
          </div>
        );
      }
      return null;
    }

    const result = toolbarRenderer
      ? toolbarRenderer(toolbar, index)
      : undefined;

    if (result !== void 0) {
      return result;
    }

    const $$editable = childProps.$$editable;

    return render(`toolbar/${index}`, toolbar, {
      // 包两层，主要是为了处理以下 case
      // 里面放了个 form，form 提交过来的时候不希望把 items 这些发送过来。
      // 因为会把数据呈现在地址栏上。
      data: createObject(
        createObject(store.filterData, {
          items: childProps.items,
          selectedItems: childProps.selectedItems,
          unSelectedItems: childProps.unSelectedItems,
        }),
        {},
      ),
      page: store.page,
      lastPage: store.lastPage,
      perPage: store.perPage,
      total: store.total,
      onQuery: this.handleQuery,
      onAction: this.handleAction,
      onChangePage: this.handleChangePage,
      onBulkAction: this.handleBulkAction,
      $$editable,
      noPadding,
    });
  }

  renderHeaderFilter() {
    const {
      render,
      store,
      classnames: cx,
      translate: __,
      headerFilter,
      noPadding = false,
    } = this.props;

    return headerFilter
      ? render(
          'header-filter',
          {
            title: '',
            mode: 'inline',
            ...headerFilter,
            actions: [],
            type: 'form',
            api: null,
          },
          {
            key: 'headerFilter',
            panelClassName: cx(
              'Crud-header-filter',
              headerFilter.panelClassName || 'Panel--default',
              noPadding ? 'pm-px-0' : ''
            ),
            data: store.filterData,
            onInit: (values, props) =>
              this.handleFilterInit(values, props, true),
            onChange: this.onHeaderFilterChange,
            updateDataNoChange: true, // 传递的data改变的时候是否触发onChange事件
            reactionFormLazyChange: false, // fix: issue#533、642 修复配置headerFilter时，翻页失效问题
            formStore: undefined,
          },
        )
      : null;
  }

  onHeaderFilterChange = (values: object) => {
    // issue#400 用于处理使用reset动作 needSearch 为 false 时，不触发搜索
    if(this.needSearch === false) {
      this.needSearch = true;
      return
    }
    const {defaultParams, store, orderBy, orderDir} = this.props;
    const params = {...defaultParams};

    if (orderBy) {
      params['orderBy'] = orderBy;
      params['orderDir'] = orderDir || 'asc';
    }

    this.handleFilterSubmit({
      ...params,
      ...store.query,
      ...values,
    });
  };

  renderHeaderToolbar(
    childProps: any,
    toolbarRenderer?: (toolbar: SchemaNode, index: number) => React.ReactNode,
  ) {
    let {toolbar, toolbarInline, headerToolbar} = this.props;

    if (toolbar) {
      if (Array.isArray(headerToolbar)) {
        headerToolbar = toolbarInline
          ? headerToolbar.concat(toolbar)
          : [headerToolbar, toolbar];
      } else if (headerToolbar) {
        headerToolbar = [headerToolbar, toolbar];
      } else {
        headerToolbar = toolbar;
      }
    }

    return this.renderToolbar(
      headerToolbar || [],
      0,
      childProps,
      toolbarRenderer,
    );
  }

  renderFooterToolbar(
    childProps: any,
    toolbarRenderer?: (toolbar: SchemaNode, index: number) => React.ReactNode,
  ) {
    let {toolbar, toolbarInline, footerToolbar, noPadding = false} = this.props;

    if (toolbar) {
      if (Array.isArray(footerToolbar)) {
        footerToolbar = toolbarInline
          ? footerToolbar.concat(toolbar)
          : [footerToolbar, toolbar];
      } else if (footerToolbar) {
        footerToolbar = [footerToolbar, toolbar];
      } else {
        footerToolbar = toolbar;
      }
    }

    return this.renderToolbar(footerToolbar, 0, childProps, toolbarRenderer);
  }

  renderSelection(): React.ReactNode {
    const {
      store,
      classnames: cx,
      labelField,
      labelTpl,
      primaryField,
      translate: __,
      keepItemSelectionOnPageChange,
      multiple,
      mode = 'table',
    } = this.props;

    if (!store?.selectedItems.length) {
      return null;
    }

    if (keepItemSelectionOnPageChange && multiple !== false) {
      return (
        <div
          className={cx('Crud-selection', {
            'Crud-selection-cards': mode === 'cards',
            'Crud-selection-list': mode === 'list',
          })}
        >
          <div className={cx('Crud-selectionLabel')}>
            {__('CRUD.selected', {total: store.selectedItems.length})}
          </div>
          <div className={cx('Crud-selectionContent')}>
            {store.selectedItems.map((item, index) => (
              <div key={index} className={cx(`Crud-value`)}>
                <span className={cx('Crud-valueLabel')}>
                  {labelTpl ? (
                    <Html html={filter(labelTpl, item)} />
                  ) : (
                    getVariable(item, labelField || 'label') ||
                    getVariable(item, primaryField || 'id')
                  )}
                </span>
                <span
                  data-tooltip={__('delete')}
                  data-position="bottom"
                  className={cx('Crud-valueIcon')}
                  onClick={this.unSelectItem.bind(this, item, index)}
                >
                  x
                </span>
              </div>
            ))}
            <a
              onClick={this.clearSelection}
              className={cx('Crud-selectionClear')}
            >
              {__('clear')}
            </a>
          </div>
        </div>
      );
    }

    return null;
  }

  renderFilterForm(topToolbarReactNode: any) {
    const {
      render,
      classnames: cx,
      translate: __,
      filter,
      store,
      filterFormAdvanceSearchAble,
      filterRowNum,
    } = this.props;

    // 高级搜索逻辑已移动到 Form 组件内部处理
    let body = filter.body;
    let actions = filter.actions;

    return render(
      'filter',
      {
        title: __('CRUD.filter'),
        mode: 'inline',
        submitText: __('search'),
        // inheritData: false, // #867, 注意：不能配置false，不然filter的data字段配置表达式时回显展示${xx}问题
        updatePristineAfterStoreDataReInit: true,
        ...filter,
        type: 'form',
        api: null,
        showLabelColon: true,
        // 将高级搜索相关参数传递给 Form 组件
        advanceFilter: filterFormAdvanceSearchAble,
        filterRowNum,
        body,
        actions,
      },
      {
        key: 'filter',
        panelClassName: cx(
          'Crud-filter',
          filter.panelClassName || 'Panel--default',
          {'Crud-advance-search': filterFormAdvanceSearchAble},
        ),
        data: store.filterData,
        onReset: this.handleFilterReset,
        onSubmit: this.handleFilterSubmit,
        onInit: this.handleFilterInit,
        formStore: undefined,
        topToolbarReactNode,
      },
    );
  }

  render() {
    const {
      className,
      style,
      bodyClassName,
      filter,
      render,
      store,
      mode: originMode = 'table',
      syncLocation,
      children,
      bulkActions,
      pickerMode,
      multiple,
      valueField,
      primaryField,
      value,
      hideQuickSaveBtn,
      itemActions,
      classnames: cx,
      keepItemSelectionOnPageChange,
      maxKeepItemSelectionLength,
      onAction,
      popOverContainer,
      translate: __,
      onQuery,
      autoGenerateFilter,
      onSelect,
      autoFillHeight,
      onEvent,
      onSave,
      onSaveOrder,
      onPopOverOpened,
      onPopOverClosed,
      onSearchableFromReset,
      onSearchableFromSubmit,
      onSearchableFromInit,
      headerToolbarRender,
      footerToolbarRender,
      topToolbar,
      topToolbarClassName,
      spinnerSize,
      headerFilter,
      updateAllRows,
      noPadding = false,
      flatHeadSearchable = true,
      draggable,
      draggableOn,
      filterRowNum,
      ...rest
    } = this.props;
    // table 模式如果传入了 source 和 perPage,没传入 api,默认是本地分页
    let items = store.data.items || [];
    const {api, source, perPage} = this.props;
    const mode = isPureVariable(originMode)
      ? resolveVariableAndFilter(originMode, this.props.data, '| raw')
      : originMode;
    if (items.length && mode === 'table' && !api && source && perPage) {
      const {perPage, page = 1} = store;
      if (typeof perPage === 'number' && perPage && items.length > perPage) {
        items = items.slice((page - 1) * perPage, page * perPage);
      }
    }

    const topToolbarReactNode = topToolbar ? (
      <div className={cx('Crud-top-header', topToolbarClassName, noPadding ? 'pm-px-0' : '')}>
        {render('topToolbar', topToolbar, {
          key: 'topToolbar',
          data: createObject(store.filterData, {
            ...store.mergedData,
            filterData: store.filterData,
          }),
        })}
      </div>
    ) : null;

    // issue#615: draggable表达式数据域支持获取curd的data、query和pristineQuery
    const draggableData = extendObject(store.data, {
      __query: store.query,
      __pristineQuery: store.pristineQuery
    })
    // 带On的优先级高，需要配置表达式
    const finnalDraggable = draggableOn !== undefined
      ? resolveVariableAndFilter(draggableOn, draggableData, '| raw')
      : draggable

    return (
      <div
        className={cx('Crud', className, {
          'is-loading': store.loading,
        })}
        style={style}
      >
        {!filter && !autoGenerateFilter && topToolbarReactNode}
        {filter && (!store.filterTogggable || store.filterVisible)
          ? this.renderFilterForm(topToolbarReactNode)
          : null}

        {/* {keepItemSelectionOnPageChange && multiple !== false
          ? this.renderSelection()
          : null} */}

        {render(
          'body',
          {
            ...rest,
            multiple,
            // 通用事件 例如cus-event 如果直接透传给table 则会被触发2次
            // 因此只将下层组件table、cards中自定义事件透传下去 否则通过crud配置了也不会执行
            onEvent: this.filterOnEvent(onEvent),
            columns: store.columns ?? rest.columns,
            type: mode || 'table',
            topToolbarReactNode,
            draggable: finnalDraggable,
            filterRowNum,
          },
          {
            key: 'body',
            className: cx('Crud-body', bodyClassName),
            ref: this.controlRef,
            autoGenerateFilter: !filter && autoGenerateFilter,
            autoFillHeight: autoFillHeight,
            selectable: !!(
              (this.hasBulkActionsToolbar() && this.hasBulkActions()) ||
              pickerMode
            ),
            itemActions,
            multiple:
              multiple === void 0
                ? bulkActions && bulkActions.length > 0
                  ? true
                  : false
                : multiple,
            selected:
              pickerMode ||
              keepItemSelectionOnPageChange ||
              !!(this.hasBulkActionsToolbar() && this.hasBulkActions())
                ? store.selectedItemsAsArray
                : undefined,
            keepItemSelectionOnPageChange,
            maxKeepItemSelectionLength,
            valueField: valueField || primaryField,
            primaryField: primaryField,
            hideQuickSaveBtn,
            items,
            syncLocation,
            location,
            query: store.query,
            orderBy: store.query.orderBy,
            orderDir: store.query.orderDir,
            popOverContainer,
            onAction: this.handleAction,
            onSave: this.handleSave,
            onSaveOrder: this.handleSaveOrder,
            onQuery: this.handleQuery,
            syncQuery: this.syncQuery,
            onSelect: this.handleSelectChange,
            handleSelect: this.handleSelect,
            onPopOverOpened: this.handleChildPopOverOpen,
            onPopOverClosed: this.handleChildPopOverClose,
            onSearchableFromReset: this.handleFilterReset,
            onSearchableFromSubmit: this.handleFilterSubmit,
            onSearchableFromInit: this.handleFilterInit,
            onHeaderFilterChange: this.onHeaderFilterChange,
            headerToolbarRender: this.renderHeaderToolbar,
            headerFilterRender: this.renderHeaderFilter,
            footerToolbarRender: this.renderFooterToolbar,
            data: store.mergedData,
            loading: store.loading,
            spinnerSize,
            dispatchSortEvent: this.dispatchSortEvent,
            renderSelection: this.renderSelection,
            updateAllRows,
            noPadding,
            flatHeadSearchable,
          },
        )}
        {render(
          'dialog',
          {
            ...((store.action as ActionObject) &&
              ((store.action as ActionObject).dialog as object)),
            type: 'dialog',
          },
          {
            key: 'dialog',
            data: store.dialogData,
            onConfirm: this.handleDialogConfirm,
            onClose: this.handleDialogClose,
            show: store.dialogOpen,
          },
        )}
      </div>
    );
  }
}

@Renderer({
  type: 'crud',
  storeType: CRUDStore.name,
  isolateScope: true,
})
export class CRUDRenderer extends CRUD {
  static contextType = ScopedContext;

  constructor(props: CRUDProps, context: IScopedContext) {
    super(props);

    const scoped = context;
    scoped.registerComponent(this);
  }

  componentWillUnmount() {
    super.componentWillUnmount();
    const scoped = this.context as IScopedContext;
    scoped.unRegisterComponent(this);
  }

  reload(
    subpath?: string,
    query?: any,
    ctx?: any,
    silent?: boolean,
    replace?: boolean,
    args?: any,
  ) {
    const scoped = this.context as IScopedContext;
    if (subpath) {
      return scoped.reload(
        query ? `${subpath}?${qsstringify(query)}` : subpath,
        ctx,
      );
    }

    return super.reload(subpath, query, replace, args?.resetPage ?? true);
  }

  receive(
    values: any,
    subPath?: string,
    replace?: boolean,
    resetPage?: boolean,
  ) {
    const scoped = this.context as IScopedContext;
    if (subPath) {
      return scoped.send(subPath, values);
    }

    return super.receive(values, undefined, replace, resetPage);
  }

  reloadTarget(target: string, data: any) {
    const scoped = this.context as IScopedContext;
    scoped.reload(target, data);
  }

  closeTarget(target: string) {
    const scoped = this.context as IScopedContext;
    scoped.close(target);
  }
}
