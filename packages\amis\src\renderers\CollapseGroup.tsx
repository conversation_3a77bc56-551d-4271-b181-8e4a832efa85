import React from 'react';
import {Renderer, RendererProps, isPureVariable, resolveVariableAndFilter} from 'amis-core';
import {BaseSchema, SchemaCollection, SchemaObject} from '../Schema';
import {CollapseGroup} from 'amis-ui';
import {ScopedContext, IScopedContext} from 'amis-core';

/**
 * CollapseGroup 折叠渲染器，格式说明。
 * 文档：https://baidu.gitee.io/amis/docs/components/collapse
 */
export interface CollapseGroupSchema extends BaseSchema {
  /**
   * 指定为折叠器类型
   */
  type: 'collapse-group';

  /**
   * 激活面板
   */
  activeKey?: Array<string | number | never> | string | number;

  /**
   * 手风琴模式
   */
  accordion?: boolean;

  /**
   * 自定义切换图标
   */
  expandIcon?: SchemaObject;

  /**
   * 设置图标位置
   */
  expandIconPosition?: 'left' | 'right';

  /**
   * 内容区域
   */
  body?: SchemaCollection;
}
export interface CollapseGroupProps
  extends RendererProps,
    Omit<CollapseGroupSchema, 'type' | 'className'> {
  children?: JSX.Element | ((props?: any) => JSX.Element);
  autoSwitchWhenValidated: false;
}

export class CollapseGroupRender extends React.Component<
  CollapseGroupProps,
  {}
> {
  static contextType = ScopedContext;
  toDispose: Array<Function> = [];
  control?: any;

  constructor(props: CollapseGroupProps) {
    super(props);

    this.validateHook = this.validateHook.bind(this);
    this.controlRef = this.controlRef.bind(this);
  }

  componentDidMount(): void {
    const {addHook} = this.props;

    if (addHook) {
      this.toDispose.push(addHook(this.validateHook, 'validate'));
    }
  }

  componentWillUnmount(): void {
    this.toDispose.forEach((fn) => fn());
  }

  controlRef(control: any) {
    // 因为 control 有可能被 n 层 hoc 包裹。
    while (control && control.getWrappedInstance) {
      control = control.getWrappedInstance();
    }

    this.control = control;
  }

  async validateHook() {
    if (!this.props.autoSwitchWhenValidated) {
      return;
    }
    const scoped = this.context as IScopedContext;
    const formItems = scoped.getChildrenFormItems(this.props.$path);
    // 查找所有有错误的表单项并记录其Collapse Index
    const errorIndices = [...new Set(
      formItems
        .filter(item => item.props.formItem?.errors?.length > 0)
        .map(item => parseInt(item.props.$path.slice(this.props.$path.length).split('/')[2]))
    )];

    if (errorIndices.length > 0) {
      this.control.updateActiveKey(errorIndices);
    }
  }

  render() {
    const {
      accordion,
      expandIcon,
      expandIconPosition,
      body,
      className,
      style,
      data,
      render
    } = this.props;
    let { defaultActiveKey } =  this.props;

    if (isPureVariable(defaultActiveKey)) {
      defaultActiveKey = resolveVariableAndFilter(defaultActiveKey, data, '| raw');
    }

    return (
      <CollapseGroup
        ref={this.controlRef}
        defaultActiveKey={defaultActiveKey}
        accordion={accordion}
        expandIcon={expandIcon}
        expandIconPosition={expandIconPosition}
        className={className}
        style={style}
      >
        {render('body', body || '')}
      </CollapseGroup>
    );
  }
}

@Renderer({
  type: 'collapse-group'
})
export class CollapseGroupRenderer extends CollapseGroupRender {}
