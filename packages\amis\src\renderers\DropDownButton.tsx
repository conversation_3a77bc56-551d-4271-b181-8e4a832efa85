import React from 'react';
import {createObject, Renderer, RendererProps} from 'amis-core';
import {Overlay} from 'amis-core';
import {PopOver} from 'amis-core';
import {TooltipWrapper} from 'amis-ui';
import {isDisabled, isVisible, noop} from 'amis-core';
import {filter} from 'amis-core';
import {Icon, hasIcon} from 'amis-ui';
import {BaseSchema, SchemaClassName, SchemaIcon} from '../Schema';
import {ActionSchema} from './Action';
import {DividerSchema} from './Divider';
import {RootClose} from 'amis-core';
import type {
  TooltipObject,
  Trigger
} from 'amis-ui/lib/components/TooltipWrapper';

export type DropdownButton =
  | (ActionSchema & {children?: Array<DropdownButton>})
  | DividerSchema
  | 'divider';

/**
 * 下拉按钮渲染器。
 * 文档：https://baidu.gitee.io/amis/docs/components/dropdown-button
 */
export interface DropdownButtonSchema extends BaseSchema {
  /**
   * 指定为 DropDown Button 类型
   */
  type: 'dropdown-button';

  /**
   * 是否独占一行 `display: block`
   */
  block?: boolean;

  /**
   * 给 Button 配置 className。
   */
  btnClassName?: SchemaClassName;

  /**
   * 按钮集合，支持分组
   */
  buttons?: Array<DropdownButton>;

  /**
   * 按钮文字
   */
  label?: string;

  /**
   * 按钮级别，样式
   */
  level?: 'info' | 'success' | 'danger' | 'warning' | 'primary' | 'link';

  /**
   * 按钮提示文字，hover 时显示
   */
  // tooltip?: SchemaTooltip;

  /**
   * 点击外部是否关闭
   */
  closeOnOutside?: boolean;

  /**
   * 点击内容是否关闭
   */
  closeOnClick?: boolean;

  /**
   * 按钮大小
   */
  size?: 'xs' | 'sm' | 'md' | 'lg';

  /**
   * 对齐方式
   */
  align?: 'left' | 'right';

  /**
   * 是否只显示图标。
   */
  iconOnly?: boolean;

  /**
   * 左侧图标
   */
  icon?: SchemaIcon;

  /**
   * 右侧图标
   */
  rightIcon?: SchemaIcon;

  /**
   * 触发条件，默认是 click
   */
  trigger?: 'click' | 'hover';

  /**
   * 是否显示下拉按钮
   */
  hideCaret?: boolean;

  /**
   * 菜单 CSS 样式
   */
  menuClassName?: string;

  overlayPlacement?: string;
}

export interface DropDownButtonProps
  extends RendererProps,
    Omit<DropdownButtonSchema, 'type' | 'className'> {
  disabledTip?: string | TooltipObject;
  /**
   * 按钮提示文字，hover focus 时显示
   */
  tooltip?: string | TooltipObject;
  placement?: 'top' | 'right' | 'bottom' | 'left';
  tooltipContainer?: any;
  tooltipTrigger?: Trigger | Array<Trigger>;
  tooltipRootClose?: boolean;
  defaultIsOpened?: boolean;
  label?: any;
  // 激活状态
  isActived?: boolean;
  menuClassName?: string;
  /**
   * 下拉菜单浮层挂载位置 类名方案
   */
  popOverContainerSelector?: string;
  /**
   * 提示浮层挂载位置
   */
  tooltipContainerSelector?: string;
}

export interface DropDownButtonState {
  isOpened: boolean;
}

export default class DropDownButton extends React.Component<
  DropDownButtonProps,
  DropDownButtonState
> {
  state: DropDownButtonState = {
    isOpened: false
  };

  static defaultProps: Pick<
    DropDownButtonProps,
    'placement' | 'tooltipTrigger' | 'tooltipRootClose' | 'overlayPlacement'
  > = {
    placement: 'top',
    tooltipTrigger: ['hover', 'focus'],
    tooltipRootClose: false,
    overlayPlacement: 'auto'
  };

  target: any;
  timer: ReturnType<typeof setTimeout>;
  constructor(props: DropDownButtonProps) {
    super(props);

    this.open = this.open.bind(this);
    this.close = this.close.bind(this);
    this.toogle = this.toogle.bind(this);
    this.keepOpen = this.keepOpen.bind(this);
    this.domRef = this.domRef.bind(this);
  }

  componentDidMount() {
    if (this.props.defaultIsOpened) {
      this.setState({
        isOpened: true
      });
    }
  }

  componentWillUnmount(): void {
    if (this.timer) {
      clearTimeout(this.timer)
    }
  }

  domRef(ref: any) {
    this.target = ref;
  }

  toogle(e: React.MouseEvent<any>) {
    e.preventDefault();
    // issue#1005：react17下会触发document的click事件
    e.stopPropagation();

    this.setState({
      isOpened: !this.state.isOpened
    });
  }

  async open() {
    const {dispatchEvent, data, buttons, disabled} = this.props;

    // disabled 状态，trigger为hover，不应该出现下拉框
    if (disabled) {
      return;
    }
    await dispatchEvent(
      'mouseenter',
      createObject(data, {
        items: buttons // 为了保持名字统一
      })
    );
    this.setState({
      isOpened: true
    });
  }

  close(e?: React.MouseEvent<any>) {
    this.timer = setTimeout(() => {
      this.props.dispatchEvent(
        'mouseleave',
        createObject(this.props.data, {items: this.props.buttons})
      );
      // FIX: 当组件销毁之后，仍然 setState，会导致报错。（判断组件在未销毁时，再调用setState）
      if (this.target) {
        this.setState({
          isOpened: false
        });
      }
    }, 200);

    // 如果是下拉菜单，并且是下载链接，则不阻止默认事件
    if (!(e?.target as any)?.getAttribute?.('download')) {
      // PopOver hide会直接调用close方法
      e && e.preventDefault();
    }
  }

  keepOpen() {
    if (this.timer) {
      clearTimeout(this.timer);
    }
  }

  renderButton(
    button: DropdownButton,
    index: number | string
  ): React.ReactNode {
    const {render, classnames: cx, data} = this.props;
    index = typeof index === 'number' ? index.toString() : index;

    if (typeof button !== 'string' && Array.isArray(button?.children)) {
      return (
        <div key={index} className={cx('DropDown-menu')}>
          <li key={`${index}/0`} className={cx('DropDown-groupTitle')}>
            {button.icon ? <Icon cx={cx} icon={button.icon} className="m-r-xs" /> : null}
            <span>{button.label}</span>
          </li>
          {button.children.map((child, childIndex) =>
            this.renderButton(child, `${index}/${childIndex + 1}`)
          )}
        </div>
      );
    }

    if (typeof button !== 'string' && !isVisible(button, data)) {
      return null;
    } else if (button === 'divider' || button.type === 'divider') {
      return <li key={index} className={cx('DropDown-divider')} />;
    } else {
      return (
        <li
          key={index}
          className={cx('DropDown-button', {
            ['is-disabled']: isDisabled(button, data)
          })}
        >
          {render(`button/${index}`, {
            type: 'button',
            ...(button as any),
            isMenuItem: true
          })}
        </li>
      );
    }
  }

  renderOuter() {
    const {
      render,
      buttons,
      data,
      popOverContainer,
      popOverContainerSelector,
      classnames: cx,
      classPrefix: ns,
      children,
      align,
      closeOnClick,
      closeOnOutside,
      menuClassName,
      overlayPlacement,
      trigger
    } = this.props;
    let body = (
      <RootClose
        disabled={!this.state.isOpened}
        onRootClose={closeOnOutside !== false ? this.close : noop}
      >
        {(ref: any) => {
          return (
            <ul
              className={cx(
                'DropDown-menu-root',
                'DropDown-menu',
                menuClassName
              )}
              onClick={closeOnClick ? this.close : noop}
              onMouseEnter={this.keepOpen}
              ref={ref}
            >
              {children
                ? children
                : Array.isArray(buttons)
                ? buttons.map((button, index) =>
                    this.renderButton(button, index)
                  )
                : null}
            </ul>
          );
        }}
      </RootClose>
    );
    if (popOverContainer || popOverContainerSelector) {
      return (
        <Overlay
          container={popOverContainer}
          target={() => this.target}
          placement={overlayPlacement}
          containerSelector={popOverContainerSelector}
          show
        >
          <PopOver
            overlay={trigger !== 'hover'} // 触发方式为hover时，不渲染蒙层；渲染会导致鼠标移出，下拉菜单不自动关闭
            onHide={this.close}
            classPrefix={ns}
            className={cx('DropDown-popover', menuClassName)}
            style={{minWidth: this.target?.offsetWidth}}
          >
            {body}
          </PopOver>
        </Overlay>
      );
    }

    return body;
  }

  render() {
    const {
      tooltip,
      placement,
      tooltipContainer,
      tooltipTrigger,
      tooltipRootClose,
      disabledTip,
      block,
      disabled,
      btnDisabled,
      btnClassName,
      size = 'default',
      label,
      level,
      primary,
      className,
      style,
      classnames: cx,
      align,
      iconOnly,
      icon,
      rightIcon,
      isActived,
      trigger,
      data,
      hideCaret,
      tooltipContainerSelector,
      env
    } = this.props;

    return (
      <div
        className={cx(
          'DropDown ',
          {
            'DropDown--block': block,
            'DropDown--alignRight': align === 'right',
            'is-opened': this.state.isOpened,
            'is-actived': isActived
          },
          className
        )}
        style={style}
        onMouseEnter={trigger === 'hover' ? this.open : () => {}}
        onMouseLeave={trigger === 'hover' ? this.close : () => {}}
        ref={this.domRef}
      >
        <TooltipWrapper
          placement={placement}
          tooltip={disabled ? disabledTip : tooltip}
          container={tooltipContainer || env?.getModalContainer}
          containerSelector={tooltipContainerSelector}
          trigger={tooltipTrigger}
          rootClose={tooltipRootClose}
        >
          <button
            onClick={this.toogle}
            disabled={disabled || btnDisabled}
            className={cx(
              'Button',
              btnClassName,
              typeof level === 'undefined'
                ? 'Button--default'
                : level
                ? `Button--${level}`
                : '',
              {
                'Button--block': block,
                'Button--primary': primary,
                'Button--iconOnly': iconOnly
              },
              `Button--size-${size}`
            )}
          >
            <Icon cx={cx} icon={icon} className="icon m-r-xs" />
            {typeof label === 'string' ? filter(label, data) : label}
            {rightIcon && (
              <Icon cx={cx} icon={rightIcon} className="icon m-l-xs" />
            )}
            {!hideCaret ? (
              <span className={cx('DropDown-caret')}>
                <Icon icon="caret" className="icon" />
              </span>
            ) : null}
          </button>
        </TooltipWrapper>
        {this.state.isOpened ? this.renderOuter() : null}
      </div>
    );
  }
}

@Renderer({
  type: 'dropdown-button'
})
export class DropDownButtonRenderer extends DropDownButton {}
