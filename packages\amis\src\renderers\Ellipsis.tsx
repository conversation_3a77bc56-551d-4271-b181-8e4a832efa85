import React, {createRef} from 'react';
import debouce from 'lodash/debounce';
import {RendererProps, Schema} from 'amis-core';

import hoistNonReactStatic from 'hoist-non-react-statics';

import {filter, isObject} from 'amis-core';
import { TooltipWrapper } from 'amis-ui';
import type {TooltipObject} from 'amis-ui/lib/components/TooltipWrapper';

export interface SchemaEllipsisObject {
  /**
   * 配置宽度
   */
  width?: number | string;

  /**
   * 配置最高几行
   */
  rows?: number;

  /**
   * 提示配置
   */
  tooltip?: TooltipObject;
  style?: React.CSSProperties;
}

export type SchemaEllipsis = boolean | SchemaEllipsisObject;

export interface EllipsisProps extends RendererProps {
  name?: string;
  label?: string;
  ellipsis: SchemaEllipsis;
}

export const HocEllipsis =
  () =>
  (Component: React.ComponentType<any>): any => {
    class EllipsisComponent extends React.PureComponent<EllipsisProps, any> {
      static ComposedComponent = Component;

      textRef = createRef<HTMLDivElement>();
      hideRef = createRef<HTMLDivElement>()

      lazyCheckExceed = debouce(this.checkExceed.bind(this), 250);

      constructor(props: any) {
        super(props);
        this.state = {
          isExceed: false,
        };
      }

      componentDidMount() {
        this.checkExceed();
        window.addEventListener('resize', this.lazyCheckExceed);
      }

      componentWillUnmount() {
        window.removeEventListener('resize', this.lazyCheckExceed);
      }

      // 检测内容是否溢出
      checkExceed () {
        const { classnames: cx, className, ellipsis } = this.props;
        const { rows } = ellipsis || {};
        // 创建元素
        const element = this.textRef.current;

        if (element) {
          // const hideElement = document.createElement('span');
          // // 设置元素属性
          // const classList = cx(
          //   'Typography',
          //   'Typography--hidden',
          //   'Typography-common',
          //   rows ? 'Typography-hidden-multiline' : 'Typography-hidden-singleline',
          //   className,
          // ).split(' ');
          // hideElement.innerHTML = element.innerHTML;
          // hideElement.classList.add(...classList);
          // element?.parentElement?.appendChild(hideElement)

          // // 多行文本时，处理下有 padding 时等的情况下，宽度需要与原来的相等再进行计算
          // if (this.props.ellipsis?.rows) {
          //   hideElement.style.width = element.offsetWidth + 'px';
          // }

          // const hideElementBound = hideElement.getBoundingClientRect();
          // const elementBound = element.getBoundingClientRect();
          // const isExceed =
          //   +hideElementBound.height.toFixed(2) > +elementBound.height.toFixed(2) ||
          //   +hideElementBound.width.toFixed(2) > +elementBound.width.toFixed(2);
          const isExceed =
            element.scrollHeight > element.clientHeight ||
              element.scrollWidth > element.clientWidth;

          this.setState({isExceed});
          // 计算结束位置之后移除元素
          // element?.parentElement?.removeChild(hideElement);
        }
      };

      renderContent() {
        const {
          classnames: cx,
          className,
          ellipsis,
          $schema,
        } = this.props;
        const { rows, width = $schema?.column?.width, style, } = ellipsis as SchemaEllipsisObject;

        const measureStyle: React.CSSProperties = {
          width,
          margin: 0,
          padding: 0,
        };

        const content = (
          <span
            ref={this.textRef}
            style={{
              ...measureStyle,
              ...style,
              WebkitLineClamp: rows,
            }}
            className={cx(
              'Typography',
              'Typography-common',
              'Typography-content',
              rows ? 'Typography-ellipsis-multiline' : 'Typography-ellipsis-singleline',
              className,
            )}
          >
            <Component {...this.props} contentsOnly noHoc />
          </span>
        );

        return content;
      }

      renderBody() {
        const {ellipsis, classnames: cx, className} = this.props;
        const {isExceed} = this.state;
        const {tooltip} = ellipsis as SchemaEllipsisObject;

        const content = this.getTooltipContent();

        /**
         * 是否展示tooltip，下面满足一条即可
         * 1. 内容溢出
         * 2. tooltip配置内容时
         */
        const tooltipContent = isObject(tooltip) ? tooltip!.content : tooltip;
        const showTooltip = isExceed || tooltipContent !== undefined;

        if (showTooltip) {
          return (
            <TooltipWrapper
              tooltipClassName={cx('Typography-tooltip')}
              tooltip={{
                tooltipTheme: 'dark',
                disabled: !showTooltip,
                ...(isObject(tooltip) ? tooltip : {}),
                content, // content支持schema
              }}
            >
              {this.renderContent()}
            </TooltipWrapper>
          );
        }

        return this.renderContent();
      }

      /**
       * 获取tooltip内容
       * 1、默认取column的name
       * 2、配置tooltip的content时，取配置的值。content支持schema
       *
       */
      getTooltipContent() {
        const { data, render, ellipsis, name } = this.props;
        const { tooltip } = ellipsis as SchemaEllipsisObject;
        const tooltipContent = isObject(tooltip) ? tooltip!.content : tooltip;

        // 1. 如果配置了tooltip的content，则直接返回
        if (
          isObject(tooltipContent) &&
          (tooltipContent as unknown as Schema).type || Array.isArray(tooltipContent)
        ) {
          return render('tooltip', tooltipContent as unknown as Schema);
        }

        // 2. 默认取column的name
        let content = filter('${' + name + ' | raw }', data);
        if (tooltipContent) {
          content = tooltipContent as string;
        }

        // 3. 如果是自定义渲染，又没有配置tooltip，取textRef的innerHTML兜底
        if (!content && this.textRef.current) {
          content = this.textRef.current.innerHTML;
        }

        return render('text', {
          type: 'tpl',
          tpl: content,
        })
      }

      render() {
        const {
          className,
          noHoc,
          classnames: cx,
          translate: __,
          ellipsis,
          $schema,
        } = this.props;

        if (
          $schema?.column?.type !== 'typography' &&
          !!ellipsis &&
          !noHoc
        ) {
          return (
            <Component
              {...this.props}
              className={cx(`Field--ellipsis`, className)}
            >
              {/* <Component {...this.props} contentsOnly noHoc /> */}
              {this.renderBody()}
            </Component>
          );
        }
        return <Component {...this.props} />;
      }
    }
    hoistNonReactStatic(EllipsisComponent, Component);
    return EllipsisComponent;
  };

export default HocEllipsis;
