import React from 'react';
import {<PERSON><PERSON><PERSON>, RendererProps, filter} from 'amis-core';
import {BaseSchema, SchemaTpl} from '../Schema';
import {Empty} from 'dataseed-ui';

/**
 * Empty
 * 文档：https://baidu.gitee.io/amis/docs/components/empty
 */
export interface EmptySchema extends BaseSchema {
  type: 'empty';
  description: SchemaTpl;
  [propName: string]: any;
}

export interface EmptyProps
  extends RendererProps,
    Omit<EmptySchema, 'type' | 'className'> {}

export default class Emptye extends React.Component<EmptyProps, object> {
  static defaultProps: Pick<EmptyProps, 'className' | 'icon' | 'description'> = {
    className: '',
    icon: "table-empty",
    description: "暂无数据"
  };

  render() {
    const {description, data} = this.props;
    const descriptionVal = filter(description, data);
    return (
      <Empty {...this.props} description={descriptionVal} />
    );
  }
}

@Renderer({
  type: 'empty'
})
export class EmptyRenderer extends Emptye {}
