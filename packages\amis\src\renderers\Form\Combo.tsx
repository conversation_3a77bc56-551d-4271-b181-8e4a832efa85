import {
  ActionObject,
  Api,
  ApiObject,
  ComboStore,
  FormControlProps,
  FormHorizontal,
  FormItem,
  IComboStore,
  isMobile,
  resolveEventData,
  tokenize,
  validateItems,
} from 'amis-core';
import {<PERSON><PERSON>, Tab, Tabs as CTabs} from 'amis-ui';
import cloneDeep from 'lodash/cloneDeep';
import React from 'react';
import {findDOMNode} from 'react-dom';

import {
  anyChanged,
  autobind,
  createObject,
  dataMapping,
  evalExpression,
  extendObject,
  filter,
  guid,
  isEffectiveApi,
  isObject,
  isObjectShallowModified,
  isPureVariable,
  ListenerAction,
  resolveVariable,
  resolveVariableAndFilter,
  SchemaNode,
  str2AsyncFunction,
} from 'amis-core';
import {Alert2, Icon, Select} from 'amis-ui';
import find from 'lodash/find';
import memoize from 'lodash/memoize';
import omit from 'lodash/omit';
import pick from 'lodash/pick';
import {isAlive} from 'mobx-state-tree';
import Sortable from 'sortablejs';
import type {SchemaTokenizeableString, SchemaExpression} from '../../Schema';
import {
  FormBaseControlSchema,
  SchemaApi,
  SchemaClassName,
  SchemaObject,
  SchemaTpl,
} from '../../Schema';

const Type = {
  InputKV: 'input-kv',
  InputKVS: 'input-kvs',
};

const CanNotAddErrMsg = '当前有项目的 key 未填写，无法新增项目';

export type ComboCondition = {
  test: string;
  items: Array<ComboSubControl>;
  label: string;
  scaffold?: any;
  mode?: string;
};

export type ComboSubControl = SchemaObject & {
  /**
   * 是否唯一, 只有在 combo 里面才有用
   */
  unique?: boolean;

  /**
   * 列类名，可以用来修改这类宽度。
   */
  columnClassName?: SchemaClassName;
};

/**
 * Combo 组合输入框类型
 * 文档：https://baidu.gitee.io/amis/docs/components/form/combo
 */
export interface ComboControlSchema extends FormBaseControlSchema {
  /**
   * 指定为组合输入框类型
   */
  type: 'combo';

  /**
   * 单组表单项初始值。默认为 `{}`
   *
   * @default {}
   */
  scaffold?: any;

  /**
   * 是否含有边框
   */
  noBorder?: boolean;

  /**
   * 确认删除时的提示
   */
  deleteConfirmText?: string;

  /**
   * 删除时调用的api
   */
  deleteApi?: SchemaApi;

  /**
   * 是否可切换条件，配合`conditions`使用
   */
  typeSwitchable?: boolean;

  /**
   * 符合某类条件后才渲染的schema
   */
  conditions?: Array<ComboCondition>;

  /**
   * 内部单组表单项的类名
   */
  formClassName?: SchemaClassName;

  /**
   * 新增按钮CSS类名
   */
  addButtonClassName?: SchemaClassName;

  /**
   * 新增按钮文字
   * @default 新增
   */
  addButtonText?: string;

  /**
   * 是否可新增
   */
  addable?: boolean;

  /**
   * Add at top
   */
  addattop?: boolean;

  /**
   * 数组输入框的子项
   */
  items?: Array<ComboSubControl>;

  /**
   * 是否可拖拽排序
   */
  draggable?: boolean;

  /**
   * 可拖拽排序的提示信息。
   *
   * @default 可拖拽排序
   */
  draggableTip?: string;

  /**
   * 是否将结果扁平化(去掉name),只有当controls的length为1且multiple为true的时候才有效
   */
  flat?: boolean;

  /**
   * 当扁平化开启并且joinValues为true时，用什么分隔符
   *
   * @deprecated
   */
  delimiter?: string;

  /**
   * 当扁平化开启的时候，是否用分隔符的形式发送给后端，否则采用array的方式
   * @deprecated
   */
  joinValues?: boolean;

  /**
   * 限制最大个数
   */
  maxLength?: number | SchemaTokenizeableString;

  /**
   * 限制最小个数
   */
  minLength?: number | SchemaTokenizeableString;

  /**
   * 是否多行模式，默认一行展示完
   */
  multiLine?: boolean;

  /**
   * 是否可多选
   */
  multiple?: boolean;

  /**
   * 是否可删除
   */
  removable?: boolean;

  /**
   * 是否可复制
   */
  copyable?: boolean;

  /**
   * 行新增按钮，multiple时有效
   */
  itemAddable?: boolean;

  /**
   * 子表单的模式。
   */
  subFormMode?: 'normal' | 'horizontal' | 'inline';

  /**
   * 如果是水平排版，这个属性可以细化水平排版的左右宽度占比。
   */
  subFormHorizontal?: FormHorizontal;

  /**  是否开启子表单强制校验 注意：开启此项后每次触发校验都会重新校验当前子表单的所有formItem */
  subFormForceValidate?: boolean;

  /**
   * 没有成员时显示。
   * @default empty
   */
  placeholder?: string;

  /**
   * 是否可以访问父级数据，正常 combo 已经关联到数组成员，是不能访问父级数据的。
   */
  canAccessSuperData?: boolean;

  /**
   * 采用 Tabs 展示方式？
   */
  tabsMode?: boolean;

  /**
   * 倒序增加模式
   */
  reverseMode?: boolean;

  /**
   * 前面第几个不可拖拽
   */
  disableDropTop?: number | SchemaTokenizeableString;

  /**
   * 前面第几个不可拖拽
   */
  disableDropBottom?: number | SchemaTokenizeableString;

  /**
   * 元素可删除
   */
  itemRemovableOn?: SchemaExpression;

  /**
   * 元素可删除
   */
  itemCopyableOn?: SchemaExpression;

  /**
   * Tabs 的展示模式。
   */
  tabsStyle?: '' | 'line' | 'card' | 'radio' | 'strong';

  /**
   * 选项卡标题的生成模板。
   */
  tabsLabelTpl?: SchemaTpl;

  /**
   * 选项卡图标的生成模板。支持纯字符串或可解析表达式，表达式将在当前 tab 数据上下文中运行。
   */
  tabsIconTpl?: SchemaTpl;

  /**
   * 卡片隐藏就销毁卡片节点。仅在tabs模式下生效，支持表达式
   */
  unmountOnExit?: boolean | string;

  /**
   * 卡片首次激活时才渲染。仅在tabs模式下生效，支持表达式
   */
  mountOnEnter?: boolean | string;

  /**
   * 数据比较多，比较卡时，可以试试开启。
   */
  lazyLoad?: boolean;

  /**
   * 严格模式，为了性能默认不开的。
   */
  strictMode?: boolean;

  /**
   * 配置同步字段。只有 `strictMode` 为 `false` 时有效。
   * 如果 Combo 层级比较深，底层的获取外层的数据可能不同步。
   * 但是给 combo 配置这个属性就能同步下来。输入格式：`["os"]`
   */
  syncFields?: string[];

  /**
   * 允许为空，如果子表单项里面配置验证器，且又是单条模式。可以允许用户选择清空（不填）。
   */
  nullable?: boolean;

  /**
   * 提示信息
   */
  messages?: {
    /**
     * 验证错误提示
     */
    validateFailed?: string;

    /**
     * 最少添加条数错误提示
     */
    minLengthValidateFailed?: string;

    /**
     * 最多添加条数错误提示
     */
    maxLengthValidateFailed?: string;

    /** 无法新增字段错误提示 */
    addFailed?: string;
  };
  updatePristineAfterStoreDataReInit?: boolean;

  /**
   * 是否始终展示新增按钮，配置后会始终展示新增按钮
   */
  alwaysShowAddBtn?: boolean;

  /**
   * 自定义操作按钮，multiple为true时生效
   */
  extraActions?: SchemaNode | SchemaNode[];

  /**
   * 选项卡图标位置，支持 'left' | 'right'，也支持表达式。
   */
  tabsIconPosition?: 'left' | 'right' | string;

  /**
   * 超过多少个时折叠按钮
   */
  tabsCollapseOnExceed?: number;

  /**
   * 折叠按钮文字
   */
  tabsCollapseBtnLabel?: string;
}

export type ComboRendererEvent = 'add' | 'delete' | 'tabsChange';

function pickVars(vars: any, fields: Array<string>) {
  return fields.reduce((data: any, key: string) => {
    data[key] = resolveVariable(key, vars);
    return data;
  }, {});
}

export interface ComboProps
  extends FormControlProps,
    Omit<
      ComboControlSchema,
      'type' | 'className' | 'descriptionClassName' | 'inputClassName'
    > {
  store: IComboStore;
  changeImmediately?: boolean;
}

interface ComboState {
  showCanNotAddHint: boolean;
  dialogCtx?: {
    mode?: 'add' | 'edit';
    index?: number;
  };
}

export default class ComboControl extends React.Component<
  ComboProps,
  ComboState
> {
  static defaultProps: Pick<
    ComboProps,
    | 'minLength'
    | 'maxLength'
    | 'multiple'
    | 'multiLine'
    | 'addButtonClassName'
    | 'formClassName'
    | 'subFormMode'
    | 'draggableTip'
    | 'addButtonText'
    | 'canAccessSuperData'
    | 'addIcon'
    | 'dragIcon'
    | 'deleteIcon'
    | 'tabsMode'
    | 'tabsStyle'
    | 'placeholder'
    | 'itemClassName'
    | 'itemsWrapperClassName'
    | 'subFormForceValidate'
    | 'mountOnEnter'
    | 'unmountOnExit'
    | 'alwaysShowAddBtn'
    | 'copyable'
    | 'itemAddable'
  > = {
    minLength: 0,
    maxLength: Infinity,
    multiple: false,
    multiLine: false,
    addButtonClassName: '',
    formClassName: '',
    subFormMode: 'normal',
    draggableTip: '',
    addButtonText: 'add',
    canAccessSuperData: false,
    addIcon: true,
    dragIcon: '',
    deleteIcon: '',
    tabsMode: false,
    tabsStyle: '',
    placeholder: 'placeholder.empty',
    itemClassName: '',
    itemsWrapperClassName: '',
    subFormForceValidate: false,
    mountOnEnter: true,
    unmountOnExit: false,
    alwaysShowAddBtn: false,
    copyable: false,
    itemAddable: false,
  };
  static propsList: Array<string> = [
    'minLength',
    'maxLength',
    'multiple',
    'multiLine',
    'addButtonClassName',
    'subFormMode',
    'draggableTip',
    'addButtonText',
    'draggable',
    'scaffold',
    'canAccessSuperData',
    'addIcon',
    'dragIcon',
    'deleteIcon',
    'noBorder',
    'conditions',
    'tabsMode',
    'tabsStyle',
    'lazyLoad',
    'changeImmediately',
    'strictMode',
    'items',
    'conditions',
    'messages',
    'formStore',
    'itemClassName',
    'itemsWrapperClassName',
    'subFormForceValidate',
    'mountOnEnter',
    'unmountOnExit',
    'tabsIconTpl',
    'tabsIconPosition',
    'tabsCollapseOnExceed',
    'tabsCollapseBtnLabel',
  ];

  subForms: Array<any> = [];
  subFormDefaultValues: Array<{
    index: number;
    values: any;
    setted: boolean;
  }> = [];

  keys: Array<string> = [];
  dragTip?: HTMLElement;
  sortable?: Sortable;
  defaultValue?: any;
  toDispose: Array<Function> = [];
  id: string = guid();
  constructor(props: ComboProps) {
    super(props);

    this.state = {
      showCanNotAddHint: false,
      dialogCtx: undefined,
    };

    this.handleChange = this.handleChange.bind(this);
    this.handleSingleFormChange = this.handleSingleFormChange.bind(this);
    this.handleSingleFormInit = this.handleSingleFormInit.bind(this);
    this.handleFormInit = this.handleFormInit.bind(this);
    this.handleAction = this.handleAction.bind(this);
    this.deleteItem = this.deleteItem.bind(this);
    this.dragTipRef = this.dragTipRef.bind(this);
    this.flush = this.flush.bind(this);
    this.handleComboTypeChange = this.handleComboTypeChange.bind(this);
    this.defaultValue = {
      ...props.scaffold,
    };

    const {store, value, multiple, formItem, addHook} = props;

    store.config({
      multiple,
      minLength: this.resolveVariableProps(props, 'minLength'),
      maxLength: this.resolveVariableProps(props, 'maxLength'),
      length: this.getValueAsArray(props).length,
    });

    formItem && isAlive(formItem) && formItem.setSubStore(store);
    addHook && this.toDispose.push(addHook(this.flush, 'flush'));
  }

  componentDidMount(): void {
    const { $schema, store, data } = this.props;
    const { defaultKey, defaultActiveKey } = $schema;

    // 设置默认激活tab
    let activeKey = 0;
    if (typeof this.props.activeKey !== 'undefined') {
      activeKey = this.props.activeKey;
    } else if (defaultKey !== undefined) {
      activeKey =
        typeof defaultKey === 'string'
          ? resolveVariableAndFilter(defaultKey, data)
          : defaultKey;
    } else if (defaultActiveKey) {
      // schema中的activeKey会转成defaultActiveKey，仅支持表达式
      activeKey = resolveVariableAndFilter(
        defaultActiveKey,
        data
      );
    }

    store.setActiveKey(activeKey);
  }

  componentDidUpdate(prevProps: ComboProps) {
    const props = this.props;

    if (anyChanged(['minLength', 'maxLength', 'value'], prevProps, props)) {
      const {store, multiple} = props;
      const values = this.getValueAsArray(props);

      store.config({
        multiple,
        minLength: this.resolveVariableProps(props, 'minLength'),
        maxLength: this.resolveVariableProps(props, 'maxLength'),
        length: values.length,
      });

      if (store.activeKey >= values.length) {
        store.setActiveKey(Math.max(0, values.length - 1));
      }

      // combo 进来了新的值，且这次 form 初始化时带来的新值变化，但是之前的值已经 onInit 过了
      // 所以，之前 onInit 设置进去的初始值是过时了的。这个时候修复一下。
      if (
        props.value !== prevProps.value &&
        !prevProps.formInited &&
        this.subFormDefaultValues.length
      ) {
        this.subFormDefaultValues = this.subFormDefaultValues.map(
          (item, index) => {
            return {
              ...item,
              values: values[index],
            };
          },
        );
      }
    }

    // 检测 syncFields 变化，保持数据一致性
    const {syncFields, data, onChange, multiple} = props;
    if (Array.isArray(syncFields) && syncFields.length > 0 && props.strictMode === false) {
      // 检测外层 syncFields 的变化
      const hasExternalChange = syncFields.some(field => {
        const prevValue = resolveVariable(field, prevProps.data);
        const currentValue = resolveVariable(field, data);
        return prevValue !== currentValue;
      });

      if (hasExternalChange) {
        // 获取变化的字段值
        const changedSyncData = pickVars(data, syncFields);

        if (multiple) {
          // 多选模式：更新所有combo项的syncFields字段
          const currentValue = this.getValueAsArray();
          const newValue = currentValue.map((item: any) => ({
            ...item,
            ...changedSyncData  // 只覆盖syncFields指定的字段
          }));

          onChange(newValue, false, true);
        } else {
          // 单选模式：更新combo值的syncFields字段
          const currentValue = props.value || {};
          const newValue = {
            ...currentValue,
            ...changedSyncData
          };

          onChange(newValue, false, true);
        }
      }
    }

    if (props.value !== prevProps.value) {
      this.hideCanNotAddHintIfNecessary(props.value);
    }
  }

  // 提前知道变量值改变，避免二次渲染
  UNSAFE_componentWillReceiveProps(nextProps: CollapseGroupProps) {
    const props = this.props;
    const {store} = props;

    const prevActiveKey = resolveVariableAndFilter(
      props.defaultActiveKey,
      props.data
    );
    const activeKey = resolveVariableAndFilter(
      nextProps.defaultActiveKey,
      nextProps.data
    );
    // activeKey 变量值变化时，需要更新 activeKey
    if (prevActiveKey !== activeKey && activeKey != undefined) {
      store.setActiveKey(activeKey);
    }
  }

  componentWillUnmount() {
    const {formItem} = this.props;

    formItem && isAlive(formItem) && formItem.setSubStore(null);

    this.toDispose.forEach(fn => fn());
    this.toDispose = [];
    this.memoizedFormatValue.cache.clear?.();
    this.makeFormRef.cache.clear?.();
  }

  /** 解析props中的变量，目前支持'minLength' | 'maxLength' */
  resolveVariableProps(props: ComboProps, key: 'minLength' | 'maxLength') {
    const defaultMap = {
      minLength: 0,
      maxLength: Infinity,
    };
    let value = props[key];

    if (!value) {
      return defaultMap[key];
    }

    if (typeof value === 'string') {
      if (isPureVariable(value)) {
        const resolved = resolveVariableAndFilter(value, props.data, '| raw');
        value = (
          typeof resolved === 'number' && resolved >= 0
            ? resolved
            : defaultMap[key]
        ) as number;
      } else {
        const parsed = parseInt(value, 10);
        value = (isNaN(parsed) ? defaultMap[key] : parsed) as number;
      }
    }

    return value;
  }

  doAction(action: ListenerAction, args: any) {
    const actionType = action?.actionType as string;
    const {onChange, resetValue, tabsMode} = this.props;

    if (actionType === 'addItem') {
      this.addItem(args.item ?? {}, args.index);
    } else if (actionType === 'clear') {
      onChange('');
    } else if (actionType === 'reset') {
      onChange(resetValue ?? '');
    } else if (actionType === 'changeActiveKey' && tabsMode) {
      // tabs模式支持 changeActiveKey 动作，用于切换tab
      let activeKey = args?.activeKey as number;
      // 处理非用户自定义key，索引从1开始
      if (typeof args?.activeKey !== 'string') {
        activeKey--;
      }
      this.handleTabSelect(activeKey);
    }
  }

  getValueAsArray(props = this.props) {
    const {flat, joinValues, delimiter, type, formItem} = props;
    // 因为 combo 多个子表单可能同时发生变化。
    // onChagne 触发多次，上次变更还没应用到 props.value 上来，这次触发变更就会包含历史数据，把上次触发的数据给重置成旧的了。
    // 通过props.getValue 获取到的始终是最新的，issue#460
    let value = props.getValue();

    if (joinValues && flat && typeof value === 'string') {
      value = value.split(delimiter || ',');
    } else if (!Array.isArray(value)) {
      value = [];
    } else {
      value = value.concat();
    }
    return value;
  }

  /** 清除"无法新增错误提示"，仅在所有 key 都变为非空时清除 */
  hideCanNotAddHintIfNecessary(value: Array<Record<string, any>>) {
    if (!this.state.showCanNotAddHint) {
      return;
    }

    const {type} = this.props;

    if (
      (type === Type.InputKV && value.every(item => item.key)) ||
      (type === Type.InputKVS && value.every(item => item._key))
    ) {
      this.setState({showCanNotAddHint: false});
    }
  }

  // 复制操作
  @autobind
  async copyItem(index: number) {
    const { dispatchEvent } = this.props;
    let value = this.getValueAsArray();
    let copyValue = value[index];

    const rendererEvent = await dispatchEvent(
      'copy',
      resolveEventData(
        this.props,
        {
          value,
          copyValue,
          copyIndex: index,
        },
        'value',
      ),
    );

    if (rendererEvent?.prevented) {
      return;
    }

    this.addItem(copyValue, index + 1);
  }

  // 行添加操作
  @autobind
  async itemAddItem(index: number) {
    this.addItem(undefined, index + 1);
  }

  // tabs模式拦截新增逻辑，先判断是否需要先弹窗再新增
  @autobind
  tabsAddItem() {
    const {addFormDialog} = this.props;
    if (addFormDialog) {
      this.open();
    } else {
      this.addItem();
    }
  }

  /**
   * 新增
   *
   * @param itemValue 新增元素值
   * @param index 新增元素索引
   */
  @autobind
  async addItem(itemValue?: any, index?: number) {
    const {
      type,
      value: valueProp,
      flat,
      joinValues,
      addattop,
      delimiter,
      scaffold,
      disabled,
      submitOnChange,
      dispatchEvent,
      name,
      store,
      tabsMode,
      maxLength,
      env,
      reverseMode,
    } = this.props;

    if (disabled || !name) {
      return;
    }

    // 新增超过maxLength提示
    if (maxLength !== undefined && valueProp?.length >= maxLength) {
      env.notify('error', `组合表单已达到${maxLength}个上限，无法继续新增`);
      return;
    }

    // 仅在点击"新增"按钮时检查是否显示无法新增提示
    if (
      (type === Type.InputKV &&
        valueProp.some((item: Record<string, any>) => item.key === '')) ||
      (type === Type.InputKVS &&
        valueProp.some((item: Record<string, any>) => item._key === ''))
    ) {
      this.setState({showCanNotAddHint: true});
      return;
    }

    let value = this.getValueAsArray();

    // todo:这里的数据结构与表单项最终类型不一致，需要区分是否多选、是否未input-kv or input-kvs
    const rendererEvent = await dispatchEvent(
      'add',
      resolveEventData(
        this.props,
        {
          value:
            flat && joinValues
              ? value.join(delimiter || ',')
              : cloneDeep(value),
        },
        'value',
      ),
    );

    if (rendererEvent?.prevented) {
      return;
    }

    // 如果新增时没有传入值，则使用默认值
    itemValue = itemValue === undefined ? scaffold : itemValue;

    // 新增值插入到指定索引位置
    if (index !== undefined) {
      value.splice(index, 0, itemValue);
      this.keys.splice(index, 0, guid());
    } else {
      value.push(
        flat
          ? itemValue ?? ''
          : {
              ...itemValue,
            },
      );
      this.keys.push(guid());

      // 配置addattop，同时没有指定插入索引，新增项在最上面
      if ((addattop === true || reverseMode === true) && index === undefined) {
        value.unshift(value.pop());
        this.keys.unshift(this.keys.pop()!);
      }
    }

    if (flat && joinValues) {
      value = value.join(delimiter || ',');
    }

    // 如果是tab模式，新增后需要定位最新tab
    if (tabsMode) {
      let activeKey =
        index !== undefined ? index : (addattop || reverseMode) ? 0 : value.length - 1;
      store.setActiveKey(activeKey);
    }

    this.props.onChange(value, submitOnChange, true);
    const addSuccessEvent = await dispatchEvent(
      'addSuccess',
      resolveEventData(
        this.props,
        {
          value:
            flat && joinValues
              ? value.join(delimiter || ',')
              : cloneDeep(value),
        },
        'value',
      ),
    );
    if (addSuccessEvent?.prevented) {
      return;
    }
  }

  @autobind
  async deleteItem(key: number) {
    const {
      flat,
      joinValues,
      delimiter,
      disabled,
      deleteApi,
      deleteConfirmText,
      data,
      env,
      translate: __,
      dispatchEvent,
    } = this.props;

    if (disabled) {
      return;
    }

    let value = this.getValueAsArray();
    const ctx = createObject(data, value[key]);

    // todo:这里的数据结构与表单项最终类型不一致，需要区分是否多选、是否未input-kv or input-kvs
    const rendererEvent = await dispatchEvent(
      'delete',
      resolveEventData(
        this.props,
        {
          key,
          value:
            flat && joinValues
              ? value.join(delimiter || ',')
              : cloneDeep(value),
          item: value[key],
        },
        'value',
      ),
    );

    if (rendererEvent?.prevented) {
      return;
    }

    if (isEffectiveApi(deleteApi, ctx)) {
      const confirmed = await env.confirm(
        deleteConfirmText
          ? filter(deleteConfirmText, ctx)
          : __('deleteConfirm'),
      );
      if (!confirmed) {
        // 如果不确认，则跳过！
        return;
      }

      const result = await env.fetcher(deleteApi as Api, ctx);

      if (!result.ok) {
        env.notify(
          'error',
          (deleteApi as ApiObject)?.messages?.failed ?? __('deleteFailed'),
        );
        return;
      }
    }

    value.splice(key, 1);
    this.keys.splice(key, 1);

    if (flat && joinValues) {
      value = value.join(delimiter || ',');
    }

    this.props.onChange(value);
  }

  handleChange(values: any, diff: any, {index}: any) {
    const {flat, store, joinValues, delimiter, disabled, submitOnChange, type} =
      this.props;

    if (disabled) {
      return;
    }

    let value = this.getValueAsArray();
    value[index] = flat ? values.flat : {...values};
    this.hideCanNotAddHintIfNecessary(value);

    if (flat && joinValues) {
      value = value.join(delimiter || ',');
    }

    if (type === 'input-kv') {
      let hasDuplicateKey = false;
      const keys: {[key: string]: boolean} = {};
      for (const item of value) {
        if (keys[item.key]) {
          hasDuplicateKey = true;
        } else {
          keys[item.key] = true;
        }
      }
      // 有重复值就不触发修改，因为 KV 模式下无法支持重复值
      if (!hasDuplicateKey) {
        this.props.onChange(value, submitOnChange, true);
      }
    } else if (type === 'input-kvs') {
      // input-kvs 为了避免冲突 key 改成了 _key
      let hasDuplicateKey = false;
      const keys: {[key: string]: boolean} = {};
      for (const item of value) {
        if ('_key' in item) {
          if (keys[item._key]) {
            hasDuplicateKey = true;
          } else {
            keys[item._key] = true;
          }
        }
      }
      // 有重复值就不触发修改，因为 KV 模式下无法支持重复值
      if (!hasDuplicateKey) {
        this.props.onChange(value, submitOnChange, true);
      }
    } else {
      this.props.onChange(value, submitOnChange, true);
    }

    // store.forms.forEach(
    //   form =>
    //     isAlive(form) &&
    //     form.items.forEach(
    //       item => item.unique && item.syncOptions(undefined, form.data),
    //     ),
    // );
  }

  handleSingleFormChange(values: object) {
    this.props.onChange(
      {
        ...values,
      },
      this.props.submitOnChange,
      true,
    );
  }

  handleFormInit(values: any, {index}: any) {
    const {
      syncDefaultValue,
      flat,
      joinValues,
      delimiter,
      formInited,
      onChange,
      submitOnChange,
      setPrinstineValue,
    } = this.props;

    this.subFormDefaultValues.push({
      index,
      values,
      setted: false,
    });

    if (
      syncDefaultValue === false ||
      this.subFormDefaultValues.length <
        this.subForms.filter(item => item !== undefined).length
    ) {
      return;
    }

    let value = this.getValueAsArray();
    let isModified = false;
    this.subFormDefaultValues = this.subFormDefaultValues.map(
      ({index, values, setted}) => {
        const newValue = flat ? values.flat : {...values};

        if (!setted && isObjectShallowModified(value[index], newValue)) {
          value[index] = flat ? values.flat : {...values};
          isModified = true;
        }

        return {
          index,
          values,
          setted: true,
        };
      },
    );

    if (!isModified) {
      return;
    }

    if (flat && joinValues) {
      value = value.join(delimiter || ',');
    }

    formInited
      ? onChange(value, submitOnChange, true)
      : setPrinstineValue(value);
  }

  handleSingleFormInit(values: any) {
    const {syncDefaultValue, setPrinstineValue, value, nullable} = this.props;

    if (
      syncDefaultValue !== false &&
      !nullable &&
      isObjectShallowModified(value, values)
    ) {
      setPrinstineValue({
        ...values,
      });
    }
  }

  handleAction(e: React.UIEvent<any> | undefined, action: ActionObject): any {
    const {onAction} = this.props;

    if (action.actionType === 'delete') {
      action.index !== void 0 && this.deleteItem(action.index);
      return;
    }

    onAction && onAction.apply(null, arguments);
  }

  /**
   * 下发给子表单的onValidChange，子表单校验状态改变时回调通知
   * @param valid 子表单校验状态
   * @param index 子表单索引
   */
  @autobind
  handleSubFormValid(valid: boolean, {index}: any) {
    const {store} = this.props;
    store.setMemberValid(valid, index);
  }

  validate(): any {
    const {
      messages,
      nullable,
      translate: __,
      subFormForceValidate,
      store,
      tabsMode,
    } = this.props;
    const value = this.getValueAsArray();
    // const minLength = this.resolveVariableProps(this.props, 'minLength');
    // const maxLength = this.resolveVariableProps(this.props, 'maxLength');

    // if (minLength && (!Array.isArray(value) || value.length < minLength)) {
    //   return __(
    //     (messages && messages.minLengthValidateFailed) || 'Combo.minLength',
    //     {minLength},
    //   );
    // } else if (maxLength && Array.isArray(value) && value.length > maxLength) {
    //   return __(
    //     (messages && messages.maxLengthValidateFailed) || 'Combo.maxLength',
    //     {maxLength},
    //   );
    // } else
    if (this.subForms.length && (!nullable || value)) {
      return Promise.all(
        this.subForms.map(item => item.validate(subFormForceValidate)),
      ).then(values => {
        // tabs模式下，当存在校验不通过的tab时
        if (tabsMode && values.some(item => item === false)) {
          const firstErrorTabIndex = values.findIndex(item => item === false);
          // 定位到第一个错误的tab
          store.setActiveKey(firstErrorTabIndex);

          // memberValid 收集子表单校验状态
          Object.entries(values).forEach(([index, value]) => {
            store.setMemberValid(value, Number(index));
          });
        }

        if (~values.indexOf(false)) {
          return __((messages && messages.validateFailed) || 'validateFailed');
        }

        return;
      });
    }
  }

  flush() {
    this.subForms.forEach(form => form.flush());
  }

  dragTipRef(ref: any) {
    if (!this.dragTip && ref) {
      this.initDragging();
    } else if (this.dragTip && !ref) {
      this.destroyDragging();
    }

    this.dragTip = ref;
  }

  initDragging() {
    const { classPrefix: ns, data, disableDropTop = 0, disableDropBottom = 0 } = this.props;
    const topNumber: number = Number(tokenize(disableDropTop as string, data));
    const bottomNumber: number = Number(tokenize(disableDropBottom as string, data));
    const submitOnChange = this.props.submitOnChange;
    const dom = findDOMNode(this) as HTMLElement;
    this.sortable = new Sortable(
      dom.querySelector(`.${ns}Combo-items`) as HTMLElement,
      {
        group: `combo-${this.id}`,
        animation: 150,
        handle: `.${ns}Combo-itemDrager`,
        filter: `.${ns}Combo-itemDrager.is-dragDisabled`,
        ghostClass: `${ns}Combo-item--dragging`,
        onMove: (e: any) => {
          const parent = e.to as HTMLElement;
          const items = parent.querySelectorAll(`.${ns}Combo-item`);
          const siblings: any[] = Array.from(items);

          const related = e.related;
          // 移动到了不允许的位置，直接返回
          const  relatedIndex: number = Array.prototype.indexOf.call(siblings, related);
          if (relatedIndex < topNumber || relatedIndex >= siblings.length - bottomNumber) {
            return false;
          }

          return true;
        },
        onEnd: (e: any) => {
          // 没有移动
          if (e.newIndex === e.oldIndex) {
            return;
          }

          // 换回来
          const parent = e.to as HTMLElement;
          if (e.oldIndex < parent.childNodes.length - 1) {
            parent.insertBefore(e.item, parent.childNodes[e.oldIndex]);
          } else {
            parent.appendChild(e.item);
          }

          const value = this.props.value;
          if (!Array.isArray(value)) {
            return;
          }
          const newValue = value.concat();
          newValue.splice(e.newIndex, 0, newValue.splice(e.oldIndex, 1)[0]);
          this.keys.splice(e.newIndex, 0, this.keys.splice(e.oldIndex, 1)[0]);
          this.props.onChange(newValue, submitOnChange, true);
        },
      },
    );
  }

  destroyDragging() {
    this.sortable && this.sortable.destroy();
  }

  refsMap: {
    [propName: number]: any;
  } = {};

  makeFormRef = memoize(
    (index: number) => (ref: any) => this.formRef(ref, index),
  );

  formRef(ref: any, index: number = 0) {
    if (ref) {
      while (ref && ref.getWrappedInstance) {
        ref = ref.getWrappedInstance();
      }
      this.subForms[index] = ref;
      this.refsMap[index] = ref;
    } else {
      const form = this.refsMap[index];
      this.subForms = this.subForms.filter(item => item !== form);
      this.subFormDefaultValues = this.subFormDefaultValues.filter(
        ({index: dIndex}) => dIndex !== index,
      );
      delete this.refsMap[index];
    }
  }

  memoizedFormatValue = memoize(
    (
      strictMode: boolean,
      syncFields: Array<string> | void,
      value: any,
      index: number,
      data: any,
    ) => {
      return createObject(
        extendObject(data, {index, __index: index, ...data}),
        {
          ...value,
          ...(Array.isArray(syncFields) ? pickVars(data, syncFields!) : null),
        },
      );
    },
    (
      strictMode: boolean,
      syncFields: Array<string> | void,
      value: any,
      index: number,
      data: any,
    ) =>
      Array.isArray(syncFields)
        ? JSON.stringify([value, index, data, pickVars(data, syncFields)])
        : strictMode
        ? JSON.stringify([value, index])
        : JSON.stringify([value, index, data]),
  );

  formatValue(value: any, index: number = -1) {
    const {flat, data, strictMode, syncFields} = this.props;

    if (flat) {
      value = {
        flat: value,
      };
    }

    value = value || this.defaultValue;

    return this.memoizedFormatValue(
      strictMode !== false,
      syncFields,
      value,
      index,
      data,
    );
  }

  pickCondition(value: any): ComboCondition | null {
    const conditions: Array<ComboCondition> = this.props.conditions!;
    return find(
      conditions,
      item => item.test && evalExpression(item.test, value),
    ) as ComboCondition | null;
  }

  handleComboTypeChange(index: number, selection: any) {
    const {multiple, onChange, value, flat, submitOnChange} = this.props;

    const conditions: Array<ComboCondition> = this.props
      .conditions as Array<ComboCondition>;
    const condition = find(conditions, item => item.label === selection.label);

    if (!condition) {
      return;
    }

    if (multiple) {
      const newValue = this.getValueAsArray();
      newValue.splice(index, 1, {
        ...dataMapping(condition.scaffold || {}, newValue[index]),
      });

      // todo 支持 flat
      onChange(newValue, submitOnChange, true);
    } else {
      onChange(
        {
          ...dataMapping(condition.scaffold || {}, value),
        },
        submitOnChange,
        true,
      );
    }
  }

  @autobind
  handleDragChange(e: any) {
    const { onChange, submitOnChange, store } = this.props;
    let value = this.getValueAsArray();

    value.splice(e.newIndex, 0, value.splice(e.oldIndex, 1)[0]);
    this.keys.splice(e.newIndex, 0,  this.keys.splice(e.oldIndex, 1)[0]);

    store.setActiveKey(e.newIndex);

    onChange?.(value, submitOnChange, true);
  }

  @autobind
  async handleTabSelect(key: number) {
    const {store, data, name, value, dispatchEvent} = this.props;
    const eventData = {
      key,
      item: value[key],
    };
    const rendererEvent = await dispatchEvent(
      'tabsChange',
      createObject(
        data,
        name
          ? {
              ...eventData,
              [name]: value,
            }
          : eventData,
      ),
    );

    if (rendererEvent?.prevented) {
      return;
    }

    store.setActiveKey(key);
  }

  @autobind
  setNull(e: React.MouseEvent) {
    e.preventDefault();
    const {onChange} = this.props;
    onChange(null);

    Array.isArray(this.subForms) &&
      this.subForms.forEach(subForm => {
        subForm.clearErrors();
      });
  }

  renderPlaceholder() {
    const {placeholder, translate: __} = this.props;
    return (
      <span className="text-muted">
        {__(placeholder || 'placeholder.noData')}
      </span>
    );
  }

  renderTabsMode() {
    const {
      classPrefix: ns,
      classnames: cx,
      tabsStyle,
      formClassName,
      render,
      disabled,
      store,
      flat,
      subFormMode,
      addButtonText,
      addable,
      copyable,
      removable,
      typeSwitchable,
      itemRemovableOn,
      itemCopyableOn,
      delimiter,
      canAccessSuperData,
      addIcon,
      deleteIcon,
      tabsLabelTpl,
      conditions,
      changeImmediately,
      addBtnText,
      static: isStatic,
      translate: __,
      mountOnEnter,
      unmountOnExit = false,
      staticShowPlaceholder = false,
      placeholder,
      reverseMode,
      disableDropTop = 0,
      disableDropBottom = 0,
      draggable,
      data,
      tabsIconTpl,
      tabsIconPosition,
      tabsCollapseOnExceed,
      tabsCollapseBtnLabel,
    } = this.props;

    const topNumber: number = Number(tokenize(disableDropTop as string, data));
    const bottomNumber: number = Number(tokenize(disableDropBottom as string, data));

    let items = this.props.items;
    let propValue = this.props.value;

    if (flat && typeof propValue === 'string') {
      propValue = propValue.split(delimiter || ',');
    }

    const finnalRemovable =
      store.removable !== false && // minLength ?
      !disabled && // 控件自身是否禁用
      !isStatic && // 是否是静态展示
      removable !== false; // 是否可以删除

    const finnalCopyable =
      store.addable !== false && // maxLength
      !disabled && // 控件自身是否禁用
      !isStatic && // 是否是静态展示
      copyable !== false; // 是否可复制

    if (!Array.isArray(propValue)) {
      propValue = []; // 让 tabs 输出，否则会没有新增按钮。
    }

    // todo 支持拖拽排序。

    // tab模式无数据场景静态模式处理
    if(isStatic && propValue.length === 0) {
      return (<span className="text-muted leading-8">{staticShowPlaceholder ? (__(placeholder) || '-') : '-'}</span>)
    }

    return (
      <CTabs
        addBtnText={__(addBtnText || 'add')}
        className={'ComboTabs'}
        mode={tabsStyle}
        keepChildrenKey={true}
        activeKey={store.activeKey}
        onSelect={this.handleTabSelect}
        reverseMode={reverseMode}
        draggable={draggable}
        onDragChange={this.handleDragChange}
        disableDropTop={topNumber}
        disableDropBottom={bottomNumber}
        collapseOnExceed={tabsCollapseOnExceed}
        collapseBtnLabel={tabsCollapseBtnLabel}
        // combo的tabs模式下，无数据时展示暂无数据的兜底图，样式和table的保持一致
        placeholder={
          !propValue.length && tabsStyle === "strong" ? (
            <div className={cx('Empty', 'Table-placeholder')}>
              <Icon icon="table-empty" className={cx('Empty-icon', 'icon')} />
              暂无数据
            </div>
          ) : null
        }
        customToolButtons={
          !disabled && !isStatic && addable !== false && store.addable ? (
            <div className={cx(`Tabs-link ComboTabs-addLink`,  { 'ComboTabs-addLink-reverse': reverseMode })}>
              {this.renderAddBtn()}
            </div>
          ) : null
        }
      >
        {propValue.map((value: any, index: number) => {
          const data = this.formatValue(value, index);
          let condition: ComboCondition | null | undefined = null;
          let toolbar = (
            <div className={cx('Combo-tab-toolbar')}>
              {
              store.activeKey === index &&
              finnalCopyable &&
              (!itemCopyableOn ||
                evalExpression(itemCopyableOn, {
                  index,
                  __index: index,
                  __items: propValue,
                  ...value,
                }) !== false) && (
                  <div
                    onClick={(e: React.MouseEvent) => {
                      e.stopPropagation();
                      this.copyItem &&
                        this.copyItem(index);
                    }}
                    key="copy"
                    className={cx(
                      `Combo-tab-copyBtn`,
                    )}
                    data-tooltip={__('copy')}
                    data-position="bottom"
                  >
                    <Icon icon="copy" className="icon" />
                  </div>
              )}
              {
                finnalRemovable && // 表达式判断单条是否可删除
                (!itemRemovableOn ||
                  evalExpression(itemRemovableOn, {
                    index,
                    __index: index,
                    __items: propValue,
                    ...value,
                  }) !== false) && (
                    <div
                      onClick={(e: React.MouseEvent) => {
                        e.stopPropagation();
                        this.deleteItem &&
                          this.deleteItem(index);
                      }}
                      key="delete"
                      className={cx(
                        `Combo-tab-delBtn ${!store.removable ? 'is-disabled' : ''}`,
                      )}
                    >
                      {deleteIcon ? (
                        <i className={deleteIcon} />
                      ) : (
                        <Icon icon="status-close" className="icon" />
                      )}
                    </div>
                  )
              }
            </div>
          );

          if (Array.isArray(conditions) && conditions.length) {
            condition = this.pickCondition(data);
            items = condition ? condition.items : undefined;
          }

          let finnalControls =
            flat && items
              ? [
                  {
                    ...(items && items[0]),
                    name: 'flat',
                  },
                ]
              : items;

          const hasUnique =
            Array.isArray(finnalControls) &&
            finnalControls.some((item: any) => item.unique);

          // 处理当前 tab 的 icon：如果组件级别配置了 tabsIconTpl，则基于当前 tab 数据上下文解析
          const tabIcon = tabsIconTpl
            ? filter(
                tabsIconTpl,
                {
                  index,
                  __index: index,
                  __items: propValue,
                  ...data,
                }
              )
            : undefined;

          // 处理 iconPosition，未配置则默认为 'left'
          const tabIconPos = tabsIconPosition
            ? filter(
                tabsIconPosition,
                {
                  index,
                  __index: index,
                  __items: propValue,
                  ...data,
                }
              )
            : undefined;

          // 确定当前tab的unmountOnExit设置：
          // 如果组件级别的unmountOnExit是表达式，则基于当前tab的上下文计算
          // 否则直接使用布尔值
          const tabUnmountOnExit: boolean = typeof unmountOnExit === 'string'
            ? evalExpression(unmountOnExit, {
                index,
                __index: index,
                __items: propValue,
                ...data
              }) as boolean
            : Boolean(unmountOnExit);

          // 确定当前tab的mountOnEnter设置：
          // 如果组件级别的mountOnEnter是表达式，则基于当前tab的上下文计算
          // 否则直接使用布尔值
          const tabMountOnEnter: boolean = typeof mountOnEnter === 'string'
            ? evalExpression(mountOnEnter, {
                index,
                __index: index,
                __items: propValue,
                ...data
              }) as boolean
            : Boolean(mountOnEnter);

          return (
            <Tab
              icon={tabIcon}
              iconPosition={tabIconPos as any}
              title={filter(
                tabsLabelTpl ||
                  __('{{index}}', {index: (data as any).index + 1}),
                data,
              )}
              key={this.keys[index] || (this.keys[index] = guid())}
              toolbar={toolbar}
              eventKey={index}
              // 不能按需渲染，因为 unique 会失效。
              mountOnEnter={!hasUnique && tabMountOnEnter}
              unmountOnExit={tabUnmountOnExit}
              className={
                store.memberValidMap[index] === false ? 'has-error' : ''
              }
              tabClassName={
                store.memberValidMap[index] === false ? 'has-error' : ''
              }
            >
              {condition && typeSwitchable !== false ? (
                <div className={cx('Combo-itemTag')}>
                  <label>{__('Combo.type')}</label>
                  <Select
                    onChange={this.handleComboTypeChange.bind(this, index)}
                    options={(conditions as Array<ComboCondition>).map(
                      item => ({
                        label: item.label,
                        value: item.label,
                      }),
                    )}
                    value={condition.label}
                    clearable={false}
                  />
                </div>
              ) : null}
              <div className={cx(`Combo-itemInner`)}>
                {finnalControls ? (
                  this.renderItems(finnalControls, data, index)
                ) : (
                  <Alert2 level="warning" className="m-b-none">
                    {__('Combo.invalidData')}
                  </Alert2>
                )}
              </div>
            </Tab>
          );
        })}
      </CTabs>
    );
  }

  renderDelBtn(value: any, index: number) {
    if (this.props.static) {
      return null;
    }

    const {
      classPrefix: ns,
      classnames: cx,
      render,
      store,
      deleteIcon,
      translate: __,
      itemRemovableOn,
      disabled,
      removable,
      deleteBtn,
      value: propValue,
    } = this.props;

    const finnalRemovable =
      store.removable !== false && // minLength ?
      !disabled && // 控件自身是否禁用
      removable !== false; // 是否可以删除

    if (
      !(
        finnalRemovable && // 表达式判断单条是否可删除
        (!itemRemovableOn ||
          evalExpression(itemRemovableOn, {
            index,
            __index: index,
            __items: propValue,
            ...value,
          }) !== false)
      )
    ) {
      // 不符合删除条件，则不渲染删除按钮
      return null;
    }

    // deleteBtn是对象，则根据自定义配置渲染按钮
    if (isObject(deleteBtn)) {
      return render('delete-btn', {
        ...deleteBtn,
        type: 'button',
        className: cx(
          'Combo-delController',
          deleteBtn ? deleteBtn.className : '',
        ),
        onClick: (e: any) => {
          if (!deleteBtn.onClick) {
            this.deleteItem(index);
            return;
          }

          let originClickHandler = deleteBtn.onClick;
          if (typeof originClickHandler === 'string') {
            originClickHandler = str2AsyncFunction(
              deleteBtn.onClick,
              'e',
              'index',
              'props',
            );
          }
          const result = originClickHandler(e, index, this.props);
          if (result && result.then) {
            result.then(() => {
              this.deleteItem(index);
            });
          } else {
            this.deleteItem(index);
          }
        },
      });
    }

    // deleteBtn是string，则渲染按钮文本
    if (typeof deleteBtn === 'string') {
      return render('delete-btn', {
        type: 'button',
        className: cx('Combo-delController'),
        label: deleteBtn,
        onClick: this.deleteItem.bind(this, index),
      });
    }

    // 如果上述按钮不满足，则渲染默认按钮
    return (
      <a
        onClick={this.deleteItem.bind(this, index)}
        key="delete"
        className={cx(`Combo-delBtn ${!store.removable ? 'is-disabled' : ''}`)}
        data-tooltip={__('delete')}
        data-position="bottom"
      >
        {deleteIcon ? (
          <i className={deleteIcon} />
        ) : (
          <Icon icon="status-close" className="icon" />
        )}
      </a>
    );
  }

  renderCopyBtn(value: any, index: number) {
    if (this.props.static) {
      return null;
    }

    const {
      classnames: cx,
      render,
      copyIcon,
      translate: __,
      itemCopyableOn,
      disabled,
      copyable,
      copyBtn,
      value: propValue,
    } = this.props;

    const finnalCopyable =
      !disabled && // 控件自身是否禁用
      copyable !== false; // 是否可以复制

    if (
      !(
        finnalCopyable && // 表达式判断单条是否可复制
        (!itemCopyableOn ||
          evalExpression(itemCopyableOn, {
            index,
            __index: index,
            __items: propValue,
            ...value,
          }) !== false)
      )
    ) {
      // 不符合复制条件，则不渲染复制按钮
      return null;
    }

    // copyBtn是对象，则根据自定义配置渲染按钮
    if (isObject(copyBtn)) {
      return render('copy-btn', {
        ...copyBtn,
        type: 'button',
        className: cx('Combo-copyController', copyBtn ? copyBtn.className : ''),
        onClick: (e: any) => {
          if (!copyBtn.onClick) {
            this.copyItem(index);
            return;
          }

          let originClickHandler = copyBtn.onClick;
          if (typeof originClickHandler === 'string') {
            originClickHandler = str2AsyncFunction(
              copyBtn.onClick,
              'e',
              'index',
              'props',
            );
          }
          const result = originClickHandler(e, index, this.props);
          if (result && result.then) {
            result.then(() => {
              this.copyItem(index);
            });
          } else {
            this.copyItem(index);
          }
        },
      });
    }

    // copyBtn是string，则渲染按钮文本
    if (typeof copyBtn === 'string') {
      return render('copy-btn', {
        type: 'button',
        className: cx('Combo-copyController'),
        label: copyBtn,
        onClick: this.copyItem.bind(this, index),
      });
    }

    // 如果上述按钮不满足，则渲染默认按钮
    return (
      <a
        onClick={this.copyItem.bind(this, index)}
        key="copy"
        className={cx(`Combo-copyBtn`)}
        data-tooltip={__('copy')}
        data-position="bottom"
      >
        {copyIcon ? (
          <i className={copyIcon} />
        ) : (
          <Icon icon="copy" className="icon" />
        )}
      </a>
    );
  }

  // 渲染行添加按钮
  renderItemAddBtn(value: any, index: number) {
    if (this.props.static) {
      return null;
    }

    const {
      classnames: cx,
      render,
      itemAddIcon,
      translate: __,
      itemAddVisibleOn,
      disabled,
      itemAddable,
      itemAddBtn,
      value: propValue,
    } = this.props;

    const finnalCopyable =
      !disabled && // 控件自身是否禁用
      itemAddable !== false; // 是否展示行新增

    if (
      !(
        finnalCopyable && // 表达式判断单条是否可新增
        (!itemAddVisibleOn ||
          evalExpression(itemAddVisibleOn, {
            index,
            __index: index,
            __items: propValue,
            ...value,
          }) !== false)
      )
    ) {
      // 不符合行新增，则不渲染行新增按钮
      return null;
    }

    // itemAddBtn是对象，则根据自定义配置渲染按钮
    if (isObject(itemAddBtn)) {
      return render('itemadd-btn', {
        ...itemAddBtn,
        type: 'button',
        className: cx(
          'Combo-itemAddController',
          itemAddBtn ? itemAddBtn.className : '',
        ),
        onClick: (e: any) => {
          if (!itemAddBtn.onClick) {
            this.itemAddItem(index);
            return;
          }

          let originClickHandler = itemAddBtn.onClick;
          if (typeof originClickHandler === 'string') {
            originClickHandler = str2AsyncFunction(
              itemAddBtn.onClick,
              'e',
              'index',
              'props',
            );
          }
          const result = originClickHandler(e, index, this.props);
          if (result && result.then) {
            result.then(() => {
              this.itemAddItem(index);
            });
          } else {
            this.itemAddItem(index);
          }
        },
      });
    }

    // itemAddBtn是string，则渲染按钮文本
    if (typeof itemAddBtn === 'string') {
      return render('itemadd-btn', {
        type: 'button',
        className: cx('Combo-itemAddController'),
        label: itemAddBtn,
        onClick: this.itemAddItem.bind(this, index),
      });
    }

    // 如果上述按钮不满足，则渲染默认按钮
    return (
      <a
        onClick={this.itemAddItem.bind(this, index)}
        key="itemAdd"
        className={cx(`Combo-itemAddBtn`)}
        data-tooltip={__('add')}
        data-position="bottom"
      >
        {itemAddIcon ? (
          <i className={itemAddIcon} />
        ) : (
          <Icon icon="plus" className="icon" />
        )}
      </a>
    );
  }

  renderAddBtn() {
    if (this.props.static) {
      return null;
    }

    const {
      classPrefix: ns,
      classnames: cx,
      render,
      addButtonClassName,
      store,
      addButtonText,
      addBtn,
      addable,
      addIcon,
      conditions,
      translate: __,
      tabsMode,
      alwaysShowAddBtn,
    } = this.props;

    const hasConditions = Array.isArray(conditions) && conditions.length;
    return (
      <>
        {(alwaysShowAddBtn || store.addable) &&
          addable !== false &&
          (hasConditions ? (
            render(
              'add-button',
              {
                type: 'dropdown-button',
                icon: addIcon ? <Icon icon="plus" className="icon" /> : '',
                label: __(addButtonText || 'add'),
                level: 'info',
                size: 'sm',
                closeOnClick: true,
                btnClassName: addButtonClassName,
              },
              {
                buttons: conditions?.map(item => ({
                  label: item.label,
                  onClick: (e: any) => {
                    this.addItem(item.scaffold);
                    return false;
                  },
                })),
              },
            )
          ) : tabsMode ? (
            <a onClick={this.tabsAddItem}>
              {addIcon ? <Icon icon="plus" className="icon" /> : null}
              <span>{__(addButtonText || 'add')}</span>
            </a>
          ) : isObject(addBtn) ? (
            render('add-button', {
              ...addBtn,
              type: 'button',
              onClick: () => this.addItem(),
            })
          ) : (
            <Button
              className={cx(`Combo-addBtn`, addButtonClassName)}
              onClick={() => this.addItem()}
            >
              {addIcon ? <Icon icon="plus" className="icon" /> : null}
              <span>{__(addButtonText || 'add')}</span>
            </Button>
          ))}
      </>
    );
  }

  renderCanNotAddHint() {
    const {classnames: cx, messages} = this.props;
    return (
      this.state.showCanNotAddHint && (
        <div className={cx('Combo-error-hint')}>
          {messages?.addFailed ?? CanNotAddErrMsg}
        </div>
      )
    );
  }

  // 渲染自定义操作按钮
  renderExtraActions(data: any, index: number) {
    const {render, extraActions} = this.props;

    let extraActionList: any[] = Array.isArray(extraActions)
      ? extraActions
      : [extraActions];

    return render(`/combo/extraactions-${index}`, extraActionList, {
      data: extendObject(data, {index, __index: index}),
    });
  }

  renderMultipe() {
    if (this.props.tabsMode) {
      return (
        <>
          {this.renderTabsMode()}
          {this.renderCanNotAddHint()}
        </>
      );
    }

    const {
      classPrefix: ns,
      classnames: cx,
      multiLine,
      disabled,
      flat,
      draggable,
      draggableTip,
      typeSwitchable,
      delimiter,
      dragIcon,
      noBorder,
      conditions,
      placeholder,
      translate: __,
      itemClassName,
      itemsWrapperClassName,
      static: isStatic,
      store,
      addable,
      alwaysShowAddBtn,
      staticShowPlaceholder = false,
      data,
      disableDropTop = 0,
      disableDropBottom = 0,
    } = this.props;

    const topNumber: number = Number(tokenize(disableDropTop as string, data));
    const bottomNumber: number = Number(tokenize(disableDropBottom as string, data));


    const mobileUI = isMobile();
    let items = this.props.items;
    let value = this.props.value;

    if (flat && typeof value === 'string') {
      value = value.split(delimiter || ',');
    }

    return (
      <div
        className={cx(
          `Combo Combo--multi`,
          multiLine ? `Combo--ver` : `Combo--hor`,
          noBorder ? `Combo--noBorder` : '',
          disabled ? 'is-disabled' : '',
          !isStatic &&
            !disabled &&
            draggable &&
            Array.isArray(value) &&
            value.length > 1
            ? 'is-draggable'
            : '',
          {
            'is-mobile': mobileUI
          }
        )}
      >
        <div
          className={cx(
            `Combo-items`,
            itemsWrapperClassName,
            // 不展示新增按钮时，通过该类名去除8px下边距
            (alwaysShowAddBtn || store.addable) && addable !== false
              ? ``
              : `no-addable`,
          )}
        >
          {Array.isArray(value) && value.length ? (
            value.map((value, index, thelist) => {
              let delBtn: any = this.renderDelBtn(value, index);
              let copyBtn: any = this.renderCopyBtn(value, index);
              let itemAddBtn: any = this.renderItemAddBtn(value, index);
              const isDragDisabled = topNumber > index || index >= thelist.length - bottomNumber;

              const data = this.formatValue(value, index);
              const extraActionsDom = this.renderExtraActions(data, index);
              let condition: ComboCondition | null = null;

              if (Array.isArray(conditions) && conditions.length) {
                condition = this.pickCondition(data);
                items = condition ? condition.items : undefined;
              }

              let finnalControls =
                flat && items
                  ? [
                      {
                        ...(items && items[0]),
                        name: 'flat',
                      },
                    ]
                  : items;

              return (
                <div
                  className={cx(
                    `Combo-item`,
                    itemClassName,
                    store.memberValidMap[index] === false ? 'has-error' : '',
                  )}
                  key={this.keys[index] || (this.keys[index] = guid())}
                >
                  {!isStatic && !disabled && draggable && thelist.length > 1 ? (
                    <div
                      className={cx('Combo-itemDrager', isDragDisabled ? 'is-dragDisabled' : '',)}
                    >
                      <a
                        key="drag"
                        data-tooltip={__('Combo.dragDropSort')}
                        data-position="bottom"
                      >
                        {dragIcon ? (
                          <i className={dragIcon} />
                        ) : (
                          <Icon icon="drag-bar" className="icon" />
                        )}
                      </a>
                    </div>
                  ) : null}
                  {condition && typeSwitchable !== false ? (
                    <div className={cx('Combo-itemTag')}>
                      <label>{__('Combo.type')}</label>
                      <Select
                        onChange={this.handleComboTypeChange.bind(this, index)}
                        options={(conditions as Array<ComboCondition>).map(
                          item => ({
                            label: item.label,
                            value: item.label,
                          }),
                        )}
                        value={condition.label}
                        clearable={false}
                      />
                    </div>
                  ) : null}
                  <div className={cx(`Combo-itemInner`)}>
                    {finnalControls ? (
                      this.renderItems(finnalControls, data, index)
                    ) : (
                      <Alert2 level="warning" className="m-b-none">
                        {__('Combo.invalidData')}
                      </Alert2>
                    )}
                  </div>
                  <div className={cx('Combo-operation')}>
                    {extraActionsDom}
                    {itemAddBtn}
                    {copyBtn}
                    {delBtn}
                  </div>
                </div>
              );
            })
          ) : placeholder ? (
            <div className={cx(`Combo-placeholder`)}>{isStatic && !staticShowPlaceholder ? '-' :  __(placeholder)}</div>
          ) : null}
        </div>

        {!isStatic && !disabled ? (
          <>
            <div className={cx(`Combo-toolbar`)}>
              {this.renderAddBtn()}
              {draggable ? (
                <span className={cx(`Combo-dragableTip`)} ref={this.dragTipRef}>
                  {Array.isArray(value) && value.length > 1
                    ? __(draggableTip)
                    : ''}
                </span>
              ) : null}
            </div>
            {this.renderCanNotAddHint()}
          </>
        ) : null}
      </div>
    );
  }

  renderSingle() {
    const {
      conditions,
      classnames: cx,
      value,
      multiLine,
      noBorder,
      disabled,
      typeSwitchable,
      nullable,
      translate: __,
      itemClassName,
    } = this.props;

    const mobileUI = isMobile();
    let items = this.props.items;
    const data = isObject(value) ? this.formatValue(value) : this.defaultValue;
    let condition: ComboCondition | null = null;

    if (Array.isArray(conditions) && conditions.length) {
      condition = this.pickCondition(data);
      items = condition ? condition.items : undefined;
    }

    return (
      <div
        className={cx(
          `Combo Combo--single`,
          multiLine ? `Combo--ver` : `Combo--hor`,
          noBorder ? `Combo--noBorder` : '',
          disabled ? 'is-disabled' : '',
          {
            'is-mobile': mobileUI
          }
        )}
      >
        <div className={cx(`Combo-item`, itemClassName)}>
          {condition && typeSwitchable !== false ? (
            <div className={cx('Combo-itemTag')}>
              <label>{__('Combo.type')}</label>
              <Select
                onChange={this.handleComboTypeChange.bind(this, 0)}
                options={(conditions as Array<ComboCondition>).map(item => ({
                  label: item.label,
                  value: item.label,
                }))}
                value={condition.label}
                clearable={false}
              />
            </div>
          ) : null}

          <div className={cx(`Combo-itemInner`)}>
            {items ? (
              this.renderItems(items, data)
            ) : (
              <Alert2 level="warning" className="m-b-none">
                {__('Combo.invalidData')}
              </Alert2>
            )}
          </div>
        </div>
        {value && nullable ? (
          <a className={cx('Combo-setNullBtn')} href="#" onClick={this.setNull}>
            {__('clear')}
          </a>
        ) : null}
      </div>
    );
  }

  // 为了给 editor 重写使用
  renderItems(finnalControls: ComboSubControl[], data: object, index?: number) {
    const {
      classnames: cx,
      formClassName,
      render,
      multiLine,
      name,
      disabled,
      canAccessSuperData,
      multiple,
      tabsMode,
      subFormMode,
      subFormHorizontal,
      changeImmediately,
      lazyLoad,
      translate: __,
      static: isStatic,
      updatePristineAfterStoreDataReInit
    } = this.props;

    // 单个
    if (!multiple) {
      return render(
        'single',
        {
          type: 'form',
          name: `${name}-form-0`,
          body: finnalControls,
          wrapperComponent: 'div',
          wrapWithPanel: false,
          mode: multiLine ? subFormMode || 'normal' : 'row',
          horizontal: subFormHorizontal,
          className: cx(`Combo-form`, formClassName),
        },
        {
          index: 0,
          disabled: disabled,
          static: isStatic,
          data,
          onChange: this.handleSingleFormChange,
          onValidChange: this.handleSubFormValid,
          ref: this.makeFormRef(0),
          onInit: this.handleSingleFormInit,
          canAccessSuperData,
          lazyChange: changeImmediately ? false : true,
          formLazyChange: false,
          formStore: undefined,
          updatePristineAfterStoreDataReInit:
            updatePristineAfterStoreDataReInit ?? false
        },
      );
    } else if (multiple && index !== undefined && index >= 0) {
      return render(
        `multiple/${index}`,
        {
          type: 'form',
          name: `${name}-form-${index}`,
          body: finnalControls,
          wrapperComponent: 'div',
          wrapWithPanel: false,
          mode: tabsMode ? subFormMode : multiLine ? subFormMode : 'row',
          horizontal: subFormHorizontal,
          className: cx(`Combo-form`, formClassName),
        },
        {
          index,
          disabled,
          static: isStatic,
          data,
          onChange: this.handleChange,
          onValidChange: this.handleSubFormValid,
          onInit: this.handleFormInit,
          onAction: this.handleAction,
          ref: this.makeFormRef(index),
          canAccessSuperData,
          lazyChange: changeImmediately ? false : true,
          formLazyChange: false,
          value: undefined,
          formItemValue: undefined,
          formStore: undefined,
          ...(tabsMode ? {} : {lazyLoad}),
          updatePristineAfterStoreDataReInit:
            updatePristineAfterStoreDataReInit ?? false
        },
      );
    }
    return <></>;
  }

  // 新增弹窗 - 打开。如果配置addFormDialog，需要先打开弹窗
  open() {
    this.setState({
      dialogCtx: {
        mode: 'add',
      },
    });
  }

  // 新增弹窗 - 关闭
  @autobind
  close() {
    this.setState({
      dialogCtx: undefined,
    });
  }

  // 新增弹窗 - 确认
  @autobind
  handleDialogConfirm(values: Array<object>) {
    const {onChange, store, addattop, joinValues, flat, delimiter} = this.props;
    const {dialogCtx} = this.state;
    // 弹窗收集到的表单值
    const formValue = values[0];

    if (dialogCtx?.mode === 'add') {
      this.addItem(formValue);
    }

    this.close();
  }

  // 新增弹窗schema
  buildDialogSchema() {
    let {addFormDialog} = this.props;

    const dialogProps = [
      'title',
      'actions',
      'name',
      'size',
      'closeOnEsc',
      'closeOnOutside',
      'showErrorMsg',
      'showCloseButton',
      'bodyClassName',
      'type',
    ];

    return {
      showCloseButton: false,
      type: 'dialog',
      ...pick(addFormDialog, dialogProps),
      body: {
        type: 'form',
        ...omit(addFormDialog, dialogProps),
      },
    };
  }

  // 渲染新增弹窗schema
  renderAddFormDialog() {
    const {render} = this.props;
    const {dialogCtx} = this.state;

    if (!dialogCtx) {
      return null;
    }

    return render(`modal`, this.buildDialogSchema(), {
      show: !!dialogCtx,
      onClose: this.close,
      onConfirm: this.handleDialogConfirm,
      formStore: undefined,
    });
  }

  renderStatic(displayValue = '-') {
    // 如有 staticSchema 会被拦截渲染schema, 不会走到这里
    return this.props.render(
      'static-input-kv',
      {
        type: 'json',
      },
      this.props,
    );
  }

  render() {
    const {
      type,
      formInited,
      multiple,
      className,
      style,
      classPrefix: ns,
      classnames: cx,
      static: isStatic,
      staticSchema,
    } = this.props;

    // 静态展示时
    // 当有staticSchema 或 type = input-kv | input-kvs
    // 才拦截处理，其他情况交给子表单项处理即可
    if (
      isStatic &&
      (staticSchema || ['input-kv', 'input-kvs'].includes(type))
    ) {
      return this.renderStatic();
    }

    return formInited || typeof formInited === 'undefined' ? (
      <div className={cx(`ComboControl`, className)}>
        {multiple ? this.renderMultipe() : this.renderSingle()}
        {this.renderAddFormDialog()}
      </div>
    ) : null;
  }
}

@FormItem({
  type: 'combo',
  storeType: ComboStore.name,
  extendsData: false,
  isMultiple: (props) => {
    return props.multiple;
  },
  isFlat: (props) => {
    return props.flat;
  },
})
export class ComboControlRenderer extends ComboControl {
  // 支持更新指定索引的值
  setData(value: any, replace?: boolean, index?: number) {
    const {multiple, onChange, submitOnChange} = this.props;
    if (multiple) {
      if (index !== undefined && ~index) {
        let newValue = [...this.getValueAsArray()];
        newValue.splice(index, 1, {...newValue[index], ...value});
        onChange?.(newValue, submitOnChange, true);
      } else {
        onChange?.(value, submitOnChange, true);
      }
    } else {
      onChange?.(value, submitOnChange, true);
    }
  }
}

@FormItem({
  type: 'input-kv',
  storeType: ComboStore.name,
  extendsData: false,
})
export class KVControlRenderer extends ComboControl {}

@FormItem({
  type: 'input-kvs',
  storeType: ComboStore.name,
  extendsData: false,
})
export class KVSControlRenderer extends ComboControl {}
