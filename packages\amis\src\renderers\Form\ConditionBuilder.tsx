import React from 'react';
import {
  FormItem,
  FormControlProps,
  FormBaseControl,
  Schema,
  isPureVariable,
  resolveVariableAndFilter,
  ICondiOpt,
  resolveEventData,
  evalExpression
} from 'amis-core';

import {
  FormBaseControlSchema,
  SchemaApi,
  SchemaTokenizeableString,
} from '../../Schema';

import {autobind} from 'amis-core';
import type {
  ConditionGroupValue,
  ConditionRule,
  SchemaExpression,
} from 'amis-core';
import {
  ConditionBuilderFields,
  ConditionBuilderFuncs,
  ConditionBuilderConfig,
  withRemoteConfig,
  RemoteOptionsProps,
  ConditionBuilder,
} from 'amis-ui';

import {IconSchema} from '../Icon';

import {
  anyChanged,
} from 'amis-core'

/**
 * 条件组合控件
 * 文档：https://baidu.gitee.io/amis/docs/components/form/condition-builder
 */
export interface ConditionBuilderControlSchema extends FormBaseControlSchema {
  /**
   * 指定为
   */
  type: 'condition-builder';

  /**
   * 内嵌模式，默认为 true
   */
  embed?: boolean;

  /**
   * 非内嵌模式时 弹窗触发icon
   */
  pickerIcon?: IconSchema;

  /**
   * 函数集合
   */
  funcs?: ConditionBuilderFuncs;

  /**
   * 字段集合
   */
  fields: ConditionBuilderFields;

  /**
   * 其他配置
   */
  config?: ConditionBuilderConfig;

  /**
   * 通过远程拉取配置项
   */
  source?: SchemaApi | SchemaTokenizeableString;

  /**
   * 展现模式
   */
  builderMode?: 'simple' | 'full';

  /**
   * 是否显示并或切换键按钮，只在简单模式下有用
   */
  showANDOR?: boolean;

  /**
   * 是否显示头部配置
   */
  showHeader?: boolean;

  /**
   * 自定义条件选项
   */
  conditionItemBody?: Schema;

  /**
   * 根条件的自定义条件选项
   */
  rootCondiOptions?: Array<ICondiOpt>;

  /**
   * 叶子条件的自定义条件选项
   */
  leafCondiOptions?: Array<ICondiOpt>;
  /**
   * 一个条件组合里最大条件节点数量
   */
  maxLevel?: number | SchemaExpression;
  /**
   * 一个条件组合里最小条件节点数量
   */
  minLevel?: number | SchemaExpression;
  /** 手动改变条件连接符 */
  onConjunctionChange?: (
    conjunction: ICondiOpt,
    changedItem: ConditionRule | ConditionGroupValue,
    value: ConditionGroupValue,
  ) => void;
  /** 新增条件或条件组 */
  onAdd?: (
    addedItem: ConditionRule | ConditionGroupValue,
    value: ConditionGroupValue,
  ) => void;
  /**
   * 是否可拖拽，默认为 true
   */
  draggable?: boolean;
  toolbarMode?: 'vertical'|'horizontal',
  removeable?: boolean; // 控制删除组
  copyable?: boolean; // 控制复制组
  addable?: boolean; // 控制添加条件组
  itemAddable?: boolean; // 控制添加条件项
  itemRemoveable?: boolean;// 控制删除条件项
  itemCopyable?: boolean; // 控制复制条件项
  removeableOn?: string;
  copyableOn?: string;
  addableOn?: string;
  itemAddableOn?: string;
  itemRemoveableOn?: string;
  itemCopyableOn?: string;
  verticalLineStyle?: boolean; // 横版使用竖版样式风格
}

export interface ConditionBuilderProps
  extends FormControlProps,
    Omit<
      ConditionBuilderControlSchema,
      'type' | 'className' | 'descriptionClassName' | 'inputClassName'
    > {}

export default class ConditionBuilderControl extends React.Component<ConditionBuilderProps> {
  static defaultProps = {
    draggable: true
  };

  handleConjunctionChange = async (
    conjunction: ICondiOpt,
    changedItem: ConditionRule | ConditionGroupValue,
    value: ConditionGroupValue,
  ) => {
    const {dispatchEvent, onConjunctionChange} = this.props;
    const rendererEvent = await dispatchEvent(
      'conjunctionChange',
      resolveEventData(this.props, {conjunction, changedItem, value}, 'value'),
    );

    if (!rendererEvent?.prevented) {
      onConjunctionChange?.(conjunction, changedItem, value);
    }
  };

  handleAdd = async (
    addedItem: ConditionRule | ConditionGroupValue,
    value: ConditionGroupValue,
  ) => {
    const {dispatchEvent, onAdd} = this.props;
    const rendererEvent = await dispatchEvent(
      'add',
      resolveEventData(this.props, {addedItem, value}, 'value'),
    );

    if (!rendererEvent?.prevented) {
      onAdd?.(addedItem, value);
    }
  };

  @autobind
  renderEtrValue(schema: Schema, data: any) {
    return this.props.render(
      'inline',
      Object.assign(schema, {label: false}),
      data,
    );
  }

  renderPickerIcon() {
    const {render, pickerIcon} = this.props;
    return pickerIcon ? render('picker-icon', pickerIcon) : undefined;
  }

  render() {
    const {className, classnames: cx, style, pickerIcon, ...rest} = this.props;

    // 处理一下formula类型值的变量列表
    let formula = this.props.formula ? {...this.props.formula} : undefined;
    if (formula && formula.variables && isPureVariable(formula.variables)) {
      // 如果 variables 是 ${xxx} 这种形式，将其处理成实际的值
      formula.variables = resolveVariableAndFilter(
        formula.variables,
        this.props.data,
        '| raw',
      );
    }

    return (
      <div className={cx(`ConditionBuilderControl`, {
        'is-static': rest.static,
      }, className)}>
        <ConditionBuilderWithRemoteOptions
          renderEtrValue={this.renderEtrValue}
          pickerIcon={this.renderPickerIcon()}
          {...rest}
          formula={formula}
          onConjunctionChange={this.handleConjunctionChange}
          onAdd={this.handleAdd}
        />
      </div>
    );
  }
}

const ConditionBuilderWithRemoteOptions = withRemoteConfig({
  adaptor: data => data.fields || data,
})(
  class extends React.Component<
    RemoteOptionsProps & React.ComponentProps<typeof ConditionBuilder> & ConditionBuilderProps
  > {
    getLevelValue = (levelValue: any) => {
      if (!levelValue) {
        return undefined;
      }
      const v = Number(levelValue);
      return Number.isNaN(v) ? undefined : v;
    };

    // 解析表达式，用于在amis-ui层去解析表达式
    resolveExpression = (type: string, data: any) => {
      const expression = this.props[`${type}On`];
      if(typeof expression !== 'string') {
        return undefined;
      }
      return evalExpression(expression, data);
    }

    render() {
      const {
        loading,
        config,
        deferLoad,
        conditionItemBody,
        maxLevel,
        minLevel,
        data,
        disabled,
        renderEtrValue,
        toolbarMode,
        ...rest
      } = this.props;
      const maxLevelValue = this.getLevelValue(
        isPureVariable(maxLevel)
          ? resolveVariableAndFilter(maxLevel, data, '| raw')
          : maxLevel,
      );
      const minLevelValue = this.getLevelValue(
        isPureVariable(minLevel)
          ? resolveVariableAndFilter(minLevel, data, '| raw')
          : minLevel,
      );

      return (
        <React.Fragment>
          <ConditionBuilder
            {...rest}
            fields={config || rest.fields || []}
            disabled={disabled || loading}
            renderEtrValue={renderEtrValue}
            conditionItemBody={conditionItemBody}
            maxLevel={maxLevelValue}
            minLevel={minLevelValue}
            toolbarMode={toolbarMode}
            data={data}
            resolveExpression={this.resolveExpression}
          />
        </React.Fragment>
      );
    }
  },
);

@FormItem({
  type: 'condition-builder',
  strictMode: false,
})
export class ConditionBuilderRenderer extends ConditionBuilderControl {
  shouldComponentUpdate(nextProps: Readonly<ConditionBuilderProps>, nextState: Readonly<{}>, nextContext: any): boolean {
    /*
      formInited是form表单初始化标识状态，conditionbuilder不需要用它做逻辑
      formItemDispatchEvent是function，统一直接下发dispatch
      $schema是schema配置，引用发生了变化，但是值未发生变化
    */
    const blackList = ['formInited', '$schema'];
    const keys = Object.keys(nextProps).filter(key => !blackList.includes(key));
    if (anyChanged(keys, this.props, nextProps)) {
     return true;
    }

    return false;
  }
}
