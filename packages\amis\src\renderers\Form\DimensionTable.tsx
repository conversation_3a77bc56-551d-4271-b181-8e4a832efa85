import {
  ActionObject,
  autobind,
  buildStyle,
  createObject,
  evalExpression,
  filter,
  FormControlProps,
  FormItem,
  Schema,
  tokenize,
} from 'amis-core';
import {DimensionTable} from 'amis-ui';
import React from 'react';
import isPlainObject from 'lodash/isPlainObject';
import type { DimensionTd } from 'amis-ui/lib/components/dimension-table/TableCell';
import {SchemaCollection, SchemaTpl, FormBaseControlSchema} from '../../Schema';
import {supportStatic} from './StaticHoc';

interface ControlAble {
  copyable: boolean
  addable: boolean
  editable: boolean
  removable: boolean
  mergeable: boolean
  copyableOn: string
  addableOn: string
  editableOn: string
  removableOn: string
  mergeableOn: string
}

interface TableArea extends ControlAble {
  body: SchemaCollection
  style: object
  editDialog: {
    body: SchemaCollection
    [key: string]: any
  }
  rules: Array<{
    rule: string;
    message?: string;
  }>;
}

export interface DimensionTableSchema extends FormBaseControlSchema, ControlAble {
  type: 'dimension-table'
  // 表达式，追踪数据变化全量更新表格
  trackExpression: string
  /**
   * 表格模式
   * basic: 一维表格
   * standard: 标准多维度表格
   * merge: 可合并多维表格
   */
  tableMode: 'basic' | 'standard' | 'merge';
  // 标题区域
  title: TableArea;
  // 行头区域
  rowHeader: TableArea;
  // 列头区域
  columnHeader: TableArea;
  // 数据格区域
  cell: TableArea;
  /**
   * 静态模式下，空数据占位符
   */
  emptyHolder?: string | SchemaTpl;
  // hover 模式
  hoverMode: 'row' | 'cross';
}

export interface DimensionTableProps extends
  FormControlProps,
  Omit<DimensionTableSchema,
    'type' | 'className' | 'descriptionClassName' | 'inputClassName'
  > {
  //
}

export interface DimensionTableState {
  //
}

interface RenderTdOptions {
  rowIndex: number;
  columnIndex: number;
  td: DimensionTd;
  toolbarActions: any[];
  onTdDataChange: any
  getToolbarActions: (options: any) => any[]
}

export default class DimensionTableControl extends React.Component<
  DimensionTableProps,
  DimensionTableState
> {
  static defaultProps: Partial<DimensionTableProps> = {
    tableMode: 'basic',
    hoverMode: 'row',
  };

  state: DimensionTableState;
  control: any;

  constructor(props: DimensionTableProps) {
    super(props);
  }

  doAction(action: ActionObject, data: object, throwErrors: boolean) {
    const {resetValue, onChange} = this.props;
    const actionType = action?.actionType as string;

    if (actionType === 'clear') {
      onChange?.('');
    } else if (actionType === 'reset') {
      onChange?.(resetValue ?? '');
    }
  }

  async validate() {
    const { value } = this.props
    const isPass = this.control.validate(value)
    // 未通过时设置 空白子符串：不展示 formItem 底部文案
    return isPass ? '' : ' '
  }

  @autobind
  controlRef(ref: any) {
    while (ref?.getWrappedInstance) {
      ref = ref.getWrappedInstance();
    }
    this.control = ref;
  }

  @autobind
  getTdRenderData(
    options: {
      td: DimensionTd
      rowIndex: number
      columnIndex: number
      value?: any
    }
  ) {
    const { name } = this.props
    const { td, rowIndex, columnIndex, value } = options
    const { data: tdData, ...restTd } = td

    const renderData: any = {
      ...tdData,
      __td: {
        rowIndex,
        columnIndex,
        ...restTd,
      },
    }

    /**
     * 表单项 name 与 value 都存在时，将 name 放到数据域，防止td在解析的时候，拿不到 table 最新的数据。（目前只有 rule校验时，传入了最新value， 此处需要优化为，每次都是最新的value）
     * 原因：表格是先渲染之后，再将value同步到form，因此渲染时拿到的时候上一次同步的value。
     * 注意：当tdData的表单项中的name 与 table的name 相同时，会存在覆盖问题
     */
    if (name && value) {
      renderData[name] = value
    }

    return createObject(this.props.data, renderData)
  }

  // 校验单个格子
  @autobind
  handleValidateTd(
    options: {
      td: DimensionTd
      rowIndex: number
      columnIndex: number
      value: any // table全量数据
    }
  ) {
    const { td, rowIndex, columnIndex, value } = options
    const config = this.getTdConfig(td)
    const { rules = [] } = config

    let errMsg = ''
    if (Array.isArray(rules) && rules.length) {
      const tdRenderData = this.getTdRenderData({
        td,
        rowIndex,
        columnIndex,
        value
      })
      rules.some((item) => {
          const validateRes = filter(item.rule, tdRenderData)
          // 返回为 'false' 或 ''  表示校验成功。
          const isError = !['false',''].includes(validateRes)

          // 校验返回为 'true' 时，使用 item.message 提示，其他情况 使用返回的字符串提示
          errMsg = isError
            ? validateRes === 'true'
              ? item.message || '校验出错'
              : validateRes
            : ''

          return isError
        }
      );
    }

    return errMsg
  }

  @autobind
  handleChangeValue(values: any) {
    const {onChange} = this.props;
    onChange(values);
  }

  @autobind
  getTdConfig(td: DimensionTd) {
    const { title, columnHeader, rowHeader, cell } = this.props

    let config: any = {}

    if (td.isTitle) {
      config = title || {}
    } else if (td.isColumnHeader) {
      config = columnHeader || {}
    } else if (td.isRowHeader) {
      config = rowHeader || {}
    } else if (td.isCell) {
      config = cell || {}
    }

    return config as TableArea
  }

  @autobind
  renderTableTd(options: RenderTdOptions) {
    const {
      rowIndex,
      columnIndex,
      td = {},
    } = options;

    const {
      render,
      classnames: cx,
    } = this.props;

    const config = this.getTdConfig(td)
    const tdRendererData = this.getTdRenderData({
      td,
      rowIndex,
      columnIndex
    })

    return (
      <div
        style={
          config.style
            ? buildStyle(config.style, tdRendererData)
            : undefined
        }
      >
        {
          render(`tableCell/${rowIndex}_${columnIndex}`, config.body, {
            data: tdRendererData,
          })
        }
        {td.errMsg && <div className={cx('Form-feedback')}>{td.errMsg}</div>}
      </div>
    )
  }

  /**
   * 判断 是否具有某个操作功能，默认都开启
   */
  isEnable(config: any, action: string, data: any) {
    const { tableMode } = this.props
    const enable = config?.[action] ?? this.props[action]
    const enableOn = config?.[`${action}On`] ?? this.props[`${action}On`]

    let result = true

    // 非 合并模式下，mergeable，强制 设置为 false 不可合并
    if (tableMode !== 'merge' && action === 'mergeable') {
      result = false
    } else {
      // 其他情况下，默认为 true
      result = typeof enable === 'boolean'
        ? enable
        : enableOn
          ? evalExpression(config?.[`${action}On`], data)
          : true
    }

    return result
  }

  @autobind
  buildTdEditDialog(options: {
    editDialog: Partial<Schema>,
    onTdDataChange: (value: any) => void
  }) {
    const { standardMode } = this.props
    const { onTdDataChange, editDialog = {}  } = options
    const { body: dialogBody = {}, ...resetDialog } = editDialog

    // form 提交成功事件（处理修改单元格数据）
    const submitSucc = {
      actions: [
        {
          actionType: 'custom',
          script: (_: any, __: any, event: any) => {
            onTdDataChange({
              values: event.data,
            });
          },
        },
      ],
    }

    // 弹窗内 form 表单配置
    let dialogFormConfig = {
      type: 'form',
      standardMode,
      body: dialogBody,
      onEvent: {
        submitSucc
      },
    }

    // 针对 form 复写
    if (isPlainObject(dialogBody) && dialogBody.type === 'form') {
      const userSubmitSucc = dialogBody.onEvent?.submitSucc
      dialogFormConfig = {
        ...dialogBody,
        onEvent: {
          ...dialogBody.onEvent,
          submitSucc: userSubmitSucc
            ? {
              ...userSubmitSucc,
              actions: submitSucc.actions.concat(userSubmitSucc.actions || [])
            }
            : submitSucc,
        }
      }
    }

    // 编辑弹窗配置
    const dialogConfig = {
      title: '编辑',
      actions: [
        {
          type: 'button',
          actionType: 'close',
          label: '取消',
        },
        {
          type: 'button',
          actionType: 'submit',
          level: 'primary',
          label: '确认',
        },
      ],
      ...resetDialog,
      body: dialogFormConfig,
    }

    return dialogConfig
  }

  @autobind
  renderTableTdToolbar(options: RenderTdOptions) {
    const {
      onTdDataChange,
      td,
      rowIndex,
      columnIndex,
      getToolbarActions
    } = options;

    const {
      render,
    } = this.props;

    const config = this.getTdConfig(td)

    const tdProps = {
      data: this.getTdRenderData({
        td,
        rowIndex,
        columnIndex
      }),
    }

    const enableControl = {
      copyable: this.isEnable(config, 'copyable', tdProps.data),
      addable: this.isEnable(config, 'addable', tdProps.data),
      editable: this.isEnable(config, 'editable', tdProps.data),
      removable: this.isEnable(config, 'removable', tdProps.data),
      mergeable: this.isEnable(config, 'mergeable', tdProps.data),
    }

    // 获取操作按钮数组
    const toolbarActions = getToolbarActions({ enableControl })
    // 不存在任何操作，不显示
    if (!toolbarActions?.length) {
      return null
    }

    const buttons = toolbarActions.map(action => {
      const { actionKey, onAction, label } = action
      // 编辑按钮，进行复写
      if (actionKey === 'edit') {
        return {
          onAction,
          label,
          type: 'button',
          actionType: 'dialog',
          dialog: this.buildTdEditDialog({
            onTdDataChange,
            editDialog: config.editDialog || {}
          }),
        };
      }

      return {
        type: 'button',
        label,
        onAction
      };
    });

    return render('toolbar/${rowIndex}_${columnIndex}', {
      type: 'dropdown-button',
      icon: 'fa fa-ellipsis-h',
      iconOnly: true,
      level: 'link',
      closeOnClick: true,
      hideCaret: true,
      trigger: 'hover',
      /**
       * TODO: popOverContainerSelector 配置为body时，此处存在性能问题.(显示dropdown菜单 耗时30ms)
       * 问题大致位置：packages/amis-core/src/components/Overlay.tsx updatePosition
       */
      popOverContainerSelector: 'body',
      buttons:  buttons,
    }, tdProps)
  }

  renderInput() {
    const {
      value,
      data,
      onChange,
      classnames,
      trackExpression,
      tableMode,
      rowHeader,
      columnHeader,
      cell,
      static: isStatic,
      disabled: isDisabled,
      hoverMode,
    } = this.props;

    const mergeable = {
      rowHeader: this.isEnable(rowHeader, 'mergeable', data),
      columnHeader: this.isEnable(columnHeader, 'mergeable', data),
      cell: this.isEnable(cell, 'mergeable', data),
    }

    const trackData = typeof trackExpression === 'string'
      ? tokenize(trackExpression, data)
      : undefined

    return (
      <DimensionTable
        ref={this.controlRef}
        value={value}
        tableMode={tableMode}
        hoverMode={hoverMode}
        trackData={trackData}
        readOnly={isStatic || isDisabled}
        classnames={classnames}
        mergeable={mergeable}
        onChange={onChange}
        onValidateTd={this.handleValidateTd}
        renderTableTd={this.renderTableTd}
        renderTableTdToolbar={this.renderTableTdToolbar}
      />
    )
  }

  renderStatic() {
    const {
      className,
      value,
      classnames: cx,
      emptyHolder,
      render
    } = this.props;

    return (
      <div className={cx(`DimensionTable--static`, className || '')}>
        {
          value
          ?  this.renderInput()
          : (
            render('placeholder', {
              className: cx(`DimensionTable-placeholder`),
              type: 'table',
              placeholder: emptyHolder
            })
          )
        }
      </div>
    );
  }

  @supportStatic()
  render() {
    const { classnames: cx, className } = this.props

    return (
      <div className={cx('DimensionTable-control', className || '')}>
        {this.renderInput()}
      </div>
    );
  }
}

@FormItem({
  type: 'dimension-table',
})
export class DimensionTableRenderer extends DimensionTableControl {}
