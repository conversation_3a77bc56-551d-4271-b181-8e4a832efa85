import React from 'react';
import isEqual from 'lodash/isEqual';
import {FormItem, resolveEventData, ActionObject} from 'amis-core';
import {DsDatePicker, DsDateRangePicker} from 'dataseed-ui';
import {supportStatic} from './StaticHoc';

class AmisDsBaseDatePicker extends React.Component {
  constructor(props: any) {
    super(props);

    this.state = {
      value: this.getValue(this.props)
    };
  }

  getValue = (props: any) => {
    return props?.value === '' ? null : props?.value;
  };

  // setValue
  setData = (value: string | [string, string]) => {
    this.setState({
      value,
    });
  };

  componentDidUpdate(prevProps: any) {
    const prevValue = this.getValue(prevProps);
    const value = this.getValue(this.props);
    if (!isEqual(prevValue, value)) {
      this.setState({
        value,
      });
    }
  }

  // static模式渲染内容
  renderStatic(staticText: any) {
    // console.log('args staticText', staticText)
    if (Array.isArray(staticText)) {
      return staticText.join(' ~ ');
    }
    return staticText;
  }

  // 动作
  doAction = (action: ActionObject, data: object, throwErrors: boolean) => {
    const {resetValue, onChange} = this.props;
    switch (action.actionType) {
      case 'clear':
        this.setState({
          value: null,
        });
        onChange?.('');
        break;
      case 'reset':
        this.setState({
          value: resetValue,
        });
        onChange?.(resetValue ?? '');
        break;
    }
  };

  dispatchEvent = (e: React.SyntheticEvent<HTMLElement>) => {
    const {dispatchEvent, value} = this.props;
    dispatchEvent(e, resolveEventData(this.props, {value}));
  };
  handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    this.props.onFocus?.(e);
  };

  handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    this.props.onBlur?.(e);
  };
  handleData = async (dates, fnName: string, eventName: string, extraData?: object = null) => {
    const {dispatchEvent, data} = this.props;
    const fn: Function = this.props[fnName];
    const rendererEvent = await dispatchEvent(
      eventName,
      resolveEventData(this.props, {value: dates, ...extraData}),
    );
    if (fnName === 'onChange') {
      this.setState({
        value: dates,
      });
    }
    if (rendererEvent?.prevented) {
      return;
    }
    fn && fn(dates);
  };
  handleChange = async (dates, extraData?) => {
    await this.handleData(dates, 'onChange', 'change', extraData);
  };
  handleOk = async dates => {
    await this.handleData(dates, 'onOk', 'ok');
  };
  handlePanelChange = async dates => {
    await this.handleData(dates, 'onPanelChange', 'panelChange');
  };
  handleOpenChange = async (bool: boolean) => {
    const {dispatchEvent, value} = this.props;
    await dispatchEvent(
      'openChange',
      resolveEventData(this.props, {value: bool}),
    );
  };
}

@FormItem({
  type: 'ds-date-picker',
  autoVar: true,
  detectProps: [
    'presetValue',
    'dynamicRange',
  ],
})
export class AmisDsDatePicker extends AmisDsBaseDatePicker {
  @supportStatic()
  render() {
    const {
      className,
      classnames: cx,
    } = this.props;
    return (
      <div className={cx(
        `DateControl`,
        className
      )}>
        <DsDatePicker
          {...this.props}
          value={this.state.value}
          onChange={this.handleChange}
          onPanelChange={this.handlePanelChange}
          onOpenChange={this.handleOpenChange}
          onFocus={this.dispatchEvent}
          onBlur={this.dispatchEvent}
        />
      </div>
    );
  }
}

@FormItem({
  type: 'ds-date-range-picker',
  autoVar: true,
  detectProps: [
    'presetValue',
    'dynamicRange',
  ]
})
export class AmisDsDateRangePicker extends AmisDsBaseDatePicker {
  handleCalendarChange = async dates => {
    await this.handleData(dates, 'onCalendarChange', 'calendarChange');
  };
  @supportStatic()
  render() {
    const {
      className,
      classnames: cx
    } = this.props;
    return (
      <div
        className={cx(
          `DateRangeControl`,
          className
        )}
      >
        <DsDateRangePicker
          {...this.props}
          value={this.state.value}
          onChange={this.handleChange}
          onPanelChange={this.handlePanelChange}
          onOpenChange={this.handleOpenChange}
          onCalendarChange={this.handleCalendarChange}
          onFocus={this.dispatchEvent}
          onBlur={this.dispatchEvent}
        />
      </div>
    );
  }
}
