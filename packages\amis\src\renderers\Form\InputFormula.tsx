import React from 'react';
import {FormItem, FormControlProps, resolveEventData} from 'amis-core';
import {FormulaPicker, FormulaCodeEditor, TooltipWrapper} from 'amis-ui';
import {autobind, SchemaNode} from 'amis-core';

import type {BaseSchema, FormBaseControlSchema, SchemaIcon} from '../../Schema';
import {createObject, isPureVariable, resolveVariableAndFilter} from 'amis-core';
import type {
  FuncGroup,
  VariableItem
} from 'amis-ui/lib/components/formula/CodeEditor';
import type { Option } from 'amis-ui/lib/components/Select';
import type { ItemRenderStates } from 'amis-ui/lib/components/Selection';
import type {FormulaPickerInputSettings} from 'amis-ui/lib/components/formula/Picker';
import {supportStatic} from './StaticHoc';

/**
 * InputFormula 公式编辑器
 * 文档：https://baidu.gitee.io/amis/zh-CN/components/form/input-formula
 */
export interface InputFormulaControlSchema extends FormBaseControlSchema {
  type: 'input-formula';

  /**
   * evalMode 即直接就是表达式，否则
   * 需要 ${这里面才是表达式}
   * 默认为 true
   */
  evalMode?: boolean;

  /**
   * 混合模式，意味着这个输入框既可以输入不同文本
   * 也可以输入公式。
   * 当输入公式时，值格式为 ${公式内容}
   * 其他内容当字符串。
   */
  mixedMode?: boolean;

  /**
   * 用于提示的变量集合，默认为空
   */
  variables: Array<VariableItem>;

  /**
   * 变量展现模式，可选值：'tabs' ｜ 'tree'
   */
  variableMode?: 'tabs' | 'tree';

  /**
   * 函数集合，默认不需要传，即  amis-formula 里面那个函数
   * 如果有扩充，则需要传。
   */
  functions: Array<FuncGroup>;

  /**
   * 编辑器标题
   */
  title?: string;

  /**
   * 顶部标题，默认为表达式
   */
  header: string;

  /**
   * 控件模式
   */
  inputMode?: 'button' | 'input-button' | 'input-group';

  /**
   * 外层input是否允许输入，否需要点击fx在弹窗中输入
   */
  allowInput?: boolean;

  /**
   * 按钮图标
   */
  icon?: SchemaIcon;

  /**
   * 按钮Label，inputMode为button时生效
   */
  btnLabel?: string;

  /**
   * 按钮样式
   */
  level?:
    | 'info'
    | 'success'
    | 'warning'
    | 'danger'
    | 'link'
    | 'primary'
    | 'dark'
    | 'light';

  /**
   * 按钮大小
   */
  btnSize?: 'xs' | 'sm' | 'md' | 'lg';

  /**
   * 边框模式，全边框，还是半边框，或者没边框。
   */
  borderMode?: 'full' | 'half' | 'none';

  /**
   * 输入框占位符
   */
  placeholder?: string;

  /**
   * 变量面板CSS样式类名
   */
  variableClassName?: string;

  /**
   * 函数面板CSS样式类名
   */
  functionClassName?: string;

  /**
   * 当前输入项字段 name: 用于避免循环绑定自身导致无限渲染
   */
  selfVariableName?: string;

  /**
   * 输入框的类型
   */
  inputSettings?: FormulaPickerInputSettings;

  /**
   * 是否单行显示模式(仅对编辑态有效，静态展示时均为多行模式) 默认为 true
   */
  singleLine?: boolean

 /**
 * 是否启用运行面板
 */
  enableRunPanel?: boolean;

  /**
  * 是否启用源码面板
  */
  enableSourceMode?:  boolean;

  /**
  * 变量展示模版
  */
  variableMenuTpl?: string | BaseSchema;

  /**
    * 变量展示模版
    */
  variableExtraActions?: SchemaNode[];
}

export interface InputFormulaProps
  extends FormControlProps,
    Omit<
      InputFormulaControlSchema,
      'options' | 'inputClassName' | 'className' | 'descriptionClassName'
    > {}

@FormItem({
  type: 'input-formula'
})
export class InputFormulaRenderer extends React.Component<InputFormulaProps> {
  static defaultProps: Pick<
    InputFormulaControlSchema,
    'inputMode' | 'borderMode' | 'evalMode'
  > = {
    inputMode: 'input-button',
    borderMode: 'full',
    evalMode: true
  };

  ref: any;

  @autobind
  formulaRef(ref: any) {
    if (ref) {
      while (ref && ref.getWrappedInstance) {
        ref = ref.getWrappedInstance();
      }
      this.ref = ref;
    } else {
      this.ref = undefined;
    }
  }

  validate() {
    const {translate: __, value} = this.props;

    if (this.ref?.validate && value) {
      const res = this.ref.validate(value);
      if (res !== true) {
        return __('FormulaEditor.invalidData', {err: res});
      }
    }
  }

  resolveVariables() {
    let {variables, functions} = this.props;

    if (isPureVariable(variables)) {
      // 如果 variables 是 ${xxx} 这种形式，将其处理成实际的值
      variables = resolveVariableAndFilter(variables, this.props.data, '| raw');
    }

    if (isPureVariable(functions)) {
      // 如果 functions 是 ${xxx} 这种形式，将其处理成实际的值
      functions = resolveVariableAndFilter(functions, this.props.data, '| raw');
    }

    return {
      variables,
      functions
    }
  }

  /**
   * 渲染 变量列表选项的Label标签
   */
  @autobind
  renderVariableMenuLabel(option: Option, states: ItemRenderStates) {
    const {variableMenuTpl, render, data} = this.props;
    const originData = createObject(createObject(data, states), option);

    const renderContent =  typeof variableMenuTpl === 'string'
      ? resolveVariableAndFilter(variableMenuTpl as string, originData)
      : variableMenuTpl

    return render(`menu/${states.index}/label`, renderContent, {
      showNativeTitle: true,
      data: originData
    });
  }

  /**
   * 渲染 变量列表选项右侧的操作按钮
   */
  @autobind
  renderVariableExtraActions(option: Option, states: ItemRenderStates) {
    const {variableExtraActions, render, data} = this.props;

    let extraActionList: any[] = variableExtraActions as any;

    if (!Array.isArray(variableExtraActions)) {
      extraActionList = [variableExtraActions]
    }

    const originData = createObject(createObject(data, states), option);

    return render(`/menu/${states.index}/extraactions`, extraActionList, {data: originData});
  }

  @autobind
  async handleChange(nextValue: any, isChange: boolean = true) {
    const { onChange, dispatchEvent } = this.props;

    if(isChange) {
      const changeEvent = await dispatchEvent(
        'change',
        resolveEventData(this.props, {value: nextValue})
      );
  
      if (changeEvent?.prevented) {
        return;
      }
    }

    onChange && onChange(nextValue);
  }

  /**
   * 渲染 InputFormula 静态展示的情况
   */
  renderStatic(displayValue = '-') {
    let {
      evalMode,
      value,
      mixedMode,
      classnames: cx,
    } = this.props;

    if(!value) {
      return <>{displayValue}</>
    }

    const { functions, variables } = this.resolveVariables()

    return (
      <TooltipWrapper
        tooltipTheme="dark"
        tooltip={value}
      >
        <div className={cx('FormulaPicker', 'is-static')}>
          <FormulaPicker
            functions={functions}
            variables={variables}
          >
            {(childrenProps) => (
              <FormulaCodeEditor
                readOnly
                static
                functions={childrenProps.functions}
                variables={childrenProps.variables}
                evalMode={mixedMode ? false : evalMode}
                value={value}
              />
          )}
          </FormulaPicker>
        </div>
      </TooltipWrapper>
    )
  }

  @supportStatic()
  render() {
    const {
      disabled,
      evalMode,
      mixedMode,
      variableMode,
      header,
      label,
      value,
      style,
      clearable = false,
      className,
      classPrefix: ns,
      classnames: cx,
      allowInput = true,
      borderMode,
      placeholder,
      inputMode,
      btnLabel,
      level,
      btnSize,
      icon,
      title,
      variableClassName,
      functionClassName,
      data,
      onPickerOpen,
      selfVariableName,
      env,
      inputSettings,
      singleLine = true,
      enableRunPanel = false,
      enableSourceMode = false,
      variableMenuTpl,
      variableExtraActions,
      mobileUI
    } = this.props;
    const { functions, variables } = this.resolveVariables()

    return (
      <FormulaPicker
        popOverContainer={env.getModalContainer}
        ref={this.formulaRef}
        className={className}
        style={style}
        value={value}
        singleLine={singleLine}
        disabled={disabled}
        onChange={this.handleChange}
        evalMode={evalMode}
        variables={variables}
        variableMode={variableMode}
        functions={functions}
        header={header || label || ''}
        borderMode={borderMode}
        placeholder={placeholder}
        mode={inputMode}
        inputSettings={inputSettings}
        btnLabel={btnLabel}
        level={level}
        btnSize={btnSize}
        icon={icon}
        title={title}
        clearable={clearable}
        variableClassName={variableClassName}
        functionClassName={functionClassName}
        data={data}
        onPickerOpen={onPickerOpen}
        selfVariableName={selfVariableName}
        mixedMode={mixedMode}
        enableRunPanel={enableRunPanel}
        enableSourceMode={enableSourceMode}
        allowInput={allowInput}
        renderVariableMenuLabel={variableMenuTpl ? this.renderVariableMenuLabel : undefined}
        renderVariableExtraActions={variableExtraActions?.length ? this.renderVariableExtraActions : undefined}
        mobileUI={mobileUI}
      />
    );
  }
}
