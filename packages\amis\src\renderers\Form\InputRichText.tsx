import React from 'react';
import {
  FormItem,
  FormControlProps,
  FormBaseControl,
  buildApi,
  qsstringify,
  resolveEventData
} from 'amis-core';
import cx from 'classnames';
import {LazyComponent} from 'amis-core';
import {tokenize} from 'amis-core';
import {normalizeApi} from 'amis-core';
import {ucFirst} from 'amis-core';
import type {FormBaseControlSchema, SchemaApi} from '../../Schema';
import {promiseUploadOss} from '@dataseed/cdn-cloud';
import { supportStatic } from './StaticHoc';

/**
 * RichText
 * 文档：https://baidu.gitee.io/amis/docs/components/form/input-rich-text
 */
export interface RichTextControlSchema extends FormBaseControlSchema {
  type: 'input-rich-text';

  /**
   * 编辑器类型
   */
  vendor?: 'froala' | 'tinymce';

  /**
   * 图片保存 API
   *
   * @default /api/upload/image
   */
  receiver?: SchemaApi;

  /**
   * 视频保存 API
   *
   * @default /api/upload/video
   */
  videoReceiver?: SchemaApi;

  /**
   * 接收器的字段名
   */
  fileField?: string;

  /**
   * 边框模式，全边框，还是半边框，或者没边框。
   */
  borderMode?: 'full' | 'half' | 'none';

  /**
   *  tinymce 或 froala 的配置
   */
  options?: any;

  /**
   * 是否上传图片
   */
  uploadImage?: boolean;
}

export interface RichTextProps extends FormControlProps {
  options?: any;
  vendor?: 'froala' | 'tinymce';
}

function loadRichText(
  type: 'tinymce' | 'froala' = 'froala'
): () => Promise<any> {
  return () =>
    type === 'tinymce'
      ? import('amis-ui/lib/components/Tinymce').then(item => item.default)
      : import('amis-ui/lib/components/RichText').then(item => item.default);
}

export default class RichTextControl extends React.Component<
  RichTextProps,
  any
> {
  static defaultProps: Partial<RichTextProps> = {
    imageEditable: true,
    receiver: '/api/upload/image',
    videoReceiver: '/api/upload/video',
    fileField: 'file',
    placeholder: 'placeholder.enter',
    uploadImage: true,
    options: {
      toolbarButtons: [
        'undo',
        'redo',
        'paragraphFormat',
        'textColor',
        'backgroundColor',
        'bold',
        'underline',
        'strikeThrough',
        'formatOL',
        'formatUL',
        'align',
        'quote',
        'insertLink',
        'insertImage',
        'insertEmotion',
        'insertTable',
        'html'
      ]
    },
    previewBorder: false,
  };

  state = {
    focused: false
  };
  config: any = null;
  iframeRef: React.RefObject<HTMLIFrameElement> = React.createRef();
  constructor(props: RichTextProps) {
    super(props);

    const finnalVendor =
      props.vendor || (props.env.richTextToken ? 'froala' : 'tinymce');
    this.handleFocus = this.handleFocus.bind(this);
    this.handleBlur = this.handleBlur.bind(this);
    this.handleChange = this.handleChange.bind(this);
    const imageReceiver = normalizeApi(
      props.receiver,
      props.receiver.method || 'post'
    );
    imageReceiver.data = imageReceiver.data || {};
    const imageApi = buildApi(imageReceiver, props.data, {
      method: props.receiver.method || 'post'
    });
    if (finnalVendor === 'froala') {
      const videoReceiver = normalizeApi(
        props.videoReceiver,
        props.videoReceiver.method || 'post'
      );
      videoReceiver.data = videoReceiver.data || {};
      const videoApi = buildApi(videoReceiver, props.data, {
        method: props.videoReceiver.method || 'post'
      });
      this.config = {
        imageAllowedTypes: ['jpeg', 'jpg', 'png', 'gif'],
        imageDefaultAlign: 'left',
        imageEditButtons: props.imageEditable
          ? [
              'imageReplace',
              'imageAlign',
              'imageRemove',
              '|',
              'imageLink',
              'linkOpen',
              'linkEdit',
              'linkRemove',
              '-',
              'imageDisplay',
              'imageStyle',
              'imageAlt',
              'imageSize'
            ]
          : [],
        key: props.env.richTextToken,
        attribution: false,
        ...props.options,
        editorClass: props.editorClass,
        placeholderText: props.translate(props.placeholder),
        imageUploadURL: imageApi.url,
        imageUploadParams: {
          from: 'rich-text',
          ...imageApi.data
        },
        videoUploadURL: videoApi.url,
        videoUploadParams: {
          from: 'rich-text',
          ...videoApi.data
        },
        events: {
          ...(props.options && props.options.events),
          focus: this.handleFocus,
          blur: this.handleBlur
        },
        language:
          !this.props.locale || this.props.locale === 'zh-CN' ? 'zh_cn' : ''
      };

      if (props.buttons) {
        this.config.toolbarButtons = props.buttons;
      }
    } else {
      const fetcher = props.env.fetcher;
      const images_upload_handler = (blobInfo: any, progress: any) =>
        new Promise(async (resolve, reject) => {
          const formData = new FormData();

          if (imageApi.data) {
            qsstringify(imageApi.data)
              .split('&')
              .filter(item => item !== '')
              .forEach(item => {
                let parts = item.split('=');
                formData.append(parts[0], decodeURIComponent(parts[1]));
              });
          }

          formData.append(
            props.fileField || 'file',
            blobInfo.blob(),
            blobInfo.filename()
          );

          try {
            let response = {} as any;
            const { env, uploadMode } = this.props;
            const receiver = {
              adaptor: (payload: object) => {
                return {
                  ...payload,
                  data: payload
                };
              },
              ...imageApi
            };

            if (uploadMode === 'oss') {
              const file = blobInfo.blob();
              response = await promiseUploadOss({
                file,
                fetcher: env.fetcher,
                tokenOptions: {
                  ...this.props.receiver
                },
                ossOptions: {
                  config: {
                    method: 'post',
                    withCredentials: false,
                  }
                }
              }).catch((err: any) => {
                throw new Error(err?.message || '上传失败');
              });
            } else {
              response = await fetcher(receiver, formData, {
                method: 'post'
              });
            }
            if (response.ok) {
              const location =
                response.data?.link ||
                response.data?.url ||
                response.data?.value ||
                response.data?.data?.link ||
                response.data?.data?.url ||
                response.data?.data?.value;
              if (location) {
                resolve(location);
              } else {
                console.warn('must have return value');
              }
            }
          } catch (e) {
            reject(e);
          }
        })
      this.config = {
        // FIX: issue #635 支持关闭自动上传图片
        images_upload_handler: props.uploadImage ? images_upload_handler : undefined,
        ...props.options,
      };
    }
  }

  async handleFocus() {
    const {dispatchEvent, value} = this.props;

    // FIX issue#635 富文本支持focus事件
    const rendererEvent = await dispatchEvent('focus', resolveEventData(
      this.props,
      { value },
      'value'
    ));

    if (rendererEvent?.prevented) {
      return;
    }

    this.setState({
      focused: true
    });
  }

  async handleBlur() {
    const {dispatchEvent, value} = this.props;

    // FIX issue#635 富文本支持blur事件
    const rendererEvent = await dispatchEvent('blur', resolveEventData(
      this.props,
      { value },
      'value'
    ));

    if (rendererEvent?.prevented) {
      return;
    }

    this.setState({
      focused: false
    });
  }

  async handleChange(
    value: any,
    submitOnChange?: boolean,
    changeImmediately?: boolean
  ) {
    const {onChange, disabled, dispatchEvent} = this.props;

    if (disabled) {
      return;
    }

    // FIX issue#635 富文本支持change事件
    const rendererEvent = await dispatchEvent('change', resolveEventData(
      this.props,
      { value },
      'value'
    ));

    if (rendererEvent?.prevented) {
      return;
    }

    onChange?.(value, submitOnChange, changeImmediately);
  }

  // FIX: issue#679 使静态iframe的高度自适应
  onLoad = () => {
    const iframe = this.iframeRef.current;
    if(iframe) {
      const doc = iframe.contentDocument || iframe.contentWindow?.document;
      iframe.height = (doc?.documentElement.scrollHeight as number)?.toString();
    }
  }

  // FIX: issue#679 添加预览模式
  renderPreview() {
    const { getValue, classPrefix: ns, className, borderMode, disabled, previewBorder } = this.props;
    const value = getValue();

    return (
      <div
        className={cx(`${ns}RichTextControl--preview`, className, {
          'is-focused': this.state.focused,
          'is-disabled': disabled,
          'no-border': !previewBorder,
          [`${ns}RichTextControl--border${ucFirst(borderMode)}`]: borderMode
        })}
      >
        <iframe
          className='static-iframe'
          srcDoc={value}
          onLoad={this.onLoad}
          ref={this.iframeRef}
        >{'你的浏览器不支持srcdoc'}</iframe>
      </div>
    )
  }

  // FIX: issue#679 添加预览模式
  onEnlarge = (richText: any) => {
    const { onImageEnlarge } = this.props;

    onImageEnlarge && onImageEnlarge({ type: 'rich-text', richText });
  }

  render() {
    const {
      className,
      style,
      classPrefix: ns,
      value,
      onChange,
      disabled,
      size,
      vendor,
      env,
      locale,
      translate,
      borderMode,
      static: isStatic,
      render,
      previewMode,
      previewTrigger,
    } = this.props;

    const finnalVendor = vendor || (env.richTextToken ? 'froala' : 'tinymce');

    if(previewMode) {
      const preivew = this.renderPreview();

      if(previewTrigger) {
        return render(
          'button',
          {
            label: translate('Pdf.view'),
            ...previewTrigger,
            type: 'button',
          },
          {
            onClick: () => this.onEnlarge(preivew),
          },
        );
      }

      return preivew
    }

    return (
      <div
        className={cx(`${ns}RichTextControl`, className, {
          'is-focused': this.state.focused,
          'is-disabled': disabled,
          [`${ns}RichTextControl--border${ucFirst(borderMode)}`]: borderMode
        })}
      >
        <LazyComponent
          getComponent={loadRichText(finnalVendor)}
          model={value}
          onModelChange={this.handleChange}
          onFocus={this.handleFocus}
          onBlur={this.handleBlur}
          config={this.config}
          disabled={disabled || isStatic}
          locale={locale}
          translate={translate}
        />
      </div>
    );
  }
}

@FormItem({
  type: 'input-rich-text',
  sizeMutable: false
})
export class RichTextControlRenderer extends RichTextControl {}
