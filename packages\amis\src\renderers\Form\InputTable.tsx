import React from 'react';
import {
  FormItem,
  FormControlProps,
  FormBaseControl,
  createObject,
  getTree,
  getVariable,
  setVariable,
  spliceTree,
  filterDate,
  isEffectiveApi,
  filter,
  dataMapping,
  SimpleMap,
  RendererData,
  ActionObject,
  Payload,
  ApiObject,
  autobind,
  isExpression,
  extendObject,
  ITableStore,
  isPureVariable,
  resolveVariableAndFilter,
  getRendererByName,
  resolveEventData,
  ListenerAction,
  evalExpression,
  evalExpressionWithConditionBuilder,
  isArrayChildrenModified,
  calculationDataLevel,
  isObject,
  everyTree,
} from 'amis-core';
import {Button, Icon} from 'amis-ui';
import omit from 'lodash/omit';
import findIndex from 'lodash/findIndex';
import cloneDeep from 'lodash/cloneDeep';
import isEqual from 'lodash/isEqual';
import inRange from 'lodash/inRange';
import {TableSchema} from '../Table';
import {Schema<PERSON><PERSON>, SchemaCollection} from '../../Schema';
import find from 'lodash/find';
import moment from 'moment';
import merge from 'lodash/merge';
import mergeWith from 'lodash/mergeWith';

import type {SchemaTokenizeableString} from '../../Schema';
import { PaginationProps } from '../Pagination';

export interface TableControlSchema
  extends FormBaseControl,
    Omit<TableSchema, 'type'> {
  type: 'input-table';

  /**
   * 可新增
   */
  addable?: boolean;

  /**
   * 可复制新增
   */
  copyable?: boolean;

  /**
   * 复制按钮文字
   */
  copyBtnLabel?: string;

  /**
   * 复制按钮图标
   */
  copyBtnIcon?: string;

  /**
   * 是否显示复制按钮
   */
  copyAddBtn?: boolean;

  /**
   * 是否可以拖拽排序
   */
  draggable?: boolean;

  /**
   * 新增 API
   */
  addApi?: SchemaApi;

  /**
   * 新增按钮文字
   */
  addBtnLabel?: string;

  /**
   * 新增按钮图标
   */
  addBtnIcon?: string;

  /**
   * 可否删除
   */
  removable?: boolean;

  /**
   * 删除的 API
   */
  deleteApi?: SchemaApi;

  /**
   * 可否编辑
   */
  editable?: boolean;

  /**
   * 更新按钮名称
   */
  editBtnLabel?: string;

  /**
   * 更新按钮图标
   */
  editBtnIcon?: string;

  /**
   * 确认按钮文字
   */
  confirmBtnLabel?: string;

  /**
   * 确认按钮图标
   */
  confirmBtnIcon?: string;

  /**
   * 取消按钮文字
   */
  cancelBtnLabel?: string;

  /**
   * 取消按钮图标
   */
  cancelBtnIcon?: string;

  /**
   * 删除按钮文字
   */
  deleteBtnLabel?: string;

  /**
   * 删除按钮图标
   */
  deleteBtnIcon?: string;

  /**
   * 更新 API
   */
  updateApi?: SchemaApi;

  /**
   * 初始值，新增的时候
   */
  scaffold?: any;

  /**
   * 删除确认文字
   */
  deleteConfirmText?: string;

  /**
   * 值字段
   */
  valueField?: string;

  /**
   * 是否为确认的编辑模式。
   */
  needConfirm?: boolean;

  /**
   * 是否可以访问父级数据，正常 combo 已经关联到数组成员，是不能访问父级数据的。
   */
  canAccessSuperData?: boolean;

  /**
   * 是否显示序号
   */
  showIndex?: boolean;

  /**
   * 分页个数，默认不分页
   */
  perPage?: number;

  /**
   * 限制最大个数
   */
  maxLength?: number | SchemaTokenizeableString;

  /**
   * 限制最小个数
   */
  minLength?: number | SchemaTokenizeableString;

  /**
   * 是否显示底部新增按钮
   */
  showFooterAddBtn?: boolean;

  /**
   * 是否显示表格操作栏新增按钮
   */
  showTableAddBtn?: boolean;

  /**
   * 是否显示新增子级按钮
   */
  showAddChildrenBtn?: boolean;

  /**
   * 新增子级按钮图标
   */
  addChildrenBtnIcon?: boolean;

  /**
   * 新增子级按钮名称
   */
  addChildrenBtnLabel?: boolean;

  /**
   * 向上添加同级按钮名称
   */
  addAboveBtnLabel?: boolean;

  /**
   * 向下添加同级按钮名称
   */
  addBelowBtnLabel?: boolean;

  /**
   * 底部新增按钮配置
   */
  footerAddBtn?: SchemaCollection;

  /**
   * 是否渲染全部
   */
  mountAll?: boolean;

  /**
   * 控制表格操作栏 新增 表达式
   */
  tableAddBtnVisibleOn?: string;

  /**
   * 控制表格操作栏 新子级增 表达式
   */
  tableAddChildrenBtnVisibleOn?: string

  /**
   * 控制删除按钮表达式
   */
  tableDeleteBtnVisibleOn?: string;

  /**
   * 控制编辑按钮表达式
   */
  tableEditBtnVisibleOn?: string;

  /**
   * 点击表格操作栏新增按钮直接新增
   */
  tableAddBtnImmediateAdd?: boolean;

  // 支持自定义额外的footer配置
  extraFooterConfig?: SchemaCollection;
  // 是否更新Table所有的行
  updateAllRows?: boolean;
  /**
   * 分页容器配置属性
   */
  paginationConfig?: PaginationProps;
  /**
   * 默认只有当分页数大于 1 是才显示，如果总是想显示请配置。
   */
  alwaysShowPagination?: boolean;
  /**
   * 	是否复用每行以提高性能，默认全复用，match为匹配复用，true为完全复用，false为不复用（不推荐）
   */
  reUseRow?: 'match' | boolean;
  /**
   * 	分批渲染个数，配置后默认开启分批渲染
   */
  batchNum?: number;
}

export interface TableProps
  extends FormControlProps,
    Omit<
      TableControlSchema,
      'type' | 'className' | 'descriptionClassName' | 'inputClassName'
    > {
  parent?: Record<string, any>;
  showExpansionColumn?: boolean
  footerToolbarClassName?: string; // 底部按钮区域className
}

export interface TableState {
  items: Array<any>;
  columns: Array<any>;
  editIndex: number;
  childrenEditIndex: number;
  isCreateMode?: boolean;
  page?: number;
  perPage?: number;
  selectedRows: Array<any>;
  subTable: object;
  lastModifiedRow?: {
    index: number;
    data: Record<string, any>;
  };
}

export type FormTableRendererEvent =
  | 'add'
  | 'addConfirm'
  | 'addSuccess'
  | 'addFail'
  | 'edit'
  | 'editConfirm'
  | 'editSuccess'
  | 'editFail'
  | 'delete'
  | 'deleteSuccess'
  | 'deleteFail';

export type FormTableRendererAction = 'add' | 'delete' | 'reset' | 'clear' | 'changePage' | 'setSelectedItems';

export default class FormTable extends React.Component<TableProps, TableState> {
  static defaultProps = {
    placeholder: '暂无数据',
    scaffold: {},
    addBtnIcon: 'plus',
    addChildrenBtnIcon: 'sub-plus',
    copyBtnIcon: 'copy',
    editBtnIcon: 'pencil',
    deleteBtnIcon: 'minus',
    confirmBtnIcon: 'check',
    cancelBtnIcon: 'close',
    valueField: '',
    minLength: 0,
    maxLength: Infinity,
    showTableAddChildrenBtn: true,
    showTableAddBtn: true,
    showFooterAddBtn: true,
    updateAllRows: false,
    transferAllData: true,
    alwaysShowPagination: false,
    paginationConfig: {},
    columnsTogglable: false, // 默认不显示列选择
  };

  static propsList: Array<string> = [
    'onChange',
    'name',
    'columns',
    'label',
    'scaffold',
    'showTableAddBtn',
    'showTableAddChildrenBtn',
    'addable',
    'removable',
    'copyable',
    'editable',
    'addApi',
    'updateApi',
    'deleteApi',
    'needConfirm',
    'canAccessSuperData',
    'formStore',
    'extraFooterConfig',
    'tableAddBtnVisibleOn',
    'tableAddChildrenBtnVisibleOn',
    'tableDeleteBtnVisibleOn',
    'tableEditBtnVisibleOn',
    'tableAddBtnImmediateAdd',
    'parent',
  ];

  entries: SimpleMap<any, number>;
  entityId: number = 1;
  subForms: any = {};
  rowPrinstine: Array<any> = [];
  editting: any = {};
  tableStore?: ITableStore;
  tableContainer: HTMLDivElement;

  constructor(props: TableProps) {
    super(props);

    const items = Array.isArray(props.value) ? props.value.concat() : [];
    const { parent, subTable } = props;
    // 公用表头场景，用input-table schema配置生成subTable配置
    const level = calculationDataLevel(items);
    let subTableConfig = subTable;
    const subConfig = this.generateTree(level, 1);
    if (!parent && !subTable && level > 1) {
      subTableConfig = subConfig
    }

    this.state = {
      columns: this.buildColumns(props),
      perPage: props.perPage,
      editIndex: -1,
      isCreateMode: false,
      childrenEditIndex: -1,
      items,
      selectedRows: Array.isArray(props.selected)
        ? props.selected.concat()
        : typeof props.selected === 'string'
        ? resolveVariableAndFilter(props.selected, props.data, '| raw') || []
        : [],
      subTable: subTableConfig,
    };

    this.entries = new SimpleMap();
    this.buildItemProps = this.buildItemProps.bind(this);
    this.confirmEdit = this.confirmEdit.bind(this);
    this.cancelEdit = this.cancelEdit.bind(this);
    this.handleSaveTableOrder = this.handleSaveTableOrder.bind(this);
    this.handleTableSave = this.handleTableSave.bind(this);
    this.getEntryId = this.getEntryId.bind(this);
    this.subFormRef = this.subFormRef.bind(this);
    this.handlePageChange = this.handlePageChange.bind(this);
    this.emitValue = this.emitValue.bind(this);
    this.handleSelectedChange = this.handleSelectedChange.bind(this);
    this.handleSelect = this.handleSelect.bind(this);
    this.replaceChildren = this.replaceChildren.bind(this);
    this.updateParentEditState = this.updateParentEditState.bind(this);
    this.tableRef = this.tableRef.bind(this)
  }

  componentDidUpdate(nextProps: TableProps) {
    const props = this.props;
    let toUpdate: any = null;

    // 如果static为true 或 disabled为true，
    // 则删掉正在新增 或 编辑的那一行
    // Form会向FormItem下发disabled属性，disbaled 属性值也需要同步到
    if (
      props.disabled !== nextProps.disabled ||
      props.$schema.disabled !== nextProps.$schema.disabled ||
      props.$schema.static !== nextProps.$schema.static
    ) {
      const items = this.state.items.filter(item => !item.__isPlaceholder);
      toUpdate = {
        ...toUpdate,
        items,
        editIndex: -1,
        columns: this.buildColumns(props)
      };
    }
    // 当columns为${}时，不能使用props.columns比较，因为始终为字符串
    const curColumns = this.resolveVariableProps(props, 'columns')
    const nextColumns = this.resolveVariableProps(nextProps, 'columns')
    if (curColumns !== nextColumns) {
      toUpdate = {
        ...toUpdate,
        columns: this.buildColumns(props)
      };
    }

    if (props.value !== nextProps.value) {
      toUpdate = {
        ...toUpdate,
        items: Array.isArray(props.value) ? props.value.concat() : [],
        editIndex: -1
      };
    }

    const isCurrent = props.subTableOperationIndex === props?.parent?.index && typeof props.subTableOperationIndex === 'number';
    if ((isCurrent && props.isCreateModeOfSubTable !== this.state.isCreateMode)) {
      toUpdate = {
        ...toUpdate,
        isCreateMode: props.isCreateModeOfSubTable,
        editIndex: isCurrent ? props.subTableAddRowIndex : -1,
        columns: this.buildColumns(props, props.isCreateModeOfSubTable),
      };
    }

    if (props.selected) {
      let propsSelected = props.selected;
      let nextPropsSelected = nextProps.selected;
      if (typeof props.selected === 'string') {
        propsSelected = resolveVariableAndFilter(
          props.selected,
          props.data,
          '| raw'
        );
        nextPropsSelected = resolveVariableAndFilter(
          nextProps.selected,
          nextProps.data,
          '| raw'
        );
      }

      if (isArrayChildrenModified(propsSelected!, nextPropsSelected!)) {
        toUpdate = {
          ...toUpdate,
          selectedRows: propsSelected || []
        };
      }
    }

    toUpdate && this.setState(toUpdate);
  }

  componentWillUnmount() {
    this.entries.dispose();
  }

  subTableProps = [
      'id',
      'type',
      'valueField',
      'selected',
      'addable',
      'editable',
      'removable',
      'copyable',
      'selectable',
      'multiple',
      'columns',
      'showTableAddBtn',
      'showAddChildrenBtn',
      'addChildrenBtnIcon',
      'addChildrenBtnLabel',
      'addAboveBtnLabel',
      'addBelowBtnLabel',
      'expandConfig',
      'addApi',
      'updateApi',
      'deleteApi',
      'addBtnLabel',
      'addBtnIcon',
      'copyBtnLabel',
      'copyBtnIcon',
      'editBtnLabel',
      'editBtnIcon',
      'deleteBtnLabel',
      'deleteBtnIcon',
      'confirmBtnLabel',
      'confirmBtnIcon',
      'cancelBtnLabel',
      'cancelBtnIcon',
      'needConfirm',
      'canAccessSuperData',
      'strictMode',
      'minLength',
      'maxLength',
      'perPage',
      'showIndex',
      'extraFooterConfig',
      'tableAddBtnVisibleOn',
      'tableAddChildrenBtnVisibleOn',
      'tableDeleteBtnVisibleOn',
      'tableEditBtnVisibleOn',
      'tableAddBtnImmediateAdd',
    ];

  tableContainerRef = (ref: HTMLDivElement) => {
    this.tableContainer = ref;
  }

  extractFields = (obj:TableProps, fieldList: string[]) => {
    const extractedObj: any = {};

    fieldList.forEach(field => {
      if (obj.hasOwnProperty(field)) {
        extractedObj[field] = obj[field];
      }
    });

    return extractedObj;
  }

  generateTree(levels: number, currentLevel = 1) {
    if (currentLevel > levels) {
      return null;
    }

    const subTablePropsObj = this.extractFields(this.props, this.subTableProps);
    const { id = 'subTable', name = 'subTable' } = this.props;
    const node = {
      ...subTablePropsObj,
      name: `${name}_${currentLevel}`,
      id: `${id}_${currentLevel}`, // 多层级表格，保证id唯一
      isPublicHeader: true,
      showFooterAddBtn: false, // 公共表头，子表格无左下角新增按钮
      perPage: null, // 公共表头，子表格无分页器
    }

    const childNode = this.generateTree(levels, currentLevel + 1);
    node.subTable = childNode;

    return node;
  }

  resolveVariableProps(props: TableProps, key: 'minLength' | 'maxLength' | 'columns', paramsValue?: any) {
    const defaultMap = {
      minLength: 0,
      maxLength: Infinity,
      columns: [],
    };
    let value = paramsValue || props[key];

    if (!value) {
      return defaultMap[key];
    }

    if (typeof value === 'string') {
      if (isPureVariable(value)) {
        const resolved = resolveVariableAndFilter(value, props.data, '| raw');
        if (['minLength', 'maxLength'].includes(key)) {
          value = (
              typeof resolved === 'number' && resolved >= 0
                ? resolved
                : defaultMap[key]
          ) as number;
        } else if (['columns'].includes(key)) {
          value = resolved;
        }
      } else {
        const parsed = parseInt(value, 10);
        value = (isNaN(parsed) ? defaultMap[key] : parsed) as number;
      }
    }

    return value;
  }

  subFormRef(form: any, x: number, y: number, path: string, columnPositon: string) {
    // subForms的key如果有变更，需同步变更行保存时，收集行subForm的逻辑，否则匹配不到当前编辑行，无法进行校验
    this.subForms[`${path}-${x}-${y}-${columnPositon}`] = form;
  }

  // notSubmit: 当前页最后一行向下新增时，新增的数据会在下一页，因此分页时触发的校验，不考虑正在编辑的场景。
  async validate(isSubmit: boolean = true): Promise<string | void> {
    const {value, translate: __} = this.props;
    const columns = this.resolveVariableProps(this.props, 'columns');
    const minLength = this.resolveVariableProps(this.props, 'minLength');
    const maxLength = this.resolveVariableProps(this.props, 'maxLength');

    // todo: 如果当前正在编辑中，表单提交了，应该先让正在编辑的东西提交然后再做验证。
    if (~this.state.editIndex && isSubmit) {
      return __('Table.editing');
    }

    if (minLength && (!Array.isArray(value) || value.length < minLength)) {
      return __('Combo.minLength', {minLength});
    } else if (maxLength && Array.isArray(value) && value.length > maxLength) {
      return __('Combo.maxLength', {maxLength});
    } else {
      const subForms: Array<any> = [];
      Object.keys(this.subForms).forEach(
        key => this.subForms[key] && subForms.push(this.subForms[key])
      );
      if (subForms.length) {
        const results = await Promise.all(
          subForms.map(item => item.validate())
        );

        let msg = ~results.indexOf(false) ? __('Form.validateFailed') : '';
        let uniqueColumn = '';

        if (
          !msg &&
          Array.isArray(columns) &&
          Array.isArray(value) &&
          columns.some(item => {
            if (item.unique && item.name) {
              let exists: Array<any> = [];

              return value.some((obj: any) => {
                const value = getVariable(obj, item.name);

                if (~exists.indexOf(value)) {
                  uniqueColumn = `${item.label || item.name}`;
                  return true;
                }

                exists.push(value);
                return false;
              });
            }

            return false;
          })
        ) {
          msg = __('InputTable.uniqueError', {
            label: uniqueColumn
          });
        }
        // msg有值，说明校验未通过;
        // mountAll为false: 校验的是当前展开项，校验未通过也不需要展开折叠行
        // mountAll为true: 校验表格所有内容，校验未通过 展开table所有折叠项
        if (msg && this.props.mountAll) {
          !this.tableStore?.allExpanded && this.tableStore?.toggleExpandAll();
          const children = this.tableStore?.children?.filter(
            (item) => item?.storeType === 'TableStore'
          );
          children?.forEach((item) => {
            !item?.allExpanded && item.toggleExpandAll();
          })
        }

        return msg;
      }
    }

    if (!this.tableStore) return;

    // 校验子项
    const children = this.tableStore.children.filter(
      item => item?.storeType === 'FormItemStore'
    );

    const results = await Promise.all(
      children.map(item => item.validate(this.props.value))
    );
  }

  // 替换table data中指定的一行的children
  replaceChildren(
    allData: Array<any>,
    replaceRow: object,
    newChild: Array<any>
  ) {
    const dataSource = [...allData]
    dataSource.forEach((item: any, index) => {
      const newItem = {...item};
      if (newItem.children) delete newItem.children;
      if (isEqual(newItem, replaceRow)) {
        newItem.children = newChild;
        dataSource[index] = newItem;
      } else if (item.children?.length) {
        newItem.children = this.replaceChildren(item.children, replaceRow, newChild);
        dataSource[index] = newItem;
      }
    });
    return dataSource;
  }

  async emitValue(values?: any[]) {
    const {onChange} = this.props;
    const isPrevented = await this.dispatchEvent('change');
    const data = values ?? this.state.items
    isPrevented || onChange?.(data);
    return isPrevented;
  }

  updateParentEditState(values: any) {
    this.setState({
      ...values,
    });
  };

  async addAction(actionType: string, action: any, args: any) {
    const {
      valueField,
      needConfirm,
      env,
      addable,
      addApi,
      translate: __,
    } = this.props;

    const ctx = this.props.store?.data || {}; // 获取当前上下文数据
    const isAdd = actionType === 'add';

    if(addable === false) {
      return;
    }

    const items = this.state.items.concat();

    if (addApi || (isAdd ? action.payload : args)) {
      let toAdd = null;

      if(isEffectiveApi(addApi, ctx)) {
        const payload = await env.fetcher(addApi, ctx);
        if (payload && !payload.ok) {
          env.notify(
            'error',
            (addApi as ApiObject)?.messages?.failed ??
              (payload.msg || __('fetchFailed'))
          );
          return;
        } else if (payload && payload.ok) {
          toAdd = payload.data;
        }
      } else {
        toAdd = isAdd ? dataMapping(action.payload, ctx) : args.item ?? {};
      }

      toAdd = Array.isArray(toAdd) ? toAdd : [toAdd];

      const pushIndex = typeof args.index === 'number' ? args.index : items.length; // Math.max(args.index || 0, items.length);
      // 从右往左插入
      for (let i = toAdd.length; i >= 1; i--) {
        if (
          !valueField ||
          !find(
            items,
            item =>
              item[valueField as string] == toAdd[i - 1][valueField as string]
          )
        ) {
          if(isAdd) {
            items.push(toAdd);
          } else {
            items.splice(pushIndex, 0, toAdd[i - 1]);
          }
        }
      }

       // 添加 needConfirm 的场景
       if(needConfirm !== false && toAdd.length === 1) {
        toAdd.forEach((item: any) => {
          item.__isPlaceholder = true
        })
      }

      this.addItem(
        isAdd ? 'below' : 'above',
        isAdd ? items.length - 1 : pushIndex,
        !!action.needDispatch, {
          items,
          ignoreStarEdit: toAdd.length !== 1
        }
      )
    } else {
      this.addItem(action.addType || 'below', items.length - 1, !!action.needDispatch);
    }
  }

  async doAction(action: ActionObject, ctx: RendererData, ...rest: Array<any>) {
    const {
      onAction,
      valueField,
      env,
      onChange,
      editable,
      needConfirm,
      addable,
      addApi,
      translate: __
    } = this.props;

    if (action.actionType === 'add') {
      return this.addAction(action.actionType, action, {});
    } else if (
      action.actionType === 'remove' ||
      action.actionType === 'delete'
    ) {
      if (!valueField) {
        return env.alert(__('Table.valueField'));
      } else if (!action.payload) {
        return env.alert(__('Table.playload'));
      }

      const items = this.state.items.concat();
      let toRemove: any = dataMapping(action.payload, ctx);
      toRemove = Array.isArray(toRemove) ? toRemove : [toRemove];

      toRemove.forEach((toRemove: any) => {
        const idx = findIndex(
          items,
          item => item[valueField as string] == toRemove[valueField as string]
        );
        if (~idx) {
          items.splice(idx, 1);
        }
      });

      this.setState(
        {
          items
        },
        () => onChange(items)
      );

      // todo 如果配置删除 Api 怎么办？
      return;
    }

    return onAction && onAction(action, ctx, ...rest);
  }

  copyItem(index: number) {
    const {needConfirm} = this.props;
    const items = this.state.items.concat();

    if (needConfirm === false) {
      items.splice(index + 1, 0, items[index]);
    } else {
      // 复制相当于新增一行，需要同addItem一致添加__placeholder属性
      items.splice(index + 1, 0, {
        ...items[index],
        __isPlaceholder: true
      });
    }
    index = Math.min(index + 1, items.length - 1);

    this.addItem('above', index, true, {
      items,
    })
  }

  add = () => {
    const {needConfirm, scaffold, data} = this.props;
    const columns = this.resolveVariableProps(this.props, 'columns');
    let value: any = {
      __isPlaceholder: true
    };

    if (Array.isArray(columns)) {
      columns.forEach(column => {
        if (
          typeof column.value !== 'undefined' &&
          typeof column.name === 'string'
        ) {
          if (
            'type' in column &&
            (column.type === 'input-date' ||
              column.type === 'input-datetime' ||
              column.type === 'input-time' ||
              column.type === 'input-month' ||
              column.type === 'input-quarter' ||
              column.type === 'input-year')
          ) {
            const date = filterDate(column.value, data, column.format || 'X');
            setVariable(
              value,
              column.name,
              (column.utc ? moment.utc(date) : date).format(
                column.format || 'X'
              )
            );
          } else {
            /** 如果value值设置为表达式，则忽略 */
            if (!isExpression(column.value)) {
              setVariable(value, column.name, column.value);
            }
          }
        }
      });
    }

    value = merge({}, value, scaffold);

    if (needConfirm === false) {
      delete value.__isPlaceholder;
    }

    return value;
  }

  getIdxAndUpdateTreeNode (tree: any, indexPath: any, newValue: any) {
    if (!tree || !indexPath.length) {
      return;
    }

    const index = parseInt(indexPath?.shift());

    if (index >= tree.length) {
      return;
    }

    const itm = tree[index];

    if (indexPath.length === 0) {
      itm.children = itm?.children || [];
      const val = Array.isArray(newValue) ? newValue : [newValue];
      itm.children.push(...val);
      const addIdx = itm.children.length - 1;
      return addIdx;
    }

    this.getIdxAndUpdateTreeNode(itm.children, indexPath, newValue);
  }

  async addItem(
    type: string, // 'above' 新增到当前行 | 'blow' 新增到下一行
    index: number, // 发起插入时行索引
    isDispatch: boolean = true, // 是否需要 广播 'add' 事件
    options?: {
      items?: Array<any>; // 需要更新的items，不传则在指定位置添加空行
      ignoreStarEdit?: boolean // 确认模式下，忽略 进入编辑模式
    }
  ) {
    const { needConfirm, validateOnPageChange, env } = this.props;
    const { perPage, page = 1 } = this.state

    const insertIdx = type === 'above' ? index : index + 1;

    let items = options?.items

    if (!items) {
      items = this.state.items.concat();
      const value: any = this.add();

      items.splice(insertIdx, 0, value);
      index = Math.min(insertIdx, items.length - 1);
    }

    // 添加项超出最大项时不处理
    const maxLength = this.resolveVariableProps(this.props, 'maxLength');
    if (maxLength && items.length > maxLength) {
      env.notify('warning', `组合表单数量不能超出${maxLength}个`)
      return
    }

    // 新增项，跳转到指定页面逻辑
    let needValidate = false
    let goPageNo: number | undefined // 需要的跳转页码
    if (typeof perPage === 'number' && perPage && items.length >= perPage) {
      // 新增项起始页码
      const pageNo = Math.ceil((insertIdx + 1) / perPage)
      if (pageNo !== page) {
        goPageNo = pageNo
      }
      // 非确认模式， 开启 validateOnPageChange，切换分页时
      needValidate =  !needConfirm && goPageNo && validateOnPageChange
      if (needValidate) {
        const isFail = await this.validate(false)
        if (isFail) {
          return
        }
      }
    }

    // 清除校验状态（否则，新增项会展示校验状态）
    if (needValidate) {
      Object.values(this.subForms).map((subForm: any) => {
        subForm?.clearErrors?.()
      })
    }

    this.setState(
      {
        items,
      },
      async () => {
        if (goPageNo) {
          await this.handlePageChange(goPageNo, perPage!, {ignoreValidate: true})
        }
        if (isDispatch) {
          const isPrevented = await this.dispatchEvent('add', {index});
          if (isPrevented) {
            return;
          }
        }
        if (needConfirm === false) {
          this.emitValue();
        } else if (!options?.ignoreStarEdit) {
          this.startEdit(index, true);
        } else {
          // issue894 这个是修复通过addItem动作添加多行数据的情况下视图上添加上了，但是数据域中没有添加上的场景
          this.emitValue();
        }
      }
    );

    // 阻止触发 onAction 动作
    // 因为 footerAddButton 的 onClick 也绑定了这个
    // Action 会先触发 onClick，没被阻止就会 onAction
    // 而执行 onAction 的话，dialog 会监控所有的 onAction
    // onAction 过程中会下发 disabled: true
    // 所以重新构建 buildColumns 的结果就是表单项都不可点了
    return false;
  }

  async addChildrenItem(isDispatch: boolean = true, index: number, row: any) {
    const {needConfirm} = this.props;
    let items = this.state.items.concat();
    const value: any = this.add();

    const pathArr = row.path.split('.');
    let cache = JSON.parse(JSON.stringify(items))
    const addIdx = this.getIdxAndUpdateTreeNode(cache, pathArr, value)
    items = cache;

    const params: any = needConfirm === false ? {
        items,
        isCreateModeOfSubTable: true,
        subTableOperationIndex: index,
        subTableAddRowIndex: addIdx,
      } : {
        items,
        isCreateModeOfSubTable: true,
        subTableOperationIndex: index,
        subTableAddRowIndex: addIdx,
      }
    this.setState(
      params,
      async () => {
        if (isDispatch) {
          const isPrevented = await this.dispatchEvent('add', {addIdx});
          if (isPrevented) {
            return;
          }
        }
        if (needConfirm === false) {
          this.emitValue();
        }
      }
    );
  }

   /**
   * 点击“编辑”按钮
   * @param index 编辑的行索引
   */
  async editItem(index: number) {
    const {items} = this.state;
    const item = items[index];
    const isPrevented = await this.dispatchEvent('edit', {index, item});
    !isPrevented && this.startEdit(index, true);
  }

  /**
   * 派发事件
   * @param eventName 事件名称
   * @param eventData 事件数据
   * @returns
   */
  async dispatchEvent(eventName: string, eventData: any = {}) {
    const {dispatchEvent} = this.props;
    const {items} = this.state;
    const rendererEvent = await dispatchEvent(
      eventName,
      resolveEventData(this.props, {
        value: [...items],
        ...eventData
      })
    );

    return !!rendererEvent?.prevented;
  }

  startEdit(index: number, isCreate: boolean = false) {
    this.setState({
      editIndex: index,
      isCreateMode: isCreate,

      columns: this.buildColumns(this.props, isCreate)
    });
  }

  async confirmEdit() {
    const {addApi, updateApi, data, env, translate: __} = this.props;
    const {perPage} = this.state

    // form 是 lazyChange 的，先让他们 flush, 即把未提交的数据提交。
    const subForms: Array<any> = [];
    Object.keys(this.subForms).forEach(
      key => this.subForms[key] && subForms.push(this.subForms[key])
    );
    subForms.forEach(form => form.flush());

    const page = this.state.page || 1;
    let offset = 0;
    if (perPage && typeof perPage === 'number') {
      offset = (page - 1) * perPage;
    }

    const validateForms: Array<any> = [];
    Object.keys(this.subForms).forEach(key => {
      // key = ${path}-${colIndex}-${rowIndex}-${columnPosition}，取 rowIndex 与 editIndex 对比
      const arr = key.split('-').slice(-2); // 取得[rowIndex, columnPosition]
      const num = +arr[0] + offset; // 切换分页后，index计算应考虑页码，不能仅考虑当前页
      if (num === this.state.editIndex && this.subForms[key]) {
        validateForms.push(this.subForms[key]);
      }
    });

    const results = await Promise.all(
      validateForms.map(item => item.validate())
    );

    // 有校验不通过的
    if (~results.indexOf(false)) {
      return;
    }

    const items = this.state.items.concat();
    let item = {
      ...items[this.state.editIndex]
    };
    const isNew = !!item.__isPlaceholder;
    const confirmEventName = isNew ? 'addConfirm' : 'editConfirm';
    let isPrevented = await this.dispatchEvent(confirmEventName, {
      index: this.state.editIndex,
      item
    });
    if (isPrevented) {
      return;
    }

    let remote: Payload | null = null;
    let apiMsg = undefined;
    if (isNew && isEffectiveApi(addApi, createObject(data, item))) {
      remote = await env.fetcher(addApi, createObject(data, item));
      apiMsg = (addApi as ApiObject)?.messages?.failed;
    } else if (isEffectiveApi(updateApi, createObject(data, item))) {
      remote = await env.fetcher(updateApi, createObject(data, item));
      apiMsg = (updateApi as ApiObject)?.messages?.failed;
    }

    if (remote && !remote.ok) {
      env.notify('error', apiMsg ?? (remote.msg || __('saveFailed')));
      const failEventName = isNew ? 'addFail' : 'editFail';
      this.dispatchEvent(failEventName, {
        index: this.state.editIndex,
        item,
        error: remote
      });
      return;
    } else if (remote && remote.ok) {
      item = merge(
        {},
        ((isNew ? addApi : updateApi) as ApiObject).replaceData ? {} : item,
        remote.data
      );
    }

    delete item.__isPlaceholder;
    items.splice(this.state.editIndex, 1, item);

    // 如果props.isCreateModeOfSubTable: true，代表当前保存行是新增子级的新增行，
    // 点击保存，需更新父级isCreateModeOfSubTable、subTableAddRowIndex 数据
    if(this.props.isCreateModeOfSubTable) {
      this.props.updateParentEditState({
        isCreateModeOfSubTable: false,
        subTableAddRowIndex: -1,
      })
    }

    this.setState(
      {
        editIndex: -1,
        isCreateMode: false,
        items: items,
        columns: this.buildColumns(this.props),
        lastModifiedRow: undefined, // 编辑结束，清空缓存值
      },
      async () => {
        const isPrevented = await this.emitValue();
        if (isPrevented) {
          return;
        }
        const successEventName = isNew ? 'addSuccess' : 'editSuccess';
        this.dispatchEvent(successEventName, {
          index: this.state.editIndex,
          item
        });
      }
    );
  }

  cancelEdit() {
    const items = this.state.items.concat();
    const lastModifiedRow = this.state.lastModifiedRow;

    const item = {
      ...items[this.state.editIndex]
    };

    // 新增行时，点击取消，删除该行数据，其他情况，仅取消编辑态。
    const isNew = !!item.__isPlaceholder;
    if (isNew) {
      items.splice(this.state.editIndex, 1);
    } else {
      /** 恢复编辑前的值 */
      if (
        lastModifiedRow &&
        ~lastModifiedRow?.index &&
        isObject(lastModifiedRow?.data)
      ) {
        items.splice(this.state.editIndex, 1, lastModifiedRow.data);
      }
    }

    if (this.props.isCreateModeOfSubTable) {
      this.props.updateParentEditState({
        isCreateModeOfSubTable: false,
        subTableAddRowIndex: -1,
      })
    }
    this.setState(
      {
        editIndex: -1,
        isCreateMode: false,
        items: items,
        columns: this.buildColumns(this.props),
        lastModifiedRow: undefined // 编辑结束，清空缓存值
      }, this.emitValue
    );
  }

  removeNodeById = (tree: any[], targetId: any) => {
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i];
      if (node.id === targetId || node.key === targetId) {
        tree.splice(i, 1); // 找到并删除节点
        return;
      } else if (Array.isArray(node.children) && node.children.length > 0) {
        this.removeNodeById(node.children, targetId); // 递归检查子节点
      }
    }
  }


  async removeItem(index: number) {
    const {
      value,
      onChange,
      deleteApi,
      deleteConfirmText,
      env,
      data,
      translate: __,
      dispatchEvent,
      valueField = 'value'
    } = this.props;
    const {perPage} = this.state
    const page = this.state.page || 1;

    let newValue = Array.isArray(value) ? value.concat() : [];
    const item = newValue[index];

    if (!item) {
      return;
    }

    let isPrevented = await this.dispatchEvent('delete', {index, item});
    if (isPrevented) {
      return;
    }

    const ctx = createObject(data, item);
    if (isEffectiveApi(deleteApi, ctx)) {
      const confirmed = await env.confirm(
        deleteConfirmText ? filter(deleteConfirmText, ctx) : __('deleteConfirm')
      );
      if (!confirmed) {
        // 如果不确认，则跳过！
        return;
      }

      const result = await env.fetcher(deleteApi, ctx);

      if (!result.ok) {
        env.notify(
          'error',
          (deleteApi as ApiObject)?.messages?.failed ?? __('deleteFailed')
        );
        this.dispatchEvent('deleteFail', {index, item, error: result});
        return;
      }
    }

    this.removeEntry(item);
    newValue.splice(index, 1);


    // 删除需要删除掉selected
    const idx = this.state.selectedRows.findIndex(
      selectedItem => selectedItem[valueField] === item[valueField]
    );
    if (idx > -1) {
      const selectedRows = [...this.state.selectedRows];
      selectedRows.splice(idx, 1);
      // 从所有数据中过滤已选中的，剩余是未选中的
      const selectedValueList = selectedRows.map(item => item[valueField]);
      const unSelectedRows = newValue.filter(
        item => !selectedValueList.includes(item[valueField])
      );
      const rendererEvent = await dispatchEvent(
        'selectedChange',
        createObject(data, {
          selectedItems: selectedRows,
          unSelectedItems: unSelectedRows
        })
      );

      if (rendererEvent?.prevented) {
        return;
      }

      this.setState({
        selectedRows
      });
    }

    // 当前页数据删除完需要更新page，以自动调整到上一页，而不是仍停留当前页展示暂无数据
    if (typeof perPage === 'number' && perPage) {
      const lastPage = Math.ceil(newValue.length / perPage);
      if(lastPage < page) {
        this.setState({page: lastPage});
      }
    }

    const isChangePrevented = await this.emitValue(newValue);
    if (isChangePrevented) {
      return;
    }

    this.dispatchEvent('deleteSuccess', {value: newValue, index, item});
  }

  buildItemProps(item: any, index: number) {
    if (this.props.needConfirm === false) {
      return {
        quickEditEnabled: true
      };
    } else if (
      !this.props.editable &&
      !this.props.addable &&
      !this.state.isCreateMode
    ) {
      return null;
    }

    const {perPage} = this.state
    const page = this.state.page || 1;
    let offset = 0;
    if (typeof perPage === 'number' && perPage) {
      offset = (page - 1) * perPage;
    }

    return {
      quickEditEnabled: this.state.editIndex === index + offset
    };
  }

  buildColumns(props: TableProps, isCreateMode = false): Array<any> {
    const env = this.props.env;
    const result = this.resolveVariableProps(this.props, 'columns');
    let columns: Array<any> = Array.isArray(result) ? result.concat() : [];
    const ns = this.props.classPrefix;
    const __ = this.props.translate;
    const needConfirm = this.props.needConfirm;
    const showIndex = this.props.showIndex;
    const minLength = this.resolveVariableProps(this.props, 'minLength');
    const maxLength = this.resolveVariableProps(this.props, 'maxLength');
    const isStatic = this.props.static;
    const disabled = this.props.disabled;
    const {
      addable,
      addBtnIcon,
      addBtnLabel,
      showTableAddBtn,
      showAddChildrenBtn,
      addAboveBtnLabel,
      addBelowBtnLabel,
      addChildrenBtnIcon,
      addChildrenBtnLabel,
      tableAddBtnImmediateAdd,
      popOverContainerSelector,
    } = this.props;

    let btns = [];

    if (!isStatic && addable && (showTableAddBtn !== false || showAddChildrenBtn)) {
      btns.push({
        children: ({
          key,
          rowIndex,
          row,
          offset,
        }: {
          key: any;
          rowIndex: number;
          row: any;
          offset: number;
        }) => {
          const buttons = []

          const addAboveItemBtn = {
            "type": "button",
            "label": addAboveBtnLabel || "向上新增同级",
            "onClick": this.addItem.bind(this, 'above', rowIndex + offset, undefined)
              }

          const addBelowItemBtn = {
            "type": "button",
            "label": addBelowBtnLabel || "向下新增同级",
            "onClick": this.addItem.bind(this, 'below', rowIndex + offset, undefined)
          }

          const addChildrenBtn = showAddChildrenBtn && (
            <Button
              classPrefix={ns}
              key={key}
              level="link"
              tooltip={__('Table.subAddRow')}
              tooltipContainer={
                env && env.getModalContainer ? env.getModalContainer : undefined
              }
              disabled={disabled}
              // 直接新增时默认向下新增同级
              onClick={this.addChildrenItem.bind(this, undefined, rowIndex + offset, row)}
            >
              {addChildrenBtnIcon ? (
                <Icon
                  cx={props.classnames}
                  icon={addChildrenBtnIcon}
                  className="icon"
                />
              ) : null}
              {addChildrenBtnLabel ? (
                <span>{addChildrenBtnLabel}</span>
              ) : null}
            </Button>
          )

          rowIndex === 0 && showTableAddBtn !== false && buttons.push(addAboveItemBtn);
          showTableAddBtn !== false && buttons.push(addBelowItemBtn)

          const superRow = extendObject(props.store?.data, { index: rowIndex, __index: rowIndex, __depth: row.depth, offset, ...row.data });
          const addBtnHidden = props.tableAddBtnVisibleOn ? evalExpression(props.tableAddBtnVisibleOn, superRow) === false : false;
          const addChildrenBtnHidden = props.tableAddChildrenBtnVisibleOn ? evalExpression(props.tableAddChildrenBtnVisibleOn, superRow) === false : false;
          const addBtnDom = tableAddBtnImmediateAdd ? (
            <Button
              classPrefix={ns}
              key={key}
              level="link"
              tooltip={__('Table.addRow')}
              tooltipContainer={
                env && env.getModalContainer ? env.getModalContainer : undefined
              }
              disabled={disabled}
              // 直接新增时默认向下新增同级
              onClick={this.addItem.bind(this, 'below', rowIndex + offset, undefined)}
            >
              {addBtnIcon ? (
                <Icon
                  cx={props.classnames}
                  icon={addBtnIcon}
                  className="icon"
                />
              ) : null}
              {addBtnLabel ? (
                <span>{addBtnLabel}</span>
              ) : null}
            </Button>
          ) : (
            <div>
              {this.props.render('dropdown-button', {
                "className": "add-btns-wrap",
                "type": "dropdown-button",
                "label": addBtnLabel,
                "level": "link",
                "hideCaret": true,
                "tooltip": __("Table.addRow"),
                "closeOnClick": true,
                "align": "right",
                "buttons": buttons,
                "disabled": disabled,
                "popOverContainerSelector": popOverContainerSelector || 'body',
                // 保证和tableAddBtnImmediateAdd时使用的icon都是svg新增图标，fa是i标签图标
                ...(addBtnIcon ? { "icon": addBtnIcon === 'plus' ? 'plus' : `fa fa-${addBtnIcon}` } : {})
              }, {})}
            </div>
          )

          const isEditing = this.state.editIndex > -1 && needConfirm !== false

          if (isEditing) {
            return null
          }

          return (
            <>
              {maxLength <= this.state.items.length || addBtnHidden ? null : addBtnDom}
              {addChildrenBtnHidden ? null : addChildrenBtn}
            </>
          )
        }
      });
    }

    if (!isStatic && props.copyable && props.showCopyBtn !== false) {
      btns.push({
        children: ({
          key,
          rowIndex,
          offset
        }: {
          key: any;
          rowIndex: number;
          offset: number;
        }) =>
          ~this.state.editIndex && needConfirm !== false ? null : (
            <Button
              classPrefix={ns}
              key={key}
              level="link"
              tooltip={__('Table.copyRow')}
              tooltipContainer={
                env && env.getModalContainer ? env.getModalContainer : undefined
              }
              disabled={disabled}
              onClick={this.copyItem.bind(this, rowIndex + offset, undefined)}
            >
              {props.copyBtnIcon ? (
                <Icon
                  cx={props.classnames}
                  icon={props.copyBtnIcon}
                  className="icon"
                />
              ) : null}
              {props.copyBtnLabel ? <span>{props.copyBtnLabel}</span> : null}
            </Button>
          )
      });
    }

    if (props.needConfirm === false) {
      columns = columns.map(column => {
        const quickEdit = column.quickEdit;

        return quickEdit === false
          ? omit(column, ['quickEdit'])
          : {
              ...column,
              quickEdit: {
                ...this.columnToQuickEdit(column),
                ...quickEdit,
                saveImmediately: true,
                mode: 'inline',
                disabled,
                static: isStatic
              }
            };
      });
    } else if (!isStatic && props.addable || props.editable || isCreateMode) {
      columns = columns.map(column => {
        const quickEdit =
          !isCreateMode && column.hasOwnProperty('quickEditOnUpdate')
            ? column.quickEditOnUpdate
            : column.quickEdit;

        const render = getRendererByName(column?.type);

        return quickEdit === false
          ? omit(column, ['quickEdit'])
          : {
              ...column,
              quickEdit: {
                ...this.columnToQuickEdit(column),
                ...quickEdit,
                isQuickEditFormMode: !!render?.isFormItem,
                saveImmediately: true,
                mode: 'inline',
                disabled
              }
            };
      });

      !isStatic &&
        props.editable &&
        btns.push({
          children: ({
            key,
            rowIndex,
            row,
            data,
            offset
          }: {
            key: any;
            rowIndex: number;
            row: any;
            data: any;
            offset: number;
            }) => {
            const superRow = extendObject(props.store?.data, { index: rowIndex, __index: rowIndex, offset, ...row.data });
            const editBtnHidden = props.tableEditBtnVisibleOn ? evalExpression(props.tableEditBtnVisibleOn, superRow) === false : false;
            return ~this.state.editIndex || (data && data.__isPlaceholder) || editBtnHidden ? null : (
              <Button
                classPrefix={ns}
                key={key}
                level="link"
                tooltip={__('Table.editRow')}
                tooltipContainer={
                  env && env.getModalContainer
                    ? env.getModalContainer
                    : undefined
                }
                disabled={disabled}
                onClick={() => this.editItem(rowIndex + offset)}
              >
                {/* 兼容之前的写法 */}
                {typeof props.updateBtnIcon !== 'undefined' ? (
                  props.updateBtnIcon ? (
                    <Icon
                      cx={props.classnames}
                      icon={props.updateBtnIcon}
                      className="icon"
                    />
                  ) : null
                ) : props.editBtnIcon ? (
                  <Icon
                    cx={props.classnames}
                    icon={props.editBtnIcon}
                    className="icon"
                  />
                ) : null}
                {props.updateBtnLabel || props.editBtnLabel ? (
                  <span>{props.updateBtnLabel || props.editBtnLabel}</span>
                ) : null}
              </Button>
            )
          }
        });

      !isStatic &&
        btns.push({
          children: ({
            key,
            rowIndex,
            offset
          }: {
            key: any;
            rowIndex: number;
            offset: number;
          }) => {
            return (
              <>
                {
                  this.state.editIndex === rowIndex + offset ? (
                  <Button
                    classPrefix={ns}
                    key={key}
                    level="link"
                    tooltip={__('save')}
                    tooltipContainer={
                      env && env.getModalContainer
                        ? env.getModalContainer
                        : undefined
                    }
                    onClick={this.confirmEdit}
                  >
                    {props.confirmBtnIcon ? (
                      <Icon
                        cx={props.classnames}
                        icon={props.confirmBtnIcon}
                        className="icon"
                      />
                    ) : null}
                    {props.confirmBtnLabel ? (
                      <span>{props.confirmBtnLabel}</span>
                    ) : null}
                  </Button>
                ) : null
                }
              </>
            )
          }

        });

      !isStatic &&
        btns.push({
          children: ({
            key,
            rowIndex,
            offset
          }: {
            key: any;
            rowIndex: number;
            offset: number;
          }) =>
            this.state.editIndex === rowIndex + offset ? (
              <Button
                classPrefix={ns}
                key={key}
                level="link"
                tooltip={__('cancel')}
                tooltipContainer={
                  env && env.getModalContainer
                    ? env.getModalContainer
                    : undefined
                }
                onClick={this.cancelEdit}
              >
                {props.cancelBtnIcon ? (
                  <Icon
                    cx={props.classnames}
                    icon={props.cancelBtnIcon}
                    className="icon"
                  />
                ) : null}
                {props.cancelBtnLabel ? (
                  <span>{props.cancelBtnLabel}</span>
                ) : null}
              </Button>
            ) : null
        });
    } else {
      columns = columns.map(column => {
        const render = getRendererByName(column?.type);
        if (!!render?.isFormItem) {
          return {
            ...column,
            quickEdit: {
              ...column,
              isFormMode: true
            }
          };
        }
        return column;
      });
    }

    if (!isStatic && props.removable) {
      btns.push({
        children: ({
          key,
          rowIndex,
          row,
          data,
          offset
        }: {
          key: any;
          rowIndex: number;
          row: any;
          data: any;
          offset: number;
          }) => {
          const superRow = extendObject(props.store?.data, { index: rowIndex, __index: rowIndex, offset, ...row.data });
          const deleteBtnHidden = props.tableDeleteBtnVisibleOn ? evalExpression(props.tableDeleteBtnVisibleOn, superRow) === false : false;

          return ((~this.state.editIndex || (data && data.__isPlaceholder)) && needConfirm !== false) ||
            minLength >= this.state.items.length || deleteBtnHidden ? null : (
            <Button
              classPrefix={ns}
              key={key}
              level="link"
              tooltip={__('Table.deleteRow')}
              tooltipContainer={
                env && env.getModalContainer ? env.getModalContainer : undefined
              }
              disabled={disabled}
              onClick={this.removeItem.bind(this, rowIndex + offset)}
            >
              {props.deleteBtnIcon ? (
                <Icon
                  cx={props.classnames}
                  icon={props.deleteBtnIcon}
                  className="icon"
                />
              ) : null}
              {props.deleteBtnLabel ? (
                <span>{props.deleteBtnLabel}</span>
              ) : null}
            </Button>
          )
        }
      });
    }

    let idx = columns.findIndex(item => item.type === 'operation');
    let operation = columns[idx];
    if (operation?.hasOwnProperty('quickEdit')) {
      // 防止操作列变成输入框
      delete operation.quickEdit;
    }
    // 存在内置操作按钮
    if (btns.length) {
      if (idx === -1) {
        operation = {
          type: 'operation',
          buttons: [],
          label: __('Table.operation'),
          className: 'v-middle nowrap',
          fixed: 'right',
          width: '1%',
          innerClassName: 'm-n'
        };
        columns.push(operation);
      } else {
        // #1163 拷贝一层operation，避免直接修改原有的
        operation = {
          ...operation
        };
        columns.splice(idx, 1, operation);
      }

      operation.buttons = Array.isArray(operation.buttons)
        ? operation.buttons.concat()
        : [];
      operation.buttons.unshift.apply(operation.buttons, btns);
      operation.collapseOnExceed = operation.buttons.length;
    }

    if (showIndex) {
      columns.unshift({
        label: __('Table.index'),
        width: '1%',
        children: (props: any) => {
          return <td>{props.offset + props.data.index + 1}</td>;
        }
      });
    }

    return columns;
  }

  columnToQuickEdit(column: any) {
    const quickEdit: any = {
      type: 'input-text'
    };

    if (
      (column.type &&
        /^input\-|(?:select|picker|checkbox|checkboxes|editor|transfer|radios)$/i.test(
          column.type
        )) ||
      ~['textarea', 'combo', 'condition-builder', 'group'].indexOf(column.type)
    ) {
      return {
        ...column,
        label: ''
      };
    }

    return quickEdit;
  }

  handleTableSave(
    rows: Array<object> | object,
    diff: Array<object> | object,
    rowIndexes: Array<string> | string
  ) {
    const {perPage} = this.state
    const lastModifiedRow = this.state.lastModifiedRow;

    if (~this.state.editIndex) { //确认模式下编辑场景，editIndex值不为-1。
      const items = this.state.items.concat();
      const origin = items[this.state.editIndex];

      if (!origin) {
        return;
      }

      const value: any = {
        ...rows
      };
      this.entries.set(value, this.entries.get(origin) || this.entityId++);
      this.entries.delete(origin);
      items.splice(this.state.editIndex, 1, value);

      this.setState({
        items,
        // 记录最近一次编辑记录，用于取消编辑数据回溯
        ...(lastModifiedRow?.index === this.state.editIndex
          ? {}
          : {
              lastModifiedRow: origin.__isPlaceholder
                ? undefined
                : {index: this.state.editIndex, data: {...origin}}
            })
      });
      return;
    }

    const page = this.state.page;
    // 子表格编辑此操作是为了后续执行时能去除__super，不放在计算内
    let items = this.props.parent
      ? this.state.items.concat().reduce((arr, item) => {
          arr.push({...item});
          return arr;
        }, [])
      : this.state.items.concat();

      // 通过场景分析和代码分析，不会出现rows是Array的情况，先注释掉此段逻辑。
    // if (Array.isArray(rows)) {
    //   (rowIndexes as Array<string>).forEach((rowIndex, index) => {
    //     const indexes = rowIndex.split('.').map(item => parseInt(item, 10));

    //     if (page && page > 1 && typeof perPage === 'number') {
    //       indexes[0] += (page - 1) * perPage;
    //     }
    //     const origin = getTree(items, indexes);
    //     const data = merge({}, origin, (diff as Array<object>)[index]);

    //     items = spliceTree(items, indexes, 1, data);
    //   });
    // } else {
      const indexes = (rowIndexes as string)
        .split('.')
        .map(item => parseInt(item, 10));

      if (page && page > 1 && typeof perPage === 'number') {
        indexes[0] += (page - 1) * perPage;
      }

      const origin = getTree(items, indexes);
      const data: any = {...rows}; // #1128 参考百度amis pr: https://github.com/baidu/amis/pull/10247

      items = spliceTree(items, indexes, 1, data);
      this.entries.set(data, this.entries.get(origin) || this.entityId++);
    // }

    this.setState(
      {
        items,
      },
      this.emitValue
    );
  }

  // 不会走到emitValue
  handleSaveTableOrder(moved: Array<object>, rows: Array<object>) {
    const {onChange} = this.props;

    onChange(rows.map((item: object) => ({...item})));
  }

  async handlePageChange(
    page: number,
    pageSize: number,
    options?: { ignoreValidate?: boolean }
  ) {
    const { validateOnPageChange, paginationConfig, expandConfig } = this.props;

    // 分页改变触发函数
    const onPageChange = paginationConfig?.onPageChange
    const {perPage} = this.state

    // perPage 不等于 pageSize 代表切换页码 不需要校验，直接切换
    // 为了和CRUD保持一致，切换 pageSize 的时候 page 重置为1
    if(perPage !== pageSize){
      this.setState({page:1, perPage:pageSize});
      onPageChange?.(1, pageSize)
    }

    const curPage = this.state.page || 1;
    const { editIndex } = this.state;
    const isCurPageEditing = perPage && editIndex < (curPage * perPage);

    if (!options?.ignoreValidate && validateOnPageChange) {
      const isFail = await this.validate(!!isCurPageEditing);
      // 校验通过后再执行切换分页操作
      if(!isFail){
        this.setState({page});
        onPageChange?.(page, pageSize)
      }
      return
    }

    // 未配置 keepExpanded 时，切换分页默认关闭所有展开项目
    if (!expandConfig?.keepExpanded && this.tableStore) {
      this.tableStore.toggleExpandAll(false)
    }

    this.setState({page});
    onPageChange?.(page, pageSize)
  }

  /**
   * Table Row中数据更新到InputTable中
   * 解决columns形如[{name: 'a'}, {name: 'c', value: '${a}'}]时，使用默认值的列数据无法更新到数据域的问题
   *
   * @param data 行数据
   * @param rowIndex 行索引值
   */
  @autobind
  handlePristineChange(data: Record<string, any>, rowIndex: string) {
    const {needConfirm} = this.props;
    const indexes = rowIndex.split('.').map(item => parseInt(item, 10));

    this.setState(
      prevState => {
        let items = prevState.items.concat();
        const page = prevState.page;
        const perPage = prevState.perPage;

        if (page && page > 1 && typeof perPage === 'number') {
          indexes[0] += (page - 1) * perPage;
        }
        const origin = getTree(items, indexes);
        const value = {
          ...origin,
          ...data
        };
        this.entries.set(value, this.entries.get(origin) || this.entityId++);
        this.entries.delete(origin);
        items = spliceTree(items, indexes, 1, value);

        return {
          items
        };
      },
      () => {
        // 只针对非确认模式，确认模式表单初始化后不会走这里，因为不会渲染出来TableCell表单
        if (needConfirm === false) {
          this.emitValue();
        }
      }
    );
  }

  removeEntry(entry: any) {
    if (this.entries.has(entry)) {
      this.entries.delete(entry);
    }
  }

  // 此处嵌套子表格，children发生变化，会重新生成id, 影响子表格输入时会失焦
  getEntryId(entry: any, _index: number) {
    if (!this.entries.has(entry)) {
      this.entries.set(entry, this.entityId++);
    }
    return String(this.entries.get(entry));
  }

  tableRef(ref: any) {
    while (ref && ref.getWrappedInstance) {
      ref = ref.getWrappedInstance();
    }

    this.tableStore = ref?.props?.store;
  }

  /**
   * FIX issue#483 计算selectedRows的逻辑
   */
  computedSelectedRows(selectedItems: any[], unSelectedItems: any[]) {
    const { valueField = 'value' } = this.props;
    const {perPage} = this.state
    let selectedRows = selectedItems;
    let unSelectedRows = unSelectedItems;

    let items = this.state.items;
    const page = this.state.page || 1;

    if (typeof perPage === 'number' && perPage && items.length > perPage) {
      // 判断state.selectedRows中哪些数据不是当前页的
      items = items.slice((page - 1) * perPage, page * perPage);
      const itemsValueList = items.map(item => item[valueField]);
      const otherPageSelectedRows = this.state.selectedRows.filter(
        selectedItem =>
          !itemsValueList.includes(selectedItem[valueField || 'value'])
      );

      selectedRows = otherPageSelectedRows.concat(selectedRows)!;
      // 从所有数据中过滤已选中的，剩余是未选中的
      const selectedValueList = selectedRows.map(item => item[valueField]);
      unSelectedRows = this.state.items.filter(
        item => !selectedValueList.includes(item[valueField])
      );
    }

    return {
      selectedRows,
      unSelectedRows
    }
  }

  /**
   * FIX issue#483 用于传给下层table组件
   * 将selectedRows更新逻辑与派发事件逻辑抽离
   */
  handleSelect(selectedItems: any[], unSelectedItems: any[]) {
    const { selectedRows } = this.computedSelectedRows(selectedItems, unSelectedItems);

    this.setState({
      selectedRows,
    });
  }

  /**
   * FIX issue#483 用于传给下层table组件派发selectedChange事件
   * 不会走emitValue
   */
  async handleSelectedChange(selectedItems: any[], unSelectedItems: any[]) {
    const { dispatchEvent, data } = this.props;
    const { selectedRows, unSelectedRows } = this.computedSelectedRows(
      selectedItems,
      unSelectedItems,
    );

    const rendererEvent = await dispatchEvent(
      'selectedChange',
      createObject(data, {
        selectedItems: selectedRows,
        unSelectedItems: unSelectedRows
      })
    );

    if (rendererEvent?.prevented) {
      return;
    }

    this.setState({
      selectedRows
    });
  }

  render() {
    const {
      className,
      style,
      value,
      disabled,
      render,
      placeholder,
      draggable,
      addable,
      columnsTogglable,
      combineNum,
      combineFromIndex,
      translate: __,
      formItem,
      canAccessSuperData,
      expandConfig,
      mountAll,
      affixRow,
      prefixRow,
      formInited,
      classnames: cx,
      rowClassName,
      rowClassNameExpr,
      affixHeader = false,
      autoFillHeight = false,
      tableContentClassName,
      static: isStatic,
      showFooterAddBtn,
      extraFooterConfig,
      showTableAddBtn,
      footerAddBtn,
      selectable,
      multiple,
      selected,
      valueField,
      maxKeepItemSelectionLength,
      isPublicHeader,
      onChange,
      showExpansionColumn,
      footerToolbarClassName,
      updateAllRows,
      transferAllData,
      alwaysShowPagination,
      paginationConfig,
      reUseRow,
      batchNum,
      quickEditFormRef,
      subTable: propSubTable,
    } = this.props;

    const { perPage,subTable, isCreateModeOfSubTable, subTableOperationIndex, subTableAddRowIndex } = this.state;
    const maxLength = this.resolveVariableProps(this.props, 'maxLength');

    if (formInited === false) {
      return null;
    }

    let items = this.state.items;
    let selectedRows = this.state.selectedRows || [];
    let showPager = false;
    let page = this.state.page || 1;
    let offset = 0;
    let lastPage = 1;
    // 总数
    const total = items.length || 0;

    // alwaysShowPagination 设置为true 代表分页器要一直展示
    if (typeof perPage === 'number' && perPage && (
      items.length > perPage || alwaysShowPagination
    )) {
      lastPage = Math.ceil(items.length / perPage);
      // 当前页码大于最大页码时，同步为最大页码
      // FIX: 确认模式新增时，跳转到最后一页后，再点击取消未移出多余页码  https://github.com/baidu/amis/pull/8327
      if (page > lastPage) {
        page = lastPage;
      }
      items = items.slice((page - 1) * perPage, page * perPage);
      const itemsValueField = items.map(item => item[valueField || 'value']);
      selectedRows = selectedRows.filter(selectedItem =>
        itemsValueField.includes(selectedItem[valueField || 'value'])
      );
      showPager = true;
      offset = (page - 1) * perPage;
    }

    const showFooterToolbar = !isStatic && addable && showFooterAddBtn !== false && (!maxLength || maxLength > this.state.items.length);

    return (
      <div className={cx('InputTable', className, {'is-public-header': subTable?.isPublicHeader || isPublicHeader })} ref={this.tableContainerRef}>
        {render(
          'body',
          {
            type: 'table',
            className: 'input-table',
            tableKey: "input-table",
            placeholder: __(placeholder),
            columns: this.state.columns,
            items: items,
            formItem,
            affixHeader,
            prefixRow,
            affixRow,
            affixOffsetTop:
              this.props.affixOffsetTop ?? this.props.env.affixOffsetTop ?? 0,
            autoFillHeight,
            tableContentClassName,
            selectable: selectable && !~this.state.editIndex,
            multiple,
            selected: selectedRows,
            valueField,
            maxKeepItemSelectionLength,
            isPublicHeader,
            showExpansionColumn
          },
          {
            ref: this.tableRef,
            value: undefined,
            saveImmediately: true,
            disabled,
            draggable: draggable && !~this.state.editIndex,
            getEntryId: this.getEntryId,
            // 如果开启选中模式，则配置为match，表示匹配复用
            reUseRow: selectable ? "match" : reUseRow, // 寻找 id 相同的行，更新数据
            batchNum,
            onSave: this.handleTableSave,
            onSaveOrder: this.handleSaveTableOrder,
            buildItemProps: this.buildItemProps,
            // issue#1068 打补丁，quickEditFormRef 只针对 subTable 嵌套子表格生效，后续应该重新设计未加载dom的校验
            quickEditFormRef: (propSubTable !== undefined && quickEditFormRef) || this.subFormRef,
            columnsTogglable: columnsTogglable,
            combineNum: combineNum,
            combineFromIndex: combineFromIndex,
            expandConfig,
            mountAll,
            canAccessSuperData,
            // reUseRow: false,
            offset,
            rowClassName,
            rowClassNameExpr,
            // TODO: 这里是为了处理columns里使用value变量添加的，目前会影响初始化数据加载后的组件行为，先回滚
            // fix：issue#553、https://github.com/baidu/amis/issues/6838
            onPristineChange: this.handlePristineChange,
            updateParentEditState: this.updateParentEditState,
            onSelect: this.handleSelectedChange,
            handleSelect: this.handleSelect,
            onChange,
            subTable,
            subTableOperationIndex,
            subTableAddRowIndex,
            isCreateModeOfSubTable,
            updateAllRows,
          }
        )}
        {showFooterToolbar || extraFooterConfig || showPager ? (
          <div className={cx('InputTable-toolbar')}>
            <div className={cx('InputTable-toolbar-left', footerToolbarClassName)}>
              {showFooterToolbar
                ? render(
                    'button',
                    {
                      type: 'button',
                      level: 'primary',
                      size: 'sm',
                      label: __('Table.add'),
                      icon: 'fa fa-plus',
                      disabledTip: __('Table.addButtonDisabledTip'),
                      ...((footerAddBtn as any) || {}),
                    },
                    {
                      disabled: disabled || !!~this.state.editIndex,
                      onClick: () => this.addItem('below', this.state.items.length - 1)
                    }
                  )
                : null}

              {extraFooterConfig
                ? render(
                  'button',
                  extraFooterConfig,
                  {
                    // 自定义左下角总结行和按钮时，按钮在编辑态的行为与默认新增按钮保持一致 issue438
                    disabled: disabled || !!~this.state.editIndex,
                  }
                  )
                : null}
            </div>
            {showPager
              ? render(
                  'pager',
                  {
                    type: 'pagination'
                  },
                  {
                    ...(paginationConfig || {}),
                    perPage: perPage,
                    total: total,
                    activePage: page,
                    lastPage: lastPage,
                    onPageChange: this.handlePageChange,
                    className: 'InputTable-pager'
                  }
                )
              : null}
          </div>
        ) : null}
      </div>
    );
  }
}

@FormItem({
  type: 'input-table'
})
export class TableControlRenderer extends FormTable {
  setData(value: any, replace?: boolean, index?: number) {
    if (index !== undefined && ~index) {
      // 如果setValue动作传入了index，更新指定索引的值
      const items = [...this.state.items];
      items.splice(index, 1, value);

      this.setState({items, page: 1}, () => {
        this.emitValue();
      });
    } else {

      // 如果setValue动作没有传入index，则直接替换组件数据
      this.setState(
        {
          items: [...value],
          page: 1
        },
        () => {
          this.emitValue();
        }
      );
    }
  }

  async doAction(
    action: ListenerAction | ActionObject,
    args: any,
    ...rest: Array<any>
  ) {
    const {
      valueField,
      env,
      needConfirm,
      addable,
      addApi,
      deleteApi,
      resetValue,
      translate: __,
      onChange,
      data,
    } = this.props;

    const actionType = action.actionType as string;
    const ctx = this.props.store?.data || {}; // 获取当前上下文数据

    if (actionType === 'addItem') {
      return this.addAction(actionType, action, args);
    } else if (actionType === 'addChildrenItem') {
      if (addable === false) {
        return;
      }

      let items = this.state.items.concat();

      if (addApi || args) {
        let toAdd: any = null;

        if (isEffectiveApi(addApi, ctx)) {
          const payload = await env.fetcher(addApi, ctx);
          if (payload && !payload.ok) {
            env.notify(
              'error',
              (addApi as ApiObject)?.messages?.failed ??
                (payload.msg || __('fetchFailed'))
            );
            return;
          } else if (payload && payload.ok) {
            toAdd = payload.data;
          }
        } else {
          toAdd = args.item;
        }

        const pathArr = args.path.split('.');
        let cache = JSON.parse(JSON.stringify(items))
        const addIdx = this.getIdxAndUpdateTreeNode(cache, pathArr, toAdd)
        items = cache;

        this.setState(
          {
            items,
            isCreateModeOfSubTable: true,
            subTableOperationIndex: args.subTableIndex,
            subTableAddRowIndex: addIdx,
          },
          () => {
            if (needConfirm === false) {
              onChange?.(items);
            }
          }
        );
        return;
      } else {
        return this.addChildrenItem(false, args.subTableIndex, {...args, path: args.path, index: args.subTableIndex });
      }
    } else if (actionType === 'deleteItem') {
      let items = [...this.state.items];
      const deletedItems: any = [];

      if (args?.index !== undefined) {
        const indexs = String(args.index).split(',');
        indexs.forEach(i => {
          // 支持嵌套场景的删除
          const spliceIndexes = i.split('.').map(item => parseInt(item, 10));
          deletedItems.push(getTree(items, spliceIndexes));
          items = spliceTree(items, spliceIndexes, 1);
        });
      } else if (args?.condition !== undefined) {
        // 解决嵌套场景，从for循环遍历改成everyTree遍历树
        const promises: Array<() => Promise<any>> = [];
        everyTree(items, (item, index, level, paths, indexes) => {
          promises.unshift(async () => {
            const result = await evalExpressionWithConditionBuilder(
              args?.condition,
              createObject(data, {...item, rowIndex: index, level, paths, indexes})
            );

            if (result) {
              deletedItems.push(item);
              items = spliceTree(items, [...indexes, index], 1);
            }
          });

          return true;
        });
        await promises.reduce((p, fn) => p.then(fn), Promise.resolve());
      }

      // 删除api
      if (isEffectiveApi(deleteApi, createObject(ctx, {deletedItems}))) {
        const payload = await env.fetcher(
          deleteApi,
          createObject(ctx, {deletedItems})
        );
        if (payload && !payload.ok) {
          !(deleteApi as ApiObject)?.silent &&
            env.notify(
              'error',
              (deleteApi as ApiObject)?.messages?.failed ??
                (payload.msg || __('fetchFailed'))
            );
          return;
        }
      }
      this.setState(
        {
          items: items
        },
        () => {
          onChange?.(items);
        }
      );
      return;
    } else if (actionType === 'clear') {
      this.setState(
        {
          items: []
        },
        () => {
          onChange?.([]);
        }
      );
      return;
    } else if (actionType === 'reset') {
      const newItems = Array.isArray(resetValue) ? resetValue : [];
      this.setState(
        {
          items: newItems
        },
        () => {
          onChange?.(newItems);
        }
      );
      return;
    } else if (actionType === 'changePage') {
      this.setState({ page: args.page });
      return;
    } else if (actionType === 'setSelectedItems') {
      this.setState({ selectedRows: args.items });
      return;
    }
    return super.doAction(action as ActionObject, ctx, ...rest);
  }
}
