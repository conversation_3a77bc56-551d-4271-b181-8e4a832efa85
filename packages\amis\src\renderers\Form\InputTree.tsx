import React from 'react';
import memoize from 'memoize-one';
import { omit, debounce } from 'lodash';
import cx from 'classnames';
import {matchSorter} from 'match-sorter';
import {SpinnerExtraProps, Tree as TreeSelector, DropPosition} from 'amis-ui';
import type { IDropInfo } from 'amis-ui';
import {highlightForHtmlStr} from 'amis-core';
import {
  Option,
  OptionsControl,
  OptionsControlProps,
  autobind,
  createObject,
  ActionObject,
  isPureVariable,
  resolveVariableAndFilter,
  resolveEventData,
  toNumber,
  eachTree,
  someTree,
  evalExpression,
  findTreeIndex,
  spliceTree,
  getTreeParent,
} from 'amis-core';
import type {
  SchemaExpression,
  Api,
  BaseApiObject,
  SchemaNode
} from 'amis-core';
import {Spinner, SearchBox} from 'amis-ui';
import {FormOptionsSchema, SchemaApi} from '../../Schema';
import {supportStatic} from './StaticHoc';
import type {ItemRenderStates} from 'amis-ui/lib/components/Selection';
import { SchemaTpl } from '../../Schema';
import { IconSchema } from '../Icon';

/** 代表无 DropNode */
const NonDropNode = 'NonDropNode';
type INonDropNode = typeof NonDropNode;

/**
 * Tree 下拉选择框。
 * 文档：https://baidu.gitee.io/amis/docs/components/form/tree
 */
export interface TreeControlSchema extends FormOptionsSchema {
  type: 'input-tree';

  /**
   * 是否隐藏顶级
   */
  hideRoot?: boolean;

  /**
   * 顶级选项的名称
   */
  rootLabel?: string;

  /**
   * 顶级选项的值
   */
  rootValue?: any;

  /**
   * 显示图标
   */
  showIcon?: boolean;

  /**
   * ui级联关系，true代表级联选中，false代表不级联，默认为true
   */
  autoCheckChildren?: boolean;

  /**
   * 该属性代表数据级联关系，autoCheckChildren为true时生效，默认为false，具体数据级联关系如下：
   * 1.casacde为false，ui行为为级联选中子节点，子节点禁用；值只包含父节点的值
   * 2.cascade为false，withChildren为true，ui行为为级联选中子节点，子节点禁用；值包含父子节点的值
   * 3.cascade为true，ui行为级联选中子节点，子节点可反选，值包含父子节点的值，此时withChildren属性失效
   * 4.cascade不论为true还是false，onlyChildren为true，ui行为级联选中子节点，子节点可反选，值只包含子节点的值
   */
  cascade?: boolean;

  /**
   * 选父级的时候是否把子节点的值也包含在内。
   */
  withChildren?: boolean;

  /**
   * 选父级的时候，是否只把子节点的值包含在内
   */
  onlyChildren?: boolean;

  /**
   * 单选时，只运行选择叶子节点
   */
  onlyLeaf?: boolean;

  /**
   * 顶级节点是否可以创建子节点
   */
  rootCreatable?: boolean;

  /**
   * 是否开启节点路径模式
   */
  enableNodePath?: boolean;

  /**
   * 开启节点路径模式后，节点路径的分隔符
   */
  pathSeparator?: string;

  /**
   * 是否显示展开线
   */
  showOutline?: boolean;

  deferApi?: SchemaApi;

  /**
   * 需要高亮的字符串
   */
  highlightTxt?: string;

  /**
   * 是否为选项添加默认的Icon，默认值为true
   */
  enableDefaultIcon?: boolean;

  /**
   * 是否开启搜索
   */
  searchable?: boolean;

  /**
   * 搜索框的配置
   */
  searchConfig?: {
    /**
     * 搜索框外层CSS样式类
     */
    className?: string;
    /**
     * 占位符
     */
    placeholder?: string;

    /**
     * 是否为 Mini 样式。
     */
    mini?: boolean;

    /**
     * 是否为加强样式
     */
    enhance?: boolean;

    /**
     * 是否可清除
     */
    clearable?: boolean;

    /**
     * 是否立马搜索。
     */
    searchImediately?: boolean;

    /**
     * 搜索框是否吸顶
     */
    sticky?: boolean;
  };

  /**
   * 是否禁用
   */
  disabled?: boolean;

  /** 是否开启拖拽功能 */
  draggable?: boolean;

  /** 请求调整节点位置的 API */
  dragApi?: Api;

  /**
   * 展开/折叠图标
   */
  switcherIcon?: SchemaTpl | IconSchema;

  /**
   * 节点是否占据一行
   * @deprecated 1.70.0版本废弃，只支持整行，业务项目没搜到有用，猜测只是为了同步antd的tree功能
   */
  blockNode?: boolean;

  /** 根据其计算结果决定是否允许移动节点 */
  dropOn?: SchemaExpression;

  /*
    单一节点上可以额外渲染的shcemaNode
  */
 extraActions?: SchemaNode[];

 /**
  * 树组件在禁用条件下
  */
 showActionsOnDisabled?: boolean;

 /**
  * 各个节点的禁用状态
  */
 itemDisabledOn?: string;

/*
  * 单选模式下，点击选中的节点是否取消选中
  */
 canCancelSelectedNode?: boolean;

  /**
   * 配置行的表达式类名
   */
  itemClassNameExpr?: string;

  /**
   * 可多选条件下，是否可全选
   */
  checkAll?: boolean;
  /**
   * 可多选条件下，全选项文案，默认 "全选"
   */
  checkAllLabel?: string;

  /** 默认展开的节点的 value 值 */
  defaultExpandedValues?: any[];
  onExpand?: (option: Option, expanded: boolean) => void;
  onRightClick?: (option: Option) => void;
  onDragStart?: (dragItem: Option) => void;
  onDragEnd?: (dragItem: Option) => void;
  onDragEnter?: (dropItem: Option | null) => void;
  onDragOver?: (dropItem: Option | null) => void;
  onDragLeave?: (dropItem: Option | null) => void;
  onDropSuccess?: (dropItem: Option | null, dragItem: Option, options: Option[]) => void;
  onDropFail?: (dropItem: Option | null, dragItem: Option) => void;
}

export interface TreeProps
  extends OptionsControlProps,
    Omit<
      TreeControlSchema,
      | 'type'
      | 'options'
      | 'className'
      | 'inputClassName'
      | 'descriptionClassName'
      | 'deferApi'
    >,
    SpinnerExtraProps {
  enableNodePath?: boolean;
  pathSeparator?: string;
}

interface TreeState {
  keyword: string;
}

const defaultProps = {
  placeholder: 'placeholder.noData',
  multiple: false,
  rootLabel: 'Tree.root',
  rootValue: '',
  showIcon: true,
  enableNodePath: false,
  pathSeparator: '/',
  disabled: false,
  draggable: false,
  labelField: 'label',
  valueField: 'value',
  searchable: false,
  showActionsOnDisabled: false,
  canCancelSelectedNode: false,
  checkAll: false,
  checkAllLabel: 'Select.checkAll',
  alwaysShowActions: false,
};

type InnerTreeProps = TreeProps & typeof defaultProps;

class TreeControl extends React.Component<InnerTreeProps, TreeState> {
  static defaultProps: Partial<TreeProps> = defaultProps;
  treeRef: any;
  searchBoxRef: any;

  constructor(props: TreeProps) {
    super(props as InnerTreeProps);
    this.state = {
      keyword: '',
    };
    this.handleSearch = debounce(this.handleSearch.bind(this), 250, {
      trailing: true,
      leading: false
    });
    this.searchBoxRefFn = this.searchBoxRefFn.bind(this);
  }

  reload() {
    const reload = this.props.reloadOptions;
    reload && reload();
  }

  doAction(action: ActionObject, data: any, throwErrors: boolean) {
    const actionType = action?.actionType as string;
    const {resetValue, onChange} = this.props;

    if (actionType === 'clear') {
      onChange?.('');
    } else if (actionType === 'reset') {
      onChange?.(resetValue ?? '');
    } else if (action.actionType === 'expand') {
      this.treeRef.syncUnFolded(this.treeRef.props, action.args?.openLevel, action.args?.openValues);
      this.treeRef.flattenOptions();
    } else if (action.actionType === 'collapse') {
      this.treeRef.syncUnFolded(this.treeRef.props, 1);
      this.treeRef.flattenOptions();
    } else if (action.actionType === 'setSearchValue') {
      const {searchValue = ''} = action.args || {};
      this.searchBoxRef?.handleChange?.({currentTarget: {value: searchValue}});
    }
  }

  async dispatchEvent({
    eventName,
    eventData,
    handler,
  }: {
    eventName: string,
    eventData: Record<string, any>,
    handler: Function,
  }) {
    const rendererEvent = await this.props.dispatchEvent(
      eventName,
      resolveEventData(this.props, eventData, 'value')
    );

    if (!rendererEvent?.prevented) {
      handler();
    }
  }

  /** 基于 Props 入参生成默认选项数据 */
  generateDefaultOptions = memoize((options: Option[], valueField: string, defaultExpandedValues?: any[]): Option[] => {
    // 值在 defaultExpandedValues 中的选项及其祖先选项要默认展开
    if (defaultExpandedValues?.length) {
      const valueSet = new Set(defaultExpandedValues);

      eachTree(options, (option, _i, _l, path) => {
        if (valueSet.has(option[valueField])) {
          path.forEach(opt => { opt.collapsed = false })
        }
      }, undefined, undefined, true)
    }

    return options;
  });

  /** 生成最终传给 TreeSelector 的选项数据 */
  generateFinalOptions = memoize((options: Option[], keyword: string, searchable: boolean, labelField: string, valueField: string): Option[] => {
    if (searchable && keyword) {
      return filterOptions(options, keyword, labelField, valueField);
    }

    return options;
  });

  getFinalOptions = () => {
    const { keyword } = this.state;
    const {
      options,
      searchable,
      valueField,
      labelField,
      defaultExpandedValues,
    } = this.props;
    const defaultOptions = this.generateDefaultOptions(options, valueField, defaultExpandedValues);
    return this.generateFinalOptions(defaultOptions, keyword, searchable, labelField, valueField);
  };

  handleChange = (value: any) => {
    const { onChange, getValue, canCancelSelectedNode, joinValues, valueField, multiple } = this.props;

    // 配置canCancelSelectedNode 的时候再次点击相同节点会取消选中状态，需要置空value
    let nextValue = value;

    // canCancelSelectedNode只在单选模式下生效，多选模式下用户可以通过取消勾选来移除选择
    if (canCancelSelectedNode && !multiple) {
      const currentValue = getValue();
      let shouldCancel = false;

      if (joinValues) {
        shouldCancel = value === currentValue;
      } else {
        // #1235 当joinValues为false时，比较的是valueField对应的值
        const newValueKey = value?.[valueField];
        const currentValueKey = currentValue?.[valueField];
        shouldCancel = newValueKey === currentValueKey && newValueKey !== undefined;
      }

      nextValue = shouldCancel ? undefined : value;
    }

    this.dispatchEvent({
      eventName: 'change',
      eventData: { value: nextValue },
      handler: () => onChange?.(nextValue),
    });
  }

  handleExpand = (item: Option, expanded: boolean) => {
    const { onExpand } = this.props;
    this.dispatchEvent({
      eventName: 'expand',
      eventData: { item, expanded },
      handler: () => onExpand?.(item, expanded),
    });
  }

  handleRightClick = (item: Option) => {
    const { onRightClick } = this.props;
    this.dispatchEvent({
      eventName: 'rightClick',
      eventData: { item },
      handler: () => onRightClick?.(item),
    });
  }

  /** 在 props.options 中寻找具有指定值的选项的路径 */
  findOptionPathByValue(value: any, options?: Option[]) {
    options = options || this.props.options;
    const { valueField } = this.props;
    let res = null;

    someTree(options, (option, _k, _l, path) => {
      if (option[valueField] === value) {
        res = path;
        res.push(option);
        return true;
      }

      return false;
    });

    return res as Option[] | null;
  }

  /**
   * 返回最终将接收 dragNode 的节点。
   * 如果 dragNode 被放在第一层，则返回 null。
   * 进入节点自身区域返回 null。
   **/
  getFinalDropNode({ node, position }: IDropInfo) {
    if (position === DropPosition.Slef) {
      return null;
    }

    const { valueField } = this.props;
    const dropNodePath = this.findOptionPathByValue(node[valueField])!;

    if (position === DropPosition.BottomRight) {
      return dropNodePath.pop()!;
    }

    const len = dropNodePath.length;
    return len > 1 ? dropNodePath[len - 2] : null;
  }

  handleDragStart = (dragItem: Option) => {
    const {
      valueField,
      onDragStart,
    } = this.props;

    const item = this.findOptionPathByValue(dragItem[valueField])!.pop()!;
    this.dispatchEvent({
      eventName: 'dragStart',
      eventData: { item },
      handler: () => onDragStart?.(item),
    });
  }

  handleDragEnd = (dragItem: Option) => {
    this.finalDropNode = NonDropNode;

    const {
      valueField,
      onDragEnd,
    } = this.props;

    const item = this.findOptionPathByValue(dragItem[valueField])!.pop()!;
    this.dispatchEvent({
      eventName: 'dragEnd',
      eventData: { item },
      handler: () => onDragEnd?.(item),
    });
  }

  // 基于 amis-ui/Tree 中触发的 drag 事件合成本组件对外触发的 dragEnter、dragOver、dragLeave 事件。
  /** 最终接收节点 */
  finalDropNode: Option | null | INonDropNode = NonDropNode;

  handleDragOver = (dropInfo: IDropInfo) => {
    clearTimeout(this.dragLeaveTimeout);
    const preFinalDropNode = this.finalDropNode;

    if (dropInfo.position === DropPosition.Slef) {
      if (preFinalDropNode !== NonDropNode) {
        // 如果从可放置区域进入拖拽节点自身区域
        this.dispatchEvent({
          eventName: 'dragLeave',
          eventData: { item: preFinalDropNode },
          handler: () => this.props.onDragLeave?.(preFinalDropNode),
        });
        this.finalDropNode = NonDropNode;
      }

      return;
    }

    const item = this.getFinalDropNode(dropInfo);
    // 肯定需要触发 dragOver
    this.dispatchEvent({
      eventName: 'dragOver',
      eventData: { item },
      handler: () => this.props.onDragOver?.(item),
    });

    if (item !== preFinalDropNode) {
      // 放置节点放生变化
      if (preFinalDropNode !== NonDropNode) {
        // 从另一个放置节点移出
        this.dispatchEvent({
          eventName: 'dragLeave',
          eventData: { item: preFinalDropNode },
          handler: () => this.props.onDragLeave?.(preFinalDropNode),
        });
      }

      this.dispatchEvent({
        eventName: 'dragEnter',
        eventData: { item },
        handler: () => this.props.onDragEnter?.(item),
      });
      this.finalDropNode = item;
    }
  }

  dragLeaveTimeout = -1;

  handleDragLeave = (dropInfo: IDropInfo) => {
    if (dropInfo.position === DropPosition.Slef) {
      // 从拖拽节点本身离开不触发 dragLeave
      return;
    }

    const preFinalDropNode = this.finalDropNode as Option | null;
    // 如果此定时任务执行了，说明移出了可接收节点的区域
    this.dragLeaveTimeout = setTimeout(() => {
      this.dispatchEvent({
        eventName: 'dragLeave',
        eventData: { item: preFinalDropNode },
        handler: () => this.props.onDragLeave?.(preFinalDropNode),
      });
      this.finalDropNode = NonDropNode;
    })
  }

  handleMove = async (dropInfo: IDropInfo) => {
    if (
      dropInfo.position !== DropPosition.Top &&
      dropInfo.position !== DropPosition.BottomLeft &&
      dropInfo.position !== DropPosition.BottomRight
    ) {
      return;
    }

    const {
      valueField,
      dropOn,
      data,
      setOptions,
      onDropSuccess,
      onDropFail,
      source,
      dragApi,
      env,
    } = this.props;
    let options = this.props.options.slice();

    const dragNodePath = this.findOptionPathByValue(dropInfo.dragNode[valueField], options)!;
    const dragNode = dragNodePath.pop() as Option;
    const dragNodeParent = dragNodePath.pop() || null;

    const dropNodeValue = dropInfo.node[valueField]
    const dropNodePath = this.findOptionPathByValue(dropNodeValue, options)!;
    let dropNode = dropNodePath.pop() as Option;
    const dropNodeParent = dropNodePath.pop() || null;

    let finalDropNode = dropInfo.position === DropPosition.BottomRight ? dropNode : dropNodeParent;
    const dispatchDropFail = (reason: string) => {
       this.dispatchEvent({
        eventName: 'dropFail',
        eventData: {
          dropItem: finalDropNode,
          dragItem: dragNode,
          reason,
        },
        handler: () => onDropFail?.(finalDropNode, dragNode),
      });
    };

    // 如果 drop 计算结果为 false，则放弃调整树结构，并报 dropFail
    if (dropOn) {
      const res = evalExpression(
        dropOn,
        createObject(data, {
          dragItem: dropInfo.dragNode,
          dropItem: finalDropNode,
        }),
      );

      if (!res) {
        dispatchDropFail('dropOn returns falsy value');
        return;
      }
    }

    // 1. 将 dragNode 从原先的位置移除
    options = spliceTree(
      options,
      findOptionPostion(options, dragNode)!,
      1,
    );

    if (dragNodeParent) {
      const newDragNodeParent = this.findOptionPathByValue(dragNodeParent[valueField], options)!.pop()!;

      if (!newDragNodeParent.children!.length) {
        delete newDragNodeParent.children;
      }
    }

    dropNode = this.findOptionPathByValue(dropNodeValue, options)!.pop()!;

    // 2. 将 dragNode 插入新位置
    const dropNodePostion = findOptionPostion(options, dropNode)!;
    // dragNode 作为 dropNode 的前置兄弟节点时的位置
    let newDragNodePostion = dropNodePostion.slice();

    if (dropInfo.position === DropPosition.BottomLeft) {
      // dragNode 作为 dropNode 的后继兄弟节点
      newDragNodePostion[newDragNodePostion.length - 1]++;
    } else if (dropInfo.position === DropPosition.BottomRight) {
      // dragNode 作为 dropNode 的第一个子节点
      newDragNodePostion.push(0);
    }

    options = spliceTree(options, newDragNodePostion, 0, dragNode);
    finalDropNode = getTreeParent(options, dragNode);

    // 3. 请求服务端更新节点位置
    let requestSuccessful = true;

    if (dragApi) {
      try {
        const payload = await env.fetcher(
          dragApi,
          {
            item: cloneOptionAndDeleteChildren(dragNode),
            position: newDragNodePostion, // 树结构调整后最终停留的位置
          },
          { method: 'post' },
        );

        if (!payload.ok) {
          requestSuccessful = false;
          const msg = (dragApi as BaseApiObject).messages?.failed ??
            (payload.msg || '调整位置失败');
          env.notify('error', msg);
          dispatchDropFail(`request error: ${msg}`);
        }
      } catch (e) {
        requestSuccessful = false;
        env.notify('error', e.message);
        dispatchDropFail(`request error: ${e.message}`)
      }
    }

    if (!requestSuccessful) {
      return;
    }

    // 4. 更新选项数据
    if (source && dragApi) {
      // 与 packages/amis-core/src/renderers/Options.tsx 中的 handleOptionAdd 保持相同判断条件
      this.reload();
    } else {
      setOptions(options);
    }

    // 5. 触发 dropSuccess
    this.dispatchEvent({
      eventName: 'dropSuccess',
      eventData: {
        dropItem: finalDropNode,
        dragItem: dragNode,
        options,
      },
      handler: () => onDropSuccess?.(finalDropNode, dragNode, options),
    });
  }

  handleSearch(keyword: string) {
    this.setState({ keyword });
  }

  @autobind
  domRef(ref: any) {
    this.treeRef = ref;
  }

  validate(): any {
    const {value, minLength, maxLength, delimiter} = this.props;

    let curValue = Array.isArray(value)
      ? value
      : (value ? String(value) : '').split(delimiter || ',');
    if (minLength && curValue.length < minLength) {
      return `已选择数量低于设定的最小个数${minLength}，请选择更多的选项。`;
    } else if (maxLength && curValue.length > maxLength) {
      return `已选择数量超出设定的最大个数${maxLength}，请取消选择超出的选项。`;
    }
  }

  @autobind
  renderOptionItem(option: Option, states: ItemRenderStates) {
    const {menuTpl, render, data, searchable} = this.props;
    const originData = createObject(createObject(data, {...states}), option);

    const getMenuTplStrResult = () => {
      const {keyword} = this.state;
      let {highlightTxt} = this.props;
      if (isPureVariable(highlightTxt)) {
        highlightTxt = resolveVariableAndFilter(highlightTxt, data);
      }

      highlightTxt = searchable ? keyword : highlightTxt
      const menuTplResult = resolveVariableAndFilter(menuTpl, originData);

      const result = highlightForHtmlStr(menuTplResult, highlightTxt);

      return highlightTxt ? result : menuTplResult
    }

    return render(`option/${states.index}`, typeof menuTpl !== 'string' ?  menuTpl : getMenuTplStrResult(), {
      data: originData
    });
  }

  renderSwitcherIcon() {
    const {
      render,
      data,
      switcherIcon,
    } = this.props;

    if (!switcherIcon) {
      return undefined
    }

    return render(
      'switcherIcon',
      typeof switcherIcon === 'string' ? {
        type: 'icon',
        icon: switcherIcon,
      } : switcherIcon,
      data,
    )
  }

  searchBoxRefFn(ref: any) {
    this.searchBoxRef = ref;
  }

  @supportStatic()
  render() {
    const {
      className,
      treeContainerClassName,
      classPrefix: ns,
      value,
      enableNodePath,
      pathSeparator = '/',
      disabled,
      draggable,
      joinValues,
      extractValue,
      delimiter,
      placeholder,
      multiple,
      valueField,
      initiallyOpen,
      unfoldedLevel: tmpUnfoldedLevel,
      withChildren,
      onlyChildren,
      onlyLeaf,
      loading,
      hideRoot,
      rootLabel,
      autoCheckChildren,
      cascade,
      rootValue,
      showIcon,
      showRadio,
      showOutline,
      onAdd,
      creatable,
      createTip,
      addControls,
      onEdit,
      editable,
      editTip,
      editControls,
      removable,
      removeTip,
      onDelete,
      rootCreatable,
      rootCreateTip,
      labelField,
      iconField,
      nodePath,
      deferLoad,
      expandTreeOptions,
      translate: __,
      data,
      virtualThreshold,
      itemHeight,
      loadingConfig,
      menuTpl,
      enableDefaultIcon,
      searchable,
      searchConfig = {},
      blockNode,
      extraActions,
      render,
      autoFillHeight,
      showActionsOnDisabled,
      itemClassNameExpr,
      itemDisabledOn,
      checkAll,
      checkAllLabel,
      alwaysShowActions,
    } = this.props;
    let {highlightTxt} = this.props;
    let unfoldedLevel = tmpUnfoldedLevel;

    if (typeof tmpUnfoldedLevel === "string") {
      unfoldedLevel = Number(resolveVariableAndFilter(tmpUnfoldedLevel, data) ?? tmpUnfoldedLevel);
    }

    if (isPureVariable(highlightTxt)) {
      highlightTxt = resolveVariableAndFilter(highlightTxt, data);
    }

    const {keyword} = this.state;
    const finalOptions = this.getFinalOptions();

    const TreeCmpt = (
      <TreeSelector
        classPrefix={ns}
        onRef={this.domRef}
        labelField={labelField}
        valueField={valueField}
        iconField={iconField}
        disabled={disabled}
        draggable={draggable}
        onChange={this.handleChange}
        onExpand={this.handleExpand}
        onRightClick={this.handleRightClick}
        onDragStart={this.handleDragStart}
        onDragEnd={this.handleDragEnd}
        onDragOver={this.handleDragOver}
        onDragLeave={this.handleDragLeave}
        onMove={this.handleMove}
        joinValues={joinValues}
        extractValue={extractValue}
        delimiter={delimiter}
        placeholder={__(placeholder)}
        options={finalOptions}
        highlightTxt={searchable ? keyword : highlightTxt}
        multiple={multiple}
        checkAll={checkAll}
        checkAllLabel={checkAllLabel}
        initiallyOpen={initiallyOpen}
        unfoldedLevel={unfoldedLevel}
        withChildren={withChildren}
        onlyChildren={onlyChildren}
        onlyLeaf={onlyLeaf}
        hideRoot={hideRoot}
        rootLabel={__(rootLabel)}
        rootValue={rootValue}
        showIcon={showIcon}
        showRadio={showRadio}
        showOutline={showOutline}
        autoCheckChildren={autoCheckChildren}
        cascade={cascade}
        foldedField="collapsed"
        value={value || ''}
        nodePath={nodePath}
        enableNodePath={enableNodePath}
        pathSeparator={pathSeparator}
        selfDisabledAffectChildren={false}
        onAdd={onAdd}
        creatable={creatable}
        createTip={createTip}
        rootCreatable={rootCreatable}
        rootCreateTip={rootCreateTip}
        onEdit={onEdit}
        editable={editable}
        editTip={editTip}
        removable={removable}
        removeTip={removeTip}
        onDelete={onDelete}
        bultinCUD={!addControls && !editControls}
        onDeferLoad={deferLoad}
        onExpandTree={expandTreeOptions}
        virtualThreshold={virtualThreshold}
        itemHeight={toNumber(itemHeight) > 0 ? toNumber(itemHeight) : undefined}
        itemRender={menuTpl ? this.renderOptionItem : undefined}
        enableDefaultIcon={enableDefaultIcon}
        switcherIcon={this.renderSwitcherIcon()}
        blockNode={blockNode}
        extraActions={extraActions}
        renderSchema={render}
        data={data}
        autoFillHeight={autoFillHeight}
        className={cx({
          'is-searchable': searchable,
          hidden: loading, // issue#1037 Tree组件不能被销毁，内部保存的状态会丢失
        })}
        itemClassNameExpr={itemClassNameExpr}
        showActionsOnDisabled={showActionsOnDisabled}
        itemDisabledOn={itemDisabledOn}
        alwaysShowActions={alwaysShowActions}
      />
    );

    return (
      <div
        className={cx(`${ns}TreeControl`, className, treeContainerClassName, {
          'is-sticky': searchable && searchConfig?.sticky,
          'auto-fill-height': autoFillHeight
        })}
      >
        <Spinner
          size="sm"
          key="info"
          show={loading}
          loadingConfig={loadingConfig}
        />
        {searchable ? (
          <>
            <SearchBox
              triggerRef={this.searchBoxRefFn}
              className={cx(
                `${ns}TreeControl-searchbox`,
                searchConfig?.className,
                {
                  'is-sticky': searchConfig?.sticky,
                  hidden: loading,
                }
              )}
              mini={false}
              clearable={true}
              {...omit(searchConfig, 'className', 'sticky')}
              onSearch={this.handleSearch}
            />
            {TreeCmpt}
          </>
        ) : (
          TreeCmpt
        )}
      </div>
    );
  }
}

export default TreeControl as React.ComponentClass<TreeProps, TreeState>;

@OptionsControl({
  type: 'input-tree'
})
export class TreeControlRenderer extends (TreeControl as React.ComponentClass<TreeProps, TreeState>) {}

/** 生成基于搜索关键词 keyword 进行处理后的选项数据 */
function filterOptions (options: Option[], keyword: string, labelField: string, valueField: string): Option[] {
  return options.map(option => {
    option = {...option};
    option.visible = !!matchSorter([option], keyword, {
      keys: [labelField || 'label', valueField || 'value'],
      threshold: matchSorter.rankings.CONTAINS
    }).length;

    if (!option.visible && option.children) {
      option.children = filterOptions(option.children, keyword, labelField, valueField);
      option.visible = option.children.some(child => child.visible);
    }

    if (option.visible) {
      option.collapsed = false;
    }

    return option;
  });
};

function cloneOptionAndDeleteChildren(option: Option) {
  option = { ...option }
  delete option.children;
  return option;
}

function findOptionPostion (options: Option[], option: Option) {
  return findTreeIndex(options, opt => opt === option);
}
