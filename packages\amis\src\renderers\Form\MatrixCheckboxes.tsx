/**
 * @file filter
 * <AUTHOR>
 */

import React from 'react';
import {
  FormBaseControl,
  FormControlProps,
  FormItem,
  resolveEventData
} from 'amis-core';
import {buildApi, isValidApi, isEffectiveApi} from 'amis-core';
import {Checkbox, Spinner, SpinnerExtraProps} from 'amis-ui';
import {setVariable, createObject} from 'amis-core';
import {ApiObject, ActionObject} from 'amis-core';
import {FormBaseControlSchema, SchemaApi} from '../../Schema';
import {supportStatic} from './StaticHoc';
import cloneDeep from 'lodash/cloneDeep';

/**
 * Matrix 选择控件。适合做权限勾选。
 * 文档：https://baidu.gitee.io/amis/docs/components/form/matrix
 */
export interface MatrixControlSchema extends FormBaseControlSchema {
  type: 'matrix-checkboxes';

  /**
   * 配置singleSelectMode时设置为false
   */
  multiple?: boolean;

  /**
   * 设置单选模式，multiple为false时有效
   */
  singleSelectMode?: boolean;

  /**
   * 可用来通过 API 拉取 options。
   */
  source?: SchemaApi;

  columns?: Array<{
    label: string;
    [propName: string]: any;
  }>;

  rows?: Array<{
    label: string;
    [propName: string]: any;
  }>;

  /**
   * 行标题说明
   */
  rowLabel?: string;
}

export interface Column {
  label: string;
  [propName: string]: any;
}

export interface Row {
  label: string;
  [propName: string]: any;
}

export interface ValueItem extends Column, Row {
  checked: boolean;
}

export interface MatrixProps extends FormControlProps, SpinnerExtraProps {
  columns: Array<Column>;
  rows: Array<Row>;
  multiple: boolean;
  cellCheckedMode: "checkbox" | "highlight";
}

export interface MatrixState {
  columns: Array<Column>;
  rows: Array<Row>;
  loading: boolean;
  error?: string;
  singleSelectMode?: 'cell' | 'row' | 'column';
  startX: number,
  startY: number,
  endX: number,
  endY: number,
  isShowMask: boolean,
}

export default class MatrixCheckbox extends React.Component<
  MatrixProps,
  MatrixState
> {
  static defaultProps: Partial<MatrixProps> = {
    columns: [],
    rows: [],
    multiple: true,
    singleSelectMode: 'column', // multiple 为 false 时有效。
    cellCheckedMode: "checkbox",
  };

  state: MatrixState;
  mounted: boolean = false;
  maskRef: React.RefObject<HTMLDivElement> = React.createRef();
  tableRef: React.RefObject<HTMLTableElement> = React.createRef();

  constructor(props: MatrixProps) {
    super(props);

    this.state = {
      columns: props.columns || [],
      rows: props.rows || [],
      loading: false,
      startX: 0,
      startY: 0,
      endX: 0,
      endY: 0,
      isShowMask: false,
    };

    this.toggleItem = this.toggleItem.bind(this);
    this.reload = this.reload.bind(this);
    this.initOptions = this.initOptions.bind(this);
    this.handleMouseDown = this.handleMouseDown.bind(this);
    this.handleMouseMove = this.handleMouseMove.bind(this);
    this.handleMouseUp = this.handleMouseUp.bind(this);
    this.handleScroll = this.handleScroll.bind(this);
    this.mounted = true;
  }

  componentDidMount() {
    const {formInited, addHook, blockFormInit} = this.props;
    // FIX issue#632 配置blockFormInit不阻塞form的init
    formInited || !addHook || !blockFormInit ? this.reload() : addHook(this.initOptions, 'init');
  }

  // 自动给columns添加colSpan
  buildColumns(origin: any[]) {
    const columns = origin || [];
    const hiddenColIndexs: number[] = [];
    columns.forEach((col, index) => {
      if(typeof col.colSpan === 'number' && col.colSpan > 1) {
        for(let i = 1; i < col.colSpan; i++) {
          hiddenColIndexs.push(index + i);
        }
      }
    })

    return columns.map((col, index) => ({
      ...col,
      colSpan: hiddenColIndexs.includes(index) ? 0 : col.colSpan,
    }));
  }

  buildRows(origin: any[]) {
    const rows = origin || [];
    const hiddenRowIndexs: number[] = [];
    rows.forEach((row, index) => {
      if(typeof row.rowSpan === 'number' && row.rowSpan > 1) {
        for(let i = 1; i < row.rowSpan; i++) {
          hiddenRowIndexs.push(index + i);
        }
      }
    })

    return rows.map((row, index) => ({
      ...row,
      rowSpan: hiddenRowIndexs.includes(index) ? 0 : row.rowSpan,
    }))
  }

  componentDidUpdate(prevProps: MatrixProps) {
    const props = this.props;

    if (prevProps.columns !== props.columns || prevProps.rows !== props.rows) {
      this.setState({
        columns: props.columns || [],
        rows: props.rows || []
      });
    } else if (
      props.formInited &&
      (props.source !== prevProps.source || prevProps.data !== props.data)
    ) {
      let prevApi = buildApi(
        prevProps.source as string,
        prevProps.data as object,
        {
          ignoreData: true
        }
      );
      let nextApi = buildApi(props.source as string, props.data as object, {
        ignoreData: true
      });

      if (prevApi.url !== nextApi.url && isValidApi(nextApi.url)) {
        this.reload();
      }
    }
  }

  componentWillUnmount() {
    this.mounted = false;
    const {removeHook} = this.props;
    removeHook?.(this.initOptions, 'init');
  }

  doAction(action: ActionObject, data: object, throwErrors: boolean) {
    const {resetValue, onChange} = this.props;
    const actionType = action?.actionType as string;

    if (actionType === 'clear') {
      onChange?.('');
    } else if (actionType === 'reset') {
      onChange?.(resetValue ?? '');
    }
  }

  async initOptions(data: any) {
    await this.reload();
    const {formItem, name} = this.props;
    if (!formItem) {
      return;
    }
    if (formItem.value) {
      setVariable(data, name!, formItem.value);
    }
  }

  async reload() {
    const {source, data, env, onChange, translate: __} = this.props;

    if (!isEffectiveApi(source, data) || this.state.loading) {
      return;
    }

    if (!env || !env.fetcher) {
      throw new Error('fetcher is required');
    }

    // todo 优化这块
    return await new Promise<void>((resolve, reject) => {
      if (!this.mounted) {
        return resolve();
      }

      this.setState(
        {
          loading: true
        },
        () => {
          if (!this.mounted) {
            return resolve();
          }
          env
            .fetcher(source, data)
            .then(ret => {
              if (!ret.ok) {
                throw new Error(ret.msg || __('fetchFailed'));
              }
              if (!this.mounted) {
                return resolve();
              }
              this.setState(
                {
                  loading: false,
                  rows: (ret.data as any).rows || [],
                  columns: (ret.data as any).columns || []
                },
                () => {
                  let replace = source && (source as ApiObject).replaceData;
                  let value = (ret.data as any).value;
                  if (value) {
                    value = (source as ApiObject).replaceData
                      ? value
                      : mergeValue(value, this.state.columns, this.state.rows);
                    onChange(value);
                  }
                  resolve();
                }
              );
            })
            .catch(reason =>
              this.setState(
                {
                  error: reason,
                  loading: false
                },
                () => resolve()
              )
            );
        }
      );
    });
  }

  async toggleItem(checked: boolean, x: number, y: number) {
    const {columns, rows} = this.state;
    const {multiple, singleSelectMode, dispatchEvent, data} = this.props;

    const value = this.props.value || buildDefaultValue(columns, rows);

    if (multiple) {
      value[x][y] = {
        ...value[x][y],
        checked
      };
    } else if (singleSelectMode === 'row') {
      for (let x2 = 0, len = columns.length; x2 < len; x2++) {
        value[x2][y] = {
          ...value[x2][y],
          checked: x === x2 ? checked : !checked
        };
      }
    } else if (singleSelectMode === 'column') {
      for (let y2 = 0, len = rows.length; y2 < len; y2++) {
        value[x][y2] = {
          ...value[x][y2],
          checked: y === y2 ? checked : !checked
        };
      }
    } else {
      // 只剩下 cell 了
      for (let y2 = 0, len = rows.length; y2 < len; y2++) {
        for (let x2 = 0, len2 = columns.length; x2 < len2; x2++) {
          value[x2][y2] = {
            ...value[x2][y2],
            checked: x === x2 && y === y2 ? checked : !checked
          };
        }
      }
    }

    const rendererEvent = await dispatchEvent(
      'change',
      resolveEventData(this.props, {value: value.concat()}, 'value')
    );
    if (rendererEvent?.prevented) {
      return;
    }

    this.props.onChange(value.concat());
  }

  handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    if(!this.props.dragSelect) return;

    e.preventDefault();
    this.setState({
      startX: e.clientX,
      startY: e.clientY,
      endX: e.clientX,
      endY: e.clientY,
      isShowMask: true,
    })
    document.body.addEventListener("mousemove", this.handleMouseMove);
    document.body.addEventListener("mouseup", this.handleMouseUp);
  }

  handleMouseMove(e: React.MouseEvent<HTMLDivElement>) {
    e.preventDefault();
    this.setState({
      endX: e.clientX,
      endY: e.clientY,
    })
  }

  handleMouseUp(e: React.MouseEvent<HTMLDivElement>) {
    e.preventDefault();
    document.body.removeEventListener("mousemove", this.handleMouseMove);
    document.body.removeEventListener("mouseup", this.handleMouseUp);
    this.handleDomSelect();
    this.setState({
      isShowMask: false,
    })
    this.resetXY();
  }

  resetXY = () => {
    this.setState({
      startX: 0,
      startY: 0,
      endX: 0,
      endY: 0,
    })
  }

  handleDomSelect() {
    const { onChange } = this.props;
    const {columns, rows} = this.state;

    const value = this.props.value || buildDefaultValue(columns, rows);

    if(this.maskRef.current) {
      const domMask = this.maskRef.current;
      const rect = domMask.getClientRects()[0];

      let selectKeys: any[] = [];
      this.tableRef.current?.querySelectorAll('td.text-center').forEach((node, index) => {
        const rects = node.getClientRects()[0];
        if(this.collide(rects, rect)) {
          const row = node.getAttribute('data-row');
          const col = node.getAttribute('data-col');
          // console.log('rects', node, [row, col]);
          selectKeys.push([row, col]);
        }
      })
      // console.log('selectKeys', selectKeys);
      if(selectKeys.length < 2) return;
      const nextValue = cloneDeep(value) ?? new Array(columns?.length).fill(0).map((item) => new Array(rows?.length).fill(0).map((item) => ({})));

      const isAllCheck = selectKeys.every((key) => {
        return value?.[key[1]]?.[key[0]]?.checked;
      })
      selectKeys.forEach((key) => {
        nextValue[key[1]][key[0]] = {
          ...(nextValue?.[key[1]]?.[key[0]] || {}),
          checked: isAllCheck ? false : true,
        }
      })

      onChange(nextValue)
    }
  }

  collide(rect1: DOMRect, rect2: DOMRect) {
    const maxX = Math.max(rect1.x + rect1.width, rect2.x + rect2.width);
    const maxY = Math.max(rect1.y + rect1.height, rect2.y + rect2.height);
    const minX = Math.min(rect1.x, rect2.x);
    const minY = Math.min(rect1.y, rect2.y);
    if (
      maxX - minX <= rect1.width + rect2.width &&
      maxY - minY <= rect1.height + rect2.height
    ) {
      return true;
    } else {
      return false;
    }
  }

  async handleCheckAllRows(isRowAllCheck: boolean, y: number, rowSpan: number = 1) {
    const {columns, rows} = this.state;
    const {multiple, dispatchEvent} = this.props;

    const value = this.props.value || buildDefaultValue(columns, rows);

    if (multiple) {
      value.forEach((item: any, index: number) => {
        if (isRowAllCheck) {
          if (rowSpan === 1) {
            item[y] = {
              ...item[y],
              checked: false
            }
          } else  {
            for(let i = 0; i < rowSpan; i++) {
              item[y + i] = {
                ...item[y + i],
                checked: false
              }
            }
          }
        } else {
          if (rowSpan === 1) {
            item[y] = {
              ...item[y],
              checked: true
            }
          } else {
            for(let i = 0; i < rowSpan; i++) {
              item[y + i] = {
                ...item[y + i],
                checked: true
              }
            }
          }
        }
      })
    }

    const rendererEvent = await dispatchEvent(
      'change',
      resolveEventData(this.props, {value: value.concat()}, 'value')
    );
    if (rendererEvent?.prevented) {
      return;
    }

    this.props.onChange(value.concat());
  }

  handleScroll(e: React.UIEvent<HTMLElement>) {
    const table = this.tableRef.current;
    if(!table) {
      return;
    }

    const scrollLeft = table.parentElement?.scrollLeft ?? 0;

    if(scrollLeft > 0) {
      table.classList.add('fixed-left-table')
    } else {
      table.classList.remove('fixed-left-table')
    }
  }

  renderInput(forceDisabled = false) {
    const {columns: cols, rows: rs} = this.state;
    const columns = this.buildColumns(cols);
    const rows = this.buildRows(rs);
    const {rowLabel, disabled, classnames: cx, multiple, rowCheckAll, cellCheckedMode, stickyRow, columnConfig, render, clearable, compactMode} = this.props;

    const value = this.props.value || buildDefaultValue(columns, rows);
    const stickyFirstCol = columnConfig?.sticky;
    const colWidth = columnConfig?.width;

    return (
      <div className={cx('Table m-b-none', {
        'bg-cell-table': cellCheckedMode === 'highlight',
      })} onMouseDown={this.handleMouseDown}>
        <div className={cx('Table-content')} onScroll={this.handleScroll}>
          <table className={cx('Table-table', { 'is-compact': compactMode })} ref={this.tableRef}>
            <thead>
              <tr className={cx({ 'is-sticky': stickyRow })}>
                <th className={cx({ 'is-sticky': stickyFirstCol })}>{rowLabel}</th>
                {columns.map((column, x) => typeof column.colSpan === 'number' && column.colSpan === 0 ? null : (
                  <th key={x} className="text-center" colSpan={column.colSpan ?? 1} style={{
                    minWidth: colWidth * (column.colSpan ?? 1) || 'auto',
                    width: colWidth * (column.colSpan ?? 1) || 'auto',
                  }}>
                    {column.label}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {rows.map((row, y) => {
                const rowSpan = row.rowSpan ?? 1;
                const isRowAllCheck = value?.every((item: any[]) => {
                  if(rowSpan === 1) {
                    return item?.[y]?.checked;
                  }
                  return item.slice(y, y + rowSpan).every((item: any) => item?.checked);
                });
                // const checkColLength = value?.filter((item: any[]) => item?.[y]?.checked).length;
                const isRowPartial = value?.some((item: any[]) => {
                  if(rowSpan === 1) {
                    return item?.[y]?.checked;
                  }
                  return item.slice(y, y + rowSpan).some((item: any) => item?.checked);
                }) && !isRowAllCheck;

                return (
                  <tr key={y}>
                    {
                      typeof row.rowSpan === 'number' && row.rowSpan === 0 ? null :
                      (
                        <td className={cx({ 'is-sticky': stickyFirstCol })} rowSpan={rowSpan}>
                          {
                            (multiple && rowCheckAll) ? (
                              <>
                                <Checkbox
                                  className='no-wrap'
                                  type='checkbox'
                                  label={row.label}
                                  description={row.description || row.desc}
                                  checked={isRowAllCheck || isRowPartial}
                                  partial={isRowPartial}
                                  disabled={forceDisabled || disabled}
                                  onChange={() => this.handleCheckAllRows(isRowAllCheck, y, rowSpan)}
                                />
                              </>
                            ) : (
                              <>
                                {row.label}
                                {row.description || row.desc ? (
                                  <span className="m-l-xs text-muted text-xs">
                                    {row.description || row.desc}
                                  </span>
                                ) : null}
                              </>
                            )
                          }
                        </td>
                      )
                    }
                    {columns.map((column, x) => (
                      <td 
                        key={x} 
                        className={cx("text-center", {
                          'is-checked': !!(value[x] && value[x][y] && value[x][y].checked)
                        })} 
                        data-row={y} 
                        data-col={x}
                        style={{
                          minWidth: colWidth,
                          width: colWidth,
                          height: compactMode ? 36 : 51,
                        }}
                        onClick={() => !(forceDisabled || disabled) && cellCheckedMode === 'highlight' && this.toggleItem(!(value[x] && value[x][y] && value[x][y].checked), x, y)}
                      >
                        {
                          cellCheckedMode === 'checkbox' && (
                            <Checkbox
                              type={multiple ? 'checkbox' : 'radio'}
                              disabled={forceDisabled || disabled}
                              checked={
                                !!(value[x] && value[x][y] && value[x][y].checked)
                              }
                              onChange={(checked: boolean) =>
                                this.toggleItem(checked, x, y)
                              }
                            />
                          )
                        }
                      </td>
                    ))}
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>
        {
          clearable ? render('clear', {
            type: 'button',
            level: 'link',
            label: '清空',
            className: cx('clear-btn'),
            onClick: () => {
              this.props.onChange("");
            }
          }) : null
        }
      </div>
    );
  }

  renderStatic(displayValue = '-') {
    const {className, render, classnames: cx} = this.props;
    const {error} = this.state;
    return (
      <div key="input" className={cx('MatrixControl', className || '')}>
        {error ? displayValue : this.renderInput(true)}
      </div>
    );
  }

  @supportStatic()
  render() {
    const {className, render, classnames: cx, loadingConfig} = this.props;
    const {error, loading} = this.state;

    return (
      <div key="input" className={cx('MatrixControl', className || '')}>
        {error ? (
          <div className={cx('MatrixControl-error Alert Alert--danger')}>
            {String(error)}
          </div>
        ) : (
          this.renderInput()
        )}

        <div
          ref={this.maskRef}
          className={cx('MatrixControl-mask')}
          style={{
            display: this.state.isShowMask ? 'block' : 'none',
            left: Math.min(this.state.startX, this.state.endX),
            top: Math.min(this.state.startY, this.state.endY),
            width: Math.abs(this.state.endX - this.state.startX),
            height: Math.abs(this.state.endY - this.state.startY),
          }}
        ></div>

        <Spinner
          size="lg"
          overlay
          key="info"
          show={loading}
          loadingConfig={loadingConfig}
        />
      </div>
    );
  }
}

function buildDefaultValue(
  columns: Array<Column>,
  rows: Array<Row>
): Array<Array<ValueItem>> {
  if (!Array.isArray(columns)) {
    columns = [];
  }

  if (!Array.isArray(rows)) {
    rows = [];
  }

  return columns.map(column =>
    rows.map(row => ({
      ...row,
      ...column,
      checked: false
    }))
  );
}

function mergeValue(
  value: Array<Array<ValueItem>>,
  columns: Array<Column>,
  rows: Array<Row>
): Array<Array<ValueItem>> {
  return value.map((column, x) =>
    column.map((item, y) => ({
      ...columns[x],
      ...rows[y],
      ...item
    }))
  );
}

@FormItem({
  type: 'matrix-checkboxes',
  strictMode: false,
  sizeMutable: false
})
export class MatrixRenderer extends MatrixCheckbox {}
