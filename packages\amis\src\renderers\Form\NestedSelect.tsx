import React from 'react';
import {
  ResultBox,
  Spinner,
  Icon,
  PopUp,
  Checkbox,
  Cascader,
  SpinnerExtraProps,
} from 'amis-ui';
import {
  Overlay,
  resolveEventData,
  PopOver,
  Option,
  Options,
  autobind,
  flattenTree,
  filterTree,
  string2regExp,
  getTreeAncestors,
  getTreeParent,
  ucFirst,
  isMobile,
  OptionsControl,
  OptionsControlProps,
  RootClose,
  ActionObject,
  createObject,
  findTree,
  eachTree,
  highlight,
} from 'amis-core';
import {FormOptionsSchema, SchemaObject} from '../../Schema';
import {findDOMNode} from 'react-dom';
import union from 'lodash/union';
import compact from 'lodash/compact';
import {supportStatic} from './StaticHoc';
import {isString} from 'lodash';

/**
 * Nested Select
 * 文档：https://baidu.gitee.io/amis/docs/components/form/nested-select
 */
export interface NestedSelectControlSchema extends FormOptionsSchema {
  type: 'nested-select';
  /**
   * 边框模式，全边框，还是半边框，或者没边框。
   */
  borderMode?: 'full' | 'half' | 'none';

  /**
   * 弹框的 css 类
   */
  menuClassName?: string;

  /**
   * 父子之间是否完全独立。
   */
  cascade?: boolean;

  /**
   * 选父级的时候是否把子节点的值也包含在内。
   */
  withChildren?: boolean;

  /**
   * 选父级的时候，是否只把子节点的值包含在内
   */
  onlyChildren?: boolean;

  /**
   * 只允许选择叶子节点
   */
  onlyLeaf?: boolean;

  /**
   * 是否隐藏选择框中已选中节点的祖先节点的文本信息
   */
  hideNodePathLabel?: boolean;

  /**
   * 被选中项的展示模板
   */
  valueTpl?: SchemaObject;

  /**
   * 下拉选项面板的标题
   */
  dropdownTitles?: string[];
}

export interface NestedSelectProps
  extends OptionsControlProps,
    SpinnerExtraProps {
  cascade?: boolean;
  noResultsText?: string;
  withChildren?: boolean;
  onlyChildren?: boolean;
  hideNodePathLabel?: boolean;
  useMobileUI?: boolean;
  valueTpl?: SchemaObject;
  dropdownTitles?: string[];
}

export interface NestedSelectState {
  isOpened?: boolean;
  isFocused?: boolean;
  inputValue?: string;
  stack: Array<Array<Option>>;
  hoverPath: Set<any>;
}

const OptionCheckStatus = {
  Unchecked: 'Unchecked',
  FullChecked: 'FullChecked',
  PartialChecked: 'PartialChecked',
} as const;

export default class NestedSelectControl extends React.Component<
  NestedSelectProps,
  NestedSelectState
> {
  static defaultProps: Partial<NestedSelectProps> = {
    cascade: false,
    withChildren: false,
    onlyChildren: false,
    onlyLeaf: false,
    searchPromptText: 'Select.searchPromptText',
    noResultsText: 'noResult',
    checkAll: true,
    checkAllLabel: 'Select.checkAll',
    hideNodePathLabel: false,
  };
  target: any;
  input: HTMLInputElement;
  lastFocusedOptionValue: any;
  state: NestedSelectState = {
    isOpened: false,
    isFocused: false,
    inputValue: '',
    stack: [this.props.options],
    hoverPath: new Set(),
  };

  @autobind
  domRef(ref: any) {
    this.target = ref;
  }

  componentDidUpdate(prevProps: NestedSelectProps) {
    if (prevProps.options !== this.props.options) {
      const {valueField} = this.props;
      const oldStack = this.state.stack;
      const newStack = [this.props.options];

      while (newStack.length < oldStack.length) {
        let found = false;
        const nextLevel = newStack.length;

        for (const option of newStack[nextLevel - 1]) {
          if (
            option.children?.length &&
            option.children[0][valueField] ===
              oldStack[nextLevel][0][valueField]
          ) {
            found = true;
            newStack.push(option.children);
            break;
          }
        }

        // 说明 options 有较大改动，有的老数据已经没了
        if (!found) {
          break;
        }
      }

      this.setState({stack: newStack});
    }
  }

  doAction(action: ActionObject) {
    const {resetValue, onChange} = this.props;
    const actionType = action?.actionType as string;

    if (actionType === 'clear') {
      onChange?.('');
    } else if (actionType === 'reset') {
      onChange?.(resetValue ?? '');
    }
  }

  @autobind
  async dispatchEvent(eventName: string, eventData: any = {}) {
    const {dispatchEvent} = this.props;
    const rendererEvent = await dispatchEvent(
      eventName,
      resolveEventData(this.props, eventData, 'value'),
    );
    // 返回阻塞标识
    return !!rendererEvent?.prevented;
  }

  @autobind
  handleOutClick(e: React.MouseEvent<any>) {
    // issue#1005：react17下会触发document的click事件
    e.stopPropagation();

    e.defaultPrevented ||
      this.setState({
        isOpened: !this.state.isOpened,
      });
  }

  @autobind
  handleResultClear() {
    this.setState({
      inputValue: undefined,
    });
  }

  @autobind
  closeDropdown() {
    this.setState({
      isOpened: false,
      hoverPath: new Set(),
      stack: [this.props.options],
    });
  }

  async removeItem(index: number, e?: React.MouseEvent<HTMLElement>) {
    let {
      onChange,
      selectedOptions,
      joinValues,
      valueField,
      extractValue,
      delimiter,
      value,
    } = this.props;

    e && e.stopPropagation();

    selectedOptions.splice(index, 1);

    if (joinValues) {
      value = (selectedOptions as Options)
        .map(item => item[valueField || 'value'])
        .join(delimiter || ',');
    } else if (extractValue) {
      value = (selectedOptions as Options).map(
        item => item[valueField || 'value'],
      );
    }

    const isPrevented = await this.dispatchEvent('change', {
      value,
    });
    isPrevented || onChange(value);
  }

  @autobind
  renderValue(option: Option, key?: any) {
    const {
      classnames: cx,
      labelField,
      valueField,
      options,
      hideNodePathLabel,
      data,
      valueTpl,
      render,
    } = this.props;

    const inputValue = this.state.inputValue;
    const regexp = string2regExp(inputValue || '');
    const ancestors = getTreeAncestors(options, option, true) ?? [option];

    if (hideNodePathLabel) {
      const label = option[labelField];
      const value = option[valueField];
      return regexp.test(value) || regexp.test(label)
        ? highlight(
            label,
            inputValue as string,
            cx('NestedSelect-optionLabel-highlight'),
          )
        : option[labelField];
    }

    return (
      <span className={cx('Select-valueLabel')} key={key || option[valueField]}>
        {valueTpl
          ? render('value', valueTpl, {
              data: createObject(data, {
                label: option[labelField],
                value: option[valueField],
                labels: ancestors.map(item => item[labelField]),
                selectedOptions: ancestors,
              }),
            })
          : ancestors.map((item, index) => {
              const label = item[labelField];
              const value = item[valueField];
              const isEnd = index === ancestors.length - 1;
              return (
                <span key={index}>
                  {regexp.test(value) || regexp.test(label)
                    ? highlight(
                      label,
                      inputValue as string,
                      cx('NestedSelect-optionLabel-highlight')
                    )
                    : label}
                  {!isEnd && ' / '}
                </span>
              );
            })}
      </span>
    );
  }

  /** 单选模式选择 */
  @autobind
  async handleOptionClick(option: Option) {
    const {multiple, onChange, joinValues, extractValue, valueField, onlyLeaf} =
      this.props;

    if (multiple) {
      return;
    }

    const value = joinValues || extractValue ? option[valueField] : option;

    if (value === undefined) {
      return;
    }

    if (onlyLeaf && option.children) {
      return;
    }

    const isPrevented = await this.dispatchEvent('change', {
      value,
    });

    if (!isPrevented) {
      onChange(value);
      this.handleResultClear();
    }

    this.closeDropdown();
  }

  /** 获取选项当前的选中状态 */
  getOptionCheckStatus = (option: Option) => {
    const {options, selectedOptions, cascade, withChildren, onlyChildren} =
      this.props;

    if (selectedOptions.includes(option)) {
      return OptionCheckStatus.FullChecked;
    }

    // 如果父子节点不联动，则直接认为未选中
    if (cascade) {
      return OptionCheckStatus.Unchecked;
    }

    // withChildren 为 true 时 onlyChildren 失效
    if (withChildren) {
      // 子节点全部被选中时，selectedOptions 中同时包含父子节点
      if (!option.children?.length) {
        return OptionCheckStatus.Unchecked;
      } else {
        return this.partialChecked(option.children)
          ? OptionCheckStatus.PartialChecked
          : OptionCheckStatus.Unchecked;
      }
    } else if (onlyChildren) {
      // 子节点全部被选中时，selectedOptions 中只包含子节点
      if (!option.children?.length) {
        return OptionCheckStatus.Unchecked;
      } else {
        if (this.allChecked(option.children)) {
          return OptionCheckStatus.FullChecked;
        }

        if (this.partialChecked(option.children)) {
          return OptionCheckStatus.PartialChecked;
        }

        return OptionCheckStatus.Unchecked;
      }
    } else {
      // 子节点全部被选中时，selectedOptions 中只包含父节点
      const ancestors = getTreeAncestors(options, option) ?? [];

      if (ancestors.some(item => selectedOptions.includes(item))) {
        return OptionCheckStatus.FullChecked;
      }

      if (this.partialChecked(option.children ?? [])) {
        return OptionCheckStatus.PartialChecked;
      }

      return OptionCheckStatus.Unchecked;
    }
  };

  /** 多选模式选择 */
  @autobind
  async handleCheck(option: Option | Options, index?: number) {
    const {
      onChange,
      selectedOptions,
      joinValues,
      delimiter,
      extractValue,
      withChildren,
      onlyChildren,
      cascade,
      options,
      onlyLeaf,
      valueField,
    } = this.props;
    const {stack} = this.state;

    // “仅叶子节点可选”限制逻辑
    if (onlyLeaf && !Array.isArray(option) && option.children) {
      return;
    }

    // 更新选项面板
    if (
      !Array.isArray(option) &&
      option.children &&
      option.children.length &&
      typeof index === 'number'
    ) {
      if (stack[index]) {
        stack.splice(index + 1, 1, option.children);
      } else {
        stack.push(option.children);
      }
    }

    let value: Option[] = [];
    /**
     * 打平树且只保留叶节点
     */
    const flattenTreeWithLeafNodes = (option: Option | Options) =>
      compact(
        flattenTree(Array.isArray(option) ? option : [option], node =>
          node.children && node.children.length ? null : node,
        ),
      );

    if (Array.isArray(option)) {
      // 全选
      if (withChildren) {
        option = flattenTree(option);
      } else if (onlyChildren) {
        option = flattenTreeWithLeafNodes(option);
      }

      value =
        selectedOptions.length === option.length ? [] : (option as Options);
    } else {
      const currentCheckStatus = this.getOptionCheckStatus(option);

      if (currentCheckStatus === OptionCheckStatus.FullChecked) {
        // 要变成未选中
        if (cascade) {
          value = selectedOptions.filter(opt => opt !== option);
        } else {
          // 所有祖先节点、子孙节点也都要变成未选中
          const ancestors = getTreeAncestors(options, option) ?? [];

          if (withChildren || onlyChildren) {
            const optSet = new Set([...ancestors, ...flattenTree([option])]);
            value = selectedOptions.filter(opt => !optSet.has(opt));
          } else {
            // selectedOption 中仅包含父节点
            if (selectedOptions.includes(option)) {
              value = selectedOptions.filter(opt => opt !== option);
            } else {
              const optSet = new Set(selectedOptions);
              // 肯定非 -1
              let i = ancestors.findIndex(item => optSet.has(item));

              while (i < ancestors.length) {
                optSet.delete(ancestors[i]);
                ancestors[i].children!.forEach(item => optSet.add(item));
                i++;
              }

              optSet.delete(option);
              value = Array.from(optSet);
            }
          }
        }
      } else {
        // 要变成选中
        if (cascade) {
          // 父子节点不联动
          value = [...selectedOptions, option];
        } else {
          // 父子节点联动
          if (withChildren) {
            // 同时包含父子节点的情况，本节点和所有子孙节点和必要的父节点也要变成选中
            const optSet = new Set(
              selectedOptions.concat(flattenTree([option])),
            );
            const ancestors = getTreeAncestors(options, option) ?? [];

            while (ancestors.length) {
              const ancestor = ancestors.pop()!;

              if (ancestor.children!.every(child => optSet.has(child))) {
                optSet.add(ancestor);
              } else {
                break;
              }
            }

            value = Array.from(optSet);
          } else if (onlyChildren) {
            // 仅包含叶子节点的情况，所有的叶子节点要变成选中
            value = union(selectedOptions, flattenTreeWithLeafNodes(option));
          } else {
            debugger;
            // 仅包含父节点的情况，本节点或必要的父节点需要变成选中
            let optSet = new Set([...selectedOptions, option]);
            // 将子孙节点从已选项中移除，最终结果可定不包含它们。
            eachTree(option.children ?? [], opt => optSet.delete(opt));
            const ancestors = getTreeAncestors(options, option) ?? [];

            while (ancestors.length) {
              const ancestor = ancestors.pop()!;

              if (ancestor.children!.every(child => optSet.has(child))) {
                ancestor.children!.forEach(child => optSet.delete(child));
                optSet.add(ancestor);
              } else {
                break;
              }
            }

            value = Array.from(optSet);
          }
        }
      }
    }

    const newValue = joinValues
      ? value.map(item => item[valueField as string]).join(delimiter)
      : extractValue
      ? value.map(item => item[valueField as string])
      : value;
    const isPrevented = await this.dispatchEvent('change', {
      value: newValue,
    });
    isPrevented || onChange(newValue);
    isPrevented || this.handleResultClear();
    /** 选项选择后需要重置下拉数据源：搜索结果 => 原始数据 */
    this.isSearch() && this.setState({stack: [this.props.options]});
  }

  /**
   * 判断 options 中的节点及其子孙节点是否被全部选中。
   * 如果 options 为空数组，返回 true
   */
  allChecked(options: Options): boolean {
    const {selectedOptions, withChildren, onlyChildren} = this.props;
    return options.every(option => {
      if ((withChildren || onlyChildren) && option.children) {
        return this.allChecked(option.children);
      }

      return selectedOptions.includes(option);
    });
  }

  /**
   * options 中的节点及其子孙节点中是否有任一节点被选中。
   * 如果 options 为空数组，返回 false。
   */
  partialChecked(options: Options): boolean {
    return options.some(option => {
      if (this.props.selectedOptions.some(item => item === option)) {
        return true;
      }

      return option.children ? this.partialChecked(option.children) : false;
    });
  }

  reload() {
    const reload = this.props.reloadOptions;
    reload && reload();
  }

  @autobind
  getValue() {
    let {
      selectedOptions,
      joinValues,
      valueField,
      extractValue,
      delimiter,
      value,
    } = this.props;

    if (joinValues) {
      value = (selectedOptions as Options)
        .map(item => item[valueField || 'value'])
        .join(delimiter || ',');
    } else if (extractValue) {
      value = (selectedOptions as Options).map(
        item => item[valueField || 'value'],
      );
    }

    return value;
  }

  @autobind
  async onFocus(e: any) {
    const {onFocus, disabled} = this.props;

    const value = this.getValue();

    if (!disabled && !this.state.isOpened) {
      this.setState({
        isFocused: true,
      });

      const isPrevented = await this.dispatchEvent('focus', {
        value,
      });
      isPrevented || (onFocus && onFocus(e));
    }
  }

  @autobind
  async onBlur(e: any) {
    const {onBlur} = this.props;

    const value = this.getValue();

    this.setState({
      isFocused: false,
    });

    const isPrevented = await this.dispatchEvent('blur', {
      value,
    });
    isPrevented || (onBlur && onBlur(e));
  }

  @autobind
  getTarget() {
    if (!this.target) {
      this.target = findDOMNode(this) as HTMLElement;
    }
    return this.target as HTMLElement;
  }

  @autobind
  handleKeyPress(e: React.KeyboardEvent) {
    if (e.key === ' ') {
      this.handleOutClick(e as any);
      e.preventDefault();
    }
  }

  @autobind
  handleInputKeyDown(event: React.KeyboardEvent) {
    const inputValue = this.state.inputValue;
    const {multiple, selectedOptions} = this.props;

    if (
      event.key === 'Backspace' &&
      !inputValue &&
      selectedOptions.length &&
      multiple
    ) {
      this.removeItem(selectedOptions.length - 1);
    }
  }

  @autobind
  handleInputChange(inputValue: string) {
    this.setState({
      inputValue,
    });
  }

  @autobind
  async handleResultChange(value: Array<Option>) {
    const {
      joinValues,
      extractValue,
      delimiter,
      valueField,
      onChange,
      multiple,
      cascade,
      withChildren,
      onlyChildren,
      options,
      selectedOptions,
    } = this.props;

    let newValue: any = Array.isArray(value) ? value.concat() : [];

    // 多选模式、父子节点联动、结果中同时存在父子节点时，删除某个节点，要同时删除其祖先节点及其子孙节点
    if (multiple && !cascade && withChildren && newValue.length) {
      const newValues = newValue.map((item: Option) => item[valueField]);
      // 找到被删除的节点
      const deletedOptions = selectedOptions.filter(
        option => !newValues.includes(option[valueField]),
      );
      const descendants = flattenTree(deletedOptions);
      const ancestors: Option[] = [];

      deletedOptions.forEach(option => {
        const _ancestors = getTreeAncestors(options, option);

        if (_ancestors?.length) {
          ancestors.push(..._ancestors);
        }
      });

      // 需要被删除的节点的值集合
      const targetValuesSet = new Set(
        descendants.concat(ancestors).map(item => item[valueField]),
      );
      newValue = newValue.filter(
        (item: Option) => !targetValuesSet.has(item[valueField]),
      );
      console.log(newValue);
    }

    if (!multiple && !newValue.length) {
      const isPrevented = await this.dispatchEvent('change', {
        value: '',
      });
      isPrevented || onChange('');
      return;
    }

    if (joinValues || extractValue) {
      newValue = newValue.map((item: Option) => item[valueField]);
    }

    if (joinValues) {
      newValue = newValue.join(delimiter || ',');
    }

    const isPrevented = await this.dispatchEvent('change', {
      value: newValue,
    });
    isPrevented || onChange(newValue);
  }

  /** 渲染非搜索状态下的单/多选选项列表 */
  renderOptions() {
    const {
      multiple,
      selectedOptions,
      classnames: cx,
      options: propOptions,
      disabled,
      checkAll,
      checkAllLabel,
      translate: __,
      labelField,
      menuClassName,
      cascade,
      dropdownTitles = [],
      valueField,
    } = this.props;

    const {stack, hoverPath} = this.state;
    let partialChecked = this.partialChecked(propOptions);
    let allChecked = this.allChecked(propOptions);

    return stack.map((options, index) => (
      <div key={index} className={cx('NestedSelect-menu', menuClassName)}>
        {isString(dropdownTitles[index]) ? (
          <div className={cx('NestedSelect-title')}>
            {dropdownTitles[index]}
          </div>
        ) : null}

        {multiple && checkAll && index === 0 ? (
          <div
            className={cx('NestedSelect-option', 'checkall')}
            onMouseEnter={this.clearHoverPath}
          >
            <Checkbox
              size="sm"
              onChange={this.handleCheck.bind(this, options)}
              checked={partialChecked}
              partial={partialChecked && !allChecked}
            />
            <span onClick={this.handleCheck.bind(this, options)}>
              {__(checkAllLabel)}
            </span>
          </div>
        ) : null}

        {options.map((option, idx) => {
          const ancestors = getTreeAncestors(propOptions, option) ?? [];
          const parentDisabled = ancestors.some(item => !!item.disabled);
          const nodeDisabled = option.disabled || parentDisabled || !!disabled;

          // 是否所有子孙节点都被选中
          const allChildrenChecked = this.allChecked(option.children ?? []);
          // 是否存在至少一个祖先节点被完全选中
          const someAncestorsChecked = ancestors.some(item =>
            selectedOptions.includes(item),
          );
          // 是否存在至少一个子孙节点被（部分）选中
          const someChildrenChecked = this.partialChecked(
            option.children ?? [],
          );
          const selfInSelectedOptions = selectedOptions.includes(option);

          // 本节点被（部分）选中，复合以下条件之一：
          // 1. selectedOptions 中有本节。
          // 2. 在父子节点存在联动的情况下存在至少一个祖先节点被完全选中。
          // 3. 在父子节点存在联动的情况下存在至少一个子孙节点被（部分）选中。
          const selfChecked =
            selfInSelectedOptions ||
            (!cascade && (someAncestorsChecked || someChildrenChecked));

          // 本节点被部分选中，复合以下所有条件：
          // 1. selectedOptions 中无本节。
          // 2. 没有祖先节点被完全选中。
          // 3. 在父子节点存在联动。
          // 4. 至少一个子孙节点被（部分）选中。
          // 5. 并不是所有子孙节点都被选中。
          const partialChecked =
            !selectedOptions.includes(option) &&
            !someAncestorsChecked &&
            !cascade &&
            someChildrenChecked &&
            !allChildrenChecked;

          return (
            <div
              key={idx}
              className={cx('NestedSelect-option', {
                'is-active': !nodeDisabled && selfChecked,
                'NestedSelect-option-hover': hoverPath.has(option[valueField]),
              })}
              onMouseEnter={this.onMouseEnter.bind(this, option, index)}
            >
              {multiple ? (
                <Checkbox
                  size="sm"
                  onChange={this.handleCheck.bind(this, option, index)}
                  trueValue={option[valueField]}
                  checked={selfChecked}
                  partial={partialChecked}
                  disabled={nodeDisabled}
                ></Checkbox>
              ) : null}

              <div
                className={cx('NestedSelect-optionLabel', {
                  'is-disabled': nodeDisabled,
                })}
                onClick={() =>
                  !nodeDisabled &&
                  (multiple
                    ? this.handleCheck(option, index)
                    : this.handleOptionClick(option))
                }
              >
                {option[labelField]}
              </div>

              {this.renderOptionIcon(option)}
            </div>
          );
        })}
      </div>
    ));
  }

  renderOptionIcon(option: Option) {
    const {classnames: cx} = this.props;

    if (option.children?.length || (option.defer && !option.loading)) {
      return (
        <div className={cx('NestedSelect-optionArrowRight')}>
          <Icon icon="right-arrow-bold" className="icon" />
        </div>
      );
    }

    if (option.defer && option.loading) {
      return (
        <div className={cx('NestedSelect-optionLoading')}>
          <Spinner icon="loading-outline" size="sm" />
        </div>
      );
    }

    return null;
  }

  renderSearchResult() {
    const {stack, inputValue} = this.state;
    const {
      classnames: cx,
      translate: __,
      options: propOptions,
      labelField,
      valueField,
      cascade,
      selectedOptions,
      multiple,
      disabled,
      onlyChildren,
      render,
      onlyLeaf,
      hideNodePathLabel,
    } = this.props;

    let noResultsText: any = this.props.noResultsText;
    if (noResultsText) {
      noResultsText = render('noResultText', __(noResultsText));
    }
    const regexp = string2regExp(inputValue || '');
    const flattenTreeWithNodes = flattenTree(stack[0]).filter(option => {
      // issue#502，配置了onlyLeaf，搜索的时候不显示有子节点的节点
      if(onlyLeaf && option.children?.length) {
        return false;
      }

      return !!(
        regexp.test(option[valueField || 'value']) ||
        regexp.test(option[labelField || 'label'])
      );
    });

    // 一个stack一个menu
    const resultBody = (
      <div className={cx('NestedSelect-menu')}>
        {flattenTreeWithNodes.length ? (
          flattenTreeWithNodes.map((option, index) => {
            const ancestors = getTreeAncestors(propOptions, option as any);

            const uncheckable = cascade
              ? false
              : multiple &&
                ancestors?.some(item => !!~selectedOptions.indexOf(item));

            let isNodeDisabled =
              uncheckable ||
              option.disabled ||
              !!disabled ||
              ancestors?.some(item => !!item.disabled);

            let isChildrenChecked = !!(
              option.children && this.partialChecked(option.children)
            );

            let isChecked = uncheckable || !!~selectedOptions.indexOf(option);

            if (
              !isChecked &&
              onlyChildren &&
              option.children &&
              this.allChecked(option.children)
            ) {
              isChecked = true;
            }

            return (
              <div
                className={cx('NestedSelect-option', {
                  'is-active':
                    !isNodeDisabled &&
                    (isChecked || (!cascade && isChildrenChecked)),
                })}
                key={index}
              >
                <div
                  className={cx('NestedSelect-optionLabel', {
                    'is-disabled': isNodeDisabled,
                  })}
                  onClick={() => {
                    !isNodeDisabled &&
                      (multiple
                        ? this.handleCheck(option, option.value)
                        : this.handleOptionClick(option));
                  }}
                >
                  {this.renderValue(option, option.value)}
                </div>
              </div>
            );
          })
        ) : (
          <div
            className={cx('NestedSelect-option', {
              'no-result': true,
            })}
          >
            {noResultsText}
          </div>
        )}
      </div>
    );
    return resultBody;
  }

  clearHoverPath = () => {
    this.setState({
      hoverPath: new Set(),
    });
  };

  /** 更新下拉菜单中被 hover 项的路径 */
  updateHoverPath(option: Option) {
    const {options, valueField} = this.props;
    const path = getTreeAncestors(options, option) ?? [];
    this.setState({
      hoverPath: new Set(path.map(option => option[valueField])),
    });
  }

  async onMouseEnter(option: Option, index: number) {
    this.updateHoverPath(option);
    const {valueField} = this.props;
    this.lastFocusedOptionValue = option[valueField];
    this.updateMenuStack(option, index);

    if (await this.loadSubOptionsIfNeeded(option)) {
      setTimeout(() => {
        option = findTree(
          this.props.options,
          item => item[valueField] === option[valueField],
        ) as Option;

        // 子选项加载完成时聚焦的选项可能已经发生变化，只有在未发生变化时才更新菜单
        if (option[valueField] === this.lastFocusedOptionValue) {
          this.updateMenuStack(option, index);
        }
      });
    }
  }

  async loadSubOptionsIfNeeded(option: Option) {
    if (option.defer && !option.loaded && !option.loading) {
      await this.props.deferLoad(option);
      return true;
    }

    return false;
  }

  /**
   * 更新菜单栈
   * @param option 当前鼠标指向的选项
   * @param index option 的菜单级别，从 0 开始
   */
  updateMenuStack(option: Option, index: number) {
    const stack = this.state.stack.slice(0, index + 1);

    if (option.children?.length) {
      stack.push(option.children);
    }

    this.setState({stack});
  }

  /** 当前是否处于搜索结果状态 */
  isSearch() {
    return !!this.state.inputValue;
  }

  renderOuter() {
    const {
      popOverContainer,
      translate: __,
      classnames: cx,
      options,
      render,
    } = this.props;
    let noResultsText: any = this.props.noResultsText;

    if (noResultsText) {
      noResultsText = render('noResultText', __(noResultsText));
    }

    let body = (
      <RootClose
        disabled={!this.state.isOpened}
        onRootClose={this.closeDropdown}
      >
        {(ref: any) => {
          return (
            <div className={cx('NestedSelect-menuOuter')} ref={ref}>
              {this.isSearch() ? (
                this.renderSearchResult()
              ) : options.length ? (
                this.renderOptions()
              ) : (
                <div className={cx('NestedSelect-noResult')}>
                  {noResultsText}
                </div>
              )}
            </div>
          );
        }}
      </RootClose>
    );

    return (
      <Overlay
        target={this.getTarget}
        container={popOverContainer || (() => findDOMNode(this))}
        placement={'auto'}
        show
      >
        <PopOver className={cx('NestedSelect-popover')}>{body}</PopOver>
      </Overlay>
    );
  }

  @supportStatic()
  render() {
    const {
      className,
      disabled,
      classnames: cx,
      multiple,
      placeholder,
      translate: __,
      inline,
      searchable,
      selectedOptions,
      clearable,
      loading,
      borderMode,
      useMobileUI,
      env,
      loadingConfig,
    } = this.props;

    const mobileUI = useMobileUI && isMobile();
    return (
      <div className={cx('NestedSelectControl', className)}>
        <ResultBox
          useMobileUI={useMobileUI}
          disabled={disabled}
          ref={this.domRef}
          placeholder={__(placeholder ?? 'placeholder.empty')}
          inputPlaceholder={''}
          className={cx(`NestedSelect`, {
            'NestedSelect--inline': inline,
            'NestedSelect--single': !multiple,
            'NestedSelect--multi': multiple,
            'NestedSelect--searchable': searchable,
            'is-opened': this.state.isOpened,
            'is-focused': this.state.isFocused,
            [`NestedSelect--border${ucFirst(borderMode)}`]: borderMode,
          })}
          result={
            multiple
              ? selectedOptions
              : selectedOptions.length
              ? selectedOptions[0]
              : ''
          }
          onResultClick={this.handleOutClick}
          value={this.state.inputValue}
          onChange={this.handleInputChange}
          onResultChange={this.handleResultChange}
          onClear={this.handleResultClear}
          itemRender={this.renderValue}
          onKeyPress={this.handleKeyPress}
          onFocus={this.onFocus}
          onBlur={this.onBlur}
          onKeyDown={this.handleInputKeyDown}
          clearable={clearable}
          hasDropDownArrow={true}
          allowInput={searchable}
        >
          {loading ? (
            <Spinner loadingConfig={loadingConfig} size="sm" />
          ) : undefined}
        </ResultBox>

        {mobileUI ? (
          <PopUp
            className={cx(`NestedSelect-popup`)}
            container={
              env && env.getModalContainer ? env.getModalContainer : undefined
            }
            isShow={this.state.isOpened}
            onHide={this.closeDropdown}
            showConfirm={false}
            showClose={false}
          >
            <Cascader
              onClose={this.closeDropdown}
              {...this.props}
              onChange={this.handleResultChange}
              options={this.props.options.slice()}
              value={selectedOptions}
            />
          </PopUp>
        ) : this.state.isOpened ? (
          this.renderOuter()
        ) : null}
      </div>
    );
  }
}

@OptionsControl({
  type: 'nested-select',
})
export class NestedSelectControlRenderer extends NestedSelectControl {}
@OptionsControl({
  type: 'cascader-select',
})
export class CascaderSelectControlRenderer extends NestedSelectControl {}
