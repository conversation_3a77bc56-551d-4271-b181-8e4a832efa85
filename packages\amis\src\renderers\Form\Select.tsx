import React from 'react';
import cx from 'classnames';
import {
  OptionsControl,
  OptionsControlProps,
  Option,
  FormOptionsControl,
  resolveEventData,
  extendObject,
} from 'amis-core';
import {filterContents} from '../Remark';
import {normalizeOptions} from 'amis-core';
import find from 'lodash/find';
import debouce from 'lodash/debounce';
import isPlainObject from 'lodash/isPlainObject';
import isNil from 'lodash/isNil';
import {Api, ActionObject} from 'amis-core';
import {isEffectiveApi, isApiOutdated} from 'amis-core';
import {isEmpty, createObject, autobind, isMobile, resolveVariableAndFilter, highlightForHtmlStr} from 'amis-core';

import {BaseSchema, FormOptionsSchema, SchemaApi} from '../../Schema';
import {Spinner, Select, SpinnerExtraProps} from 'amis-ui';
import {BaseTransferRenderer, TransferControlSchema} from './Transfer';
import {TransferDropDown} from 'amis-ui';

import type {SchemaClassName} from '../../Schema';
import type {TooltipObject} from 'amis-ui/lib/components/TooltipWrapper';
import type {PopOverOverlay} from 'amis-ui/lib/components/PopOverContainer';
import {supportStatic} from './StaticHoc';

/**
 * Select 下拉选择框。
 * 文档：https://baidu.gitee.io/amis/docs/components/form/select
 */
export interface SelectControlSchema
  extends FormOptionsSchema,
    SpinnerExtraProps {
  type: 'select' | 'multi-select';

  /**
   * 自动完成 API，当输入部分文字的时候，会将这些文字通过 ${term} 可以取到，发送给接口。
   * 接口可以返回匹配到的选项，帮助用户输入。
   */
  autoComplete?: SchemaApi;

  /**
   * 可以自定义菜单展示。
   */
  menuTpl?: string | BaseSchema;

  /**
   * 当在value值未匹配到当前options中的选项时，是否value值对应文本飘红显示
   */
  showInvalidMatch?: boolean;

  /**
   * 边框模式，全边框，还是半边框，或者没边框。
   */
  borderMode?: 'full' | 'half' | 'none';

  /**
   * 勾选展示模式
   */
  selectMode?: 'table' | 'group' | 'tree' | 'chained' | 'associated';

  /**
   * 当 selectMode 为 associated 时用来定义左侧的选项
   */
  leftOptions?: Array<Option>;

  /**
   * 当 selectMode 为 associated 时用来定义左侧的选择模式
   */
  leftMode?: 'tree' | 'list';

  /**
   * 当 selectMode 为 associated 时用来定义右侧的选择模式
   */
  rightMode?: 'table' | 'list' | 'tree' | 'chained';

  /**
   * 搜索结果展示模式
   */
  searchResultMode?: 'table' | 'list' | 'tree' | 'chained';

  /**
   * 当 selectMode 为 table 时定义表格列信息。
   */
  columns?: Array<any>;

  /**
   * 当 searchResultMode 为 table 时定义表格列信息。
   */
  searchResultColumns?: Array<any>;

  /**
   * 可搜索？
   */
  searchable?: boolean;

  /**
   * 搜索 API
   */
  searchApi?: SchemaApi;

  /**
   * 单个选项的高度，主要用于虚拟渲染
   */
  itemHeight?: number;

  /**
   * 在选项数量达到多少时开启虚拟渲染
   */
  virtualThreshold?: number;

  /**
   * 可多选条件下，是否可全选
   */
  checkAll?: boolean;
  /**
   * 可多选条件下，是否默认全选中所有值
   */
  defaultCheckAll?: boolean;
  /**
   * 可多选条件下，全选项文案，默认 ”全选“
   */
  checkAllLabel?: string;

  /**
   * 标签的最大展示数量，超出数量后以收纳浮层的方式展示，仅在多选模式开启后生效
   */
  maxTagCount?: number;

  /**
   * 允许添加的标签的最大数量
   */
  max?: number;

  /**
   * 收纳标签的Popover配置
   */
  overflowTagPopover?: object;

  /**
   * 选项的自定义CSS类名
   */
  optionClassName?: SchemaClassName;

  /**
   * 下拉框 Popover 设置
   */
  overlay?: {
    /**
     * 下拉框 Popover 的宽度设置，支持单位 '%'、'px'、'rem'、'em'、'vw', 支持相对写法如 '+20px'
     */
    width?: number | string;
    /**
     * 下拉框 Popover 的对齐方式
     */
    align?: 'left' | 'center' | 'right';
  };

  /**
   * 隐藏下拉框时是否清空搜索值
   */
  autoClearSearchValue?: boolean;
  /**
   * @deprecated 已废弃，请使用 `selectedTooltip.placement` 属性
   * 已选值tooltip展示位置
   */
  selectedTooltipPlacement: 'top' | 'bottom' | 'left' | 'right';
  /**
   * 已选项的 Tooltip 配置
   */
  selectedTooltip?: TooltipObject;
}

export interface SelectProps extends OptionsControlProps, SpinnerExtraProps {
  autoComplete?: Api;
  searchable?: boolean;
  showInvalidMatch?: boolean;
  defaultOpen?: boolean;
  useMobileUI?: boolean;
  maxTagCount?: number;
  max?: number;
  overflowTagPopover?: TooltipObject;
}

export type SelectRendererEvent =
  | 'change'
  | 'blur'
  | 'focus'
  | 'add'
  | 'edit'
  | 'delete';
export default class SelectControl extends React.Component<SelectProps, any> {
  static defaultProps: Partial<SelectProps> = {
    clearable: false,
    searchable: false,
    multiple: false,
    showInvalidMatch: false,
    autoClearSearchValue: false,
    selectedTooltipPlacement: 'top',
  };

  input: any;
  selectRef: any;
  unHook: Function;
  lazyloadRemote: Function;
  lastTerm: string = ''; // 用来记录上一次搜索时关键字
  constructor(props: SelectProps) {
    super(props);

    this.changeValue = this.changeValue.bind(this);
    this.lazyloadRemote = debouce(this.loadRemote.bind(this), 250, {
      trailing: true,
      leading: false
    });
    this.inputRef = this.inputRef.bind(this);
  }

  componentDidUpdate(prevProps: SelectProps) {
    const props = this.props;

    if (
      isEffectiveApi(props.autoComplete, props.data) &&
      isApiOutdated(
        prevProps.autoComplete,
        props.autoComplete,
        prevProps.data,
        props.data
      )
    ) {
      this.lazyloadRemote(this.lastTerm);
    }
  }

  componentWillUnmount() {
    this.unHook && this.unHook();
  }

  inputRef(ref: any) {
    this.input = ref;  // 之前ref取值是空，没什么用，感觉可删除
    this.selectRef = ref;
  }

  foucs() {
    this.input && this.input.focus();
  }

  getValue(
    value: Option | Array<Option> | string | void,
    additonalOptions: Array<any> = []
  ) {
    const {joinValues, extractValue, delimiter, multiple, valueField, options} =
      this.props;
    let newValue: string | Option | Array<Option> | void = value;

    (Array.isArray(value) ? value : value ? [value] : []).forEach(
      (option: any) => {
        let resolved = find(
          options,
          (item: any) =>
            item[valueField || 'value'] == option[valueField || 'value']
        );
        resolved || additonalOptions.push(option);
      }
    );

    if (joinValues) {
      if (multiple) {
        newValue = Array.isArray(value)
          ? (value
              .map(item => item[valueField || 'value'])
              .join(delimiter) as string)
          : value
          ? (value as Option)[valueField || 'value']
          : '';
      } else {
        newValue = newValue ? (newValue as Option)[valueField || 'value'] : '';
      }
    } else if (extractValue) {
      if (multiple) {
        newValue = Array.isArray(value)
          ? value.map(item => item[valueField || 'value'])
          : value
          ? [(value as Option)[valueField || 'value']]
          : [];
      } else {
        newValue = newValue ? (newValue as Option)[valueField || 'value'] : '';
      }
    }

    return newValue;
  }

  diffValues(
    currValue: Option | Array<Option> | string | void,
    prevValue: Option | Array<Option> | string | void
  ) {
    const {joinValues, extractValue, multiple, valueField, sameOptionChange = true} = this.props;

    // 选择相同的选项如果需要执行change直接返回true
    if(sameOptionChange) return true;

    if (multiple) {
      // 多选模式区分情况比较
      if(joinValues) {
        return currValue !== prevValue;
      } else if(extractValue) {
        return prevValue?.length !== currValue?.length || (currValue as Array<Option>)?.join(',') !== (prevValue as Array<Option>)?.join(',');
      } else {
        return prevValue?.length !== currValue?.length || (prevValue as Array<Option>)?.map(item => item?.[valueField])?.join(',') !== (currValue as Array<Option>)?.map(item => item?.[valueField])?.join(',');
      }
    } else {
      // 单选模式直接比较
      return currValue !== prevValue;
    }
  }

  async dispatchEvent(eventName: SelectRendererEvent, eventData: any = {}) {
    const event = 'on' + eventName.charAt(0).toUpperCase() + eventName.slice(1);
    const {dispatchEvent, options, data, multiple, selectedOptions} =
      this.props;

    // 触发渲染器事件
    const rendererEvent = await dispatchEvent(
      eventName,
      resolveEventData(
        this.props,
        {
          options,
          items: options, // 为了保持名字统一
          value: ['onEdit', 'onDelete'].includes(event)
            ? eventData
            : eventData && eventData.value,
          selectedItems: multiple ? selectedOptions : selectedOptions[0]
        },
        'value'
      )
    );
    if (rendererEvent?.prevented) {
      return;
    }
    // 触发外部方法
    this.props[event](eventData);
  }

  async changeValue(value: Option | Array<Option> | string | void) {
    const {onChange, setOptions, options, dispatchEvent,value: prevValue} = this.props;

    let additonalOptions: Array<any> = [];
    let newValue: string | Option | Array<Option> | void = this.getValue(
      value,
      additonalOptions
    );

    // 不设置没法回显
    additonalOptions.length && setOptions(options.concat(additonalOptions));

    // 修改前后的值不一样才派发change Event
    if(this.diffValues(newValue, prevValue)) {
      const rendererEvent = await dispatchEvent(
        'change',
        resolveEventData(
          this.props,
          {
            value: newValue,
            options,
            items: options, // 为了保持名字统一
            selectedItems: value
          },
          'value'
        )
      );
      if (rendererEvent?.prevented) {
        return;
      }

      onChange?.(newValue);
    }
  }

  async loadRemote(input: string) {
    const {
      autoComplete,
      env,
      data,
      setOptions,
      setLoading,
      formInited,
      addHook
    } = this.props;

    if (!env || !env.fetcher) {
      throw new Error('fetcher is required');
    }

    if (!formInited) {
      this.unHook && this.unHook();
      return (this.unHook = addHook(this.loadRemote.bind(this, input), 'init'));
    }

    this.lastTerm = input;
    const ctx = createObject(data, {
      term: input,
      value: input
    });

    if (!isEffectiveApi(autoComplete, ctx)) {
      return Promise.resolve({
        options: []
      });
    }

    setLoading(true);
    try {
      const ret = await env.fetcher(autoComplete, ctx);

      let options = (ret.data && (ret.data as any).options) || ret.data || [];
      let combinedOptions = this.mergeOptions(options);
      setOptions(combinedOptions);

      return {
        options: combinedOptions
      };
    } finally {
      setLoading(false);
    }
  }

  mergeOptions(options: Array<object>) {
    const {selectedOptions, valueField = 'value'} = this.props;
    let combinedOptions = normalizeOptions(
      options,
      undefined,
      valueField
    ).concat();

    if (Array.isArray(selectedOptions) && selectedOptions.length) {
      selectedOptions.forEach(option => {
        if (
          !find(
            combinedOptions,
            (item: Option) => item[valueField] === option[valueField]
          )
        ) {
          combinedOptions.push({
            ...option,
            hidden: true,
          });
        }
      });
    }
    return combinedOptions;
  }

  @autobind
  renderMenu(option: Option, state: any) {
    const {menuTpl, render, data, optionClassName, classnames: cx} = this.props;
    const originData = createObject(createObject(data, state), option);

    const getMenuTplStrResult = () => {
      const {inputValue} = state;
      const menuTplResult = resolveVariableAndFilter(menuTpl, originData);

      const result = highlightForHtmlStr(menuTplResult, inputValue, cx('Select-option-hl'));

      return inputValue ? result : menuTplResult;
    }

    return render(`menu/${state.index}`, typeof menuTpl !== 'string' ?  menuTpl: getMenuTplStrResult(), {
      showNativeTitle: true,
      className: cx('Select-option-content', optionClassName),
      data: originData
    });
  }

  reload() {
    const reload = this.props.reloadOptions;
    reload && reload();
  }

  option2value() {}

  renderOtherMode() {
    const {selectMode, ...rest} = this.props;
    return (
      <TransferDropdownRenderer
        {...rest}
        selectMode={selectMode === 'group' ? 'list' : selectMode}
      />
    );
  }

  doAction(action: ActionObject, data: object, throwErrors: boolean): any {
    const {resetValue, onChange} = this.props;
    const actionType = action?.actionType as string;

    if (actionType === 'clear') {
      onChange?.('');
    } else if (actionType === 'reset') {
      const value = this.getValue(resetValue ?? '');
      onChange?.(value);
    } else if (actionType === 'clearSearchValue') {
      this.selectRef.ref?.clearSearchValue?.();
    }
  }
  
  // issue1099 处理多选的自定义tooltip
  @autobind
  renderTagTooltip(item: any) {
    const { render, data, selectedTooltip, labelField, valueField } = this.props;
    
    if(selectedTooltip?.content) {
      return render(`tooltip/${item[valueField || 'value']}`, selectedTooltip.content, {
        data: createObject(data, { ...item, __selectedOption: item }),
      })
    }

    return item[labelField || 'label'];
  }

  @supportStatic()
  render() {
    let {
      autoComplete,
      searchable,
      showInvalidMatch,
      options,
      className,
      style,
      loading,
      value,
      selectedOptions,
      multi,
      multiple,
      placeholder,
      id,
      classPrefix,
      classnames,
      creatable,
      inline,
      noResultsText,
      render,
      menuTpl,
      borderMode,
      selectMode,
      env,
      useMobileUI,
      overlay,
      selectedTooltip,
      selectedTooltipPlacement,
      ...rest
    } = this.props;

    if (noResultsText) {
      noResultsText = render('noResultText', noResultsText);
    }

    const mobileUI = useMobileUI && isMobile();
    // FIXME：#857 暂时只针对单选情况自定义tooltip
    // const tooltipData = extendObject(rest.data, { __selectedOption: selectedOptions?.[0] });
    // const tooltip = filterContents(selectedTooltip, tooltipData, render);
    selectedTooltip = {
      placement: selectedTooltipPlacement,
      ...(
        isPlainObject(selectedTooltip)
          ? selectedTooltip as object
          : isNil(selectedTooltip)
            ? undefined
            : { content: selectedTooltip }
      )
    };

    return (
      <div className={cx(`${classPrefix}SelectControl`, className)}>
        {['table', 'list', 'group', 'tree', 'chained', 'associated'].includes(
          selectMode
        ) ? (
          this.renderOtherMode()
        ) : (
          <Select
            {...rest}
            renderTagTooltip={this.renderTagTooltip}
            selectedTooltip={selectedTooltip}
            useMobileUI={useMobileUI}
            popOverContainer={
              mobileUI && env && env.getModalContainer
                ? env.getModalContainer
                : mobileUI
                ? undefined
                : rest.popOverContainer
            }
            borderMode={borderMode}
            placeholder={placeholder}
            multiple={multiple || multi}
            ref={this.inputRef}
            value={selectedOptions}
            options={options}
            loadOptions={
              isEffectiveApi(autoComplete) ? this.lazyloadRemote : undefined
            }
            showInvalidMatch={showInvalidMatch}
            creatable={creatable}
            searchable={searchable || !!autoComplete}
            onChange={this.changeValue}
            onBlur={(e: any) => this.dispatchEvent('blur', e)}
            onFocus={(e: any) => this.dispatchEvent('focus', e)}
            onAdd={() => this.dispatchEvent('add')}
            onEdit={(item: any) => this.dispatchEvent('edit', item)}
            onDelete={(item: any) => this.dispatchEvent('delete', item)}
            loading={loading}
            noResultsText={noResultsText}
            renderMenu={menuTpl ? this.renderMenu : undefined}
            overlay={overlay}
          />
        )}
      </div>
    );
  }
}
export interface TransferDropDownProps
  extends OptionsControlProps,
    Omit<
      TransferControlSchema,
      | 'type'
      | 'options'
      | 'inputClassName'
      | 'className'
      | 'descriptionClassName'
    >,
    SpinnerExtraProps {
  borderMode?: 'full' | 'half' | 'none';
  useMobileUI?: boolean;
}

class TransferDropdownRenderer extends BaseTransferRenderer<TransferDropDownProps> {
  @autobind
  renderItem(item: Option): any {
    const {labelField} = this.props;
    return `${item.scopeLabel || ''}${item[labelField || 'label']}`;
  }

  render() {
    const {
      className,
      classnames: cx,
      selectedOptions,
      sortable,
      loading,
      searchable,
      searchResultMode,
      showArrow,
      deferLoad,
      disabled,
      clearable,
      selectTitle,
      selectMode,
      multiple,
      columns,
      leftMode,
      borderMode,
      useMobileUI,
      popOverContainer,
      maxTagCount,
      overflowTagPopover,
      placeholder,
      itemHeight,
      virtualThreshold,
      rightMode,
      loadingConfig,
      labelField,
      showInvalidMatch,
      checkAll,
      checkAllLabel,
      overlay,
      max,
    } = this.props;

    // 目前 LeftOptions 没有接口可以动态加载
    // 为了方便可以快速实现动态化，让选项的第一个成员携带
    // LeftOptions 信息
    let {options, leftOptions, leftDefaultValue} = this.props;
    if (
      selectMode === 'associated' &&
      options &&
      options.length &&
      options[0].leftOptions &&
      Array.isArray(options[0].children)
    ) {
      leftOptions = options[0].leftOptions;
      leftDefaultValue = options[0].leftDefaultValue ?? leftDefaultValue;
      options = options[0].children;
    }

    return (
      <>
        <TransferDropDown
          selectMode={selectMode}
          className={className}
          value={selectedOptions}
          disabled={disabled}
          clearable={clearable}
          options={options}
          onChange={this.handleChange}
          option2value={this.option2value}
          itemRender={this.renderItem}
          sortable={sortable}
          searchResultMode={searchResultMode}
          onSearch={searchable ? this.handleSearch : undefined}
          showArrow={showArrow}
          onDeferLoad={deferLoad}
          selectTitle={selectTitle}
          multiple={multiple}
          columns={columns}
          leftMode={leftMode}
          rightMode={rightMode}
          leftOptions={leftOptions}
          borderMode={borderMode}
          useMobileUI={useMobileUI}
          popOverContainer={popOverContainer}
          maxTagCount={maxTagCount}
          max={max}
          overflowTagPopover={overflowTagPopover}
          placeholder={placeholder}
          itemHeight={itemHeight}
          virtualThreshold={virtualThreshold}
          virtualListHeight={266}
          labelField={labelField}
          showInvalidMatch={showInvalidMatch}
          checkAllLabel={checkAllLabel}
          checkAll={checkAll}
          overlay={overlay}
        />

        <Spinner
          overlay
          key="info"
          show={loading}
          loadingConfig={loadingConfig}
        />
      </>
    );
  }
}

@OptionsControl({
  type: 'select'
})
export class SelectControlRenderer extends SelectControl {}

@OptionsControl({
  type: 'multi-select'
})
export class MultiSelectControlRenderer extends SelectControl {
  static defaultProps = {
    multiple: true
  };
}
