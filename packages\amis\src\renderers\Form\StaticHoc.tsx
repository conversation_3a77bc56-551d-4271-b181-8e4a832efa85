import React from 'react';
import {getPropValue, FormControlProps, checkValueIsStaticAndNeedTransform} from 'amis-core';
import {Icon} from 'amis-ui';

function renderCommonStatic(props: any, defaultValue: string, revealValue: boolean) {
  const {type, render, staticSchema} = props;
  const staticProps = {
    revealValue,
    ...props,
    ...staticSchema
  };

  switch (type) {
    case 'select':
    case 'checkboxes':
    case 'button-group-select':
    case 'input-tree':
    case 'tree-select':
    case 'nested-select':
    case 'cascader-select':
    case 'radios':
    case 'multi-select':
    case 'transfer':
    case 'transfer-picker':
    case 'tabs-transfer':
    case 'tabs-transfer-picker':
      return render('static-select', {type: 'words'}, staticProps);

    case 'input-date':
    case 'input-datetime':
    case 'input-time':
    case 'input-month':
    case 'input-quarter':
    case 'input-year':
      return renderStaticDateTypes(staticProps);

    case 'input-date-range':
    case 'input-datetime-range':
    case 'input-time-range':
    case 'input-month-range':
    case 'input-quarter-range':
    case 'input-year-range':
      return render(
        'static-input-date-range',
        {type: 'date-range'},
        {
          ...props,
          valueFormat: props.format,
          format: props.inputFormat,
          revealValue,
          ...staticSchema
        }
      );

    case 'input-password':
      return render('static-input-password', {type: 'password'}, staticProps);

    case 'input-color':
      return render('static-color', {type: 'color'}, staticProps);

    // issue#990修复，内置的tags组件仅在此处使用，组件库规范侧新增了暴露出去的tags组件，因此这里修改为直接使用words组件传递默认参数
    case 'input-tag':
      return render('static-input-tag', {type: 'words', inTag: true }, staticProps);

    case 'input-url':
      return render(
        'static-input-url',
        {type: 'link', href: defaultValue},
        staticProps
      );

    case 'input-number':
      return render(
        'static-input-number',
        {type: 'number'},
        {
          ...props,
          ...staticSchema,
          revealValue
        }
      );

    default:
      return defaultValue;
  }
}

/**
 * 表单项类成员render支持静态展示装饰器
 */
export function supportStatic<T extends FormControlProps>() {
  return function (
    target: any,
    name: string,
    descriptor: TypedPropertyDescriptor<any>
  ) {
    const original = descriptor.value;
    descriptor.value = function (...args: any[]) {
      const props = (this as TypedPropertyDescriptor<any> & {props: T}).props;
      const { revealValue = false } = (this as any)?.state || {};
      const staticShowPlaceholder = (typeof props.staticShowPlaceholder === 'boolean' ? props.staticShowPlaceholder : true) as boolean;
      if (props.static) {
        const {
          render,
          staticSchema,
          classPrefix: ns,
          classnames: cx,
          className,
          placeholder,
          staticPlaceholder = (
            <span className="text-muted">{staticShowPlaceholder ? (placeholder || '-') : '-'}</span>
          ),
          type,
          revealStaicReg,
          revealStaicValue,
        } = props;

        let body;
        let displayValue = getPropValue(props);
        const isValueEmpty = displayValue == null || displayValue === '';

        if (
          staticSchema &&
          (staticSchema.type ||
            Array.isArray(staticSchema) ||
            typeof staticSchema === 'string' ||
            typeof staticSchema === 'number')
        ) {
          // 有自定义schema 且schema有type 时，展示schema
          body = render('form-static-schema', staticSchema, props);
        } else if (target.renderStatic) {
          // 特殊组件，control有 renderStatic 时，特殊处理
          body = target.renderStatic.apply(this, [
            ...args,
            isValueEmpty ? staticPlaceholder : displayValue
          ]);
        } else if (isValueEmpty) {
          // 空值时，展示 staticPlaceholder
          body = staticPlaceholder;
        } else {
          // 静态展示脱敏操作
          displayValue = checkValueIsStaticAndNeedTransform(revealStaicReg, revealStaicValue, revealValue, displayValue);
         // 可复用组件 统一处理
          body = renderCommonStatic(props, displayValue, revealValue);
          if (revealStaicReg && revealStaicValue && type !== 'input-password') {
            body = (
              <div
                className={cx('TextControl-revealStaicValueWrapper')}
              >
                <div className={cx('TextControl-revealStaicValueWrapper-body')}>{body}</div>
                <a
                  onClick={() => { target?.setState.call(this, {revealValue: !revealValue}) }}
                  className={cx('TextControl-revealStaicValue')}
                >
                  {revealValue ? (
                    <Icon
                      icon="view"
                      className={cx('TextControl-icon-view')}
                      classNameProp={cx('TextControl-icon-view')}
                    />
                  ) : (
                    <Icon
                      icon="invisible"
                      className={cx('TextControl-icon-invisible')}
                      classNameProp={cx('TextControl-icon-invisible')}
                    />
                  )}
                </a>
              </div>
            )
          }
        }

        return <div className={cx(`${ns}Form-static`, className)}>{body}</div>;
      }

      return original.apply(this, args);
    };
    return descriptor;
  };
}

function renderStaticDateTypes(props: any) {
  const {render, type, inputFormat, timeFormat, format, value, revealValue} = props;
  return render('static-input-date', {
    type: 'date',
    value,
    format: type === 'time' && timeFormat ? timeFormat : inputFormat,
    valueFormat: format,
    revealValue
  });
}
