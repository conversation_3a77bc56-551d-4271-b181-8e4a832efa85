import {OptionsControlProps, OptionsControl, resolveEventData, eachTree} from 'amis-core';
import React from 'react';
import find from 'lodash/find';
import cloneDeep from 'lodash/cloneDeep';
import {Spinner, SpinnerExtraProps} from 'amis-ui';
import {BaseTransferRenderer, TransferControlSchema} from './Transfer';
import {TabsTransfer} from 'amis-ui';
import {Option, optionValueCompare} from 'amis-core';
import {
  autobind,
  string2regExp,
  filterTree,
  findTree,
  createObject,
  findTreeIndex,
  getTree,
  spliceTree,
  searchTreeNotOnlyLeaf,
  flattenTreeWithLeafNodes
} from 'amis-core';
import {BaseSelection} from 'amis-ui';
import {ActionObject, toNumber} from 'amis-core';
import type {ItemRenderStates} from 'amis-ui';
import {supportStatic} from './StaticHoc';

/**
 * TabsTransfer
 * 文档：https://baidu.gitee.io/amis/docs/components/form/tabs-transfer
 */
export interface TabsTransferControlSchema
  extends Omit<TransferControlSchema, 'type'>,
    SpinnerExtraProps {
  type: 'tabs-transfer';

  /**
   * 是否默认展开所有节点
   */
  initiallyOpen?: boolean;

  /**
   * 默认展开的层级数，从1开始，只有initiallyOpen不是true时生效
   */
  unfoldedLevel?: number;
}

export interface TabsTransferProps
  extends OptionsControlProps,
    Omit<
      TabsTransferControlSchema,
      | 'type'
      | 'options'
      | 'inputClassName'
      | 'className'
      | 'descriptionClassName'
    >,
    SpinnerExtraProps {}

interface BaseTransferState {
  activeKey: number;
}

export class BaseTabsTransferRenderer<
  T extends OptionsControlProps = TabsTransferProps
> extends BaseTransferRenderer<T> {
  state: BaseTransferState = {
    activeKey: 0
  };

  @autobind
  async onTabChange(key: number) {
    const {dispatchEvent} = this.props;
    const rendererEvent = await dispatchEvent('tab-change', {key});
    if (rendererEvent?.prevented) {
      return;
    }
    this.setState({
      activeKey: key
    });
  }

  @autobind
  async handleTabSearch(
    term: string,
    option: Option,
    cancelExecutor: Function
  ) {
    const {
      options,
      labelField,
      valueField,
      env,
      data,
      translate: __
    } = this.props;
    const {searchApi} = option;

    if (searchApi) {
      try {
        const payload = await env.fetcher(
          searchApi,
          createObject(data, {term}),
          {
            cancelExecutor
          }
        );

        if (!payload.ok) {
          throw new Error(__(payload.msg || 'networkError'));
        }

        const result =
          payload.data.options || payload.data.items || payload.data;
        if (!Array.isArray(result)) {
          throw new Error(__('CRUD.invalidArray'));
        }

        return result.map(item => {
          let resolved: any = null;
          const value = item[valueField || 'value'];

          // 只有 value 值有意义的时候，再去找；否则直接返回
          if (Array.isArray(options) && value !== null && value !== undefined) {
            resolved = find(options, optionValueCompare(value, valueField));
          }

          return resolved || item;
        });
      } catch (e) {
        if (!env.isCancel(e)) {
          env.notify('error', e.message);
        }

        return [];
      }
    } else if (term) {
      const regexp = string2regExp(term);
      const propValue = (option) => option[(valueField as string) || 'value'];
      const testTerm = (option) => {
        // 如果是undefined，应该置为空字符，因为/de/.test(undefined)为true。
        const label = option[(labelField as string) || 'label'] || '';
        const value = option[(valueField as string) || 'value'] || '';
        return regexp.test(label) || regexp.test(value)
      };

      // associated 模式需要考虑命中lefOptions，有独特的查找逻辑。
      if (option.selectMode === 'associated') {
        let {children : options, leftOptions} = option;
        // 关键字leftOptions命中结果
        const matchedLeftOptions = searchTreeNotOnlyLeaf(leftOptions, testTerm);
        // 抽取LeftOptons所有命中的叶子节点
        const matchedLeftOptionsLeafs = flattenTreeWithLeafNodes(matchedLeftOptions);
        // 从options里过滤出符合以下条件的options:
        // 一：命中关键字options
        // 二：关联到命中的leftOptions
        const filterdOptions = searchTreeNotOnlyLeaf(options, option=> {
          return (
            testTerm(option) || (option.ref &&  matchedLeftOptionsLeafs.find(leaf => propValue(leaf) === option.ref))
          )
        });
        // 从LeftOption里过滤出符合以下条件的leftOptions:
        // 一：关联到过滤出的options的lefOptions
        // 二：命中matchedLeftOptionsLeafs
        const filterdLeftOptions = searchTreeNotOnlyLeaf(leftOptions,
          //用leftOptoins的叶子节点去跟options的根节点去做匹配
          leaf => (!leaf.children && filterdOptions.find(root => propValue(leaf) === root.ref)) || matchedLeftOptionsLeafs.includes(leaf))

        // 不同的显示模式，返回不同的数据结构。
        const mode = option.searchResultMode || option.selectMode || this.props.selectMode;
        if (mode === 'associated') {
          return [
            {
              ...option,
              leftOptions: filterdLeftOptions,
              children: filterdOptions,
            }
          ]
        } else {
          //仅保留options里的节点，不保留leftOptions的节点。
          const normalizedOptions: Option[] = [];
          filterdOptions.forEach((option: Option) => {
            if (option.children?.length) {
              normalizedOptions.push(...option.children);
            }
          })
          return [
            {
              ...option,
              children: normalizedOptions,
            }
          ]
        }
      } else {
        return [
          {
            ...option,
            children: searchTreeNotOnlyLeaf(option.children, testTerm),
          }
        ]
      }
    } else {
      return [option];
    }
  }

  @autobind
  async handleChange(value: Array<Option> | Option, optionModified?: boolean) {
    const {
      onChange,
      joinValues,
      delimiter,
      valueField,
      extractValue,
      options,
      dispatchEvent,
      setOptions,
      translate: __
    } = this.props;
    let newValue: any = value;
    let newOptions = options.concat();
    const UN_MATCH_RESULT = 'UN_MATCH_RESULT';

    if (Array.isArray(value)) {
      newValue = value.map(item => {
        const indexes = findTreeIndex(
          options,
          optionValueCompare(
            item[(valueField as string) || 'value'],
            (valueField as string) || 'value'
          )
        );

        // 这里主要是把查询出来的没有匹配的搜索的结果（一般是DEFFER时）聚合在一个分类下
        if (!indexes) {
          const searchIndexes = findTreeIndex(
            newOptions,
            item => item.value === UN_MATCH_RESULT
          );
          if (!searchIndexes) {
            newOptions.push({
              label: __('searchResult'),
              value: UN_MATCH_RESULT,
              visible: false,
              children: [item]
            });
          } else {
            const origin = getTree(newOptions, searchIndexes);
            if (origin?.children) {
              origin.children.push(item);
              newOptions = spliceTree(newOptions, searchIndexes, 1, {
                ...origin,
                ...item
              });
            }
          }
        } else if (optionModified) {
          const origin = getTree(newOptions, indexes);
          newOptions = spliceTree(newOptions, indexes, 1, {
            ...origin,
            ...item
          });
        }

        return joinValues || extractValue
          ? item[(valueField as string) || 'value']
          : item;
      });

      if (joinValues) {
        newValue = newValue.join(delimiter || ',');
      }
    } else if (value) {
      newValue =
        joinValues || extractValue
          ? value[(valueField as string) || 'value']
          : value;
    }

    (newOptions.length > options.length || optionModified) &&
      setOptions(newOptions, true);

    // 触发渲染器事件
    const rendererEvent = await dispatchEvent(
      'change',
      resolveEventData(
        this.props,
        {
          value: newValue,
          options,
          items: options // 为了保持名字统一
        },
        'value'
      )
    );
    if (rendererEvent?.prevented) {
      return;
    }

    onChange(newValue);
  }
}

@OptionsControl({
  type: 'tabs-transfer'
})
export class TabsTransferRenderer extends BaseTabsTransferRenderer<TabsTransferProps> {
  static defaultProps = {
    multiple: true
  };

  @autobind
  optionItemRender(option: any, states: ItemRenderStates) {
    const {menuTpl, render, data} = this.props;
    const ctx = arguments[2] || {};

    if (menuTpl) {
      return render(`item/${states.index}`, menuTpl, {
        data: createObject(
          createObject(data, {
            ...states,
            ...ctx
          }),
          option
        )
      });
    }

    return BaseSelection.itemRender(option, states);
  }

  // 动作
  doAction(action: ActionObject, args: any) {
    const {resetValue, onChange} = this.props;
    const activeKey = args?.activeKey as number;
    switch (action.actionType) {
      case 'clear':
        onChange?.('');
        break;
      case 'reset':
        onChange?.(resetValue ?? '');
        break;
      case 'changeTabKey':
        this.setState({
          activeKey
        });
        break;
    }
  }

  @supportStatic()
  render() {
    const {
      className,
      style,
      classnames: cx,
      options,
      selectedOptions,
      sortable,
      loading,
      searchResultMode,
      showArrow,
      deferLoad,
      leftDeferLoad,
      disabled,
      selectTitle,
      resultTitle,
      itemHeight,
      virtualThreshold,
      onlyChildren,
      loadingConfig,
      initiallyOpen,
      unfoldedLevel
    } = this.props;

    return (
      <div className={cx('TabsTransferControl', className)}>
        <TabsTransfer
          onlyChildren={onlyChildren}
          activeKey={this.state.activeKey}
          value={selectedOptions}
          disabled={disabled}
          options={options}
          onChange={this.handleChange}
          option2value={this.option2value}
          sortable={sortable}
          searchResultMode={searchResultMode}
          onSearch={this.handleTabSearch}
          showArrow={showArrow}
          onDeferLoad={deferLoad}
          onLeftDeferLoad={leftDeferLoad}
          selectTitle={selectTitle}
          resultTitle={resultTitle}
          optionItemRender={this.optionItemRender}
          resultItemRender={this.resultItemRender}
          onTabChange={this.onTabChange}
          itemHeight={
            toNumber(itemHeight) > 0 ? toNumber(itemHeight) : undefined
          }
          virtualThreshold={virtualThreshold}
          initiallyOpen={initiallyOpen}
          unfoldedLevel={unfoldedLevel}
        />

        <Spinner
          overlay
          key="info"
          show={loading}
          loadingConfig={loadingConfig}
        />
      </div>
    );
  }
}
