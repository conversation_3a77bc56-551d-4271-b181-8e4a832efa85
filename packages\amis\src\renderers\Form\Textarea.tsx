import type { ListenerAction } from 'amis-core';
import {
autobind,
FormControlProps,
FormItem,
resolveEventData,
SchemaNode
} from 'amis-core';
import { Textarea } from 'amis-ui';
import React from 'react';
import { FormBaseControlSchema } from '../../Schema';
import { supportStatic } from './StaticHoc';
import isNil from 'lodash/isNil';

/**
 * TextArea 多行文本输入框。
 * 文档：https://baidu.gitee.io/amis/docs/components/form/textarea
 */
export interface TextareaControlSchema extends FormBaseControlSchema {
  /**
   * 指定为多行文本输入框
   */
  type: 'textarea';

  /**
   * 最大行数
   */
  maxRows?: number;

  /**
   * 最小行数
   */
  minRows?: number;

  /**
   * 是否只读
   */
  readOnly?: boolean;

  /**
   * 边框模式，全边框，还是半边框，或者没边框。
   */
  borderMode?: 'full' | 'half' | 'none';

  /**
   * 限制文字个数
   */
  maxLength?: number;

  /**
   * 是否显示计数
   */
  showCounter?: boolean;

  /**
   * 输入内容是否可清除
   */
  clearable?: boolean;

  /**
   * 重置值
   */
  resetValue?: string;

  /**
   * 复制
   */
  copyable?: boolean;

  /**
   * 自定义按钮
   */
  extraActions?: SchemaNode | SchemaNode[];
}

export type TextAreaRendererEvent = 'blur' | 'focus' | 'change';

export interface TextAreaProps extends FormControlProps {
  placeholder?: string;
  minRows?: number;
  maxRows?: number;
  clearable?: boolean;
  resetValue?: string;
  copyable?: boolean;
  extraActions?: SchemaNode | SchemaNode[];
}

export interface TextAreaState {
  focused: boolean;
}

export default class TextAreaControl extends React.Component<
  TextAreaProps,
  TextAreaState
> {
  static defaultProps: Partial<TextAreaProps> = {
    minRows: 3,
    maxRows: 20,
    trimContents: true,
    resetValue: '',
    clearable: false,
  };

  inputRef = React.createRef<any>();

  componentDidMount(): void {
    const { value } = this.props;
    if (typeof value === 'string') {
      // #1186 textarea初始化后，光标位置在最后
      this.inputRef?.current?.setSelectionRange(value.length, value.length);
    }
  }

  doAction(action: ListenerAction, args: any) {
    const actionType = action?.actionType as string;
    const onChange = this.props.onChange;

    if (!!~['clear', 'reset'].indexOf(actionType)) {
      onChange?.(this.props.resetValue);
      this.focus();
    } else if (actionType === 'focus') {
      this.focus();
    } else if (actionType === 'insertValue') {
      // #1186
      this.insertValue(action.args?.value);
    }
  }

  async insertValue(value?: string) {
    if (isNil(value) || value === '') return;

    const { onChange } = this.props;
    const currValue = this.inputRef?.current?.value;
    const selectionStart = this.inputRef?.current?.selectionStart ?? currValue?.length ?? 0;
    const selectionEnd = this.inputRef?.current?.selectionEnd ?? currValue?.length ?? 0;
    let nextValue = currValue;
    let newSelectionStart = selectionStart;
    let newSelectionEnd = selectionEnd;

    if(selectionStart === selectionEnd) {
      // 光标开始位置和结束位置一样，将value插入到光标位置
      nextValue = currValue?.substring(0, selectionStart) + value + currValue?.substring(selectionStart);
      newSelectionStart = selectionStart + value.length;
      newSelectionEnd = newSelectionStart;
    } else {
      // 光标开始位置和结束位置不一样，将value 覆盖光标覆盖区域的文案
      nextValue = currValue?.substring(0, selectionStart) + value + currValue?.substring(selectionEnd);
      newSelectionStart = selectionStart;
      newSelectionEnd = newSelectionStart + value.length;
    }

    onChange(nextValue);

    // 异步设置光标位置，需要等onChange触发render后执行
    setTimeout(() => {
      if(this.inputRef?.current) {
        // 设置光标位置
        this.inputRef?.current.setSelectionRange(newSelectionStart, newSelectionEnd);
        this.inputRef?.current.focus();
      }
    }, 200);
  }

  focus() {
    this.inputRef.current?.focus();
  }

  @autobind
  handleChange(e: React.ChangeEvent<HTMLTextAreaElement>) {
    const {onChange, dispatchEvent} = this.props;

    dispatchEvent('change', resolveEventData(this.props, {value: e}, 'value'));

    onChange && onChange(e);
  }

  @autobind
  handleFocus(e: React.FocusEvent<HTMLTextAreaElement>) {
    const {onFocus, dispatchEvent, value} = this.props;

    this.setState(
      {
        focused: true
      },
      async () => {
        const rendererEvent = await dispatchEvent(
          'focus',
          resolveEventData(this.props, {value}, 'value')
        );

        if (rendererEvent?.prevented) {
          return;
        }
        onFocus && onFocus(e);
      }
    );
  }

  @autobind
  handleBlur(e: React.FocusEvent<HTMLTextAreaElement>) {
    const {onBlur, trimContents, value, onChange, dispatchEvent} = this.props;

    this.setState(
      {
        focused: false
      },
      async () => {
        if (trimContents && value && typeof value === 'string') {
          onChange(value.trim());
        }

        const rendererEvent = await dispatchEvent(
          'blur',
          resolveEventData(this.props, {value}, 'value')
        );

        if (rendererEvent?.prevented) {
          return;
        }

        onBlur && onBlur(e);
      }
    );
  }

  renderStatic(displayValue = '-') {
    const {render, staticSchema = {}, extraActions = [], copyable} = this.props;

    // 自定义按钮
    const extraActionList: any[] = Array.isArray(extraActions)
      ? extraActions
      : [extraActions];

    // 是否支持复制
    // 仅判断copyable，会添加多个schema，后续排查下原因
    if (copyable && extraActionList.every(item => item.actionType !== 'copy'))
      extraActionList.push({
        type: 'button',
        level: 'link',
        label: '复制',
        actionType: 'copy',
        content: displayValue,
      });

    const extraActionDoms = render('body', extraActionList);

    return render(
      'static-textarea',
      {
        type: 'multiline-text',
        maxRows: staticSchema.limit || 5,
        extraActions: extraActionDoms,
        ...staticSchema,
      },
      {
        value: displayValue,
      },
    );
  }

  @supportStatic()
  render() {
    const {...rest} = this.props;
    return (
      <Textarea
        {...rest}
        forwardRef={this.inputRef}
        onFocus={this.handleFocus}
        onBlur={this.handleBlur}
        onChange={this.handleChange}
      />
    );
  }
}

@FormItem({
  type: 'textarea',
})
export class TextAreaControlRenderer extends TextAreaControl {}
