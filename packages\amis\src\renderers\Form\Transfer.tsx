import React from 'react';
import find from 'lodash/find';

import {
  OptionsControlProps,
  OptionsControl,
  // FormOptionsControl,
  resolveEventData
} from 'amis-core';
import {SpinnerExtraProps, Transfer} from 'amis-ui';
import type {Option} from 'amis-core';
import {
  autobind,
  filterTree,
  string2regExp,
  createObject,
  findTree,
  findTreeIndex,
  getTree,
  spliceTree,
  isPureVariable,
  isEffectiveApi,
  searchTreeNotOnlyLeaf,
  flattenTreeWithLeafNodes
} from 'amis-core';
import {Spinner} from 'amis-ui';
import {optionValueCompare} from 'amis-core';
import {resolveVariable} from 'amis-core';
import {FormOptionsSchema, SchemaApi, SchemaExpression, SchemaObject, SchemaClassName} from '../../Schema';
import {Selection as BaseSelection} from 'amis-ui';
import {ResultList} from 'amis-ui';
import {ActionObject, toNumber} from 'amis-core';
import type {ItemRenderStates} from 'amis-ui/lib/components/Selection';
import {supportStatic} from './StaticHoc';
import type {PaginationSchema} from '../Pagination';
import pick from 'lodash/pick';
import {isAlive} from 'mobx-state-tree';

/**
 * Transfer
 * 文档：https://baidu.gitee.io/amis/docs/components/form/transfer
 */
export interface TransferControlSchema
  extends FormOptionsSchema,
    SpinnerExtraProps {
  type: 'transfer';

  /**
   * 是否显示剪头
   */
  showArrow?: boolean;

  /**
   * 可排序？
   */
  sortable?: boolean;

  /**
   * 勾选展示模式
   */
  selectMode?: 'table' | 'list' | 'tree' | 'chained' | 'associated';

  /**
   * 结果面板是否追踪显示
   */
  resultListModeFollowSelect?: boolean;

  /**
   * 当 selectMode 为 associated 时用来定义左侧的选项
   */
  leftOptions?: Array<Option>;

  /**
   * 当 selectMode 为 associated 时用来定义左侧的选择模式
   */
  leftMode?: 'tree' | 'list';

  /**
   * 当 selectMode 为 associated 时用来定义右侧的选择模式
   */
  rightMode?: 'table' | 'list' | 'tree' | 'chained';

  /**
   * 搜索结果展示模式
   */
  searchResultMode?: 'table' | 'list' | 'tree' | 'chained';

  /**
   * 当 selectMode 为 table 时定义表格列信息。
   */
  columns?: Array<any>;

  /**
   * 当 searchResultMode 为 table 时定义表格列信息。
   */
  searchResultColumns?: Array<any>;

  /**
   * 可搜索？
   */
  searchable?: boolean;

  /**
   * 结果（右则）列表的检索功能，当设置为true时，可以通过输入检索模糊匹配检索内容
   */
  resultSearchable?: boolean;

  /**
   * 搜索 API
   */
  searchApi?: SchemaApi;

  /**
   * 左侧的标题文字
   */
  selectTitle?: string;

  /**
   * 右侧结果的标题文字
   */
  resultTitle?: string;

  /**
   * 用来丰富选项展示
   */
  menuTpl?: SchemaObject;

  /**
   * 用来丰富值的展示
   */
  valueTpl?: SchemaObject;

  /**
   * 左侧列表搜索框提示
   */
  searchPlaceholder?: string;

  /**
   * 右侧列表搜索框提示
   */
  resultSearchPlaceholder?: string;

  /**
   * 统计数字
   */
  statistics?: boolean;

  /**
   * 单个选项的高度，主要用于虚拟渲染
   */
  itemHeight?: number;

  /**
   * 在选项数量达到多少时开启虚拟渲染
   */
  virtualThreshold?: number;

  /**
   * 当在value值未匹配到当前options中的选项时，是否value值对应文本飘红显示
   */
  showInvalidMatch?: boolean;

  /**
   * 树形模式下，仅选中子节点
   */
  onlyChildren?: boolean;
  /**
   * 分页配置，selectMode为默认和table才会生效
   */
  pagination?: {
    /** 是否左侧选项分页，默认不开启 */
    enable: SchemaExpression;
    /** 分页组件CSS类名 */
    className?: SchemaClassName;
  } & Pick<
    PaginationSchema,
    'layout' | 'maxButtons' | 'perPageAvailable' | 'popOverContainerSelector'
  >;
}

export interface BaseTransferProps
  extends OptionsControlProps,
    Omit<
      TransferControlSchema,
      | 'type'
      | 'options'
      | 'className'
      | 'descriptionClassName'
      | 'inputClassName'
    >,
    SpinnerExtraProps {
  resultItemRender?: (option: Option) => JSX.Element;
  virtualThreshold?: number;
  itemHeight?: number;
}

type OptionsControlWithSpinnerProps = OptionsControlProps & SpinnerExtraProps;

export class BaseTransferRenderer<
  T extends OptionsControlWithSpinnerProps = BaseTransferProps
> extends React.Component<T> {
  static defaultProps = {
    multiple: true
  };

  tranferRef?: any;

  reload() {
    const {reloadOptions} = this.props;

    reloadOptions?.();
  }

  // 添加一个方法用来比较前后value的值
  diffValues(
    currValue: Option | Array<Option> | string | void,
    prevValue: Option | Array<Option> | string | void
  ) {
    const {joinValues, extractValue, multiple, valueField, sameOptionChange = true} = this.props;

    // 选择相同的选项如果需要执行change直接返回true
    if(sameOptionChange) return true;

    if (multiple) {
      // 多选模式区分情况比较
      if(joinValues) {
        return currValue !== prevValue;
      } else if(extractValue) {
        return prevValue?.length !== currValue?.length || (currValue as Array<Option>)?.join(',') !== (prevValue as Array<Option>)?.join(',');
      } else {
        return prevValue?.length !== currValue?.length || (prevValue as Array<Option>)?.map(item => item?.[valueField])?.join(',') !== (currValue as Array<Option>)?.map(item => item?.[valueField])?.join(',');
      }
    } else {
      // 单选模式直接比较
      return currValue !== prevValue;
    }
  }

  @autobind
  async handleChange(value: Array<Option> | Option, optionModified?: boolean) {
    const {
      onChange,
      joinValues,
      delimiter,
      valueField,
      extractValue,
      options,
      dispatchEvent,
      setOptions,
      selectMode,
      deferApi,
      value: prevValue
    } = this.props;
    let newValue: any = value;
    let newOptions = options.concat();

    if (Array.isArray(value)) {
      newValue = value.map(item => {
        const indexes = findTreeIndex(
          options,
          optionValueCompare(
            item[(valueField as string) || 'value'],
            (valueField as string) || 'value'
          )
        );

        if (!indexes) {
          newOptions.push({
            ...item,
            visible: false
          });
        } else if (optionModified) {
          const origin = getTree(newOptions, indexes);
          newOptions = spliceTree(newOptions, indexes, 1, {
            ...origin,
            ...item
          });
        }

        return joinValues || extractValue
          ? item[(valueField as string) || 'value']
          : item;
      });

      if (joinValues) {
        newValue = newValue.join(delimiter || ',');
      }
    } else if (value) {
      newValue =
        joinValues || extractValue
          ? value[(valueField as string) || 'value']
          : value;
      const indexes = findTreeIndex(
        options,
        optionValueCompare(
          value[(valueField as string) || 'value'],
          (valueField as string) || 'value'
        )
      );

      if (!indexes) {
        newOptions.push(value);
      } else if (optionModified) {
        const origin = getTree(newOptions, indexes);
        newOptions = spliceTree(newOptions, indexes, 1, {
          ...origin,
          ...value
        });
      }
    }

    // 是否是有懒加载的树，这时不能将 value 添加到 options。因为有可能 value 在懒加载结果中
    const isTreeDefer =
      selectMode === 'tree' &&
      (!!deferApi ||
        !!findTree(
          options,
          (option: Option) => option.deferApi || option.defer
        ));

    isTreeDefer === true ||
      ((newOptions.length > options.length || optionModified) &&
        setOptions(newOptions, true));

    // 修改前后的值不一样才派发change Event
    if (this.diffValues(newValue, prevValue)) {
      // 触发渲染器事件
      const rendererEvent = await dispatchEvent(
        'change',
        resolveEventData(
          this.props,
          {
            value: newValue,
            options,
            items: options // 为了保持名字统一
          },
          'value'
        )
      );
      if (rendererEvent?.prevented) {
        return;
      }

      onChange(newValue);
    }
  }

  @autobind
  option2value(option: Option) {
    return option;
  }

  // 兼容assocated模式下的两种数据结构
  // 数据结构一：props: {options:[{children: [], leftOptions: []}]}
  // 数据结构二：props: {options:[}, leftOptions: []}
  normalizeOptions() {
    let {options, leftOptions, leftDefaultValue, selectMode} = this.props;
    // 如果命中数据结构一，将数据结构一转化为数据结构二
    if (
      selectMode === 'associated' &&
      options &&
      options.length &&
      options[0].leftOptions &&
      Array.isArray(options[0].children)
    ) {
      leftOptions = options[0].leftOptions;
      leftDefaultValue = options[0].leftDefaultValue ?? leftDefaultValue;
      options = options[0].children;
    }
    return {
      leftOptions,
      options,
      leftDefaultValue
    };
  }

  @autobind
  async handleSearch(term: string, cancelExecutor: Function) {
    let {
      searchApi,
      options,
      leftOptions,
      labelField,
      valueField,
      env,
      data,
      translate: __
    } = this.props;

    if (searchApi) {
      try {
        const payload = await env.fetcher(
          searchApi,
          createObject(data, {term}),
          {
            cancelExecutor
          }
        );

        if (!payload.ok) {
          throw new Error(__(payload.msg || 'networkError'));
        }

        const result =
          payload.data.options || payload.data.items || payload.data;
        if (!Array.isArray(result)) {
          throw new Error(__('CRUD.invalidArray'));
        }

        return result.map(item => {
          let resolved: any = null;
          const value = item[valueField || 'value'];

          // 只有 value 值有意义的时候，再去找；否则直接返回
          if (Array.isArray(options) && value !== null && value !== undefined) {
            resolved = find(options, optionValueCompare(value, valueField));
          }

          return resolved || item;
        });
      } catch (e) {
        if (!env.isCancel(e)) {
          env.notify('error', e.message);
        }

        return [];
      }
    } else if (term) {
      const regexp = string2regExp(term);
      const propValue = (option : Option) => option[(valueField as string) || 'value'];
      const testTerm = (option : Option) => {
        // 如果是undefined，应该置为空字符，因为/de/.test(undefined)为true。
        const label = option[(labelField as string) || 'label'] || '';
        const value = option[(valueField as string) || 'value'] || '';
        return regexp.test(label) || regexp.test(value)
      }

      // associated 模式需要考虑命中lefOptions，有独特的查找逻辑。
      if (this.props.selectMode === 'associated') {
        let {options, leftOptions} = this.normalizeOptions();
        // 关键字leftOptions命中结果
        const matchedLeftOptions = searchTreeNotOnlyLeaf(leftOptions, testTerm);
        // 抽取LeftOptons所有命中的叶子节点
        const matchedLeftOptionsLeafs = flattenTreeWithLeafNodes(matchedLeftOptions);
        // 从options里过滤出符合以下条件的options:
        // 一：命中关键字options
        // 二：关联到命中的leftOptions
        const filterdOptions = searchTreeNotOnlyLeaf(options, option=> testTerm(option) || (option.ref &&  matchedLeftOptionsLeafs.find(leaf => propValue(leaf) === option.ref)));
        // 从LeftOption里过滤出符合以下条件的leftOptions:
        // 一：关联到过滤出的options的lefOptions
        // 二：命中matchedLeftOptionsLeafs
        const filterdLeftOptions = searchTreeNotOnlyLeaf(leftOptions,
          //用leftOptoins的叶子节点去跟options的根节点去做匹配
          leaf => (!leaf.children && filterdOptions.find(root => propValue(leaf) === root.ref)) || matchedLeftOptionsLeafs.includes(leaf))

        // 不同的显示模式，返回不同的数据结构。
        const mode = this.props.searchResultMode || this.props.selectMode;
        if (mode === 'associated') {
          return {
            leftOptions: filterdLeftOptions,
            options: filterdOptions,
          }
        } else {
          //仅保留options里的节点，不保留leftOptions的节点。
          const normalizedOptions: Option[] = [];
          filterdOptions.forEach((option: Option) => {
            if (option.children?.length) {
              normalizedOptions.push(...option.children);
            }
          })
          return normalizedOptions;
        }
      } else {
        return searchTreeNotOnlyLeaf(options, testTerm)
      }
    } else {
      return options;
    }
  }

  @autobind
  handleResultSearch(term: string, item: Option) {
    const {valueField, labelField} = this.props;
    const regexp = string2regExp(term);
    const labelTest = item[(labelField as string) || 'label'];
    const valueTest = item[(valueField as string) || 'value'];
    return regexp.test(labelTest) || regexp.test(valueTest);
  }

  @autobind
  handlePageChange(
    page: number,
    perPage?: number,
    direction?: 'forward' | 'backward'
  ) {
    const {source, data, formItem, onChange} = this.props;
    const ctx = createObject(data, {
      page: page ?? 1,
      perPage: perPage ?? 10,
      ...(direction ? {pageDir: direction} : {})
    });

    if (!formItem || !isAlive(formItem)) {
      return;
    }

    if (isPureVariable(source)) {
      formItem.loadOptionsFromDataScope(source, ctx, onChange);
    } else if (isEffectiveApi(source, ctx)) {
      formItem.loadOptions(source, ctx, undefined, false, onChange, false);
    }
  }

  @autobind
  optionItemRender(option: Option, states: ItemRenderStates) {
    const {menuTpl, render, data} = this.props;

    if (menuTpl) {
      return render(`item/${states.index}`, menuTpl, {
        data: createObject(createObject(data, states), option)
      });
    }

    return BaseSelection.itemRender(option, states);
  }

  @autobind
  resultItemRender(option: Option, states: ItemRenderStates) {
    const {valueTpl, render, data} = this.props;

    if (valueTpl) {
      return render(`value/${states.index}`, valueTpl, {
        onChange: states.onChange,
        data: createObject(createObject(data, states), option)
      });
    }

    return ResultList.itemRender(option, states);
  }

  @autobind
  renderCell(
    column: {
      name: string;
      label: string;
      [propName: string]: any;
    },
    option: Option,
    colIndex: number,
    rowIndex: number
  ) {
    const {render, data, classnames: cx, showInvalidMatch} = this.props;
    return render(
      `cell/${colIndex}/${rowIndex}`,
      {
        type: 'text',
        className: cx({
          'is-invalid': showInvalidMatch ? option?.__unmatched : false
        }),
        ...column
      },
      {
        value: resolveVariable(column.name, option),
        data: createObject(data, option)
      }
    );
  }

  @autobind
  getRef(ref: any) {
    while (ref && ref.getWrappedInstance) {
      ref = ref.getWrappedInstance();
    }
    this.tranferRef = ref;
  }

  @autobind
  onSelectAll(options: Option[]) {
    const {dispatchEvent, data} = this.props;
    dispatchEvent('selectAll', createObject(data, {items: options}));
  }

  // 动作
  doAction(action: ActionObject, data: object, throwErrors: boolean) {
    const {resetValue, onChange} = this.props;
    switch (action.actionType) {
      case 'clear':
        onChange?.('');
        break;
      case 'reset':
        onChange?.(resetValue ?? '');
        break;
      case 'selectAll':
        this.tranferRef?.selectAll();
        break;
    }
  }

  @supportStatic()
  render() {
    let {
      className,
      style,
      classnames: cx,
      selectedOptions,
      showArrow,
      sortable,
      selectMode,
      columns,
      loading,
      searchable,
      searchResultMode,
      searchResultColumns,
      deferLoad,
      leftMode,
      rightMode,
      disabled,
      selectTitle,
      resultTitle,
      menuTpl,
      searchPlaceholder,
      resultListModeFollowSelect = false,
      resultSearchPlaceholder,
      resultSearchable = false,
      statistics,
      labelField,
      virtualThreshold,
      itemHeight,
      loadingConfig,
      showInvalidMatch,
      onlyChildren,
      pagination,
      formItem,
      env,
      popOverContainer
    } = this.props;

    // 目前 LeftOptions 没有接口可以动态加载
    // 为了方便可以快速实现动态化，让选项的第一个成员携带
    // LeftOptions 信息
    let {options, leftOptions, leftDefaultValue} = this.normalizeOptions();

    return (
      <div className={cx('TransferControl', className)}>
        <Transfer
          onlyChildren={onlyChildren}
          value={selectedOptions}
          options={options}
          accumulatedOptions={formItem?.accumulatedOptions ?? []}
          disabled={disabled}
          onChange={this.handleChange}
          option2value={this.option2value}
          sortable={sortable}
          showArrow={showArrow}
          selectMode={selectMode}
          searchResultMode={searchResultMode}
          searchResultColumns={searchResultColumns}
          columns={columns}
          onSearch={searchable ? this.handleSearch : undefined}
          onDeferLoad={deferLoad}
          leftOptions={leftOptions}
          leftMode={leftMode}
          rightMode={rightMode}
          cellRender={this.renderCell}
          selectTitle={selectTitle}
          resultTitle={resultTitle}
          resultListModeFollowSelect={resultListModeFollowSelect}
          onResultSearch={this.handleResultSearch}
          searchPlaceholder={searchPlaceholder}
          resultSearchable={resultSearchable}
          resultSearchPlaceholder={resultSearchPlaceholder}
          statistics={statistics}
          labelField={labelField}
          optionItemRender={this.optionItemRender}
          resultItemRender={this.resultItemRender}
          onSelectAll={this.onSelectAll}
          onRef={this.getRef}
          virtualThreshold={virtualThreshold}
          itemHeight={
            toNumber(itemHeight) > 0 ? toNumber(itemHeight) : undefined
          }
          loadingConfig={loadingConfig}
          showInvalidMatch={showInvalidMatch}
          pagination={{
            ...pick(pagination, [
              'className',
              'layout',
              'perPageAvailable',
              'popOverContainerSelector'
            ]),
            enable:
              !!formItem?.enableSourcePagination &&
              (!selectMode ||
                selectMode === 'list' ||
                selectMode === 'table') &&
              options.length > 0,
            maxButtons: Number.isInteger(pagination?.maxButtons)
              ? pagination.maxButtons
              : 5,
            page: formItem?.sourcePageNum,
            perPage: formItem?.sourcePerPageNum,
            total: formItem?.sourceTotalNum,
            popOverContainer: popOverContainer ?? env?.getModalContainer
          }}
          onPageChange={this.handlePageChange}
        />

        <Spinner
          overlay
          key="info"
          loadingConfig={loadingConfig}
          show={loading}
        />
      </div>
    );
  }
}

// ts 3.9 里面非得这样才不报错，鬼知道为何。
export class TransferRender extends BaseTransferRenderer {}

export default OptionsControl({
  type: 'transfer'
})(TransferRender);
