import type {RendererEnv} from 'amis-core';
import {
  autobind,
  buildApi,
  createObject,
  filter,
  getPropValue,
  handleAction,
  localeable,
  LocaleProps,
  normalizeApi,
  Renderer,
  RendererProps,
  themeable,
  ThemeProps,
  dataMapping,
  resolveVariableAndFilter,
} from 'amis-core';
import {Icon, PdfPreview} from 'amis-ui';
import {Watermark} from 'dataseed-ui';
import {ButtonSchema} from 'amis/lib/renderers/Action';
import React from 'react';
import {
  BaseSchema,
  SchemaApi,
  SchemaClassName,
  SchemaTpl,
  SchemaUrlPath,
} from '../Schema';
import {imagePlaceholder} from './Image';

type IFile = SchemaUrlPath & Record<string, any>;

export interface PdfSchema extends BaseSchema {
  /**
   * 指定为pdf展示类型
   */
  type: 'pdf' | 'static-pdf';

  /**
   * 默认pdf地址
   */
  defaultImage?: SchemaUrlPath;

  /**
   * pdf标题
   */
  title?: SchemaTpl;

  /**
   * 关联字段名，也可以直接配置 src
   */
  name?: string;

  /**
   * pdf描述信息
   */
  pdfCaption?: SchemaTpl;

  /**
   * pdf地址，如果配置了 name，这个属性不用配置。
   */
  src?: IFile;

  /**
   * 是否启动放大功能。
   */
  enlargeAble?: boolean;

  /**
   * 默认显示文件路径的时候会支持直接下载，
   * 可以支持加前缀如：`http://xx.dom/filename=` ，
   * 如果不希望这样，可以把当前配置项设置为 `false`。
   *
   * 1.1.6 版本开始将支持变量 ${xxx} 来自己拼凑个下载地址，并且支持配置成 post.
   */
  downloadUrl?: SchemaApi | boolean;

  /**
   * 是否显示尺寸。
   */
  // showDimensions?: boolean;

  /**
   * 高度
   */
  height?: number;

  /**
   * 宽度
   */
  width?: number;

  /**
   * 外层 css 类名
   */
  className?: SchemaClassName;

  /** 组件内层 css 类名 */
  innerClassName?: SchemaClassName;

  /**
   * pdf容器 css 类名
   */
  pdfClassName?: SchemaClassName;

  /**
   * pdf缩略图外层 css 类名
   */
  thumbClassName?: SchemaClassName;

  /** pdf说明文字 */
  caption?: SchemaTpl;

  /**
   * pdf展示模式，默认为缩略图模式、可以配置成原图模式
   */
  pdfMode?: 'thumb' | 'original';

  /**
   * 链接地址
   */
  href?: SchemaTpl;

  /**
   * 是否新窗口打开
   */
  blank?: boolean;

  /**
   * 链接的 target
   */
  htmlTarget?: string;

  /**
   * 水印配置
   */
  watermarkConfig: any;
}

export interface PdfThumbProps
  extends LocaleProps,
    ThemeProps,
    Omit<PdfSchema, 'type' | 'className' | 'innerClassName'> {
  onEnlarge?: () => void;
  index?: number;
  overlays?: JSX.Element;
  data?: any;
  env?: RendererEnv;
}

interface PdfThumbState {
  pdfLoading: boolean; // 切换pdf后，pdf是否在加载
}

export class PdfThumb extends React.Component<PdfThumbProps, PdfThumbState> {
  static defaultProps: Pick<PdfThumbProps, 'defaultImage' | 'pdfMode'> = {
    defaultImage: imagePlaceholder,
    pdfMode: 'thumb',
    watermarkConfig: {}
  };

  constructor(props: PdfThumbProps) {
    super(props);

    this.state = {
      pdfLoading: false,
    };
  }

  componentDidUpdate(preProps: PdfThumbProps) {
    if (preProps.src !== this.props.src) {
      this.setState({
        pdfLoading: true,
      });
    }
  }

  @autobind
  handlePdfLoaded() {
    this.setState({
      pdfLoading: false,
    });
  }

  @autobind
  handlePdfError() {
    this.setState({
      pdfLoading: false,
    });
  }

  @autobind
  handleDownload(e: React.MouseEvent) {
    e.preventDefault();
    e.stopPropagation();

    let {downloadUrl} = this.props;
    if (!downloadUrl) return;

    const {src} = this.props;
    if (downloadUrl === true) {
      downloadUrl = typeof src === 'string' ? src : src?.url;
    }

    this.handleApi(downloadUrl as SchemaApi);
  }

  @autobind
  handleApi(api: SchemaApi, payload?: object) {
    const {data, env} = this.props;

    if (api) {
      const ctx = createObject(data, {
        ...payload,
      });

      const apiObject = normalizeApi(api);

      if (apiObject.method?.toLowerCase() === 'get' && !apiObject.data) {
        window.open(buildApi(apiObject, ctx).url);
      } else {
        apiObject.responseType = apiObject.responseType ?? 'blob';
        env?.fetcher(apiObject, ctx, {
          responseType: 'blob',
        });
      }
    }
  }

  render() {
    const {
      classnames: cx,
      className,
      style,
      pdfClassName,
      thumbClassName,
      height,
      width,
      src,
      title,
      caption,
      href,
      blank = true,
      htmlTarget,
      enlargeAble,
      translate: __,
      overlays,
      pdfMode,
      downloadUrl,
      onEnlarge,
      data,
      buildWatermarkConfig,
    } = this.props;

    const { pdfLoading } = this.state;

    // 解析src.data中的表达式
    if (src?.data !== undefined) {
      src.data = dataMapping(src.data, data);
    }

    const pdfContent = (
      <>
        {pdfLoading ? (
          <img
            className={cx('Image-image', pdfClassName)}
            src={imagePlaceholder}
          />
        ) : null}
        {/* // issue#545 支持水印 */}
        <Watermark {...(pdfMode === 'original' ? buildWatermarkConfig?.() : {})}>
          <PdfPreview
            thumb={pdfMode === 'thumb'}
            file={src}
            pdfClassName={cx('Image-pdf', pdfClassName, {
              'Image-pdf--loading': pdfLoading,
            })}
            onLoad={this.handlePdfLoaded}
            onError={this.handlePdfError}
          />
        </Watermark>
      </>
    );

    const enlarge =
      enlargeAble || downloadUrl || overlays ? (
        <div key="overlay" className={cx('Image-overlay')}>
          {enlargeAble ? (
            <a
              data-tooltip={__('Pdf.viewFullText')}
              data-position="bottom"
              target="_blank"
              onClick={() => onEnlarge?.()} // pdfField中处理传给ImageGallery的回调，此处不需要传参数
            >
              <Icon icon="view" className="icon" />
            </a>
          ) : null}
          {downloadUrl ? (
            <a
              data-tooltip={__('File.download')}
              data-position="bottom"
              target="_blank"
              onClick={this.handleDownload}
            >
              <Icon icon="download2" className="icon" />
            </a>
          ) : null}
          {overlays}
        </div>
      ) : null;

    let pdf = (
      <div
        className={cx(
          'Image',
          pdfMode === 'original' ? 'Image--original' : 'Image--thumb',
          className,
        )}
        style={href ? undefined : style} // 避免重复设置style
      >
        {pdfMode === 'original' ? (
          <div
            className={cx('Image-origin')}
            style={{height: height, width: width}}
          >
            {pdfContent}
            {enlarge}
          </div>
        ) : (
          <div className={cx('Image-thumbWrap')}>
            <div
              className={cx('Image-thumb', 'Image-pdfThumb', thumbClassName)}
              style={{height: height, width: width}}
            >
              {pdfContent}
            </div>
            {enlarge}
          </div>
        )}

        {title || caption ? (
          <div key="caption" className={cx('Image-info')}>
            {title ? (
              <div className={cx('Image-title')} title={title}>
                {title}
              </div>
            ) : null}
            {caption ? (
              <div className={cx('Image-caption', 'break-all')} title={caption}>
                {caption}
              </div>
            ) : null}
          </div>
        ) : null}
      </div>
    );

    if (href) {
      pdf = (
        <a
          href={href}
          target={htmlTarget || (blank ? '_blank' : '_self')}
          className={cx('Link', className)}
          style={style}
          title={title}
        >
          {pdf}
        </a>
      );
    }

    return pdf;
  }
}
const ThemedPdfThumb = themeable(localeable(PdfThumb));
export default ThemedPdfThumb;

interface Info {
  src: IFile;
  title?: string;
  caption?: string;
  watermarkConfig?: any;
}

interface FilterInfo extends Info {
  href: string;
}

export interface PdfFieldProps extends RendererProps {
  className?: string;
  innerClassName?: string;
  pdfClassName?: string;
  thumbClassName?: string;
  placeholder: string;
  description?: string;
  enlargeTitle?: string;
  enlargeCaption?: string;
  pdfMode?: 'thumb' | 'original';
  enlargeAble?: boolean;
  onPdfEnlarge?: (info: Info, target: any) => void;
  enlargeTrigger?: ButtonSchema;
}

export class PdfField extends React.Component<PdfFieldProps, object> {
  static defaultProps: Pick<
    PdfFieldProps,
    'defaultImage' | 'placeholder' | 'pdfMode' | 'enlargeTrigger'
  > = {
    defaultImage: imagePlaceholder,
    placeholder: '-',
    pdfMode: 'thumb',
  };

  // issue#545 支持水印
  buildWatermarkConfig = () => {
    const { watermarkConfig, data } = this.props;
    const { content } = watermarkConfig || {};

    let watermarkContent = content;

    if(Array.isArray(content)) {
      watermarkContent = content.map(item => resolveVariableAndFilter(item, data, '| raw'));
    }

    if(typeof content === 'string') {
      watermarkContent = resolveVariableAndFilter(content, data, '| raw');
    }

    return {
      ...watermarkConfig,
      content: watermarkContent,
      font: {
        ...watermarkConfig,
      }
    }
  }

  @autobind
  handleEnlarge({src, title, caption}: Info) {
    const {onImageEnlarge, enlargeTitle, enlargeCaption} = this.props;

    onImageEnlarge?.(
      {
        type: 'pdf',
        src: src!,
        title: enlargeTitle || title,
        caption: enlargeCaption || caption,
        // FIX issue#545 解决enlargeTrigger 水印不展示问题
        watermarkConfig: this.buildWatermarkConfig(),
      },
      this.props,
    );
  }

  @autobind
  onEnlarge() {
    const pdfThumbInfo = this.getFilteredPdfInfo();
    this.handleEnlarge && this.handleEnlarge(pdfThumbInfo);
  }

  @autobind
  getFilteredPdfInfo(): FilterInfo {
    const {defaultImage, pdfCaption, title, data, src, href} = this.props;

    let finnalSrc = '';
    if (src && typeof src === 'string') {
      finnalSrc = filter(src, data, '| raw');
    } else if (src && typeof src === 'object') {
      finnalSrc = {...src, url: filter(src.url, data, '| raw')};
    }
    let value =
      finnalSrc || getPropValue(this.props) || defaultImage || imagePlaceholder;
    let rawTitle = filter(title, data);
    let caption = filter(pdfCaption, data);

    const finnalHref = href ? filter(href, data, '| raw') : '';

    return {
      src: value,
      title: rawTitle,
      caption,
      href: finnalHref
    };
  }

  @autobind
  handleClick(e: React.MouseEvent<HTMLElement>) {
    const clickAction = this.props.clickAction;
    if (clickAction) {
      handleAction(e, clickAction, this.props);
    }
  }

  render() {
    const {
      className,
      style,
      innerClassName,
      defaultImage,
      pdfCaption,
      title,
      data,
      env,
      pdfClassName,
      thumbClassName,
      height,
      width,
      classnames: cx,
      src,
      href,
      placeholder,
      enlargeAble,
      pdfMode,
      downloadUrl,
      render,
      enlargeTrigger,
      translate: __,
      watermarkConfig,
    } = this.props;

    // 如果配置了放大预览触发器，直接return，其他属性不生效
    if (enlargeTrigger) {
      return render(
        'button',
        {
          label: __('Pdf.view'),
          ...enlargeTrigger,
          type: 'button',
        },
        {
          onClick: () => this.onEnlarge(),
        },
      );
    }

    let finnalSrc = '';
    if (src && typeof src === 'string') {
      finnalSrc = filter(src, data, '| raw');
    } else if (src && typeof src === 'object') {
      finnalSrc = {...src, url: filter(src.url, data, '| raw')};
    }
    let value =
      finnalSrc || getPropValue(this.props) || defaultImage || imagePlaceholder;

    const finnalHref = href ? filter(href, data, '| raw') : '';

    return (
      <div
        className={cx(
          'ImageField',
          pdfMode === 'original' ? 'ImageField--original' : 'ImageField--thumb',
          className,
        )}
        style={style}
        onClick={this.handleClick}
      >
        {value ? (
          <ThemedPdfThumb
            className={innerClassName}
            pdfClassName={pdfClassName}
            thumbClassName={thumbClassName}
            height={height}
            width={width}
            src={value}
            href={finnalHref}
            title={filter(title, data)}
            caption={filter(pdfCaption, data)}
            enlargeAble={enlargeAble && value !== defaultImage}
            onEnlarge={this.onEnlarge}
            pdfMode={pdfMode}
            downloadUrl={downloadUrl}
            data={data}
            watermarkConfig={watermarkConfig}
            env={env}
            buildWatermarkConfig={this.buildWatermarkConfig}
          />
        ) : (
          <span className="text-muted">{placeholder}</span>
        )}
      </div>
    );
  }
}

@Renderer({
  type: 'pdf',
})
export class PdfFieldRenderer extends PdfField {}
