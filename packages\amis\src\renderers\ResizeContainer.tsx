import React from 'react';
import {
  Renderer,
  RendererProps,
  autobind,
  isObject,
} from 'amis-core';
import {ResizeContainer as ResizeWrapper} from 'dataseed-ui';
import { BaseSchema, SchemaCollection } from '../Schema';
import type { ResizeCallback, ResizeStartCallback } from 're-resizable';

/** Container 拖拽改变宽高容器渲染器 */
export interface ContainerSchema extends BaseSchema {
  type: 'container';
  /** 内容 */
  body: SchemaCollection;
  /** 使用的标签 */
  wrapperComponent?: string;
  minWidth?: string | number;
  maxWidth?: string | number;
  className?: string;
  bodyClassName?: string;
  enable?: {
    right: boolean, // 向右拖拽
    left: boolean,  // 向左拖拽
    top: boolean,   // 向上拖拽
    bottom: boolean,  // 向下拖拽
    topRight: boolean,  // 向右上拖拽
    bottomRight: boolean, // 向右下拖拽
    topLeft: boolean, // 向左上拖拽
    bottomLeft: boolean, // 向左下拖拽
  };
  autoFillHeight?: boolean;
  autoFillWidth?: boolean;
  style?: React.CSSProperties;
  /** 是否可折叠 */
  collapsable?: boolean;
}

export interface ContainerProps
  extends RendererProps,
    Omit<ContainerSchema, 'type' | 'className' | 'style'> {
  children?: (props: any) => React.ReactNode;
}

export default class ResizeContainer<T> extends React.Component<
  ContainerProps & T,
  object
> {
  static propsList: Array<string> = ['body', 'className'];
  static defaultProps = {
    minWidth: 32,
    maxWidth: 500,
    minHeight: 32,
    maxHeight: 500,
    enable: {
      right: true,
      left: false,
      top: false,
      bottom: false,
      topRight: false,
      bottomRight: false,
      bottomLeft: false,
      topLeft: false,
    },
    autoFillHeight: false,
    autoFillWidth: false,
  };

  state: any = {
    collapsed: false, // 记录折叠状态
  };

  startSize: any; // 记录拖拽开始的尺寸
  tempSize: any; // 记录拖拽过程中的尺寸
  resizable?: any; // 记录拖拽组件实例
  tempBorderRight: string = ''; // 记录border-right的宽度

  // 拖拽开始回调函数
  @autobind
  handleResizeStart(...rest: Parameters<ResizeStartCallback>) {
    if (!this.props.collapsable) return

    const [, , dom] = rest;
    const rect = dom.getBoundingClientRect();
    // 移除transition过度，避免拖拽过程中出现卡顿
    dom.classList.remove('resize-collapsable');

    this.startSize = {
      width: rect.width, // number 类型
    }
  }

  // 拖拽改变宽高回调函数
  @autobind
  handleResize(...rest: Parameters<ResizeCallback>) {
    if (!this.props.collapsable) return

    const [, , , d] = rest;
    this.tempSize = {
      width: this.startSize.width + d.width,
    }
  }

  @autobind
  handleResizeStop(...rest: Parameters<ResizeCallback>) {
    if (!this.props.collapsable) return

    const [, , dom] = rest;
    if (dom) {
      dom.classList.add('resize-collapsable');
    }
  }

  /**
   * 处理容器的展开和收起
   * @param open 是否展开
   * 
   */
  @autobind
  handleToggleResize(open: boolean) {
    const resizableDom = this.resizable.resizable as HTMLElement;
    const psRight = resizableDom.querySelector('.psRight') as HTMLElement;

    if (open) {
      this.resizable?.updateSize(this.tempSize);
      if (psRight) {
        psRight.style.display = 'block';
        resizableDom.style.borderRight = this.tempBorderRight;
      }
    } else {
      this.resizable?.updateSize({
        width: 0
      })
      if (psRight) {
        psRight.style.display = 'none';
        this.tempBorderRight = getComputedStyle(resizableDom).borderRight;
        resizableDom.style.borderRight = 'none';
        resizableDom.style.minWidth = 'unset'; // FIXME: https://github.com/bokuweb/re-resizable/issues/775
      }
    }

    this.setState({ collapsed: !open })
  }

  // 处理传入的schema，如果配置了 openResizable 或 closeResizable，添加相应事件
  buildFileSchema(schema: any): any {
    if (!schema) return;
    const { collapsed } = this.state;

    if(Array.isArray(schema)) {
      return schema.map((item: any) => this.buildFileSchema(item));
    }

    if(typeof schema === 'string' || typeof schema === 'number') {
      return schema;
    }

    if(isObject(schema)) {
      const result = {
        ...schema,
        body: this.buildFileSchema(schema.body),
        items: this.buildFileSchema(schema.items),
        actions: this.buildFileSchema(schema.actions),
        left: this.buildFileSchema(schema.left),
      }

      if(schema.openResizable) {
        result.onClick = this.handleToggleResize.bind(this, true);
        result.visible = collapsed;
      } else if(schema.closeResizable) {
        result.onClick = this.handleToggleResize.bind(this, false);
        result.visible = !collapsed;
      }

      return result;
    }
  }

  renderBody(): JSX.Element | null {
    const {
      body,
      render,
      classnames: cx,
      bodyClassName,
      disabled,
      collapsable,
    } = this.props;

    const bodySchema = collapsable ? this.buildFileSchema(body) : body;
    const containerBody = body
      ? (render('body', bodySchema, {disabled}) as JSX.Element)
      : null;

    return (
      <div className={cx('Container-body', bodyClassName)}>
        {containerBody}
      </div>
    );
  }

  @autobind
  resizeRef(ref: any) {
    if (ref) {
      while (ref && ref.getWrappedInstance) {
        ref = ref.getWrappedInstance();
      }
      this.resizable = ref;
    } else {
      this.resizable = undefined;
    }

    if (this.props.collapsable && this.resizable) {
      // 记录初始尺寸，避免初始化后直接点收起时，再次点击展开没有宽度
      const rect = this.resizable.resizable?.getBoundingClientRect?.() as DOMRect;
      this.tempSize = {
        width: rect.width, // number 类型
      }
      // 初始化时，设置具体尺寸，避免width: auto时收起没过度动画
      this.resizable?.updateSize(this.tempSize);
    }
  }

  render() {
    const {
      className,
      minWidth,
      maxWidth,
      ...rest
    } = this.props;

    return (
      <ResizeWrapper
        className={className}
        minWidth={minWidth}
        maxWidth={maxWidth}
        onResizeStart={this.handleResizeStart}
        onResize={this.handleResize}
        onResizeStop={this.handleResizeStop}
        ref={this.resizeRef}
        {...rest}
      >
        {this.renderBody()}
      </ResizeWrapper>
    )
  }
}

@Renderer({
  type: 'resize-container'
})
export class ResizeContainerRenderer extends ResizeContainer<{}> {}
