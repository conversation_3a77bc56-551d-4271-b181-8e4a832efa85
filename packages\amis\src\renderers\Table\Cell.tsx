import {
  ColorScale,
  extendObject,
  filter,
  isPureVariable,
  <PERSON><PERSON><PERSON>,
  RendererProps,
  isExpression,
  resolveVariableAndFilter,
  getPropValue,
} from 'amis-core';
import omit from 'lodash/omit';
import React from 'react';
import Ellipsis from './Ellipsis';

export interface TableCellProps extends RendererProps {
  wrapperComponent?: React.ElementType;
  column: any;
  contentsOnly?: boolean;
}

export class TableCell extends React.Component<TableCellProps> {
  static defaultProps = {
    wrapperComponent: 'td',
  };

  readonly propsNeedRemove: string[] = [];

  render() {
    let {
      classnames: cx,
      className,
      classNameExpr,
      style = {},
      wrapperComponent: Component,
      contentsOnly,
      column,
      value,
      data,
      children,
      width,
      align,
      innerClassName,
      label,
      tabIndex,
      onKeyUp,
      rowSpan,
      colSpan,
      body: _body,
      tpl,
      remark,
      cellPrefix,
      cellAffix,
      isHead,
      colIndex,
      row,
      showBadge,
      itemBadge,
      store,
    } = this.props;

    if (isHead) {
      Component = 'th';
    } else {
      Component = Component || 'td';
    }
    const isTableCell = Component === 'td' || Component === 'th';

    const schema = {
      ...column,
      style: column?.innerStyle || {}, // column的innerStyle配置 作为内部组件的style 覆盖column的style
      className: innerClassName,
      type: (column && column.type) || 'plain',
    };
    let isHeighLight: boolean = false;
    // @ts-ignore
    store?.columns.forEach(col => {
      if(col.name === column.name) {
        isHeighLight = col.isHighLight;
      }
    })

    // 如果本来就是 type 为 button，不要删除，其他情况下都应该删除。
    if (schema.type !== 'button' && schema.type !== 'dropdown-button') {
      delete schema.label;
    }

    if (isTableCell) {
      // table Cell 会用 colGroup 来设置宽度，这里不需要再设置
      // 同时剔除style中的定位相关样式，避免表格样式异常
      // TODO: 如果历史column设置了style: {width}将不会生效。删掉这个改动应该没啥影响，因为col的style.width优先级高于td的style.width
      style.width && (style = omit(style, ['width', 'position', 'display']));
    } else if (width) {
      style = {
        ...style,
        width: (style && style.width) || width,
      };
    }

    if (align) {
      style = {
        ...style,
        textAlign: align,
      };
    }

    if (column?.backgroundScale) {
      const backgroundScale = column.backgroundScale;
      let min = backgroundScale.min;
      let max = backgroundScale.max;

      if (isPureVariable(min)) {
        min = resolveVariableAndFilter(min, data, '| raw');
      }
      if (isPureVariable(max)) {
        max = resolveVariableAndFilter(max, data, '| raw');
      }

      if (typeof min === 'undefined') {
        min = Math.min(...data.rows.map((r: any) => r[column.name]));
      }
      if (typeof max === 'undefined') {
        max = Math.max(...data.rows.map((r: any) => r[column.name]));
      }

      const colorScale = new ColorScale(
        min,
        max,
        backgroundScale.colors || ['#FFEF9C', '#FF7127'],
      );
      let value = data[column.name];
      if (isPureVariable(backgroundScale.source)) {
        value = resolveVariableAndFilter(backgroundScale.source, data, '| raw');
      }

      const color = colorScale.getColor(Number(value)).toHexString();
      style.background = color;
    }

    const body = children || getPropValue(this.props);

    return (
      <Component
        rowSpan={rowSpan > 1 ? rowSpan : undefined}
        colSpan={colSpan > 1 ? colSpan : undefined}
        data-name={isExpression(column.name) ? resolveVariableAndFilter(column.name, data) : column.name}
        style={style}
        className={cx(
          className,
          {
            'is-highlight': isHeighLight,
          },
          column?.classNameExpr ? filter(column.classNameExpr, data) : null,
        )}
        tabIndex={tabIndex}
        onKeyUp={onKeyUp}
      >
        {cellPrefix}
        {body}
        {cellAffix}
      </Component>
    );
  }
}

@Ellipsis()
export class Cell extends TableCell {}
