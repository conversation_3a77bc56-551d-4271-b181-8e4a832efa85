import React from 'react';

import { ITableStore,RendererProps,Schema, flattenTree, isObject as amisIsObject } from 'amis-core'; // lodash的isObject传入数组会返回true
import type { IColumn } from 'amis-core/lib/store/table';
import cloneDeep from 'lodash/cloneDeep';
import isObject from 'lodash/isObject';
import { observer } from 'mobx-react';

export interface HeadSearchProps extends RendererProps {
  className?: string;
  columns?: Array<IColumn>;
  store: ITableStore;
  onQuery: (values: object) => void;
}

@observer
export class HeadSearchRender extends React.Component<HeadSearchProps> {
  buildSchema = ({
    searchable,
    label,
    name,
    value,
  }: {
    searchable: boolean | Schema;
    label?: string;
    name?: string;
    value?: any;
  }) => {
    const { columnSearchFormTooltip = {}  } = this.props;

    let schema: any;

    if (searchable === true) {
      schema = {
        title: '',
        controls: [
          {
            type: 'text',
            name,
            placeholder: label,
            label,
            clearable: true,
            staticSchema: [
              {
                ...columnSearchFormTooltip,
                visibleOn: `\${${name}.length > 20}`,
                type: "tooltip-wrapper",
                content: `\${${name}}`,
                body: `\${${name}|truncate:20}`,
              },
              {
                visibleOn: `\${${name}.length <= 20}`,
                type: 'text',
                name,
              }
            ]
          }
        ]
      };
    } else if (searchable) {
      if (searchable.controls || searchable.tabs || searchable.fieldSet) {
        schema = {
          title: '',
          ...searchable,
          controls: Array.isArray(searchable.controls)
            ? searchable.controls.slice()
            : undefined
        };
      } else {
        let renderValue = value;
        const valueField = searchable.valueField || 'value';
        const labelField = searchable.labelField || 'label';
        const delimiter = searchable?.delimiter || ',';
         // options 存在的下拉，此处处理下拉框场景，树下拉等组件没配置joinValues的场景
         // 如果options是通过接口获取的，此处无法处理，需要开发自行将joinValues设置为false
        if(searchable.options && searchable.joinValues !== false) {
          // object类型的options转换成数组处理
          let options = amisIsObject(searchable.options) ? Object.entries(searchable.options).map(([value, label]) => ({
            [valueField]: value,
            [labelField]: label,
          })) : searchable.options;

          options = flattenTree(options);
          
          renderValue = Array.isArray(renderValue) 
            ? renderValue
            : typeof renderValue === 'string' ?
              renderValue?.split(delimiter)
              : [renderValue];

          renderValue = renderValue
            ?.map((item: any) => {
              // 这里统一转换成字符串是因为value为数字且多选的场景会被统一修改为字符串
              // ?? 是为了考虑 0 的情况
              const option = options.find(option => (option?.[valueField] ?? option).toString() === item.toString())
              return option?.[labelField] || item;
            })?.join(delimiter);

        } else if (searchable.joinValues === false) {
          // 如果下拉类型组件数据源配置的是接口，需要开发自行将joinValues设置为false，这里处理这种场景
          // PS：下拉框数据源如果来自接口，不将joinValues设置为false的情况下列过滤只会展示code
          renderValue = Array.isArray(renderValue) ? renderValue?.map((item: any) => item?.[labelField] || item)?.join(delimiter) : renderValue;
        }

        // 基本表单中需要特殊处理的两个表单项，input-quarter-range、input-tag
        // input-tag: 自身static模式是tag风格，长度超过20会统一使用text展示，为保证风格统一，需将长度小于20的input-tag一样使用text展示静态模式
        // input-quarter-range: 季度选择器的值类型是时间戳，两时间戳的长度加起来会超过20，但是这个场景不需要走进staticSchema逻辑（走进去会渲染时间戳）
        schema = {
          title: '',
          className: searchable.formClassName,
          controls: [
            {
              ...searchable,
              // 保证基础表单展示风格统一，input-tag 使用text渲染
              type: searchable.type !== 'input-tag' ? searchable.type || 'text' : 'text',
              name: searchable.name || name,
              placeholder: label,
              label,
              staticSchema: searchable.type !== 'input-quarter-range' && renderValue.length > 20 ? [
                {
                  ...searchable,
                  ...columnSearchFormTooltip,
                  type: "tooltip-wrapper",
                  content: renderValue,
                  body: `${renderValue?.slice(0, 20)}...`,
                }
              ] : undefined,
            },
          ],
        };
      }
    }

    if (schema) {
      schema = {
        ...schema,
        type: 'form',
        wrapWithPanel: false,
        static: true,
        mode: 'horizontal',
        labelWidth: 'auto'
      };
    }
    
    return schema || 'error';
  };

  // 重置某一列的筛选条件
  resetValueByName = (name: string) => {
    const {store, onQuery, flatHeadSearchable} = this.props;
   
    const resetData = { [name]: undefined };
    // 如果是对象并且存在 _isMoreFields 那么说明是列过滤中配置了多个字段
    // @ts-ignore
    if(amisIsObject(store.searchFormData[name]) && store.searchFormData[name]._isMoreFields) {
      // @ts-ignore
      Object.keys(store.searchFormData[name]).map(key => {
        resetData[key] = undefined;
      })
    }

    const searchFormData = {
      ...store.searchFormData,
      ...resetData,
    };
    store.update({
      searchFormData
    });

    // 搜索的参数需要将参数拍平
    const query: Record<string, any> = {}
    Object.keys(searchFormData).forEach(key => {
      // @ts-ignore
      const value = searchFormData[key];
      if(flatHeadSearchable && amisIsObject(value) && value._isMoreFields) {
        Object.keys(value).forEach(k => {
          if(k !== '_isMoreFields') {
            query[k] = value[k];
          }
        })
      } else {
        query[key] = value;
      }
    })
    onQuery(query);
  };

  // 重置所有列的筛选条件
  reset = (needResetKeys: any, isAction: boolean = false, needSearch: boolean = true) => {
    if (!isAction && !needResetKeys?.length) {
      return;
    }

    const {store, onQuery, unsetQueryParams = [], flatHeadSearchable} = this.props;
    const searchFormData: {[propName: string]: any} = {};
    // 将列过滤中收集的字段置空，然后同步到 query 中
    Object.keys(store.searchFormData).forEach(key => {
      // 不是动作触发的话情况所有字段
      // 动作触发的话查看 needResetKeys 长度为 0 的时候情况清除所有字段
      const flag = isAction ? (!needResetKeys?.length || needResetKeys.includes(key)) : store.searchFormData[key] !== undefined;
      if (!unsetQueryParams.includes(key) && flag) {
        const value = store.searchFormData[key];
        // 如果是多个字段并且配置了拍平需要将收集的对象中的字段拍平设置为空，不然只重置自己字段就行
        if(flatHeadSearchable && amisIsObject(value) && value?._isMoreFields) {
          Object.keys(value).forEach(k => {
            if(k !== '_isMoreFields') {
              searchFormData[k] = undefined;
            }
          })
          searchFormData[key] = undefined;
        } else {
          searchFormData[key] = undefined;
        }
      }
    });

    store.update({
      searchFormData
    });

    if(!needSearch) {
      return
    }

    // 搜索的参数需要将参数拍平
    const query: Record<string, any> = {}
    Object.keys(searchFormData).forEach(key => {
      // @ts-ignore
      const value = searchFormData[key];
      if(flatHeadSearchable && amisIsObject(value) && value._isMoreFields) {
        Object.keys(value).forEach(k => {
          if(k !== '_isMoreFields') {
            query[k] = value[k];
          }
        })
      } else {
        query[key] = value;
      }
    })

    onQuery(query);
  };

  render() {
    const {
      classnames: cx,
      className,
      render,
      columns,
      store,
      autoGenerateFilter,
      translate: __,
    } = this.props;
    const {searchFormData} = store;

    // 处理属于查询条件，但是不属于columns头部的搜索条件过滤到不展示在已选条件中
    const newsearchFormData: {[propName: string]: any} = {};
    const seachFormKeyData = Object.keys(searchFormData || {});

    columns?.forEach(column => {
      if ((autoGenerateFilter && column.headSearchable) || (!autoGenerateFilter && (column.headSearchable || column.searchable))) {
        const name = (column.searchable || column.headSearchable)?.name || column?.name;
        if (seachFormKeyData.includes(name)) {
          const value = searchFormData?.[name];
          const typename = typeof value;
          // 过滤数据，空数据不渲染在表头
          if (typename !== 'function' && (typename === 'boolean' || typename === 'number' || (Array.isArray(value) && value.length) || (isObject(value) && Object.keys(value).length) || !!value)) {
            newsearchFormData[name] = {
              value,
              label: (column.searchable || column.headSearchable)?.label || column?.label || '',
              name: (column.searchable || column.headSearchable)?.name || column?.name || '',
              searchable: column.searchable || column.headSearchable
            };
          }
        }
      }
    });

    return (
      <>
        {columns?.length && Object.values(newsearchFormData).length ? (
          <div className={cx('Table-searchHeader', className)}>
            <div className={cx('Table-searchHeader-wrapper')}>
              <span>{__('Table.searchHeader')}</span>
              <div>
                {Object.entries(newsearchFormData).map(([key, val]) => {
                  const {value, name, label, searchable} = val || {};

                  if (typeof value === 'undefined') {
                    return null;
                  }

                  const data = amisIsObject(value) ? value : { [name]: value };

                  if (searchable) {
                    return (
                      <div key={key} className={cx('Table-searchHeader-item')}>
                        <div className="content">
                          {render(
                            `headsearch/${name}`,
                            this.buildSchema({
                              searchable,
                              label,
                              name,
                              value,
                            }),
                            {
                              data
                            },
                          )}
                        </div>
                        <div
                          data-tooltip={__('delete')}
                          data-position="bottom"
                          className="close"
                          onClick={() => this.resetValueByName(name)}
                        >
                          x
                        </div>
                      </div>
                    );
                  }
                  return null;
                })}
                <div
                  className={cx('Table-searchHeader-reset')}
                  onClick={() => this.reset(Object.keys(newsearchFormData))}
                >
                  <i className="far fa-refresh" aria-hidden="true"></i>
                  清空
                </div>
              </div>
            </div>
          </div>
        ) : null}
      </>
    );
  }
}
