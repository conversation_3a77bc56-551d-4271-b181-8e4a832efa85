import {ClassNamesFn} from 'amis-core';
import React from 'react';

import {
  ActionObject,
  createObject,
  filter,
  isObject,
  LocaleProps,
  SchemaNode,
  guid,
  RendererEvent,
} from 'amis-core';
import type {IColumn, IRow, ITableStore} from 'amis-core/lib/store/table';
import {observer} from 'mobx-react';
import {ActionSchema} from '../Action';
import TableRow from './TableRow';

// 生成subTable子表格的id
export const genSubTableId = (tableId: string | number | undefined, rowIndex: number) => `${tableId ? tableId + '-' : ''}${rowIndex}`;
// 生成subTable子表格service容器的id
export const genSubTableServiceId = (tableId: string | number | undefined, rowIndex: number) => `service${tableId ? tableId : ''}-${rowIndex}`;

export interface TableBodyProps extends LocaleProps {
  store: ITableStore;
  className?: string;
  rowsProps?: any;
  tableClassName?: string;
  classnames: ClassNamesFn;
  columns: Array<IColumn>;
  rows: Array<IRow>;
  render: (region: string, node: SchemaNode, props?: any) => JSX.Element;
  renderCell: (
    region: string,
    column: IColumn,
    item: IRow,
    props: any,
  ) => React.ReactNode;
  onCheck: (item: IRow, value: boolean, shift?: boolean) => void;
  onRowClick: (item: IRow, index: number) => Promise<RendererEvent<any> | void>;
  onRowDbClick: (
    item: IRow,
    index: number
  ) => Promise<RendererEvent<any> | void>;
  onQuickChange?: (
    item: IRow,
    values: object,
    saveImmediately?: boolean | any,
    savePristine?: boolean,
  ) => void;
  footable?: boolean;
  ignoreFootableContent?: boolean;
  footableColumns: Array<IColumn>;
  checkOnItemClick?: boolean;
  buildItemProps?: (item: IRow, index: number) => any;
  onAction?: (e: React.UIEvent<any>, action: ActionObject, ctx: object) => void;
  rowClassNameExpr?: string;
  rowClassName?: string;
  affixRowClassName?: string;
  prefixRowClassName?: string;
  data?: any;
  prefixRow?: Array<any>;
  affixRow?: Array<any>;
  itemAction?: ActionSchema;
  subTable?: SchemaNode;
  subTableOperationIndex?: number;
  subTableAddRowIndex?: number;
  isCreateModeOfSubTable?: boolean;
  valueField?: string;
  mountAll?: boolean;
  tableKey?: string;
  tableId?: string;
  offset?: number;
  columnPosition: string;
  updateAllRows: boolean;
}

@observer
export class TableBody extends React.Component<TableBodyProps> {
  componentDidMount(): void {
    // fix: https://github.com/baidu/amis/pull/7735
    this.props.store.initTableWidth();
  }

  renderRows(
    rows: Array<any>,
    columns = this.props.columns,
    rowProps: any = {},
  ): any {
    const {
      rowClassName,
      rowClassNameExpr,
      onAction,
      buildItemProps,
      checkOnItemClick,
      classnames: cx,
      render,
      renderCell,
      onCheck,
      onRowClick,
      onRowDbClick,
      onQuickChange,
      footable,
      ignoreFootableContent,
      footableColumns,
      itemAction,
      subTable,
      subTableOperationIndex,
      subTableAddRowIndex,
      isCreateModeOfSubTable,
      valueField = 'value',
      mountAll,
      tableKey,
      tableId,
      store,
      offset,
      columnPosition,
      updateAllRows,
    } = this.props;

    // 嵌套表格场景，rowIndex不同层级，会相同
    const domRows = rows.map((item: IRow, rowIndex: number) => {
      const itemProps = buildItemProps ? buildItemProps(item, rowIndex) : null;
      const itemClassName = cx(
        rowClassNameExpr ? filter(rowClassNameExpr, item.data) : rowClassName,
      );
      // rowKey: 行的唯一key[嵌套子表格，children发生变化，会重新生成id, 影响子表格输入时会失焦]
      const key = item?.pristine?.[valueField] || item.id;
      const subKey = guid();

      const doms = [
        <TableRow
          {...itemProps}
          itemAction={itemAction}
          classnames={cx}
          store={store}
          checkOnItemClick={checkOnItemClick}
          key={key}
          rowKey={key}
          itemIndex={rowIndex}
          item={item}
          offset={offset}
          itemClassName={cx(
            rowClassNameExpr
              ? filter(rowClassNameExpr, item.data)
              : rowClassName,
            {
              // is-last类名控制竖线高度只有一半，需同时满足以下条件：
              // 1. 当前行层级大于1
              // 2. 当前行是当前表格最后一行
              // 3. 当前行没有子项或子项未展开
              'is-last':
                item.depth > 1 &&
                rowIndex === rows.length - 1 &&
                (!item.children.length || !item.expanded),
            },
          )}
          columns={columns}
          renderCell={renderCell}
          render={render}
          onAction={onAction}
          onCheck={onCheck}
          // todo 先注释 quickEditEnabled={item.depth === 1}
          onRowClick={onRowClick}
          onRowDbClick={onRowDbClick}
          onQuickChange={onQuickChange}
          columnPosition={columnPosition}
          updateAllRows={updateAllRows}
          {...rowProps}
        />,
      ];

      if (footable && footableColumns.length) {
        if (item.depth === 1) {
          doms.push(
            <TableRow
              {...itemProps}
              itemAction={itemAction}
              classnames={cx}
              store={store}
              checkOnItemClick={checkOnItemClick}
              key={`foot-${key}`}
              itemIndex={rowIndex}
              item={item}
              itemClassName={cx(
                rowClassNameExpr
                  ? filter(rowClassNameExpr, item.data)
                  : rowClassName,
              )}
              offset={offset}
              columns={footableColumns}
              renderCell={renderCell}
              render={render}
              onAction={onAction}
              onCheck={onCheck}
              onRowClick={onRowClick}
              onRowDbClick={onRowDbClick}
              footableMode
              footableColSpan={columns.length}
              onQuickChange={onQuickChange}
              ignoreFootableContent={ignoreFootableContent}
              updateAllRows={updateAllRows}
              {...rowProps}
            />,
          );
        }
      } else if (subTable && (mountAll || item.expanded)) {
        if (columnPosition === "fixed") {
          doms.push(<tr
            key={`sub-${key}`}
            data-index={subKey}
            data-id={subKey}
            className={cx(
              itemClassName,
              'sub-table-row',
              {
                // 'is-hovered': item.isHover,
                // 'is-checked': item.checked,
                // 'is-modified': item.modified,
                // 'is-moved': item.moved,
                // 'is-expanded': item.expanded && item.expandable,
                // 'is-expandable': item.expandable,
                [`Table-tr--hasItemAction`]: itemAction,
                [`Table-tr--odd`]: rowIndex % 2 === 0,
                [`Table-tr--even`]: rowIndex % 2 === 1,
              },
              `Table-tr--${item.depth}th`,
            )}
          >
            <td></td>
          </tr>)
          return doms;
        }
        // 嵌套子元素组件
        let subItemNode: any = subTable;
        if (typeof subTable !== 'string' && isObject(subTable)) {
          const subTables: any = {...subTable};
          subTables.className = mountAll
            ? `sub-Table ${
                item.expanded ? 'sub-Table-expanded' : 'sub-Table-expandable'
              }`
            : 'sub-Table';

          // 构造subTable嵌套子表格的id，形式：最外层table的id + 路径，比如：tableId-0-1
          subTables.id = genSubTableId(tableId, rowIndex);
          subTables.parent = item;
          subTables.source = subTables.source || '$children';
          subTables.type = subTables?.type || 'table';

          // 嵌套子表格如果设置api了，需要调用接口
          if (subTables.type === 'table' && subTables.api) {
            const subTableServiceId = genSubTableServiceId(tableId, rowIndex);
            subItemNode = {
              type: 'service',
              api: subTables.api,
              id: subTableServiceId,
              className: 'sub-Table',
              parent: item,
              body: subTables,
              onEvent: subTables.onEvent,
              $$serviceId: subTableServiceId, // 内置service容器的id，方便下面获取
            };
          } else if (tableKey === 'input-table') { // 如果顶层是input-table
            subTables.subTableOperationIndex = subTableOperationIndex;
            subTables.subTableAddRowIndex = subTableAddRowIndex;
            subTables.isCreateModeOfSubTable = isCreateModeOfSubTable;
            // 在inputTable里只认children，source无效，否则无法通过name映射修改上层数据
            subTables.source = '';
            // inputTable设计重新改造，使用tableCell方式接管自定义渲染
            subTables.name = 'children'  // 为了inputTable多层级嵌套后，children命名，直接会由form改变上层children数据
            subItemNode = subTables;
          } else {
            subItemNode = subTables;
          }
        }

        // 区分input-table 和 其他使用到table的
        if (tableKey !== 'input-table') {
          // 获取subTable的id，如果subTable被service包裹，则先取body的id
          const _subTableId = subItemNode?.body?.id ?? subItemNode?.id;
          // 获取内置service容器的id，上面定义好的$$serviceId
          const _subTableServiceId = subItemNode.$$serviceId;

          doms.push(
            <tr
              key={`sub-${key}`}
              data-index={subKey}
              data-id={subKey}
              className={cx(
                itemClassName,
                'sub-table-row',
                {
                  // 'is-hovered': item.isHover,
                  // 'is-checked': item.checked,
                  // 'is-modified': item.modified,
                  // 'is-moved': item.moved,
                  // 'is-expanded': item.expanded && item.expandable,
                  // 'is-expandable': item.expandable,
                  [`Table-tr--hasItemAction`]: itemAction,
                  [`Table-tr--odd`]: rowIndex % 2 === 0,
                  [`Table-tr--even`]: rowIndex % 2 === 1,
                },
                `Table-tr--${item.depth}th`,
              )}
            >
              <td colSpan={columns.length}>
                {this.props.render(`subTable/${key}`, subItemNode, {
                  ...rowProps,
                  parent: item,
                  data: {
                    ...(item.data || {}),
                    parent: item,
                    _subTableId, // 子表格id标识放到data里，subTable中可通过表达式获取
                    _subTableServiceId, // 子表格service id标识放到data里，subTable中可通过表达式获取
                  },
                })}
              </td>
            </tr>,
          );
        } else if (tableKey === 'input-table' && item?.data?.children?.length > 0) {
            /**
             * 缓存数据节点和信息，然后将节点传递到内部，内部独立渲染
             * 复用TableRow标签，将原本设计使用的信息全都接管过去
             */
            let temp: any = {
              node: {
                ...subItemNode,
                // 标识自定义标签
                type: `internalCustomization_${subItemNode.type}`
              },
              props: {
                ...rowProps,
                parent: item,
              }
            }
            doms.push(
              <TableRow
                {...itemProps}
                itemAction={itemAction}
                classnames={cx}
                store={store}
                checkOnItemClick={checkOnItemClick}
                key={`foot-${key}`}
                itemIndex={rowIndex}
                item={item}
                itemClassName={cx(
                  rowClassNameExpr
                    ? filter(rowClassNameExpr, item.data)
                    : rowClassName,
                )}
                offset={offset}
                columns={footableColumns}
                renderCell={renderCell}
                render={render}
                onAction={onAction}
                onCheck={onCheck}
                onRowClick={onRowClick}
                onRowDbClick={onRowDbClick}
                footableMode
                footableColSpan={columns.length}
                onQuickChange={onQuickChange}
                ignoreFootableContent={ignoreFootableContent}
                updateAllRows={updateAllRows}
                {...rowProps}
                extraNode={temp}
              />,
            )
        }
      } else if (item.children.length && (mountAll || item.expanded)) {
        // if (columnPosition === "fixed") {
        //   return doms;
        // }
        // 嵌套表格
        (tableKey !== 'input-table' ||
          (tableKey === 'input-table' && item.children.length > 0)) &&
          doms.push(
            ...this.renderRows(item.children, columns, {
              ...rowProps,
              parent: item,
            }),
          );
      }
      return doms;
    });

    return domRows;
  }

  renderSummaryRow(
    position: 'prefix' | 'affix',
    items?: Array<any>,
    rowIndex?: number,
  ) {
    const {
      columns,
      render,
      data,
      classnames: cx,
      rows,
      prefixRowClassName,
      affixRowClassName,
      store,
    } = this.props;

    if (!(Array.isArray(items) && items.length)) {
      return null;
    }

    let offset = 0;

    // 遍历总结行中配置的列，将column中列的隐藏对应的把总结行也隐藏起来
    const result: any[] = items
      .map((item, index) => {
        let colIdxs: number[] = [offset + index]; // 收集总结行中列的索引集合
        // 如果存在合并列的情况，合并后面的列
        if (item.colSpan > 1) {
          for (let i = 1; i < item.colSpan; i++) {
            colIdxs.push(offset + index + i);
          }
          offset += item.colSpan - 1;
        }

        // 总结列中的列匹配对应的column。rawIndex：原始列的索引
        const matchedColumns = colIdxs
          .map(idx => columns.find(col => col.rawIndex === idx))
          .filter(item => item);

        return {
          ...item,
          colSpan: matchedColumns.length,
          firstColumn: matchedColumns[0],
          lastColumn: matchedColumns[matchedColumns.length - 1]
        };
      })
      .filter(item => item.colSpan);

    // 如果是勾选栏，或者是展开栏，或者是拖拽栏，让它和下一列合并。
    columns.forEach((column, idx) => {
      if (
        result[0] &&
        typeof column?.type === 'string' &&
        column?.type.substring(0, 2) === '__'
      ) {
        const innerColumn = {
          ...columns[idx],
          type: 'plain',
          className: 'Table-cell--inner',
          firstColumn: columns[idx],
          lastColumn: columns[idx],
          fixed: 'left',
        };
        if (result[0]?.pristine?.type?.substring(0, 2) === '__') {
          // 插入到result第idx个
          result.splice(idx, 0, innerColumn);
        } else {
          result.unshift(innerColumn)
        }
      }
    })

    // 缺少的单元格补齐
    let appendLen =
      columns.length - result.reduce((p, c) => p + (c.colSpan || 1), 0);

    // 多了则干掉一些
    while (appendLen < 0) {
      const item = result.pop();
      if (!item) {
        break;
      }
      appendLen += item.colSpan || 1;
    }

    // 少了则补个空的
    if (appendLen) {
      const item = {
        type: 'html',
        html: '&nbsp;'
      };
      const column = store.filteredColumns[store.filteredColumns.length - 1];
      result.push({
        ...item,
        colSpan: appendLen,
        firstColumn: column,
        lastColumn: column
      });
    }

    const ctx = createObject(data, {
      items: rows.map(row => row.locals),
    });

    return (
      <tr
        className={cx(
          'Table-tr',
          'is-summary',
          position === 'prefix' ? prefixRowClassName : '',
          position === 'affix' ? affixRowClassName : ''
        )}
        key={`summary-${position}-${rowIndex || 0}`}
      >
        {result.map((item, index) => {
          const Com = item.isHead ? 'th' : 'td';
          const firstColumn = item.firstColumn;
          const lastColumn = item.lastColumn;

          const style = {...item.style};
          if (item.align) {
            style.textAlign = item.align;
          }
          const [stickyStyle, stickyClassName] = store.getStickyStyles(
            // 如果是右侧固定列，则取最后一个，否则取第一个
            lastColumn?.fixed === 'right' ? lastColumn : firstColumn,
            store.filteredColumns
          );
          Object.assign(style, stickyStyle);

          return (
            <Com
              key={index}
              colSpan={item.colSpan == 1 ? undefined : item.colSpan}
              style={style}
              className={(item.cellClassName || '') + ' ' + stickyClassName}
            >
              {render(`summary-row/${index}`, item, {
                data: ctx
              })}
            </Com>
          );
        })}
      </tr>
    );
  }

  renderSummary(position: 'prefix' | 'affix', items?: Array<any>) {
    return Array.isArray(items)
      ? items.some(i => Array.isArray(i))
        ? items.map((i, rowIndex) =>
            this.renderSummaryRow(
              position,
              Array.isArray(i) ? i : [i],
              rowIndex,
            ),
          )
        : this.renderSummaryRow(position, items)
      : null;
  }

  render() {
    const {
      classnames: cx,
      className,
      render,
      rows,
      columns,
      rowsProps,
      prefixRow,
      affixRow,
      translate: __,
    } = this.props;

    return (
      <tbody className={className}>
        {rows.length ? (
          <>
            {this.renderSummary('prefix', prefixRow)}
            {this.renderRows(rows, columns, rowsProps)}
            {this.renderSummary('affix', affixRow)}
          </>
        ) : null}
      </tbody>
    );
  }
}
