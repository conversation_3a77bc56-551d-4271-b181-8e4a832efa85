import {
  ColorScale,
  extendObject,
  filter,
  isPureVariable,
  Ren<PERSON><PERSON>,
  RendererProps,
  isExpression,
  shallowEqual,
  resolveVariableAndFilter,
} from 'amis-core';
import {Badge} from 'amis-ui';
import omit from 'lodash/omit';
import {observer} from 'mobx-react';
import React from 'react';
import Copyable from '../Copyable';
import PopOverable from '../PopOver';
import QuickEdit from '../QuickEdit';
import Ellipsis from '../Ellipsis';

export interface TableCellProps extends RendererProps {
  wrapperComponent?: React.ElementType;
  column: any;
  contentsOnly?: boolean;
}

class TableCellWrap extends React.Component<TableCellProps> {

  shouldComponentUpdate(nextProps: Readonly<TableCellProps>): boolean {
    // 全选场景下不重新渲染td
    if (nextProps.allChecked !== this.props.allChecked) {
      return false;
    }

    return true;
  }

  render() {
    let {
      classnames: cx,
      className,
      classNameExpr,
      render,
      style = {},
      wrapperComponent: Component,
      contentsOnly,
      column,
      value,
      data,
      children,
      width,
      align,
      innerClassName,
      label,
      tabIndex,
      onKeyUp,
      rowSpan,
      colSpan,
      body: _body,
      tpl,
      remark,
      cellPrefix,
      cellAffix,
      isHead,
      colIndex,
      row,
      showBadge,
      itemBadge,
      store,
      ...rest
    } = this.props;

    if (isHead) {
      Component = 'th';
    } else {
      Component = Component || 'td';
    }
    const isTableCell = Component === 'td' || Component === 'th';

    const schema = {
      ...column,
      style: column?.innerStyle || {}, // column的innerStyle配置 作为内部组件的style 覆盖column的style
      className: innerClassName,
      type: (column && column.type) || 'plain',
    };
    const canAccessSuperData = schema?.canAccessSuperData !== false;
    let isHeighLight: boolean = false;
    // @ts-ignore
    store.columns.forEach(col => {
      if(col.name === column.name) {
        isHeighLight = col.isHighLight;
      }
    })

    // 如果本来就是 type 为 button，不要删除，其他情况下都应该删除。
    if (schema.type !== 'button' && schema.type !== 'dropdown-button') {
      delete schema.label;
    }

    let body = children
      ? children
      : render('field', schema, {
          ...omit(rest, Object.keys(schema), this.propsNeedRemove),
          // inputOnly 属性不能传递给子组件，在 SchemaRenderer.renderChild 中处理掉了
          inputOnly: true,
          /** value没有返回值时设置默认值，避免错误获取到父级数据域的值 */
          value: canAccessSuperData ? value : value ?? '',
          // data: createObject(data, {expanded: row.expanded}),
          /** 为了暴露_amisExpanded给到行数据域中，便于自定义功能，且不影响数据域链路 */
          data: extendObject(data, {_amisExpanded: row.expanded}),
        });

    if (isTableCell) {
      // table Cell 会用 colGroup 来设置宽度，这里不需要再设置
      // 同时剔除style中的定位相关样式，避免表格样式异常
      // TODO: 如果历史column设置了style: {width}将不会生效。删掉这个改动应该没啥影响，因为col的style.width优先级高于td的style.width
      style.width && (style = omit(style, ['width', 'position', 'display']));
    } else if (width) {
      style = {
        ...style,
        width: (style && style.width) || width,
      };
    }

    if (align) {
      style = {
        ...style,
        textAlign: align,
      };
    }

    if (column?.backgroundScale) {
      const backgroundScale = column.backgroundScale;
      let min = backgroundScale.min;
      let max = backgroundScale.max;

      if (isPureVariable(min)) {
        min = resolveVariableAndFilter(min, data, '| raw');
      }
      if (isPureVariable(max)) {
        max = resolveVariableAndFilter(max, data, '| raw');
      }

      if (typeof min === 'undefined') {
        min = Math.min(...data.rows.map((r: any) => r[column.name]));
      }
      if (typeof max === 'undefined') {
        max = Math.max(...data.rows.map((r: any) => r[column.name]));
      }

      const colorScale = new ColorScale(
        min,
        max,
        backgroundScale.colors || ['#FFEF9C', '#FF7127'],
      );
      let value = data[column.name];
      if (isPureVariable(backgroundScale.source)) {
        value = resolveVariableAndFilter(backgroundScale.source, data, '| raw');
      }

      const color = colorScale.getColor(Number(value)).toHexString();
      style.background = color;
    }

    if (contentsOnly) {
      return body as JSX.Element;
    }

    return (
      <Component
        rowSpan={rowSpan > 1 ? rowSpan : undefined}
        colSpan={colSpan > 1 ? colSpan : undefined}
        data-name={isExpression(column.name) ? resolveVariableAndFilter(column.name, data) : column.name}
        style={style}
        className={cx(
          className,
          {
            'is-highlight': isHeighLight,
          },
          column?.classNameExpr ? filter(column.classNameExpr, data) : null,
        )}
        tabIndex={tabIndex}
        onKeyUp={onKeyUp}
      >
        {showBadge ? (
          <Badge
            classnames={cx}
            badge={{
              ...itemBadge,
              className: cx(`Table-badge`, itemBadge?.className),
            }}
            data={row.data}
          />
        ) : null}
        {cellPrefix}
        {body}
        {cellAffix}
      </Component>
    );
  }
}

export class TableCell extends React.Component<TableCellProps> {
  static defaultProps = {
    wrapperComponent: 'td',
  };

  static propsList: Array<string> = [
    'type',
    'label',
    'column',
    'body',
    'tpl',
    'rowSpan',
    'colSpan',
    'remark',
    'contentsOnly',
  ];

  readonly propsNeedRemove: string[] = [];

  render() {
    const allChecked = this.props.store?.allChecked ?? false;

    return <TableCellWrap {...this.props} allChecked={allChecked} />;
  }
}

@Renderer({
  test: /(^|\/)table\/(?:.*\/)?cell$/,
  name: 'table-cell',
})
@QuickEdit()
@PopOverable({
  targetOutter: true,
})
@Copyable()
// @Ellipsis()
@observer
export class TableCellRenderer extends TableCell {

  static propsList = [
    'quickEdit',
    'quickEditEnabledOn',
    'popOver',
    'copyable',
    'inline',
    ...TableCell.propsList,
  ];
}

@Renderer({
  type: 'field',
  name: 'field',
})
// @Ellipsis()
@PopOverable()
@Copyable()
export class FieldRenderer extends TableCell {
  static defaultProps = {
    ...TableCell.defaultProps,
    wrapperComponent: 'div',
  };
}
