import {
ActionObject,anyChanged,autobind,autoGetRenderColumnsNum,createObject,difference,eachTree,evalExpression,filter,getScrollParent,isArrayChildrenModified,IScopedContext,isExpression,isObject,isPureVariable,ITableStore,noop,padArr,Renderer,RendererProps,resizeSensor,resolveVariable,
resolveVariableAndFilter,Schema,SchemaExpression,SchemaNode,ScopedContext,TableStore,position,getMatchedEventTargets,extendObject,
isElementInViewport,
scrollElementIntoView,
getPropValue
} from 'amis-core';
import { BadgeObject,Button,Checkbox,Icon,Spinner,SpinnerExtraProps } from 'amis-ui';
import debounce from 'lodash/debounce';
import find from 'lodash/find';
import forEach from 'lodash/forEach';
import isEqual from 'lodash/isEqual';
import isPlainObject from 'lodash/isPlainObject';
import { isAlive } from 'mobx-state-tree';
import React from 'react';
import { findDOMNode } from 'react-dom';
import Sortable from 'sortablejs';
import {
BaseSchema,
SchemaApi,
SchemaClassName,
SchemaObject,
SchemaTokenizeableString,SchemaTpl
} from '../../Schema';
import { SchemaCopyable } from '../Copyable';
import { SchemaPopOver } from '../PopOver';
import { SchemaQuickEdit } from '../QuickEdit';
import { SchemaRemark } from '../Remark';
import ColumnToggler from './ColumnToggler';
import { HeadCellFilterDropDown } from './HeadCellFilterDropdown';
import { HeadCellSearchDropDown } from './HeadCellSearchDropdown';
import { HeadSearchRender } from './HeadSeachRender';
import { genSubTableId, genSubTableServiceId, } from './TableBody';
import { TableCell } from './TableCell';
import { TableContent } from './TableContent';

import type { IColumn,IRow } from 'amis-core';
import { getStyleNumber,offset } from 'amis-core';
import intersection from 'lodash/intersection';
import { IconSchema } from '../Icon';
import { exportExcel } from './exportExcel';
import { BatchedRender } from './BatchedRender';

/**
 * 表格列，不指定类型时默认为文本类型。
 */
export type TableColumnObject = {
  /**
   * 列标题
   */
  label: string;

  /**
   * 配置是否固定当前列
   */
  fixed?: 'left' | 'right' | 'none';

  /**
   * 绑定字段名
   */
  name?: string;

  /**
   * 配置查看详情功能
   */
  popOver?: SchemaPopOver;

  /**
   * 配置快速编辑功能
   */
  quickEdit?: SchemaQuickEdit;

  /**
   * 作为表单项时，可以单独配置编辑时的快速编辑面板。
   */
  quickEditOnUpdate?: SchemaQuickEdit;

  /**
   * 配置点击复制功能
   */
  copyable?: SchemaCopyable;

  /**
   * 配置是否可以排序
   */
  sortable?: boolean;

  /**
   * 是否可快速搜索
   */
  searchable?: boolean | SchemaObject;

  /** 是否可快速搜索且table头部回显 */
  headSearchable?: boolean | SchemaObject;

  /**
   * 配置是否默认展示
   */
  toggled?: boolean;

  /**
   * 列宽度
   */
  width?: number | string;

  /**
   * 列对齐方式
   */
  align?: 'left' | 'right' | 'center' | 'justify';

  /**
   * 列样式表
   */
  className?: string;

  /**
   * 单元格样式表达式
   */
  classNameExpr?: string;

  /**
   * 列头样式表
   */
  labelClassName?: string;

  /**
   * todo
   */
  filterable?:
    | boolean
    | {
        source?: string;
        options?: Array<any>;
      };

  /**
   * 结合表格的 footable 一起使用。
   * 填写 *、xs、sm、md、lg指定 footable 的触发条件，可以填写多个用空格隔开
   */
  breakpoint?: '*' | 'xs' | 'sm' | 'md' | 'lg';

  /**
   * 提示信息
   */
  remark?: SchemaRemark;

  /**
   * 默认值, 只有在 inputTable 里面才有用
   */
  value?: any;

  /**
   * 是否唯一, 只有在 inputTable 里面才有用
   */
  unique?: boolean;

  /**
   * 表格列单元格是否可以获取父级数据域值，默认为false，该配置对当前列内单元格生效。（针对value）
   */
  canAccessSuperData?: boolean;

  /**
   * 单元格内部组件自定义样式 style作为单元格自定义样式的配置
   */
  innerStyle?: {
    [propName: string]: any;
  };
};

export type TableColumnWithType = SchemaObject & TableColumnObject;
export type TableColumn = TableColumnWithType | TableColumnObject;

type AutoGenerateFilterObject = {
  /* 是否显示设置查询字段 */
  showBtnToolbar?: boolean;

  /* 是否默认展开 */
  defaultExpanded?: boolean;
};

type AutoFillHeightObject = Record<'height' | 'maxHeight', number>;
/**
 * Table 表格渲染器。
 * 文档：https://baidu.gitee.io/amis/docs/components/table
 */
export interface TableSchema extends BaseSchema {
  /**
   * 指定为表格渲染器。
   */
  type: 'table' | 'static-table';

  /**
   * 是否固定表头
   */
  affixHeader?: boolean;

  /**
   * 表格的列信息
   */
  columns?: Array<TableColumn>;

  /**
   * 展示列显示开关，自动即：列数量大于或等于5个时自动开启
   */
  columnsTogglable?: boolean | 'auto';

  /**
   * 是否开启底部展示功能，适合移动端展示
   */
  footable?:
    | boolean
    | {
        expand?: 'first' | 'all' | 'none';

        /**
         * 是否为手风琴模式
         */
        accordion?: boolean;
      };

  /**
   * 底部外层 CSS 类名
   */
  footerClassName?: SchemaClassName;

  /**
   * 顶部外层 CSS 类名
   */
  headerClassName?: SchemaClassName;

  /**
   * 占位符
   */
  placeholder?: string | SchemaTpl;

  /**
   * 是否显示底部
   */
  showFooter?: boolean;

  /**
   * 是否显示头部
   */
  showHeader?: boolean;

  /**
   * 数据源：绑定当前环境变量
   */
  source?: SchemaTokenizeableString;

  /**
   * 表格 CSS 类名
   */
  tableClassName?: SchemaClassName;

  /**
   * 标题
   */
  title?: string;

  /**
   * 工具栏 CSS 类名
   */
  toolbarClassName?: SchemaClassName;

  /**
   * 合并单元格配置，配置数字表示从左到右的多少列自动合并单元格。
   */
  combineNum?: number | SchemaExpression;

  /**
   * 合并单元格配置，配置从第几列开始合并。
   */
  combineFromIndex?: number;

  /**
   * 顶部总结行
   */
  prefixRow?: Array<SchemaObject>;

  /**
   * 底部总结行
   */
  affixRow?: Array<SchemaObject>;

  /**
   * 是否可调整列宽
   */
  resizable?: boolean;

  /**
   * 行样式表表达式
   */
  rowClassNameExpr?: string;

  /**
   * 行角标
   */
  itemBadge?: BadgeObject;

  /**
   * 开启查询区域，会根据列元素的searchable属性值，自动生成查询条件表单
   */
  autoGenerateFilter?: AutoGenerateFilterObject | boolean;

  /**
   * 表格是否可以获取父级数据域值，默认为false
   */
  canAccessSuperData?: boolean;

  /**
   * 表格自动计算高度
   */
  autoFillHeight?: boolean | AutoFillHeightObject;
  /**
   * 设置Form依赖的Spinner的size
   */
  spinnerSize?: 'sm' | 'lg' | '',

  /** 控制多列排序， 默认是true */
  sortMultiple?: boolean;

  isInlineHeader?: boolean;
  updateAllRows?: boolean;

  /**
   * 配置 table-layout 属性
   * auto：属性根据内容宽度自适应布局
   * fixed：拖动改变自身列宽时不会改变其他列宽；设置最小拖动宽度
   */
  tableLayout?: 'auto' | 'fixed';

  /**
   * 是否去拍平列过滤的参数，默认 true
   */
  flatHeadSearchable?: boolean;
}

export interface TableProps extends RendererProps, SpinnerExtraProps {
  title?: string; // 标题
  header?: SchemaNode;
  footer?: SchemaNode;
  actions?: ActionObject[];
  className?: string;
  headerClassName?: string;
  footerClassName?: string;
  store: ITableStore;
  columns?: Array<any>;
  headingClassName?: string;
  toolbarClassName?: string;
  headerToolbarClassName?: string;
  footerToolbarClassName?: string;
  tableClassName?: string;
  source?: string;
  selectable?: boolean;
  selected?: Array<any>;
  maxKeepItemSelectionLength?: number;
  valueField?: string;
  draggable?: boolean;
  columnsTogglable?: boolean | 'auto';
  affixHeader?: boolean;
  affixColumns?: boolean;
  combineNum?: number | SchemaExpression;
  combineFromIndex?: number;
  footable?:
    | boolean
    | {
        expand?: 'first' | 'all' | 'none';
        expandAll?: boolean;
        accordion?: boolean;
      };
  expandConfig?: {
    expand?: 'first' | 'all' | 'none';
    expandAll?: boolean;
    accordion?: boolean;
  };
  itemCheckableOn?: string;
  itemDraggableOn?: string;
  itemActions?: Array<ActionObject>;
  onSelect: (
    selectedItems: Array<object>,
    unSelectedItems: Array<object>,
  ) => void;
  onPristineChange?: (data: object, rowIndexe: string) => void;
  onSave?: (
    items: Array<object> | object,
    diff: Array<object> | object,
    rowIndexes: Array<string> | string,
    unModifiedItems?: Array<object>,
    rowOrigins?: Array<object> | object,
    options?: {
      resetOnFailed?: boolean;
      reload?: string;
    },
  ) => void;
  onSaveOrder?: (moved: Array<object>, items: Array<object>) => void;
  onQuery: (values: object) => void;
  onImageEnlarge?: (data: any, target: any) => void;
  buildItemProps?: (item: any, index: number) => any;
  checkOnItemClick?: boolean;
  hideCheckToggler?: boolean;
  rowClassName?: string;
  rowClassNameExpr?: string;
  popOverContainer?: any;
  canAccessSuperData?: boolean;
  reUseRow?: boolean | 'match';
  itemBadge?: BadgeObject;
  loading?: boolean;
  autoFillHeight?: boolean | AutoFillHeightObject;
  subTable?: SchemaNode;
  subTableOperationIndex?: number;
  subTableAddRowIndex?: number;
  isCreateModeOfSubTable?: boolean;
  searchFormData?: object;
  /** 展开/折叠图标，若只配置unfoldIcon，表示折叠是展示旋转180度的 */
  unfoldIcon?: SchemaTpl | IconSchema;
  foldIcon?: SchemaTpl | IconSchema;
  mountAll?: boolean;
  /** 展开/收起列是否显示，默认 true */
  showExpansionColumn?: boolean;
  isPublicHeader?: boolean;
  tableKey?: string;
  /** 单列排序/多列排序，默认true */
  sortMultiple?: boolean;
  dispatchSortEvent?: (data: any) => void;
  isInlineHeader?: boolean;
  updateAllRows: boolean;
  noPadding?: boolean;
  tableLayout?: 'auto' | 'fixed';
}

export type ExportExcelToolbar = SchemaNode & {
  api?: SchemaApi;
  columns?: string[];
  exportColumns?: any[];
  filename?: string;
};

export type TableRendererEvent =
  | 'selectedChange'
  | 'columnSort'
  | 'columnFilter'
  | 'columnSearch'
  | 'columnToggled'
  | 'orderChange'
  | 'rowClick'
  | 'rowDbClick';

export type TableRendererAction =
  | 'selectAll'
  | 'clearAll'
  | 'select'
  | 'initDrag';

export default class Table extends React.Component<TableProps, object> {
  static contextType = ScopedContext;

  static propsList: Array<string> = [
    'header',
    'headerToolbarRender',
    'footer',
    'footerToolbarRender',
    'footable',
    'expandConfig',
    'placeholder',
    'tableClassName',
    'headingClassName',
    'source',
    'selectable',
    'columnsTogglable',
    'affixHeader',
    'affixColumns',
    'headerClassName',
    'footerClassName',
    'selected',
    'multiple',
    'primaryField',
    'hideQuickSaveBtn',
    'itemCheckableOn',
    'itemDraggableOn',
    'draggable', // issue#660 避免draggable透传给label schema
    'checkOnItemClick',
    'hideCheckToggler',
    'itemAction',
    'itemActions',
    'combineNum',
    'combineFromIndex',
    'items',
    'columns',
    'valueField',
    'saveImmediately',
    'rowClassName',
    'rowClassNameExpr',
    'affixRowClassNameExpr',
    'prefixRowClassNameExpr',
    'popOverContainer',
    'headerToolbarClassName',
    'toolbarClassName',
    'footerToolbarClassName',
    'itemBadge',
    'autoFillHeight',
    'onSelect',
    'keepItemSelectionOnPageChange',
    'maxKeepItemSelectionLength',
    'autoGenerateFilter',
    'showExpansionColumn',
    'spinnerSize',
    'sortList',
    'sortMultiple',
    'isInlineHeader',
    'parent',
    'renderSelection',
    'tableLayout',
  ];
  static defaultProps: Partial<TableProps> = {
    className: '',
    placeholder: 'placeholder.noData',
    tableClassName: '',
    source: '$items',
    selectable: false,
    columnsTogglable: 'auto',
    affixHeader: false,
    headerClassName: '',
    footerClassName: '',
    toolbarClassName: '',
    headerToolbarClassName: '',
    footerToolbarClassName: '',
    primaryField: 'id',
    itemCheckableOn: '',
    itemDraggableOn: '',
    hideCheckToggler: false,
    canAccessSuperData: false,
    resizable: true,
    showExpansionColumn: true,
    spinnerSize: '',
    sortMultiple: true,
    updateAllRows: false,
    noPadding: false,
    flatHeadSearchable: true,
    filterRowNum: 2,
  };

  table?: HTMLTableElement;
  headSearch: any;
  sortable?: Sortable;
  dragTip?: HTMLElement;
  affixedTable?: HTMLTableElement;
  parentNode?: HTMLElement | Window;
  renderedToolbars: Array<string> = [];
  subForms: any = {};
  timer: ReturnType<typeof setTimeout>;
  toDispose: Array<() => void> = [];
  updateTableInfoLazy = debounce(this.updateTableInfo.bind(this), 250, {
    trailing: true,
    leading: true,
  });
  mounted: boolean = false;

  updateAutoFillHeightLazy = debounce(
    this.updateAutoFillHeight.bind(this),
    250,
    {
      trailing: true,
      leading: false
    }
  );

  constructor(props: TableProps, context: IScopedContext) {
    super(props);

    const scoped = context;
    scoped.registerComponent(this);

    this.handleOutterScroll = this.handleOutterScroll.bind(this);
    this.updateTableInfo = this.updateTableInfo.bind(this);
    this.tableRef = this.tableRef.bind(this);
    this.headSearchRef = this.headSearchRef.bind(this);
    this.affixedTableRef = this.affixedTableRef.bind(this);
    this.handleAction = this.handleAction.bind(this);
    this.handleCheck = this.handleCheck.bind(this);
    this.handleCheckAll = this.handleCheckAll.bind(this);
    this.handleQuickChange = this.handleQuickChange.bind(this);
    this.handleSave = this.handleSave.bind(this);
    this.handleSaveOrder = this.handleSaveOrder.bind(this);
    this.reset = this.reset.bind(this);
    this.dragTipRef = this.dragTipRef.bind(this);
    this.getPopOverContainer = this.getPopOverContainer.bind(this);
    this.renderCell = this.renderCell.bind(this);
    this.renderHeadCell = this.renderHeadCell.bind(this);
    this.renderToolbar = this.renderToolbar.bind(this);
    this.handleMouseMove = this.handleMouseMove.bind(this);
    this.handleMouseLeave = this.handleMouseLeave.bind(this);
    this.subFormRef = this.subFormRef.bind(this);
    this.handleColumnToggle = this.handleColumnToggle.bind(this);

    this.updateAutoFillHeight = this.updateAutoFillHeight.bind(this);
    this.renderExpandableIcon = this.renderExpandableIcon.bind(this);
    this.renderIcon = this.renderIcon.bind(this);
    this.autoCollectSearchable = this.autoCollectSearchable.bind(this);

    const {
      store,
      selectable,
      columnsTogglable,
      draggable,
      orderBy,
      orderDir,
      sortList,
      subTable,
      subTableOperationIndex,
      subTableAddRowIndex,
      isCreateModeOfSubTable,
      searchFormData,
      multiple,
      footable,
      primaryField,
      itemCheckableOn,
      itemDraggableOn,
      hideCheckToggler,
      combineFromIndex,
      expandConfig,
      formItem,
      keepItemSelectionOnPageChange,
      maxKeepItemSelectionLength,
      syncLocation,
      location,
      autoGenerateFilter,
      showExpansionColumn,
      cascadeSelection = false,
      env,
      syncQuery,
      canAccessSuperData,
      tableLayout,
      id,
    } = props;

    let combineNum = props.combineNum;
    if (typeof combineNum === 'string') {
      combineNum = parseInt(
        resolveVariableAndFilter(combineNum, props.data, '| raw'),
        10,
      );
    }

    let columns: IColumn[] = props.columns as any;
    if (typeof columns === 'string') {
      columns = resolveVariableAndFilter(columns, props.data, '| raw') as any;
    }

    store.update({
      selectable,
      draggable,
      columns,
      columnsTogglable,
      orderBy,
      orderDir,
      sortList,
      subTable,
      subTableOperationIndex,
      subTableAddRowIndex,
      isCreateModeOfSubTable,
      searchFormData,
      multiple,
      footable,
      expandConfig,
      primaryField,
      itemCheckableOn,
      itemDraggableOn,
      hideCheckToggler,
      combineNum,
      combineFromIndex,
      keepItemSelectionOnPageChange,
      maxKeepItemSelectionLength,
      showExpansionColumn,
      cascadeSelection,
      routet: env?.routerType,
      canAccessSuperData,
      tableLayout,
      toggledColumnsPersistId: id,
    });

    if (
      isPlainObject(autoGenerateFilter) &&
      autoGenerateFilter.defaultExpanded
    ) {
      store.setSearchFormExpanded(true);
    }

    formItem && isAlive(formItem) && formItem.setSubStore(store);

    Table.syncRows(store, this.props, undefined) && this.syncSelected();

    // 处理syncLocation 同步 searchFormData
    const newSearchFormData = this.autoCollectSearchable(columns, autoGenerateFilter);
    store.update({ searchFormData: newSearchFormData });
    typeof syncQuery === "function" && syncQuery(newSearchFormData, false);
  }

  autoCollectSearchable(columns: IColumn[], autoGenerateFilter: boolean) {
    const headerSearchColumn = columns?.filter(
      (item: IColumn) => item.headSearchable,
    );
    let headSearchFormData: any = {};
    headerSearchColumn?.forEach((item: IColumn) => {
      if (item.name) {
        let key = '';
        if (typeof item.headSearchable === 'boolean') {
          key = item.name;
        } else if (isObject(item.headSearchable)) {
          key = item.headSearchable.name || item.name;
        }
        key && (headSearchFormData[key] = item?.headSearchable?.value);
      }
    });
    /*
      autoGenerateFilter会影响searchable生成的是区域搜索还是列搜索
        如果为true，生成区域搜索，无需收集数据
        否则，生成列搜索，需要收集数据
    */
    if (!autoGenerateFilter) {
      const searchColumn = columns?.filter(
        (item: IColumn) => item.searchable,
      );
      const searchFormData: any = {};
      searchColumn?.forEach((item: IColumn) => {
        if (item.name) {
          let key = '';
          if (typeof item.searchable === 'boolean') {
            key = item.name;
          } else if (isObject(item.searchable)) {
            key = item.searchable.name || item.name;
          }
          key && (searchFormData[key] = item?.searchable?.value);
        }
      });
      Object.assign(headSearchFormData, searchFormData)
    }

    return headSearchFormData;
  }

  static syncRows(
    store: ITableStore,
    props: TableProps,
    prevProps?: TableProps,
  ) {
    const source = props.source;
    const value = props.value || props.items;
    let rows: Array<object> = [];
    let updateRows = false;

    // 要严格比较前后的value值，否则某些情况下会导致循环update无限渲染
    if (
      Array.isArray(value) &&
      (!prevProps || !isEqual(prevProps.value || prevProps.items, value))
    ) {
      updateRows = true;
      rows = value;
    } else if (typeof source === 'string') {
      const resolved = resolveVariableAndFilter(source, props.data, '| raw');
      const prev = prevProps
        ? resolveVariableAndFilter(source, prevProps.data, '| raw')
        : null;

      if (prev && prev === resolved) {
        updateRows = false;
      } else if (Array.isArray(resolved)) {
        updateRows = true;
        rows = resolved;
      }
    }

    updateRows &&
      store.initRows(
        rows,
        props.getEntryId,
        props.reUseRow,
      );

    if (rows && rows.length) {
      let resolveSelected = [];
      if (Array.isArray(props.selected)) {
        resolveSelected = [...props.selected];
      } else if (typeof props.selected === 'string') {
        resolveSelected = resolveVariableAndFilter(
          props.selected,
          props.data,
          '| raw',
        );
      }

      resolveSelected.length &&
        store.updateSelected(resolveSelected, props.valueField);
    }
    return updateRows;
  }

  componentDidMount() {
    this.mounted = true;
    const currentNode = findDOMNode(this) as HTMLElement;
    let parent: HTMLElement | Window | null = getScrollParent(currentNode);

    if (!parent || parent === document.body) {
      parent = window;
    }

    this.parentNode = parent;

    const dom = findDOMNode(this) as HTMLElement;
    if (dom.closest('.modal-body')) {
      return;
    }

    // fix: https://github.com/baidu/amis/issues/7606
    // 配置autoFillHeight时，需要监听父级容器高度变化重新计算表格高度
    if (this.props.autoFillHeight) {
      this.toDispose.push(
        resizeSensor(
          currentNode.parentElement!,
          this.updateAutoFillHeightLazy,
          false,
          'height'
        )
      );
      this.updateAutoFillHeight();
    }

    // 自动滚动以及高亮
    this.autoScrollToFirstNeedViewCenterColumnName();
    this.autoHighLightFirstColumnName();

    // 监听table元素的width改变时更新表格信息
    this.toDispose.push(
      resizeSensor(currentNode, this.updateTableInfoLazy, false, 'width')
    );
    const {store, autoGenerateFilter, onSearchableFromInit} = this.props;

    // autoGenerateFilter 开启后
    // 如果没有一个 searchable 的 column crud 就不会初始化加载
    // 所以这里加个判断默认初始加载一次
    if (
      autoGenerateFilter &&
      !store.searchableColumns.length &&
      onSearchableFromInit
    ) {
      onSearchableFromInit({});
    }
  }

  updateSortList = (args: any) => {
    const store = this.props.store;
    store.setSortList(args);
  }

  renderIcon(icon: SchemaTpl | IconSchema) {
    const {render, data} = this.props;

    if (!icon) {
      return undefined;
    }

    return render(
      'expandIcon',
      typeof icon === 'string'
        ? {
            type: 'icon',
            icon,
          }
        : icon,
      data,
    );
  }

  renderExpandableIcon(expanded: boolean) {
    const {unfoldIcon, foldIcon} = this.props;
    return unfoldIcon ? (
      this.renderIcon(!expanded && foldIcon ? foldIcon : unfoldIcon)
    ) : (
      <Icon icon="right-arrow-bold" className="icon" />
    );
  }

  /**
   * 自动设置表格高度占满界面剩余区域
   * 用 css 实现有点麻烦，要改很多结构，所以先用 dom hack 了，避免对之前的功能有影响
   */
  updateAutoFillHeight() {
    const {autoFillHeight, footerToolbar, classPrefix: ns} = this.props;
    if (!autoFillHeight) {
      return;
    }
    const table = this.table!;
    const tableContent = table.parentElement as HTMLElement;

    if (!tableContent) {
      return;
    }

    // 可能数据还没到，没有渲染 footer
    // 也可能是弹窗中，弹窗还在动画中，等一下再执行
    if (
      !tableContent.offsetHeight ||
      tableContent.getBoundingClientRect().height / tableContent.offsetHeight <
        0.8
    ) {
      this.timer = setTimeout(() => {
        this.updateAutoFillHeight();
      }, 100);
      return;
    }

    // 计算 table-content 在 dom 中的位置

    let viewportHeight = window.innerHeight;
    let tableContentTop = offset(tableContent).top;

    // 获取控制当前元素滚动的父元素
    const parent = getScrollParent(tableContent.parentElement as HTMLElement);
    if (parent && parent !== document.body) {
      viewportHeight = parent.clientHeight - 1;
      tableContentTop = position(tableContent, parent).top;
    }

    let tableContentBottom = 0;
    let selfNode = tableContent;
    let parentNode = selfNode.parentElement;
    while (parentNode) {
      const paddingBottom = getStyleNumber(parentNode, 'padding-bottom');
      const borderBottom = getStyleNumber(parentNode, 'border-bottom-width');

      let nextSiblingHeight = 0;
      let nextSibling = selfNode.nextElementSibling as HTMLElement;
      while (nextSibling) {
        const positon = getComputedStyle(nextSibling).position;
        if (positon !== 'absolute' && positon !== 'fixed') {
          nextSiblingHeight +=
            nextSibling.offsetHeight +
            getStyleNumber(nextSibling, 'margin-bottom');
        }

        nextSibling = nextSibling.nextElementSibling as HTMLElement;
      }

      const marginBottom = getStyleNumber(selfNode, 'margin-bottom');
      tableContentBottom +=
        paddingBottom + borderBottom + marginBottom + nextSiblingHeight;

      selfNode = parentNode;
      parentNode = selfNode.parentElement;

      if (parent && parent !== document.body && parent === selfNode) {
        break;
      }
    }

    const heightField =
      autoFillHeight && (autoFillHeight as AutoFillHeightObject).maxHeight
        ? 'maxHeight'
        : 'height';

    const heightValue = isObject(autoFillHeight)
      ? (autoFillHeight as AutoFillHeightObject)[heightField]
      : 0;

    const tableContentHeight = heightValue
      ? `${heightValue}px`
      : `${Math.round(
          viewportHeight - tableContentTop - tableContentBottom
        )}px`;

    tableContent.style[heightField] = tableContentHeight;
  }

  componentDidUpdate(prevProps: TableProps) {
    const props = this.props;
    const store = props.store;

    if (
      anyChanged(
        [
          'selectable',
          'columnsTogglable',
          'draggable',
          'orderBy',
          'orderDir',
          // 'sortList',
          'multiple',
          'footable',
          'primaryField',
          'itemCheckableOn',
          'itemDraggableOn',
          'hideCheckToggler',
          'combineNum',
          'combineFromIndex',
          'expandConfig',
          'showExpansionColumn',
          'tableLayout',
        ],
        prevProps,
        props,
      )
    ) {
      let combineNum = props.combineNum;
      if (typeof combineNum === 'string') {
        combineNum = parseInt(
          resolveVariableAndFilter(combineNum, props.data, '| raw'),
          10,
        );
      }
      store.update({
        selectable: props.selectable,
        columnsTogglable: props.columnsTogglable,
        draggable: props.draggable,
        orderBy: props.orderBy,
        orderDir: props.orderDir,
        // sortList: props.sortList,
        multiple: props.multiple,
        primaryField: props.primaryField,
        footable: props.footable,
        itemCheckableOn: props.itemCheckableOn,
        itemDraggableOn: props.itemDraggableOn,
        hideCheckToggler: props.hideCheckToggler,
        combineNum: combineNum,
        combineFromIndex: props.combineFromIndex,
        expandConfig: props.expandConfig,
        showExpansionColumn: props.showExpansionColumn,
      });
    }

    let prevColumns = typeof prevProps.columns === 'string' ? resolveVariableAndFilter(prevProps.columns, prevProps.data, '| raw') : prevProps.columns;
    let curColumns = typeof props.columns === 'string' ? resolveVariableAndFilter(props.columns, props.data, '| raw') : props.columns;
    if (prevColumns !== curColumns) {
      const { syncQuery, autoGenerateFilter } = this.props;
      const newSearchFormData = this.autoCollectSearchable(curColumns, autoGenerateFilter);
      store.update({
        columns: curColumns,
        searchFormData: newSearchFormData
      });
      typeof syncQuery === "function" && syncQuery(newSearchFormData, true);
      // 列配置变化时 自动滚动以及高亮
      this.autoScrollToFirstNeedViewCenterColumnName();
      this.autoHighLightFirstColumnName();
    } else {
      // 列数据不变的清空下需要考虑一下 scrollIntoViewOn 的表达式结果有没有发生变化，变化需要重新执行一次滚动操作，高亮同理
      if(this.isChangedColumnsExpression(prevProps, 'scrollIntoViewOn')) {
        this.autoScrollToFirstNeedViewCenterColumnName();
      }
      if(this.isChangedColumnsExpression(prevProps, 'isHighLightOn')) {
        this.autoHighLightFirstColumnName();
      }
    }

    if(anyChanged(['source', 'items'], prevProps, props)) {
      // 表格数据内容变化时，重新执行高亮和滚动
      this.autoScrollToFirstNeedViewCenterColumnName();
      this.autoHighLightFirstColumnName();
    }

    if (
      anyChanged(['source', 'value', 'items'], prevProps, props) ||
      (!props.value &&
        !props.items &&
        (props.data !== prevProps.data ||
          (typeof props.source === 'string' && isPureVariable(props.source))))
    ) {
      // // 更新时不再设置expandConfig了
      // const newProps = {...props};
      // newProps.expandConfig && delete newProps.expandConfig;
      Table.syncRows(store, props, prevProps);

      // 更新selected
      if (props.selected) {
        let prevPropsSelected = prevProps.selected;
        let propsSelected = props.selected;
        if (
          typeof props.selected === 'string' &&
          typeof prevProps.selected === 'string'
        ) {
          prevPropsSelected = resolveVariableAndFilter(
            prevProps.selected,
            prevProps.data,
            '| raw',
          );
          propsSelected = resolveVariableAndFilter(
            props.selected,
            props.data,
            '| raw',
          );
        }

        if (isArrayChildrenModified(prevPropsSelected!, propsSelected!)) {
          const prevSelectedRows = store.selectedRows
            .map(item => item.id)
            .join(',');

          store.updateSelected(propsSelected || [], props.valueField);
          const selectedRows = store.selectedRows
            .map(item => item.id)
            .join(',');
          prevSelectedRows !== selectedRows && this.syncSelected();
        }
      }
    } else if (props.selected) {
      // input-table场景，items值不变, 只变selected
      let prevPropsSelected = prevProps.selected;
      let propsSelected = props.selected;
      if (
        typeof props.selected === 'string' &&
        typeof prevProps.selected === 'string'
      ) {
        prevPropsSelected = resolveVariableAndFilter(
          prevProps.selected,
          prevProps.data,
          '| raw',
        );
        propsSelected = resolveVariableAndFilter(
          props.selected,
          props.data,
          '| raw',
        );
      }

      if (isArrayChildrenModified(prevPropsSelected!, propsSelected!)) {
        const prevSelectedRows = store.selectedRows
          .map(item => item.id)
          .join(',');

        store.updateSelected(propsSelected || [], props.valueField);
        const selectedRows = store.selectedRows.map(item => item.id).join(',');
        prevSelectedRows !== selectedRows && this.syncSelected();
      }
    }
  }

  componentWillUnmount() {
    const {formItem} = this.props;

    this.toDispose.forEach(fn => fn());
    this.toDispose = [];

    this.updateTableInfoLazy.cancel();
    this.updateAutoFillHeightLazy.cancel();
    formItem && isAlive(formItem) && formItem.setSubStore(null);
    clearTimeout(this.timer);

    const scoped = this.context as IScopedContext;
    scoped.unRegisterComponent(this);
    this.mounted = false;
  }

  subFormRef(form: any, x: number, y: number, columnPosition: string) {
    const {quickEditFormRef} = this.props;

    quickEditFormRef && quickEditFormRef(form, x, y, this.props.$path, columnPosition);
    this.subForms[`${x}-${y}-${columnPosition}`] = form;
    form && this.props.store.addForm(form.props.store, y);
  }

  // 判断columns中表达式结果是否变化
  isChangedColumnsExpression(prevProps: any, expressionField: string) {
    const props = this.props;

    let isChanged = false;
    const columns = typeof props.columns === 'string' ? resolveVariableAndFilter(props.columns, props.data, '| raw') : props.columns;
    // 防止异常类型
    if(!Array.isArray(columns)) {
      return;
    }

    columns.forEach(col => {
      if(isChanged) {
        return
      };
      let expression = col?.[expressionField];
      if(expression && evalExpression(expression, props.data) !== evalExpression(expression, prevProps.data)) {
        isChanged = true;
      }
    })

    return isChanged;
  }

  handleAction(
    e: React.UIEvent<any> | undefined,
    action: ActionObject,
    ctx: object,
  ) {
    const {onAction} = this.props;

    // todo
    onAction(e, action, ctx);
  }

  async handleCheck(item: IRow, value: boolean, shift?: boolean) {
    const {store, data, dispatchEvent} = this.props;

    const selectedItems = value
      ? [...store.selectedRows.map(row => row.data), item.data]
      : store.selectedRows.filter(row => row.id !== item.id) || [];
    const unSelectedItems = value
      ? store.unSelectedRows.filter(row => row.id !== item.id) || []
      : [...store.unSelectedRows.map(row => row.data), item.data];

    const rendererEvent = await dispatchEvent(
      'selectedChange',
      createObject(data, {
        selectedItems,
        unSelectedItems,
      }),
    );

    if (rendererEvent?.prevented) {
      return;
    }

    if (shift) {
      store.toggleShift(item);
    } else {
      item.toggle();
    }

    this.bubbleSelectedChange();
  }

  @autobind
  handleRowClick(item: IRow, index: number) {
    const {dispatchEvent, store, data} = this.props;
    return dispatchEvent(
      'rowClick',
      createObject(data, {
        rowItem: item.data, // 保留rowItem 可能有用户已经在用 兼容之前的版本
        item: item.data,
        index: index,
      })
    );
  }

  @autobind
  handleRowDbClick(item: IRow, index: number) {
    const { dispatchEvent, store, data } = this.props;
    return dispatchEvent(
      'rowDbClick',
      createObject(data, {
        item,
        index
      })
    );
  }

  async handleCascadeCheck(item: IRow, value: boolean, shift?: boolean) {

    const {store, data, dispatchEvent} = this.props;

    const selectedItems = value
      ? [...store.selectedRows.map(row => row.data), item.data]
      : store.selectedRows.filter(row => row.id !== item.id) || [];
    const unSelectedItems = value
      ? store.unSelectedRows.filter(row => row.id !== item.id) || []
      : [...store.unSelectedRows.map(row => row.data), item.data];

    const rendererEvent = await dispatchEvent(
      'selectedChange',
      createObject(data, {
        selectedItems,
        unSelectedItems,
      }),
    );

    if (rendererEvent?.prevented) {
      return;
    }
    item.cascadeToggle();
    this.bubbleSelectedChange();
  }

  async handleCheckAll() {
    const {store, data, dispatchEvent} = this.props;

    const items = store.getSelectedRows().map(item => item.data);

    const selectedItems = store.allChecked ? [] : items;
    const unSelectedItems = store.allChecked ? items : [];

    const rendererEvent = await dispatchEvent(
      'selectedChange',
      createObject(data, {
        selectedItems,
        unSelectedItems,
      }),
    );

    if (rendererEvent?.prevented) {
      return;
    }

    store.toggleAll();
    this.bubbleSelectedChange();
  }

  handleQuickChange(
    item: IRow,
    values: object,
    saveImmediately?: boolean | any,
    savePristine?: boolean,
    options?: {
      resetOnFailed?: boolean;
      reload?: string;
    },
    extraOptions = {}, // 处理定位的formItem
  ) {
    if (!isAlive(item)) {
      return;
    }

    const {
      onSave,
      onPristineChange,
      saveImmediately: propsSaveImmediately,
      primaryField,
    } = this.props;

    item.change(values, savePristine);
    // 根据情况，判断是clearErrors还是validate，考虑异步拿到状态
    const { rowIndex: y, colIndex: x, columnPosition } = extraOptions as any;
    const currKey = `${x}-${y}-${columnPosition}`;
    // errorFields
    const currForm = this.subForms[currKey];
      if (currForm) {
        const errorFields = currForm?.props?.store?.errorFields;
        const posibbleKey = `${x}-${y}-${columnPosition === "normal" ? "fixed" : "normal"}`;
        const tmpForm = this.subForms[posibbleKey];
        if (tmpForm) {
          if (errorFields.length) {
            const formErrors = errorFields.reduce((sum: any, curr: any) => {

              sum[curr.name] = curr?.errors;
              return sum
            }, {});
            tmpForm.setErrors(formErrors);
          } else {
            const directItems = tmpForm?.props?.store?.directItems ?? [];
              const formErrors = directItems.reduce((sum: any, curr: any) => {
                sum[curr.name] = [];
                return sum
              }, {});
              tmpForm.setErrors(formErrors);
          }
        }
      }
    // 值发生变化了，需要通过 onSelect 通知到外面，否则会出现数据不同步的问题
    item.modified && this.syncSelected();

    if (savePristine) {
      onPristineChange?.(item.data, item.path);
      return;
    } else if (!saveImmediately && !propsSaveImmediately) {
      return;
    }

    if (saveImmediately && saveImmediately.api) {
      this.props.onAction(
        null,
        {
          actionType: 'ajax',
          api: saveImmediately.api,
          reload: options?.reload,
        },
        values,
      );
      return;
    }

    if (!onSave) {
      return;
    }

    onSave(
      item.data,
      difference(item.data, item.pristine, ['id', primaryField]),
      item.path,
      undefined,
      item.pristine,
      options,
    );
  }

  async handleSave() {
    const {store, onSave, primaryField} = this.props;

    if (!onSave || !store.modifiedRows.length) {
      return;
    }

    // 验证所有表单项，没有错误才继续
    const subForms: Array<any> = [];
    Object.keys(this.subForms).forEach(
      key => this.subForms[key] && subForms.push(this.subForms[key]),
    );
    if (subForms.length) {
      const result = await Promise.all(subForms.map(item => item.validate()));
      if (~result.indexOf(false)) {
        return;
      }
    }

    const rows = store.modifiedRows.map(item => item.data);
    const rowIndexes = store.modifiedRows.map(item => item.path);
    const diff = store.modifiedRows.map(item =>
      difference(item.data, item.pristine, ['id', primaryField]),
    );
    const unModifiedRows = store.rows
      .filter(item => !item.modified)
      .map(item => item.data);
    onSave(
      rows,
      diff,
      rowIndexes,
      unModifiedRows,
      store.modifiedRows.map(item => item.pristine),
    );
  }

  async handleSaveOrder() {
    const {store, onSaveOrder, data, dispatchEvent} = this.props;

    const movedItems = store.movedRows.map(item => item.data);
    const items = store.rows.map(item => item.getDataWithModifiedChilden());

    const rendererEvent = await dispatchEvent(
      'orderChange',
      createObject(data, {movedItems}),
    );

    if (rendererEvent?.prevented) {
      return;
    }

    if (!onSaveOrder || !store.movedRows.length) {
      return;
    }

    onSaveOrder(movedItems, items);
  }

  /**
   * FIX issue#483
   * 抽离同步selected逻辑和和冒泡selectedChange事件逻辑
   */
  bubbleSelectedChange() {
    const {store, onSelect} = this.props;
    const selectedItems = store.selectedRows.map(item => item.data);
    const unSelectedItems = store.unSelectedRows.map(item => item.data);

    onSelect && onSelect(selectedItems, unSelectedItems);
  }

  /**
   * FIX issue#483
   * 抽离同步selected逻辑和和冒泡selectedChange事件逻辑
   */
  syncSelected() {
    const {store, handleSelect} = this.props;
    const selectedItems = store.selectedRows.map(item => item.data);
    const unSelectedItems = store.unSelectedRows.map(item => item.data);

    handleSelect && handleSelect(selectedItems, unSelectedItems);
  }

  reset() {
    const {store} = this.props;

    store.reset();

    const subForms: Array<any> = [];
    Object.keys(this.subForms).forEach(
      key => this.subForms[key] && subForms.push(this.subForms[key]),
    );
    subForms.forEach(item => item.clearErrors());
  }

  bulkUpdate(value: any, items: Array<object>) {
    const {store, primaryField} = this.props;

    if (primaryField && value.ids) {
      const ids = value.ids.split(',');
      const rows = store.rows.filter(item =>
        find(ids, (id: any) => id && id == item.data[primaryField]),
      );
      const newValue = {...value, ids: undefined};
      rows.forEach(row => row.change(newValue));
    } else {
      const rows = store.rows.filter(item => ~items.indexOf(item.pristine));
      rows.forEach(row => row.change(value));
    }
  }

  getSelected() {
    const {store} = this.props;

    return store.selectedRows.map(item => item.data);
  }

  // 更新table的数据，realWidth、width、外层滚动条等
  updateTableInfo(callback?: () => void) {
    // 拖动列宽时，不更新
    if (this.resizeLine) {
      return;
    }
    this.props.store.initTableWidth();
    this.props.store.syncTableWidth();
    this.handleOutterScroll();
    callback && setTimeout(callback, 20);
  }

  // 当表格滚动时，需要让 affixHeader 部分的表格也滚动
  handleOutterScroll() {
    const table = this.table as HTMLElement;
    if (!table) {
      return;
    }

    const outter = table?.parentNode as HTMLElement;
    const scrollLeft = outter.scrollLeft;
    if (this.affixedTable) {
      // 表格滚动时，同步滚动affixHeader表格
      this.affixedTable.parentElement!.scrollLeft = scrollLeft;
    }

    if (this.props.store.filteredColumns.some(column => column.fixed)) {
      let leading = scrollLeft === 0; // 是否滚动到最左边
      let trailing =
        Math.ceil(scrollLeft) + outter.offsetWidth >= table.scrollWidth; // 是否滚动到最右边

      [table, this.affixedTable]
        .filter(item => item)
        .forEach((table: HTMLElement) => {
          table.classList.remove('table-fixed-left', 'table-fixed-right');
          // 滚动到最左边则不加table-fixed-left，最后一个左固定列右侧不会有阴影
          leading || table.classList.add('table-fixed-left');
          // 滚动到最右边则不加table-fixed-right，第一个右固定列右侧不会有阴影
          trailing || table.classList.add('table-fixed-right');
        });
    }
  }

  // 自动滚动到第一个配置了scrollIntoView的列中
  autoScrollToFirstNeedViewCenterColumnName() {
    const { columns, data } = this.props;
    if(!columns) {
      return;
    }

    const currColumns = typeof columns === 'string' ? resolveVariableAndFilter(columns, data, '| raw') : columns;
    // 防止异常类型
    if(!Array.isArray(currColumns)) {
      return;
    }

    const firstNeedViewCenterColumnName = currColumns.find(col => {
      const isScrollIntoView =  isExpression(col?.scrollIntoViewOn) ? evalExpression(col.scrollIntoViewOn, data) : (
        typeof col?.scrollIntoViewOn === 'boolean' ? col?.scrollIntoViewOn : col.scrollIntoView // onEvent 的时候 scrollIntoViewOn 会被提前解析为布尔值
      );
      return isScrollIntoView;
    })?.name;
    firstNeedViewCenterColumnName && this.scrollIntoViewByColumnName(firstNeedViewCenterColumnName);
  }

  // 自动高亮第一列
  autoHighLightFirstColumnName() {
    const { columns, store } = this.props;
    if(!columns) {
      return;
    }

    this.removeColumnIsHighLight();
    const firstNeedActiveColumnName = store.columns.find(col => {
      const isHighLight = isExpression(col.pristine.isHighLightOn) ? evalExpression(col.pristine.isHighLightOn, store.data) : (
       typeof col.pristine.isHighLightOn === 'boolean' ? col.pristine.isHighLightOn : col.pristine.isHighLight // onEvent 的时候 isHighLightOn 会被提前解析为布尔值
      );
      return isHighLight
    })?.name;
    // this.prevActiveColumnName = firstNeedActiveColumnName;
    firstNeedActiveColumnName && this.setColumnIsHighLightByColumnName(firstNeedActiveColumnName);
  }

  // 根据列里配置的name 将列滚动到视图内
  scrollIntoViewByColumnName = async (name: string) => {
    // 组件已经卸载直接不执行后续逻辑
    if(!this.mounted) return;
    // requestAnimationFrame(() => {
    const columnDom = this.table?.querySelector(`[data-name="${name}"]`);

    scrollElementIntoView(columnDom, {
      behavior: "smooth", // 滚动是平滑滚动
      inline: "center",  // 水平方向滚动到视图中间
      block: "nearest", // 垂直方向的对齐
    }).catch(e => {
      // 如果滚动失败，重新开启滚动
      this.scrollIntoViewByColumnName(name);
    })
  }

  // 高亮某一列
  setColumnIsHighLightByColumnName(name: string) {
    if(!name) {
      return
    }
    const { store } = this.props;

    store.columns.forEach(column => {
      const columnName = isExpression(column.name) ? resolveVariableAndFilter(column.name, store.data) : column.name;
      column.setIsHighLight(columnName === name);
    })
  }

  // 取消高亮
  removeColumnIsHighLight() {
    const { store } = this.props;

    store.columns.forEach(column => {
      column.setIsHighLight(false);
    })
  }

  tableRef(ref: HTMLTableElement) {
    this.table = ref;
    isAlive(this.props.store) && this.props.store.setTable(ref);
    ref && this.handleOutterScroll();
  }

  headSearchRef(ref: any) {
    // console.log('headSearchRef', ref);
    this.headSearch = ref;
  }

  dragTipRef(ref: any) {
    if (!this.dragTip && ref) {
      this.initDragging();
    } else if (this.dragTip && !ref) {
      this.destroyDragging();
    }

    this.dragTip = ref;
  }

  affixedTableRef(ref: HTMLTableElement) {
    this.affixedTable = ref;
    ref && this.handleOutterScroll();
  }

  initDragging() {
    const {store, classPrefix: ns} = this.props;
    this.sortable = new Sortable(
      (this.table as HTMLElement).querySelector('tbody') as HTMLElement,
      {
        group: 'table',
        animation: 150,
        handle: `.${ns}Table-dragCell`,
        filter: `.${ns}Table-dragCell.is-dragDisabled`,
        ghostClass: 'is-dragging',
        onEnd: async (e: any) => {
          // 没有移动
          if (e.newIndex === e.oldIndex) {
            return;
          }

          const parent = e.to as HTMLElement;
          if (e.oldIndex < parent.childNodes.length - 1) {
            parent.insertBefore(e.item, parent.childNodes[e.oldIndex]);
          } else {
            parent.appendChild(e.item);
          }

          store.exchange(e.oldIndex, e.newIndex);
        },
      },
    );
  }

  destroyDragging() {
    this.sortable && this.sortable.destroy();
  }

  getPopOverContainer() {
    return findDOMNode(this);
  }

  handleMouseMove(e: React.MouseEvent<any>) {
    const tr: HTMLElement = (e.target as HTMLElement).closest(
      'tr[data-id]',
    ) as HTMLElement;

    if (!tr) {
      return;
    }

    const {store, affixColumns, itemActions} = this.props;

    const id = tr.getAttribute('data-id') as string;
    const row = store.hoverRow;

    if (row?.id === id) {
      return;
    }
    eachTree<IRow>(store.rows, (item: IRow) => item.setIsHover(item.id === id));
  }

  handleMouseLeave() {
    const store = this.props.store;
    const row = store.hoverRow;

    row?.setIsHover(false);
  }

  draggingTr: HTMLTableRowElement;
  originIndex: number;
  draggingSibling: Array<HTMLTableRowElement>;

  @autobind
  handleDragStart(e: React.DragEvent) {
    const store = this.props.store;
    const target = e.currentTarget;
    const tr = (this.draggingTr = target.closest('tr')!);
    const id = tr.getAttribute('data-id')!;
    const tbody = tr.parentNode!;
    this.originIndex = Array.prototype.indexOf.call(tbody.childNodes, tr);

    tr.classList.add('is-dragging');

    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', id);

    e.dataTransfer.setDragImage(tr, 0, 0);
    const item = store.getRowById(id)!;
    store.collapseAllAtDepth(item.depth);

    let siblings: Array<IRow> = store.rows;
    if (item.parentId) {
      const parent = store.getRowById(item.parentId)!;
      siblings = parent.children as any;
    }
    siblings = siblings.filter(sibling => sibling !== item);

    tbody.addEventListener('dragover', this.handleDragOver);
    tbody.addEventListener('drop', this.handleDrop);

    this.draggingSibling = siblings.map(item => {
      let tr: HTMLTableRowElement = tbody.querySelector(
        `tr[data-id="${item.id}"]`,
      ) as HTMLTableRowElement;

      tr.classList.add('is-drop-allowed');

      return tr;
    });
    tr.addEventListener('dragend', this.handleDragEnd);
  }

  @autobind
  handleDragOver(e: any) {
    if (!e.target) {
      return;
    }
    e.preventDefault();
    e.dataTransfer!.dropEffect = 'move';

    const overTr: HTMLElement = (e.target as HTMLElement).closest('tr')!;
    if (
      !overTr ||
      !~overTr.className.indexOf('is-drop-allowed') ||
      overTr === this.draggingTr
    ) {
      return;
    }

    const tbody = overTr.parentElement!;
    const dRect = this.draggingTr.getBoundingClientRect();
    const tRect = overTr.getBoundingClientRect();
    let ratio = dRect.top < tRect.top ? 0.1 : 0.9;

    const next = (e.clientY - tRect.top) / (tRect.bottom - tRect.top) > ratio;
    tbody.insertBefore(this.draggingTr, (next && overTr.nextSibling) || overTr);
  }

  @autobind
  async handleDrop() {
    const {store} = this.props;
    const tr = this.draggingTr;
    const tbody = tr.parentElement!;
    const index = Array.prototype.indexOf.call(tbody.childNodes, tr);
    const item: IRow = store.getRowById(tr.getAttribute('data-id')!) as any;

    // destroy
    this.handleDragEnd();

    store.exchange(this.originIndex, index, item);
  }

  @autobind
  handleDragEnd() {
    const tr = this.draggingTr;
    const tbody = tr.parentElement!;
    const index = Array.prototype.indexOf.call(tbody.childNodes, tr);
    tbody.insertBefore(
      tr,
      tbody.childNodes[
        index < this.originIndex ? this.originIndex + 1 : this.originIndex
      ],
    );

    tr.classList.remove('is-dragging');
    tr.removeEventListener('dragend', this.handleDragEnd);
    tbody.removeEventListener('dragover', this.handleDragOver);
    tbody.removeEventListener('drop', this.handleDrop);
    this.draggingSibling.forEach(item =>
      item.classList.remove('is-drop-allowed'),
    );
  }

  @autobind
  handleImageEnlarge(info: any, target: {rowIndex: number; colIndex: number}) {
    const onImageEnlarge = this.props.onImageEnlarge;

    // 如果已经是多张了，直接跳过
    if (Array.isArray(info.list)) {
      return onImageEnlarge && onImageEnlarge(info, target);
    }

    // 从列表中收集所有图片，然后作为一个图片集合派送出去。
    const store = this.props.store;
    const column = store.columns[target.colIndex].pristine;

    let index = target.rowIndex;
    const list: Array<any> = [];
    store.rows.forEach((row, i) => {
      const src = resolveVariable(column.name, row.data);

      if (!src) {
        if (i < target.rowIndex) {
          index--;
        }
        return;
      }

      list.push({
        src,
        originalSrc: column.originalSrc
          ? filter(column.originalSrc, row.data)
          : src,
        title: column.enlargeTitle
          ? filter(column.enlargeTitle, row.data)
          : column.title
          ? filter(column.title, row.data)
          : undefined,
        caption: column.enlargeCaption
          ? filter(column.enlargeCaption, row.data)
          : column.caption
          ? filter(column.caption, row.data)
          : undefined,
      });
    });

    if (list.length > 1) {
      onImageEnlarge &&
        onImageEnlarge(
          {
            ...info,
            list,
            index,
          },
          target,
        );
    } else {
      onImageEnlarge && onImageEnlarge(info, target);
    }
  }

  // 以下变量都是用于列宽度调整拖拽
  resizeLine?: HTMLElement;
  lineStartX: number;
  lineStartWidth: number;

  // 开始列宽度调整
  @autobind
  handleColResizeMouseDown(e: React.MouseEvent<HTMLElement>) {
    this.lineStartX = e.clientX;
    const currentTarget = e.currentTarget;
    this.resizeLine = currentTarget;
    const store = this.props.store;
    const index = parseInt(this.resizeLine!.getAttribute('data-index')!, 10);
    const column = store.columns[index];
    this.lineStartWidth = column.realWidth || column.width;
    this.resizeLine!.classList.add('is-resizing');

    document.addEventListener('mousemove', this.handleColResizeMouseMove);
    document.addEventListener('mouseup', this.handleColResizeMouseUp);

    // 防止选中文本
    e.preventDefault();
    e.stopPropagation();
  }

  // 垂直线拖拽移动
  @autobind
  handleColResizeMouseMove(e: MouseEvent) {
    const moveX = e.clientX - this.lineStartX;
    const store = this.props.store;
    const index = parseInt(this.resizeLine!.getAttribute('data-index')!, 10);
    const column = store.columns[index];

    column.setWidth(Math.max(this.lineStartWidth + moveX, 30, column.minWidth));
  }

  // 垂直线拖拽结束
  @autobind
  handleColResizeMouseUp(e: MouseEvent) {
    this.resizeLine!.classList.remove('is-resizing');
    delete this.resizeLine;
    document.removeEventListener('mousemove', this.handleColResizeMouseMove);
    document.removeEventListener('mouseup', this.handleColResizeMouseUp);
  }

  handleColumnToggle(columns: Array<IColumn>) {
    const {store} = this.props;

    store.updateColumns(columns);
    store.persistSaveToggledColumns();
  }

  renderAutoFilterForm(): React.ReactNode {
    const {
      render,
      store,
      onSearchableFromReset,
      onSearchableFromSubmit,
      onSearchableFromInit,
      classnames: cx,
      translate: __,
      autoGenerateFilter,
      topToolbarReactNode,
      filterRowNum,
    } = this.props;
    const {showBtnToolbar = true} =
      typeof autoGenerateFilter === 'boolean' ? {} : autoGenerateFilter;
    const searchableColumns = store.searchableColumns;
    const activedSearchableColumns = store.activedSearchableColumns;

    if (!searchableColumns.length) {
      return null;
    }

    const body: Array<any> = [];

    // 自动计算是否超过两行来判断是否开启折叠功能： 展开 or 收起
    const itemsNum = autoGetRenderColumnsNum(activedSearchableColumns, filterRowNum);

    padArr(activedSearchableColumns, itemsNum, true).forEach(group => {
      const children: Array<any> = [];

      group.forEach(column => {
        children.push(
          column
            ? {
                ...(column.searchable === true
                  ? {
                      type: 'input-text',
                      name: column.name,
                      label: column.label,
                    }
                  : {
                      type: 'input-text',
                      name: column.name,
                      ...column.searchable,
                    }),
                name: column.searchable?.name ?? column.name,
                label: column.searchable?.label ?? column.label,
                columnRatio: column.searchable?.columnRatio || 4,
              }
            : {
                type: 'tpl',
                tpl: '',
                columnRatio: 0,
              },
        );
      });

      body.push({
        type: 'group',
        body: children,
      });
    });

    let showExpander = activedSearchableColumns.length > itemsNum;

    // todo 以后做动画
    if (!store.searchFormExpanded) {
      // body.splice(1, body.length - 1);

      // FIX: issue#797 syncLocation 在高级搜索下折叠表单项不会发送问题
      for(let i = 1; i < body.length; i++) {
        body[i].body = body[i]?.body?.filter((item: any) => {
          const value = getPropValue({ ...item, data: store.data });
          return value !== undefined;
        }).map((item: any) => ({ ...item, type: 'hidden' }))
      }
    }

    return render(
      'searchable-form',
      {
        type: 'form',
        api: null,
        title: '',
        mode: 'horizontal',
        submitText: __('search'),
        showLabelColon: true,
        updatePristineAfterStoreDataReInit: true,
        body: body,
        actions: [
          {
            type: 'dropdown-button',
            label: __('Table.searchFields'),
            className: cx('Table-searchableForm-dropdown', 'mr-2'),
            level: 'link',
            trigger: 'click',
            size: 'sm',
            align: 'right',
            visible: showBtnToolbar,
            buttons: searchableColumns.map(column => {
              return {
                type: 'checkbox',
                className: cx('Table-searchableForm-checkbox'),
                inputClassName: cx('Table-searchableForm-checkbox-inner'),
                name: `__search_${column.searchable?.name ?? column.name}`,
                option: column.searchable?.label ?? column.label,
                value: column.enableSearch,
                badge: {
                  offset: [-10, 5],
                  visibleOn: `${
                    column.toggable && !column.toggled && column.enableSearch
                  }`,
                },
                onChange: (value: boolean) => {
                  column.setEnableSearch(value);
                  store.setSearchFormExpanded(true);
                },
              };
            }),
          },
          {
            type: 'reset',
            label: __('CRUD.reset'),
            className: 'w-18',
          },
          {
            type: 'submit',
            label: __('CRUD.search'),
            level: 'primary',
            className: 'w-18',
          },

          showExpander
            ? {
                children: () => (
                  <a
                    className={cx(
                      'Table-SFToggler',
                      store.searchFormExpanded ? 'is-expanded' : '',
                    )}
                    onClick={store.toggleSearchFormExpanded}
                  >
                    {__('Table.collapse')}
                    <span className={cx('Table-SFToggler-arrow')}>
                      <Icon icon="right-arrow-bold" className="icon" />
                    </span>
                  </a>
                ),
              }
            : null,
        ].filter(item => item),
      },
      {
        key: 'searchable-form',
        panelClassName: cx('Table-searchableForm'),
        actionsClassName: cx('Table-searchableForm-footer'),
        onReset: onSearchableFromReset,
        onSubmit: onSearchableFromSubmit,
        onInit: onSearchableFromInit,
        formStore: undefined,
        loading: false,
        topToolbarReactNode
      },
    );
  }

  renderHeading() {
    let {
      title,
      store,
      hideQuickSaveBtn,
      data,
      classnames: cx,
      saveImmediately,
      headingClassName,
      quickSaveApi,
      translate: __,
      columns: unresolvedColumns,
      autoGenerateFilter,
    } = this.props;

    const columns = typeof unresolvedColumns === 'string' ? resolveVariableAndFilter(unresolvedColumns, data, '| raw'): unresolvedColumns;

    // 当被修改列的 column 开启 quickEdit.saveImmediately 时，不展示提交、放弃按钮
    let isModifiedColumnSaveImmediately = false;
    if (store.modifiedRows.length === 1) {
      const saveImmediatelyColumnNames: string[] =
        columns
          ?.map(column =>
            column?.quickEdit?.saveImmediately ? column?.name : '',
          )
          .filter(a => a) || [];

      const item = store.modifiedRows[0];
      const diff = difference(item.data, item.pristine);
      if (intersection(saveImmediatelyColumnNames, Object.keys(diff)).length) {
        isModifiedColumnSaveImmediately = true;
      }
    }

    const headSearchableList =
      columns?.map(column => column?.headSearchable).filter(a => a) || [];

    if (
      title ||
      (quickSaveApi &&
        !saveImmediately &&
        !isModifiedColumnSaveImmediately &&
        store.modified &&
        !hideQuickSaveBtn) ||
      store.moved
    ) {
      return (
        <div className={cx('Table-heading', headingClassName)} key="heading">
          {!saveImmediately &&
          store.modified &&
          !hideQuickSaveBtn &&
          !isModifiedColumnSaveImmediately ? (
            <span>
              {__('Table.modified', {
                modified: store.modified,
              })}
              <button
                type="button"
                className={cx('Button Button--size-xs Button--success m-l-sm')}
                onClick={this.handleSave}
              >
                <Icon icon="check" className="icon m-r-xs" />
                {__('Form.submit')}
              </button>
              <button
                type="button"
                className={cx('Button Button--size-xs Button--danger m-l-sm')}
                onClick={this.reset}
              >
                <Icon icon="drag-close" className="icon m-r-xs" />
                {__('Table.discard')}
              </button>
            </span>
          ) : store.moved ? (
            <span>
              {__('Table.moved', {
                moved: store.moved,
              })}
              <button
                type="button"
                className={cx('Button Button--xs Button--success m-l-sm')}
                onClick={this.handleSaveOrder}
              >
                <Icon icon="check" className="icon m-r-xs" />
                {__('Form.submit')}
              </button>
              <button
                type="button"
                className={cx('Button Button--xs Button--danger m-l-sm')}
                onClick={this.reset}
              >
                <Icon icon="drag-close" className="icon m-r-xs" />
                {__('Table.discard')}
              </button>
            </span>
          ) : title ? (
            // 表格搜索列回显
            <>
              {filter(title, data)}
              {store.searchFormData ? (
                <HeadSearchRender
                  ref={this.headSearchRef}
                  {...this.props}
                  store={store}
                  columns={columns}
                  onQuery={this.props.onQuery}
                />
              ) : null}
            </>
          ) : null}
        </div>
      );
    } else if (
      (!autoGenerateFilter ||
        (autoGenerateFilter && headSearchableList.length)) &&
      store.searchFormData &&
      Object.keys(store.searchFormData).length
    ) {
      return (
        <HeadSearchRender
          ref={this.headSearchRef}
          {...this.props}
          store={store}
          columns={columns}
          onQuery={this.props.onQuery}
        />
      );
    }

    return null;
  }

  renderHeadCell(column: IColumn, props?: any) {
    const {
      store,
      query,
      onQuery,
      multiple,
      env,
      render,
      classPrefix: ns,
      resizable,
      classnames: cx,
      autoGenerateFilter,
      dispatchEvent,
      data,
      disabled,
      sortMultiple,
      dispatchSortEvent,
      cascadeSelection,
      flatHeadSearchable,
    } = this.props;

    // 注意，这里用关了哪些 store 里面的东西，TableContent 里面得也用一下
    // 因为 renderHeadCell 是 TableContent 回调的，tableContent 不重新渲染，这里面也不会重新渲染

    const style = {...props.style};
    const [stickyStyle, stickyClassName] = store.getStickyStyles(
      column,
      store.filteredColumns
    );
    Object.assign(style, stickyStyle);

    /** 如果当前列定宽，则不能操作drag bar */
    const pristineWidth = store.columns?.[column.index]?.pristine?.width;
    const disableColDrag =
      '__' !== column.type.substring(0, 2) &&
      typeof pristineWidth === 'number' &&
      pristineWidth > 0;

    const resizeLine = (
      <div
        className={cx('Table-content-colDragLine', {
          'Table-content-colDragLine--disabled': disableColDrag
        })}
        key={`resize-${column.id}`}
        data-index={column.index}
        onMouseDown={disableColDrag ? noop : this.handleColResizeMouseDown}
      />
    );

    // th 里面不应该设置
    if (style?.width) {
      delete style.width;
    }

    if (column.type === '__checkme') {
      return (
        <th
          {...props}
          style={style}
          className={cx(column.pristine?.className, stickyClassName)}
        >
          {store.rows.length && multiple ? (
            <Checkbox
              classPrefix={ns}
              partial={store.someChecked && !store.allChecked}
              checked={store.someChecked}
              disabled={disabled || (cascadeSelection ? false : store.isSelectionThresholdReached)}
              onChange={this.handleCheckAll}
            />
          ) : (
            '\u00A0'
          )}
        </th>
      );
    } else if (column.type === '__dragme') {
      return (
        <th
          {...props}
          style={style}
          className={cx(column.pristine?.className, stickyClassName)}
        />
      );
    } else if (column.type === '__expandme') {
      // 表格隐藏头部收缩、折叠, 但是需要占位
      return (
        // <th {...props} className={cx(column.pristine.className)}>
        //   {(store.footable &&
        //     (store.footable.expandAll === false || store.footable.accordion)) ||
        //   (store.expandConfig &&
        //     (store.expandConfig.expandAll === false ||
        //       store.expandConfig.accordion)) ? null : (
        //     <a
        //       className={cx(
        //         'Table-expandBtn',
        //         store.allExpanded ? 'is-active' : ''
        //       )}
        //       // data-tooltip="展开/收起全部"
        //       // data-position="top"
        //       onClick={store.toggleExpandAll}
        //     >
        //       <Icon icon="right-arrow-bold" className="icon" />
        //     </a>
        //   )}
        // </th>
        <th
          {...props}
          style={style}
          className={cx(column.pristine?.className, stickyClassName)}
        />
      );
    }

    let affix = [];

    if (column.searchable && column.name && !autoGenerateFilter) {
      affix.push(
        <HeadCellSearchDropDown
          key="table-head-search"
          {...this.props}
          onQuery={onQuery}
          name={column.name}
          searchable={column.searchable}
          sortable={false}
          type={column.type}
          data={query}
          orderBy={store.orderBy}
          orderDir={store.orderDir}
          sortList={store.sortList}
          popOverContainer={this.getPopOverContainer}
        />,
      );
    }
    if (column.sortable && column.name) {
      // 多列排序
      let sortList = [...(store.sortList || [])];
      const idx = sortList.findIndex(item => item.orderBy === column.name);
      const currentOrder =
        idx > -1 ? sortList[idx] : {orderBy: '', orderDir: ''};
      const currentOderDir = currentOrder && currentOrder.orderDir;
      affix.push(
        <span
          key="table-head-sort"
          className={cx('TableCell-sortBtn')}
          onClick={async () => {
            let orderBy = '';
            let orderDir = '';
            if (column.name === store.orderBy) {
              if (store.orderDir !== 'desc') {
                // 升序之后降序
                orderBy = column.name;
                orderDir = 'desc';
              }
            } else {
              orderBy = column.name!;
            }
            const order = orderDir ? 'desc' : 'asc';
            // 按照点击排序的顺序，来控制排序
            if (column.name) {
              if (idx > -1) {
                if (currentOderDir === 'asc') {
                  // 先删除，往最后push
                  sortList.splice(idx, 1);
                  sortList.push({orderBy: column.name, orderDir: 'desc'});
                } else {
                  // 已经向下，取消
                  sortList.splice(idx, 1);
                }
              } else {
                // 第一次向上排序
                sortList.push({
                  orderBy: column.name,
                  orderDir: 'asc',
                });
              }

              if (sortList?.length > 1 && !sortMultiple) {
                sortList = [sortList[sortList.length - 1]];
              }
              const sortDir = sortList.length > 0 ? sortList[sortList.length - 1].orderDir : '';
              const sortBy = sortList.length > 0 ? sortList[sortList.length - 1].orderBy : '';

              const rendererEvent = await dispatchEvent(
                'columnSort',
                createObject(data, {
                  orderBy: sortBy,
                  orderDir: sortDir,
                  sortList,
                }),
              );

              if (rendererEvent?.prevented) {
                return;
              }

              store.setOrderByInfo(sortBy, sortDir);
              store.setSortList(sortList);
              dispatchSortEvent && dispatchSortEvent({
                orderBy: sortBy,
                orderDir: sortDir,
                sortList
              });

              onQuery &&
                onQuery({
                  orderBy: store.orderBy,
                  orderDir: store.orderDir,
                  sortList,
                }, undefined, undefined, true);
            }
          }}
        >
          <i
            className={cx(
              'TableCell-sortBtn--down',
              // store.orderBy === column.name && store.orderDir === 'desc'
              idx > -1 && currentOderDir === 'desc' ? 'is-active' : '',
            )}
          >
            <Icon icon="sort-desc" className="icon" />
          </i>
          <i
            className={cx(
              'TableCell-sortBtn--up',
              // store.orderBy === column.name && store.orderDir === 'asc'
              idx > -1 && currentOderDir === 'asc' ? 'is-active' : '',
            )}
          >
            <Icon icon="sort-asc" className="icon" />
          </i>
          <i
            className={cx(
              'TableCell-sortBtn--default',
              // store.orderBy === column.name ? '' : 'is-active'
              idx > -1 ? '' : 'is-active',
            )}
          >
            <Icon icon="sort-default" className="icon" />
          </i>
        </span>,
      );
    }

    if (!column.searchable && column.name && column.headSearchable) {
      affix.push(
        <HeadCellSearchDropDown
          key="table-head-search"
          {...this.props}
          onQuery={onQuery}
          name={column.name}
          searchable={column.headSearchable}
          sortable={false}
          type={column.type}
          data={query}
          orderBy={store.orderBy}
          orderDir={store.orderDir}
          sortList={store.sortList}
          popOverContainer={this.getPopOverContainer}
          flatHeadSearchable={flatHeadSearchable}
        />,
      );
    }

    if (
      !column.searchable &&
      !column.headSearchable &&
      column.filterable &&
      column.name
    ) {
      affix.push(
        <HeadCellFilterDropDown
          key="table-head-filter"
          {...this.props}
          onQuery={onQuery}
          name={column.name}
          type={column.type}
          data={query}
          filterable={column.filterable}
          popOverContainer={this.getPopOverContainer}
        />,
      );
    }

    return (
      <th
        {...props}
        style={style}
        className={cx(props ? (props as any).className : '', stickyClassName, {
          'TableCell--sortable': column.sortable,
          'TableCell--searchable': column.searchable || column.headSearchable,
          'TableCell--filterable': column.filterable,
          'Table-operationCell': column.type === 'operation',
          'is-highlight': column.isHighLight,
        })}
      >
        <div
          className={cx(
            `${ns}TableCell--title`,
            column.pristine?.className,
            column.pristine?.labelClassName,
          )}
          style={props.style}
        >
          {column.required ? (
            <span className={cx('Form-new-star')}>*</span>
          ) : null}
          {column.label ? render('tpl', column.label) : null}
          {column.remark
            ? render('remark', {
                type: 'remark',
                tooltip: column.remark,
                icon: column.remark?.icon || 'question-mark',
                container:
                  env && env.getModalContainer
                    ? env.getModalContainer
                    : undefined,
              })
            : null}
        </div>

        {affix}
        {resizable === false ? null : resizeLine}
      </th>
    );
  }

  renderIgnoreDragCell = (
    region: string,
    column: IColumn,
    item: IRow,
    props: any,
  ) => {
    return this.renderCell(region, column, item, props, true);
  }

  renderCell(
    region: string,
    column: IColumn,
    item: IRow,
    props: any,
    ignoreDrag = false,
  ) {
    const {
      render,
      store,
      multiple,
      classPrefix: ns,
      classnames: cx,
      checkOnItemClick,
      popOverContainer,
      canAccessSuperData,
      itemBadge,
      foldIcon,
      disabled,
      cascadeSelection,
      id: tableId,
    } = this.props;

    const isInputTable = this.props.tableKey === 'input-table';
    if (column.name && (item.rowSpans[column.name] === 0 || item.colSpans[column.name] === 0)) {
      return null;
    }

    const style: any = {...column.pristine?.style};
    const [stickyStyle, stickyClassName] = store.getStickyStyles(
      column,
      store.filteredColumns
    );
    Object.assign(style, stickyStyle);

    // 实现最多可选择maxKeepItemSelectionLength， 超过禁用
    let checkable = true;
    const { maxKeepItemSelectionLength, selectedRows } = this.props.store;
    if (
      maxKeepItemSelectionLength > 0 &&
      selectedRows?.length &&
      maxKeepItemSelectionLength <= selectedRows.length
    ) {
      checkable =
        selectedRows.findIndex(selectRowItem => selectRowItem.id === item.id) >
          -1
          ? true
          : false;
    }

    if (column.type === '__checkme') {
      if (cascadeSelection && multiple) {
        return (
          <td key={props.key} style={style} className={cx(column.pristine?.className, stickyClassName)}>
            <Checkbox
              partial={item.someChecked && !item.allChecked}
              checked={item.someChecked}
              classPrefix={ns}
              type={'checkbox'}
              disabled={
                disabled || item.checkdisable || !item.checkable
              }
              onChange={
                checkOnItemClick ? noop : this.handleCascadeCheck.bind(this, item)
              }
            />
          </td>
        );
      }
      return (
        <td key={props.key} style={style} className={cx(column.pristine?.className, stickyClassName)}>
          <Checkbox
            classPrefix={ns}
            type={multiple ? 'checkbox' : 'radio'}
            checked={item.checked}
            disabled={
              disabled || item.checkdisable || !item.checkable || !checkable
            }
            onChange={
              checkOnItemClick ? noop : this.handleCheck.bind(this, item)
            }
          />
        </td>
      );
    } else if (column.type === '__dragme') {
      return (
        <td
          key={props.key}
          style={style}
          className={cx(column.pristine?.className, stickyClassName, {
            'is-dragDisabled': !item.draggable,
          })}
        >
          {item.draggable ? <Icon icon="drag" className="icon" /> : null}
        </td>
      );
    } else if (column.type === '__expandme') {
      return (
        <td key={props.key} style={style} className={cx(column.pristine?.className, stickyClassName)}>
          {item.depth > 2
            ? Array.from({length: item.depth - 2}).map((_, index) => (
                <i key={index} className={cx('Table-divider-' + (index + 1))} />
              ))
            : null}

          {item.depth > 1 ? <i className={cx('Table-divider2')} /> : null}
          {item.depth > 1 ? <i className={cx('Table-divider3')} /> : null}

          {/* 此处控制子表格的收缩折叠 */}
          {/* input-table：子表格无数据时，不展示子表格栏位，也就不需要有展开折叠按钮。table、crud交互不变 */}
          {item.expandable && ((isInputTable && item?.data?.children?.length > 0) || !isInputTable) ? (
            <a
              className={cx(
                'Table-expandBtn',
                !foldIcon && item.expanded ? 'is-active' : '',
              )}
              // data-tooltip="展开/收起"
              // data-position="top"
              onClick={item.toggleExpanded}
            >
              {this.renderExpandableIcon(item.expanded)}
            </a>
          ) : null}
        </td>
      );
    }

    let prefix: React.ReactNode = null;

    if (
      !ignoreDrag &&
      column.isPrimary &&
      store.isNested &&
      store.draggable &&
      item.draggable
    ) {
      prefix = (
        <a
          draggable
          onDragStart={this.handleDragStart}
          className={cx('Table-dragBtn')}
        >
          <Icon icon="drag" className="icon" />
        </a>
      );
    }

    const _rowSubTableId = genSubTableId(tableId, item.index);
    const _rowSubTableServiceId = genSubTableServiceId(tableId, item.index);
    // 添加subTable相关id到数据域中，方便column中获取。同时不改表数据链层级
    const data = createObject(
      extendObject(
        item.locals.__super,
        { _rowSubTableId, _rowSubTableServiceId, }
      ),
      item.locals
    );
    const subProps: any = {
      ...props,
      // 操作列不下发loading，否则会导致操作栏里面的所有按钮都出现loading
      loading: column.type === 'operation' ? false : props.loading,
      btnDisabled: store.dragging,
      data,
      value: column.name
        ? resolveVariable(
            column.name,
            canAccessSuperData ? item.locals : item.data,
          )
        : column.value,
      popOverContainer: popOverContainer || this.getPopOverContainer,
      rowSpan: item.rowSpans[column.name as string],
      colSpan: item.colSpans[column.name as string],
      quickEditFormRef: this.subFormRef,
      cellPrefix: prefix,
      onImageEnlarge: this.handleImageEnlarge,
      canAccessSuperData,
      row: item,
      itemBadge,
      showBadge:
        !props.isHead &&
        itemBadge &&
        store.firstToggledColumnIndex === props.colIndex,
      style,
      className: cx(column.pristine?.className, column.className, stickyClassName)
    };
    delete subProps.label;
    delete subProps.parent;


    //  特殊处理inputTable嵌套，还原嵌套设计
    if (column.type.startsWith('internalCustomization_')) {
      let _temp: any = {
        ...column,
        type: column.type.replace(/internalCustomization_/, '')
      }

      return render(
        region,
        _temp,
        {
          ...subProps,
          // 这里强制下发数据，解决无限死循环问题
          data: subProps?.data?.children || [],
        },
      );
    }


    return render(
      region,
      {
        ...column.pristine,
        column: column.pristine,
        type: 'cell',
      },
      subProps,
    );
  }

  // 渲染固定的列表头
  renderAffixHeader(tableClassName: string) {
    const {
      store,
      affixHeader,
      render,
      classnames: cx,
      autoFillHeight,
      isPublicHeader,
      tableKey,
    } = this.props;
    const hideHeader = store.filteredColumns.every(column => !column.label);
    const columnsGroup = store.columnGroup;
    const isInputTable = tableKey === 'input-table';

    // affixHeader和autoFillHeight功能冲突，开启autoFillHeight后默认关闭affixHeader
    return affixHeader && !autoFillHeight ? (
      <>
        <div
          className={cx('Table-fixedTop', {
            'is-fakeHide': hideHeader
          })}
        >
          {this.renderHeader(false)}
          {this.renderHeading()}
          {store.columnWidthReady ? (
            <div className={cx('Table-wrapper')}>
              <table
                ref={this.affixedTableRef}
                className={cx(
                  tableClassName,
                  store.tableLayout === 'fixed' ? 'is-layout-fixed' : ''
                )}
              >
                <colgroup>
                  {store.filteredColumns.map(column => {
                    const style: any = {
                      width: `var(--Table-column-${column.index}-width)`
                    };

                    if (store.tableLayout === 'auto') {
                      style.minWidth = style.width;
                    }

                    return (
                      <col
                        data-index={column.index}
                        style={style}
                        key={column.id}
                      />
                    );
                  })}
                </colgroup>
                {(!isPublicHeader || !isInputTable) && (
                  <thead>
                    {columnsGroup.length ? (
                      <tr>
                        {columnsGroup.map((item, index) => {
                          const [stickyStyle, stickyClassName] =
                            store.getStickyStyles(
                              item as any,
                              columnsGroup as any
                            );

                          return item.rowSpan === 1 ? ( // 如果是分组自己，则用 th 渲染
                            <th
                              key={index}
                              data-index={item.index}
                              colSpan={item.colSpan}
                              rowSpan={item.rowSpan}
                              style={stickyStyle}
                              className={stickyClassName}
                            >
                              {item.label ? render('tpl', item.label) : null}
                            </th>
                          ) : (
                            // fix: 否则走 renderHeadCell 因为不走的话，排序按钮不会渲染
                            this.renderHeadCell(item.has[0], {
                              'label': item.label,
                              'key': index,
                              'data-index': item.index,
                              'colSpan': item.colSpan,
                              'rowSpan': item.rowSpan,
                              'style': stickyStyle,
                              'className': stickyClassName
                            })
                          );
                        })}
                      </tr>
                    ) : null}
                    <tr>
                      {store.filteredColumns.map(column =>
                        columnsGroup.find(group => ~group.has.indexOf(column))
                          ?.rowSpan === 2
                          ? null
                          : this.renderHeadCell(column, {
                            'key': column.index,
                            'data-index': column.index
                          })
                      )}
                    </tr>
                  </thead>
                )}
              </table>
            </div>
          ) : null}
        </div>
      </>
    ) : null;
  }

  renderToolbar(toolbar: SchemaNode) {
    const type = (toolbar as Schema).type || (toolbar as string);

    if (type === 'columns-toggler') {
      this.renderedToolbars.push(type);
      return this.renderColumnsToggler(toolbar as any);
    } else if (type === 'drag-toggler') {
      this.renderedToolbars.push(type);
      return this.renderDragToggler();
    } else if (type === 'export-excel') {
      this.renderedToolbars.push(type);
      return this.renderExportExcel(toolbar);
    }

    return void 0;
  }

  renderColumnsToggler(config?: any) {
    const {
      className,
      store,
      classPrefix: ns,
      classnames: cx,
      ...rest
    } = this.props;
    const __ = rest.translate;
    const env = rest.env;
    const render = this.props.render;

    if (!store.columnsTogglable) {
      return null;
    }

    return (
      <ColumnToggler
        {...rest}
        {...(isObject(config) ? config : {})}
        tooltip={config?.tooltip || __('Table.columnsVisibility')}
        tooltipContainer={
          env && env.getModalContainer ? env.getModalContainer : undefined
        }
        align={config?.align ?? 'left'}
        isActived={store.hasColumnHidden()}
        classnames={cx}
        classPrefix={ns}
        key="columns-toggable"
        size={config?.size || 'sm'}
        icon={config?.icon}
        label={config?.label}
        draggable={config?.draggable}
        columns={store.columnsData}
        activeToggaleColumns={store.activeToggaleColumns}
        onColumnToggle={this.handleColumnToggle}
      >
        {store.toggableColumns.length ? (
          <li
            className={cx('ColumnToggler-menuItem')}
            key={'selectAll'}
            onClick={async () => {
              const {data, dispatchEvent} = this.props;

              const allToggled = !(
                store.activeToggaleColumns.length ===
                store.toggableColumns.length
              );
              const rendererEvent = await dispatchEvent(
                'columnToggled',
                createObject(data, {
                  columns: allToggled
                    ? store.toggableColumns.map(column => column.pristine)
                    : [],
                }),
              );

              if (rendererEvent?.prevented) {
                return;
              }

              store.toggleAllColumns();
            }}
          >
            <Checkbox
              size="sm"
              classPrefix={ns}
              key="checkall"
              checked={!!store.activeToggaleColumns.length}
              partial={
                !!(
                  store.activeToggaleColumns.length &&
                  store.activeToggaleColumns.length !==
                    store.toggableColumns.length
                )
              }
            >
              {__('Checkboxes.selectAll')}
            </Checkbox>
          </li>
        ) : null}

        {store.toggableColumns.map(column => (
          <li
            className={cx('ColumnToggler-menuItem')}
            key={column.index}
            onClick={async () => {
              const {data, dispatchEvent} = this.props;
              let columns = store.activeToggaleColumns.map(
                item => item.pristine,
              );
              if (!column.toggled) {
                columns.push(column.pristine);
              } else {
                columns = columns.filter(c => c.name !== column.pristine?.name);
              }
              const rendererEvent = await dispatchEvent(
                'columnToggled',
                createObject(data, {
                  columns,
                }),
              );

              if (rendererEvent?.prevented) {
                return;
              }

              column.toggleToggle();
            }}
          >
            <Checkbox size="sm" classPrefix={ns} checked={column.toggled}>
              {column.label ? render('tpl', column.label) : null}
            </Checkbox>
          </li>
        ))}
      </ColumnToggler>
    );
  }

  renderDragToggler() {
    const {store, env, draggable, classPrefix: ns, translate: __, onSelect} = this.props;

    if (!draggable || store.isNested) {
      return null;
    }

    return (
      <Button
        disabled={!!store.modified}
        classPrefix={ns}
        key="dragging-toggle"
        tooltip={__('Table.startSort')}
        tooltipContainer={
          env && env.getModalContainer ? env.getModalContainer : undefined
        }
        size="sm"
        active={store.dragging}
        onClick={(e: React.MouseEvent<any>) => {
          e.preventDefault();
          store.toggleDragging();
          if (store.dragging) {
            store.clear()
            onSelect && onSelect([], store.getData()?.items ?? []);
          }
        }}
        iconOnly
      >
        <Icon icon="exchange" className="icon" />
      </Button>
    );
  }

  renderExportExcel(toolbar: ExportExcelToolbar) {
    const {
      store,
      env,
      classPrefix: ns,
      classnames: cx,
      translate: __,
      data,
      render,
    } = this.props;

    let columns = store.filteredColumns || [];

    if (!columns) {
      return null;
    }

    return render(
      'exportExcel',
      {
        label: __('CRUD.exportExcel'),
        ...(toolbar as any),
        type: 'button',
      },
      {
        onAction: () => {
          import('exceljs').then(async (ExcelJS: any) => {
            exportExcel(ExcelJS, this.props, toolbar);
          });
        },
      },
    );
  }

  renderActions(region: string) {
    let {actions, render, store, classnames: cx, data} = this.props;

    actions = Array.isArray(actions) ? actions.concat() : [];

    if (
      store.toggable &&
      region === 'header' &&
      !~this.renderedToolbars.indexOf('columns-toggler')
    ) {
      actions.push({
        type: 'button',
        children: this.renderColumnsToggler(),
      });
    }

    if (
      store.draggable &&
      !store.isNested &&
      region === 'header' &&
      store.rows.length > 1 &&
      !~this.renderedToolbars.indexOf('drag-toggler')
    ) {
      actions.push({
        type: 'button',
        children: this.renderDragToggler(),
      });
    }

    return Array.isArray(actions) && actions.length ? (
      <div className={cx('Table-actions')}>
        {actions.map((action, key) =>
          render(
            `action/${key}`,
            {
              type: 'button',
              ...(action as any),
            },
            {
              onAction: this.handleAction,
              key,
              btnDisabled: store.dragging,
              data: store.getData(data),
            },
          ),
        )}
      </div>
    ) : null;
  }

  renderHeader(editable?: boolean) {
    const {
      header,
      headerClassName,
      toolbarClassName,
      headerToolbarClassName,
      headerToolbarRender,
      headerFilterRender,
      render,
      showHeader,
      store,
      classnames: cx,
      data,
      translate: __,
      noPadding = false,
    } = this.props;

    if (showHeader === false) {
      return null;
    }

    const otherProps: any = {};
    // editable === false && (otherProps.$$editable = false);

    const child = headerToolbarRender
      ? headerToolbarRender(
          {
            ...this.props,
            selectedItems: store.selectedRows.map(item => item.data),
            items: store.rows.map(item => item.data),
            unSelectedItems: store.unSelectedRows.map(item => item.data),
            ...otherProps,
          },
          this.renderToolbar,
        )
      : null;
    const actions = this.renderActions('header');

    const headerFilterNode = headerFilterRender ? headerFilterRender() : null;

    const toolbarNode =
      actions || child || store.dragging ? (
        <div
          className={cx(
            'Table-toolbar Table-headToolbar',
            toolbarClassName,
            headerToolbarClassName,
            noPadding ? 'pm-px-0' : '' // 如果设置了noPadding 去除左右padding
          )}
          key="header-toolbar"
        >
          {actions}
          {child}
          {store.dragging ? (
            <div className={cx('Table-dragTip')} ref={this.dragTipRef}>
              {__('Table.dragTip')}
            </div>
          ) : null}
        </div>
      ) : null;
    const headerNode =
      header && (!Array.isArray(header) || header.length) ? (
        <div className={cx('Table-header', headerClassName)} key="header">
          {render('header', header, {
            ...(editable === false ? otherProps : null),
            data: store.getData(data),
          })}
        </div>
      ) : null;

    const nodes: any = [];

    [headerFilterNode, headerNode, toolbarNode].forEach((itm) => {
      itm && nodes.push(itm);
    });

    return nodes.length > 0 ?  nodes : null;
  }

  renderFooter() {
    const {
      footer,
      toolbarClassName,
      footerToolbarClassName,
      footerClassName,
      footerToolbarRender,
      render,
      showFooter,
      store,
      data,
      classnames: cx,
    } = this.props;

    if (showFooter === false) {
      return null;
    }

    const child = footerToolbarRender
      ? footerToolbarRender(
          {
            ...this.props,
            selectedItems: store.selectedRows.map(item => item.data),
            items: store.rows.map(item => item.data),
          },
          this.renderToolbar,
        )
      : null;
    const actions = this.renderActions('footer');

    const toolbarNode =
      actions || child ? (
        <div
          className={cx(
            'Table-toolbar Table-footToolbar',
            toolbarClassName,
            footerToolbarClassName,
          )}
          key="footer-toolbar"
        >
          {actions}
          {child}
        </div>
      ) : null;
    const footerNode =
      footer && (!Array.isArray(footer) || footer.length) ? (
        <div className={cx('Table-footer', footerClassName)} key="footer">
          {render('footer', footer, {
            data: store.getData(data),
          })}
        </div>
      ) : null;
    return footerNode && toolbarNode
      ? [toolbarNode, footerNode]
      : footerNode || toolbarNode || null;
  }

  renderTableContent(n?: number) {
    const {
      classnames: cx,
      tableClassName,
      store,
      placeholder,
      render,
      checkOnItemClick,
      buildItemProps,
      rowClassNameExpr,
      rowClassName,
      prefixRow,
      locale,
      affixRow,
      tableContentClassName,
      translate,
      itemAction,
      affixRowClassNameExpr,
      affixRowClassName,
      prefixRowClassNameExpr,
      prefixRowClassName,
      autoFillHeight,
      itemActions,
      dispatchEvent,
      onEvent,
      loading = false,
      loadingConfig,
      subTable,
      subTableOperationIndex,
      subTableAddRowIndex,
      isCreateModeOfSubTable,
      valueField,
      mountAll,
      isPublicHeader,
      tableKey,
      spinnerSize,
      offset,
      updateAllRows,
      affixHeader,
      isAuthorityDataLoaded,
      id: tableId,
    } = this.props;

    // 理论上来说 store.rows 应该也行啊
    // 不过目前看来只有这样写它才会重新更新视图
    store.rows.length;

    return (
      <>
        <TableContent
          isAuthorityDataLoaded={isAuthorityDataLoaded}
          tableClassName={cx(
            {
              'Table-table--checkOnItemClick': checkOnItemClick,
              'Table-table--withCombine': store.isMergedCell,
              'Table-table--affixHeader':
                affixHeader && !autoFillHeight && store.columnWidthReady
            },
            tableClassName,
          )}
          className={tableContentClassName}
          itemActions={itemActions}
          itemAction={itemAction}
          store={store}
          classnames={cx}
          columns={store.filteredColumns}
          columnsGroup={store.columnGroup}
          rows={n ? store.rows.slice(0, n) : store.rows}
          placeholder={placeholder}
          render={render}
          onMouseMove={
            // 如果没有 itemActions, 那么就不需要处理了。
            Array.isArray(itemActions) && itemActions.length
              ? this.handleMouseMove
              : undefined
          }
          onScroll={this.handleOutterScroll}
          tableRef={this.tableRef}
          renderHeadCell={this.renderHeadCell}
          renderCell={this.renderCell}
          onCheck={this.handleCheck}
          onRowClick={this.handleRowClick}
          onRowDbClick={this.handleRowDbClick}
          onQuickChange={store.dragging ? undefined : this.handleQuickChange}
          footable={store.footable}
          footableColumns={store.footableColumns}
          checkOnItemClick={checkOnItemClick}
          buildItemProps={buildItemProps}
          onAction={this.handleAction}
          rowClassNameExpr={rowClassNameExpr}
          rowClassName={rowClassName}
          data={store.data}
          prefixRow={prefixRow}
          affixRow={affixRow}
          prefixRowClassName={prefixRowClassName}
          affixRowClassName={affixRowClassName}
          locale={locale}
          translate={translate}
          dispatchEvent={dispatchEvent}
          onEvent={onEvent}
          loading={loading}
          subTable={subTable}
          subTableOperationIndex={subTableOperationIndex}
          subTableAddRowIndex={subTableAddRowIndex}
          isCreateModeOfSubTable={isCreateModeOfSubTable}
          valueField={valueField}
          mountAll={mountAll}
          isPublicHeader={isPublicHeader}
          tableKey={tableKey}
          tableId={tableId}
          offset={offset}
          columnPosition={"normal"}
          updateAllRows={updateAllRows}
        >
          {TableContent.renderItemActions({
            store,
            classnames: cx,
            render,
            itemActions
          })}
        </TableContent>

        <Spinner loadingConfig={loadingConfig} overlay show={loading} size={spinnerSize} />
      </>
    );
  }

  /**
   * 通过 index 或者 condition 获取需要处理的目标
   *
   * - index 支持数字
   * - index 支持逗号分隔的数字列表
   * - index 支持路径比如 0.1.2,0.1.3
   * - index 支持表达式，比如 0.1.2,${index}
   *
   * - condition 上下文为当前行的数据
   *
   * @param ctx
   * @param index
   * @param condition
   * @returns
   */
  async getEventTargets(
    ctx: any,
    args?: any,
    index?: string | number,
    condition?: string
  ) {
    const {store} = this.props;
    return getMatchedEventTargets<IRow>(
      store.rows,
      ctx || this.props.data,
      args
    );
  }

  async doAction(action: ActionObject, args: any, throwErrors: boolean): Promise<any> {
    const {store, valueField, data} = this.props;

    const actionType = action?.actionType as string;

    switch (actionType) {
      case 'selectAll':
        store.clear();
        store.toggleAll();
        break;
      case 'clearAll':
        store.clear();
        break;
      case 'select':
        const dataSource = store.getData(data);
        const selected: Array<any> = [];
        dataSource.items.forEach((item: any, rowIndex: number) => {
          const flag = evalExpression(args?.selected, {record: item, rowIndex});
          if (flag) {
            selected.push(item);
          }
        });
        store.updateSelected(selected, valueField);
        break;
      case 'initDrag':
        store.stopDragging();
        store.toggleDragging();
        break;
      case 'toggleExpanded':
        // store.rows.find(row => row.data?.id === args.id)?.toggleExpanded();
        // 根据args获取需要处理的目标
        const ctx = store.data;
        // 兼容args.id情况
        args.ids = args?.id !== undefined ? args.id : args?.ids;
        const targets = await this.getEventTargets(
          ctx,
          args
        );
        targets.forEach(target => {
          store.toggleExpanded(target);
        });
        break;
      case 'scrollIntoView':
        this.scrollIntoViewByColumnName(args.columnName);
        break
      case 'setHighLightColumn':
        this.setColumnIsHighLightByColumnName(args.columnName);
        break
      case 'removeHighLightColumn':
        this.removeColumnIsHighLight();
        break
      default:
        this.handleAction(undefined, action, data);
        break;
    }
  }

  render() {
    const {
      className,
      style,
      store,
      classnames: cx,
      affixColumns,
      autoFillHeight,
      autoGenerateFilter,
      isInlineHeader,
      renderSelection,
      parent,
      affixHeader,
      offset,
      batchNum,
    } = this.props;

    this.renderedToolbars = []; // 用来记录哪些 toolbar 已经渲染了，已经渲染了就不重复渲染了。
    const heading =
      affixHeader && !autoFillHeight ? null : this.renderHeading();
    const header = affixHeader && !autoFillHeight ? null : this.renderHeader();
    const footer = this.renderFooter();
    const tableClassName = cx('Table-table', this.props.tableClassName, {
      'Table-table--withCombine': store.isMergedCell,
    });
    const selectRender = renderSelection?.();

    return (
      <div
        className={cx('Table', className, {
          'Table--unsaved': !!store.modified || !!store.moved,
          'Table--autoFillHeight': autoFillHeight
        })}
        style={store.buildStyles(style)}
      >
        {autoGenerateFilter ? this.renderAutoFilterForm() : null}
        {this.renderAffixHeader(tableClassName)}
        <div className={cx('Table-headerContainer', {'Table--headerWrap': isInlineHeader})}>
          {header}
          {selectRender}
          {heading}
        </div>
        <div
          className={cx('Table-contentWrap')}
          onMouseLeave={this.handleMouseLeave}
        >
          {batchNum ? (
            <BatchedRender batchNum={batchNum} batchKey={offset} total={store.rows.length} >
            {(n, m) => {
              console.log('renderTableContent', n, m);
              return this.renderTableContent(n)
            }}
          </BatchedRender>
          ) : this.renderTableContent()}
        </div>

        {footer}
      </div>
    );
  }
}

@Renderer({
  type: 'table',
  storeType: TableStore.name,
  name: 'table',
})
export class TableRenderer extends Table {
  receive(values: any, subPath?: string) {
    const scoped = this.context as IScopedContext;
    const parents = scoped?.parent?.getComponents();

    /**
     * 因为Table在scope上注册，导致getComponentByName查询组件时会优先找到Table，和CRUD联动的动作都会失效
     * 这里先做兼容处理，把动作交给上层的CRUD处理
     */
    if (Array.isArray(parents) && parents.length) {
      // CRUD的name会透传给Table，这样可以保证找到CRUD
      const crud = parents.find(cmpt => cmpt?.props?.name === this.props?.name);

      return crud?.receive?.(values, subPath);
    }

    if (subPath) {
      return scoped.send(subPath, values);
    }
  }

  reload(subPath?: string, query?: any, ctx?: any) {
    const scoped = this.context as IScopedContext;
    const parents = scoped?.parent?.getComponents();

    if (Array.isArray(parents) && parents.length) {
      // CRUD的name会透传给Table，这样可以保证找到CRUD
      const crud = parents.find(cmpt => cmpt?.props?.name === this.props?.name);

      return crud?.reload?.(subPath, query, ctx);
    }

    if (subPath) {
      return scoped.reload(subPath, ctx);
    }
  }
}

export { TableCell };
