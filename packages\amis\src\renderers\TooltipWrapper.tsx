import React from 'react';
import {Renderer, RendererProps} from 'amis-core';
import {BaseSchema, SchemaCollection} from '../Schema';
import {filter, autobind, buildStyle, resolveEventData} from 'amis-core';
import {TooltipWrapper as TooltipWrapperComp} from 'amis-ui';
import type {
  TooltipObject,
  footerBottonObject,
  Trigger
} from 'amis-ui/lib/components/TooltipWrapper';

export interface footerBottonProps
  extends Omit<footerBottonObject, 'onCancel' | 'onConfirm'> {}

export interface TooltipWrapperSchema extends BaseSchema {
  /**
   * 文字提示容器
   */
  type: 'tooltip-wrapper';

  /**
   * 文字提示标题
   */
  title?: string;

  /**
   * 文字提示内容，兼容 tooltip，但建议通过 content 来实现提示内容
   */
  content?: string;

  /**
   *  @deprecated 文字提示内容
   */
  tooltip?: string;

  /**
   * 文字提示浮层出现位置，默认为top
   */
  placement?: 'top' | 'right' | 'bottom' | 'left';

  /**
   * 浮层位置相对偏移量
   */
  offset?: [number, number];

  /**
   * 是否展示浮层指向箭头
   */
  showArrow?: boolean;

  /**
   * 是否禁用提示
   */
  disabled?: boolean;

  /**
   * 浮层触发方式，默认为hover
   */
  trigger?: Trigger | Array<Trigger>;

  /**
   * 浮层延迟显示时间, 单位 ms
   */

  mouseEnterDelay?: number;
  /**
   * 浮层延迟隐藏时间, 单位 ms
   */
  mouseLeaveDelay?: number;

  /**
   * 是否点击非内容区域关闭提示，默认为true
   */
  rootClose?: boolean;

  /**
   * 内容区域
   */
  body?: SchemaCollection;

  /**
   * 内容区包裹标签
   */
  wrapperComponent: string;

  /**
   * 内容区是否内联显示，默认为false
   */
  inline?: boolean;

  /**
   * 主题样式， 默认为light
   */
  tooltipTheme?: 'light' | 'dark';

  /**
   * 内容区自定义样式
   */
  style?: {
    [propName: string]: any;
  };

  /**
   * 是否可以移入浮层中, 默认true
   */
  enterable?: boolean;

  /**
   * 自定义提示浮层样式
   */
  tooltipStyle?: {
    [propName: string]: any;
  };

  /**
   * 内容区CSS类名
   */
  className?: string;

  /**
   * 文字提示浮层CSS类名
   */
  tooltipClassName?: string;
  /**
   * 底部按钮配置
   */
  footerBottonProps?: footerBottonProps;

}

function filterContents(
  tooltip:
    | string
    | undefined
    | {title?: string; children?: any; content?: string; body?: string},
  data: any,
  render: any
) {
  if (typeof tooltip === 'string') {
    return filter(tooltip, data);
  } else if (tooltip) {
    return render('content', tooltip);
  }
  return tooltip;
}

export interface TooltipWrapperProps extends RendererProps {
  /**
   * 文字提示标题
   */
  title?: string;
  /**
   * 文字提示
   */
  content?: string;
  tooltip?: string;
  /**
   * 文字提示位置
   */
  placement: 'top' | 'right' | 'bottom' | 'left';
  inline?: boolean;
  trigger: Trigger | Array<Trigger>;
  rootClose?: boolean;
  showArrow?: boolean;
  offset?: [number, number];
  disabled?: boolean;
  mouseEnterDelay?: number;
  mouseLeaveDelay?: number;
  container?: HTMLElement | (() => HTMLElement);
  style?: React.CSSProperties;
  tooltipStyle?: React.CSSProperties;
  wrapperComponent?: string;
  tooltipTheme?: 'light' | 'dark';
  /**
   * 文字提示浮层挂载位置
   */
  containerSelector?: string
}

interface TooltipWrapperState {}

export default class TooltipWrapper extends React.Component<
  TooltipWrapperProps,
  TooltipWrapperState
> {
  static defaultProps: Pick<
    TooltipWrapperProps,
    | 'placement'
    | 'trigger'
    | 'rootClose'
    | 'mouseEnterDelay'
    | 'mouseLeaveDelay'
    | 'inline'
    | 'wrap'
    | 'tooltipTheme'
  > = {
    placement: 'top',
    trigger: 'hover',
    rootClose: true,
    mouseEnterDelay: 0,
    mouseLeaveDelay: 200,
    inline: false,
    wrap: false,
    tooltipTheme: 'dark'
  };

  constructor(props: TooltipWrapperProps) {
    super(props);
  }

  @autobind
  async onCancel() {
    const {dispatchEvent} = this.props;

    const cancelEvent = await dispatchEvent('cancel');
    return cancelEvent; // 返回事件结果，让UI层判断是否prevented
  }

  @autobind
  async onConfirm() {
    const {dispatchEvent} = this.props;

    const cancelEvent = await dispatchEvent('confirm');
    return cancelEvent;
  }

  renderBody() {
    const {
      render,
      classnames: cx,
      body,
      className,
      wrapperComponent,
      inline,
      style,
      data,
      wrap
    } = this.props;
    const Comp =
      (wrapperComponent as keyof JSX.IntrinsicElements) ||
      (inline ? 'span' : 'div');

    return (
      <Comp
        className={cx('TooltipWrapper', className, {
          'TooltipWrapper--inline': inline
        })}
        style={buildStyle(style, data)}
      >
        {render('body', body)}
      </Comp>
    );
  }

  render() {
    const {
      classPrefix: ns,
      classnames: cx,
      tooltipClassName,
      tooltipTheme,
      container,
      placement,
      rootClose,
      tooltipStyle,
      title,
      content,
      tooltip,
      mouseEnterDelay,
      mouseLeaveDelay,
      trigger,
      offset,
      showArrow,
      disabled,
      enterable,
      data,
      footerBottonProps,
      containerSelector,
      render,
      env
    } = this.props;

    const tooltipObj: TooltipObject = {
      title: filter(title, data),
      content: filterContents(content || tooltip, data, render),
      style: buildStyle(tooltipStyle, data),
      placement,
      trigger,
      rootClose,
      containerSelector:containerSelector,
      container:
        container !== undefined
          ? container
          : env && env.getModalContainer
          ? env.getModalContainer
          : undefined,
      tooltipTheme,
      tooltipClassName,
      mouseEnterDelay,
      mouseLeaveDelay,
      offset,
      showArrow,
      disabled,
      enterable,
      footerBottonProps: {
        ...footerBottonProps,
        onCancel: this.onCancel,
        onConfirm: this.onConfirm,
      },
    };

    return (
      <TooltipWrapperComp classPrefix={ns} classnames={cx} tooltip={tooltipObj}>
        {this.renderBody()}
      </TooltipWrapperComp>
    );
  }
}

@Renderer({
  type: 'tooltip-wrapper'
})
export class TooltipWrapperRenderer extends TooltipWrapper {}
