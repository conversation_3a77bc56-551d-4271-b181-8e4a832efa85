import {
  autobind,
  createObject,
  filter,
  getPropValue,
  Renderer,
  RendererProps,
  isObject,
  Schema,
} from 'amis-core';
import type {TypographyEllipsisObject} from 'amis-ui';
import {Typography as TypographyComp} from 'amis-ui';
import React from 'react';
import isNil from 'lodash/isNil';
import {BaseSchema, SchemaTpl} from '../Schema';
import {filterContents} from './Remark';

/**
 * Typography 渲染器
 */
export interface TypographySchema extends BaseSchema {
  /**
   * 指定为模板渲染器。
   *
   */
  type: 'typography';

  /**
   * 文本内容
   */
  text?: SchemaTpl | Schema;
  content?: SchemaTpl;

  /**
   * 自定义样式
   */
  style?: {
    [propName: string]: any;
  };

  /**
   * 文本容器CSS类名
   */
  className?: string;

  /**
   * 占位符，默认 -
   *
   */
  placeholder?: string;

  /**
   * 省略号配置
   */
  ellipsis?: TypographyEllipsisObject;
}

export interface TypographyProps
  extends RendererProps,
    Omit<TypographySchema, 'type' | 'className'> {}

export class Typography extends React.Component<TypographyProps, object> {
  static defaultProps: Partial<TypographyProps> = {
    placeholder: '-',
    ellipsis: {
      tooltip: {
        tooltipTheme: 'dark',
      },
    },
  };

  constructor(props: TypographyProps) {
    super(props);
  }

  // 处理传入的text和value值
  getContent() {
    const {text, data, placeholder, render} = this.props;
    const value = getPropValue(this.props);

    // 支持schema，issue#469中针对formItem的label用此组件包裹一层，label支持配置schema
    if(isObject(text) && (text as Schema).type || Array.isArray(text)) {
      return render('text', text as Schema);
    }

    // filter将number类型处理成空字符串，number类型转成string展示
    let textContent = typeof text === 'number'
      ? String(text)
      : filter(text, data).trim?.();
    const textIsTrue = textContent !== null && textContent !== undefined && textContent !== '';
    const valueIsNull = isNil(value) || value === '';

    // FIX: issue807 最终渲染保持一致都走render
    if(!textIsTrue) {
      if(valueIsNull) {
        return <span className="text-muted">{placeholder}</span>
      }

      textContent = String(value);
    }

    return render('text', {
      type: 'tpl',
      tpl: textContent,
    })
  }

  @autobind
  handleClick(e: React.MouseEvent<HTMLDivElement>) {
    const {dispatchEvent, data} = this.props;
    dispatchEvent(
      e,
      createObject(data, {
        nativeEvent: e,
      }),
    );
  }

  @autobind
  handleMouseEnter(e: React.MouseEvent<any>) {
    const {dispatchEvent, data} = this.props;
    dispatchEvent(
      e,
      createObject(data, {
        nativeEvent: e,
      }),
    );
  }

  @autobind
  handleMouseLeave(e: React.MouseEvent<any>) {
    const {dispatchEvent, data} = this.props;
    dispatchEvent(
      e,
      createObject(data, {
        nativeEvent: e,
      }),
    );
  }

  render() {
    const {
      wrapperComponent,
      classnames: cx,
      ellipsis,
      data,
      render,
      ...rest
    } = this.props;
    const content = this.getContent();

    let formatEllipsis = {
      ...ellipsis,
      // #1219 处理tooltip未配置content/body/title时，filterContents会返回undefined，导致其他tooltip配置项被丢弃
      tooltip: filterContents(ellipsis?.tooltip, data, render) ?? ellipsis?.tooltip,
    };

    return (
      <TypographyComp
        onClick={this.handleClick}
        onMouseEnter={this.handleMouseEnter}
        onMouseLeave={this.handleMouseLeave}
        ellipsis={formatEllipsis}
        {...rest}
      >
        {content}
      </TypographyComp>
    );
  }
}

@Renderer({
  type: 'typography',
})
export class TypographyRenderer extends Typography {}
