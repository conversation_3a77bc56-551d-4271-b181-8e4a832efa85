import {defineConfig} from 'father';

const ignores = ['src/**/demo/**', 'src/Table/**'];

export default defineConfig({
  esm: {
    output: 'esm',
    platform: 'browser', // 默认构建为 Browser 环境的产物
    transformer: 'babel', // 默认使用 babel 以提供更好的兼容性
    ignores,
  },
  cjs: {
    output: 'lib',
    platform: 'browser', // 默认构建为 Node.js 环境的产物
    transformer: 'babel', // 默认使用 esbuild 以获得更快的构建速度
    ignores,
  },
  umd: {
    name: '@lattebank/antd-plus-umd',
    output: {
      path: 'dist',
      filename: "antd-plus-umd-antd"
    },
    // sourcemap: true,
    // platform: 'browser',
    externals: {
      react: 'React',
      'react-dom': 'ReactDOM',
    },
    // chainWebpack: (memo) => {
    //   memo.output.library('@lattebank/antd-plus-umd');
    //   return memo;
    // },
  },
});
