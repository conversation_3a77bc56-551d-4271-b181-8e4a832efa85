{"name": "@dataseed/pro-components", "version": "1.87.3", "description": "ProCode component library for dataseed", "license": "MIT", "main": "lib/index.js", "module": "esm/index.js", "typings": "esm/index.d.ts", "sideEffects": false, "files": ["dist", "esm", "lib"], "scripts": {"build": "father build", "build:watch": "father dev", "dev": "dumi dev", "docs:build": "dumi build", "doctor": "father doctor", "lint": "npm run lint:es && npm run lint:css", "lint:css": "stylelint \"{src,test}/**/*.{css,less}\"", "lint:es": "eslint \"{src,test}/**/*.{js,jsx,ts,tsx}\"", "prepare": "dumi setup", "prepublishOnly": "father doctor && npm run build", "start": "npm run dev"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "dependencies": {"@dnd-kit/core": "6.1.0", "@dnd-kit/modifiers": "7.0.0", "@dnd-kit/sortable": "8.0.0", "@lattebank/webadmin-http": "^0.2.19", "antd": "5.8.2", "classnames": "^2.5.1", "fetch-jsonp": "1.3.0", "nprogress": "^0.2.0", "path-to-regexp": "6.1.0", "qs": "6.11.2", "query-string": "6.9.0"}, "devDependencies": {"@commitlint/cli": "^17.1.2", "@commitlint/config-conventional": "^17.1.0", "@svgr/webpack": "^8.1.0", "@umijs/lint": "^4.0.0", "axios": "0.19.2", "babel-plugin-import": "1.13.8", "dumi": "^2.0.2", "eslint": "^8.23.0", "father": "^4.1.0", "husky": "^8.0.1", "lint-staged": "^13.0.3", "prettier": "^2.7.1", "prettier-plugin-organize-imports": "^3.0.0", "prettier-plugin-packagejson": "^2.2.18", "react": "18.2.0", "react-dom": "18.2.0", "stylelint": "^14.9.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "publishConfig": {"access": "public"}, "authors": []}