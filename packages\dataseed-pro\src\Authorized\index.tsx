import React from 'react';
import { DataSourceProps, query } from '../utils/request';

export interface IAuthorizedProps {
  hasAuthority: undefined | boolean;
  noMatch?: React.ReactNode;
  children?: React.ReactNode;
}

interface IAuthorizedSate {
  isDataLoaded: boolean,
  permitComponents: any[]
}

export interface IAuthorizedOption extends DataSourceProps {
  pathname?: string;
  systemCode?: string;
}

export const checkPermissions = (hasAuthority: IAuthorizedProps["hasAuthority"], childrenRender?: any, noMatch?: any) => {

  if (hasAuthority === undefined) {
    return null;
  }

  if (hasAuthority === true) {
    return childrenRender;
  }

  return noMatch;
};

const authorizedUrl = (options: IAuthorizedOption = {}) => {
  const urlArr = options.url && options.url.split('?') || [];
  const searchParams = urlArr[1] || '';
  if (searchParams) {
    try {
      options.pathname = new URLSearchParams(searchParams).get('pathname') || '';
    } catch (error) {
      options.pathname = ''
    }
  }
  options.url = '/idaasopr/myself/pageResource?url=' + encodeURIComponent(decodeURIComponent(options.pathname || '')) + `&systemCode=${options.systemCode || ''}`;
  return options;
};

const getRequestConfig = (option: IAuthorizedOption) => {
  let config = {} as Record<string, IAuthorizedOption[keyof IAuthorizedOption]>;

  const keys = Object.keys(option || {})?.filter(key => ['url', 'pathname', 'systemCode']?.indexOf(key) < 0)
  keys?.forEach(k => {
    const key = k as keyof IAuthorizedOption;
    const value = option[key] as IAuthorizedOption[keyof IAuthorizedOption];
    config[k] = value
  });
  return config;
}

export const getAuthComponent = async (option: IAuthorizedOption) => {
  const authOption = authorizedUrl(option);
  const config = getRequestConfig(authOption);
  if (Object.keys(config)?.length > 0) {
    return query(authOption.url, {}, config);
  }

  return query(authOption.url);
}

export default class Authorized extends React.Component<IAuthorizedProps, any> {

  static useAuthority = (option: IAuthorizedOption) => (WrappedComponent: any) => {
    let requestOption = typeof option === 'string' ? ({ pathname: option } as IAuthorizedOption) : option;
    requestOption = authorizedUrl(requestOption);

    return class extends React.Component<IAuthorizedProps, IAuthorizedSate> {

      static defaultProps = {
      };

      constructor(props: Readonly<IAuthorizedProps>) {
        super(props);

        this.state = {
          isDataLoaded: false,
          permitComponents: []
        };
      }

      componentDidMount() {
        this.loadData();
      }

      getBtnAuthCode = (btnData: any) => {
        let btnCode: any[] = []
        btnData?.forEach((item: any)=>{
          if(item.identification && !item.permission) {
            btnCode.push(item.identification)
          }
        })
        return btnCode
      }

      loadData = async () => {

        let config = {} as Record<string, IAuthorizedOption[keyof IAuthorizedOption]>;

        const keys = Object.keys(requestOption || {})?.filter(key => ['url', 'pathname', 'systemCode']?.indexOf(key) < 0)
        keys?.forEach(k => {
          const key = k as keyof IAuthorizedOption;
          const value = requestOption[key] as IAuthorizedOption[keyof IAuthorizedOption];
          config[k] = value
        })
        if (Object.keys(config)?.length > 0) {
          const data = await query(requestOption.url, {}, config);
          this.setState({
            permitComponents: this.getBtnAuthCode(data || []),
            isDataLoaded: true
          });
          return;
        }

        const data = await query(requestOption.url);
        this.setState({
          permitComponents: this.getBtnAuthCode(data || []),
          isDataLoaded: true
        });
      }

      /**
       * 准入权限检查：支持字符串、数组、函数
       * 返回true、false
       */
      checkAuthority = (component: string | Array<any> | Function) => {
        const { permitComponents, isDataLoaded } = this.state;

        // 当未正常返回鉴权数据时，均直接返回 false
        if (!isDataLoaded) {
          return false
        }

        if (typeof component === 'string') {
          return !(permitComponents ? permitComponents.some(c => c === component) : false);
        }

        if (component instanceof Array) {
          const intersect = new Set([...permitComponents].filter(x => new Set(component).has(x)));
          return intersect.size < 1;
        }

        if (typeof component === 'function') {
          return !component(permitComponents);
        }
      }

      render() {
        return <WrappedComponent checkAuthority={this.checkAuthority} permitComponents={this.state.permitComponents}  {...this.props} />;
      }
    };
  }

  render() {
    const { children, hasAuthority, noMatch = null } = this.props;
    const childrenRender = typeof children === 'undefined' ? null : children;
    return checkPermissions(hasAuthority, childrenRender, noMatch);
  }
}
