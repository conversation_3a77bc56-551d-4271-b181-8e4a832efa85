import {
  But<PERSON>,
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Modal,
  Radio,
  Row,
  Select,
  Spin,
  TreeSelect,
} from 'antd';
import debounce from 'lodash/debounce';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';
import dayjs from '../dayjs';
import {
  addDataAuthorization,
  applyCheck,
  applyDataAuthorization,
  authCheck,
  getDataResourceTypeDetail,
  getOrgs,
  getUsers,
} from '../service';

import {
  DEFAULT_SENSTIVE,
  EXPIRE_TIME_TYPE_MAP,
  MODAL_TYPE_MAP,
  SUBJECT_TYPE_MAP,
} from '../constant';
import {IAuthorizedFormProps, IDataResourceType, Option} from '../types';

const {TextArea} = Input;

const filterOperatingList = (
  dataResourceTypeCode: string,
  dataResourceTypeList: Array<IDataResourceType>,
) => {
  const item = dataResourceTypeList.find(
    (item: any) => item.dataResourceTypeCode === dataResourceTypeCode,
  );
  return item?.dataResourceOperatingList || [];
};

/**
 * 获取类型，组织、机构
 */
const getOrgType = (tree, targetId) => {
  for (const node of tree) {
    if (node.corporationIdOrOrgId === targetId) {
      return node.type;
    }
    if (node.children) {
      const result = getOrgType(node.children, targetId);
      if (result) {
        return result;
      }
    }
  }
  return null;
};

const AuthorizedForm: React.FC<IAuthorizedFormProps> = forwardRef(
  (
    {
      initialValues = {
        defaultUserItem: null,
        defaultOrgItem: null,
        subjectType: SUBJECT_TYPE_MAP.USER,
        expiredTimeType: EXPIRE_TIME_TYPE_MAP.FIXEDDATE,
        expiredTimeValue: null,
        expiredTime: null,
        dataResourceTypeCode: '',
        dataResourceOuterCode: '',
        dataResourceOuterName: '',
      },
      modalType = MODAL_TYPE_MAP.AUTHORIZED,
      dataResourceTypeList = [],
      sensitive = DEFAULT_SENSTIVE,
      customDataResouceType = {
        label: '资源类型',
        disabled: false,
      },
      customDataResouceName = {
        label: '资源名称',
      },
      customOperationType = {
        label: '操作类型',
      },
      customSubjectType = {
        disabled: false,
      },
      customUserValue = {
        customUserList: null,
        disabled: false,
      },
      customOrgValue = {
        customOrgList: null,
        disabled: false,
      },
      customExpiredTimeType = {
        disabled: false,
      },
      customExpiredTimeValue = {
        disabledDate: () => {},
        disabled: false,
      },
      pageRef,
      dataResourceOwner,
      customFilterOperatingList = (list: []) => list,
      fetchAuthorizedList = () => {},
      onFormInstanceReady,
      getOwner = () => {},
    },
    ref,
  ) => {
    const [form] = Form.useForm();
    const subjectType = Form.useWatch('subjectType', form);
    const expiredTimeType = Form.useWatch('expiredTimeType', form);
    const [operationDesc, setOperationDesc] = useState('');

    const initUserItem = initialValues.defaultUserItem
      ? {
          name: initialValues?.defaultUserItem.label,
          userId: initialValues?.defaultUserItem.value,
        }
      : {
          name: localStorage.getItem('userName') || '',
          userId: null,
        };
    const [userList, setUserList] = useState<
      Array<{
        name: string;
        userId: string | null;
      }>
    >(initialValues.defaultUserItem ? [initUserItem] : []);
    const [orgList, setOrgList] = useState<
      Array<{
        name: string;
        corporationIdOrOrgId: string;
      }>
    >(
      customOrgValue.customOrgList ||
        (initialValues.defaultOrgItem
          ? [
              {
                name: initialValues.defaultOrgItem.label,
                corporationIdOrOrgId: initialValues.defaultOrgItem.value,
              },
            ]
          : []),
    );
    const [operatingList, setOperatingList] = useState<Array<Option>>(
      customFilterOperatingList(
        filterOperatingList(
          initialValues.dataResourceTypeCode,
          dataResourceTypeList,
        ),
      ),
    );

    const _formInitialValues = {
      ...initialValues,
      expiredTimeValue: initialValues.expiredTime
        ? dayjs(initialValues.expiredTime, 'YYYY-MM-DD')
        : dayjs().add(2, 'year'),
      userValue: initialValues?.defaultUserItem?.value || null,
      orgValue: initialValues?.defaultOrgItem?.value || null,
    };

    const [formInitialValues, setFormInitialValues] =
      useState(_formInitialValues);
    const [fetching, setFetching] = useState(false);
    const [curUser, setCurUser] = useState(initUserItem);
    // 提交权限申请
    const doApplyAuthorized = ({callback = () => {}} = {}) => {
      form
        .validateFields()
        .then(values => {
          const {
            dataResourceTypeCode,
            description,
            expiredTimeValue,
            resourceOperatingMetaCode,
          } = values;
          const params = {
            dataResourceOuterName: formInitialValues.dataResourceOuterName,
            dataResourceOuterCode: formInitialValues.dataResourceOuterCode,
            dataResourceTypeCode,
            description,
            resourceOperatingMetaCode,
            expiredTime: expiredTimeValue?.format('YYYY-MM-DD 23:59:59'),
            sensitive,
          };
          handleApplyCheck(params, callback);
        })
        .catch(e => {
          console.log(e.message || '校验失败');
        });
    };

    const handleApplyCheck = (params, callback) => {
      applyCheck(params)
        .then(res => {
          const {data} = res || {};
          if (data.result) {
            submitApply(params, callback);
          } else {
            Modal.confirm({
              content: '您选择的权限比原有权限更小，确认继续？',
              cancelText: '取消',
              okText: '确定',
              onOk: () => {
                submitApply(params, callback);
              },
            });
          }
        })
        .catch(e => {
          message.error(e.message || '申请校验失败');
        });
    };

    const submitApply = (params, callback) => {
      applyDataAuthorization(params)
        .then(res => {
          if (res?.status !== 200) {
            message.error(res?.message || '申请权限失败');
            return;
          }
          message.success('申请成功, BPM流程中');
          callback();
        })
        .catch(err => {
          message.error(err?.message || '申请失败');
        });
    };

    useImperativeHandle(ref, () => {
      return {
        doApplyAuthorized,
      };
    });

    useEffect(() => {
      if (onFormInstanceReady) {
        onFormInstanceReady(form);
      }
    }, [formInitialValues]);

    const getUserList = (value?: string) => {
      setFetching(true);
      getUsers({
        pageNo: 1,
        pageSize: 50,
        name: value,
      })
        .then(res => {
          if (res.status !== 200) {
            message.error(res.message || '查询授权账号列表失败');
            return;
          }
          const resData = res?.data || {
            data: [],
          };
          const _item = resData?.data?.find((item: any) => {
            return item.name === curUser.name;
          });
          const _list = resData?.data?.map(userItem => {
            const {name, nickname} = userItem;
            const _nickname = nickname ? `(${nickname})` : '';
            return {
              ...userItem,
              name: name + _nickname,
            };
          });
          if (!_item) {
            // if (value === form.getFieldValue('userValue')) {
            //   _list.unshift({
            //     ...curUser,
            //     userId: curUser?.userId || form.getFieldValue('userValue')
            //   });
            // }
            setUserList(_list.concat());
            return;
          }
          form.setFieldsValue({
            userValue: _item?.userId,
          });
          setFormInitialValues({
            ...formInitialValues,
            userValue: _item?.userId,
          });
          setUserList(_list);
          setFetching(false);
        })
        .catch(err => {
          setFetching(false);
          message.error(err?.message || '查询授权账号列表失败');
        });
    };
    const getOrgList = () => {
      if (
        Array.isArray(customOrgValue.customOrgList) &&
        customOrgValue.customOrgList?.length
      ) {
        return;
      }
      getOrgs()
        .then(res => {
          if (res.status !== 200) {
            message.error(res.message || '查询授权组织列表失败');
            return;
          }
          const resData = res?.data || [];
          setOrgList(resData);
        })
        .catch(err => {
          message.error(err?.message || '查询授权组织列表失败');
        });
    };

    const fetchOperatingList = (value: string) => {
      getDataResourceTypeDetail(value)
        .then(res => {
          if (res.status !== 200) {
            message.error(res.message || '查询授权记录详情失败');
            return;
          }
          const resData = res?.data || {};
          const operatingList = resData?.dataResourceOperatingList || [];
          setOperatingList(customFilterOperatingList(operatingList));
          form.setFieldsValue({
            resourceOperatingMetaCode: operatingList[0]?.code || '',
          });
        })
        .catch(err => {
          message.error(err?.message || '查询授权记录详情失败');
        });
    };

    const onDataResourceChange = (value: string, option: any) => {
      if (!value) {
        return;
      }
      setFormInitialValues({
        ...formInitialValues,
        ...option,
      });
      form.setFieldsValue({
        // ...formInitialValues,
        ...option,
      });
      const operatingList = customFilterOperatingList(
        filterOperatingList(value, dataResourceTypeList),
      );
      if (!Array.isArray(operatingList) || operatingList.length === 0) {
        fetchOperatingList(value);
      } else {
        setOperatingList(operatingList);
        form.setFieldsValue({
          resourceOperatingMetaCode: operatingList[0]?.code || '',
        });
      }
    };
    useEffect(() => {
      if (subjectType === SUBJECT_TYPE_MAP.USER) {
        getUserList();
      } else if (subjectType === SUBJECT_TYPE_MAP.ORG) {
        getOrgList();
      }
    }, [subjectType]);

    const onSearchUser = debounce(getUserList, 600);

    const onAddAuthorized = () => {
      form
        .validateFields()
        .then(values => {
          const {
            dataResourceTypeCode,
            expiredTimeType,
            expiredTimeValue,
            resourceOperatingMetaCode,
            subjectType,
            userValue,
            orgValue,
          } = values;
          const params = {
            dataResourceOuterName: formInitialValues.dataResourceOuterName,
            dataResourceOuterCode: formInitialValues.dataResourceOuterCode,
            dataResourceTypeCode,
            subjectType:
              subjectType === SUBJECT_TYPE_MAP.USER
                ? SUBJECT_TYPE_MAP.USER
                : getOrgType(orgList, orgValue),
            subjectCode:
              subjectType === SUBJECT_TYPE_MAP.USER ? userValue : orgValue,
            resourceOperatingMetaCode,
            expiredTime:
              EXPIRE_TIME_TYPE_MAP.FIXEDDATE === expiredTimeType
                ? expiredTimeValue?.format('YYYY-MM-DD 23:59:59')
                : null,
          };
          handleAuthCheck(params);
        })
        .catch(e => {
          console.log(e.message || '表单校验不通过');
        });
    };

    const handleAuthCheck = params => {
      authCheck(params)
        .then(res => {
          const {data} = res || {};
          if (data.result) {
            addAuth(params);
          } else {
            Modal.confirm({
              content: '您选择的权限比原有权限更小，确认继续？',
              cancelText: '取消',
              okText: '确定',
              onOk: () => {
                addAuth(params);
              },
            });
          }
        })
        .catch(e => {
          message.error(e.message || '申请校验失败');
        });
    };

    const addAuth = params => {
      addDataAuthorization(params)
        .then(res => {
          if (res && res.status !== 200) {
            message.error(res?.message || '授权失败');
            return;
          }
          message.success('授权成功');
          fetchAuthorizedList();
          pageRef?.current?.setPaginationItem?.({
            pageNo: 1,
            pageSize: 10,
          });
          getOwner();
        })
        .catch(err => {
          message.error(err?.message || '授权失败');
        });
    };

    const onChangeOperationType = (value: string) => {
      setOperationDesc(
        operatingList.find((item: any) => value === item.code)?.description ||
          '',
      );
    };

    return (
      <>
        <Form
          form={form}
          name="authorized-form-in-modal"
          labelCol={{span: 4}}
          wrapperCol={{span: 20}}
          initialValues={formInitialValues}
        >
          <Form.Item
            label={customDataResouceType.label?.slice(0, 10)}
            name="dataResourceTypeCode"
            rules={[{required: true, message: '资源类型不能为空'}]}
          >
            <Select
              options={dataResourceTypeList}
              onChange={(value, option) => {
                onDataResourceChange(value, option);
              }}
              placeholder="请选择"
              fieldNames={{
                value: 'dataResourceTypeCode',
                label: 'dataResourceTypeName',
              }}
              disabled={customDataResouceType.disabled}
            />
          </Form.Item>
          <Form.Item
            label={customDataResouceName.label?.slice(0, 10)}
            name="dataResourceOuterName"
          >
            <div>{formInitialValues.dataResourceOuterName || '-'}</div>
          </Form.Item>
          <Form.Item label={'拥有者'} name="dataResourceOwner">
            <div>{dataResourceOwner || '-'}</div>
          </Form.Item>
          <Form.Item
            style={{marginBottom: operationDesc ? '10px' : '24px'}}
            label={customOperationType.label?.slice(0, 10)}
            name="resourceOperatingMetaCode"
            rules={[{required: true, message: '操作类型不能为空'}]}
          >
            <Select
              options={operatingList.map(item => ({
                ...item,
                value: item.code,
                label: item.alias || item.name,
              }))}
              placeholder="请选择"
              onChange={onChangeOperationType}
            />
          </Form.Item>
          <Row>
            <Col span={4}></Col>
            <Col span={20}>
              <div
                style={{
                  color: '#9ca3af',
                  marginBottom: operationDesc ? '24px' : 0,
                }}
              >
                {operationDesc}
              </div>
            </Col>
          </Row>
          {MODAL_TYPE_MAP.AUTHORIZED === modalType && (
            <Form.Item
              label="授权类型"
              name="subjectType"
              rules={[{required: true, message: '授权类型不能为空'}]}
            >
              <Radio.Group disabled={customSubjectType.disabled}>
                <Radio value={'USER'}>授权给账号</Radio>
                <Radio value={'ORG'}>授权给组织</Radio>
              </Radio.Group>
            </Form.Item>
          )}

          {MODAL_TYPE_MAP.AUTHORIZED === modalType &&
            subjectType === 'USER' && (
              <Form.Item
                label="授权给账号"
                name="userValue"
                rules={[{required: true, message: '授权账号不能为空'}]}
              >
                <Select
                  showSearch
                  filterOption={false}
                  placeholder="请选择"
                  fieldNames={{
                    label: 'name',
                    value: 'userId',
                  }}
                  notFoundContent={fetching ? <Spin size="small" /> : null}
                  options={customUserValue.customUserList || userList}
                  disabled={customUserValue.disabled}
                  onSearch={value => onSearchUser(value)}
                  onChange={(value, option: any) => {
                    setCurUser(option);
                  }}
                  getPopupContainer={triggerNode => triggerNode?.parentNode}
                />
              </Form.Item>
            )}
          {MODAL_TYPE_MAP.AUTHORIZED === modalType && subjectType === 'ORG' && (
            <Form.Item
              label="授权给组织"
              name="orgValue"
              rules={[{required: true, message: '授权组织不能为空'}]}
            >
              <TreeSelect
                treeData={orgList}
                placeholder="请选择"
                fieldNames={{
                  label: 'name',
                  value: 'corporationIdOrOrgId',
                }}
                treeNodeFilterProp="name"
                showSearch={true}
                disabled={customOrgValue.disabled}
                getPopupContainer={triggerNode => triggerNode?.parentNode}
              />
            </Form.Item>
          )}
          <Form.Item
            label="授权到期日"
            name="expiredTimeType"
            rules={[{required: true, message: '授权到期日不能为空'}]}
          >
            <Radio.Group disabled={customExpiredTimeType.disabled}>
              <Radio value={EXPIRE_TIME_TYPE_MAP.FIXEDDATE}>固定日期</Radio>
              <Radio value={EXPIRE_TIME_TYPE_MAP.PERMANENT}>永久</Radio>
            </Radio.Group>
          </Form.Item>
          {expiredTimeType === EXPIRE_TIME_TYPE_MAP.FIXEDDATE && (
            <Row>
              <Col offset={4} span={12}>
                <Form.Item
                  label={false}
                  name="expiredTimeValue"
                  rules={[{required: true, message: '授权到期日不能为空'}]}
                >
                  <DatePicker
                    onChange={date => {
                      form.setFieldsValue({
                        expiredTimeValue: date,
                      });
                    }}
                    defaultValue={formInitialValues.expiredTimeValue}
                    format={'YYYY-MM-DD'}
                    style={{width: '100%'}}
                    disabled={customExpiredTimeValue.disabled}
                    disabledDate={customExpiredTimeValue.disabledDate}
                  />
                </Form.Item>
              </Col>
            </Row>
          )}
          {MODAL_TYPE_MAP.APPLIED === modalType && (
            <Form.Item
              label="申请备注"
              name="description"
              rules={[{required: true, message: '申请备注不能为空'}]}
            >
              <TextArea placeholder="请输入" maxLength={100} showCount />
            </Form.Item>
          )}
          {MODAL_TYPE_MAP.AUTHORIZED === modalType && (
            <Form.Item
              wrapperCol={{offset: 20, span: 4}}
              style={{textAlign: 'right'}}
            >
              <Button type="primary" onClick={onAddAuthorized}>
                添加
              </Button>
            </Form.Item>
          )}
        </Form>
      </>
    );
  },
);

export default AuthorizedForm;
