import {
ExclamationCircleFilled,
QuestionCircleOutlined,
SearchOutlined
} from '@ant-design/icons';
import type { TableColumnsType } from 'antd';
import {
Button,
Col,
DatePicker,
Form,
Input,
message,
Modal,
Radio,
Row,
Table,
Tooltip
} from 'antd';

import React,{ useState } from 'react';
import { EXPIRE_TIME_TYPE_MAP,SUBJECT_TYPE_NAME_MAP } from '../constant';
import dayjs from '../dayjs';
import { removeDataAuthorization,updateDataAuthorization } from '../service';

const { confirm } = Modal;

const AuthorizedTable: React.FC<any> = (props) => {
  const {
    authorizedLogList = [],
    fetchAuthorizedList = () => {},
    pageRef,
    total = 0,
    customTableHeader = {
      dataResouceTypeLabel: '资源类型',
      operationTypeLabel: '授权操作',
    },
  } = props;
  const [updateForm] = Form.useForm();
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [curRecord, setCurRecord] = useState<any>({});
  const [paginationItem, setPaginationItem] = useState({
    pageNo: 1,
    pageSize: 10,
  });
  const expiredTimeType = Form.useWatch('expiredTimeType', updateForm);
  pageRef.current.setPaginationItem = setPaginationItem;

  const [searchText, setSearchText] = useState('');

  const onRemoveClick = (record) => {
    confirm({
      title: '确认移除',
      icon: null,
      content: (
        <div style={{ marginBottom: '0px' }}>
          <span style={{ color: '#faad14', marginRight: '6px' }}>
            <ExclamationCircleFilled />
          </span>
          <span>确认移除这个授权吗？</span>
        </div>
      ),
      okText: '确认',
      cancelText: '取消',
      className: 'biz-authorized',
      onOk() {
        const { dataAuthorizationId } = record;
        removeDataAuthorization(dataAuthorizationId)
          .then((res) => {
            if (res && res.status !== 200) {
              message.error(res.message || '移除失败');
              return;
            }
            message.info('移除成功');
            fetchAuthorizedList(1, paginationItem.pageSize);
            setPaginationItem({
              pageNo: 1,
              pageSize: paginationItem.pageSize,
            });
          })
          .catch((err) => {
            message.error(err?.message || '移除失败');
          });
      },
      onCancel() {},
    });
  };

  const onDoUpdateLog = () => {
    updateForm
      .validateFields()
      .then((values) => {
        const { expiredTimeValue, expiredTimeType } = values;
        updateDataAuthorization(curRecord.dataAuthorizationId, {
          expiredTime:
            EXPIRE_TIME_TYPE_MAP.FIXEDDATE === expiredTimeType
              ? expiredTimeValue?.format('YYYY-MM-DD 23:59:59')
              : null,
        })
          .then((res) => {
            if (res && res.status !== 200) {
              message.error(res.message || '更新失败');
              return;
            }
            message.info('更新成功');
            setShowUpdateModal(false);
            fetchAuthorizedList();
            setPaginationItem({
              pageNo: 1,
              pageSize: 10,
            });
          })
          .catch((err) => {
            message.error(err?.message || '更新失败');
          });
      })
      .catch((e) => {
        console.log(e.message || '校验失败');
      });
  };

  /**
   * 搜索
   */
  const handleSearch = (selectedKeys, confirm) => {
    confirm();
    setSearchText(selectedKeys[0]);
  };

  /**
   * 重置
   */
  const handleReset = (clearFilters, closePop, confirm) => {
    clearFilters();
    setSearchText('');
    confirm(false);
    closePop();
  };

  /**
   * 表格单列搜索功能
   */
  const getColumnSearchProps = (dataIndex) => ({
    filterDropdown: ({
      setSelectedKeys,
      selectedKeys,
      confirm,
      clearFilters,
      close,
    }) => (
      <div style={{ padding: 8 }}>
        <Input
          placeholder="请输入"
          value={selectedKeys[0]}
          onChange={(e) =>
            setSelectedKeys(e.target.value ? [e.target.value] : [])
          }
          onPressEnter={() => handleSearch(selectedKeys, confirm)}
          style={{ marginBottom: 8, display: 'block' }}
        />
        <div style={{ textAlign: 'right' }}>
          <Button
            onClick={() => handleReset(clearFilters, close, confirm)}
            size="small"
            style={{ width: 50 }}
          >
            重置
          </Button>
          <Button
            onClick={() => close()}
            size="small"
            style={{ width: 50, marginLeft: '10px' }}
          >
            取消
          </Button>
          <Button
            type="primary"
            onClick={() => handleSearch(selectedKeys, confirm)}
            size="small"
            style={{ width: 50, marginLeft: '10px' }}
          >
            搜索
          </Button>
        </div>
      </div>
    ),
    filterIcon: (filtered) => (
      <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
    ),
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes(value.toLowerCase())
        : '',
  });

  const onUpdateClick = (record) => {
    setShowUpdateModal(true);
    const _expiredTimeType = record.expiredTime
      ? EXPIRE_TIME_TYPE_MAP.FIXEDDATE
      : EXPIRE_TIME_TYPE_MAP.PERMANENT;
    const _expiredTimeValue = record.expiredTime
      ? dayjs(record.expiredTime, 'YYYY-MM-DD')
      : dayjs().add(2, 'year');
    setCurRecord({
      ...record,
      expiredTimeType: _expiredTimeType,
      expiredTimeValue: _expiredTimeValue,
    });
    updateForm.setFieldsValue({
      expiredTimeType: _expiredTimeType,
      expiredTimeValue: _expiredTimeValue,
    });
  };
  const columns: TableColumnsType<any> = [
    {
      title: '序号',
      dataIndex: 'productName',
      key: 'productName',
      width: '60px',
      render: (text: any, record: any, index: number) => {
        return record?.index || index + 1;
      },
    },
    {
      title: customTableHeader.dataResouceTypeLabel?.slice(0, 10),
      dataIndex: 'resourceTypeMetaName',
      key: 'resourceTypeMetaName',
      width: '100px',
    },
    {
      title: '资源名称',
      dataIndex: 'resourceOuterName',
      key: 'resourceOuterName',
      width: '150px',
    },
    {
      title: '授权类型',
      dataIndex: 'subjectType',
      key: 'subjectType',
      width: '120px',
      render(value) {
        return <span>{SUBJECT_TYPE_NAME_MAP[value] || '-'}</span>;
      },
    },
    {
      title: '授权对象',
      dataIndex: 'subjectName',
      key: 'subjectName',
      width: '120px',
      ...getColumnSearchProps('subjectName'),
    },
    {
      title: '授权到期日',
      dataIndex: 'expiredTime',
      key: 'expiredTime',
      width: '120px',
      render(value) {
        return <span>{value || '-'}</span>;
      },
    },
    {
      title: customTableHeader.operationTypeLabel?.slice(0, 10),
      dataIndex: 'resourceOperatingMetaName',
      key: 'resourceOperatingMetaName',
      width: '120px',
      render(text, record) {
        return (
          <div>
            {record?.resOprMetaAlias || text}
            {record?.resOprMetaDescription ? (
              <Tooltip title={record?.resOprMetaDescription}>
                <QuestionCircleOutlined style={{ marginLeft: '10px' }} />
              </Tooltip>
            ) : (
              ''
            )}
          </div>
        );
      },
    },
    {
      title: '授权时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: '120px',
    },
    {
      title: '操作',
      key: 'operator',
      width: '200px',
      fixed: 'right',
      render: (text: any, record: any) => {
        return (
          <>
            <Button
              style={{ marginRight: '10px' }}
              type="link"
              onClick={() => {
                onRemoveClick(record);
              }}
            >
              移除
            </Button>
            <Button
              type="link"
              onClick={() => {
                onUpdateClick(record);
              }}
            >
              更改授权到期日
            </Button>
          </>
        );
      },
    },
  ];
  return (
    <>
      <Table
        columns={columns}
        scroll={{ x: 1200 }}
        dataSource={authorizedLogList}
        // loading={tableLoading}
        pagination={{
          position: ['bottomRight'],
          showSizeChanger: true,
          showTotal: (total) => `共 ${total} 条`,
          current: paginationItem.pageNo,
          pageSize: paginationItem.pageSize,
          total,
          onChange: (pageNo, pageSize) => {
            setPaginationItem({
              pageNo,
              pageSize,
            });
            fetchAuthorizedList(pageNo, pageSize);
          },
        }}
      />
      {showUpdateModal && (
        <Modal
          open={showUpdateModal}
          title="更改授权到期日"
          okText="确定"
          cancelText="取消"
          className="biz-authorized"
          onOk={onDoUpdateLog}
          onCancel={() => {
            setShowUpdateModal(false);
          }}
        >
          <Form
            form={updateForm}
            name="authorized-form-in-modal"
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
          >
            <Form.Item
              label="授权到期日"
              name="expiredTimeType"
              rules={[{ required: true, message: '授权到期日不能为空' }]}
            >
              <Radio.Group>
                <Radio value={EXPIRE_TIME_TYPE_MAP.FIXEDDATE}>固定日期</Radio>
                <Radio value={EXPIRE_TIME_TYPE_MAP.PERMANENT}>永久</Radio>
              </Radio.Group>
            </Form.Item>
            {expiredTimeType === EXPIRE_TIME_TYPE_MAP.FIXEDDATE && (
              <Form.Item
                label={false}
                name="expiredTimeValue"
                rules={[{ required: true, message: '授权到期日不能为空' }]}
              >
                <Row>
                  <Col offset={8}>
                    <DatePicker
                      onChange={(date) => {
                        updateForm.setFieldsValue({
                          expiredTimeValue: date,
                        });
                      }}
                      defaultValue={curRecord.expiredTimeValue}
                      format={'YYYY-MM-DD'}
                      style={{ width: '100%' }}
                    />
                  </Col>
                </Row>
              </Form.Item>
            )}
          </Form>
        </Modal>
      )}
    </>
  );
};

export default AuthorizedTable;
