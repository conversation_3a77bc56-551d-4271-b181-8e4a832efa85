import { Button,ConfigProvider,message,Modal } from 'antd';
import zhCN from 'antd/es/locale/zh_CN';
import React,{ useEffect,useRef,useState } from 'react';
import AuthorizedForm from './components/authorizedForm';
import AuthorizedTable from './components/authorizedTable';
import { MODAL_TYPE_MAP } from './constant';
import { getDataAuthorization} from './service'



const DataResourceAuthorizedV2: React.FC<any> = props => {
  const {
    title,
    visible = false,
    initialValues,
    modalType = '',
    dataResourceTypeList = [],
    customLocale = zhCN,
    onClose = () => {},
    onSubmit = () => {},
    dispatchEvent = () => {},
  } = props;

  const [authorizedLogList, setAuthorizedLogList] = useState<any>([]);
  const authorizedFormRef = useRef<IAuthorizedFormRef>();
  const [total, setTotal] = useState(0);
  const pageRef = useRef({});

  // 校验初始化数据格式
  const vallidInitData = () => {};

  const fetchAuthorizedList = (pageNo = 1, pageSize = 10) => {
    getDataAuthorization({
      typeAndResource: dataResourceTypeList.map(item => {
        const {dataResourceTypeCode, dataResourceOuterCode} = item;

        return {
          dataResourceTypeCode,
          dataResourceOuterCode,
        };
      }),
      pageNo,
      pageSize,
    })
      .then(res => {
        if (res && res.status !== 200) {
          message.error(res.message || '查询授权记录失败');
          return;
        }
        const {data} = res;
        setTotal(data?.total || 0);
        setAuthorizedLogList(data?.data || []);
      })
      .catch(err => {
        message.error(err?.message || '查询授权记录失败');
      });
  };

  useEffect(() => {
    fetchAuthorizedList();
  }, [dataResourceTypeList]);

  useEffect(() => {
    vallidInitData();
  }, []);

  const afterSubmit = () => {
    try {
      onSubmit();
      dispatchEvent('onSubmit', {});
    } catch (error) {
      console.log(error)
    }
  }

  const afterClose = () => {
    try {
      onClose();
      dispatchEvent('onClose', {});
    } catch (error) {
      console.log(error)
    }
  }

  const onOk = async () => {
    try {
      await authorizedFormRef.current?.doApplyAuthorized({
        callback: () => {
          afterSubmit();
          afterClose();
        },
      });
    } catch (error) {}
  };

  const onCancel = () => {
    afterClose();
  };

  return (
    <ConfigProvider locale={customLocale}>
      <Modal
        title={
          title ||
          (modalType === MODAL_TYPE_MAP.AUTHORIZED ? '授权管理' : '权限申请')
        }
        open={visible}
        okButtonProps={{autoFocus: true}}
        destroyOnClose
        closable={false}
        onCancel={onCancel}
        width={960}
        footer={
          modalType === MODAL_TYPE_MAP.APPLIED ? (
            <>
              <Button type="default" onClick={onCancel}>
                取消
              </Button>
              <Button type="primary" onClick={onOk}>
                提交
              </Button>
            </>
          ) : null
        }
        className="biz-authorized"
      >
        <AuthorizedForm
          {...props}
          ref={authorizedFormRef}
          initialValues={initialValues}
          dataResourceTypeList={dataResourceTypeList}
          modalType={modalType}
          pageRef={pageRef}
          fetchAuthorizedList={fetchAuthorizedList}
        />
        {modalType === MODAL_TYPE_MAP.AUTHORIZED && (
          <AuthorizedTable
            {...props}
            authorizedLogList={authorizedLogList}
            total={total}
            pageRef={pageRef}
            fetchAuthorizedList={fetchAuthorizedList}
          />
        )}
      </Modal>
    </ConfigProvider>
  );
};

export default DataResourceAuthorizedV2;
