import $http from '@lattebank/webadmin-http';

/**
 * 设置为 true，方便调试，避免 401 跳转
 * 提交前一定要置为 false
 */
const CANCEL_401 = false;

/**
 * 新增 FROM_DATASEED_PRO 字段标识接口来自组件库
 * 业务应用和组件库如果是同一版本的 http 库则会共用 $http 实例，两者逻辑影响时业务应用可根据该字段做判断
 */

// 查询授权账号
export const getUsers = (data) => $http.get('/idaas/v2/users', data, { FROM_DATASEED_PRO: true, CANCEL_401 });
// 查询授权组织
export const getOrgs = () => $http.get('/idaas/orgs/tree', {}, { FROM_DATASEED_PRO: true, CANCEL_401 });
// 数据权限授权记录查询
export const getDataAuthorization = (params) => $http.post('/idaasopr/data-authorization/opt/query', params, { FROM_DATASEED_PRO: true, CANCEL_401 });
// 查询数据资源类型详情
export const getDataResourceTypeDetail = (dataResourceTypeCode: string) => $http.get(`/idaasopr/data-resource-type/${dataResourceTypeCode}`, {}, { FROM_DATASEED_PRO: true, CANCEL_401 })
// 新增授权记录
export const addDataAuthorization = (params) => $http.post('/idaasopr/data-authorization', params, { FROM_DATASEED_PRO: true, CANCEL_401 });
// 申请数据权限
export const applyDataAuthorization = (params) => $http.post('/idaasopr/data-authorization/opt/apply', params, { FROM_DATASEED_PRO: true, CANCEL_401 });
// 修改数据授权记录
export const updateDataAuthorization = (dataAuthorizationId, params) => $http.put(`/idaasopr/data-authorization/${dataAuthorizationId}`, params, { FROM_DATASEED_PRO: true, CANCEL_401 });
//  移除数据授权记录
export const removeDataAuthorization = (dataAuthorizationId) => $http.delete(`/idaasopr/data-authorization/${dataAuthorizationId}`, {}, { FROM_DATASEED_PRO: true, CANCEL_401 });

// 申请校验
export const applyCheck = (params) => $http.post(`/idaasopr/data-authorization/opt/apply/check`, params, { FROM_DATASEED_PRO: true, CANCEL_401 });

// 授权校验
export const authCheck = (params) => $http.post(`/idaasopr/data-authorization/check`, params, { FROM_DATASEED_PRO: true, CANCEL_401 });