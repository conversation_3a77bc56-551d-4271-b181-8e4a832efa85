import type { FormInstance } from 'antd';

export interface Option {
  code: string;
  name: string;
  label: string;
  value: string;
  description: string;
}

export interface IAuthorizedFormValue {
  dataResourceTypeCode: string;
  subjectType?: string;
  resourceOperatingMetaCode?: string;
  dataResourceOuterCode?: string;
  dataResourceOuterName?: string;
  expiredTime?: string;
  expiredTimeType?: string;
  defaultUserItem?: Option;
  defaultOrgItem?: Option;
  dataResourceOwner?: string;
}

export interface IDataResourceType {
  dataResourceTypeCode: string;
  dataResourceTypeName: string;
  dataResourceOuterCode: string;
  dataResourceOuterName: string;
  dataResourceOperatingList: Array<{
    code: string;
    name: string;
  }>;
}

interface IOrg {
  name: string;
  corporationIdOrOrgId: string;
  children: Array<IOrg>;
}

export interface IAuthorizedFormProps {
  initialValues: IAuthorizedFormValue;
  dataResourceTypeList: Array<IDataResourceType>;
  orgOptions: Array<IOrg>;
  sensitive?: string;
  modalType: string;
  customDataResouceType: {
    label: string;
    disabled: boolean;
  };
  customDataResouceName: {
    label: string;
  };
  customOperationType: {
    label: string;
  };
  customSubjectType: {
    disabled: boolean;
  };
  customUserValue: {
    customUserList: null | [];
    disabled: boolean;
  };
  customOrgValue: {
    customOrgList: null | [];
    disabled: boolean;
  };
  customExpiredTimeType: {
    disabled: boolean;
  };
  customExpiredTimeValue: {
    disabledDate: () => {};
    disabled: boolean;
  };
  customTableHeader: {
    dataResouceTypeLabel: string;
    operationTypeLabel: string;
  };
  customTableCell: {
    operationTypeMap: null;
  };
  pageRef: any;
  onSubmit: () => void;
  onClose: () => void;
  customFilterOperatingList: () => [];
  fetchAuthorizedList: () => void;
  onFormInstanceReady?: (instance: FormInstance<IAuthorizedFormValue>) => void;
}

export interface IAuthorizedFormRef {
  doApplyAuthorized: (any) => void;
}
