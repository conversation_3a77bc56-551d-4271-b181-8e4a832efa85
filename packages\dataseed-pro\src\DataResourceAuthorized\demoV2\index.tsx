import {DataResourceAuthorized} from '@dataseed/pro-components';
import {Button, Table} from 'antd';
import React, {useState} from 'react';
import {MODAL_TYPE_MAP} from '../constant';

const dataResourceTypeList = [
  {
    dataResourceTypeCode: 'drt_user',
    dataResourceTypeName: '账号',
    dataResourceOuterCode: '4e01471d-9317-4743-94c1-2cb9c945c0b4',
    dataResourceOuterName: '<EMAIL>',
    dataResourceOperatingList: [
      {
        code: 'dot_owner',
        name: '拥有者',
        key: 0,
      },
      {
        code: 'dot_readwrite',
        name: '读写',
        key: 1,
      },
      {
        code: 'dot_readonly',
        name: '只读',
        key: 2,
      },
      {
        code: 'dot_visitor',
        name: '访客',
        key: 3,
      },
    ],
  },
];

const dataSource = [
  {
    corporationOrOrgList: [
      {
        fillPathName: '数禾科技 / 路杰的测试组_103 / child2',
        id: '64f7887e-958a-430a-99ed-09167cc6a563',
        name: 'child2',
        orgType: 'VIRTUAL_GROUP',
        queryChildOrg: false,
        source: 'CUSTOM',
        type: 'ORG',
      },
    ],
    createdAt: '2025-01-21T16:14:51',
    createdBy: '郭启缘',
    dataResourceTypeCode: 'drt_user',
    email: '<EMAIL>',
    memberId: 'cc75efc0-117b-4ee2-b50c-639ee2a9c5e0',
    name: 'dasfdsa',
    nickname: '',
    phone: '135****1821',
    resourceOperatingMetaCode: 'dot_owner',
    status: 'NORMAL',
    statusName: '已启用',
    type: 'CUSTOM',
    typeName: '客制',
    updatedAt: '2025-01-21T16:14:51',
    updatedBy: '郭启缘',
    userId: '4e01471d-9317-4743-94c1-2cb9c945c0b4',
    userName: '13556771821',
  },
  {
    corporationOrOrgList: [
      {
        fillPathName: '数禾科技 / 路杰的测试组_103',
        id: 'b0eb5daf-4f2e-4b47-b2cf-2232e5daf9fc',
        name: '路杰的测试组_103',
        orgType: 'VIRTUAL_GROUP',
        queryChildOrg: false,
        source: 'CUSTOM',
        type: 'ORG',
      },
    ],
    createdAt: '2024-10-30T13:45:20',
    createdBy: '郭启缘',
    dataResourceTypeCode: 'drt_user',
    email: '<EMAIL>',
    memberId: '16c4b6f4-ab53-4bf2-8f41-840673c8ece5',
    name: 'dsafds',
    nickname: '',
    phone: '135****8347',
    resourceOperatingMetaCode: 'dot_owner',
    status: 'NORMAL',
    statusName: '已启用',
    type: 'CUSTOM',
    typeName: '客制',
    updatedAt: '2024-10-30T20:28:49',
    updatedBy: '郭启缘',
    userId: '7b530a11-184e-46ac-b5ca-93a1ab767a4d',
    userName: '13564518347',
  },
  {
    corporationOrOrgList: [],
    createdAt: '2024-08-23T09:59:01',
    createdBy: '罗肖怡',
    dataResourceTypeCode: 'drt_user',
    email: '<EMAIL>',
    memberId: '965df2d5-8fac-41a9-a22d-fc0a9cdd3d41',
    name: 'lxy测试1',
    nickname: '测试昵称1',
    phone: '180****1801',
    resourceOperatingMetaCode: 'dot_readwrite',
    status: 'NORMAL',
    statusName: '已启用',
    type: 'CUSTOM',
    typeName: '客制',
    updatedAt: '2024-09-02T12:23:45',
    updatedBy: '郭启缘',
    userId: 'bc128873-60b8-4851-ba4c-0aec94e13d77',
    userName: '18018011801',
  },
  {
    corporationOrOrgList: [
      {
        fillPathName: '测试机构',
        id: '08f68160-cacf-4696-a104-eb86b2e1fc08',
        name: '测试机构',
        queryChildOrg: false,
        source: 'CUSTOM',
        type: 'CORPORATION',
      },
    ],
    createdAt: '2024-08-22T18:08:36',
    createdBy: '罗肖怡',
    dataResourceTypeCode: 'drt_user',
    email: '<EMAIL>',
    memberId: 'a1d31a5f-7de9-4057-9b1c-59c6187507af',
    name: 'lxy测试',
    nickname: '测试的昵称1',
    phone: '180****1234',
    resourceOperatingMetaCode: 'dot_owner',
    status: 'NORMAL',
    statusName: '已启用',
    type: 'CUSTOM',
    typeName: '客制',
    updatedAt: '2024-12-02T17:29:49',
    updatedBy: '单平',
    userId: '0805f82d-d4f5-4baf-bcae-0f33d92aaa7e',
    userName: '18091231234',
  },
];

const orgOptions = [
  {
    children: [
      {
        children: [
          {
            corporationIdOrOrgId: '40edffcc-5788-4489-bfd7-87666a6bc571',
            createdAt: '2023-12-06T14:49:42',
            createdBy: '刘梅',
            fillPathId:
              'ddec0255-c402-4e5d-a4ae-9175b5b55989,a0b6920b-ad73-4378-9caa-37a8f108bf40,40edffcc-5788-4489-bfd7-87666a6bc571',
            fillPathName: '自定义机构abcd测试 / dddd / 自定义测试111',
            name: '自定义测试111',
            order: 0,
            orgType: 'VIRTUAL_GROUP',
            orgTypeName: '虚拟组',
            parentId: 'a0b6920b-ad73-4378-9caa-37a8f108bf40',
            type: 'ORG',
            updatedAt: '2023-12-08T14:46:09',
            updatedBy: '刘梅',
          },
        ],
        corporationIdOrOrgId: 'a0b6920b-ad73-4378-9caa-37a8f108bf40',
        createdAt: '2023-12-06T14:49:42',
        createdBy: '刘梅',
        fillPathId:
          'ddec0255-c402-4e5d-a4ae-9175b5b55989,a0b6920b-ad73-4378-9caa-37a8f108bf40',
        fillPathName: '自定义机构abcd测试 / dddd',
        name: 'dddd',
        order: 0,
        orgType: 'VIRTUAL_GROUP',
        orgTypeName: '虚拟组',
        parentId: 'ddec0255-c402-4e5d-a4ae-9175b5b55989',
        type: 'ORG',
        updatedAt: '2023-12-08T14:46:09',
        updatedBy: '刘梅',
      },
    ],
    corporationIdOrOrgId: 'ddec0255-c402-4e5d-a4ae-9175b5b55989',
    createdAt: '2023-12-06T14:49:42',
    createdBy: '刘梅',
    fillPathId: 'ddec0255-c402-4e5d-a4ae-9175b5b55989',
    fillPathName: '自定义机构abcd测试',
    name: '自定义机构abcd测试',
    order: 0,
    parentId: 'root',
    type: 'CORPORATION',
    updatedAt: '2023-12-08T14:46:09',
    updatedBy: '刘梅',
  },
];

const UserAuthorizedDemoV2: React.FC<any> = () => {
  const [showAuthorized, setShowAuthorized] = useState(false);
  const [initialValues, setInitialValues] = useState({});
  const [modalType, setModalType] = useState(MODAL_TYPE_MAP.AUTHORIZED);

  const onAuthorized = (record: any) => {
    const {userId, name, dataResourceTypeCode} = record;
    setModalType(MODAL_TYPE_MAP.AUTHORIZED);
    setInitialValues({
      dataResourceTypeCode,
      dataResourceOuterCode: userId,
      dataResourceOuterName: name,
      resourceOperatingMetaCode: 'dot_owner',
      subjectType: 'USER',
      dataResourceOwner: 'gqy',
      defaultOrgItem: {
        label: '技术中心',
        value: 'eb8b2622-0907-44e2-b243-4201e17ebda2',
      },
      expiredTimeType: 'fixedDate',
      // expiredTime: '2028-05-30'
    });
    setShowAuthorized(true);
  };

  const onApplied = (record: any) => {
    const {userId, email, dataResourceTypeCode} = record;
    setInitialValues({
      dataResourceTypeCode,
      dataResourceOuterCode: userId,
      dataResourceOuterName: email,
      resourceOperatingMetaCode: 'dot_owner',
      subjectType: 'USER',
      expiredTimeType: 'fixedDate',
      // expiredTime: '2028-05-30'
    });
    setModalType(MODAL_TYPE_MAP.APPLIED);
    setShowAuthorized(true);
  };

  const onClose = (flag = false) => {
    setShowAuthorized(flag);
  };
  const onSubmit = () => {};

  const columns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '操作',
      dataIndex: 'operator',
      key: 'operator',
      render: (text: any, record: any) => {
        return (
          <>
            <Button
              style={{marginRight: '10px'}}
              type="link"
              onClick={() => {
                onAuthorized(record);
              }}
            >
              授权管理
            </Button>
            <Button
              type="link"
              onClick={() => {
                onApplied(record);
              }}
            >
              权限申请
            </Button>
          </>
        );
      },
    },
  ];

  return (
    <>
      <div className="demoTitle">数据资源列表V2</div>
      <Table dataSource={dataSource} columns={columns} />
      {showAuthorized && (
        <DataResourceAuthorized
          componentVersion="V2"
          visible={showAuthorized}
          initialValues={initialValues}
          modalType={modalType}
          dataResourceTypeList={dataResourceTypeList}
          customDataResouceType={{label: '资源类型自定义'}}
          customDataResouceName={{label: '资源名称自定义'}}
          // customSubjectType={{disabled: true}}
          customOrgValue={{
            customOrgList: orgOptions,
            // disabled: true,
          }}
          customOperationType={{
            label: '操作类型自定义123456789',
          }}
          customExpiredTimeType={{disabled: true}}
          customExpiredTimeValue={{disabledDate: () => {}, disabled: true}}
          customTableHeader={{
            dataResouceTypeLabel: '资源类型自定义',
            operationTypeLabel: '授权操作自定义',
          }}
          customFilterOperatingList={(list: []) => list}
          onClose={onClose}
          onSubmit={onSubmit}
        />
      )}
    </>
  );
};

export default UserAuthorizedDemoV2;
