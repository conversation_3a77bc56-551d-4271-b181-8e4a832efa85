---
category: Components
subtitle: 授权
group: 数据录入
title: 数据资源权限
cover: https://mdn.alipayobjects.com/huamei_7uahnr/afts/img/A*zo76T7KQx2UAAAAAAAAAAAAADrJ8AQ/original
coverDark: https://mdn.alipayobjects.com/huamei_7uahnr/afts/img/A*5oPiTqPxGAUAAAAAAAAAAAAADrJ8AQ/original
demo:
  cols: 2
---

数据资源权限。

## 何时使用

- 下游业务系统，对接身份管理平台，进行数据资源授权和申请时使用

## 代码演示

### 初始版本（已废弃）
<!-- <code src="./demo/index.tsx">基本使用</code> -->

### V2版本
<code src="./demoV2/index.tsx">基本使用</code>


## /idaas/orgs/tree和/idaas/v2/users接口404，怎么处理？

`/idaas/orgs/tree`和`/idaas/v2/users`接口无需opr转发，可参考如下配置代理：

```javascript
 proxy: {
    '/idaas': {
        target: 'http://moka.dmz.dev.caijj.net',
        changeOrigin: true,
    }
 },
```
