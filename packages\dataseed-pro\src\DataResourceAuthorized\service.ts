import $http from '@lattebank/webadmin-http';

// 查询授权账号
export const getUsers = (data) => $http.get('/idaas/v2/users', data);
// 查询授权组织
export const getOrgs = () => $http.get('/idaas/orgs/tree');
// 数据权限授权记录查询
export const getDataAuthorization = (params) => $http.post('/idaasopr/data-authorization/opt/query', params);
// 查询数据资源类型详情
export const getDataResourceTypeDetail = (dataResourceTypeCode: string) => $http.get(`/idaasopr/data-resource-type/${dataResourceTypeCode}`)
// 新增授权记录
export const addDataAuthorization = (params) => $http.post('/idaasopr/data-authorization', params);
// 申请数据权限
export const applyDataAuthorization = (params) => $http.post('/idaasopr/data-authorization/opt/apply', params);
// 修改数据授权记录
export const updateDataAuthorization = (dataAuthorizationId, params) => $http.put(`/idaasopr/data-authorization/${dataAuthorizationId}`, params);
//  移除数据授权记录
export const removeDataAuthorization = (dataAuthorizationId) => $http.delete(`/idaasopr/data-authorization/${dataAuthorizationId}`);
