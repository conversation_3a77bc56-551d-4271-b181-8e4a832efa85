/* eslint-disable react/jsx-filename-extension */
/* eslint-disable rno-noninteractive-element-interactions */
import classnames from 'classnames';
import React,{ useEffect,useState } from 'react';
import TenantChoose from '../tenant-choose';
import { fetchTenantList } from '../tenant-choose/components/tenantList';
import './index.less';



const Mine = props => {
  const {userInfo, isOulei, isShuhe, mokaHost, onShuheLogout, onLogout} = props;
  const [showList, setShowList] = useState(false);
  const [mineWrapperHover, setMineWrapperHover] = useState(false);
  const [tenantList, setTenantList] = useState([]);

  const logout = () => {
    mokaHost ? onShuheLogout() : onLogout();
  };

  useEffect(() => {
    fetchTenantList().then(res => {
      setTenantList(res);
    });
  }, []);

  return (
    <>
      <div
        className={classnames({
          'mine-wrap': true,
          'mine-wrap-hover': mineWrapperHover,
        })}
        onClick={() => {
          setShowList(true);
          setMineWrapperHover(true);
        }}
      >
        <div
          className={classnames({
            'name': true,
            'name-hover': mineWrapperHover,
          })}
        >
          {(userInfo as t.User)?.label}
        </div>
        <div className="more-icon"></div>
      </div>
      {showList && (
        <>
          <div className="mine-list">
            <div className="list-wrap">
              {isShuhe && (
                <>
                  {/* <div
                    className="list-item"
                    onClick={() => {
                      window.location.href = '/infraui/#/personal-center';
                      setShowList(false);
                      setMineWrapperHover(false);
                    }}
                  >
                    我的信息
                  </div> */}
                  <div
                    className="list-item"
                    onClick={() => {
                      window.location.href = '/idaasui/#/outer/myRoles';
                      setShowList(false);
                      setMineWrapperHover(false);
                    }}
                  >
                    我的权限
                  </div>
                </>
              )}
              <div className="list-item change-tenant">
                {!isOulei && (
                  <TenantChoose isShuhe={isShuhe} tenantList={tenantList} />
                )}
              </div>

              <div className="list-item logout" onClick={logout}>
                退出登录
              </div>
            </div>
          </div>
          <div
            className="mine-first-mask"
            onClick={() => {
              setShowList(false);
              setMineWrapperHover(false);
            }}
          />
        </>
      )}
    </>
  );
};

export default Mine
