/* eslint-disable react/jsx-filename-extension */
/* eslint-disable rno-noninteractive-element-interactions */
import React, { Component } from 'react';

import * as t from '../../types';

interface ISecondMenuProps {
  sortable?: boolean;
  seacrchValue: string;
  listData: any;
  topMenu?: t.Menu;
  secondMenu?: t.Menu;
  threeMenu?: t.Menu;
  level4Menu?: t.Menu;
  openKeys: String[];
  maxAllowCount: number;
  setMenuId: (m: t.Menu, isJump?: Boolean, isInit?: boolean) => void;
  onOpenChange: (menuItem: any) => void;
}
class SecondMenu extends Component<ISecondMenuProps> {
  getMenuData = (menus: t.Menu[]) => {
    const result = menus.map((threeMebuItem: t.Menu) => {
      const m3Child = threeMebuItem?.children?.filter(
        (item: any) =>
          !item?.attributes?.type || item?.attributes?.type === '1',
      );
      return {
        ...threeMebuItem,
        children: m3Child,
      };
    });
    return result?.filter(
      (item: any) => !item?.attributes?.type || item?.attributes?.type === '1',
    );
  };
  // 如果y轴设置overflow:auto x轴通过隐式转换也会变成overflow:auto 为了让x轴的内容不受overflow的影响，需要把它设置fixed
  onMouseEnter = (e: any) => {
    const childDom = e?.currentTarget?.querySelector('.four-child-wrap');
    if (childDom) {
      const {x, y} = childDom?.getBoundingClientRect() || {};
      childDom.style.position = 'fixed';
      childDom.style.left = `${x}px`;
      childDom.style.top = `${y}px`;
    }
  };
  // 移出的时候需要把设置的样式重置
  onMouseLeave = (e: any) => {
    const childDom = e?.currentTarget?.querySelector('.four-child-wrap');
    if (childDom) {
      childDom.style.position = '';
      childDom.style.left = ``;
      childDom.style.top = ``;
    }
  };

  // 渲染二级菜单
  renderMenuSecond = (maxAllowCount: number, menus?: t.Menu[]) => {
    // 取前六项
    const newMenus = menus?.filter(
      (item) => !item?.attributes?.type || item?.attributes?.type === '1',
    );

    if (newMenus && newMenus.length > 0) {
      const { secondMenu, setMenuId } = this.props;
      return newMenus?.map((m2: t.Menu, i: number) => {
        const result = this.getMenuData(m2?.children || []);

        if (i < maxAllowCount) {
          return (
            <li
              key={m2.id}
              className={`second-menu-item-wrap ${
                secondMenu?.id === m2.id ? 'active' : ''
              } ${result?.length > 0 ? 'm2-has-child' : ''}`}
              onClick={() => {
                if (m2?.attributes?.pathname) {
                  setMenuId(m2, true);
                }
              }}
            >
              <a className="ant-btn-link">{m2.label}</a>
              <div className="second-child-wrap">
                {result?.length > 0 && (
                  <div className="second-child-content">
                    {result.map((thirdItem: any) => {
                      return (
                        <div
                          className={`second-child-item ${
                            thirdItem.children.length > 0 ? 'has-four' : ''
                          }`}
                          onClick={() => {
                            if (thirdItem.children.length === 0) {
                              setMenuId(thirdItem, true);
                            }
                          }}
                          onMouseEnter={
                            thirdItem.children.length > 0
                              ? this.onMouseEnter
                              : () => {}
                          }
                          onMouseLeave={
                            thirdItem.children.length > 0
                              ? this.onMouseLeave
                              : () => {}
                          }
                          key={thirdItem.id}
                        >
                          <div className="child-menu-item">
                            {thirdItem.label}
                          </div>
                          <span className="item-icon" />
                          {thirdItem.children.length > 0 && (
                            <div className="four-child-wrap">
                              <div className="four-child-content">
                                {thirdItem.children.map((item: any) => {
                                  const itemChild = item.children?.filter(
                                    (child) =>
                                      !child?.attributes?.type ||
                                      child?.attributes?.type === '1',
                                  );
                                  const isPage =
                                    (!item?.attributes?.type ||
                                      item?.attributes?.type === '1') &&
                                    itemChild?.length > 0;
                                  return (
                                    <div
                                      className={`four-child-item ${
                                        item.children.length > 0
                                          ? 'has-five'
                                          : ''
                                      }`}
                                      onClick={(event) => {
                                        if (item?.attributes?.pathname) {
                                          setMenuId(item, true);
                                          event.stopPropagation();
                                        }
                                      }}
                                      key={item.id}
                                    >
                                      <div className="child-menu-item">
                                        {item.label}
                                      </div>
                                      {isPage && <span className="item-icon" />}

                                      {itemChild.length > 0 && (
                                        <div className="five-child-wrap">
                                          <div className="five-child-content">
                                            {itemChild.map((level5: any) => {
                                              return (
                                                <div
                                                  className={`five-child-item ${
                                                    level5.children.length > 0
                                                      ? 'has-six'
                                                      : ''
                                                  }`}
                                                  onClick={(event) => {
                                                    if (
                                                      level5?.attributes
                                                        ?.pathname
                                                    ) {
                                                      setMenuId(level5, true);
                                                      event.stopPropagation();
                                                    }
                                                  }}
                                                  key={level5.id}
                                                >
                                                  <div className="child-menu-item">
                                                    {level5.label}
                                                  </div>
                                                </div>
                                              );
                                            })}
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  );
                                })}
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </li>
          );
        } else if (i === maxAllowCount) {
          const arr = newMenus.slice(maxAllowCount, newMenus.length);
          return (
            <li className="more-menus" key={m2.id}>
              {/* 为兼容mgr项目使用svg渲染 */}
              <div className="more-menus-icon">
                <svg
                  viewBox="64 64 896 896"
                  focusable="false"
                  className="ico-more"
                  data-icon="menu"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path d="M904 160H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0 624H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0-312H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8z" />
                </svg>
              </div>
              <div className="space" />
              <ul className="more-menus-container">
                {arr?.map((m2: t.Menu) => {
                  const restrResult = this.getMenuData(m2?.children || []);
                  return (
                    <li
                      key={m2.id}
                      className={`${
                        secondMenu?.id === m2.id ? 'active' : ''
                      } moreli ${
                        restrResult.length > 0 ? 'has-child-menu' : ''
                      }`}
                      onClick={() => {
                        if (m2?.attributes?.pathname) {
                          setMenuId(m2, true);
                        }
                      }}
                    >
                      <div className="btn ant-btn-link">{m2.label}</div>
                      <span className="child-item-icon" />
                      <div className="second-child-wrap">
                        <div className="second-child-content">
                          {restrResult.map((thirdItem: any) => {
                            return (
                              <div
                                className={`second-child-item ${
                                  thirdItem.children.length > 0
                                    ? 'has-child-menu has-four'
                                    : ''
                                }`}
                                onClick={() => {
                                  if (thirdItem.children.length === 0) {
                                    setMenuId(thirdItem);
                                  }
                                }}
                                key={thirdItem.id}
                              >
                                <div className="child-menu-item">
                                  {thirdItem.label}
                                </div>
                                <span className="item-icon" />
                                {thirdItem.children.length > 0 && (
                                  <div className="four-child-wrap">
                                    <div className="four-child-content">
                                      {thirdItem.children.map((item: any) => {
                                        return (
                                          <div
                                            className={`second-child-item ${
                                              item.children.length > 0
                                                ? 'has-four'
                                                : ''
                                            }`}
                                            onClick={() => {
                                              setMenuId(item);
                                            }}
                                            key={item.id}
                                          >
                                            <div className="child-menu-item">
                                              {item.label}
                                            </div>
                                          </div>
                                        );
                                      })}
                                    </div>
                                  </div>
                                )}
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    </li>
                  );
                })}
              </ul>
            </li>
          );
        }
        return null;
      });
    }
    return null;
  };

  render() {
    const { maxAllowCount, topMenu } = this.props;

    return (
      <ul className="second-menu">
        {this.renderMenuSecond(maxAllowCount, topMenu?.children || [])}
      </ul>
    );
  }
}
export default SecondMenu;
