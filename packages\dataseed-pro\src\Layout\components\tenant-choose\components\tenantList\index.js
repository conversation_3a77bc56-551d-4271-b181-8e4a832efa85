import { getUserTenants,toggleTenant } from '../../service';
import { getImgUrl } from './getImgUrl';

export const fetchTenantList = async () => {
  const res = await getUserTenants();
  const imgUrls = await Promise.allSettled(
    res?.data?.map((item) => getImgUrl(item?.tenantIconKey)),
  );
  return res?.data?.map((item, i) => ({
    ...item,
    imgUrl: imgUrls[i]?.value,
  }));
};

export const handleTenantChooseItem = async (item, callback) => {
  toggleTenant({
    tenantId: item?.tenantId,
    userId: item?.userId,
    redirect: window.location.href,
  }).then(async response => {
    // 切换租户重新走认证流程
    localStorage.removeItem('X-TOKEN');

    localStorage.removeItem('X-TENANT-CODE');

    if (item?.imgUrl) {
      localStorage.setItem('tenant-icon', item?.imgUrl);
    } else {
      await getImgUrl(item?.tenantIconKey).then(res => {
        localStorage.setItem('tenant-icon', res);
      });
    }

    callback(response?.data?.url);

    // getProductsByMe().then((res) => {
    //   // if (res.data?.length === 1) {
    //   //   window.location.href = res?.data[0]?.productHomeUrl;
    //   //   onlyOneProductCallback?.(res?.data[0]?.productHomeUrl);
    //   // } else {
    //   //   window.location.href = '/idaasui/#/entry';
    //   // }
    // });
  });
};
