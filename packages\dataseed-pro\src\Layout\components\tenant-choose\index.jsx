import './index.less';

// import { DownOutlined, RightOutlined } from '@ant-design/icons';
// import Icon from '@ant-design/icons';
import classnames from 'classnames';
import React, { Component } from 'react';

import {
  DEFAULTPAGEURL,
  DEFAULTPATHNAME,
  OLD_DEFAULTPATHNAME,
  OLD_DEFAULTPAGEURL,
  FIRSTAUTHPAGE,
  SHUHEKEY,
} from '../../constants';

import {
  handleTenantChooseItem,
} from './components/tenantList';

class TenantChoose extends Component {
  constructor(props) {
    super(props);
    this.state = {
      tenantList: props.tenantList,
      userInfoModalVisible: false,
    };
  }

  oChangeTenantChooseItem = (item) => {
    if (this.state.tenantList?.length <= 1) {
      return;
    }
    handleTenantChooseItem(item, (redirectUrl) => {
      if (item?.tenantCode === SHUHEKEY) {
        localStorage.removeItem('tenant-icon');
      }

      window.location.href = redirectUrl || '/idaasui/#/keycloak-login-callback'
    });
  };

  componentDidUpdate(prevProps) {
    if (prevProps.tenantList !== this.props.tenantList) {
      this.setState({ tenantList: this.props.tenantList || [] });
    }
  }

  render() {
    const { isShuhe } = this.props;
    const { tenantList, userInfoModalVisible } = this.state;
    const curTenant = tenantList?.find(
      (item) => item?.tenantCode === localStorage.getItem('X-TENANT-CODE'),
    );
    const isMultiTenant = tenantList.length > 1;
    return (
      <>
        <div
          className='more-tenant'
        >
          <div>切换租户</div>
          <div className='more-tenant-icon'></div>
          
          <div
            className="menu-choose-tenant-modal"
          >
            <div className="tenant-main">
              {tenantList?.map((item) => (
                <div
                  key={item.id}
                  className="tenant-item"
                  onClick={() => this.oChangeTenantChooseItem(item)}
                >
                  <div className="tenant-name">
                    {item.tenantName}
                  </div>
                </div>
              ))}
            </div>
            <div className="opacity-box" />
          </div>
        </div>
      </>
    );
  }
}

export default TenantChoose;
