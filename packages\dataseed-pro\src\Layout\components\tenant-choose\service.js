import $http from '@lattebank/webadmin-http';

export const getProductsByMe = (params) =>
  $http.get(`/idaasopr/myself/products`, params);

export const getUserTenants = (params) =>
  $http.get(`/idaasopr/session/users`, params);

// 根据key查看图片
export const getImageBlob = (params) => {
  return $http({
    url: `/idaasopr/file/download`,
    method: 'GET',
    params,
    responseType: 'blob',
  });
};

// 切换登录用户的租户
export const toggleTenant = (params) => {
  return $http.post(`/idaas/v2/session/users:toggle`, params);
};
