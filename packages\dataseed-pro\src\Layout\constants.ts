/*
 * @Author: wa<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-05-08 13:58:12
 * @LastEditors: pangjiancong
 * @LastEditTime: 2025-04-28 11:37:48
 * @FilePath: /mainui/src/constant/index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export const DEFAULTPATHNAME = '/idaasui/';
// mainui中的默认页
const DEFAULTMAINUIPATHNAME = '/mainui/base-home/';
// 默认页pathname
export const DEFAULTPATHNAMELIST = [DEFAULTPATHNAME, DEFAULTMAINUIPATHNAME];
// 默认页的pageName
export const DEFAULTPAGENAME = '/default-page';

export const DEFAULTHASH = '#/default-page';

export const DEFAULTPAGEURL = `${DEFAULTPATHNAME}${DEFAULTHASH}`;

export const OLD_DEFAULTPATHNAME = '/idaasui/';

export const OLD_DEFAULTHASH = '#/old-default-page';

export const OLD_DEFAULTPAGEURL = `${OLD_DEFAULTPATHNAME}${OLD_DEFAULTHASH}`

export const FIRSTAUTHPAGE = 'first-auth-page';

export const SHUHEKEY = 'shuhe';

export const XIAODAIKEY = 'xiaodai';

export const DEFAULT_MAINUI_CONFIG_DATA = {
  noMenuRoutes: [
    '/idaasui/#/login-entry',
    '/idaasui/#/login',
    '/idaasui/#/modify-password',
    '/idaasui/#/o-modify-password',
    '/idaasui/#/o-forget-password',
    '/idaasui/#/employee-register',
    '/idaasui/#/account-login',
    '/idaasui/#/keycloak-login-callback',
    '/idaasui/#/logout',
    '/idaasui/#/customer-confirm-result',
    '/idaasui/#/dingtalk-mobile-login/(.*)',
    '/idaasui/#/two-auth',
    '/idaasui/#/tenant-choose',
  ],
  connectedApps: ['idaasui'],
  beforeLoginedList: [
    '/idaasui/#/login-entry',
    '/idaasui/#/login',
    '/idaasui/#/modify-password',
    '/idaasui/#/o-modify-password',
    '/idaasui/#/o-forget-password',
    '/idaasui/#/employee-register',
    '/idaasui/#/account-login',
    '/idaasui/#/keycloak-login-callback',
    '/idaasui/#/logout',
    '/idaasui/#/customer-confirm-result',
    '/idaasui/#/dingtalk-mobile-login/',
    '/idaasui/#/two-auth',
  ],
  defaultRegisterUI: [
    {
      context: 'idaasui',
      type: 'ui',
      id: 1234,
    },
  ],
  whiteRouteList: ['/idaasui/#/tenant-choose', `${DEFAULTPAGEURL}/:id`, `${OLD_DEFAULTPAGEURL}/:id`],
};

export const ROUTE_PORTAL = "/infraui/#/portal";

export const needRenderStaticScripts: { src: string, id: string, globalBtnConfigKeys: string[] }[] = [
  // 提交问题工单
  {
    id: "shuhe-qa",
    src: "/staticui/productservicedesk/productservicedesk.sdk.js",
    globalBtnConfigKeys: ['submitQuestionnaire', 'productServiceDesk']
  },
];

export const MOKA_HOST = [
  'localhost',
  'moka.dmz.dev.caijj.net',
  'moka.dev.caijj.net',
  'pdmoka.dmz.dev.shuheo.cn',
  'moka.dmz.sit.caijj.net',
  'moka.sit.caijj.net',
  'pdmoka.dmz.sit.shuheo.cn',
  'moka.dmz.pre.caijj.net',
  'moka.pre.caijj.net',
  'pdmoka.dmz.pre.shuheo.cn',
  'moka.dmz.prod.caijj.net',
  'moka.caijj.net',
  'pdmoka.dmz.shuheo.cn',
];

export const OULEI_DOMAIN = [
  'localhost:8000/bizbasicui',
  'b.sit.caijj.net/bizbasicui',
  'b.pre.caijj.net/bizbasicui',
  'b.caijj.net/bizbasicui',
  'localhost:8000/privateopsmgrui',
  'b.sit.caijj.net/privateopsmgrui',
  'b.pre.caijj.net/privateopsmgrui',
  'b.caijj.net/privateopsmgrui',
];

export const MENU_SOURCE_MAP = {
  product: 'PRODUCT',
  tenant: 'TENANT',
};
export const SENTRY_EXTERNAL_DOMAIN = ['localhost', '127.0.0.1'];
