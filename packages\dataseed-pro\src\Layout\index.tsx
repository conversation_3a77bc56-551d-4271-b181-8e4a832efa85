/* eslint-disable no-return-assign */
/* eslint-disable no-param-reassign */
/* eslint-disable object-shorthand */
/* eslint-disable no-nested-ternary */
/* eslint-disable prefer-const */
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import 'nprogress/nprogress.css';
import './style/main.less';

import $http from '@lattebank/webadmin-http';
import { message } from 'antd';
import classnames from 'classnames';
import NProgress from 'nprogress';
import { pathToRegexp } from 'path-to-regexp';
import React from 'react';
// import { history } from 'umi';
import { openUrlForMain } from './utils/common';

import {
DEFAULTHASH,DEFAULTPAGENAME,DEFAULTPATHNAMELIST,OLD_DEFAULTHASH,OLD_DEFAULTPAGEURL,OLD_DEFAULTPATHNAME,
SHUHEKEY
} from './constants';
import { default as API } from './utils/api';
import {
appNameRegex,
checkConnectedApps,
checkIsSameApp,
checkNoMenu,
findMenuById,
findMenuByPath,
findNearestNumber,getBreadRoutes,
getCookie,
getMenuId,
getNavMenuMaxAllowCount,
getUrlQueries,
initFindMenu,
watermark
} from './utils/layoutUtil';
import axios from './utils/request';
import { isMokaHost,isOuLeiUI } from './utils/userUtil';

import Breadcrumb from './components/breadcrumb';
import ForbiddenPage from './components/forbidden-page';
import LayoutLoading from './components/layout-loading';
import LeftSider from './components/left-sider';
import MenuNavigation from './components/menu-navigation';
import Mine from './components/mine';
import NoAuthorityModal from './components/no-authority-modal';
import NotFoundPage from './components/not-found';
import ProductDropdown from './components/product-dropdown';
import SecondMenu from './components/second-menu';
import initalIcon from './components/tenant-choose/images/tenant-normal.svg';
import MyTodoTrigger from './components/todos-modal';
import UpdateTokenModal from './components/update-token-modal';
import { needRenderStaticScripts,ROUTE_PORTAL } from './constants';
import logo from './images/logo.png';
import * as t from './types';








const defaultGuidebookUrl = `${window.origin}/productportalui/#/portal/products/document/P0039?key=9166`

const clsPrefix = 'antdplus-layout-new common-layout';
const queryPathCode = (params) => axios.get(`/authopr/authpp/menu/resource/query/pathCode`, { params });

class LayoutView extends React.Component<t.Props, t.State> {

  constructor(props: t.Props) {
    super(props);
    this.state = {
      menus: props?.menus ?? [],
      userInfo: props?.userInfo ?? { id: '' },
      openKeys: [], // 三级菜单选中项
      topMenu: {}, // 当前对应的一级菜单信息
      secondMenu: {}, // 当前对应的二级菜单信息
      threeMenu: {}, // 当前对应的三级菜单信息
      level4Menu: {}, // 当前对应的四级菜单信息
      level5Menu: {}, // 当前对应的五级菜单信息
      breadcrumbRoutes: [], // 面包屑
      isForbidden: false, // 是否403
      isNotFound: false, // 是否404
      authCheckFlag: false,
      waterImg: watermark(props.userInfo?.label ?? ''), // 水印
      maxAllowCount: 6, // 二级菜单允许的最多个数
      historyList: [],
      guidebookUrl:
        'http://moka.dmz.prod.caijj.net/productportalui/#/portal/products/document/P0039?key=9166',
      noAuthorityModal: {
        visible: false,
      },
      updateTokenModal: {
        visible: false,
      },
      firstMenuNavigationVisible: false,
      onlyHeader: false, // 仅有顶部一、二级菜单，无左侧侧边栏
      solutionsRatio: 1,
      latestWidth: window.innerWidth,
      leftSiderIsFolded: false,
      isNoMenu: checkNoMenu(props.mainuConfigData),
      isShuhe: true,
      hasConnectedApps: checkConnectedApps(props.mainuConfigData),
      permitMenuPath: checkConnectedApps(props.mainuConfigData)
        ? props.permitMenuPathV2
        : props.permitMenuPath,
      layoutIsInited: false,
    };
  }

  handleUpdateToken = (event) => {
    if (
      event.key === 'X-TENANT-CODE' &&
      event?.newValue &&
      event?.oldValue !== event?.newValue
    ) {
      window.location.reload()
    }
  };

  componentDidMount() {
    const { isMicroContext } = this.props;
    window.addEventListener('popstate', this.popStateFn);
    window.addEventListener('resize', this.resizeFn);
    if (isMicroContext) {
      window.addEventListener('mainui:routeChange', this.handleRouteChange);
      window.addEventListener('mainui:notFound', this.handleNotFound);
      window.addEventListener('mainui:forbbiden', this.handleForbbiden);
    } else {
      window.addEventListener('hashchange', this.hashchangeFn);
    }
    window._globalBtnConfig = window._globalBtnConfig || {
      userManual: true,
      submitQuestionnaire: true,
      productServiceDesk: true,
      globalOpen: true,
    };

    window.addEventListener('storage', this.handleUpdateToken);

    this.initialLayout(this.props);

    this.setTraceUserInfo();

    this.initLeftSiderIsFolded();

    this.handleTenatChange();
    this.removeLocalstrage();
  }

  UNSAFE_componentWillReceiveProps(nextProps: t.Props) {
    const { menus, mainuConfigData, permitMenuPathV2, permitMenuPath } = nextProps;
    this.setState({
      menus,
      isNoMenu: checkNoMenu(mainuConfigData),
      hasConnectedApps: checkConnectedApps(mainuConfigData),
      permitMenuPath: checkConnectedApps(mainuConfigData)
        ? permitMenuPathV2
        : permitMenuPath,
     }, () => {
      this.initialLayout(nextProps);
    })
  }

  componentWillUnmount() {
    const {isMicroContext} = this.props;
    window.removeEventListener('popstate', this.popStateFn);
    window.removeEventListener('resize', this.resizeFn);
    window.removeEventListener('storage', this.handleUpdateToken);
    if (!isMicroContext) {
      window.removeEventListener('hashchange', this.hashchangeFn);
    } else {
      window.removeEventListener('mainui:routeChange', this.handleRouteChange);
      window.removeEventListener('mainui:notFound', this.handleNotFound);
      window.removeEventListener('mainui:forbbiden', this.handleForbbiden);
      window.removeEventListener('storage', this.handleUpdateToken);
    }
  }

  setTraceUserInfo = () => {
    const {
      userInfo: { id },
    } = this.state;
    const wind: t.Windows = window;
    if (wind.o_trace_w_$BizConfigs) {
      wind.o_trace_w_$BizConfigs.uid = id;
    } else {
      wind.o_trace_w_$BizConfigs = { uid: id };
    }
  };

  removeLocalstrage = () => {
    if (localStorage.getItem('X-IDAASUI-TENANT-ID')) {
      localStorage.removeItem('X-IDAASUI-TENANT-ID');
    }
    if (localStorage.getItem('X-IDAASUI-TOKEN')) {
      localStorage.removeItem('X-IDAASUI-TOKEN');
    }
  };

  handleForbbiden = () => {
    console.log('Forbiden');
  };

  handleNotFound = () => {
    const wind: t.Windows = window;
    if (wind.o_trace_w_$BizConfigs) {
      wind.o_trace_w_$BizConfigs.pageName = 'unknown';
    } else {
      wind.o_trace_w_$BizConfigs = { pageName: 'unknown' };
    }
    console.log('NotFound');
    this.setState({
      isNotFound: true,
    });
  };

  handleNoMenu = () => {
    const { isNoMenu } = this.state;
    const { mainuConfigData } = this.props;
    const result = checkNoMenu(mainuConfigData);
    // 有从无菜单跳转到有菜单，刷新页面
    if (!result && isNoMenu) {
      window.location.reload();
    }
    // 从有菜单跳转到无菜单，隐藏菜单并刷新页面
    if (result && !isNoMenu) {
      this.setState({ isNoMenu: true }, () => {
        window.location.reload();
      });
    }
  };

  handleRouteChangeCb = (event, isSameApp) => {
    const { detail = {} } = event || {};
    if (isSameApp) {
      NProgress.start();
      const pageLoadEvent = new CustomEvent('tracesdk:pageload', {
        detail,
      });
      const pageRemoveEvent = new CustomEvent('tracesdk:pageremove', {
        detail,
      });
      window.dispatchEvent(pageLoadEvent);
      window.dispatchEvent(pageRemoveEvent);
      setTimeout(() => {
        NProgress.done();
      });
    }
    this.hashchangeFn(detail);
  };

  handleRouteChange = (event) => {
    this.handleNoMenu();
    this.handleConnnectedApps(event);
  };

  handleTenatChange = () => {
    const curTenantCode = localStorage.getItem('X-TENANT-CODE');
    const isShuhe = curTenantCode ? curTenantCode === SHUHEKEY : true;
    const { isNoMenu } = this.state;
    const { userInfo = {} } = this.props;
    try {
      // 添加代码异步加载工单和机器人js
      if (
        !isNoMenu &&
        isShuhe &&
        !isOuLeiUI() &&
        userInfo?.attributes?.isDefaultShuheCorpUser === 'true'
      ) {
        for (const it of needRenderStaticScripts) {
          if (
            it?.globalBtnConfigKeys?.some((key) => window._globalBtnConfig?.[key])
          ) {
            const arr = document.getElementsByTagName('script') || [];
            let current = null;
            Array.from(arr)?.forEach(i => {
              if (i?.id && i?.id === it.id) {
                current = i;
              }
            });
            // 如果当前页面已挂载shuhe百事通js,不再重复挂载。
            if (!current) {
              const scriptdom = document.createElement('script');
              scriptdom.id = it.id;
              scriptdom.src = it.src;
              document.body.appendChild(scriptdom);
            }
          }
        }
      }
    } catch (error) {
      console.log('添加代码异步加载工单和机器人js-error', error)
    }

    this.setState({
      isShuhe,
    });
  };

  initLeftSiderIsFolded() {
    const userId = this.props.userInfo?.id || getCookie('USER_ID');
    const isFold =
      localStorage.getItem(`osystem-menu-left-sider__${userId}`) || '';
    this.setState({
      leftSiderIsFolded: isFold === 'true',
    });
  }

  getAuthStatus = () => {
    const hash =
      window.location.hash &&
      window.location.hash.split('#')?.[1].split('?')?.[0];
    const { permitMenuPath } = this.state;

    const hasAuth = permitMenuPath.some((item) => {
      const contextPath = item.split('#')?.[0] || '';
      let menuPath = item.split('#')?.[1] || '';
      const idx = menuPath.indexOf('?');
      if (idx > -1 && idx !== menuPath.length - 1) {
        menuPath = menuPath.slice(0, idx);
      }
      const match = pathToRegexp(menuPath).exec(hash);

      return (
        match && match.length > 0 && contextPath === window.location.pathname
      );
    });

    return hasAuth;
  };

  handleConnnectedApps = (event) => {
    const {
      mainuConfigData = {},
      permitMenuPath = [],
      permitMenuPathV2 = [],
    } = this.props;
    const { detail = {} } = event || {};
    const { newURL, oldURL } = detail;
    const isSameApp = checkIsSameApp(newURL, oldURL);
    let changeInitData = {
      isNotFound: false,
    };
    if (!isSameApp) {
      const hasConnectedApps = checkConnectedApps(mainuConfigData);
      changeInitData = {
        ...changeInitData,
        // 这里不能进行优化，所以注释掉，否则会导致将qiankun的wrap容器卸载掉，加载子应用异常
        // layoutIsInited: false,
        hasConnectedApps,
        permitMenuPath: hasConnectedApps ? permitMenuPathV2 : permitMenuPath,
      };
    }
    this.setState(changeInitData, () =>
      this.handleRouteChangeCb(event, isSameApp),
    );
  };

  async authorizedCheck(historyList, hasAuth, pathname) {
    // const isOnlineEnv = checkdomain();
    const hash =
      window.location.hash &&
      window.location.hash.split('#')?.[1].split('?')?.[0];
    const { permitMenuPath, isShuhe, hasConnectedApps } = this.state;
    if (permitMenuPath.length > 0 && !hasAuth && isShuhe && !hasConnectedApps) {
      // 查询当前URL，是否配置了菜单
      const curTopMenu = (historyList || this.state.historyList)?.[0];
      const rootCode =
        curTopMenu?.attributes?.pathId || curTopMenu?.id || curTopMenu?.appId;
      const params = {
        pathname: `${window.location.pathname}#${hash}`,
        rootCode,
        code: getMenuId(),
      };

      // 当前URL配置了菜单，但没有权限
      try {
        const res = await queryPathCode(params);
          const isForbidden = res.data;

          if (!window.location.pathname.includes('mgrui') && isForbidden) {
            this.setState(
              {
                isForbidden,
                layoutIsInited: true,
              },
              () => {
                this.onlyRenderHeader(true);
              },
            );
          } else {
            this.setState(
              {
                isForbidden: false,
                layoutIsInited: true,
              },
              () => {
                this.initMenu(pathname, historyList);
              },
            );
          }
      } catch (err) {
          console.log('系统忙不过来');
          this.setState({
            authCheckFlag: true,
            layoutIsInited: true,
          });
      }
    } else if (permitMenuPath.length > 0 && !hasAuth && hasConnectedApps) {
      this.setState(
        {
          isForbidden: true,
          layoutIsInited: true,
        },
        () => {
          this.onlyRenderHeader(true);
        },
      );
    } else {
      this.initMenu(pathname, historyList);
    }
  }

  // 监听浏览器前进后退(多入口菜单处理)
  popStateFn = (event: PopStateEvent) => {
    event.state?.menuId &&
      sessionStorage.setItem('menuId', event.state?.menuId);
  };

  // 统一权限校验
  checkAuthFn = async (historyList) => {
    const pathname =
      window.location.hash &&
      window.location.hash.split('#')?.[1].split('?')?.[0];
    const hasAuth = this.getAuthStatus();
    if (!hasAuth) {
      await this.authorizedCheck(historyList, hasAuth, pathname);
    } else {
      this.setState({
        isForbidden: false,
      });
      this.initMenu(pathname, historyList);
    }
  };

  // 更新面包屑+多入口菜单处理
  hashchangeFn = async (event: any) => {
    const { oldURL, newURL } = event;
    // 路由发生变化, 更新面包屑
    if (oldURL !== newURL) {
      await this.checkAuthFn(null);
    }
  };

  onlyRenderHeader = (isForbidden) => {
    const { menus = [], historyList } = this.state;
    const { isBCDS = false } = this.props;

    let openKeys = [];
    const { pathname, hash } = window.location || {};
    const curPath = `${pathname}${hash}`;

    let forbiddenRenderPreMenu = false;

    const isRouteFromMenu = JSON.parse(
      sessionStorage.getItem('url_isRouteFrom'),
    );
    const routeMenuKeys = isRouteFromMenu?.attributes?.pathId.split(',') || [];
    const historyTopMenu = historyList?.[0];
    const historyTopMenuId =
      historyTopMenu?.attributes?.pathId ||
      historyTopMenu?.id ||
      historyTopMenu?.appId;
    const isRouteFrom =
      isRouteFromMenu?.attributes?.pathname?.split('?')?.[0] ===
      curPath.split('?')?.[0];
    if (isForbidden && isRouteFrom) {
      const topMenuItem = historyTopMenu;
      openKeys =
        (
          topMenuItem?.attributes?.pathId ||
          topMenuItem?.id ||
          topMenuItem?.appId
        ).split(',') || [];

      sessionStorage.removeItem('url_isRouteFrom');
    } else if (isForbidden && historyTopMenuId === routeMenuKeys[0]) {
      forbiddenRenderPreMenu = true;
      openKeys = routeMenuKeys;
      sessionStorage.removeItem('url_isRouteFrom');
    } else if (DEFAULTPATHNAMELIST.includes(pathname) && hash.includes(DEFAULTHASH)) {
      const id = hash.split('#/default-page/')?.[1];
      openKeys = id ? [id] : [];
    } else if (pathname === OLD_DEFAULTPATHNAME && hash.includes(OLD_DEFAULTHASH)) {
      const id = hash.split('#/old-default-page/')?.[1];
      openKeys = id ? [id] : [];
    }
    let topMenu: any = findMenuById(menus, openKeys[0]) || {};
    topMenu = this.portalMenuCheck(topMenu);
    // 初始化加载的时候如果是默认页，需要单独设置一个pageName
    if (
      DEFAULTPATHNAMELIST.includes(pathname) &&
      (hash.includes(DEFAULTHASH) || hash.includes(OLD_DEFAULTHASH))
    ) {
      const wind: t.Windows = window;
      if (wind.o_trace_w_$BizConfigs) {
        wind.o_trace_w_$BizConfigs.pageName = `${topMenu?.pathLabel}${DEFAULTPAGENAME}`;
      } else {
        wind.o_trace_w_$BizConfigs = {
          pageName: `${topMenu?.pathLabel}${DEFAULTPAGENAME}`,
        };
      }
    }
    // 暴露菜单信息
    window.$$menuInfo = {
      ...window.$$menuInfo,
      getCurrentMenu: () => null,
      getCurrentProduct: () => {
        // 如果一级菜单是产品菜单，把对应产品暴露出去
        if (topMenu.source === 'PRODUCT') {
          return {
            productCode: topMenu.productCode
          }
        }
        return null
      },
    };

    const secondMenu = findMenuById(topMenu?.children, openKeys[1]);
    const threeMenu = forbiddenRenderPreMenu
      ? findMenuById(topMenu?.children, openKeys[2])
      : {};
    const level4Menu = forbiddenRenderPreMenu
      ? findMenuById(topMenu?.children, openKeys[3])
      : {};
    const level5Menu = forbiddenRenderPreMenu
      ? findMenuById(topMenu?.children, openKeys[4])
      : {};

    const breadcrumbRoutes = forbiddenRenderPreMenu
      ? getBreadRoutes(null, menus)
      : [];

    this.setState(
      {
        topMenu,
        secondMenu,
        threeMenu,
        level4Menu,
        level5Menu,
        breadcrumbRoutes,
        onlyHeader: isBCDS,
        authCheckFlag: true,
      },
      () => {
        this.resizeFn();
        forbiddenRenderPreMenu && this.onOpenChange(openKeys);
      },
    );
    topMenu.id &&
      this.onHistoryUpdate({ ...topMenu, name: topMenu?.label || '' });
  };

  // 设置当前二级菜单最多可以放几个
  resizeFn = () => {
    const { topMenu } = this.state;
    const twoMenu = topMenu?.children || [];
    const maxAllowCount = getNavMenuMaxAllowCount(twoMenu);
    this.setState({
      maxAllowCount,
      latestWidth: window.innerWidth,
    });
  };

  getProductMenus = (products: any) => {
    const productMenus: t.Menu[] = [];
    products.forEach((itm: t.Menu) => {
      itm &&
        itm?.children &&
        itm.children.length > 0 &&
        productMenus.push(...itm.children);
    });
    return productMenus;
  };

  initMenu = (pathname, historyData) => {
    const { menus, historyList } = this.state;
    const menu = initFindMenu(menus, pathname, historyData || historyList);

    this.setState({authCheckFlag: true, layoutIsInited: true});
    if (menu === null) {
      // 如果刷新页面没有在菜单元数据配菜单，只渲染顶部，并且取历史默认最近的产品或者解决方案
      this.onlyRenderHeader();
    } else {
      // 暴露菜单信息
      window.$$menuInfo = {
        ...window.$$menuInfo,
        getCurrentMenu: () => menu,
        // 匹配当前菜单对应的产品
        getCurrentProduct: () => ({
          productCode: menu?.productCode,
        }),
      };

      if (menu?.level === 1) {
        this.topMenuInit(menu, true);
        return;
      }
      this.onMenuClick(menu || {}, true);
    }
  };

  // 设置菜单初始化状态+初始化水印
  initialLayout = async (lastestProps: t.Props) => {
    const { userInfo = {}, solutions = [], products = [] } = lastestProps;
    const productMenus = this.getProductMenus(products);
    const target = solutions.length / (productMenus.length + solutions.length);
    const arr = [0.2, 0.4, 0.6, 0.8];
    const solutionsRatio = findNearestNumber(arr, target) * 5;
    const userId = userInfo.id || getCookie('USER_ID');
    const list =
      localStorage.getItem(`antd-menu-layout-history__${userId}`) || '';
    const historyList = list ? JSON.parse(list) : [];

    this.setState({
      historyList,
      solutionsRatio,
    });

    await this.checkAuthFn(historyList);
  };

  getVirtualPortalMenu = (topMenu) => {
    return {
      ...topMenu,
      attributes: {
        ...topMenu?.attributes,
        pathLabel: `${topMenu?.label} / 首页`,
        pathId: `${topMenu?.attributes?.pathId},${topMenu?.attributes?.pathId}_portal`,
      },
      level: 2,
      id: `${topMenu?.attributes?.pathId}_portal`,
      key: `${topMenu?.attributes?.pathId}_portal`,
      parent: topMenu?.attributes?.pathId,
      label: '首页',
      children: [],
    };
  };

  topMenuInit = (menuItem: any, isInit) => {
    const pathId =
      menuItem?.attributes?.pathId || menuItem?.id || menuItem?.appId;
    const { menus = [] } = this.state;
    const openKeys = pathId?.split(',') || [];

    const topMenu: any = findMenuById(menus, openKeys[0]);

    const topUrl = topMenu?.attributes?.pathname;
    let targetMenu = topMenu;
    if (topUrl) {
      const portalMenu = findMenuByPath(topMenu.children, topUrl);
      if (portalMenu) {
        targetMenu = portalMenu;
      } else {
        targetMenu = this.getVirtualPortalMenu(topMenu);
      }
    }
    this.setMenuId(targetMenu, true, isInit);
    if (isInit) {
      setTimeout(() => {
        this.onMenuClick(targetMenu, false, this.resizeFn);
      });
    } else {
      this.renderPage(targetMenu);
      this.onHistoryUpdate({ ...topMenu, name: topMenu.label });
    }
  };

  // 根据当前菜单是否有pathname，判断是否调整统一欢迎页
  renderPage = (m: t.Menu) => {
    const isDefault = !(m?.attributes?.pathname && m?.level && m?.level > 1);
    if (isDefault) {
      // 跳转默认页之前，给默认页手动添加pageName字段，保证埋点的正常上报
      const wind: t.Windows = window;
      if (wind.o_trace_w_$BizConfigs) {
        wind.o_trace_w_$BizConfigs.pageName = `${m?.pathLabel}${DEFAULTPAGENAME}`;
      } else {
        wind.o_trace_w_$BizConfigs = {
          pageName: `${m?.pathLabel}${DEFAULTPAGENAME}`,
        };
      }
      window.location.href = `${OLD_DEFAULTPAGEURL}/${m?.attributes?.pathId}`;
    }
  };

  portalMenuCheck = (topMenu) => {
    let newTopMenu = topMenu;
    const topUrl = topMenu?.attributes?.pathname;
    if (topUrl) {
      const portalMenu = findMenuByPath(topMenu.children, topUrl);
      if (!portalMenu) {
        const virtualPortalMenu = this.getVirtualPortalMenu(topMenu);

        newTopMenu = {
          ...topMenu,
          children: [virtualPortalMenu, ...topMenu?.children],
        };
      }
    }

    return newTopMenu;
  };

  // 根据菜单信息，获取面包屑，更新菜单的高亮，isChangeOpenKeys判断是否要处理三级菜单的折叠收缩 callback处理二级菜单是否能放下
  onMenuClick = (m: t.Menu, isChangeOpenKeys: boolean, callback?: Function) => {
    const pathId = m?.attributes?.pathId || m?.id || m?.appId;
    if (pathId) {
      const {menus = []} = this.state;
      const openKeys = pathId?.split(',') || [];

      let topMenu: any = findMenuById(menus, openKeys[0]);
      if (!topMenu) {
        return;
      }

      topMenu = this.portalMenuCheck(topMenu);

      const topChild = topMenu?.children;
      const secondMenu: any = topMenu
        ? findMenuById(topChild, openKeys[1])
        : null;
      const threeMenu: any = secondMenu
        ? findMenuById(secondMenu?.children, openKeys[2])
        : null;
      const level4Menu: any = threeMenu
        ? findMenuById(threeMenu?.children, openKeys[3])
        : null;
      const level5Menu: any = level4Menu
        ? findMenuById(threeMenu?.children, openKeys[4])
        : null;
      const guidebook =
        level5Menu?.attributes?.guidebook ||
        level4Menu?.attributes?.guidebook ||
        threeMenu?.attributes?.guidebook ||
        secondMenu?.attributes?.guidebook ||
        topMenu?.attributes?.guidebook;
      const isProduct =
        guidebook && getUrlQueries(guidebook)?.documentSpaceType === 'PRODUCT';
      const urlPreFix = `${window.origin}/productportalui/#/portal/${
        isProduct ? 'products' : 'solutions'
      }/document/`;
      const guidebookUrl = guidebook?.startsWith('http')
        ? guidebook
        : guidebook
        ? `${urlPreFix}${guidebook}`
        : defaultGuidebookUrl;

      const obj = {
        topMenu: topMenu || this.state.topMenu,
        secondMenu,
        threeMenu,
        level4Menu,
        level5Menu,
        guidebookUrl,
      };

      // 面包屑 + 埋点字段处理
      const breadcrumbRoutes = getBreadRoutes(m, menus);
      let pathLabel = '';
      if (breadcrumbRoutes && breadcrumbRoutes.length > 0) {
        Object.assign(obj, {
          breadcrumbRoutes,
        });
        pathLabel = breadcrumbRoutes.reduce((sum, curr, index) => {
          return (sum += `${index === 0 ? '' : '/'}${curr.breadcrumbName}`);
        }, '');
      } else {
        Object.assign(obj, {
          breadcrumbRoutes,
        });
      }

      const wind: t.Windows = window;
      if (wind.o_trace_w_$BizConfigs) {
        wind.o_trace_w_$BizConfigs.pageName = pathLabel;
      } else {
        wind.o_trace_w_$BizConfigs = {pageName: pathLabel};
      }

      const maxAllowCount = getNavMenuMaxAllowCount(topMenu?.children || []);

      this.setState(
        {
          ...obj,
          maxAllowCount,
          onlyHeader: false,
        },
        () => {
          callback?.();
          // 设置选中折叠的三级菜单
          isChangeOpenKeys && this.onOpenChange(openKeys);
        },
      );

      // 如果有pathname走跳转逻辑，如果没有，走默认页面逻辑
      this.renderPage(m);

      if ((m.label || m.name) === topMenu?.label) {
        this.onHistoryUpdate({...m, name: m.label || m.name});
      } else {
        this.onHistoryUpdate({...topMenu, name: topMenu.label});
      }
      this.setState({
        firstMenuNavigationVisible: false,
      });

      // 设置title
      const titleObj = JSON.parse(JSON.stringify(breadcrumbRoutes || []))
        .reverse()
        .find((item: any) => item.type === '1');

      const breadcrumbName = titleObj?.breadcrumbName;
      if (breadcrumbName) {
        document.title = titleObj?.breadcrumbName;
      }
    }
  };

  // 点击一级菜单
  onTopMenuClick = (menuItem: any) => {
    const pathId =
      menuItem?.attributes?.pathId || menuItem?.id || menuItem?.appId;
    if (pathId) {
      const { menus = [] } = this.state;
      const openKeys = pathId?.split(',') || [];

      const topMenu: any = findMenuById(menus, openKeys[0]);

      if (!topMenu) {
        // 没有topMenu，说明没有该解决方案/产品的权限，弹出无权限提示弹窗
        this.setState({
          noAuthorityModal: {
            visible: true,
            applyMenuId: pathId,
            content: `您的账号尚未开通，当前${
              menuItem.menuType === 'product' ? '产品' : '解决方案'
            }权限，无法使用${menuItem.name}。`,
          },
        });
        return;
      }

      // eslint-disable-next-line no-useless-escape
      const reg = /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/;
      const exp = new RegExp(reg);
      const link = menuItem.path || menuItem.link;
      if (exp.test(link)) {
        this.onHistoryUpdate({ ...menuItem });
        window.open(link);
        return;
      }

      this.setState(
        {
          firstMenuNavigationVisible: false,
        },
        () => {
          setTimeout((item) => {
            this.topMenuInit(menuItem);
          }, 200);
        },
      );
      return;
    }

    this.setState(
      {
        firstMenuNavigationVisible: false,
      },
      () => {
        this.setMenuId(menuItem, true, false);
        this.renderPage(menuItem);
        this.onHistoryUpdate({
          ...menuItem,
          name: menuItem.label || menuItem.name,
        });
      },
    );
  };

  // 三级菜单展开缩起功能
  onOpenChange = (openKeys?: String[]) => {
    const { topMenu } = this.state;
    const currentTopMenu =
      topMenu && Object.keys(topMenu || {}).length > 0
        ? topMenu
        : this.state.menus?.[0] || {};

    const oldOpenkeys = JSON.parse(JSON.stringify(this.state.openKeys || []));

    if (openKeys) {
      let latestOpenKey: String = '';
      let initOenKeysFlag = false;
      const openKeysLen = openKeys.length;
      if (!currentTopMenu?.attributes?.product) {
        initOenKeysFlag = openKeysLen <= 4;
        latestOpenKey =
          openKeysLen > 3 ? openKeys?.slice(2, 4) : openKeys[openKeysLen - 1];
      } else {
        initOenKeysFlag = openKeysLen <= 2;
        latestOpenKey =
          openKeysLen > 2 ? openKeys?.[1] : openKeys[openKeysLen - 1];
      }
      if (
        JSON.stringify(oldOpenkeys) ===
          JSON.stringify(
            Array.isArray(latestOpenKey) ? latestOpenKey : [latestOpenKey],
          ) &&
        initOenKeysFlag
      ) {
        this.setState({
          openKeys: latestOpenKey.length > 1 ? [latestOpenKey[0]] : [],
        });
      } else {
        this.setState({
          openKeys: Array.isArray(latestOpenKey)
            ? latestOpenKey
            : [latestOpenKey],
        });
      }
    }
  };

  // 点击菜单设置menuId
  setMenuId = (m: t.Menu, isJump: Boolean = true, isInit?: boolean) => {
    // eslint-disable-next-line no-useless-escape
    const reg = /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/;
    const exp = new RegExp(reg);
    let url = m?.attributes?.pathname;

    if (exp.test(url)) {
      window.open(url);
      return;
    }

    sessionStorage.setItem('menuId', m.id || '');
    if (isJump && url && !isInit) {
      url = url.includes('?')
        ? `${url}&_shMenuId=${m.id}`
        : `${url}?_shMenuId=${m.id}`;

      sessionStorage.setItem('url_isRouteFrom', JSON.stringify(m));
      // 未接入微前端的应用保持原来的跳转方式
      const { appList = [], history } = this.props;
      const matchArr = url.match(appNameRegex);
      const appName = matchArr?.[1];
      const isMicroApp = appList.some((item) => item.context === appName);
      if (isMicroApp) {
        openUrlForMain(url, history);
      } else {
        window.location.href = url;
      }
    }
  };

  onHistoryUpdate = (historyData: any) => {
    const { historyList = [], userInfo } = this.state;
    // 保存最近访问, 插在头部
    const idx = historyList.findIndex((item) => item.id === historyData.id);
    if (idx < 0) {
      historyList.unshift(historyData);
    } else if (idx > 0) {
      historyList.unshift(historyList.splice(idx, 1)[0]);
    }

    const newData = historyList.slice(0, 5) || [];
    this.setState({
      historyList: newData,
    });
    localStorage.removeItem(`antd-menu-layout-history__${userInfo.id}`);
    localStorage.setItem(
      `antd-menu-layout-history__${userInfo.id}`,
      JSON.stringify(newData),
    );
    const topPathLabel = historyData?.attributes?.pathLabel || 'O系统';
    document.title = topPathLabel;
  };

  onClearHistory = () => {
    const { userInfo } = this.state;
    this.setState({
      historyList: [],
    });
    localStorage.removeItem(`antd-menu-layout-history__${userInfo.id}`);
  };

  onAuthorityModalCancel = () => {
    this.setState({
      noAuthorityModal: {
        visible: false,
      },
    });
  };

  onUpdateTokenModalCancel = () => {
    this.setState({
      updateTokenModal: {
        visible: false,
      },
    });
  };

  handleNavigation = () => {
    const { noAuthorityModal, firstMenuNavigationVisible } = this.state;
    const val = !firstMenuNavigationVisible;
    if (noAuthorityModal.visible) return;

    if (val) {
      document.body.classList.add('page-hidden');
    } else {
      document.body.classList.remove('page-hidden');
    }
    this.setState({
      firstMenuNavigationVisible: val,
    });
  };

  onLeftSiderFoldedToggle = () => {
    const { leftSiderIsFolded, userInfo } = this.state;
    const isFold = !leftSiderIsFolded;
    localStorage.setItem(
      `osystem-menu-left-sider__${userInfo.id}`,
      JSON.stringify(isFold),
    );
    this.setState({
      leftSiderIsFolded: isFold,
    });
    // const rootDom: any = document.getElementsByClassName('App')?.[0];
    // if (rootDom) {
    //   rootDom.style = isFold
    //     ? 'left: 30px; width: calc(100% - 30px); transition: all 300ms;'
    //     : 'left: 207px; width: calc(100% - 210px); transition: all 300ms;';
    // }
  };

  // 逻辑迁移到后端，通过接口获取
  commonLogout = () => {
    localStorage.removeItem('tenant-icon');
    $http
      .post('/idaasopr/logout', {
        redirect: window.location.href
      })
      .then(res => {
        localStorage.removeItem('X-TOKEN');
        localStorage.removeItem('X-TENANT-CODE');
        if (res?.data?.url) {
          window.location.href = res?.data?.url;
        }
      })
      .catch(err => {
        err?.message && message.error(err?.message);
      });
  };

  /**
   * 判断是否是新权限
   */
  isNewManu = (m: t.Menu) => {
    return m?.newAuth && !m.tripartite;
  }

  render() {
    const {
      solutions = [],
      products = [],
      favorites = [],
      children,
      className = '',
      location,
      renderBreadcrumbToolBar,
      menuV2: { menus: menusV2 = [] } = {
        menus: [],
      },
    } = this.props;
    const {
      userInfo,
      guidebookUrl,
      firstMenuNavigationVisible,
      onlyHeader,
      isForbidden,
      isNotFound,
      secondMenu,
      menus = [],
      breadcrumbRoutes,
      waterImg,
      maxAllowCount,
      noAuthorityModal,
      updateTokenModal,
      historyList,
      openKeys,
      level4Menu,
      level5Menu,
      threeMenu,
      solutionsRatio,
      permitMenuPath,
      authCheckFlag,
      latestWidth,
      leftSiderIsFolded,
      isNoMenu,
      isShuhe,
      hasConnectedApps,
      layoutIsInited,
    } = this.state;

    // 如果当前菜单匹配不到,默认展示第一个有权限的一级菜单
    const topMenu =
      this.state.topMenu && Object.keys(this.state.topMenu).length > 0
        ? this.state.topMenu
        : permitMenuPath.length > 0 && authCheckFlag
        ? menus[0] || {}
        : {};
    const hasLayout =
      !`${window.location.pathname}${window.location.hash}`.includes(
        'layout=0',
      ) && !isNoMenu;

    const noLeftSider =
      `${window.location.pathname}${window.location.hash}`.includes(
        'layout=100',
      );
    const onlyNoBreadcrumb =
      `${window.location.pathname}${window.location.hash}`.includes(
        'layout=110',
      );
    const isShowUserManual = window?._globalBtnConfig?.userManual;

    const _thirdMenuList = !topMenu?.attributes?.product
      ? secondMenu?.children || []
      : topMenu?.children || [];
    const thirdMenuList = _thirdMenuList?.filter(
      (item) => !item?.attributes?.type || item?.attributes?.type === '1',
    )
    console.log(
      `layout hasLayout: ${hasLayout};isForbidden: ${isForbidden};permitMenuPath: ${permitMenuPath.length}; layoutIsInited: ${layoutIsInited}`,
    );
    const noHeadPage = [
      '/pipeline/:code/:version',
      '/pipeline/:env/:code/:version',
      '/artifacts-detail/:appName/:env/:version',
    ];
    const noHead = noHeadPage.some((item) => {
      const match = pathToRegexp(item).exec(location?.pathname);
      return match && match.length > 0;
    });

    const hasThirdMebu =
      !noLeftSider && !onlyHeader && thirdMenuList.length > 0;
    const hasBreadcrumb =
      !noLeftSider &&
      !onlyNoBreadcrumb &&
      hasThirdMebu &&
      breadcrumbRoutes &&
      breadcrumbRoutes.length > 0;
    const forbiddenAndNotFoundClass =
      isForbidden || isNotFound ? 'forbidden-wrapper' : '';
    const tenantIcon = localStorage.getItem('tenant-icon');
    const curLogo = isShuhe ? logo : tenantIcon || initalIcon;
    const isOulei = isOuLeiUI();
    const mokaHost = isMokaHost() || isOulei;

    return (
      <>
        <div
          className={classnames(
            clsPrefix,
            className,
            forbiddenAndNotFoundClass,
          )}
        >
          <div
            style={{
              zIndex: 9999,
              position: 'absolute',
              left: hasLayout ? '208px' : 0,
              top: hasLayout ? '100px' : 0,
              width: hasLayout ? 'calc(100% - 208px)' : '100%',
              height: hasLayout ? 'calc(100% - 100px)' : '100%',
              backgroundSize: '300px auto',
              pointerEvents: 'none',
              backgroundRepeat: 'repeat',
              backgroundImage: `url(${waterImg})`,
            }}
          />
          {hasLayout ? (
            menus.length > 0 && permitMenuPath.length > 0 ? (
              <React.Fragment>
                {!noHead && (
                  <div className="layout-header">
                    <div className="logo-menu">
                      <a
                        className="to-portal"
                        id="to-portal"
                        // eslint-disable-next-line no-script-url
                        href={isShuhe ? ROUTE_PORTAL : '#'}
                      >
                        <img alt="logo" className="layout-logo" src={curLogo} />
                      </a>
                      {/* 一级菜单 */}
                      {isShuhe ? (
                        <div
                          className={`layout-header_drop${
                            firstMenuNavigationVisible ? ' expand' : ''
                          }`}
                          onClick={() => this.handleNavigation()}
                        >
                          <div className="dropdown">
                            <div className="dropdown-drop">
                              <span className="dropdown-link">
                                {topMenu?.attributes?.pathLabel || ''}
                              </span>
                              {/* 一级菜单导航栏 */}
                              <MenuNavigation
                                visible={firstMenuNavigationVisible}
                                latestWidth={latestWidth}
                                menus={menus}
                                isShuhe={isShuhe}
                                solutions={solutions}
                                products={products}
                                favorites={favorites}
                                historyList={historyList}
                                solutionsRatio={solutionsRatio}
                                onTopMenuClick={this.onTopMenuClick}
                                onClearHistory={this.onClearHistory}
                              />
                            </div>
                          </div>
                        </div>
                      ) : (
                        <ProductDropdown
                          currentProduct={topMenu}
                          productList={menusV2}
                          onTopMenuClick={this.onTopMenuClick}
                        />
                      )}
                    </div>
                    {/* 二级菜单 */}
                    {!topMenu?.attributes?.product && (
                      <SecondMenu
                        maxAllowCount={maxAllowCount}
                        topMenu={topMenu}
                        secondMenu={secondMenu}
                        setMenuId={this.setMenuId}
                      />
                    )}
                    <div className="profile" id="profile">
                      {/* <>{!isOulei && <TenantChoose isShuhe={isShuhe} />}</> */}
                      {isShuhe && (
                        <>
                          {isShowUserManual &&
                            userInfo?.attributes?.isDefaultShuheCorpUser ===
                              'true' && (
                              <>
                                <div
                                  style={{height: '56px', paddingTop: '2px'}}
                                >
                                  <a
                                    onClick={() => {
                                      const productCode =
                                        window.$$menuInfo?.getCurrentProduct()
                                          ?.productCode;
                                      let linkUrl = '';
                                      if (productCode) {
                                        linkUrl = `${window.origin}/productportalui/#/portal/products/document/${productCode}?key=9166`;
                                      }
                                    window.open(
                                      linkUrl ||
                                        guidebookUrl ||
                                        defaultGuidebookUrl,
                                    );
                                    }}
                                    target="_blank"
                                    className="layout-header-user-manual"
                                    rel="noreferrer"
                                  >
                                    <svg
                                      width="16"
                                      height="16"
                                      viewBox="0 0 16 16"
                                      fill="none"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <path
                                        fill-rule="evenodd"
                                        clip-rule="evenodd"
                                        d="M14.1899 3.91406L10.6538 0.585938C10.2554 0.210938 9.71416 0 9.15137 0H2.0625C1.47646 0 1 0.446875 1 1V15C1 15.5516 1.47646 16 2.0625 16H13.75C14.336 16 14.8125 15.5516 14.8125 15V5.32812C14.8125 4.79844 14.5884 4.28906 14.1899 3.91406ZM10.0312 1.41406L13.3101 4.5H11.0938C10.5077 4.5 10.0312 4.05156 10.0312 3.5V1.41406ZM9.16999 7.25C8.76999 6.89 8.24999 6.72 7.60999 6.72C6.86999 6.72 6.28999 6.93 5.86999 7.36C5.44999 7.78 5.23999 8.35 5.23999 9.08H6.28999C6.28999 8.64 6.37999 8.3 6.55999 8.06C6.75999 7.77 7.08999 7.63 7.53999 7.63C7.89999 7.63 8.18999 7.73 8.38999 7.93C8.57999 8.13 8.67999 8.4 8.67999 8.75C8.67999 9.01 8.57999 9.25 8.39999 9.48L8.22999 9.67C7.60999 10.22 7.22999 10.63 7.09999 10.91C6.95999 11.17 6.89999 11.49 6.89999 11.86V12.03H7.96999V11.86C7.96999 11.62 8.01999 11.4 8.12999 11.2C8.21999 11.02 8.35999 10.84 8.54999 10.68C9.01999 10.27 9.29999 10.01 9.38999 9.9C9.63999 9.58 9.76999 9.17 9.76999 8.68C9.76999 8.08 9.56999 7.6 9.16999 7.25ZM7.95999 12.77C7.81999 12.63 7.63999 12.57 7.42999 12.57C7.22999 12.57 7.04999 12.63 6.90999 12.77C6.76999 12.9 6.69999 13.07 6.69999 13.28C6.69999 13.48 6.76999 13.65 6.90999 13.79C7.04999 13.93 7.22999 14 7.42999 14C7.62999 14 7.80999 13.93 7.94999 13.8C8.08999 13.66 8.16999 13.49 8.16999 13.28C8.16999 13.07 8.09999 12.9 7.95999 12.77Z"
                                        fill="#999999"
                                      />
                                    </svg>
                                  </a>
                                </div>
                                <span className="divider" />
                              </>
                            )}
                          {userInfo?.attributes?.isDefaultShuheCorpUser ===
                            'true' && <MyTodoTrigger userId={userInfo?.id} />}
                          <span className="divider" style={{marginLeft: 0}} />
                          {/* <span className="name-wrap">
                            <span className="name">
                              {(userInfo as t.User)?.label}
                            </span>
                          </span> */}
                        </>
                      )}
                      <Mine
                        userInfo={userInfo}
                        isOulei={isOulei}
                        isShuhe={isShuhe}
                        onLogout={this.commonLogout}
                      ></Mine>
                      {/* <span className="divider" /> */}
                      {/* <span>
                        {mokaHost ? (
                          <span onClick={this.onShuheLogout}>退出</span>
                        ) : (
                          <span onClick={this.onLogout}>退出</span>
                        )}
                      </span> */}
                    </div>
                  </div>
                )}
                {/* 三四级菜单 */}
                <div
                  className={`layout-content show ${
                    hasThirdMebu ? 'has-left' : ''
                  } ${noHead ? '' : 'has-head'}${
                    leftSiderIsFolded ? ' folded-left-sider' : ''
                  }`}
                >
                  {hasThirdMebu && (
                    <LeftSider
                      menus={menus}
                      openKeys={openKeys}
                      level4Menu={level4Menu}
                      level5Menu={level5Menu}
                      topMenu={topMenu}
                      secondMenu={secondMenu}
                      threeMenu={threeMenu}
                      isFold={leftSiderIsFolded}
                      onToggle={this.onLeftSiderFoldedToggle}
                      onOpenChange={this.onOpenChange}
                      setMenuId={this.setMenuId}
                    />
                  )}
                  <div
                    className={`right-sider${
                      hasThirdMebu ? '' : ' only-right'
                    }`}
                  >
                    {hasBreadcrumb && (
                      <Breadcrumb
                        breadcrumbRoutes={breadcrumbRoutes}
                        renderBreadcrumbToolBar={renderBreadcrumbToolBar}
                      />
                    )}
                    {isForbidden ? (
                      <ForbiddenPage
                        pathMenuIds={isForbidden}
                        hasConnectedApps={hasConnectedApps}
                      />
                    ) : isNotFound ? (
                      <NotFoundPage />
                    ) : (
                      layoutIsInited && children
                    )}
                  </div>
                </div>
              </React.Fragment>
            ) : layoutIsInited ? (
              <ForbiddenPage
                pathMenuIds={isForbidden}
                hasConnectedApps={hasConnectedApps}
              />
            ) : (
              <LayoutLoading />
            )
          ) : permitMenuPath.length > 0 ? (
            isForbidden ? (
              <ForbiddenPage
                pathMenuIds={isForbidden}
                hasConnectedApps={hasConnectedApps}
              />
            ) : isNotFound ? (
              <NotFoundPage />
            ) : (
              layoutIsInited && children
            )
          ) : layoutIsInited ? (
            <ForbiddenPage
              pathMenuIds={isForbidden}
              hasConnectedApps={hasConnectedApps}
            />
          ) : (
            <LayoutLoading />
          )}
        </div>
        {noAuthorityModal?.visible && (
          <NoAuthorityModal
            modalInfo={noAuthorityModal}
            onCancel={this.onAuthorityModalCancel}
          />
        )}
        {updateTokenModal?.visible && (
          <UpdateTokenModal onCancel={this.onUpdateTokenModalCancel} />
        )}
      </>
    );
  }
}

export const LayoutApi = API;

export default LayoutView;
