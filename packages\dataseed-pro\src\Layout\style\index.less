@prefixCls: antdplus;

@body-bg-color: #f8fafc;
@layout-head-bg: #fff;
@first-menu-bg: #fff;
@first-menu-bg-hover: #fff;
@second-menu-bg-active: #fff;
@three-menu-bg: #fff;
@three-menu-bg-active: #eef5ff;
@leve4-menu-bg: #fff;
@leve4-menu-bg-active: #eef5ff;

@bd-color: #fff;

@loading-color: #1890ff;

@first-menu-width: 170px;
@menu-height: 40px;
@three-title-padding: 0 36px 0 20px;
@drop-menu-margin: 4px 0;
@leve4-menu-padding: 0 20px 0 32px;

body {
  background-color: @body-bg-color;

  #root-master, #root, .App {
    height: 100%;
    background-color: @body-bg-color;
  }
}

#root-master, #root, .App {
  .@{prefixCls}-layout-new {
    height: 100%;
    font-weight: 400;
    font-size: 14px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>,
      'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji',
      'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
    font-variant: tabular-nums;
    font-feature-settings: 'tnum';
    -webkit-font-smoothing: initial;

    > div:not(.layout-header):not(.layout-content):not(.common-layout-loading) {
      height: 100%;

      .qiankun-micro-app-custom-wrapper {
        > div {
          height: 100%;
        }
      }
    }

    ul,
    li {
      list-style-type: none;
    }

    .layout-header,
    .left-sider {
      ul {
        margin: 0;
        padding: 0;
      }
    }

    .layout-header {
      position: fixed;
      top: 0;
      left: 0;
      z-index: 999;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 56px;
      padding: 0 24px 0 12px;
      line-height: 56px;
      background-color: @layout-head-bg !important;
      border: 0 !important;
      box-shadow: 0 5px 12px 0 rgba(238, 238, 242, 1);

      .logo-menu {
        position: relative;
        display: flex;
        width: 196px;
        height: 56px;

        &::before,
        &::after {
          position: absolute;
          top: 16px;
          display: inline-block;
          width: 1px;
          height: 24px;
          margin: 0;
          vertical-align: middle;
          background-color: #fff;
          content: '';
        }

        &::before {
          left: 46px;
        }

        &::after {
          right: 0;
          background-color: #d0dbe5;
        }

        .to-portal {
          margin-right: 12px;

          .layout-logo {
            width: 30px;
            height: 30px;
            margin: 12px 0;
          }
        }

        .ant-layout-drop {
          top: 56px !important;
          width: @first-menu-width;
          height: 500px;
          overflow: hidden;
          text-align: center;
        }

        .dropdown-drop-link {
          position: relative;
        }
      }

      &_drop {
        position: relative;
        flex: 1;
        padding-right: 31px;
        text-align: center;
        background-image: url('../images/icon_menu_not_expand.png');
        background-repeat: no-repeat;
        background-position: right 21px center;
        background-size: 10px;
        cursor: pointer;

        &.expand {
          background-image: url('../images/icon_menu_expand.png');
        }

        &:hover {
          .dropdown-link {
            color: #4e8ce5;
          }
        }

        .dropdown {
          &-drop {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 56px;
            overflow: hidden;

            img {
              width: 10px;
              height: 10px;
            }
          }

          .position-config {
            position: absolute;
            top: 56px;
            left: -62px;
            z-index: 999;
          }

          .mask {
            z-index: 998;
            display: none;
          }

          .menu-first {
            height: 0;
            overflow: hidden;
            background-color: #f9f9fa !important;
            box-shadow: 0 5px 12px 0 #eeeef2 inset,
              0 8px 20px 0 rgba(122, 154, 201, 0.3);
            cursor: default;
            opacity: 0;
            transition: height 0.2s cubic-bezier(0.215, 0.61, 0.355, 1),
              opacity 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);

            &.navigation-show {
              height: 660px;
            }

            .wrap {
              display: flex;
              height: 100%;

              .favorite-content {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                width: 216px;
                min-width: 216px;
                height: 100%;
                padding: 40px 2px 0 22px;
                background-color: #fff;
                background-image: url('../images/bg_my_collection.png');
                background-repeat: no-repeat;
                background-position: bottom;
                background-size: 100% 239px;
                box-shadow: 0 5px 12px 0 #eeeef2 inset;
                user-select: none;

                .favorite-title {
                  padding: 0 0 13px 4px;
                  color: #000;
                  font-weight: 600;
                  font-size: 14px;
                  font-family: PingFangSC-Semibold;
                  line-height: 22px;
                  letter-spacing: 0;
                }

                .favorite-list {
                  flex: 1;
                  width: 100%;
                  overflow-x: hidden;
                  overflow-y: auto;
                  color: #000;
                  font-weight: 400;
                  font-size: 12px;
                  font-family: PingFangSC-Regular;
                  line-height: 20px;
                  letter-spacing: 0;
                  text-align: left;

                  &::-webkit-scrollbar {
                    width: 4px;
                    color: #b6bbc3;
                    border-radius: 4px;
                  }

                  .product-item:first-child {
                    margin-top: 0;
                  }
                }

                .favorite-empty {
                  margin: 0 auto;
                  padding-top: 138px;
                  font-weight: 400;
                  font-family: PingFangSC-Regular;
                  text-align: center;
                  background-image: url('../images/bg_favirate_empty.png');
                  background-repeat: no-repeat;
                  background-position: top 40px center;
                  background-size: 100px 82px;

                  .main-text {
                    color: #000;
                    font-size: 14px;
                    line-height: 22px;
                  }

                  .sub-text {
                    color: #999;
                    font-size: 12px;
                    line-height: 24px;
                  }
                }
              }

              .menu-list {
                flex: 1;
                padding: 0 0 0 36px;

                .search-content {
                  display: flex;
                  align-items: center;
                  width: 1088px;
                  padding: 40px 40px 40px 0;
                  line-height: 22px;
                  //  margin: 0 auto;
                  text-align: left;

                  .search-input-wrap {
                    position: relative;
                    width: 370px;
                    margin-right: 24px;

                    .search-input {
                      width: 370px;
                      height: 32px;
                      padding: 6px 12px 6px 36px;
                      line-height: 1;
                      background: #fff;
                      border: 1px solid #d6dbe3;
                      border-radius: 4px;
                      outline: none;

                      &::-webkit-input-placeholder {
                        color: #ccc;
                      }
                    }

                    .search-btn {
                      position: absolute;
                      top: 8px;
                      left: 12px;
                    }
                  }

                  .history-content {
                    color: #333;
                    font-weight: 400;
                    font-size: 14px;
                    font-family: PingFangSC-Regular;
                    line-height: 22px;
                    white-space: nowrap;

                    .history-item {
                      display: inline-block;
                      margin-right: 12px;
                      padding: 0 8px;
                      font-size: 12px;
                      background: #e9ecf2;
                      border-radius: 4px;
                      cursor: pointer;
                    }

                    .clear-btn {
                      color: #4e8ce5;
                      cursor: pointer;
                    }
                  }
                }

                .menu-tab {
                  display: flex;
                  overflow-y: auto;
                  white-space: nowrap;
                  //  justify-content: center;
                  text-align: left;

                  .sub-title {
                    display: block;
                    width: 160px;
                    margin: 0 4px;
                    margin-bottom: 16px;
                    padding-top: 4px;
                    color: #4e8ce5;
                    font-weight: 600;
                    font-size: 14px;
                    font-family: PingFangSC-Semibold;
                    line-height: 38px;
                    letter-spacing: 0;
                    border-bottom: 1px solid #e6e9ee;
                  }

                  .solution-content {
                    text-align: left;

                    .solution-title {
                      width: 168px;
                      // margin-bottom: 12px;
                      padding-left: 4px;
                      color: #333;
                      font-weight: 600;
                      font-size: 14px;
                      font-family: PingFangSC-Semibold;
                      line-height: 22px;
                      letter-spacing: 0;

                      &.no-solution {
                        display: none;
                        margin-bottom: 0;
                      }
                    }

                    .sub-title {
                      // padding-top: 24px;

                      &.no-solution {
                        display: none;
                        padding-top: 0;
                      }
                    }

                    .solution-list {
                      column-count: 1;
                      column-gap: 0;
                      column-width: 168px;
                    }
                  }

                  .product-content {
                    height: max-content;
                    text-align: left;
                    border-left: 1px solid #d6dbe3;

                    .product-title {
                      color: #000;
                      font-weight: 600;
                      font-size: 14px;
                      font-family: PingFangSC-Semibold;
                      line-height: 22px;
                      letter-spacing: 0;

                      &.no-product {
                        display: none;
                      }
                      //  padding-left: 54px;
                    }

                    .product-list {
                      column-count: 2;
                      column-gap: 0;
                      column-width: 168px;

                      .product-line-item {
                        width: 168px;
                        margin-left: 50px;
                        padding-bottom: 20px;
                        break-inside: avoid;

                        &:last-child {
                          padding-bottom: 0;
                        }

                        .product-item:last-child {
                          margin-bottom: 0;
                        }
                      }
                    }
                  }
                }
              }
            }
          }

          .navigator-show {
            .mask {
              display: block;
            }

            .menu-first {
              opacity: 1;
            }
          }
        }
      }

      .dropdown-link {
        position: relative;
        display: inline-block;
        max-width: 113px;
        height: 56px;
        margin: 0 10px 0 0;
        overflow: hidden;
        color: #333;
        font-size: 14px;
        line-height: 56px;
        white-space: nowrap;
        text-align: center;
        text-overflow: ellipsis;

        .dropdown-icon {
          position: absolute;
          top: 22px;
          right: 0;
        }
      }

      .second-menu {
        display: flex;
        flex: 1;
        align-items: center;
        padding-left: 10px;
        overflow: visible;
        color: #333;
        font-weight: 400;
        font-size: 14px;

        .second-menu-item-wrap {
          padding: 0 36px 0 20px;
          color: #333;
          white-space: nowrap;

          &.m2-has-child {
            background-image: url('../images/icon_menu_not_expand.png');
            background-repeat: no-repeat;
            background-position: right 20px center;
            background-size: 10px;

            &:hover {
              background-color: @first-menu-bg;
              background-image: url('../images/icon_menu_expand.png');

              .second-child-wrap {
                display: block;
              }
            }
          }

          &:hover {
            .ant-btn-link {
              color: #4e8ce5;
            }
          }

          &.active {
            background-color: @second-menu-bg-active;
          }
        }

        .ant-btn-link {
          padding: 0;
          color: #333;
          font-size: 14px;
        }
      }

      .more-menus {
        position: relative;

        &::after {
          position: absolute;
          top: 56px;
          left: 30px;
          z-index: 999;
          display: none;
          border-color: transparent transparent @bd-color;
          border-style: solid;
          border-width: 0 6px 7px;
          content: '';
        }

        &:hover,
        &:focus {
          .more-menus-container,
          .space,
          &::after {
            display: block;
          }

          .ico-more {
            color: @loading-color;
          }
        }

        .ico-more {
          color: #333;
        }

        .space {
          position: absolute;
          top: 56px;
          left: 15px;
          z-index: 99;
          display: none;
          width: 100%;
          height: 20px;
          background-color: transparent;
        }

        &-icon {
          width: 56px;
          height: 56px;
          padding-top: 4px;
          overflow: hidden;
          text-align: center;
          cursor: pointer;
        }

        &-container {
          position: absolute;
          top: 63px;
          left: 15px;
          z-index: 999;
          display: none;
          width: 190px;
          padding-top: 14px;
          background-color: @first-menu-bg;
          border-radius: 5px;
          box-shadow: 0 5px 16px 0 rgba(225, 226, 237, 1);

          .moreli {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 14px;
            padding: 0 18px 0 20px;
            line-height: 22px;
            text-align: left;
            cursor: pointer;

            &.has-child-menu {
              .child-item-icon {
                display: inline-block;
                width: 10px;
                height: 10px;
                background-image: url('../images/icon_menu_not_expand.png');
                background-repeat: no-repeat;
                background-position: center;
                background-size: 10px;
                transform: rotate(-90deg);
              }

              &:hover {
                color: #4e8ce5;

                .item-icon {
                  background-image: url('../images/icon_menu_expand.png');
                  transform: rotate(90deg);
                }
              }
            }

            &:hover {
              color: #4e8ce5;
              background-color: @first-menu-bg-hover;

              .second-child-wrap {
                display: block;
              }
            }

            .ant-btn-link {
              font-size: 14px;
            }

            &.active {
              .ant-btn-link {
                color: #4e8ce5;
              }
            }

            .second-child-wrap,
            .four-child-wrap,
            .five-child-wrap {
              position: absolute;
              z-index: 999;
              color: #666;
              font-size: 14px;
              font-family: PingFangSC-Regular;
              line-height: 22px;
              cursor: pointer;
            }

            .second-child-wrap {
              top: -14px;
              left: -190px;
              display: none;
              padding-top: 14px;
              transition: display 0s;
              transition-delay: 3s;

              .second-child-content {
                padding: 7px 0;
                background: #fff;
                box-shadow: 0 5px 16px 0 rgba(225, 226, 237, 1);

                .second-child-item {
                  position: relative;
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  width: 190px;
                  padding: 7px 20px;

                  &:hover {
                    color: #4e8ce5;
                  }

                  &.has-four {
                    .item-icon {
                      display: inline-block;
                      width: 10px;
                      height: 10px;
                      background-image: url('../images/icon_menu_not_expand.png');
                      background-repeat: no-repeat;
                      background-position: center;
                      background-size: 10px;
                      transform: rotate(-90deg);
                    }

                    &:hover {
                      color: #4e8ce5;

                      .item-icon {
                        background-image: url('../images/icon_menu_expand.png');
                        transform: rotate(90deg);
                      }

                      .four-child-wrap {
                        display: block;
                      }
                    }
                  }

                  .four-child-wrap {
                    top: 2px;
                    left: -214px;
                    display: none;
                    padding-right: 4px;

                    .four-child-content {
                      padding: 7px 0 7px 20px;
                      background: #fff;
                      box-shadow: 0 5px 16px 0 rgba(225, 226, 237, 1);

                      .four-child-item {
                        .item-icon {
                          display: none;
                        }

                        &.has-five {
                          .item-icon {
                            display: inline-block;
                            width: 10px;
                            height: 10px;
                            background-image: url('../images/icon_menu_not_expand.png');
                            background-repeat: no-repeat;
                            background-position: center;
                            background-size: 10px;
                            transform: rotate(-90deg);
                          }

                          &:hover {
                            color: #4e8ce5;

                            .item-icon {
                              background-image: url('../images/icon_menu_expand.png');
                              transform: rotate(90deg);
                            }

                            .four-child-wrap {
                              display: block;
                            }
                          }
                        }
                      }
                    }
                  }

                  .five-child-wrap {
                    top: 2px;
                    left: 170px;
                    display: none;
                    padding-left: 4px;

                    .five-child-content {
                      padding: 7px 0 7px 20px;
                      background: #fff;
                      box-shadow: 0 5px 16px 0 rgba(225, 226, 237, 1);

                      .five-child-item {
                        .item-icon {
                          display: none;
                        }

                        &.has-six {
                          .item-icon {
                            display: inline-block;
                            width: 10px;
                            height: 10px;
                            background-image: url('../images/icon_menu_not_expand.png');
                            background-repeat: no-repeat;
                            background-position: center;
                            background-size: 10px;
                            transform: rotate(-90deg);
                          }

                          &:hover {
                            color: #4e8ce5;

                            .item-icon {
                              background-image: url('../images/icon_menu_expand.png');
                              transform: rotate(90deg);
                            }

                            .five-child-wrap {
                              display: block;
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }

      .second-menu-item-wrap {
        position: relative;

        &.active {
          .ant-btn-link {
            color: #4e8ce5;
          }
        }

        .second-child-wrap,
        .four-child-wrap,
        .five-child-wrap {
          position: absolute;
          z-index: 999;
          color: #666;
          font-size: 14px;
          font-family: PingFangSC-Regular;
          line-height: 22px;
          cursor: pointer;
        }

        .second-child-wrap {
          top: 56px;
          left: 0;
          display: none;
          padding-top: 14px;

          .second-child-content {
            padding: 7px 0 7px 20px;
            background: #fff;
            box-shadow: 0 5px 16px 0 rgba(225, 226, 237, 1);
            max-height: 80vh;
            overflow-y: auto;
            overflow-x: hidden;

            .second-child-item,
            .four-child-item,
            .five-child-item {
              position: relative;
              display: flex;
              align-items: center;
              justify-content: space-between;
              width: 170px;
              padding: 7px 20px 7px 0;

              &:hover {
                color: #4e8ce5;
              }

              &.has-four {
                .item-icon {
                  display: inline-block;
                  width: 10px;
                  height: 10px;
                  background-image: url('../images/icon_menu_not_expand.png');
                  background-repeat: no-repeat;
                  background-position: center;
                  background-size: 10px;
                  transform: rotate(-90deg);
                }

                &:hover {
                  color: #4e8ce5;

                  .item-icon {
                    background-image: url('../images/icon_menu_expand.png');
                    transform: rotate(90deg);
                  }

                  .four-child-wrap {
                    display: block;
                  }
                }
              }

              .four-child-wrap {
                top: 2px;
                left: 170px;
                display: none;
                padding-left: 4px;

                .four-child-content {
                  padding: 7px 0 7px 20px;
                  background: #fff;
                  box-shadow: 0 5px 16px 0 rgba(225, 226, 237, 1);

                  .four-child-item {
                    .item-icon {
                      display: none;
                    }

                    &.has-five {
                      .item-icon {
                        display: inline-block;
                        width: 10px;
                        height: 10px;
                        background-image: url('../images/icon_menu_not_expand.png');
                        background-repeat: no-repeat;
                        background-position: center;
                        background-size: 10px;
                        transform: rotate(-90deg);
                      }

                      &:hover {
                        color: #4e8ce5;

                        .item-icon {
                          background-image: url('../images/icon_menu_expand.png');
                          transform: rotate(90deg);
                        }

                        .five-child-wrap {
                          display: block;
                        }
                      }
                    }
                  }
                }
              }

              .five-child-wrap {
                top: 2px;
                left: 170px;
                display: none;
                padding-left: 4px;

                .five-child-content {
                  padding: 7px 0 7px 20px;
                  background: #fff;
                  box-shadow: 0 5px 16px 0 rgba(225, 226, 237, 1);

                  .five-child-item {
                    .item-icon {
                      display: none;
                    }

                    &.has-six {
                      .item-icon {
                        display: inline-block;
                        width: 10px;
                        height: 10px;
                        background-image: url('../images/icon_menu_not_expand.png');
                        background-repeat: no-repeat;
                        background-position: center;
                        background-size: 10px;
                        transform: rotate(-90deg);
                      }

                      &:hover {
                        color: #4e8ce5;

                        .item-icon {
                          background-image: url('../images/icon_menu_expand.png');
                          transform: rotate(90deg);
                        }

                        .four-child-wrap {
                          display: block;
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }

      .child-menu-item {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .profile {
        display: flex;
        color: #666;
        font-size: 14px;
        line-height: 56px;
        white-space: nowrap;

        a {
          color: #666;

          &:hover {
            color: #4e8ce5;
          }
        }

        > span {
          display: flex;
          align-items: center;
          cursor: pointer;
        }

        .divider {
          display: inline-block;
          width: 1px;
          height: 16px;
          margin: 20px 16px;
          padding: 0;
          background-color: #d0dbe5;
        }
      }
    }

    .layout-content {
      position: relative;
      display: flex;
      height: 100%;
      overflow: hidden;
      transition: padding-left 300ms;

      &.has-head {
        padding-top: 56px;

        .left-sider {
          top: 56px;
        }
      }

      &.has-left {
        padding-left: 208px;

        &.folded-left-sider {
          padding-left: 30px;
        }
      }

      .left-sider {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 2;
        display: flex;
        flex-direction: column;
        align-items: stretch;
        justify-content: space-between;
        width: 208px;
        height: calc(100% - 56px);
        padding-top: 10px;
        padding-bottom: 56px;
        overflow-x: hidden;
        overflow-y: auto;
        font-size: 14px;
        background-color: @three-menu-bg;
        box-shadow: 5px 0 12px 0 rgba(238, 238, 242, 0.5);
        transition: width 300ms;

        &.folded {
          width: 30px;

          .left-sider-container {
            ul {
              display: none;
            }

            .no-child-three {
              display: none;
            }
          }

          .left-sider-toolbar {
            width: 30px;
            padding-left: 0;
            background-image: url('../images/ico_folded.png');
            background-position: center;
          }
        }

        .menus-bgc {
          color: #333 !important;
          line-height: @menu-height;
          background-color: @three-menu-bg;

          &.has-child-three,
          &.has-child-four {
            .ant-menu-item-active,
            .ant-menu-item:hover,
            .ant-menu-submenu-active,
            .ant-menu-submenu-title:hover,
            .ant-menu:not(.ant-menu-inline) .ant-menu-submenu-open {
              color: #fff;
            }
          }

          &.has-child-four {
            padding-left: 12px;
          }

          &.no-child-three {
            display: block;
            padding-left: 20px;
            white-space: nowrap;
          }

          &-selected {
            position: relative;
            color: #4e8ce5 !important;
            background-color: @three-menu-bg-active;

            &::after {
              position: absolute;
              top: 0;
              right: 0;
              bottom: 0;
              display: block;
              border-right: 3px solid #eef5ff;
              content: '';
            }
          }

          .menus-icon {
            width: 14px;
            height: 14px;
            margin-top: -4px;
          }

          .menus-font {
            display: inline-block;
            width: calc(100% - 26px);
            margin-left: 0;
            overflow: hidden;
            line-height: 40px;
            text-overflow: ellipsis;
            vertical-align: middle;
            cursor: pointer;
          }

          .three-menu-title,
          .four-menu-title {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            width: 100%;
            margin: 0;
            padding: @three-title-padding;
            overflow: hidden;
            color: #333 !important;
            white-space: nowrap;
            background-image: url('../images/icon_menu_not_expand.png');
            background-repeat: no-repeat;
            background-position: right 20px center;
            background-size: 10px;
            cursor: pointer;

            &.expand {
              color: #4e8ce5 !important;
              background-image: url('../images/icon_menu_expand.png');
            }
          }

          .forth-menus,
          .five-menus {
            background-color: @leve4-menu-bg;
          }

          .forth-menus-item,
          .five-menus-item {
            display: block;
            height: @menu-height;
            margin: @drop-menu-margin;
            padding: @leve4-menu-padding;
            color: #333;
            line-height: @menu-height;
            cursor: pointer;

            .labelA {
              display: inline-block;
              width: 100%;
              overflow: hidden;
              color: #333;
              white-space: nowrap;
              word-break: keep-all;
            }

            &_selected {
              position: relative;
              background-color: @leve4-menu-bg-active;

              .labelA {
                color: #4e8ce5;
              }

              &::after {
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                display: block;
                border-right: 3px solid #eef5ff;
                content: '';
              }
            }
          }
        }

        .left-sider-toolbar {
          position: fixed;
          bottom: 0;
          left: 0;
          width: 208px;
          height: 56px;
          margin-left: 0; // 防止第三方插件样式表干扰定位
          padding-left: 102px;
          color: #666;
          font-size: 14px;
          line-height: 56px;
          background-color: #fff;
          background-image: url('../images/ico_unfold.png');
          background-repeat: no-repeat;
          background-position: 78px center;
          background-size: 20px 20px;
          cursor: pointer;
          transition: all 300ms;
        }
      }

      .right-sider {
        flex: 1;
        width: 100%;
        height: 100%;
        overflow: auto;

        &.only-right {
          > div:not(.breadcrumb) {
            padding: 0;
          }
        }

        // > div:not(.breadcrumb) {
        //   height: 100%;
        // }
        > div.qiankun-micro-app-custom-wrapper {
          height: 100%;

          .qiankun-micro-app-wrapper {
            height: 100%;
          }

          .qiankun-sub-app-wrapper {
            height: calc(100vh - 56px);

            > div {
              height: 100%;
              overflow: auto;
            }
          }
        }

        > div:not(.breadcrumb) {
          height: 100%;

          .qiankun-micro-app-custom-wrapper {
            height: 100%;

            .qiankun-micro-app-wrapper {
              height: 100%;
            }

            .qiankun-sub-app-wrapper {
              height: calc(100vh - 56px);

              > div {
                height: 100%;
                overflow: auto;
              }
            }
          }
        }

        .breadcrumb {
          position: relative;
          padding: 16px 0 16px 24px;

          + div.qiankun-micro-app-custom-wrapper {
            height: calc(100% - 56px);

            .qiankun-micro-app-wrapper {
              height: 100%;
            }

            .qiankun-sub-app-wrapper {
              height: calc(100vh - 127px);

              > div {
                height: 100%;
                overflow: auto;
              }
            }
          }

          + div {
            height: calc(100% - 56px);

            .qiankun-micro-app-custom-wrapper {
              height: 100%;

              .qiankun-micro-app-wrapper {
                height: 100%;
              }

              .qiankun-sub-app-wrapper {
                height: calc(100vh - 127px);

                > div {
                  height: 100%;
                  overflow: auto;
                }
              }
            }
          }

          span,
          a {
            color: rgba(0, 0, 0, 0.45);
            line-height: 24px;
          }

          a:hover {
            color: #006ff9;
            cursor: 'pointer';
          }

          > span:last-child .breadcrumb-link span {
            color: rgba(0, 0, 0, 0.65);
          }

          .breadcrumb-separator {
            margin: 0 8px;
          }
        }
      }

      .layout-main {
        padding: 16px 22px;

        .layout-main-breadcrumb {
          margin-bottom: 15px;

          .ant-btn-link {
            padding: 0;
          }
        }
      }
    }

    .first-menu-arrow {
      position: relative;
      display: inline-block;
      width: 8px;
      height: 28px;
      line-height: 56px;

      &::before {
        transform: rotate(-45deg) translateX(2px);
      }

      &::after {
        transform: rotate(45deg) translateX(-2px);
      }

      &::before,
      &::after {
        position: absolute;
        width: 6px;
        height: 1.5px;
        background: #fff;
        content: '';
      }

      &.up,
      &.down {
        position: absolute;
        top: 50%;
        right: 16px;
        height: 20px;
        margin: 0;
        line-height: 20px;
      }

      &.up {
        &::before {
          transform: rotate(45deg) translateX(2px);
        }

        &::after {
          transform: rotate(-45deg) translateX(-2px);
        }
      }
    }
  }
}

.@{prefixCls}-layout-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  color: @loading-color;
  transform: translate(-50%, -50%);
  transition: transform 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86),
    -webkit-transform 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);

  &-dot {
    position: relative;
    display: inline-block;
    width: 1em;
    height: 1em;
    font-size: 32px;
    transform: rotate(45deg);
    animation: antRotate 1.2s infinite linear;
  }

  .dot-item {
    position: absolute;
    display: block;
    width: 14px;
    height: 14px;
    background-color: @loading-color;
    border-radius: 100%;
    transform: scale(0.75);
    transform-origin: 50% 50%;
    opacity: 0.3;
    animation: antSpinMove 1s infinite linear alternate;

    &:nth-child(1) {
      top: 0;
      left: 0;
    }

    &:nth-child(2) {
      top: 0;
      right: 0;
      animation-delay: 0.4s;
    }

    &:nth-child(3) {
      right: 0;
      bottom: 0;
      animation-delay: 0.8s;
    }

    &:nth-child(4) {
      bottom: 0;
      left: 0;
      animation-delay: 1.2s;
    }
  }
}

.ant-modal-body {
  padding-bottom: 40px;
}

@media only screen and (max-width: 1272px) {
  .navigator-show .menu-first {
    overflow-x: scroll !important;
  }

  .menu-first {
    max-width: 1366px;

    .wrap {
      width: 1366px;

      .solution-list {
        column-width: 214px !important;
      }

      .product-line-item {
        margin-left: 50px !important;
      }
    }
  }
}

@media only screen and (min-width: 1448px) {
  .menu-first {
    max-width: 1440px;

    .menu-first .wrap {
      width: 1440px;
    }
  }
}

.layout-header-idaas-dropdown {
  left: 54px !important;
  width: 200px;

  .ant-dropdown-menu-title-content {
    width: 170px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
