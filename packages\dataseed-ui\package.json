{"name": "dataseed-ui", "main": "lib/index.js", "module": "esm/index.js", "types": "lib/index.d.ts", "version": "1.90.5", "description": "", "scripts": {"build": "npm run clean-dist && NODE_ENV=production rollup -c ", "dev": "rollup -c -w", "test": "echo \"Warnings: no test specified\"", "coverage": "echo \"Warnings: no coverage specified\"", "gen-doc": "ts-node ./scripts/genDoc.ts", "update-snapshot": "jest --updateSnapshot", "clean-dist": "rimraf lib/** esm/** tsconfig.tsbuildinfo .rollup.cache/**"}, "author": "fex", "license": "Apache-2.0", "files": ["lib", "esm", "scss"], "exports": {".": {"require": "./lib/index.js", "import": "./esm/index.js"}, "./scss/": "./scss/", "./lib/themes/": "./lib/themes/", "./lib/*": {"require": "./lib/*.js", "import": "./esm/*.js"}, "./esm/*": {"require": "./lib/*.js", "import": "./esm/*.js"}, "./src": "./src/index.tsx"}, "dependencies": {"@rc-component/mini-decimal": "^1.0.1", "amis-core": "^1.90.5", "amis-formula": "^1.90.5", "antd": "5.25.3", "classnames": "2.3.1", "codemirror": "^5.63.0", "dayjs": "^1.11.9", "downshift": "6.1.12", "echarts": "5.4.0", "froala-editor": "3.1.1", "hoist-non-react-statics": "^3.3.2", "jsbarcode": "^3.11.5", "keycode": "^2.2.1", "lodash": "^4.17.15", "markdown-it": "^12.0.6", "markdown-it-html5-media": "^0.7.1", "match-sorter": "^6.3.1", "mobx": "^4.5.0", "mobx-react": "^6.3.1", "mobx-state-tree": "^3.17.3", "moment": "^2.19.4", "monaco-editor": "0.30.1", "prop-types": "^15.6.1", "rc-input-number": "^7.4.0", "rc-menu": "^9.8.4", "rc-progress": "^3.1.4", "re-resizable": "6.9.11", "react-color": "^2.19.3", "react-draggable": "^4.4.5", "react-hook-form": "7.39.0", "react-json-view": "1.21.3", "react-overlays": "5.1.1", "react-textarea-autosize": "8.3.3", "react-transition-group": "4.4.2", "react-visibility-sensor": "5.1.1", "sortablejs": "1.15.0", "tinymce": "^6.1.2", "tslib": "^2.3.1", "uncontrollable": "7.2.1"}, "devDependencies": {"@rollup/plugin-babel": "6.0.4", "@rollup/plugin-commonjs": "^22.0.2", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^14.1.0", "@rollup/plugin-typescript": "^8.3.4", "@svgr/rollup": "^6.2.1", "@testing-library/jest-dom": "^5.16.4", "@types/babel__core": "^7.1.19", "@types/jest": "^28.1.0", "@types/react": "^18.0.24", "@types/react-dom": "^18.0.8", "autoprefixer": "^10.4.12", "babel-plugin-import": "1.13.8", "jest": "^29.0.3", "jest-environment-jsdom": "^29.0.3", "moment-timezone": "^0.5.34", "postcss-import": "^14.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-auto-external": "^2.0.0", "rollup-plugin-license": "^2.7.0", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-scss": "^3.0.0", "sass": "^1.54.9", "ts-jest": "^29.0.2", "ts-node": "^10.4.0", "typescript": "^4.6.4"}, "peerDependencies": {"amis-core": "*", "amis-formula": "*", "react": ">=16.8.6", "react-dom": ">=16.8.6"}, "jest": {"testEnvironment": "jsdom", "collectCoverageFrom": ["src/**/*"], "moduleFileExtensions": ["ts", "tsx", "js"], "transform": {"\\.(ts|tsx)$": ["ts-jest", {"diagnostics": false}]}, "setupFiles": ["jest-canvas-mock"], "testRegex": "/.*\\.test\\.(ts|tsx|js)$", "moduleNameMapper": {"\\.(css|less|sass|scss)$": "<rootDir>/../../__mocks__/styleMock.js", "\\.(svg)$": "<rootDir>/../../__mocks__/svgMock.js", "\\.svg\\.js$": "<rootDir>/../../__mocks__/svgJsMock.js"}, "setupFilesAfterEnv": ["<rootDir>/../amis-core/__tests__/jest.setup.js"], "testPathIgnorePatterns": ["/node_modules/", "/.rollup.cache/"]}, "publishConfig": {"access": "public", "registry": "http://registry.caijj.net/repository/npm-caijiajia/"}, "gitHead": "37d23b4a8eb1c663bc38e8dd9040889ea1526ec4"}