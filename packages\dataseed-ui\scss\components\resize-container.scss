.#{$ns}ResizeContainer{
  &.autoFillHeight {
    height: 100% !important;
  }

  &.autoFillWidth {
    width: 100% !important;
  }

  &.resize-left {
    border-left: var(--borderDefaultWidth) solid var(--borderDefaultColor);
  }

  &.resize-right {
    border-right: var(--borderDefaultWidth) solid var(--borderDefaultColor);
  }

  &.resize-top {
    border-top: var(--borderDefaultWidth) solid var(--borderDefaultColor);
  }

  &.resize-bottom {
    border-bottom: var(--borderDefaultWidth) solid var(--borderDefaultColor);
  }

  &.resize-topRight,
  &.resize-bottomRight,
  &.resize-bottomLeft,
  &.resize-topLeft,
  &.resize-positionAll {
    border: var(--borderDefaultWidth) solid var(--borderDefaultColor);
  }

  &.resize-collapsable {
    position: relative;
    transition-property: width;
    transition-duration: 300ms;
    transition-timing-function: cubic-bezier(0.4,0,0.2,1);
  }

  &-asideResizor {
    position: relative;
    height: 100%;
  }
  
  &-handelWrapper div {
    &::after {
      position: absolute;
      width: 0.75rem;
      height: 1.5rem;
      border: 0.0625rem solid #dee2e6;
      background-color: #fff;
      border-radius: 0.142rem;
      font-size: 12px;
      line-height: 0.625rem;
      text-align: center;
      color: #666;
    }

    &:hover::after {
      color: #000;
      box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.12),
        0 2px 4px 0 rgba(0, 0, 0, 0.08);
    }

    &.psRight{
      width: 0 !important;
      cursor: ew-resize !important;
  
      &::after {
        right: 0;
        top: 50%;
        writing-mode: vertical-lr;
        margin-top: -0.75rem;
        content: "···";
        cursor: ew-resize;
      }
    }

    &.psLeft {
      width: 0 !important;
      cursor: ew-resize !important;
  
      &::after {
        left: 0;
        top: 50%;
        writing-mode: vertical-lr;
        margin-top: -0.75rem;
        content: "···";
        cursor: ew-resize;
      }
    }

    &.psTop{
      cursor: ns-resize !important;

      &::after {
        top: 50%;
        right: 50%;
        width: 1.5rem;
        height: 0.75rem;
        content: "···";
        writing-mode: unset;
        cursor: ns-resize;
        margin-right: -0.75rem;
        margin-top: -0.375rem;
      }
    }

    &.psBottom{
      cursor: ns-resize !important;
      
      &::after {
        top: 0;
        right: 50%;
        width: 1.5rem;
        height: 0.75rem;
        content: "···";
        writing-mode: unset;
        cursor: ns-resize;
        margin-right: -0.75rem;
        margin-top: 0;
      }
    }

    &.psTopRight{
      cursor: nesw-resize !important;
      
      &::after {
        top: 50%;
        right: 0;
        width: 1.5rem;
        height: 0.75rem;
        // content: "···";
        writing-mode: unset;
        cursor: nesw-resize;
        margin-top: -0.375rem;
      }
    }

    &.psTopLeft{
      cursor: nwse-resize !important;
      
      &::after {
        top: 50%;
        right: 0;
        width: 1.5rem;
        height: 0.75rem;
        // content: "···";
        writing-mode: unset;
        cursor: nwse-resize;
        margin-top: -0.375rem;
      }
    }

    &.psBottomRight{
      cursor: nwse-resize !important;
      
      &::after {
        top: 50%;
        right: 0;
        width: 1.5rem;
        height: 0.75rem;
        // content: "···";
        writing-mode: unset;
        cursor: nwse-resize;
        margin-top: -0.375rem;
      }
    }

    &.psBottomLeft{
      cursor: nesw-resize !important;
      
      &::after {
        top: 50%;
        right: 0;
        width: 1.5rem;
        height: 0.75rem;
        // content: "···";
        writing-mode: unset;
        cursor: nesw-resize;
        margin-top: -0.375rem;
      }
    }
  }
  
}
