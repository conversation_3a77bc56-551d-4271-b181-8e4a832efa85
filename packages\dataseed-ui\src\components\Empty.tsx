import React from 'react';
import { Icon, Html } from 'amis-ui';

interface EmptyProps {
  classnames?: any;
  className?: string;
  placeholder?: string;
  description: any;
  icon?: any;
}

class Empty extends React.Component<EmptyProps, any> {

  render() {
    const {classnames: cx, className, icon, description} = this.props;
    return (
      <div className={cx('Empty', className)}>
        {
          icon ? <Icon
            icon={icon || "table-empty"}
            className={cx('Empty-icon', 'icon')}
          /> : null
        }
        <Html html={description || '暂无数据'} />
    </div>
    )
  }
}

export default Empty;
