import {ThemeProps, themeable, autobind} from 'amis-core';
import React from 'react';
import { Resizable, ResizableProps } from "re-resizable";

export interface ResizeContainerProps
  extends ThemeProps,
    Omit<ResizableProps, 'type' | 'className' | 'style'> {
      wrapperComponent?: keyof JSX.IntrinsicElements;
      autoFillHeight?: boolean;
      autoFillWidth?: boolean;
      style?: React.CSSProperties;
      collapsable?: boolean;
}

export class ResizeContainer extends React.Component<ResizeContainerProps> {

  ref: any;

  constructor(props: ResizeContainerProps) {
    super(props);
    this.refFn = this.refFn.bind(this);
  }

  getWrappedInstance() {
    return this.ref;
  }

  @autobind
  refFn(ref: any) {
    this.ref = ref;
  }

  render() {
    const {
      className,
      classnames: cx,
      children,
      wrapperComponent,
      minWidth,
      maxWidth,
      defaultSize,
      enable,
      autoFillHeight,
      autoFillWidth,
      collapsable,
      onResizeStart,
      onResize,
      onResizeStop,
      ...rest
    } = this.props;

    const positionAll = typeof enable === 'boolean';
    const isLeft = enable && !positionAll && enable.left;
    const isRight = enable && !positionAll && enable.right;
    const isTop = enable && !positionAll && enable.top;
    const isBottom = enable && !positionAll && enable.bottom;
    const isTopRight = enable && !positionAll && enable.topRight;
    const isBottomRight = enable && !positionAll && enable.bottomRight;
    const isBottomLeft = enable && !positionAll && enable.bottomLeft;
    const isTopLeft = enable && !positionAll && enable.topLeft;

    const Component =
      (wrapperComponent as keyof JSX.IntrinsicElements) || 'div';

    return (
        <Resizable
          ref={this.refFn}
          className={cx('ResizeContainer', className, { 
            autoFillWidth,
            autoFillHeight,
            'resize-left': isLeft,
            'resize-right': isRight,
            'resize-top': isTop,
            'resize-bottom': isBottom,
            'resize-topRight': isTopRight,
            'resize-bottomRight': isBottomRight,
            'resize-bottomLeft': isBottomLeft,
            'resize-topLeft': isTopLeft,
            'resize-positionAll': positionAll,
            'resize-collapsable': collapsable,
          })}
          defaultSize={defaultSize}
          minWidth={minWidth}
          maxWidth={maxWidth}
          enable={enable}
          handleWrapperClass={cx('ResizeContainer-handelWrapper')}
          handleClasses={{
            left: isLeft ? 'psLeft': '',
            right: isRight ? 'psRight': '',
            top: isTop ? 'psTop': '',
            bottom: isBottom ? 'psBottom': '',
            topRight: isTopRight ? 'psTopRight': '',
            bottomRight: isBottomRight ? 'psBottomRight': '',
            bottomLeft: isBottomLeft ? 'psBottomLeft': '',
            topLeft: isTopLeft ? 'psTopLeft': '',
          }}
          onResizeStart={onResizeStart}
          onResize={onResize}
          onResizeStop={onResizeStop}
        >
          <Component className={cx('ResizeContainer-asideResizor')}>
            {children}
          </Component>
        </Resizable>
      
    );
  }
}

export default themeable(ResizeContainer);
