import { RendererProps, Schema, autobind, resolveEventData, resolveVariableAndFilter } from "amis-core";
import React from "react";
import { Input, Row, InputProps } from 'antd';
import MessageList from "./MessageList";

export interface ChatProps extends RendererProps {
  /**
   * 聊天室标题
   */
  title?: string | Schema;

  /**
   * 前缀
   */
  prefix?: string;

  /**
   * 后缀
   */
  suffix?: string;

  /**
   * 聊天室高度
   */
  bodyHeight?: number;
}

export default class Chat extends React.Component<ChatProps> {
  static defaultProps = {
    bodyHeight: 300,
    contentField: 'content',
    avatarField: 'avatar',
    senderField: 'sender',
    previewMode: false,
    sendOnEnter: true,
    timeField: '',
    senderMessageBackground: '#dbeafe',
    otherMessageBackground: '#dbeafe',
    // issue#1075 解决头像组件名称设置两个汉字只展示一个的问题
    avatarConfig: {
      showOneWord: false,
      show: true,
    }
  }

  bodyRef: React.RefObject<HTMLDivElement> = React.createRef();

  state = {
    inputText: ''
  }

  handleInputChange: InputProps['onChange'] = (e) => {
    this.setState({
      inputText: e.target.value
    });
  }

  handlePressEnter: InputProps['onPressEnter'] = (e) => {
    if (!this.props.sendOnEnter) {
      return;
    }
    e.preventDefault();
    this.handleSend();
  }

  handleSend = () => {
    const { onSend } = this.props;
    const { inputText } = this.state;

    if (inputText) {
      onSend && onSend(inputText, () => {
        this.setState({
          inputText: ''
        });

        setTimeout(() => {
          if(this.bodyRef?.current) {
            this.bodyRef.current.scrollTop = this.bodyRef.current?.scrollHeight;
          }
        })
      })
    }
  }

  renderTitle() {
    const { title, render, onAction } = this.props;

    return render('title', title!, {
      onAction
    });
  }

  @autobind
  renderAvatar(avatar: string) {
    const { render, avatarConfig } = this.props;
    const { show, ...restAvatarConfig } = avatarConfig;

    // #1218 头像可以不显示
    if (!show) return null;

    return render('avatar', {
      type: 'avatar',
      text: avatar,
      src: avatar,
      onError: 'return true;',
      icon: 'fa fa-user',
      ...(restAvatarConfig || {}),
    })
  }

  render(): React.ReactNode {
    const {
      classnames: cx,
      title,
      render,
      source,
      bodyHeight,
      contentField,
      senderField,
      avatarField,
      resolveSenderExpression,
      renderMessageItem,
      renderfix,
      previewMode,
      timeField,
      className,
      ...rest
    } = this.props;

    return <div className={cx(className, "ChatControl")}>
      {!!title && <div className={cx("ChatControl-head")}>
        <div className={cx("ChatControl-title")}>{ this.renderTitle() }</div>
      </div>}
      <div
        className={cx("ChatControl-body")}
        style={{
          height: bodyHeight,
        }}
        ref={this.bodyRef}
      >
        <MessageList
          {...rest}
          classnames={cx}
          renderAvatar={this.renderAvatar}
          source={source}
          contentField={contentField}
          resolveSenderExpression={resolveSenderExpression}
          renderMessageItem={renderMessageItem}
          avatarField={avatarField}
          senderField={senderField}
          renderfix={renderfix}
          timeField={timeField}
        />
      </div>
      {!previewMode && <div className={cx('ChatControl-footer')}>
          <Input.TextArea
            autoSize={{ minRows: 2, maxRows: 4 }}
            placeholder="请输入内容"
            bordered={false}
            value={this.state.inputText}
            // @ts-ignore
            onChange={this.handleInputChange}
            // @ts-ignore
            onPressEnter={this.handlePressEnter}
          />
          <Row justify={'end'} className={cx('ChatControl-footer-send-button')}>
            {render('send-button', {
              type: 'button',
              label: '发送',
              level: 'primary',
              onClick: this.handleSend
            })}
          </Row>
      </div>}
    </div>;
  }
}
