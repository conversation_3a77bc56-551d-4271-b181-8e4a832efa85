import dayjs from 'dayjs';
import isPlainObject from 'lodash/isPlainObject'
import uniqBy from 'lodash/uniqBy'
import quarterOfYear from 'dayjs/plugin/quarterOfYear';
import weekday from 'dayjs/plugin/weekday';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import localeData from 'dayjs/plugin/localeData';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import isBetween from 'dayjs/plugin/isBetween';
import { isPureVariable, resolveVariableAndFilter, filterDate } from 'amis-core'
import type {Dayjs, ManipulateType, QUnitType} from 'dayjs';
import 'dayjs/locale/zh-cn';

dayjs.locale('zh-cn')
dayjs.extend(weekday);
dayjs.extend(advancedFormat);
dayjs.extend(quarterOfYear);
// Fix: caught TypeError: clone2.localeData is not a function
dayjs.extend(localeData);
dayjs.extend(customParseFormat);
dayjs.extend(isBetween);

export {dayjs};
export type {Dayjs};

type DayjsType = Dayjs | Dayjs[] | null | undefined;

// 动态范围支持的时间单位类型
type DynamicRangeUnit = ManipulateType | 'quarter';

export const relativeValueRe: RegExp =
  /^(.+)?(\+|-)(\d+)(day|week|month|quarter|year|second|minute|hour)s?$/i;

export const dayjs2str = (d: DayjsType, format: string) => {
  if (Array.isArray(d)) {
    return d.map(item => {
      return item && item.format ? item.format(format) : item;
    });
  }
  return d && d.format ? d.format(format) : d;
};

export const dayjsFormat = (
  value: Dayjs | null | undefined | string | (string | undefined)[],
  format?: string,
) => {
  if (typeof value === 'string') {
    value = value.trim();
    return dateFilter(value, format) as Dayjs;
  } else if (Array.isArray(value)) {
    return value.map(item => {
      if (typeof item === 'string') {
        return dateFilter(item, format);
      }
      return undefined;
    }) as Dayjs[];
  }
  return value;
};

function dateFilter(value: string, format?: string) {
  if (typeof value === 'string') {
    value = value.trim();
  }
  let regs: RegExpExecArray | null;
  if (
    value &&
    typeof value === 'string' &&
    (regs = relativeValueRe.exec(value))
  ) {
    const step = parseInt(regs[3], 10);
    const from: Dayjs = regs[1]
      ? (dateFilter(regs[1]) as Dayjs)
      : /(minute|hour|second)s?/.test(regs[4])
      ? dayjs()
      : dayjs().startOf('day');

    return regs[2] === '-'
      ? from.subtract(step, regs[4])
      : from.add(step, regs[4]);
  } else if (value === 'now') {
    return dayjs();
  } else if (value === 'today') {
    return dayjs().startOf('day');
  } else {
    return dayjs(value, format, true) as Dayjs;
  }
}

type stringUndef = string | undefined;
type numberUndef = number | undefined;
function validRanges(arr: [numberUndef, numberUndef][] | undefined) {
  return Array.isArray(arr) && arr.length > 0;
}

// 解析动态范围参数
function parseDynamicRange(dynamicRange: number | string): { value: number; unit: DynamicRangeUnit } {
  if (typeof dynamicRange === 'number') {
    // 数字表示天数
    return { value: dynamicRange, unit: 'day' };
  }

  if (typeof dynamicRange === 'string') {
    // 解析字符串格式，如 "3months", "7days"
    const match = relativeValueRe.exec(`+${dynamicRange}`);
    if (match) {
      return {
        value: parseInt(match[3], 10),
        unit: match[4] as DynamicRangeUnit
      };
    }
  }

  throw new Error(`Invalid dynamicRange format: ${dynamicRange}`);
}

// 统一处理时间单位转换的辅助函数
function calculateDateTimeRange(fromDate: Dayjs, value: number, unit: DynamicRangeUnit): { min: Dayjs, max: Dayjs } {
  if (unit === 'quarter') {
    // quarter 需要转换为 month 计算 (1 quarter = 3 months)
    return {
      min: fromDate.subtract(value * 3, 'month'),
      max: fromDate.add(value * 3, 'month')
    };
  } else {
    return {
      min: fromDate.subtract(value, unit as ManipulateType),
      max: fromDate.add(value, unit as ManipulateType)
    };
  }
}

// 可用日期选择范围，这个功能定制性较强
// 1. 支持闭合区间可选
// 2. 支持开放区间可选
// 3. 支持多区间可选 disabledDate: [[, maxDate1], [minDate2, maxDate2], ..., [minDateN, ]]
export function disabledDateFn(props: {
  minDate?: stringUndef; // 简单配置，优先级最低
  maxDate?: stringUndef;
  disabledDateRanges?: [stringUndef, stringUndef][]; // 优先级最高
  dynamicRange?: number | string; // 动态范围限制
  showTime?: boolean; // 是否显示时间
  format?: string;
}) {
  const {minDate, maxDate, disabledDateRanges, dynamicRange, showTime, format} = props;

  const disabledFn = (
    current: Dayjs,
    minDate: stringUndef,
    maxDate: stringUndef,
  ) => {
    // 禁选区间，命中区间则禁用，区间外则可用
    const minDateTime = minDate ? dayjsFormat(minDate, format) as Dayjs : null;
    const maxDateTime = maxDate ? dayjsFormat(maxDate, format) as Dayjs : null;

    const isBeforeMin =
      minDateTime && current < minDateTime.startOf('day');
    const isAfterMax =
      maxDateTime && current > maxDateTime.endOf('day');
    // true 为禁选态
    return !isBeforeMin && !isAfterMax;
  };

  const fn = (current: Dayjs, info?: { from?: Dayjs; type?: string }) => {
    let isDynamicRangeDisabled = false;
    let isDateRangesDisabled = false;
    let isMinMaxDisabled = false;

    // 1. 检查动态范围限制
    if (dynamicRange && info?.from) {
      // 确保 from 是有效的 dayjs 对象
      const fromDate = dayjs.isDayjs(info.from) ? info.from : dayjs(info.from);

      // 解析动态范围参数
      const { value, unit } = parseDynamicRange(dynamicRange);

      let minDateTime, maxDateTime;

      if (showTime) {
        // showTime: true - 前后各推指定单位
        const range = calculateDateTimeRange(fromDate, value, unit);
        minDateTime = range.min;
        maxDateTime = range.max;
      } else {
        // showTime: false - 前后各推 (value - 1) 个单位
        const rangeOffset = value - 1;
        const range = calculateDateTimeRange(fromDate, rangeOffset, unit);
        minDateTime = range.min.startOf('day');
        maxDateTime = range.max.endOf('day');
      }

      // 判断当前日期是否在动态范围内
      const isInDynamicRange = current.isBetween(minDateTime, maxDateTime, 'day', '[]');

      if (!isInDynamicRange) {
        isDynamicRangeDisabled = true;
      }
    }

    // 2. 检查 disabledDateRanges 限制
    if (disabledDateRanges && validRanges(disabledDateRanges)) {
      for (let i = 0; i < disabledDateRanges.length; i++) {
        if (Array.isArray(disabledDateRanges[i])) {
          const [minDate, maxDate] = disabledDateRanges[i];
          const disabled = disabledFn(current, minDate, maxDate);
          // 如果有命中禁选，标记为禁用
          if (disabled) {
            isDateRangesDisabled = true;
            break;
          }
        }
      }
    }

    // 3. 检查 minDate/maxDate 限制（无论是否有 disabledDateRanges）
    if (minDate || maxDate) {
      const disabled = disabledFn(current, minDate, maxDate);
      // 上述返回为禁选，此处对应的是可选区间，所以取反
      isMinMaxDisabled = !disabled;
    }

    // 4. 合并结果：任一规则禁用则最终禁用
    return isDynamicRangeDisabled || isDateRangesDisabled || isMinMaxDisabled;
  };
  return fn;
}

function genNumberRange(start: numberUndef = 0, end: numberUndef, max: number) {
  const result = [];
  for (let i = start; i <= (end || max); i++) {
    result.push(i);
  }
  return result;
}

export function disabledTimeFn(props: {
  enabledHourRanges?: [numberUndef, numberUndef][];
  enabledMinuteRanges?: [numberUndef, numberUndef][];
  enabledSecondRanges?: [numberUndef, numberUndef][];
  // disabled 优先级更高
  disabledHourRanges?: [numberUndef, numberUndef][];
  disabledMinuteRanges?: [numberUndef, numberUndef][];
  disabledSecondRanges?: [numberUndef, numberUndef][];
  // 动态范围限制
  dynamicRange?: number | string;
}) {
  const {
    enabledHourRanges,
    enabledMinuteRanges,
    enabledSecondRanges,
    // disabled 优先级更高
    disabledHourRanges,
    disabledMinuteRanges,
    disabledSecondRanges,
    // 动态范围参数
    dynamicRange,
  } = props;

  const tempFn = (
    enabledTimeRanges: [numberUndef, numberUndef][] | undefined,
    disabledTimeRanges: [numberUndef, numberUndef][] | undefined,
    max: number,
  ) => {
    let result: number[] = [];
    if (disabledTimeRanges && validRanges(disabledTimeRanges)) {
      // 返回禁用区间
      result = disabledTimeRanges.reduce((arr, item) => {
        const temp: number[] = genNumberRange(item[0], item[1], max);
        return [...arr, ...temp];
      }, []);
    } else if (enabledTimeRanges && validRanges(enabledTimeRanges)) {
      // 返回可用区间
      result = enabledTimeRanges.reduce((arr, item) => {
        const temp: number[] = genNumberRange(item[0], item[1], max);
        return [...arr, ...temp];
      }, []);
      // 加工为禁用区间
      result = Array.from({length: max})
        .map((item, index) => index)
        .filter(item => !result.includes(item));
    }
    return result;
  };

  const disabledHours = tempFn(enabledHourRanges, disabledHourRanges, 24);
  const disabledMinutes = tempFn(enabledMinuteRanges, disabledMinuteRanges, 60);
  const disabledSeconds = tempFn(enabledSecondRanges, disabledSecondRanges, 60);

  const fn = (date: Dayjs, partial: 'start' | 'end', info: { from?: Dayjs }) => {
    let dynamicDisabledHours: number[] = [];
    let dynamicDisabledMinutes: number[] = [];
    let dynamicDisabledSeconds: number[] = [];

    // 1. 计算动态范围时间限制
    if (dynamicRange && info?.from && date) {
      // 确保 from 是有效的 dayjs 对象
      const fromDateTime = dayjs.isDayjs(info.from) ? info.from : dayjs(info.from);

      // 解析动态范围参数
      const { value, unit } = parseDynamicRange(dynamicRange);

      // 计算精确的起始和结束时间点
      const range = calculateDateTimeRange(fromDateTime, value, unit);
      const startDateTime = range.min;
      const endDateTime = range.max;

      const currentDate = date.format('YYYY-MM-DD');
      const startDate = startDateTime.format('YYYY-MM-DD');
      const endDate = endDateTime.format('YYYY-MM-DD');

      if (currentDate === startDate) {
        // 起始边界日期：只能选择 >= startDateTime 的时间
        const startHour = startDateTime.hour();
        const startMinute = startDateTime.minute();
        const startSecond = startDateTime.second();
        const currentHour = date.hour();
        const currentMinute = date.minute();

        // 禁用小于起始时间的小时
        dynamicDisabledHours = Array.from({length: startHour}, (_, i) => i);

        // 如果当前小时等于起始小时，限制分钟
        if (currentHour === startHour) {
          dynamicDisabledMinutes = Array.from({length: startMinute}, (_, i) => i);

          // 如果当前分钟也等于起始分钟，限制秒
          if (currentMinute === startMinute) {
            dynamicDisabledSeconds = Array.from({length: startSecond}, (_, i) => i);
          }
        }
      } else if (currentDate === endDate) {
        // 结束边界日期：只能选择 <= endDateTime 的时间
        const endHour = endDateTime.hour();
        const endMinute = endDateTime.minute();
        const endSecond = endDateTime.second();
        const currentHour = date.hour();
        const currentMinute = date.minute();

        // 禁用大于结束时间的小时
        dynamicDisabledHours = Array.from({length: 24 - endHour - 1}, (_, i) => endHour + 1 + i);

        // 如果当前小时等于结束小时，限制分钟
        if (currentHour === endHour) {
          dynamicDisabledMinutes = Array.from({length: 60 - endMinute - 1}, (_, i) => endMinute + 1 + i);

          // 如果当前分钟也等于结束分钟，限制秒
          if (currentMinute === endMinute) {
            dynamicDisabledSeconds = Array.from({length: 60 - endSecond - 1}, (_, i) => endSecond + 1 + i);
          }
        }
      }
    }

    // 2. 合并常规时间限制和动态范围限制
    return {
      disabledHours: () => [...new Set([...disabledHours, ...dynamicDisabledHours])],
      disabledMinutes: () => [...new Set([...disabledMinutes, ...dynamicDisabledMinutes])],
      disabledSeconds: () => [...new Set([...disabledSeconds, ...dynamicDisabledSeconds])],
    };
  };
  return fn;
}

// 此属于锦上添花，不是强功能
// shortcuts 选择时间点
// ranges 选择时间范围
// 参考 amis-ui/src/components/DatePicker.tsx
// now today yesterday thisweek thismonth prevmonth prevquarter thisquarter tomorrow endofthisweek endofthismonth endoflastmonth
const availableShortcuts = {
  now: {
    label: '现在',
    date: (now?: Dayjs) => {
      return dayjs();
    },
  },
  today: {
    label: '今天',
    date: (now?: Dayjs) => {
      return dayjs().startOf('day');
    },
  },

  yesterday: {
    label: '昨天',
    date: (now?: Dayjs) => {
      return dayjs().add(-1, 'day').startOf('day');
    },
  },

  // https://day.js.org/docs/zh-CN/get-set/weekday
  thisweek: {
    label: '本周一',
    date: (now?: Dayjs) => {
      // return dayjs().startOf('week').startOf('day');
      return dayjs().weekday(1);
    },
  },

  thismonth: {
    label: '本月初',
    date: (now?: Dayjs) => {
      return dayjs().startOf('month');
    },
  },

  prevmonth: {
    label: '上个月初',
    date: (now?: Dayjs) => {
      return dayjs().startOf('month').add(-1, 'month');
    },
  },

  prevquarter: {
    label: '上个季度初',
    date: (now?: Dayjs) => {
      return dayjs().startOf('quarter').add(-1, 'quarter');
    },
  },

  thisquarter: {
    label: '本季度初',
    date: (now?: Dayjs) => {
      return dayjs().startOf('quarter');
    },
  },

  tomorrow: {
    label: '明天',
    date: (now?: Dayjs) => {
      return dayjs().add(1, 'day').startOf('day');
    },
  },

  endofthisweek: {
    label: '本周日',
    date: (now?: Dayjs) => {
      // return dayjs().endOf('week');
      return dayjs().weekday(7);
    },
  },

  endofthismonth: {
    label: '本月底',
    date: (now?: Dayjs) => {
      return dayjs().endOf('month');
    },
  },

  endoflastmonth: {
    label: '下个月底',
    date: (now?: Dayjs) => {
      return dayjs().add(-1, 'month').endOf('month');
    },
  },
};

const availableRanges = {
  'today': {
    label: '今天',
    startDate: (now?: Dayjs) => {
      return dayjs().startOf('day');
    },
    endDate: (now?: Dayjs) => {
      return dayjs().endOf('day');
    },
  },

  'yesterday': {
    label: '昨天',
    startDate: (now?: Dayjs) => {
      return dayjs().add(-1, 'day').startOf('day');
    },
    endDate: (now?: Dayjs) => {
      return dayjs().add(-1, 'day').endOf('day');
    },
  },

  'tomorrow': {
    label: '明天',
    startDate: (now?: Dayjs) => {
      return dayjs().add(1, 'day').startOf('day');
    },
    endDate: (now?: Dayjs) => {
      return dayjs().add(1, 'day').endOf('day');
    },
  },

  // 兼容一下错误的用法
  '1daysago': {
    label: '最近 1 天',
    startDate: (now?: Dayjs) => {
      return dayjs().add(-1, 'day').startOf('day');
    },
    endDate: (now?: Dayjs) => {
      return dayjs().add(-1, 'day').endOf('day');
    },
  },

  '3daysago': {
    label: '最近 3 天',
    startDate: (now?: Dayjs) => {
      return dayjs().add(-3, 'day').startOf('day');
    },
    endDate: (now?: Dayjs) => {
      return dayjs().add(-1, 'day').endOf('day');
    },
  },

  '7daysago': {
    label: '最近 7 天',
    startDate: (now?: Dayjs) => {
      return dayjs().add(-7, 'day').startOf('day');
    },
    endDate: (now?: Dayjs) => {
      return dayjs().add(-1, 'day').endOf('day');
    },
  },

  '30daysago': {
    label: '最近 30 天',
    startDate: (now?: Dayjs) => {
      return dayjs().add(-30, 'day').startOf('day');
    },
    endDate: (now?: Dayjs) => {
      return dayjs().add(-1, 'day').endOf('day');
    },
  },

  '90daysago': {
    label: '最近 90 天',
    startDate: (now?: Dayjs) => {
      return dayjs().add(-90, 'day').startOf('day');
    },
    endDate: (now?: Dayjs) => {
      return dayjs().add(-1, 'day').endOf('day');
    },
  },

  'prevweek': {
    label: '上周',
    startDate: (now?: Dayjs) => {
      return dayjs().startOf('week').add(-1, 'weeks');
    },
    endDate: (now?: Dayjs) => {
      return dayjs().startOf('week').add(-1, 'day').endOf('day');
    },
  },

  'thisweek': {
    label: '本周',
    startDate: (now?: Dayjs) => {
      return dayjs().startOf('week');
    },
    endDate: (now?: Dayjs) => {
      return dayjs().endOf('week');
    },
  },

  'thismonth': {
    label: '本月',
    startDate: (now?: Dayjs) => {
      return dayjs().startOf('month');
    },
    endDate: (now?: Dayjs) => {
      return dayjs().endOf('month');
    },
  },

  'thisquarter': {
    label: '本季度',
    startDate: (now?: Dayjs) => {
      return dayjs().startOf('quarter');
    },
    endDate: (now?: Dayjs) => {
      return dayjs().endOf('quarter');
    },
  },

  'prevmonth': {
    label: '上个月',
    startDate: (now?: Dayjs) => {
      return dayjs().startOf('month').add(-1, 'month');
    },
    endDate: (now?: Dayjs) => {
      return dayjs().startOf('month').add(-1, 'day').endOf('day');
    },
  },

  'prevquarter': {
    label: '上个季度',
    startDate: (now?: Dayjs) => {
      return dayjs().startOf('quarter').add(-1, 'quarter');
    },
    endDate: (now?: Dayjs) => {
      return dayjs().startOf('quarter').add(-1, 'day').endOf('day');
    },
  },

  'thisyear': {
    label: '今年',
    startDate: (now?: Dayjs) => {
      return dayjs().startOf('year');
    },
    endDate: (now?: Dayjs) => {
      return dayjs().endOf('year');
    },
  },

  'prevyear': {
    label: '去年',
    startDate: (now?: Dayjs) => {
      return dayjs().startOf('year').add(-1, 'year');
    },
    endDate: (now?: Dayjs) => {
      return dayjs().endOf('year').add(-1, 'year').endOf('day');
    },
  },
};

/**
 * 修改 availableRanges 中， 近 xx 天，时未包括当天。
 */
const availableRangesIncludeToday = {

  // 兼容一下错误的用法
  '1daysago': {
    label: '最近 1 天',
    startDate: (now?: Dayjs) => {
      return dayjs().add(-24, 'hours');
    },
    endDate: (now?: Dayjs) => {
      return dayjs().endOf('day');
    },
  },

  '3daysago': {
    label: '最近 3 天',
    startDate: (now?: Dayjs) => {
      return dayjs().add(-2, 'day').startOf('day');
    },
    endDate: (now?: Dayjs) => {
      return dayjs().endOf('day');
    },
  },

  '7daysago': {
    label: '最近 7 天',
    startDate: (now?: Dayjs) => {
      return dayjs().add(-6, 'day').startOf('day');
    },
    endDate: (now?: Dayjs) => {
      return dayjs().endOf('day');
    },
  },

  '30daysago': {
    label: '最近 30 天',
    startDate: (now?: Dayjs) => {
      return dayjs().add(-29, 'day').startOf('day');
    },
    endDate: (now?: Dayjs) => {
      return dayjs().endOf('day');
    },
  },

  '90daysago': {
    label: '最近 90 天',
    startDate: (now?: Dayjs) => {
      return dayjs().add(-89, 'day').startOf('day');
    },
    endDate: (now?: Dayjs) => {
      return dayjs().endOf('day');
    },
  },
};

type AvailableShortcutsKeys = keyof typeof availableShortcuts | {
  key: string
  label: string
  date: string
};
type AvailableRangesKeys = keyof typeof availableRanges | {
  key: string
  label: string
  startDate: string
  endDate: string
};

export function getAvailableShortcuts(presets: AvailableShortcutsKeys[]) {

  // presets 非数组时 直接不处理
  if (!Array.isArray(presets)) {
    return undefined
  }

  const presetItems = presets
    .flatMap(presetItem => {
      let current = null

      // 配置 相对快捷设置字符串， 从 availableShortcuts 中获取
      if (typeof presetItem === 'string') {
        const dateItem = availableShortcuts[presetItem];
        if (dateItem) {
          current = {
            key: presetItem,
            label: dateItem.label,
            value: dateItem.date(),
          };
        }
      } else if (isPlainObject(presetItem)) {
        // #822： 支持自定义配置preset
        let { date, label, key } = presetItem
        // 解析 date 数据，此处没有使用 数据域data解析
        if (isPureVariable(date)) {
          date = resolveVariableAndFilter(date)
        }
        // date 存在时
        if (date) {
          current = {
            key,
            label,
            // 将非 dayjs 对象 转为 dayjs
            value: dayjs.isDayjs(date)
                ? date
                : dayjs(date)
          }
        }
      }

      if (current?.value) {
        //  #779: 给 value, hack 一个 presetValue 属性，用于 onChange 事件
        // @ts-ignore
        current.value.presetValue = current.key;
      }

      return current ? [current] : [];
    })

  // 将 key 相同的 preset 进行排除，防止 presetValue 设置值时功能异常。
  return uniqBy(presetItems, 'key')
}

export function getAvailableRanges(presets: AvailableRangesKeys[], presetsIncludeToday?: boolean) {

  // presets 非数组时 直接不处理
  if (!Array.isArray(presets)) {
    return undefined
  }

  const presetItems = presets
    .flatMap((presetItem) => {
      let current = null

      // 配置 相对快捷设置字符串， 从 availableRanges 中获取
      if (typeof presetItem === 'string') {
        const ranges = presetsIncludeToday ? {
          ...availableRanges,
          ...availableRangesIncludeToday,
        } : {...availableRanges};

        const rangeItem = ranges[presetItem]
        if (rangeItem) {
          current = {
            key: presetItem,
            label: rangeItem.label,
            value: [rangeItem.startDate(), rangeItem.endDate()],
          };
        }
      } else if (isPlainObject(presetItem)) {
        // #822： 支持自定义配置preset
        let { startDate, endDate, label, key } = presetItem
        // 解析 startDate, endDate 数据，此处没有使用 数据域data解析
        if (isPureVariable(startDate)) {
          startDate = resolveVariableAndFilter(startDate)
        }
        if (isPureVariable(endDate)) {
          endDate = resolveVariableAndFilter(endDate)
        }

        // 开始结束时间都存在时
        if (startDate && endDate) {
          current = {
            key,
            label,
            // 将非 dayjs 对象 转为 dayjs
            value: [
              dayjs.isDayjs(startDate)
                ? startDate
                : dayjs(startDate),
              dayjs.isDayjs(endDate)
                ? endDate
                : dayjs(endDate)
            ]
          }
        }
      }

      if (current?.value) {
        //  #779: 给 value 数组 hack 一个 presetValue 属性，用于 onChange 事件
        // @ts-ignore
        current.value.presetValue = current.key;
      }

      return current ? [current] : []
    })

  // 将 key 相同的 preset 进行排除，防止 presetValue 设置值时功能异常。
  return uniqBy(presetItems, 'key')
}
