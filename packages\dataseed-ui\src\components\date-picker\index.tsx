import React from 'react';
import {DatePicker, ConfigProvider} from 'antd';
import type {DatePickerProps} from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import {
  getAvailableShortcuts,
  getAvailableRanges,
  dayjsFormat,
  dayjs2str,
  disabledDateFn,
  disabledTimeFn,
} from './helper';
import type {Dayjs} from 'dayjs';

const RangePicker = DatePicker.RangePicker;

// 根据 picker 设置默认的 format  time | date | week | month | quarter | year
// 年/季度/月/年第几周/星期几/天T时:分:秒
// 'YYYY/[Q]Q/MM/年第w周/星期d/DDTHH:mm:ss'
const pickerFormat = {
  time: 'HH:mm:ss',
  date: 'YYYY-MM-DD',
  week: 'YYYY-w',
  month: 'YYYY-MM',
  quarter: 'YYYY-[Q]Q',
  year: 'YYYY',
};

// prettier-ignore
const antdFilterWrite = [
  // 简单处理即可
  'allowClear',
  'autoFocus',
  'bordered',
  'className',
  'disabled',
  'disabledDate',
  'format',
  'inputReadOnly',

  'defaultValue', // amis 中被 value 覆盖
  // 'defaulPickerValue',
  'value',

  // 'locale',
  'open',
  'picker',
  'placeholder',
  'placement',
  'presets',
  'size',
  'status',
  'onOpenChange',
  'onPanelChange',

  // DatePicker API
  'showNow',
  'showTime',
  'showToday',
  // 'value',
  'onChange',
  'onOk',

  // RangePicker API
  'allowEmpty',
  'disabledTime',
  'onCalendarChange',

  // custom API
  'minDate',
  'maxDate',
  'disabledDateRanges',
  'dynamicRange',
  'static',
  'resetValue',

  // custom timePicker API
  'hideDisabledOptions',
  'enabledHourRanges',
  'enabledMinuteRanges',
  'enabledSecondRanges',
  'disabledHourRanges',
  'disabledMinuteRanges',
  'disabledSecondRanges',
  'static',
];

const antdApiFilter = (
  rest: Record<string, any> = {},
  filter: string[] = antdFilterWrite,
) => {
  return Object.keys(rest).reduce((obj: Record<string, any>, key: string) => {
    if (filter.includes(key)) {
      if (typeof rest[key] !== 'undefined') {
        obj[key] = rest[key];
      }
    }
    return obj;
  }, {});
};

interface DsBaseDatePickerState {
  staticValue?: string | Dayjs | (string | Dayjs)[] | null;
  value?: Dayjs | Dayjs[] | null;
}

interface DsDatePickerProps {
  static?: boolean;
  presetValue?: string;
  dynamicRange?: number | string; // 动态范围限制，支持数字(天数)或字符串格式
  [propName: string]: any;
}

type IProps = DatePickerProps & DsDatePickerProps;

class DsBaseDatePicker extends React.Component<IProps, DsBaseDatePickerState> {
  latestPresetValue: string = ''; // 保存最新的 presetValue, 防止多次触发 onChange

  constructor(props: DatePickerProps) {
    super(props);

    const { presetValue, presetsIncludeToday, presets, setPrinstineValue } = this.props
    const restProps = this.getRestProps();
    const {format} = restProps;
    const val = this.getValue();
    const dayjsValue = dayjsFormat(val, format);
    const staticValue = dayjs2str(dayjsValue, format);
    this.state = {
      staticValue,
      value: dayjsValue,
    };

    const presetItems = this instanceof DsDateRangePicker
      ? getAvailableRanges(presets as any, presetsIncludeToday)
      : getAvailableShortcuts(presets as any)
    const matchedPreset = presetItems?.find((item) => item?.key === presetValue)
    if (matchedPreset?.value) {
      const value = dayjs2str(matchedPreset?.value, restProps.format);
      // #779: 新增 presetValue 设置日期组件的默认值
      // #1223: 使用setPrinstineValue设置初始值，同时不会触发Form层的onChange
      setPrinstineValue(value)
    }
  }

  getValue = () => {
    return this.props.value;
  };

  componentDidUpdate(prevProps: IProps) {
    const restProps = this.getRestProps();
    const {value, format, picker} = restProps;
    const dayjsValue = dayjsFormat(value, format);
    const staticValue = dayjs2str(dayjsValue, format);
    const prevStaticValue = dayjs2str(
      dayjsFormat(prevProps.value, format),
      format,
    );
    if (JSON.stringify(prevStaticValue) !== JSON.stringify(staticValue)) {
      this.setState({
        staticValue,
        value: dayjsValue,
      });
    }
    if (prevProps.presetValue !== this.props.presetValue) {
      this.handleParsePresetValue();

      /**
       * 当 presetValue 变化,且未命中 handleParsePresetValue 调用的 handleChange 时，latestPresetValue 值未更新。
       * 修复： 当 setValue，或者 api返回 更改日期组件的值， presetValue 仅能生效一次。
       */
      this.latestPresetValue = this.props.presetValue || ''
    }
  }

  // #779: 由 props.presetValue 设置日期组件的 value
  handleParsePresetValue() {
    const { presetValue, presetsIncludeToday, presets } = this.props

    // 防止重复更新，详见 http://wiki.caijj.net/pages/viewpage.action?pageId=329571336

    // presetValue存在 且命中presetItem时，调用 handleChange，其他情况不处理
    if (presetValue && presetValue !== this.latestPresetValue) {
      // 根据当前不同的实例， 获取对应的 presetItems
      const presetItems = this instanceof DsDateRangePicker
        ? getAvailableRanges(presets as any, presetsIncludeToday)
        : getAvailableShortcuts(presets as any)

      // 如果存在 匹配的 preset， 回调 handleChange 修改值
      const matchedPreset = presetItems?.find((item) => item?.key === presetValue)
      if (matchedPreset?.value) {
        this.handleChange(matchedPreset.value)
      }
    }
  }

  handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    this.props.onFocus?.(e);
  };

  handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    this.props.onBlur?.(e);
  };

  handleData = async (
    dates: DsBaseDatePickerState['value'],
    fnName: string,
    eventName: string,
    extraData?: object,
  ) => {
    const fn: Function = this.props[fnName];
    const restProps = this.getRestProps();
    const value = dayjs2str(dates, restProps.format);

    fn && fn(value, extraData);
  };

  handleChange = async (dates: null | Dayjs[] | Dayjs) => {
    const extraData = dates?.presetValue
      ? { presetValue: dates.presetValue }
      : undefined;

    this.latestPresetValue = dates?.presetValue || '';

    await this.handleData(dates, 'onChange', 'change', extraData);
  };
  handleOk = async (dates: any) => {
    await this.handleData(dates, 'onOk', 'ok');
  };
  handlePanelChange = async (dates: any) => {
    await this.handleData(dates, 'onPanelChange', 'panelChange');
  };
  handleOpenChange = async (bool: boolean) => {
    this.props.onOpenChange?.(bool);
  };
  getRestProps = () => {
    const {props} = this;
    const {picker = 'date', showTime} = props;

    let defaultFormat: string = pickerFormat[picker] || '';

    if (picker === 'date' && showTime) {
      defaultFormat = 'YYYY-MM-DD HH:mm:ss';
    }

    if (props.picker === 'time') {
      if (showTime?.use12Hours) {
        defaultFormat = 'h:mm:ss a';
      }
      if (showTime?.format) {
        defaultFormat = showTime.format;
      }
    }
    const restProps = antdApiFilter(props);
    restProps.format = props.format || defaultFormat;

    return restProps;
  };

  render() {
    return <></>;
  }
}

class DsDatePicker extends DsBaseDatePicker {
  render() {
    const restProps: any = this.getRestProps();

    // 创建传给 antd 的 props，去掉自定义属性
    const {minDate, maxDate, disabledDateRanges, dynamicRange, ...antdProps} = restProps;

    return (
      <ConfigProvider locale={zhCN}>
        <DatePicker
          disabledDate={disabledDateFn(restProps)}
          disabledTime={disabledTimeFn(restProps)}
          {...antdProps}
          className={`dsdate-input ${antdProps.className || ''}`}
          popupClassName={`dsdate-popup ${antdProps.popupClassName || ''}`}
          value={this.state.value}
          presets={getAvailableShortcuts(antdProps.presets)}
          onChange={this.handleChange}
          onPanelChange={this.handlePanelChange}
          onOpenChange={this.handleOpenChange}
          onFocus={this.handleFocus}
          onBlur={this.handleBlur}
        />
      </ConfigProvider>
    );
  }
}

class DsDateRangePicker extends DsBaseDatePicker {
  handleCalendarChange = async (dates: any) => {
    await this.handleData(dates, 'onCalendarChange', 'calendarChange');
  };
  render() {
    const restProps: any = this.getRestProps();

    // 创建传给 antd 的 props，去掉自定义属性
    const {minDate, maxDate, disabledDateRanges, dynamicRange, ...antdProps} = restProps;

    return (
      <ConfigProvider locale={zhCN}>
        <RangePicker
          disabledDate={disabledDateFn(restProps)}
          disabledTime={disabledTimeFn(restProps)}
          {...antdProps}
          className={`dsdate-input ${antdProps.className || ''}`}
          popupClassName={`dsdate-popup ${antdProps.popupClassName || ''}`}
          value={this.state.value}
          presets={getAvailableRanges(antdProps.presets, this.props.presetsIncludeToday)}
          onChange={this.handleChange}
          onOk={this.handleOk}
          onPanelChange={this.handlePanelChange}
          onOpenChange={this.handleOpenChange}
          onCalendarChange={this.handleCalendarChange}
          onFocus={this.handleFocus}
          onBlur={this.handleBlur}
        />
      </ConfigProvider>
    );
  }
}

export {DsBaseDatePicker, DsDatePicker, DsDateRangePicker};
