<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<?mso-application progid="Word.Document"?>
<pkg:package xmlns:pkg="http://schemas.microsoft.com/office/2006/xmlPackage">
  <pkg:part pkg:name="/_rels/.rels"
    pkg:contentType="application/vnd.openxmlformats-package.relationships+xml" pkg:padding="512">
    <pkg:xmlData>
      <Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
        <Relationship Id="rId3"
          Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties"
          Target="docProps/app.xml" />
        <Relationship Id="rId2"
          Type="http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties"
          Target="docProps/core.xml" />
        <Relationship Id="rId1"
          Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument"
          Target="word/document.xml" />
      </Relationships>
    </pkg:xmlData>
  </pkg:part>
  <pkg:part pkg:name="/word/_rels/document.xml.rels"
    pkg:contentType="application/vnd.openxmlformats-package.relationships+xml" pkg:padding="256">
    <pkg:xmlData>
      <Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
        <Relationship Id="rId3"
          Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/webSettings"
          Target="webSettings.xml" />
        <Relationship Id="rId2"
          Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/settings"
          Target="settings.xml" />
        <Relationship Id="rId1"
          Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles"
          Target="styles.xml" />
        <Relationship Id="rId6"
          Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme"
          Target="theme/theme1.xml" />
        <Relationship Id="rId5"
          Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/fontTable"
          Target="fontTable.xml" />
        <Relationship Id="rId4"
          Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/image"
          Target="media/image1.png" />
      </Relationships>
    </pkg:xmlData>
  </pkg:part>
  <pkg:part pkg:name="/word/document.xml"
    pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml">
    <pkg:xmlData>
      <w:document xmlns:wpc="http://schemas.microsoft.com/office/word/2010/wordprocessingCanvas"
        xmlns:cx="http://schemas.microsoft.com/office/drawing/2014/chartex"
        xmlns:cx1="http://schemas.microsoft.com/office/drawing/2015/9/8/chartex"
        xmlns:cx2="http://schemas.microsoft.com/office/drawing/2015/10/21/chartex"
        xmlns:cx3="http://schemas.microsoft.com/office/drawing/2016/5/9/chartex"
        xmlns:cx4="http://schemas.microsoft.com/office/drawing/2016/5/10/chartex"
        xmlns:cx5="http://schemas.microsoft.com/office/drawing/2016/5/11/chartex"
        xmlns:cx6="http://schemas.microsoft.com/office/drawing/2016/5/12/chartex"
        xmlns:cx7="http://schemas.microsoft.com/office/drawing/2016/5/13/chartex"
        xmlns:cx8="http://schemas.microsoft.com/office/drawing/2016/5/14/chartex"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:aink="http://schemas.microsoft.com/office/drawing/2016/ink"
        xmlns:am3d="http://schemas.microsoft.com/office/drawing/2017/model3d"
        xmlns:o="urn:schemas-microsoft-com:office:office"
        xmlns:oel="http://schemas.microsoft.com/office/2019/extlst"
        xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"
        xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math"
        xmlns:v="urn:schemas-microsoft-com:vml"
        xmlns:wp14="http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing"
        xmlns:wp="http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing"
        xmlns:w10="urn:schemas-microsoft-com:office:word"
        xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"
        xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml"
        xmlns:w15="http://schemas.microsoft.com/office/word/2012/wordml"
        xmlns:w16cex="http://schemas.microsoft.com/office/word/2018/wordml/cex"
        xmlns:w16cid="http://schemas.microsoft.com/office/word/2016/wordml/cid"
        xmlns:w16="http://schemas.microsoft.com/office/word/2018/wordml"
        xmlns:w16du="http://schemas.microsoft.com/office/word/2023/wordml/word16du"
        xmlns:w16sdtdh="http://schemas.microsoft.com/office/word/2020/wordml/sdtdatahash"
        xmlns:w16se="http://schemas.microsoft.com/office/word/2015/wordml/symex"
        xmlns:wpg="http://schemas.microsoft.com/office/word/2010/wordprocessingGroup"
        xmlns:wpi="http://schemas.microsoft.com/office/word/2010/wordprocessingInk"
        xmlns:wne="http://schemas.microsoft.com/office/word/2006/wordml"
        xmlns:wps="http://schemas.microsoft.com/office/word/2010/wordprocessingShape"
        mc:Ignorable="w14 w15 w16se w16cid w16 w16cex w16sdtdh wp14">
        <w:body>
          <w:p w14:paraId="4315A0CE" w14:textId="69E0855B" w:rsidR="00347F72"
            w:rsidRDefault="00347F72">
            <w:r>
              <w:rPr>
                <w:noProof />
              </w:rPr>
              <w:drawing>
                <wp:inline distT="0" distB="0" distL="0" distR="0" wp14:anchorId="6568D1B8"
                  wp14:editId="0F58EF51">
                  <wp:extent cx="1803400" cy="558800" />
                  <wp:effectExtent l="431800" t="0" r="304800" b="0" />
                  <wp:docPr id="1" name="Picture 1"
                    descr="Logo&#xA;&#xA;Description automatically generated" />
                  <wp:cNvGraphicFramePr>
                    <a:graphicFrameLocks
                      xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main"
                      noChangeAspect="1" />
                  </wp:cNvGraphicFramePr>
                  <a:graphic xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main">
                    <a:graphicData uri="http://schemas.openxmlformats.org/drawingml/2006/picture">
                      <pic:pic xmlns:pic="http://schemas.openxmlformats.org/drawingml/2006/picture">
                        <pic:nvPicPr>
                          <pic:cNvPr id="1" name="Picture 1"
                            descr="Logo&#xA;&#xA;Description automatically generated" />
                          <pic:cNvPicPr />
                        </pic:nvPicPr>
                        <pic:blipFill>
                          <a:blip r:embed="rId4">
                            <a:extLst>
                              <a:ext uri="{28A0092B-C50C-407E-A947-70E740481C1C}">
                                <a14:useLocalDpi
                                  xmlns:a14="http://schemas.microsoft.com/office/drawing/2010/main"
                                  val="0" />
                              </a:ext>
                            </a:extLst>
                          </a:blip>
                          <a:stretch>
                            <a:fillRect />
                          </a:stretch>
                        </pic:blipFill>
                        <pic:spPr>
                          <a:xfrm rot="2958604">
                            <a:off x="0" y="0" />
                            <a:ext cx="1803400" cy="558800" />
                          </a:xfrm>
                          <a:prstGeom prst="rect">
                            <a:avLst />
                          </a:prstGeom>
                        </pic:spPr>
                      </pic:pic>
                    </a:graphicData>
                  </a:graphic>
                </wp:inline>
              </w:drawing>
            </w:r>
          </w:p>
          <w:sectPr w:rsidR="00347F72">
            <w:pgSz w:w="12240" w:h="15840" />
            <w:pgMar w:top="1440" w:right="1440" w:bottom="1440" w:left="1440" w:header="708"
              w:footer="708" w:gutter="0" />
            <w:cols w:space="708" />
            <w:docGrid w:linePitch="360" />
          </w:sectPr>
        </w:body>
      </w:document>
    </pkg:xmlData>
  </pkg:part>
  <pkg:part pkg:name="/word/media/image1.png" pkg:contentType="image/png" pkg:compression="store">
    <pkg:binaryData>iVBORw0KGgoAAAANSUhEUgAAARwAAABZCAYAAADsFpuLAAAAAXNSR0IArs4c6QAAAERlWElmTU0A
      KgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAABHKADAAQAAAAB
      AAAAWQAAAAAb/JV6AAAmFUlEQVR4Ae2dCZwfRZXHqyYT7isooK4gKJdIuLw5E0QUFDTIuqALKios
      d4AokPlP8k9mJiFckgCuoNy6ygoi67ULCIgHrBxCQEBkIYCCyH2EgJNM7ffXPZ35/2f+fVX3P5lA
      13x6urvq1avX79/1+tWrV6+MWdGSc3ZFI7mit+JAxYGQA6O/80rAdJtPQO7nOHbjWN848xfO1xhr
      vm967Y1cV6niQMWBFYADo1vgTHP7mCVmJnzcLoGXd5gxwMy0VyfAVEUVByoOjAIOjE6B0+12MgPm
      TPjzgRw8usGMNcebur0zR50KtOJAxYFlyIHRJXC63IYMk+YwZDrQiwcWMWXMXIZZx3vVrypVHKg4
      0FYOdLYVe1bkdbeaWWy+hqA5kWPVrNWa4EJhc5HpNKc05Vc3FQcqDowaDix/DafbHYCQOZVjQ2+u
      WPM7NKNjTI+93RtHVbHiQMWBtnNg+QmcunsfWs1cBM2OBZ7yr9Q90fTZ7xXAUVWtODC6ONDt3kO/
      2AOidH4A2+T12CbvGF1E+lGz7AVO3b0FQTMLRn4Rkn3bf810mDPMWuD5ml3o9+hVrYoDo4wDdddJ
      36jTN06CsjEN1Dmu55oNyD/GvtaQv8JdLjsbzjy3svm7mWz6TRdcWtObU9b8GDvNCUj8h7xxnOlW
      NW83/zCftUu8cVQVKw6UzYFQ2Kh/DE/6ME82TwWTIicML1yR7n01jHzPOM1Nwp/mdCq9M1/FJuh7
      0YeOZQbquqbcvDdd7sDBmbAXBvFdnxdFBV9xoHQO1Ny7wTkf7SZeCdDEiMUnrcfeXXr7ywhh/MOV
      QUC3Gw+L5iJsJnqjs+Z5mDwdBfObaDWLvfHU3QdQV8/iB/0wh9KGnH9putyVg/47j3rjripWHCjO
      gT0ThY3wOwwJJrDtVAKnid9192Y690yEzaHkN45Fm8ASb8Jp7m8j72sImqcTYZMKe90/mVex9fSb
      gwBrpdF9hrK9Tc3Npq3TaOvVJHRVWcWBNnFgfCa8zmydCW6UApWr4ZznxrLK6UiEzXSk8Trez2zN
      r+n8x9D5/b2GZad51kwxi5jFMmb1FFpWhd6Z0P0lBM9khm3/lQJfFVccKJcDzizIhNCaFVoTL0/g
      THN7wQotR9gyE+NaAVnzGDrI1xmj/qBVceY8+fY8G3gsb5S5jgCd2YT/VyN0fo7AOxaB92Cu+hVw
      xQFfDowxv8X0kJ468DlbgVOrIUa+x6m7LRmSSNDsla9iA7Rl0GMYzshLuG5faSjJd9nt3o/QkJ2m
      iG9P1Kam3k8z4xiOHW8XRZnVueJA2zjQ5X4E7kmx+K35Odq3IiessMlf4NTdOgia6Tz5kRxjvTlg
      zY8QNJrmXuCNo+7exnBIvj0Hg8P/mVoTsAAr1ORqNXpr5lS5JXIgtH1+n/d4jxFYLRrQquafTZd9
      YkTZCpSRv3P+pxtj7jZfhSk9HG/2flZr/ogGcSwd+ZfeOOpuFQTNCdBxMjjS7DTezQxW/BliVXYl
      f/+fohRU9V//HFD8p2nm87zTH+VhNVX+Zz6hN/DRu5B3b2BFZ0A+gSM7zQDDDIfLtX96DgZOR6v5
      dxjoP83d7T4LHVqD9Q5/UnLWDId+swgBduqK7vGZ88kr8IoDpXAgm8DpdtvSsU/nGKnqZSWjrGnu
      bvde6JCdZuesTY+ACwVHJzh8jeYP8MU5opB2NoKoKqPiwOufA8kCR7aRftOHRnIwnVNOR77pNoYj
      h6PR3OaLwNTd+oN2mkPAkUx3UiPW/Cdi5kRwrcwznQboPkngiWUKcbqqOY5x9ZOJcFVhxYGKAwEH
      WndcLSLrx15hTJ2jyLoneQl3oQ18y3v8Kd+eR83RwTDMsVzTN1lzOwJmMivLf9OEouZ25/5MyrZt
      ys96E3pCn4gXz7eNtaEPc9a6FVzFgTcYB0YKHC2NHzD/AR+2KcQLay5Dk5iCoPm7N56a+zh1NXza
      whuHMU+gm01FIFwSKxBkCL8L47MByqD5+CTNIhiM6b32Pp/qVZ2KA28EDjQLnG53KMLmLB58Ve+H
      1+yTw77RZ2/yxlF3mzLk+QZ4PumNI7TTnIHQk2/Py5nwaAGdMxcDmyeW8hBqywp0Y3ppczZt+hvE
      hzDmu9IMxwyEc7/5IBrheJ7lTSAYx/U4rvu5fpbrZwfP96B5/gY6H83XSBuhJfjnm11oQdrmWzne
      MnjW8PdFaH+as+j+g/knaD/M6pmKpXCovitI9JtvAn7xS8txtJzmqeDseKdXN9eZqfYZ7t84SSF/
      O9gpxeHMq3fJBrPS4cy0M1r8LB79keNusy4f3Az+akMCp8spBsfsAtxUXJqZZiMEhe+LMMetaV5m
      7ZSGPs6s5E2LNZdTX4G5HsmNI9R2rqJeEdvOHxA6/0pnvjd3+z4VutzO/PiHUHUSz51vSYm8u8Mt
      d+Yul1XIEpLTWcs2YD4DHftAf1ZXC812yofrdPh8fy62hW1+mjb/jfY+Qt309X6a9HDmDmCvxshw
      tjnJvpCrzSzANacwu2F4CmsWUOU2nm8Wz7fsXDHqbjs+9kfQ9h7QskkWsgMYa+Swew0C6nwmU34R
      Vy8UOHW3Dd8/rVsaEkBxNeLzX6L2DBg0Fwbl+7rrBahhmLZoIy74qsW3klxyGw88mY6j4Y1fCm1G
      P6Typ/wQDNay5nvw4mB40T7fiWnuI7jDSyPduhCtQ5X/B/7NgH83D2W18arbfZBuPI8W/DRKkRYK
      gouZlDgOXr+YSm3Y5reA2y4VNh7gGdqdjXvEOaW6R9RcN++/hvWNaSG/yaH8JjJztC9Ncx/jXdK6
      w4mFG7FooJZJoh77v8NxhQKm5jTlLRtGGelevhdHIeVuyIQsDBsxj/Y/mAm+FZA1j1N/KoOZS2Pt
      NK3qDc+rOUn1s8n2Xw+mmCahZ3K25x9OQ5b7uluDr9AF0PrZLOA5YbSipwYv5xTiZVKj0mRforO6
      2BX8SbXjyhYgdCYhdPThbJ1q7kgKNEHgrz03Y74H48PHTc3+tTnb8661wJFQfYWP13ierXxNp+7W
      4l06G54c7El162oWrOEo48xGgEjg3Ejhbo0Fha81ZRwajR9viUuhRvuDIdwXKPfTrGSnsajUa6EZ
      FQk1WncbQYsYI7XeL2k8qz1CxzNb1c5Iggq3scj8FCKLfKGzPONP6MD785LLLlVemuXWo/v8gvft
      veUhHcSkGcNO1vTV7S1NuE9zq2NxOJ82P9eUX8ZNuHp7TyYL/lQYXZzACRFfhYlgv8JtNCLodtvD
      k6s43tGYXep1R6DpSKMMUiRwHqPRt0eZpZ0tFhmHir4Rw6zIrlN3KyH7jiW/m3aKTLlfzsv1dV4u
      f6Onwp4+yfY0lqURzqzm+dz91D/HrMFztmNc30hUGHLjZmj1m8JvxJXt+kJe8i9nA80AFQr2a4Hc
      PAO0H4glkO1qDDGnWhl8jQkN0dfAs939EGaq9RRtblt4nVOywFlEP1p7aT/KRFYCUDgb/Ssg3pQA
      VbxImk4nExmD2lnHIMb1i2NugcHRDbUK/FHsQ9PcRI5P0Pw9/Pinku8nbKy5lTHtznxRDigkbGru
      k7yasrBrTZivsNH6qq2h5fi2Cxux95nA23tZCRu1eAihOjQMKZ4kLPvRbNopbESlw7KyEC0zSvPR
      otsrbNTSemid50dNtum8Kg4eG5SCu8u9A55cB672ChsRK2/+xSgGg6kTD145+aWNaZ8DXlb5jQfr
      5T1thUHq+ryVmuCt+Rv3UxEPFxeyLdTd5jBAU+57N+HPc2PNfYArUNc1eaoVgg1XxH8FupPSIrQt
      Gcw18yR+PQH838hTvoT/Ohhpt+C8Lfc7UZb2u+uFOc30uSsKe1NLWBqzFUd6kmuFMbdA41O0/zzX
      6hhv49AUbRZN/FMISmlmLwD/Nc5JydGOPoL6kD0NfzT7tTIV1uMYz7EjZelLYOTC0e2+iKH0Yuq0
      Jy0ubYHyeTzTWzISqenve4HVTODfORZSV6Fb1uOs30THplwn/S4TgAmSGBlpOYNZLU6aotsQo+5j
      wRKHqUC8swVUe7JC35a56EM95kT7EsZMv1R36yJYp3McDoKxXkhCH5Y6r1+xhac+jS8x+/GjjhQQ
      sluYwKZzFXT9N1rfK5nQz3Jvwpbyr8DWwbtOQp1VEVfHUX5SAkxykbRJ+WYlJanexqgjzEGQS2CO
      TJrNnBYIAIUi2XUkQFPOWU13w2/EN8dWQ2MDL/inhxcvvT/FrY1h4CjuTwJeQjs+ifbz3PdKG/bE
      t+RfokXPA+ZjiQhCg++lSIZLmQD5Le+UfpvkFMYvlyuJjg81ATvkRd11gGfAGqm6zwRz6E0wTTfW
      /C8vQYgk3DvnIMo1hbdJE1zZNzboSMfT9p+9UUdhTwd4VeUE55PCH+BbvJzTYZoc55Z96nJn06he
      /Cj180Job67eQgbzPvdWBMrV/JbvjxC3OL/Es2/Es0u45UsSEt1ohEne4jK4d9IJsm72Fgqeo+k4
      EiqhHTIfVbehK+y91M6TpW7NySlQWvrGieCWmDW99opEmLjCZBuOnnRLcBczTnc5aY/xmmaovU8q
      1E6X2xFa9VEYmojawKwiF4IOhjrpGo4JPGhDNkna9dqLeEG2BOnxHO3ogPeDdy/a2YfDX9jU3L5o
      ZX/kxfwGxPsKm2t41m0xnh693ISNOG8b1GnLiz+WpSc99uRCwkZ4FdBpNfMJ8D+i25i0JprhXjFl
      ydnTGWokCRvDBLk1EzILG7WmNWs9dh5vbqMATqYjKpWPyFoYkCOjcpSfdu61D8NzdaBnEkFdsHFA
      IshyK1TUh2Rh8xfeddlHiwm1Pvs7cExAO9qb31ZD+6Wpg86YReC8trRGdKHp0l77DbrBpiA9g+yR
      MBFs9rPGi8fzLd0G3P+dvdowSHlLdrnredH15d5sWGm2W0vgozFmX+jQl1dj2OWbHE+iZFFze/AC
      zetdm0S9Op9cL5OSbfhaJcENLxtgPV1S6kDz7LH66uZPPfab0P2DHBWXAP/VYGieo9JSUM2IdqQO
      Lfcwcl0YjcmhfSUlF8z6lqdAyOO4M3B/eCBqtgN1ekx0k3D+R2zZyfY5OuUUfsh3c/ipkmGsnO8g
      vDYLhFg0hR7baExBn9sAY+F3sAbcDsTEGKi0bAm9Kdis3oPz4k/SgGPLJfS63Wmx5T4FCq2xDTNH
      7ViVPh6/KU0px6cJ8UUxJTX3LsRkkq3lIcyyGir6J4WnlftFliTh1GP1bvinmThcGvb7jk8y0e8Y
      X7xcS96d0Ho/GtxVCeV+RXX7OHjHRx7Z0m6yaDjxAiciQypnr5UE3Y0X4A9RdurZsgjPmvcxZPlq
      bjU3Qi5/Gq0FewWtxJkvc2R5pqh2eA6F3vmDQu8Mb8OfFgPW3PmB0HPBQsTmdnzvOggxuSFG3nY5
      FQqvM7+KJU/DIjnQ5Uu7J4J3mO8Wfh690Ia1c1lSB8tNiiYJextEU4jH1MH7PDrThrFkWWY027Un
      W4PzaHkCJ3oSrRLvhOEddPxh47cIJDhr0WCHORAhtQtfnewCqgkJNzW3P99lTVPP5lhzeHGme2tu
      hNYdEHqHeQs9OTR2uSkIGgk9xXzOL/SSiNX6MF/NLwlvY1nah+L5zAsrI6wToouW5zEZBUXLyg2Z
      CoWSnl7iF7k2HSwDRKf5YSKUG7UCZ90EukfOgCYA+xapUwxkqJyu4TQi0YLFHnsh3X9zfuRTKBqy
      74TLEXoQSlsCk2f83diCYZptB4TNr+jYP+TYpLkw4501DzOg3B+hNxFa7spYaySYjNP9ge+I4j2v
      NRJgBcmxKQbRDoIQ5EmNsxQj673EnJ8+FMXTGDZOTBtWydcmy/RuNmo0qRHvfuDM9tnQLHOoePuM
      /HJqbot2UySBkz7H7hpmqfJQJL8ZzaTIvmPMlZyvoENqam8aP378D5bUhtZgdbkLoPpWcCXZB+Kx
      6OW0OBGuD10z7ZXxgCkldbc1P9K10HE1kJumQI/+4oHAuTOezjC+Tnx5Y4ncEQxRa+KSBEBZtqhw
      Rf6dcU0F+YppU1YK20vCN44PonzcRlt6MoWgC4wW1rYxdWKzWBK4jiU3MgkD6B/QBhQqNF1ADccl
      +45BkyiStCbm7mAb4R7QrEUn90mqdQna1ck8R9N0XS5kcppbSBiBfnMY9bIY3XOhX47AydpuB29L
      1vR06vAryfCatZVGOGmoOzdmNF3LebXcJHeN98eiXIl1T2lT6LGV21TgAi/0T8Zid3ifv0jMn2nY
      Q2cQa6isD0JDg510nCUN93GX4xh4nc1xOF/049BQrokDbEu+YpjMx7u3iKoql/9OliMUC+SudSFH
      wrPpPOe4tjxr2UjlYb2E6HljGOAuRlB3cF6Cx2xHC0Ep24OfIB9JdX+wNGBkfpSjiHFlJpc4w6aW
      ss1kZaVJEQiT0j9GocDpMD+jD8vWmZQ25f24AmfNh+nrP2AkcCWf1jvKEj6dvIpLWFyZNW3FC/k/
      EPJTOq92yyz7K9VMx2w3js49GyYdSoFtLsx4p/ABlsBCRexFakp7coUhLLbM2PKyB9Ms0os48bkg
      oJVCjI6H5rcGhES6S/R5ie7bReXiFHuPxeGvzGSDdVNJGMsVOKI/STgvCQROEj3LvqzH3k3f/S/o
      3je18dAuqigKJ+Oh9RT1fkkdTQj9lrPsYV5vUCeTYdErmErDUgAtVOvHFb3mzoGAmTSe3+V9KbKY
      iy73Bb5J8mNZLwYiOVtGPa1tWRccGWKtxiKL9k5f4ulpG4u4pIJw8e0ksP0LQ+O9Oa9aEuZiaKRB
      Jb1ZrmSNI00AyDum3LQwEd1AjuFnIqLSC6eAcReOPBq6FmoeQJ0D6Pf69L9I37+Jq+u4vo6PeZI9
      C7ChxGpxJFVXkqgeAh52NRYijkNNPwj7TnmBp+puK3Bq+LTrsPay3oZ+Ep14hNbtX7JWGgEXalfT
      gyGUQayOxjTN7QN9Esptn11ow+N7fSET6CgbX0JTQVFyex2eGnlaq0XLtVSoi8W0ij/suz12OBMr
      HFq2wvIYdz//L2KJzGVpMYE0SyXXv8/DHr/OqaDXAwiI+Tj7KcZu8SSaNMPhk35PzR2xMSmAud/z
      yDjd7Y7g+yt/GgUKG33CRjN1UnGXGKnHK6Kw8fltqzplcUBrnSxLDrRXWzlJZoY56JALeC/nGUV1
      jEmhwFGAZkXlUhB046l6yl6wBPWqy/2YKcFNY9pLz67be1grtBNC8HCAsxkWFdPYEDqjl2Xxw8NL
      prc4BCGBeRfBwgbMuWS+aahgFF3V3GZoNb9D0Ow+iqiqSFnROKAFmhuaD9PP5KCrGbfiSeFTHJtW
      LjT/hxw4qBXCUOCoRH4xvbbOlQSPv0OedjtYjM+D1hEpQLNP0nRcj/0WKlry+iw5EXYgZtbGwbDP
      XuZtSa+7d8KgHwUCs7wdEHyePLmOwkJqatPX0TEZe1X6RuOAPNfloLsN/WyM2ZN+fwFHvHNgdv7I
      l+dStJ1vIgOGZAyZI4cLfUHwowPpgFpUN5fjfRz5UijpptAxDqbRGq1cgEBLHvO2akGhEwwrXBXA
      yaB1ONaRR0kLGbW6tddj76kIR7j7wVQMYceTtXKUPSrPYRwiufDHqqvD6H6Ie+0TdBd8epDrh/kd
      XsKUuZC/RS1/j263G9rdjcPwVLevdw6E6/Ou5TGvRUAcyod3W96ZXbnflfNOnDfwYoFjlLKYPxNs
      Gx6gGClwIswa5zn3AYIHfIGXcBbZ4fRqVJ7lrPiyhliv8l2pOYXkvDFLtREwvfanLBy8gQGWnP60
      6dsJSOZfj4DLmhEGcDoIQXMKVfI/V9Z2yoRbzNR+uh+S5hC+hx3rDATKPWU2X+F6g3AgVAy0tlGH
      FA5GP5hI+hE8Ntg08GO8h+rX2ZKGWF3udkYgl6hCvMBRaehpeDENXoHQmEpDfppAuMvADQgd7ZT4
      NTqDvr75UrgNjNovluREWCu4+Zoh7i0BExHER0LM5sUIylC77tbhB+9OgVwATfshiP0XwqY0UBW/
      QTlQt9KQdVyCEqIwr3IQ/Tz3n+f85gxcmY3CcIWCxTWNr2Iram/uXjsVCReuiYoFTClwdIh+AjJ3
      u9ntXrMxghIFIa+5SxESN1P2gRHlWTIUatQytBvLuilFnLMNi1Kz1PeFWcwGb0lDPq3KH4sBsBI2
      vhyu6mXlQGhfvRV5MBk95+1UUzyiND+8t+K5ExiRswmciJhe+zCq0f40MJHjrig753llOv1JWBMe
      QPAcgvaUj4acjZkwVo7sNA8gjfXQfh7LGt9aNp/rtUehoUWGNY1P41MUpS8eImuJnK6S0mHQ9Lck
      gKqs4kDpHCBGMfLgTEYtH6RvPJKI34VbZ/t1dtliOokf0xEsXnwqsaG4Qi2HH8CY3M+q7263SxxY
      ofya228wVk4feFb3whWFGu2ze6JBNHtUOsxr7U5SYY2ZENuMlm50BsHmY0EyF8hlskoVB/JyIFzi
      dEhKtR1V7idwVFPGpR57PksBN+M1PYMcGSx90g4InpsY7lyOtiMVrXjSlhVhTOMr0Wo28UT4AvVO
      SAw1apeBwJkR7P+zUsIzaBuPgYTyPEVrJAIPeP/GiWirwtcBB3rt9ciBx2KfRN7J7BATCpwae+n4
      Bn7W9rZhTOP30NhPYhtMK3Dms7zO9yMoWJJA9DyfpLARXe5cBJgMpxN9UMA0dd4w1KjUxeQoe8lD
      Ki8CRlR624ic5ozHm28L3NnE/ak0xRANJQs0UlX15kDnKFknF/cALjHWs8GOs3YocLTJ+yLzJ7SM
      Ljr7KnH4EvO1RqPP7jvoQNQ89Eis2FSoYc9sTLPzWSbx0aaSpBv5qNScPBz/DJg2XBuTBJ5QdgOd
      anueI2uo0fYPqRanzgKsk/A8eYu2SKlQCZwUBhUsfjWx/kCuBZeJqNpSmKTxhx/ypzuCaa6w9dUZ
      fvTS2e+ls2tWxC/NtNfSabdjsHY02oLfC6r1QUtwWqu5qzg2SySk5j6OZiTntnnAjUuEjS98CBG1
      H4Jmd4Yn8+PBhpWk2XBCJg+rlPPWmuQobQ6DXVnJDdsxsRmvYyuzZFqa4au7vBywuFskpYGEDeyS
      6kVlWieo0LztS/E+bY4QFwTvG2nDkc1jCdG+utx1GHM1TMqfFBWwxyp0hew7Z3P4DT2c+TSC5I/Q
      cjaM2ngpITKk1tzuHDdR/gvy43cSXFqp5cVL0HYSfpRbEWr0qpYQyZlpz/WP5OoZStcwC1KgFOZ0
      QgpMenHdbQRQEp77jIbPVWofB1zqB3qPQo0/wiejnz3ba+6rhfC0qqwtmgxbK8WlwcXhIwXOUIWP
      0JnvpLPPNQrV4JM0fdxrj0HwbEvH1nJ4nzSWSkfBqAeh5QqYdToucI9A2y85/Ga3Qs3jQnxXNoe+
      OdGeObmJS1IhQ2TFBY7iQmsmKjl9B4FcbGilfdeTV8bflExCVVqYA+lTy/vwO29asB2FlTmffjSv
      VF+4V9nLLWmnEkvgPhJRO1goGbdCPHwBj8E28gAE4uvh6TOjnSu1g6Vlo3P/lamyy3yGhzqBY0MR
      75Usey91Eou2z34ZFc/Pd6XuVoMfM2l/QiINDjFZTrokEY0z76KlXyGQ/fhSc0eC/5DENsZ4fzAS
      0VaFDRwYE/i2LWzIGX45ht/58lIEhZYcvMj6ui73dfr1asMbynXf7T7HVMtxiXXGhFvrdARAaWNH
      uS87Aqj3Ez+jiM+M1kRpR0vtbJk19ETiU+QqfDCw0/TaCQiaO3LVbATucgcyQPwT/OjmSDaw+9qw
      GtvTdaf5DjxLm/reBph7eYE0y7fucBQt72tuEwTnpTzHOS3LhzIfMlsTe6dK7eWATBE28IRPamcH
      BIUCnU8yssnkSVOIgtm8vY1CsMzhfZbgORecn9DUdWaUioZZc5fxZn6XOkm03Eufu1N4OweRP8Y5
      bfpVoNuBPPSZcayJCleWD6LIeAqnmc8gSM+lPH4ftfx2yszYHAx+ng7Vw/DpHB7af4jT7d4Lnrkc
      O3FkS2kqcjYsBrof5YedC3jyV8QRHF2zfP2mDryGnDfz/Pejxz6HXe41XgkFUF+XvPHAfYjyXTmn
      O/t1EFWwXTt+QkCVGjjggs67R0NOq0sFOv8Rc7mvIigW8AsuAOhpjvDN7CD63kx7w4iKGs10uQfJ
      36apLFyMeQQ4j8CK9Crvzm/ApLV5GgE8Af4nKevgTxNLstVswfnDvGdbNuGJu7EEshtMkcDRFz/7
      bIcjfq6GRzV3CiKLuDc2eTovaq3xPNU+xe2haEznclZH3q2xuPC1DNXSylaj8021z3jj09a9i1kt
      78yXOEKNMCsya36fFTQVbn2CWf+doFvhQtg08JWB2xugvTkbXpYwRefwtRzMTD3dhuvhRalQwwEc
      L+6yTf4fEz862/N8G7GN8GN8ILOYDUINe0tgmzv+QBDo/IaYx7qc/GaB0wgY4gwF3nCdevh9Y724
      a0v0gl57XVQcdaCfRxmZz46u7IK9me5DFftM5nrDAbXjpYY52gHTotqVk36GIByPBna0t7Bp3rrX
      RwtbhD5xfTmPAxatW+kMePRoaTjTEFm+mmOxm6ntvGmVYHVx3lr+8AOeIXL9W5TPV/lJIwBLnKd2
      pTUIB7ys9suyLFvqZMKnIYUCR7aVcHlCQ1Hmy435gl6BqnY9toOtM9caDqgdMDuD6e0uaHl5eHGm
      e8tWedZ8FEHzSbSu+zPVaQWksWw/uAzam9/WvQsRoJ/ScvxW6L3zFCZgFWI2a5uOdifZnxyr1DWc
      80ldVj47yX4lPnjj6oxNdR+Iq+mb7+vcmt5euKXRWemAMRAdQcjd1oUn2+d4N/+ZfpLm0tG6fvbc
      Wxjgf5T35/nGKpGGY9AyZMg9sQAhE+mkmkaXz0w2o2UjJbrW0KzPzsKBe3Po0MxMVuX/SeAPRWBp
      NfdS9W04+tR77a3c5X6OAP0psL5xbtTJ9mQMfW1qez4ANftXNCcFITuPw0fJTW9VwbUdi3P77G/S
      gRMhzkwsLbMwFIzt4XkrOuVZb82PWxWVkjcWG2mH8eNfR/CxjCcjtO9Mgv4mYRBfIUeJ3kn53o1F
      2LTw2xoSOMLZZ08F+H0ct+ZoohFUlmr5zGga/fDcVvQIk0KL9tovwnAte785yh5xVkxji5F0LRwM
      e+23EVh+HfAUtza2pDPoZNJq9hrRTtYM7Z0+Fi1N0RLbmcL1a/+GgP0wz19eW4qrE+65vhPP8Ejh
      R9gADTHp9yvcwDAEY3HiXFbDBTXdiRG/rImBYY/Cuyzn2RPAP4kiGXqzJdlM6nZBKrBGNQofbEuK
      NBA2eAv4PkRfPAYaXm5FQ+sZCk23zQ/GXnU64TqtKmbMmw8Bx0LAjRnhR4KFEcYOhI45HG9fCqBA
      744XrEjHkF/R4mCWTLNl6y3FnffCEqXeEA2x1y6fqeO624bn+Ao0iE9vzkn+a/xGt3JcAgcu87LX
      JDUoHmv2w6HGuyCAW8TnE4MPXFJdnzJtoUNocKruwLEFx+oBGm2FpN1Jyk5z3JrEdpoB2gk8nzze
      Vw6a6GAGsEgY3EY6w/d0X/AfTLaG1JopGpksPjqrE0dYw6Y8SVEwFX9YGyDk7++a/Pk+H1rFLZ+f
      1mxrgRPV0urrVwJmHgYhnVG2x/mH1NE0uv9XU85JS4K4vrtBiTa5u8WDjqEqXU7Dknk81/ZDmbmv
      XgBHH/TMhZ5lPUsyklgJ5xl0ssVoPton3BKTzbHdjWUqPHRCfI7rZ6moQ97av+XVvbV0ITOSsiqn
      TA5ol5HFZmNGAOMYwKzD+XmOu3kHHyjUzHluLKb3HXkvtgOPAqm/jfPaHHK3eJl3R0MwHX+l7Hbu
      f89HVh/bzClZ4ERoau7dXJ5JIx+PsjzOi2DKaRis5sCYVzzql1Oly70DRklb+pcCCDXB/G2+JtOY
      BZOEr1LFgYoDGTiQTeBEiLQy2wXBtqQ6+iUF6QmdBi/3Q+BZK1R9T6b2cbS/iicWVbsW9fF4hOY9
      BXBUVSsOvCE5kE/giEXh/kgaYs3gTq7RfsmaXzMUkXHpTj8EGWuF498vAd0LzW/JWKsV2P1oZ1OY
      ffpZq8Iqr+JAxYF0DuQXOBFOrU5ezJDCBcZlrejOnzSF5lgnNNYQ+MvKNbvcNM1NxO6jqUWNSf2S
      bB6yjIwx7CLIzEGVKg5UHPDmgL/AiZpUgCyHbUYWbt8UGqPqaDznltKptYR/ceC092lfkqgnj89v
      YqeZkdvqX6DRqmrFgdczB4oLnIg7CogVGpa3jbJyn625D8Pysd5Oc9K6lgSruI9CCK6Uu/2oQuib
      MAUL/J+irOpccaDiQHEOlCdwREvoc3EIA6Ve7lr7CmSj+Wq0ixMyT7nJrrQET2PZlfL7oQxRpKUR
      oT+Nv7fyELbqquJAxYFhHChX4ETIwxmhLjr/ZLJCR6ioLPv5NbSdb2A76WOY1dJrMUBVxsyZvFM7
      2AB4PFPdVRiG7L9QBVlxICcH2iNwIiJq7l1cyn9n3yjL4/wEdU5EZ/ruYHTCEEXdbYWdRssR/H2D
      wgVs/46dZnplp/H4ZaoqFQdycqC9Aicipub25PIshIMcCH3TLWghx7Cw86FSvJ+tuY5h22Tcz9u3
      6tf3Sat6FQdepxxYNgJHzAvtLDLmKlj3Op781Ga0C6kvV2vf9NCgfejHvgiqehUHKg74cWDZCZyI
      vlluPURGL53+KwiO5tXqEUx7zgtBOwtT9hnV2qH2MLjCWnEgjQPLXuBEFHW77RE4Ci26S5TVprO0
      ov/Ax4fo9PbxNrVRoa04UHEgAweWn8CJiOt2ByB0TuXYMMoq8XxbYPfpsTeXiLNCVXGg4oAnB5a/
      wBHhUeiJAUJYmFI2bH8SQTOViMsXNc1seTKpqlZxoOJAORwYHQInepYwdMTpaDv7R1k5z1qOMI/h
      00yGTy/mrFuBVxyoONBmDowugRM9bLhXtvx38gTHupJFoFMRNMWCEEU0VOeKAxUHSufA6BQ4ekxF
      r5tuPs0yCU2jx6/PCv1ppuJPc2vp3KkQVhyoOFAqB0avwIkeMxQ8eyN4ZFzemez1GDY9yvkahk4X
      otGkxlGNUFXnigMVB5YvB/4fD7i6XFwaW1YAAAAASUVORK5CYII=</pkg:binaryData>
  </pkg:part>
  <pkg:part pkg:name="/word/theme/theme1.xml"
    pkg:contentType="application/vnd.openxmlformats-officedocument.theme+xml">
    <pkg:xmlData>
      <a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">
        <a:themeElements>
          <a:clrScheme name="Office">
            <a:dk1>
              <a:sysClr val="windowText" lastClr="000000" />
            </a:dk1>
            <a:lt1>
              <a:sysClr val="window" lastClr="FFFFFF" />
            </a:lt1>
            <a:dk2>
              <a:srgbClr val="44546A" />
            </a:dk2>
            <a:lt2>
              <a:srgbClr val="E7E6E6" />
            </a:lt2>
            <a:accent1>
              <a:srgbClr val="4472C4" />
            </a:accent1>
            <a:accent2>
              <a:srgbClr val="ED7D31" />
            </a:accent2>
            <a:accent3>
              <a:srgbClr val="A5A5A5" />
            </a:accent3>
            <a:accent4>
              <a:srgbClr val="FFC000" />
            </a:accent4>
            <a:accent5>
              <a:srgbClr val="5B9BD5" />
            </a:accent5>
            <a:accent6>
              <a:srgbClr val="70AD47" />
            </a:accent6>
            <a:hlink>
              <a:srgbClr val="0563C1" />
            </a:hlink>
            <a:folHlink>
              <a:srgbClr val="954F72" />
            </a:folHlink>
          </a:clrScheme>
          <a:fontScheme name="Office">
            <a:majorFont>
              <a:latin typeface="Calibri Light" panose="020F0302020204030204" />
              <a:ea typeface="" />
              <a:cs typeface="" />
              <a:font script="Jpan" typeface="游ゴシック Light" />
              <a:font script="Hang" typeface="맑은 고딕" />
              <a:font script="Hans" typeface="等线 Light" />
              <a:font script="Hant" typeface="新細明體" />
              <a:font script="Arab" typeface="Times New Roman" />
              <a:font script="Hebr" typeface="Times New Roman" />
              <a:font script="Thai" typeface="Angsana New" />
              <a:font script="Ethi" typeface="Nyala" />
              <a:font script="Beng" typeface="Vrinda" />
              <a:font script="Gujr" typeface="Shruti" />
              <a:font script="Khmr" typeface="MoolBoran" />
              <a:font script="Knda" typeface="Tunga" />
              <a:font script="Guru" typeface="Raavi" />
              <a:font script="Cans" typeface="Euphemia" />
              <a:font script="Cher" typeface="Plantagenet Cherokee" />
              <a:font script="Yiii" typeface="Microsoft Yi Baiti" />
              <a:font script="Tibt" typeface="Microsoft Himalaya" />
              <a:font script="Thaa" typeface="MV Boli" />
              <a:font script="Deva" typeface="Mangal" />
              <a:font script="Telu" typeface="Gautami" />
              <a:font script="Taml" typeface="Latha" />
              <a:font script="Syrc" typeface="Estrangelo Edessa" />
              <a:font script="Orya" typeface="Kalinga" />
              <a:font script="Mlym" typeface="Kartika" />
              <a:font script="Laoo" typeface="DokChampa" />
              <a:font script="Sinh" typeface="Iskoola Pota" />
              <a:font script="Mong" typeface="Mongolian Baiti" />
              <a:font script="Viet" typeface="Times New Roman" />
              <a:font script="Uigh" typeface="Microsoft Uighur" />
              <a:font script="Geor" typeface="Sylfaen" />
              <a:font script="Armn" typeface="Arial" />
              <a:font script="Bugi" typeface="Leelawadee UI" />
              <a:font script="Bopo" typeface="Microsoft JhengHei" />
              <a:font script="Java" typeface="Javanese Text" />
              <a:font script="Lisu" typeface="Segoe UI" />
              <a:font script="Mymr" typeface="Myanmar Text" />
              <a:font script="Nkoo" typeface="Ebrima" />
              <a:font script="Olck" typeface="Nirmala UI" />
              <a:font script="Osma" typeface="Ebrima" />
              <a:font script="Phag" typeface="Phagspa" />
              <a:font script="Syrn" typeface="Estrangelo Edessa" />
              <a:font script="Syrj" typeface="Estrangelo Edessa" />
              <a:font script="Syre" typeface="Estrangelo Edessa" />
              <a:font script="Sora" typeface="Nirmala UI" />
              <a:font script="Tale" typeface="Microsoft Tai Le" />
              <a:font script="Talu" typeface="Microsoft New Tai Lue" />
              <a:font script="Tfng" typeface="Ebrima" />
            </a:majorFont>
            <a:minorFont>
              <a:latin typeface="Calibri" panose="020F0502020204030204" />
              <a:ea typeface="" />
              <a:cs typeface="" />
              <a:font script="Jpan" typeface="游明朝" />
              <a:font script="Hang" typeface="맑은 고딕" />
              <a:font script="Hans" typeface="等线" />
              <a:font script="Hant" typeface="新細明體" />
              <a:font script="Arab" typeface="Arial" />
              <a:font script="Hebr" typeface="Arial" />
              <a:font script="Thai" typeface="Cordia New" />
              <a:font script="Ethi" typeface="Nyala" />
              <a:font script="Beng" typeface="Vrinda" />
              <a:font script="Gujr" typeface="Shruti" />
              <a:font script="Khmr" typeface="DaunPenh" />
              <a:font script="Knda" typeface="Tunga" />
              <a:font script="Guru" typeface="Raavi" />
              <a:font script="Cans" typeface="Euphemia" />
              <a:font script="Cher" typeface="Plantagenet Cherokee" />
              <a:font script="Yiii" typeface="Microsoft Yi Baiti" />
              <a:font script="Tibt" typeface="Microsoft Himalaya" />
              <a:font script="Thaa" typeface="MV Boli" />
              <a:font script="Deva" typeface="Mangal" />
              <a:font script="Telu" typeface="Gautami" />
              <a:font script="Taml" typeface="Latha" />
              <a:font script="Syrc" typeface="Estrangelo Edessa" />
              <a:font script="Orya" typeface="Kalinga" />
              <a:font script="Mlym" typeface="Kartika" />
              <a:font script="Laoo" typeface="DokChampa" />
              <a:font script="Sinh" typeface="Iskoola Pota" />
              <a:font script="Mong" typeface="Mongolian Baiti" />
              <a:font script="Viet" typeface="Arial" />
              <a:font script="Uigh" typeface="Microsoft Uighur" />
              <a:font script="Geor" typeface="Sylfaen" />
              <a:font script="Armn" typeface="Arial" />
              <a:font script="Bugi" typeface="Leelawadee UI" />
              <a:font script="Bopo" typeface="Microsoft JhengHei" />
              <a:font script="Java" typeface="Javanese Text" />
              <a:font script="Lisu" typeface="Segoe UI" />
              <a:font script="Mymr" typeface="Myanmar Text" />
              <a:font script="Nkoo" typeface="Ebrima" />
              <a:font script="Olck" typeface="Nirmala UI" />
              <a:font script="Osma" typeface="Ebrima" />
              <a:font script="Phag" typeface="Phagspa" />
              <a:font script="Syrn" typeface="Estrangelo Edessa" />
              <a:font script="Syrj" typeface="Estrangelo Edessa" />
              <a:font script="Syre" typeface="Estrangelo Edessa" />
              <a:font script="Sora" typeface="Nirmala UI" />
              <a:font script="Tale" typeface="Microsoft Tai Le" />
              <a:font script="Talu" typeface="Microsoft New Tai Lue" />
              <a:font script="Tfng" typeface="Ebrima" />
            </a:minorFont>
          </a:fontScheme>
          <a:fmtScheme name="Office">
            <a:fillStyleLst>
              <a:solidFill>
                <a:schemeClr val="phClr" />
              </a:solidFill>
              <a:gradFill rotWithShape="1">
                <a:gsLst>
                  <a:gs pos="0">
                    <a:schemeClr val="phClr">
                      <a:lumMod val="110000" />
                      <a:satMod val="105000" />
                      <a:tint val="67000" />
                    </a:schemeClr>
                  </a:gs>
                  <a:gs pos="50000">
                    <a:schemeClr val="phClr">
                      <a:lumMod val="105000" />
                      <a:satMod val="103000" />
                      <a:tint val="73000" />
                    </a:schemeClr>
                  </a:gs>
                  <a:gs pos="100000">
                    <a:schemeClr val="phClr">
                      <a:lumMod val="105000" />
                      <a:satMod val="109000" />
                      <a:tint val="81000" />
                    </a:schemeClr>
                  </a:gs>
                </a:gsLst>
                <a:lin ang="5400000" scaled="0" />
              </a:gradFill>
              <a:gradFill rotWithShape="1">
                <a:gsLst>
                  <a:gs pos="0">
                    <a:schemeClr val="phClr">
                      <a:satMod val="103000" />
                      <a:lumMod val="102000" />
                      <a:tint val="94000" />
                    </a:schemeClr>
                  </a:gs>
                  <a:gs pos="50000">
                    <a:schemeClr val="phClr">
                      <a:satMod val="110000" />
                      <a:lumMod val="100000" />
                      <a:shade val="100000" />
                    </a:schemeClr>
                  </a:gs>
                  <a:gs pos="100000">
                    <a:schemeClr val="phClr">
                      <a:lumMod val="99000" />
                      <a:satMod val="120000" />
                      <a:shade val="78000" />
                    </a:schemeClr>
                  </a:gs>
                </a:gsLst>
                <a:lin ang="5400000" scaled="0" />
              </a:gradFill>
            </a:fillStyleLst>
            <a:lnStyleLst>
              <a:ln w="6350" cap="flat" cmpd="sng" algn="ctr">
                <a:solidFill>
                  <a:schemeClr val="phClr" />
                </a:solidFill>
                <a:prstDash val="solid" />
                <a:miter lim="800000" />
              </a:ln>
              <a:ln w="12700" cap="flat" cmpd="sng" algn="ctr">
                <a:solidFill>
                  <a:schemeClr val="phClr" />
                </a:solidFill>
                <a:prstDash val="solid" />
                <a:miter lim="800000" />
              </a:ln>
              <a:ln w="19050" cap="flat" cmpd="sng" algn="ctr">
                <a:solidFill>
                  <a:schemeClr val="phClr" />
                </a:solidFill>
                <a:prstDash val="solid" />
                <a:miter lim="800000" />
              </a:ln>
            </a:lnStyleLst>
            <a:effectStyleLst>
              <a:effectStyle>
                <a:effectLst />
              </a:effectStyle>
              <a:effectStyle>
                <a:effectLst />
              </a:effectStyle>
              <a:effectStyle>
                <a:effectLst>
                  <a:outerShdw blurRad="57150" dist="19050" dir="5400000" algn="ctr"
                    rotWithShape="0">
                    <a:srgbClr val="000000">
                      <a:alpha val="63000" />
                    </a:srgbClr>
                  </a:outerShdw>
                </a:effectLst>
              </a:effectStyle>
            </a:effectStyleLst>
            <a:bgFillStyleLst>
              <a:solidFill>
                <a:schemeClr val="phClr" />
              </a:solidFill>
              <a:solidFill>
                <a:schemeClr val="phClr">
                  <a:tint val="95000" />
                  <a:satMod val="170000" />
                </a:schemeClr>
              </a:solidFill>
              <a:gradFill rotWithShape="1">
                <a:gsLst>
                  <a:gs pos="0">
                    <a:schemeClr val="phClr">
                      <a:tint val="93000" />
                      <a:satMod val="150000" />
                      <a:shade val="98000" />
                      <a:lumMod val="102000" />
                    </a:schemeClr>
                  </a:gs>
                  <a:gs pos="50000">
                    <a:schemeClr val="phClr">
                      <a:tint val="98000" />
                      <a:satMod val="130000" />
                      <a:shade val="90000" />
                      <a:lumMod val="103000" />
                    </a:schemeClr>
                  </a:gs>
                  <a:gs pos="100000">
                    <a:schemeClr val="phClr">
                      <a:shade val="63000" />
                      <a:satMod val="120000" />
                    </a:schemeClr>
                  </a:gs>
                </a:gsLst>
                <a:lin ang="5400000" scaled="0" />
              </a:gradFill>
            </a:bgFillStyleLst>
          </a:fmtScheme>
        </a:themeElements>
        <a:objectDefaults />
        <a:extraClrSchemeLst />
        <a:extLst>
          <a:ext uri="{05A4C25C-085E-4340-85A3-A5531E510DB2}">
            <thm15:themeFamily xmlns:thm15="http://schemas.microsoft.com/office/thememl/2012/main"
              name="Office Theme" id="{62F939B6-93AF-4DB8-9C6B-D6C7DFDC589F}"
              vid="{4A3C46E8-61CC-4603-A589-7422A47A8E4A}" />
          </a:ext>
        </a:extLst>
      </a:theme>
    </pkg:xmlData>
  </pkg:part>
  <pkg:part pkg:name="/word/settings.xml"
    pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml">
    <pkg:xmlData>
      <w:settings xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:o="urn:schemas-microsoft-com:office:office"
        xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"
        xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math"
        xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w10="urn:schemas-microsoft-com:office:word"
        xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"
        xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml"
        xmlns:w15="http://schemas.microsoft.com/office/word/2012/wordml"
        xmlns:w16cex="http://schemas.microsoft.com/office/word/2018/wordml/cex"
        xmlns:w16cid="http://schemas.microsoft.com/office/word/2016/wordml/cid"
        xmlns:w16="http://schemas.microsoft.com/office/word/2018/wordml"
        xmlns:w16sdtdh="http://schemas.microsoft.com/office/word/2020/wordml/sdtdatahash"
        xmlns:w16se="http://schemas.microsoft.com/office/word/2015/wordml/symex"
        xmlns:sl="http://schemas.openxmlformats.org/schemaLibrary/2006/main"
        mc:Ignorable="w14 w15 w16se w16cid w16 w16cex w16sdtdh">
        <w:zoom w:percent="120" />
        <w:doNotDisplayPageBoundaries />
        <w:bordersDoNotSurroundHeader />
        <w:bordersDoNotSurroundFooter />
        <w:proofState w:spelling="clean" w:grammar="clean" />
        <w:defaultTabStop w:val="720" />
        <w:characterSpacingControl w:val="doNotCompress" />
        <w:compat>
          <w:useFELayout />
          <w:compatSetting w:name="compatibilityMode"
            w:uri="http://schemas.microsoft.com/office/word" w:val="15" />
          <w:compatSetting w:name="overrideTableStyleFontSizeAndJustification"
            w:uri="http://schemas.microsoft.com/office/word" w:val="1" />
          <w:compatSetting w:name="enableOpenTypeFeatures"
            w:uri="http://schemas.microsoft.com/office/word" w:val="1" />
          <w:compatSetting w:name="doNotFlipMirrorIndents"
            w:uri="http://schemas.microsoft.com/office/word" w:val="1" />
          <w:compatSetting w:name="differentiateMultirowTableHeaders"
            w:uri="http://schemas.microsoft.com/office/word" w:val="1" />
          <w:compatSetting w:name="useWord2013TrackBottomHyphenation"
            w:uri="http://schemas.microsoft.com/office/word" w:val="0" />
        </w:compat>
        <w:rsids>
          <w:rsidRoot w:val="00347F72" />
          <w:rsid w:val="00347F72" />
          <w:rsid w:val="003A1B6B" />
          <w:rsid w:val="00685F3F" />
          <w:rsid w:val="009E0530" />
          <w:rsid w:val="00BA1FBE" />
        </w:rsids>
        <m:mathPr>
          <m:mathFont m:val="Cambria Math" />
          <m:brkBin m:val="before" />
          <m:brkBinSub m:val="--" />
          <m:smallFrac m:val="0" />
          <m:dispDef />
          <m:lMargin m:val="0" />
          <m:rMargin m:val="0" />
          <m:defJc m:val="centerGroup" />
          <m:wrapIndent m:val="1440" />
          <m:intLim m:val="subSup" />
          <m:naryLim m:val="undOvr" />
        </m:mathPr>
        <w:themeFontLang w:val="en-CN" w:eastAsia="zh-CN" />
        <w:clrSchemeMapping w:bg1="light1" w:t1="dark1" w:bg2="light2" w:t2="dark2"
          w:accent1="accent1" w:accent2="accent2" w:accent3="accent3" w:accent4="accent4"
          w:accent5="accent5" w:accent6="accent6" w:hyperlink="hyperlink"
          w:followedHyperlink="followedHyperlink" />
        <w:decimalSymbol w:val="." />
        <w:listSeparator w:val="," />
        <w14:docId w14:val="7C955DF2" />
        <w15:chartTrackingRefBased />
        <w15:docId w15:val="{19828F2F-C26C-3447-956A-22E91FC59338}" />
      </w:settings>
    </pkg:xmlData>
  </pkg:part>
  <pkg:part pkg:name="/docProps/core.xml"
    pkg:contentType="application/vnd.openxmlformats-package.core-properties+xml" pkg:padding="256">
    <pkg:xmlData>
      <cp:coreProperties
        xmlns:cp="http://schemas.openxmlformats.org/package/2006/metadata/core-properties"
        xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:dcterms="http://purl.org/dc/terms/"
        xmlns:dcmitype="http://purl.org/dc/dcmitype/"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
        <dc:title />
        <dc:subject />
        <dc:creator>administrator</dc:creator>
        <cp:keywords />
        <dc:description />
        <cp:lastModifiedBy>administrator</cp:lastModifiedBy>
        <cp:revision>4</cp:revision>
        <dcterms:created xsi:type="dcterms:W3CDTF">2023-03-28T09:21:00Z</dcterms:created>
        <dcterms:modified xsi:type="dcterms:W3CDTF">2023-03-29T03:50:00Z</dcterms:modified>
      </cp:coreProperties>
    </pkg:xmlData>
  </pkg:part>
  <pkg:part pkg:name="/docProps/app.xml"
    pkg:contentType="application/vnd.openxmlformats-officedocument.extended-properties+xml"
    pkg:padding="256">
    <pkg:xmlData>
      <Properties xmlns="http://schemas.openxmlformats.org/officeDocument/2006/extended-properties"
        xmlns:vt="http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes">
        <Template>Normal.dotm</Template>
        <TotalTime>2</TotalTime>
        <Pages>1</Pages>
        <Words>0</Words>
        <Characters>1</Characters>
        <Application>Microsoft Office Word</Application>
        <DocSecurity>0</DocSecurity>
        <Lines>1</Lines>
        <Paragraphs>1</Paragraphs>
        <ScaleCrop>false</ScaleCrop>
        <Company />
        <LinksUpToDate>false</LinksUpToDate>
        <CharactersWithSpaces>1</CharactersWithSpaces>
        <SharedDoc>false</SharedDoc>
        <HyperlinksChanged>false</HyperlinksChanged>
        <AppVersion>16.0000</AppVersion>
      </Properties>
    </pkg:xmlData>
  </pkg:part>
  <pkg:part pkg:name="/word/webSettings.xml"
    pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.webSettings+xml">
    <pkg:xmlData>
      <w:webSettings xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"
        xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"
        xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml"
        xmlns:w15="http://schemas.microsoft.com/office/word/2012/wordml"
        xmlns:w16cex="http://schemas.microsoft.com/office/word/2018/wordml/cex"
        xmlns:w16cid="http://schemas.microsoft.com/office/word/2016/wordml/cid"
        xmlns:w16="http://schemas.microsoft.com/office/word/2018/wordml"
        xmlns:w16sdtdh="http://schemas.microsoft.com/office/word/2020/wordml/sdtdatahash"
        xmlns:w16se="http://schemas.microsoft.com/office/word/2015/wordml/symex"
        mc:Ignorable="w14 w15 w16se w16cid w16 w16cex w16sdtdh">
        <w:optimizeForBrowser />
        <w:allowPNG />
      </w:webSettings>
    </pkg:xmlData>
  </pkg:part>
  <pkg:part pkg:name="/word/styles.xml"
    pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml">
    <pkg:xmlData>
      <w:styles xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"
        xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"
        xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml"
        xmlns:w15="http://schemas.microsoft.com/office/word/2012/wordml"
        xmlns:w16cex="http://schemas.microsoft.com/office/word/2018/wordml/cex"
        xmlns:w16cid="http://schemas.microsoft.com/office/word/2016/wordml/cid"
        xmlns:w16="http://schemas.microsoft.com/office/word/2018/wordml"
        xmlns:w16sdtdh="http://schemas.microsoft.com/office/word/2020/wordml/sdtdatahash"
        xmlns:w16se="http://schemas.microsoft.com/office/word/2015/wordml/symex"
        mc:Ignorable="w14 w15 w16se w16cid w16 w16cex w16sdtdh">
        <w:docDefaults>
          <w:rPrDefault>
            <w:rPr>
              <w:rFonts w:asciiTheme="minorHAnsi" w:eastAsiaTheme="minorEastAsia"
                w:hAnsiTheme="minorHAnsi" w:cstheme="minorBidi" />
              <w:sz w:val="24" />
              <w:szCs w:val="24" />
              <w:lang w:val="en-CN" w:eastAsia="zh-CN" w:bidi="ar-SA" />
            </w:rPr>
          </w:rPrDefault>
          <w:pPrDefault />
        </w:docDefaults>
        <w:latentStyles w:defLockedState="0" w:defUIPriority="99" w:defSemiHidden="0"
          w:defUnhideWhenUsed="0" w:defQFormat="0" w:count="376">
          <w:lsdException w:name="Normal" w:uiPriority="0" w:qFormat="1" />
          <w:lsdException w:name="heading 1" w:uiPriority="9" w:qFormat="1" />
          <w:lsdException w:name="heading 2" w:semiHidden="1" w:uiPriority="9" w:unhideWhenUsed="1"
            w:qFormat="1" />
          <w:lsdException w:name="heading 3" w:semiHidden="1" w:uiPriority="9" w:unhideWhenUsed="1"
            w:qFormat="1" />
          <w:lsdException w:name="heading 4" w:semiHidden="1" w:uiPriority="9" w:unhideWhenUsed="1"
            w:qFormat="1" />
          <w:lsdException w:name="heading 5" w:semiHidden="1" w:uiPriority="9" w:unhideWhenUsed="1"
            w:qFormat="1" />
          <w:lsdException w:name="heading 6" w:semiHidden="1" w:uiPriority="9" w:unhideWhenUsed="1"
            w:qFormat="1" />
          <w:lsdException w:name="heading 7" w:semiHidden="1" w:uiPriority="9" w:unhideWhenUsed="1"
            w:qFormat="1" />
          <w:lsdException w:name="heading 8" w:semiHidden="1" w:uiPriority="9" w:unhideWhenUsed="1"
            w:qFormat="1" />
          <w:lsdException w:name="heading 9" w:semiHidden="1" w:uiPriority="9" w:unhideWhenUsed="1"
            w:qFormat="1" />
          <w:lsdException w:name="index 1" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="index 2" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="index 3" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="index 4" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="index 5" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="index 6" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="index 7" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="index 8" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="index 9" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="toc 1" w:semiHidden="1" w:uiPriority="39" w:unhideWhenUsed="1" />
          <w:lsdException w:name="toc 2" w:semiHidden="1" w:uiPriority="39" w:unhideWhenUsed="1" />
          <w:lsdException w:name="toc 3" w:semiHidden="1" w:uiPriority="39" w:unhideWhenUsed="1" />
          <w:lsdException w:name="toc 4" w:semiHidden="1" w:uiPriority="39" w:unhideWhenUsed="1" />
          <w:lsdException w:name="toc 5" w:semiHidden="1" w:uiPriority="39" w:unhideWhenUsed="1" />
          <w:lsdException w:name="toc 6" w:semiHidden="1" w:uiPriority="39" w:unhideWhenUsed="1" />
          <w:lsdException w:name="toc 7" w:semiHidden="1" w:uiPriority="39" w:unhideWhenUsed="1" />
          <w:lsdException w:name="toc 8" w:semiHidden="1" w:uiPriority="39" w:unhideWhenUsed="1" />
          <w:lsdException w:name="toc 9" w:semiHidden="1" w:uiPriority="39" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Normal Indent" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="footnote text" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="annotation text" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="header" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="footer" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="index heading" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="caption" w:semiHidden="1" w:uiPriority="35" w:unhideWhenUsed="1"
            w:qFormat="1" />
          <w:lsdException w:name="table of figures" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="envelope address" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="envelope return" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="footnote reference" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="annotation reference" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="line number" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="page number" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="endnote reference" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="endnote text" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="table of authorities" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="macro" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="toa heading" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="List" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="List Bullet" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="List Number" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="List 2" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="List 3" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="List 4" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="List 5" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="List Bullet 2" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="List Bullet 3" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="List Bullet 4" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="List Bullet 5" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="List Number 2" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="List Number 3" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="List Number 4" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="List Number 5" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Title" w:uiPriority="10" w:qFormat="1" />
          <w:lsdException w:name="Closing" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Signature" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Default Paragraph Font" w:semiHidden="1" w:uiPriority="1"
            w:unhideWhenUsed="1" />
          <w:lsdException w:name="Body Text" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Body Text Indent" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="List Continue" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="List Continue 2" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="List Continue 3" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="List Continue 4" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="List Continue 5" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Message Header" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Subtitle" w:uiPriority="11" w:qFormat="1" />
          <w:lsdException w:name="Salutation" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Date" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Body Text First Indent" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Body Text First Indent 2" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Note Heading" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Body Text 2" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Body Text 3" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Body Text Indent 2" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Body Text Indent 3" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Block Text" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Hyperlink" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="FollowedHyperlink" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Strong" w:uiPriority="22" w:qFormat="1" />
          <w:lsdException w:name="Emphasis" w:uiPriority="20" w:qFormat="1" />
          <w:lsdException w:name="Document Map" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Plain Text" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="E-mail Signature" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="HTML Top of Form" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="HTML Bottom of Form" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Normal (Web)" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="HTML Acronym" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="HTML Address" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="HTML Cite" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="HTML Code" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="HTML Definition" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="HTML Keyboard" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="HTML Preformatted" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="HTML Sample" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="HTML Typewriter" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="HTML Variable" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Normal Table" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="annotation subject" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="No List" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Outline List 1" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Outline List 2" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Outline List 3" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Simple 1" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Simple 2" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Simple 3" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Classic 1" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Classic 2" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Classic 3" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Classic 4" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Colorful 1" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Colorful 2" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Colorful 3" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Columns 1" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Columns 2" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Columns 3" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Columns 4" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Columns 5" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Grid 1" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Grid 2" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Grid 3" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Grid 4" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Grid 5" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Grid 6" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Grid 7" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Grid 8" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table List 1" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table List 2" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table List 3" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table List 4" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table List 5" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table List 6" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table List 7" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table List 8" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table 3D effects 1" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table 3D effects 2" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table 3D effects 3" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Contemporary" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Elegant" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Professional" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Subtle 1" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Subtle 2" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Web 1" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Web 2" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Web 3" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Balloon Text" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Table Grid" w:uiPriority="39" />
          <w:lsdException w:name="Table Theme" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Placeholder Text" w:semiHidden="1" />
          <w:lsdException w:name="No Spacing" w:uiPriority="1" w:qFormat="1" />
          <w:lsdException w:name="Light Shading" w:uiPriority="60" />
          <w:lsdException w:name="Light List" w:uiPriority="61" />
          <w:lsdException w:name="Light Grid" w:uiPriority="62" />
          <w:lsdException w:name="Medium Shading 1" w:uiPriority="63" />
          <w:lsdException w:name="Medium Shading 2" w:uiPriority="64" />
          <w:lsdException w:name="Medium List 1" w:uiPriority="65" />
          <w:lsdException w:name="Medium List 2" w:uiPriority="66" />
          <w:lsdException w:name="Medium Grid 1" w:uiPriority="67" />
          <w:lsdException w:name="Medium Grid 2" w:uiPriority="68" />
          <w:lsdException w:name="Medium Grid 3" w:uiPriority="69" />
          <w:lsdException w:name="Dark List" w:uiPriority="70" />
          <w:lsdException w:name="Colorful Shading" w:uiPriority="71" />
          <w:lsdException w:name="Colorful List" w:uiPriority="72" />
          <w:lsdException w:name="Colorful Grid" w:uiPriority="73" />
          <w:lsdException w:name="Light Shading Accent 1" w:uiPriority="60" />
          <w:lsdException w:name="Light List Accent 1" w:uiPriority="61" />
          <w:lsdException w:name="Light Grid Accent 1" w:uiPriority="62" />
          <w:lsdException w:name="Medium Shading 1 Accent 1" w:uiPriority="63" />
          <w:lsdException w:name="Medium Shading 2 Accent 1" w:uiPriority="64" />
          <w:lsdException w:name="Medium List 1 Accent 1" w:uiPriority="65" />
          <w:lsdException w:name="Revision" w:semiHidden="1" />
          <w:lsdException w:name="List Paragraph" w:uiPriority="34" w:qFormat="1" />
          <w:lsdException w:name="Quote" w:uiPriority="29" w:qFormat="1" />
          <w:lsdException w:name="Intense Quote" w:uiPriority="30" w:qFormat="1" />
          <w:lsdException w:name="Medium List 2 Accent 1" w:uiPriority="66" />
          <w:lsdException w:name="Medium Grid 1 Accent 1" w:uiPriority="67" />
          <w:lsdException w:name="Medium Grid 2 Accent 1" w:uiPriority="68" />
          <w:lsdException w:name="Medium Grid 3 Accent 1" w:uiPriority="69" />
          <w:lsdException w:name="Dark List Accent 1" w:uiPriority="70" />
          <w:lsdException w:name="Colorful Shading Accent 1" w:uiPriority="71" />
          <w:lsdException w:name="Colorful List Accent 1" w:uiPriority="72" />
          <w:lsdException w:name="Colorful Grid Accent 1" w:uiPriority="73" />
          <w:lsdException w:name="Light Shading Accent 2" w:uiPriority="60" />
          <w:lsdException w:name="Light List Accent 2" w:uiPriority="61" />
          <w:lsdException w:name="Light Grid Accent 2" w:uiPriority="62" />
          <w:lsdException w:name="Medium Shading 1 Accent 2" w:uiPriority="63" />
          <w:lsdException w:name="Medium Shading 2 Accent 2" w:uiPriority="64" />
          <w:lsdException w:name="Medium List 1 Accent 2" w:uiPriority="65" />
          <w:lsdException w:name="Medium List 2 Accent 2" w:uiPriority="66" />
          <w:lsdException w:name="Medium Grid 1 Accent 2" w:uiPriority="67" />
          <w:lsdException w:name="Medium Grid 2 Accent 2" w:uiPriority="68" />
          <w:lsdException w:name="Medium Grid 3 Accent 2" w:uiPriority="69" />
          <w:lsdException w:name="Dark List Accent 2" w:uiPriority="70" />
          <w:lsdException w:name="Colorful Shading Accent 2" w:uiPriority="71" />
          <w:lsdException w:name="Colorful List Accent 2" w:uiPriority="72" />
          <w:lsdException w:name="Colorful Grid Accent 2" w:uiPriority="73" />
          <w:lsdException w:name="Light Shading Accent 3" w:uiPriority="60" />
          <w:lsdException w:name="Light List Accent 3" w:uiPriority="61" />
          <w:lsdException w:name="Light Grid Accent 3" w:uiPriority="62" />
          <w:lsdException w:name="Medium Shading 1 Accent 3" w:uiPriority="63" />
          <w:lsdException w:name="Medium Shading 2 Accent 3" w:uiPriority="64" />
          <w:lsdException w:name="Medium List 1 Accent 3" w:uiPriority="65" />
          <w:lsdException w:name="Medium List 2 Accent 3" w:uiPriority="66" />
          <w:lsdException w:name="Medium Grid 1 Accent 3" w:uiPriority="67" />
          <w:lsdException w:name="Medium Grid 2 Accent 3" w:uiPriority="68" />
          <w:lsdException w:name="Medium Grid 3 Accent 3" w:uiPriority="69" />
          <w:lsdException w:name="Dark List Accent 3" w:uiPriority="70" />
          <w:lsdException w:name="Colorful Shading Accent 3" w:uiPriority="71" />
          <w:lsdException w:name="Colorful List Accent 3" w:uiPriority="72" />
          <w:lsdException w:name="Colorful Grid Accent 3" w:uiPriority="73" />
          <w:lsdException w:name="Light Shading Accent 4" w:uiPriority="60" />
          <w:lsdException w:name="Light List Accent 4" w:uiPriority="61" />
          <w:lsdException w:name="Light Grid Accent 4" w:uiPriority="62" />
          <w:lsdException w:name="Medium Shading 1 Accent 4" w:uiPriority="63" />
          <w:lsdException w:name="Medium Shading 2 Accent 4" w:uiPriority="64" />
          <w:lsdException w:name="Medium List 1 Accent 4" w:uiPriority="65" />
          <w:lsdException w:name="Medium List 2 Accent 4" w:uiPriority="66" />
          <w:lsdException w:name="Medium Grid 1 Accent 4" w:uiPriority="67" />
          <w:lsdException w:name="Medium Grid 2 Accent 4" w:uiPriority="68" />
          <w:lsdException w:name="Medium Grid 3 Accent 4" w:uiPriority="69" />
          <w:lsdException w:name="Dark List Accent 4" w:uiPriority="70" />
          <w:lsdException w:name="Colorful Shading Accent 4" w:uiPriority="71" />
          <w:lsdException w:name="Colorful List Accent 4" w:uiPriority="72" />
          <w:lsdException w:name="Colorful Grid Accent 4" w:uiPriority="73" />
          <w:lsdException w:name="Light Shading Accent 5" w:uiPriority="60" />
          <w:lsdException w:name="Light List Accent 5" w:uiPriority="61" />
          <w:lsdException w:name="Light Grid Accent 5" w:uiPriority="62" />
          <w:lsdException w:name="Medium Shading 1 Accent 5" w:uiPriority="63" />
          <w:lsdException w:name="Medium Shading 2 Accent 5" w:uiPriority="64" />
          <w:lsdException w:name="Medium List 1 Accent 5" w:uiPriority="65" />
          <w:lsdException w:name="Medium List 2 Accent 5" w:uiPriority="66" />
          <w:lsdException w:name="Medium Grid 1 Accent 5" w:uiPriority="67" />
          <w:lsdException w:name="Medium Grid 2 Accent 5" w:uiPriority="68" />
          <w:lsdException w:name="Medium Grid 3 Accent 5" w:uiPriority="69" />
          <w:lsdException w:name="Dark List Accent 5" w:uiPriority="70" />
          <w:lsdException w:name="Colorful Shading Accent 5" w:uiPriority="71" />
          <w:lsdException w:name="Colorful List Accent 5" w:uiPriority="72" />
          <w:lsdException w:name="Colorful Grid Accent 5" w:uiPriority="73" />
          <w:lsdException w:name="Light Shading Accent 6" w:uiPriority="60" />
          <w:lsdException w:name="Light List Accent 6" w:uiPriority="61" />
          <w:lsdException w:name="Light Grid Accent 6" w:uiPriority="62" />
          <w:lsdException w:name="Medium Shading 1 Accent 6" w:uiPriority="63" />
          <w:lsdException w:name="Medium Shading 2 Accent 6" w:uiPriority="64" />
          <w:lsdException w:name="Medium List 1 Accent 6" w:uiPriority="65" />
          <w:lsdException w:name="Medium List 2 Accent 6" w:uiPriority="66" />
          <w:lsdException w:name="Medium Grid 1 Accent 6" w:uiPriority="67" />
          <w:lsdException w:name="Medium Grid 2 Accent 6" w:uiPriority="68" />
          <w:lsdException w:name="Medium Grid 3 Accent 6" w:uiPriority="69" />
          <w:lsdException w:name="Dark List Accent 6" w:uiPriority="70" />
          <w:lsdException w:name="Colorful Shading Accent 6" w:uiPriority="71" />
          <w:lsdException w:name="Colorful List Accent 6" w:uiPriority="72" />
          <w:lsdException w:name="Colorful Grid Accent 6" w:uiPriority="73" />
          <w:lsdException w:name="Subtle Emphasis" w:uiPriority="19" w:qFormat="1" />
          <w:lsdException w:name="Intense Emphasis" w:uiPriority="21" w:qFormat="1" />
          <w:lsdException w:name="Subtle Reference" w:uiPriority="31" w:qFormat="1" />
          <w:lsdException w:name="Intense Reference" w:uiPriority="32" w:qFormat="1" />
          <w:lsdException w:name="Book Title" w:uiPriority="33" w:qFormat="1" />
          <w:lsdException w:name="Bibliography" w:semiHidden="1" w:uiPriority="37"
            w:unhideWhenUsed="1" />
          <w:lsdException w:name="TOC Heading" w:semiHidden="1" w:uiPriority="39"
            w:unhideWhenUsed="1" w:qFormat="1" />
          <w:lsdException w:name="Plain Table 1" w:uiPriority="41" />
          <w:lsdException w:name="Plain Table 2" w:uiPriority="42" />
          <w:lsdException w:name="Plain Table 3" w:uiPriority="43" />
          <w:lsdException w:name="Plain Table 4" w:uiPriority="44" />
          <w:lsdException w:name="Plain Table 5" w:uiPriority="45" />
          <w:lsdException w:name="Grid Table Light" w:uiPriority="40" />
          <w:lsdException w:name="Grid Table 1 Light" w:uiPriority="46" />
          <w:lsdException w:name="Grid Table 2" w:uiPriority="47" />
          <w:lsdException w:name="Grid Table 3" w:uiPriority="48" />
          <w:lsdException w:name="Grid Table 4" w:uiPriority="49" />
          <w:lsdException w:name="Grid Table 5 Dark" w:uiPriority="50" />
          <w:lsdException w:name="Grid Table 6 Colorful" w:uiPriority="51" />
          <w:lsdException w:name="Grid Table 7 Colorful" w:uiPriority="52" />
          <w:lsdException w:name="Grid Table 1 Light Accent 1" w:uiPriority="46" />
          <w:lsdException w:name="Grid Table 2 Accent 1" w:uiPriority="47" />
          <w:lsdException w:name="Grid Table 3 Accent 1" w:uiPriority="48" />
          <w:lsdException w:name="Grid Table 4 Accent 1" w:uiPriority="49" />
          <w:lsdException w:name="Grid Table 5 Dark Accent 1" w:uiPriority="50" />
          <w:lsdException w:name="Grid Table 6 Colorful Accent 1" w:uiPriority="51" />
          <w:lsdException w:name="Grid Table 7 Colorful Accent 1" w:uiPriority="52" />
          <w:lsdException w:name="Grid Table 1 Light Accent 2" w:uiPriority="46" />
          <w:lsdException w:name="Grid Table 2 Accent 2" w:uiPriority="47" />
          <w:lsdException w:name="Grid Table 3 Accent 2" w:uiPriority="48" />
          <w:lsdException w:name="Grid Table 4 Accent 2" w:uiPriority="49" />
          <w:lsdException w:name="Grid Table 5 Dark Accent 2" w:uiPriority="50" />
          <w:lsdException w:name="Grid Table 6 Colorful Accent 2" w:uiPriority="51" />
          <w:lsdException w:name="Grid Table 7 Colorful Accent 2" w:uiPriority="52" />
          <w:lsdException w:name="Grid Table 1 Light Accent 3" w:uiPriority="46" />
          <w:lsdException w:name="Grid Table 2 Accent 3" w:uiPriority="47" />
          <w:lsdException w:name="Grid Table 3 Accent 3" w:uiPriority="48" />
          <w:lsdException w:name="Grid Table 4 Accent 3" w:uiPriority="49" />
          <w:lsdException w:name="Grid Table 5 Dark Accent 3" w:uiPriority="50" />
          <w:lsdException w:name="Grid Table 6 Colorful Accent 3" w:uiPriority="51" />
          <w:lsdException w:name="Grid Table 7 Colorful Accent 3" w:uiPriority="52" />
          <w:lsdException w:name="Grid Table 1 Light Accent 4" w:uiPriority="46" />
          <w:lsdException w:name="Grid Table 2 Accent 4" w:uiPriority="47" />
          <w:lsdException w:name="Grid Table 3 Accent 4" w:uiPriority="48" />
          <w:lsdException w:name="Grid Table 4 Accent 4" w:uiPriority="49" />
          <w:lsdException w:name="Grid Table 5 Dark Accent 4" w:uiPriority="50" />
          <w:lsdException w:name="Grid Table 6 Colorful Accent 4" w:uiPriority="51" />
          <w:lsdException w:name="Grid Table 7 Colorful Accent 4" w:uiPriority="52" />
          <w:lsdException w:name="Grid Table 1 Light Accent 5" w:uiPriority="46" />
          <w:lsdException w:name="Grid Table 2 Accent 5" w:uiPriority="47" />
          <w:lsdException w:name="Grid Table 3 Accent 5" w:uiPriority="48" />
          <w:lsdException w:name="Grid Table 4 Accent 5" w:uiPriority="49" />
          <w:lsdException w:name="Grid Table 5 Dark Accent 5" w:uiPriority="50" />
          <w:lsdException w:name="Grid Table 6 Colorful Accent 5" w:uiPriority="51" />
          <w:lsdException w:name="Grid Table 7 Colorful Accent 5" w:uiPriority="52" />
          <w:lsdException w:name="Grid Table 1 Light Accent 6" w:uiPriority="46" />
          <w:lsdException w:name="Grid Table 2 Accent 6" w:uiPriority="47" />
          <w:lsdException w:name="Grid Table 3 Accent 6" w:uiPriority="48" />
          <w:lsdException w:name="Grid Table 4 Accent 6" w:uiPriority="49" />
          <w:lsdException w:name="Grid Table 5 Dark Accent 6" w:uiPriority="50" />
          <w:lsdException w:name="Grid Table 6 Colorful Accent 6" w:uiPriority="51" />
          <w:lsdException w:name="Grid Table 7 Colorful Accent 6" w:uiPriority="52" />
          <w:lsdException w:name="List Table 1 Light" w:uiPriority="46" />
          <w:lsdException w:name="List Table 2" w:uiPriority="47" />
          <w:lsdException w:name="List Table 3" w:uiPriority="48" />
          <w:lsdException w:name="List Table 4" w:uiPriority="49" />
          <w:lsdException w:name="List Table 5 Dark" w:uiPriority="50" />
          <w:lsdException w:name="List Table 6 Colorful" w:uiPriority="51" />
          <w:lsdException w:name="List Table 7 Colorful" w:uiPriority="52" />
          <w:lsdException w:name="List Table 1 Light Accent 1" w:uiPriority="46" />
          <w:lsdException w:name="List Table 2 Accent 1" w:uiPriority="47" />
          <w:lsdException w:name="List Table 3 Accent 1" w:uiPriority="48" />
          <w:lsdException w:name="List Table 4 Accent 1" w:uiPriority="49" />
          <w:lsdException w:name="List Table 5 Dark Accent 1" w:uiPriority="50" />
          <w:lsdException w:name="List Table 6 Colorful Accent 1" w:uiPriority="51" />
          <w:lsdException w:name="List Table 7 Colorful Accent 1" w:uiPriority="52" />
          <w:lsdException w:name="List Table 1 Light Accent 2" w:uiPriority="46" />
          <w:lsdException w:name="List Table 2 Accent 2" w:uiPriority="47" />
          <w:lsdException w:name="List Table 3 Accent 2" w:uiPriority="48" />
          <w:lsdException w:name="List Table 4 Accent 2" w:uiPriority="49" />
          <w:lsdException w:name="List Table 5 Dark Accent 2" w:uiPriority="50" />
          <w:lsdException w:name="List Table 6 Colorful Accent 2" w:uiPriority="51" />
          <w:lsdException w:name="List Table 7 Colorful Accent 2" w:uiPriority="52" />
          <w:lsdException w:name="List Table 1 Light Accent 3" w:uiPriority="46" />
          <w:lsdException w:name="List Table 2 Accent 3" w:uiPriority="47" />
          <w:lsdException w:name="List Table 3 Accent 3" w:uiPriority="48" />
          <w:lsdException w:name="List Table 4 Accent 3" w:uiPriority="49" />
          <w:lsdException w:name="List Table 5 Dark Accent 3" w:uiPriority="50" />
          <w:lsdException w:name="List Table 6 Colorful Accent 3" w:uiPriority="51" />
          <w:lsdException w:name="List Table 7 Colorful Accent 3" w:uiPriority="52" />
          <w:lsdException w:name="List Table 1 Light Accent 4" w:uiPriority="46" />
          <w:lsdException w:name="List Table 2 Accent 4" w:uiPriority="47" />
          <w:lsdException w:name="List Table 3 Accent 4" w:uiPriority="48" />
          <w:lsdException w:name="List Table 4 Accent 4" w:uiPriority="49" />
          <w:lsdException w:name="List Table 5 Dark Accent 4" w:uiPriority="50" />
          <w:lsdException w:name="List Table 6 Colorful Accent 4" w:uiPriority="51" />
          <w:lsdException w:name="List Table 7 Colorful Accent 4" w:uiPriority="52" />
          <w:lsdException w:name="List Table 1 Light Accent 5" w:uiPriority="46" />
          <w:lsdException w:name="List Table 2 Accent 5" w:uiPriority="47" />
          <w:lsdException w:name="List Table 3 Accent 5" w:uiPriority="48" />
          <w:lsdException w:name="List Table 4 Accent 5" w:uiPriority="49" />
          <w:lsdException w:name="List Table 5 Dark Accent 5" w:uiPriority="50" />
          <w:lsdException w:name="List Table 6 Colorful Accent 5" w:uiPriority="51" />
          <w:lsdException w:name="List Table 7 Colorful Accent 5" w:uiPriority="52" />
          <w:lsdException w:name="List Table 1 Light Accent 6" w:uiPriority="46" />
          <w:lsdException w:name="List Table 2 Accent 6" w:uiPriority="47" />
          <w:lsdException w:name="List Table 3 Accent 6" w:uiPriority="48" />
          <w:lsdException w:name="List Table 4 Accent 6" w:uiPriority="49" />
          <w:lsdException w:name="List Table 5 Dark Accent 6" w:uiPriority="50" />
          <w:lsdException w:name="List Table 6 Colorful Accent 6" w:uiPriority="51" />
          <w:lsdException w:name="List Table 7 Colorful Accent 6" w:uiPriority="52" />
          <w:lsdException w:name="Mention" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Smart Hyperlink" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Hashtag" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Unresolved Mention" w:semiHidden="1" w:unhideWhenUsed="1" />
          <w:lsdException w:name="Smart Link" w:semiHidden="1" w:unhideWhenUsed="1" />
        </w:latentStyles>
        <w:style w:type="paragraph" w:default="1" w:styleId="Normal">
          <w:name w:val="Normal" />
          <w:qFormat />
        </w:style>
        <w:style w:type="character" w:default="1" w:styleId="DefaultParagraphFont">
          <w:name w:val="Default Paragraph Font" />
          <w:uiPriority w:val="1" />
          <w:semiHidden />
          <w:unhideWhenUsed />
        </w:style>
        <w:style w:type="table" w:default="1" w:styleId="TableNormal">
          <w:name w:val="Normal Table" />
          <w:uiPriority w:val="99" />
          <w:semiHidden />
          <w:unhideWhenUsed />
          <w:tblPr>
            <w:tblInd w:w="0" w:type="dxa" />
            <w:tblCellMar>
              <w:top w:w="0" w:type="dxa" />
              <w:left w:w="108" w:type="dxa" />
              <w:bottom w:w="0" w:type="dxa" />
              <w:right w:w="108" w:type="dxa" />
            </w:tblCellMar>
          </w:tblPr>
        </w:style>
        <w:style w:type="numbering" w:default="1" w:styleId="NoList">
          <w:name w:val="No List" />
          <w:uiPriority w:val="99" />
          <w:semiHidden />
          <w:unhideWhenUsed />
        </w:style>
      </w:styles>
    </pkg:xmlData>
  </pkg:part>
  <pkg:part pkg:name="/word/fontTable.xml"
    pkg:contentType="application/vnd.openxmlformats-officedocument.wordprocessingml.fontTable+xml">
    <pkg:xmlData>
      <w:fonts xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"
        xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main"
        xmlns:w14="http://schemas.microsoft.com/office/word/2010/wordml"
        xmlns:w15="http://schemas.microsoft.com/office/word/2012/wordml"
        xmlns:w16cex="http://schemas.microsoft.com/office/word/2018/wordml/cex"
        xmlns:w16cid="http://schemas.microsoft.com/office/word/2016/wordml/cid"
        xmlns:w16="http://schemas.microsoft.com/office/word/2018/wordml"
        xmlns:w16sdtdh="http://schemas.microsoft.com/office/word/2020/wordml/sdtdatahash"
        xmlns:w16se="http://schemas.microsoft.com/office/word/2015/wordml/symex"
        mc:Ignorable="w14 w15 w16se w16cid w16 w16cex w16sdtdh">
        <w:font w:name="Calibri">
          <w:panose1 w:val="020F0502020204030204" />
          <w:charset w:val="00" />
          <w:family w:val="swiss" />
          <w:pitch w:val="variable" />
          <w:sig w:usb0="E0002AFF" w:usb1="C000247B" w:usb2="00000009" w:usb3="00000000"
            w:csb0="000001FF" w:csb1="00000000" />
        </w:font>
        <w:font w:name="DengXian">
          <w:altName w:val="等线" />
          <w:panose1 w:val="02010600030101010101" />
          <w:charset w:val="86" />
          <w:family w:val="auto" />
          <w:pitch w:val="variable" />
          <w:sig w:usb0="A00002BF" w:usb1="38CF7CFA" w:usb2="00000016" w:usb3="00000000"
            w:csb0="0004000F" w:csb1="00000000" />
        </w:font>
        <w:font w:name="Times New Roman">
          <w:panose1 w:val="02020603050405020304" />
          <w:charset w:val="00" />
          <w:family w:val="roman" />
          <w:pitch w:val="variable" />
          <w:sig w:usb0="E0002EFF" w:usb1="C000785B" w:usb2="00000009" w:usb3="00000000"
            w:csb0="000001FF" w:csb1="00000000" />
        </w:font>
        <w:font w:name="DengXian Light">
          <w:altName w:val="等线 Light" />
          <w:panose1 w:val="02010600030101010101" />
          <w:charset w:val="86" />
          <w:family w:val="auto" />
          <w:pitch w:val="variable" />
          <w:sig w:usb0="A00002BF" w:usb1="38CF7CFA" w:usb2="00000016" w:usb3="00000000"
            w:csb0="0004000F" w:csb1="00000000" />
        </w:font>
        <w:font w:name="Calibri Light">
          <w:panose1 w:val="020F0302020204030204" />
          <w:charset w:val="00" />
          <w:family w:val="swiss" />
          <w:pitch w:val="variable" />
          <w:sig w:usb0="E0002AFF" w:usb1="C000247B" w:usb2="00000009" w:usb3="00000000"
            w:csb0="000001FF" w:csb1="00000000" />
        </w:font>
      </w:fonts>
    </pkg:xmlData>
  </pkg:part>
</pkg:package>