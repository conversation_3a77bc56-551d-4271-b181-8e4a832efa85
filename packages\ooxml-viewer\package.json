{"name": "ooxml-viewer", "version": "1.0.0-beta", "description": "office 文档在线预览", "main": "lib/index.js", "module": "esm/index.js", "types": "lib/index.d.ts", "scripts": {"dev": "vite", "build": "npm run clean-dist &&  cross-env NODE_ENV=production rollup -c ", "test": "jest", "lib": "npm run clean-dist && cross-env NODE_ENV=production IS_LIB=1 rollup -c", "update-snapshot": "jest --updateSnapshot", "coverage": "jest --coverage", "declaration": "tsc --project tsconfig-for-declaration.json --allowJs --declaration --emitDeclarationOnly --declarationDir ./lib --rootDir ./src", "clean-dist": "rimraf lib/** esm/** tsconfig.tsbuildinfo .rollup.cache/**", "xsd2ts": "cd tools && ts-node --transpileOnly xsd2ts.ts"}, "exports": {".": {"require": "./lib/index.js", "import": "./esm/index.js"}, "./lib/*": {"require": "./lib/*.js", "import": "./esm/*.js"}}, "files": ["lib", "esm"], "keywords": ["office", "docx"], "license": "Apache 2.0", "bugs": {"url": "https://github.com/baidu/amis/issues"}, "homepage": "https://github.com/baidu/amis#readme", "dependencies": {"cross-env": "7.0.3", "fflate": "^0.7.4", "rimraf": "4.4.1"}, "devDependencies": {"@rollup/plugin-commonjs": "^22.0.2", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^14.1.0", "@rollup/plugin-typescript": "^8.3.4", "@testing-library/jest-dom": "^5.16.4", "@types/jest": "^28.1.0", "fast-xml-parser": "4.1.3", "jest": "^29.0.3", "jest-environment-jsdom": "^29.0.3", "rollup": "^2.60.2", "rollup-plugin-terser": "^7.0.2", "ts-jest": "^29.0.2", "ts-loader": "^9.2.3", "ts-node": "^10.4.0", "typescript": "^4.3.5", "vite": "^3.2.2", "xml-formatter": "^3.3.2"}, "jest": {"testEnvironment": "jsdom", "collectCoverageFrom": ["src/**/*"], "moduleFileExtensions": ["ts", "tsx", "js"], "transform": {"\\.(ts|tsx)$": ["ts-jest", {"diagnostics": false}]}, "setupFiles": ["jest-canvas-mock"], "testRegex": "/.*\\.test\\.(ts|tsx|js)$", "moduleNameMapper": {"\\.(css|less|sass|scss)$": "<rootDir>/../__mocks__/styleMock.js", "\\.(svg)$": "<rootDir>/../__mocks__/svgMock.js"}, "snapshotFormat": {"escapeString": false, "printBasicPrototype": false}}, "publishConfig": {"access": "public", "registry": "http://registry.caijj.net/repository/npm-caijiajia/"}, "authors": ["<EMAIL>"]}