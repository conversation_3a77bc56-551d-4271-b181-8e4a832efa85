import {
  addRule,
  AlertComponent,
  registerFunction,
  render as renderAmis,
  ToastComponent,
} from 'amis';
import React from 'react';
import {
  HashRouter as Router,
  Redirect,
  Route,
  Switch,
} from 'react-router-dom';
import InputExample from './InputExample';
import DynamicRangeTest from './DynamicRangeTest';

import 'amis-utils';

// 扩展 Window 接口以支持 appSchema
declare global {
  interface Window {
    appSchema: any;
  }
}

// import Select from './select';

import appSchema from './appSchema';
import env from './env';

export default class App extends React.PureComponent<{
  location: Location;
}> {
  constructor(props: any) {
    super(props);
  }

  componentDidMount() {
    window.appSchema = appSchema;
    console.log('window.appSchema:', appSchema);

    registerFunction('CUSTOM_FN', (text: string, ...rest: any) => {
      // console.log('CUSTOM_FN', text, rest);
      const str = `${text.toUpperCase()} ${rest.join(',')}`;
      return str;
    });

    addRule('endPriceValueValide', (values, val) => {
      const {index, tierInformation = []} = values || {};

      const up = tierInformation?.[index + 1];

      const down = tierInformation?.[index - 1];

      let msg = '';
      if (val && Number(val) <= Number(values?.pricingStepRageBegin)) {
        msg = '结束数值需大于开始数值';
      } else if (val <= 0) {
        msg = '请输入大于0的数值';
      } else if (
        up &&
        up?.pricingStepRageBegin &&
        values?.pricingStepRageEnd !== up?.pricingStepRageBegin
      ) {
        msg = `第${index + 1}行结束值应该与第${index + 2}行开始值保持一致`;
      }

      if (msg) {
        return {
          error: true,
          msg,
        };
      }

      return true;
    });

    addRule('pricingStepRageBeginValidate', (values, val) => {
      const {index, tierInformation = []} = values || {};
      const stepObj = tierInformation?.[index + 1] || {};
      console.log('RageBegin-index------', index, tierInformation);
      console.log('RageBegin-val------', val);
      const up = tierInformation?.[index + 1];

      const down = tierInformation?.[index - 1];

      let msg = '';
      if (val && Number(val) >= Number(values?.pricingStepRageEnd)) {
        msg = '结束数值需大于开始数值';
      } else if (val <= 0) {
        msg = '请输入大于0的数值';
      } else if (
        down &&
        down.pricingStepRageEnd &&
        values?.pricingStepRageBegin !== down?.pricingStepRageEnd
      ) {
        console.log(
          val?.pricingStepRageBegin !== down?.pricingStepRageEnd,
          'val?.pricingStepRageBegin',
          values?.pricingStepRageBegin,
          down?.pricingStepRageEnd,
        );
        msg = `第${index + 1}行开始值应该与第${index}行结束值保持一致`;
      }

      if (msg) {
        return {
          error: true,
          msg,
        };
      }

      return true;
    });

    addRule('isAdiffB', (values, val) => {
      const {a, b} = values || {};

      console.log('isAdiffB------', values, val);
      let msg = '';
      if (val && Number(a) <= Number(b)) {
        msg = 'a需大于b';
      }

      if (msg) {
        return {
          error: true,
          msg,
        };
      }

      return true;
    });

    addRule('pricingStepRageEndFlagValidate', (values, val) => {
      const {index, tierInformation = []} = values || {};
      console.log(
        'pricingStepRageEndFlagValide-index------',
        index,
        tierInformation,
      );
      console.log('pricingStepRageEndFlagValide-val------', val);
      let msg = '';
      const up = tierInformation?.[index + 1];
      const valFlag = typeof val !== 'undefined';
      const upvalFlag = typeof up?.pricingStepRageBeginFlag !== 'undefined';

      if (valFlag && upvalFlag && val === up.pricingStepRageBeginFlag) {
        msg = '相邻两行区间选择需互斥000';
      }

      if (msg) {
        return {
          error: true,
          msg,
        };
      }

      return true;
    });

    addRule('pricingStepRageBeginFlagValidate', (values, val) => {
      const {index, tierInformation = []} = values || {};
      console.log(
        'pricingStepRageBeginFlagValide-index------',
        index,
        tierInformation,
      );
      console.log('pricingStepRageBeginFlagValide-val------', val);
      let msg = '';

      const down = tierInformation?.[index - 1];
      const valFlag = typeof val !== 'undefined';
      const downFlag = typeof down?.pricingStepRageEndFlag !== 'undefined';

      console.log(
        val,
        'val----',
        down.pricingStepRageEndFlag,
        valFlag && downFlag && val === down.pricingStepRageEndFlag,
      );

      if (valFlag && downFlag && val === down.pricingStepRageEndFlag) {
        msg = '相邻两行区间选择需互斥111';
      }

      if (msg) {
        return {
          error: true,
          msg,
        };
      }

      return true;
    });
  }

  render() {
    return (
      <>
        <Router>
          <Switch>
            <Route
              exact
              path="/"
              render={() => <div>{renderAmis(appSchema, {"standardMode": true}, env)}</div>}
            />
            <Route path="/input-demo" component={InputExample} />
            <Route path="/dynamic-range-test" component={DynamicRangeTest} />
            {/* <Redirect to="/" /> */}
          </Switch>
        </Router>
        <ToastComponent theme={'antd'} locale={'zh-CN'} />
        <AlertComponent theme={'antd'} locale={'zh-CN'} />
      </>
    );
  }
}
