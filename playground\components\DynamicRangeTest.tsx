import React, { useState } from 'react';
import { DsDateRangePicker } from '../../packages/dataseed-ui/src/components/date-picker';

export default function DynamicRangeTest() {
  const [selectedRange, setSelectedRange] = useState<string | null>(null);
  const [dynamicDays, setDynamicDays] = useState(7);

  const handleRangeChange = (value: string | null) => {
    console.log('Range changed:', value);
    setSelectedRange(value);
  };

  const resetRange = () => {
    setSelectedRange(null);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px' }}>
      <h2>动态范围日期选择器测试</h2>

      <div style={{ backgroundColor: '#e6f7ff', padding: '16px', marginBottom: '20px', borderRadius: '6px' }}>
        <p><strong>功能说明：</strong>当用户选择任意一个日期后，另一个日期的可选范围会立即被限制为：该日期前后N天内</p>
        <p><strong>测试步骤：</strong></p>
        <ol>
          <li>设置动态范围天数（默认7天）</li>
          <li>点击第一个日期（比如：2024-01-10）</li>
          <li>观察第二个日期的可选范围是否被限制为：2024-01-03 到 2024-01-17（10号前后7天）</li>
          <li>选择第二个日期后，观察限制是否解除</li>
          <li>清空其中一个日期，观察限制是否重新启用</li>
        </ol>
      </div>

      <div style={{ marginBottom: '32px' }}>
        {/* 配置区域 */}
        <div style={{ backgroundColor: '#fafafa', padding: '16px', marginBottom: '16px', borderRadius: '6px' }}>
          <h3>配置</h3>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <span>动态范围天数：</span>
            <input
              type="number"
              min={1}
              max={30}
              value={dynamicDays}
              onChange={(e) => setDynamicDays(Number(e.target.value) || 7)}
              style={{ width: '80px', padding: '4px' }}
            />
            <span style={{ color: '#666' }}>天</span>
            <button onClick={resetRange} style={{ marginLeft: '16px', padding: '4px 8px' }}>重置选择</button>
          </div>
        </div>

        {/* 动态范围测试 */}
        <div style={{ backgroundColor: '#fafafa', padding: '16px', marginBottom: '16px', borderRadius: '6px' }}>
          <h3>动态范围日期选择器</h3>
          <div style={{ marginBottom: '16px' }}>
            <p><strong>配置：</strong>dynamicRangeDays={dynamicDays}</p>
            <DsDateRangePicker
              dynamicRangeDays={dynamicDays}
              value={selectedRange}
              onChange={handleRangeChange}
              style={{ width: '300px' }}
              showTime={true}
            />
            {selectedRange && (
              <div style={{ backgroundColor: '#f6ffed', padding: '12px', marginTop: '12px', borderRadius: '4px' }}>
                <p><strong>选择范围：</strong>{selectedRange}</p>
              </div>
            )}
          </div>
        </div>

        {/* 对照组：普通日期选择器 */}
        <div style={{ backgroundColor: '#fafafa', padding: '16px', marginBottom: '16px', borderRadius: '6px' }}>
          <h3>普通日期选择器（对照组）</h3>
          <div>
            <p><strong>配置：</strong>无动态范围限制</p>
            <DsDateRangePicker
              style={{ width: '300px' }}
            />
            <p style={{ color: '#666', marginTop: '8px' }}>
              这个选择器没有动态范围限制，可以选择任意日期
            </p>
          </div>
        </div>
      </div>

      <div style={{ backgroundColor: '#fff7e6', padding: '16px', borderRadius: '6px' }}>
        <h4>观察要点</h4>
        <ul>
          <li>点击任意一个日期后，观察另一个日期的可选范围是否立即被限制</li>
          <li>尝试点击超出动态范围的日期，应该被禁用（灰色显示，无法点击）</li>
          <li>选择完两个日期后，观察限制是否解除</li>
          <li>清空其中一个日期，观察限制是否重新启用</li>
          <li>对比普通日期选择器，验证动态范围功能是否正常工作</li>
        </ul>
      </div>
    </div>
  );
}
