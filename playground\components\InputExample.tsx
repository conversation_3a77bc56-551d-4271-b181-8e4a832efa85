import React, { useState } from 'react';
import { types } from 'mobx-state-tree';
import { observer } from 'mobx-react';

// 创建两个不同的 MST store
const StoreB = types.model('StoreB', {
  count: types.number
}).actions(self => ({
  setCount(value: number) {
    console.log('🏪 StoreB: setCount called with', value);
    self.count = value;
  }
}));

const StoreC = types.model('StoreC', {
  value: types.string
}).actions(self => ({
  setValue(value: string) {
    console.log('🏬 StoreC: setValue called with', value);
    self.value = value;
  }
}));

const storeB = StoreB.create({ count: 0 });
const storeC = StoreC.create({ value: 'initial' });

// A组件 - 顶层组件
class ComponentA extends React.Component<{}, { counter: number }> {
  constructor(props: any) {
    super(props);
    this.state = { counter: 0 };
  }

  handleClick = () => {
    console.log('\n🔥 =========================');
    console.log('🔥 A组件状态更新，触发B和C的props变化');
    console.log('🔥 =========================');
    this.setState({ counter: this.state.counter + 1 });
  };

  render() {
    console.log('🅰️ ComponentA: render, counter =', this.state.counter);
    return (
      <div style={{ border: '2px solid blue', padding: '10px', margin: '10px' }}>
        <h2>Component A (顶层组件)</h2>
        <p>Counter: {this.state.counter}</p>
        <button
          onClick={this.handleClick}
          style={{
            padding: '8px 16px',
            marginBottom: '10px',
            backgroundColor: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          点击触发重新渲染
        </button>
        <ComponentB counter={this.state.counter} />
      </div>
    );
  }
}

// B组件 - 在constructor中修改storeB，同时渲染C组件
@observer
class ComponentB extends React.Component<{ counter: number }> {
  constructor(props: any) {
    super(props);
    console.log('🅱️ ComponentB: constructor start');

    // 🔥 在constructor中修改StoreB
    const randomValue = Math.floor(Math.random() * 100);
    storeB.setCount(randomValue);

    console.log('🅱️ ComponentB: constructor end');
  }

  UNSAFE_componentWillReceiveProps(nextProps: { counter: number }) {
    console.log('🅱️ ComponentB: componentWillReceiveProps, props.counter:', this.props.counter, '→', nextProps.counter);

    // 🧪 关键测试：在componentWillReceiveProps中修改store
    if (this.props.counter !== nextProps.counter) {
      console.log('🧪 ComponentB: 在componentWillReceiveProps中修改storeB');
      const newValue = storeB.count + nextProps.counter * 10;
      storeB.setCount(newValue);
    }
  }

  componentDidMount() {
    console.log('🅱️ ComponentB: componentDidMount');
  }

  componentDidUpdate() {
    console.log('🅱️ ComponentB: componentDidUpdate');
  }

  render() {
    console.log('🅱️ ComponentB: render called, storeB.count =', storeB.count);
    return (
      <div style={{ border: '2px solid red', padding: '10px', margin: '10px' }}>
        <h3>Component B (中层组件)</h3>
        <p><strong>Props Counter: {this.props.counter}</strong></p>
        <p><strong>StoreB Count: {storeB.count}</strong></p>
        <ComponentC counter={this.props.counter} />
      </div>
    );
  }
}

// C组件 - 在constructor中修改storeC
@observer
class ComponentC extends React.Component<{ counter: number }> {
  constructor(props: any) {
    super(props);
    console.log('🅲 ComponentC: constructor start');

    // 🔥 在constructor中修改StoreC（另一个store）
    const randomString = `value-${Math.floor(Math.random() * 1000)}`;
    storeC.setValue(randomString);

    console.log('🅲 ComponentC: constructor end');
  }

  UNSAFE_componentWillReceiveProps(nextProps: { counter: number }) {
    console.log('🅲 ComponentC: componentWillReceiveProps, props.counter:', this.props.counter, '→', nextProps.counter);

    // 🧪 关键测试：在componentWillReceiveProps中修改store
    if (this.props.counter !== nextProps.counter) {
      console.log('🧪 ComponentC: 在componentWillReceiveProps中修改storeC');
      const newValue = `updated-${nextProps.counter}-${Math.floor(Math.random() * 100)}`;
      storeC.setValue(newValue);
    }
  }

  componentDidMount() {
    console.log('🅲 ComponentC: componentDidMount');
  }

  componentDidUpdate() {
    console.log('🅲 ComponentC: componentDidUpdate');
  }

  render() {
    console.log('🅲 ComponentC: render called, storeC.value =', storeC.value);
    return (
      <div style={{ border: '2px solid green', padding: '10px', margin: '10px' }}>
        <h4>Component C (底层组件)</h4>
        <p><strong>Props Counter: {this.props.counter}</strong></p>
        <p><strong>StoreC Value: {storeC.value}</strong></p>
        <p>👀 查看控制台，观察render被调用的次数和顺序</p>
      </div>
    );
  }
}

// 测试用的App组件
function App() {
  const [renderKey, setRenderKey] = useState(0);

  const triggerRerender = () => {
    console.log('\n🔄 =========================');
    console.log('🔄 触发重新渲染，key =', renderKey + 1);
    console.log('🔄 =========================');
    setRenderKey(k => k + 1);
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>MST componentWillReceiveProps 渲染测试</h1>

      <div style={{ backgroundColor: '#f8f9fa', padding: '15px', borderRadius: '4px', marginBottom: '20px' }}>
        <h3>测试说明：</h3>
        <ul>
          <li><strong>组件层级：</strong> A → B → C</li>
          <li><strong>初始化：</strong> B和C在constructor中修改各自的store</li>
          <li><strong>关键测试：</strong> 点击A组件的绿色按钮，触发props变化</li>
          <li><strong>ComponentB</strong> 在 componentWillReceiveProps 中修改 <span style={{color: 'red'}}>StoreB</span></li>
          <li><strong>ComponentC</strong> 在 componentWillReceiveProps 中修改 <span style={{color: 'green'}}>StoreC</span></li>
          <li>观察控制台输出，看各组件的 render 被调用几次和顺序</li>
          <li><strong>预期：</strong> 在componentWillReceiveProps中修改store可能会导致额外的渲染</li>
        </ul>
      </div>

      <ComponentA key={renderKey} />

      <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#e9ecef', borderRadius: '4px' }}>
        <div style={{ marginBottom: '10px' }}>
          <strong>当前 StoreB 值: </strong>
          <span style={{ color: 'red', fontWeight: 'bold' }}>{storeB.count}</span>
        </div>
        <div>
          <strong>当前 StoreC 值: </strong>
          <span style={{ color: 'green', fontWeight: 'bold' }}>{storeC.value}</span>
        </div>
      </div>
    </div>
  );
}

export default App;
