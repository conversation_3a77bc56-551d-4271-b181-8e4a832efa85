import axios from "axios";
import copy from 'copy-to-clipboard';
import qs from 'qs';
import { normalizeLink } from 'amis';

import { toast } from 'amis-ui';

export default {
  // 下面三个接口必须实现
  // fetcher: ({
  //   url, // 接口地址
  //   method, // 请求方法 get、post、put、delete
  //   data, // 请求数据
  //   responseType,
  //   config, // 其他配置
  //   headers // 请求头
  // }: any) => {
  //   config = config || {};
  //   config.withCredentials = true;
  //   responseType && (config.responseType = responseType);

  //   if (config.cancelExecutor) {
  //     config.cancelToken = new (axios as any).CancelToken(
  //       config.cancelExecutor
  //     );
  //   }

  //   config.headers = headers || {};
  //   // NOTE: 有代理sit环境时开启，不用时必须关掉
  //   // config.headers['X-TOKEN'] = localStorage.getItem('X-TOKEN');

  //   if (method !== 'post' && method !== 'put' && method !== 'patch') {
  //     if (data) {
  //       config.params = data;
  //     }

  //     return (axios as any)[method](url, config);
  //   } else if (data && data instanceof FormData) {
  //     config.headers = config.headers || {};
  //     config.headers['Content-Type'] = 'multipart/form-data';
  //   } else if (
  //     data &&
  //     typeof data !== 'string' &&
  //     !(data instanceof Blob) &&
  //     !(data instanceof ArrayBuffer)
  //   ) {
  //     data = JSON.stringify(data);
  //     config.headers = config.headers || {};
  //     config.headers['Content-Type'] = 'application/json';
  //   }

  //   return (axios as any)[method](url, data, config);
  // },
  // 跟play.tsx保持一致
  fetcher: async api => {
    let {url, method, data, responseType, config, headers, dataProvider, tdata} = api;
    config = config || {};

    // 配置是否是 dataProvider 的场景是用来渲染动态schema，不用要请求接口
    if (dataProvider) {
      const _result = { data: tdata };
      if(typeof dataProvider === 'function') {
        _result.data = dataProvider(tdata);
      }
      return Promise.resolve(_result);
    }

    // 如果在 gh-pages 里面
    if (
      /^\/amis/.test(window.location.pathname) &&
      typeof url === 'string' &&
      url.startsWith('/examples/static/')
    ) {
      url = url.replace('/examples/static/', '/amis/static/');
    }

    config.url = url;
    responseType && (config.responseType = responseType);

    if (config.cancelExecutor) {
      config.cancelToken = new axios.CancelToken(config.cancelExecutor);
    }

    config.headers = headers || {};
    config.method = method;
    config.data = data;

    if (method === 'get' && data) {
      config.params = data;
    } else if (data && data instanceof FormData) {
      // config.headers['Content-Type'] = 'multipart/form-data';
    } else if (
      data &&
      typeof data !== 'string' &&
      !(data instanceof Blob) &&
      !(data instanceof ArrayBuffer)
    ) {
      data = JSON.stringify(data);
      config.headers['Content-Type'] = 'application/json';
    }

    // 支持返回各种报错信息
    config.validateStatus =
      config.validateStatus ||
      function () {
        return true;
      };

    let response = await axios(config);
    // response = await attachmentAdpator(response, __);

    if (response.status >= 400) {
      if (response.data) {
        // 主要用于 raw: 模式下，后端自己校验登录，
        if (
          response.status === 401 &&
          response.data.location &&
          response.data.location.startsWith('http')
        ) {
          location.href = response.data.location.replace(
            '{{redirect}}',
            encodeURIComponent(location.href)
          );
          return new Promise(() => {});
        } else if (response.data.msg) {
          throw new Error(response.data.msg);
        } else {
          throw new Error(
            __('System.requestError') +
              JSON.stringify(response.data, null, 2)
          );
        }
      } else {
        throw new Error(
          `${__('System.requestErrorStatus')} ${response.status}`
        );
      }
    }

    return response;
  },
  isCancel: (value: any) => (axios as any).isCancel(value),
  copy: (content: any) => {
    copy(content);
    toast.success('内容已复制到粘贴板');
  },
  theme: 'antd',

  // 后面这些接口可以不用实现

  // 默认是地址跳转
  jumpTo: (to, action) => {
    if (to === 'goBack') {
      return history.location.goBack();
    }
    to = normalizeLink(to);
    if (action && action.actionType === 'url') {
      !action.blank
        ? (window.location.href = to)
        : window.open(to);
      return;
    }
    if (action && to && action.target) {
      window.open(to, action.target);
      return;
    }
    if (/^https?:\/\//.test(to)) {
      window.location.replace(to);
    } else {
      history.push(to);
    }
  },

  updateLocation: (
    url: string /*目标地址*/,
    replace: boolean /*是replace，还是push？*/
  ) => {
    // 地址替换，跟 jumpTo 类似
    // const hash = window.location.href.split('#')?.[1];
    // const hashurl = window.location.href.split('?')?.[0];
    const paramsurl = window.location.href.split('?')?.[1];
    const query = qs.parse(paramsurl);
    const newQuery = qs.parse(url?.split('?')?.[1] || '');
    const obj = { ...query, ...newQuery };
    const newStr = qs.stringify(obj);
    const jumpUrl = `${window.location.origin}${window.location.pathname}?${newStr}`;

    console.log(jumpUrl, 'jumpUrl')

    if (url === 'goBack') {
      return window.history.back();
    }

    if (replace && window.history.replaceState) {
      window.history.replaceState('', document.title, jumpUrl);
    }

    location.href = jumpUrl;
  },

  // isCurrentUrl: (
  //   url: string /*url地址*/,
  // ) => {
  //   // 用来判断是否目标地址当前地址
  // },

  // notify: (
  //   type: 'error' | 'success' /**/,
  //   msg: string /*提示内容*/
  // ) => {
  //   toast[type]
  //     ? toast[type](msg, type === 'error' ? '系统错误' : '系统消息')
  //     : console.warn('[Notify]', type, msg);
  // },
  // alert,
  // confirm,
  // tracker: (eventTracke) => {}
}
