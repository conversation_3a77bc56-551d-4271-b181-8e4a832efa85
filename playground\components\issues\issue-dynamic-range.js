export default {
  type: 'page',
  title: '动态范围日期选择器测试',
  body: [
    {
      type: 'alert',
      level: 'info',
      body: [
        {
          type: 'tpl',
          tpl: `
            <p><strong>动态范围功能：</strong>当用户选择一个日期范围后，可选的日期范围会自动调整为：从结束日期往前数N天到结束日期往后数N天</p>
            <p><strong>测试步骤：</strong></p>
            <ol>
              <li>设置动态范围天数（默认7天）</li>
              <li>选择一个日期范围（比如：2024-01-10 到 2024-01-15）</li>
              <li>观察日期选择器的可选范围是否变为：2024-01-08 到 2024-01-22（15号前后7天）</li>
            </ol>
          `
        }
      ]
    },
    {
      type: 'form',
      title: '动态范围测试',
      debug: true,
      body: [
        {
          type: 'input-number',
          name: 'dynamicDays',
          label: '动态范围天数',
          value: 7,
          min: 1,
          max: 30,
          description: '设置选择范围后的动态可选天数'
        },
        {
          type: 'ds-date-range-picker',
          name: 'dynamicRange',
          label: '动态范围日期选择器',
          dynamicRangeDays: '${dynamicDays}',
          allowClear: true,
          description: '选择完整日期范围后，可选范围会动态调整'
        }
      ]
    },
    {
      type: 'form',
      title: '普通日期选择器（对照组）',
      body: [
        {
          type: 'ds-date-range-picker',
          name: 'normalRange',
          label: '普通日期选择器',
          allowClear: true,
          description: '这个选择器没有动态范围限制，可以选择任意日期'
        }
      ]
    },
    {
      type: 'alert',
      level: 'warning',
      body: [
        {
          type: 'tpl',
          tpl: `
            <strong>观察要点：</strong>
            <ul>
              <li>选择完整日期范围后，再次打开日期选择器，观察可选日期是否被限制</li>
              <li>尝试选择超出动态范围的日期，应该被禁用</li>
              <li>修改动态范围天数，观察可选范围的变化</li>
              <li>对比普通日期选择器的行为差异</li>
            </ul>
          `
        }
      ]
    }
  ]
}
