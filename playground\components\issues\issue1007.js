const demo = {
  "type": "page",
  "body": {
    "type": "container",
    "draggable": true,
    "body": {
      "type": "service",
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=5",
      "body": [
        {
          "type": "table",
          "source": "$rows",
          "draggable": true,
          "columns": [
            {
              "name": "engine",
              "label": "Engine"
            },
            {
              "name": "version",
              "label": "Version"
            }
          ]
        }
      ]
    }
  }
}

// 配置 draggableConfig 的 handle
const demo2 = {
  "type": "page",
  "body": {
    "type": "container",
    "draggable": true,
    "draggableConfig": {
      "handle": ".dragHandle"
    },
    "body": {
      "type": "service",
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=5",
      "body": [
        {
          "type": "button",
          "className": "dragHandle",
          "label": "drag"
        },
        {
          "type": "table",
          "source": "$rows",
          "draggable": true,
          "columns": [
            {
              "name": "engine",
              "label": "Engine"
            },
            {
              "name": "version",
              "label": "Version"
            }
          ]
        }
      ]
    }
  }
}

export default demo;
