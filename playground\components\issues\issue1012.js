const demo = {
  "type": "page",
  "body": [
    {
      "type": "crud",
      "name": "crud",
      "syncLocation": false,
      "api": "/api/mock2/crud/table4",
      "topToolbar": [
        {
          "type": "button",
          "label": "新建",
          "actionType": "url",
          "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
          "level": "primary",
          "blank": false
        }
      ],
      // "canAccessSuperData": false,
      "updateAllRows": true,
      "filter": {
        "debug": true,
        "title": "",
        "body": [
          {
            "type": "group",
            "mode": "horizontal",
            "body": [
              {
                "type": "input-text",
                "name": "keywords",
                "label": "关键字",
                "clearable": true,
                "placeholder": "通过关键字搜索",
                "columnRatio": 4
              },
              {
                "type": "input-text",
                "name": "engine",
                "label": "Engine",
                "clearable": true,
                "columnRatio": 4
              },
              {
                "type": "input-text",
                "name": "platform",
                "label": "Platform",
                "clearable": true,
                "columnRatio": 4
              },
              {
                "type": "input-text",
                "name": "keywords1",
                "label": "关键字1",
                "clearable": true,
                "placeholder": "通过关键字搜索",
                "columnRatio": 4
              },
              {
                "type": "input-text",
                "name": "engine1",
                "label": "Engine1",
                "clearable": true,
                "columnRatio": 4
              },
              {
                "type": "input-text",
                "name": "platform1",
                "label": "Platform1",
                "clearable": true,
                "columnRatio": 4
              },
              {
                "type": "input-text",
                "name": "keywords2",
                "label": "关键字2",
                "clearable": true,
                "placeholder": "通过关键字搜索",
                "columnRatio": 4
              },
              {
                "type": "input-text",
                "name": "engine2",
                "label": "Engine2",
                "clearable": true,
                "columnRatio": 4
              },
              {
                "type": "input-text",
                "name": "platform2",
                "label": "Platform2",
                "clearable": true,
                "columnRatio": 4
              }
            ]
          }
        ],
        actions: [
          {
            "type": "reset",
            "label": "重 置"
          },
          {
            "type": "submit",
            "level": "primary",
            "label": "查 询"
          }
        ]
      },
      "columns": [
        {
          "name": "id",
          "label": "ID"
        },
        {
          "name": "engine",
          "label": "Rendering engine",
          "headSearchable": true,
          // "type": "tpl",
          // "body": "${engine}",
          "canAccessSuperData": false
        },
        {
          "name": "browser",
          "label": "Browser",
          "headSearchable": {
            "type": "input-text",
            "name": "browser",
            "label": "Browser"
          }
        },
        {
          "name": "platform",
          "label": "Platform(s)"
        },
        {
          "name": "version",
          "label": "Engine version"
        },
        {
          "name": "grade",
          "label": "CSS grade"
        }
      ]
    }
  ]
}

export default demo;
