const demo = {
  "type": "page",
  "id": "pageid",
  "data": {
    "myItems": [
      {
        "id": 1
      },
      {
        "id": 2
      },
      {
        "id": 3
      },
      {
        "id": 4
      },
      {
        "id": 5
      },
      {
        "id": 6
      },
      {
        "id": 7
      },
      {
        "id": 8
      },
      {
        "id": 9
      },
      {
        "id": 10
      },
      {
        "id": 11
      },
      {
        "id": 12
      },
      {
        "id": 13
      }
    ]
  },
  "body": [
    {
      "type": "button",
      "label": "更新crud数据",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "setValue",
              "componentId": "pageid",
              "args": {
                "value": {
                  "myItems": [
                    {
                      "id": 1
                    },
                    {
                      "id": 2
                    },
                    {
                      "id": 3
                    },
                    {
                      "id": 4
                    },
                    {
                      "id": 5
                    },
                    {
                      "id": 6
                    },
                    {
                      "id": 7
                    },
                    {
                      "id": 8
                    },
                    {
                      "id": 9
                    },
                    {
                      "id": 10
                    },
                    {
                      "id": 11
                    },
                    {
                      "id": 12
                    }
                  ]
                }
              }
            },
            // {
            //   "actionType": "changePage",
            //   "componentId": "crudid",
            //   "args": {
            //     "page": 1,
            //     "perPage": 10
            //   }
            // }
          ]
        }
      }
    },
    {
      "type": "crud",
      "id": "crudid",
      "source": "${myItems}",
      "syncLocation": false,
      "columns": [
        {
          "name": "id",
          "label": "ID"
        }
      ]
    }
  ]
}

export default demo;
