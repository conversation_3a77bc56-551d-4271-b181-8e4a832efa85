const demo = {
  "type": "page",
  "body": {
    "type": "form",
    "labelWidth": 40,
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "body": [
      {
        "type": "input-tree",
        "name": "tree",
        "label": "Tree",
        "searchable": true,
        "creatable": true,
        "removable": true,
        "editable": true,
        "options": [
          {
            "label": "Folder A",
            "value": 1,
            "children": [
              {
                "label": "file A",
                "value": 2
              },
              {
                "label": "Folder B",
                "value": 3,
                "children": [
                  {
                    "label": "file b1",
                    "value": 3.1
                  },
                  {
                    "label": "file b2",
                    "value": 3.2
                  }
                ]
              }
            ]
          },
        ]
      },
    ]
  }
}

const demo2 = {
  "type": "page",
  "body": {
    "type": "form",
    "labelWidth": 40,
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "body": [
      {
        "type": "wrapper",
        "body": {
          "type": "input-tree",
          "name": "tree",
          "label": "Tree",
          "searchable": true,
          "multiple": true,
          "editApi": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
          "autoFillHeight": true,
          "treeContainerClassName": "h-80 max-h-none",
          "virtualThreshold": 9,
          "source": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/tree/search"
        }
      }
    ]
  }
}

export default {
  "type": "page",
  "body": {
    "type": "container",
    "body": "这里是容器内容区",
    "draggable": true,
    // "draggableConfig": {
    //   "bounds": {
    //     "left": 0,
    //     "top": 0,
    //     "right": 0,
    //     "bottom": 0
    //   }
    // }
  }
};
