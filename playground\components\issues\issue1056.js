import { data1 } from '../../mock';

const demo = {
  "type": "page",
  // "standardMode": true,
  "body": {
    "type": "crud",
    "syncLocation": false,
    // "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
    "api": {
      "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
      adaptor(_, __, request) {
        const { query } = request;
        const { page, perPage } = query;
        // 从data1.list中截取对应页码的数据
        const data = data1.list.slice((page - 1) * perPage, page * perPage);
        console.log('data', data)
        // mock数据
        return {
          code: 0,
          "data": {
            rows: data,
            count: data1.totalCount,
          },
        }
      }
    },
    simplify: true,
    // "api": {
    //   "url": "/telmarketcore/workstation/case/person/list",
    //   "method": "post",
    //   "responseData": {
    //     "&": "$$",
    //     "total": "${totalCount}",
    //   }
    // },
    // pageField: 'pageNo',
    // perPageField: 'pageSize',
    // "defaultParams": {
    //   caseStatusList: [3],
    //   latestDistributionTimeEnd: "2025-02-24 23:59:59",
    //   latestDistributionTimeStart: "2025-02-10 00:00:00",
    // },
    "columns": [
      // {
      //     label: 'id',
      //     name: 'id',
      //     // type: 'typography',
      //   // "copyable": true,
      //   // ellipsis: true,
      //   ellipsis: {
      //     tooltip: {
      //       content: "xxxx ${id}"
      //     }
      //   }
      // },
      // {
      //   label: 'browser2',
      //   width: 200,
      //   ellipsis: true,
      //   type: 'container',
      //   body: [
      //     {
      //       type: 'tpl',
      //       tpl: '${browser} 房东沙发房东该生动阿盖僧僧赛',
      //     },
      //     {
      //       type: 'tpl',
      //       tpl: '222222 ${browser} 房东沙发房东该生动阿盖僧僧赛',
      //     }
      //   ]
      //   // ellipsis: {
      //   //   tooltip: 'xxxxx',
      //   // },
      // },
      // {
      //   label: 'browser3',
      //   name: 'browser',
      //   // ellipsis: {
      //   //   width: '100px',
      //   //   tooltip: 'xxxxx',
      //   // },
      // },
      // {
      //   label: 'browser',
      //   name: 'browser',
      //   // ellipsis: {
      //   //   width: '100px',
      //   //   tooltip: 'xxxxx',
      //   // },
      // },
      // {
      //   label: 'browser',
      //   name: 'browser',
      //   // ellipsis: {
      //   //   width: '100px',
      //   //   tooltip: 'xxxxx',
      //   // },
      // },
      // {
      //   label: 'browser',
      //   name: 'browser',
      //   // ellipsis: {
      //   //   width: '100px',
      //   //   tooltip: 'xxxxx',
      //   // },
      // },
      // {
      //   label: 'browser',
      //   name: 'browser',
      //   // ellipsis: {
      //   //   tooltip: 'xxxxx',
      //   // },
      // },
      // {
      //   label: 'browser',
      //   name: 'browser',
      //   // ellipsis: {
      //   //   width: '100px',
      //   //   tooltip: 'xxxxx',
      //   // },
      // },
      // {
      //   label: 'browser',
      //   name: 'browser',
      //   // ellipsis: {
      //   //   width: '100px',
      //   //   tooltip: 'xxxxx',
      //   // },
      // },
      // {
      //   label: 'browser',
      //   name: 'browser',
      //   // ellipsis: {
      //   //   width: '100px',
      //   //   tooltip: 'xxxxx',
      //   // },
      // },
      // {
      //   label: 'browser',
      //   name: 'browser',
      //   // ellipsis: {
      //   //   width: '100px',
      //   //   tooltip: 'xxxxx',
      //   // },
      // },
      // =========================== 最简 1.5s ===========================
      // {
      //   label: 'id',
      //   name: 'id',
      // },
      // {
      //   label: 'id',
      //   name: 'id',
      // },
      // {
      //   label: 'id',
      //   name: 'id',
      // },
      // {
      //   label: 'id',
      //   name: 'id',
      // },
      // {
      //   label: 'id',
      //   name: 'id',
      // },
      // {
      //   label: 'id',
      //   name: 'id',
      // },
      // {
      //   label: 'id',
      //   name: 'id',
      // },
      // {
      //   label: 'id',
      //   name: 'id',
      // },
      // {
      //   label: 'id',
      //   name: 'id',
      // },
      // {
      //   label: 'id',
      //   name: 'id',
      // },
      // ================ 10个wrapper套4层，3.4s ================
      // {
      //   type: 'wrapper',
      //   body: {
      //     type: 'wrapper',
      //     body: {
      //       type: 'wrapper',
      //       body: {
      //         type: 'wrapper',
      //         body: {
      //           type: 'tpl',
      //           tpl: '${id}',
      //         }
      //       }
      //     }
      //   }
      // },
      // {
      //   type: 'wrapper',
      //   body: {
      //     type: 'wrapper',
      //     body: {
      //       type: 'wrapper',
      //       body: {
      //         type: 'wrapper',
      //         body: {
      //           type: 'tpl',
      //           tpl: '${id}',
      //         }
      //       }
      //     }
      //   }
      // },
      // {
      //   type: 'wrapper',
      //   body: {
      //     type: 'wrapper',
      //     body: {
      //       type: 'wrapper',
      //       body: {
      //         type: 'wrapper',
      //         body: {
      //           type: 'tpl',
      //           tpl: '${id}',
      //         }
      //       }
      //     }
      //   }
      // },
      // {
      //   type: 'wrapper',
      //   body: {
      //     type: 'wrapper',
      //     body: {
      //       type: 'wrapper',
      //       body: {
      //         type: 'wrapper',
      //         body: {
      //           type: 'tpl',
      //           tpl: '${id}',
      //         }
      //       }
      //     }
      //   }
      // },
      // {
      //   type: 'wrapper',
      //   body: {
      //     type: 'wrapper',
      //     body: {
      //       type: 'wrapper',
      //       body: {
      //         type: 'wrapper',
      //         body: {
      //           type: 'tpl',
      //           tpl: '${id}',
      //         }
      //       }
      //     }
      //   }
      // },
      // {
      //   type: 'wrapper',
      //   body: {
      //     type: 'wrapper',
      //     body: {
      //       type: 'wrapper',
      //       body: {
      //         type: 'wrapper',
      //         body: {
      //           type: 'tpl',
      //           tpl: '${id}',
      //         }
      //       }
      //     }
      //   }
      // },
      // {
      //   type: 'wrapper',
      //   body: {
      //     type: 'wrapper',
      //     body: {
      //       type: 'wrapper',
      //       body: {
      //         type: 'wrapper',
      //         body: {
      //           type: 'tpl',
      //           tpl: '${id}',
      //         }
      //       }
      //     }
      //   }
      // },
      // {
      //   type: 'wrapper',
      //   body: {
      //     type: 'wrapper',
      //     body: {
      //       type: 'wrapper',
      //       body: {
      //         type: 'wrapper',
      //         body: {
      //           type: 'tpl',
      //           tpl: '${id}',
      //         }
      //       }
      //     }
      //   }
      // },
      // {
      //   type: 'wrapper',
      //   body: {
      //     type: 'wrapper',
      //     body: {
      //       type: 'wrapper',
      //       body: {
      //         type: 'wrapper',
      //         body: {
      //           type: 'tpl',
      //           tpl: '${id}',
      //         }
      //       }
      //     }
      //   }
      // },
      // {
      //   type: 'wrapper',
      //   body: {
      //     type: 'wrapper',
      //     body: {
      //       type: 'wrapper',
      //       body: {
      //         type: 'wrapper',
      //         body: {
      //           type: 'tpl',
      //           tpl: '${id}',
      //         }
      //       }
      //     }
      //   }
      // },
      // =========================== 排版组件10个 4.2s ===========================
      // {
      //   label: 'typography id',
      //   type: 'typography',
      //   name: 'id',
      // },
      // {
      //   label: 'id',
      //   type: 'typography',
      //   name: 'id',
      // },
      // {
      //   label: 'id',
      //   type: 'typography',
      //   name: 'id',
      // },
      // {
      //   label: 'id',
      //   type: 'typography',
      //   name: 'id',
      // },
      // {
      //   label: 'id',
      //   type: 'typography',
      //   name: 'id',
      // },
      // {
      //   label: 'id',
      //   type: 'typography',
      //   name: 'id',
      // },
      // {
      //   label: 'id',
      //   type: 'typography',
      //   name: 'id',
      // },
      // {
      //   label: 'id',
      //   type: 'typography',
      //   name: 'id',
      // },
      // {
      //   label: 'id',
      //   type: 'typography',
      //   name: 'id',
      // },
      // {
      //   label: 'id',
      //   type: 'typography',
      //   name: 'id',
      // },
      // =========================== mapping组件10个 2.7s ===========================
      // {
      //   label: 'mapping id',
      //   type: 'mapping',
      //   map: {
      //     '*': '${id}',
      //   },
      //   name: 'id',
      // },
      // {
      //   label: 'id',
      //   type: 'mapping',
      //   map: {
      //     '*': '${id}',
      //   },
      //   name: 'id',
      // },
      // {
      //   label: 'id',
      //   type: 'mapping',
      //   map: {
      //     '*': '${id}',
      //   },
      //   name: 'id',
      // },
      // {
      //   label: 'id',
      //   type: 'mapping',
      //   map: {
      //     '*': '${id}',
      //   },
      //   name: 'id',
      // },
      // {
      //   label: 'id',
      //   type: 'mapping',
      //   map: {
      //     '*': '${id}',
      //   },
      //   name: 'id',
      // },
      // {
      //   label: 'id',
      //   type: 'mapping',
      //   map: {
      //     '*': '${id}',
      //   },
      //   name: 'id',
      // },
      // {
      //   label: 'id',
      //   type: 'mapping',
      //   map: {
      //     '*': '${id}',
      //   },
      //   name: 'id',
      // },
      // {
      //   label: 'id',
      //   type: 'mapping',
      //   map: {
      //     '*': '${id}',
      //   },
      //   name: 'id',
      // },
      // {
      //   label: 'id',
      //   type: 'mapping',
      //   map: {
      //     '*': '${id}',
      //   },
      //   name: 'id',
      // },
      // {
      //   label: 'id',
      //   type: 'mapping',
      //   map: {
      //     '*': '${id}',
      //   },
      //   name: 'id',
      // },
      // ============ 业务字段 =============
      {
        name: 'name',
        label: '姓名',
        width: 150,
        fixed: 'left',
        // type: 'typography',
        ellipsis: {
          rows: 1
        },
        toggable: false,
      },
      {
        name: 'modelScoreGroup',
        label: '模型分分组',
        sortable: true,
        type: 'mapping',
        map: {
          // http://jira.caijj.net/projects/BUG/issues/BUG-13576?filter=myopenissues
          // 模型分分组为0时，没有实际意义，列表上的模型分分组改为显示为"-"
          0: {
            // 利用 static 显示默认的 placeholder
            type: 'static',
          },
          '*': '${modelScoreGroup}',
        },
      },
      {
        name: 'callTimes',
        label: '拨打次数',
        sortable: true,
      },
      {
        label: '已接通次数',
        name: 'connectTimes',
        sortable: true,
      },
      {
        label: '最近一次通话小结',
        name: 'summaryCodeNames',
        type: 'each',
        className: 'casePools-table-tags__addMargin',
        width: 200,
        placeholder: '-',
        items: {
          type: 'tag',
          label: '${item}',
          displayMode: 'bordered',
          color: 'running',
        },
      },
      {
        label: '会话节点',
        name: 'sessionNode',
        type: 'mapping',
        width: 80,
        map: {
          1: '开场白',
          2: '产品介绍',
          3: '流程引导',
          4: '同意邀约',
        },
      },
      {
        label: '拨打备注',
        name: 'callRemark',
        // type: 'typography',
        width: 150,
        ellipsis: {
          rows: 2
        }
      },
      {
        label: '用户可用额度',
        name: 'availableLimit',
        sortable: true,
        type: 'number',
        kilobitSeparator: true,
      },
      {
        label: '最近打开APP时间',
        name: 'latestOpenAppTime',
        sortable: true,
        width: 180,
        // type: 'typography',
        ellipsis: {
          rows: 1
        },
      },
      {
        label: '预约时间',
        name: 'latestBookingTime',
        sortable: true,
        width: 180,
        // type: 'typography',
        ellipsis: {
          rows: 1
        },
      },
      {
        label: '收藏标志',
        name: 'favLevel',
        sortable: true,
        type: 'mapping',
        map: {
          1: '一星',
          2: '二星',
          3: '三星',
        },
      },
      {
        label: '最近一次拨打时间',
        sortable: true,
        name: 'latestCallTime',
        width: 180,
        // type: 'typography',
        ellipsis: {
          rows: 1
        },
      },
      {
        label: '剩余经营天数',
        sortable: true,
        name: 'leftOperatingDays',
      },
      {
        label: '规则码',
        name: 'ruleCodeName',
        width: 180,
        // type: 'typography',
        ellipsis: {
          rows: 1
        },
      },
      {
        label: '案件状态',
        name: 'caseStatus',
        type: 'mapping',
        map: {
          // 0: '初始化',
          // 1: '待分配',
          3: '跟进中',
          4: '已结案',
        },
      },
      {
        label: '跟进人',
        name: 'assignedAgentName',
        width: 150,
        // type: 'typography',
        ellipsis: {
          rows: 1
        },
      },
      {
        label: '最近1笔订单时间',
        sortable: true,
        name: 'latestOrderTime',
        width: 180,
        // type: 'typography',
        ellipsis: {
          rows: 1
        },
      },
      {
        label: '最近1笔订单状态',
        name: 'latestOrderStatus',
        type: 'mapping',
        map: {
          1: '成功',
          2: '失败',
          3: '审核中',
        },
      },
      {
        label: '最近1笔交易时间',
        sortable: true,
        name: 'latestDealTime',
        width: 180,
        // type: 'typography',
        ellipsis: {
          rows: 1
        },
      },
      {
        label: '主管推荐',
        name: 'recommendStatus',
        type: 'mapping',
        map: {
          0: '未推荐',
          1: '生效中',
          2: '失效',
        },
      },
      {
        label: '首次分配时间',
        name: 'firstDistributionTime',
        sortable: true,
        width: 180,
        // type: 'typography',
        ellipsis: {
          rows: 1
        },
      },
      {
        label: '最近分配时间',
        sortable: true,
        name: 'latestDistributionTime',
        width: 180,
        // type: 'typography',
        ellipsis: {
          rows: 1
        },
      },
      {
        label: '用户标签',
        name: 'userTagNames',
        style: {
          minWith: 200,
        },
        type: 'each',
        className: 'casePools-table-tags__addMargin',
        placeholder: '-',
        items: {
          type: 'tag',
          label: '${item}',
          displayMode: 'bordered',
          color: 'running',
        },
      },
      {
        label: '结案原因',
        name: 'closeReason',
        width: 180,
        // type: 'typography',
        ellipsis: {
          rows: 1
        },
      },
      {
        label: 'uid',
        name: 'uid',
        width: 280,
        type: 'typography',
        ellipsis: {
          rows: 1
        },
      },
      {
        label: '案件编号',
        name: 'caseId',
        width: 280,
        // type: 'typography',
        ellipsis: {
          rows: 1
        },
      },
    ],
    footerToolbar: [
      {
        type: 'tpl',
        // 默认取  count||total 中数据，最好能支持 自定义取字段
        tpl: '共${totalCount||count||0}条',
        className: 'pr-2',
      },
      {
        type: 'pagination',
        layout: 'pager,perPage,go',
      }
    ],
  }
}

export default demo;
