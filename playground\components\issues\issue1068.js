const demo = {
  "type": "page",
  "body": {
    "type": "form",
    "api": "post:/api/form/save",
    "data": {
      "tabls": [
        {}
      ]
    },
    "body": [
      {
        "type": "input-table",
        "name": "tabls",
        "addable": true,
        "label": "业务场景示例（触发不了必填校验",
        "needConfirm": false,
        "columns": [
          {
            "label": "name",
            "type": "input-sub-form",
            "name": "subForm",
            "title": "子表单",
            "required": true,
            "form": {
              "showCloseButton": false,
              "showErrorMsg": false,
              "size": "lg",
              "body": [
                {
                  "type": "tabs",
                  "tabs": [
                    {
                      "title": "1",
                      "tab": [
                        {
                          "type": "input-table",
                          "name": "table",
                          "addable": true,
                          "needConfirm": false,
                          "columns": [
                            {
                              "name": "name",
                              "label": "名称",
                              "type": "input-text",
                              "required": true
                            },
                          ]
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          }
        ]
      },
      {
        "type": "input-sub-form",
        "name": "subForm",
        "label": "不在InputTable中场景（可触发校验）",
        "title": "子表单",
        "required": true,
        "form": {
          "showCloseButton": false,
          "showErrorMsg": false,
          "size": "lg",
          "body": [
            {
              "type": "tabs",
              "tabs": [
                {
                  "title": "1",
                  "tab": [
                    {
                      "type": "input-table",
                      "name": "table",
                      "addable": true,
                      "needConfirm": false,
                      "columns": [
                        {
                          "name": "name",
                          "label": "名称",
                          "type": "input-text",
                          "required": true
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      }
    ]
  }
}

const demo1 = {
  type: 'page',
  data: {
    type: 'none',
  },
  body: {
    type: 'form',
    data: {
      table: [
        {
          a: '1',
          b: 'b1',
          id: 1,
        },
        {
          a: '2',
          b: 'b2',
          id: 2,
          children: [
            {
              a: 'a1-child1',
              b: 'b1-child1',
              id: '2-1',
            },
            {
              a: 'a1-child2',
              b: 'b1-child2',
              id: '2-2',
            },
          ],
        },
      ],
    },
    api: "https://3xsw4ap8wah59.cfc-execute.bj.baidubce.com/api/amis-mock/mock2/form/saveForm",
    body: [
      {
        type: 'input-table',
        name: 'table',
        id: 'inputTable',
        valueField: 'id',
        expandConfig: {
          expand: 'all',
          expandAll: true,
        },
        mountAll: true,
        addable: true,
        columns: [
          {
            name: 'id',
            label: '序号11',
          },
          {
            name: 'a',
            label: 'A',
            type: 'input-text',
            required: true,
            validations: {
              isNumeric: true,
            },
            validationErrors: {
              isNumeric: '请输入数字',
            },
          },
          {
            name: 'b',
            label: 'B',
            type: 'input-text',
          },
        ],
      },
    ],
  },
}

export default demo
