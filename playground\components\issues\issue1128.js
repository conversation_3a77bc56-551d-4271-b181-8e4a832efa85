export default {
  "type": "page",
  "body": {
    "type": "form",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
    "actions": [],
    "debug": true,
    "body": [
      {
        "type": "input-table",
        "name": "table",
        "label": "特征参数",
        "addable": true,
        "removable": true,
        "editable": true,
        "needConfirm": false,
        "columns": [
          {
            "name": "a",
            "label": "A",
            "type": "input-sub-form",
            "btnLabel": "设置子表单",
            "form": {
              "title": "配置子表单",
              debug: true,
              "body": [
                {
                  "label": "多选",
                  "type": "select",
                  "name": "select2",
                  "joinValues": false,
                  "multiple": true,
                  "clearable": true,
                  "source": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getOptions"
                }
              ]
            }
          },
          {
            "type": "combo",
            "name": "combo2",
            "label": "Combo 多选展示",
            "multiple": true,
            "items": [
              {
                "name": "text",
                "label": "文本",
                "type": "input-text"
              },
              {
                "name": "select",
                "label": "选项",
                "type": "select",
                "options": [
                  "a",
                  "b",
                  "c"
                ]
              }
            ]
          }
        ]
      }
    ]
  }
}

// table column里嵌套各类组件的情况
// export default {
//   "type": "page",
//   "body": {
//     "type": "form",
//     "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
//     "actions": [],
//     "debug": true,
//     "body": [
//       {
//         "type": "input-table",
//         "name": "table",
//         "label": "特征参数",
//         "addable": true,
//         "removable": true,
//         "editable": true,
//         "needConfirm": false,
//         "columns": [
//           {
//             "type": "condition-builder",
//             "label": "条件组件",
//             "name": "conditions",
//             "description": "适合让用户自己拼查询条件，然后后端根据数据生成 query where",
//             "fields": [
//               {
//                 "label": "文本",
//                 "type": "text",
//                 "name": "text"
//               },
//               {
//                 "label": "数字",
//                 "type": "number",
//                 "name": "number"
//               },
//             ]
//           },
//           {
//             "name": "a",
//             "label": "A",
//             "type": "select",
//             "name": "select2",
//             "joinValues": false,
//             "multiple": true,
//             "clearable": true,
//             "source": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getOptions"
//           },
//           {
//             "name": "d",
//             "label": "D",
//             "type": "json-schema",
//             "schema": {
//               type: 'object',
//               properties: {
//                 id: {
//                   type: 'number',
//                   title: 'ID'
//                 },
//                 name: {
//                   type: 'string',
//                   title: '名称'
//                 },
//                 description: {
//                   type: 'string',
//                   title: '描述'
//                 }
//               }
//             }
//           },
//           {
//             "name": "c",
//             "label": "C",
//             "type": "input-group",
//             "body": {
//               "type": "select",
//               "name": "select",
//               "joinValues": false,
//               "multiple": true,
//               "clearable": true,
//               "source": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getOptions"
//             }
//           },
//           {
//             "name": "b",
//             "label": "B"
//           }
//         ]
//       }
//     ]
//   }
// }

// table column里嵌套各类组件的情况
// export default {
//   "type": "page",
//   "body": {
//     "type": "form",
//     "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
//     "actions": [],
//     "debug": true,
//     "body": [
//       {
//         "type": "input-table",
//         "name": "table",
//         "label": "特征参数",
//         "addable": true,
//         "removable": true,
//         "editable": true,
//         "needConfirm": false,
//         "columns": [
//           {
//             "type": "condition-builder",
//             "label": "条件组件",
//             "name": "conditions",
//             "description": "适合让用户自己拼查询条件，然后后端根据数据生成 query where",
//             "fields": [
//               {
//                 "label": "文本",
//                 "type": "text",
//                 "name": "text"
//               },
//               {
//                 "label": "数字",
//                 "type": "number",
//                 "name": "number"
//               },
//             ]
//           },
//           {
//             "name": "a",
//             "label": "A",
//             "type": "select",
//             "name": "select2",
//             "joinValues": false,
//             "multiple": true,
//             "clearable": true,
//             "source": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getOptions"
//           },
//           {
//             "name": "d",
//             "label": "D",
//             "type": "json-schema",
//             "schema": {
//               type: 'object',
//               properties: {
//                 id: {
//                   type: 'number',
//                   title: 'ID'
//                 },
//                 name: {
//                   type: 'string',
//                   title: '名称'
//                 },
//                 description: {
//                   type: 'string',
//                   title: '描述'
//                 }
//               }
//             }
//           },
//           {
//             "name": "c",
//             "label": "C",
//             "type": "input-group",
//             "body": {
//               "type": "select",
//               "name": "select",
//               "joinValues": false,
//               "multiple": true,
//               "clearable": true,
//               "source": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getOptions"
//             }
//           },
//           {
//             "name": "b",
//             "label": "B"
//           }
//         ]
//       }
//     ]
//   }
// }

// table column里嵌套各类组件的情况
// export default {
//   "type": "page",
//   "body": {
//     "type": "form",
//     "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
//     "actions": [],
//     "debug": true,
//     "body": [
//       {
//         "type": "input-table",
//         "name": "table",
//         "label": "特征参数",
//         "addable": true,
//         "removable": true,
//         "editable": true,
//         "needConfirm": false,
//         "columns": [
//           {
//             "type": "condition-builder",
//             "label": "条件组件",
//             "name": "conditions",
//             "description": "适合让用户自己拼查询条件，然后后端根据数据生成 query where",
//             "fields": [
//               {
//                 "label": "文本",
//                 "type": "text",
//                 "name": "text"
//               },
//               {
//                 "label": "数字",
//                 "type": "number",
//                 "name": "number"
//               },
//             ]
//           },
//           {
//             "name": "a",
//             "label": "A",
//             "type": "select",
//             "name": "select2",
//             "joinValues": false,
//             "multiple": true,
//             "clearable": true,
//             "source": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getOptions"
//           },
//           {
//             "name": "d",
//             "label": "D",
//             "type": "json-schema",
//             "schema": {
//               type: 'object',
//               properties: {
//                 id: {
//                   type: 'number',
//                   title: 'ID'
//                 },
//                 name: {
//                   type: 'string',
//                   title: '名称'
//                 },
//                 description: {
//                   type: 'string',
//                   title: '描述'
//                 }
//               }
//             }
//           },
//           {
//             "name": "c",
//             "label": "C",
//             "type": "input-group",
//             "body": {
//               "type": "select",
//               "name": "select",
//               "joinValues": false,
//               "multiple": true,
//               "clearable": true,
//               "source": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getOptions"
//             }
//           },
//           {
//             "name": "b",
//             "label": "B",
//           }
//         ]
//       }
//     ]
//   }
// }

// table column里使用quickEdit语法嵌套各类组件的情况
// export default {
//   "type": "page",
//   "body": {
//     "type": "form",
//     "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
//     "actions": [],
//     "debug": true,
//     "body": [
//       {
//         "type": "input-table",
//         "name": "table",
//         "label": "特征参数",
//         "addable": true,
//         "removable": true,
//         "editable": true,
//         "needConfirm": false,
//         "columns": [
//           {
//             "type": "condition-builder",
//             "label": "条件组件",
//             "name": "conditions",
//             "description": "适合让用户自己拼查询条件，然后后端根据数据生成 query where",
//             "fields": [
//               {
//                 "label": "文本",
//                 "type": "text",
//                 "name": "text"
//               },
//               {
//                 "label": "数字",
//                 "type": "number",
//                 "name": "number"
//               },
//             ]
//           },
//           {
//             "name": "a",
//             "label": "A",
//             "quickEdit": {
//               "type": "select",
//               "name": "select2",
//               "joinValues": false,
//               "multiple": true,
//               "clearable": true,
//               "source": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getOptions"
//             }
//           },
//           {
//             "label": "D",
//             "quickEdit": {
//               "type": "json-schema",
//               "name": "d",
//               "schema": {
//                 type: 'object',
//                 properties: {
//                   id: {
//                     type: 'number',
//                     title: 'ID'
//                   },
//                   name: {
//                     type: 'string',
//                     title: '名称'
//                   },
//                   description: {
//                     type: 'string',
//                     title: '描述'
//                   }
//                 }
//               }
//             }
//           },
//           {
//             "name": "c",
//             "label": "C",
//             "type": "input-group",
//             "body": {
//               "type": "select",
//               "name": "select",
//               "joinValues": false,
//               "multiple": true,
//               "clearable": true,
//               "source": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getOptions"
//             }
//           },
//           {
//             "name": "b",
//             "label": "B",
//             "quickEdit": {
//               "type": "input-sub-form",
//               "btnLabel": "设置子表单",
//               "form": {
//                 "title": "配置子表单",
//                 debug: true,
//                 "body": [
//                   {
//                     "label": "多选",
//                     "type": "select",
//                     "name": "select2",
//                     "joinValues": false,
//                     "multiple": true,
//                     "clearable": true,
//                     "source": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getOptions"
//                   }
//                 ]
//               }
//             }
//           }
//         ]
//       }
//     ]
//   }
// }

// 测试table handleSave
// export default {
//   "type": "page",
//   "body": {
//     "type": "crud",
//     "syncLocation": false,
//     "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/mock2/sample",
//     "quickSaveApi": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/mock2/sample/bulkUpdate",
//     "columns": [
//       {
//         "name": "id",
//         "label": "ID"
//       },
//       {
//         "name": "grade",
//         "label": "CSS grade",
//         "quickEdit": {
//           "mode": "inline",
//           "type": "select",
//           "size": "xs",
//           "options": [
//             "A",
//             "B",
//             "C",
//             "D",
//             "X"
//           ]
//         }
//       },
//       {
//         "name": "switch",
//         "label": "switch",
//         "quickEdit": {
//           "mode": "inline",
//           "type": "switch",
//           "onText": "开启",
//           "offText": "关闭"
//         }
//       }
//     ]
//   }
// }