export default {
  "type": "page",
  "body": {
    "title": "",
    "type": "form",
    "renderDialog": false,
    "renderTypography": false,
    // "debug": true,
    "mode": "horizontal",
    "autoFocus": false,
    "data": {
      "some1": [
        {
        label: "大于",
        value: "a",
        },
        {
        label: "等于",
        value: "b",
        },
        {
        label: "介于",
        value: "be",
        }
      ],
      "blEvm": [
        {
          "label": "da12341",
          "value": "a"
        },
        {
          "label": "qwrqwqtqtq",
          "value": "b"
        },
        {
          "label": "qweq1134",
          "value": "c"
        },
        {
          "label": "D啊SQE",
          "value": "d"
        },
        {
          "label": "E",
          "value": "e"
        }
      ],
      "blEvmTop": [
        {
          "label": "songAaaqe1",
          "value": "a"
        },
        {
          "label": "中信银行",
          "value": "b"
        },
        {
          "label": "啊红狗",
          "value": "c"
        },
        {
          "label": "艾薇",
          "value": "d"
        },
        {
          "label": "E",
          "value": "e"
        }
      ],
      "tabsCombo": [
        {
          first: "测试一下",
          second: "a",
          ruleListCombo: [
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
            {
              "type": "RULE",
              "label": "策略规则",
              "title": "策略规则",
              "iner1": "qweas123",
              "iner2": "a",
            conditions: {
              conjunction: "and",
              children: [
                  {
                  inerT: "a",
                  operateType: "a",
                  result: "通过"
                  },
                  {
                  inerT: "a",
                  operateType: "b",
                  result: "通过"
                  },
                  {
                  conjunction: "and",
                  children: [
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                  'rightValue': "xixi"
                      },
                      {
                      inerT: "a",
                      operateType: "be",
                      result: "通过",
                      leftValue: "哈哈",
                     'rightValue': "xixi"
                      }
                    ]
                  },
                ]
              }
            },
          ]
        },
      ]
    },
    "body": [
      {
        "type": "combo",
        "name": "tabsCombo",
        "strictMode": true,
        "label": "组合",
        "labelWidth": 50,
        "multiple": true,
        "multiLine": true,
        "tabsMode": true,
        "subFormMode": "horizontal",
        "subFormHorizontal": {
          "labelWidth": 70
        },
        "tabsLabelTpl": "${title || '策略分支'}",
        "maxLength": 3,
        "items": [
          {
            "type": "group",
            body: [
              {
                "name": "title",
                "label": "策略名称",
                "type": "input-text",
                "placeholder": "文本",
                "value": "",
                "size": "full"
              },
              {
                "name": "second",
                "label": "策略id",
                "type": "select",
                "options": [
                  "a",
                  "b",
                  "c"
                ],
                "size": "full"
              },
              {
                "name": "b",
                "label": "选项",
                "type": "select",
                "options": [
                  "a",
                  "b",
                  "c"
                ],
                "size": "full"
              }
            ]
          },
          {
            "type": 'combo',
              name: 'ruleListCombo',
              id: 'ruleListCombo',
              label: false,
              draggable: true,
              copyable: true,
              itemAddable: true,
              multiLine: true,
              multiple: true,
              strictMode: false, // 强制刷新
              typeSwitchable: false,
              submitText: null,
              subFormHorizontal: {
                labelWidth: 80,
            },
              scaffold: {
              type: 'RULE',
              title: '策略规则',
            },
            "conditions": [
              {
                "label": "规则",
                "test": "this.type === 'RULE'",
                "scaffold": {
              type: 'RULE',
              label: '策略规则',
              title: '策略规则',
                },
               items: [
                  {
                    "type": 'group-container',
                    // "collapsible": true,
                    "items": [
                      {
                        "header": {
                          "title": "策略",
                        },
                        "body": [
                          {
                            "type": "group",
                            "body": [
                              {
                                "type": "input-text",
                                "name": "iner1",
                                "label": "策略code"
                              },
                              {
                                "type": "select",
                                "name": "iner2",
                                "label": "输出结果",
                                "source": "${blEvmTop}"
                              },
                              {
                                "type": "select",
                                "name": "iner3",
                                "label": "选项",
                                "source": "${blEvmTop}"
                              },
                            ]
                          },
                          {
                            "type": "condition-builder",
                            "strictMode": true,
                            "toolbarMode": "vertical",
                            "label": "条件",
                            "name": "conditions",
                            // "searchable": true,
                            "conditionItemBody": [
                              {
                                "label": "变量",
                                "type": "select",
                                "name": "inerT",
                                "source": "${blEvm}"
                              },

                              {
                                "label": "条件",
                                "type": "select",
                                "name": "operateType",
                                "source": "${some1}"
                              },
                              {
                                "label": "结果",
                                "type": "input-text",
                                "name": "result",
                                visibleOn:'${operateType === "a"}',
                              },
                              {
                                  type: 'between',
                                  label: '值区间',
                                  visibleOn:
                                    '${operateType === "be"}',
                      required: true,
                      items: [
                                  {
          type: 'input-number',
          name: 'leftValue',
          placeholder: '请输入',
                                  },
                                  {
          type: 'input-number',
          name: 'rightValue',
          placeholder: '请输入',
                                  },
                                ],
                              },

                            ]
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          },
        ]
      }
    ],
    "submitText": null,
    "actions": []
  }
}
