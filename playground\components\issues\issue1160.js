export default {
  "type": "page",
  "body": [{
    "type": "form",
    "id": "form",
    "debug": true,
    "data": {
      "tree": 5,
      "city": "110109",
      "combo2": [
        {
          "text": "1",
          "select": "a"
        },
        {
          "text": "2",
          "select": "b"
        }
      ]
    },
    "labelWidth": 40,
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "body": [
      {
        "type": "input-text",
        "required": true,
        "name": "a",
        "label": "必填项"
      }
    ]
  },{
    "type": "form",
    "debug": true,
    "data": {
      "tree": 5,
      "city": "110109",
      "combo2": [
        {
          "text": "1",
          "select": "a"
        },
        {
          "text": "2",
          "select": "b"
        }
      ]
    },
    "labelWidth": 40,
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "body": [
      {
        "label": "选项",
        "type": "select",
        "name": "select",
        "options": [
          {
            "label": "IEInternet Explorer（简称：IE）是微软公司推出的一款网页浏览器。原称Microsoft Internet Explorer（6版本以前）和Windows Internet Explorer（7、8、9、10、11版本）。在IE7以前，中文直译为“网络探路者”，但在IE7以后官方便直接俗称",
            "value": "IE"
          },
          {
            "label": "FIREFOX",
            "value": "FIREFOX"
          },
          {
            "label": "CHROME(Google Chrome是一款由Google公司开发的网页浏览器。 该浏览器基于其他开源软件如WebKit撰写，目标是提升稳定性、速度和安全性，并创造出简单且有效率的使用者界面。)",
            "value": "CHROME"
          }],
        "onEvent": {
          "change": {
            "actions": [
              {
                "actionType": "validate",
                "componentId": "form"
              },
              {
                "preventDefault": "${event.data.validateResult.error}",
                "stopPropagation": "${event.data.validateResult.error}"
              },
              {
                "actionType": "toast",
                "message": "测试"
              }
              // {
              //   "actionType": "setValue",
              //   "componentId": "form",
              //   "expression": "${event.data.validateResult.error}",
              //   "args": {
              //     "value": {
              //       "tree": 5
              //     }
              //   }
              // },
            ]
          }
        }
      },
      {
        "name": "checkbox",
        "id": "checkbox",
        "type": "checkbox",
        "label": "勾选框",
        "option": "选项说明",
        "onEvent": {
          "change": {
            "actions": [
              {
                "actionType": "validate",
                "componentId": "form"
              },
              {
                "preventDefault": "${event.data.validateResult.error}",
                "stopPropagation": "${event.data.validateResult.error}"
              },
              {
                "actionType": "toast",
                "message": "测试"
              }
              // {
              //   "actionType": "setValue",
              //   "componentId": "form",
              //   "expression": "${event.data.validateResult.error}",
              //   "args": {
              //     "value": {
              //       "tree": 5
              //     }
              //   }
              // },
            ]
          }
        }
      },
      {
        "type": "button",
        "label": "勾选",
        "onEvent": {
          "click": {
            "actions": [
              {
                "actionType": "setValue",
                "componentId": "checkbox",
                "args": {
                  "value": "${!checkbox}"
                }
              }
            ]
          }
        }
      },
      {
        "name": "array",
        "label": "颜色集合",
        "type": "input-array",
        "value": [
          "red"
        ],
        "inline": true,
        "items": {
          "type": "input-color",
          "clearable": false
        },
        "onEvent": {
          "change": {
            "actions": [
              {
                "actionType": "validate",
                "componentId": "form"
              },
              {
                "preventDefault": "${event.data.validateResult.error}",
                "stopPropagation": "${event.data.validateResult.error}"
              },
              {
                "actionType": "toast",
                "message": "测试"
              }
            ]
          }
        }
      },
      {
        "type": "combo",
        "name": "combo2",
        "label": "Combo 多选展示",
        // "disabled": true,
        "multiple": true,
        "items": [
          {
            "name": "text",
            "label": "文本",
            "type": "input-text"
          },
          {
            "name": "select",
            "label": "选项",
            "type": "select",
            "options": [
              "a",
              "b",
              "c"
            ]
          }
        ],
        "onEvent": {
          "change": {
            "actions": [
              {
                "actionType": "validate",
                "componentId": "form"
              },
              {
                "preventDefault": "${event.data.validateResult.error}",
                "stopPropagation": "${event.data.validateResult.error}"
              },
              {
                "actionType": "toast",
                "message": "测试"
              }
            ]
          }
        }
      },
      {
        "type": "input-table",
        "name": "table",
        "label": "特征参数",
        "addable": true,
        "removable": true,
        "editable": true,
        "needConfirm": false,
        "columns": [
          {
            "name": "a",
            "label": "A"
          },
          {
            "name": "b",
            "label": "B"
          },
          {
            "type": "input-group",
            "label": "C",
            "body": [
              {
                "type": "combo",
                "name": "c",
                "multiLine": true,
                "multiple": false,
                "label": false,
                "required": true,
                "items": [
                  {
                    "type": "input-text",
                    "name": "c1",
                    "required": true,
                    "label": false
                  },
                  {
                    "type": "input-text",
                    "name": "c2",
                    "required": true,
                    "label": false
                  }
                ]
              }
            ]
          }
        ],
        "onEvent": {
          "change": {
            "actions": [
              {
                "actionType": "validate",
                "componentId": "form"
              },
              {
                "preventDefault": "${event.data.validateResult.error}",
                "stopPropagation": "${event.data.validateResult.error}"
              },
              {
                "actionType": "toast",
                "message": "测试"
              }
            ]
          }
        }
      },
      {
        "type": "input-tree",
        "name": "tree",
        "checkAll": true,
        "label": "Tree",
        "hideRoot": false,
        "multiple": true,
        "options": [
          {
            "label": "Folder A",
            "value": 1,
            "children": [
              {
                "label": "file A",
                "value": 2
              },
              {
                "label": "Folder B",
                "value": 3,
                "children": [
                  {
                    "label": "file b1",
                    "value": 3.1
                  },
                  {
                    "label": "file b2",
                    "value": 3.2
                  }
                ]
              }
            ]
          },
          {
            "label": "file C",
            "value": 4
          },
          {
            "label": "file D",
            "value": 5
          }
        ],
        "onEvent": {
          "change": {
            "actions": [
              {
                "actionType": "validate",
                "componentId": "form"
              },
              {
                "preventDefault": "${event.data.validateResult.error}",
                "stopPropagation": "${event.data.validateResult.error}"
              },
              {
                "actionType": "toast",
                "message": "测试"
              }
              // {
              //   "actionType": "setValue",
              //   "componentId": "form",
              //   "expression": "${event.data.validateResult.error}",
              //   "args": {
              //     "value": {
              //       "tree": 5
              //     }
              //   }
              // },
            ]
          }
        }
      },
      {
        "name": "select3",
        "type": "chained-select",
        "label": "链式下拉",
        "source": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/options/chainedOptions?waitSeconds=1&parentId=$parentId&level=$level&maxLevel=4",
        "value": "a,b",
        "onEvent": {
          "change": {
            "actions": [
              {
                "actionType": "validate",
                "componentId": "form"
              },
              {
                "preventDefault": "${event.data.validateResult.error}",
                "stopPropagation": "${event.data.validateResult.error}"
              },
              {
                "actionType": "toast",
                "message": "测试"
              }
              // {
              //   "actionType": "setValue",
              //   "componentId": "form",
              //   "expression": "${event.data.validateResult.error}",
              //   "args": {
              //     "value": {
              //       "tree": 5
              //     }
              //   }
              // },
            ]
          }
        }
      }
    ]
  }]
}
