export default {
  type: 'page',
  body: {
    type: 'button',
    label: '打开弹窗',
    actionType: 'dialog',
    dialog: {
      title: '打开弹窗',
      body: {
        type: 'form',
        data: {
          "flowId": ""
        },
        canAccessSuperData: false,
        body: [
          {
            type: 'select',
            label: '选择需要复制的流程',
            name: 'flowId',
            clearable: true,
            options: [{
              label: 'test01',
              value: 'test01'
            },{
              label: 'test02',
              value: 'test02'
            }],
          },
          {
            type: 'select',
            label: '版本',
            name: 'versionId',
            required: true,
            searchable: true,
            clearable: true,
            labelField: 'versionName',
            valueField: 'id',
            source: {
              url: 'http://openapi.api.huanbeiapi.com/apicenter/apiMockExec/forward/callcenterivr/ivr/flows/${flowId}/version/query',
              method: 'get',
              data: {
                flowId: '${flowId}',
              },
              sendOn: '${flowId}',
              adaptor: (_, res) => {
                const {data} = res || {}
                return {
                  options: []
                };
              }
            }
          },
          {
            type: 'alert',
            body: '${flowId}',
            level: 'info',
          },
        ]
      }
    }
  }
}
