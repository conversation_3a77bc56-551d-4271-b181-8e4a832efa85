export default {
  "type": "page",
  "body": {
    type: 'form',
    api: 'https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm',
    body: [
      {
        type: 'nested-select',
        name: 'nestedSelect1',
        label: '级联选择器（单选）',
        options: [
          {
            label: 'A',
            value: 'a'
          },
          {
            label: 'B',
            value: 'b',
            children: [
              {
                label: 'B-1',
                value: 'b1',
                disabled: true
              },
              {
                label: 'B-2',
                value: 'b2'
              },
              {
                label: 'B-3',
                value: 'b3'
              }
            ]
          },
          {
            label: 'C',
            value: 'c'
          }
        ],
        searchable: true,
        multiple: false,
        joinValues: true,
        clearable: true
      },
      {
        type: 'nested-select',
        name: 'nestedSelect2',
        label: '级联选择器（多选）',
        options: [
          {
            label: 'A',
            value: 'a'
          },
          {
            label: 'B',
            value: 'b',
            children: [
              {
                label: 'B-1',
                value: 'b1',
                disabled: true
              },
              {
                label: 'B-2',
                value: 'b2'
              },
              {
                label: 'B-3',
                value: 'b3'
              }
            ]
          },
          {
            label: 'C',
            value: 'c'
          }
        ],
        searchable: true,
        multiple: true,
        joinValues: true,
        clearable: true
      }
    ]
  }
}
