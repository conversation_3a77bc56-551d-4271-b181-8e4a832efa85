export default {
  "type": "page",
  "body": {
  "type": "form",
  "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
  "body": [
    {
      "label": "人员选择",
      "type": "select",
      "name": "b",
      "multiple": true,
      "sortable": true,
      "searchable": true,
      "selectMode": "associated",
      "leftMode": "tree",
      "source": {
        "url":"https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/departUser",
        "adaptor": "return {\n    ...payload,msg:111, \n    status: payload.code === 200 ? 500 : payload.code\n}"
      },

      // "searchApi": 'https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/departUserSearch?term=${term}',
      // "deferApi": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/departUser?ref=${ref}&dep=${value}"
    }
  ]
}

}