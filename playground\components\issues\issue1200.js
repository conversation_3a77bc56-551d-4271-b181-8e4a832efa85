export default {
  type: 'page',
  name: 'issue1200',
  body: [
    {
      type: 'form',
      title: '动态范围测试 - DateRangePicker',
      body: [
        // 基础测试：7天范围
        {
          type: 'ds-date-range-picker',
          name: 'test1',
          label: '基础测试 (7天范围)',
          dynamicRange: 7,
          showTime: false,
          format: 'YYYY-MM-DD'
        },

        // 时间模式测试：3天范围
        {
          type: 'ds-date-range-picker',
          name: 'test2',
          label: '时间模式 (3天范围)',
          dynamicRange: 3,
          showTime: true,
          format: 'YYYY-MM-DD HH:mm:ss'
        },

        // 字符串格式测试：3个月范围
        {
          type: 'ds-date-range-picker',
          name: 'test3',
          label: '字符串格式 (3个月范围)',
          dynamicRange: '3months',
          showTime: false,
          format: 'YYYY-MM-DD'
        },

        // 结合其他限制测试
        {
          type: 'ds-date-range-picker',
          name: 'test4',
          label: '结合 maxDate 限制 (7天范围 + maxDate)',
          dynamicRange: 7,
          maxDate: '+30days',
          showTime: false,
          format: 'YYYY-MM-DD'
        },

        // 周选择器 + 动态范围
        {
          type: 'ds-date-range-picker',
          name: 'test5',
          label: '周选择器 (4周范围)',
          dynamicRange: '4weeks',
          picker: 'week',
          format: 'YYYY-wo'
        },

        // 月选择器 + 动态范围
        {
          type: 'ds-date-range-picker',
          name: 'test6',
          label: '月选择器 (6个月范围)',
          dynamicRange: '6months',
          picker: 'month',
          format: 'YYYY-MM'
        }
      ]
    }
  ]
};
