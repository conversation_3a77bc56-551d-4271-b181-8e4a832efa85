export default {
  "type": "page",
  "bodyClassName": "h-screen",
  "body": {
    "type": "form",
    "autoFillHeight": true,
    "body": {
      "type": "left-right-container",
      "defaultWidth": 250,
      "left": {
        "type": "input-tree",
        "label": false,
        "searchable": true,
        "editable": true,
        "autoFillHeight": true,
        "name": "tree2",
        "multiple": false,
        "autoCheckChildren": false,
        "options": [
          {
            "label": "A",
            "value": "a"
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "C",
            "value": "c"
          }
        ],
        "onEvent": {
          "change": {
            "actions": [
              {
                "actionType": "query",
                "componentId": "right-crud",
                "args": {
                  "queryParams": {
                    "tree2": "${event.data.value}"
                  }
                }
              }
            ]
          }
        },
      },
      "right": {
        "type": "form",
        "static": true,
        "mode": "vertical",
        "actions": [],
        "body": [
          {
            "type": "input-image",
            "placeholder": 'xxx',
            "placeholderPlacement": "bottom",
            label: "图片",
          }
        ]
      }
    }
  }
}
