export default {
  "type": "page",
  "body": {
    "type": "tabs",
    "tabsMode": "strong",
    "draggable": true,
    "addable": true,
    "closable": true,
    "onEvent": {
      "change": {
        "actions": [
          {
            "actionType": "toast",
            "args": {
              "msgType": "info",
              "msg": "派发change事件 ${value}"
            }
          }
        ]
      },
      "change": {
        "actions": [
          {
            "actionType": "toast",
            "args": {
              "msgType": "info",
              "msg": "派发change事件 ${value}"
            }
          }
        ]
      },
      "dragSuccess": {
        "actions": [
          {
            "actionType": "toast",
            "args": {
              "msgType": "info",
              "msg": "派发change事件 ${value}"
            }
          }
        ]
      },
      "add": {
        "actions": [
          {
            "actionType": "toast",
            "args": {
              "msgType": "info",
              "msg": "派发add事件${tabs-line-1}|${value}|${tab.title}"
            }
          }
        ]
      },
      "close": {
        "actions": [
          {
            "actionType": "toast",
            "args": {
              "msgType": "info",
              "msg": "派发close事件 ${tabs-line-1}|${value}|${tab.title}"
            }
          }
        ]
      }
    },
    "tabs": [
      {
        "title": "Tab - 0",
        "tab": "line - 0"
      },
      {
        "title": "Tab - 1",
        "tab": "line - 1"
      }
    ]
  }
}
