// export default {
//   "type": "page",
//   "body": [
//     {
//       "type": "button",
//       "label": "多级弹框",
//       "actionType": "dialog",
//       "dialog": {
//         "title": "提示",
//         "name": "dialog_1",
//         "body": {
//             "type": "form",
//             "api": "/amis/api/mock2/form/saveForm",
//             "id":"otherVersionCopyForm",
//             "body": [
//               {
//                 "type": "input-text",
//                 "name": "name",
//                 "label": "姓名：",
//                 "required": true
//               },
//               {
//                 "name": "email",
//                 "type": "input-text",
//                 "label": "邮箱：",
//                 "required": true
//               }
//             ]
//         },
//         "actions": [
//           {
//             "type": "button",
//             "actionType": "cancel",
//             "label": "取消",
//             "primary": true
//           },
//           {
//             "type": "button",
//             "level": "primary",
//             "label": "确定",
//             "onEvent": {
//                 "click": {
//                   "actions": [
//                     {
//                       "actionType": "validate",
//                       "componentId": "otherVersionCopyForm",
//                     },
//                     {
//                       "actionType": "dialog",
//                       "expression": "${event.data.validateResult.responseData}",
//                       "args": {
//                           "dialog": {
//                               "title": "弹框中的弹框",
//                               "body": "弹第二层弹窗",
//                               "actions": [
//                                 {
//                                   "type": "button",
//                                   "label": "关闭所有",
//                                   "level": "info",
//                                   // "actionType": "close",
//                                   "close": "dialog_1",
//                                 }
//                               ],
//                             },
//                       },
//                     },
//                   ],
//                 },
//             },
//           }
//         ]
//       }
//     }
//   ]
// }

// store tree 测试
// export default {
//   type: 'page',
//   body: [
//     {
//       type: 'form',
//       body: [
//         {
//           type: 'input-text',
//           name: 'name',
//           label: '姓名：',
//           required: true
//         },
//         {
//           name: 'email',
//           type: 'input-text',
//           label: '邮箱：',
//           required: true
//         },
//         {
//           type: 'input-table',
//           name: 'table',
//           label: '表格',
//           columns: [
//             {
//               type: 'input-text',
//               name: 'name',
//               label: '姓名'
//             },
//             {
//               type: 'input-text',
//               name: 'email',
//               label: '邮箱'
//             }
//           ],
//           addable: true,
//         }
//       ]
//     }
//   ]
// }

// 以下代码输出 store 的父子关系
// Object.values(amisStore.stores).forEach(store => {
//   const parentStore = amisStore.getStoreById(store.parentId);
//   console.log('store name: ', store.name || store.storeType, ', store path: ', store.path, '; parent store name: ', parentStore?.name || parentStore?.storeType, ', parent store path: ', parentStore?.path);
// })

// 分层dialog 测试
// export default {
//   type: 'page',
//   body: [
//     {
//       type: 'CRUD',
//       name: 'crud',
//       title: '表格',
//       data: {
//         text1: 'test',
//       },
//       columns: [
//         {
//           name: 'name',
//           label: '姓名'
//         },
//         {
//           name: 'email',
//           label: '邮箱'
//         }
//       ],
//       actions: [
//         {
//           type: 'button',
//           label: '添加',
//           actionType: 'dialog',
//           dialog: {
//             title: '添加',
//             data: {
//               email: 'text1',
//             },
//             body: {
//               type: 'form',
//               body: [{
//                 type: 'input-text',
//                 name: 'text1',
//                 label: '姓名：',
//                 required: true
//               }, {
//                 type: 'input-text',
//                 name: 'email',
//                 label: '邮箱：',
//               }]
//             }
//           }
//         }
//       ],
//       // api: '/amis/api/mock2/form/saveForm',
//     }
//   ]
// }


// feedback 测试
export default {
  "type": "page",
  "body": {
    "type": "button",
    "label": "弹一个框",
    "actionType": "dialog",
    "dialog": {
      "title": "提示",
      "body": {
        "type": "form",
        "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm?waitSeconds=2",
        "body": [
          {
            "type": "input-text",
            "name": "text",
            "required": true,
            "label": "用户名",
            "placeholder": "请输入用户名"
          },
          {
            "type": "input-password",
            "name": "password",
            "label": "密码",
            "required": true,
            "placeholder": "请输入密码"
          },
          {
            "type": "checkbox",
            "name": "rememberMe",
            "label": "记住登录"
          }
        ]
      },
      "actions": [
        {
          "type": "button",
          "label": "提交表单 Feedback",
          "actionType": "confirm",
          "feedback": {
            "body": "feedback弹框中，不请求接口了，直接点击按钮关闭",
            "actions": [
              {
                "type": "button",
                "actionType": "close",
                "label": "关闭"
              }
            ]
          }
        },
        {
          "type": "button",
          "label": "ajax请求 Feedback",
          "actionType": "ajax",
          "close": true,
          "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/initData?waitSeconds=1",
          "feedback": {
            "body": "feedback弹框中，不请求接口了，直接点击按钮关闭",
            "actions": [
              {
                "type": "button",
                "actionType": "close",
                "label": "关闭"
              }
            ]
          }
        }
      ]
    }
  }
}
