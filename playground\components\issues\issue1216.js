export default {
  "type": "page",
  "body": {
    "type": "form",
    "labelWidth": 60,
    "body": {
      "type": "group-container",
      "autoSwitchWhenValidated": true,
      "activeKey": [
        "1"
      ],
      "collapsible": true,
      "items": [
        {
          "header": {
            "title": "第一步，基础信息",
            "assistContent": [
              {
                "type": "remark",
                "content": "这是一段提示"
              }
            ],
            "actions": [
              {
                "type": "button",
                "label": "实验列表",
                "level": "link"
              }
            ],
          },
          "body": [
            {
              "type": "input-text",
              "name": "text1",
              "label": "姓名1",
              "required": true
            },
            {
              "type": "input-text",
              "name": "text2",
              "label": "年龄"
            },
            {
              "type": "input-text",
              "name": "text3",
              "label": "班级"
            },
            {
              "type": "input-text",
              "name": "text4",
              "label": "性别",
            },
            {
              "type": "input-text",
              "name": "text5",
              "label": "爱好",
            },
            {
              "type": "input-text",
              "name": "text6",
              "label": "备注",
            },
            {
              "type": "input-text",
              "name": "text7",
              "label": "备注1",
            },
            {
              "type": "input-text",
              "name": "text8",
              "label": "备注2",
            },
            {
              "type": "input-text",
              "name": "text9",
              "label": "备注3",
            },
            {
              "type": "input-text",
              "name": "text10",
              "label": "班级",
            },
            {
              "type": "wizard",
              "steps": [
                {
                  "title": "第一步",
                  "body": [
                    {
                      "name": "website",
                      "label": "网址",
                      "type": "input-url",
                      "required": true
                    },
                    {
                      "name": "email",
                      "label": "邮箱",
                      "type": "input-email"
                    }
                  ]
                },
                {
                  "title": "Step 2",
                  "body": [
                    {
                      "name": "email2",
                      "label": "邮箱",
                      "type": "input-email",
                      // "required": true
                    }
                  ]
                },
                {
                  "title": "Step 3",
                  "body": [
                    "这是最后一步了"
                  ]
                }
              ]
            }
          ]
        },
        {
          "header": {
            "title": "第二步，复杂信息"
          },
          "body": [
            {
              "type": "group",
              "body": [
                {
                  "type": "input-text",
                  "name": "second1",
                  "label": "邮箱"
                },
                {
                  "type": "input-text",
                  "name": "second2",
                  "label": "电话"
                },
                {
                  "type": "input-text",
                  "name": "second3",
                  "label": "地址",
                  "columnRatio": 4
                }
              ]
            },
            {
              "type": "group",
              "body": [
                {
                  "type": "textarea",
                  "name": "textarea",
                  "label": "姓名",
                  "placehold": "请输入",
                  // "required": true
                }
              ]
            },
            {
              "type": "group",
              "body": [
                {
                  "type": "input-rich-text",
                  "name": "second5",
                  "label": "其它"
                }
              ]
            }
          ]
        },
        {
          "header": {
            "title": "第三步，策略信息"
          },
          "body": [
            {
              "type": "group",
              "body": [
                {
                  "type": "tabs",
                  "autoSwitchWhenValidated": true,
                  "addable": true,
                  "closable": true,
                  "editable": true,
                  "addBtnText": " ",
                  "tabPosition": "center",
                  "tabsMode": "strong",
                  "defaultTabForAdd": {
                    "title": "策略分支x",
                    "body": [
                      {
                        "type": "group",
                        "body": [
                          {
                            "type": "input-text",
                            "name": "third1",
                            "label": "sjksajkd"
                          },
                          {
                            "type": "input-text",
                            "name": "third2",
                            "label": "sjksajkd"
                          },
                          {
                            "type": "input-text",
                            "name": "third3",
                            "label": "sjksajkd"
                          }
                        ]
                      },
                      {
                        "type": "group",
                        "body": [
                          {
                            "type": "input-text",
                            "name": "third4",
                            "label": "sjksajkd"
                          },
                          {
                            "type": "input-text",
                            "name": "third5",
                            "label": "sjksajkd"
                          },
                          {
                            "type": "input-text",
                            "name": "third6",
                            "label": "sjksajkd"
                          }
                        ]
                      }
                    ]
                  },
                  "tabs": [
                    {
                      "title": "策略分支1",
                      "tab": [
                        {
                          "type": "group",
                          "body": [
                            {
                              "type": "input-text",
                              "name": "third1",
                              "label": "sjksajkd",

                            },
                            {
                              "type": "input-text",
                              "name": "third2",
                              "label": "sjksajkd"
                            },
                            {
                              "type": "input-text",
                              "name": "third3",
                              "label": "sjksajkd"
                            }
                          ]
                        },
                        {
                          "type": "input-text",
                          "name": "third1",
                          "label": "sjksajkd"
                        },
                        {
                          "type": "input-text",
                          "name": "third2",
                          "label": "sjksajkd"
                        },
                        {
                          "type": "input-text",
                          "name": "third3",
                          "label": "sjksajkd"
                        },
                        {
                          "type": "input-text",
                          "name": "third3",
                          "label": "sjksajkd"
                        },
                        {
                          "type": "input-text",
                          "name": "third31",
                          "label": "sjksajkd"
                        },
                        {
                          "type": "input-text",
                          "name": "third32",
                          "label": "sjksajkd"
                        },
                        {
                          "type": "input-text",
                          "name": "third33",
                          "label": "sjksajkd"
                        },
                        {
                          "type": "input-text",
                          "name": "third34",
                          "label": "sjksajkd"
                        },
                        {
                          "type": "input-text",
                          "name": "third35",
                          "label": "sjksajkd"
                        },
                        {
                          "type": "input-text",
                          "name": "third5",
                          "label": "sjksajkd",
                          "required": true
                        },
                        {
                          "type": "select",
                          "name": "third4",
                          "label": "sjksajkd",
                          "options": [
                            {
                              "label": "a",
                              "value": "a"
                            },
                            {
                              "label": "b",
                              "value": "b"
                            }
                          ]
                        },
                        {
                          "type": "input-text",
                          "name": "third6",
                          "label": "sjksajkd"
                        }
                      ]
                    },
                    {
                      "title": "策略分支2",
                      "tab": [
                        {
                          "type": "group",
                          "body": [
                            {
                              "type": "input-text",
                              "name": "third7",
                              "label": "sjksajkd"
                            },
                            {
                              "type": "input-text",
                              "name": "third8",
                              "label": "sjksajkd"
                            },
                            {
                              "type": "input-text",
                              "name": "third9",
                              "label": "sjksajkd",
                              // "required": true
                            }
                          ]
                        },
                        {
                          "type": "group",
                          "body": [
                            {
                              "type": "input-text",
                              "name": "third10",
                              "label": "sjksajkd"
                            },
                            {
                              "type": "select",
                              "name": "third4",
                              "label": "sjksajkd",
                              "options": [
                                {
                                  "label": "a",
                                  "value": "a"
                                },
                                {
                                  "label": "b",
                                  "value": "b"
                                }
                              ]
                            },
                            {
                              "type": "input-text",
                              "name": "third12",
                              "label": "sjksajkd"
                            }
                          ]
                        }
                      ]
                    },
                    {
                      "title": "策略分支3",
                      "tab": [
                        {
                          "type": "group",
                          "body": [
                            {
                              "type": "input-text",
                              "name": "third13",
                              "label": "sjksajkd"
                            },
                            {
                              "type": "input-text",
                              "name": "third14",
                              "label": "sjksajkd"
                            },
                            {
                              "type": "input-text",
                              "name": "third15",
                              "label": "sjksajkd"
                            }
                          ]
                        },
                        {
                          "type": "group",
                          "body": [
                            {
                              "type": "input-text",
                              "name": "third16",
                              "label": "sjksajkd"
                            },
                            {
                              "type": "select",
                              "name": "third4",
                              "label": "sjksajkd",
                              "options": [
                                {
                                  "label": "a",
                                  "value": "a"
                                },
                                {
                                  "label": "b",
                                  "value": "b"
                                }
                              ]
                            },
                            {
                              "type": "input-text",
                              "name": "third18",
                              "label": "sjksajkd"
                            }
                          ]
                        }
                      ]
                    }
                  ]
                },
              ]
            }
          ]
        }
      ],
      "actions": [
        {
          "type": "button",
          "label": "取消"
        },
        {
          "type": "submit",
          "level": "primary",
          "label": "保存"
        }
      ]
    }
  }
}
