export default {
  "type": "page",
  "body": {
    "type": "form",
    "body": [
      {
        "name": "phone",
        "type": "input-text",
        "required": true,
        "label": "手机号",
        "addOn": {
          "id": "countdown1",
          "label": "发送验证码",
          "type": "button",
          // "countDown": 60,
          "confirmText": "确认要发出这个请求？",
          // "countDownTpl": "${timeLeft} 秒后重发",
          "actionType": "ajax",
          "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm?phone=${phone}",
          "onEvent": {
            "click": {
              "actions": [
                {
                  "actionType": "toast",
                  "args": {
                    "msgType": "success",
                    "msg": "我是全局警告消息，可以配置不同类型和弹出位置~",
                  },
                  // "preventDefault": true,
                }
              ]
            }
          }
        }
      }
    ]
  }
}
