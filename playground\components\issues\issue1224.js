export default {
  "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "id": "submitForm",
    "labelWidth": 40,
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "body": [
      {
        "type": "input-tree",
        "name": "tree",
        "label": "Tree",
        "searchable": true,
        "creatable": true,
        "removable": true,
        "editable": true,
        "options": [
          {
            "label": "Folder A",
            "value": 1,
            "children": [
              {
                "label": "file A",
                "value": 2
              },
              {
                "label": "Folder B",
                "value": 3,
                "children": [
                  {
                    "label": "file b1",
                    "value": 3.1
                  },
                  {
                    "label": "file b2",
                    "value": 3.2
                  }
                ]
              }
            ]
          },
          {
            "label": "file C",
            "value": 4
          },
          {
            "label": "file D",
            "value": 5
          }
        ],
        "onEvent": {
          "change": {
            "actions": [
              {
                "actionType": "validate",
                "componentId": "submitForm"
              },
              // {
              //   "actionType": "setValue",
              //   "componentId": "submitForm",
              //   "preventDefault": true,
              //   "args": {
              //     "value": {
              //       "tree": "${value}"
              //     }
              //   }
              // }
              {
                "actionType": "toast",
                "args": {
                  "msg": "currentValue: ${tree} ,value: ${value}"
                }
              }
            ]
          }
        }
      },
    ]
  }
}