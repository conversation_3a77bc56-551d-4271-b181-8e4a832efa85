export default {
  type: 'page',
  body: {
    type: 'form',
    debug: true,
    body: [
      {
        type: 'combo',
        name: 'combo',
        label: '组合多条多行',
        value: [{a: '111'}, {a: '222'}, {a: '333'}],
        multiple: true,
        tabsMode: true,
        removable: false,
        mountOnEnter: '${index === 2}',
        unmountOnExit: '${index === 0 || index === 2}', // 表达式：第1、3个tab为true，第2个为false
        items: [
          {
            name: 'a',
            label: '文本',
            type: 'input-text',
          }
        ]
      }
    ]
  }
}