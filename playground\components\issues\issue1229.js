export default {
  "type": "page",
  "body": {
    "type": "form",
    "mode": "horizontal",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "debug": true,
    "body": [
      {
        "type": "switch",
        "name": "open",
        "label": "开关"
      },
      {
        "type": "combo",
        "name": "combo1",
        "label": "Combo 单选展示",
        "multiple": true,
        "strictMode": false,
        "syncFields": [
          "open"
        ],
        "items": [
          {
            "name": "text",
            "label": "文本",
            "type": "input-text"
          },
          {
            "name": "select",
            "label": "选项",
            "type": "select",
            "options": [
              "a",
              "b",
              "c"
            ],
            "visibleOn": "${open}",
            "clearValueOnHidden": true
          }
        ]
      }
    ]
  }
}

// export default {
//   "type": "page",
//   "body": {
//     "type": "form",
//     "debug": true,
//     "mode": "horizontal",
//     "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
//     "body": [
//       {
//         "type": "input-text",
//         "label": "父级文本框",
//         "name": "super_text",
//         "value": "123"
//       },
//       {
//         "type": "input-text",
//         "label": "辅助文本框",
//         "name": "asssit_text",
//         "value": "a"
//       },
//       {
//         "type": "combo",
//         "name": "combo2",
//         "label": "可获取父级数据",
//         "multiple": true,
//         "canAccessSuperData": true,
//         "strictMode": false,
//         "syncFields": [
//           "super_text"
//         ],
//         "items": [
//           {
//             "name": "super_text",
//             "type": "input-text"
//           }
//         ]
//       }
//     ]
//   }
// }