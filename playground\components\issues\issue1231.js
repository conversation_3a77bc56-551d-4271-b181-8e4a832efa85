export default {
  "type": "page",
  "body": {
    "type": "form",
    "id": "submitForm",
    "debug": true,
    "data": {
      "resources": [
        {
          "label": "导航栏组件",
          "rootRules": [
            {
              "rootName": "导航栏1"
            },
            {
              "rootName": "导航栏2"
            }
          ]
        },
        {
          "label": "公告栏组件",
          "rootRules": [
            {
              "rootName": "公告栏1"
            },
            {
              "rootName": "公告栏2"
            }
          ]
        }
      ],
      "activeComponentInfo": null
    },
    "body": [
      {
        "type": "select",
        "name": "select",
        "options": [
          {
            "label": "导航栏组件",
            "value": 0
          },
          {
            "label": "公告栏组件",
            "value": 1
          }
        ],
        "onEvent": {
          "change": {
            "actions": [
              {
                "componentId": "submitForm",
                "actionType": "setValue",
                "args": {
                  "value": {
                    "activeComponentInfo": "${resources[value]}"
                  }
                }
              }
            ]
          }
        }
      },
      {
        "type": "combo",
        "name": "activeComponentInfo.rootRules",
        "label": false,
        "multiple": true,
        "copyable": true,
        "tabsMode": true,
        "reverseMode": true,
        "unmountOnExit": true,
        "draggable": true,
        "strictMode": false,
        "canAccessSuperData": true,
        // "mountOnEnter": false,
        "tabsLabelTpl": "${'规则' + (index+1)}",
        "items": [
          {
            "type": "tpl",
            "tpl": "${rootName}"
          }
        ]
      }
    ]
  }
}

// export default {
//   "type": "page",
//   "body": {
//     type: 'form',
//     mode: 'horizontal',
//     body: [
//       {
//         type: 'combo',
//         name: 'combo',
//         label: '动态增删测试',
//         value: [{a: 'value1'}, {a: 'value2'}],
//         multiple: true,
//         tabsMode: true,
//         mountOnEnter: true,
//         items: [
//           {
//             name: 'a',
//             label: '文本',
//             type: 'input-text'
//           }
//         ]
//       }
//     ]
//   }
// }