export default {
  "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "labelWidth": 40,
    "id": "formId",
    "data": {
      "options": []
    },
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "body": [
      {
        "type": "input-tree",
        "name": "tree",
        "label": "Tree",
        "creatable": true,
        "removable": true,
        "editable": true,
        "source": "${options}",
        "addControls": [
          {
            label: '节点名称',
            type: 'input-text',
            required: true,
            name: 'label',
            value: '',
            placeholder: '请输入字段名称',
          },
          {
            label: '节点值',
            type: 'input-text',
            required: true,
            name: 'value',
            placeholder: '请输入字段code',
          }
        ],
        creatable: true,
        "onEvent": {
          add: {
            actions: [
              {
                "actionType": "setValue",
                "componentId": "formId",
                "preventDefault": true,
                args: {
                  value: {
                    "options": [
                      {
                        "label": "Folder A",
                        "value": 1,
                        "children": [
                          {
                            "label": "file A",
                            "value": 2
                          },
                          {
                            "label": "file B",
                            "value": 3
                          }
                        ]
                      },
                      {
                        "label": "file C",
                        "value": 4
                      },
                      {
                        "label": "file D",
                        "value": 5
                      }
                    ]
                  }
                }
              }
            ]
          }
        }
      }
    ]
  }
}
