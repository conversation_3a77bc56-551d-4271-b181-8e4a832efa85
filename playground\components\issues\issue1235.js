{"type": "page", "body": {"type": "form", "labelWidth": 40, "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm", "body": [{"type": "input-tree", "name": "tree", "label": "Tree", "canCancelSelectedNode": true, "joinValues": false, "searchable": true, "options": [{"label": "Folder A", "value": 1, "children": [{"label": "file A", "value": 2}, {"label": "file B", "value": 3}]}, {"label": "file C", "value": 4}, {"label": "file D", "value": 5}, {"label": "Folder E", "value": "61", "children": [{"label": "Folder G", "value": "62", "children": [{"label": "file H", "value": 6}, {"label": "file I", "value": 7}]}]}]}]}}