export default {
  "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "id": "myForm",
    "data": {
      "originTableData": [
        {
          "eventType": "a",
          "sourceName": "dwa.dwa_risk_wewewewe_rwewrwer1",
          "text1": "t1",
          "text2": "t2",
          "id": "a"
        }
      ],
      "tableAddBtnVisible": false,
    },
    "body": {
      "label": "触发事件",
      "type": "group",
      "body": [
        {
          "type": "service",
          id: "rebuildService",
          "schemaApi": {
            "url": "/",
            "dataProvider": true,
            "tdata": {
              "originTableData": "${originTableData}"
            },
            "sendOn": "${originTableData}",
            "trackExpression": "${originTableData}",
            adaptor: (payload)=>{
              return {
                "type": "input-table",
                "id": "myTable",
                "perPage": 5,
                "name": "filterTableData",
                "required": true,
                "editable": true,
                "removable": true,
                "addable": true,
                // "tableAddBtnVisibleOn": "tableAddBtnVisible",
                "columns": [
                  {
                    "label": "事件类型",
                    "name": "eventType"
                  },
                  {
                    "label": "过滤条件表达式",
                    "name": "text1"
                  }
                ],
                "label": false,
                "footerToolbarClassName": "flex-1 pr-4",
                "onEvent": {
                  "addConfirm": {
                    "actions": [
                      {
                        actionType: 'setValue',
                        componentId: 'myForm',
                        args: {
                          value: {
                            filterTableData: "${CONCAT(filterTableData,[event.data.item])}",
                            originTableData: "${CONCAT(originTableData,[event.data.item])}",
                          },
                        }
                      },
                      {
                        "actionType": 'rebuild',
                        "componentId": 'rebuildService',
                      }
                    ]
                  },
                }
              }
            }
          }
        },
        {
          "type": "formula",
          "name": "filterTableData",
          "id": "filterTableDataFormula",
          "autoSet": false,
          "formula": "${keywords ? ARRAYFILTER(originTableData, row => row.eventType === keywords) : originTableData}",
        }
      ]
    }
  }
}
