export default {
  "type": "page",
  "body": {
    "type": "form",
    "mode": "horizontal",
    "data": {
      "groupfix1": 10,
      "groupfix2": 20
    },
    "body": [
      {
        "type": "group",
        "body": [
          {
            "type": "between",
            "label": "指标异动阈值",
            "required": true,
            "separatorStr": "~",
            "items": [
              {
                "type": "input-group",
                "name": "rangeTypeStart",
                "suffix": "%",
                "body": [
                  {
                    "type": "input-number",
                    "name": "rangeTypeStart",
                    "label": false,
                    "required": true,
                    "dependencies": [
                      "rangeTypeEnd"
                    ],
                    "validations": {
                      "isNumeric": true,
                      "minimum": 0
                    }
                  }
                ]
              },
              {
                "type": "input-group",
                "name": "rangeTypeEnd",
                "suffix": "%",
                "body": [
                  {
                    "type": "input-number",
                    "name": "rangeTypeEnd",
                    "label": false,
                    "required": true,
                    "dependencies": [
                      "rangeTypeStart"
                    ],
                    "validations": {
                      "isNumeric": true,
                      "minimum": "${rangeTypeStart || 0}"
                    },
                    "validationErrors": {
                      "minimum": "请筛选正确的区间范围",
                    },
                  },
                ],
              }
            ]
          }
        ]
      }
    ]
  }
}
