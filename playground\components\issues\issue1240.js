export default {
  "type": "page",
  "body": {
    "type": "form",
    "labelWidth": 40,
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "body": [
      {
        "type": "icon",
        "icon": "pause-circle"
      },
      {
        "type": "input-tree",
        "name": "tree",
        "label": "Tree",
        "searchable": true,
        "creatable": true,
        "removable": true,
        "editable": true,
        "options": [
          {
            "label": "Folder A",
            "value": 1,
            "icon": "fa fa-pause-circle",
            "children": [
              {
                "label": "file A",
                "value": 2,
                "icon": "home",
              },
              {
                "label": "Folder B",
                "value": 3,
                "children": [
                  {
                    "label": "file b1",
                    "value": 3.1
                  },
                  {
                    "label": "file b2",
                    "value": 3.2
                  }
                ]
              }
            ]
          },
          {
            "label": "file C",
            "value": 4
          },
          {
            "label": "file D",
            "value": 5
          }
        ]
      },
    ]
  }
}
