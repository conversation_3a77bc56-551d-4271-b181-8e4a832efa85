export default {
  "type": "page",
  "data": {
    "name1": "张三"
  },
  "body": [
    {
      "type": "form",
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
      "body": [
        {
          "type": "input-text",
          "name": "name",
          "label": "姓名"
        },
        {
          "name": "email",
          "type": "input-email",
          "label": "邮箱"
        },
      ],
    },
    {
      "type": "button",
      "label": "提示+关联信息",
      "actionType": "dialog",
      "dialog": {
        "title": "节点下线",
        "data": {
          "&": "$$",
        },
        "showCloseButton": false,
        "body": [
          {
            "type": "form",
            "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm?name=${name}",
            "body": {
              "type": "tpl",
              "tpl": "name:${name2}"
            }
          },
        ]
      }
    },
  ]
}
