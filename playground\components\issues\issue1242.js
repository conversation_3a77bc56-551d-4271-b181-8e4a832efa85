export default {
  "type": "page",
  "body":  {
    type: 'button',
    label: '取消',
    onEvent: {
      click: {
        actions: [
          {
            actionType: 'dialog',
            dialog: {
              type: 'dialog',
              title: '验证模型特征',
              body: [
                {
                  type: 'form',
                  id: 'valModelFeaForm',
                  body: [
                    {
                      type: 'select',
                      name: 'trainingTableName',
                      label: '表名',
                      "options": [
                        {
                          "label": "IEInternet Explorer（简称：IE）是微软公司推出的一款网页浏览器。原称Microsoft Internet Explorer（6版本以前）和Windows Internet Explorer（7、8、9、10、11版本）。在IE7以前，中文直译为“网络探路者”，但在IE7以后官方便直接俗称",
                          "value": "IE"
                        },
                        {
                          "label": "FIREFOX",
                          "value": "FIREFOX"
                        },
                        {
                          "label": "CHROME(Google Chrome是一款由Google公司开发的网页浏览器。 该浏览器基于其他开源软件如WebKit撰写，目标是提升稳定性、速度和安全性，并创造出简单且有效率的使用者界面。)",
                          "value": "CHROME"
                        },
                      ],
                      // 清空 主题字段、业务时间字段
                      onEvent: {
                        change: {
                          actions: [
                            {
                              actionType: 'toast', // 执行toast提示动作
                              args: {
                                // 动作参数
                                msgType: 'info',
                                msg: '${event.data.selectedItems | json}',
                              },
                            },
                            // {
                            //   actionType: 'toast', // 执行toast提示动作
                            //   args: {
                            //     // 动作参数
                            //     msgType: 'info',
                            //     msg: '${selectedItems | json}',
                            //   },
                            // },
                          ],
                        },
                      },
                    },
                  ],
                },
              ],
            }
          },
        ],
      },
    },
  },
}