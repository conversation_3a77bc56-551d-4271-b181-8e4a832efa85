export default {
  "type": "page",
  "body": {
    "type": "form",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "body": [
      {
        "type": "input-text",
        "name": "userName",
        "label": "用户名",
        "placeholder": "请输入用户名"
      },
      {
        "type": "tooltip-wrapper",
        "title": "删除任务提示",
        "content": {
          "type": "form",
          id: 'assess_form_id',
          wrapWithPanel: false,
          "body": [
            {
              "type": "input-text",
              "required": true,
              "label": "文本框",
              "name": "text"
            }
          ]
        },
        "disabledOn": "${differenceRate <= 69}",
        "showArrow": true,
        "inline": true,
        "tooltipTheme": "light",
        "trigger": "click",
        "footerBottonProps": {
          "showButtons": [],
          "okText": "确定",
          "cancelText": "取消"
        },
        "body": {
          "type": "button",
          "disabledTip": {
            "tooltipTheme": "dark",
            "placement": "top",
            "content": "当前状态禁止删除"
          },
          "label": "删除",
          "disabledOn": "${differenceRate <= 69}",
          "level": "link"
        },
        "onEvent": {
          "confirm": {
            "actions": [
              {
                actionType: 'validate',
                componentId: 'assess_form_id',
                // preventDefault: true,
              },
            //   {
            // actionType: 'submit',
            // expression: "${event.data.validateResult.responseData}",
            // componentId: 'assess_form_id',
            //   },
            ]
          }
        }
      },
    ]
  }
}
