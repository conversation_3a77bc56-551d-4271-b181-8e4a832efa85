// export default {
//   "type": "page",
//   "body": {
//     "type": "form",
//     "debug": true,
//     "mode": "horizontal",
//     "api": "https://aisuda.bce.baidu.com/amis/api/mock2/form/saveFormFailedCombo?waitSeconds=1",
//     "body": [
//       {
//         "type": "combo",
//         "name": "combo1",
//         "label": "组合多条单行",
//         "multiple": true,
//         "value": [
//           {
//             "a": "a1",
//             "b": "a"
//           },
//           {
//             "a": "a2",
//             "b": "c"
//           }
//         ],
//         "items": [
//           {
//             "name": "a",
//             "type": "input-text"
//           },
//           {
//             "name": "b",
//             "type": "select",
//             "options": [
//               "a",
//               "b",
//               "c"
//             ]
//           }
//         ]
//       },
//       {
//         "type": "combo",
//         "name": "combo2",
//         "label": "组合单条单行",
//         "value": {
//           "a": "a",
//           "b": "b"
//         },
//         "items": [
//           {
//             "name": "a",
//             "type": "input-text"
//           },
//           {
//             "name": "b",
//             "type": "select",
//             "options": [
//               "a",
//               "b",
//               "c"
//             ]
//           }
//         ]
//       }
//     ]
//   }
// }

export default {
  "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "mode": "horizontal",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveFormFailedTable?waitSeconds=1",
    "body": [
      {
        "label": "Table 服务端校验",
        "type": "input-table",
        "name": "table",
        "multiple": true,
        "value": [
          {
            "a": "a1",
            "b": "b1"
          },
          {
            "a": "a2",
            "b": "b2"
          }
        ],
        "columns": [
          {
            "name": "a",
            "type": "text",
            "label": "A",
            "quickEdit": {
              "mode": "inline"
            }
          },
          {
            "name": "b",
            "type": "text",
            "label": "B",
            "quickEdit": {
              "mode": "inline"
            }
          }
        ]
      },
      {
        "label": "Combo 内 Table 服务端校验",
        "type": "combo",
        "name": "combo",
        "items": [
          {
            "name": "a",
            "type": "text",
            "label": "A"
          },
          {
            "label": "Table",
            "type": "input-table",
            "name": "table",
            "multiple": true,
            "value": [
              {
                "a": "a1",
                "b": "b1"
              },
              {
                "a": "a2",
                "b": "b2"
              }
            ],
            "columns": [
              {
                "name": "a",
                "type": "text",
                "label": "A",
                "quickEdit": {
                  "mode": "inline"
                }
              },
              {
                "name": "b",
                "type": "text",
                "label": "B",
                "quickEdit": {
                  "mode": "inline"
                }
              }
            ]
          }
        ]
      }
    ]
  }
}