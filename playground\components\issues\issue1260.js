export default {
  "type": "page",
  "body": {
    "type": "form",
    "mode": "horizontal",
    "static": true,
    "labelWidth": 80,
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
    "data": {
      "combos": [
        {
          "combo1": [
            {
              "text": "333",
              "select": "a"
            },
            {
              "text": "333",
              "select": "a"
            },
            {
              "text": "333",
              "select": "a"
            }
          ],
          "text": "1"
        },
        {
          "combo1": [
            {
              "text": "333sss",
              "select": "a"
            }
          ],
          "text": "1"
        },
        {
          "combo1": [
            {
              "text": "333",
              "select": "a"
            }
          ],
          "text": "1"
        }
      ]
    },
    "body": [
      {
        "type": "group",
        "body": [
          {
            "type": "combo",
            "name": "combos",
            "label": "产品配置",
            "multiple": true,
            "multiLine": true,
            "copyable": true,
            "extraActions": {
              "type": "icon",
              "icon": "fa-exchange",
              "onEvent": {
                "click": {
                  "actions": [
                    {
                      "actionType": "dialog",
                      "showCloseButton": false,
                      "dialog": {
                        "title": "触发标题",
                        "body": "触发了点击"
                      }
                    }
                  ]
                }
              }
            },
            "items": [
              {
                "type": "fieldSet",
                "title": "配置${index + 1}",
                "collapsable": true,
                "body": [
                  {
                    "type": "group",
                    "label": "产品名称",
                    "body": [
                      {
                        "name": "text",
                        "label": false,
                        "type": "input-text"
                      }
                    ]
                  },
                  {
                    "type": "combo",
                    "name": "combo1",
                    "label": false,
                    "multiple": true,
                    "draggable": true,
                    "items": [
                      {
                        "name": "text",
                        "label": "结算方式",
                        "type": "input-text"
                      },
                      {
                        "name": "select",
                        "label": "结算单价",
                        "type": "select",
                        "options": [
                          "a",
                          "b",
                          "c"
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    ],
    "actions": [
      {
        "type": "button",
        "label": "取消"
      },
      {
        "type": "button",
        "label": "提交",
        "level": "primary"
      }
    ]
  }
}
