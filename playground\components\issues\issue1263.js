export default {
  "type": "page",
  "body": {
    "type": "flex",
    "direction": "column",
    "justify": "start",
    "gap": true,
    "items": [
      {
        "type": "crud",
        "autoGenerateFilter": false,
        "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4",
        "columns": [
          {
            "name": "id",
            "label": "ID"
          },
          {
            "name": "engine",
            "label": "Rendering engine",
            "headSearchable": {
              "type": "input-text",
              "name": "engine3",
              "label": "Rendering engine"
            }
          },
          {
            "name": "browser",
            "label": "Browser",
          },
          {
            "name": "platform",
            "label": "Platform(s)",
            "headSearchable": {
              "type": "input-text",
              "name": "platform3",
              "label": "Platform(s)"
            }
          },
          {
            "name": "version",
            "label": "Engine version"
          },
          {
            "name": "grade",
            "label": "CSS grade"
          },
              {
                "name": "platform",
                "label": "Platform(s)",
                "headSearchable": {
                  "type": "input-text",
                  "name": "platform3",
                  "label": "Platform(s)"
                }
              },

        ]
      },
      {
        "type": "button",
        "label": "中号",
        "actionType": "dialog",
        "dialog": {
          "title": "中号",
          "size": "lg",
          "body": {
            "type": "crud",
            "autoGenerateFilter": false,
            "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4",
            "columns": [
              {
                "name": "id",
                "label": "ID"
              },
              {
                "name": "engine",
                "label": "Rendering engine",
                "headSearchable": {
                  "type": "input-text",
                  "name": "engine3",
                  "label": "Rendering engine"
                }
              },
              {
                "name": "browser",
                "label": "Browser",
              },
              {
                "name": "platform",
                "label": "Platform(s)",
                "headSearchable": {
                  "type": "input-text",
                  "name": "platform3",
                  "label": "Platform(s)"
                }
              },
              {
                "name": "version",
                "label": "Engine version"
              },
              {
                "name": "grade",
                "label": "CSS grade"
              },
                  {
                    "name": "platform",
                    "label": "Platform(s)",
                    "headSearchable": {
                      "type": "input-text",
                      "name": "platform3",
                      "label": "Platform(s)"
                    }
                  },

            ]
          }
        }
      },

    ]
  }
}
