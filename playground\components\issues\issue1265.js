export default {
  "type": "page",
  "idExpr": "${'101'}",
  "body": [{
    "title": "",
    "type": "form",
    "debug": true,
    "mode": "horizontal",
    "api": "https://3xsw4ap8wah59.cfc-execute.bj.baidubce.com/api",
    "id": "form101",
    "autoFocus": false,
    "body": [
      {
        "type": "input-text",
        "name": "validateResult",
        "label": "验证结果",
        "value": "",
        "size": "full"
      },
      {
        "type": "combo",
        "name": "combo101",
        "label": "组合多条多行",
        // "unmountOnExit": true,
        "multiple": true,
        "multiLine": true,
        "value": [
          {}
        ],
        "tabsMode": true,
        "subFormMode": "horizontal",
        "subFormHorizontal": {
          "labelWidth": 40
        },
        "maxLength": 3,
        "items": [
          {
            "name": "a",
            "label": "文本",
            "type": "input-text",
            "placeholder": "文本",
            "value": "",
            "size": "full",
            "required": true
          },
          {
            "name": "b",
            "label": "选项",
            "type": "select",
            "required": true,
            "options": [
              "a",
              "b",
              "c"
            ],
            "size": "full"
          },
          {
            "type": "submit",
            "label": "提交",
            "actionType": "reload",
            "target": "service101?index=${index}",
          },
          {
            "type": "button",
            "label": "校验",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "actionType": "validate",
                    "componentName": "${'combo101-form-' + index}",
                    "outputVar": "form_validate_result",
                    "args": {
                      "validateFields": [
                        "a",
                      ]
                    }
                  },
                  {
                    "actionType": "toast",
                    "args": {
                      "msg": "${event.data.form_validate_result|json}"
                    }
                  }
                ]
              }
            }
          }
          // {
          //   "type": "form",
          //   "body": [
          //     {
          //       "type": "input-text",
          //       "name": "input101",
          //       "label": "输入",
          //       "size": "full"
          //     }
          //   ]
          // }
        ]
      },
      {
        "type": "service",
        "name": "service101",
        "initFetch": false,
        "api": {
          "url": "https://aisuda.bce.baidu.com/amis/api/mock2/form/saveForm",
          "data": {
            "&": "${combo101[index]}"
          },
          responseData: {}
        }
      }
    ],
    "submitText": null,
    "actions": []
  }]
}

// export default {
//   "type": "page",
//   "body": [
//     {
//       "type": "form",
//       "target": "detailForm",
//       "body": [
//         {
//           "type": "input-text",
//           "placeholder": "关键字",
//           "name": "keywords"
//         }
//       ]
//     },
//     {
//       "type": "form",
//       "name": "detailForm",
//       // "initApi": "/amis/api/mock2/page/initData?keywords=${keywords}",
//       "body": [
//         {
//           "label": "名称",
//           "type": "static",
//           "name": "name"
//         },
//         {
//           "label": "作者",
//           "type": "static",
//           "name": "author"
//         },
//         {
//           "label": "关键字",
//           "type": "static",
//           "name": "keywords"
//         },
//         {
//           "label": "请求时间",
//           "type": "static-datetime",
//           "format": "YYYY-MM-DD HH:mm:ss",
//           "name": "date"
//         }
//       ]
//     }
//   ]
// }
