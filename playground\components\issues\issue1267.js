// export default{
//   "type": "page",
//   "body": {
//     "type": "form",
//     "labelWidth": 60,
//     "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
//     "body": {
//       "type": "group-container",
//       // "activeKey": [
//       //   "1"
//       // ],
//       "collapsible": true,
//       "items": [
//         {
//           "collapsed": false, // 这个属性无用
//           "header": {
//             "title": "第一步，基础信息",
//           },
//           "body": [
//             {
//               "type": "group",
//               "body": [
//                 {
//                   "type": "input-text",
//                   "name": "text1",
//                   "label": "姓名"
//                 },
//                 {
//                   "type": "input-text",
//                   "name": "text2",
//                   "label": "年龄"
//                 },
//                 {
//                   "type": "input-text",
//                   "name": "text3",
//                   "label": "班级",
//                   "required": true
//                 }
//               ]
//             }
//           ]
//         }
//       ]
//     },
//     "actions": [
//       {
//         "type": "button",
//         "label": "取消"
//       },
//       {
//         "type": "submit",
//         "level": "primary",
//         "label": "保存"
//       }
//     ]
//   }
// }

export default {
  "type": "page",
  "body": {
    "type": "collapse-group",
    "body": [
      {
        "type": "collapse",
        "key": "1",
        "header": "标题1",
        "body": "这里是内容1"
      },
      {
        "type": "collapse",
        "key": "2",
        "header": "标题2",
        "body": "这里是内容2",
        "collapsed": false
      },
      {
        "type": "collapse",
        "collapsed": false,
        "key": "3",
        "header": "标题3",
        "body": "这里是内容3"
      }
    ]
  }
}
