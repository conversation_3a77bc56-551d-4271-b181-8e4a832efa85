export default{
  type: "page",
  body: [
    {
      debug: true,
      type: "form",
      id: "form-comp",
      data: {
        list: new Array(20).fill(0).map((item, index) => ({ order: index
        })),
      },
      body: {
        type: "crud",
        source: "${list}",
        updateAllRows: true,
        perPage: 5,
        columns: [
          {
            name: "order",
            label: "order",
          },
          {
            type: "operation",
            label: "操作",
            buttons: [
              {
                type: "button",
                label: "删除",
                onEvent: {
                  click: {
                    actions: [
                      {
                        actionType: "custom",
                        script: (context, doAction, event) => {
                          console.log('event order: ', event.data.order)
                          console.log('list: ', context.props.data.list);
                          const newList = context.props.data.list.filter(
                            (item) => item.order !== event.data.order
                          )

                          doAction({
                            actionType: "setValue",
                            componentId: "form-comp",
                            args: {
                              value: {
                                list: newList,
                              },
                            },
                          });
                        },
                      },
                    ],
                  },
                },
              },
            ],
          },
        ],
      },
    },
  ],
}
