export default {
  "type": "page",
  "body": {
    "type": "crud",
    "loadDataOnce": true,
    // "loadDataOnceFetchOnFilter": true,
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4",
    "topToolbar": [
      {
        "type": "button",
        "label": "主按钮",
        "actionType": "url",
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
        "level": "primary",
        "blank": false
      },
      {
        "type": "button",
        "label": "次按钮1",
        "actionType": "url",
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
      },
      {
        "type": "button",
        "label": "次按钮2",
        "actionType": "url",
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
      },
      {
        "type": "button",
        "label": "次按钮3",
        "actionType": "url",
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
      },
      {
        "type": "button",
        "label": "次按钮4",
        "disabled": true
      },
      {
        "type": "button",
        "label": "次按钮5"
      },
      {
        "type": "button",
        "label": "次按钮6"
      }
    ],
    "filter": {
      "title": "",
      "body": [
        {
          "type": "group",
          "mode": "horizontal",
          "body": [
            {
              "type": "input-text",
              "name": "keywords",
              "label": "关键字",
              "clearable": true,
              "placeholder": "通过关键字搜索",
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "engine",
              "label": "Engine",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "platform",
              "label": "Platform",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "keywords1",
              "label": "关键字1",
              "clearable": true,
              "placeholder": "通过关键字搜索",
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "engine1",
              "label": "Engine1",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "platform1",
              "label": "Platform1",
              "clearable": true,
              "columnRatio": 4
            }
          ]
        }
      ],
      "actions": [
        {
          "type": "reset",
          "label": "重 置",
          "actionType": "reset-and-submit"
        },
        {
          "type": "submit",
          "level": "primary",
          "label": "查 询"
        }
      ]
    },
    "columns": [
      {
        "name": "id",
        "label": "ID"
      },
      {
        "name": "engine",
        "label": "Rendering engine",
        "headSearchable": {
          "type": "input-text",
          "name": "engine3",
          "label": "Rendering enginer"
        }
      },
      {
        "name": "browser",
        "label": "Browser",
        "headSearchable": {
          "type": "input-text",
          "name": "browser3",
          "label": "Browser"
        }
      },
      {
        "name": "platform",
        "label": "Platform(s)"
      },
      {
        "name": "version",
        "label": "Engine version"
      },
      {
        "name": "grade",
        "label": "CSS grade"
      },
      {
        "type": "operation",
        "label": "操作",
        "buttons": [
          {
            "type": "button",
            "level": "link",
            "label": "详情",
            "actionType": "dialog",
            "dialog": {
              "title": "详情",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "删除",
            "type": "button",
            "actionType": "ajax",
            "level": "link",
            "disabled": true,
            "confirmText": "确认要删除吗？",
            "api": {
              "method": "delete",
              "url": "/commercialopr/messagecenterconf/wxgateway/mp-app-mappings"
            }
          },
          {
            "label": "编辑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "编辑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          },
          {
            "label": "空跑",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "空跑",
              "showCloseButton": false,
              "body": "这是个简单的弹框。"
            }
          }
        ]
      }
    ]
  }
}

// #issue 579
// export default {
//   "type": "page",
//   "body": {
//     "type": "crud",
//     "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
//     "syncLocation": false,
//     "filterFormAdvanceSearchAble": true,
//     "filter": {
//       "debug": true,
//       "title": "",
//       "id": "page-crud",
//       "body": [
//         {
//           "type": "input-text",
//           "name": "keywords",
//           "label": "关键字",
//           "clearable": true,
//           "placeholder": "通过关键字搜索",
//           "columnRatio": 4
//         },
//         {
//           "type": "input-text",
//           "name": "engine",
//           "label": "Engine",
//           "clearable": true,
//           "columnRatio": 4
//         },
//         {
//           "type": "input-text",
//           "name": "platform",
//           "label": "Platform",
//           "clearable": true,
//           "columnRatio": 4
//         },
//         {
//           "type": "input-text",
//           "name": "keywords1",
//           "label": "关键字1",
//           "clearable": true,
//           "placeholder": "通过关键字搜索",
//           "columnRatio": 4
//         },
//         {
//           "type": "input-text",
//           "name": "engine1",
//           "label": "Engine1",
//           "clearable": true,
//           "columnRatio": 4
//         },
//         {
//           "type": "input-text",
//           "name": "platform1",
//           "label": "Platform1",
//           "clearable": true,
//           "columnRatio": 4
//         },
//         {
//           "type": "input-text",
//           "name": "keywords2",
//           "label": "关键字2",
//           "clearable": true,
//           "placeholder": "通过关键字搜索",
//           "columnRatio": 4
//         },
//         {
//           "type": "input-text",
//           "name": "engine2",
//           "label": "Engine2",
//           "clearable": true,
//           "columnRatio": 4
//         },
//         {
//           "type": "input-text",
//           "name": "platform2",
//           "label": "Platform2",
//           "clearable": true,
//           "columnRatio": 4
//         }
//       ],
//       "actions": [
//         {
//           "type": "button",
//           "label": "重 置",
//           "onEvent": {
//             "click": {
//               "actions": [
//                 {
//                   "actionType": "clear",
//                   "componentId": "page-crud"
//                 }
//               ]
//             }
//           }
//         },
//         {
//           "type": "submit",
//           "level": "primary",
//           "label": "查 询"
//         }
//       ]
//     },
//     "columns": [
//       {
//         "name": "id",
//         "label": "ID"
//       },
//       {
//         "name": "engine",
//         "label": "Rendering engine"
//       },
//       {
//         "name": "browser",
//         "label": "Browser"
//       },
//       {
//         "name": "platform",
//         "label": "Platform(s)"
//       },
//       {
//         "name": "version",
//         "label": "Engine version"
//       },
//       {
//         "name": "grade",
//         "label": "CSS grade"
//       },
//       {
//         "type": "show-more",
//         "label": "操作",
//         "width": 80,
//         "buttons": [
//           {
//             "label": "详情",
//             "type": "button",
//             "level": "link",
//             "actionType": "dialog",
//             "dialog": {
//               "title": "查看详情",
//               "body": {
//                 "type": "form",
//                 "body": [
//                   {
//                     "type": "input-text",
//                     "name": "engine",
//                     "label": "Engine"
//                   },
//                   {
//                     "type": "input-text",
//                     "name": "browser",
//                     "label": "Browser"
//                   },
//                   {
//                     "type": "input-text",
//                     "name": "platform",
//                     "label": "platform"
//                   },
//                   {
//                     "type": "input-text",
//                     "name": "version",
//                     "label": "version"
//                   },
//                   {
//                     "type": "control",
//                     "label": "grade",
//                     "body": {
//                       "type": "tag",
//                       "label": "${grade}",
//                       "displayMode": "normal",
//                       "color": "active"
//                     }
//                   }
//                 ]
//               }
//             }
//           },
//           {
//             "label": "删除",
//             "type": "button",
//             "level": "link",
//             "disabledOn": "this.grade === 'A'"
//           }
//         ]
//       }
//     ]
//   }
// }


// #issue 458
// export default {
//   "type": "page",
//   "className": "bg-light",
//   "body": {
//     "type": "crud",
//     "syncLocation": false,
//     "columnsTogglable": false,
//     "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4",
//     "columns": [
//       {
//         "name": "engine",
//         "label": "Rendering engine",
//         "headSearchable": {
//           "type": "input-group",
//           "label": "数据保留规则",
//           "body": [
//             {
//               "type": "select",
//               "name": "unit",
//               "options": [
//                 {
//                   "label": "月",
//                   "value": "month"
//                 },
//                 {
//                   "label": "年",
//                   "value": "year"
//                 }
//               ]
//             },
//             {
//               "type": "input-tag",
//               "name": "engine",
//               "label": "数据保留规则",
//               "placeholder": "数据保留规则"
//             }
//           ]
//         }
//       },
//       {
//         "name": "browser",
//         "label": "Browser"
//       },
//       {
//         "name": "platform",
//         "label": "Platform(s)",
//         "headSearchable": {
//           "type": "input-text",
//           "name": "platform3",
//           "label": "Platform(s)"
//         }
//       }
//     ]
//   }
// }

// #issue 860
// export default {
//   "type": "page",
//   "data": {},
//   "initApi": {
//     "method": "get",
//     "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/mock2/form/getOptions",
//     "tdata": {
//       "platform": "${platform}"
//     },
//     "adaptor": "return ({ 'data': { 'engine': 'Gecko,Trident', 'platform': api.tdata.platform || 'Win 98+ / OSX.2+', 'engineOptions': [{label:'Blink', value:'Blink'}, {label:'Trident', value:'Trident'}, {label:'Gecko', value:'Gecko'}], 'platformOptions': [{label:'Win 95+', value:'Win 95+'}, {label:'Win XP', value:'Win XP'}, {label:'Win 98+ / OSX.2+', value:'Win 98+ / OSX.2+'}] } })"
//   },
//   "body": [
//     {
//       "type": "crud",
//       "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/mock2/crud/table4",
//       "syncLocation": false,
//       "autoGenerateFilter": {
//         "defaultExpanded": false,
//         "showBtnToolbar": false
//       },
//       "filter": {
//         "title": "",
//         "data": {},
//         "id": "filter-id",
//         "canAccessSuperData": true,
//         "body": [
//           {
//             "type": "group",
//             "mode": "horizontal",
//             "body": [
//               {
//                 "type": "select",
//                 "name": "grade",
//                 "label": "CSS grade",
//                 "placeholder": "选择grade",
//                 "clearable": true,
//                 "source": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/mock2/form/getOptions"
//               },
//               {
//                 "type": "select",
//                 "name": "engine",
//                 "label": "engine",
//                 "multiple": true,
//                 "placeholder": "选择engine",
//                 "source": "${engineOptions}"
//               },
//               {
//                 "type": "select",
//                 "name": "platform",
//                 "label": "平台",
//                 "multiple": true,
//                 "placeholder": "选择平台",
//                 "labelField": "label",
//                 "valueField": "value",
//                 "resetValue": "Win XP",
//                 "source": "${platformOptions}"
//               }
//             ]
//           }
//         ],
//         "actions": [
//           {
//             "type": "reset",
//             "label": "重 置",
//             // "onEvent": {
//             //   "click": {
//             //     "actions": [
//             //       {
//             //         "actionType": "setValue",
//             //         "componentId": "filter-id",
//             //         "args": {
//             //           "data": {
//             //             "engine": "Gecko,Trident",
//             //             "platform": "Win 98+ / OSX.2+"
//             //           }
//             //         }
//             //       }
//             //     ]
//             //   }
//             // }
//           },
//           {
//             "type": "submit",
//             "level": "primary",
//             "label": "查 询"
//           }
//         ]
//       },
//       "columns": [
//         {
//           "name": "engine",
//           "label": "Rendering engine"
//         },
//         {
//           "name": "browser",
//           "label": "Browser"
//         },
//         {
//           "name": "platform",
//           "label": "Platform(s)"
//         },
//         {
//           "name": "version",
//           "label": "Engine version"
//         },
//         {
//           "name": "grade",
//           "label": "CSS grade"
//         }
//       ]
//     }
//   ]
// }
