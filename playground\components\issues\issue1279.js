export default {
  "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "mode": "horizontal",
    "body": [
      {
        "type": "combo",
        "name": "combo101",
        "label": "组合多条多行",
        "multiple": true,
        "multiLine": true,
        "tabsMode": true,
        "value": [{}],
        "items": [
          {
            "type": "group-container",
            "collapsible": true,
            "validateName": "group1",
            "autoSwitchWhenValidated": true,
            "items": [
              {
                "header": {
                  "title": "第一步，基础信息"
                },
                "body": [
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "combo",
                        "label": false,
                        "name": "groupcombo",
                        "items": [
                          {
                            "type": "input-text",
                            "name": "text1",
                            "label": "姓名",
                            "required": true
                          },
                          {
                            "type": "input-text",
                            "name": "text2",
                            "label": "年龄"
                          },
                          {
                            "type": "input-text",
                            "name": "text3",
                            "label": "班级",
                            "required": true
                          }
                        ]
                      }
                    ]
                  },
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "input-text",
                        "name": "text4",
                        "label": "邮箱"
                      },
                      {
                        "type": "input-text",
                        "name": "text5",
                        "label": "电话"
                      },
                      {
                        "type": "input-text",
                        "name": "text6",
                        "label": "地址",
                        "columnRatio": 4
                      }
                    ]
                  },
                  {
                    "type": "group",
                    "body": [
                      {
                        "type": "input-text",
                        "name": "text7",
                        "label": "其它",
                        "columnRatio": 4
                      }
                    ]
                  }
                ]
              }
            ]
          },
          {
            "type": "flex",
            "justify": "flex-end",
            "items": [{
              "type": "button",
              "label": "校验",
              "onEvent": {
                "click": {
                  "actions": [
                    {
                      "actionType": "validate",
                      "componentName": "${'combo101-form-' + index}",
                      "outputVar": "validateRes",
                      "args": {
                        "validateFields": ["groupcombo", "group1"]
                      }
                    },
                    {
                      "actionType": "toast",
                      "args": {
                        "msg": "${validateRes|json}"
                      }
                    }
                  ]
                }
              }
            }],
          }
        ]
      }
    ]
  }
}
