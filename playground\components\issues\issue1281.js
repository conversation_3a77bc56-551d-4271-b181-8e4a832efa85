export default {
  "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "messages": {
      "validateFailed": "不好意思，校验失败",
      "showValidedMsg": true
    },
    "body": [
      {
        "type": "input-text",
        "name": "name",
        "label": "姓名",
        "required": true
      },
      {
        "name": "email",
        "type": "input-email",
        "label": "邮箱",
        "required": true
      },
      {
        "type": "service",
        "schemaApi": {
          "url": "/",
          "dataProvider": true,
          "adaptor": "return({status: 0, data: {type: 'input-text', name: 'cc', label: '测试'}})"
        }
      }
    ]
  }
}
