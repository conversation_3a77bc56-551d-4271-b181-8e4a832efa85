export default {
  "type": "page",
  "data": {
    "table": [
      {
        "a": "a1",
        "b": "b1",
        "c": {
          "c1": "123",
          "c2": "222"
        }
      }
    ],
    "title": "获取衍生特征值时，需传入以下参数，请赋值:"
  },
  "body": {
    "type": "form",
    "debug": true,
    "title": "",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
    "body": [
      {
        "type": "input-table",
        "name": "table",
        "label": "特征参数",
        "columns": [
          {
            "name": "a",
            "label": "A"
          },
          {
            "name": "b",
            "label": "B"
          },
          {
            "name": "radios",
            "type": "radios",
            "label": "radios",
            "options": [
              {
                "label": "OptionA",
                "value": "a"
              },
              {
                "label": "OptionB",
                "value": "b"
              },
              {
                "label": "OptionC",
                "value": "c"
              },
              {
                "label": "OptionD",
                "value": "d"
              }
            ]
          }
        ]
      }, {
        "name": "radios",
        "type": "radios",
        "label": "radios",
        "options": [
          {
            "label": "OptionA",
            "value": "a"
          },
          {
            "label": "OptionB",
            "value": "b"
          },
          {
            "label": "OptionC",
            "value": "c"
          },
          {
            "label": "OptionD",
            "value": "d"
          }
        ]
      }
    ],
  }
}
