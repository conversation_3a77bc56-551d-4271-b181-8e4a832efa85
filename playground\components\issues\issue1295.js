
export default {
  "type": "page",
  "body": {
    "type": "crud",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
    "syncLocation": false,
    "filterRowNum": 3,
    "filterFormAdvanceSearchAble": true,
    "pageField": "pageNo",
    "perPageField": "pageSize",
    "filter": {
      "debug": true,
      "title": "",
      "wrapperComponent": "form",
      "body": [
        {
          "type": "input-text",
          "name": "keywords",
          "label": "关键字",
          "clearable": true,
          "placeholder": "通过关键字搜索",
          "columnRatio": 4,
          "value": "111"
        },
        {
          "type": "select",
          "name": "engine",
          "clearable": true,
          "columnRatio": 4,
          "label": "人员选择",
          "multiple": true,
          "searchable": true,
          "selectMode": "associated",
          "leftMode": "tree",
          "source": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/departUser"
        },
        {
          "type": "input-text",
          "name": "platform",
          "label": "Platform",
          "clearable": true,
          "columnRatio": 4
        },
        {
          "type": "input-text",
          "name": "keywords1",
          "label": "关键字1",
          "clearable": true,
          "placeholder": "通过关键字搜索",
          "columnRatio": 4
        },
        {
          "type": "input-text",
          "name": "engine1",
          "label": "Engine1",
          "clearable": true,
          "columnRatio": 4
        },
        {
          "type": "input-text",
          "name": "platform1",
          "label": "Platform1",
          "clearable": true,
          "columnRatio": 4
        },
        {
          "type": "input-text",
          "name": "keywords2",
          "label": "关键字2",
          "clearable": true,
          "placeholder": "通过关键字搜索",
          "columnRatio": 4
        },
        {
          "type": "input-text",
          "name": "engine2",
          "label": "Engine2",
          "clearable": true,
          "columnRatio": 4
        },
        {
          "type": "input-text",
          "name": "platform2",
          "label": "Platform2",
          "clearable": true,
          "columnRatio": 4
        }
      ],
      "actions": [
        {
          "type": "reset",
          "label": "重 置"
        },
        {
          "type": "submit",
          "level": "primary",
          "label": "查 询"
        }
      ]
    },
    "columns": [
      {
        "name": "id",
        "label": "ID"
      },
      {
        "name": "engine",
        "label": "Rendering engine"
      },
      {
        "name": "browser",
        "label": "Browser"
      },
      {
        "name": "platform",
        "label": "Platform(s)"
      },
      {
        "name": "version",
        "label": "Engine version"
      },
      {
        "name": "grade",
        "label": "CSS grade"
      },
      {
        "type": "operation",
        "label": "操作",
        "width": 80,
        "buttons": [
          {
            "label": "详情",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "查看详情",
              "body": {
                "type": "form",
                "body": [
                  {
                    "type": "input-text",
                    "name": "engine",
                    "label": "Engine"
                  },
                  {
                    "type": "input-text",
                    "name": "browser",
                    "label": "Browser"
                  },
                  {
                    "type": "input-text",
                    "name": "platform",
                    "label": "platform"
                  },
                  {
                    "type": "input-text",
                    "name": "version",
                    "label": "version"
                  },
                  {
                    "type": "control",
                    "label": "grade",
                    "body": {
                      "type": "tag",
                      "label": "${grade}",
                      "displayMode": "normal",
                      "color": "active"
                    }
                  }
                ]
              }
            }
          },
          {
            "label": "删除",
            "type": "button",
            "level": "link",
            "disabledOn": "this.grade === 'A'"
          }
        ]
      }
    ],
    "headerFilter": {
      "debug": true,
      "body": [
        {
          type: 'button-group-select',
          name: 'querySelfType',
          align: 'right',
          value: 'all',
          options: [
            {
              value: 'all',
              label: '查询全部',
            },
            {
              value: 'SELF_CREATED',
              label: '我创建的',
            },
            {
               value: 'SELF_EDITED',
               label: '我的工单草稿',
            }
          ]
        }
      ]
    }
  }
}
