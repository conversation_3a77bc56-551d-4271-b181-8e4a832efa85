// export default {
//   "type": "page",
//   "id": "myPage",
//   "data": {
//     "ids": []
//   },
//   "body": [
//     {
//       "type": "tpl",
//       "tpl": "${ids|json}"
//     },
//     {
//       "type": "crud",
//       "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
//       "id": "tableId",
//       "showExpansionColumn": false,
//       "loadDataOnce": true,
//       "updateAllRows": true,
//       "columns": [
//         {
//           "name": "id",
//           "label": "ID"
//         },
//         {
//           "name": "engine",
//           "label": "Rendering engine"
//         },
//         {
//           "name": "browser",
//           "label": "Browser"
//         },
//         {
//           "name": "platform",
//           "label": "Platform(s)"
//         },
//         {
//           "name": "version",
//           "label": "Engine version"
//         },
//         {
//           "name": "grade",
//           "label": "CSS grade"
//         },
//         {
//           "type": "operation",
//           "label": "操作",
//           "width": 80,
//           "buttons": [
//             {
//               "type": "button",
//               "label": "${_amisExpanded ? '收起' : '展开'}",
//               "level": "link",
//               "onEvent": {
//                 "click": {
//                   "actions": [
//                     {
//                       "actionType": "toggleExpanded",
//                       "componentId": "tableId",
//                       "args": {
//                         "condition": "${id === currentId}",
//                         "currentId": "${id}"
//                       }
//                     }
//                   ]
//                 }
//               }
//             }
//           ]
//         }
//       ],
//       "subTable": {
//         "type": "crud",
//         // "trackExpression": "${ids}",
//         "updateAllRows": true,
//         "loadDataOnce": true,
//         "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
//         "columns": [
//           {
//             "name": "engine",
//             "label": "Engine"
//           },
//           {
//             "name": "grade",
//             "label": "Grade"
//           },
//           {
//             "name": "_rowSubTableId",
//             "label": "行子表格id"
//           },
//           {
//             "label": "操作",
//             "type": "operation",
//             "buttons": [
//               {
//                 "type": "button",
//                 "label": "添加",
//                 "level": "link",
//                 "visibleOn": "${!ARRAYSOME(ids, v => v === engine)}",
//                 "onEvent": {
//                   "click": {
//                     "actions": [
//                       {
//                         "actionType": "toast",
//                         "args": {
//                           "msg": "${ids|json}",
//                         }
//                       },
//                       {
//                         "actionType": "setValue",
//                         "componentId": "myPage",
//                         "args": {
//                           "value": {
//                             "ids": "${CONCAT(ids, [engine])}"
//                           }
//                         }
//                       }
//                     ]
//                   }
//                 }
//               }
//             ]
//           }
//         ]
//       }
//     }
//   ]
// }

// export default {
//   "type": "page",
//   "id": "page",
//   "data": {
//     'a2': 11,
//     "a1": 1
//   },
//   "body": [{
//     "type": "form",
//     "id": "outterForm",
//     "body": {
//       "type": "input-text",
//       "name": "a1",
//       "label": "a1"
//     }
//   },{
//     "type": "crud",
//     "api": "https://aisuda.bce.baidu.com/amis/api/mock2/sample",
//     "syncLocation": false,
//     "columns": [
//       {
//         "name": "id",
//         "label": "ID"
//       },
//       {
//         "name": "engine",
//         "label": "Rendering engine"
//       },
//       {
//         "name": "browser",
//         "label": "Browser"
//       },
//       {
//         "name": "platform",
//         "label": "Platform(s)"
//       },
//       {
//         "name": "version",
//         "label": "Engine version"
//       },
//       {
//         "name": "grade",
//         "label": "CSS grade"
//       }
//     ]
//   },{
//     "type": "button",
//     "label": "赋值",
//     "onEvent": {
//       "click": {
//         "actions": [
//           {
//             "actionType": "setValue",
//             "componentId": "page",
//             "args": {
//               "value": {
//                 "a2": 22,
//                 "a1": 2
//               }
//             }
//           }
//         ]
//       }
//     }
//   }]
// }

// export default {
//   "type": "page",
//   "id": "page",
//   "data": {
//     'a2': 11,
//     "a1": 1
//   },
//   "body": [{
//     "type": "service",
//     "data": {
//       "service1": 't1'
//     },
//     "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/page/initData",
//     "body": {
//       "type": "service",
//       "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/page/initData",
//       "data": {
//         "service2": 't2'
//       },
//       "body": {
//         "type": "service",
//         "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/page/initData",
//         "data": {
//           "service3": 't3'
//         },
//         "body": {
//           "type": "tpl",
//           "tpl": "${a1}"
//         }
//       }
//     }
//   },{
//     "type": "button",
//     "label": "赋值",
//     "onEvent": {
//       "click": {
//         "actions": [
//           {
//             "actionType": "setValue",
//             "componentId": "page",
//             "args": {
//               "value": {
//                 "a2": 22,
//                 "a1": 2
//               }
//             }
//           }
//         ]
//       }
//     }
//   }]
// }

export default {
  "type": "page",
  "body": [
    {
      "type": "alert",
      "body": "当有动态显示表单项显示时，<br/> 带value默认值时，form中静态展示table依赖的数据更新，table不刷新（BUG） <br/> 不带value默认值时，表现正常"
    },
    {
      "type": "form",
      "debug": true,
      "body": [
        {
          "type": "select",
          "label": "table数据源选择",
          "name": "tableSource",
          "joinValues": false,
          "extractValue": false,
          "multiple": true,
          "options": [
            {
              "label": "列1",
              "value": "c1"
            },
            {
              "label": "列2",
              "value": "c2"
            },
            {
              "label": "列3",
              "value": "c3"
            },
            {
              "label": "列4",
              "value": "c4"
            }
          ]
        },
        {
          "type": "table",
          "source": "${tableSource}",
          "columns": [
            {
              "name": "value",
              "label": "key"
            },
            {
              "name": "label",
              "label": "value"
            }
          ]
        },
        {
          "type": "radios",
          "name": "caseType",
          "inline": false,
          "label": "显示隐藏表单",
          "options": [
            {
              "value": "case1",
              "label": "有默认值的表单项-table不更新(BUG)"
            },
            {
              "value": "case2",
              "label": "没有默认值的表单项-table表现正常"
            }
          ]
        },
        {
          "type": "input-text",
          "name": "inputCase1",
          "label": "存在默认值",
          "visibleOn": "${caseType ===\"case1\"}",
          "value": "一段文本"
        },
        {
          "type": "input-text",
          "name": "inputCase1",
          "label": "不存在默认值",
          "visibleOn": "${caseType ===\"case2\"}"
        }
      ]
    }
  ]
}
