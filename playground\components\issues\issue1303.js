export default {
  "type": "page",
  "body": [
    {
      "type": "form",
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
      "mode": "horizontal",
      "labelWidth": 60,
      "debug": true,
      "body": [
        {
          "type": "combo",
          "name": "combo101",
          "id": "activeComboId",
          // "mountOnEnter": false,
          "label": "Tabs模式",
          "multiple": true,
          "multiLine": true,
          "strictMode": false,
          "copyable": true,
          "reverseMode": true,
          "draggable": true,
          "value": [
            {
              "defaultRule": true
            }
          ],
          "tabsMode": true,
          "subFormMode": "horizontal",
          "subFormHorizontal": {
            "labelWidth": 40
          },
          "tabsLabelTpl": "${defaultRule ? '默认规则' : '规则' + (index+1)}",
          "itemRemovableOn": "${!defaultRule}",
          "itemCopyableOn": "${!defaultRule}",
          "disableDropBottom": 1,
          "items": [
            {
              "name": "a",
              "label": "文本",
              "type": "input-text",
              "placeholder": "文本",
              "required": true
            },
            {
              "name": "b",
              "label": "选项",
              "type": "select",
              "required": true,
              "options": [
                "a",
                "b",
                "c"
              ],
              "size": "full"
            }
          ]
        }
      ]
    }
  ]
}
