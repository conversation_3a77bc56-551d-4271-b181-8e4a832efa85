export default {
  "type": "page",
  "body": {
    "type": "form",
    "data": {
      "file": [{
        "a": 11,
        "b": 22,
        "fileName": "1.txt"
      }]
    },
    "debug": true,
    "mode": "horizontal",
    "body": [
      {
        "type": "input-file",
        "name": "file",
        "label": "素材上传",
        "multiple": true,
        "nameField": 'fileName',
        "joinValues": false,
        // "valueField": 'fileData',
        "extractValue": true,
        "receiver": {
          "url": '/',
          "dataProvider": true,
          "adaptor": () => {
            return {
              status: 0,
              data: {
                "value": {
                  a: 1,
                  b: 2,
                  fileName: "2.txt",
                }
              },
            };
          },
        }
      }
    ]
  }
}
