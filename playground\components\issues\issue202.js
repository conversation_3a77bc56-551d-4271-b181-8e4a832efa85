const demo = {
  type: 'page',
  asideResizor: true,
  asideMinWidth: 32,
  asideMaxWidth: 500,
  aside: [
    {
      type: 'container',
      body: [
        {
          label: '多选',
          type: 'select',
          name: 'select2',
          className: 'overflow-y-auto pb-8',
          popOverContainerSelector: 'body',
          "options": [
          {
            "label": "A",
            "value": "a"
          },
          {
            "label": "B",
            "value": "b"
          },
          {
            "label": "C",
            "value": "c"
          }
        ]
        },
      ],
    },
    {
      type: 'input-tree',
      treeContainerClassName: 'pm-input-tree-container h-full',
      name: 'tree2',
      multiple: false,
      searchable: true,
      autoCheckChildren: false,
      options: [
        {
          label: 'A',
          value: 'a',
        },
        {
          label: 'B',
          value: 'b',
          children: [
            {
              label: 'B-1',
              value: 'b-1',
            },
            {
              label: 'B-2',
              value: 'b-2',
            },
            {
              label: 'B-3',
              value: 'b-3',
            },
          ],
        },
        {
          label: 'C',
          value: 'c',
        },
      ],
      onEvent: {
        change: {
          actions: [
            {
              actionType: 'query',
              componentId: 'right-crud',
              args: {
                queryParams: {
                  tree2: '${event.data.value}',
                },
              },
            },
          ],
        },
      },
    },
  ],
  body: [
    {
      type: 'crud',
      className: 'undefined max-h-screen overflow-y-auto flex-1 pm-crud',
      syncLocation: false,
      columnsTogglable: false,
      footerToolbar: [
        {
          type: 'tpl',
          tpl: '共${count}条',
          className: 'pr-2',
        },
        {
          type: 'pagination',
          layout: 'pager,perPage,go',
        },
      ],
      // "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4",
      id: 'right-crud',
      autoGenerateFilter: {
        showBtnToolbar: false,
        defaultExpanded: false,
      },
      unsetQueryParams: ['tree2'],
      columns: [
        {
          name: 'id',
          label: 'ID',
          searchable: {
            type: 'input-text',
            name: 'id',
            label: '主键',
            placeholder: '输入id',
          },
        },
        {
          name: 'id',
          label: '序号',
        },
        {
          name: 'engine',
          label: 'Rendering engine',
          searchable: {
            type: 'input-text',
            name: 'engine',
            label: 'Rendering engine',
            placeholder: '输入Rendering engine',
          },
        },
        {
          name: 'browser',
          label: 'Browser',
          searchable: {
            type: 'input-text',
            name: 'browser',
            label: 'Browser',
            placeholder: '输入Browser',
          },
        },
        {
          name: 'platform',
          label: 'Platform(s)',
          searchable: {
            type: 'input-text',
            name: 'platform',
            label: 'Platform(s)',
            placeholder: '输入Platform(s)',
          },
        },
        {
          name: 'version',
          label: 'Engine version',
          searchable: {
            type: 'input-text',
            name: 'version',
            label: 'Engine version',
            placeholder: '输入Engine version',
          },
        },
      ],
    },
  ],
  className: 'bg-light ',
  asideClassName: 'w-xl page-aside-region pm-bg-white min-w-xl mr-4 ',
  bodyClassName: 'p-0 max-h-screen overflow-y-auto ',
};

export default demo;
