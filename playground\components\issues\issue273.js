
const demo = {
  "type": "page",
  "body": [
    {
      "type": "button",
      "label": "激活Tab",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "changeActiveKey",
              "componentId": "comboid",
              "args": {
                "activeKey": 2
              }
            }
          ]
        }
      }
    },
    {
      "title": "",
      "type": "form",
      "mode": "horizontal",
      "autoFocus": false,
      "body": [
        {
          "type": "combo",
          "name": "combo101",
          "id": "comboid",
          "label": "组合多条多行",
          "multiple": true,
          "multiLine": true,
          "value": [
            {},
            {},
            {}
          ],
          "tabsMode": true,
          "tabsStyle": "card",
          // "activeKey": 2, // 必须是表达式
          "defaultKey": 2,
          "items": [
            {
              "name": "a",
              "label": "文本",
              "type": "input-text",
              "placeholder": "文本",
              "value": "",
              "size": "full"
            },
            {
              "name": "b",
              "label": "选项",
              "type": "select",
              "options": [
                "a",
                "b",
                "c"
              ],
              "size": "full"
            },
            {
              "name": "b",
              "label": "选项",
              "type": "select",
              "options": [
                "a",
                "b",
                "c"
              ],
              "size": "full"
            }
          ]
        }
      ],
      "submitText": null,
      "actions": []
    }
  ]
}

// 动作和默认激活项的索引差异
const actionAndDefaultIndexDiff = {
  "type": "page",
  "body": [
    {
      "type": "button",
      "label": "激活Tab",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "changeActiveKey",
              "componentId": "comboid",
              "args": {
                "activeKey": 3
              }
            }
          ]
        }
      }
    },
    {
      "type": "tabs",
      "activeKey": "${2|toInt}",
      "id": "comboid",
      "tabs": [
        {
          "title": "Tab 1",
          "tab": "Content 1"
        },
        {
          "title": "Tab 2",
          "tab": "Content 2"
        },
        {
          "title": "Tab 2",
          "tab": "Content 2"
        },
        {
          "title": "Tab 2",
          "tab": "Content 2"
        }
      ]
    }
  ]
};

// activeKey取变量
const activeKeyVariable = {
  "type": "page",
  "data": {
    "defaultKey": 1,
    // "key": 2,
  },
  "body": {
    "type": "form",
    "api": "/aa",
    "body": [
      {
        "type": "radios",
        "name": "key",
        "mode": "inline",
        "label": "激活的选项卡",
        "options": [
          {
            "label": "Tab 1",
            "value": 0
          },
          {
            "label": "Tab 2",
            "value": 1
          },
          {
            "label": "Tab 3",
            "value": 2
          }
        ]
      },
      {
        "type": "combo",
        "name": "combo101",
        "id": "comboid",
        "label": "组合多条多行",
        "multiple": true,
        "multiLine": true,
        "strictMode": false,
        "value": [
          {},
          {},
          {},
          {}
        ],
        "tabsMode": true,
        "tabsStyle": "card",
        "activeKey": "${key|toInt}",
        "defaultKey": "${defaultKey|toInt}",
        "items": [
          {
            "name": "a",
            "label": "文本",
            "type": "input-text",
            "placeholder": "文本",
            "required": true
          },
          {
            "name": "b",
            "label": "选项",
            "type": "select",
            "options": [
              "a",
              "b",
              "c"
            ],
            "size": "full"
          }
        ]
      }
    ]
  }
}

// 通过变量变化不会触发change事件
const bug1 = {
  "type": "page",
  "data": {
    "defaultKey": 1,
    "activeKey": 2
  },
  "body": [
    {
      "type": "radios",
      "name": "key",
      "mode": "inline",
      "label": "激活的选项卡",
      "options": [
        {
          "label": "Tab 1",
          "value": 0
        },
        {
          "label": "Tab 2",
          "value": 1
        },
        {
          "label": "Tab 3",
          "value": 2
        }
      ]
    },
    {
      "type": "tabs",
      "activeKey": "${key|toInt}",
      "defaultKey": "${key|toInt}",
      "tabs": [
        {
          "title": "Tab 1",
          "tab": "Content 1"
        },
        {
          "title": "Tab 2",
          "tab": "Content 2"
        },
        {
          "title": "Tab 3",
          "tab": "Content 3"
        }
      ],
      "onEvent": {
        "change": {
          "actions": [
            {
              "actionType": "toast",
              "args": {
                "msg": "${event.data|json}"
              }
            }
          ]
        }
      }
    }
  ]
}

export default demo;
