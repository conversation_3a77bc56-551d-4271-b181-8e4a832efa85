export default {
  "type": "page",
  "body": [
    {
      "type": "form",
      "data": {
        "page": "1"
      },
      "debug": true,
      "body": [
        {
          "type": "select",
          "name": "page",
          "label": "选项",
          "options": ["1", "2", "3"],
        },
        {
          "type": "service",
          // string模式 标准场景
          // "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?page=1&perPage=10",
          // string模式 联动依赖
          // "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?page=${page}&perPage=10",
          // dataprovider场景
          "dataProvider": {
            "onApiFetched": "setData({ total: data.count})"
          },
          // 轮询场景
          // "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/number/random?waitSeconds=1",
          // "interval": 2000,
          // "stopAutoRefreshWhen": "this.random === 6",
          // 监听fetchInited场景
          // onEvent: {
          //   fetchInited: {
          //     actions: [
          //       {
          //         actionType: "toast",
          //         args: {
          //           msg: "${event.data | json}"
          //         }
          //       }
          //     ]
          //   }
          // },
          // object模式 标准场景
          // "api": {
          //   "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?page=1&perPage=10",
          //   "method": "get",
          //   "adaptor": (payload) => {
          //     console.log(payload);
          //     return payload;
          //   }
          // },
          // object模式 联动场景
          // "api": {
          //   "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?page=${page}&perPage=10",
          //   "method": "get",
          //   "adaptor": (payload) => {
          //     console.log(payload);
          //     return payload;
          //   }
          // },
          // object模式 轮询场景
          // "api": {
          //   "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/number/random?waitSeconds=1",
          //   "method": "get",
          //   "adaptor": (payload) => {
          //     console.log(payload);
          //     return payload;
          //   }
          // },
          // "interval": 2000,
          // "stopAutoRefreshWhen": "this.random === 6",
          // string[] 标准场景
          // "api": ["https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?page=1&perPage=10", "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/page/initData"],
          // string[] 联动场景
          // "api": ["https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?page=${page}&perPage=10", "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/page/initData"],
          // string[] 轮询场景
          // "api": ["https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/number/random?waitSeconds=1", "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/page/initData"],
          // "interval": 2000,
          // "stopAutoRefreshWhen": "this.random === 6",
          // object[] 标准场景
          "api": [
            {
              "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?page=${page}&perPage=10",
              "method": "get",
              "adaptor": (payload) => {
                console.log(payload);
                return payload;
              }
            },
            {
              "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/number/random?waitSeconds=2",
              "method": "get",
              "adaptor": (payload) => {
                console.log(payload);
                return payload;
              }
            }
          ],
          // "interval": 2000,
          // "stopAutoRefreshWhen": "this.random === 6",
          // string[] 联动场景
          // "api": ["https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?page=${page}&perPage=10", "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/page/initData"],
          // string[] 轮询场景
          // "api": ["https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/number/random?waitSeconds=1", "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/page/initData"],
          // "interval": 2000,
          // "stopAutoRefreshWhen": "this.random === 6",
          "body": [
            {
              "type": "input-text",
              "name": "date",
              "label": "时间",
            },
            {
              "type": "panel",
              "title": "随机数字",
              "body": "现在是：${random}"
            },
            {
              "type": "tpl",
              "tpl": "总条数：${total}",
            },
            {
              "type": "select",
              "name": "select",
              "label": "选项",
              "source": "$rows",
              "labelField": "engine",
              "valueField": "id"
            }
          ]
        }
      ]
    }
  ]
}

