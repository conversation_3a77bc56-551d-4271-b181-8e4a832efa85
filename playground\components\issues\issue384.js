const tableData = [];

for (let i = 0; i < 500; i ++) {
  tableData.push({
    a: `a${i + 1}`,
    b: `b${i + 1}`,
    c: `c${i + 1}`,
    d: `d${i + 1}`,
    e: `e${i + 1}`,
    f: `f${i + 1}`,
    g: `g${i + 1}`,
    h: `h${i + 1}`,
    i: `i${i + 1}`,
    j: `j${i + 1}`,
    id: `${i + 1}`
  })
}

const demo = {
  "type": "page",
  "body": {
    "type": "form",
    "renderDialog": false,
    "renderTypography": false,
    "data": {
      "table": tableData,
    },
    "body": [
      {
        "type": "input-table",
        "name": "table",
        "label": "Input Table",
        "perPage": 50,
        "showIndex": false,
        strictMode: false,
        // updateAllRows: true,
        // "reUseRow": "match", // amis 写死了match，2个柱子
        columnsTogglable: false,
        "needConfirm": false,
        "columns": [
          {
            "name": "a",
            "label": "A",
            // "type": "input-text",
          },
          {
            "name": "b",
            "label": "B",
            // "type": "input-text",
          },
          {
            "name": "c",
            "label": "C",
            // "type": "input-text",
          },
          {
            "name": "d",
            "label": "D",
            // "type": "input-text",
          },
          {
            "name": "e",
            "label": "E",
            // "type": "input-text",
          },
          {
            "name": "f",
            "label": "F",
            // "type": "input-text",
          },
          {
            "name": "g",
            "label": "G",
            // "type": "input-text",
          },
          {
            "name": "h",
            "label": "H",
            // "type": "input-text",
          },
          {
            "name": "i",
            "label": "I",
            // "type": "input-text",
          },
          {
            "name": "j",
            "label": "J",
            // "type": "input-text",
          },
        ]
      }
    ]
  }
}

export default demo;
