export default {
  "type": "page",
  "body": [
    {
      "type": "form",
      "title": "编辑表单",
      "mode": "horizontal",
      "labelWidth": 200,
      "debug": true,
      data: {
        "prefix": "1",
        "suffix": "1",
        "text": 1,
        "addOnleft": "10",
        "addOnright": "10",
        "search": "10",
        "numberprefix": 111111,
        "numbersuffix": 111111,
        "number": 111111,
        "unitOption": '222222px',
        "unitOptions": '222222em',
        "memory": "10",
        "memoryText": "5",
        "memoryUnits": "day",
        "groupfix": "1231231232",
        "formType": "edit",
      },
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
      "body": [
        {
          "name": "prefix",
          "type": "input-text",
          "label": "inputText prefix",
          "prefix": "近",
        },
        {
          "name": "suffix",
          "type": "input-text",
          "label": "inputText suffix",
          "suffix": "天",
        },
        {
          "name": "text",
          "type": "input-text",
          "label": "inputText prefix&suffix",
          "prefix": "近",
          "suffix": "天",
        },
        {
          "name": "addOnleft",
          "type": "input-text",
          "label": "inputText addOn left",
          "addOn": {
            "position": "left",
            "type": "button",
            "label": "¥"
          }
        },
        {
          "name": "addOnright",
          "type": "input-text",
          "label": "inputText addOn right",
          "addOn": {
            // "position": "left",
            "type": "button",
            "label": "元"
          }
        },
        {
          "name": "search",
          "type": "input-text",
          "label": "inputText addOn&icon",
          "addOn": {
            "type": "button",
            "icon": "https://suda.cdn.bcebos.com/images%2F2021-01%2Fsearch.svg",
            "label": "搜索"
          }
        },
        {
          "type": "input-number",
          "name": "numberprefix",
          "label": "inputNumber prefix",
          "prefix": "$",
          "kilobitSeparator": true
        },
        {
          "type": "input-number",
          "name": "numbersuffix",
          "label": "inputNumber suffix",
          // "prefix": "$",
          "suffix": "%",
          "kilobitSeparator": true
        },
        {
          "type": "input-number",
          "name": "number",
          "label": "inputNumber prefix&suffix",
          "prefix": "$",
          "suffix": "%",
          "kilobitSeparator": true
        },
        {
          "type": "input-number",
          "name": "unitOption",
          "label": "inputNumber unitOption",
          "unitOptions": [
            "px",
          ]
        },
        {
          "type": "input-number",
          "name": "unitOptions",
          "label": "inputNumber unitOptions",
          "unitOptions": [
            "px",
            "%",
            "em"
          ]
        },
        {
          "type": "input-group",
          "label": "inputGroup",
          "body": [
            {
              "type": "button",
              "label": "近"
            },
            {
              "type": "input-text",
              "name": "memory"
            },
            {
              "type": "button",
              "label": "元"
            }
          ]
        },
        {
          "type": "input-group",
          "label": "inputGroup",
          "body": [
            {
              "type": "button",
              "label": "近"
            },
            {
              "type": "input-text",
              "name": "memoryText"
            },
            {
              "type": "select",
              "name": "memoryUnits",
              "value": "day",
              "options": [
                {
                  "label": "天",
                  "value": "day"
                },
                {
                  "label": "月",
                  "value": "month"
                },
                {
                  "label": "年",
                  "value": "year"
                }
              ]
            },
          ]
        },
        {
          "type": "input-group",
          "label": "inputGroup prefix & suffix",
          "prefix": "近",
          "suffix": "元",
          "body": [
            {
              "type": "input-text",
              "name": "groupfix"
            }
          ]
        },
      ]
    },
    {
      "type": "form",
      "title": "禁用表单",
      "mode": "horizontal",
      "labelWidth": 200,
      "disabled": true,
      data: {
        "prefix": "1",
        "suffix": "1",
        "text": 1,
        "addOnleft": "10",
        "addOnright": "10",
        "search": "10",
        "numberprefix": 111111,
        "numbersuffix": 111111,
        "number": 111111,
        "unitOption": '222222px',
        "unitOptions": '222222em',
        "memory": "10",
        "memoryText": "5",
        "memoryUnits": "day",
        "groupfix": "1231231232",
        "formType": "disabled",
      },
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
      "body": [
        {
          "name": "prefix",
          "type": "input-text",
          "label": "inputText prefix",
          "prefix": "近",
        },
        {
          "name": "suffix",
          "type": "input-text",
          "label": "inputText suffix",
          "suffix": "天",
        },
        {
          "name": "text",
          "type": "input-text",
          "label": "inputText prefix&suffix",
          "prefix": "近",
          "suffix": "天",
        },
        {
          "name": "addOnleft",
          "type": "input-text",
          "label": "inputText addOn left",
          "addOn": {
            "position": "left",
            "type": "button",
            "label": "¥"
          }
        },
        {
          "name": "addOnright",
          "type": "input-text",
          "label": "inputText addOn right",
          "addOn": {
            // "position": "left",
            "type": "button",
            "label": "元"
          }
        },
        {
          "name": "search",
          "type": "input-text",
          "label": "inputText addOn&icon",
          "addOn": {
            "type": "button",
            "icon": "https://suda.cdn.bcebos.com/images%2F2021-01%2Fsearch.svg",
            "label": "搜索"
          }
        },
        {
          "type": "input-number",
          "name": "numberprefix",
          "label": "inputNumber prefix",
          "prefix": "$",
          "kilobitSeparator": true
        },
        {
          "type": "input-number",
          "name": "numbersuffix",
          "label": "inputNumber suffix",
          // "prefix": "$",
          "suffix": "%",
          "kilobitSeparator": true
        },
        {
          "type": "input-number",
          "name": "number",
          "label": "inputNumber prefix&suffix",
          "prefix": "$",
          "suffix": "%",
          "kilobitSeparator": true
        },
        {
          "type": "input-number",
          "name": "unitOption",
          "label": "inputNumber unitOption",
          "unitOptions": [
            "px",
          ]
        },
        {
          "type": "input-number",
          "name": "unitOptions",
          "label": "inputNumber unitOptions",
          "unitOptions": [
            "px",
            "%",
            "em"
          ]
        },
        {
          "type": "input-group",
          "label": "inputGroup",
          "body": [
            {
              "type": "button",
              "label": "近"
            },
            {
              "type": "input-text",
              "name": "memory"
            },
            {
              "type": "button",
              "label": "元"
            }
          ]
        },
        {
          "type": "input-group",
          "label": "inputGroup",
          "body": [
            {
              "type": "button",
              "label": "近"
            },
            {
              "type": "input-text",
              "name": "memoryText"
            },
            {
              "type": "select",
              "name": "memoryUnits",
              "value": "day",
              "options": [
                {
                  "label": "天",
                  "value": "day"
                },
                {
                  "label": "月",
                  "value": "month"
                },
                {
                  "label": "年",
                  "value": "year"
                }
              ]
            },
          ]
        },
        {
          "type": "input-group",
          "label": "inputGroup prefix & suffix",
          "prefix": "近",
          "suffix": "元",
          "body": [
            {
              "type": "input-text",
              "name": "groupfix"
            }
          ]
        },
      ]
    },
    {
      "type": "form",
      "title": "禁用表单",
      "mode": "horizontal",
      "labelWidth": 200,
      "static": true,
      data: {
        "prefix": "1",
        "suffix": "1",
        "text": 1,
        "addOnleft": "10",
        "addOnright": "10",
        "search": "10",
        "numberprefix": 111111,
        "numbersuffix": 111111,
        "number": 111111,
        "unitOption": '222222px',
        "unitOptions": '222222em',
        "memory": "10",
        "memoryText": "5",
        "memoryUnits": "day",
        "groupfix": "1231231232",
        "formType": "static",
      },
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
      "body": [
        {
          "name": "prefix",
          "type": "input-text",
          "label": "inputText prefix",
          "prefix": "近",
          "showStaticAddOn": true,
        },
        {
          "name": "suffix",
          "type": "input-text",
          "label": "inputText suffix",
          "suffix": "天",
          "showStaticAddOn": true,
        },
        {
          "name": "text",
          "type": "input-text",
          "label": "inputText prefix&suffix",
          "prefix": "近",
          "suffix": "天",
          "showStaticAddOn": true,
        },
        {
          "name": "addOnleft",
          "type": "input-text",
          "label": "inputText addOn left",
          "showStaticAddOn": true,
          "addOn": {
            "position": "left",
            "type": "button",
            "label": "¥"
          }
        },
        {
          "name": "addOnright",
          "type": "input-text",
          "label": "inputText addOn right",
          "showStaticAddOn": true,
          "addOn": {
            // "position": "left",
            "type": "button",
            "label": "元"
          }
        },
        {
          "name": "search",
          "type": "input-text",
          "label": "inputText addOn&icon",
          "addOn": {
            "type": "button",
            "icon": "https://suda.cdn.bcebos.com/images%2F2021-01%2Fsearch.svg",
            "label": "搜索"
          }
        },
        {
          "type": "input-number",
          "name": "numberprefix",
          "label": "inputNumber prefix",
          "prefix": "$",
          "kilobitSeparator": true
        },
        {
          "type": "input-number",
          "name": "numbersuffix",
          "label": "inputNumber suffix",
          // "prefix": "$",
          "suffix": "%",
          "kilobitSeparator": true
        },
        {
          "type": "input-number",
          "name": "number",
          "label": "inputNumber prefix&suffix",
          "prefix": "$",
          "suffix": "%",
          "kilobitSeparator": true
        },
        {
          "type": "input-number",
          "name": "unitOption",
          "label": "inputNumber unitOption",
          "unitOptions": [
            "px",
          ]
        },
        {
          "type": "input-number",
          "name": "unitOptions",
          "label": "inputNumber unitOptions",
          "unitOptions": [
            "px",
            "%",
            "em"
          ]
        },
        {
          "type": "input-group",
          "label": "inputGroup",
          "body": [
            {
              "type": "button",
              "label": "近"
            },
            {
              "type": "input-text",
              "name": "memory"
            },
            {
              "type": "button",
              "label": "元"
            }
          ]
        },
        {
          "type": "input-group",
          "label": "inputGroup",
          "body": [
            {
              "type": "button",
              "label": "近"
            },
            {
              "type": "input-text",
              "name": "memoryText"
            },
            {
              "type": "select",
              "name": "memoryUnits",
              "value": "day",
              "options": [
                {
                  "label": "天",
                  "value": "day"
                },
                {
                  "label": "月",
                  "value": "month"
                },
                {
                  "label": "年",
                  "value": "year"
                }
              ]
            },
          ]
        },
        {
          "type": "input-group",
          "label": "inputGroup prefix & suffix",
          "prefix": "近",
          "suffix": "元",
          "body": [
            {
              "type": "input-text",
              "name": "groupfix"
            }
          ]
        },
      ]
    }
  ]
}
