export default {
  "type": "page",
  "body": {
    "type": "crud",
    "syncLocation": false,
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
    // "loadDataOnce": true,
    "pickerMode": true,
    "columns": [
      {
        "name": "id",
        "label": "ID"
      },
      {
        "name": "engine",
        "label": "Rendering engine"
      },
      {
        "name": "browser",
        "label": "Browser"
      },
      {
        "name": "platform",
        "label": "Platform(s)"
      },
      {
        "name": "version",
        "label": "Engine version"
      },
      {
        "name": "grade",
        "label": "CSS grade",
        "sortable": true
      }
    ],
    "onEvent": {
      "selectedChange": {
        "actions": [
          {
            "actionType": "toast",
            "args": {
              "msg": "${event.data.selectedItems|json}"
            }
          }
        ]
      }
    }
  }
}
