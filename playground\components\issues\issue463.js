
const demo = {
  "type": "page",
  "id": "pageId",
  "data": {
    "selectVis": true,
    "crudVis": true,
  },
  "body": [
    {
      "type": "button",
      "label": "select切换状态",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "setValue",
              "componentId": "pageId",
              "args": {
                "value": {
                  "selectVis": "${!selectVis}"
                }
              }
            }
          ]
        }
      }
    },
    {
      "type": "form",
      // "initApi": "/api/mock2/form/getOptions?waitSeconds=5",
      "visibleOn": "${selectVis}",
      "body": [
        {
          "label": "选项",
          "type": "select",
          "name": "select",
          "source": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/mock2/form/getOptions?waitSeconds=3"
        }
      ]
    },
    {
      "type": "divider",
    },
    {
      "type": "button",
      "label": "crud切换状态",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "setValue",
              "componentId": "pageId",
              "args": {
                "value": {
                  "crudVis": "${!crudVis}"
                }
              }
            }
          ]
        }
      }
    },
    {
      "type": "crud",
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?waitSeconds=3",
      "syncLocation": false,
      "visibleOn": "${crudVis}",
      "columns": [
        {
          "name": "id",
          "label": "ID"
        },
        {
          "name": "engine",
          "label": "Rendering engine"
        },
        {
          "name": "browser",
          "label": "Browser"
        },
      ]
    },
  ]
}

export default demo;
