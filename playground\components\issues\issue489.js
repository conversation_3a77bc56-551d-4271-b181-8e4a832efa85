const demo = {
  "type": "page",
  "body": {
    "type": "service",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=5",
    "body": [
      {
        "type": "table",
        "title": "表格1",
        "source": "$rows",
        "draggable": true,
        "selectable": true,
        "multiple": true,
        // "tableLayout": "fixed",
        "columns": [
          {
            "name": "engine1",
            "label": "Engine",
            "fixed": "left"
          },
          {
            "name": "version",
            "width": 100,
            "label": "表格1表格1表格1表格1表格1表格1表格1表格1表格1"
          },
          {
            "name": "version",
            "label": "Version"
          },
          {
            "name": "version",
            "label": "Version",
            "width": 1000
          }
        ]
      }
    ]
  }
}

const itemActionsDemo = {
  "type": "page",
  "style": {
    "height": "2000px"
  },
  "body": {
    "type": "crud",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
    "syncLocation": false,
    "affixHeader": true,
    // "noPadding": true,
    "topToolbar": [
      {
        "type": "action",
        "label": "topToolbar"
      }
    ],
    "filter": {
      "title": false,
      "body": [
        {
          "type": "input-text",
          "label": "filter"
        }
      ]
    },
    "headerFilter": {
      "body": [
        {
          "type": "input-text",
          "label": "headerFilter"
        }
      ]
    },
    "headerToolbar": [
      {
        "type": "action",
        "label": "headerToolbar"
      }
    ],
    "itemActions": [
        {
          "label": "编辑",
          "type": "button",
          "actionType": "dialog",
          "dialog": {
            "title": "编辑",
            "body": "这是个简单的编辑弹框"
          }
        },
        {
          "label": "删除",
          "type": "button",
          "actionType": "ajax",
          "confirmText": "确认要删除？",
          "api": "/api/mock2/form/saveForm"
        }
      ],
    "columns": [
      {
        "name": "id",
        "label": "ID"
      },
      {
        "name": "engine",
        "label": "Rendering engine"
      },
      {
        "name": "browser",
        "label": "Browser"
      },
      {
        "name": "platform",
        "label": "Platform(s)"
      },
      {
        "name": "version",
        "label": "Engine version"
      },
      {
        "name": "grade",
        "label": "CSS grade"
      }
    ]
  }
}

export default demo;
