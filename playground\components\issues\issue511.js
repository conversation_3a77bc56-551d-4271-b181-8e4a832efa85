export default {
  "type": "page",
  "body": {
    "type": "form",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "body": [
      {
        "type": "input-tree",
        "name": "tree",
        "label": "Tree",
        "searchable": true,
        "autoFillHeight": true,
        "treeContainerClassName": "h-screen max-h-none",
        "virtualThreshold": 9,
        "source": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/tree/search"
      }
    ]
  }
}

