// crud通过pickerMode和【name/value】实现反显
const crudName = {
  "type": "page",
  "data": {
    "crudName": [
      {
        "id": "1"
      }
    ]
  },
  "body": {
    "type": "crud",
    "name": "crudName",
    "syncLocation": false,
    "pickerMode": true,
    "multiple": true,
    "api": {
      "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4",
      // adaptor: function (payload, response, api) {
      //   console.log(payload, response, api)
      //   return {
      //     ...payload,
      //     status: payload.code === 200 ? 0 : payload.code
      //   };
      // }
    },
    "canAccessSuperData": false,
    "updateAllRows": true,
    "filter": {
      "debug": true,
      "title": "",
      "body": [
        {
          "type": "input-text",
          "name": "keywords",
          "label": "关键字",
        },
      ],
    },
    "columns": [
      {
        "name": "id",
        "label": "ID"
      },
      {
        "name": "engine",
        "label": "Rendering engine",
        "headSearchable": true
      },
      {
        "name": "browser",
        "label": "Browser",
        "headSearchable": {
          "type": "input-text",
          "name": "browser",
          "label": "Browser"
        }
      },
    ]
  }
}

// picker将pickerMode透传给crud，crud的用法只有内部传pickerMode这种
const pickerDemo = {
  "type": "page",
  "body": {
    "type": "form",
    "data": {
      "picker": [
        "a"
      ]
    },
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "body": [
      {
        "type": "picker",
        "name": "picker",
        "label": "picker",
        "options": [
          {
            "label": "A",
            "value": "a"
          },
          {
            "label": "B",
            "value": "b"
          },
          {
            "label": "C",
            "value": "c"
          }
        ]
      }
    ]
  }
};

// table通过selected反显选中项
const tableSelected = {
  "type": "page",
  "body": {
    "type": "service",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=5",
    "body": [
      {
        "type": "table",
        "title": "表格1",
        "source": "$rows",
        "selectable": true,
        "multiple": true,
        "selected": [
          {
            "id": 1
          },
          {
            "id": 2
          }
        ],
        "valueField": "id",
        "columns": [
          {
            "name": "id",
            "label": "Engine"
          },
          {
            "name": "version",
            "label": "Version"
          }
        ],
        "onEvent": {
          "selectedChange": {
            "actions": [
              {
                "actionType": "custom",
                "script": "event.setData({selectRows: event.data.selectedItems })"
              }
            ]
          }
        }
      }
    ]
  }
}


const crudClearSelectItems = {
  "type": "page",
  "body": {
    "type": "crud",
    "api": "/api/mock2/sample",
    "syncLocation": false,
    "filterFormAdvanceSearchAble": true,
    "headerToolbar": [
      "bulkActions"
    ],
    "bulkActions": [
      {
        "label": "批量删除",
        "actionType": "ajax",
        "api": "delete:/api/mock2/sample/${ids|raw}",
        "confirmText": "确定要批量删除?"
      },
      {
        "label": "批量修改",
        "actionType": "dialog",
        "dialog": {
          "title": "批量编辑",
          "body": {
            "type": "form",
            "api": "/api/mock2/sample/bulkUpdate2",
            "body": [
              {
                "type": "hidden",
                "name": "ids"
              },
              {
                "type": "input-text",
                "name": "engine",
                "label": "Engine"
              }
            ],
            "onEvent": {
              "submitSucc": {
                "actions": [
                  {
                    "actionType": "clearAll",
                    "componentId": "crudPage"
                  }
                ]
              }
            }
          }
        }
      }
    ],
    "filter": {
      "debug": true,
      "title": "",
      "body": [
        {
          "type": "input-text",
          "name": "keywords",
          "label": "关键字",
          "clearable": true,
          "placeholder": "通过关键字搜索",
          "columnRatio": 4
        },
        {
          "type": "input-text",
          "name": "engine",
          "label": "Engine",
          "clearable": true,
          "columnRatio": 4
        },
        {
          "type": "input-text",
          "name": "platform",
          "label": "Platform",
          "clearable": true,
          "columnRatio": 4
        },
        {
          "type": "input-text",
          "name": "keywords1",
          "label": "关键字1",
          "clearable": true,
          "placeholder": "通过关键字搜索",
          "columnRatio": 4
        },
        {
          "type": "input-text",
          "name": "engine1",
          "label": "Engine1",
          "clearable": true,
          "columnRatio": 4
        },
        {
          "type": "input-text",
          "name": "platform1",
          "label": "Platform1",
          "clearable": true,
          "columnRatio": 4
        },
        {
          "type": "input-text",
          "name": "keywords2",
          "label": "关键字2",
          "clearable": true,
          "placeholder": "通过关键字搜索",
          "columnRatio": 4
        },
        {
          "type": "input-text",
          "name": "engine2",
          "label": "Engine2",
          "clearable": true,
          "columnRatio": 4
        },
        {
          "type": "input-text",
          "name": "platform2",
          "label": "Platform2",
          "clearable": true,
          "columnRatio": 4
        }
      ],
      "actions": [
        {
          "type": "reset",
          "label": "重 置"
        },
        {
          "type": "submit",
          "level": "primary",
          "label": "查 询"
        }
      ]
    },
    "columns": [
      {
        "name": "id",
        "label": "ID"
      },
      {
        "name": "engine",
        "label": "Rendering engine"
      },
      {
        "name": "browser",
        "label": "Browser"
      },
      {
        "name": "platform",
        "label": "Platform(s)"
      },
      {
        "name": "version",
        "label": "Engine version"
      },
      {
        "name": "grade",
        "label": "CSS grade"
      },
      {
        "type": "show-more",
        "label": "操作",
        "width": 80,
        "buttons": [
          {
            "label": "详情",
            "type": "button",
            "level": "link",
            "actionType": "dialog",
            "dialog": {
              "title": "查看详情",
              "body": {
                "type": "form",
                "body": [
                  {
                    "type": "input-text",
                    "name": "engine",
                    "label": "Engine"
                  },
                  {
                    "type": "input-text",
                    "name": "browser",
                    "label": "Browser"
                  },
                  {
                    "type": "input-text",
                    "name": "platform",
                    "label": "platform"
                  },
                  {
                    "type": "input-text",
                    "name": "version",
                    "label": "version"
                  },
                  {
                    "type": "control",
                    "label": "grade",
                    "body": {
                      "type": "tag",
                      "label": "${grade}",
                      "displayMode": "normal",
                      "color": "active"
                    }
                  }
                ]
              }
            }
          },
          {
            "label": "删除",
            "type": "button",
            "level": "link",
            "disabledOn": "this.grade === 'A'"
          }
        ]
      }
    ]
  }
}

// 勾选在翻页后按钮状态异常demo
const bulkActionsErrorDemo = {
  "type": "page",
  "body": {
    "type": "crud",
    "syncLocation": false,
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
    "headerToolbar": [
      "bulkActions"
    ],
    "bulkActions": [
      {
        "label": "批量删除",
        "actionType": "ajax",
        "api": "delete:/api/mock2/sample/${ids|raw}",
        "confirmText": "确定要批量删除?"
      },
      {
        "label": "批量修改",
        "actionType": "dialog",
        "dialog": {
          "title": "批量编辑",
          "body": {
            "type": "form",
            "api": "/api/mock2/sample/bulkUpdate2",
            "body": [
              {
                "type": "hidden",
                "name": "ids"
              },
              {
                "type": "input-text",
                "name": "engine",
                "label": "Engine"
              }
            ]
          }
        }
      }
    ],
    "columns": [
      {
        "name": "id",
        "label": "ID"
      },
      {
        "name": "engine",
        "label": "Rendering engine"
      },
      {
        "name": "browser",
        "label": "Browser"
      },
      {
        "name": "platform",
        "label": "Platform(s)"
      },
      {
        "name": "version",
        "label": "Engine version"
      },
      {
        "name": "grade",
        "label": "CSS grade"
      }
    ]
  }
}

// picker渲染器inline模式下正常回显
const pickerInlineDemo = {
  "type": "page",
  "body": {
    "type": "form",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "body": [
      {
        "type": "picker",
        "name": "type4",
        "joinValues": true,
        "valueField": "id",
        "labelField": "engine",
        "label": "Picker",
        "embed": true,
        "source": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/tree?waitSeconds=1",
        "size": "lg",
        "value": "1,5",
        "multiple": true,
        "pickerSchema": {
          "mode": "table",
          "name": "thelist",
          "quickSaveApi": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample/bulkUpdate",
          "quickSaveItemApi": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample/$id",
          "draggable": true,
          "filter": {
            "body": [
              {
                "type": "input-text",
                "name": "keywords",
                "label": "关键词"
              }
            ]
          },
          "columns": [
            {
              "name": "engine",
              "label": "Rendering engine",
              "sortable": true,
              "searchable": true,
              "type": "text",
              "toggled": true
            },
            {
              "name": "id",
              "label": "id",
              "sortable": true,
              "type": "text",
              "toggled": true
            }
          ]
        }
      }
    ]
  }
}

export default pickerInlineDemo;
