const demo = {
  "type": "page",
  "body": {
    "type": "service",
    "data": {
      "rows": [
        {
          "engine": "Trident",
          "browser": "Internet Explorer 4.2",
          "platform": "Win 95+",
          "version": "4",
          "grade": "A"
        },
        {
          "engine": "Trident",
          "browser": "Internet Explorer 4.2",
          "platform": "Win 95+",
          "version": "4",
          "grade": "B"
        },
        {
          "engine": "Trident",
          "browser": "AOL browser (AOL desktop)",
          "platform": "Win 95+",
          "version": "4",
          "grade": "C"
        },
        {
          "engine": "Trident",
          "browser": "AOL browser (AOL desktop)",
          "platform": "Win 98",
          "version": "3",
          "grade": "A"
        },
        {
          "engine": "Trident",
          "browser": "AOL browser (AOL desktop)",
          "platform": "Win 98",
          "version": "4",
          "grade": "A"
        },
        {
          "engine": "Gecko",
          "browser": "Firefox 1.0",
          "platform": "Win 98+ / OSX.2+",
          "version": "4",
          "grade": "A"
        },
        {
          "engine": "Gecko",
          "browser": "Firefox 1.0",
          "platform": "Win 98+ / OSX.2+",
          "version": "5",
          "grade": "A"
        },
        {
          "engine": "Gecko",
          "browser": "Firefox 2.0",
          "platform": "Win 98+ / OSX.2+",
          "version": "5",
          "grade": "B"
        },
        {
          "engine": "Gecko",
          "browser": "Firefox 2.0",
          "platform": "Win 98+ / OSX.2+",
          "version": "5",
          "grade": "C"
        },
        {
          "engine": "Gecko",
          "browser": "Firefox 2.0",
          "platform": "Win 98+ / OSX.2+",
          "version": "5",
          "grade": "D"
        },
        {
          "engine": "Gecko",
          "browser": "Firefox 2.0",
          "platform": "Win 98+ / OSX.2+",
          "version": "5",
          "grade": "D"
        },
        {
          "engine": "Gecko",
          "browser": "Firefox 2.0",
          "platform": "Win 98+ / OSX.2+",
          "version": "5",
          "grade": "D"
        },
        {
          "engine": "Gecko",
          "browser": "Firefox 2.0",
          "platform": "Win 98+ / OSX.2+",
          "version": "5",
          "grade": "D"
        },
        {
          "engine": "Gecko",
          "browser": "Firefox 2.0",
          "platform": "Win 98+ / OSX.2+",
          "version": "5",
          "grade": "D"
        },
        {
          "engine": "Gecko",
          "browser": "Firefox 2.0",
          "platform": "Win 98+ / OSX.2+",
          "version": "5",
          "grade": "D"
        },
        {
          "engine": "Gecko",
          "browser": "Firefox 2.0",
          "platform": "Win 98+ / OSX.2+",
          "version": "5",
          "grade": "D"
        },
        {
          "engine": "Gecko",
          "browser": "Firefox 2.0",
          "platform": "Win 98+ / OSX.2+",
          "version": "5",
          "grade": "D"
        },
        {
          "engine": "Gecko",
          "browser": "Firefox 2.0",
          "platform": "Win 98+ / OSX.2+",
          "version": "5",
          "grade": "D"
        },
        {
          "engine": "Gecko",
          "browser": "Firefox 2.0",
          "platform": "Win 98+ / OSX.2+",
          "version": "5",
          "grade": "D"
        },
        {
          "engine": "Gecko",
          "browser": "Firefox 2.0",
          "platform": "Win 98+ / OSX.2+",
          "version": "5",
          "grade": "D"
        },
        {
          "engine": "Gecko",
          "browser": "Firefox 2.0",
          "platform": "Win 98+ / OSX.2+",
          "version": "5",
          "grade": "D"
        },
        {
          "engine": "Gecko",
          "browser": "Firefox 2.0",
          "platform": "Win 98+ / OSX.2+",
          "version": "5",
          "grade": "D"
        }
      ]
    },
    "body": [
      {
        "type": "crud",
        "source": "$rows",
        "className": "m-b-none",
        "combineNum": 2,
        "columnsTogglable": false,
        syncLocation: false,
        "perPage": 10,
        "footerToolbar": [
          {
            "type": "pagination",
            "layout": "total,pager,perPage,go",
            "perPageAvailable": [
              5,
              10,
              20,
              50
            ]
          }
        ],
        "columns": [
          {
            "name": "engine",
            "label": "Rendering engine"
          },
          {
            "name": "browser",
            "label": "Browser"
          },
          {
            "name": "platform",
            "label": "Platform(s)"
          },
          {
            "name": "version",
            "label": "Engine version"
          },
          {
            "name": "grade",
            "label": "CSS grade"
          }
        ]
      }
    ]
  }
}

export default demo;
