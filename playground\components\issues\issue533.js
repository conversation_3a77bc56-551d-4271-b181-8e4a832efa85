const demo = {
  "type": "page",
  "body": {
    "type": "crud",
    "updatePristineAfterStoreDataReInit": false,
    "data": {
      "customFilter": "all"
    },
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
    "syncLocation": false,
    "columnsTogglable": false,
    "autoGenerateFilter": {
      "defaultExpanded": true,
      "showBtnToolbar": false
    },
    "headerToolbar": [
      {
        "type": "columns-toggler",
        "align": "right",
        "draggable": true,
        "icon": "fas fa-cog",
        "overlay": true,
        "footerBtnSize": "sm"
      }
    ],
    "headerFilter": {
      // "formLazyChange": false,
      "body": [
        {
          "type": "button-group-select",
          "name": "customFilter",
          "options": [
            {
              "value": "all",
              "label": "查询全部"
            },
            {
              "value": "forme",
              "label": "待我审核"
            },
            {
              "value": "reject",
              "label": "已拒绝"
            }
          ]
        }
      ]
    },
    "columns": [
      {
        "name": "id",
        "label": "ID",
        "searchable": {
          "type": "input-text",
          "name": "id",
          "label": "主键",
          "placeholder": "输入id"
        }
      },
      {
        "name": "engine",
        "label": "Rendering engine",
        "searchable": true
      },
      {
        "name": "browser",
        "label": "Browser",
        "searchable": {
          "type": "select",
          "name": "browser",
          "label": "浏览器",
          "placeholder": "选择浏览器",
          "options": [
            {
              "label": "Internet Explorer ",
              "value": "ie"
            },
            {
              "label": "AOL browser",
              "value": "aol"
            },
            {
              "label": "Firefox",
              "value": "firefox"
            }
          ]
        }
      },
      {
        "name": "platform",
        "label": "Platform(s)",
        "headSearchable": true
      },
      {
        "name": "version",
        "label": "Engine version",
        "searchable": {
          "type": "input-number",
          "name": "version",
          "label": "版本号",
          "placeholder": "输入版本号",
          "mode": "horizontal"
        }
      },
      {
        "name": "grade",
        "label": "CSS grade",
        "headSearchable": {
          "type": "input-text",
          "name": "grade",
          "label": "CSS grade",
          "mode": "horizontal"
        }
      }
    ]
  }
}

export default demo;
