const demo = {
  "type": "page",
  "body": [
    {
      "label": "提示弹框",
      "type": "button",
      "actionType": "dialog",
      "dialog": {
        "title": "弹框标题",
        "body": [
          {
            "type": "input-table",
            "name": "table",
            "addable": true,
            "copyable": true,
            "editable": true,
            "value": [
              {
                "a": "a1",
                "b": "b1"
              }
            ],
            "columns": [
              {
                "name": "a",
                "label": "A"
              },
              {
                "name": "b",
                "label": "B"
              }
            ]
          },
          {
            "label": "多选",
            "type": "select",
            "name": "select2",
            "searchable": true,
            "checkAll": true,
            "multiple": true,
            "clearable": true,
            "popOverContainerSelector": "body",
            "source": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/getOptions"
          }
        ]
      }
    }
  ]
}


export default demo;
