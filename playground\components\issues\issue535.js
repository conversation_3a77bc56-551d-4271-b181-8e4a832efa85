const crudDemo = {
  "type": "page",
  "body": {
    "type": "service",
    "api": "/api/mock2/sample?perPage=5",
    "body": [
      {
        "type": "crud",
        "title": "表格1",
        "source": "$rows",
        "selectable": true,
        "pickerMode": true,
        "multiple": true,
        "checkOnItemClick": false,
        "maxKeepItemSelectionLength": 3,
        "columns": [
          {
            "name": "id",
            "label": "id"
          },
          {
            "name": "engine",
            "label": "Engine"
          },
          {
            "name": "version",
            "label": "Version"
          }
        ],
        "onEvent": {
          "selectedChange": {
            "actions": [
              {
                "actionType": "custom",
                "script": "event.setData({selectRows: event.data.selectedItems })"
              }
            ]
          }
        }
      }
    ]
  }
}

const tableDemo = {
  "type": "page",
  "body": {
    "type": "service",
    "api": "/api/mock2/sample?perPage=5",
    "body": [
      {
        "type": "table",
        "title": "表格1",
        "source": "$rows",
        "selectable": true,
        "multiple": true,
        "checkOnItemClick": false,
        "maxKeepItemSelectionLength": 3,
        "columns": [
          {
            "name": "id",
            "label": "id"
          },
          {
            "name": "engine",
            "label": "Engine"
          },
          {
            "name": "version",
            "label": "Version"
          }
        ],
        "onEvent": {
          "selectedChange": {
            "actions": [
              {
                "actionType": "custom",
                "script": "event.setData({selectRows: event.data.selectedItems })"
              }
            ]
          }
        }
      }
    ]
  }
}

export default crudDemo;
