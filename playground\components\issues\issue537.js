const demo = {
  "type": "page",
  "body": {
    "type": "service",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=2",
    "body": [
      {
        "type": "table",
        "source": "$rows",
        // "selectable": true,
        // "multiple": true,
        "columns": [
          {
            "name": "id",
            "label": "ID",
            "width": 80,
            "fixed": "left"
          },
          {
            "name": "browser",
            "label": "Browser",
            "width": 160,
            "fixed": "left"
          },
          {
            "name": "version",
            "label": "Version",
          },
          {
            "name": "version",
            "label": "Version2",
            "width": 1200
          }
        ],
        "affixRow": [
          {
            "type": "text",
            "text": "总计"
          },
          {
            "type": "tpl",
            "tpl": "${rows|pick:version|sum}"
          }
        ]
      },
      {
        "type": "divider"
      },
      {
        "type": "crud",
        "syncLocation": false,
        "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=5",
        // "loadDataOnce": true,
        // "draggable": true,
        "prefixRow": [
          {
            "type": "text",
            "text": "头总结"
          },
          {
            "type": "text",
            "text": "200"
          }
        ],
        "affixRow": [
          {
            "type": "text",
            "text": "尾总结"
          },
          {
            "type": "text",
            "text": "200"
          },
        ],
        "columns": [
          {
            "name": "id",
            "label": "ID",
            "width": 80,
            "fixed": "left"
          },
          {
            "name": "engine",
            "label": "Rendering engine2"
          },
          {
            "name": "engine",
            "label": "Rendering engine3"
          },
          {
            "name": "engine",
            "label": "Rendering engine4"
          },
          {
            "name": "engine",
            "label": "Rendering engine5"
          },
          {
            "name": "engine",
            "label": "Rendering engine6",
            "width": 800
          },
        ],
        "headerToolbar": [
          "bulkActions"
        ],
        "bulkActions": [
          {
            "label": "批量删除",
            "actionType": "ajax",
          },
        ],
      }
    ]
  }
}

// 带子表格的情况
const demo2 = {
  "type": "page",
  "body": {
    "type": "service",
    "data": {
      "rows": [
        {
          "engine": "Trident",
          "browser": "Internet Explorer 4.0",
          "platform": "Win 95+",
          "version": "4",
          "grade": "X",
          "id": 1,
          "children": [
            {
              "engine": "Trident",
              "browser": "Internet Explorer 4.0",
              "platform": "Win 95+",
              "version": "4",
              "grade": "X",
              "id": 1001
            },
            {
              "engine": "Trident",
              "browser": "Internet Explorer 5.0",
              "platform": "Win 95+",
              "version": "5",
              "grade": "C",
              "id": 1002
            }
          ]
        },
        {
          "engine": "Trident",
          "browser": "Internet Explorer 5.0",
          "platform": "Win 95+",
          "version": "5",
          "grade": "C",
          "id": 2,
          "children": [
            {
              "engine": "Trident",
              "browser": "Internet Explorer 4.0",
              "platform": "Win 95+",
              "version": "4",
              "grade": "X",
              "id": 2001
            },
            {
              "engine": "Trident",
              "browser": "Internet Explorer 5.0",
              "platform": "Win 95+",
              "version": "5",
              "grade": "C",
              "id": 2002
            }
          ]
        }
      ]
    },
    "body": [
      {
        "type": "table",
        "source": "$rows",
        "className": "m-b-none",
        "columnsTogglable": false,
        "selectable": true,
        "affixRow": [
          {
            "type": "text",
            "text": "总计",
            // "colSpan": 2
          },
          {
            "type": "tpl",
            "tpl": "${rows|pick:version|sum}"
          }
        ],
        "columns": [
          {
            "name": "id",
            "label": "ID",
            "width": 80,
            "fixed": "left"
          },
          {
            "name": "grade",
            "label": "Grade"
          },
          {
            "name": "version",
            "label": "Version"
          },
          {
            "name": "browser",
            "label": "Browser"
          },
          {
            "name": "platform",
            "label": "Platform",
            "width": 800
          }
        ]
      }
    ]
  }
}

export default demo;
