import {
  getTabTableListSchema,
  getWithoutMarginsCRUDSchema,
  getWithoutMarginsCRUDSchemaV2,
  getCompactModeListSchema,
  getCompactModeListSchemaV2,
 } from 'amis-utils'

// 1. 一般模式
const demo = {
  "type": "page",
  "style": {
    "height": "3000px"
  },
  "body": [
    {
      "type": "crud",
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
      "syncLocation": false,
      "affixHeader": true,
      // "topToolbar": [
      //   {
      //     "type": "action",
      //     "label": "topToolbar"
      //   }
      // ],
      // "filter": {
      //   "title": false,
      //   "body": [
      //     {
      //       "type": "input-text",
      //       "label": "filter"
      //     }
      //   ]
      // },
      "headerFilter": {
        "body": [
          {
            "type": "input-text",
            "label": "headerFilter"
          }
        ]
      },
      // "headerToolbar": [
      //   {
      //     "type": "action",
      //     "label": "headerToolbar"
      //   }
      // ],
      "columns": [
        {
          "name": "id",
          "label": "ID",
          "fixed": "left"
        },
        {
          "name": "engine",
          "label": "Rendering engine"
        },
        {
          "name": "browser",
          "label": "Browser",
          "width": 1200
        }
      ]
    },
    {
      "type": "divider"
    },
    {
      "type": "crud",
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
      "syncLocation": false,
      // "affixHeader": true,
      // "topToolbar": [
      //   {
      //     "type": "action",
      //     "label": "topToolbar"
      //   }
      // ],
      // "filter": {
      //   "title": false,
      //   "body": [
      //     {
      //       "type": "input-text",
      //       "label": "filter"
      //     }
      //   ]
      // },
      "headerFilter": {
        "body": [
          {
            "type": "input-text",
            "label": "headerFilter"
          }
        ]
      },
      // "headerToolbar": [
      //   {
      //     "type": "action",
      //     "label": "headerToolbar"
      //   }
      // ],
      "columns": [
        {
          "name": "id",
          "label": "ID"
        },
        {
          "name": "engine",
          "label": "Rendering engine"
        },
        {
          "name": "browser",
          "label": "Browser"
        },
      ]
    }
  ]
}

// 2. noPadding模式1/2
const noPaddingDemo = {
  "type": "page",
  "style": {
    "height": "3000px"
  },
  "body": [
    getWithoutMarginsCRUDSchemaV2({
      "type": "crud",
      "syncLocation": false,
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
      "loadDataOnce": true,
      "affixHeader": true,
      "title": "标题",
      // "topToolbar": [
      //   {
      //     "type": "action",
      //     "label": "topToolbar"
      //   }
      // ],
      // "filter": {
      //   "title": false,
      //   "body": [
      //     {
      //       "type": "input-text",
      //       "label": "filter"
      //     }
      //   ]
      // },
      "headerFilter": {
        "body": [
          {
            "type": "input-text",
            "label": "headerFilter"
          }
        ]
      },
      "headerToolbar": [
        {
          "type": "action",
          "label": "headerToolbar"
        }
      ],
      "columns": [
        {
          "name": "id",
          "label": "ID"
        },
        {
          "name": "engine",
          "label": "Rendering engine"
        },
      ]
    }, true, false),
    getWithoutMarginsCRUDSchemaV2({
      "type": "crud",
      "syncLocation": false,
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
      "loadDataOnce": true,
      "title": "标题",
      // "affixHeader": true,
      // "topToolbar": [
      //   {
      //     "type": "action",
      //     "label": "topToolbar"
      //   }
      // ],
      // "filter": {
      //   "title": false,
      //   "body": [
      //     {
      //       "type": "input-text",
      //       "label": "filter"
      //     }
      //   ]
      // },
      "headerFilter": {
        "body": [
          {
            "type": "input-text",
            "label": "headerFilter"
          }
        ]
      },
      "headerToolbar": [
        {
          "type": "action",
          "label": "headerToolbar"
        }
      ],
      "columns": [
        {
          "name": "id",
          "label": "ID"
        },
        {
          "name": "engine",
          "label": "Rendering engine"
        },
      ]
    }, true, false)
  ]
}

// 3. 紧凑模式1/2
const compactDemo = {
  "type": "page",
  "style": {
    "height": "3000px"
  },
  "body": [
    getCompactModeListSchemaV2({
      "type": "crud",
      "syncLocation": false,
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
      "loadDataOnce": true,
      // "noPadding": true,
      "affixHeader": true,
      "headerFilter": {
        "body": [
          {
            "type": "input-text",
            "label": "headerFilter"
          }
        ]
      },
      "headerToolbar": [
        {
          "type": "action",
          "label": "headerToolbar"
        }
      ],
      "columns": [
        {
          "name": "id",
          "label": "ID"
        },
        {
          "name": "engine",
          "label": "Rendering engine"
        },
        {
          "name": "browser",
          "label": "Browser"
        },
        {
          "name": "platform",
          "label": "Platform(s)"
        },
        {
          "name": "version",
          "label": "Engine version"
        },
        {
          "name": "grade",
          "label": "CSS grade",
          "sortable": true
        }
      ]
    }),
    getCompactModeListSchemaV2({
      "type": "crud",
      "syncLocation": false,
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
      "loadDataOnce": true,
      // "noPadding": true,
      // "affixHeader": true,
      "headerFilter": {
        "body": [
          {
            "type": "input-text",
            "label": "headerFilter"
          }
        ]
      },
      "headerToolbar": [
        {
          "type": "action",
          "label": "headerToolbar"
        }
      ],
      "columns": [
        {
          "name": "id",
          "label": "ID"
        },
        {
          "name": "engine",
          "label": "Rendering engine"
        },
        {
          "name": "browser",
          "label": "Browser"
        },
        {
          "name": "platform",
          "label": "Platform(s)"
        },
        {
          "name": "version",
          "label": "Engine version"
        },
        {
          "name": "grade",
          "label": "CSS grade",
          "sortable": true
        }
      ]
    })
  ]
}


const itemActionsDemo = {
  "type": "page",
  "style": {
    "height": "2000px"
  },
  "body": {
    "type": "crud",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
    "syncLocation": false,
    "affixHeader": true,
    // "noPadding": true,
    "topToolbar": [
      {
        "type": "action",
        "label": "topToolbar"
      }
    ],
    "filter": {
      "title": false,
      "body": [
        {
          "type": "input-text",
          "label": "filter"
        }
      ]
    },
    "headerFilter": {
      "body": [
        {
          "type": "input-text",
          "label": "headerFilter"
        }
      ]
    },
    "headerToolbar": [
      {
        "type": "action",
        "label": "headerToolbar"
      }
    ],
    "itemActions": [
        {
          "label": "编辑",
          "type": "button",
          "actionType": "dialog",
          "dialog": {
            "title": "编辑",
            "body": "这是个简单的编辑弹框"
          }
        },
        {
          "label": "删除",
          "type": "button",
          "actionType": "ajax",
          "confirmText": "确认要删除？",
          "api": "/api/mock2/form/saveForm"
        }
      ],
    "columns": [
      {
        "name": "id",
        "label": "ID"
      },
      {
        "name": "engine",
        "label": "Rendering engine"
      },
      {
        "name": "browser",
        "label": "Browser"
      },
      {
        "name": "platform",
        "label": "Platform(s)"
      },
      {
        "name": "version",
        "label": "Engine version"
      },
      {
        "name": "grade",
        "label": "CSS grade"
      }
    ]
  }
}

const COLUMNS = [
  {
    "name": "id",
    "label": "ID"
  },
  {
    "name": "engine",
    "label": "Rendering engine"
  },
  {
    "name": "browser",
    "label": "Browser"
  },
];

// 4. tabs模式
const tabCrudDemo = {
  "type": "page",
  "style": {
    "height": "2000px"
  },
  "className": "bg-light",
  "body": [
    getTabTableListSchema({
      "api": "/api/mock2/crud/table5",
      "filter": {
        "title": "",
        "body": [
          {
            "type": "group",
            "mode": "horizontal",
            "body": [
              {
                "type": "input-text",
                "name": "keywords",
                "label": "关键字",
                "clearable": true,
                "placeholder": "通过关键字搜索",
                "columnRatio": 4
              },
              {
                "type": "input-text",
                "name": "engine",
                "label": "Engine",
                "clearable": true,
                "columnRatio": 4
              },
              {
                "type": "input-text",
                "name": "platform",
                "label": "Platform",
                "clearable": true,
                "columnRatio": 4
              },
            ]
          }
        ],
        "actions": [
          {
            "type": "reset",
            "label": "重 置"
          },
          {
            "type": "submit",
            "level": "primary",
            "label": "查 询"
          }
        ]
      },
      "tabs": [
        {
          "title": "列表 1",
          "tab": {
            "source": "${list2}",
            "columns": COLUMNS,
            "affixHeader": true,
            "headerFilter": {
              "body": [
                {
                  "type": "input-text",
                  "label": "headerFilter"
                }
              ]
            },
            "headerToolbar": [
              {
                "type": "action",
                "label": "headerToolbar"
              }
            ],
            "footerToolbar": [
              {
                "type":"tpl",
                "tpl": "共${list2.length}条",
                "className": "pr-2"
              },
              {
                "type": "pagination",
                "layout": "pager,perPage,go",
              },
            ]
          }
        },
        {
          "title": "列表 2",
          "tab": {
            "source": "${list1}",
            "columns": COLUMNS,
            "headerFilter": {
              "body": [
                {
                  "type": "input-text",
                  "label": "headerFilter"
                }
              ]
            },
            "headerToolbar": [
              {
                "type": "action",
                "label": "headerToolbar"
              }
            ],
            "footerToolbar": [
              {
                "type":"tpl",
                "tpl": "共${list1.length}条",
                "className": "pr-2"
              },
              {
                "type": "pagination",
                "layout": "pager,perPage,go",
              },
            ]
          }
        },
        {
          "title": "列表 3",
          "tab": {
            "source": "${list3}",
            "columns": COLUMNS,
            "footerToolbar": [
              {
                "type":"tpl",
                "tpl": "共${list3.length}条",
                "className": "pr-2"
              },
              {
                "type": "pagination",
                "layout": "pager,perPage,go",
              },
            ]
          }
        }
      ]
    })
  ]
};


export default demo;
