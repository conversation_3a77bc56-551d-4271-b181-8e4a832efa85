
const demo = {
  "type": "page",
  "data": {
    "idx": "2",
  },
  "body": [
    {
      "type": "button-toolbar",
      "className": "m-b",
      "buttons": [
        {
          "name": "trigger1",
          "type": "action",
          "label": "指定toggle(condition)",
          "onEvent": {
            "click": {
              "actions": [
                {
                  "actionType": "toggleExpanded",
                  "componentId": "table-id",
                  "args": {
                    "condition": "${id == 1}",
                  }
                }
              ]
            }
          }
        },
        {
          "name": "trigger1",
          "type": "action",
          "label": "指定toggle(index)",
          "onEvent": {
            "click": {
              "actions": [
                {
                  "actionType": "toggleExpanded",
                  "componentId": "table-id",
                  "args": {
                    "index": "1,${idx}",
                  }
                }
              ]
            }
          }
        }
      ]
    },
    {
      "type": "service",
      "data": {
        "rows": [
          {
            "engine": "Trident",
            "browser": "Internet Explorer 4.0",
            "platform": "Win 95+",
            "version": "4",
            "grade": "X",
            "id": 1,
            "children": [
              {
                "engine": "Trident",
                "browser": "Internet Explorer 4.0",
                "platform": "Win 95+",
                "version": "4",
                "grade": "X",
                "id": 1001
              },
              {
                "engine": "Trident",
                "browser": "Internet Explorer 5.0",
                "platform": "Win 95+",
                "version": "5",
                "grade": "C",
                "id": 1002
              }
            ]
          },
          {
            "engine": "Trident",
            "browser": "Internet Explorer 5.0",
            "platform": "Win 95+",
            "version": "5",
            "grade": "C",
            "id": 2,
            "children": [
              {
                "engine": "Trident",
                "browser": "Internet Explorer 4.0",
                "platform": "Win 95+",
                "version": "4",
                "grade": "X",
                "id": 2001
              },
              {
                "engine": "Trident",
                "browser": "Internet Explorer 5.0",
                "platform": "Win 95+",
                "version": "5",
                "grade": "C",
                "id": 2002
              }
            ]
          },
          {
            "engine": "Trident",
            "browser": "Internet Explorer 5.5",
            "platform": "Win 95+",
            "version": "5.5",
            "grade": "A",
            "id": 3,
            "children": [
              {
                "engine": "Trident",
                "browser": "Internet Explorer 4.0",
                "platform": "Win 95+",
                "version": "4",
                "grade": "X",
                "id": 3001
              },
              {
                "engine": "Trident",
                "browser": "Internet Explorer 5.0",
                "platform": "Win 95+",
                "version": "5",
                "grade": "C",
                "id": 3002
              }
            ]
          },
        ]
      },
      "body": [
        {
          "type": "table",
          "id": "table-id",
          "source": "$rows",
          "className": "m-b-none",
          "columnsTogglable": false,
          "columns": [
            {
              "name": "id",
              "label": "ID"
            },
            {
              "name": "engine",
              "label": "Engine"
            },
            {
              "name": "grade",
              "label": "Grade"
            },
            {
              "type": "show-more",
              "label": "操作",
              "width": 80,
              "buttons": [
                {
                  "type": "button",
                  "label": "${_amisExpanded ? '收起' : '展开'}",
                  "level": "link",
                  "visibleOn": "${children && children.length > 0}",
                  "onEvent": {
                    "click": {
                      "actions": [
                        {
                          "actionType": "toggleExpanded",
                          "componentId": "table-id",
                          "args": {
                            "ids": "${id}"
                          }
                        }
                      ]
                    }
                  }
                }
              ]
            }
          ]
        }
      ]
    },
    {
      "type": "divider"
    },
    {
      "type": "button",
      "label": "toggled指定id",
      "level": "primary",
      "className": "mb-2",
      "onEvent": {
        "click": {
          "actions": [
            {
              "componentId": "crud-id",
              "actionType": "toggleExpanded",
              "args": {
                "condition": "${id == 1}",
              }
            }
          ]
        }
      }
    },
    {
      "type": "service",
      "data": {
        "rows": [
          {
            "engine": "Trident",
            "browser": "Internet Explorer 4.0",
            "platform": "Win 95+",
            "version": "4",
            "grade": "X",
            "id": 1,
            "children": [
              {
                "engine": "Trident",
                "browser": "Internet Explorer 4.0",
                "platform": "Win 95+",
                "version": "4",
                "grade": "X",
                "id": 1001,
                "children": [
                  {
                    "engine": "Trident",
                    "browser": "Internet Explorer 5.0",
                    "platform": "Win 95+",
                    "version": "5",
                    "grade": "C",
                    "id": 10011
                  }
                ]
              },
              {
                "engine": "Trident",
                "browser": "Internet Explorer 5.0",
                "platform": "Win 95+",
                "version": "5",
                "grade": "C",
                "id": 1002
              }
            ]
          },
          {
            "engine": "Trident",
            "browser": "Internet Explorer 5.0",
            "platform": "Win 95+",
            "version": "5",
            "grade": "C",
            "id": 2,
            "children": [
              {
                "engine": "Trident",
                "browser": "Internet Explorer 4.0",
                "platform": "Win 95+",
                "version": "4",
                "grade": "X",
                "id": 2001
              },
              {
                "engine": "Trident",
                "browser": "Internet Explorer 5.0",
                "platform": "Win 95+",
                "version": "5",
                "grade": "C",
                "id": 2002
              }
            ]
          },
        ]
      },
      "body": [
        {
          "type": "crud",
          "id": "crud-id",
          "syncLocation": false,
          "source": "$rows",
          "columnsTogglable": false,
          "columns": [
            {
              "name": "id",
              "label": "ID"
            },
            {
              "name": "engine",
              "label": "Engine"
            },
            {
              "name": "grade",
              "label": "Grade"
            },
            {
              "type": "show-more",
              "label": "操作",
              "width": 80,
              "buttons": [
                {
                  "type": "button",
                  "label": "${_amisExpanded ? '收起' : '展开'}",
                  "level": "link",
                  "visibleOn": "${children && children.length > 0}",
                  "onEvent": {
                    "click": {
                      "actions": [
                        {
                          "actionType": "toggleExpanded",
                          "componentId": "crud-id",
                          "args": {
                            "ids": "${id}"
                          }
                        }
                      ]
                    }
                  }
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}

// crud
const demo2 = {
  "type": "page",
  "body": [
    {
      "type": "button",
      "label": "toggled指定id",
      "level": "primary",
      "className": "mb-2",
      "onEvent": {
        "click": {
          "actions": [
            {
              "componentId": "crud-id",
              "actionType": "toggleExpanded",
              "args": {
                "condition": "${id == 1}",
              }
            }
          ]
        }
      }
    },
    {
      "type": "service",
      "data": {
        "rows": [
          {
            "engine": "Trident",
            "browser": "Internet Explorer 4.0",
            "platform": "Win 95+",
            "version": "4",
            "grade": "X",
            "id": 1,
            "children": [
              {
                "engine": "Trident",
                "browser": "Internet Explorer 4.0",
                "platform": "Win 95+",
                "version": "4",
                "grade": "X",
                "id": 1001,
                "children": [
                  {
                    "engine": "Trident",
                    "browser": "Internet Explorer 5.0",
                    "platform": "Win 95+",
                    "version": "5",
                    "grade": "C",
                    "id": 10011
                  }
                ]
              },
              {
                "engine": "Trident",
                "browser": "Internet Explorer 5.0",
                "platform": "Win 95+",
                "version": "5",
                "grade": "C",
                "id": 1002
              }
            ]
          },
          {
            "engine": "Trident",
            "browser": "Internet Explorer 5.0",
            "platform": "Win 95+",
            "version": "5",
            "grade": "C",
            "id": 2,
            "children": [
              {
                "engine": "Trident",
                "browser": "Internet Explorer 4.0",
                "platform": "Win 95+",
                "version": "4",
                "grade": "X",
                "id": 2001
              },
              {
                "engine": "Trident",
                "browser": "Internet Explorer 5.0",
                "platform": "Win 95+",
                "version": "5",
                "grade": "C",
                "id": 2002
              }
            ]
          },
        ]
      },
      "body": [
        {
          "type": "crud",
          "id": "crud-id",
          "syncLocation": false,
          "source": "$rows",
          "columnsTogglable": false,
          "columns": [
            {
              "name": "id",
              "label": "ID"
            },
            {
              "name": "engine",
              "label": "Engine"
            },
            {
              "name": "grade",
              "label": "Grade"
            },
            {
              "type": "show-more",
              "label": "操作",
              "width": 80,
              "buttons": [
                {
                  "type": "button",
                  "label": "${_amisExpanded ? '收起' : '展开'}",
                  "level": "link",
                  "visibleOn": "${children && children.length > 0}",
                  "onEvent": {
                    "click": {
                      "actions": [
                        {
                          "actionType": "toggleExpanded",
                          "componentId": "crud-id",
                          "args": {
                            "ids": "${id}"
                          }
                        }
                      ]
                    }
                  }
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}

const demo3 = {
  "type": "page",
  "body": {
    "type": "service",
    "data": {
      "rows": [
        {
          "engine": "Trident",
          "browser": "Internet Explorer 4.0",
          "platform": "Win 95+",
          "version": "4",
          "grade": "X",
          "id": 1,
          "children": [
            {
              "engine": "Trident",
              "browser": "Internet Explorer 4.0",
              "platform": "Win 95+",
              "version": "1001",
              "grade": "X",
              "id": 1001
            },
            {
              "engine": "Trident",
              "browser": "Internet Explorer 5.0",
              "platform": "Win 95+",
              "version": "1002",
              "grade": "C",
              "id": 1002,
              "children": [
                {
                  "engine": "Trident",
                  "browser": "Internet Explorer 5.0",
                  "platform": "Win 95+",
                  "version": "10021",
                  "grade": "C",
                  "id": 10021,
                }
              ]
            }
          ]
        },
        {
          "engine": "Trident",
          "browser": "Internet Explorer 5.0",
          "platform": "Win 95+",
          "version": "5",
          "grade": "C",
          "id": 2
        }
      ],
      "expandedRowKeys": []
    },
    "body": [
      {
        "type": "table",
        "source": "$rows",
        "className": "m-b-none",
        "columnsTogglable": true,
        "selectable": true,
        "multiple": true,
        "columns": [
          {
            "name": "engine",
            "label": "Engine"
          },
          {
            "name": "grade",
            "label": "Grade"
          },
          {
            "name": "version",
            "label": "Version"
          },
          {
            "name": "browser",
            "label": "Browser"
          },
          {
            "name": "id",
            "label": "ID"
          },
          {
            "name": "platform",
            "label": "Platform"
          }
        ],
        "subTable": {
          // "type": "table",
          // "source": "$children",
          // "columns": [
          //   {
          //     "name": "engine",
          //     "label": "子表格1"
          //   },
          //   {
          //     "name": "grade",
          //     "label": "子表格2"
          //   }
          // ]
        }
      }
    ]
  }
}

// children数据嵌套
const demo4 = {
  "type": "page",
  "body": {
    "type": "service",
    "debug": true,
    "data": {
      "rows": [
        {
          "engine": "Trident",
          "browser": "Internet Explorer 4.0",
          "platform": "Win 95+",
          "version": "4",
          "grade": "X",
          "id": 1,
          "children": [
            {
              "engine": "Trident",
              "browser": "Internet Explorer 4.0",
              "platform": "Win 95+",
              "version": "4",
              "grade": "X",
              "id": 11,
              "children": [
                {
                  "engine": "Trident",
                  "browser": "Internet Explorer 4.0",
                  "platform": "Win 95+",
                  "version": "4",
                  "grade": "X",
                  "id": 111,
                },
                {
                  "engine": "Trident",
                  "browser": "Internet Explorer 4.0",
                  "platform": "Win 95+",
                  "version": "4",
                  "grade": "X",
                  "id": 112,
                }
              ]
            },
            {
              "engine": "Trident",
              "browser": "Internet Explorer 4.0",
              "platform": "Win 95+",
              "version": "4",
              "grade": "X",
              "id": 12,
              "children": [
                {
                  "engine": "Trident",
                  "browser": "Internet Explorer 4.0",
                  "platform": "Win 95+",
                  "version": "4",
                  "grade": "X",
                  "id": 121,
                },
                {
                  "engine": "Trident",
                  "browser": "Internet Explorer 4.0",
                  "platform": "Win 95+",
                  "version": "4",
                  "grade": "X",
                  "id": 122,
                }
              ]
            }
          ]
        },
        {
          "engine": "Trident",
          "browser": "Internet Explorer 5.0",
          "platform": "Win 95+",
          "version": "5",
          "grade": "C",
          "id": 2,
          "children": [
            {
              "engine": "Trident",
              "browser": "Internet Explorer 4.0",
              "platform": "Win 95+",
              "version": "4",
              "grade": "X",
              "id": 21,
              "children": [
                {
                  "engine": "Trident",
                  "browser": "Internet Explorer 4.0",
                  "platform": "Win 95+",
                  "version": "4",
                  "grade": "X",
                  "id": 211,
                },
                {
                  "engine": "Trident",
                  "browser": "Internet Explorer 4.0",
                  "platform": "Win 95+",
                  "version": "4",
                  "grade": "X",
                  "id": 212,
                }
              ]
            },
            {
              "engine": "Trident",
              "browser": "Internet Explorer 4.0",
              "platform": "Win 95+",
              "version": "4",
              "grade": "X",
              "id": 22,
              "children": [
                {
                  "engine": "Trident",
                  "browser": "Internet Explorer 4.0",
                  "platform": "Win 95+",
                  "version": "4",
                  "grade": "X",
                  "id": 221,
                },
                {
                  "engine": "Trident",
                  "browser": "Internet Explorer 4.0",
                  "platform": "Win 95+",
                  "version": "4",
                  "grade": "X",
                  "id": 222,
                }
              ]
            }
          ]
        },
        {
          "engine": "Trident",
          "browser": "Internet Explorer 5.5",
          "platform": "Win 95+",
          "version": "5.5",
          "grade": "A",
          "id": 3
        },
        {
          "engine": "Trident",
          "browser": "Internet Explorer 6",
          "platform": "Win 98+",
          "version": "6",
          "grade": "A",
          "id": 4
        },
        {
          "engine": "Trident",
          "browser": "Internet Explorer 7",
          "platform": "Win XP SP2+",
          "version": "7",
          "grade": "A",
          "id": 5
        }
      ]
    },
    "body": [
      {
        "type": "table",
        "id": "roottableId",
        "source": "$rows",
        "className": "m-b-none",
        "columnsTogglable": false,
        "columns": [
          {
            "name": "id",
            "label": "ID"
          },
          {
            "name": "engine",
            "label": "Engine"
          },
          {
            "name": "grade",
            "label": "Grade"
          },
          {
            "name": "version",
            "label": "Version"
          },
          {
            "type": "operation",
            "label": "操作",
            "width": 80,
            "buttons": [
              {
                "type": "button",
                "label": "${_amisExpanded ? '收起' : '展开'}",
                "level": "link",
                "onEvent": {
                  "click": {
                    "actions": [
                      {
                        "actionType": "toggleExpanded",
                        "componentId": "roottableId",
                        "args": {
                          "condition": "${id === currentId}",
                          "currentId": "${id}"
                        }
                      }
                    ]
                  }
                }
              }
            ]
          }
        ],
        "subTable": {
          "type": "table",
          "title": "子表格id：${_subTableId}",
          "columns": [
            {
              "name": "id",
              "label": "ID"
            },
            {
              "name": "grade",
              "label": "Grade"
            },
            {
              "type": "operation",
              "label": "操作",
              "width": 80,
              "buttons": [
                {
                  "type": "button",
                  "label": "${_amisExpanded ? '收起' : '展开'}${_subTableId}",
                  "level": "link",
                  "onEvent": {
                    "click": {
                      "actions": [
                        {
                          "actionType": "toggleExpanded",
                          "componentId": "${_subTableId}",
                          "args": {
                            "condition": "${id === currentId}",
                            "currentId": "${id}"
                          }
                        }
                      ]
                    }
                  }
                }
              ]
            }
          ],
          "source": "$children",
          "subTable": {
            "type": "table",
            "title": "子表格id：${_subTableId}",
            "columns": [
              {
                "name": "id",
                "label": "ID"
              },
              {
                "name": "grade",
                "label": "Grade"
              },
              {
                "type": "operation",
                "label": "操作",
                "width": 80,
                "buttons": [
                  {
                    "type": "button",
                    "label": "${_amisExpanded ? '收起' : '展开'}${_subTableId}",
                    "level": "link",
                    "onEvent": {
                      "click": {
                        "actions": [
                          {
                            "actionType": "toggleExpanded",
                            "componentId": "${_subTableId}",
                            "args": {
                              "condition": "${id === currentId}",
                              "currentId": "${id}"
                            }
                          }
                        ]
                      }
                    }
                  }
                ]
              }
            ],
            "source": "$children",
            "subTable": {
              "label": "弹个框",
              "type": "button",
              "actionType": "dialog",
              "dialog": {
                "title": "弹框",
                "body": "这是个简单的弹框。"
              }
            }
          }
        }
      }
    ]
  }
}

// subTable配置api
const demo5 = {
  "type": "page",
  "body": [
    {
      "type": "button",
      "label": "点击tableId",
      "level": "primary",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "toggleExpanded",
              "componentId": "tableId",
              "args": {
                "condition": "${id == 1}"
              }
            }
          ]
        }
      }
    },
    {
      "type": "button",
      "label": "点击subTable-0",
      "level": "primary",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "toggleExpanded",
              "componentId": "subTable-0",
              "args": {
                "condition": "${id == 1}"
              }
            }
          ]
        }
      }
    },
    {
      "type": "service",
      "debug": true,
      "data": {
        "rows": [
          {
            "engine": "Trident",
            "browser": "Internet Explorer 4.0",
            "platform": "Win 95+",
            "version": "4",
            "grade": "X",
            "id": 1,
            "children": []
          },
          {
            "engine": "Trident",
            "browser": "Internet Explorer 5.0",
            "platform": "Win 95+",
            "version": "5",
            "grade": "C",
            "id": 2
          },
          {
            "engine": "Trident",
            "browser": "Internet Explorer 5.5",
            "platform": "Win 95+",
            "version": "5.5",
            "grade": "A",
            "id": 3
          },
          {
            "engine": "Trident",
            "browser": "Internet Explorer 6",
            "platform": "Win 98+",
            "version": "6",
            "grade": "A",
            "id": 4
          },
          {
            "engine": "Trident",
            "browser": "Internet Explorer 7",
            "platform": "Win XP SP2+",
            "version": "7",
            "grade": "A",
            "id": 5
          }
        ]
      },
      "body": [
        {
          "type": "table",
          // "id": "tableId",
          // "showExpansionColumn": false,
          "source": "$rows",
          "className": "m-b-none",
          "columnsTogglable": false,
          "columns": [
            {
              "name": "id",
              "label": "ID"
            },
            {
              "name": "engine",
              "label": "Engine"
            },
            {
              "name": "grade",
              "label": "Grade"
            },
            {
              "name": "version",
              "label": "Version"
            },
            {
              "type": "operation",
              "label": "操作",
              "width": 80,
              "buttons": [
                {
                  "type": "button",
                  "label": "${_amisExpanded ? '收起' : '展开'}",
                  "level": "link",
                  "onEvent": {
                    "click": {
                      "actions": [
                        {
                          "actionType": "toggleExpanded",
                          "componentId": "tableId",
                          "args": {
                            "condition": "${id === currentId}",
                            "currentId": "${id}"
                          }
                        }
                      ]
                    }
                  }
                }
              ]
            }
          ],
          "subTable": {
            "type": "table",
            "id": "subTable",
            "showExpansionColumn": false,
            "api": {
              "url": "/api/mock2/sample?perPage=3&id=${id}",
              "responseData": {
                "children": "${rows}"
              }
            },
            "columns": [
              {
                "name": "id",
                "label": "ID"
              },
              {
                "name": "grade",
                "label": "子表格id: ${_subTableId}"
              },
              {
                "type": "operation",
                "label": "操作",
                "width": 80,
                "buttons": [
                  {
                    "type": "button",
                    "label": "${_amisExpanded ? '收起' : '展开'}${_subTableId}",
                    "level": "link",
                    "onEvent": {
                      "click": {
                        "actions": [
                          {
                            "actionType": "toggleExpanded",
                            "componentId": "${_subTableId}",
                            "args": {
                              "condition": "${id === currentId}",
                              "currentId": "${id}"
                            }
                          }
                        ]
                      }
                    }
                  }
                ]
              }
            ],
            "source": "$children",
            "subTable": {
              "type": "table",
              // "id": "subTable2",
              "showExpansionColumn": false,
              "api": {
                "url": "/api/mock2/sample?perPage=3&id=${id}",
                "responseData": {
                  "children": "${rows}"
                }
              },
              "columns": [
                {
                  "name": "id",
                  "label": "ID"
                },
                {
                  "name": "grade",
                  "label": "子表格id: ${_subTableId}"
                },
                {
                  "type": "operation",
                  "label": "操作",
                  "width": 80,
                  "buttons": [
                    {
                      "type": "button",
                      "label": "${_amisExpanded ? '收起' : '展开'}${_subTableId}",
                      "level": "link",
                      "onEvent": {
                        "click": {
                          "actions": [
                            {
                              "actionType": "toggleExpanded",
                              "componentId": "${_subTableId}",
                              "args": {
                                "condition": "${id === currentId}",
                                "currentId": "${id}"
                              }
                            }
                          ]
                        }
                      }
                    }
                  ]
                }
              ],
              "source": "$children",
              "subTable": {
                "label": "弹个框",
                "type": "button",
                "actionType": "dialog",
                "dialog": {
                  "title": "弹框",
                  "body": "这是个简单的弹框。"
                }
              }
            }
          }
        }
      ]
    }
  ]
}

export default demo4;
