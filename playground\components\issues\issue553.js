const demo = {
  "type": "page",
  "body": [
    {
      "type": "form",
      "debug": true,
      "mode": "horizontal",
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
      "body": [
        {
          "type": "input-text",
          "label": "父级文本框",
          "name": "super_text",
          "value": "123"
        },
        {
          "type": "switch",
          "label": "父级勾选框",
          "name": "super_switch",
          "value": false
        },
        {
          "type": "input-table",
          "name": "list",
          "label": "可获取父级数据",
          "addable": true,
          "removable": true,
          "needConfirm": false,
          "canAccessSuperData": true,
          "strictMode": false,
          "updateAllRows": true,
          "columns": [
            {
              "name": "super_text",
              "type": "text",
              "label": "表单项",
              "quickEdit": {
                "disabledOn": "this.super_switch"
              }
            },
            {
              "name": "super_switch",
              "type": "status",
              "quickEdit": false,
              "label": "非表单项"
            }
          ]
        }
      ]
    },
    {
      "type": "form",
      "debug": true,
      "mode": "horizontal",
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
      "body": [
        {
          "type": "input-text",
          "label": "父级文本框",
          "name": "super_text",
          "value": "123"
        },
        {
          "type": "combo",
          "name": "combo2",
          "label": "可获取父级数据",
          "multiple": true,
          "canAccessSuperData": true,
          "items": [
            {
              "name": "super_text",
              "type": "input-text"
            }
          ]
        }
      ]
    }
  ]
}

// combo的canAccessSuperData为true时，可以获取父级数据
const demo1 = {
  "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "mode": "horizontal",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "body": [
      {
        "type": "input-text",
        "label": "父级文本框",
        "name": "super_text",
        "value": "123"
      },
      {
        "type": "combo",
        "name": "combo2",
        "label": "可获取父级数据",
        "multiple": true,
        "canAccessSuperData": true,
        "items": [
          {
            "name": "super_text",
            "type": "input-text"
          }
        ]
      }
    ]
  }
}

// column使用value变量问题验证
const demo2 = {
  "type": "page",
  "body": [
    {
      "type": "form",
      "debug": true,
      "api": "/api/mock2/form/saveForm",
      "data": {
        "table": [
          {
            "a": "a1",
            "b": "b1"
          },
          {
            "a": "a2",
            "b": "b2"
          },
          {
            "a": "a3",
            "b": "b3"
          }
        ]
      },
      "actions": [
        {
          "type": "reset",
          "label": "Reset"
        },
        {
          "type": "submit",
          "label": "Submit"
        }
      ],
      "body": [
        {
          "type": "input-table",
          "name": "table",
          "label": "Table",
          "addable": true,
          "needConfirm": false,
          "columns": [
            {
              "label": "A",
              "name": "a",
              "type": "input-text"
            },
            {
              "label": "B",
              "name": "b",
              "type": "select",
              "options": [
                "b1",
                "b2",
                "b3"
              ]
            },
            {
              "label": "C",
              "name": "c",
              "value": "${a}",
              "type": "input-text"
            }
          ]
        }
      ]
    }
  ]
}

export default demo;
