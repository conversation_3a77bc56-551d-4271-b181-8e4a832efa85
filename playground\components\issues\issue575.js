const editDemo = {
  "type": "page",
  "body": {
    "type": "form",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "body": [
      {
        "type": "editor",
        "name": "editor",
        "label": "编辑器",
        "options": {
          "lineNumbers": "off"
        },
        "editorDidMount": (editor, monaco) => {
          console.log('editorDidMount', editor, monaco)
          // editor 是 monaco 实例，monaco 是全局的名称空间
          const dispose = monaco.languages.registerCompletionItemProvider('myLan', {
              /// 其他细节参考 monaco 手册
          });

          // 如果返回一个函数，这个函数会在编辑器组件卸载的时候调用，主要用于清理资源
          return dispose;
        }
      }
    ]
  }
}

export default editDemo
