const demo = {
  "type": "page",
  "body": {
    "type": "crud",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
    "syncLocation": false,
    // "filterFormAdvanceSearchAble": true,
    "filter": {
      "debug": true,
      "title": "",
      "id": "page-crud",
      "body": [
        {
          "type": "input-text",
          "name": "keywords",
          "label": "关键字",
          "clearable": true,
          "placeholder": "通过关键字搜索",
          "columnRatio": 4
        },
        {
          "type": "input-text",
          "name": "engine",
          "label": "Engine",
          "clearable": true,
          "columnRatio": 4
        }
      ],
      "actions": [
        {
          "type": "button",
          "label": "clear",
          "onEvent": {
            "click": {
              "actions": [
                {
                  "actionType": "clear",
                  "componentId": "page-crud"
                }
              ]
            }
          }
        },
        {
          "type": "button",
          "label": "reset",
          "onEvent": {
            "click": {
              "actions": [
                {
                  "actionType": "reset",
                  "componentId": "page-crud"
                }
              ]
            }
          }
        },
        {
          "type": "submit",
          "level": "primary",
          "label": "查 询"
        }
      ]
    },
    "columns": [
      {
        "name": "id",
        "label": "ID"
      },
      {
        "name": "engine",
        "label": "Rendering engine"
      },
      {
        "name": "platform",
        "label": "platform"
      },
      {
        "name": "version",
        "label": "version",
        "searchable": true
      }
    ]
  }
}

// url中的name参数清空问题
const demo1 = {
  "type": "page",
  "body": {
    "type": "crud",
    "name": "crud",
    "syncLocation": false,
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4?code=${code}",
    "topToolbar": [
      {
        "type": "button",
        "label": "新建",
        "actionType": "url",
        "url": "/dataseeddesigndocui/#/amis/zh-CN/course/index",
        "level": "primary",
        "blank": false
      }
    ],
    "filter": {
      "debug": true,
      "title": "条件搜索",
      "body": [
        {
          "type": "group",
          "mode": "horizontal",
          "body": [
            {
              "type": "input-text",
              "name": "code",
              "label": "关键字",
              "clearable": true,
              "placeholder": "通过关键字搜索",
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "engine",
              "label": "Engine",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "platform",
              "label": "Platform",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "keywords1",
              "label": "关键字1",
              "clearable": true,
              "placeholder": "通过关键字搜索",
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "engine1",
              "label": "Engine1",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "platform1",
              "label": "Platform1",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "keywords2",
              "label": "关键字2",
              "clearable": true,
              "placeholder": "通过关键字搜索",
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "engine2",
              "label": "Engine2",
              "clearable": true,
              "columnRatio": 4
            },
            {
              "type": "input-text",
              "name": "platform2",
              "label": "Platform2",
              "clearable": true,
              "columnRatio": 4
            }
          ]
        }
      ],
      "actions": [
        {
          "type": "button",
          "actionType": "clear",
          "label": "清空"
        },
        {
          "type": "reset",
          "label": "重 置"
        },
        {
          "type": "submit",
          "level": "primary",
          "label": "查 询"
        }
      ]
    },
    "columns": [
      {
        "name": "id",
        "label": "ID"
      },
      {
        "name": "engine",
        "label": "Rendering engine",
        "headSearchable": true
      },
      {
        "name": "browser",
        "label": "Browser",
        "headSearchable": {
          "type": "input-text",
          "name": "browser",
          "label": "Browser"
        }
      },
      {
        "name": "platform",
        "label": "Platform(s)",
        "canAccessSuperData": false
      },
      {
        "name": "version",
        "label": "Engine version"
      },
      {
        "name": "grade",
        "label": "CSS grade"
      }
    ]
  }
}

// 清空bug
const clearActionBug = {
  "type": "page",
  "className": "bg-light",
  "body": {
    "type": "crud",
    "syncLocation": false,
    "columnsTogglable": false,
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4",
    "columns": [
      {
        "name": "engine",
        "label": "Rendering engine",
        "headSearchable": {
          "type": "input-group",
          "label": "数据保留规则",
          "body": [
            {
              "type": "select",
              "name": "unit",
              "options": [
                {
                  "label": "月",
                  "value": "month"
                },
                {
                  "label": "年",
                  "value": "year"
                }
              ]
            },
            {
              "type": "input-tag",
              "name": "engine",
              "label": "数据保留规则",
              "placeholder": "数据保留规则"
            }
          ]
        }
      },
      {
        "name": "browser",
        "label": "Browser"
      },
      {
        "name": "platform",
        "label": "Platform(s)",
        "headSearchable": {
          "type": "input-text",
          "name": "platform3",
          "label": "Platform(s)"
        }
      }
    ]
  }
}

export default demo;
