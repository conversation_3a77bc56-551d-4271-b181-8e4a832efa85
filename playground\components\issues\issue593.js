// columnsTogglable 无效
const demo = {
  "type": "page",
  "body": {
    "type": "service",
    "data": {
      "rows": [
        {
          "engine": "Trident",
          "browser": "Internet Explorer 4.0",
          "platform": "Win 95+",
          "version": "4",
          "grade": "X",
          "children": [
            {
              "engine": "Trident",
              "browser": "Internet Explorer 4.0",
              "platform": "Win 95+",
              "version": "4",
              "grade": "X",
              "id": 1001
            },
            {
              "engine": "Trident",
              "browser": "Internet Explorer 5.0",
              "platform": "Win 95+",
              "version": "5",
              "grade": "C",
              "id": 1002
            }
          ],
          "id": 1
        },
        {
          "engine": "Trident",
          "browser": "Internet Explorer 5.0",
          "platform": "Win 95+",
          "version": "5",
          "grade": "C",
          "children": [
            {
              "engine": "Trident",
              "browser": "Internet Explorer 4.0",
              "platform": "Win 95+",
              "version": "4",
              "grade": "X",
              "id": 2001
            },
            {
              "engine": "Trident",
              "browser": "Internet Explorer 5.0",
              "platform": "Win 95+",
              "version": "5",
              "grade": "C",
              "id": 2002
            }
          ],
          "id": 2
        },
        {
          "engine": "Trident",
          "browser": "Internet Explorer 5.5",
          "platform": "Win 95+",
          "version": "5.5",
          "grade": "A",
          "id": 3,
          "children": [
            {
              "engine": "Trident",
              "browser": "Internet Explorer 4.0",
              "platform": "Win 95+",
              "version": "4",
              "grade": "X",
              "id": 3001
            },
            {
              "engine": "Trident",
              "browser": "Internet Explorer 5.0",
              "platform": "Win 95+",
              "version": "5",
              "grade": "C",
              "id": 3002
            }
          ]
        },
        {
          "engine": "Trident",
          "browser": "Internet Explorer 6",
          "platform": "Win 98+",
          "version": "6",
          "grade": "A",
          "id": 4,
          "children": [
            {
              "engine": "Trident",
              "browser": "Internet Explorer 4.0",
              "platform": "Win 95+",
              "version": "4",
              "grade": "X",
              "id": 4001
            },
            {
              "engine": "Trident",
              "browser": "Internet Explorer 5.0",
              "platform": "Win 95+",
              "version": "5",
              "grade": "C",
              "id": 4002
            }
          ]
        }
      ]
    },
    "body": [
      {
        "type": "input-table",
        "className": "m-b-none",
        "columns": [
          {
            "name": "id",
            "label": "ID",
          },
          {
            "name": "engine",
            "label": "Engine",
          },
          {
            "name": "grade",
            "label": "Grade",
          },
          {
            "name": "version",
            "label": "Version",
          },
          {
            "name": "browser",
            "label": "Browser",
          },
          {
            "name": "platform",
            "label": "Platform",
          }
        ],
        "minLength": 0,
        "addable": true,
        "name": "rows",
        "columnsTogglable": true,
        "editable": true,
        "showIndex": false,
        "subTable": {
          "type": "input-table",
          "columnsTogglable": true,
          // "source": "$children",
          "columns": [
            {
              "name": "id",
              "label": "ID",
            },
            {
              "name": "platform",
              "label": "Platform",
            }
          ]
        }
      }
    ]
  }
};

const demo1 = {
  "type": "page",
  "body": {
    "type": "form",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "debug": true,
    "data": {
      "rows": [
        {
          "engine": "Trident",
          "browser": "Internet Explorer 4.0",
          "platform": "Win 95+",
          "version": "4",
          "grade": "X",
          "id": 1
        },
        {
          "engine": "Trident",
          "browser": "Internet Explorer 5.0",
          "platform": "Win 95+",
          "version": "5",
          "grade": "C",
          "id": 2
        },
        {
          "engine": "Trident",
          "browser": "Internet Explorer 5.5",
          "platform": "Win 95+",
          "version": "5.5",
          "grade": "A",
          "id": 3,
        },
        {
          "engine": "Trident",
          "browser": "Internet Explorer 6",
          "platform": "Win 98+",
          "version": "6",
          "grade": "A",
          "id": 4,
        }
      ]
    },
    "body": [
      {
        "type": "input-table",
        "className": "m-b-none",
        "clearValueOnHidden": true,
        "columns": [
          {
            "name": "id",
            "label": "ID",
            "clearValueOnHidden": true
          },
          {
            "name": "engine",
            "label": "Engine",
          },
          {
            "name": "grade",
            "label": "Grade",
          },
          {
            "name": "version",
            "label": "Version",
          },
          {
            "name": "browser",
            "label": "Browser",
          },
          {
            "name": "platform",
            "label": "Platform",
          }
        ],
        "minLength": 0,
        "addable": true,
        "name": "rows",
        "needConfirm": false,
        "columnsTogglable": true,
        "editable": true,
        "showIndex": false,
      }
    ]
  }
};

const demo2 = {
  "type": "page",
  "className": "bg-light",
  "body": {
    "type": "crud",
    "className": "pm-curdTable-model pm-crud",
    "syncLocation": false,
    "columnsTogglable": false,
    "autoGenerateFilter": {
      "showBtnToolbar": false,
      "defaultExpanded": false
    },
    "headerToolbar": [
      {
        "type": "tpl",
        "tpl": "一共有${count}条数据"
      }
    ],
    "footerToolbar": [
      {
        "type": "pagination",
        "layout": "pager,perPage,go"
      }
    ],
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/operatelog",
     "headerFilter": {
      "body": [
        {
          "type": "button-group-select",
          "name": "pluginType",
          "options": [
            {
              "value": "all",
              "label": "全部插件"
            },
            {
              "value": "forme",
              "label": "我的插件"
            }
          ]
        }
      ]
    },
    "showExpansionColumn": false,
    "columns": [
      {
        "name": "updateBy",
        "label": "更新人",
      },
      {
        "name": "updateDate",
        "label": "更新时间",
        "format": "YYYY-MM-DD HH:mm:ss",
        "valueFormat": "x",
        "type": "date",
      },
      {
        "label": "更新内容",
        "type": "container",
        "body": [
          {
            "type": "button",
            "linkWithoutPadding": true,
            "level": "link",
            "className": "h-0 ",
            "label": "展开",
            "icon": "fas fa-angle-down",
            "visibleOn": "${!_amisExpanded}",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "actionType": "custom",
                    "script": "context.props.row.toggleExpanded()"
                  }
                ]
              }
            }
          },
          {
            "type": "button",
            "linkWithoutPadding": true,
            "level": "link",
            "className": "h-0 ",
            "label": "收起",
            "icon": "fas fa-angle-up",
            "visibleOn": "${_amisExpanded}",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "actionType": "custom",
                    "script": "context.props.row.toggleExpanded()"
                  }
                ]
              }
            }
          }
        ]
      }
    ],
    "subTable": {
      "type": "table",
      "source": "$updateFileds",
      "showExpansionColumn": false,
      // "headerFilter": false,
      "headerFilterRender": false,
      "columns": [
        {
          "name": "fieldName",
          "label": "字段名"
        },
        {
          "name": "baselineVersion",
          "label": "更新前"
        },
      ]
    }
  }
}


export default demo;
