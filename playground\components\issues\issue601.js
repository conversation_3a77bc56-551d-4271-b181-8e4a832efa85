const demo = {
  type: 'page',
  data: {
    list1: [
      {
        id: 1,
        name: 'hason',
        age: 14,
        email: '<EMAIL>',
        phone: '123456789',
      },
      {
        id: 2,
        name: 'raj',
        age: 43,
        email: '<EMAIL>',
        phone: '81248912',
      },
      {
        id: 3,
        name: 'pumm',
        age: 34,
        email: '<EMAIL>',
        phone: '742197',
      },
      {
        id: 4,
        name: 'saalam',
        age: 3,
        email: '<EMAIL>',
        phone: '7832882',
      },
      {
        id: 5,
        name: 'maasid',
        age: 93,
        email: '<EMAIL>',
        phone: '112381241',
      },
    ],
    list2: [
      {
        u_id: 11,
        u_name: 'hason',
        u_age: 14,
        u_email: '<EMAIL>',
        u_phone: '123456789',
      },
      {
        u_id: 22,
        u_name: 'raj',
        u_age: 43,
        u_email: '<EMAIL>',
        u_phone: '81248912',
      },
      {
        u_id: 33,
        u_name: 'pumm',
        u_age: 34,
        u_email: '<EMAIL>',
        u_phone: '742197',
      },
      {
        u_id: 44,
        u_name: 'saalam',
        u_age: 3,
        u_email: '<EMAIL>',
        u_phone: '7832882',
      },
      {
        u_id: 55,
        u_name: 'maasid',
        u_age: 93,
        u_email: '<EMAIL>',
        u_phone: '112381241',
      },
    ],
    "text1": "ddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddd"
  },
  body: [
    {
      label: '详情',
      type: 'button',
      level: 'link',
      actionType: 'dialog',
      dialog: {
        title: '详情',
        body: {
          type: 'crud',
          //api:"/mock/demo/list/1",
          source: '${list1}',
          columnsTogglable: true,
          syncLocation: false,
          id: 'crudId1',
          columns: [
            {
              data: {
                myLabel: 'u_id',
              },
              name: 'u_id',
              label: 'u_id',
            },
            {
              name: 'u_name',
              label: 'u_name',
            },
            {
              name: 'u_email',
              label: 'u_email',
            },
            {
              name: 'u_phone',
              label: 'u_phone',
            },
            {
              name: 'u_age',
              label: 'u_age',
            },
          ],
        },
      },
    },
    {
      label: '详情2',
      type: 'button',
      level: 'link',
      actionType: 'dialog',
      dialog: {
        title: '详情2',
        actions: [],
        body: {
          // type: 'tpl',
          // tpl: '详情2',
          type: 'crud',
          // api:"/mock/demo/list/1",
          source: '${list2}',
          columnsTogglable: true,
          syncLocation: false,
          columns: [
            {
              name: 'u_id',
              label: 'u_id',
            },
            {
              name: 'u_name',
              label: 'u_name',
            },
            {
              name: 'u_email',
              label: 'u_email',
            },
            {
              name: 'u_phone',
              label: 'u_phone',
            },
            {
              name: 'u_age',
              label: 'u_age',
            },
          ],
        },
      },
    },
    {
    type: 'crud',
    //api:"/mock/demo/list/0",
    source: '${list1}',
    columnsTogglable: true,
    syncLocation: false,
    columns: [
      {
        name: 'id',
        label: 'id',
      },
      {
        name: 'name',
        label: 'name',
      },
      {
        name: 'email',
        label: 'email',
      },
      {
        type: 'operation',
        label: '操作',
        buttons: [
          {
            label: '详情',
            type: 'button',
            level: 'link',
            actionType: 'dialog',
            dialog: {
              title: '详情',
              body: {
                type: 'crud',
                //api:"/mock/demo/list/1",
                id: 'crudc1',
                source: '${list1}',
                columnsTogglable: true,
                syncLocation: false,
                columns: [
                  {
                    data: {
                      myLabel: 'u_id',
                    },
                    name: 'u_id',
                    label: 'u_id',
                  },
                  {
                    name: 'u_name',
                    label: 'u_name',
                  },
                  {
                    name: 'u_email',
                    label: 'u_email',
                  },
                  {
                    name: 'u_phone',
                    label: 'u_phone',
                  },
                  {
                    name: 'u_age',
                    label: 'u_age',
                  },
                ],
              },
            },
          },
          {
            label: '详情2',
            type: 'button',
            level: 'link',
            actionType: 'dialog',
            dialog: {
              title: '详情2',
              actions: [],
              body: {
                // type: 'tpl',
                // tpl: '详情2',
                type: 'crud',
                // api:"/mock/demo/list/1",
                source: '${list2}',
                columnsTogglable: true,
                syncLocation: false,
                columns: [
                  {
                    name: 'u_id',
                    label: 'u_id',
                  },
                  {
                    name: 'u_name',
                    label: 'u_name',
                  },
                  {
                    name: 'u_email',
                    label: 'u_email',
                  },
                  {
                    name: 'u_phone',
                    label: 'u_phone',
                  },
                  {
                    name: 'u_age',
                    label: 'u_age',
                  },
                ],
              },
            },
          },
          {
            type: 'button',
            level: 'link',
            label: '编辑',
            onEvent: {
              click: {
                actions: [
                  {
                    actionType: 'custom',
                    script: (context, doAction, event) => {
                      const data = event?.data?.originData || {};
                      doAction([
                        {
                          actionType: 'dialog',
                          dialog: {
                            title: '详情2',
                            actions: [],
                            body: {
                              type: 'crud',
                              source: '${list2}',
                              columnsTogglable: true,
                              syncLocation: false,
                              columns: [
                                {
                                  name: 'u_id',
                                  label: 'u_id',
                                },
                              ],
                            },
                          },
                        },
                      ]);
                    },
                  },
                ],
              },
            },
          }
        ],
      },
    ],
  },
  ]
};

export default demo;
