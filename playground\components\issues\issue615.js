import { registerFilter } from 'amis';
import omit from 'lodash/omit';

registerFilter('draggableFilter', (query, pristineQuery) => {
  console.log('draggableFilter', query, pristineQuery);
  // 对比query和pristineQuery，如果query中存在和pristineQuery不相等的字段，则返回false
  const disableDraggable = Object.keys(omit(query, ['page', 'perPage'])).some(key => {
    // 如果query[key]为空串，则不判断
    return query[key] !== '' && query[key] !== pristineQuery[key];
  });
  console.log('disableDraggable', disableDraggable);
  return !disableDraggable;
})

const demo = {
  "type": "page",
  "body": {
    "type": "crud",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
    "syncLocation": false,
    "draggable": "${__query|draggableFilter:${__pristineQuery}}",
    // "draggable": "auto",
    "defaultParams": {
      "test": "test",
      "perPage": 6
    },
    // pageField: "page2",
    "filter": {
      "debug": true,
      "title": "",
      "body": [
        {
          "type": "input-text",
          "name": "keywords",
          "label": "关键字",
          "clearable": true,
          "columnRatio": 4
        },
        {
          "type": "input-text",
          "name": "engine",
          "label": "Engine",
          "clearable": true,
          "columnRatio": 4
        },
        {
          "type": "input-text",
          "name": "platform",
          "label": "Platform",
          "clearable": true,
          "columnRatio": 4
        },
      ],
      "actions": [
        {
          "type": "reset",
          "label": "重 置"
        },
        {
          "type": "submit",
          "level": "primary",
          "label": "查 询"
        }
      ]
    },
    "columns": [
      {
        "name": "id",
        "label": "ID"
      },
      {
        "name": "engine",
        "label": "Rendering engine"
      },
      {
        "name": "browser",
        "label": "Browser"
      },
    ]
  }
}

export default demo;
