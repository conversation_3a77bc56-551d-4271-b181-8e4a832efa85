const options = [
  {
    label: 'A',
    value: 'api',
  },
  {
    label: 'B',
    value: 'b',
  },
  {
    label: 'C',
    value: 'c',
  },
]

const treeOptions = [
  {
    label: 'api接口',
    value: 'api',
  },
  {
    label: '默认分组',
    value: 'group',
  }
]

const demo = {
  type: 'page',
  id: 'myPage',
  name: 'apiPage',
  data: {
    appSelect: '${appSelect}', // 方案2：注释appSelect
    nodeCode: "${nodeCode|default:'api'}",
    nodeType: "${nodeType|default:'DIR'}",
  },
  onEvent: {
    init: {
      actions: [
        {
          actionType: 'custom',
          script: (context, doAction, event) => {
            const {data = {}} = event;
            const {appSelect} = data.__super || {};
            const defaultAppSelect =
              window.localStorage.getItem('defaultAppSelect');

            if (!appSelect && defaultAppSelect) {
              console.log('init', defaultAppSelect);
              // 页面初始化后，从localStorage中获取默认应用
              doAction([
                {
                  actionType: 'setValue',
                  componentId: 'myPage',
                  args: {
                    value: {
                      appSelect: defaultAppSelect,
                      nodeCode: 'api',
                      nodeType: 'DIR'
                    },
                  },
                },
              ]);
            }
          },
        },
      ],
    },
  },
  body: [
    // {
      // type: 'form',
      // body: [
        {
          label: '应用',
          type: 'select',
          name: 'appSelect',
          placeholder: '请选择应用',
          options,
          onEvent: {
            change: {
              actions: [
                {
                  actionType: 'toast', // 执行toast提示动作
                  args: {
                    msgType: 'info',
                    msg: '派发点击事件',
                  },
                },
                {
                  actionType: 'setValue',
                  componentId: 'myPage',
                  args: {
                    value: {
                      appSelect: '${appSelect}',
                      nodeCode: 'api',
                    },
                  },
                },
                {
                  actionType: 'custom',
                  script: (context, doAction, event) => {
                    const {data: {appSelect} = {}} = event;
                    const {pathname, search} = window.location;
                    window.localStorage.setItem('defaultAppSelect', appSelect);
                    // window.location.href = `${pathname}${search}&appSelect=${appSelect}`;
                  },
                },
              ],
            },
          },
        },
        {
          type: 'input-tree',
          label: 'apiTree',
          name: 'apiTree', // 方案4：name: apiTree → nodeCode
          value: "${nodeCode|default:'api'}", // 方案4：注释value
          placeholder: '请选择树节点',
          // joinValues: false,
          options: treeOptions,
          onEvent: {
            change: {
              actions: [
                {
                  actionType: "toast", // 执行toast提示动作
                  args: {
                    msgType: "info",
                    msg: "派发change事件",
                  },
                },
                {
                  actionType: 'setValue',
                  componentId: 'myPage',
                  args: {
                    value: {
                      nodeCode: '${value}',
                    },
                  },
                },
              ]
            }
          }
        },
      // ]
    // },
    {
      type: 'divider',
    },
    {
      type: 'crud',
      // id: 'crudId',
      // updatePristineAfterStoreDataReInit: true, // 方案1：updatePristineAfterStoreDataReInit设置true
      api: {
        method: 'get',
        data: {
          "&": "$$",
          apiId: "${apiId$}",
        },
        sendOn: '${appSelect}',
        url: 'https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=5&context=${appSelect}&folderId=${nodeCode}&nodeType=${nodeType}&waitSeconds=0',
      },
      // api: 'https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=5',
      defaultParams: {
        perPage: 5,
      },
      title: "appSelect值: ${appSelect == '' ? '空串' : appSelect}",
      // syncLocation: false,
      visibleOn: "nodeType === 'DIR' && !['-5', '-6'].includes(nodeCode)",
      autoGenerateFilter: {
        showBtnToolbar: false,
        defaultExpanded: false,
      },
      columns: [
        {
          name: 'id',
          label: 'ID',
        },
        {
          name: 'engine',
          label: 'Engine',
          searchable: true,
        },
        {
          name: 'browser',
          label: 'Browser',
        },
      ],
    },
  ],
};

export default demo;
