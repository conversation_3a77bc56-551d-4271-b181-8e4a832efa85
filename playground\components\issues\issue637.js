const demo = {
  "type": "page",
  "initApi": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/page/initData?waitSeconds=5",
  "body": {
    "type": "crud",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
    "syncLocation": false,
    "trackExpression": "${date}",
    "columns": [
      {
        "name": "id",
        "label": "ID"
      },
      {
        "label": "Rendering engine",
        "type": "tpl",
        "tpl": "${date}",
      }
    ]
  }
}

const demo2 = {
  "type": "page",
  "id": "job-list-page",
  "initApi": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/page/initData?waitSeconds=5",
  "body": [
    {
      "type": "crud",
      "updateAllRows": true,
      "trackExpression": "${date}",
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
      "syncLocation": false,
      "columns": [
        {
          "name": "id",
          "label": "ID",
        },
        {
          "name": "date",
          "label": "测试时间",
        },
        {
          "type": "operation",
          "label": "操作",
          "buttons": [
            {
              "type": "button",
              "level": "link",
              "label": "编辑",
              "actionType": "dialog",
              "dialog": {
                "size": "lg",
                "title": "编辑作业",
                "showCloseButton": false,
                "id": "add-dialog",
                "showErrorMsg": false,
                "body": [
                  {
                    "type": "form",
                    "id": "add-form",
                    "body": [
                      {
                        "type": "group",
                        "body": [
                          {
                            "type": "tpl",
                            "tpl": "当前时间：${date}"
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            }
          ]
        }
      ]
    }
  ]
}

export default demo;
