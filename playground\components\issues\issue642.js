const demo = {
  "type": "page",
  "body": {
    "type": "form",
    "id": "dz_modal_form_id",
    "debug": "true",
    "data": {
      "table": [
        {
          "a": "a1",
          "b": "1230",
          "c": "123",
          "d": "123"
        }
      ]
    },
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "body": [
      {
        "type": "input-table",
        "name": "table",
        "columnsTogglable": false,
        "needConfirm": false,
        // "reUseRow": "match",
        "updateAllRows": true,
        "columns": [
          // {
          //   "name": "a",
          //   "label": "A"
          // },
          {
            "name": "b",
            "label": "B"
          },
          {
            "name": "c",
            "label": "C",
            "type": "input-text"
          }
        ],
        "onEvent": {
          "change": {
            "actions": [
              {
                "actionType": "custom",
                "preventDefault": true,
                "script": (context, doAction, event) => {
                  const testValue = event.data?.value?.map((item) => ({ ...item, b: String(Number(item.c) * 10) }));
                  doAction({
                    actionType: 'setValue',
                    // preventDefault: true,
                    componentId: 'dz_modal_form_id',
                    args: { value: { table: testValue } }
                  })
                }
              },
              {
                // "actionType": "custom",
                // "script": "const testValue = event.data.value.map((item) => ({...item, b: item.c})); doAction({'actionType': 'setValue', 'preventDefault': true, 'componentId': 'dz_modal_form_id', 'args': {'value': {'table': testValue}}})",
                // "preventDefault": true
              },
              // {
              //   "actionType": "toast",
              //   "args": {
              //     "msg": "success"
              //   }
              // }
            ]
          }
        },
      }
    ]
  }
}

export default demo;
