const demo = {
  "type": "page",
  "body": {
    "type": "form",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "body": [
      {
        "type": "input-tree",
        "name": "tree",
        "label": "Tree",
        "creatable": true,
        "editable": true,
        "removable": true,
        "deferApi": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/deferOptions?label=${label}&waitSeconds=0",
        "addApi": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm?waitSeconds=0",
        "options": [
          {
            "label": "Folder A",
            "value": 1,
            "collapsed": true,
            "children": [
              {
                "label": "file A",
                "value": 2
              },
              {
                "label": "file B",
                "value": 3
              }
            ]
          },
          {
            "label": "这下面是懒加载的",
            "value": 4,
            "defer": true,
          },
          {
            "label": "Folder C",
            "value": 5,
            "children": [
              {
                "label": "file C",
                "value": 51
              },
              {
                "label": "file B",
                "value": 52
              }
            ]
          },
          {
            "label": "file E",
            "value": 6
          }
        ]
      }
    ]
  }
}


const sticky = {
  "type": "page",
  "data": {
    "table": [
      {
        "id": 1,
        "version": "3",
        "parseSceneRuleList": [
          "第一列",
          "第二列第二列第二列第二列第二列第二列第二列第二列第二列第二列第二列第二列"
        ]
      },
      {
        "id": 2,
        "version": "3",
        "parseSceneRuleList": [
          "第一列",
          "第二列"
        ]
      }
    ]
  },
  "body": [
    {
      "type": "table",
      "source": "${table}",
      "syncLocation": false,
      "columns": [
        {
          "name": "id",
          "label": "ID"
        },
        {
          "name": "version",
          "label": "version版本"
        },
        {
          "label": "匹配规则",
          "width": 300,
          "name": "parseSceneRuleList",
          "type": "each",
          "items": {
            "type": "tooltip-wrapper",
            "className": "mb-1",
            "placement": "right",
            "content": "${item}",
            "body": {
              "type": "tag",
              "displayMode": "bordered",
              "className": "max-w-full w-full justify-start",
              "label": "${item}"
            }
          }
        }
      ]
    }
  ]
}

export default sticky;
