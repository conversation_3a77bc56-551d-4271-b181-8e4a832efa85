
const simpleDemo = {
  "type": "page",
  "body": {
    "type": "form",
    "debug": "true",
    "data": {
      "table": [
        {
          "a": "a1",
          "b": "b1",
          // "c": "c"
        }
      ]
    },
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "label": "a",
        "type": "input-text",
        "name": "a",
        "required": true
      },
      {
        "type": "input-table",
        "name": "table",
        "columns": [
          {
            "name": "a",
            "label": "A"
          },
          {
            "name": "b",
            "label": "B"
          },
          {
            "label": "bb",
            "type": "input-text",
            "name": "c",
            "required": true
          }
        ]
      }
    ]
  }
}

// ⚠️测试必填comfirm ✅
const demo = {
  "type": "page",
  "body": {
    "type": "form",
    "data": {
      "table": [
        {
          "a": "a1",
          "b": "b1"
        },
        {
          "a": "a2",
          "b": "b2"
        },
        {
          "a": "a3",
          "b": "b3"
        }
      ]
    },
    "api": "/amis/api/mock2/form/saveForm",
    "body": [
      {
        "showIndex": true,
        "type": "input-table",
        "name": "table",
        "addable": true,
        "columns": [
          {
            "name": "a",
            "label": "A",
            "quickEdit": {
              "type": "input-text",
              "required": true
            }
          },
          {
            "name": "b",
            "label": "B"
          }
        ],
        "needConfirm": true
      }
    ]
  }
}

// 测试canAccessSuperData
const demo1 = {
  "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "mode": "horizontal",
    "data": {
      "list": [
        {},
        {}
      ]
    },
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type": "input-text",
        "label": "父级文本框",
        "name": "super_text",
        "value": "123"
      },
      {
        "type": "switch",
        "label": "父级勾选框",
        "name": "super_switch",
        "value": false
      },
      {
        "type": "input-table",
        "name": "list",
        "label": "可获取父级数据",
        "addable": true,
        "needConfirm": false,
        "canAccessSuperData": true,
        "strictMode": false,
        "updateAllRows": true,
        // "value": [
        //   {}
        // ],
        "columns": [
          {
            "name": "super_text",
            "type": "text",
            "label": "表单项",
            "quickEdit": {
              "disabledOn": "this.super_switch"
            }
          },
          {
            "name": "super_switch",
            "type": "status",
            "quickEdit": false,
            "label": "非表单项"
          }
        ]
      }
    ]
  }
}

// 测试子表格
const demo2 = {
  "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "data": {
      "table": [
          {
            "a": "a1",
            "b": "b1",
            "id": 1,
            "children": [
              {
                "a": "a1-child1",
                "b": "b1-child1",
                "id": "1-1"
              },
              {
                "a": "a1-child2",
                "b": "b1-child2",
                "id": "1-2"
              }
            ]
          },
          {
            "a": "a2",
            "b": "b2",
            "id": 2,
            "children": [
              {
                "a": "a1-child1",
                "b": "b1-child1",
                "id": "2-1"
              },
              {
                "a": "a1-child2",
                "b": "b1-child2",
                "id": "2-2"
              }
            ]
          },
          {
            "a": "a3",
            "b": "b3",
            "id": 3,
            "children": [
              {
                "a": "a1-child1",
                "b": "b1-child1",
                "id": "3-1"
              },
              {
                "a": "a1-child2",
                "b": "b1-child2",
                "id": "3-2"
              }
            ]
          }
        ]
    },
    "api": "/api/mock2/form/saveForm",
    "body": [
      {
        "type":"input-table",
        "name":"table",
        "valueField": "id",
        "columns":[
            {
              "name": "id",
              "label": "序号"
            },
            {
              "name": "a",
              "label": "A",
              "type": "input-text"
            },
            {
              "name": "b",
              "label": "B",
              "type": "input-text"
            }
        ]
      }
    ]
  }
}

// ⚠️测试子表格+canAccessSuperData
const demo3 = {
  "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "mode": "horizontal",
    "api": "/api/mock2/form/saveForm",
    "data": {
      "table": [
        {
          "a": "a1",
          "b": "b1",
          "id": 1,
          "children": [
            {
              "a": "a1-child1",
              "b": "b1-child1",
              "id": "1-1"
            },
            {
              "a": "a1-child2",
              "b": "b1-child2",
              "id": "1-2"
            }
          ]
        },
        {
          "a": "a2",
          "b": "b2",
          "id": 2,
          "children": [
            {
              "a": "a1-child1",
              "b": "b1-child1",
              "id": "2-1"
            },
            {
              "a": "a1-child2",
              "b": "b1-child2",
              "id": "2-2"
            }
          ]
        },
        {
          "a": "a3",
          "b": "b3",
          "id": 3,
          "children": [
            {
              "a": "a1-child1",
              "b": "b1-child1",
              "id": "3-1"
            },
            {
              "a": "a1-child2",
              "b": "b1-child2",
              "id": "3-2"
            }
          ]
        }
      ]
    },
    "body": [
      {
        "type": "input-text",
        "label": "父级文本框",
        "name": "super_text",
        "value": "123"
      },
      {
        "type": "switch",
        "label": "父级勾选框",
        "name": "super_switch",
        "value": false
      },
      {
        "type": "input-table",
        "name": "table",
        "label": "可获取父级数据",
        "addable": true,
        "needConfirm": false,
        "canAccessSuperData": true,
        "strictMode": false,
        "updateAllRows": true,
        "value": [
          {}
        ],
        "columns": [
          {
            "name": "super_text",
            "type": "text",
            "label": "表单项",
            "quickEdit": {
              "disabledOn": "this.super_switch"
            }
          },
          {
            "name": "super_switch",
            "type": "status",
            "quickEdit": false,
            "label": "非表单项"
          }
        ]
      }
    ]
  }
}

// 测试autoFill
const demo4 = {
  "type": "page",
  "body": [
    {
      "type": "form",
      "api": "/api/mock2/form/saveForm",
      "body": [
        {
          "type": "input-table",
          "name": "table",
          "label": "表格表单",
          "columns": [
            {
              "label": "名称",
              "name": "name",
              "quickEdit": {
                "type": "input-text",
                "name": "name",
                "id": "u:514910e73695"
              },
              "id": "u:97d119520d7c"
            },
            {
              "label": "等级",
              "name": "level",
              "quickEdit": {
                "type": "select",
                "name": "level",
                "autoFill": {
                  "id": "$id"
                },
                "id": "u:38014752298b",
                "options": [
                  {
                    "label": "a",
                    "value": "111",
                    "id": 111
                  },
                  {
                    "label": "a1",
                    "value": "1121",
                    "id": 222
                  }
                ]
              },
              "id": "u:bc682229ad4f"
            }
          ],
          "addable": true,
          "footerAddBtn": {
            "label": "新增",
            "icon": "fa fa-plus",
            "id": "u:a0d2d9eab4f7"
          },
          "strictMode": true,
          "id": "u:c296ba75753c",
          "minLength": 0,
          "editable": true,
          "removable": true
        }
      ],
      "id": "u:a2f24ee3ab2d",
      "debug": true
    }
  ],
  "id": "u:09eedced8bb6",
}


// setState回调示例
const demo5 = {
  "type": "page",
  "data": {
    "dollar": 10,
    "table": [
      {
        "score1": 2
      },
      {
        "score1": 3
      },
      {
        "score1": 4
      }
    ],
    "title": "获取衍生特征值时，需传入以下参数，请赋值:"
  },
  "body": {
    "title": "",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
    "actions": [],
    "body": [
      {
        "type": "input-number",
        "name": "dollar",
      },
      {
        "type": "input-table",
        "name": "table",
        "label": "表格表单",
        "updateAllRows": true,
        "strictMode": false,
        "columns": [
          {
            "label": "名称",
            "name": "name",
            "type": "input-text"
          },
          {
            "label": "分数1",
            "name": "score1",
            "type": "input-number"
          },
          {
            "label": "分数",
            "type": "input-number",
            "name": "score",
            "value": "${score1 * dollar}"
          }
        ],
        "strictMode": false,
        "needConfirm": false,
        "canAccessSuperData": true
      }
    ],
    "type": "form"
  }
}

const demo6 = {
  "type": "page",
  "body": {
    "type": "form",
    "data": {
      "table": [
        {
          "a": "a1",
          "b": "b1"
        },
        {
          "a": "a2",
          "b": "b2"
        }
      ]
    },
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "body": [
      {
        "type": "input-table",
        "name": "table",
        "label": "Table",
        "columns": [
          {
            "label": "A",
            "name": "a",
            "type": "input-group",
            "body": [
              {
                "name": "a",
                "type": "input-text",
              }
            ]
          },
          {
            "label": "B",
            "name": "b"
          }
        ]
      }
    ]
  }
}

// input-group
const demo7 = {
  "type": "page",
  "body": {
    "type": "form",
    "data": {
      "table": [
        {
          "a": "a1",
          "b": "b1"
        },
        {
          "a": "a2",
          "b": "b2"
        }
      ]
    },
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "body": [
      {
        "type": "input-table",
        "name": "table",
        "label": "Table",
        "columns": [
          {
            "label": "A",
            "name": "a",
            type: "input-group",
            body: [
              {
                name: 'a',
                type: 'input-text',
              }
            ]
          },
          {
            "label": "B",
            "name": "b"
          }
        ]
      }
    ]
  }
}


/**  ======= 下面是大数据场景 ======= */
// 大数据表格
const baseUrl = '/riskbizopr/modelportal';
// 模型资产-资产明细-特征列表
const apiFeatureList = `${baseUrl}/modelAsset/version/feature/list`;
// 模型资产-列表查询
export const apiVersionList = `${baseUrl}/modelAsset/version/list`;

// 特征列表-特征code 枚举查询
const apiListFeatureInfo = '/featurestoreopr/featurestore/feature/listFeatureInfo';

const columnsType = 'input-group';

// 列表数据
const queryTableList = `${baseUrl}/modelAsset/query`;


// 长度校验规则
export const lengthRule = (length, lengthType) => {
  const desc = `长度限制为${length}个字符`;
  if (lengthType === 'rule') {
    return {
      type: 'string',
      max: length,
      message: desc,
    };
  }
  return {
    length,
    desc,
  };
};

// 特征列表-特征分类
const FEATURESOURCES_OPTIONS = [
  { label: '模型分', value: 'MODEL' },
  { label: '特征', value: 'FEATURE' },
  { label: '样本', value: 'SAMPLE' },
];

const commonTpl = {
  type: 'tpl',
  tpl: '-',
  visibleOn: '${featureSource === "SAMPLE"}',
  className: 'text-muted',
};

export const schema = {
  type: 'page',
  body: {
    type: 'form',
    debug: true,
    name: 'featureTableForm',
    id: 'featureTableForm',
    wrapWithPanel: false,
    showErrorMsg: false,
    initApi: {
      method: 'get',
      url: apiFeatureList,
      headers: {
        'X-TOKEN': localStorage.getItem('X-TOKEN'),
      },
      data: {
        pageSize: 10000,
        pageNum: 1,
        modelAssetKey: 'KNATIVE_DZ_TRADE_MOB1_202306_V2',
        modelAssetVersion: '1.0',
      },
      adaptor(payload) {
        if (!payload) {
          return {
            total: 0,
            rows: [],
          };
        }
        const { total = 0, list = [] } = payload || {};
        return {
          total,
          count: total,
          featureRows: list.map((item, index) => ({ ...item, keys: index })),
        };
      },
    },
    body: [
      {
        type: 'input-table',
        name: 'featureRows',
        valueField: 'keys',
        primaryField: 'keys',
        showErrorMsg: false,
        perPage: 5,
        // batchNum: 3,
        autoFillHeight: {
          maxHeight: 1000,
        },
        paginationConfig: {
          layout: 'total,pager,perPage',
          perPage: 10,
          perPageAvailable: [10, 20, 30, 50],
        },
        validateOnPageChange: true,
        columns: [
          {
            name: 'keys',
            label: '序号',
            showIndex: true,
            width: 30,
          },
          {
            name: 'inputKey',
            label: '模型特征key',
            type: 'input-text',
          },
          {
            name: 'inputType',
            label: '特征数据类型',
          },
          {
            label: '特征分类',
            width: 120,
            name: 'featureSource',
            type: columnsType,
            body: [
              {
                name: 'featureSource',
                type: 'select',
                placeholder: '请选择特征分类',
                options: FEATURESOURCES_OPTIONS,
                autoFill: {
                  featureVersion: undefined,
                  featureCode: undefined,
                  featureModelAssetKey: undefined,
                },
              }
            ]
          },
          {
            name: 'featureDefaultValue',
            label: '默认值',
            type: columnsType,
            body: [
              {
                placeholder: '请输入默认值',
                type: 'input-text',
                required: false,
                name: 'featureDefaultValue',
                validations: {
                  maxLength: lengthRule(100).length,
                },
                validationErrors: {
                  maxLength: lengthRule(100).desc,
                },
                visibleOn: '${featureSource !== "SAMPLE"}',
              },
              commonTpl,
            ],
            width: 200,
          },
          {
            name: 'featureSpecialValue',
            width: 200,
            label: '特殊值',
            type: columnsType,
            body: [
              {
                type: 'input-text',
                placeholder: '请输入特殊值',
                validations: {
                  maxLength: lengthRule(100).length,
                },
                validationErrors: {
                  maxLength: lengthRule(100).desc,
                },
                name: 'featureSpecialValue',
                visibleOn: '${featureSource !== "SAMPLE"}',
              },
              commonTpl,
            ],
          },
          {
            label: '特征CODE',
            width: 500,
            type: columnsType,
            name: 'featureCodeWrap',
            body: [
              {
                type: 'select',
                id: 'modelAssetKey',
                name: 'featureModelAssetKey',
                columnRatio: 6,
                visibleOn: '${featureSource === "MODEL"}',
                label: false,
                searchable: true,
                placeholder: '请选择模型',
                popOverContainer: 'body',
                itemHeight: 54,
                source: {
                  method: 'post',
                  url: queryTableList,
                  data: {
                    pageNum: 1,
                    pageSize: 10000,
                  },
                },
              },
              {
                type: 'select',
                name: 'featureVersion',
                label: false,
                columnRatio: 6,
                popOverContainer: 'body',
                placeholder: '请选择回溯资产版本',
                visibleOn: '${featureSource === "MODEL"}',
                clearable: true,
                searchable: true,
                source: {
                  method: 'get',
                  data: {
                    pageNum: 1,
                    pageSize: 10000,
                  },
                  sendOn: '${featureModelAssetKey}',
                  url: `${apiVersionList}?modelAssetKey=\${featureModelAssetKey}`,
                },
              },
              {
                type: 'select',
                name: 'featureCode',
                columnRatio: 12,
                label: false,
                searchable: true,
                clearable: true,
                placeholder: '请选择特征CODE',
                popOverContainer: 'body',
                source: '${featureCodeList}',
                itemHeight: 54,
                autoComplete: {
                  method: 'post',
                  url: apiListFeatureInfo,
                  data: {
                    searchWord: '${term}',
                    pageNum: 1,
                    pageSize: 100,
                    direct: 'DESC',
                    sortBy: 'createdAt',
                    featureType: 'OFFLINE-TABLE,OFFLINE-MANUAL',
                  },
                  initFetchOn: false,
                  sendOn: '${term}',
                },
              },
              commonTpl,
            ],
          },
          {
            name: 'featureName',
            label: '特征名称',
            width: 200,
            body: [
              {
                type: 'tpl',
                tpl: '${featureName || "-"}',
                classNameExpr: '${featureName?"":"text-muted"}',
                visibleOn: '${featureSource !== "SAMPLE"}',
              },
              commonTpl,
            ],
          },
          {
            label: '回溯是否包含当天',
            type: columnsType,
            width: 120,
            validationConfig: {
              errorMode: 'full',
              errorMsgIndex: false,
            },
            validateOnChange: true,
            name: 'tracebackIncludeToday',
            body: [
              {
                name: 'tracebackIncludeToday',
                label: false,
                type: 'select',
                placeholder: '请选择回溯是否包含当天',
                requiredOn: '${featureSource !== "SAMPLE"}',
                visibleOn: '${featureSource === "FEATURE"}',
                options: [
                  {
                    label: '是',
                    value: '1',
                  },
                  {
                    label: '否',
                    value: '0',
                  },
                ],
                validateOnChange: true,
              },
              {
                type: 'tpl',
                tpl: '-',
                visibleOn:
                  '${featureSource === "MODEL" || featureSource === "SAMPLE"}',
                className: 'text-muted',
              },
            ],
          },
          {
            label: '回溯时间范围（天）',
            width: 180,
            type: columnsType,
            name: 'tracebackRange',
            validationConfig: {
              errorMode: 'full',
              errorMsgIndex: false,
            },
            validateOnChange: true,
            body: [
              {
                name: 'tracebackRange',
                placeholder: '请输入 [1,180] 的自然数',
                type: 'input-text',
                visibleOn: '${featureSource === "FEATURE"}',
                validateOnChange: true,
              },
              {
                type: 'tpl',
                tpl: '-',
                visibleOn:
                  '${featureSource === "MODEL" || featureSource === "SAMPLE"}',
                className: 'text-muted',
              },
            ],
          },
        ],
      },
    ],
  },
}


export default demo1;
