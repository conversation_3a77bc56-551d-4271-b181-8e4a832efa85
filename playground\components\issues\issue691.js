const demo = {
  "type": "page",
  "body": {
    "type": "form",
    "id": "detail-form",
    "mode": "horizontal",
    "body": [
      {
        "type": "input-text",
        "name": "editor",
        "label": "编辑器"
      }
    ],
    "actions": [
      {
        "type": "button",
        "level": "primary",
        "label": "校验",
        "onEvent": {
          "click": {
            "actions": [
              {
                "actionType": "ajax",
                "args": {
                  "api": {
                    "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/form/saveForm",
                    "method": "post",
                    "data": {
                      "editor": "${editor}"
                    }
                  }
                }
              }
            ]
          }
        }
      }
    ]
  }
}

const demo2 = {
  "type": "page",
  "body": [
    {
      "label": "点击弹框",
      "type": "button",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "dialog",
              "data": {
                "name": "牛牛牛"
              },
              "dialog": {
                "showCloseButton": false,
                "title": "弹框标题",
                "body": {
                  "type": "form",
                  "body": [
                    {
                      "type": "input-text",
                      "label": "名字",
                      "name": "username"
                    },
                    {
                      "type": "tpl",
                      "tpl": "${name}222222"
                    }
                  ]
                }
              }
            }
          ]
        }
      }
    }
  ]
}

export default demo;
