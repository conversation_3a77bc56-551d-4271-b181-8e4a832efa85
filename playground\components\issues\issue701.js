const demo = {
  "type": "page",
  id: "testPage",
  "body": {
    "type": "button",
    "label": "批量下线",
    level: 'primary',
    onEvent: {
      click: {
        actions: [
          {
            actionType: 'dialog',
            // expression: 'event.data.offLineIsSuccess',
            id: 'offLineDialog',
            args: {
              dialog: {
                title: '下线',
                showCloseButton: false,
                body: [
                  {
                    type: 'tpl',
                    tpl: '确定要下线吗？',
                  },
                ],
                actions: [
                  {
                    type: 'button',
                    label: '取消',
                    onEvent: {
                      click: {
                        actions: [
                          {
                            actionType: 'closeDialog',
                          },
                        ],
                      },
                    },
                  },
                  {
                    type: 'button',
                    level: 'primary',
                    label: '确认',
                    onEvent: {
                      click: {
                        actions: [
                          {
                            actionType: 'ajax',
                            args: {
                              api: {
                                url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/initData",
                                method: 'get',
                                adaptor: (_, res, tdata) => {
                                  const { status, message, data } = res;
                                  return {
                                    status: status === 200 ? 0 : status,
                                    msg: message,
                                    data: {
                                      offLineOkRelList: [
                                        { name: '度量1' },
                                        { name: '度量2' },
                                      ],
                                      offLineOkIsSuccess: false,
                                    },
                                  };
                                },
                              },
                            },
                          },
                          {
                            actionType: 'toast',
                            args: {
                              msg: '${event|json}'
                            }
                          },
                        ],
                      },
                    },
                  },
                ],
              },
            },
          },
        ],
      },
    },
  }
}


const demo2 = {
  "type": "page",
  id: "testPage",
  "body": {
    "type": "button",
    "label": "批量下线",
    level: 'primary',
    onEvent: {
      click: {
        actions: [
          {
            actionType: 'dialog',
            id: 'offLineDialog',
            args: {
              dialog: {
                title: '下线',
                showCloseButton: false,
                body: [
                  {
                    type: 'tpl',
                    tpl: '确定要下线吗？',
                  },
                ],
                actions: [
                  {
                    type: 'button',
                    label: '取消',
                    onEvent: {
                      click: {
                        actions: [
                          {
                            actionType: 'closeDialog',
                          },
                        ],
                      },
                    },
                  },
                  {
                    type: 'button',
                    level: 'primary',
                    label: '确认',
                    onEvent: {
                      click: {
                        actions: [
                          {
                            actionType: 'ajax',
                            args: {
                              api: {
                                url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/initData",
                                method: 'get',
                                adaptor: (_, res, tdata) => {
                                  const { status, message, data } = res;
                                  return {
                                    status: status === 200 ? 0 : status,
                                    msg: message,
                                    data: {
                                      offLineOkRelList: [
                                        { name: '度量1' },
                                        { name: '度量2' },
                                      ],
                                      offLineOkIsSuccess: false,
                                    },
                                  };
                                },
                              },
                            },
                          },
                          {
                            actionType: 'toast',
                            args: {
                              msg: '${event.data.responseData|json}'
                            }
                          },
                        ],
                      },
                    },
                  },
                ],
              },
            },
          },
        ],
      },
    },
  }
}


const demo3 = {
  "type": "page",
  id: "pageId",
  "data": {
    "name": "page"
  },
  "body": [
    {
      "type": "button",
      "label": "批量下线 - ${name}",
      level: 'primary',
      onEvent: {
        click: {
          actions: [
            {
              actionType: 'dialog',
              id: 'offLineDialog',
              args: {
                dialog: {
                  title: '下线',
                  showCloseButton: false,
                  body: [
                    {
                      type: 'tpl',
                      tpl: '确定要下线吗？',
                    },
                  ],
                  actions: [
                    {
                      type: 'button',
                      label: '取消',
                      onEvent: {
                        click: {
                          actions: [
                            {
                              actionType: 'closeDialog',
                            },
                          ],
                        },
                      },
                    },
                    {
                      type: 'button',
                      level: 'primary',
                      label: '确认',
                      onEvent: {
                        click: {
                          actions: [
                            {
                              actionType: 'custom',
                              script: function () {
                                console.log('custom');
                              }
                            },
                            {
                              actionType: 'ajax',
                              args: {
                                api: {
                                  url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/initData",
                                  method: 'get',
                                  adaptor: (_, res, tdata) => {
                                    const { status, message, data } = res;
                                    return {
                                      status: status === 200 ? 0 : status,
                                      msg: message,
                                      data: {
                                        offLineOkRelList: [
                                          { name: '度量1' },
                                          { name: '度量2' },
                                        ],
                                        offLineOkIsSuccess: false,
                                        name: 'changeName'
                                      },
                                    };
                                  },
                                },
                              },
                            },
                            {
                              actionType: 'toast',
                              args: {
                                msg: '${event.data.responseData|json}'
                              }
                            },
                            {
                              actionType: 'reload',
                              componentId: 'serviceId',
                              data: {
                                name: '${event.data.responseData.name}'
                              },
                            }
                          ],
                        },
                      },
                    },
                  ],
                },
              }
            },
          ],
        },
      },
    },
    {
      "type": "service",
      id: "serviceId",
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
      "body": {
        "type": "panel",
        "title": "$title",
        "body": "现在是：${name}"
      }
    }
  ]
}

export default demo3;
