import { registerFunction } from 'amis';

registerFunction('CUSTOM_ITEM', (...rest) => {
  const [config = true, folded, children] = rest;

  // 递归查找children中是否含有config为false的项
  const hasConfigFalsy = (_children) => {
    for (const child of _children) {
      if (
        child.config === false ||
        (child.children && hasConfigFalsy(child.children))
      ) {
        return true;
      }
    }
    return false;
  }

  // config为false或者folded为true，同时children中包含config为false的项
  if (config === false || (folded && hasConfigFalsy(children))) {
    return 'bg-success';
  }
})

const demo = {
  "type": "page",
  "body": {
    "type": "form",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "body": [
      {
        "type": "input-tree",
        "name": "tree",
        "label": "Tree",
        // "itemClassNameExpr": "${value === 1 ? 'bg-success' : ''}",
        "itemClassNameExpr": "${CUSTOM_ITEM(config, __folded, children)}",
        "options": [
          {
            "label": "Folder A",
            "value": 1,
            "config": true,
            "children": [
              {
                "label": "file A",
                "value": 2,
                "config": true
              },
              {
                "label": "Folder B",
                "value": 3,
                "children": [
                  {
                    "label": "file b1",
                    "value": 3.1,
                    "config": false
                  },
                  {
                    "label": "file b2",
                    "value": 3.2,
                    "config": true
                  }
                ]
              }
            ]
          },
          {
            "label": "file C",
            "value": 4
          },
          {
            "label": "file D",
            "value": 5,
            "config": false
          }
        ]
      }
    ]
  }
}

export default demo;
