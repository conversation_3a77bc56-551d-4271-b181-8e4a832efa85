const demo = {
  "type": "page",
  "body": [
    {
      "type": "button",
      "label": "setValue按钮",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "setValue",
              "componentId": "comboId2",
              "args": {
                // "value": {
                //   "text": "aaa"
                // },
                // "index": 0
                "value": [
                  {
                    "text": "aaa"
                  },
                  {
                    "text": "bbb"
                  },
                  {
                    "text": "ccc"
                  }
                ]
              }
            }
          ]
        }
      }
    },
    {
      "type": "button",
      "label": "addItem按钮",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "addItem",
              "componentId": "comboId2",
              "args": {
                "item": {
                  "text": "aaa"
                },
                "index": 0
              }
            }
          ]
        }
      }
    },
    {
      "type": "button",
      "label": "clear按钮",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "clear",
              "componentId": "comboId2",
            }
          ]
        }
      }
    },
    {
      "type": "form",
      "mode": "horizontal",
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
      "debug": true,
      "body": [
        {
          "type": "input-text",
          "name": "text",
          "label": "文本"
        },
        {
          "type": "combo",
          "name": "combo1",
          "id": "comboId1",
          "label": "Combo 单选展示",
          "nullable": true,
          "items": [
            {
              "name": "text",
              "label": "文本",
              "type": "input-text"
            },
            {
              "name": "select",
              "label": "选项",
              "type": "select",
              "options": [
                "a",
                "b",
                "c"
              ]
            }
          ],
          "onEvent": {
            "change": {
              "actions": [
                {
                  "actionType": "toast",
                  "args": {
                    "msg": "${event.data|json}"
                  }
                }
              ]
            }
          }
        },
        {
          "type": "divider"
        },
        {
          "type": "combo",
          "name": "combo2",
          "id": "comboId2",
          "label": "Combo 多选展示",
          "multiple": true,
          "draggable": true,
          "syncFields": ["text"],
          "strictMode": false,
          "canAccessSuperData": true,
          "items": [
            {
              "name": "text",
              "label": "文本",
              "type": "input-text"
            },
            {
              "name": "select",
              "label": "选项",
              "type": "select",
              "options": [
                "a",
                "b",
                "c"
              ]
            }
          ],
          "onEvent": {
            "change": {
              "actions": [
                // {
                //   "preventDefault": true // ui和debug数据不一致问题，inputTable同理
                // },
                {
                  "actionType": "toast",
                  "args": {
                    "msg": "${event.data|json}"
                  }
                }
              ]
            }
          }
        }
      ]
    }
  ]
}

export default demo;
