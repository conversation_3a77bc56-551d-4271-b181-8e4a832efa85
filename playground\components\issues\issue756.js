const demo = {
  type: 'page',
  body: [
    {
      type: 'service',
      id: 'serviceId',
      api: {
        url: 'https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample',
        adaptor: payload => {
          return {
            ...payload,
            data: {
              reloadFinished: true, // 控制 crud 是否发送请求
              selectedMagicGroup: 'selectValue', // 给 select 设置默认值
            },
          };
        },
      },
      body: [
        {
          // type: 'form',
          // wrapWithPanel: false,
          // body: {
            type: 'select', // 要素1：select的name和value共存，且名字不同。要素2：select不包裹form
            id: 'magic-group-select',
            name: 'magic-group-select', // 这个name并没有对应值。改成和value同名也行，删掉value
            // name: "selectedMagicGroup", // name一致没有
            value: '${selectedMagicGroup}', // 决定了curd的withStore进哪个分支
          // },
        },
        {
          type: 'crud',
          api: {
            url: 'https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample',
            trackExpression: '${reloadFinished}', // 要素3： 配置 trackExpression
            sendOn: '${reloadFinished}',
            adaptor: (payload, response) => {
              console.log('payload', {payload}, {response});
              return {
                // 重点：接口没有返回列表数据，结合上层service变动导致curd报错
                // 数据结构：{ status, msg, data: { count, rows } }
                ...payload,
                data: {}, // 模拟接口空数据
              };
            },
          },
          syncLocation: false,
          columns: [
            {
              name: 'id',
              label: 'ID',
            },
            {
              name: 'engine',
              label: 'Rendering engine',
            },
          ],
        },
      ],
    },
  ],
};

// bug: curd先有数据，service响应后数据置空
const demo1 = {
  type: 'page',
  data: {
    crudData: [{ id: 1, name: 'test' }],
  },
  body: [
    {
      type: 'service',
      id: 'serviceId',
      api: {
        url: 'https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?waitSeconds=3',
        adaptor: payload => {
          return {
            ...payload,
            data: {
              selectedMagicGroup: 'selectValue', // 给 select 设置默认值
            },
          };
        },
      },
      body: [
        {
          type: 'select', // 要素1：select的name和value共存，且名字不同。要素2：select不包裹form
          id: 'magic-group-select',
          name: 'magic-group-select', // 这个name并没有对应值。改成和value同名也行，删掉value
          value: '${selectedMagicGroup}', // 决定了curd的withStore进哪个分支
        },
        {
          type: 'crud',
          source: "${crudData}",
          syncLocation: false,
          columns: [
            {
              name: 'id',
              label: 'ID',
            },
            {
              name: 'engine',
              label: 'Rendering engine',
            },
          ],
        },
      ],
    },
  ],
};

export default demo1;
