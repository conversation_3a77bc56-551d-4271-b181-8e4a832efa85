/**
 * 配置2个data时
 *
 * 百度amis
 * 老版本(2.9.0)：dialog的data优先级高，覆盖action.data
 * 新版本：action.data优先级高，覆盖dialog.data（dialog.data在配置action.data后无效）
 *
 * @dataseed/amis
 * 老版本(2.9.0)：dialog的data优先级高，覆盖action.data
 * 改动方向1：同步百度amis
 * 改动方向2：根据dataMergeMode决定action.data是否覆盖，action.data优先级高
 */
const demo = {
  "type": "page",
  "body": [
    {
      "label": "提示弹框",
      "type": "button",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "dialog",
              "data": {
                "name1": "name1"
              },
              "dialog": {
                "data": {
                  "name2": "name2"
                },
                "title": "弹框标题",
                "body": [
                  {
                    "type": "tpl",
                    "tpl": "${name1} - ${name2}"
                  }
                ],
                "actions": []
              }
            }
          ]
        }
      }
    },
    {
      "label":"选项",
      "type":"select",
      "name":"select",
      "selectedTooltipMode": "always",
      "multiple": false,
      "options":[
          {
              "label":"label0",
              "value":0,
              "id":"id0"
          },
          {
              "label":"label1",
              "value":1,
              "id":"id1"
          }
      ],
  }
  ]
}

// 测试data用法
const demo1 = {
  "type": "page",
  "body": [
    {
      type: 'button',
      level: 'primary',
      label: '确认',
      onEvent: {
        click: {
          actions: [
            {
              actionType: 'ajax',
              args: {
                api: {
                  url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/initData",
                  method: 'get',
                  adaptor: (_, res, tdata) => {
                    const { status, message, data } = res;
                    return {
                      status: status === 200 ? 0 : status,
                      msg: message,
                      data: {
                        offLineOkRelList: [
                          { name: '度量1' },
                          { name: '度量2' },
                        ],
                        offLineOkIsSuccess: false,
                        name: 'changeName'
                      },
                    };
                  },
                },
              },
            },
            {
              actionType: 'reload',
              componentId: 'serviceId',
              // dataMergeMode: 'override',
              data: {
                reloadData: 'reloadData'
              },
            }
          ],
        },
      },
    },
    {
      "type": "service",
      id: "serviceId",
      "data": {
        "initData": "initData"
      },
      // "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/page/initData",
      "body": {
        "type": "panel",
        "title": "$title",
        "body": "initData: ${initData};<br /> reloadData：${reloadData}; <br /> res date: ${date}; "
      }
    }
  ]
}

export default demo;
