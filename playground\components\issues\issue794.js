const demo = {
  "type": "page",
  data: {
    nodeLabel: 'FEATURE',
  },
  "body": [
    {
      "type": "form",
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
      "showLabelColon": true,
      "body": [
        {
          "type": "input-text",
          "name": "name",
          "label": "姓名",
          "required": true,
          "validations": {
            "isNumeric": true,
            "minimum": 10
          }
        },
        {
          "name": "email",
          "type": "input-email",
          "label": "邮箱",
          "required": true
        }
      ],
      onEvent: {
        validateSucc: {
          actions: [
            {
              actionType: 'reload',
              componentId: 'targetFormServiceId',
            },
          ],
        },
      },
    },
    {
      "type": "form",
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
      "showLabelColon": true,
      id: 'targetFormId',
      "body": {
        type: 'service',
        id: 'targetFormServiceId',
        api: {
          method: 'post',
          url: '/datalineage/lineage/lineageSummarySearch',
          sendOn: '${object}',
          data: {
            objectType: '${objectType}',
            object: '${object}',
            version: '${version}',
            associateLabels: '${associateLabels}',
          },
          requestAdaptor(api) {
            const {
              objectType,
              object,
              version,
              associateLabels = [],
            } = api?.data || {};
            let id = `${objectType.value}-${object.value}`;
            if (objectType.value === 'TABLE')
              id = `${objectType.value}-${object.tableType}-${object.instanceName}-${object.projectName}-${object.tableName}`;
            /** 查询对象为API类型 */
            if (objectType.value === 'API') {
              /** 应用名 */
              const context = object?.context;
              /** api Url路径 */
              const apiUrl = object?.url || object?.value;
              id = `${objectType.value}-${context}-${apiUrl}-${object?.method}`;
            }
            /** 表和资信没有版本 */
            if (version && !NOVERSION.includes(objectType.value))
              id = `${id}-${version.versionNo}`;
            const newAssociateLabels = associateLabels.map((item) => {
              return item.value;
            });
            return {
              ...api,
              data: {
                id,
                associateLabels: newAssociateLabels,
              },
            };
          },
          adaptor: async (payload, res) => {
            return {
              status: Number(res.code) === 10000 ? 0 : Number(res.code),
              msg: res?.message,
              data:
                Number(res.code) === 10000
                  ? {
                      domainList,
                      summary,
                      reloadTimestamp: new Date().getTime(),
                    }
                  : undefined,
            };
          },
        },
        "body": [
          {
            "type": "input-text",
            "name": "name",
            "label": "姓名",
            "required": true,
            "validations": {
              "isNumeric": true,
              "minimum": 10
            }
          },
          // 这里还有一层form
          {
            type: 'container',
            body: [
              {
                type: 'crud',
                api: {
                  sendOn: '${nodeLabel}',
                  trackExpression: '${nodeLabel},${reloadTimestamp}', // 这里需要根据nodeLabel来reload
                }
              }
            ],
          },
        ]
      }
    }
  ]
}

export default demo;
