const demo = {
  "type": "page",
  "initApi": {
    "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/options/autoComplete3?waitSeconds=5",
    "adaptor": "return { data: { options: payload.data, 'other': 'other' } }"
  },
  "body": [
    // {
    //   "type": "tpl",
    //   "tpl": "${options|json}"
    // },
    {
      "type": "crud",
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
      "syncLocation": false,
      "filter": {
        // "inheritData": false,
        // "trackExpression": "${options | json}",
        "body": [
          {
            "type": "select",
            "name": "browser",
            "label": "浏览器",
            "labelField": "lab",
            "valueField": "val",
            "placeholder": "选择浏览器",
            "source": "${options}"
          }
        ]
      },
      "columns": [
        {
          "name": "id",
          "label": "ID"
        },
        {
          "name": "engine",
          "label": "engine",
        }
      ]
    }
  ]
}

const demo1 = {
  "type": "page",
  "initApi": {
    "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/options/autoComplete?waitSeconds=3",
    "adaptor": "return { data: { options: payload.data } }"
  },
  "body": [
    {
      "type": "crud",
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?waitSeconds=5",
      "syncLocation": false,
      "filter": {
        // "inheritData": false,
        // "trackExpression": "${options | json}",
        "body": [
          {
            "type": "select",
            "name": "browser",
            "label": "浏览器",
            "labelField": "lab",
            "valueField": "val",
            "placeholder": "选择浏览器",
            "source": "${options}"
          }
        ]
      },
      "columns": [
        {
          "name": "id",
          "label": "ID"
        },
        {
          "name": "engine",
          "label": "options",
          "type": "tpl",
          "tpl": "options数量：${options.length}"
        },
        {
          "name": "browser",
          "label": "Browser"
        },
      ]
    }
  ]
}

// inheritData: false 在form中的使用
const demo3 = {
  "type": "page",
  "data": {
    "p1": "p1",
    "p2": "p2"
  },
  "body": {
    "debug": true,
    "type": "form",
    "inheritData": false,
    "api": "https://3xsw4ap8wah59.cfc-execute.bj.baidubce.com/api/amis-mock/mock2/form/saveForm",
    "body": [
      {
        "type": "input-text",
        "name": "name",
        "label": "姓名："
      },
      {
        "name": "email",
        "type": "input-email",
        "label": "邮箱："
      }
    ]
  }
}


export default demo;


const d = {
  "type": "page",
  "body": {
    "title": "",
    "type": "form",
    "mode": "horizontal",
    "data": {
      presetDate3: "10DayAfter"
    }
    "body": [
      {
        "type": "service",
        "label": "操作",
        "schemaApi": {
          "url": "/", // url需要随便配置一个值，不然不会走进组件逻辑
          "dataProvider": true, // 设置了 dataProvider 之后不会发送请求，1.59.0以上版本
          "tdata": "${actions}", // 通过tdata传递数据
          "adaptor": (payload) => {
            // 在adaptor中的payload就是我们传入的tdata，在此处可以根据数据去计算出一个需要渲染的schema并返回，最终返回的这个schema会被渲染到页面上
            return {
              "type": "ds-date-range-picker",
              name: "date3",
              label: "时间范围",
              showTime: true,
              presetValue: '${presetDate3}',
              presets: [
                "today",
                {
                  "label": "10天后", // 必填，字符串，快捷按钮展示文案
                  "key": "10DayAfter", // 必填，字符串，当前快捷按钮唯一key
                  "startDate": "${NOW() - 5*60*1000}", // 必填，字符串｜dayjs，可使用表达式 DATEMODIFY,NOW 计算时间。也可直接使用 dayjs对象，比如： dayjs().add(10, 'day')
                  "endDate": "${NOW()}" // 必填，字符串|dayjs， 可使用表达式 DATEMODIFY,NOW 计算时间。也可直接使用 dayjs对象，比如： dayjs().add(10, 'day')
                }
                ,
                "7daysago",
                "30daysago"
              ]
            }
          }
        }
      }
    ],
    "submitText": null,
    "actions": []
  }
}
