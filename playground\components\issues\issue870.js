const demo = {
  "type": "page",
  "body": {
    "type": "crud",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4",
    "columns": [
      {
        "name": "id",
        "label": "ID"
      },
      {
        "name": "engine",
        "label": "Rendering engine",
      },
    ],
    subTable: {
      type: 'button',
      label: '弹窗',
      onEvent: {
        click: {
          actions: [{
            actionType: 'toast',
            args: {
              msg: 'text'
            }
          }]
        }
      }
    }
  }
}

const demo1 = {
  "type": "page",
  "body": {
    "type": "crud",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/crud/table4",
    "headerToolbar": [
      "bulkActions"
    ],
    "bulkActions": [
      {
        "label": "批量删除",
        "actionType": "ajax",
        "api": "delete:https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample/${ids|raw}",
        "confirmText": "确定要批量删除?"
      }
    ],
    "columns": [
      {
        "name": "id",
        "label": "ID"
      },
      {
        "name": "engine",
        "label": "Rendering engine",
        "headSearchable": {
          "type": "input-text",
          "name": "engine3",
          "label": "Rendering enginer"
        }
      },
      {
        "name": "browser",
        "label": "Browser",
        "headSearchable": {
          "type": "input-text",
          "name": "browser3",
          "label": "Browser"
        }
      },
      {
        "name": "platform",
        "label": "Platform(s)"
      },
      {
        "name": "version",
        "label": "Engine version"
      },
      {
        "name": "grade",
        "label": "CSS grade"
      },
    ],
    onEvent: {
      selectedChange: {
        actions: [{
          actionType: 'toast',
          args: {
            msg: 'text'
          }
        }]
      }
    }
  }
}

// subTalbe onEvent失效根本原因
const demo2 = {
  "type": "page",
  "body": {
    "type": "service",
    "api": "/api/mock2/sample?perPage=10",
    "body": [
      {
        "type": "table",
        "source": "$rows",
        "onEvent": {}, // 只要有这个属性，subTable就会失效
        "columns": [
          {
            "name": "id",
            "label": "ID",
          },
          {
            "name": "browser",
            "label": "Browser",
          },
        ],
        subTable: {
          type: 'button',
          label: '弹窗',
          onEvent: {
            click: {
              actions: [
                {
                  actionType: 'toast',
                  args: {
                    msg: 'text'
                  }
                }
              ]
            }
          }
        }
      }
    ]
  }
}

// crud双击事件
const demo3 = {
  "type": "page",
  "body": {
    "type": "crud",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=10",
    "syncLocation": false,
    "columns": [
      {
        "name": "id",
        "label": "ID"
      },
      {
        "name": "engine",
        "label": "Rendering engine"
      },
      {
        "name": "browser",
        "label": "Browser"
      },
    ],
    "onEvent": {
      "rowDbClick": {
        "actions": [
          {
            "actionType": "toast",
            "args": {
              "msgType": "info",
              "msg": "行双击数据：${event.data.item|json}；行索引：${event.data.index}"
            }
          }
        ]
      }
    }
  }
}

const demo4 = {
  "type": "page",
  "body": {
    "type": "crud",
    "syncLocation": false,
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
    "headerToolbar": [],
    "footerToolbar": [],
    "columns": [
      {
        "name": "id",
        "label": "ID"
      },
      {
        "name": "engine",
        "label": "Rendering engine"
      },
      {
        "name": "browser",
        "label": "Browser"
      },
      {
        "name": "platform",
        "label": "Platform(s)"
      },
      {
        "name": "version",
        "label": "Engine version"
      },
      {
        "name": "grade",
        "label": "CSS grade",
        "sortable": true
      }
    ]
  }
}

export default demo;
