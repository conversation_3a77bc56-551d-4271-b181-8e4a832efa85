// table多次render优化对比
const demo = {
  "type": "page",
  "initApi": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=5&waitSeconds=5",
  "body": {
    "type": "service",
    "body": {
      "type": "form",
      "body": [
        {
          "type": "crud",
          "source": "$rows",
          "columns": [
            {
              "name": "engine",
              "label": "Engine"
            },
            {
              "name": "version",
              "label": "Version"
            }
          ]
        }
      ]
    }
  }
}

/**
 * case: crud的filter取不到最新值
 * 问题：
 * 1. 翻页loading - deepCompare问题
 * 2. cell取不到最新数据 - updateAllRows开启即可
 * 
 */
const demo1 = {
  "type": "page",
  "initApi": {
    "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/options/autoComplete3?waitSeconds=5",
    "adaptor": "return { data: { options: payload.data, 'other': 'other' } }"
  },
  "body": [
    // {
    //   "type": "tpl",
    //   "tpl": "${options|json}"
    // },
    {
      "type": "crud",
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
      "syncLocation": false,
      "updateAllRows": true,
      "filter": {
        // "inheritData": false,
        // "trackExpression": "${options | json}",
        "body": [
          {
            "type": "select",
            "name": "browser",
            "label": "浏览器",
            "labelField": "lab",
            "valueField": "val",
            "placeholder": "选择浏览器",
            "source": "${options}"
          }
        ]
      },
      "columns": [
        {
          "name": "id",
          "label": "ID"
        },
        {
          "name": "engine",
          "label": "engine",
        },
        {
          "label": "数量",
          "type": "tpl",
          "tpl": "options数量：${options.length}"
        }
      ]
    }
  ]
}

/**
 * case: render dialog的地方渲染异常
 */
const demo2 = {
  "type": "page",
  "data": {
    "table": [
      {
        "a": "a1",
        "b": "b1",
        "c": {
          "c1": "123",
          "c2": "222"
        }
      }
    ],
    "title": "获取衍生特征值时，需传入以下参数，请赋值:"
  },
  "body": {
    "title": "",
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/saveForm?waitSeconds=2",
    "actions": [],
    "body": [
      // {
      //   "type": "input-table",
      //   "name": "table",
      //   "label": "特征参数",
      //   "addable": true,
      //   "removable": true,
      //   "editable": true,
      //   "columns": [
      //     {
      //       "name": "a",
      //       "label": "A"
      //     },
      //     {
      //       "name": "b",
      //       "label": "B"
      //     },
      //     {
      //       "type": "input-group",
      //       "label": "C",
      //       "body": [
      //         {
      //           "type": "combo",
      //           "name": "c",
      //           "multiLine": true,
      //           "multiple": false,
      //           "label": false,
      //           "required": true,
      //           "items": [
      //             {
      //               "type": "input-text",
      //               "name": "c1",
      //               "required": true,
      //               "label": false
      //             },
      //             {
      //               "type": "input-text",
      //               "name": "c2",
      //               "required": true,
      //               "label": false
      //             }
      //           ]
      //         }
      //       ]
      //     }
      //   ]
      // },
      {
        "type": "input-table",
        "name": "table",
        "label": "特征参数",
        // "addable": true,
        // "removable": true,
        // "editable": true,
        "needConfirm": false,
        "columns": [
          {
            "name": "a",
            "label": "A"
          },
          // {
          //   "name": "b",
          //   "label": "B"
          // },
          // {
          //   "type": "input-text",
          //   "name": "c1",
          //   "required": true,
          //   "label": false
          // }
        ]
      }
    ],
    "type": "form"
  }
}

/**
 * dialog 弹窗失效
 */
const demo3 = {
  "type": "page",
  "body": [
    {
      "label": "点击弹框",
      "type": "button",
      "actionType": "dialog",
      "dialog": {
        "showCloseButton": false,
        "title": "弹框标题",
        "body": {
          "type": "form",
          "body": [
            {
              "type": "input-text",
              "label": "名字",
              "name": "username"
            }
          ]
        }
      }
    }
  ]
}



/**  ==================== issue 列表 ========================  */
// #403 表单 reset 失效。setValue page时行为改后应该正常
const demo4 = {
  "type": "page",
  "id": "page_data",
  "body": {
    "type": "form",
    "body": [
      {
        "type": "input-text",
        "name": "name",
        "label": "姓名"
      },
      {
        "type": "input-email",
        "name": "email",
        "label": "邮箱"
      }
    ],
    "actions": [
      {
        "type": "reset",
        "label": "重置"
      },
      {
        "type": "submit",
        "label": "保存"
      },
      {
        "type": "button",
        "label": "更新",
        "level": "primary",
        "className": "mb-2",
        "onEvent": {
          "click": {
            "actions": [
              {
                "actionType": "setValue",
                "componentId": "page_data",
                "args": {
                  "value": {
                    "name": 1
                  }
                }
              }
            ]
          }
        }
      }
    ]
  }
}

// #419 form 中 动态显隐带默认值的formItem, table 数据域变化 会导致不触发更新
const demo5 = {
  "type": "page",
  "body": [
    {
      "type": "alert",
      "body": "当有动态显示表单项显示时，<br/> 带value默认值时，form中静态展示table依赖的数据更新，table不刷新（BUG） <br/> 不带value默认值时，表现正常"
    },
    {
      "type": "form",
      "debug": true,
      "body": [
        {
          "type": "select",
          "label": "table数据源选择",
          "name": "tableSource",
          "joinValues": false,
          "extractValue": false,
          "multiple": true,
          "options": [
            {
              "label": "列1",
              "value": "c1"
            },
            {
              "label": "列2",
              "value": "c2"
            },
            {
              "label": "列3",
              "value": "c3"
            },
            {
              "label": "列4",
              "value": "c4"
            }
          ]
        },
        {
          "type": "table",
          "source": "${tableSource}",
          "columns": [
            {
              "name": "value",
              "label": "key"
            },
            {
              "name": "label",
              "label": "value"
            }
          ]
        },
        {
          "type": "radios",
          "name": "caseType",
          "inline": false,
          "label": "显示隐藏表单",
          "options": [
            {
              "value": "case1",
              "label": "有默认值的表单项-table不更新(BUG)"
            },
            {
              "value": "case2",
              "label": "没有默认值的表单项-table表现正常"
            }
          ]
        },
        {
          "type": "input-text",
          "name": "inputCase1",
          "label": "存在默认值",
          "visibleOn": "${caseType ===\"case1\"}",
          "value": "一段文本"
        },
        {
          "type": "input-text",
          "name": "inputCase1",
          "label": "不存在默认值",
          "visibleOn": "${caseType ===\"case2\"}"
        }
      ]
    }
  ]
}

// #860 crud 设置搜索项默认值后，直接点击重置，搜索区默认值被清空，但接口传参是有默认值的
const demo6 = {
  "type": "page",
  "data": {},
  "initApi": {
    "method": "get",
    "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/mock2/form/getOptions",
    "tdata": {
      "platform": "${platform}"
    },
    "adaptor": "return ({ 'data': { 'engine': 'Gecko,Trident', 'platform': api.tdata.platform || 'Win 98+ / OSX.2+', 'engineOptions': [{label:'Blink', value:'Blink'}, {label:'Trident', value:'Trident'}, {label:'Gecko', value:'Gecko'}], 'platformOptions': [{label:'Win 95+', value:'Win 95+'}, {label:'Win XP', value:'Win XP'}, {label:'Win 98+ / OSX.2+', value:'Win 98+ / OSX.2+'}] } })"
  },
  "body": [
    {
      "type": "crud",
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/mock2/crud/table4",
      "syncLocation": false,
      "autoGenerateFilter": {
        "defaultExpanded": false,
        "showBtnToolbar": false
      },
      "filter": {
        "title": "",
        "debug": true,
        "data": {},
        "id": "filter-id",
        "canAccessSuperData": true,
        "body": [
          {
            "type": "group",
            "mode": "horizontal",
            "body": [
              {
                "type": "select",
                "name": "grade",
                "label": "CSS grade",
                "placeholder": "选择grade",
                "clearable": true,
                "source": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/mock2/form/getOptions"
              },
              {
                "type": "select",
                "name": "engine",
                "label": "engine",
                "multiple": true,
                "placeholder": "选择engine",
                "source": "${engineOptions}"
              },
              {
                "type": "select",
                "name": "platform",
                "label": "平台",
                "multiple": true,
                "placeholder": "选择平台",
                "labelField": "label",
                "valueField": "value",
                "resetValue": "Win XP",
                "source": "${platformOptions}"
              }
            ]
          }
        ],
        "actions": [
          {
            "type": "reset",
            "label": "重 置",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "actionType": "setValue",
                    "componentId": "filter-id",
                    "args": {
                      "data": {
                        "engine": "Gecko,Trident",
                        "platform": "Win 98+ / OSX.2+"
                      }
                    }
                  }
                ]
              }
            }
          },
          {
            "type": "submit",
            "level": "primary",
            "label": "查 询"
          }
        ]
      },
      "columns": [
        {
          "name": "engine",
          "label": "Rendering engine"
        },
        {
          "name": "browser",
          "label": "Browser"
        },
        {
          "name": "platform",
          "label": "Platform(s)"
        },
        {
          "name": "version",
          "label": "Engine version"
        },
        {
          "name": "grade",
          "label": "CSS grade"
        }
      ]
    }
  ]
}

const demo61 = {
  "type": "page",
  "id": "pageid",
  // "data": {
  //   "text": "init"
  // },
  "initApi": {
    "method": "get",
    "url": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/mock2/form/getOptions?waitSeconds=5",
    "tdata": {
      "platform": "${platform}"
    },
    "adaptor": "return ({ 'data': { 'text': 'api text', 'platform': api.tdata.platform || 'Win 98+ / OSX.2+', 'engineOptions': [{label:'Blink', value:'Blink'}, {label:'Trident', value:'Trident'}, {label:'Gecko', value:'Gecko'}], 'platformOptions': [{label:'Win 95+', value:'Win 95+'}, {label:'Win XP', value:'Win XP'}, {label:'Win 98+ / OSX.2+', value:'Win 98+ / OSX.2+'}] } })"
  },
  "body": [
    {
      "type": "action",
      "label": "更新",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "setValue",
              "componentId": "pageid",
              "args": {
                "value": {
                  "text": "xxx"
                }
              }
            }
          ]
        }
      }
    },
    {
      "type": "form",
      "debug": true,
      "body": [
        {
          "type": "input-text",
          "label": "文本框",
          "name": "text"
        },
        {
          "type": "input-password",
          "label": "<a href='./password'>密码</a>",
          "name": "password"
        }
      ]
    },
    {
      "type": "crud",
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/mock2/crud/table4",
      "syncLocation": false,
      "autoGenerateFilter": {
        "defaultExpanded": false,
        "showBtnToolbar": false
      },
      "filter": {
        "title": "",
        "data": {},
        "id": "filter-id",
        "canAccessSuperData": true,
        "body": [
          {
            "type": "group",
            "mode": "horizontal",
            "body": [
              {
                "type": "select",
                "name": "grade",
                "label": "CSS grade",
                "placeholder": "选择grade",
                "clearable": true,
                "source": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/mock2/form/getOptions"
              },
              {
                "type": "select",
                "name": "engine",
                "label": "engine",
                "multiple": true,
                "placeholder": "选择engine",
                "source": "${engineOptions}"
              },
              {
                "type": "select",
                "name": "platform",
                "label": "平台",
                "multiple": true,
                "placeholder": "选择平台",
                "labelField": "label",
                "valueField": "value",
                "resetValue": "Win XP",
                "source": "${platformOptions}"
              }
            ]
          }
        ],
        "actions": [
          {
            "type": "reset",
            "label": "重 置",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "actionType": "setValue",
                    "componentId": "filter-id",
                    "args": {
                      "data": {
                        "engine": "Gecko,Trident",
                        "platform": "Win 98+ / OSX.2+"
                      }
                    }
                  }
                ]
              }
            }
          },
          {
            "type": "submit",
            "level": "primary",
            "label": "查 询"
          }
        ]
      },
      "columns": [
        {
          "name": "engine",
          "label": "Rendering engine"
        },
        {
          "name": "browser",
          "label": "Browser"
        },
        {
          "name": "platform",
          "label": "Platform(s)"
        },
        {
          "name": "version",
          "label": "Engine version"
        },
        {
          "name": "grade",
          "label": "CSS grade"
        }
      ]
    }
  ]
}

// #337 重置无效，用form包裹后可以解决
const demo7 = {
  "type": "page",
  "id": "mainPage",
  "title": "父数据域发生变化导致表单默认值丢失问题",
  "body": [
    {
      "ignoreSchemaCheck": true,
      "type": "select",
      "name": "selectedTreeId",
      "label": "变更值",
      "options": [
        {
          "label": "Folder A",
          "value": "aaa"
        },
        {
          "label": "Folder B",
          "value": "bbb"
        }
      ]
    },
    {
      "type": "crud",
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
      "syncLocation": false,
      "autoGenerateFilter": {
        "defaultExpanded": true,
        "showBtnToolbar": false
      },
      "headerToolbar": [
        {
          "type": "columns-toggler",
          "align": "right",
          "draggable": true,
          "icon": "fas fa-cog",
          "overlay": true,
          "footerBtnSize": "sm"
        }
      ],
      "columns": [
        {
          "name": "id",
          "label": "ID",
          "searchable": {
            "type": "input-text",
            "name": "id",
            "label": "主键",
            "placeholder": "输入id"
          }
        },
        {
          "name": "engine",
          "label": "Rendering engine",
          "searchable": true
        },
        {
          "name": "version",
          "label": "Engine version",
          "searchable": {
            "type": "input-number",
            "name": "version",
            "label": "版本号",
            "placeholder": "输入版本号",
            "mode": "horizontal"
          }
        }
      ]
    }
]
}

export default demo;
