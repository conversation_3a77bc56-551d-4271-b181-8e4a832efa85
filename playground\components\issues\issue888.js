const demo = {
  "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "labelWidth": 130,
    "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
    "body": [
      {
        "type": "input-tree",
        "name": "tree1",
        "label": "默认自动选中子节点",
        "multiple": true,
        "checkAll": true,
        "options": [
          {
            "label": "A",
            "value": "a"
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "C",
            "value": "c"
          }
        ]
      },
      {
        "type": "divider"
      },
      {
        "type": "tree-select",
        "name": "tree2",
        "label": "不自动选中子节点",
        "multiple": true,
        "checkAll": true,
        "autoCheckChildren": false,
        "options": [
          {
            "label": "A",
            "value": "a"
          },
          {
            "label": "B",
            "value": "b",
            "children": [
              {
                "label": "B-1",
                "value": "b-1"
              },
              {
                "label": "B-2",
                "value": "b-2"
              },
              {
                "label": "B-3",
                "value": "b-3"
              }
            ]
          },
          {
            "label": "C",
            "value": "c"
          }
        ]
      }
    ]
  }
}

export default demo;



// 原始combo加载
const demo1 = {
  type: "page",
  id: "access-rule-manage",
  data: {
    "ruleInfos": [
      {
        "rules": [
          {
            "paramCode": "age",
            "paramName": "年龄",
            "paramOp": "GREATER_EQUAL",
            "paramValue": "18"
          },
          {
            "paramCode": "age",
            "paramName": "年龄",
            "paramOp": "LESS_THAN",
            "paramValue": "60"
          },
          {
            "paramCode": "sex",
            "paramName": "性别",
            "paramOp": "IN",
            "paramValue": "男"
          },
          {
            "paramCode": "sex",
            "paramName": "性别",
            "paramOp": "IN",
            "paramValue": "女"
          },
          {
            "paramCode": "overdueDays",
            "paramName": "overdueDays",
            "paramOp": "NOT_LIKE",
            "paramValue": "5、3、5"
          },
          {
            "paramCode": "overdueDays",
            "paramName": "overdueDays",
            "paramOp": "NOT_IN",
            "paramValue": "yy"
          }
        ],
        "type": "BASE",
        "typeCn": "基础信息"
      },
      {
        "rules": [
          {
            "paramCode": "overdueDays",
            "paramName": "逾期天数2",
            "paramOp": "LESS_EQUAL",
            "paramValue": "3"
          }
        ],
        "type": "RISK",
        "typeCn": "风险信息"
      },
      {
        "rules": [
          {
            "paramCode": "loanForbiddenDays",
            "paramName": "借款禁申期天数",
            "paramOp": "EQUAL",
            "paramValue": "69"
          },
          {
            "paramCode": "creditForbiddenDays",
            "paramName": "授信禁申期天数",
            "paramOp": "GREATER_THAN",
            "paramValue": "15"
          },
          {
            "paramCode": "creditForbiddenDays",
            "paramName": "授信禁申期天数",
            "paramOp": "LESS_EQUAL",
            "paramValue": "66"
          }
        ],
        "type": "FORBIDDEN",
        "typeCn": "禁申期信息"
      }
    ]
  },
  initApi: {
    // 获取准入负责详情
    url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=1",
    method: "post",
    adaptor: (payload, res) => {
      const { code, message: msg
      } = res;
      const fundObj = {
        "businessOwner": "黄珊",
        "fund": "MSXF",
        "fundName": "马上消金轻资产",
        "updatedAt": "2025-01-08 15:37:53"
      }
      return {
        status: 0,
        msg,
        data: {
          fundName: fundObj.fundName, fund: fundObj.funds, businessOwner: fundObj.businessOwner, ruleInfos: fundObj.ruleInfos
        },
      };
    },
  },
  body: [
    {
      type: "service", // 获取业务分类
      api: {
        url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=2",
        method: "get",
        adaptor: (payload, res) => {
          const { code, message: msg } = res;
          const data = [
            {
              "desc": "基础信息",
              "type": "BASE"
            },
            {
              "desc": "风险信息",
              "type": "RISK"
            },
            {
              "desc": "禁申期信息",
              "type": "FORBIDDEN"
            }
          ]
          return {
            status: 0, // 2000 表示请求成功
            msg,
            data: {
              typeList: data?.map((item) => ({
                ...item, lable: item.desc, value: item.type
              })) || [],
            },
          };
        },
      },
      onEvent: {
        fetchInited: {
          actions: [
            {
              actionType: "setValue",
              componentId: "access-rule-manage",
              args: {
                value: {
                  typeList: "${typeList}", // setValue 到page上
                },
              },
            },
          ],
        },
      },
    },
    {
      type: "service",
      api: {
        // 获取操作类型
        url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=3",
        method: "get",
        adaptor: (payload, res) => {
          const { code, message: msg } = res;
          const data = [
            {
              "desc": "大于",
              "name": "GREATER_THAN"
            },
            {
              "desc": "等于",
              "name": "EQUAL"
            },
            {
              "desc": "小于",
              "name": "LESS_THAN"
            },
            {
              "desc": "大于等于",
              "name": "GREATER_EQUAL"
            },
            {
              "desc": "小于等于",
              "name": "LESS_EQUAL"
            },
            {
              "desc": "不等于",
              "name": "NOT_EQUAL"
            },
            {
              "desc": "包括",
              "name": "LIKE"
            },
            {
              "desc": "不包括",
              "name": "NOT_LIKE"
            },
            {
              "desc": "包含",
              "name": "IN"
            },
            {
              "desc": "不包含",
              "name": "NOT_IN"
            }
          ]
          return {
            status: 0,
            data: { paramOpList: data },
          };
        },
      },
      body: {
        type: "form",
        mode: "horizontal",
        labelWidth: 80,
        body: [
          {
            type: "alert",
            body: "请按照拒绝逻辑配置规则",
            level: "warning",
            showIcon: true,
          },
          {
            type: "group",
            direction: "vertical",
            label: "资金方",
            required: true,
            body: [
              {
                type: "select",
                name: "fund",
                searchable: true,
                clearable: true,
                label: false,
                required: true,
                disabledOn: "${operType === 'edit'}", // 编辑的时候只读
                placeholder: "请选择",
                labelField: "bankName",
                valueField: "bankCode",
                source: {
                  method: "get",
                  url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=4",
                  adaptor: (payload) => {
                    const data = [
                      {
                        "assetId": "cehisyige",
                        "assetName": "cehisyige",
                        "assetsAttribute": "lightAsset",
                        "bankCode": "cehisyige",
                        "bankName": "cehisyige",
                        "funderAttribute": "P2P",
                        "publishStatus": true,
                        "totalSwitch": true
                      },
                      {
                        "assetId": "YINGXIAOHUA",
                        "assetName": "盈小花",
                        "assetsAttribute": "lightAsset",
                        "bankCode": "YINGXIAOHUA",
                        "bankName": "盈小花",
                        "cooperateModel": "LIGHT_ASSET",
                        "funderAttribute": "sameTrade",
                        "publishStatus": true,
                        "totalSwitch": true
                      }
                    ]
                    return data;
                  },
                },
                autoFill: {
                  fundName: "${bankName}",
                },
              },
              {
                type: "input-text",
                name: "fundName",
                visibleOn: "false",
              },
              {
                type: "combo",
                name: "ruleInfos",
                multiple: true,
                multiLine: true,
                strictMode: false,
                label: false,
                minLength: 1,
                //   syncFields: [
                //   "typeList"
                // ],
                items: {
                  type: "fieldSet",
                  title: "配置${index + 1}",
                  collapsable: true,
                  body: [
                    {
                      type: "select",
                      name: "type",
                      unique: true,
                      searchable: true,
                      clearable: true,
                      label: "业务分类",
                      required: true,
                      placeholder: "请选择",
                      source: "${typeList}",
                      valueField: "type",
                      labelField: "desc",
                      unique: true,
                      autoFill: {
                        rules: [
                          {}
                        ],
                      },
                    },
                    {
                      type: "service",
                      api: {
                        method: "get",
                        url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=5",
                        trackExpression: "${type}",
                        sendOn: "${type}",
                        data: {
                          type: "${type}",
                        },
                        adaptor: (payload, res) => {
                          const data = {
                            "code": 2000,
                            "data": {
                              "records": [
                                {
                                  "id": 6,
                                  "paramCode": "overdueDayse",
                                  "paramDesc": "4",
                                  "paramName": "overdueDayse",
                                  "type": "BASE",
                                  "typeCn": "基础信息",
                                  "updatedAt": "2025-01-08 15:02:16"
                                },
                                {
                                  "id": 4,
                                  "paramCode": "overdueDays",
                                  "paramDesc": "overdueDays",
                                  "paramName": "overdueDays",
                                  "type": "BASE",
                                  "typeCn": "基础信息",
                                  "updatedAt": "2025-01-03 15:15:15"
                                }
                              ],
                              "total": 2
                            },
                            "message": "成功"
                          }
                          return {
                            status: 0,
                            msg: "",
                            data: {
                              paramCodeList: data.data.records || [],
                            },
                          };
                        },
                      },
                      body: [
                        {
                          type: "combo",
                          name: "rules",
                          label: "准入规则",
                          multiple: true,
                          draggable: false,
                          required: true,
                          minLength: 1,
                          syncFields: [
                            "paramCodeList"
                          ],
                          strictMode: false,
                          visibleOn: "${type}",
                          items: [
                            {
                              name: "paramCode",
                              label: false,
                              type: "select",
                              required: true,
                              placeholder: "请选择",
                              valueField: "paramCode",
                              labelField: "paramName",
                              source: "${paramCodeList}",
                              autoFill: {
                                paramName: "${paramName}", // 自动带出paramName
                              },
                            },
                            {
                              type: "input-text",
                              name: "paramName",
                              visibleOn: "false", // 只有保存的时候需要，不需要展示
                            },
                            {
                              name: "paramOp",
                              label: false,
                              type: "select",
                              required: true,
                              placeholder: "请选择",
                              source: "${paramOpList}",
                              labelField: "desc",
                              valueField: "name",
                            },
                            {
                              name: "paramValue",
                              label: false,
                              required: true,
                              placeholder: "请输入",
                              type: "input-text",
                            },
                          ],
                        },
                      ],
                    },
                  ],
                },
              },
            ],
          },
        ],
        actions: [],
      },
    },
  ],
}


const demo2 = {
  type: "page",
  id: "access-rule-manage",
  data: {
    "ruleInfos": [
      {
        "rules": [
          {
            "paramCode": "age",
            "paramName": "年龄",
            "paramOp": "GREATER_EQUAL",
            "paramValue": "18"
          },
          {
            "paramCode": "age",
            "paramName": "年龄",
            "paramOp": "LESS_THAN",
            "paramValue": "60"
          },
          {
            "paramCode": "sex",
            "paramName": "性别",
            "paramOp": "IN",
            "paramValue": "男"
          },
          {
            "paramCode": "sex",
            "paramName": "性别",
            "paramOp": "IN",
            "paramValue": "女"
          },
          {
            "paramCode": "overdueDays",
            "paramName": "overdueDays",
            "paramOp": "NOT_LIKE",
            "paramValue": "5、3、5"
          },
          {
            "paramCode": "overdueDays",
            "paramName": "overdueDays",
            "paramOp": "NOT_IN",
            "paramValue": "yy"
          }
        ],
        "type": "BASE",
        "typeCn": "基础信息"
      },
      {
        "rules": [
          {
            "paramCode": "overdueDays",
            "paramName": "逾期天数2",
            "paramOp": "LESS_EQUAL",
            "paramValue": "3"
          }
        ],
        "type": "RISK",
        "typeCn": "风险信息"
      },
      {
        "rules": [
          {
            "paramCode": "loanForbiddenDays",
            "paramName": "借款禁申期天数",
            "paramOp": "EQUAL",
            "paramValue": "69"
          },
          {
            "paramCode": "creditForbiddenDays",
            "paramName": "授信禁申期天数",
            "paramOp": "GREATER_THAN",
            "paramValue": "15"
          },
          {
            "paramCode": "creditForbiddenDays",
            "paramName": "授信禁申期天数",
            "paramOp": "LESS_EQUAL",
            "paramValue": "66"
          }
        ],
        "type": "FORBIDDEN",
        "typeCn": "禁申期信息"
      }
    ]
  },
  initApi: {
    // 获取准入负责详情
    url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=1",
    method: "post",
    adaptor: (payload, res) => {
      const { code, message: msg
      } = res;
      const fundObj = {
        "businessOwner": "黄珊",
        "fund": "MSXF",
        "fundName": "马上消金轻资产",
        "updatedAt": "2025-01-08 15:37:53"
      }
      return {
        status: 0,
        msg,
        data: {
          fundName: fundObj.fundName, fund: fundObj.funds, businessOwner: fundObj.businessOwner, ruleInfos: fundObj.ruleInfos
        },
      };
    },
  },
  body: [
    {
      type: "service", // 获取业务分类
      api: {
        url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=2",
        method: "get",
        adaptor: (payload, res) => {
          const { code, message: msg
          } = res;
          const data = [
            {
              "desc": "基础信息",
              "type": "BASE"
            },
            {
              "desc": "风险信息",
              "type": "RISK"
            },
            {
              "desc": "禁申期信息",
              "type": "FORBIDDEN"
            }
          ]
          return {
            status: 0, // 2000 表示请求成功
            msg,
            data: {
              typeList: data?.map((item) => ({
                ...item, lable: item.desc, value: item.type
              })) || [],
            },
          };
        },
      },
      onEvent: {
        fetchInited: {
          actions: [
            {
              actionType: "setValue",
              componentId: "access-rule-manage",
              args: {
                value: {
                  typeList: "${typeList}", // setValue 到page上
                },
              },
            },
          ],
        },
      },
    },
    {
      type: "service",
      // api: {
      //   url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=3",
      //   method: "get",
      //   adaptor: (payload, res) => {
      //     const { code, message: msg
      //     } = res;
      //     const data = [
      //       {
      //         "desc": "大于",
      //         "name": "GREATER_THAN"
      //       },
      //       {
      //         "desc": "等于",
      //         "name": "EQUAL"
      //       },
      //       {
      //         "desc": "小于",
      //         "name": "LESS_THAN"
      //       },
      //       {
      //         "desc": "大于等于",
      //         "name": "GREATER_EQUAL"
      //       },
      //       {
      //         "desc": "小于等于",
      //         "name": "LESS_EQUAL"
      //       },
      //       {
      //         "desc": "不等于",
      //         "name": "NOT_EQUAL"
      //       },
      //       {
      //         "desc": "包括",
      //         "name": "LIKE"
      //       },
      //       {
      //         "desc": "不包括",
      //         "name": "NOT_LIKE"
      //       },
      //       {
      //         "desc": "包含",
      //         "name": "IN"
      //       },
      //       {
      //         "desc": "不包含",
      //         "name": "NOT_IN"
      //       }
      //     ]
      //     return {
      //       status: 0, // 2000 表示请求成功
      //       data: { paramOpList: data },
      //     };
      //   },
      // },
      body: {
        type: "form",
        mode: "horizontal",
        labelWidth: 80,
        body: [
          {
            type: "alert",
            body: "请按照拒绝逻辑配置规则",
            level: "warning",
            showIcon: true,
          },
          {
            type: "group",
            direction: "vertical",
            label: "资金方",
            required: true,
            body: [
              {
                type: "select",
                name: "fund",
                searchable: true,
                clearable: true,
                label: false,
                required: true,
                disabledOn: "${operType === 'edit'}", // 编辑的时候只读
                placeholder: "请选择",
                labelField: "bankName",
                valueField: "bankCode",
                source: {
                  method: "get",
                  url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=4",
                  adaptor: (payload) => {
                    const data = [
                      {
                        "assetId": "cehisyige",
                        "assetName": "cehisyige",
                        "assetsAttribute": "lightAsset",
                        "bankCode": "cehisyige",
                        "bankName": "cehisyige",
                        "funderAttribute": "P2P",
                        "publishStatus": true,
                        "totalSwitch": true
                      },
                      {
                        "assetId": "YINGXIAOHUA",
                        "assetName": "盈小花",
                        "assetsAttribute": "lightAsset",
                        "bankCode": "YINGXIAOHUA",
                        "bankName": "盈小花",
                        "cooperateModel": "LIGHT_ASSET",
                        "funderAttribute": "sameTrade",
                        "publishStatus": true,
                        "totalSwitch": true
                      }
                    ]
                    return data;
                  },
                },
                autoFill: {
                  fundName: "${bankName}",
                },
              },
              {
                type: "input-text",
                name: "fundName",
                visibleOn: "false",
              },
              {
                type: "each",
                name: "ruleInfos",
                multiple: true,
                multiLine: true,
                strictMode: false,
                label: false,
                minLength: 1,
                //   syncFields: [
                //   "typeList"
                // ],
                items: {
                  type: "fieldSet",
                  title: "配置${index + 1}",
                  collapsable: true,
                  body: [
                    {
                      type: "select",
                      name: "type",
                      unique: true,
                      searchable: true,
                      clearable: true,
                      label: "业务分类",
                      required: true,
                      placeholder: "请选择",
                      source: "${typeList}",
                      valueField: "type",
                      labelField: "desc",
                      unique: true,
                      autoFill: {
                        rules: [
                          {}
                        ],
                      },
                    },
                    {
                      type: "service",
                      // api: {
                      //   method: "get",
                      //   url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=5",
                      //   trackExpression: "${type}",
                      //   sendOn: "${type}",
                      //   data: {
                      //     type: "${type}",
                      //   },
                      //   adaptor: (payload, res) => {
                      //     const data = {
                      //       "code": 2000,
                      //       "data": {
                      //         "records": [
                      //           {
                      //             "id": 6,
                      //             "paramCode": "overdueDayse",
                      //             "paramDesc": "4",
                      //             "paramName": "overdueDayse",
                      //             "type": "BASE",
                      //             "typeCn": "基础信息",
                      //             "updatedAt": "2025-01-08 15:02:16"
                      //           },
                      //           {
                      //             "id": 4,
                      //             "paramCode": "overdueDays",
                      //             "paramDesc": "overdueDays",
                      //             "paramName": "overdueDays",
                      //             "type": "BASE",
                      //             "typeCn": "基础信息",
                      //             "updatedAt": "2025-01-03 15:15:15"
                      //           },
                      //           {
                      //             "id": 2,
                      //             "paramCode": "sex",
                      //             "paramDesc": "性别判断",
                      //             "paramName": "性别",
                      //             "type": "BASE",
                      //             "typeCn": "基础信息",
                      //             "updatedAt": "2025-01-03 15:14:13"
                      //           },
                      //           {
                      //             "id": 1,
                      //             "paramCode": "age",
                      //             "paramDesc": "年龄",
                      //             "paramName": "年龄",
                      //             "type": "BASE",
                      //             "typeCn": "基础信息",
                      //             "updatedAt": "2025-01-02 16:03:47"
                      //           }
                      //         ],
                      //         "total": 4
                      //       },
                      //       "message": "成功"
                      //     }
                      //     return {
                      //       status: 0,
                      //       msg: "",
                      //       data: {
                      //         paramCodeList: data.data.records || [],
                      //       },
                      //     };
                      //   },
                      // },
                      body: [
                        {
                          type: "combo",
                          name: "rules",
                          label: "准入规则",
                          multiple: true,
                          draggable: false,
                          required: true,
                          minLength: 1,
                          syncFields: [
                            "paramCodeList"
                          ],
                          strictMode: false,
                          visibleOn: "${type}",
                          items: [
                            {
                              name: "paramCode",
                              label: false,
                              type: "select",
                              required: true,
                              placeholder: "请选择",
                              valueField: "paramCode",
                              labelField: "paramName",
                              source: {
                                cache: 2000,
                                method: "get",
                                url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=5",
                                trackExpression: "${type}",
                                sendOn: "${type}",
                                data: {
                                  type: "${type}",
                                },
                                adaptor: (payload, res) => {
                                  const data = {
                                    "code": 2000,
                                    "data": {
                                      "records": [
                                        {
                                          "id": 6,
                                          "paramCode": "overdueDayse",
                                          "paramDesc": "4",
                                          "paramName": "overdueDayse",
                                          "type": "BASE",
                                          "typeCn": "基础信息",
                                          "updatedAt": "2025-01-08 15:02:16"
                                        },
                                        {
                                          "id": 4,
                                          "paramCode": "overdueDays",
                                          "paramDesc": "overdueDays",
                                          "paramName": "overdueDays",
                                          "type": "BASE",
                                          "typeCn": "基础信息",
                                          "updatedAt": "2025-01-03 15:15:15"
                                        },
                                        {
                                          "id": 2,
                                          "paramCode": "sex",
                                          "paramDesc": "性别判断",
                                          "paramName": "性别",
                                          "type": "BASE",
                                          "typeCn": "基础信息",
                                          "updatedAt": "2025-01-03 15:14:13"
                                        },
                                        {
                                          "id": 1,
                                          "paramCode": "age",
                                          "paramDesc": "年龄",
                                          "paramName": "年龄",
                                          "type": "BASE",
                                          "typeCn": "基础信息",
                                          "updatedAt": "2025-01-02 16:03:47"
                                        }
                                      ],
                                      "total": 4
                                    },
                                    "message": "成功"
                                  }
                                  return {
                                    status: 0,
                                    msg: "",
                                    data: {
                                      paramCodeList: data.data.records || [],
                                    },
                                  };
                                },
                              },
                              autoFill: {
                                paramName: "${paramName}", // 自动带出paramName
                              },
                            },
                            {
                              name: "paramOp",
                              label: false,
                              type: "select",
                              required: true,
                              placeholder: "请选择",
                              source: {
                                cache: 2000,
                                url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=3",
                                method: "get",
                                adaptor: (payload, res) => {
                                  const { code, message: msg
                                  } = res;
                                  const data = [
                                    {
                                      "desc": "大于",
                                      "name": "GREATER_THAN"
                                    },
                                    {
                                      "desc": "等于",
                                      "name": "EQUAL"
                                    },
                                    {
                                      "desc": "小于",
                                      "name": "LESS_THAN"
                                    },
                                    {
                                      "desc": "大于等于",
                                      "name": "GREATER_EQUAL"
                                    },
                                    {
                                      "desc": "小于等于",
                                      "name": "LESS_EQUAL"
                                    },
                                    {
                                      "desc": "不等于",
                                      "name": "NOT_EQUAL"
                                    },
                                    {
                                      "desc": "包括",
                                      "name": "LIKE"
                                    },
                                    {
                                      "desc": "不包括",
                                      "name": "NOT_LIKE"
                                    },
                                    {
                                      "desc": "包含",
                                      "name": "IN"
                                    },
                                    {
                                      "desc": "不包含",
                                      "name": "NOT_IN"
                                    }
                                  ]
                                  return {
                                    status: 0, // 2000 表示请求成功
                                    data: { paramOpList: data },
                                  };
                                },
                              },
                              labelField: "desc",
                              valueField: "name",
                            },
                            {
                              name: "paramValue",
                              label: false,
                              required: true,
                              placeholder: "请输入",
                              type: "input-text",
                            },
                          ],
                        },
                      ],
                    },
                  ],
                },
              },
            ],
          },
        ],
        actions: [],
      },
    },
  ],
}


const demo3 = {
  type: "page",
  id: "access-rule-manage",
  data: {
    "ruleInfos": [
      {
        "rules": [
          {
            "paramCode": "age",
            "paramName": "年龄",
            "paramOp": "GREATER_EQUAL",
            "paramValue": "18"
          },
          {
            "paramCode": "age",
            "paramName": "年龄",
            "paramOp": "LESS_THAN",
            "paramValue": "60"
          },
          {
            "paramCode": "sex",
            "paramName": "性别",
            "paramOp": "IN",
            "paramValue": "男"
          },
          {
            "paramCode": "sex",
            "paramName": "性别",
            "paramOp": "IN",
            "paramValue": "女"
          },
          {
            "paramCode": "overdueDays",
            "paramName": "overdueDays",
            "paramOp": "NOT_LIKE",
            "paramValue": "5、3、5"
          },
          {
            "paramCode": "overdueDays",
            "paramName": "overdueDays",
            "paramOp": "NOT_IN",
            "paramValue": "yy"
          }
        ],
        "type": "BASE",
        "typeCn": "基础信息"
      },
      {
        "rules": [
          {
            "paramCode": "overdueDays",
            "paramName": "逾期天数2",
            "paramOp": "LESS_EQUAL",
            "paramValue": "3"
          }
        ],
        "type": "RISK",
        "typeCn": "风险信息"
      },
      {
        "rules": [
          {
            "paramCode": "loanForbiddenDays",
            "paramName": "借款禁申期天数",
            "paramOp": "EQUAL",
            "paramValue": "69"
          },
          {
            "paramCode": "creditForbiddenDays",
            "paramName": "授信禁申期天数",
            "paramOp": "GREATER_THAN",
            "paramValue": "15"
          },
          {
            "paramCode": "creditForbiddenDays",
            "paramName": "授信禁申期天数",
            "paramOp": "LESS_EQUAL",
            "paramValue": "66"
          }
        ],
        "type": "FORBIDDEN",
        "typeCn": "禁申期信息"
      }
    ]
  },
  initApi: {
    // 获取准入负责详情
    url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=1",
    method: "post",
    adaptor: (payload, res) => {
      const { code, message: msg
      } = res;
      const fundObj = {
        "businessOwner": "黄珊",
        "fund": "MSXF",
        "fundName": "马上消金轻资产",
        "updatedAt": "2025-01-08 15:37:53"
      }
      return {
        status: 0,
        msg,
        data: {
          fundName: fundObj.fundName, fund: fundObj.funds, businessOwner: fundObj.businessOwner, ruleInfos: fundObj.ruleInfos
        },
      };
    },
  },
  body: [
    {
      type: "service", // 获取业务分类
      api: {
        url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=2",
        method: "get",
        adaptor: (payload, res) => {
          const { code, message: msg
          } = res;
          const data = [
            {
              "desc": "基础信息",
              "type": "BASE"
            },
            {
              "desc": "风险信息",
              "type": "RISK"
            },
            {
              "desc": "禁申期信息",
              "type": "FORBIDDEN"
            }
          ]
          return {
            status: 0, // 2000 表示请求成功
            msg,
            data: {
              typeList: data?.map((item) => ({
                ...item, lable: item.desc, value: item.type
              })) || [],
            },
          };
        },
      },
      onEvent: {
        fetchInited: {
          actions: [
            {
              actionType: "setValue",
              componentId: "access-rule-manage",
              args: {
                value: {
                  typeList: "${typeList}", // setValue 到page上
                },
              },
            },
          ],
        },
      },
    },
    {
      type: "service",
      // api: {
      //   url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=3",
      //   method: "get",
      //   adaptor: (payload, res) => {
      //     const { code, message: msg
      //     } = res;
      //     const data = [
      //       {
      //         "desc": "大于",
      //         "name": "GREATER_THAN"
      //       },
      //       {
      //         "desc": "等于",
      //         "name": "EQUAL"
      //       },
      //       {
      //         "desc": "小于",
      //         "name": "LESS_THAN"
      //       },
      //       {
      //         "desc": "大于等于",
      //         "name": "GREATER_EQUAL"
      //       },
      //       {
      //         "desc": "小于等于",
      //         "name": "LESS_EQUAL"
      //       },
      //       {
      //         "desc": "不等于",
      //         "name": "NOT_EQUAL"
      //       },
      //       {
      //         "desc": "包括",
      //         "name": "LIKE"
      //       },
      //       {
      //         "desc": "不包括",
      //         "name": "NOT_LIKE"
      //       },
      //       {
      //         "desc": "包含",
      //         "name": "IN"
      //       },
      //       {
      //         "desc": "不包含",
      //         "name": "NOT_IN"
      //       }
      //     ]
      //     return {
      //       status: 0, // 2000 表示请求成功
      //       data: { paramOpList: data },
      //     };
      //   },
      // },
      body: {
        type: "form",
        mode: "horizontal",
        labelWidth: 80,
        body: [
          {
            type: "alert",
            body: "请按照拒绝逻辑配置规则",
            level: "warning",
            showIcon: true,
          },
          {
            type: "group",
            direction: "vertical",
            label: "资金方",
            required: true,
            body: [
              {
                type: "select",
                name: "fund",
                searchable: true,
                clearable: true,
                label: false,
                required: true,
                disabledOn: "${operType === 'edit'}", // 编辑的时候只读
                placeholder: "请选择",
                labelField: "bankName",
                valueField: "bankCode",
                source: {
                  method: "get",
                  url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=4",
                  adaptor: (payload) => {
                    const data = [
                      {
                        "assetId": "cehisyige",
                        "assetName": "cehisyige",
                        "assetsAttribute": "lightAsset",
                        "bankCode": "cehisyige",
                        "bankName": "cehisyige",
                        "funderAttribute": "P2P",
                        "publishStatus": true,
                        "totalSwitch": true
                      },
                      {
                        "assetId": "YINGXIAOHUA",
                        "assetName": "盈小花",
                        "assetsAttribute": "lightAsset",
                        "bankCode": "YINGXIAOHUA",
                        "bankName": "盈小花",
                        "cooperateModel": "LIGHT_ASSET",
                        "funderAttribute": "sameTrade",
                        "publishStatus": true,
                        "totalSwitch": true
                      }
                    ]
                    return data;
                  },
                },
                autoFill: {
                  fundName: "${bankName}",
                },
              },
              {
                type: "input-text",
                name: "fundName",
                visibleOn: "false",
              },
              {
                type: "combo",
                name: "ruleInfos",
                multiple: true,
                multiLine: true,
                strictMode: false,
                label: false,
                minLength: 1,
                //   syncFields: [
                //   "typeList"
                // ],
                items: {
                  type: "fieldSet",
                  title: "配置${index + 1}",
                  collapsable: true,
                  body: [
                    {
                      type: "select",
                      name: "type",
                      unique: true,
                      searchable: true,
                      clearable: true,
                      label: "业务分类",
                      required: true,
                      placeholder: "请选择",
                      source: "${typeList}",
                      valueField: "type",
                      labelField: "desc",
                      unique: true,
                      autoFill: {
                        rules: [
                          {}
                        ],
                      },
                    },
                    {
                      type: "service",
                      // api: {
                      //   method: "get",
                      //   url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=5",
                      //   trackExpression: "${type}",
                      //   sendOn: "${type}",
                      //   data: {
                      //     type: "${type}",
                      //   },
                      //   adaptor: (payload, res) => {
                      //     const data = {
                      //       "code": 2000,
                      //       "data": {
                      //         "records": [
                      //           {
                      //             "id": 6,
                      //             "paramCode": "overdueDayse",
                      //             "paramDesc": "4",
                      //             "paramName": "overdueDayse",
                      //             "type": "BASE",
                      //             "typeCn": "基础信息",
                      //             "updatedAt": "2025-01-08 15:02:16"
                      //           },
                      //           {
                      //             "id": 4,
                      //             "paramCode": "overdueDays",
                      //             "paramDesc": "overdueDays",
                      //             "paramName": "overdueDays",
                      //             "type": "BASE",
                      //             "typeCn": "基础信息",
                      //             "updatedAt": "2025-01-03 15:15:15"
                      //           },
                      //           {
                      //             "id": 2,
                      //             "paramCode": "sex",
                      //             "paramDesc": "性别判断",
                      //             "paramName": "性别",
                      //             "type": "BASE",
                      //             "typeCn": "基础信息",
                      //             "updatedAt": "2025-01-03 15:14:13"
                      //           },
                      //           {
                      //             "id": 1,
                      //             "paramCode": "age",
                      //             "paramDesc": "年龄",
                      //             "paramName": "年龄",
                      //             "type": "BASE",
                      //             "typeCn": "基础信息",
                      //             "updatedAt": "2025-01-02 16:03:47"
                      //           }
                      //         ],
                      //         "total": 4
                      //       },
                      //       "message": "成功"
                      //     }
                      //     return {
                      //       status: 0,
                      //       msg: "",
                      //       data: {
                      //         paramCodeList: data.data.records || [],
                      //       },
                      //     };
                      //   },
                      // },
                      body: [
                        {
                          type: "combo",
                          name: "rules",
                          label: "准入规则",
                          multiple: true,
                          draggable: false,
                          required: true,
                          minLength: 1,
                          syncFields: [
                            "paramCodeList"
                          ],
                          strictMode: false,
                          visibleOn: "${type}",
                          items: [
                            {
                              name: "paramCode",
                              label: false,
                              type: "select",
                              required: true,
                              placeholder: "请选择",
                              valueField: "paramCode",
                              labelField: "paramName",
                              source: {
                                cache: 20000,
                                method: "get",
                                url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=5",
                                trackExpression: "${type}",
                                sendOn: "${type}",
                                data: {
                                  type: "${type}",
                                },
                                adaptor: (payload, res) => {
                                  const data = {
                                    "code": 2000,
                                    "data": {
                                      "records": [
                                        {
                                          "id": 6,
                                          "paramCode": "overdueDayse",
                                          "paramDesc": "4",
                                          "paramName": "overdueDayse",
                                          "type": "BASE",
                                          "typeCn": "基础信息",
                                          "updatedAt": "2025-01-08 15:02:16"
                                        },
                                        {
                                          "id": 4,
                                          "paramCode": "overdueDays",
                                          "paramDesc": "overdueDays",
                                          "paramName": "overdueDays",
                                          "type": "BASE",
                                          "typeCn": "基础信息",
                                          "updatedAt": "2025-01-03 15:15:15"
                                        },
                                        {
                                          "id": 2,
                                          "paramCode": "sex",
                                          "paramDesc": "性别判断",
                                          "paramName": "性别",
                                          "type": "BASE",
                                          "typeCn": "基础信息",
                                          "updatedAt": "2025-01-03 15:14:13"
                                        },
                                        {
                                          "id": 1,
                                          "paramCode": "age",
                                          "paramDesc": "年龄",
                                          "paramName": "年龄",
                                          "type": "BASE",
                                          "typeCn": "基础信息",
                                          "updatedAt": "2025-01-02 16:03:47"
                                        }
                                      ],
                                      "total": 4
                                    },
                                    "message": "成功"
                                  }
                                  return {
                                    status: 0,
                                    msg: "",
                                    data: {
                                      options: data.data.records || [],
                                      paramCodeList: data.data.records || [],
                                    },
                                  };
                                },
                              },
                              autoFill: {
                                paramName: "${paramName}", // 自动带出paramName
                              },
                            },
                            {
                              name: "paramOp",
                              label: false,
                              type: "select",
                              required: true,
                              placeholder: "请选择",
                              source: {
                                cache: 20000,
                                url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=3",
                                method: "get",
                                adaptor: (payload, res) => {
                                  const { code, message: msg
                                  } = res;
                                  const data = [
                                    {
                                      "desc": "大于",
                                      "name": "GREATER_THAN"
                                    },
                                    {
                                      "desc": "等于",
                                      "name": "EQUAL"
                                    },
                                    {
                                      "desc": "小于",
                                      "name": "LESS_THAN"
                                    },
                                    {
                                      "desc": "大于等于",
                                      "name": "GREATER_EQUAL"
                                    },
                                    {
                                      "desc": "小于等于",
                                      "name": "LESS_EQUAL"
                                    },
                                    {
                                      "desc": "不等于",
                                      "name": "NOT_EQUAL"
                                    },
                                    {
                                      "desc": "包括",
                                      "name": "LIKE"
                                    },
                                    {
                                      "desc": "不包括",
                                      "name": "NOT_LIKE"
                                    },
                                    {
                                      "desc": "包含",
                                      "name": "IN"
                                    },
                                    {
                                      "desc": "不包含",
                                      "name": "NOT_IN"
                                    }
                                  ]
                                  return {
                                    status: 0, // 2000 表示请求成功
                                    data: { paramOpList: data, options: data, },
                                  };
                                },
                              },
                              labelField: "desc",
                              valueField: "name",
                            },
                            {
                              name: "paramValue",
                              label: false,
                              required: true,
                              placeholder: "请输入",
                              type: "input-text",
                            },
                          ],
                        },
                      ],
                    },
                  ],
                },
              },
            ],
          },
        ],
        actions: [],
      },
    },
  ],
}

// 单个combo
const demo4 = {
  type: "page",
  id: "access-rule-manage",
  data: {
    "rules": [
      {
        "paramCode": "age",
        "paramName": "年龄",
        "paramOp": "GREATER_EQUAL",
        "paramValue": "18"
      },
      {
        "paramCode": "age",
        "paramName": "年龄",
        "paramOp": "LESS_THAN",
        "paramValue": "60"
      },
      {
        "paramCode": "sex",
        "paramName": "性别",
        "paramOp": "IN",
        "paramValue": "男"
      },
      {
        "paramCode": "sex",
        "paramName": "性别",
        "paramOp": "IN",
        "paramValue": "女"
      },
      {
        "paramCode": "overdueDays",
        "paramName": "overdueDays",
        "paramOp": "NOT_LIKE",
        "paramValue": "5、3、5"
      },
      {
        "paramCode": "overdueDays",
        "paramName": "overdueDays",
        "paramOp": "NOT_IN",
        "paramValue": "yy"
      }
    ],
  },
  body: [
    {
      type: "service",
      api: {
        // 获取操作类型
        url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=3",
        method: "get",
        adaptor: (payload, res) => {
          const { code, message: msg } = res;
          const data = [
            {
              "desc": "大于",
              "name": "GREATER_THAN"
            },
            {
              "desc": "等于",
              "name": "EQUAL"
            },
            {
              "desc": "小于",
              "name": "LESS_THAN"
            },
            {
              "desc": "大于等于",
              "name": "GREATER_EQUAL"
            },
            {
              "desc": "小于等于",
              "name": "LESS_EQUAL"
            },
            {
              "desc": "不等于",
              "name": "NOT_EQUAL"
            },
            {
              "desc": "包括",
              "name": "LIKE"
            },
            {
              "desc": "不包括",
              "name": "NOT_LIKE"
            },
            {
              "desc": "包含",
              "name": "IN"
            },
            {
              "desc": "不包含",
              "name": "NOT_IN"
            }
          ]
          const paramCodeList = [
            {
              "id": 6,
              "paramCode": "overdueDayse",
              "paramDesc": "4",
              "paramName": "overdueDayse",
              "type": "BASE",
              "typeCn": "基础信息",
              "updatedAt": "2025-01-08 15:02:16"
            },
            {
              "id": 4,
              "paramCode": "overdueDays",
              "paramDesc": "overdueDays",
              "paramName": "overdueDays",
              "type": "BASE",
              "typeCn": "基础信息",
              "updatedAt": "2025-01-03 15:15:15"
            }
          ]
          return {
            status: 0,
            data: {
              paramOpList: data,
              paramCodeList,
            },
          };
        },
      },
      body: {
        type: "form",
        mode: "horizontal",
        labelWidth: 80,
        body: [
          {
            type: "alert",
            body: "请按照拒绝逻辑配置规则",
            level: "warning",
            showIcon: true,
          },

          {
            type: "combo",
            name: "rules",
            label: "准入规则",
            multiple: true,
            required: true,
            minLength: 1,
            syncFields: [
              "paramOpList",
              "paramCodeList"
            ],
            strictMode: false,
            items: [
              {
                name: "paramCode",
                label: false,
                type: "select",
                required: true,
                placeholder: "请选择",
                valueField: "paramCode",
                labelField: "paramName",
                source: "${paramCodeList}",
                autoFill: {
                  paramName: "${paramName}", // 自动带出paramName
                },
              },
              {
                type: "input-text",
                name: "paramName",
                visibleOn: "false", // 只有保存的时候需要，不需要展示
              },
              {
                name: "paramOp",
                label: false,
                type: "select",
                required: true,
                placeholder: "请选择",
                source: "${paramOpList}",
                labelField: "desc",
                valueField: "name",
              },
              {
                name: "paramValue",
                label: false,
                required: true,
                placeholder: "请输入",
                type: "input-text",
              },
            ],
          },
        ],
        actions: [],
      },
    },
  ],
}

// combo嵌套combo + service
const demo5 = {
  type: "page",
  id: "access-rule-manage",
  data: {
    "ruleInfos": [
      {
        "rules": [
          {
            "paramCode": "age",
            "paramName": "年龄",
            "paramOp": "GREATER_EQUAL",
            "paramValue": "18"
          },
          {
            "paramCode": "age",
            "paramName": "年龄",
            "paramOp": "LESS_THAN",
            "paramValue": "60"
          },
          {
            "paramCode": "sex",
            "paramName": "性别",
            "paramOp": "IN",
            "paramValue": "男"
          },
          {
            "paramCode": "sex",
            "paramName": "性别",
            "paramOp": "IN",
            "paramValue": "女"
          },
          {
            "paramCode": "overdueDays",
            "paramName": "overdueDays",
            "paramOp": "NOT_LIKE",
            "paramValue": "5、3、5"
          },
          {
            "paramCode": "overdueDays",
            "paramName": "overdueDays",
            "paramOp": "NOT_IN",
            "paramValue": "yy"
          }
        ],
        "type": "BASE",
        "typeCn": "基础信息"
      },
      {
        "rules": [
          {
            "paramCode": "overdueDays",
            "paramName": "逾期天数2",
            "paramOp": "LESS_EQUAL",
            "paramValue": "3"
          }
        ],
        "type": "RISK",
        "typeCn": "风险信息"
      },
      {
        "rules": [
          {
            "paramCode": "loanForbiddenDays",
            "paramName": "借款禁申期天数",
            "paramOp": "EQUAL",
            "paramValue": "69"
          },
          {
            "paramCode": "creditForbiddenDays",
            "paramName": "授信禁申期天数",
            "paramOp": "GREATER_THAN",
            "paramValue": "15"
          },
          {
            "paramCode": "creditForbiddenDays",
            "paramName": "授信禁申期天数",
            "paramOp": "LESS_EQUAL",
            "paramValue": "66"
          }
        ],
        "type": "FORBIDDEN",
        "typeCn": "禁申期信息"
      }
    ]
  },
  initApi: {
    // 获取准入负责详情
    url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=1",
    method: "post",
    adaptor: (payload, res) => {
      const { code, message: msg
      } = res;
      const fundObj = {
        "businessOwner": "黄珊",
        "fund": "MSXF",
        "fundName": "马上消金轻资产",
        "updatedAt": "2025-01-08 15:37:53"
      }
      return {
        status: 0,
        msg,
        data: {
          fundName: fundObj.fundName, fund: fundObj.funds, businessOwner: fundObj.businessOwner, ruleInfos: fundObj.ruleInfos
        },
      };
    },
  },
  body: [
    {
      type: "service",
      api: {
        // 获取操作类型
        url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=3",
        method: "get",
        adaptor: (payload, res) => {
          const { code, message: msg } = res;
          const data = [
            {
              "desc": "大于",
              "name": "GREATER_THAN"
            },
            {
              "desc": "等于",
              "name": "EQUAL"
            },
            {
              "desc": "小于",
              "name": "LESS_THAN"
            },
            {
              "desc": "大于等于",
              "name": "GREATER_EQUAL"
            },
            {
              "desc": "小于等于",
              "name": "LESS_EQUAL"
            },
            {
              "desc": "不等于",
              "name": "NOT_EQUAL"
            },
            {
              "desc": "包括",
              "name": "LIKE"
            },
            {
              "desc": "不包括",
              "name": "NOT_LIKE"
            },
            {
              "desc": "包含",
              "name": "IN"
            },
            {
              "desc": "不包含",
              "name": "NOT_IN"
            }
          ]
          return {
            status: 0,
            data: { paramOpList: data },
          };
        },
      },
      body: {
        type: "form",
        mode: "horizontal",
        labelWidth: 80,
        body: [
          {
            type: "alert",
            body: "请按照拒绝逻辑配置规则",
            level: "warning",
            showIcon: true,
          },
          {
            type: "group",
            direction: "vertical",
            label: "资金方",
            required: true,
            body: [
              {
                type: "select",
                name: "fund",
                searchable: true,
                clearable: true,
                label: false,
                required: true,
                disabledOn: "${operType === 'edit'}", // 编辑的时候只读
                placeholder: "请选择",
                labelField: "bankName",
                valueField: "bankCode",
                source: {
                  method: "get",
                  url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=4",
                  adaptor: (payload) => {
                    const data = [
                      {
                        "assetId": "cehisyige",
                        "assetName": "cehisyige",
                        "assetsAttribute": "lightAsset",
                        "bankCode": "cehisyige",
                        "bankName": "cehisyige",
                        "funderAttribute": "P2P",
                        "publishStatus": true,
                        "totalSwitch": true
                      },
                      {
                        "assetId": "YINGXIAOHUA",
                        "assetName": "盈小花",
                        "assetsAttribute": "lightAsset",
                        "bankCode": "YINGXIAOHUA",
                        "bankName": "盈小花",
                        "cooperateModel": "LIGHT_ASSET",
                        "funderAttribute": "sameTrade",
                        "publishStatus": true,
                        "totalSwitch": true
                      }
                    ]
                    return data;
                  },
                },
                autoFill: {
                  fundName: "${bankName}",
                },
              },
              {
                type: "input-text",
                name: "fundName",
                visibleOn: "false",
              },
              {
                type: "combo",
                name: "ruleInfos",
                multiple: true,
                multiLine: true,
                strictMode: false,
                label: false,
                minLength: 1,
                items: {
                  type: "fieldSet",
                  title: "配置${index + 1}",
                  collapsable: true,
                  body: [
                    {
                      type: "service",
                      api: {
                        method: "get",
                        url: "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample?perPage=5",
                        trackExpression: "${type}",
                        sendOn: "${type}",
                        data: {
                          type: "${type}",
                        },
                        adaptor: (payload, res) => {
                          const data = {
                            "code": 2000,
                            "data": {
                              "records": [
                                {
                                  "id": 6,
                                  "paramCode": "overdueDayse",
                                  "paramDesc": "4",
                                  "paramName": "overdueDayse",
                                  "type": "BASE",
                                  "typeCn": "基础信息",
                                  "updatedAt": "2025-01-08 15:02:16"
                                },
                                {
                                  "id": 4,
                                  "paramCode": "overdueDays",
                                  "paramDesc": "overdueDays",
                                  "paramName": "overdueDays",
                                  "type": "BASE",
                                  "typeCn": "基础信息",
                                  "updatedAt": "2025-01-03 15:15:15"
                                }
                              ],
                              "total": 2
                            },
                            "message": "成功"
                          }
                          return {
                            status: 0,
                            msg: "",
                            data: {
                              paramCodeList: data.data.records || [],
                            },
                          };
                        },
                      },
                      body: [
                        {
                          type: "combo",
                          name: "rules",
                          label: "准入规则",
                          multiple: true,
                          draggable: false,
                          required: true,
                          minLength: 1,
                          syncFields: [
                            "paramCodeList"
                          ],
                          strictMode: false,
                          // visibleOn: "${type}",
                          items: [
                            {
                              name: "paramCode",
                              label: false,
                              type: "select",
                              required: true,
                              placeholder: "请选择",
                              valueField: "paramCode",
                              labelField: "paramName",
                              source: "${paramCodeList}",
                              autoFill: {
                                paramName: "${paramName}", // 自动带出paramName
                              },
                            },
                            {
                              type: "input-text",
                              name: "paramName",
                              visibleOn: "false", // 只有保存的时候需要，不需要展示
                            },
                            {
                              name: "paramOp",
                              label: false,
                              type: "select",
                              required: true,
                              placeholder: "请选择",
                              source: "${paramOpList}",
                              labelField: "desc",
                              valueField: "name",
                            },
                            {
                              name: "paramValue",
                              label: false,
                              required: true,
                              placeholder: "请输入",
                              type: "input-text",
                            },
                          ],
                        },
                      ],
                    },
                  ],
                },
              },
            ],
          },
        ],
        actions: [],
      },
    },
  ],
}

// 极简combo
const demo6 = {
  type: "page",
  id: "access-rule-manage",
  data: {
    "rules": [
      {
        "paramCode": "age",
        "paramName": "年龄",
        "paramOp": "GREATER_EQUAL",
        "paramValue": "18"
      },
      {
        "paramCode": "age",
        "paramName": "年龄",
        "paramOp": "LESS_THAN",
        "paramValue": "60"
      },
      {
        "paramCode": "sex",
        "paramName": "性别",
        "paramOp": "IN",
        "paramValue": "男"
      },
      {
        "paramCode": "sex",
        "paramName": "性别",
        "paramOp": "IN",
        "paramValue": "女"
      },
      {
        "paramCode": "overdueDays",
        "paramName": "overdueDays",
        "paramOp": "NOT_LIKE",
        "paramValue": "5、3、5"
      },
      {
        "paramCode": "overdueDays",
        "paramName": "overdueDays",
        "paramOp": "NOT_IN",
        "paramValue": "yy"
      }
    ],
    "paramCodeList": [
      {
        "id": 6,
        "paramCode": "overdueDayse",
        "paramDesc": "4",
        "paramName": "overdueDayse",
        "type": "BASE",
        "typeCn": "基础信息",
        "updatedAt": "2025-01-08 15:02:16"
      },
      {
        "id": 4,
        "paramCode": "overdueDays",
        "paramDesc": "overdueDays",
        "paramName": "overdueDays",
        "type": "BASE",
        "typeCn": "基础信息",
        "updatedAt": "2025-01-03 15:15:15"
      }
    ],
    "paramOpList": [
      {
        "desc": "大于",
        "name": "GREATER_THAN"
      },
      {
        "desc": "等于",
        "name": "EQUAL"
      },
      {
        "desc": "小于",
        "name": "LESS_THAN"
      },
      {
        "desc": "大于等于",
        "name": "GREATER_EQUAL"
      },
      {
        "desc": "小于等于",
        "name": "LESS_EQUAL"
      },
      {
        "desc": "不等于",
        "name": "NOT_EQUAL"
      },
      {
        "desc": "包括",
        "name": "LIKE"
      },
      {
        "desc": "不包括",
        "name": "NOT_LIKE"
      },
      {
        "desc": "包含",
        "name": "IN"
      },
      {
        "desc": "不包含",
        "name": "NOT_IN"
      }
    ],
  },
  body: [
    {
      type: "form",
      mode: "horizontal",
      labelWidth: 80,
      body: [
        {
          type: "alert",
          body: "请按照拒绝逻辑配置规则",
          level: "warning",
          showIcon: true,
        },
        {
          type: "combo",
          name: "rules",
          label: "准入规则",
          multiple: true,
          required: true,
          minLength: 1,
          syncFields: [
            "paramOpList",
            "paramCodeList"
          ],
          strictMode: false,
          items: [
            {
              name: "paramCode",
              label: false,
              type: "select",
              required: true,
              placeholder: "请选择",
              valueField: "paramCode",
              labelField: "paramName",
              source: "${paramCodeList}",
              autoFill: {
                paramName: "${paramName}", // 自动带出paramName
              },
            },
            {
              type: "input-text",
              name: "paramName",
              visibleOn: "false", // 只有保存的时候需要，不需要展示
            },
            {
              name: "paramOp",
              label: false,
              type: "select",
              required: true,
              placeholder: "请选择",
              source: "${paramOpList}",
              labelField: "desc",
              valueField: "name",
            },
            {
              name: "paramValue",
              label: false,
              required: true,
              placeholder: "请输入",
              type: "input-text",
            },
          ],
        },
      ],
      actions: [],
    },
  ],
}
