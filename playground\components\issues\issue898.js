// 百度amis没有问题
const demo = {
  "type": "page",
  "body": [
    {
      "type": "button",
      "label": "中号",
      "actionType": "dialog",
      "dialog": {
        "title": "中号",
        "size": "lg",
        // "closeDialogBusying": true,
        "body": [
          {
            "type": "crud",
            "id": "crudPage",
            "syncLocation": false,
            "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
            "headerToolbar": [
              "bulkActions"
            ],
            "bulkActions": [
              {
                "label": "批量删除",
                "actionType": "ajax",
                "api": "delete:https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample/${ids|raw}",
              },
              {
                "type": "button",
                "label": "清空选中",
                "level": "primary",
                "onEvent": {
                  "click": {
                    "actions": [
                      {
                        "actionType": "clearAll",
                        "componentId": "crudPage"
                      }
                    ]
                  }
                }
              }
            ],
            "columns": [
              {
                "name": "id",
                "label": "ID"
              }
            ]
          }
        ]
      }
    }
  ]
}

const demo1 = {
  "type": "page",
  "body": [
    {
      "type": "button",
      "label": "中号",
      "actionType": "dialog",
      "dialog": {
        "title": "中号",
        "size": "lg",
        "body": [
          {
            "type": "crud",
            "id": "crudPage",
            "syncLocation": false,
            "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
            "headerToolbar": [
              "bulkActions"
            ],
            "bulkActions": [
              {
                "label": "批量删除",
                "actionType": "ajax",
                "api": "delete:https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample/${ids|raw}",
              },
              {
                "type": "button",
                "label": "清空选中",
                "onEvent": {
                  "click": {
                    "actions": [
                      {
                        "actionType": "clearAll",
                        "componentId": "crudPage"
                      }
                    ]
                  }
                }
              }
            ],
            "columns": [
              {
                "name": "id",
                "label": "ID"
              }
            ]
          }
        ]
      }
    },
    {
      "type": "divider"
    },
    {
      "type": "button",
      "label": "清空选中",
      "onEvent": {
        "click": {
          "actions": [
            {
              "actionType": "clearAll",
              "componentId": "crudPage2"
            }
          ]
        }
      }
    },
    {
      "type": "crud",
      "id": "crudPage2",
      "syncLocation": false,
      "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample",
      "headerToolbar": [
        "bulkActions"
      ],
      "bulkActions": [
        {
          "label": "批量删除",
          "actionType": "ajax",
          "api": "delete:https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/sample/${ids|raw}",
        },
      ],
      "columns": [
        {
          "name": "id",
          "label": "ID"
        }
      ]
    }
  ]
}

export default demo;
