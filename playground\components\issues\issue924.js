const demo = {
  "type": "page",
  "id": "pg",
  "data": {
    "b": "initB"
  },
  "body": [
    "${a}",
    "${b}",
    {
      "type": "button",
      "label": "dialog",
      "actionType": "dialog",
      "dialog": {
        "title": "数据域不更新",
        // "canAccessSuperData": true,
        // "trackExpression": "${b}",
        // "data": {
        //   "&": "$$"
        // },
        "body": [
          "a: ${a}",
          "b: ${b}",
          {
            "type": "action",
            "label": "setA",
            "visibleOn": "${b == 'bbbb'}",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "actionType": "setValue",
                    "componentId": "pg",
                    "args": {
                      "value": {
                        "a": "aaaa"
                      }
                    }
                  }
                ]
              }
            }
          },
          {
            "type": "button",
            "label": "setB",
            "onEvent": {
              "click": {
                "actions": [
                  {
                    "actionType": "setValue",
                    "componentId": "pg",
                    "args": {
                      "value": {
                        "b": "bbbb"
                      }
                    }
                  }
                ]
              }
            }
          }
        ]
      }
    }
  ]
}

const demo1 = {
  "type": "page",
  "id": "pg",
  "data": {
    "b": "initB"
  },
  "body": {
    "type": "service", // service没有自己的dialog
    "data": { "s": "s" },
    "body": [
      "${a}",
      "${b}",
      {
        "type": "button",
        "label": "dialog",
        "actionType": "dialog",
        "dialog": {
          "title": "数据域不更新",
          "body": [
            "a: ${a}",
            "b: ${b}",
            {
              "type": "action",
              "label": "setA",
              "visibleOn": "${b == 'bbbb'}",
              "onEvent": {
                "click": {
                  "actions": [
                    {
                      "actionType": "setValue",
                      "componentId": "pg",
                      "args": {
                        "value": {
                          "a": "aaaa"
                        }
                      }
                    }
                  ]
                }
              }
            },
            {
              "type": "button",
              "label": "setB",
              "onEvent": {
                "click": {
                  "actions": [
                    {
                      "actionType": "setValue",
                      "componentId": "pg",
                      "args": {
                        "value": {
                          "b": "bbbb"
                        }
                      }
                    }
                  ]
                }
              }
            }
          ]
        }
      }
    ]
  }
}

export default demo
