// Combo 测试
// export default {
//   "type": "page",
//   "body": {
//     "type": "form",
//     "data": {
//       "combo666": [
//         {
//           "text": "text1",
//           "select": "a"
//         },
//         {
//           "text": "text2",
//           "select": "b"
//         }
//       ]
//     },
//     "debug": true,
//     "mode": "horizontal",
//     "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
//     "body": [
//       {
//         "type": "combo",
//         "name": "combo666",
//         "label": "唯一",
//         "multiple": true,
//         "minLength": 3,
//         "messages": {
//           "minLengthValidateFailed": "${minLength}个以上才合格"
//         },
//         // "validations": {
//         //   "isRequired": '${false}'
//         // },
//         "items": [
//           {
//             "name": "text",
//             "type": "input-text",
//             "placeholder": "文本",
//             // "unique": true
//           },
//           {
//             "name": "select",
//             "type": "select",
//             "options": [
//               "a",
//               "b",
//               "c",
//               "d",
//             ],
//             // "unique": true
//           }
//         ]
//       }
//     ]
//   }
// }

// input-table 全量校验测试
export default {
  "type": "page",
  "body": {
    "type": "form",
    "debug": true,
    "data": {
      "minLength": 1,
      "table": [
        {
          // "a": "a1",
          "b": "b1",
          "memory": '100',
        },
        {
          "a": "a2",
          "b": "b2",
          "memory": '200',
        },
        {
          "a": "a3",
          "b": "b3",
          "memory": '300',
        },
        {
          "a": "a4",
          "b": "b4",
          "memory": '400',
        },
        {
          "a": "a5",
          "b": "b5",
          "memory": '500',
        },
        {
          "a": "a6",
          "b": "b6",
          "memory": '600',
        }
      ]
    },
    "api": "/amis/api/mock2/form/saveForm",
    "body": [
      {
        "type": "input-table",
        "name": "table",
        "label": "Table",
        "isFullValidate": true,
        "addable": true,
        "removable": true,
        "needConfirm": false,
        "perPage": 2,
        "minLength": "$minLength",
        // "validations": {
        //   "isUnique": [['a', 'b'], ["A", "B"]],
        //   "isUnique$": ['a'],
        // },
        // "validationErrors": {
        //   "isUnique": "XX和XX不存在",
        //   "isUnique$": "XX不存在",
        // },
        "columns": [
          {
            "label": "A",
            "name": "a",
            "disabled": true,
            // "type": "input-text",
            // "validations": {
            //   "isRequired": true,
            // }
            // "unique": true,
            "required": true,
          },
          {
            "name": "colors",
            "label": "颜色集合",
            "type": "input-array",
            // "value": [
            //   "red"
            // ],
            "inline": true,
            // "required": true,
            "items": {
              "type": "input-color",
              "clearable": false,
              "required": true,
            }
          },
          {
            "label": "B",
            "name": "b",
            // "unique": true,
            "quickEdit": {
              "type": "select",
              "clearable": true,
              "required": true,
              "options": [
                "b1",
                "b2",
                "b3",
                "b4",
                "b5",
                "b6",
                "b7",
                "b8",
              ]
            },
          },
          {
            "type": "combo",
            "name": "c",
            "multiLine": true,
            // "required": true,
            "multiple": true,
            // "minLength": "${minLength}",
            "items": [
              {
                "type": "input-text",
                "name": "c1",
                "required": true,
                "disabled": true,
                "visibleOn": "${c2 !== '1'}"
              },
              {
                "type": "input-text",
                "name": "c2"
              }
            ]
          },
          {
            "type": "input-group",
            "label": "各种组合",
            "name": "test",
            "body": [
              {
                "type": "select",
                "name": "memoryUnits",
                "options": [
                  {
                    "label": "Gi",
                    "value": "Gi"
                  },
                  {
                    "label": "Mi",
                    "value": "Mi"
                  },
                  {
                    "label": "Ki",
                    "value": "Ki"
                  }
                ],
                "value": "Gi"
              },
              {
                "type": "input-text",
                "name": "memory",
                "required": true,
              },
              {
                "type": "select",
                "name": "memoryUnits2",
                "options": [
                  {
                    "label": "Gi",
                    "value": "Gi"
                  },
                  {
                    "label": "Mi",
                    "value": "Mi"
                  },
                  {
                    "label": "Ki",
                    "value": "Ki"
                  }
                ],
                "value": "Gi"
              }
            ]
          }
        ]
      }
    ]
  }
}

// input-table unique 测试
// export default {
//   "type": "page",
//   "body": {
//     "type": "form",
//     "debug": true,
//     "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
//     "body": [
//       {
//         "type": "input-table",
//         "name": "table",
//         "label": "Table",
//         "addable": true,
//         "removable": true,
//         "needConfirm": false,
//         "perPage": 2,
//         "isFullValidate": true,
//         "validations": {
//           "isUnique": [["userName", "gender"], ["用户名", "性别"]],
//           "isUnique$": [["age"], ["年龄"]]
//         },
//         "columns": [
//           {
//             "label": "用户名",
//             "name": "userName",
//             "type": "input-text",
//             "unique": true,
//           },
//           {
//             "label": "性别",
//             "name": "gendar",
//             "type": "select",
//             "unique": true,
//             "options": [
//               {
//                 "label": "男",
//                 "value": "male"
//               },
//               {
//                 "label": "女",
//                 "value": "female"
//               }
//             ]
//           },
//           {
//             "label": "年龄",
//             "name": "age",
//             "unique": true,
//             "type": "input-number",
//             "min": 1,
//             "max": 100
//           }
//         ]
//       }
//     ]
//   }
// }

// combo isunique 测试
// export default {
//   "type": "page",
//   "body": {
//     "title": "",
//     "type": "form",
//     "debug": true,
//     "api": "/tet",
//     "mode": "horizontal",
//     "autoFocus": false,
//     "body": [
//       {
//         "type": "combo",
//         "name": "combo101",
//         "label": "组合多条多行",
//         "multiple": true,
//         "multiLine": true,
//         "unmountOnExit": true,
//         "value": [
//           {}
//         ],
//         "tabsMode": true,
//         "tabsStyle": "card",
//         "maxLength": 3,
//         "items": [
//           {
//             "name": "a",
//             "label": "文本",
//             "type": "input-text",
//             "placeholder": "文本",
//             "value": "",
//             "required": true,
//             "size": "full"
//           },
//           {
//             "name": "b",
//             "label": "选项",
//             "type": "select",
//             "options": [
//               "a",
//               "b",
//               "c"
//             ],
//             "size": "full"
//           }
//         ]
//       }
//     ]
//   }
// }

// input-array 测试
// export default{
//   "type": "page",
//   "body": {
//     "type": "form",
//     "debug": true,
//     "debugConfig": {
//       "levelExpand": 2
//     },
//     "body": [
//       {
//         "name": "array",
//         "label": "整数集合",
//         "mode": "horizontal",
//         "type": "input-array",
//         "minLength": 3,
//         "value": [
//           123,
//           456
//         ],
//         "scaffold": 0,
//         "inline": true,
//         "items": {
//           "type": "input-number",
//           "required": true
//         }
//       }
//     ]
//   }
// }

// submit api 测试
// export default {
//   "type": "page",
//   "body": {
//     "type": "form",
//     "debug": true,
//     "mode": "horizontal",
//     "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveFormFailedTable?waitSeconds=1",
//     "body": [
//       {
//         "label": "Table 服务端校验",
//         "type": "input-table",
//         "name": "table",
//         "multiple": true,
//         "value": [
//           {
//             "a": "a1",
//             "b": "b1"
//           },
//           {
//             "a": "a2",
//             "b": "b2"
//           }
//         ],
//         "columns": [
//           {
//             "name": "a",
//             "type": "text",
//             "label": "A",
//             "quickEdit": {
//               "mode": "inline"
//             }
//           },
//           {
//             "name": "b",
//             "type": "text",
//             "label": "B",
//             "quickEdit": {
//               "mode": "inline"
//             }
//           }
//         ]
//       },
//       {
//         "label": "Combo 内 Table 服务端校验",
//         "type": "combo",
//         "name": "combo",
//         "items": [
//           {
//             "name": "a",
//             "type": "text",
//             "label": "A"
//           },
//           {
//             "label": "Table",
//             "type": "input-table",
//             "name": "table",
//             "multiple": true,
//             "value": [
//               {
//                 "a": "a1",
//                 "b": "b1"
//               },
//               {
//                 "a": "a2",
//                 "b": "b2"
//               }
//             ],
//             "columns": [
//               {
//                 "name": "a",
//                 "type": "text",
//                 "label": "A",
//                 "quickEdit": {
//                   "mode": "inline"
//                 }
//               },
//               {
//                 "name": "b",
//                 "type": "text",
//                 "label": "B",
//                 "quickEdit": {
//                   "mode": "inline"
//                 }
//               }
//             ]
//           }
//         ]
//       }
//     ]
//   }
// }

// condition-builder 测试
// export default {
//   "type": "page",
//   "body": {
//     "type": "form",
//     "id": "myForm",
//     "api": "/tet",
//     "debug": true,
//     "body": [
//       {
//         "type": "button",
//         "label": "validate动作",
//         "onEvent": {
//           "click": {
//             "actions": [
//               {
//                 "actionType": "validate",
//                 "componentId": "myForm",
//                 "args": {
//                   "validateFields": [
//                     "conditions",
//                     "field1",
//                   ]
//                 }
//               }
//             ]
//           }
//         }
//       },
//       {
//         "type": "condition-builder",
//         "label": "条件组件",
//         "name": "conditions",
//         "description": "适合让用户自己拼查询条件，然后后端根据数据生成 query where",
//         "searchable": true,
//         "required": true,
//         "conditionItemBody": [
//           {
//             "type": "input-text",
//             "name": "field1",
//             "label": "字段1",
//             "required": true,
//           },
//           {
//             "name": "field2",
//             "type": "input-text",
//             "label": "字段2",
//             "required": true,
//           }
//         ],
//         "rootCondiOptions": [
//           {
//             "label": "条件1",
//             "value": "test1"
//           },
//           {
//             "label": "条件2",
//             "value": "test2"
//           }
//         ],
//         "leafCondiOptions": [
//           {
//             "label": "条件3",
//             "value": "test3"
//           },
//           {
//             "label": "条件4",
//             "value": "test4"
//           }
//         ]
//       }
//     ]
//   }
// }

// quickEdit 测试
// export default {
//   "type": "page",
//   "body": {
//     "type": "form",
//     "debug": true,
//     "data": {
//       "table": [
//         {
//           "a": "a1",
//           "b": "b1",
//           "c": {
//             "name": "c1",
//             "id": "C1"
//           }
//         },
//         {
//           "a": "a2",
//           "b": "b2",
//           "c": {
//             "name": "c2",
//             "id": "C2"
//           }
//         },
//         {
//           "a": "a3",
//           "b": "b3",
//           "c": {
//             "name": "c3",
//             "id": "C3"
//           }
//         }
//       ]
//     },
//     "api": "https://aliyunfc-amis-mock-gmecwxibod.cn-beijing.fcapp.run/api/amis-mock/mock2/form/saveForm",
//     "body": [
//       {
//         "type": "input-table",
//         "name": "table",
//         "label": "Table",
//         "needConfirm": false,
//         "columns": [
//           {
//             "label": "A",
//             "name": "a",
//             "quickEdit": {
//               "type": "select",
//               "options": ["a1", "a2", "a3"],
//               "required": true,
//               "clearable": true
//             }
//           },
//           {
//             "label": "B",
//             "name": "b",
//             "quickEdit": true,
//             "required": true
//           },
//           {
//             "label": "C",
//             "name": "c.name",

//             "quickEdit": {
//               "type": "select",
//               "name": "c",
//               "labelField": "name",
//               "valueField": "id",
//               "joinValues": false,
//               "options": [
//                 {
//                   "name": "c1",
//                   "id": "C1"
//                 },
//                 {
//                   "name": "c2",
//                   "id": "C2"
//                 },
//                 {
//                   "name": "c3",
//                   "id": "C3"
//                 }
//               ]
//             }
//           },
//         ]
//       }
//     ]
//   }
// }
