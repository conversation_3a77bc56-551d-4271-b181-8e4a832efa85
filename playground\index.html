<!DOCTYPE html>
<html lang="zh">

<head>
  <meta charset="UTF-8" />
  <title>本地开发Demo页面</title>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <link type="image/x-icon" rel="shortcut icon" href="../examples/static/favicon.png" />
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
  <meta http-equiv="X-UA-Compatible" content="IE=Edge" />
  <link rel="stylesheet" href="../examples/static/iconfont.css" />
  <link rel="stylesheet" href="../examples/static/officefont.css" />
  <link rel="stylesheet" href="../node_modules/@fortawesome/fontawesome-free/css/all.css" />
  <link
    rel="stylesheet"
    href="../node_modules/@fortawesome/fontawesome-free/css/v4-shims.css"
  />
  <link rel="stylesheet" href="../node_modules/prismjs/themes/prism.css" />
  <!--DEPENDENCIES_INJECT_PLACEHOLDER-->
  <link rel="stylesheet" href="../examples/doc.css" />

  <link rel="stylesheet" href="../examples/style.scss" />
  <style>
    * {
      margin: 0;
      padding: 0;
    }

    #root {
      padding: 16px;
    }

    /* table th {
      white-space: pre-line !important;
    } */
  </style>
  <script type="module">
    let theme = 'antd';
    // let theme = 'cxd';
    ['ang', 'cxd', 'antd'].forEach(key => {
      const link = document.createElement('link');
      link.setAttribute('rel', 'stylesheet');
      link.setAttribute('title', key);
      if (theme !== key) {
        link.setAttribute('disabled', 'disabled');
      }
      link.setAttribute(
        'href',
        new URL(`../packages/amis-ui/scss/themes/${key}.scss`, import.meta.url)
          .href
      );
      document.head.appendChild(link);
    });
  </script>
  <link rel="stylesheet" href="../packages/amis-ui/scss/helper.scss" />
</head>

<body>
  <div id="root" class="app-wrapper"></div>
  <script type="module">
    import { bootstrap } from './index.jsx';

    window.enableAMISDebug = true;

    const initialState = {};
    bootstrap(document.getElementById('root'), initialState);
  </script>
</body>

</html>
