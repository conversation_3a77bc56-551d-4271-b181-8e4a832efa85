const fs = require('fs');

try {
  const pacakgeList = [
    {
      name: 'amis',
      dependencies: ['amis-core', 'amis-ui', 'dataseed-ui'],
    },
    {
      name: 'amis-core',
      dependencies: ['amis-formula'],
    },
    /** {
    name: 'amis-editor',
    dependencies: ['amis-editor-core']
  }, {
    name: 'amis-editor-core',
    dependencies: []
  }, */ {
      name: 'amis-formula',
      dependencies: [],
    },
    {
      name: 'amis-utils',
      dependencies: ['amis-core'],
    },
    {
      name: 'amis-ui',
      dependencies: ['amis-core', 'amis-formula', 'dataseed-ui'],
    },
    {
      name: 'dataseed-ui',
      dependencies: ['amis-core', 'amis-formula'],
    } /**, {
    name: 'ooxml-viewer',
    dependencies: ['amis-formula']
  }*/,
  ];
  const lernaJson = JSON.parse(fs.readFileSync(`./lerna.json`));
  if (!lernaJson || !lernaJson.version) {
    throw new Error('lerna.json must be existed and had version config');
  }
  const {version} = lernaJson;
  pacakgeList.forEach(item => {
    const {name, dependencies} = item;
    const jsonData = JSON.parse(
      fs.readFileSync(`./tempPkg/packages/${name}/package.json`),
    );
    console.log('\x1b[32m%s\x1b[0m', '=========================');
    console.log(
      '\x1b[32m%s\x1b[0m',
      `name: ${name}; version: ${jsonData.version} -> new version: ${version}`,
    );
    jsonData.version = version;
    dependencies.forEach(dep => {
      if (jsonData['dependencies'][`@dataseed/${dep}`]) {
        console.log(
          '\x1b[32m%s\x1b[0m',
          `name: @dataseed/${dep}; version: ${
            jsonData['dependencies'][`@dataseed/${dep}`]
          } -> new version: ${version}`,
        );
        jsonData['dependencies'][`@dataseed/${dep}`] = version;
      }
    });
    fs.writeFileSync(
      `./tempPkg/packages/${name}/package.json`,
      JSON.stringify(jsonData, null, 2),
      'utf8',
    );
  });
} catch (error) {
  console.log('\x1b[31m%s\x1b[0m', '❌ [amis] 替换packages包的版本失败!');
  console.log('\x1b[31m%s\x1b[0m', JSON.stringify(error));
  process.exit(1);
}
