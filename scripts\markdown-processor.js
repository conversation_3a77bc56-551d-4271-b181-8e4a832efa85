/* eslint-disable */

// 统一的 Markdown 处理器
// 支持 Vite 环境

var marked = require('marked');
var yaml = require('js-yaml');
var rYml = /^\s*---([\s\S]*?)---\s/;

// 统一的 Prism 管理
let prism;
let prismInitialized = false;

function initializePrism() {
  if (prismInitialized) return prism;

  try {
    prism = require('prismjs');

    // 确保 Prism 全局对象存在
    if (typeof global !== 'undefined' && !global.Prism) {
      global.Prism = prism;
    }

    // 安全加载语言组件
    try {
      let loadLanguages = require('prismjs/components/');
      loadLanguages([
        'bash',
        'javascript',
        'java',
        'python',
        'jsx',
        'tsx',
        'css',
        'markup',
        'json'
      ]);
    } catch (langError) {
      console.warn('Prism 语言组件加载失败:', langError);
    }

    prismInitialized = true;
  } catch (error) {
    console.warn('Prism 初始化失败:', error);
    // 创建一个简单的 fallback 对象
    prism = {
      languages: {},
      highlight: function(code) { return code; }
    };
    prismInitialized = true;
  }

  return prism;
}

// 设置 marked 配置
var renderer = new marked.Renderer();
marked.setOptions({
  renderer: renderer,
  gfm: true,
  tables: true,
  breaks: false,
  pedantic: false,
  smartLists: true,
  smartypants: false
});



/**
 * 解析代码块属性
 */
function parseAttributes(attr) {
  const setting = {};
  if (!attr) return setting;

  // 支持两种格式：
  // 1. key=value 格式（原有格式）
  // 2. key="value" 或 key='value' 格式（新增支持）

  // 使用正则表达式匹配 key="value" 或 key='value' 或 key=value 格式
  const regex = /(\w+)=(?:"([^"]*)"|'([^']*)'|([^\s]+))/g;
  let match;

  while ((match = regex.exec(attr)) !== null) {
    const key = match[1];
    const value = match[2] || match[3] || match[4] || '';

    setting[key] = decodeURIComponent(value);

    if (key === 'height') {
      setting.height = parseInt(setting.height, 10);
    }
  }

  return setting;
}

/**
 * 统一的文件读取函数
 */
function readFileContent(filePath) {
  try {
    const fs = require('fs');
    const path = require('path');
    const fullPath = path.resolve(process.cwd(), filePath);
    return fs.readFileSync(fullPath, { encoding: 'utf-8' });
  } catch (error) {
    console.warn(`无法读取文件: ${filePath}`, error);
    return null;
  }
}

/**
 * 处理 include 指令（统一处理）
 */
function processIncludes(content) {
  // 处理 !!!include 指令（packages 目录下的文件）
  content = content.replace(/\!\!\!include\s*\(([^\)]+?)\)\!\!\!/g, (_, val) => {
    const filePath = `packages/${val}`;
    const fileContent = readFileContent(filePath);
    return fileContent || `// 无法读取文件: ${filePath}`;
  });

  // 处理 !!!includeFile 指令（项目根目录下的任意文件）
  content = content.replace(/\!\!\!includeFile\s*\(([^\)]+?)\)\!\!\!/g, (_, filePath) => {
    const fileContent = readFileContent(filePath);
    return fileContent || `// 无法读取文件: ${filePath}`;
  });

  return content;
}

/**
 * 创建代码块处理器
 */
function createHandlers() {
  const prismInstance = initializePrism();

  return {
    html: (code, setting, attr, options) => {
      if (~code.indexOf('<html') || ~code.indexOf('<link')) {
        return null;
      }
      return `<!--amis-preview-start--><div class="amis-doc"><div class="preview">${code}</div><pre><code class="lang-html">${prismInstance.highlight(
        code
          .replace(/"data:(\w+\/\w+);.*?"/g, '"data:$1; ..."')
          .replace(/<svg([^>]*)>[\s\S]*?<\/svg>/g, '<svg$1>...</svg>')
          .replace(/class="([^"]*?)\.\.\.([^"]*?)"/g, 'class="$1..."'),
        prismInstance.languages.html,
        'html'
      )}</code></pre></div><!--amis-preview-end-->`;
    },

    mermaid: (code, setting, attr, options) => {
      return `<!--amis-preview-start--><div class="amis-mermaid-preview"><script type="text/mermaid">${code}</script></div><!--amis-preview-end-->`;
    },

    schema: (code, setting, attr, options) => {
      return `<!--amis-preview-start--><div class="amis-preview" style="min-height: ${setting.height || 200}px"><script type="text/schema" ${attr || ''}>${code}</script></div><!--amis-preview-end-->`;
    },

    collapsible: (code, setting, attr, options) => {
      const title = setting.title || '点击展开/收起代码';
      const defaultOpen = setting.open === 'true' || setting.open === true;
      const language = setting.lang || 'javascript';

      // 处理代码：去掉export default以便在编辑器中直接运行
      const processedCode = code
        .replace(/^export\s+default\s+/, '') // 去掉开头的 export default
        .replace(/^export\s+default\s*\{/, '{') // 处理 export default { 的情况
        .trim();

      // collapsible 代码块默认使用 Monaco Editor，除非明确禁用
      const disableMonaco = setting.monaco === 'false' || setting.editor === 'false';
      const monacoAttr = disableMonaco ? '' : ' data-monaco="true"';

      // 不进行高亮处理，让 Monaco Editor 来处理
      const rawCode = escape(processedCode);

      return `<!--amis-preview-start--><div class="collapsible-code-block ${defaultOpen ? '' : 'collapsed'}"${monacoAttr}>
        <div class="collapsible-header" onclick="this.parentNode.classList.toggle('collapsed')">
          <i class="collapsible-icon"></i>
          <span class="collapsible-title">${title}</span>
          <button class="collapsible-copy-btn" onclick="event.stopPropagation(); navigator.clipboard.writeText(decodeURIComponent(this.getAttribute('data-code'))).then(() => { this.textContent = '已复制!'; setTimeout(() => this.textContent = '复制', 1000); })" data-code="${encodeURIComponent(processedCode).replace(/'/g, '&#39;')}" title="复制代码">复制</button>
        </div>
        <div class="collapsible-content">
          <pre><code class="lang-${language}">${rawCode}</code></pre>
        </div>
      </div><!--amis-preview-end-->`;
    }
  };
}

/**
 * 设置链接渲染器
 */
function setupLinkRenderer() {
  // 链接处理逻辑
  renderer.link = function (href, title, text) {
    if (this.options.sanitize) {
      try {
        var prot = decodeURIComponent(unescape(href))
          .replace(/[^\w:]/g, '')
          .toLowerCase();
      } catch (e) {
        return '';
      }
      if (prot.indexOf('javascript:') === 0 || prot.indexOf('vbscript:') === 0) {
        return '';
      }
    }
    if(href.indexOf('/dataseeddesigndocui/#/amis/') > -1) {
      href = href.replace('/dataseeddesigndocui/#/amis/', '');
      if (href && href[0] === '#') {
        href =
          '?anchor=' +
          encodeURIComponent(
            href
              .substring(1)
              .toLowerCase()
              .replace(/[^\u4e00-\u9fa5_a-zA-Z0-9]+/g, '-')
          );
      } else if(href && href.indexOf('#') > 0) {
        const hrefArr = href.split('#');
        let _hrefArr = [];
        hrefArr.forEach((pathStr, index) => {
          if (index === 0) {
            _hrefArr.push(pathStr);
            return;
          }
          _hrefArr.push(`?anchor=${pathStr}`);
        })
        href = _hrefArr.join('');
      }
      href = `/dataseeddesigndocui/#/amis/${href}`;
    }

    var out = '<a href="' +  href + '"';
    if (title) {
      out += ' title="' + title + '"';
    }
    out += '>' + text + '</a>';
    return out;
  };
}

/**
 * 设置自定义渲染器和高亮配置
 */
function setupCustomRenderer() {
  const prismInstance = initializePrism();
  const handlers = createHandlers();

  // 自定义代码块渲染器
  renderer.code = function (code, language, escaped) {
    if (!language) {
      return `<pre><code>${escaped ? code : escape(code)}</code></pre>`;
    }

    // 解析语言和属性
    let actualLanguage = language;
    let attr = '';
    let setting = {};

    // 支持两种格式：
    // 1. schema:height="200" 格式（冒号分隔）
    // 2. collapsible title="xxx" lang="xxx" 格式（空格分隔）

    if (language.includes(':')) {
      // 格式1：schema:height="200"
      const parts = language.split(':');
      actualLanguage = parts[0];
      attr = parts[1] || '';
      setting = parseAttributes(attr);
    } else if (language.includes(' ')) {
      // 格式2：collapsible title="xxx" lang="xxx" open="false"
      const parts = language.split(' ');
      actualLanguage = parts[0];
      attr = parts.slice(1).join(' ');
      setting = parseAttributes(attr);
    }

    // 处理特殊语言类型
    if (actualLanguage && handlers[actualLanguage]) {
      const result = handlers[actualLanguage](code, setting, attr);
      return result || `<pre><code class="lang-${actualLanguage}">${escaped ? code : escape(code)}</code></pre>`;
    }

    // 标准语言高亮
    if (actualLanguage && prismInstance.languages[actualLanguage]) {
      const highlighted = prismInstance.highlight(code, prismInstance.languages[actualLanguage], actualLanguage);
      return `<pre><code class="lang-${actualLanguage}">${highlighted}</code></pre>`;
    }

    // 默认处理
    return `<pre><code class="lang-${actualLanguage}">${escaped ? code : escape(code)}</code></pre>`;
  };

  marked.setOptions({ renderer });
}

/**
 * HTML 转义函数
 */
function escape(html) {
  return html
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');
}

/**
 * 主处理函数
 * @param {string} content - markdown 内容
 * @returns {string} 处理后的 JS 模块代码
 */
function processMarkdown(content) {
  // 解析 YAML 头部
  var m = rYml.exec(content);
  var info = {};
  if (m && m[1]) {
    info = yaml.load(m[1]);
    content = content.substring(m[0].length);
  }

  // 注册自定义渲染器（如代码块、链接等），确保 marked.js 解析时能正确处理特殊语法和自定义代码块
  setupLinkRenderer();
  setupCustomRenderer();

  // 构建目录
  var toc = {
    label: '目录',
    type: 'toc',
    children: [],
    level: 0
  };
  var stack = [toc];

  renderer.heading = function (text, level) {
    var escapedText = encodeURIComponent(
      text.toLowerCase().replace(/[^\u4e00-\u9fa5_a-zA-Z0-9]+/g, '-')
    );

    if (level < 5) {
      var menu = {
        label: text,
        fragment: escapedText,
        fullPath: '#' + escapedText,
        level: level
      };

      while (stack.length && stack[0].level >= level) {
        stack.shift();
      }

      stack[0].children = stack[0].children || [];
      stack[0].children.push(menu);

      stack.unshift(menu);
    }

    var anchor =
      '<a class="anchor" name="' +
      escapedText +
      '" aria-hidden="true"><svg aria-hidden="true" class="octicon octicon-link" height="16" version="1.1" viewBox="0 0 16 16" width="16"><path d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>';

    return '<h' + level + '>' + anchor + text + '</h' + level + '>';
  };

  // 第一步：处理 include 指令
  content = processIncludes(content);

  // 第二步：marked 解析（包含自定义渲染器处理特殊代码块）
  content = marked.parse(content);

  // 第三步：生成最终HTML
  info.html =
    '<div class="markdown-body">' +
    content.replace(
      /<\!\-\-amis\-preview\-(start|end)\-\-\>/g,
      function (_, type) {
        return type === 'start' ? '</div>' : '<div class="markdown-body">';
      }
    ) +
    '</div>';
  info.toc = toc;

  // 第四步：返回模块代码（使用 ES6 模块导出）
  return 'export default ' + JSON.stringify(info, null, 2) + ';';
}

module.exports = processMarkdown;
