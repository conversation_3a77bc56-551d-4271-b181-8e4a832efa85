#!/bin/bash
set -e

# 用于站点打包后(npm run vite:build)本地预览

echo "Cleaning up previous build..."
rm -rf dataseeddesigndocui
# npm run vite:build

# Create proper directory structure for serving
# Use copy instead of move to avoid permission issues
echo "Setting up preview directory..."
cp -r dist dataseeddesigndocui

echo "Starting local server..."
echo "Visit: http://localhost:3000/dataseeddesigndocui/"
npx serve . --listen 3000
