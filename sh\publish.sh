#!/bin/bash
set -e

if [ "$1" != "nobuild" ]; then
  npm run build --workspace=amis-formula --workspace=amis-utils --workspace=amis-core --workspace=amis-ui --workspace=dataseed-ui --workspace=@dataseed/pro-components --workspace=amis
else
  echo "nobuild"
fi

rm -rf tempPkg
mkdir -p tempPkg/packages

# 如果有问题可以注释掉这两行，不知道为啥会导致 cp -rf 挂掉
# rm -rf packages/amis/node_modules/.bin
# rm -rf packages/amis-ui/node_modules/.bin

cp -r packages/{amis-formula,amis-utils,amis-core,amis-ui,dataseed-ui,dataseed-pro,amis} tempPkg/packages
cp package.json tempPkg
cp lerna.json tempPkg
cp .npmrc tempPkg

# 记录last commit，便于区分内网版本包之间的差异
REVISION=revision.json
npm run revision -- $REVISION

if [ -f "$REVISION" ]; then
  for dir in $(find ./tempPkg/packages -mindepth 1 -maxdepth 1 -type d); do
    [ -d "$dir" ] && cp $REVISION "$dir/$REVISION";
  done;
else
  echo "$REVISION not exists."
fi

cd tempPkg

# package.json 里面把包名称换了
for f in $(find ./packages -name "package.json"); do
  # 第一个替换：将 "name": "amis" 改为 "@dataseed/amis"
  sed -i -e 's/\"name\": \"amis/\"name\": \"@dataseed\/amis/g' $f
  # 第二个替换：将dependencies里的"amis-" 开头的包名改为 "@dataseed/amis-"
  sed -i -e 's/\"amis-/\"@dataseed\/amis-/g' $f
  # 新增替换：将 "name": "dataseed-ui" 改为 "@dataseed/dataseed-ui"
  sed -i -e 's/\"name\": \"dataseed-ui\"/\"name\": \"@dataseed\/dataseed-ui\"/g' $f
  # 新增替换：将dependencies里的"dataseed-ui" 包名改为 "@dataseed/dataseed-ui"
  sed -i -e 's/\"dataseed-ui\"/\"@dataseed\/dataseed-ui\"/g' $f
done

# 把代码里面import的部分换成内部包名称
for f in $(find ./packages/*/lib -type f -name "*.[tj]s"); do
  # 替换 amis 相关引用
  sed -i -e $'s/from \'amis/from \'@dataseed\/amis/g' $f
  sed -i -e $'s/import(\'amis/import(\'@dataseed\/amis/g' $f
  sed -i -e $'s/import[ ]*\'amis/import \'@dataseed\/amis/g' $f
  sed -i -e $'s/require(\'amis/require(\'@dataseed\/amis/g' $f
  sed -i -e $'s/require(\[\'amis/require(\[\'@dataseed\/amis/g' $f

  # 新增 dataseed-ui 替换规则
  sed -i -e $'s/from \'dataseed-ui/from \'@dataseed\/dataseed-ui/g' $f
  sed -i -e $'s/import(\'dataseed-ui/import(\'@dataseed\/dataseed-ui/g' $f
  sed -i -e $'s/import[ ]*\'dataseed-ui/import \'@dataseed\/dataseed-ui/g' $f
  sed -i -e $'s/require(\'dataseed-ui/require(\'@dataseed\/dataseed-ui/g' $f
  sed -i -e $'s/require($\'dataseed-ui/require($\'@dataseed\/dataseed-ui/g' $f
done

for f in $(find ./packages/*/esm -type f -name "*.[tj]s"); do
  # 替换 amis 相关引用
  sed -i -e $'s/from \'amis/from \'@dataseed\/amis/g' $f
  sed -i -e $'s/import(\'amis/import(\'@dataseed\/amis/g' $f
  sed -i -e $'s/import[ ]*\'amis/import \'@dataseed\/amis/g' $f
  sed -i -e $'s/require(\'amis/require(\'@dataseed\/amis/g' $f
  sed -i -e $'s/require(\[\'amis/require(\[\'@dataseed\/amis/g' $f

  # 新增 dataseed-ui 替换规则
  sed -i -e $'s/from \'dataseed-ui/from \'@dataseed\/dataseed-ui/g' $f
  sed -i -e $'s/import(\'dataseed-ui/import(\'@dataseed\/dataseed-ui/g' $f
  sed -i -e $'s/import[ ]*\'dataseed-ui/import \'@dataseed\/dataseed-ui/g' $f
  sed -i -e $'s/require(\'dataseed-ui/require(\'@dataseed\/dataseed-ui/g' $f
  sed -i -e $'s/require($\'dataseed-ui/require($\'@dataseed\/dataseed-ui/g' $f
done

cd ..

npm run reversion

cd tempPkg

LERNA_JSON_PATH="lerna.json"

# 检查 lerna.json 文件是否存在
if [ ! -f "$LERNA_JSON_PATH" ]; then
  # 如果文件不存在，输出错误信息并退出脚本
  echo "Error: lerna.json not found at $LERNA_JSON_PATH"
  exit 1
fi

# 使用 awk 提取 version 字段的值（双引号中的内容）
LERNA_JSON_VERSION=$(awk -F'"version": "' '/"version":/ {print $2}' "$LERNA_JSON_PATH" | awk -F'"' '{print $1}')

# 判断版本号中是否包含 alpha/beta/rc 关键字，并设置对应的 npm tag
if [[ $LERNA_JSON_VERSION == *alpha* ]]; then
  NPM_TAG="alpha"
elif [[ $LERNA_JSON_VERSION == *beta* ]]; then
  NPM_TAG="beta"
elif [[ $LERNA_JSON_VERSION == *rc* ]]; then
  NPM_TAG="rc"
else
  # 如果没有特殊标记，默认为 latest（生产环境的正式版本）
  NPM_TAG="latest"
fi

echo "Detected version: $LERNA_JSON_VERSION"

npm publish --workspaces --registry=http://registry.caijj.net/repository/npm-caijiajia/ --ignore-scripts --tag $NPM_TAG

echo "Publish completed with tag: $NPM_TAG"

cd ..
rm -rf tempPkg
