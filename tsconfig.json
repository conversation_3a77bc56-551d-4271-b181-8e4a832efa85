{"compilerOptions": {"outDir": "lib", "rootDir": "./", "module": "commonjs", "target": "es5", "lib": ["ES6", "DOM", "ES2015", "ES2021"], "jsx": "react", "moduleResolution": "node", "importHelpers": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceRoot": "", "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": true, "strictNullChecks": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "downlevelIteration": true, "composite": true, "baseUrl": "./", "paths": {"amis-core": ["./packages/amis-core/src/index.tsx"], "amis-formula": ["./packages/amis-formula/src/index.ts"], "amis-utils": ["./packages/amis-utils/src/index.ts"], "amis-ui": ["./packages/amis-ui/src/index.tsx"], "dataseed-ui": ["./packages/dataseed-ui/src/index.tsx"], "amis": ["./packages/amis/src/index.tsx"], "ooxml-viewer": ["./packages/ooxml-viewer/src/index.ts"], "amis-editor-core": ["./packages/amis-editor-core/src/index.ts"], "amis-editor": ["./packages/amis-editor/src/index.tsx"]}}, "types": ["typePatches"], "references": [], "include": ["**/*.ts", "**/*.tsx", "**/*.jsx", "scripts/replaceStringPlugin.ts", "scripts/mockApiPlugin.ts", "scripts/vite-markdown-plugin.ts", "packages/amis-ui/src/custom.d.ts", "packages/dataseed-ui/src/custom.d.ts"]}