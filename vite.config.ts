import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import svgr from 'vite-plugin-svgr';
import monacoEditorPlugin from 'vite-plugin-monaco-editor';
import replace from '@rollup/plugin-replace';
import replaceStringPlugin from './scripts/replaceStringPlugin';
import markdown from './scripts/vite-markdown-plugin';
import mockApi from './scripts/mockApiPlugin';
import cacheStrategyPlugin from './scripts/cacheStrategyPlugin';
import transformMobileHtml from './scripts/transformMobileHtml';
// @ts-ignore
import replaceApiPlugin from './scripts/replaceApiPlugin';

// https://vitejs.dev/config/
export default defineConfig(({ command }) => ({
  plugins: [
    cacheStrategyPlugin(),
    replaceStringPlugin(),
    markdown(),
    mockApi(),
    transformMobileHtml(),

    react({
      jsxRuntime: 'classic',
      babel: {
        parserOpts: {
          plugins: ['decorators-legacy', 'classProperties']
        }
      }
    }),
    svgr({
      exportAsDefault: true,
      svgrOptions: {
        svgProps: {
          className: 'icon'
        },
        prettier: false,
        dimensions: false
      }
    }),
    monacoEditorPlugin({}),
    replaceApiPlugin(),
  ],
  optimizeDeps: {
    include: ['amis-formula/lib/doc'],
    esbuildOptions: {
      target: 'esnext'
    }
  },
  base: command === 'serve' ? '/' : '/dataseeddesigndocui/',
  server: {
    host: true,
    port: 8888,
    proxy: {
      "/midwareopr": {
        target: "http://moka.dmz.dev.caijj.net/",
        changeOrigin: true,
      },
      "/staticui": {
        target: "http://moka.dmz.dev.caijj.net/",
        changeOrigin: true,
      },
      "/idaasopr": {
        target: "http://moka.dmz.sit.caijj.net/",
        changeOrigin: true,
      },
      "/dataseeddesigndocui": {
        target: "http://localhost:8888/",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/dataseeddesigndocui/, ''),
      },
      '/infraopr': {
        target: `http://moka.dmz.sit.caijj.net/`,
        changeOrigin: true,
      },
      '/riskbizopr': {
        target: `http://moka.dmz.sit.caijj.net/`,
        changeOrigin: true,
      },
      '/yottaopr': {
        target: `http://moka.dmz.pre.caijj.net/`,
        changeOrigin: true,
      },
      '/fundopr': {
        target: `http://moka.dmz.sit.caijj.net/`,
        changeOrigin: true,
      }
    },
  },
  resolve: {
    alias: [
      {
        find: 'axios',
        replacement: path.resolve(__dirname, './node_modules/axios')
      },
      {
        find: 'amis-formula/lib',
        replacement: path.resolve(__dirname, './packages/amis-formula/src')
      },
      {
        find: 'amis-formula',
        replacement: path.resolve(__dirname, './packages/amis-formula/src')
      },
      {
        find: 'amis-ui/lib',
        replacement: path.resolve(__dirname, './packages/amis-ui/src')
      },
      {
        find: 'amis-ui',
        replacement: path.resolve(__dirname, './packages/amis-ui/src')
      },
      {
        find: 'amis-core',
        replacement: path.resolve(__dirname, './packages/amis-core/src')
      },
      {
        find: 'amis/lib',
        replacement: path.resolve(__dirname, './packages/amis/src')
      },
      {
        find: 'amis',
        replacement: path.resolve(__dirname, './packages/amis/src')
      },
      {
        find: 'amis-utils/lib',
        replacement: path.resolve(__dirname, './packages/amis-utils/src')
      },
      {
        find: 'amis-utils',
        replacement: path.resolve(__dirname, './packages/amis-utils/src')
      },
      {
        find: 'amis-editor',
        replacement: path.resolve(__dirname, './packages/amis-editor/src')
      },
      {
        find: 'dataseed-ui/lib',
        replacement: path.resolve(__dirname, './packages/dataseed-ui/src')
      },
      {
        find: 'dataseed-ui',
        replacement: path.resolve(__dirname, './packages/dataseed-ui/src')
      },
      {
        find: 'amis-editor-core',
        replacement: path.resolve(__dirname, './packages/amis-editor-core/src')
      },
      {
        find: 'ooxml-viewer',
        replacement: path.resolve(__dirname, './packages/ooxml-viewer/src')
      }
    ]
  }
}));
